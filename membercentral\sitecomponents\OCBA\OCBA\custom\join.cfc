<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();		
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		local.crlf = chr(13) & chr(10);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];

		local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="a8209cbd-affa-4ffc-9000-9b4e28c83bd0" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a OCBA member. If you have questions about your membership, please call (248) 334-3400." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (248) 334-3400." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="OCBA Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="489eb5a9-bd35-42ea-b7e8-0069e5a57edb" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PreCheckedAddOns",type="STRING",desc="UIDs for Add-Ons that qualified users will have to opt out of",value="289e65ab-9be8-4a0e-bba6-701d811efef3" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="OCBA CIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="OCBA_Pay_Later" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="OCBA Membership Application Receipt" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="MemberBilledMessage", type="CONTENTOBJ", desc="Display message for billed members", value="You need to renew your OCBA membership. You will be re-directed to your renewal shortly." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Join OCBA',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));
		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="#variables.formname#");
		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
		variables.historyStartedText = "Member started Join form.";
		variables.historyCompletedText = "Member completed Join form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					case "spam":
						local.returnHTML = showError(errorCode='spam');
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":			
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
		
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Category')>	
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
					.subRateLabel {font-weight:normal;}
					.subRatesDisabled {
						opacity: 0.6; /* Real browsers */
						filter: alpha(opacity = 60); /* MSIE */
					}
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;
	
					var memberTypeField;
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);					
					}
					function assignMemberData(memObj) {
						$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
						mc_continueForm($('###variables.formName#'),afterFormLoad);
					}
	
					function validatePaymentForm(isPaymentRequired) {
						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
	
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);     
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous');			        	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}
					
					function resizeBox(newW,newH) { 
						var windowWidth = $(window).width();
						var _popupWidth = newW;
						if(windowWidth < 585) {
							_popupWidth = windowWidth - 30;
						}
						$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
					}
					
					$(document).ready(function() {
						<cfif variables.useMID and NOT local.isSuperUser>					
							var mo = { memberID:#variables.useMID# };
							assignMemberData(mo);					
						<cfelseif local.isSuperUser>
							$('div##div#variables.formName#wrapper').hide();
							$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
						</cfif>
						$(window).on("resize load", function() {
							var windowWidth = $(window).width();
							if(windowWidth < 585) {
								$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
							} else{
								$.colorbox.resize({innerWidth:550, innerHeight:330});		
							}				
						});
					});
				</script>
	
				<script type="text/javascript">
	
					function subscriptionRateOverrideBoxHandler(event) {
	
						var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());
	
						//check subscription if not already checked
						if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
							$(subRateRadioButton)[0].click();
							$(this).focus();
						} else if (subRateRadioButton) {
							$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
						}
					}
	
					function subscriptionCheckboxHandler() {
						if ($(this)[0].checked) {
							$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
							$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
						} else {
							$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
							$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
						}
					}
	
					function subscriptionRateRadioButtonHandler() {
	
						if ($(this)[0].checked) {
							var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
							var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
							var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();
	
							if (rateOverrideBox.length) {
								//rateoverride box is present
								rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
							}
	
							//put label of selected rate radio button next to subscription
							rateDescription = ' - ' + rateDescription;
							$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);
	
							//check subscription if not already checked
							if (!$(subCheckbox)[0].checked)
								$(subCheckbox)[0].click();
						}
					}
					
					function initializeAddons() {
						$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
					}
	
					function selectAllSubscriptionsIfRequired() {
						var addonData = $(this).data();
						// select all addons if minimum required by set is gte available count
						// hide checkboxes so they can not be unselected
						if (addonData.minallowed >= $('.subCheckbox',this).length) {
							$('.subCheckbox:not(:checked)',this).click().hide();
							$('.subCheckbox',this).hide();
						}
					}
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				
				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>
	
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = variables.strPageFields.SubTypeTest>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structkeyexists(session,"useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>			
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>	

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfset local.strFieldSetCategory = application.objCustomPageUtils.renderFieldSet(uid='fe2b1c27-e13f-43e5-9c65-aaff6e9b9ae2', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldPersonalInformation = application.objCustomPageUtils.renderFieldSet(uid='a03d61ba-7056-46b0-ae33-0f6babb61424', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldProfessionalInformation = application.objCustomPageUtils.renderFieldSet(uid='6da39b02-5784-412e-bf55-b36f222136b4', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldOrganisationAddress = application.objCustomPageUtils.renderFieldSet(uid='b4bdb34f-d4dd-488a-994c-29f7f74cd8ca', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldDemographicInfo = application.objCustomPageUtils.renderFieldSet(uid='6337963e-d011-467a-b58c-7d43435a6854', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldHomeAddress = application.objCustomPageUtils.renderFieldSet(uid='d805213c-f64a-460d-b15c-1ffd252c2b23', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldAddressPref = application.objCustomPageUtils.renderFieldSet(uid='5d5fa8f8-9914-4110-95eb-7c74d21e7b17', mode="collection", strData=local.strData)>
		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Category')>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function afterFormLoad(){					
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				
				var memberTypeField;
				var #toScript(local.strFieldSetCategory.strfields, "categoryFieldsObj")#; 
				var #toScript(local.strFieldProfessionalInformation.strfields, "professionalInfoFieldsObj")#; 
				var #toScript(local.strFieldOrganisationAddress.strfields, "organizationAddressFieldsObj")#; 
				var #toScript(local.strFieldDemographicInfo.strfields, "demographicInfoFieldsObj")#; 
				
				var contactTypeSelector = '';
				var publicServiceAttorneySelector = '';
				var partTimeSelector = '';
				var otherStateLicenceSelector = '';
				
				var orgAddrFCNameSelector = '';
				var orgStreetAddressSelector = '';
				var orgAddress2Selector = '';
				var orgCitySelector = '';
				var orgZipSelector = '';
				var orgStateSelector = '';
				var orgAlternateEmailSelector = '';
				var orgWebsiteSelector = '';
				
				var lawSchoolSelector = '';
				var yearGraduatedSelector = '';
				var michiganLicenceNumberSelector = '';
				var michiganStatusSelector = '';
				var michiganDateSelector = '';
				
				
				$.each(professionalInfoFieldsObj, function (key, val) {
					if(val == 'Public Service Attorney'){
						publicServiceAttorneySelector = key;
					}else if(val == 'Part Time'){
						partTimeSelector = key;
					}else if(val == 'Other State Licenses'){
						otherStateLicenceSelector = key;
					}else if(val == 'P##'){
						michiganLicenceNumberSelector = key;
					}else if(val == 'Michigan State Bar Status'){
						michiganStatusSelector = key;
					}else if(val == 'Date Admitted to Practice'){
						michiganDateSelector = key;
					}
				});
				
				$.each(categoryFieldsObj, function (key, val) {
					if(val == 'Contact Type'){
						contactTypeSelector = key;
					}
				});
				
				$.each(organizationAddressFieldsObj, function (key, val) {
					if(val == 'Firm/Company Name'){
						orgAddrFCNameSelector = key;
					}else if(val == 'Street Address'){
						orgStreetAddressSelector = key;
					}else if(val == 'Address 2'){
						orgAddress2Selector = key;
					}else if(val == 'City'){
						orgCitySelector = key;
					}else if(val == 'Zip'){
						orgZipSelector = key;
					}else if(val == 'State'){
						orgStateSelector = key;
					}else if(val == 'Alternate Email'){
						orgAlternateEmailSelector = key;
					}else if(val == 'Website'){
						orgWebsiteSelector = key;
					}
				});	

				$.each(demographicInfoFieldsObj, function (key, val) {
					if(val == 'Law School'){
						lawSchoolSelector = key;
					}else if(val == 'Year Graduated'){
						yearGraduatedSelector = key;
					}
				});
				
				function adjustFieldsetDisplay() {
					var memType = $("option:selected",memberTypeField).text();
					switch(memType) {
						case 'Paralegal or Other Support Staff': 
						case 'Student':
						$('div##professionalInfo-wrapper').hide();
							break;
						default:
						$('div##professionalInfo-wrapper').show();
							break;
					}
				}
				function toggleFTM() {
				}				
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var memType = $(memberTypeField).val();

					if ($("##category-wrapper").is(':visible')) {
						#local.strFieldSetCategory.jsValidation#
					}
					if ($("##personalInfo-wrapper").is(':visible')) {
						#local.strFieldPersonalInformation.jsValidation#
					}
					if ($("##professionalInfo-wrapper").is(':visible')) {
						#local.strFieldProfessionalInformation.jsValidation#
					}
					
					if(contactTypeSelector != ''){
						contactTypeVal = $('##category-wrapper ##'+contactTypeSelector+' option:selected').text();
						if( contactTypeVal == 'Attorney' || contactTypeVal == 'Law School Affiliate'){
							publicServiceAttorneyVal = $('##professionalInfo-wrapper ##'+publicServiceAttorneySelector).val();
							
							partTimeVal = $('##professionalInfo-wrapper ##'+partTimeSelector).val();
							if(publicServiceAttorneyVal == ''){
								arrReq.push('Please select Public Service Attorney');
							}
							if(partTimeVal == ''){
								arrReq.push('Please select Part Time');
							}							
							if(contactTypeVal == 'Attorney'){
							
								orgFCNameVal = $('##organisationAddress-wrapper ##'+orgAddrFCNameSelector).val();
								orgStreetAddVal = $('##organisationAddress-wrapper ##'+orgStreetAddressSelector).val();
								orgAdd2Val = $('##organisationAddress-wrapper ##'+orgAddress2Selector).val();
								orgCityVal = $('##organisationAddress-wrapper ##'+orgCitySelector).val();
								orgZipVal = $('##organisationAddress-wrapper ##'+orgZipSelector).val();
								orgStateVal = $('##organisationAddress-wrapper ##'+orgStateSelector).val();
								orgAEmailVal = $('##organisationAddress-wrapper ##'+orgAlternateEmailSelector).val();
								orgWebsiteVal = $('##organisationAddress-wrapper ##'+orgWebsiteSelector).val();			
								otherStateLicenseVal = $('##professionalInfo-wrapper ##'+otherStateLicenceSelector).val();
																
								if(otherStateLicenseVal == null){
									msLicenseNo = $( "input[name^='"+michiganLicenceNumberSelector.split('_')[0]+'_'+michiganLicenceNumberSelector.split('_')[1]+"_licenseNumber']" ).val();
									msLicenseDate = $( "input[name^='"+michiganDateSelector.split('_')[0]+'_'+michiganDateSelector.split('_')[1]+"_activeDate']" ).val();
									msLicenseStatus = $('##professionalInfo-wrapper ##'+michiganStatusSelector).val();
									
									if(msLicenseNo == ''){
										arrReq.push('Please enter Michigan State Bar #variables.strProfLicenseLabels.profLicenseNumberLabel#');
									}
									if(msLicenseStatus == ''){
										arrReq.push('Please enter Michigan State Bar #variables.strProfLicenseLabels.profLicenseStatusLabel#');
									}
									if(msLicenseDate == ''){
										arrReq.push('Please enter Michigan State Bar #variables.strProfLicenseLabels.profLicenseDateLabel#');
									}
								}
							
							}
						}else if( contactTypeVal == 'Student'){
							lawSchoolVal = $('##demographicInfo-wrapper ##'+lawSchoolSelector).val();
							yearGraduatedVal = $('##demographicInfo-wrapper ##'+yearGraduatedSelector).val();
							if(lawSchoolVal == ''){
								arrReq.push('Please enter Law School');
							}
							if(yearGraduatedVal == ''){
								arrReq.push('Please enter Graduated Year');
							}						
						}
					}
					
					
					if ($("##organisationAddress-wrapper").is(':visible')) {
						#local.strFieldOrganisationAddress.jsValidation#
					}
					if ($("##demographicInfo-wrapper").is(':visible')) {
						#local.strFieldDemographicInfo.jsValidation#
					}
					if ($("##homeAddress-wrapper").is(':visible')) {
						#local.strFieldHomeAddress.jsValidation#
					}
					if ($("##addressPreference-wrapper").is(':visible')) {
						#local.strFieldAddressPref.jsValidation#
					}
					
									
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				$(document).ready(function() {
					prefillData();
					
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
					memberTypeField = $('##md_#local.memberTypeFieldInfo.columnID#');
					$(memberTypeField).change(adjustFieldsetDisplay);
					adjustFieldsetDisplay();
					
					$(document).on('change','##category-wrapper ##'+contactTypeSelector,function(){
						val =  $(this).find('option:selected').text();

						if( val == 'Student'){
							$("##professionalInfo-wrapper").hide();
							$("##organisationAddress-wrapper").hide();
							$("##addressPreference-wrapper").hide();
						
						}else{
							$("##professionalInfo-wrapper").show();
							$("##organisationAddress-wrapper").show();
							$("##addressPreference-wrapper").show();
						
						}
					});	
					
				});				
			</script>
			
			<style type="text/css">
				##divfrmJoinwrapper input[type="text"] {
						width:206px!important;
					}
					##divfrmJoinwrapper select{
						width:220px!important;
					}
									
					div.tsAppSectionHeading{margin-bottom:20px}
					
					##divfrmJoinwrapper table td:nth-child(2) {
						white-space: initial!important;
					}
					@media screen and (max-width: 767px){
						##divfrmJoinwrapper table td {
							display: block;
							margin-bottom:0px;
						}
						##divfrmJoinwrapper table td:nth-child(1) {
							display: inline;
							margin: 0;
							padding: 0;
						}
						##divfrmJoinwrapper table td:nth-child(2) {
							display: inline;
							margin: 0;
							padding: 0;
						}
						##divfrmJoinwrapper table td:nth-child(3) {
							display: inline;
							margin: 0;
							padding: 0;
						}
						##divfrmJoinwrapper table td:nth-child(4) {
							/*margin-bottom: 12px;*/
							margin-left: 0;
							padding-left: 0;
						}
								
						##divfrmJoinwrapper div.ui-multiselect-menu{width:auto!important;}
												
					}	
					##divfrmJoinwrapper button.ui-multiselect {width:220px!important;}
					##divfrmJoinwrapper div.ui-multiselect-menu {width:214px!important;}							
				
					div.alert-danger{
						padding: 10px !important;
					}

					
					
			</style>
			
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.Step1TopContent)>
				<div id="Step1TopContent">#variables.strPageFields.Step1TopContent#</div>
			</cfif>

			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#"  <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
			<cfif NOT local.isLoggedIn AND local.identifiedMemberID GT 0>
				<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
			</cfif>
			
			<div id="divFrmErr" class="alert alert-danger" style="display:none;margin:6px 0;"></div>

			<div id="category-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetCategory.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetCategory.fieldSetContent#
				</div>
			</div>
			<div id="personalInfo-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldPersonalInformation.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldPersonalInformation.fieldSetContent#
				</div>
			</div>	
			<div id="professionalInfo-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldProfessionalInformation.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldProfessionalInformation.fieldSetContent#
				</div>
			</div>
			<div id="organisationAddress-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldOrganisationAddress.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldOrganisationAddress.fieldSetContent#
				</div>
			</div>
			<div id="demographicInfo-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldDemographicInfo.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldDemographicInfo.fieldSetContent#
				</div>
			</div>
			<div id="homeAddress-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldHomeAddress.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldHomeAddress.fieldSetContent#
				</div>
			</div>
			<div id="addressPreference-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldAddressPref.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldAddressPref.fieldSetContent#
				</div>
			</div>			
			<div class="row-fluid fieldsetFormWrapper">
				<div class="span12 tsAppSectionContentContainer">							
					#variables.captchaDetails.htmlContent#
				</div>
			</div> 

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

				<script language="javascript">	
				
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
							function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
								var _address = _this.val();
								
								if(_address.length >0){
									if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
										$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
									}
								} else {
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
								}
							}
							
							$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
							});
						</cfloop>
					});
					
					
				</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- save member info and record history --->		
		
		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>	

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										
			<cfset session.captchaEntered = 1>
			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subxml">
			set nocount on;

			declare @subID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.subscriptionID#">;
				
			select [dbo].[fn_sub_getSubscriptionStructureXML] (@subID,1) as subxml;

			set nocount off;
		</cfquery>
		 
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>			
		
		<cfset	local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>		

		<cfsavecontent variable="local.headtext">
			<cfoutput>
				<script type="text/javascript">
					$(document).ready(function(){
						$('input.subCheckbox:checkbox').on('change',subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').on('change',subscriptionRateRadioButtonHandler);
						$('input.subRateOverrideBox').on('change',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('focus',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('blur',subscriptionRateOverrideBoxHandler);

						initializeAddons();

						$('input.subCheckbox:checkbox').each(subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').each(subscriptionRateRadioButtonHandler);
						
						$(document).on('change',"*[id^='sub#local.subscriptionID#_rate']",function(){
							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').not('[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').closest('label').hide();
							
							$('div [data-setname="LRS Membership"] input:radio[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').closest('label').show();
							
							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').not('[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').prop('checked',false);

							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').prop('disabled',false);
							
							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').not('[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').prop('disabled',true);							
						});
											
						
					});
					function validateMembershipInfoForm(){
							var arrReq = new Array();
							
							<cfif val(local.subscriptionID)>
								if($(".well").eq(1).find('input[type="radio"],input[type="checkbox"],input[name="sub#local.subscriptionID#"]').length == 0){
									arrReq[arrReq.length] = " Select Membership.";
								}
								else if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
									arrReq[arrReq.length] = " Select Membership.";
								}
							</cfif>	
		
							//make sure selected subscriptions all have selected rates.
							$('input.subCheckbox:checkbox:checked').each(function(x,item){
								var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
								var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
								if ( numRates > 0 && numRatesChecked==0) {
									arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
								}
							});
		
							//make sure any chosen editable rates have amounts greater than zero.		
							$('input.subRateCheckbox:radio:checked').each(function(index,item){
								var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
								if ($(rateOverrideField).length) {
									var overridePrice = parseFloat($(rateOverrideField)[0].value);
									if (!overridePrice) {
										var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
										subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
										arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
									}
								}
							});
							
							var min = $('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper').data('minallowed');
							var max = $('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper').data('maxallowed');
							
							if($('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
								
								if($('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper input.subCheckbox:checked').length < min ){
									arrReq[arrReq.length] = "Please select at least " + min + ' LRS Panels subscription';
								}	

								if($('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper input.subCheckbox:checked').length > max ){
									arrReq[arrReq.length] = "Please select no more than " + max + ' LRS Panels subscription';
								}
							}							
							
							#Replace(local.result.jsValidation, "$('.subRateCheckbox:checked, .subRateCheckbox:hidden')", "$('.subRateCheckbox:checked')", "ALL")#
							#local.result.jsAddonValidation#				
														
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								return false;
							}		
							
							mc_continueForm($('###variables.formName#'),afterFormLoad);
							return false;
						}
						function afterFormLoad(){
							$('html, body').animate({ scrollTop: 0 }, 500);
						}
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headtext#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">

			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert alert-danger" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<cfoutput>#local.result.formcontent#</cfoutput>
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				<script>				
					$(document).ready(function(){
						if($('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').show();						
						}else{
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').hide();
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
						}

						$(document).on('change',$('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox'),function(){
							if($('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
								$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').show();						
							}else{
								$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').hide();
								$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
							}
						});						
					});
				</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset	local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc)>		

		<cfif local.strResult.success>
			
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>		
			
				
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
				
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
	<cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>

		<cfreturn getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)>
	</cffunction>
	<cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', totalFullPrice = 0 }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">			
		</cfif>
		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								- 
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
										<!--- #local.thisRate.rateName#  --->
										
										<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
											#local.thisRate.rateName#  (<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> Full)
											<cfset local.strReturn.totalFullPrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
										<cfelse>
										   <cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												
												<cfset local.endData = ListToArray(local.thisRate.termAFEndDate,"T")>
												<cfset local.dateDiff = dateDiff("m", Now(), local.endData[1])>
												<cfset local.quarterLeft = '4'>
												<cfif local.thisRateFrequency.frequencyName eq "Full">
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
												<cfelseif local.thisRateFrequency.frequencyName eq "Quarterly">
													<cfif local.dateDiff gte 0 && local.dateDiff lte 3>
														<cfset local.quarterLeft = 1>
													<cfelseif local.dateDiff gte 4 AND local.dateDiff lte 6 >
														<cfset local.quarterLeft = 2>
													<cfelseif local.dateDiff gte 7 AND local.dateDiff lte 9 >
														<cfset local.quarterLeft = 3>
													<cfelse>
														<cfset local.quarterLeft = 4>
													</cfif>
												</cfif>
												
												<cfif arguments.recursionLevel eq 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
													<cfif local.thisRateFrequency.frequencyName eq "Quarterly">
														<cfset local.strReturn.totalFullPrice = (local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft />
														<cfset local.rateVal = dollarFormat((local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft)>
													<cfelse>
														<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
														<cfset local.rateVal = dollarFormat(local.thisRateFrequency.rateAmt)>
													</cfif>
													
													#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#local.rateVal#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
												<cfelseif arguments.recursionLevel gt 1>
													<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
														<cfif local.thisRateFrequency.frequencyName eq "Quarterly">
															<cfset local.strReturn.totalFullPrice = (local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft />
															<cfset local.rateVal = dollarFormat((local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft)>
														<cfelse>
															<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
															<cfset local.rateVal = dollarFormat(local.thisRateFrequency.rateAmt)>
														</cfif>
													</cfif>
													
													
													<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid )>
														   #local.thisRate.rateName#  
														   <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#local.rateVal#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
													</cfif> 
													
												</cfif>							
											</cfloop>
										</cfif>
									</cfif>
								</cfloop>
							</span>
						</div>
					</div>			
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false />							
									<cfset local.pcNumCounter = 1 />									
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false />
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true />
										</cfif>										
										<cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree) />
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
										<cfif len(trim(local.thisAddonSubForm.formContent))>	
																			
											<cfset local.selectionFound = true />
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
											</cfif>
											<cfset local.pcNumCounter = local.pcNumCounter + 1 />											
											#local.thisAddonSubForm.formContent#
										</cfif>									
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>						
						</div>
					</cfif>						
				</cfif>
			</cfoutput>				
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfscript>
			local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc);
		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >	
		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			
			<cfif len(variables.strPageFields.Step3TopContent)>
				<div id="Step3TopContent">#variables.strPageFields.Step3TopContent#</div><br/>
			</cfif>
			
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub"
					>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert alert-danger" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
			</div>
			<br/>

			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>

			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			//create subscriptions
			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
		
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodeCredit>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodeCredit>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.strFieldSetCategory = application.objCustomPageUtils.renderFieldSet(uid='fe2b1c27-e13f-43e5-9c65-aaff6e9b9ae2', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.strFieldPersonalInformation = application.objCustomPageUtils.renderFieldSet(uid='a03d61ba-7056-46b0-ae33-0f6babb61424', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.strFieldProfessionalInformation = application.objCustomPageUtils.renderFieldSet(uid='6da39b02-5784-412e-bf55-b36f222136b4', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.strFieldOrganisationAddress = application.objCustomPageUtils.renderFieldSet(uid='b4bdb34f-d4dd-488a-994c-29f7f74cd8ca', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.strFieldDemographicInfo = application.objCustomPageUtils.renderFieldSet(uid='6337963e-d011-467a-b58c-7d43435a6854', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.strFieldHomeAddress = application.objCustomPageUtils.renderFieldSet(uid='d805213c-f64a-460d-b15c-1ffd252c2b23', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.strFieldAddressPref = application.objCustomPageUtils.renderFieldSet(uid='5d5fa8f8-9914-4110-95eb-7c74d21e7b17', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Category')>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset	local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<p>Here are the details of your application:</p>

			#local.strFieldSetCategory.fieldSetContent#
			#local.strFieldPersonalInformation.fieldSetContent#
			#local.strFieldProfessionalInformation.fieldSetContent#
			#local.strFieldOrganisationAddress.fieldSetContent#
			#local.strFieldDemographicInfo.fieldSetContent#
			#local.strFieldHomeAddress.fieldSetContent#
			#local.strFieldAddressPref.fieldSetContent#			
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#local.strResult.formContent#
					<br/>
					<br/>
					<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
					<br/>
				</td>
			</tr>
			</table>

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="Thank you for your application to OCBA",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.Subject = variables.strPageFields.StaffConfirmationSub >
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to OCBA", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	<cffunction name="storeMembershipApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument',
				parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', 
				docDesc='Membership Application Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
				contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.ActiveAcceptedMessage#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ActiveAcceptedMessage#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.MemberBilledMessage#
						<script type="text/javascript">
							setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
							function AJAXRenewSub(member){
								var redirect = function(r) {
									redirectLink = '/renewsub/' + r.data.directlinkcode[0];
									window.location = redirectLink;								
								};		
								
								var params = { memberID:member, status:'O', distinct:false };
								TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
							}						
						</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>