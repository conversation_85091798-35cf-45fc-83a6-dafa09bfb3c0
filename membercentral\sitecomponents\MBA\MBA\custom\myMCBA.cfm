<cfsavecontent variable="local.pghead">
	<cfoutput>
	<style type="text/css">
		.myMAJ .nav-tabs > .active > a,.myMAJ  .nav-tabs>.active>a:hover { color:##fff!important; background:##355B8C!important; }
		.myMAJ .nav-tabs a, .myMAJ .nav-tabs a:hover { color:##455b68; background:##d3d3d3 !important; }
		.myMAJ .nav-tabs>li>a { margin-right:23px; background:##ececec!important; }
		.myMAJ .nav-tabs>li:last-child>a { margin-right:auto; }
		.myMAJ .nav { margin-bottom:0px; }
		.myMAJ .sponsors { margin-bottom:0px; margin-top:0px; }
		.infoCont{padding-top:20px !important;margin-bottom:10px !important;}
		.MAJRow{margin-left:0px !important;margin-bottom:0px !important;}
		.tab-content { border:2px solid ##ddd; min-height:220px; padding:10px; margin-bottom:20px; background:##fff;}
		.showBullets{list-style: inside !important;}
		.myMAJ .nav-tabs > li > a { border: 1px solid transparent; border-radius: 4px 4px 0 0; line-height: 1.42857; margin-right: 2px;}
		.HeaderText {color: ##333436;font-weight:bold; font-size: 16px; line-height: 19px; margin: 0; padding: 0 0 13px;}
		.myMAJ .myInfo .showBullets a{color:##6395db !important;}
		.sponsors{background:##355B8C none repeat scroll 0 0;color:##fff;padding: 0px 15px;border-radius:4px 4px 0 0;font-size: inherit !important;font-weight: normal !important;text-align: center;}
		##wrapper{width:100%;}
		.carousel-inner p { padding: 15px !important; margin: 15px 0 20px !important; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pghead#">

<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="Sponsors", type="CONTENTOBJ", desc="Sponsors", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FeatureResourcesDesc", type="CONTENTOBJ", desc="Featured Resources", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="QuickLinksDesc", type="CONTENTOBJ", desc="Quick Links Description", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
</cfscript>

<cfoutput>
	<div class="container">
		<div class="row-fluid">
			<div id="mainContent">
				<div class="span12 row-fluid myMAJ infoCont">
					<div class="span1 myPhoto">
						<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
							<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
						</cfif>
					</div>
					<div class="span10 myInfo">
						<span class="HeaderText">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName# #session.cfcUser.memberData.suffix#</span><br />
						<span class="BodyText">
							<ul style="margin:0px" class="showBullets">
								<li><a href="/?pg=updateMember">Update My Profile</a></li>
								<li><a href="/?pg=updateMember##memberUpdateSectionLoginInfo">Change My Login</a></li>
								<li><a href="/?pg=updatemember&memaction=updatePhoto">Update Directory Photo</a></li>
								<li><a href="/?pg=listviewer">Browse My List Servers</a></li>
							</ul>
						</span>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##upcomingEvents" data-toggle="tab" class="MainNavText">Upcoming Events</a></li>
								<li><a href="##registeredEvents" data-toggle="tab" class="MainNavText">My Events</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="upcomingEvents">
									[[upcomingEvents pagename=[events] includeRegistered=[false] includeNotRegistered=[true] maxrows=[5] noresultstext=[There are currently no upcoming events.]]]
									<br/>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
								<div class="tab-pane BodyText" id="registeredEvents">
									[[upcomingEvents pagename=[events] includeRegistered=[true] includeNotRegistered=[false] maxrows=[5] noresultstext=[You are currently not registered for any Events.]]]
									<br/>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>				
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##invoices" data-toggle="tab" class="MainNavText">Past Due Invoices</a></li>
								<li><a href="##myPayments" data-toggle="tab" class="MainNavText">Recent Payments</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="invoices">
									[[overdueInvoices maxrows=[4] noresultstext=[You do not have any past due invoices.]]]
									<br/>
									<div>
										<a href="/?pg=invoices"><i class="icon-calendar icon2x">&nbsp;</i><strong>View All Past Due Invoices</strong></a>
									</div>
								</div>
								<div class="tab-pane BodyText" id="myPayments">
									[[myRecentPayments maxrows=[5] noresultstext=[There are no recent payments.]]]
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<h3 class="sponsors">Sponsors</h3>
							<div class="tab-content">
								<div class="tab-pane active" id="sponsers">
									<div id="myCarousel" class="carousel slide text-center">
										<!-- Carousel items -->
										<div class="carousel-inner text-center">
											#local.strPageFields.Sponsors#
										</div>
									</div>
									<script type='text/javascript'>
										$(document).ready(function() {
											$('.carousel').carousel({
												interval: 4000
											})
										});    
									</script>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##announcements" data-toggle="tab" class="MainNavText">Announcements</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="announcements">
									[[myAnnouncements maxrows=[5] noresultstext=[Currently, there are no announcements.]]]
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##quickLinks" data-toggle="tab" class="MainNavText">Quick Links</a></li>
								<li><a href="##recentSearch" data-toggle="tab" class="MainNavText">My Recent Searches</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="quickLinks">
									#local.strPageFields.QuickLinksDesc#
								</div>
								<div class="tab-pane BodyText" id="recentSearch">
									[[mySearchHistory maxrows=[5] noresultstext=[You do not have any previous searches.]]]
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##featuredResources" data-toggle="tab" class="MainNavText">Featured Resources</a></li>	
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="featuredResources">
									#local.strPageFields.FeatureResourcesDesc#
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					[[myECommunities format=[json] jsonvariable=[eCommunity]]]
					<div class="mcMergeTemplate" data-mcjsonvariable="eCommunity">
						{{##if eCommunities}}
							<div class="span4">
								<div class="row-fluid">
									<ul class="nav nav-tabs" id="sponsorTab">
										<li class="active"><a href="##eComms" data-toggle="tab" class="MainNavText">eCommunities</a></li>
									</ul>
									<div class="tab-content">
										<div class="tab-pane active BodyText" id="eComms">
							            	<div class="sectionTitle"><b>You have access to the following groups:</b></div>
											{{##each eCommunities}}
												<ul class="mc-communityList">
													<li>{{{communityName}}} <a href="{{{pageLink}}}">(View)</a></li>
												</ul>
											{{/each}}
										</div>
									</div>
								</div>
							</div>
						{{/if}}
						<div class="span4">
							<div class="row-fluid">
								<ul class="nav nav-tabs" id="myTabs">
									<li class="active"><a href="##newstoreproducts" data-toggle="tab" class="MainNavText">New Store Items</a></li>
									<li><a href="##store" data-toggle="tab" class="MainNavText">Recent Purchases</a></li>
								</ul>
								<div class="tab-content">
									<div class="tab-pane active BodyText" id="newstoreproducts">
										<div class="sectionTitle"><strong>These are the latest products added to the Online Store</strong></div>
										<br />
										[[latestStoreProducts maxrows=[5] noresultstext=[There are no recent store products.]]]
										<div class="sectionContent">
											<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top:35px;" class="">
											<tr>
												<td width="5%" style="vertical-align:top;">
													<div class="circle-tile-heading-small green pull-right">
														<div class="circle-text-small">
															<a href="/?pg=store"><i class="icon-book" style="color:##00b">&nbsp;</i></a>
														</div>
													</div>
												</td>
												<td style="text-align:left;padding-left:3px;"><a href="/?pg=store">Browse the Online Store</a></td> 
											</tr>
											</table>
										</div>
									</div>
									<div class="tab-pane BodyText" id="store">
										[[myStorePurchases maxrows=[5] noresultstext=[You have no recent purchases to display.]]]
										<div class="sectionContent">
											<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top:35px;" class="bottomLinks">
												<tr>
													<td width="5%" style="vertical-align:top;">
														<div class="circle-tile-heading-small green pull-right">
															<div class="circle-text-small">
																<a href="/?pg=store&sa=myPurchases"><i class="icon-shopping-cart" style="color:##00b">&nbsp;</i></a>
															</div>
														</div>
													</td>
													<td style="text-align:left;padding-left:3px;"><a href="/?pg=store&sa=myPurchases">Browse All My Purchases</td> 
												</tr>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="span4">
							<div class="row-fluid">
								<ul class="nav nav-tabs" id="myTabs">
									<li class="active"><a href="##newfilesharedocs" data-toggle="tab" class="MainNavText">New Fileshare Docs</a></li>
									<li><a href="##myfiledownloads" data-toggle="tab" class="MainNavText">My File Downloads</a></li>
								</ul>
								<div class="tab-content">
									<div class="tab-pane active BodyText" id="newfilesharedocs">
										[[recentFileShareUploads noresultstext=[There are no recent documents.]]]
									</div>
									<div class="tab-pane BodyText" id="myfiledownloads">
										[[myRecentFileshareDownloads noresultstext=[There are no recent downloads.]]]
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfoutput>