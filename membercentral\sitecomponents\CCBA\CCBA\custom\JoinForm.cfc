<cfcomponent extends="model.customPage.customPage" output="false">

	<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.crlf = chr(13) & chr(10);
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];

		local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Identify Yourself" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Continue" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorIntroText",type="CONTENTOBJ",desc="Text to show above account locator",value="Thank you for your interest in joining our association." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CCPayProfileCode",type="STRING",desc="pay profile code for CC",value="CCBA_CC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CheckPayProfileCode",type="STRING",desc="pay profile code for Check",value="PayByCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="errActiveFound",type="CONTENTOBJ",desc="Error message when active subscription found",value="CCBA records indicate you are currently a CCBA member. <a href='/?pg=manageSubscriptions'>Click here</a> to continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="errAcceptedFound",type="CONTENTOBJ",desc="Error message when active subscription found",value="CCBA records indicate you are currently a CCBA member. <a href='/?pg=manageSubscriptions'>Click here</a> to continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="errBilledFound",type="CONTENTOBJ",desc="Error message when active subscription found",value="You need to renew your CCBA membership. <a href='/?pg=manageSubscriptions'>Click here</a> to continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your CCBA membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentPage1",type="CONTENTOBJ",desc="Content at the top of Page 1",value="Fields marked with a * are required." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMembershipDues",type="CONTENTOBJ",desc="Content at the top of membership rates display",value="Based on the information you have provided, you are eligible for the membership rate(s) listed below. #local.crlf# The Membership Assessment is required for all members. #local.crlf# Your $60 Chester County Bar Foundation dues are optional and tax-deductible to the extent allowed by law." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentGroupMemberships",type="CONTENTOBJ",desc="Content at the top of group memberships display",value="The Chester County Bar Association is your association and we encourage you to get involved with our over 40 Committees and Sections to see firsthand the many benefits of your membership. The level of activity and the frequency of meetings vary by Committee and Section. " }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentConfirmation",type="CONTENTOBJ",desc="Content on Confirmation",value="Thank you for joining CCBA!" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="subscriptionUID",type="STRING",desc="UID of the Root Subscription that this form offers",value="0614a7ff-b50c-441b-b573-35417ed1f69c" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="defaultAddonSubscriptionUIDs",type="STRING",desc="List of optional Addon Subscription UIDs that should be added by default",value="df300e49-e5f2-48e2-9b93-a2302b5db832" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="foundationFellowText",type="CONTENTOBJ",desc="Text explaning the Foundation Fellows option",value="Text about Foundation Fellows goes here" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="foundationKeyFellowText",type="CONTENTOBJ",desc="Text explaning the Foundation Key Fellows option",value="Text about Foundation Key Fellows goes here" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

		variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Join CCBA',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('historyID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('historyID')));
		variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Mbrapp', subName='Started');
		variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Mbrapp', subName='Completed');
		variables.historyStartedText = "Member started join form.";
		variables.historyCompletedText = "Member completed join form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo();
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;	
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}

				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='member type')>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				.subRateLabel {font-weight:normal;}
				.subRatesDisabled {
					opacity: 0.6; /* Real browsers */
					filter: alpha(opacity = 60); /* MSIE */
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;


				var memberTypeField;

				function adjustFieldsetDisplay() {
					var memType = $("option:selected",memberTypeField).text();
					switch(memType) {
						case 'Law Student': 
						case '':
							$('div##licenses-wrapper').hide();
							$('div##proInfo-wrapper').hide();
							$('div##businessAddress-wrapper').hide();
							$('div##licenses-wrapper').hide();
							$('div##addressPrefs-wrapper').hide();
							break;
						default:
							$('div##licenses-wrapper').show();
							$('div##proInfo-wrapper').show();
							$('div##businessAddress-wrapper').show();
							$('div##licenses-wrapper').show();
							$('div##addressPrefs-wrapper').show();
					}
				}

				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}

				function validatePaymentForm(ispaymentrequired) {
					if(ispaymentrequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>

			<script type="text/javascript">



				function subscriptionRateOverrideBoxHandler(event) {

					var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());

					//check subscription if not already checked
					if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
						$(subRateRadioButton)[0].click();
						$(this).focus();
					} else if (subRateRadioButton) {
						$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
					}
				}

				function subscriptionCheckboxHandler() {
					if ($(this)[0].checked) {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
					} else {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
					}
				}

				function subscriptionRateRadioButtonHandler() {

					if ($(this)[0].checked) {
						var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
						var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
						var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();

						if (rateOverrideBox.length) {
							//rateoverride box is present
							rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
						}

						//put label of selected rate radio button next to subscription
						rateDescription = ' - ' + rateDescription;
						$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);

						//check subscription if not already checked
						if (!$(subCheckbox)[0].checked)
							$(subCheckbox)[0].click();
					}
				}


				function initializeAddons() {

					$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);

				}

				function selectAllSubscriptionsIfRequired() {
					var addonData = $(this).data();
					// select all addons if minimum required by set is gte available count
					// hide checkboxes so they can not be unselected
					if (addonData.minallowed >= $('.subCheckbox',this).length) {
						$('.subCheckbox:not(:checked)',this).click().hide();
						$('.subCheckbox',this).parent().attr('for','')
						$('.subCheckbox',this).hide();
					}
				}
			</script>


			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<cfif len(variables.strPageFields.AccountLocatorIntroText)>
				<div id="AccountLocatorIntroText">#variables.strPageFields.AccountLocatorIntroText#</div><br/>
			</cfif>


			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">


		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = "646f5b87-1081-4ee8-a179-c2bc9cfd4a65">

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>

		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structkeyexists(session,"useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>			
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>	

		<cfset local.strFieldSetMembership = variables.objCustomPageUtils.renderFieldSet(uid='87762F07-DE39-4FD7-A6A9-261EECDFC04C', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetEducation = variables.objCustomPageUtils.renderFieldSet(uid='F905930D-4AAD-4379-9D9D-379B6FC59ADE', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetLicenses = variables.objCustomPageUtils.renderFieldSet(uid='00B26D32-21B6-4D94-A3EF-0BC28828D479', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetProInfo = variables.objCustomPageUtils.renderFieldSet(uid='6D42CE7A-FF36-4F1D-A693-17C6505AFEDF', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetBusinessAddress = variables.objCustomPageUtils.renderFieldSet(uid='955C31D7-67C9-4ACA-A790-279C59DD11B8', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetHomeAddress = variables.objCustomPageUtils.renderFieldSet(uid='557A94C0-9744-414C-9E3F-7C649FE14058', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetAddressPrefs = variables.objCustomPageUtils.renderFieldSet(uid='AF092242-73B0-4084-A0F3-36CDAE38D9C5', mode="collection", strData=local.strData)>
		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='member type')>

		<cfset local.licensesOnForm = structNew()>
		<cfloop collection="#local.strFieldSetLicenses.strFields#" item="local.thisField">
			<cfif refind('mpl\_\d+\_(activeDate|licenseNumber|status)', local.thisField)>
				<cfset local.licenseID = listGetAt(local.thisField, 2,'_')>
				<cfset local.licensePart = listGetAt(local.thisField, 3,'_')>
				<cfif not structKeyExists(local.licensesOnForm, local.licenseID)>
					<cfset local.licensesOnForm[local.licenseID] = arrayNew(1)>
				</cfif>
				<cfset arrayAppend(local.licensesOnForm[local.licenseID],local.strFieldSetLicenses.strFields[local.thisField])>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">



				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var memType = $(memberTypeField).val();

					if ($("##membership-wrapper").is(':visible')) {
						#local.strFieldSetMembership.jsValidation#
					}
					if ($("##education-wrapper").is(':visible')) {
						#local.strFieldSetEducation.jsValidation#
					}
					if ($("##proInfo-wrapper").is(':visible')) {
						#local.strFieldSetProInfo.jsValidation#
					}
					if ($("##businessAddress-wrapper").is(':visible')) {
						#local.strFieldSetBusinessAddress.jsValidation#
					}
					if ($("##homeAddress-wrapper").is(':visible')) {
						#local.strFieldSetHomeAddress.jsValidation#
					}
					if ($("##addressPrefs-wrapper").is(':visible')) {
						#local.strFieldSetAddressPrefs.jsValidation#
					}


					if ($("##licenses-wrapper").is(':visible')) {

						#local.strFieldSetLicenses.jsValidation#

						/* 
							Make sure that all 3 license parts are filled in or all left empty
							-------------------------------------------------------------------
							if all parts are empty then min and max length both equal zero.
							if all parts filled in then min and max length both greater than zero.
							if min length equal zero and max length greater than zero, then some are filled in and others aren't

						*/
						<cfloop collection="#local.licensesOnForm#" item="local.thisLicense">
							var mpl_#local.thisLicense#_activeDate = $('##mpl_#local.thisLicense#_activeDate').val().trim();
							var mpl_#local.thisLicense#_licenseNumber = $('##mpl_#local.thisLicense#_licenseNumber').val().trim();
							var mpl_#local.thisLicense#_status = $('##mpl_#local.thisLicense#_status').val().trim();
							var mpl_#local.thisLicense#_errorMessage = "These fields must be either all empty or all filled in: #arrayToList(local.licensesOnForm[local.thisLicense])#";


							if (Math.min(
									mpl_#local.thisLicense#_activeDate.length,
									mpl_#local.thisLicense#_licenseNumber.length,
									mpl_#local.thisLicense#_status.length) == 0 
								&& Math.max(
									mpl_#local.thisLicense#_activeDate.length,
									mpl_#local.thisLicense#_licenseNumber.length,
									mpl_#local.thisLicense#_status.length) > 0
								) {
									arrReq[arrReq.length] = mpl_#local.thisLicense#_errorMessage;

							}
						</cfloop>
					}


					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				$(document).ready(function() {
					prefillData();
					memberTypeField = $('##md_#local.memberTypeFieldInfo.columnID#');
					$(memberTypeField).change(adjustFieldsetDisplay);
					adjustFieldsetDisplay();

				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.contentPage1)>
				<div id="contentPage1">#variables.strPageFields.contentPage1#</div>
			</cfif>


			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div id="membership-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetMembership.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetMembership.fieldSetContent#
				</div>
			</div>
			<div id="education-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetEducation.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetEducation.fieldSetContent#
				</div>
			</div>
			<div id="licenses-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetLicenses.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetLicenses.fieldSetContent#
				</div>
			</div>
			<div id="proInfo-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetProInfo.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetProInfo.fieldSetContent#
				</div>
			</div>
			<div id="businessAddress-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetBusinessAddress.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetBusinessAddress.fieldSetContent#
				</div>
			</div>
			<div id="homeAddress-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetHomeAddress.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetHomeAddress.fieldSetContent#
				</div>
			</div>
			<div id="addressPrefs-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetAddressPrefs.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetAddressPrefs.fieldSetContent#
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- save member info and record history --->		
		<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "useHistoryID")>
				<cfset variables.useHistoryID = variables.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subxml">
			set nocount on;

			declare @subID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.subscriptionID#">;
				
			select [dbo].[fn_sub_getSubscriptionStructureXML] (@subID,1) as subxml;

			set nocount off;
		</cfquery>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.defaultAddonSubscriptionUIDs#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>			
		<cfscript>
			local.result = variables.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				);

			local.eligibleForKeyFellows = variables.objCustomPageUtils.mem_getGroups(
				memberID=variables.useMID,
				orgID=variables.orgID,
				groupCode='keyFellows_credentialing'
			).recordcount;
		</cfscript>
		<cfsavecontent variable="local.headtext">
			<cfoutput>
				<script type="text/javascript">
					function validateMembershipInfoForm(){
						var arrReq = new Array();

						if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
							arrReq[arrReq.length] = "Select Membership.";
						}

						//make sure selected subscriptions all have selected rates.
						$('input.subCheckbox:checkbox:checked').each(function(x,item){
							var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
							var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
							if ( numRates > 0 && numRatesChecked==0) {
								arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
							}
						});


						//make sure Foundation Fellow payment preference chosen, when applicable
						var selectedFoundationFellow = $('##mccf_foundationFellowCheckbox:checked').length;
						var selectedFoundationFellowAutoPay = $('##mccf_foundationFellowPaymentPreference_auto:checked').length;
						var selectedFoundationFellowInvoice = $('##mccf_foundationFellowPaymentPreference_invoice:checked').length;

						if (selectedFoundationFellow && !(selectedFoundationFellowAutoPay || selectedFoundationFellowInvoice))
							arrReq[arrReq.length] = "Choose a payment preference for Foundation Fellow";


						//make sure Foundation Key Fellow payment preference chosen, when applicable
						var selectedFoundationKeyFellow = $('##mccf_foundationKeyFellowCheckbox:checked').length;
						var selectedFoundationKeyFellowAutoPay = $('##mccf_foundationKeyFellowPaymentPreference_auto:checked').length;
						var selectedFoundationKeyFellowInvoice = $('##mccf_foundationKeyFellowPaymentPreference_invoice:checked').length;

						if (selectedFoundationKeyFellow && !(selectedFoundationKeyFellowAutoPay || selectedFoundationKeyFellowInvoice))
							arrReq[arrReq.length] = "Choose a payment preference for Foundation Key Fellow";

						//make sure any chosen editable rates have amounts greater than zero.

						$('input.subRateCheckbox:radio:checked').each(function(index,item){
							var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
							if ($(rateOverrideField).length) {
								var overridePrice = parseFloat($(rateOverrideField)[0].value);
								if (!overridePrice) {
									var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
									subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
									arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
								}
							}
						});


						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}

						mc_continueForm($('###variables.formName#'));
						return false;
					}
					$(function() {
						$('input.subCheckbox:checkbox').on('change',subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').on('change',subscriptionRateRadioButtonHandler);
						$('input.subRateOverrideBox').on('change',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('focus',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('blur',subscriptionRateOverrideBoxHandler);

						initializeAddons();

						$('input.subCheckbox:checkbox').each(subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').each(subscriptionRateRadioButtonHandler);

					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headtext#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.contentMembershipDues)>
				<div id="contentMembershipDues">#variables.strPageFields.contentMembershipDues#</div><br/>
			</cfif>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">

			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<cfoutput>#local.result.formcontent#</cfoutput>
						<div class="well subAddonWrapper" id="foundationFellowCheckbox">
							<legend>Foundation Fellow</legend>
							<cfif len(variables.strPageFields.foundationFellowText)>
								<div id="foundationFellowText">#variables.strPageFields.foundationFellowText#</div><br/>
							</cfif>

							<div class="addonMessageArea"></div>
							<cfset local.mccf_foundationFellowSelected = 0>
							<cfset local.mccf_foundationFellowRate = 0>
							<cfset local.mccf_foundationFellowPaymentPreference = 0>

							<cfif structKeyExists(arguments.rc, "mccf_foundationFellowCheckbox")>
								<cfset local.mccf_foundationFellowSelected = 1>
							</cfif>
							<cfif structKeyExists(arguments.rc, "mccf_foundationFellowRate")>
								<cfset local.mccf_foundationFellowRate = arguments.rc.mccf_foundationFellowRate>
							</cfif>
							<cfif structKeyExists(arguments.rc, "mccf_foundationFellowPaymentPreference")>
								<cfset local.mccf_foundationFellowPaymentPreference = arguments.rc.mccf_foundationFellowPaymentPreference>
							</cfif>

							<div>
								<div class="">
									<label class="checkbox subLabel" for="mccf_foundationFellowCheckbox">
									<input class="subCheckbox" type="checkbox" <cfif local.mccf_foundationFellowSelected>checked="checked"</cfif> name="mccf_foundationFellowCheckbox" id="mccf_foundationFellowCheckbox" value="1"> 
										Foundation Fellow
										<span class="selectedRate" id="mccf_foundationFellow_selectedRate">
										</span>
									</label>
									<div style="margin-left:15px;" class="subAvailableRates subRatesDisabled">
										<label class="radio subRateLabel" for="mccf_foundationFellow_1500">
											<input type="radio" class="subRateCheckbox" <cfif local.mccf_foundationFellowRate eq 1500>checked="checked"</cfif> name="mccf_foundationFellowRate" id="mccf_foundationFellow_1500" value="1500">
											<span class="labelText">$1500 one-time payment</span>
										</label>
										<label class="radio subRateLabel" for="mccf_foundationFellow_500">
											<input type="radio" class="subRateCheckbox" <cfif local.mccf_foundationFellowRate eq 500>checked="checked"</cfif> name="mccf_foundationFellowRate" id="mccf_foundationFellow_500" value="500">
											<span class="labelText">$500 annually for 3 years</span>
										</label>
										<label class="radio subRateLabel" for="mccf_foundationFellow_300">
											<input type="radio" class="subRateCheckbox" <cfif local.mccf_foundationFellowRate eq 300>checked="checked"</cfif> name="mccf_foundationFellowRate" id="mccf_foundationFellow_300" value="300">
											<span class="labelText">$300 annually for 5 years</span>
										</label>

										<p>We will invoice for for Foundation Fellow separately. Please select your preference.</p>

										<label class="radio" for="mccf_foundationFellowPaymentPreference_auto">
											<input type="radio" class="" <cfif local.mccf_foundationFellowPaymentPreference eq "autopay">checked="checked"</cfif> name="mccf_foundationFellowPaymentPreference" id="mccf_foundationFellowPaymentPreference_auto" value="autopay">
											<span class="labelText">Auto-pay with card provided</span>
										</label>
										<label class="radio" for="mccf_foundationKeyFellowPaymentPreference_invoice">
											<input type="radio" class="" <cfif local.mccf_foundationFellowPaymentPreference eq "invoice">checked="checked"</cfif> name="mccf_foundationFellowPaymentPreference" id="mccf_foundationFellowPaymentPreference_invoice" value="invoice">
											<span class="labelText">Send me the invoice</span>
										</label>
									</div>
								</div>
							</div>
						</div>

						<cfif local.eligibleForKeyFellows>

							<cfset local.mccf_foundationKeyFellowSelected = 0>
							<cfset local.mccf_foundationKeyFellowRate = 0>
							<cfset local.mccf_foundationKeyFellowPaymentPreference = 0>
							<cfif structKeyExists(arguments.rc, "mccf_foundationKeyFellowCheckbox")>
								<cfset local.mccf_foundationKeyFellowSelected = 1>
							</cfif>
							<cfif structKeyExists(arguments.rc, "mccf_foundationKeyFellowRate")>
								<cfset local.mccf_foundationKeyFellowRate = arguments.rc.mccf_foundationKeyFellowRate>
							</cfif>
							<cfif structKeyExists(arguments.rc, "mccf_foundationKeyFellowPaymentPreference")>
								<cfset local.mccf_foundationKeyFellowPaymentPreference = arguments.rc.mccf_foundationKeyFellowPaymentPreference>
							</cfif>

							<div class="well subAddonWrapper" id="foundationKeyFellow">
								<legend>Foundation Key Fellow</legend>
								<cfif len(variables.strPageFields.foundationKeyFellowText)>
									<div id="foundationKeyFellowText">#variables.strPageFields.foundationKeyFellowText#</div><br/>
								</cfif>

								<div class="addonMessageArea"></div>
								<div>
									<div class="">
										<label class="checkbox subLabel" for="mccf_foundationKeyFellowCheckbox">
										<input class="subCheckbox" type="checkbox" <cfif local.mccf_foundationKeyFellowSelected>checked="checked"</cfif> name="mccf_foundationKeyFellowCheckbox" id="mccf_foundationKeyFellowCheckbox" value="1"> 
											Foundation Key Fellow (for current Fellows only)
											<span class="selectedRate" id="mccf_foundationKeyFellow_selectedRate">
											</span>
										</label>
										<div style="margin-left:15px;" class="subAvailableRates subRatesDisabled">
											<label class="radio subRateLabel" for="mccf_foundationKeyFellow_1000">
												<input type="radio" class="subRateCheckbox" <cfif local.mccf_foundationKeyFellowRate eq 1000>checked="checked"</cfif> name="mccf_foundationKeyFellowRate" id="mccf_foundationKeyFellow_1000" value="1000">
												<span class="labelText">$1000 one-time payment</span>
											</label>
											<label class="radio subRateLabel" for="mccf_foundationKeyFellow_500">
												<input type="radio" class="subRateCheckbox" <cfif local.mccf_foundationKeyFellowRate eq 500>checked="checked"</cfif> name="mccf_foundationKeyFellowRate" id="mccf_foundationKeyFellow_500" value="500">
												<span class="labelText">$500 annually for 2 years</span>
											</label>
											<label class="radio subRateLabel" for="mccf_foundationKeyFellow_250">
												<input type="radio" class="subRateCheckbox" <cfif local.mccf_foundationKeyFellowRate eq 250>checked="checked"</cfif> name="mccf_foundationKeyFellowRate" id="mccf_foundationKeyFellow_250" value="250">
												<span class="labelText">$250 annually for 4 years</span>
											</label>

											<p>We will invoice for for Foundation Key Fellow separately. Please select your preference.</p>

											<label class="radio" for="mccf_foundationFellowPaymentPreference_auto">
												<input type="radio" class="" <cfif local.mccf_foundationKeyFellowPaymentPreference eq "autopay">checked="checked"</cfif> name="mccf_foundationKeyFellowPaymentPreference" id="mccf_foundationKeyFellowPaymentPreference_auto" value="Auto-pay with card provided">
												<span class="labelText">Auto-pay with card provided</span>
											</label>
											<label class="radio" for="mccf_foundationKeyFellowPaymentPreference_invoice">
												<input type="radio" class="" <cfif local.mccf_foundationKeyFellowPaymentPreference eq "invoice">checked="checked"</cfif> name="mccf_foundationKeyFellowPaymentPreference" id="mccf_foundationKeyFellowPaymentPreference_invoice" value="Send me the invoice">
												<span class="labelText">Send me the invoice</span>
											</label>


										</div>
									</div>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		<cfscript>
			local.strResult = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);



		</cfscript>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>


			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>


			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>


		<cfscript>
			local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc);

			// test for eligibility for additional non-subscription options
			
			local.eligibleForKeyFellows = variables.objCustomPageUtils.mem_getGroups(
				memberID=variables.useMID,
				orgID=variables.orgID,
				groupCode='keyFellows_credentialing'
			).recordcount;


		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >


		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.CCPayProfileCode, variables.strPageFields.CheckPayProfileCode ]>
			<cfset local.strReturn = 
				variables.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>


		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				

 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub"
					>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
				<br/>

				<strong>Foundation Fellow</strong><br/>
				<cfif structKeyExists(arguments.rc, 'mccf_foundationFellowCheckbox') and structKeyExists(arguments.rc, 'mccf_foundationFellowRate')>
					<cfswitch expression="#arguments.rc.mccf_foundationFellowRate#">
						<cfcase value="1500">
							$1500 one-time payment (billed separately)
						</cfcase>
						<cfcase value="500">
							$500 annually for 3 years (billed separately)
						</cfcase>
						<cfcase value="300">
							$300 annually for 5 years (billed separately)
						</cfcase>
					</cfswitch>
					<br/>
					<cfif structKeyExists(arguments.rc, "mccf_foundationFellowPaymentPreference")>
						<cfswitch expression="#arguments.rc.mccf_foundationFellowPaymentPreference#">
							<cfcase value="autopay">
								Auto-pay with card provided
							</cfcase>
							<cfcase value="invoice">
								Send me the invoice
							</cfcase>
						</cfswitch>
						<br/>
					</cfif>
				<cfelse>
					No Selections Made
				</cfif>
				<br/>

				<cfif local.eligibleForKeyFellows>
					<strong>Foundation Key Fellow</strong><br/>
					<cfif structKeyExists(arguments.rc, 'mccf_foundationKeyFellowCheckbox') and structKeyExists(arguments.rc, 'mccf_foundationKeyFellowRate')>
						<cfswitch expression="#arguments.rc.mccf_foundationKeyFellowRate#">
							<cfcase value="1000">
								$1000 one-time payment (billed separately)
							</cfcase>
							<cfcase value="500">
								$500 annually for 2 years (billed separately)
							</cfcase>
							<cfcase value="250">
								$250 annually for 4 years (billed separately)
							</cfcase>
						</cfswitch>
						<br/>
						<cfif structKeyExists(arguments.rc, "mccf_foundationKeyFellowPaymentPreference")>
							<cfswitch expression="#arguments.rc.mccf_foundationKeyFellowPaymentPreference#">
								<cfcase value="autopay">
									Auto-pay with card provided
								</cfcase>
								<cfcase value="invoice">
									Send me the invoice
								</cfcase>
							</cfswitch>
							<br/>
						</cfif>
					<cfelse>
						No Selections Made
					</cfif>
					<br/>
				</cfif>
			</div>
			<br/>

			


			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>

			var local = structNew();
			local.returnstruct = structNew();
			variables.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);


			// test for eligibility for additional non-subscription options
			
			local.eligibleForKeyFellows = variables.objCustomPageUtils.mem_getGroups(
				memberID=variables.useMID,
				orgID=variables.orgID,
				groupCode='keyFellows_credentialing'
			).recordcount;


			if (structKeyExists(arguments.rc, 'mccf_foundationFellowCheckbox') and structKeyExists(arguments.rc, 'mccf_foundationFellowRate') and structKeyExists(arguments.rc, "mccf_foundationFellowPaymentPreference")) {

				switch (lcase(arguments.rc.mccf_foundationFellowPaymentPreference)) {
					case "autopay":
						local.mccf_foundationFellowHistoryCategory = "Auto-Pay";
						break;
					case "invoice":
						local.mccf_foundationFellowHistoryCategory = "Send Invoice";
						break;
					default:
						local.mccf_foundationFellowHistoryCategory = "";
				}
				local.mh_fellow = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='FoundationFellow', subName=local.mccf_foundationFellowHistoryCategory);
				local.numPayments = (1500/arguments.rc.mccf_foundationFellowRate);

				local.historyID = variables.objCustomPageUtils.mh_addHistory(
					memberID=variables.useMID, 
					categoryID=local.mh_fellow.categoryID, 
					subCategoryID=local.mh_fellow.subCategoryID,
					description='', 
					qty=local.numPayments,
					dollarAmt=arguments.rc.mccf_foundationFellowRate,
					enteredByMemberID=variables.useMID,
					newAccountsOnly=false
				);
			}

			if (local.eligibleForKeyFellows and structKeyExists(arguments.rc, 'mccf_foundationKeyFellowCheckbox') and structKeyExists(arguments.rc, 'mccf_foundationKeyFellowRate') and structKeyExists(arguments.rc, "mccf_foundationKeyFellowPaymentPreference")) {

				switch (lcase(arguments.rc.mccf_foundationKeyFellowPaymentPreference)) {
					case "autopay":
						local.mccf_foundationKeyFellowHistoryCategory = "Auto-Pay";
						break;
					case "invoice":
						local.mccf_foundationKeyFellowHistoryCategory = "Send Invoice";
						break;
					default:
						local.mccf_foundationKeyFellowHistoryCategory = "";
				}
				local.mh_keyFellow = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='FoundationKeyFellow', subName=local.mccf_foundationKeyFellowHistoryCategory);
				local.numPayments = (1000/arguments.rc.mccf_foundationKeyFellowRate);

				local.historyID = variables.objCustomPageUtils.mh_addHistory(
					memberID=variables.useMID, 
					categoryID=local.mh_keyFellow.categoryID, 
					subCategoryID=local.mh_keyFellow.subCategoryID,
					description='', 
					qty=local.numPayments,
					dollarAmt=arguments.rc.mccf_foundationKeyFellowRate,
					enteredByMemberID=variables.useMID,
					newAccountsOnly=false
				);
			}
			//create subscriptions
			local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID);

			local.subStructResults = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID, siteID=variables.siteID, orgID=variables.orgID)>
				
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>


		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>


		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset variables.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset variables.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.CCPayProfileCode>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.CCPayProfileCode>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strFieldSetMembership = variables.objCustomPageUtils.renderFieldSet(uid='87762F07-DE39-4FD7-A6A9-261EECDFC04C', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetEducation = variables.objCustomPageUtils.renderFieldSet(uid='F905930D-4AAD-4379-9D9D-379B6FC59ADE', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetLicenses = variables.objCustomPageUtils.renderFieldSet(uid='00B26D32-21B6-4D94-A3EF-0BC28828D479', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetProInfo = variables.objCustomPageUtils.renderFieldSet(uid='6D42CE7A-FF36-4F1D-A693-17C6505AFEDF', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetBusinessAddress = variables.objCustomPageUtils.renderFieldSet(uid='955C31D7-67C9-4ACA-A790-279C59DD11B8', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetHomeAddress = variables.objCustomPageUtils.renderFieldSet(uid='557A94C0-9744-414C-9E3F-7C649FE14058', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetAddressPrefs = variables.objCustomPageUtils.renderFieldSet(uid='AF092242-73B0-4084-A0F3-36CDAE38D9C5', mode="confirmation", strData=arguments.rc)>



		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>


		<cfscript>
			local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc);

			// test for eligibility for additional non-subscription options
		
			local.eligibleForKeyFellows = variables.objCustomPageUtils.mem_getGroups(
				memberID=variables.useMID,
				orgID=variables.orgID,
				groupCode='keyFellows_credentialing'
			).recordcount;

		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >


		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.contentConfirmation)>
				<div id="contentConfirmation">#variables.strPageFields.contentConfirmation#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<p>Here are the details of your application:</p>

			#local.strFieldSetMembership.fieldSetContent#
			#local.strFieldSetEducation.fieldSetContent#
			#local.strFieldSetLicenses.fieldSetContent#
			#local.strFieldSetProInfo.fieldSetContent#
			#local.strFieldSetBusinessAddress.fieldSetContent#
			#local.strFieldSetHomeAddress.fieldSetContent#
			#local.strFieldSetAddressPrefs.fieldSetContent#

			<br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#local.strResult.formContent#
					<br/>


					<strong>Foundation Fellow</strong><br/>
					<cfif structKeyExists(arguments.rc, 'mccf_foundationFellowCheckbox') and structKeyExists(arguments.rc, 'mccf_foundationFellowRate')>
						<cfswitch expression="#arguments.rc.mccf_foundationFellowRate#">
							<cfcase value="1500">
								$1500 one-time payment (billed separately)
							</cfcase>
							<cfcase value="500">
								$500 annually for 3 years (billed separately)
							</cfcase>
							<cfcase value="300">
								$300 annually for 5 years (billed separately)
							</cfcase>
						</cfswitch>
						<br/>
						<cfif structKeyExists(arguments.rc, "mccf_foundationFellowPaymentPreference")>
							<cfswitch expression="#arguments.rc.mccf_foundationFellowPaymentPreference#">
								<cfcase value="autopay">
									Auto-pay with card provided
								</cfcase>
								<cfcase value="invoice">
									Send me the invoice
								</cfcase>
							</cfswitch>
							<br/>
						</cfif>
					<cfelse>
						No Selections Made
					</cfif>
					<br/>

					<cfif local.eligibleForKeyFellows>
						<strong>Foundation Key Fellow</strong><br/>
						<cfif structKeyExists(arguments.rc, 'mccf_foundationKeyFellowCheckbox') and structKeyExists(arguments.rc, 'mccf_foundationKeyFellowRate')>
							<cfswitch expression="#arguments.rc.mccf_foundationKeyFellowRate#">
								<cfcase value="1000">
									$1000 one-time payment (billed separately)
								</cfcase>
								<cfcase value="500">
									$500 annually for 2 years (billed separately)
								</cfcase>
								<cfcase value="250">
									$250 annually for 4 years (billed separately)
								</cfcase>
							</cfswitch>
							<br/>
							<cfif structKeyExists(arguments.rc, "mccf_foundationKeyFellowPaymentPreference")>
								<cfswitch expression="#arguments.rc.mccf_foundationKeyFellowPaymentPreference#">
									<cfcase value="autopay">
										Auto-pay with card provided
									</cfcase>
									<cfcase value="invoice">
										Send me the invoice
									</cfcase>
								</cfswitch>
								<br/>
							</cfif>
						<cfelse>
							No Selections Made
						</cfif>
						<br/>
					</cfif>
				</div>
				<br/>
				<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
				<br/>
				</td>
			</tr>
			</table>

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from},
			emailto=[{ name="", email=variables.memberEmail.to}],
			emailreplyto= variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = variables.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to CCBA", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.errActiveFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errAcceptedFound#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.errBilledFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>					
