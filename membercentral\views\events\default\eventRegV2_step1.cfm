<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function validateAndSearchReg() {
			var #toScript(application.regEx.email, "mc_emailregex")#
			var emailRegEx = new RegExp(mc_emailregex,"i");
			$('##err_frmEvRegIdentifier').html('').hide();
			$('##btnSearchReg').html('<i class="icon-spin icon-spinner"></i> loading...').prop('disabled',true);

			$('##frmEvRegIdentifier input[type="text"]').each(function() {
				$(this).val($(this).val().trim());
			});

			let arrReq = [];
			if (!$('##fEvRegFirstName').val().length) arrReq.push('Enter the First Name.');
			if (!$('##fEvRegLastName').val().length) arrReq.push('Enter the Last Name.');
			if (!$('##fEvRegEmail').val().length || !(emailRegEx.test($('##fEvRegEmail').val()))) arrReq.push('Enter a valid Email Address.');

			if (arrReq.length) {
				onErrorSearchReg(arrReq.join('<br/>'));
				return false;
			} else {
				$.getJSON('#arguments.event.getValue("locatorurl")#', $('##frmEvRegIdentifier').serializeArray())
					.done(showIdentifiedRegistrants)
					.fail(showIdentifiedRegistrants);
			}
		}
		function showIdentifiedRegistrants(respObj) {
			if (respObj.success) {
				if (!respObj.arrMembers.length) {
					showNewRegForm();
				} else {
					let evRegTemplate = Handlebars.compile($('##mc_evIdentifiedReg_template').html());
					$('##EvRegIdentifierFormContainer').hide();
					$('##EvRegIdentifierResults').html(evRegTemplate(respObj)).show(300);
				}
			} else {
				onErrorSearchReg('There was a problem displaying the data. Try again!');
			}
		}
		function onErrorSearchReg(msg) {
			$('##err_frmEvRegIdentifier').html(msg).show();
			$('##btnSearchReg').html('Continue').prop('disabled',false);
		}
		function useMember(mid,regselmode) {
			self.location.href='#arguments.event.getValue('mainregurl')#&regaction=usemid&mid=' + mid + (regselmode ? '&regselmode='+regselmode : '');
		}
		function showNewRegForm() {
			<cfif len(arguments.event.getTrimValue('mc_siteinfo.alternateGuestAccountCreationLink','')) gt 0>
				self.location.href='#arguments.event.getTrimValue('mc_siteinfo.alternateGuestAccountCreationLink')#';
			<cfelse>
				$('##EvRegIdentifierFormContainer').hide();
				$('##EvRegIdentifierResults')
					.html('<img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...')
					.load('#arguments.event.getValue('newregacctformurl')#')
					.show();
			</cfif>
		}
		function useNA() {
			self.location.href='#arguments.event.getValue("mainregurl")#';
		}
		function goToRegCart() {
			self.location.href='#arguments.event.getValue('mainurl')#&regcartv2';
		}
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<fieldset class="tsApp" style="padding:20px 10px;">
	<div id="EvRegIdentifierFormContainer" class="tsAppBodyText">
		<legend class="tsApp tsAppLegendTitle">Welcome to Express Registration!</legend>
		<div style="margin-bottom:18px;">Enter registrant's First Name, Last Name, and Email<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>, or <a href="/?pg=login">login</a> </cfif> to register:</div>

		<div id="err_frmEvRegIdentifier" class="alert tsAppBodyText" style="display:none;margin:10px 0;"></div>

		<form name="frmEvRegIdentifier" id="frmEvRegIdentifier" onsubmit="validateAndSearchReg();return false;" autocomplete="off">
			<table class="tsAppBodyText">
				<tr>
					<td class="tsAppBodyText">First Name: <span class="evreg-text-danger">*</span></td>
					<td class="tsAppBodyText">
						<input type="text" name="fEvRegFirstName" id="fEvRegFirstName" value="" size="24">
					</td>
				</tr>
				<tr>
					<td class="tsAppBodyText">Last Name: <span class="evreg-text-danger">*</span></td>
					<td class="tsAppBodyText">
						<input type="text" name="fEvRegLastName" id="fEvRegLastName" value="" size="24">
					</td>
				</tr>
				<tr>
					<td class="tsAppBodyText">Email: <span class="evreg-text-danger">*</span></td>
					<td class="tsAppBodyText">
						<input type="text" name="fEvRegEmail" id="fEvRegEmail" value="" size="24">
					</td>
				</tr>
				<tr>
					<td></td>
					<td class="tsAppBodyText">
						<button type="submit" name="btnSearchReg" id="btnSearchReg" class="tsAppBodyButton">Continue</button>
						<a href="##" onclick="doCancelEventReg();return false;" style="margin-left:28px;">Cancel</a>
						<div class="evreg-font-size-sm evreg-mt-2 evreg-text-dim">
							<span class="evreg-text-danger">*</span> = required
						</div>
					</td>
				</tr>
			</table>
		</form>
	</div>
	<div id="EvRegIdentifierResults" style="display:none;"></div>
</fieldset>

<script id="mc_evIdentifiedReg_template" type="text/x-handlebars-template">
	<legend class="tsApp tsAppLegendTitle evreg-mb-3">
		You may already have an account.
	</legend>
	{{##each arrMembers}}
		<div class="evreg-mb-3" style="padding:10px;border:1px solid ##ccc;border-radius:6px;">
			<div class="evreg-d-flex">
				<div class="evreg-col">
					<div class="evreg-d-flex">
						<div class="evreg-mr-3" style="width:160px;">
							{{##switch evRegStatus}}
								{{##case 'NC'}}<a href="javascript:goToRegCart();" class="evreg-text-decoration-none" style="border-bottom: 1px solid;">Registration Pending</a>{{/case}}
								{{##case 'NA'}}<span class="evreg-text-danger evreg-font-size-xs">Already Registered</span>{{/case}}
								{{##case 'NQ'}}<span class="evreg-text-danger evreg-font-size-xs">Not Eligible for Registration</span>{{/case}}
								{{##default}}
									<button type="button" class="tsAppBodyButton" onclick="useMember({{memberID}})">Register</button>
								{{/default}}
							{{/switch}}
						</div>
						{{##if ../showMemberPhoto}}
							<div class="evreg-mr-3">
								{{##if hasPhoto}}
									<img style="max-width:80px;" src="/memberphotosth/{{memberphoto}}">
								{{else}}
									<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
								{{/if}}
							</div>
						{{/if}}
						<div>
							<b>{{mc_combinedName}}</b>
							<div class="evreg-mt-1">
								<div>{{company}}</div>
								{{{mc_combinedAddresses}}}
								{{{mc_extraInfo}}}
							</div>
						</div>
					</div>
				</div>
				<div class="evreg-col-auto">
					{{##compare mc_recordType.length '>' 0}}{{mc_recordType}}<br/>{{/compare}}
					{{##compare mc_memberType.length '>' 0}}{{mc_memberType}}<br/>{{/compare}}
					{{##compare mcaccountstatus '==' 'I'}}<span class="evreg-text-danger evreg-font-weight-bold">ACCOUNT INACTIVE</span><br/>{{/compare}}
					{{##compare mcaccountstatus '!=' 'I'}}
						{{##compare mc_memberStatus.length '>' 0}}{{mc_memberStatus}}<br/>{{/compare}}
					{{/compare}}
					{{##compare mc_lastlogin.length '>' 0}}{{{mc_lastlogin}}}<br/>{{/compare}}
				</div>	
			</div>
		</div>
	{{/each}}
	<div class="tsAppLegendTitle evreg-mt-3">
		Not you? <a href="javascript:showNewRegForm();" title="Create New Account">Create a New Account</a> to register.
	</div>
</script>
</cfoutput>