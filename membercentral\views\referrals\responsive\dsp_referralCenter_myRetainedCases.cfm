<cfoutput>
<div class="form-group row-fluid">
	<div class="span12" style="text-align:left;">
		<h4><b>My Retained Cases</b></h4>
	</div>
</div>
<div class="form-group row-fluid">
	<div class="span12">
		<form class="form-inline refFilterForm" action="/" method="get">
			<input type="hidden" name="pg" value="referrals">
			<input type="hidden" name="panel" value="browse">
			<input type="hidden" name="tab" value="#variables.structData.tabToShow#">
			<input type="hidden" name="sort" value="#variables.structData.sort#">
			<input type="hidden" name="count" value="#variables.structData.count#">
			<input type="hidden" name="orderBy" value="#variables.structData.orderBy#">
			<input type="hidden" name="start" value="1">
			<label class="refDateRange"><br/>
				<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
					<option <cfif variables.structData.refDateRanges eq 0 > selected </cfif> value="0">My Retained Cases</option>
					<option <cfif variables.structData.refDateRanges eq 30 > selected </cfif>  value="30">Last 30 Days</option>
					<option <cfif variables.structData.refDateRanges eq 60 > selected </cfif> value="60">Last 60 Days</option>
					<option <cfif variables.structData.refDateRanges eq 90 > selected </cfif> value="90">Last 90 Days</option>
				</select>
			</label>
			<label class="refferalID"><b>Referral ID</b><br/>
				<input type="text" name="filterReferralID" class="input-small refferalID" <cfif variables.structData.filterReferralID GT 0> value="#variables.structData.filterReferralID#" </cfif>>
			</label>
			<label class="clientLastName"><b>Client Last Name</b><br/>
				<input type="text" name="filterClientLastName" class="input-medium clientLastName" value="#variables.structData.filterClientLastName#">
			</label>
			<label class="clientFirstName"><b>Client First Name</b><br/>
				<input type="text" name="filterClientFirstName" class="input-medium clientFirstName" value="#variables.structData.filterClientFirstName#">
			</label>
			<label class="Status"><b>Status</b><br/>
				<select class="Status input-medium" name="filterStatus">
					<option value="0"></option>
					<cfloop query="variables.structData.qryGetReferralCaseStatus">
						<option <cfif variables.structData.filterStatus eq variables.structData.qryGetReferralCaseStatus.clientReferralStatusID > selected </cfif> value="#variables.structData.qryGetReferralCaseStatus.clientReferralStatusID#">#variables.structData.qryGetReferralCaseStatus.statusName#</option>
					</cfloop>
				</select>
			</label>				
			<label class="feesDue"><b>Fees Due</b><br/>
				<input type="text" name="filterfeesDue" class="input-small feesDue" value="#variables.structData.filterfeesDue#">
			</label>								
			<button type="submit" class="btn filterRefferals">Search</button>
			<button type="reset" class="btn filterClear">Clear Filters</button>
		</form>
	</div>							
</div>
<div class="form-group row-fluid <cfif arrayLen(variables.structData.mrcData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>	
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>		
</div>
<cfif arrayLen(variables.structData.mrcData) gt 0> 
	<div class="dataResp">
		<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.mrcData)#">
			<div class="row-fluid" bgcolor="##DEDEDE">
				<div class="span12 clearfix well well-small">
					<div class="row-fluid">
						<div class="span8 eventLeft list-text">			
							<p>														
								<a href="javascript:editCase(#variables.structData.mrcData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>Referral ##  #variables.structData.mrcData[local.Counter].CLIENTREFERRALID#</b></a>
							</p>
							<p><b>Client Name :</b>  #variables.structData.mrcData[local.Counter].CLIENTNAME#</p>													
							<p><b>Referral Date :</b> #variables.structData.mrcData[local.Counter].clientReferralDate#</p>
							<p><b>Collected from Client to Date :</b> #dollarFormat(variables.structData.mrcData[local.Counter].collectedFeeTotal)#</p>
							<p><b>Referral Fees :</b> #dollarFormat(variables.structData.mrcData[local.Counter].referralDuesTotal)#</p>
							<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
								<p><b>Client Fees :</b> #dollarFormat(variables.structData.mrcData[local.Counter].totalClientFee)#</p>
							</cfif>
							<p><b>Total Fees Due :</b> #dollarFormat(variables.structData.mrcData[local.Counter].totalFeesDue)#</p>
							<p><b>Fees Paid to Date :</b> #dollarFormat(variables.structData.mrcData[local.Counter].totalFeesPaid)#</p>
						</div>												
						<div class="span4 " style="text-align:right">
							<cfif variables.structData.mrcData[local.Counter].totalFeesDue gt 0>
								<a href="javascript:applyPayment(#variables.structData.mrcData[local.Counter].clientReferralID#)" title="Apply Payment to this Case" style="margin-right:4px;"><i class="icon-money"></i></a>
							<cfelse>
								<a href="javascript:void(0);"  ><img src="/assets/common/images/spacer.gif" /></a>
							</cfif>
							<a href="javascript:sendStatement(#variables.structData.mrcData[local.Counter].clientReferralID#)" title="View and E-mail Statement" ><i class="icon-envelope"></i></a>
						</div>
					</div>											
				</div>
			</div>
		</cfloop>	
	</div>
	<div class="row-fluid  dataTable hide">
		<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
			<tr class="gridHead">
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referralid">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				
				<td class="sortColumn" data-sort="referralid" data-order="#local.order#"><b>Referral ID##</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "clientname">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				
				<td class="sortColumn" data-sort="clientname" data-order="#local.order#"><b>Client Name</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referraldate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="referraldate" data-order="#local.order#"><b>Referral Date</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "collectedfromdate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="collectedfromdate" data-order="#local.order#"><b>Collected from Client to Date</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "reffees">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="reffees" data-order="#local.order#"><b>Referral Fees</b>&nbsp;<i class="fa fa-sort"></i></td>

				<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
					<cfset local.order = 'desc'>
					<cfif variables.structData.sort EQ "clientfees">
						<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
					</cfif>
					<td class="sortColumn" data-sort="clientfees" data-order="#local.order#"><b>Client Fees</b>&nbsp;<i class="fa fa-sort"></i></td>
				</cfif>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "feesdue">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="feesdue" data-order="#local.order#"><b>Total Fees Due</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "feespaidtodate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="feespaidtodate" data-order="#local.order#"><b>Fees Paid to Date</b>&nbsp;<i class="fa fa-sort"></i></td>
				<td></td>
			</tr>
			<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.mrcData)#">
				<tr class="gridData">
					<td width="140" style="text-align:left;">
						<a href="javascript:editCase(#variables.structData.mrcData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>#variables.structData.mrcData[local.Counter].CLIENTREFERRALID#</b></a>
					</td>
					<td width="175" style="text-align:left;">
						#variables.structData.mrcData[local.Counter].CLIENTNAME#
					</td>
					<td width="175" style="text-align:left;">
						#variables.structData.mrcData[local.Counter].clientReferralDate#
					</td>
					<td width="190" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].collectedFeeTotal)#</td>
					<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
						<td width="130" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].referralDuesTotal)#</td>
						<td width="130" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].totalClientFee)#</td>
						<td width="130" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].totalFeesDue)#</td>
						<td width="135" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].totalFeesPaid)#</td>
					<cfelse>
						<td width="175" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].referralDuesTotal)#</td>
						<td width="175" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].totalFeesDue)#</td>
						<td width="175" style="text-align:left;">#dollarFormat(variables.structData.mrcData[local.Counter].totalFeesPaid)#</td>
					</cfif>
					<td width="175" style="text-align:left;">
						<cfif variables.structData.mrcData[local.Counter].totalFeesDue gt 0>
							<a href="javascript:applyPayment(#variables.structData.mrcData[local.Counter].clientReferralID#)" title="Apply Payment to this Case" style="margin-right:4px;"><i class="icon-money"></i></a>
						<cfelse>
							<a href="javascript:void(0);" class="invisible" style="margin-right:4px;"><i class="icon-money"></i></a>
						</cfif>
						<a href="javascript:sendStatement(#variables.structData.mrcData[local.Counter].clientReferralID#)" title="View and E-mail Statement" ><i class="icon-envelope"></i></a>
					</td>												
				</tr>											
			</cfloop>
		</table>							
	</div>								
</cfif>	
<div class="form-group row-fluid <cfif arrayLen(variables.structData.mrcData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>		
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>		
</div>
<div class="row-fluid <cfif arrayLen(variables.structData.mrcData) gt 0> hide</cfif> noReferrals" bgcolor="##DEDEDE">
	<div class="span12 clearfix well well-small">
		<div class="row-fluid">
			<p>No Retained Cases match this criteria.</p>
		</div>
	</div>
</div>
</cfoutput>