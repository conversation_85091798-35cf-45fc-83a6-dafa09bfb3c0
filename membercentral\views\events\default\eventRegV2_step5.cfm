<cfinclude template="../commonEventRegStep5JS.cfm">

<cfoutput>
<div class="evreg-card evreg-mt-3 evreg-p-3 tsAppBodyText">
	<form name="frmEventRegStep5" id="frmEventRegStep5" enctype="multipart/form-data" autocomplete="off"<cfif local.evRegV2.currentReg.currentStep NEQ 3> style="display:none;"</cfif>>		
		<input type="hidden" name="evRegistrantRoles" id="evRegistrantRoles" value="#valueList(local.qrySelectedRegistrantRoles.categoryID)#">

		<div class="evreg-mb-3 evreg-font-size-lg evreg-font-weight-bold">Event Roles:</div>
		
		<cfloop query="local.qrySelectedRegistrantRoles">
			<cfif local.strRoleFields[local.qrySelectedRegistrantRoles.categoryID].hasFields>
				<cfoutput>
				<div class="MCRoleCategoryFieldsWrapper" id="MCRoleCategory#local.qrySelectedRegistrantRoles.categoryID#">
					<div class="MCRoleCategoryButtonBar">
		 				<button type="button" class="btn btn-small MCShowRoleCategoryFieldsLink" data-linkedrolecategory="MCRoleCategory#local.qrySelectedRegistrantRoles.categoryID#"><i class="icon-chevron-up"></i></button> 
						<button type="button" class="btn btn-small MCHideRoleCategoryFieldsLink" data-linkedrolecategory="MCRoleCategory#local.qrySelectedRegistrantRoles.categoryID#"><i class="icon-chevron-down"></i></button>
					</div>
					<div class="MCRoleCategoryTitleWrapper">
						<b>Event Role: #local.qrySelectedRegistrantRoles.categoryName#</b>
					</div>
					<div class="MCRoleFieldsWrapper evreg-p-3">
						#local.strRoleFields[local.qrySelectedRegistrantRoles.categoryID].html#
					</div>
				</div>
				</cfoutput>
			</cfif>
		</cfloop>

		<div class="evreg-mt-5">
			<div id="step5Err" class="alert" style="display:none;"></div>
			<button type="button" name="btnSaveStep5" id="btnSaveStep5" class="tsAppBodyButton" onclick="doS5ValidateAndSave();">
				Save Changes
			</button>
		</div>
		<div id="evRegStep5SaveLoading" style="display:none;"></div>
	</form>
	<div id="evRegStep5Summary" class="evRegStepSummary evreg-cursor-pointer" data-evregsummarystep="5"<cfif local.evRegV2.currentReg.currentStep EQ 5> style="display:none;"</cfif>>
		<div class="evreg-d-flex">
			<a href="##" class="evreg-align-self-center evreg-mr-2 evreg-font-size-lg evreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="evreg-col">
				<div class="evreg-font-size-lg evreg-font-weight-bold">Event Roles: #valueList(local.qrySelectedRegistrantRoles.categoryName).replaceAll(',',', ')#</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>