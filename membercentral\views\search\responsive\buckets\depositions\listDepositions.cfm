<cfoutput>
<div id="#local.uniqueID#">
	<div class="bk-d-flex well bk-p-2 bk-mb-2 bk-border-gray">
		<span>
			<cfif local.returnStruct.totalCount GT local.returnStruct.count>
				Showing #local.returnStruct.startPos#-<cfif ((local.returnStruct.startPos + local.returnStruct.count) - 1) lte local.returnStruct.totalCount>#local.returnStruct.startPos + local.returnStruct.count - 1#<cfelse>#local.returnStruct.totalCount#</cfif> of #NumberFormat(local.returnStruct.totalCount,",")# Depositions
			<cfelse>
				Showing #local.returnStruct.totalCount# Deposition<cfif local.returnStruct.totalCount GT 1>s</cfif>
			</cfif>
		</span>
		<span class="bk-ml-auto">
			<ul class="pager bk-m-0">
				<cfif local.returnStruct.numCurrentPage GT 1>
					<li class="bk-font-size-sm">
						<a href="##" class="bucketPagingButtonPreviousPage bk-mr-2" onclick="b_getExpertDepositions(#local.returnStruct.startPos - local.returnStruct.count#,this);return false;"><strong>Previous</strong></a>
					</li>
				</cfif>
				<cfif local.returnStruct.numCurrentPage NEQ local.returnStruct.endPage>
					<li class="bk-font-size-sm">
						<a href="##" class="bucketPagingButtonNextPage" onclick="b_getExpertDepositions(#local.returnStruct.startPos + local.returnStruct.count#,this);return false;"><strong>Next</strong></a>
					</li>
				</cfif>
			</ul>
		</span>
	</div>

	<cfloop query="local.qryDepositions">
		<cfset local.objDocument.load(local.qryDepositions.documentid)>
		<cfset local.mycost = local.objDocument.getPrice(membertype=variables.cfcuser_membertype, billingstate=variables.cfcuser_billingstate,
				billingzip=variables.cfcuser_billingzip, websiteorgcode=session.mcstruct.sitecode, depomemberdataid=variables.cfcuser_depomemberdataid)>
		<cfset local.docCaseRefDescTxt = '#trim(local.qryDepositions.expertname)# from #DateFormat(local.qryDepositions.DocumentDate, "m/d/yyyy")# - #replace(dollarformat(local.mycost.price),".00","")#'>

		<cfset local.inCartClass = local.qryDepositions.inCart EQ 1 ? "" : "hide">
		<cfset local.addToCartClass = local.qryDepositions.inCart EQ 1 ? "hide" : "">

		<div class="tsDocViewerDocument s_row well bk-p-2 bk-mb-2 bk-border-gray" data-tsdocumentid="#local.qryDepositions.documentid#">
			<div class="bk-d-flex">
				<div class="bk-col">
					<a href="javascript:docViewer(#local.qryDepositions.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchID#);" title="Click to view document"><img src="#application.paths.images.url#images/search/preview_icon.png" width="20" height="20" border="0" align="left" class="hidden-phone"/><b><span class="hidden-phone">&nbsp;</span><cfif len(trim(local.qryDepositions.expertname))>#trim(local.qryDepositions.expertname)#<cfelse>Unknown Expert</cfif></b> <small>(Click to Preview)</small></a>
					<div class="s_dtl">
						Document ###local.qryDepositions.documentid# - #DateFormat(local.qryDepositions.DocumentDate, "mmm d, yyyy")# - #local.qryDepositions.jurisdiction# <cfif local.qryDepositions.pages gt 0>- #local.qryDepositions.pages# pages</cfif><br/>
						#local.qryDepositions.style# ... <cfif len(local.qryDepositions.causedesc)>... #local.qryDepositions.causedesc#</cfif>
						<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryDepositions.owned>
							<cfif len(local.qryDepositions.notes)><br/>Notes: #local.qryDepositions.notes#</cfif>
							<br/>Contributed by: <cfif len(trim(local.qryDepositions.email))><a href="mailto:#trim(local.qryDepositions.email)#"></cfif>#local.qryDepositions.FirstName# #local.qryDepositions.LastName#<cfif len(trim(local.qryDepositions.email))></a></cfif>, 
							<cfset local.tmp = "">
							<cfif len(local.qryDepositions.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryDepositions.billingfirm)></cfif>
							<cfif len(local.qryDepositions.phone)><cfset local.tmp = listAppend(local.tmp,"Ph: " & local.qryDepositions.phone)></cfif>
							#replace(local.tmp,",",", ","ALL")#<br/>
						<cfelse>
							<br/>Notes: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
							<br/>Contributed by: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
						</cfif>
					</div>
				</div>
				<div class="bk-col-auto p-3">
					<div class="s_opt">
						<div class="p-2 text-center #local.inCartClass#" id="s_incart#local.qryDepositions.documentID#">
							<button type="button" class="btn btn-link btn-sm text-center" onClick="viewCart();" style="width:150px;">
								<i class="icon-shopping-cart icon-large bk-mr-2"></i>In Cart
							</button>
						</div>
						<cfif local.qryDepositions.contributed>
							<div class="p-2 text-center bk-font-size-md">
								<i class="fa fa-check-square text-success" aria-hidden="true"></i> Contributed
							</div>
						<cfelseif not local.qryDepositions.owned>
							<div class="p-3 text-center #local.addToCartClass#" id="s_addtocart#local.qryDepositions.documentID#">
								<div class="bk-mb-1">#replace(dollarformat(local.mycost.price),".00","")#</div>
								<button name="addCart_btn_#local.qryDepositions.documentID#" id="addCart_btn_#local.qryDepositions.documentID#" type="button" class="btn btn-secondary btn-sm text-center" onClick="addDepoDocToCartPrompt(#local.qryDepositions.documentid#,#arguments.searchID#);" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" style="width:150px;"><i class="icon-plus-sign" title="Add to Cart"></i> Add to Cart</button>
							</div>
						</cfif>
						<cfif len(local.qryDepositions.uploadpdfdate)>
							<cfset local.fileName = '#local.objDocument.expertFileName#.pdf'>

							<cfif local.qryDepositions.owned>
								<cfset local.onClickFn = "downloadDocImmedate(#local.qryDepositions.DocumentID#,'pdf');">
							<cfelse>
								<cfset local.onClickFn = "oneClickPurchasePrompt(#local.qryDepositions.documentid#,#arguments.searchID#,'#encodeForJavaScript(local.fileName)#','pdf',#local.qryDepositions.currentRow#,'dload');">
							</cfif>
							<div class="p-3 text-center">
								<button type="button" name="pdf_btn_#local.qryDepositions.currentRow#" id="pdf_btn_#local.qryDepositions.currentRow#" class="btn btn-secondary btn-sm" onClick="#local.onClickFn#" style="width:150px;" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" data-downloaddocid="#local.qryDepositions.documentID#" data-downloaddocformat="pdf">
									<i class="icon-download-alt" title="Download PDF"></i> Download PDF
								</button>
							</div>
						</cfif>
						<cfif len(local.qryDepositions.uploadpdfdate) and len(local.dropboxAppKey)>
							<cfif local.qryDepositions.owned>
								<cfset local.onClickFn = "doDropboxSave(#local.qryDepositions.documentID#,'#local.objDocument.expertFileName#.pdf','pdf',#local.qryDepositions.currentRow#);">
							<cfelse>
								<cfset local.onClickFn = "oneClickPurchasePrompt(#local.qryDepositions.documentid#,#arguments.searchID#,'#local.objDocument.expertFileName#.pdf','pdf',#local.qryDepositions.currentRow#,'dbox');">
							</cfif>
							<div class="p-3 text-center">
								<button name="dropb_btn_#local.qryDepositions.currentRow#" id="dropb_btn_#local.qryDepositions.currentRow#" type="button" class="btn btn-secondary btn-sm drop-box-file-list" onClick="#local.onClickFn#" data-elmid="#local.qryDepositions.documentID#" data-elmname="#local.objDocument.expertFileName#.pdf" data-elmext="pdf" data-rowid="#local.qryDepositions.currentRow#" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" data-dropboxdownloaddocid="#local.qryDepositions.documentID#" style="width:150px;"><img src="/assets/common/images/dropbox-icon-ios.png" id="dropb_img_#local.qryDepositions.currentRow#" data-toggle="tooltip" width="15" height="15" border="0" title="Save to Dropbox"/> Save to Dropbox</button>
							</div>
						</cfif>
						<cfif not len(local.qryDepositions.uploadpdfdate)>
							<div class="p-3" id="txt_#local.qryDepositions.documentID#" style="text-align:center;">
								<cfif local.qryDepositions.owned>
									<button name="txt_btn_#local.qryDepositions.currentRow#" type="button" class="btn btn-secondary btn-sm" onClick="downloadDocImmedate(#local.qryDepositions.DocumentID#,'txt');" style="width:150px; margin-top:10px; text-align:center;"><i class="icon-download-alt" title="Download TEXT" data-downloaddocid="#local.qryDepositions.documentID#" data-downloaddocformat="txt"></i> Download TEXT</button>										
								</cfif>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</div>
	</cfloop>

	<cfif local.returnStruct.numTotalPages gt 1>
		<div class="pagination bk-text-center bk-m-0 bk-p-2 bk-mb-2">
			<ul>
				<cfif local.returnStruct.numCurrentPage gt 1>
					<cfif local.returnStruct.numTotalPages GT local.returnStruct.maxPage>
						<li>
							<a href="##" onclick="b_getExpertDepositions(1,this);return false;" title="First Page">&lt;&lt;</a>
						</li>
					</cfif>
					<li>
						<a href="##" onclick="b_getExpertDepositions(#local.returnStruct.startPos - local.returnStruct.count#,this);return false;" title="Previous Page">Previous</a>
					</li>
				</cfif>

				<cfloop from="#local.returnStruct.startPage#" to="#local.returnStruct.endPage#" index="local.i">
					<cfset local.thisStartRow = local.i>
					<cfif local.i gt 1>
						<cfset local.thisStartRow = (local.returnStruct.count * (local.i-1)) + 1>
					</cfif>
					
					<cfif local.returnStruct.numCurrentPage eq local.i>
						<li class="active"><a href="##" onclick="return false;">#local.i#</a></li>
					<cfelse>
						<li><a href="##" onclick="b_getExpertDepositions(#local.thisStartRow#,this);return false;">#local.i#</a></li>
					</cfif>
				</cfloop>
				
				<cfif local.returnStruct.numCurrentPage lt local.returnStruct.numTotalPages>
					<li>
						<a href="##" onclick="b_getExpertDepositions(#local.returnStruct.startPos + local.returnStruct.count#,this);return false;" title="Next Page">
							Next
						</a>
					</li>
					<cfif local.returnStruct.numTotalPages GT local.returnStruct.maxPage>
						<li>
							<a href="##" onclick="b_getExpertDepositions(#(local.returnStruct.count * (local.returnStruct.numTotalPages-1)) + 1#,this);return false;" title="Last Page">
								&gt;&gt;
							</a>
						</li>
					</cfif>
				</cfif>
			</ul>
		</div>
	</cfif>
</div>
<div class="bk-text-right">
	<button type="button" class="btn" onclick="bk_slideToggle('#local.uniqueID#','MCExpertDepositionsContainer',this)">
		<i class="fa fa-chevron-up" title="Show Less"></i><i class="fa fa-chevron-down" title="Expand" style="display:none;"></i>
	</button>
</div>
</cfoutput>