<cfcomponent extends="model.customPage.customPage" output="false">
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			variables.fund = arguments.event.getValue('fund','');
			local.formAction = arguments.event.getValue('fa','showLookup');

			/* ************************* */
			/* Custom Page Custom Fields */
			/* ************************* */
			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Donate" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Page1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Please enter your donation information below." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonationSectionTitle", type="STRING", desc="Title of donation information section", value="Donation Information" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="IndividualOption", type="STRING", desc="Label for Individual Contribution option", value="Individual Contribution" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonorInfoFieldSet", type="STRING", desc="UID of Field Set for Donor Information (Individual)", value="964c4c88-702f-42b2-b8e2-209af7a02d90" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="OrganizationOption", type="STRING", desc="Label for Organization Contribution option", value="Firm/Organization Contribution" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonorInfoFieldSetFirm", type="STRING", desc="UID of Field Set for Donor Information (Organization/Firm)", value="724fb5a7-9a56-4a98-8a7e-2d9b86f03e11" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund1Name", type="STRING", desc="Name of first fund option in dropdown", value="Fund 1" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund1Acct", type="STRING", desc="GL code for one-time Fund 1 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund2Name", type="STRING", desc="Name of second fund option in dropdown", value="Fund 2" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund2Acct", type="STRING", desc="GL code for one-time Fund 2 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund3Name", type="STRING", desc="Name of third fund option in dropdown", value="Fund 3" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund3Acct", type="STRING", desc="GL code for one-time Fund 3 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund4Name", type="STRING", desc="Name of fourth fund option in dropdown", value="Fund 4" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund4Acct", type="STRING", desc="GL code for one-time Fund 4 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund5Name", type="STRING", desc="Name of fifth fund option in dropdown", value="Fund 5" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund5Acct", type="STRING", desc="GL code for one-time Fund 5 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund6Name", type="STRING", desc="Name of sixth fund option in dropdown", value="Fund 6" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund6Acct", type="STRING", desc="GL code for one-time Fund 6 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund7Name", type="STRING", desc="Name of seventh fund option in dropdown", value="Fund 7" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund7Acct", type="STRING", desc="GL code for one-time Fund 7 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund8Name", type="STRING", desc="Name of eighth fund option in dropdown", value="Fund 8" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund8Acct", type="STRING", desc="GL code for one-time Fund 8 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund9Name", type="STRING", desc="Name of ninth fund option in dropdown", value="Fund 9" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund9Acct", type="STRING", desc="GL code for one-time Fund 9 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund10Name", type="STRING", desc="Name of tenth fund option in dropdown", value="Fund 10" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Fund10Acct", type="STRING", desc="GL code for one-time Fund 10 donations", value="TEST" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonationTypeLabel", type="STRING", desc="Label for donation type field", value="Type" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonationFrequencyLabel", type="STRING", desc="Label for donation frequency field", value="Frequency" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonationFrequencyOptions", type="STRING", desc="Comma-delimited list of frequency options", value="Monthly, Quarterly, Annual" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentMethodLabel", type="STRING", desc="Label for donation method.", value="Payment Method" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PledgeReminderQuestion", type="STRING", desc="Statement about pledge reminder", value="I would like a pledge reminder." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="HonorMemorialCheckbox", type="STRING", desc="Statement to make honor/memorial gift", value="I wish to make this gift in honor/memory of an individual." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonorComments", type="STRING", desc="Label for donor comments", value="Donor Comments" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for stored CC", value="MCB_CC" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodePayCheck", type="STRING", desc="pay profile code for check", value="MBFPayByCheck" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentCodeACH", type="STRING", desc="pay profile code for bank draft", value="MBF_ACH" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="DonationConfirmation", type="CONTENTOBJ", desc="Content at top of confirmation page and email after donation", value="Thank you for your donation!" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PledgeConfirmation", type="CONTENTOBJ", desc="Content at top of confirmation page and email after pledge", value="Thank you for your pledge!" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed confirmation", value="Thank you!" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="Online Donation/Pledge To Be Processed" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CheckPaymentNote", type="CONTENTOBJ", desc="You must click continue to finalize your donation!", value="You must click continue to finalize your donation!" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
			/* ***************** */
			/* set form defaults */
			/* ***************** */
			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmDonation',
				formNameDisplay='Donation Form',
				orgEmailTo=variables.strPageFields.StaffConfirmationTo,
				memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
			));
			
			/* ******************* */
			/* Member History Vars */
			/* ******************* */
			variables.useHistoryID = 0;
			if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
				variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
			if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
				variables.useHistoryID = int(val(session.useHistoryID));	
			
			/* ***************** */
			/* Form Process Flow */
			/* ***************** */
			switch (local.formAction) {
				case "processLookup":
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and not len(arguments.event.getTrimValue('mk',''))) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
					break;
				case "processDonationInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processDonationInfo(rc=arguments.event.getCollection())) {
						case "showPayment":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						case "skipPayment":
							switch (processPayment(rc=arguments.event.getCollection())) {
								case "success": 
									local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
									local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
									structDelete(session, "formFields");
									break;
								default:
									local.returnHTML = showError(errorCode='failpayment');
									break;				
							}
							break;
						default:
							local.returnHTML = showError(errorCode='fail');
							break;				
					}
					break;
				case "processPayment":
					switch (processPayment(rc=arguments.event.getCollection())) {
						case "success": 
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
	
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					##div#variables.formName#loading [class^="icon-"], [class*=" icon-"] {width:auto;}
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;
					function assignMemberData(memObj) {
						$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
						mc_continueForm($('###variables.formName#'));
					}
					function validatePaymentForm() {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}

						mc_continueForm($('###variables.formName#'));
						return false;
					}
					
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);     
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processDonationInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous');			        	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}

					$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				
				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>

				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>

		<cfif not len(trim(variables.fund)) and structKeyExists(arguments.rc, "fund")>
			<cfset variables.fund = arguments.rc.fund>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.DonorInfoFieldSet, mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.DonorInfoFieldSetFirm, mode="collection", strData=local.strData)>
		
		<cfset local.fieldsetList1 = ''>
		<cfloop collection=#local.strFieldSetContent1.STRFIELDS# item="local.fields"> 
			<cfif local.fieldsetList1 eq ''>
				<cfset local.fieldsetList1 = local.fields & '|' & local.strFieldSetContent1.STRFIELDS[local.fields]>
			<cfelse>	
				<cfset local.fieldsetList1 = local.fieldsetList1 & ',' & local.fields & '|' & local.strFieldSetContent1.STRFIELDS[local.fields]>
			</cfif>			
		</cfloop>
		
		<cfset local.fieldsetList2 = ''>
		<cfloop collection=#local.strFieldSetContent2.STRFIELDS# item="local.fields"> 
			<cfif local.fieldsetList2 eq ''>
				<cfset local.fieldsetList2 = local.fields & '|' & local.strFieldSetContent2.STRFIELDS[local.fields]>
			<cfelse>	
				<cfset local.fieldsetList2 = local.fieldsetList2 & ',' & local.fields & '|' & local.strFieldSetContent2.STRFIELDS[local.fields]>
			</cfif>			
		</cfloop>
		
		<cfset local.dob = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Installation Date">
				<cfset local.dob = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<script language="javascript">			
					var IndividualInfoContainer = '<div class="tsAppSectionHeading">#JSStringFormat(local.strFieldSetContent1.fieldSetTitle)#</div><div class="tsAppSectionContentContainer">#JSStringFormat(local.strFieldSetContent1.fieldSetContent)#</div>';
					var orgInfoContainer = '<div class="tsAppSectionHeading">#JSStringFormat(local.strFieldSetContent2.fieldSetTitle)#</div><div class="tsAppSectionContentContainer">#JSStringFormat(local.strFieldSetContent2.fieldSetContent)#</div>';
					
					function validateMemberInfoForm(){
						var _CF_this = document.forms['#variables.formName#'];
						var arrReq = new Array();
						
						var contType = $('input[name="contributionType"]:checked').val();
						var fund_type = $("##fund_type option:selected").text();
						var selectType = $('##selectType').val();
						var otherAmt = $('##otherAmt').val();
						var pmtOption = $('##pmtOption').val();
						if (contType == undefined) arrReq[arrReq.length] = 'Select type of contribution.';
						if ($('##fund_type').val() == '') arrReq[arrReq.length] = 'Select fund.';
						if (otherAmt == '') arrReq[arrReq.length] = 'Enter donation amount.';
						if (selectType == '') arrReq[arrReq.length] = 'Select type.';
						if (pmtOption == '' && (selectType != 'Pledge' && selectType != '')) arrReq[arrReq.length] = 'Select payment method.';
						
						$('##fundType').val(fund_type);
						if (contType == 'Individual') {
							#local.strFieldSetContent1.jsValidation#
							
							var fieldsetList1 = $('##fieldsetList1').val();

							var numbers = fieldsetList1.split(',');
							var indFieldsList = '';
							for(var i = 0; i < numbers.length; i++)	{
								var fieldItem = numbers[i].split('|');								
								var eachFieldId = fieldItem[0];
								var eachFieldName = fieldItem[1];
								if($('##'+eachFieldId).prop('type')=='select-one') {
									var selectedText = $('##'+eachFieldId).find('option:selected').text();
									indFieldsList = indFieldsList + '<>' + eachFieldName + '|' + selectedText;
								}
								else if($('##'+eachFieldId).prop('type')=='select-multiple') {
									var selectedText = '';
									$('##'+eachFieldId).find('option:selected').each(function () {
										var $this = $(this);
										if ($this.length) {
											selText = $this.text();
											if(selectedText=='') selectedText = selText;
											else selectedText = selectedText + '::' + selText;
										}
									});
									indFieldsList = indFieldsList + '<>' + eachFieldName + '|' + selectedText;
								}
								else {
									var otherFieldtext = $('##'+eachFieldId).val();
									indFieldsList = indFieldsList + '<>' + eachFieldName + '|' + otherFieldtext;
								}
								
							}
							$('##indFieldsList').val(indFieldsList);
							
							
						} else if (contType == 'Organization') {
							#local.strFieldSetContent2.jsValidation#
							
							var fieldsetList2 = $('##fieldsetList2').val();

							var numbers = fieldsetList2.split(',');
							var orgFieldsList = '';
							for(var i = 0; i < numbers.length; i++)	{
								var fieldItem = numbers[i].split('|');								
								var eachFieldId = fieldItem[0];
								var eachFieldName = fieldItem[1];
								if($('##'+eachFieldId).prop('type')=='select-one') {
									var selectedText = $('##'+eachFieldId).find('option:selected').text();
									orgFieldsList = orgFieldsList + '<>' + eachFieldName + '|' + selectedText;
								}
								else if($('##'+eachFieldId).prop('type')=='select-multiple') {
									var selectedText = '';
									$('##'+eachFieldId).find('option:selected').each(function () {
										var $this = $(this);
										if ($this.length) {
											selText = $this.text();
											if(selectedText=='') selectedText = selText;
											else selectedText = selectedText + '::' + selText;
										}
									});
									orgFieldsList = orgFieldsList + '<>' + eachFieldName + '|' + selectedText;
								}
								else {
									var otherFieldtext = $('##'+eachFieldId).val();
									orgFieldsList = orgFieldsList + '<>' + eachFieldName + '|' + otherFieldtext;
								}
							}
							$('##orgFieldsList').val(orgFieldsList);
							
						}
						
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
						
						var hmCount= $('##honorMemorialCount').val();
						var honorMemorialList = '';
						for(i=1;i<=hmCount;i++) {
							honorMemorialList = honorMemorialList + $("[name='honorMemorial_"+i+"']:checked").val() + ' ' + $("[name='honor_"+i+"']").val() + '<br/>';
						}
						$('##honorMemorialList').val(honorMemorialList);
						
						mc_continueForm($('###variables.formName#'));
						return false;
					}
					function prefillData() {
						var objPrefill = new Object();
						<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
							#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfloop>
						for (var key in objPrefill) {
							if (objPrefill.hasOwnProperty(key)) { 
								$('###variables.formName# ##'+key).val(objPrefill[key]);
							}
						}
					}
					function toggleDonorInfo() {
						var contType = $('input[name="contributionType"]:checked').val();
						$('##donorInformation').html('');
						if (contType == 'Individual') {
							$('##donorInformation').html(IndividualInfoContainer);
							$('##datefieldId').val('#local.dob#');
							var dateId = $('##datefieldId').val();
							mca_setupDatePickerField('#local.dob#');
							var btnclear = 'btnClear'+ dateId;
							$('##'+btnclear).attr("onclick","javascript:mca_clearDateRangeField('#local.dob#');");
							
							var fieldsetList1 = $('##fieldsetList1').val();
							var numbers = fieldsetList1.split(',');
							for(var i = 0; i < numbers.length; i++)	{
								var fieldItem = numbers[i].split('|');								
								var eachFieldId = fieldItem[0];
								if($('##'+eachFieldId).prop('type')=='select-multiple') {
									$("##"+eachFieldId).multiselect({
										header: false,
										noneSelectedText: ' - Please Select - ',
										selectedList: 2,
										minWidth: 400,
									});
									$("##"+eachFieldId).multiselect("uncheckAll");
								}
							}
							
						}
						else if(contType == 'Organization') {
							$('##donorInformation').html(orgInfoContainer);
							
							var fieldsetList2 = $('##fieldsetList2').val();

							var numbers = fieldsetList2.split(',');
							for(var i = 0; i < numbers.length; i++)	{
								var fieldItem = numbers[i].split('|');								
								var eachFieldId = fieldItem[0];
								if($('##'+eachFieldId).prop('type')=='select-multiple') {
									$("##"+eachFieldId).multiselect({
										header: false,
										noneSelectedText: ' - Please Select - ',
										selectedList: 2,
										minWidth: 400,
									});
									$("##"+eachFieldId).multiselect("uncheckAll");
								}
							}
						}
						prefillData();
						
					}
					
					$(function() {
						<cfif structKeyExists(local.strData, "fund_type") AND local.strData.fund_type neq ''>
							<cfif structKeyExists(local.strData, "paymentPlan") AND local.strData.paymentPlan neq ''>
								$('##paymentPlan').val('#local.strData.paymentPlan#');
								
								<cfif structKeyExists(local.strData, "otherAmt")>
									$('##otherAmt').val('#local.strData.otherAmt#');
								</cfif>
							</cfif>
						</cfif>
						<cfif structKeyExists(local.strData, "contributionType")>
							<cfif local.strData.contributionType eq 'Individual'>
								$('##donorInformation').html(IndividualInfoContainer);
							<cfelseif local.strData.contributionType eq 'Organization'>
								$('##donorInformation').html(orgInfoContainer);
							</cfif>
						</cfif>
					});
					$("##frequencyOptionRow").hide();
					$("##pmtOptionRow").hide();
					$(".pledgeReminderRow").hide();
					$(".honorMemorialDetails").hide();
					$("##selectType").change(function(){
						var selectedType = $("##selectType").val();
						if(selectedType == 'Recurring') {
							$("##frequencyOptionRow").show();
							$("##pmtOptionRow").show();
							$(".pledgeReminderRow").hide();
						}
						if (selectedType == 'One-time') {
							$("##pmtOptionRow").show();
							$("##frequencyOptionRow").hide();
							$(".pledgeReminderRow").hide();
						}
						if (selectedType == 'Pledge') {
							$(".pledgeReminderRow").show();
							$("##frequencyOptionRow").hide();
							$("##pmtOptionRow").hide();
						}
					});
					$("##honorMemorialCheckbox").change(function(){
						if($('##honorMemorialCheckbox').is(":checked"))
							$(".honorMemorialDetails").show();
						else
							$(".honorMemorialDetails").hide();
					});
					$(".addAnotherBtn").click(function(){
						var honorMemorialCount = Number(document.getElementById('honorMemorialCount').value) + 1;
						$('##honorMemorialCount').val(honorMemorialCount);
						$( ".addAnother" ).before('<br><input type="radio" name="honorMemorial_'+honorMemorialCount+'" value="In honor of"> In honor of &nbsp;&nbsp;&nbsp;<input type="radio" name="honorMemorial_'+honorMemorialCount+'" value="In memory of"> In memory of &nbsp;&nbsp;&nbsp;&nbsp;<input type="text" name="honor_'+honorMemorialCount+'" placeholder="Name">');
					});
				</script>
				<style type="text/css">
					###variables.formName# input[type="text"] {min-height:30px;}
				</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div style="clear:both;"></div>
				<div style="width:75%;margin:0 auto;">
					<div class="TitleText" style="padding-bottom:15px;">#variables.strPageFields.FormTitle#</div>
					#variables.strPageFields.Page1TopContent#
					<div style="clear:both;"></div>
					<br /><br />
					
					<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
						<input type="hidden" name="fa" id="fa" value="processDonationInfo">
						<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
						<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
						<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
						<input type="hidden" name="honorMemorialCount" id="honorMemorialCount" value="1">
						<input type="hidden" name="honorMemorialList" id="honorMemorialList" value="">
						<input type="hidden" name="fundType" id="fundType" value="">
						<input type="hidden" name="fieldsetList1" id="fieldsetList1" value="#local.fieldsetList1#">
						<input type="hidden" name="fieldsetList2" id="fieldsetList2" value="#local.fieldsetList2#">
						<input type="hidden" name="datefieldId" id="datefieldId" value="">
						<input type="hidden" name="indFieldsList" id="indFieldsList" value="">
						<input type="hidden" name="orgFieldsList" id="orgFieldsList" value="">
						<cfinclude template="/model/cfformprotect/cffp.cfm">
						
						<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
						<div class="tsAppSectionHeading">#variables.strPageFields.DonationSectionTitle#</div>
						
						<div class="tsAppSectionContentContainer">
							<table cellspacing="0" cellpadding="3" border="0" style="width:100%;">
								<tbody>
									<tr valign="top">
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<label class="radio inline">
												<input type="radio" name="contributionType" id="contributionType1" onclick="toggleDonorInfo();" value="Individual" <cfif structKeyExists(local.strData, "contributionType") and local.strData.contributionType eq variables.strPageFields.IndividualOption>checked="checked"</cfif>> #variables.strPageFields.IndividualOption# &nbsp;
											</label>
											<label class="radio inline">
												<input type="radio" name="contributionType" id="contributionType2" onclick="toggleDonorInfo();" value="Organization" <cfif structKeyExists(local.strData, "contributionType") and local.strData.contributionType eq variables.strPageFields.OrganizationOption>checked="checked"</cfif>> #variables.strPageFields.OrganizationOption#
											</label>
										</td>
									</tr>
									<tr valign="top">
										<td class="tsAppBodyText" width="150">* Fund</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<select name="fund_type" id="fund_type" style="width:475px;">
												<option value="">Please Select</option>
												<cfif variables.strPageFields.Fund1Name neq '' and variables.strPageFields.Fund1Acct neq '' and variables.strPageFields.Fund1Name neq 'NULL' and variables.strPageFields.Fund1Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund1Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund1Acct) OR variables.fund eq variables.strPageFields.Fund1Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund1Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund2Name neq '' and variables.strPageFields.Fund2Acct neq '' and variables.strPageFields.Fund2Name neq 'NULL' and variables.strPageFields.Fund2Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund2Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund2Acct) OR variables.fund eq variables.strPageFields.Fund2Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund2Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund3Name neq '' and variables.strPageFields.Fund3Acct neq '' and variables.strPageFields.Fund3Name neq 'NULL' and variables.strPageFields.Fund3Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund3Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund3Acct) OR variables.fund eq variables.strPageFields.Fund3Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund3Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund4Name neq '' and variables.strPageFields.Fund4Acct neq '' and variables.strPageFields.Fund4Name neq 'NULL' and variables.strPageFields.Fund4Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund4Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund4Acct) OR variables.fund eq variables.strPageFields.Fund4Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund4Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund5Name neq '' and variables.strPageFields.Fund5Acct neq '' and variables.strPageFields.Fund5Name neq 'NULL' and variables.strPageFields.Fund5Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund5Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund5Acct) OR variables.fund eq variables.strPageFields.Fund5Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund5Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund6Name neq '' and variables.strPageFields.Fund6Acct neq '' and variables.strPageFields.Fund6Name neq 'NULL' and variables.strPageFields.Fund6Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund6Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund6Acct) OR variables.fund eq variables.strPageFields.Fund6Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund6Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund7Name neq '' and variables.strPageFields.Fund7Acct neq '' and variables.strPageFields.Fund7Name neq 'NULL' and variables.strPageFields.Fund7Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund7Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund7Acct) OR variables.fund eq variables.strPageFields.Fund7Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund7Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund8Name neq '' and variables.strPageFields.Fund8Acct neq '' and variables.strPageFields.Fund8Name neq 'NULL' and variables.strPageFields.Fund8Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund8Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund8Acct) OR variables.fund eq variables.strPageFields.Fund8Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund8Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund9Name neq '' and variables.strPageFields.Fund9Acct neq '' and variables.strPageFields.Fund9Name neq 'NULL' and variables.strPageFields.Fund9Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund9Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund9Acct) OR variables.fund eq variables.strPageFields.Fund9Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund9Name#</option>
												</cfif>
												<cfif variables.strPageFields.Fund10Name neq '' and variables.strPageFields.Fund10Acct neq '' and variables.strPageFields.Fund10Name neq 'NULL' and variables.strPageFields.Fund10Acct neq 'NULL'>
												<option value="#variables.strPageFields.Fund10Acct#" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq variables.strPageFields.Fund10Acct) OR variables.fund eq variables.strPageFields.Fund10Acct>selected="true"</cfif>>
												#variables.strPageFields.Fund10Name#</option>
												</cfif>
											</select>
										</td>
									</tr>
									<tr><td colspan="3"><hr><br /></td></tr>
									<tr valign="top">
										<td class="tsAppBodyText" nowrap="" width="150">* Amount</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<input type="text" name="otherAmt" id="otherAmt" placeholder="Amount" onblur="this.value=formatCurrency(this.value);" value="">
										</td>
									</tr>
									<tr id="typeOptionRow" valign="top">
										<td class="tsAppBodyText">* #variables.strPageFields.DonationTypeLabel#</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<select name="selectType" id="selectType" style="width:475px;">
												<option value="">Please Select</option>
												<option value="One-time" <cfif structKeyExists(local.strData, "selectType") and local.strData.selectType eq 'One-time'>checked="checked"</cfif>>One-time</option>
												<option value="Recurring" <cfif structKeyExists(local.strData, "selectType") and local.strData.selectType eq 'Recurring'>checked="checked"</cfif>>Recurring</option>
												<option value="Pledge" <cfif structKeyExists(local.strData, "selectType") and local.strData.selectType eq 'Pledge'>checked="checked"</cfif>>Pledge</option>
											</select>
										</td>
									</tr>
									<tr id="frequencyOptionRow" valign="top" style="display:none;">
										<td class="tsAppBodyText">#variables.strPageFields.DonationFrequencyLabel#</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<select name="frequencyOpt" id="frequencyOpt" style="width:475px;">
												<option value="">Please Select</option>
												<cfloop list="#variables.strPageFields.DonationFrequencyOptions#" index="name">
													<cfoutput>
														<option value="#name#">#name#</option>
													</cfoutput>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr id="pmtOptionRow" valign="top" style="display:none;">
										<td class="tsAppBodyText">* #variables.strPageFields.PaymentMethodLabel#</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<select name="pmtOption" id="pmtOption" style="width:475px;">
												<option value="">Please Select</option>
												<option value="card">Credit Card</option>
												<option value="invoice">Check</option>
												<option value="bankdraft">Bank Draft</option>
											</select>
										</td>
									</tr>
									<tr style="display:none;"><td colspan="3">&nbsp;</td></tr>
									<tr valign="top" class="pledgeReminderRow">
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<input type="checkbox" name="pledgeReminderCheckbox" id="pledgeReminderCheckbox"> #variables.strPageFields.PledgeReminderQuestion# 
											<span style="float:right"> When? 
												<select name="month" id="month">
													<option value="">Please Select</option>
													<option value="January">January</option>
													<option value="February">February</option>
													<option value="March">March</option>
													<option value="April">April</option>
													<option value="May">May</option>
													<option value="June">June</option>
													<option value="July">July</option>
													<option value="August">August</option>
													<option value="September">September</option>
													<option value="October">October</option>
													<option value="November">November</option>
													<option value="December">December</option>
												</select>
											</span>
										</td>
									</tr>
									<tr valign="top">
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<input type="checkbox" id="honorMemorialCheckbox" name="honorMemorialCheckbox"> #variables.strPageFields.HonorMemorialCheckbox# 
										</td>
									</tr>
									<tr valign="top" class="honorMemorialDetails" style="display:none;">
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<input type="radio" name="honorMemorial_1" value="In honor of"> In honor of &nbsp;&nbsp;<input type="radio" name="honorMemorial_1" value="In memory of"> In memory of &nbsp;&nbsp;&nbsp;&nbsp;
											<input type="text" name="honor_1" placeholder="Name">
											<span class="addAnother" style="margin-left: 45%;"><i class="fa fa-plus-circle addAnotherBtn" aria-hidden="true" style="font-size: 20px;"></i> Add another </span>
										</td>
									</tr>
									<tr valign="top">
										<td class="tsAppBodyText">#variables.strPageFields.DonorComments#</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<textarea name="donorComments" id="donorComments" rows="5" style="width:400px;"><cfif structKeyExists(local.strData, "donorComments")>#local.strData.donorComments#</cfif></textarea>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div id="donorInformation"></div>
						<button name="btnContinue" type="submit" class="btn tsAppBodyButton pull-right" onClick="hideAlert();">Next &gt;&gt;</button>
					</form>
				</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processDonationInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.response = ''>
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		
		<cfif local.contributionType eq 'Organization'>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0)>
			<cfset local.company = ''>
			<cfif structKeyExists(arguments.rc,"m_company")>
				<cfset local.company = arguments.rc.m_company>
			</cfif>
			<cfset local.objSaveMember.setDemo(prefix='', firstName='Organization', middleName='', lastName='Account', suffix='', company=local.company)>
			<cfset local.objSaveMember.setRecordType(recordType='Organization')>
			<cfset local.objSaveMember.setMemberType(memberType='User')>
			<cfset local.objSaveMember.setCustomField(field="Member Type - Other", value="Organization")>
			<!--- address/phone --->
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>
			<cfset local.qryOrgPhoneTypes = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.rc.mc_siteinfo.orgid)>
			<cfloop query="local.qryOrgAddressTypes">
				<cfset local.tmpAddressTypeID = local.qryOrgAddressTypes.addressTypeID>
				<cfset local.tmpAddressType = local.qryOrgAddressTypes.addressType>

				<cfloop query="local.qryOrgPhoneTypes">
					<cfif isDefined("arguments.rc.mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#")>
						<cfset local.tmpVal = left(arguments.rc["mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#"],40)>
						<cfif len(local.tmpVal)>
							<cfset local.objSaveMember.setPhone(addresstype=local.tmpAddressType, type=local.qryOrgPhoneTypes.phoneType, value=local.tmpVal)>
						</cfif>
					</cfif>
				</cfloop>
			</cfloop>			
			<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>

			<cfif local.strResult.success>
	 			<cfset variables.useMID = local.strResult.memberID>
	 		</cfif>
		</cfif>
		
		<cfif structKeyExists(arguments.rc,"selectType") and arguments.rc.selectType neq 'Pledge'>
			<cfset local.response = 'showPayment'>
		<cfelse>
			<cfset local.response = 'skipPayment'>
		</cfif>
		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>
		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfif arguments.rc["pmtOption"] eq 'card'>
			<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC ]>
		<cfelseif arguments.rc["pmtOption"] eq 'invoice'>
			<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCheck ]>
		<cfelseif arguments.rc["pmtOption"] eq 'bankdraft'>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentCodeACH ]>
		</cfif>
		
		<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																					siteID=variables.siteID, 
																					memberID=variables.useMID, 
																					title="Donation Form", 
																					formName=variables.formName, 
																					backStep="processDonationInfo")>

		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
				<cfinput type="hidden" name="fa" id="fa" value="processPayment">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
				<cfloop collection="#arguments.rc#" item="local.thisField">
					<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
						or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
						or left(local.thisField,5) eq "mccf_" 
						or ListFindNoCase("fund_type|contributionType|paymentPlan|pmtOption|otherAmt|donorComments|selectType|frequencyOpt|honorMemorialCheckbox|honorMemorial|honor|pledgeReminderCheckbox|month|honorMemorialList|fundType|fieldsetList1|fieldsetList2|indFieldsList|orgFieldsList",local.thisField,"|")>
						<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
					</cfif>
				</cfloop>

				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
				
				<cfif structKeyExists(arguments.rc,"pmtOption") and arguments.rc["pmtOption"] eq 'invoice'>
					#variables.strPageFields.CheckPaymentNote#
				</cfif>
				
				<div class="tsAppSectionHeading">Payment for #variables.formNameDisplay#</div>
				<div class="tsAppSectionContentContainer">
					<div class="tsAppBodyText">
						<b>Amount :</b> #arguments.rc["otherAmt"]#
					</div>
				</div>
			
				#local.strReturn.paymentHTML#
				
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		
		<cfset variables.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Donation', subName=arguments.rc.selectType)>

		<cfset local.fieldsetList1 = ''>
		<cfif structKeyExists(arguments.rc,"fieldsetList1")>
			<cfset local.fieldsetList1 = arguments.rc.fieldsetList1>
		</cfif>
		
		<cfset local.fieldsetList2 = ''>
		<cfif structKeyExists(arguments.rc,"fieldsetList2")>
			<cfset local.fieldsetList2 = arguments.rc.fieldsetList2>
		</cfif>
		
		
		<cfset local.br = "#chr(13)##chr(10)#">
		<cfset local.description = 'Donor Type : ' & arguments.rc.contributionType & local.br &'Fund : ' & Trim(arguments.rc.fundType) & local.br &'Type : ' & arguments.rc.selectType >
		<cfif structKeyExists(arguments.rc,"frequencyOpt") and arguments.rc.frequencyOpt neq ''>
			<cfset local.description = local.description & 'Frequency : ' & arguments.rc.frequencyOpt & local.br >
		</cfif>
		<cfif structKeyExists(arguments.rc,"honorMemorialCheckbox")>
			<cfset local.description = local.description & 'I wish to make this gift in honor/memory of an individual : YES' & local.br >
		</cfif>
		<cfif structKeyExists(arguments.rc,"honorMemorialList")>
			<cfset tempHonorMemorialList = replace(arguments.rc.honorMemorialList,"undefined","","all")>
			<cfset tempHonorMemorialList = replace(tempHonorMemorialList,"<br/>",local.br,"all")>
			<cfif tempHonorMemorialList neq ''>
				<cfset local.description = local.description &  tempHonorMemorialList >
			</cfif>
		</cfif>
		<cfif structKeyExists(arguments.rc,"selectType") and arguments.rc.selectType neq 'Pledge'>
			<cfset local.description = local.description & 'Payment Method : ' & arguments.rc.pmtOption & local.br >
		</cfif>
		<cfif structKeyExists(arguments.rc,"pledgeReminderCheckbox") and structKeyExists(arguments.rc,"month") and arguments.rc["month"] neq ''>
			<cfset local.description = local.description & 'Pledge Reminder : ' & arguments.rc.month & local.br >
		</cfif>
		<cfset local.description = local.description & 'Donor Comments : ' & arguments.rc.donorComments & local.br & local.br>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.DonorInfoFieldSet, mode="confirmation", strData=arguments.rc)>
		<cfif arguments.rc.contributionType eq 'Individual'>
			<cfset local.indFieldsList = ''>
			<cfset local.indFieldsList = arguments.rc.indFieldsList>
			<cfset local.twoChars = left(arguments.rc.indFieldsList,2)>
			<cfif local.twoChars eq '<>'>
				<cfset local.indFieldsList = Right(arguments.rc.indFieldsList, Len(arguments.rc.indFieldsList)-2)>
			</cfif>
			<cfloop list="#local.indFieldsList#" index="local.field" delimiters="<>">
				<cfset local.fieldName = ListGetAt(local.field,1,'|')>
				<cfset local.position = FindNoCase('|', local.field)>
				<cfset local.rightCount = len(local.field) - local.position>
				<cfset local.fieldValue = ''>
				<cfif local.rightCount gt 0>
					<cfset local.fieldValue = right(local.field,local.rightCount)>
				</cfif>	
				<cfset local.description = local.description & local.fieldName & ' : ' & Replace(local.fieldValue,"::",",","all") & local.br >
			</cfloop>
		<cfelseif arguments.rc.contributionType eq 'Organization'>
			<cfset local.orgFieldsList = ''>
			<cfset local.orgFieldsList = arguments.rc.orgFieldsList>
			<cfset local.twoChars = left(arguments.rc.orgFieldsList,2)>
			<cfif local.twoChars eq '<>'>
				<cfset local.orgFieldsList = Right(arguments.rc.orgFieldsList, Len(arguments.rc.orgFieldsList)-2)>
			</cfif>
			<cfloop list="#local.orgFieldsList#" index="local.field" delimiters="<>">
				<cfset local.fieldName = ListGetAt(local.field,1,'|')>
				<cfset local.position = FindNoCase('|', local.field)>
				<cfset local.rightCount = len(local.field) - local.position>
				<cfset local.fieldValue = ''>
				<cfif local.rightCount gt 0>
					<cfset local.fieldValue = right(local.field,local.rightCount)>
				</cfif>	
				<cfset local.description = local.description & local.fieldName & ' : ' & Replace(local.fieldValue,"::",",","all") & local.br >
			</cfloop>
		</cfif>
		
		<cfif structKeyExists(arguments.rc,"otherAmt")>
			<cfset dollarAmt = LSParseNumber(rereplace(arguments.rc.otherAmt, "[^0-9\.]", "", "all"))>
		<cfelse>
			<cfset dollarAmt = 0>
		</cfif>
		<cfif arguments.rc.selectType neq 'Pledge' and structKeyExists(arguments.rc,"memberID")>
			<cfset variables.useMID = arguments.rc.memberID>
		</cfif>
		
		<!--- Add history --->
		<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistory.categoryID, 
											subCategoryID=variables.qryHistory.subCategoryID, description=local.description, 
											enteredByMemberID=variables.useMID, newAccountsOnly=false, dollarAmt=dollarAmt)>	
		<cfset session.useHistoryID = variables.useHistoryID>			
		
		
		<cfif structKeyExists(arguments.rc,"selectType") and arguments.rc.selectType eq 'One-time'>
			<cfset local.totalAmount = val(ReReplace(arguments.rc.otherAmt, "[^\d.]", "","ALL")) >
			
			<!--- This is the accounting part --->
			<cfset local.strAccTemp = {
				totalPaymentAmount=local.totalAmount,
				assignedToMemberID=variables.useMID,
				recordedByMemberID=variables.useMID,
				rc=arguments.rc
			}>
	
			<cfif structKeyExists(arguments.rc,"pmtOption") and arguments.rc["pmtOption"] eq 'card'>
				<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode=variables.strPageFields.ProfileCodePayCC
				)>
				<cfset local.strAccTemp.payment = {
					detail="#variables.formNameDisplay#",
					amount=local.strAccTemp.totalPaymentAmount,
					profileID=local.payProfileID,
					profileCode=variables.strPageFields.ProfileCodePayCC
				}>
			<cfelseif structKeyExists(arguments.rc,"pmtOption") and arguments.rc["pmtOption"] eq 'bankdraft'>
				<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode=variables.strPageFields.PaymentCodeACH
				)>
				<cfset local.strAccTemp.payment = {
					detail="#variables.formNameDisplay#",
					amount=local.strAccTemp.totalPaymentAmount,
					profileID=local.payProfileID,
					profileCode=variables.strPageFields.PaymentCodeACH
				}>
			<cfelseif structKeyExists(arguments.rc,"pmtOption") and arguments.rc["pmtOption"] eq 'invoice'>
				<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode=variables.strPageFields.ProfileCodePayCheck
				)>
				<cfset local.strAccTemp.payment = {
					detail="#variables.formNameDisplay#",
					amount=local.strAccTemp.totalPaymentAmount,
					profileID=local.payProfileID,
					profileCode=variables.strPageFields.ProfileCodePayCheck
				}>
			</cfif>
		
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
			
			<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>
				<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=variables.useMID, transactionDate=now())>
			</cfif>

			<!--- Record sale --->
			<cfif structKeyExists(arguments.rc,"otherAmt")>
				<cfset dollarAmt = LSParseNumber(rereplace(arguments.rc.otherAmt, "[^0-9\.]", "", "all"))>
			<cfelse>
				<cfset dollarAmt = 0>
			</cfif>
			<cfset donationDetail = 'Donation to ' & arguments.rc.fundType>
			<cfset local.strACCTemp = { assignedToMemberID	= variables.useMID,
															recordedByMemberID	= variables.useMID,
															detail = donationDetail,
															amount = dollarAmt,
															transactionDate = now() }>
			<cfset local.strACCTemp.revenueGLAccountCode = arguments.rc.fund_type>
			
			<!--- Allocate payment to sale --->
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCSale = local.objAccounting.recordSale(argumentcollection=local.strACCTemp)>

			<cfif val(dollarAmt) gt 0 and local.strACCResponse.paymentResponse.mc_transactionID gt 0 and local.strACCSale.transactionID gt 0>
				<cfset local.strACCTemp = { recordedOnSiteID=variables.siteID, 
															recordedByMemberID=variables.useMID, 
															statsSessionID=val(session.cfcUser.statsSessionID), 
															amount=numberFormat(dollarAmt,"0.00"),
															transactionDate=now(), 
															paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, 
															saleTransactionID=local.strACCSale.transactionID } />
				<cfset local.strACCAllocate = local.objAccounting.allocateToSale(argumentcollection=local.strACCTemp) />
			</cfif>
			<cfif structKeyExists(arguments.rc,"MCCF_PAYMETH") and arguments.rc.MCCF_PAYMETH eq variables.strPageFields.ProfileCodePayCheck and  structKeyExists(local.strACCSale,"InvoiceId") and local.strACCSale.InvoiceId neq ''>
				<cfset local.objAccounting.closeInvoice(InvoiceId=local.strACCSale.InvoiceId)>
			</cfif>
		</cfif>
		<cfset local.response = 'success'>
		
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.DonorInfoFieldSet, mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.DonorInfoFieldSetFirm, mode="confirmation", strData=arguments.rc)>
		
		<cfset local.memberPayProfileDetail = "">
		<cfif structKeyExists(arguments.rc,"mccf_payMethID") and structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
			<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
		<cfelse>
			<cfset local.memberPayProfileSelected = 0>
		</cfif>
		<cfif local.memberPayProfileSelected gt 0>
			<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
		</cfif>
	
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
				<cfif structKeyExists(arguments.rc,"selectType") and arguments.rc.selectType neq 'Pledge'>
					<cfif len(variables.strPageFields.DonationConfirmation)>
						<div>#variables.strPageFields.DonationConfirmation#</div>
					</cfif>
				<cfelse>
					<cfif len(variables.strPageFields.PledgeConfirmation)>
						<div>#variables.strPageFields.PledgeConfirmation#</div>
					</cfif>
				</cfif>
				
				<!--@@specialcontent@@-->
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation Information</td>
					</tr>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;width:150px;">
							<b>Contribution Type :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"contributionType")>#arguments.rc["contributionType"]#</cfif>
						</td>
					</tr>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Fund :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"fundType")>#arguments.rc["fundType"]#</cfif>
						</td>
					</tr>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Amount :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"otherAmt")>#arguments.rc["otherAmt"]#</cfif>
						</td>
					</tr>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Type :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"selectType")>#arguments.rc["selectType"]#</cfif>
						</td>
					</tr>
					<cfif structKeyExists(arguments.rc,"pledgeReminderCheckbox") and structKeyExists(arguments.rc,"month") and arguments.rc["month"] neq ''>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>I would like a pledge reminder in :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							#arguments.rc["month"]#
						</td>
					</tr>
					</cfif>
					<cfif structKeyExists(arguments.rc,"frequencyOpt") and arguments.rc["frequencyOpt"] neq ''>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Frequency :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							#arguments.rc["frequencyOpt"]#
						</td>
					</tr>
					</cfif>
					<cfif structKeyExists(arguments.rc,"honorMemorialCheckbox")>
					<tr>
						<td colspan="2" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>I wish to make this gift in honor/memory of an individual? </b> YES
						</td>
					</tr>
					<cfif structKeyExists(arguments.rc,"honorMemorialList")>
					<tr>
						<td colspan="2" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							#arguments.rc["honorMemorialList"]# 
						</td>
					</tr>
					</cfif>
					</cfif>
					<cfif structKeyExists(arguments.rc,"selectType") and arguments.rc.selectType neq 'Pledge'>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Payment Method :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif arguments.rc["pmtOption"] eq 'card'>Credit Card <cfelseif arguments.rc["pmtOption"] eq 'bankdraft'>Bank Draft <cfelse>Check </cfif>
						</td>
					</tr>
					</cfif>
					<tr>
						<td width="150px" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Donor Comments :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"donorComments")>#arguments.rc["donorComments"]#</cfif>
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.contributionType eq 'Individual'>
					#local.strFieldSetContent1.fieldSetContent#
				<cfelseif local.contributionType eq 'Organization'>
					#local.strFieldSetContent2.fieldSetContent#
				</cfif>
				
				<cfif local.memberPayProfileDetail neq "">
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation - Payment</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteid,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.Subject = variables.strPageFields.StaffConfirmationSub >
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>
