<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using the criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<div>#local.searchMessage#</div>
<br />

<div class="row-fluid">
	<div class="span12">
		<cfset local.formClass = "form-search">
		<div id="searchBoxError" class="alert alert-error hide"></div>
		<form name="frms_Search" id="frms_Search" action="/?pg=search&bid=#arguments.bucketid#" method="POST" style="margin-bottom:0;" onSubmit="return b_search();" class="#local.formClass#">
			<input type="hidden" name="s_a" value="doSearch">
			<input type="hidden" name="s_frm" value="1">
			<input type="text" name="s_key_all" id="s_key_all" maxlength="200" value="#local.strSearchForm.s_key_all#" class="search-query" style="width:60%;">
			<button type="submit" id="s_btn" name="s_btn" class="btn">Search</button>
		</form>
	</div>
</div>

<div>
	<br /></br /><br />
	<div class="hidden-phone">
		<img src="/assets/common/images/search/searchLeftArrow.png" alt="" /><br />
		<div class="s_rbnm" style="margin-left:105px;width:300px;">
			For advanced search options,<br />select a database on the left.
		</div>
	</div>
	<div class="visible-phone row-fluid">
		<div class="span12" style="text-align:center;">
			<i class="icon-hand-up icon-2x"></i><br />
			For advanced search options,<br />select a database from the list.
		</div>
	</div>
</div>

</cfoutput>