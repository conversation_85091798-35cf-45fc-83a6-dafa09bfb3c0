<cfset local.dropboxAppKey = "">
<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
	<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
</cfif>

<cfsavecontent variable="local.bucketJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/d3/6.5.0/d3.v6.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/datatables/1.10.24/js/jquery.dataTables.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/jQuerySteps/jquery.steps.js"></script>

	<script language="javascript">
		function b_clearCounts() {
			for (var i=0; i < bucketarray.length; i++)
				b_clearCount(bucketarray[i]);
		}
		function b_clearCount(obj) {
			b_setCount(obj,'');
		}
		function b_showCountLoading(obj) {
			b_setCount(obj,b_getloadingicon(),true);
		}
		function b_setCount(obj,cnt,isIcon=false) {
			var countDisplay;
			if(cnt == '--' || cnt == '' || isIcon) countDisplay = cnt;
			else if(cnt == '0') countDisplay = '<span class="badge">'+ cnt +'</span>';
			else countDisplay = '<span class="badge badge-success">'+ cnt +'</span>';
			
			$('##bk_' + obj.bid + '_cnt').html(countDisplay);
		}
		function b_doSearch(sid) {
			b_clearCounts();
			document.getElementById('bk_sid_holder').innerHTML = sid;
			setSearchID(sid);

			/*do current bucket actions first, then the others.*/
			document.getElementById('bk_content').innerHTML = '<div align="center">' + b_getlgloadingicon() + '</div>';
			var obj = b_getObjFromBid(vars_bid);
			isSearchReportReadyIntervalID = setInterval(isSearchReportReady,1000);
			
			if (obj) b_doSearchResults(obj,sid,1,'');
			/*now the others*/
			for (i=0; i < bucketarray.length; i++) {
				if (bucketarray[i].bid != vars_bid) b_getBucketCount(bucketarray[i],sid);
			}
		}
		function b_getBucketCount(obj,sid) {
			try {
				var objCounts = eval('b_' + sid + '_bcounts');
				var itemcount = (objCounts[obj.bid] == -1) ? 'N/A' : objCounts[obj.bid];
				if (objCounts[obj.bid] == -1) {
					b_setCount(obj,b_addCommas(itemcount).replace(/N\/A/, "--")); 
				} else if (objCounts[obj.bid] >= 0) {
					b_setCount(obj,b_addCommas(itemcount).replace(/N\/A/, "--")); 
				} else {
					b_doSearchCountResults(obj,sid);
				}
				b_calculateAndSetTotalCount(itemcount);
			} catch(err) {
				b_doSearchCountResults(obj,sid);
			}
		}
		function b_doSearchCountResults(obj,sid) {
			var objParams = { searchid:sid, bucketid:obj.bid };
			var showCountTimeout = function(r) { b_setCount(obj,'--'); obj.req = null; };
			var showCountErr = function(r) { b_setCount(obj,b_geterroricon(),true); obj.req = null; };
			var showCountResults = function(r) {
				var itemcount = '';
				if (r.success && r.success == 'true') {
					itemcount = (r.itemcount == -1) ? 'N/A' : r.itemcount;
					b_calculateAndSetTotalCount(itemcount);
					b_setCount(obj,b_addCommas(itemcount).replace(/N\/A/, "--"));
					obj.req = null;
				} else
					showCountErr(r);
			};
			obj.req = TS_AJX('SBT' + obj.btid,'getResultsCount',objParams,showCountResults,showCountErr,45000,showCountTimeout);
			b_showCountLoading(obj);
		}
		function b_doSearchResults(obj,sid,start,sorttype,filter,postfilter) {
			var bkcon = document.getElementById('bk_content');
			var objParams = { searchID:sid, bucketID:obj.bid, startRow:start, sortType:sorttype, filter:filter, postFilter:postfilter, viewDirectory:'responsive' };
			var showSearchTimeout = function(r) {
				b_setCount(obj,'--'); 
				if (bkcon) bkcon.innerHTML = '<center><div align="left" style="margin:10px;border:1px solid ##ccc;width:70%;padding:10px;"><div class="titleText">Search Cancelled</div><div class="bodyText"><br/>Your search has been cancelled. It may have timed out.<br/><br/>You may <a href="' + self.location.href + '">try your search again</a> or <a href="/?pg=search&bid=' + obj.bid + '">start a new search</a>.</div></div></center>';
				obj.req = null;
			};
			var showSearchErr = function(r,emsg) {
				b_setCount(obj,b_geterroricon(),true);
				if (bkcon) {
					var repeatSearchLink = 'You may <a href="' + self.location.href + '">try your search again</a> or <a href="/?pg=search&bid=' + obj.bid + '">start a new search</a>.';
					if(emsg && emsg.length)
						bkcon.innerHTML = '<div class="alert alert-error"><h4>'+ emsg +'</h4><div class="bodyText"><br/>'+ repeatSearchLink +'</div>';
					else
						bkcon.innerHTML = '<div class="alert alert-error"><h4>Sorry...</h4><div class="bodyText"><br/>We encountered an error while displaying your search results. We apologize for the inconvenience.<br/><br/>'+ repeatSearchLink +'</div><br><div class="bodyText">Message ID: SRCH.JS.GR</div></div>';
				}
				
				obj.req = null;
			};
			var showSearchResults = function(r) {
				var itemcount = '';
				if (r.success && r.success == 'true') {
					if (r.thisbucketcartitemtypeid) defaultBucketCartItemTypeID = r.thisbucketcartitemtypeid;

					if (bkcon) bkcon.innerHTML = r.resulthtml; 
					
					var elemFadeTR = document.getElementById('divFadeTR');
					if (elemFadeTR)
					{
						elemFadeTR.style.display = '';
					}
					window.setTimeout(introDivTimeout,5000);
					startFlash();
					
					itemcount = (r.itemcount == -1) ? 'N/A' : r.itemcount;
					b_calculateAndSetTotalCount(itemcount);
					b_setCount(obj,b_addCommas(itemcount).replace(/N\/A/, "--"));

					if (r.resultmode && r.resultmode == 'summary') b_loadSearchSummary(r);
					if (typeof r.strsummaryindiv !="undefined" && typeof r.strsummaryindiv.arrexperts !="undefined" && r.strsummaryindiv.arrexperts.length && typeof r.strsummaryindiv.arrexperts[0].arrsummaryindiv !="undefined") {
						let arrexpertcharts = r.strsummaryindiv.arrexperts.flatMap(item => item.arrsummaryindiv);
						b_loadSummaryIndiv(arrexpertcharts,r.strsummaryindiv.arrusstates);
					}

					loadSearchSponsorRightSideContainer();
					TSDocumentViewerCheckAutoShowDocument();

					medlinePriceEstimates();
					mcValidateLogins($('##bk_content'));

					obj.req = null;
				} 
				else if (r.success && r.success.toLowerCase() == 'false' && r.errmsg) {
					showSearchErr(r, r.errmsg);
				}
				else showSearchErr(r);
			};
			obj.req = TS_AJX('SBT' + obj.btid,'getResults',objParams,showSearchResults,showSearchErr,60000,showSearchTimeout);
			b_showCountLoading(obj);
		}

		function b_getlgloadingicon() {
			return '<img src="' + vars_spiu + 'images/indicator.gif" alt="Loading..." width="100" height="100">';
		}
		function b_getloadingicon() {
			return '<img src="' + vars_spiu + 'images/search/loading_animation2.gif" alt="Loading..." width="16" height="16"> ';
		}
		function b_geterroricon() {
			return '<img src="' + vars_spiu + 'images/search/exclamation.png" title="Error" width="16" height="16"> ';
		}

		/*used by Introduction bucket to swap a div*/
		var introMaxCount = 6;
		var introCurrCount = 0;
		function introDivTimeout() {
			var elemDivExp = document.getElementById("divExposition");
			var elemDivNoMatch = document.getElementById("divNoMatches");
			var elemForm = document.getElementById("divIntroSearch");
			
			if (!elemDivExp && !elemDivNoMatch && !elemForm) {
				if (introCurrCount < introMaxCount) {
					introCurrCount++;
					window.setTimeout(introDivTimeout,5000);
					return;
				}
			} else {
				introCurrCount = introMaxCount + 1;
			}
			
			if (elemDivExp) {
				$('.resultMsgExposition').html('Click each database to view matching results.');
			}
			if (elemDivNoMatch) {
				elemDivNoMatch.style.display = "block";
			}
			if (elemForm) {
				elemForm.style.display = "block";
			}
		}

		var currentSearchTotalCount = 0;

		function b_getTotalSearchCount() {
			return currentSearchTotalCount;
		}

		function b_calculateAndSetTotalCount(thisCount) {
			currentSearchTotalCount += $.isNumeric(thisCount) ? thisCount : 0;
			$('.resultTotalCountDisplay_cnt').text(b_addCommas(currentSearchTotalCount));
		}
		function b_showLoginContainer() {
			$('##MCSearchSummaryContainer').hide();
			$('##MCNotLoggedInContainer').show(300);
			$('html, body').animate({
				scrollTop: $('##bk_content').offset().top - 450
			}, 750);
		}
		function b_showInactiveUserInfo() {
			$('##MCInactiveUserSearchSummary').hide();
			$('##MCInactiveUserInfo').show(300);
			$('html, body').animate({
				scrollTop: $('##bk_content').offset().top - 450
			}, 750);
		}
		function b_loadSearchSummary(r) {
			if (r.strchart) {
				if (r.strchart.arrsearchsummary && r.strchart.arrsearchsummary.length)
					b_loadSearchSummaryBarChart(r.strchart);
				if (r.strchart.arrlocation && r.strchart.arrlocation.length)
					b_loadSearchSummaryLocationChart(r.strchart);
			}
		}
		function b_loadSearchSummaryBarChart(strchart) {
			if (!$('##MCSearchSummaryBarChart').length) {
				return false;
			}
			var containerElement = ($('##MCSearchSummaryBarChart').width() == 0) ? $('##bk_content') : $('##MCSearchSummaryBarChart');
			var chartWidth = (containerElement.width() < 375) ? containerElement.width() : 375;
			/*set the dimensions and margins of the graph*/
			var margin = { top: 30, right: 15, bottom: 60, left: 60 },
				width = chartWidth - margin.left - margin.right,
				height = 300 - margin.top - margin.bottom;

			var svg = d3.select("##MCSearchSummaryBarChart")
						.append("svg")
						.attr("width", width + margin.left + margin.right)
						.attr("height", height + margin.top + margin.bottom)
						.append("g")
						.attr("transform", "translate(" + margin.left + "," + margin.top + ")");

			/*X axis*/
			var x = d3.scaleBand()
						.range([ 0, width ])
						.domain(strchart.arrsearchsummary.map(function(d) { return d.year; }))
						.padding(0.2);
			
			svg.append("g")
				.attr("transform", "translate(0," + height + ")")
				.call(d3.axisBottom(x))
				.selectAll("text")
				.attr("transform", "translate(-10,0)rotate(-45)")
				.style("text-anchor", "end");

			/*Y axis*/
			var y = d3.scaleLinear()
						.domain([0, d3.max(strchart.arrsearchsummary, function(d) { return d.value; })])
						.range([ height, 0]);

			svg.append("g")
				.call(d3.axisLeft(y).ticks(5));

			/*Bars*/
			svg.selectAll(".bk-bg-chartblue")
				.data(strchart.arrsearchsummary)
				.enter()
				.append("rect")
				.attr("class", "bk-bg-chartblue")
				.attr("x", function(d) { return x(d.year); })
				.attr("y", function(d) { return y(d.value); })
				.attr("width", x.bandwidth())
				.attr("height", function(d) { return height - y(d.value); });

			/*Title*/
			if (strchart.searchsummaryobjtitle && strchart.searchsummaryobjtitle.length)
				svg.append("text")
					.attr("x", (width / 2))
					.attr("y", 0 - (margin.top / 2))
					.attr("text-anchor", "middle")
					.style("font-size", "16px")
					.style("font-weight", "bold")
					.text(strchart.searchsummaryobjtitle);

		}
		function b_loadSearchSummaryLocationChart(strchart) {
			if (!$('##MCSearchSummaryLocationChart').length) {
				return false;
			}

			var strData = {};
			var containerElement = ($('##MCSearchSummaryLocationChart').width() == 0) ? $('##bk_content') : $('##MCSearchSummaryLocationChart');
			var loc_chartWidth = (containerElement.width() - 10) > 500 ? 500 : (containerElement.width() - 10);
			var loc_chartHeight = 300;
			var scaleWidth = containerElement.width();
			if(scaleWidth < 400) scaleWidth = 400;
			else if(scaleWidth > 500) scaleWidth = 500;

			strchart.arrusstates.forEach(function(d) { 
				var value = 0;
				var arrFiltered = strchart.arrlocation.filter(
					function(obj,index) {
						return obj.statecode == d.statecode;
					}
				);

				if (arrFiltered.length) {
					value = arrFiltered[0].value;
				}

				strData[d.statecode] = { value:value, statecode:d.statecode, statename:d.statename, color:value >= 1 ? '##5bb75b' : d3.interpolateBlues(0.2)};
			});

			var strToolTip = { id:'bk_tooltip', fn:b_tooltipHtml };

			var b_onMouseOver = function(e,d) {
				var strFilteredState = { statename:'', value:'' };

				for (var statecode in strData) {
					if (strData[statecode].statename.toLowerCase() == d.properties.name.toLowerCase()) {
						strFilteredState = strData[statecode];
						break;
					}
				}

				d3.select("##"+strToolTip.id).transition().duration(200).style("opacity", .9);
				var relX = e.pageX - $("##MCSearchSummaryContainer").offset().left;
				var relY = e.pageY - $("##MCSearchSummaryContainer").offset().top-28;
				d3.select("##"+strToolTip.id).html(strToolTip.fn(strFilteredState.statename, strFilteredState.value))
					.style("left", (relX) + "px")     
					.style("top", (relY) + "px");
			};

			var b_onMouseOut = function(e,d) {
				d3.select("##"+strToolTip.id).transition().duration(500).style("opacity", 0);
			};

			var b_getFillColor = function(d) {
				var fillColor = '##ccc';
				for (var statecode in strData) {
					if (strData[statecode].statename.toLowerCase() == d.properties.name.toLowerCase()) {
						fillColor = strData[statecode].color;
						break;
					}
				}
				return fillColor;
			};

			var projection = d3.geoAlbersUsa()
								.translate([loc_chartWidth/2, loc_chartHeight/2])
								.scale([scaleWidth]);

			var path = d3.geoPath()
						.projection(projection);

			var svg = d3.select("##MCSearchSummaryLocationChart")
						.append("svg")
						.attr("width", loc_chartWidth)
						.attr("height", loc_chartHeight);

			d3.json("/assets/common/javascript/d3/6.5.0/us-states.json").then(function(json) {
				
				/*Bind data and create one path per GeoJSON feature*/
				svg.selectAll("path")
					.data(json.features)
					.enter()
					.append("path")
					.attr("d", path)
					.style("stroke", "##fff")
					.style("stroke-width", "0.5")
					.style("fill", b_getFillColor)
					.on("mouseover", b_onMouseOver)
					.on("mouseout", b_onMouseOut);

				/*Title*/
				if (strchart.locationobjtitle && strchart.locationobjtitle.length)
					svg.append('foreignObject')
					.attr('x', 0)
					.attr('y', 0)
					.attr('width', '100%')
					.attr('height', 40) 
					.html('<div id="MC-d3-loccharttitle" style="width: 100%;font-size:16px;font-weight:bold;text-align:center;">'+strchart.locationobjtitle+'</div>');
				
			});
		}
		function b_loadSummaryIndiv(arrcharts,arrusstates) {
			$.each(arrcharts, function( chartIndex, chartItem ) {
				if (chartItem.charttype == "map" && chartItem.arrlocation && chartItem.arrlocation.length > 0){
					b_loadUSStatesDataMap(chartItem.containerid, chartItem.charttitle, chartItem.arrlocation, arrusstates);
				}
				if (chartItem.charttype == "bar" && chartItem.arrdata && chartItem.arrdata.length > 0){
					b_loadBarChart(chartItem.containerid, chartItem.charttitle, chartItem.arrdata);
				}
			});
		}
		function b_loadUSStatesDataMap(containerid, charttitle, arrlocation, arrstates) {
			if (!$('##' + containerid).length) return false;

			var strData = {}, loc_chartWidth = 425, loc_chartHeight = 300;
			var strToolTip = { id:'bk_tooltip', fn:b_tooltipHtml };

			arrstates.forEach(function(d) { 
				var value = 0;
				var arrFiltered = arrlocation.filter(
					function(obj,index) {
						return obj.statename.toLowerCase() == d.statename.toLowerCase();
					}
				);

				if (arrFiltered.length) {
					value = arrFiltered[0].value;
				}

				strData[d.statecode] = { value:value, statecode:d.statecode, statename:d.statename, color: value > 0 ? d3.interpolateBlues( value/100 + 0.2 ) : d3.color("##d3d3d3")};
			});

			var b_onMouseOver = function(e,d) {
				var strFilteredState = { statename:'', value:'' };

				for (var statecode in strData) {
					if (strData[statecode].statename.toLowerCase() == d.properties.name.toLowerCase()) {
						strFilteredState = strData[statecode];
						break;
					}
				}

				d3.select("##"+strToolTip.id).transition().duration(200).style("opacity", .9);
			
				d3.select("##"+strToolTip.id).html(strToolTip.fn(strFilteredState.statename, strFilteredState.value))
					.style("left", (e.pageX) + "px")
					.style("top", (e.pageY - 28) + "px");
			};

			var b_onMouseOut = function(e,d) {
				d3.select("##"+strToolTip.id).transition().duration(500).style("opacity", 0);
			};

			var b_getFillColor = function(d) {
				var fillColor = '##ccc';
				for (var statecode in strData) {
					if (strData[statecode].statename.toLowerCase() == d.properties.name.toLowerCase()) {
						fillColor = strData[statecode].color;
						break;
					}
				}
				return fillColor;
			};

			var projection = d3.geoAlbersUsa()
								.translate([loc_chartWidth/2, loc_chartHeight/2])
								.scale([500]);

			var path = d3.geoPath()
						.projection(projection);

			var svg = d3.select("##" + containerid)
						.append("svg")
						.attr("width", loc_chartWidth)
						.attr("height", loc_chartHeight);

			d3.json("/assets/common/javascript/d3/6.5.0/us-states.json").then(function(json) {
				/*bind data and create one path per GeoJSON feature*/
				svg.selectAll("path")
					.data(json.features)
					.enter()
					.append("path")
					.attr("d", path)
					.style("stroke", "##fff")
					.style("stroke-width", "0.5")
					.style("fill", b_getFillColor)
					.on("mouseover", b_onMouseOver)
					.on("mouseout", b_onMouseOut);

				/*title*/
				if (charttitle && charttitle.length)
					svg.append("text")
						.attr("x", (loc_chartWidth / 2))
						.attr("y", 20)
						.attr("text-anchor", "middle")
						.style("font-size", "16px")
						.style("font-weight", "bold")
						.text(charttitle);
				
				/* Entries not being present on the states map */
				var arrNonStateCodes = ["DC","AS","GU","MP","PR","VI"];
				var resultArrNonStates = [];
				arrNonStateCodes.forEach(function(d) {
					var strThisEntry = strData[d];
					if (strThisEntry && strThisEntry.value > 0)
						resultArrNonStates.push(strThisEntry.statename + " : " + strThisEntry.value);
				});
				if(resultArrNonStates.length){
					svg.append("text")
						.attr("x", (loc_chartWidth / 2))
						.attr("y", 45)
						.attr("text-anchor", "middle")
						.style("font-size", "13px")
						.style("font-weight", "normal")
						.text("*" + resultArrNonStates.join(', '));
				}
			});
		}
		function b_loadBarChart(containerid, charttitle, arrdata) {
			if (!$('##' + containerid).length) return false;

			/*set the dimensions and margins of the graph*/
			var margin = { top: 30, right: 30, bottom: 60, left: 100 },
				width = 430 - margin.left - margin.right,
				height = 300 - margin.top - margin.bottom;

			const maxBarHeight = 40;

			var svg = d3.select('##' + containerid)
						.append("svg")
						.attr("width", width + margin.left + margin.right)
						.attr("height", height + margin.top + margin.bottom)
						.append("g")
						.attr("transform", "translate(" + margin.left + "," + margin.top + ")");

			/*X axis*/
			var x = d3.scaleLinear()
						.domain([0, d3.max(arrdata, function(d) { return d.value; })])
						.range([ 0, width ]);
			
			var xAxisTicks = x.ticks().filter(Number.isInteger);
			
			svg.append("g")
				.attr("transform", "translate(0," + height + ")")
				.call(d3.axisBottom(x).tickValues(xAxisTicks).tickFormat(d3.format('d')))
				.selectAll("text")
				.style("text-anchor", "end");

			/*Y axis*/
			var y = d3.scaleBand()
						.range([ 0, height])
						.domain(arrdata.map(function(d) { return d.key; }))
						.padding(0.2);

			const barHeight = Math.min(y.bandwidth(), maxBarHeight);

			svg.append("g")
				.call(d3.axisLeft(y));

			/*bars*/
			var bars = svg.selectAll(".bk-bg-chartblue")
				.data(arrdata)
				.enter();

			bars.append("rect")
				.attr("class", "bk-bg-chartblue")
				.attr("x", x(0))
				.attr("y", function(d) { return y(d.key) + (y.bandwidth() - barHeight) / 2; })
				.attr("width", function(d) { return x(d.value); })
				.attr("height", barHeight);

			bars.append("text")
				.text(function(d) { return d.value;})
				.attr("x", function(d) { return x(d.value) + 5; })
				.attr("y", function(d) { return (y(d.key) + (y.bandwidth()/2)); })
				.attr("fill", "black")
				.attr("font-family", "sans-serif")
				.style("font-size", "10px");

			/*title*/
			if (charttitle && charttitle.length)
				svg.append("text")
					.attr("x", (width / 2))
					.attr("y", 0 - (margin.top / 2))
					.attr("text-anchor", "middle")
					.style("font-size", "16px")
					.style("font-weight", "bold")
					.text(charttitle);
		}
		function b_tooltipHtml(title,value) {
			return '<h3 class="popover-title">'+title+'</h3><div class="popover-content">'+value+'</div>';
		}
		function b_getExpertDepositions(pos,r) {
			let obj = b_getObjFromBid(vars_bid);
			let expertsDepoContainer = typeof r == "number" ? $('##MCExpertDepositions'+r) : $(r).closest('.MCExpertDepositionsContainer');
			let encStr = expertsDepoContainer.data('encdepostr');
			
			let showResults = function(r) {
				if (r.success && r.success == 'true') {
					expertsDepoContainer.html(r.depositionshtml);
				} else {
					expertsDepoContainer.html('<div class="alert alert-error">We were unable to load this lawyer depositions.</div>');
				}
			};

			expertsDepoContainer.html('<div class="bk-text-center bk-mb-5">' + b_getloadingicon() + '</div>').removeClass('hide');
			gotoBKContainer(expertsDepoContainer.attr('id'));
			let objParams = { encodedString:encStr, startRow:pos, searchID:vars_sid, viewDirectory:'responsive' };
			TS_AJX('SBT' + obj.btid,'getExpertDepositions',objParams,showResults,showResults,60000,showResults);
		}
		function downloadDocImmedate(docid,format) {
			if ($('##docDownloadIframe').length) {
				$('##docDownloadWaitingModal').modal('show');
				document.getElementById('docDownloadIframe').src = '/?pg=tsDocDownload&da=download&mode=direct&immediate=1&t=' + format + '&did=' + docid;
				setTimeout(function(){ $('##docDownloadWaitingModal').modal('hide'); }, 3000);
			} else {
				self.location.href = '/?pg=tsDocDownload&da=download&mode=direct&immediate=1&t=' + format + '&did=' + docid;
			}
		}
		function oneClickPurchaseDepoDownload(docid, cr, filename,format, rowid, atype) {
			if(atype == "dbox"){
				$("##s_addtocart"+docid).html("<span class='bk-font-size-xs bk-mb-2 bk-font-weight-bold bk-text-nowrap'><i class='fa fa-check-square text-success' aria-hidden='true'></i> Purchased</span>");
			} else if (atype == "dload"){
				$("##s_addtocart"+docid).html("<span class='bk-font-size-xs bk-mb-2 bk-font-weight-bold bk-text-nowrap'><i class='fa fa-check-square text-success' aria-hidden='true'></i> Purchased</span>");
			} else{
				alert('Error encountered. Please try again.'); 
				return false;
			}

			if(atype == "dbox"){
				doDropboxSave(docid,filename,format,rowid);
				if($("##pdf_btn_" + rowid).length){
					$("##pdf_btn_" + rowid).attr("onclick","downloadDocImmedate('" + docid + "','pdf')");
				}
				if($("##tif_btn_" + rowid).length){
					$("##tif_btn_" + rowid).attr("onclick","downloadDocImmedate('" + docid + "','tif')");
				}
			} else if (atype == "dload"){
				downloadDocImmedate(docid,format);
				if($("##pdf_btn_" + rowid).length){
					$("##pdf_btn_" + rowid).attr("onclick","downloadDocImmedate('" + docid + "','pdf')");
				}
				if($("##tif_btn_" + rowid).length){
					$("##tif_btn_" + rowid).attr("onclick","downloadDocImmedate('" + docid + "','tif')");
				}
				if($("##dropb_btn_" + rowid).length){			
					$("##dropb_btn_" + rowid).attr("onclick","doDropboxSave('" + docid + "', '" + $("##dropb_btn_" + rowid).attr('data-elmname') + "', '" +  $("##dropb_btn_" + rowid).attr('data-elmext') + "', '" + rowid + "')");
				}	
				if(!$("##pdf_btn_" + rowid).length && !$("##tif_btn_" + rowid).length){
					$("##txt_" + docid).html('<button name="txt_btn_'+ rowid +'" type="button" class="btn btn-secondary btn-sm" onClick="downloadDocImmedate(' + docid + '\',\'txt\');" style="width:150px; margin-top:10px; text-align:center;"><i class="icon-download-alt" title="Download TEXT"></i> Download TEXT</button>');
				}
			} else{
				alert('Error encountered (oneClickPurchaseDepoDownload). Please try again.'); 
				return false;
			}

			if (isTsDocumentViewerOpen() && tsDocumentViewerCurrentDocumentID && ['dbox','dload'].indexOf(atype) != -1) {
				tsDownloadableDocumentIDs[docid] = 1;
				rebuildViewerToolbar(tsDocumentViewerCurrentDocumentID);
			}
		}
	</script>
	<cfif len(local.dropboxAppKey)>
		<script type="text/javascript" src="https://www.dropbox.com/static/api/2/dropins.js" id="dropboxjs" data-app-key="#local.dropboxAppKey#"></script>
		<script>
			window.___gcfg = { parsetags: 'explicit' };

			function doSaveToDropbox(thisArr) {
				if (thisArr.length > 0) {
					var filesArr = [];
					var tmpFilesArr = JSON.stringify(thisArr).replace(/\^~~~\^/g,'');
					var docArrFinal = JSON.parse(tmpFilesArr);
					
					docArrFinal.forEach(function(thisIndex) {
						var thisItemstr = {};
						thisItemstr.url = thisIndex.url;
						thisItemstr.filename = thisIndex.docname;
						filesArr.push(thisItemstr);
					});	

					var tmpFilesArr2 = JSON.stringify(filesArr).replace(/\^~~~\^/g,'');
					var docArrFinal2 = JSON.parse(tmpFilesArr2);
					var options = {		
						files: docArrFinal2,																						
						success: function () {
							var isSelectedItem = false;
							docArrFinal.forEach(function(thisIndex) {
								if(thisIndex.selected == true){
									$("##dropb_btn_" + thisIndex.rowid).html('<i class="icon-check icon-1x"></i> Saved to Dropbox');
									isSelectedItem = true;
								}
							});	
							/* if all files in screen are being saved */
							if (!isSelectedItem){
								$("##dropdownMenuLink").html('<b><i class="icon-check icon-1x"></i> Saved to Dropbox</b>');
								$("##paginationBar a").unbind('click');
								$("##paginationBar a").removeAttr('style');
							}
						},
						progress: function (progress) {
							var isSelectedItem = false;
							docArrFinal.forEach(function(thisIndex) {
								if(thisIndex.selected == true){
									$("##dropb_btn_" + thisIndex.rowid).html('<i class="icon-spin icon-spinner"></i> Saving file...');
									isSelectedItem = true;
								}
							});	
							/* if all files in screen are being saved */
							if (!isSelectedItem){
								$("##dropdownMenuLink").html('<i class="icon-spin icon-spinner"></i> Saving files...');
							}					
						},
						cancel: function () {						
							docArrFinal.forEach(function(thisIndex) {
								$("##dropb_btn_" + thisIndex.rowid).attr("disabled", false);
							});	
							$("##paginationBar a").unbind('click');
							$("##paginationBar a").removeAttr('style');	
						},
						error: function (errorMessage) {
							alert("We ran into an error saving your file. Try again.");
							docArrFinal.forEach(function(thisIndex) {
								$("##dropb_btn_" + thisIndex.rowid).attr("disabled", false);
							});	
							$("##paginationBar a").unbind('click');
							$("##paginationBar a").removeAttr('style');
						}
					};																				
					Dropbox.save(options);
				} else {
					alert("Error found");	
				}
			}
			function doDropboxSave(docid,filename,format,rowid) {
				var getURL = function(r) {
					doSaveToDropbox(r);
					$("##dropb_pdf_img_" + rowid).removeClass("grayed-out-img");
				};
				var docArr = [];
				var docstr = {};
				docstr.docid = docid;
				docstr.docname = filename;
				docstr.format = format;
				docstr.rowid = rowid;
				docstr.selected = true;
				docstr.url = "";
				docArr.push(docstr);
				$("##dropb_btn_" + rowid).attr("disabled", true);
 				var tmpDocArr = JSON.stringify(docArr).replace(/\^~~~\^/g,'');
				var objParams = { docarr:tmpDocArr };
				TS_AJX('MYDOCUMENTS','s3DocumentURL',objParams,getURL,getURL,10000,getURL);																
			}				
			function doDropboxSaveAll() {
				var getURL = function(r) {
					doSaveToDropbox(r)
				};	
				var docArr = [];
				$('.drop-box-file-list').each(function(i,o) {
					var docstr = {};
					docstr.docid = $(this).attr('data-elmid');
					docstr.docname = $(this).attr('data-elmname');
					docstr.format = $(this).attr('data-elmext');
					docstr.rowid = $(this).attr('data-rowid');
					docstr.selected = false;
					docstr.url = "";
					docArr.push(docstr);
				});
				$("##paginationBar a").css({"color":"##888888", cursor: "default"}).click(function(e) {
					e.preventDefault();
				});
				docArr.forEach(function(thisIndex) {
					$("##dropb_btn_" + thisIndex.rowid).attr("disabled", true);
				});
				var tmpDocArr = JSON.stringify(docArr).replace(/\^~~~\^/g,'');
				var objParams = { docarr:tmpDocArr };
				TS_AJX('MYDOCUMENTS','s3DocumentURL',objParams,getURL,getURL,10000,getURL);													
			}
		</script>		
	</cfif>
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/datatables/1.10.24/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/jQuerySteps/jquery.steps.css">
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.bucketJS,'\s{2,}',' ','ALL')#">