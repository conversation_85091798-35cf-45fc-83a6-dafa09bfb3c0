<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#
<cfif local.strResults.itemCount EQ 0>
	<div class="s_rnfne">#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName,arguments.viewDirectory)#</div>
<cfelse>
	<cfset local.expertName = UcFirst(replace('#local.strSearch.expertFName# #local.strSearch.expertLName#','"','','all'),true)>
	<cfset local.exceedsLimit = local.strResults.itemCount gt local.strResults.matchesLimit>

	<div id="MCSearchSummaryContainer" class="bk-mb-3">
		<div id="divExpertConnectResults" class="bk-mt-4">
			<div>Expert Name: <b>#local.expertName#</b></div>
			<div class="tsAppBodyText bk-mb-4 bk-mt-2">
				We found <b>#Numberformat(local.strResults.itemCount)#</b> unique plaintiff lawyers with knowledge of #local.expertName# in <b>#local.strResults.numStates#</b> states.
			</div>
			<div class="bk-d-flex bk-mb-3">
				<button type="button" class="tsAppBodyButton" onclick="toggleEmailInquiryForm(true);">Preview List of Lawyers</button>
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<button type="button" id="btnDownloadExpMatches" class="tsAppBodyButton bk-ml-2" onclick="downloadExpertMatches();"><span id="downloadMatchBtnLabel">Download Matches</span></button>
				</cfif>
			</div>
			<div class="bk-d-flex bk-flex-wrap bk-mt-4">
				<div id="MCSearchSummaryLocationChart" class="bk-mb-3"></div>
				<div id="bk_tooltip"></div>
			</div>
		</div>

		<cfset local.sendingLawyerName = local.qryDepoMember.name>
		<cfset local.arrDepoMemberNodes = XMLSearch(local.strResults.lawyersDataXML,"//lawyer/@depomemberdataid")>
		<cfset local.depoMemberDataIDList = ArrayToList(arrayMap(local.arrDepoMemberNodes, function(item){ return item.xmlvalue; }))>
		<div id="divEmailInquiryContainer" style="display:none;">
			<cfif variables.cfcuser_DisplayVersion is 1>
				<div class="tsAppBodyText">
					<p>TrialSmith DepoConnect allows members to easily email other TrialSmith members who might have experience with a specific Expert Witness.</p>
					<p>Lawyers can reply with messages and documents which are sent to your email inbox. </p>
					<p>Lawyers' responses to your questions are private and are not shared with other TrialSmith members.</p>
					<p>Any depositions shared may also be added to TrialSmith Deposition Bank.</p>
					<br/>
					<div><a href="/?pg=upgradeTrialSmith"><b>UPGRADE NOW</b></a></div>
				</div>
			<cfelseif local.qryLoggedInUserOptOutInfo.isOptedOut>
				<cfset local.TSSiteInfo = application.objSiteInfo.getSiteInfo('TS')>
				<cfset local.emailPreferencesURL = "#local.TSSiteInfo.scheme#://#local.TSSiteInfo.mainhostname#/?pg=emailpreferences">
				<div class="tsAppBodyText">
					<div>
						Your email address is currently <cfif len(local.qryLoggedInUserOptOutInfo.optOutListName)>opted out of these emails (#local.qryLoggedInUserOptOutInfo.optOutListName#) or it is</cfif> on the Global Opt-Out List. Since this is an email service, you must change your preferences if you want to participate.
					</div>
					<br/>
					<div>Click <a href="#local.emailPreferencesURL#" target="_blank"><b>here</b></a> to Manage Your Preferences</div>
				</div>
			<cfelse>
				<cfif local.exceedsLimit>
					<div class="bk-mb-3">
						<div class="bk-mb-2" style="color:##b30000;">
							<b>#local.strResults.itemCount#</b> matching lawyers seems like a high number and it exceeds our safeguard limit. So only the first <b>#local.strResults.matchesLimit#</b> lawyers will be shown.
						</div>
						Please verify your search criteria. Reach out to TrialSmith Support if you feel this message is in error.
					</div>
				</cfif>
				<div class="bk-mb-2">
					<a href="javascript:toggleEmailInquiryForm(false);"><i class="icon-chevron-left"></i> Back to Search Results</a>
				</div>
				<div id="divEmailInquiryForm">
					<h3 class="bk-mb-2">Create Your Email Inquiry for #local.expertName#</h3>
					<p>First, preview the list of lawyers. Second, verify your profile information. Third, write your message. Fourth, review and send your email. Lawyers will respond directly to your email inbox.</p>
					<form name="frm_inquiry" id="frm_inquiry">
						<input type="hidden" name="inq_depoMemberDataIDList" id="inq_depoMemberDataIDList" value="#local.depoMemberDataIDList#">
						<input type="hidden" name="inq_expertName" id="inq_expertName" value="#local.expertName#">
						<input type="hidden" name="inq_requestorName" id="inq_requestorName" value="#local.sendingLawyerName#">

						<div id="expertInquiryWizard">
							<!--- Step 1 --->
							<h3 class="font-weight-bold mb-3">Choose Recipients</h3>
							<section>
								<table id="tblExpertConnectLawyersToContact" class="display" style="width:100%">
									<thead>
										<tr>
											<th align="center">Selection</th>
											<th align="left">Name</th>
											<th align="left">Firm</th>
											<th align="left">State</th>
										</tr>
									</thead>
									<tbody>
										<cfset local.arrLawyerNodes = XMLSearch(local.strResults.lawyersDataXML,"//lawyer")>
										<cfloop array="#local.strResults.lawyersDataXML.xmlChildren#" index="local.thisfield">
											<cfset local.tmpAtt = local.thisfield.xmlattributes>
											<tr>
												<td align="center">
													<p class="elmCheckVal" style='display:none;'>1</p>
													<input type="checkbox" name="recipientCheckbox" class="recipientCheckbox" value="#local.tmpAtt.depoMemberDataID#" checked="checked">
												</td>
												<td>#local.tmpAtt.name#</td>
												<td>#local.tmpAtt.firm#</td>
												<td>#local.tmpAtt.state#</td>
											</tr>
										</cfloop>
									</tbody>
								</table>
							</section>
							<!--- Step 2 --->
							<h3 class="font-weight-bold mb-3">Verify Your Profile Data</h3>
							<section>
								<table border="0" cellspacing="0" cellpadding="2">
									<cfoutput>
									<tr>
										<td class="tsAppBodyText">Name:</td>
										<td>
											<input type="text" name="inq_name" id="inq_name" class="tsAppBodyText" size="32" value="#local.sendingLawyerName#" disabled>
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">Email Address</td>
										<td>
											<input type="text" name="inq_email" id="inq_email" class="tsAppBodyText" size="32" maxlength="400" value="#local.qryDepoMember.Email#">
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">Firm</td>
										<td>
											<input type="text" name="inq_firm" id="inq_firm" class="tsAppBodyText" size="32" maxlength="200" value="#local.qryDepoMember.BillingFirm#">
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">Address</td>
										<td>
											<input type="text" name="inq_address1" id="inq_address1" class="tsAppBodyText" size="32" maxlength="150" value="#local.qryDepoMember.BillingAddress#">
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">Address Line 2</td>
										<td>
											<input type="text" name="inq_address2" id="inq_address2" class="tsAppBodyText" size="32" maxlength="150" value="#local.qryDepoMember.BillingAddress2#">
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">Address Line 3</td>
										<td>
											<input type="text" name="inq_address3" id="inq_address3" class="tsAppBodyText" size="32" maxlength="150" value="#local.qryDepoMember.BillingAddress3#">
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">City</td>
										<td>
											<input type="text" name="inq_city" id="inq_city" class="tsAppBodyText" size="32" maxlength="100" value="#local.qryDepoMember.BillingCity#">
										</td>
									</tr>
									</cfoutput>
									<tr>
										<cfoutput><td class="tsAppBodyText">State</td></cfoutput>	
										<td>
											<select name="inq_statecode" id="inq_statecode" class="tsAppBodyText" style="width:250px;">
												<option value=""></option>
												<cfoutput query="local.qryStates">
													<option value="#local.qryStates.code#" <cfif local.qryStates.code eq local.qryDepoMember.BillingState>selected</cfif>>#local.qryStates.name# (#local.qryStates.code#)</option>
												</cfoutput>
											</select>
										</td>
									</tr>
									<cfoutput>	
									<tr>
										<td class="tsAppBodyText">Postal Code</td>
										<td>
											<input type="text" name="inq_postalcode" id="inq_postalcode" class="tsAppBodyText" size="32" maxlength="15" value="#local.qryDepoMember.BillingZip#">
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText">Law Firm Website</label>
										<td>
											<input type="text" name="inq_website" id="inq_website" class="tsAppBodyText" size="32" maxlength="400" value="">
										</td>
									</tr>
									<tr>
										<td colspan="2">
											<hr/>
											<span class="bk-mr-2 tsAppBodyText">Save Changes to Your TrialSmith Record ?</span>
											<label>
												<input type="radio" name="updateTSRecord" id="optionsRadio1" value="1" class="mc_inlinecheckbox">Yes
											</label>
											<label>
												<input type="radio" name="updateTSRecord" id="optionsRadio2" class="mc_inlinecheckbox" value="0" checked>No
											</label>
										</td>
									</tr>
									</cfoutput>
								</table>
							</section>
							<!--- Step 3 --->
							<h3 class="font-weight-bold mb-3">Create Your Email</h3>
							<section>
								<div class="tsAppBodyText bk-mb-1">Subject:</div>
								<div class="bk-d-flex bk-flex-align-center bk-mt-2">
									<span class="tsAppBodyText bk-mr-1" style="color:grey;">#local.expertName#</span>
									<input id="inq_topic" type="text" size="35" maxlength="250" class="tsAppBodyText" placeholder="Looking for Information and Depositions">
								</div>
								<div class="bk-mb-3 bk-mt-3">
									<div class="tsAppBodyText bk-mb-1">Email Message:</div>
									<div class="bk-mb-1 tsAppBodyText" style="color:grey;">Tell lawyers the types of information you're seeking on #local.expertName#</div>
									<textarea cols="50" rows="5" id="inq_descriptionOfAsk" name="inq_descriptionOfAsk" class="bk-w-100" placeholder="Type your detailed message, ask for depositions, and other specific information you need"></textarea>
								</div>
							</section>
							<!--- Step 4 --->
							<h3 class="font-weight-bold mb-3">Review and Send</h3>
							<section>
								<div id="divEmailInquiryPreviewLoading" class="bk-mt-3" style="display:none;text-align:center;"><i class="icon-spin icon-spinner bk-mr-2"></i><b>Loading Preview...</b></div>
								<div id="divEmailInquiryPreview" style="display:none;">
									<p>

										We'll send your message to the <b id="selRecipientsCount">#Numberformat(local.strResults.itemCount)#</b> unique plaintiff attorneys you selected.<br/><br/>
										Your email address will be masked, but replies will be sent directly to your email account where you can reply and interact.<br/><br/>
										Lawyers can attach documents which will be sent directly to your email account at no charge. TrialSmith retains copies of new transcripts and gives a purchase credit to the lawyer who sends the transcript.
									</p>
									<div id="divMessagePreview" style="border:1px dashed ##666;padding:10px;overflow:auto;padding-bottom:20px;">
										<table cellpadding="1" cellspacing="0" style="font-size:smaller;">
											<tr><td class="bk-align-top">From:</td><td width="3"></td><td>#local.sendingLawyerName#<span id="spFromFirm"></span></td></tr>
											<tr><td class="bk-align-top" nowrap>Reply-To:</td><td width="3"></td><td><span id="spReplyTo"></span> (your email address will be masked)</td></tr>
											<tr><td class="bk-align-top">Subject:</td><td width="3"></td><td>#local.expertName# - <span id="spSubject"></span></td></tr>
										</table>
										<div style="margin:12px 0;border-bottom:1px solid ##ccc;"></div>
										<div id="divInquiryEmailBody"></div>
									</div>
								</div>
							</section>
							<div id="err_email_inq" class="bk-alert-error" style="display:none;"></div>
						</div>
					</form>
				</div>
				<div id="divSendingInquiryLoading" class="bk-mt-3" style="display:none;text-align:center;"><i class="icon-spin icon-spinner bk-mr-2"></i><b>Sending Email Inquiry...</b></div>
				<div id="divEmailInquirySuccess" class="bk-alert-success" style="display:none;margin-top:10px;">Your Email Inquiry for <b>#local.expertName#</b> has been successfully submitted for approval.</div>
			</cfif>
		</div>
	</div>
</cfif>
</cfoutput>