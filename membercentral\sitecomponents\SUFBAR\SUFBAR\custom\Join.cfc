<cfcomponent extends="model.customPage.customPage" output="true">
	<cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			variables.redirectlink = "/?pg=join";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.crlf = chr(13) & chr(10);
			variables.currentDate = dateTimeFormat(now());
			variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

			variables.contactTypeFieldSetUID = "7D712CB0-CDF3-4A0F-9E43-B993F6F501F1";
			variables.personalInfoFieldSetUID = "B84997F3-B4A8-485B-ACD1-B17002C6C0EE";
			variables.homeAddressFieldSetUID = "F4CAC094-2F06-4980-9F83-61D897AC9DE7";
			variables.orgAddressFieldSetUID = "7FF7DA7D-7904-4FB6-BA0F-CC763351649F";
			variables.studentInfoFieldSetUID = "6BD709E1-94F9-41C3-83B0-436E2475A97B";
			variables.demographicInfoFieldSetUID = "7886DF08-D2D9-401F-8902-6BB2B5850924";
			variables.professionalInfoFieldSetUID = "F449FE76-4076-4C72-A14A-48A8CA25AD68";
			variables.recommendationFieldSetUID = "5A6DABA8-37B2-453F-BDD7-CF43D194614E";
			variables.paralegalDemographicInfoFieldSetUID = "7B4FFA44-0831-4635-817F-915648D98B89";


			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="CC911D9C-C6E4-4A0C-A3C6-3E76595C6BAD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RatesForBilledStatus",type="STRING",desc="Comma Seperated UIDs for rates to be submitted in Billed status",value="C7BDC871-780D-4E52-AE5A-E7614889B97D,6D967407-068E-4CFB-AACF-CF51AFCE3531,101B30C9-8431-4D15-9541-CEE306FDCE6B,38DE9E03-45F0-4FD6-B09F-0C4DBDF35B24" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a SCBA member. If you have questions about your membership, please call (631) 234-5511 (ext. 221), or E-Mail <NAME_EMAIL>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership,  please call (631) 234-5511 (ext. 221), or E-Mail <NAME_EMAIL>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="SCBA Membership Application" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalRecContent",type="CONTENTOBJ",desc="Content at top of Professional Rec Fields",value="Please upload your letter of recommendation or your completed recommendation form." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalLicContent",type="CONTENTOBJ",desc="Content at top of Professional License Fields",value="Please enter your bar dates" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="DE6D2EF3-07DB-4D58-87D6-4AAAF0F01AD9" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns",type="STRING",desc="UIDs for Add-Ons that qualified users will have to opt out of",value="1075E4F1-A35A-49E1-B441-B2717B990BFD,D4795C8C-8566-4C1A-A894-97CD00C9A96F" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="SCBACredit" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="SCBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="SCBAACH" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="SCBA Membership Application Receipt" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RecFormLink",type="STRING",desc="Opens up official Rec Form to send to SCBA for review",value="Click here to open and complete the official recommendation form, this will be sent to SCBA staff for review." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmJoin',
				formNameDisplay=variables.strPageFields.FormTitle,
				orgEmailTo=variables.strPageFields.StaffConfirmationTo,
				memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
			));
		
			variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="#variables.formname#");

			/* ******************* */
			/* Member History Vars */
			/* ******************* */
			variables.useHistoryID = 0;
			variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
			variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
			variables.historyStartedText = "Member started Membership form.";
			variables.historyCompletedText = "Member completed Membership form.";
			variables.origMemberID = variables.useMID;
			if(local.formAction neq "showLookup" and 
				local.formAction neq "processLookup" and 
				structKeyExists(session, "formFields") and 
				structKeyExists(session.formFields, "step0") and 
				structKeyExists(session.formFields.step0, "memberID") and 
				int(val(session.formFields.step0.memberID)) gt 0){
					variables.useMID = session.formFields.step0.memberID;
					if(structKeyExists(session.formFields.step0, "origMemberID") and int(val(session.formFields.step0.origMemberID)) gt 0){
						variables.origMemberID = session.formFields.step0.origMemberID;
					}

			}else if(session.cfcuser.memberdata.identifiedAsMemberID){
				variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
				variables.origMemberID = variables.useMID;
			}
			if( local.formAction neq "showLookup" and 
				local.formAction neq "processLookup" and 
				local.formAction neq "showMemberInfo" and 
				local.formAction neq "processMemberInfo" and 
				structKeyExists(session.formFields, "step1") and 
				structKeyExists(session.formFields.step1, "useHistoryID") and 
				int(val(session.formFields.step1.useHistoryID)) gt 0){
					variables.useHistoryID = int(val(session.formFields.step1.useHistoryID));
			}

			local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
			local.subStatus = "";
			if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
				local.subStatus = hasSub(int(variables.useMID));
			}

			/* ***************** */
			/* Form Process Flow */
			/* ***************** */
			if(local.isSuperUser){
				local.returnHTML = showError(errorCode='admin');  
			}else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
				local.returnHTML = showError(errorCode=local.subStatus);  
			}else{
				switch (local.formAction) {
					case "processLookup":
						switch (processLookup(rc=arguments.event.getCollection())) {
							case "success":
								local.returnHTML = showMemberInfo();
								break;        
							default:
								local.returnHTML = showError(errorCode='error');
								break;
						}
						break;
					case "processMemberInfo":
						switch (processMemberInfo(rc=arguments.event.getCollection())) {
							case "success":
								local.returnHTML = showMembershipInfo();
								break;
							case "spam":
								local.returnHTML = showError(errorCode='spam');
								break;
							default:
								local.returnHTML = showError(errorCode='error');
								break;
						}
						break;
					case "processMembershipInfo":
						switch (processMembershipInfo(rc=arguments.event.getCollection())) {
							case "success":
								local.returnHTML = showPayment();
								break;
							default:
								local.returnHTML = showError(errorCode='error');
								break;
						}
						break;
					case "processPayment":
						local.processStatus = processPayment(event=arguments.event);
						switch (local.processStatus) {
							case "success":
								local.returnHTML = showConfirmation();
								application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
								structDelete(session, "formFields");
								break;
							default:
								local.returnHTML = showError(errorCode='failpayment');
								break;
						}
						break;
					case "showMembershipInfo":
						local.returnHTML = showMembershipInfo();
						break;
					case "showMemberInfo":
						local.returnHTML = showMemberInfo();
						break;
					default:
						if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
						if(structKeyExists(session, "formFields")) structDelete(session, "formFields");
						local.returnHTML = showLookup();
						break;
				}
			}
			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.header ##mainNavBar { display: none;}
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##cboxOverlay{
					z-index: 99998 !important;
				}
				##colorbox{
					z-index: 99999 !important;
				}
				.acntLookUpBtn,.acntLookUpMessage{
					display:inline!important;
					float: right;
					width: 48%!important;
				}
				.acntLookUpBtn{    margin-right: 5px; }
				.acntLookUpMessage .span12{
					margin-left:0px !important;
				}
				##zoneMain{margin-bottom:30px;}
				@media screen and (min-width: 632px) and (max-width: 980px){
					.acntLookUpBtn {
						margin-top: 52px;
					}
				}
				@media screen and (min-width: 980px){
					.acntLookUpBtn {
						margin-top: 45px;
					}
				}                
				.acntLookUpBtn  {
				   position: absolute;
				   width: 50%;
				   height: 100%;
				   min-height: 100%;
				}
				.centerit {
				   position: absolute;
				   top: 50%;
				   width: 100%;
				   text-align: center;
				}
				.centerit button {
					position: relative;
					top: -35px;
				}
				.center-holder{
					position: relative;
				}
				.acntLookUpMessage p{font-size:15px;}
				.acntLookUpMessage {
					margin-left: 48%!important;
				}
				@media screen and (min-width: 0px){
					.acntLookUpBtn {
						margin-top: 0px!important;
					}
				}
				@media screen and (min-width: 359px) and (max-width: 368px){
					.acntLookUpBtn button{
						font-size:13px;
					}
				}
				@media screen and (max-width: 359px){
					.acntLookUpBtn button{
						font-size:11px;
					}
				}
				.captchaWrap img{
					display: unset !important;
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);
				}
				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}
				function openRecFormPopUp() {
					hideAlert();
					var screenWidth = $(window).width();

					if (screenWidth > 1400) {
						$.colorbox( {innerWidth:'50%', innerHeight:'90%', href:'https://memcentral.wufoo.com/forms/m1dpiloh1ppdjyd/', iframe:true, overlayClose:false} );
					}else if (screenWidth > 1000 && screenWidth <= 1400) {
						$.colorbox( {innerWidth:'70%', innerHeight:'90%', href:'https://memcentral.wufoo.com/forms/m1dpiloh1ppdjyd/', iframe:true, overlayClose:false} );
					} else {
						$.colorbox( {innerWidth:'90%', innerHeight:'100%', href:'https://memcentral.wufoo.com/forms/m1dpiloh1ppdjyd/', iframe:true, overlayClose:false} );
					}
					
				}
				function addMember(memObj) {
					assignMemberData(memObj);
				}
				function resizeBox(newW,newH) {
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				window.onhashchange = function() {
					if (location.hash.length > 0) {
						step = parseInt(location.hash.replace('##',''),10);
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");

							mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);
						}
					} else {
						step = 1;
					}
					prevStep = step;
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});
						}
					});
				});

				function toggleFTM() {}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
				<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
				
				<cfif len(variables.strPageFields.FormTitle)>
					<div class="row-fluid" id="FormTitleId">
						<div class="span12">
							<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
						</div>
					</div>
				</cfif>
				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step0")>
			<cfset structDelete(session.formFields, "step0")>
		</cfif>
		<cfset session.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        
		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.fieldSetUIDlist = '#variables.demographicInfoFieldSetUID#,#variables.paralegalDemographicInfoFieldSetUID#,#variables.contactTypeFieldSetUID#,#variables.personalInfoFieldSetUID#,#variables.homeAddressFieldSetUID#,#variables.orgAddressFieldSetUID#,#variables.studentInfoFieldSetUID#,#variables.professionalInfoFieldSetUID#,#variables.recommendationFieldSetUID#'>

		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>

		<cfset local.withinSuffolkCountyField = {fieldCode="",fieldLabel=""}>
		<cfset local.departmentField = {fieldCode="",fieldLabel=""}>
		<cfset local.lawSchoolField = {fieldCode="",fieldLabel=""}>
		<cfset local.anticipatedGraduationDateField = {fieldCode="",fieldLabel=""}>
		<cfset local.enrollmentVerificationLetterField = {fieldCode="",fieldLabel=""}>
		<cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

			<cfset local.withinSuffolkCountyData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Within Suffolk County']")/>
			<cfif arrayLen(local.withinSuffolkCountyData)>
				<cfset local.withinSuffolkCountyField.fieldCode = local.withinSuffolkCountyData[1].XmlAttributes.fieldCode>
				<cfset local.withinSuffolkCountyField.fieldLabel = local.withinSuffolkCountyData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.departmentData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Department']")/>
			<cfif arrayLen(local.departmentData)>
				<cfset local.departmentField.fieldCode = local.departmentData[1].XmlAttributes.fieldCode>
				<cfset local.departmentField.fieldLabel = local.departmentData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.lawSchoolData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Law School']")/>
			<cfif arrayLen(local.lawSchoolData)>
				<cfset local.lawSchoolField.fieldCode = local.lawSchoolData[1].XmlAttributes.fieldCode>
				<cfset local.lawSchoolField.fieldLabel = local.lawSchoolData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.anticipatedGraduationDateData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Anticipated Graduation Date']")/>
			<cfif arrayLen(local.anticipatedGraduationDateData)>
				<cfset local.anticipatedGraduationDateField.fieldCode = local.anticipatedGraduationDateData[1].XmlAttributes.fieldCode>
				<cfset local.anticipatedGraduationDateField.fieldLabel = local.anticipatedGraduationDateData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.enrollmentVerificationLetterData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Enrollment Verification Letter']")/>
			<cfif arrayLen(local.enrollmentVerificationLetterData)>
				<cfset local.enrollmentVerificationLetterField.fieldCode = local.enrollmentVerificationLetterData[1].XmlAttributes.fieldCode>
				<cfset local.enrollmentVerificationLetterField.fieldLabel = local.enrollmentVerificationLetterData[1].XmlAttributes.fieldLabel>
			</cfif>

			<cfset local.letterOfRecommendationData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Letter of Recommendation']")/>
			<cfif arrayLen(local.letterOfRecommendationData)>
				<cfset local.letterOfRecommendationField.fieldCode = local.letterOfRecommendationData[1].XmlAttributes.fieldCode>
				<cfset local.letterOfRecommendationField.fieldLabel = local.letterOfRecommendationData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.recommendationFormData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='I elect to complete the recommendation form']")/>
			<cfif arrayLen(local.recommendationFormData)>
				<cfset local.recommendationFormField.fieldCode = local.recommendationFormData[1].XmlAttributes.fieldCode>
				<cfset local.recommendationFormField.fieldLabel = local.recommendationFormData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.personalInfoFieldSetUID#,#variables.orgAddressFieldSetUID#',local.fieldSetUid)>
                <cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				<cfif NOT variables.isLoggedIn AND session.cfcuser.memberdata.identifiedAsMemberID EQ 0 AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>			
					<cfloop collection="#local.memberFieldData#" item="local.key" >
						<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
							<cfset StructDelete(local.memberFieldData, local.key)>
						</cfif>					
					</cfloop>
				</cfif>
            </cfif>
		</cfloop>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfset StructAppend(local.strData.one,local.memberFieldData)/>
		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>    
		<cfset local.strData.one.siteID = variables.siteID>
		<cfset local.categoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7D712CB0-CDF3-4A0F-9E43-B993F6F501F1', mode="collection", strData=local.strData.one)>
		<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
		<cfif variables.isLoggedIn OR (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),variables.useMID))>
			<cfset local.personalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='B84997F3-B4A8-485B-ACD1-B17002C6C0EE', mode="collection", strData=local.strData.one)>
		<cfelse>
			<cfset local.personalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='B84997F3-B4A8-485B-ACD1-B17002C6C0EE', mode="collection")>
		</cfif>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='F4CAC094-2F06-4980-9F83-61D897AC9DE7', mode="collection", strData=local.strData.one)>
		<cfset local.orgAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7FF7DA7D-7904-4FB6-BA0F-CC763351649F', mode="collection", strData=local.strData.one)>
		<cfset local.studentInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='6BD709E1-94F9-41C3-83B0-436E2475A97B', mode="collection", strData=local.strData.one)>
		<cfset local.demographicInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7886DF08-D2D9-401F-8902-6BB2B5850924', mode="collection", strData=local.strData.one)>
		<cfset local.paralegalDemographicInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7B4FFA44-0831-4635-817F-915648D98B89', mode="collection", strData=local.strData.one)>
		<cfset local.addressPrefFieldSet = application.objCustomPageUtils.renderFieldSet(uid='C51B64E5-BBCD-4F1B-A8C3-2DA9A135FB45', mode="collection", strData=local.strData.one)>
		<cfset local.professionalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='F449FE76-4076-4C72-A14A-48A8CA25AD68', mode="collection", strData=local.strData.one)>
		<cfset local.recommendationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='5A6DABA8-37B2-453F-BDD7-CF43D194614E', mode="collection", strData=local.strData.one)>

		<cfset local.contacttype = "">
		<cfloop collection="#local.categoryFieldSet.strFields#" item="local.thisField">
			<cfif local.categoryFieldSet.strFields[local.thisField] eq "Contact Type">
				<cfset local.contacttype = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.recFormSel = "">
		<cfloop collection="#local.recommendationFieldSet.strFields#" item="local.thisField">
			<cfif local.recommendationFieldSet.strFields[local.thisField] eq "I elect to complete the recommendation form">
				<cfset local.recFormSel = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset index = 1>
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[index] = local.qryOrgProLicenseStatuses.statusName>    
			<cfset index=index + 1>
		</cfloop>
		
		<cfset local.mcCreationDateFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='MC Creation Date')>
		<cfset local.mcCreationDateFieldCode = "md_" & local.mcCreationDateFieldInfo.COLUMNID/>
		<cfset local.mcCreationDate = application.objMember.getMemberViewData(memberid=variables.useMID,columnName='MC Creation Date',orgid=variables.orgID)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				div.tsAppSectionHeading{margin-bottom:10px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				div.alert-danger{
					font-size: 14px;
					line-height: 20px;
					border-radius: 10px;
					padding: 10px !important;
				}
			</style>
			<script language="javascript">
				function afterFormLoad(){
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}

				function resetProfessionalLicenses(){
					$('.mpl_pltypeid').multiselect("uncheckAll"); 
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}
				}
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					#local.categoryFieldSet.jsValidation#
					#local.personalInfoFieldSet.jsValidation#
					#local.homeAddressFieldSet.jsValidation#
					var isNewYorkSelected = false;
					<cfif len(local.contacttype)>
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						if(mcSel != 'Law Student'){
							#local.orgAddressFieldSet.jsValidation#
						}
						if(mcSel == 'Attorney' || mcSel == 'Judge') {
							<cfif len(trim(local.departmentField.fieldCode))>
								if($.trim($(_CF_this['#local.departmentField.fieldCode#']).find('option:selected').text()) == ''){
									arrReq[arrReq.length] = "#local.departmentField.fieldLabel#" + " is required.";
								}
							</cfif>
							<cfif len(trim(local.withinSuffolkCountyField.fieldCode))>
								if($.trim($(_CF_this['#local.withinSuffolkCountyField.fieldCode#']).find('option:selected').text()) == ''){
									arrReq[arrReq.length] = "#local.withinSuffolkCountyField.fieldLabel#" + " is required.";
								}
							</cfif>
							var isProfLicenseRequired = true;
							if(isProfLicenseRequired){
								var prof_license = $('.mpl_pltypeid').val();
								var isProfLicenseSelected = false;
								if(prof_license != "" && prof_license != null){
									isProfLicenseSelected = true;
									$.each(prof_license,function(i,val){
										var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();
										if(text == 'New York'){
											isNewYorkSelected = true;
										}
										if($("##mpl_"+val+"_activeDate").val().length == 0){ arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
										if($.trim($("##mpl_"+val+"_status").val()) == ''){  arrReq[arrReq.length] = 'Enter your '+text +' Status.'; }
									});
								}
								if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
							}
						}
						if(mcSel == 'Attorney' && $('###local.withinSuffolkCountyField.fieldCode# option:selected').text() == 'Yes'){
							var prof_license = $('.mpl_pltypeid').val();
							if(prof_license != "" && prof_license != null){
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseName").val();
									if(text == 'New York' && !isNewYorkSelected){
										if($("##mpl_"+val+"_activeDate").val().length == 0){ arrReq[arrReq.length] = 'Enter your '+text +' #variables.strProfLicenseLabels.profLicenseDateLabel#.'; }
										if($.trim($("##mpl_"+val+"_status").val()) == ''){ arrReq[arrReq.length] = 'Enter your '+text +' Status.'; }
									}
								});
							}
						}
						if(mcSel == 'Law Student') {
							<cfif len(trim(local.lawSchoolField.fieldCode))>
								if($.trim($(_CF_this['#local.lawSchoolField.fieldCode#']).val()) == ''){
									arrReq[arrReq.length] = "#local.lawSchoolField.fieldLabel#" + " is required.";
								}
							</cfif>
							<cfif len(trim(local.anticipatedGraduationDateField.fieldCode))>
								if($.trim($(_CF_this['#local.anticipatedGraduationDateField.fieldCode#']).val()) == ''){
									arrReq[arrReq.length] = "#local.anticipatedGraduationDateField.fieldLabel#" + " is required.";
								}
							</cfif>
							<cfif len(trim(local.enrollmentVerificationLetterField.fieldCode))>
								if($.trim($(_CF_this['#local.enrollmentVerificationLetterField.fieldCode#']).val()) == ''){
									arrReq[arrReq.length] = "#local.enrollmentVerificationLetterField.fieldLabel#" + " is required.";
								}
							</cfif>
						}
						if(mcSel != 'Law Student'){
							#local.addressPrefFieldSet.jsValidation#
						}
						if(mcSel == 'Paralegal' || mcSel == 'Legal Administrator' || mcSel == 'Legal Secretary' || mcSel =='Administrative Assistant'){
							#local.professionalInfoFieldSet.jsValidation#
							#local.recommendationFieldSet.jsValidation#
							#local.paralegalDemographicInfoFieldSet.jsValidation#
							
							if($.trim($(_CF_this['#local.letterOfRecommendationField.fieldCode#']).val()) == '' && $.trim($(_CF_this['#local.recommendationFormField.fieldCode#']).val()) == ''){
								arrReq[arrReq.length] = "Please upload #local.letterOfRecommendationField.fieldLabel# or choose #local.recommendationFormField.fieldLabel#.";
							} 
						}
						else {
							#local.demographicInfoFieldSet.jsValidation#
						}
					</cfif>
					
					#local.studentInfoFieldSet.jsValidation#
					

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				
				$(document).ready(function() {
					$('##paralegalDemographicSection').hide();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>
					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					</cfif>
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>
					
					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');
						},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');
							});
						}
					});

					function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
						$("##state_table").show();
						if(status == ''){
							status = 'Active';
						}
						strOption = '';
						<cfloop collection="#local.licenseStatus#" item="local.i" >
							strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
						</cfloop>
						if(isChecked){
							$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
									'<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
									'<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
									'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
									'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
									'<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
									'</div>');
							$('##mpl_'+val+'_status').val(status);
							mca_setupDatePickerField('mpl_'+val+'_activeDate');
							$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');
						}
						else{
							$("##tr_state_"+val).remove();
						}
						if($('##selectedLicense .row-fluid').length == 0){
							$("##state_table").hide();
						}
					}
					<cfif len(local.contacttype)>
						processContactTypeChange();
						$('###variables.formName# ###local.contacttype#').on('change',function(){
							processContactTypeChange();
						});
						$('###variables.formName# ###local.recFormSel#').on('change',function(){
							processrecFormSelChange();
						});
						function processrecFormSelChange(){
							var recFormSel = $('###variables.formName# ###local.recFormSel# option:selected').text();
							#toScript(local.letterOfRecommendationField.fieldCode,"fileFieldCode")#
							if( recFormSel.length && recFormSel == 'Yes'){
								if (typeof arrUploaders !== 'undefined') {
									$.each(arrUploaders, function() {
										if(fileFieldCode.split(this.columnID).length > 1) {
											if(this.uploader.files.length > 0){
												this.uploader.removeFile(this.uploader.files[0].id);
												$('span##'+fileFieldCode+'_newFileDetails').html('');
											}
										}								
									});
								}
								$('##recommendationSection > .tsAppSectionContentContainer > .recFormLink').show();
								$('tr:has(td:contains("#local.letterOfRecommendationField.fieldLabel#"))').hide();
							}
							else if (recFormSel.length && recFormSel == 'No') { 
								$('tr:has(td:contains("#local.letterOfRecommendationField.fieldLabel#"))').show();
								$('##recommendationSection > .tsAppSectionContentContainer > .recFormLink').hide();
								if (typeof arrUploaders !== 'undefined') {
									$.each(arrUploaders, function() {
										if(fileFieldCode.split(this.columnID).length > 1) {
											this.uploader._options.browse_button[0].disabled = false;
										}	
									});
								}
							}
							else {
								$('tr:has(td:contains("#local.letterOfRecommendationField.fieldLabel#"))').show();
								$('##recommendationSection > .tsAppSectionContentContainer > .recFormLink').hide();
								if (typeof arrUploaders !== 'undefined') {
									$.each(arrUploaders, function() {
										if(fileFieldCode.split(this.columnID).length > 1) {
											this.uploader._options.browse_button[0].disabled = true;
										}	
									});
								}
							}
						}
						function resetDemographic(isParalegal){
							if(isParalegal == 1) {
								$('##demographicHolder').html($('##paralegalDemographicSection').html());
								processrecFormSelChange();
							}
							else {
								$('##demographicHolder').html($('##DemographicInfoSection').html());
							}
							$("##demographicHolder .ui-multiselect").each(function(){
								$(this).remove();
							});							
							$("##demographicHolder [data-function]").each(function(){
								eval($(this).data("function")+"()");
							});
						}
						function processContactTypeChange(){
							var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
							if(mcSel == 'Attorney' || mcSel == 'Judge' || mcSel == 'Paralegal' || mcSel == 'Legal Administrator' || mcSel == 'Legal Secretary' || mcSel =='Administrative Assistant'){
								$('##StudentInfoSection').hide();
							} else {
								$('##StudentInfoSection').show();
								resetProfessionalLicenses();
							}
							if(mcSel == 'Law Student'){
								$('##orgAddressSection').hide();
								$('##addressPrefSection').hide();
								$('##professionalLicenseSection').hide();
								$('##professionalInfoSection').hide();
								$('##recommendationSection').hide();
								$('##paralegalDemographicSection').hide();
							} else {
								$('##orgAddressSection').show();
								$('##addressPrefSection').show();
								if(mcSel == 'Paralegal' || mcSel == 'Legal Administrator' || mcSel == 'Legal Secretary' || mcSel =='Administrative Assistant') {
									#toScript(local.letterOfRecommendationField.fieldCode,"fileFieldCode")#
									$('##professionalInfoSection').show();
									if (typeof arrUploaders !== 'undefined') {
										$.each(arrUploaders, function() {
											if(fileFieldCode.split(this.columnID).length > 1) {
												this.uploader._options.browse_button[0].defaultValue = "Upload File."
												this.uploader._options.browse_button[0].disabled = true;
											}	
										});
									}
									resetDemographic(1);
									$('##professionalLicenseSection').hide();
									$('##recommendationSection > .tsAppSectionContentContainer > .recFormLink').hide();
									$('##recommendationSection').show();
								} else {
									resetDemographic(0);
									$('##professionalLicenseSection').show();
									$('##professionalInfoSection').hide();
									$('##recommendationSection').hide();
								}
							}
						}
					</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<input type="hidden" name="#local.mcCreationDateFieldCode#" id="#local.mcCreationDateFieldCode#" value="#DateFormat(local.mcCreationDate,'m/d/yyyy')#">
					
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>
					<div id="content-wrapper" class="row-fluid">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>
						<div class="row-fluid">
							<div class="row-fluid tsAppSectionHeading">#local.categoryFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.categoryFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="row-fluid tsAppSectionHeading">#local.personalInfoFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.personalInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="row-fluid tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.homeAddressFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid" id="orgAddressSection">
							<div class="row-fluid tsAppSectionHeading">#local.orgAddressFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.orgAddressFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid" id="StudentInfoSection">
							<div class="row-fluid tsAppSectionHeading">#local.studentInfoFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.studentInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<span id="demographicHolder">

						</span>
						<div class="row-fluid" id="addressPrefSection">
							<div class="row-fluid tsAppSectionHeading">#local.addressPrefFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.addressPrefFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid" id="professionalLicenseSection">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">Professional License Information</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">
									<table cellpadding="3" border="0" cellspacing="0" >
										<tr align="top">
											<td class="tsAppBodyText" width="10">&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Professional License</td>
											<td class="tsAppBodyText">
												<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
													<cfloop query="local.qryOrgPlTypes">	
														<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="top">
											<td class="tsAppBodyText" width="10"></td>
											<td class="tsAppBodyText"></td>
											<td class="tsAppBodyText"></td>
										</tr>
									</table>
									<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
										<tr>
											<td>
												<div class="row-fluid hide" id="state_table">
													<div class="span3 proLicenseLabel">
														<b>State Name</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
													</div>
												</div>
												<span id="selectedLicense">
												</span>
											</td>
										</tr>
									</table>
								</div>
							</div>
						</div>

						<div class="row-fluid" id="professionalInfoSection">
							<div class="row-fluid tsAppSectionHeading">#local.professionalInfoFieldSet.fieldSetTitle#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.professionalInfoFieldSet.fieldSetContent#
							</div>
						</div>

						<div class="row-fluid" id="recommendationSection">
							<div class="row-fluid tsAppSectionHeading">#local.recommendationFieldSet.fieldSetTitle#</div>
							<div class="tsAppBodyText">#variables.strPageFields.ProfessionalRecContent#</div>
							<div class="row-fluid tsAppSectionContentContainer">
								#local.recommendationFieldSet.fieldSetContent#
								<div class="recFormLink">#variables.strPageFields.RecFormLink#</div>
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12">
								#variables.captchaDetails.htmlContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>

				<div class="row-fluid hide" id="DemographicInfoSection">
					<div class="row-fluid tsAppSectionHeading">#local.demographicInfoFieldSet.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.demographicInfoFieldSet.fieldSetContent#
					</div>
				</div>
				<div class="row-fluid hide" id="paralegalDemographicSection">
					<div class="row-fluid tsAppSectionHeading">#local.paralegalDemographicInfoFieldSet.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.paralegalDemographicInfoFieldSet.fieldSetContent#
					</div>
				</div>
				#application.objWebEditor.showEditorHeadScripts()#
				
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>

				<script language="javascript">
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							<cfif ListFindNoCase('Office,Home',local.qryOrgAddressTypes.addressType)>
								function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _address = _this.val();
									if(_address.length > 0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
									}
								}
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
							<cfelse>
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							</cfif>
						</cfloop>
					});
					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
				
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.response = "failure">
		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>
		<cfset local.response = "failure">
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>

		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>    
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>
		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>
		<cfset local.qryOrgSettings = CreateObject("component","model.admin.organization.organization").getSettings(orgID=variables.orgID)>
		<cfif variables.isLoggedIn OR session.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
			<cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
			
			<cfif ListContainsNoCase("Paralegal,Legal Administrator,Legal Secretary,Administrative Assistant",local.memberTypeSelected) AND NOT (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
				<cfset local.changedId = replaceNoCase(local.strResult.memberNumber,'G','' )>
				<cfset local.objSaveMember.setDemo(membernumber=local.changedId)>
				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			<cfelseif structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord>
				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
				<cfset local.changedId = replaceNoCase(local.strResult.memberNumber,'G','' )>
				<cfset local.objSaveMember.setDemo(membernumber=local.changedId)>
				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			</cfif>
		<cfelse>
			<cfset local.strData.one.memberID = 0>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
			
			<cfif ListContainsNoCase("Paralegal,Legal Administrator,Legal Secretary,Administrative Assistant",local.memberTypeSelected) >
				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
				<cfset local.changedId = replaceNoCase(local.strResult.memberNumber,'G','' )>
				<cfset local.objSaveMember.setDemo(membernumber=local.changedId)>
				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			<cfelseif ListContainsNoCase("Law Student",local.memberTypeSelected) >
				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
				<cfset local.objSaveMember.setAddressTag(type='Home', tag='Billing')>
				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			</cfif>
		</cfif>
		<cfset session.formFields.step0.origMemberID = variables.useMID/>
		<cfset session.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>
		<cfsavecontent variable="local.headCode">
			<cfoutput>					
				<script type="text/javascript">
					$(document).ready(function(){
						if (typeof arrUploaders !== 'undefined') {
							$.each(arrUploaders, function() {
								this.uploader.bind('BeforeUpload', function(uploader, file) {
									uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
								});
								this.uploader.start();								
							});
						}
					});
					</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

			<cfset session.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
												subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
												enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>    
			<cfset session.captchaEntered = 1>
			<cfset local.response = "success">
		</cfif>
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset variables.useMID = local.strData.zero.memberID/>
		<cfif StructIsEmpty(local.strData.one)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.thisAddonSubscriptionID = "">
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
		</cfloop>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>
		<cfset local.adminFeeSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = "2D7065CD-2B63-4063-B125-A2EBC0A86E94")>
		<cfsavecontent variable="local.headtext">
			<cfoutput>
				<style>
					.bottomMargin20{margin-bottom:20px;}
				</style>
				<script type="text/javascript">
					#local.result.jsAddonValidation#
					function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#
						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}else{
							checkAdminFee();
						}
						
						var rate = getJoinRate();
						
						if(rate>0){
							$('div [data-setname="Admin Fee"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
								var minlp = $(this).data("minallowed");
								if($('input:checkbox:checked',this).length == 0){
									arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $('legend',this).eq(0).text()+".";
								}
							});

							$('div [data-setname="Admin Fee"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
								var maxlp = $(this).data("maxallowed");
								if($('input:checkbox:checked',this).length > maxlp){
									arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $('legend',this).eq(0).text()+".";
								}
							});
						}
						
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}else{
							$("input[name='sub#local.adminFeeSubscriptionID#']").removeAttr("disabled");
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					
					function getJoinRate(){
						if($("[name=sub#local.subscriptionID#_rate]").length > 0){
							if($("[name=sub#local.subscriptionID#_rate]:checked").length){
								var rate = parseInt($("##"+$("[name=sub#local.subscriptionID#_rate]:checked").attr("name").split("_")[0]+"_selectedRate_rate").data("rate").replace("$", ""));
							}else{
								var rate = parseInt($("##"+$("[name=sub#local.subscriptionID#_rate]").attr("name").split("_")[0]+"_selectedRate_rate").data("rate").replace("$", ""));    
							}
						}else{
							var rate = 0;
						}
						return rate;
					}
					
					function checkAdminFee(){
						$("input[name='sub#local.adminFeeSubscriptionID#']").attr("disabled", true);
						var rate = getJoinRate();
						if(rate>0){
							$("input[name='sub#local.adminFeeSubscriptionID#'][type='checkbox']").prop("checked", true);
						}else{
							$("input[name='sub#local.adminFeeSubscriptionID#'][type='checkbox']").prop("checked", false);
						}
					}
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					$(document).ready(function(){
						if($('div [data-setname="Lawyer Referral & Information Service Option 1"] input:checkbox').not('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
							$('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper').show();
						}else{
							$('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper').hide();
							$('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
						}
						if($('div [data-setname="Lawyer Referral & Information Service Option 2"] input:checkbox').not('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
							$('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper').show();
						}else{
							$('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper').hide();
							$('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
						}
						$("input[name='sub#local.subscriptionID#_rate']").on("change", function() {
							checkAdminFee();
						});
						checkAdminFee();
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headtext#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">            
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>
					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
					<script>
						$(document).ready(function(){
							$(document).on('change',$('div [data-setname="Lawyer Referral & Information Service Option 1"] input:checkbox').not('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper input:checkbox'),function(){
								if($('div [data-setname="Lawyer Referral & Information Service Option 1"] input:checkbox').not('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
									$('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper').show();
								}else{
									$('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper').hide();
									$('div [data-setname="Lawyer Referral & Information Service Option 1"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
								}
							});
							$(document).on('change',$('div [data-setname="Lawyer Referral & Information Service Option 2"] input:checkbox').not('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper input:checkbox'),function(){
								if($('div [data-setname="Lawyer Referral & Information Service Option 2"] input:checkbox').not('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
									$('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper').show();
								}else{
									$('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper').hide();
									$('div [data-setname="Lawyer Referral & Information Service Option 2"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
								}
							});
						});
					</script>
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>
		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfif StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>        
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>        
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>        
			</cfif>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>
		</cfif>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>

				<script type="text/javascript">
					function validatePaymentForm(isPaymentRequired) {
						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					$('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;
						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}    
							}, 100);

						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
					$(document).ready(function(){
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
					<cfinput type="hidden" name="fa" id="fa" value="processPayment">
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>
					<cfif len(variables.strPageFields.Step3TopContent)>
						<div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
					</cfif>
					<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
					<div class="tsAppSectionContentContainer">
						#local.strResult.formContent#
					</div>
					<br/>
					<div class="tsAppSectionHeading">Total Price</div>
					<div class="tsAppSectionContentContainer">
						Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
					</div>
					<br/><br/>
					<cfif local.paymentRequired>
						#local.strReturn.paymentHTML#
					<cfelse>
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
						<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
					</cfif>
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.response = "failure">
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step3")>
			<cfset structDelete(session.formFields, "step3")>
		</cfif>
		<cfset session.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>
		<cfset local.strData.three = checkSessionExist("step3")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>
		<cfset local.strData.two.memberID = variables.useMID>

		<cfset local.strMemSaveResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>

		<cfif ListFindNoCase("#variables.strPageFields.RatesForBilledStatus#",local.subStructResults.subscription.rateUID)>
			<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)> 
		<cfelse>
			<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 
		</cfif>
		
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC                  --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice">
			set nocount on;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

			select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, dueNow= case when nv.dateDue < getdate() then 1 else 0 end
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs
				on subs.subscriptionID = s.subscriptionID
				and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
			inner join dbo.sub_types t
				on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
				and ta.applicationTypeID = 17
				and ta.itemType = 'Dues'
			inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
			inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
			group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
			order by nv.dateDue
		</cfquery>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(local.strData.three, 'mccf_payMethID') and structKeyExists(local.strData.three, 'p_#local.strData.three.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.three['p_#local.strData.three.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfset local.processPaymentResponse = structnew()>
		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#,#variables.strPageFields.ProfileCodeACH#",local.strData.three.mccf_payMeth)>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=local.strData.three } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.strData.three.mccf_payMethID, profileCode=local.strData.three.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.processPaymentResponse = local.strACCResponse>
		</cfif> 
		<cfset local.response = "success">

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

		<cfset local.categoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7D712CB0-CDF3-4A0F-9E43-B993F6F501F1', mode="confirmation", strData=local.strData.one)>
		<cfset local.personalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='B84997F3-B4A8-485B-ACD1-B17002C6C0EE', mode="confirmation", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='F4CAC094-2F06-4980-9F83-61D897AC9DE7', mode="confirmation", strData=local.strData.one)>
		<cfset local.orgAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7FF7DA7D-7904-4FB6-BA0F-CC763351649F', mode="confirmation", strData=local.strData.one)>
		<cfset local.studentInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='6BD709E1-94F9-41C3-83B0-436E2475A97B', mode="confirmation", strData=local.strData.one)>
		<cfset local.demographicInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7886DF08-D2D9-401F-8902-6BB2B5850924', mode="confirmation", strData=local.strData.one)>
		<cfset local.paralegalDemographicInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7B4FFA44-0831-4635-817F-915648D98B89', mode="confirmation", strData=local.strData.one)>
		<cfset local.addressPrefFieldSet = application.objCustomPageUtils.renderFieldSet(uid='C51B64E5-BBCD-4F1B-A8C3-2DA9A135FB45', mode="confirmation", strData=local.strData.one)>
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		<cfset local.professionalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid='F449FE76-4076-4C72-A14A-48A8CA25AD68', mode="confirmation", strData=local.strData.one)>
		<cfset local.recommendationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='5A6DABA8-37B2-453F-BDD7-CF43D194614E', mode="confirmation", strData=local.strData.one)>

		<cfsavecontent variable="local.invoice">
			<cfoutput>
				#local.categoryFieldSet.fieldSetContent#
				#local.personalInfoFieldSet.fieldSetContent#
				#local.homeAddressFieldSet.fieldSetContent#
				<cfif local.memberTypeSelected NEQ 'Law Student'> 
					#local.orgAddressFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected NEQ 'Attorney' AND local.memberTypeSelected NEQ 'Judge'> 
					#local.studentInfoFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Paralegal' OR local.memberTypeSelected EQ 'Legal Administrator' OR local.memberTypeSelected EQ 'Legal Secretary' OR local.memberTypeSelected EQ 'Administrative Assistant'>
					#local.paralegalDemographicInfoFieldSet.fieldSetContent#
				<cfelse>
					#local.demographicInfoFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected NEQ 'Law Student'>
					#local.addressPrefFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected NEQ 'Paralegal' AND local.memberTypeSelected NEQ 'Legal Administrator' AND local.memberTypeSelected NEQ 'Legal Secretary' AND local.memberTypeSelected NEQ 'Administrative Assistant' AND local.memberTypeSelected NEQ 'Law Student'>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Status</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_status']#</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				</cfif>

				<cfif local.memberTypeSelected EQ 'Paralegal' OR local.memberTypeSelected EQ 'Legal Administrator' OR local.memberTypeSelected EQ 'Legal Secretary' OR local.memberTypeSelected EQ 'Administrative Assistant'>
					#local.professionalInfoFieldSet.fieldSetContent#
					#local.recommendationFieldSet.fieldSetContent#
				</cfif>
				<br>

				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							#local.strResult.formContent#
							<br/>
						</div>
						<br/>
						<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
						<br/>
						</td>
					</tr>
				</table>

				<cfif local.paymentRequired>
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(local.strData.three,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>

			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<p>#variables.strPageFields.ConfirmationContent#</p>
				</cfif>

				<p>Here are the details of your application:</p>

				#local.invoice#
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfsavecontent variable="local.specialText">
			<cfoutput>

			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(local.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#local.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<p>#variables.strPageFields.ConfirmationContent#</p>
				</cfif>
				 #local.specialText#
				<p>Here are the details of your application:</p>

				#local.invoice#
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>

		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<p>#variables.strPageFields.ConfirmationContent#</p>
					</cfif>
					<p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF)>

		<!--- relocate to message page --->
		<cfset session.invoice = local.invoice />

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
				</cfif>
				<div class="tsAppSectionContentContainer">
					<p>Here are the details of your application:</p>
					#session.invoice#
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="storeMembershipApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
										parentSiteResourceID=local.parentSiteResourceID,  sectionID=local.docSectionID, docTitle='Membership Application - #DateFormat(variables.currentDate,'m/d/yyyy')#', 
										docDesc='Membership Application Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
										contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

	<cffunction name="hasSub" access="private" output="false" returntype="string">
		<cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

		<cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.isExist = false/>
		<cfset local.strData = {}>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, arguments.step)>
			<cfset local.strData = session.formFields[arguments.step]/>
		</cfif>

		<cfreturn local.strData>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">
					<cfif arguments.errorCode eq "activefound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						You need to renew your SCBA membership. You will be re-directed to your renewal shortly.
					   <script language="javascript">
							window.location = "#local.redirectLink#";
						</script>
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					 <cfelseif arguments.errorCode eq "admin">
						<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>