<cfsavecontent variable="local.pageHead">
	<cfoutput>
	<script type="text/javascript">
		function hideAlert() { $('##err_billInfo').html('').hide(); };
		function showAlert(msg) { $('##err_billInfo').html(msg).show(); };

		$(function() {
			<cfif local.showCCScreen>
				try {
					window.removeEventListener("message", MCSearchBillingfInfoMessageHandler);
				} catch(e) {}
				window.addEventListener("message", MCSearchBillingfInfoMessageHandler);
			</cfif>
			$('##oneClickPurchaseModal .modal-body').css('max-height',400);
		})
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageHead)#">

<cfoutput>
<div id="divMCSearchBillingInfoContainer">
	<div id="err_billInfo" class="alert alert-danger" style="display:none;margin:6px 0;"></div>
	<form id="billingForm" class="form-horizontal" onsubmit="return false;">
		<cfif local.showIntroScreen>
			<div id="initial_billInfo" class="mc_billinginfo_section" style="margin:6px 0;">
				<div class="control-group">
					<p><b>Setup Quick Download</b><br/>Instantly download and purchase transcripts while doing your research. We'll bill you once a day for all purchases.</p>
				</div>
			</div>
		</cfif>
		<cfif local.showBillingContactScreen>
			<div id="contact_billInfo" class="mc_billinginfo_section" style="display:none;margin:6px 0;">
				<div class="control-group">
					<div>Please verify and update your billing information.</div>
				</div>	
				<div class="control-group">
					<label class="control-label" for="">Name</label>
					<div class="controls">
						#local.qryTSMemberData.FirstName# #local.qryTSMemberData.LastName#
					</div>
				</div>			
				<div class="control-group">
					<label class="control-label" for="BillingAddress">Address</label>
					<div class="controls">
						<input type="text" id="BillingAddress" name="BillingAddress" value="#local.qryTSMemberData.BillingAddress#" maxlength="100">
					</div>
				</div>
				<div class="control-group">
					<label class="control-label" for="BillingCity">City</label>
					<div class="controls">
						<input type="text" id="BillingCity" name="BillingCity" value="#local.qryTSMemberData.BillingCity#" maxlength="50">
					</div>
				</div>
				<div class="control-group">
					<label class="control-label" for="billingstate">State/Province *</label>
					<div class="controls">
						<select id="billingstate" name="billingstate">
							<option value=""></option>
							<cfloop query="local.qryStates">
								<option value="#local.qryStates.code#" #local.user_billingstate eq local.qryStates.code ?"selected='selected'":""#>#local.qryStates.name#</option>
							</cfloop>
						</select>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label" for="billingzip">Postal Code *</label>
					<div class="controls">
						<input type="text" id="billingzip" name="billingzip" value="#local.user_billingzip#" maxlength="25">
					</div>
				</div>
				<div class="control-group">
					<label class="control-label" for="userMainEmail">E-mail *</label>
					<div class="controls">
						<input type="text" id="userMainEmail" name="userMainEmail" value="#local.qryTSMemberData.Email#" maxlength="100">
					</div>
				</div>
			</div>
		</cfif>
		<cfif local.showCCScreen>
			<div id="cc_billInfo" class="mc_billinginfo_section" style="display:none;margin:6px 0;">
				<div class="alert alert-info bk-mb-0">A Credit Card On File is Required for all Downloads.</div>	
			
				<cfset local.strPaymentForm = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").gather(merchantOrgcode='TS', customerid='olddid_#session.cfcuser.memberdata.depomemberdataid#', autoShowForm=1, editMode='frontEndPayment')>
				<cfif len(local.strPaymentForm.headcode)>
					#local.strPaymentForm.headcode#
				</cfif>
				<cfif len(local.strPaymentForm.inputForm)>
					<div id="BillingCardFormLoading" class="text-center"><i class="icon-spin icon-spinner"></i> Please wait while we load the credit card form...</div>
					<div id="CIMTable" class="p-2">
						<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_SW_fld_','ALL')#</div>
					</div>
				</cfif>

				<cfif len(local.strPaymentForm.jsvalidation)>
					<cfsavecontent variable="local.extrapayJS">
						<cfoutput>
						#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_SW_fld_','ALL')#
						</cfoutput>
					</cfsavecontent>
				</cfif>

				<cfset local.strSiteInfo = application.objSiteInfo.getSiteInfo("TS")>
				<cfset local.strOrgIdentityInfo = CreateObject("component","model.admin.organization.organization").getOrgIdentityDetailsStruct(orgID=local.strSiteInfo.orgID, orgIdentityID=local.strSiteInfo.defaultOrgIdentityID)>
				<cfset local.qrySecureData = createObject("component","model.admin.custom.mc.mc.PlatformSettings").getPlatformSettingsContent(contentTitle='Platformwide_Secure_Checkout_Policy')>
				
				<cfset local.arrEcomLinks = []>
				<cfif local.qrySecureData.rawContent neq "">
					<cfset local.arrEcomLinks.append('<a href="/?pg=buyNow&viewPolicy=secureCheckout" class="buynow-mt-sm-2" target="_blank">Secure Checkout</a>')>
				</cfif>
				<cfif StructKeyExists(local.strSiteInfo,"deliveryPolicyURL") AND len(local.strSiteInfo.deliveryPolicyURL)>
					<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=delivery" class="buynow-mt-sm-2" target="_blank">Delivery Policy</a>')>
				</cfif>
				<cfif StructKeyExists(local.strSiteInfo,"privacyPolicyURL") AND len(local.strSiteInfo.privacyPolicyURL)>
					<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=privacy" class="buynow-mt-sm-2" target="_blank">Privacy Policy</a>')>
				</cfif>
				<cfif StructKeyExists(local.strSiteInfo,"rrPolicyURL") AND len(local.strSiteInfo.rrPolicyURL)>
					<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=refund" class="buynow-mt-sm-2" target="_blank">Refunds and Returns</a>')>
				</cfif>
				<cfif StructKeyExists(local.strSiteInfo,"tcURL") AND len(local.strSiteInfo.tcURL)>
					<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=terms" class="buynow-mt-sm-2" target="_blank">Terms and Conditions</a>')>
				</cfif>

				<div class="text-center" style="padding:5px;font-size:11px;">
					<cfif arrayLen(local.arrEcomLinks)>
						<div>#arrayToList(local.arrEcomLinks,' | ')#</div>
					</cfif>
					<address style="margin-bottom:0;">
						#local.strOrgIdentityInfo.orgname# <span>#local.strOrgIdentityInfo.address1#<cfif len(local.strOrgIdentityInfo.address2)> #local.strOrgIdentityInfo.address2#</cfif><cfif len(local.strOrgIdentityInfo.city)> #local.strOrgIdentityInfo.city#</cfif><cfif len(local.strOrgIdentityInfo.state)>, #local.strOrgIdentityInfo.state#</cfif><cfif len(local.strOrgIdentityInfo.postalcode)> #local.strOrgIdentityInfo.postalcode#</cfif></span><br/>
						#local.strOrgIdentityInfo.phone# <cfif len(local.strOrgIdentityInfo.email)><a href="mailto:#local.strOrgIdentityInfo.email#">#local.strOrgIdentityInfo.email#</a></cfif>
					</address>
				</div>
			</div>
		</cfif>
		<div id="caseref_billInfo" class="mc_billinginfo_section" style="display:none;margin:6px 0;">
			<p><b>Document:</b><br/><span id="docCaseRefDesc"></span></p>
			<p><b>Case Reference</b><br/>Add reference which will appear on your monthly statement.</p>
			<div>
				<input type="text" id="caseref" name="caseref" value="" maxlength="50" style="width:98%;">
			</div>
			<div id="selCaseRefBadges" class="well bk-p-2 bk-mt-2" style="display:none;"></div>
			<cfif NOT local.showIntroScreen AND local.qryTSMemberData.paymenttype EQ "C">
				<div class="help-block bk-p-2 bk-mt-0 bk-font-size-xs">We'll bill you once a day for all purchases.</div>
			</cfif>
		</div>
	</form>
</div>
</cfoutput>