<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Add step 1 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step2TopContent", type="CONTENTOBJ", desc="Content at top of page 2", value="Add step 2 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="TulsaLawyerPrintSubscriptionContent", type="CONTENTOBJ", desc="Content at top of Tulsa Lawyer Print Subscription", value="If you would like to receive the Tulsa Lawyer Magazine, select the checkbox below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="TCBASectionsContent", type="CONTENTOBJ", desc="Content at top of TCBA Sections", value="Add TCBA Content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		local.tmpField = { name="TCBACommitteesContent", type="CONTENTOBJ", desc="Content at top of TCBA Committees", value="The excellence of the Tulsa County Bar Association and the Tulsa County Bar Foundation is directly due to the involvement and dedication of its members. We encourage you to serve on at least one of the following committees and help chart the course of the TCBA & TCBF. Please select up to three between TCBA Committees and TCBF Committees." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="TCBFCommitteesContent", type="CONTENTOBJ", desc="Content at top of TCBF Committees", value="Reminder - Please limit your selection of TCBA & TCBF Committees to a total of three." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CommitteeViceChairInterestContent", type="CONTENTOBJ", desc="Content at top of Committee Vice-Chair", value="Add Committee Vice-Chair Content." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ORCardContent", type="CONTENTOBJ", desc="Content at top of O.R. Card", value="	Annual Renewal. Must include Signed & Notarized Oath." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="CourthouseSecurityBadgeContent", type="CONTENTOBJ", desc="Content at top of Courthouse Security Badge", value="Annual Renewal. Must include Signed & Notarized Oath. Your Security Badge will not be valid without the new color coded security sticker after 10/01 of the current year." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for CC", value="TCBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayLater", type="STRING", desc="pay profile code for Cash/Check", value="TCBACashChecks" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="subscriptionUID",type="STRING", desc="UID of the Root Subscription that this form offers", value="e8adc386-b323-4d3e-9cfd-69f148912d3b" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you. You have successfully submitted your application for membership with Tulsa County Bar Association. You will be contacted by TCBA after your application has been reviewed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="TCBA Membership Submission Confirmation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application Submitted" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="TBD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="TBD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="56C0DB11-4959-4DAA-BF33-C69545EA4735" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
							
			
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Membership Application',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;	
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='contact type')>
	
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				div.tsAppSectionHeading{
					font-family: Verdana, Arial, Helvetica, sans-serif;
					color: ##222;
					background-color: ##F5F5F5;
					padding: 10px;
					font-size: 15px;
					font-weight: bold;
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;			

				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}	
				
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function toggleFTM() {
				}

				

				function validatePaymentForm(isPaymentRequired) {						
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if(typeof mccf_validatePPForm == 'function') {
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								return false;
							}
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				}); 
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div class="row-fluid tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="row-fluid tsAppSectionContentContainer">
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.membershipDuesUID = "5A8B0D95-F0F7-4CBC-8A86-697B1802BB1A">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.membershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.membershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.membershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>	
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='contact type')>
		
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Join TBA - Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='fc3a6caa-6f61-4efa-9dc8-ff5fa9062e76', mode="collection", strData=local.strData)>
		<!--- Join TBA -Professional Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='c9a69524-0b85-4c4f-9085-167a0ea9e554', mode="collection", strData=local.strData)>
		<!--- Join TBA - Professional Information-Paralegal & Legal Assistant --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='53246cff-c058-4a73-b335-b0d9586e008d', mode="collection", strData=local.strData)>
		<!--- Join TBA - Law Student Information --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='98e1d2b1-8b7c-4854-bb67-24d00fea9a4d', mode="collection", strData=local.strData)>
		<!--- Join TBA - Business Address Information --->
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='c8814175-5e6a-446b-ad7a-a5ef7a7ab07a', mode="collection", strData=local.strData)>
		<!--- Join TBA - Home Address Information --->
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='015bb1e9-fa8a-4c54-8ee1-01358f69a557', mode="collection", strData=local.strData)>
		<!--- Join TBA - Address Preferences --->
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='313dab26-167a-401c-8357-dd5594202688', mode="collection", strData=local.strData)>
		<!--- Join TCBA - Professional Information-Paralegal/Legal Assistant Student --->
		<cfset local.strFieldSetContent8 = application.objCustomPageUtils.renderFieldSet(uid='3F44DDAF-1672-43D3-A99C-58FA53AAEC11', mode="collection", strData=local.strData)>
		<!--- Join TCBA - Media Release --->
		<cfset local.strFieldSetContent9 = application.objCustomPageUtils.renderFieldSet(uid='708C56AB-3052-4D90-BFB9-D579A8696936', mode="collection", strData=local.strData)>

		<!--- get  Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.strFieldSetContent7.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent7.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.strFieldSetContent7.strFields[local.thisField] eq "Use this address for Mailing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>	
		<cfset local.contacttype = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Contact Type">
				<cfset local.contacttype = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>		
		<!--- get Home Address --->
		<cfloop collection="#local.strFieldSetContent6.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent6.strFields[local.thisField] eq "Address">
				<cfset local.memberAltAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<!--- get Business Address --->
		<cfloop collection="#local.strFieldSetContent5.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent5.strFields[local.thisField] eq "Address 1">
				<cfset local.memberAltAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.oklahomaCertificationDate = "">
		<cfloop collection="#local.strFieldSetContent3.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent3.strFields[local.thisField] eq "Oklahoma Certification Date">
				<cfset local.oklahomaCertificationDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.paralegalCertificationDate = "">
		<cfloop collection="#local.strFieldSetContent3.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent3.strFields[local.thisField] eq "Paralegal Certification Date">
				<cfset local.paralegalCertificationDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.expectedParalegalCertificationDate = "">
		<cfloop collection="#local.strFieldSetContent8.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent8.strFields[local.thisField] eq "Expected Paralegal Certification Date">
				<cfset local.expectedParalegalCertificationDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>		
		<cfset local.oklahomaLicenseNumber = "">
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent2.strFields[local.thisField] eq "OBA ##">
				<cfset local.oklahomaLicenseNumber = local.thisField>		
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.oklahomaAdmissionDate = "">
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent2.strFields[local.thisField] eq "Date Admitted to Oklahoma Bar">
				<cfset local.oklahomaAdmissionDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		 <cfset local.oklahomaStatus = "">
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent2.strFields[local.thisField] eq "Oklahoma Bar Status">
				<cfset local.oklahomaStatus = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.otherStateLicenseNumber = "">
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent2.strFields[local.thisField] eq "Other State Bar ID ##">
				<cfset local.otherStateLicenseNumber = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.otherStateAdmissionDate = "">
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent2.strFields[local.thisField] eq "Earliest Date Admitted to Another State Bar">
				<cfset local.otherStateAdmissionDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfset local.otherStateStatus = "">
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent2.strFields[local.thisField] eq "Other State Bar Status">
				<cfset local.otherStateStatus = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>		
		<cfset local.email = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Email">
				<cfset local.email = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				
				input[type="text"] {
					line-height: 30px;
				}
				select, input[type="file"]{
					height:30px;
				}
				##Step1TopContent{margin-left:14px;}				
				
				@media screen and (max-width: 767px){					
					##content-wrapper table td {
						display: block;
						margin-bottom:-10px;
					}
					##content-wrapper table tr td:nth-child(1){float:left; padding-top: 10px;}
					##content-wrapper table tr td:nth-child(2){min-width:50px;padding-top: 10px;}
					##content-wrapper table tr td:nth-child(3){margin-top:-20px;}
				}	
				##content-wrapper table{ width: 100%;margin-bottom: 20px; }
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}
				##addresspreference table td{white-space:normal;}
				##ProfessionalInformation-Paralegal table td{white-space:normal;}				
				##ProfessionalInformation table td{white-space:normal;}			
				@media screen and (min-width: 767px){	
					##ProfessionalInformation table td:nth-child(2){width:30%;}
					##ProfessionalInformation-Paralegal table td:nth-child(2){width:30%;}
					##addresspreference table td:nth-child(2){width:30%;}
				}
			</style>
			<script language="javascript">				
				var memberTypeField;				
				function adjustFieldsetDisplay() {
					var memType = $("option:selected",memberTypeField).text();
					switch(memType) {
						case 'Paralegal Student': 
							hideDefault();
							$('div##Mentor').hide();			
							$('div##ProfessionalInformation-Paralegal-Student-content').html('#JSStringFormat(local.strFieldSetContent8.fieldSetContent)#');								
							$('div##ProfessionalInformation-Paralegal-Student').show();
							mca_setupDatePickerField('#local.expectedParalegalCertificationDate#');							
							break;
						case 'Paralegal/Legal Assistant': 
							hideDefault();
							$('div##Mentor').hide();			
							$('div##ProfessionalInformation-Paralegal-content').html('#JSStringFormat(local.strFieldSetContent3.fieldSetContent)#');								
							$('div##ProfessionalInformation-Paralegal').show();
							mca_setupDatePickerField('#local.paralegalCertificationDate#');
							mca_setupDatePickerField('#local.oklahomaCertificationDate#');
							break;
						case 'Law Student':		
							hideDefault();						
							$('div##lawStudent-content').html('#JSStringFormat(local.strFieldSetContent4.fieldSetContent)#');
							$('div##lawStudent').show();
							$('div##Mentor').show();							
							break;							
						case 'Attorney':
							hideDefault();	
							nonParelegalAndStudent();					
							break;
							case 'Judge':
							hideDefault();	
							nonParelegalAndStudent();					
							break;
							case 'Law Professor':
							hideDefault();	
							nonParelegalAndStudent();					
							break;
							case 'Public Sector Employee':
							hideDefault();	
							nonParelegalAndStudent();					
							break;
							case 'Judge for Tulsa County':
							hideDefault();	
							nonParelegalAndStudent();					
							break;
						default:
							hideDefault();
							$('div##Mentor').show();
							break;
					}
				}
				function nonParelegalAndStudent(){
					$('div##ProfessionalInformation-content').html('#JSStringFormat(local.strFieldSetContent2.fieldSetContent)#');	
					$('div##ProfessionalInformation').show();
					mca_setupDatePickerField('#local.oklahomaAdmissionDate#');
					mca_setupDatePickerField('#local.otherStateAdmissionDate#');
					$('div##Mentor').show();
				}
				function hideDefault(){
					$('div##ProfessionalInformation').hide(); 
					$('div##ProfessionalInformation-content').html(''); 
					$('div##ProfessionalInformation-Paralegal').hide(); 
					$('div##ProfessionalInformation-Paralegal-content').html(''); 
					$('div##ProfessionalInformation-Paralegal-Student').hide(); 
					$('div##ProfessionalInformation-Paralegal-Student-content').html(''); 
					$('div##lawStudent').hide();
					$('div##lawStudent-content').html('');
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					#local.strFieldSetContent1.jsValidation#
					<cfif len(trim(local.contacttype))>		
					
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						if (mcSel == 'Attorney') {	
							#local.strFieldSetContent2.jsValidation#					
							<cfif len(trim(local.oklahomaLicenseNumber)) and len(trim(local.oklahomaAdmissionDate)) and len(trim(local.oklahomaStatus)) and  len(trim(local.otherStateLicenseNumber)) and len(trim(local.otherStateAdmissionDate)) and len(trim(local.otherStateStatus))>
								
								var LicenseNumberValue = $.trim($('###variables.formName# ###local.oklahomaLicenseNumber#').val());
								var AdmissionDateValue = $.trim($('###variables.formName# ###local.oklahomaAdmissionDate#').val());
								var StatusValue = $.trim($('###variables.formName# ###local.oklahomaStatus#').val());
								var otherStateLicenseNumberValue = $.trim($('###variables.formName# ###local.otherStateLicenseNumber#').val());
								var otherStateAdmissionDateValue = $.trim($('###variables.formName# ###local.otherStateAdmissionDate#').val());
								var otherStateStatusValue = $.trim($('###variables.formName# ###local.otherStateStatus#').val());
								
								if (LicenseNumberValue == '' && AdmissionDateValue == '' && StatusValue == '' && otherStateLicenseNumberValue == '' && otherStateAdmissionDateValue == '' && otherStateStatusValue == '' ) {
									arrReq[arrReq.length] = " Oklahoma License Number, Admission Date to Oklahoma Bar, and Oklahoma Status OR Other State License Number, Admission Date to Other State, and Other State Status are required.";
								} else if (LicenseNumberValue != '' || AdmissionDateValue != '' || StatusValue != '') {
									if(LicenseNumberValue == '' || AdmissionDateValue == '' || StatusValue == '') {
										if(LicenseNumberValue == '' && AdmissionDateValue == '' && StatusValue == '') {
											arrReq[arrReq.length] = "Oklahoma License Number, Admission Date, and Status to Oklahoma Bar is required for Attorney membership.";
										} else {
											if(LicenseNumberValue == '') {
												arrReq[arrReq.length] = "Oklahoma License Number is required for Attorney membership.";
											}
											if(AdmissionDateValue == '') {
												arrReq[arrReq.length] = "Admission Date to Oklahoma Bar is required for Attorney membership.";
											}
											if(StatusValue == '') {
												arrReq[arrReq.length] = "Oklahoma Status is required for Attorney membership.";
											}
										}
									}
								} else if (otherStateLicenseNumberValue != '' || otherStateAdmissionDateValue != '' || otherStateStatusValue != '') {
									if(otherStateLicenseNumberValue == '' || otherStateAdmissionDateValue == '' || otherStateStatusValue == '') {
										if(otherStateLicenseNumberValue == '' && otherStateAdmissionDateValue == '' && otherStateStatusValue == '') {
											arrReq[arrReq.length] = "Other State License Number, Admission Date, and Status to Oklahoma Bar is required for Attorney membership.";
										} else {
											if(otherStateLicenseNumberValue == '') {
												arrReq[arrReq.length] = "Other State License Number is required for Attorney membership.";
											}
											if(otherStateAdmissionDateValue == '') {
												arrReq[arrReq.length] = "Admission Date to Other State Bar is required for Attorney membership.";
											}
											if(otherStateStatusValue == '') {
												arrReq[arrReq.length] = "Other State Status is required for Attorney membership.";
											}
										}
									}
								}
							</cfif>
						}else if(mcSel == 'Paralegal Student'){
							#local.strFieldSetContent8.jsValidation#
						}
						else if(mcSel == 'Paralegal/Legal Assistant'){							
							#local.strFieldSetContent3.jsValidation#
						}
						else if(mcSel == 'Law Student'){											
							#local.strFieldSetContent4.jsValidation#
						}
						else{
							#local.strFieldSetContent2.jsValidation#
						}										
					</cfif>

					<cfif len(trim(local.email))>
						var email = $('###variables.formName# ###local.email#').val();
						$('##email').val(email);
					</cfif>						
					
					#local.strFieldSetContent5.jsValidation#
					#local.strFieldSetContent6.jsValidation#
					#local.strFieldSetContent7.jsValidation#
					#local.strFieldSetContent9.jsValidation#						
						
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) {
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					$('##mediaRelease select option:contains("Yes")').attr('selected', true);
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					memberTypeField = $('##md_#local.memberTypeFieldInfo.columnID#');
					$(memberTypeField).change(adjustFieldsetDisplay);
					adjustFieldsetDisplay();
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.Step1TopContent)>
					<div class="row-fluid" id="Step1TopContent">#variables.strPageFields.Step1TopContent#</div>
				</cfif>
			<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="hidden" id='email' name="email" value=''>
			<cfinclude template="/model/cfformprotect/cffp.cfm">			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			<div id="content-wrapper" class="row-fluid">
				<div class="row-fluid">
					<div class=" span12 tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
					<div class="span6 tsAppSectionContentContainer">
						#local.strFieldSetContent1.fieldSetContent#
					</div>
				</div></br>	
				
				<cfset local.mccf_tcbaMentoring=0>
				<cfif structKeyExists(arguments.rc, "mccf_tcbaMentoringCheckbox")>
					<cfset local.mccf_tcbaMentoring = 1>
				</cfif>
				<div class="row-fluid" id="Mentor">
					<div class="row-fluid tsAppSectionHeading" id="MentoringTitle">TCBA Mentoring Program</div></br>
					<div class="row-fluid tsAppSectionContentContainer">					
						<label class="checkbox subLabel" for="mccf_tcbaMentoringCheckbox">
						<input class="subCheckbox" type="checkbox" <cfif local.mccf_tcbaMentoring>checked="checked"</cfif> name="mccf_tcbaMentoringCheckbox" id="mccf_tcbaMentoringCheckbox" value="1">
							Please check if you are interested in learning more about TCBA Mentoring Programs.
						</label>
					</div></br>
				</div>
				
				<div class="row-fluid" id="ProfessionalInformation">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer" id="ProfessionalInformation-content">
					#local.strFieldSetContent2.fieldSetContent#
					</div>
				</div>
				
				<div class="row-fluid" id="ProfessionalInformation-Paralegal">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer" id="ProfessionalInformation-Paralegal-content">					
					</div>
				</div>

				<div class="row-fluid" id="ProfessionalInformation-Paralegal-Student">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent8.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer" id="ProfessionalInformation-Paralegal-Student-content">					
					</div>
				</div>
				
				<div class="row-fluid" id="lawStudent">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent4.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer" id="lawStudent-content">
					</div>
				</div>
				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent5.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetContent5.fieldSetContent#
					</div>
				</div>	
				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent6.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetContent6.fieldSetContent#
					</div>
				</div>
				<div class="row-fluid" id="addresspreference">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent7.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetContent7.fieldSetContent#
					</div>
				</div>
				<div class="row-fluid" id="mediaRelease">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent9.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetContent9.fieldSetContent#
					</div>
				</div>
				
				<button name="btnContinue" type="submit" class="tsAppBodyButton  btn btn-default" onClick="hideAlert();">Continue</button>
			</div>
			#application.objWebEditor.showEditorHeadScripts()#
			
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

			 <script language="javascript">	
				
				$(document).ready(function(){
					<cfloop query="local.qryOrgAddressTypes">
						addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
						function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
							var _address = _this.val();
							
							if(_address.length >0){
								if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
									$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
								}
							} else {
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							}
						}
						
						$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
						});
					</cfloop>
				});
					
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
	
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>	
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfset local.thisAddonSubscriptionID = "">
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
		</cfloop>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
		
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	
				
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript">
			function validateMembershipInfoForm(){
				var arrReq = new Array();
				#local.result.JSVALIDATION#
					if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}	

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				} 
			</script>
			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>
			
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfif StructKeyExists(arguments.rc,'email')>
				<cfinput type="hidden" name="email" value="#arguments.rc.email#">
			</cfif>
		
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#						
						<cfset local.mccf_CommitteViceChairSelected = 0>
						<cfif structKeyExists(arguments.rc, "mccf_CommitteViceChairCheckbox")>
							<cfset local.mccf_CommitteViceChairSelected = 1>
						</cfif>
						<div class="well subAddonWrapper" id="CommitteeViceChair">
							<legend>Committee Vice-Chair</legend>
							<cfif len(variables.strPageFields.CommitteeViceChairInterestContent)>
								<div id="CommitteeViceChairInterestContent">#variables.strPageFields.CommitteeViceChairInterestContent#</div><br/>
							</cfif>

							<div class="addonMessageArea"></div>
							<div>
								<div class="">
									<label class="checkbox subLabel" for="mccf_CommitteViceChairCheckbox">
									<input class="subCheckbox" type="checkbox" <cfif local.mccf_CommitteViceChairSelected>checked="checked"</cfif> name="mccf_CommitteViceChairCheckbox" id="mccf_CommitteViceChairCheckbox" value="1">
										I am willing to serve as a Committee Vice-Chair
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<button name="btnContinue" type="submit" class="btn btn-default tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.subscriptionUID)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
	
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
	
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.subscriptionUID)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC, variables.strPageFields.ProfileCodePayLater]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>
				
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<style>
				div.tsAppSectionHeading{
					font-family: Verdana, Arial, Helvetica, sans-serif;
					color: ##222;
					background-color: ##F5F5F5;
					padding: 10px;
					font-size: 15px;
					font-weight: bold;
				}				
			</style>
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">Membership Selections Confirmation</div>
				<div class="row-fluid tsAppSectionContentContainer">						
					#local.strResult.formContent#
					<br/><br/>
				</div>
			</div>	
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">Total Price</div><br/>
				<div class="row-fluid tsAppSectionContentContainer">						
					#dollarFormat(local.strResult.totalFullPrice)#
					<br/><br/>					
				</div>
			</div>	
			
			<cfif StructKeyExists(arguments.rc,'email')>
				<input type="hidden" name="email" value='#arguments.rc.email#' />
			</cfif>		
			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton btn btn-default" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
			
		<cfif structKeyExists(arguments.rc, 'mccf_tcbaMentoringCheckbox') >
			<cfset local.mccf_tcbaMentoringHistoryCategory = "Mentorship Interest">

			<cfset local.mh_tcbaMentoring = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MentorshipInterest', subName=local.mccf_tcbaMentoringHistoryCategory)>

			<cfset local.historyID = application.objCustomPageUtils.mh_addHistory(
				memberID=variables.useMID, 
				categoryID=local.mh_tcbaMentoring.categoryID, 
				subCategoryID=local.mh_tcbaMentoring.subCategoryID,
				description='', 
				enteredByMemberID=variables.useMID,
				newAccountsOnly=false
			)>		
		</cfif>
		
		<cfif structKeyExists(arguments.rc, 'mccf_CommitteViceChairCheckbox') >
			<cfset local.mccf_CommitteViceChairHistoryCategory = "Committee Vice-Chair Interest">

			<cfset local.mh_tcbaCommitteViceChair = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='CommVCInterest', subName=local.mccf_CommitteViceChairHistoryCategory)>
	
			<cfset local.historyID = application.objCustomPageUtils.mh_addHistory(
				memberID=variables.useMID, 
				categoryID=local.mh_tcbaCommitteViceChair.categoryID, 
				subCategoryID=local.mh_tcbaCommitteViceChair.subCategoryID,
				description='', 
				enteredByMemberID=variables.useMID,
				newAccountsOnly=false
			)>			
		</cfif>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
		
		<!--- create subscriptions--->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.subscriptionUID)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = arguments.rc)>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)>

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice">
			set nocount on;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

			select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, dueNow= case when nv.dateDue < getdate() then 1 else 0 end
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs
				on subs.subscriptionID = s.subscriptionID
				and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
			inner join dbo.sub_types t
				on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
				and ta.applicationTypeID = 17
				and ta.itemType = 'Dues'
			inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
			inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
			group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
			order by nv.dateDue
		</cfquery>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.contactTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=arguments.rc['md_#local.memberTypeFieldInfo.columnID#'])>
		
		<!--- Join TBA - Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='fc3a6caa-6f61-4efa-9dc8-ff5fa9062e76', mode="confirmation", strData=arguments.rc)>
		<!--- Join TBA -Professional Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='c9a69524-0b85-4c4f-9085-167a0ea9e554', mode="confirmation", strData=arguments.rc)>
		<!--- Join TBA - Professional Information-Paralegal & Legal Assistant --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='53246cff-c058-4a73-b335-b0d9586e008d', mode="confirmation", strData=arguments.rc)>
		<!--- Join TBA - Law Student Information --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='98e1d2b1-8b7c-4854-bb67-24d00fea9a4d', mode="confirmation", strData=arguments.rc)>
		<!--- Join TBA - Business Address Information --->
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='c8814175-5e6a-446b-ad7a-a5ef7a7ab07a', mode="confirmation", strData=arguments.rc)>
		<!--- Join TBA - Home Address Information --->
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='015bb1e9-fa8a-4c54-8ee1-01358f69a557', mode="confirmation", strData=arguments.rc)>
		<!--- Join TBA - Address Preferences --->
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='313dab26-167a-401c-8357-dd5594202688', mode="confirmation", strData=arguments.rc)>
		<!--- Join TCBA - Professional Information-Paralegal/Legal Assistant Student --->
		<cfset local.strFieldSetContent8 = application.objCustomPageUtils.renderFieldSet(uid='3F44DDAF-1672-43D3-A99C-58FA53AAEC11', mode="confirmation", strData=arguments.rc)>
		<!--- Join TCBA - Media Release --->
		<cfset local.strFieldSetContent9 = application.objCustomPageUtils.renderFieldSet(uid='708C56AB-3052-4D90-BFB9-D579A8696936', mode="confirmation", strData=arguments.rc)>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<fieldset>
						<legend>Form submission</legend>
						<div class="row-fluid tsAppSectionContentContainer">#variables.strPageFields.ConfirmationContent#</br></br></div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="row-fluid tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.ConfirmationContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
					<div class="row-fluid tsAppSectionContentContainer">
						<div class="span12">You have received an application for membership through the online Tulsa County Bar Association membership application form.</br></br></div>
					</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.strFieldSetContent1.fieldSetContent#
				<cfif local.contactTypeSelected EQ 'Paralegal Student'>
					#local.strFieldSetContent8.fieldSetContent#
				<cfelseif local.contactTypeSelected EQ 'Law Student'>
					#local.strFieldSetContent4.fieldSetContent#
				<cfelseif local.contactTypeSelected EQ 'Paralegal/Legal Assistant'>
					#local.strFieldSetContent3.fieldSetContent#
				<cfelse>
					#local.strFieldSetContent2.fieldSetContent#
				</cfif>
				#local.strFieldSetContent5.fieldSetContent#
				#local.strFieldSetContent6.fieldSetContent#
				#local.strFieldSetContent7.fieldSetContent#	
				#local.strFieldSetContent9.fieldSetContent#	

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join TCBA - Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfif StructKeyExists(arguments.rc,'email')>
				<cfset variables.memberEmail.TO = arguments.rc.email>
			</cfif>	
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTMLToMember,
			siteID=variables.siteID,
			memberID=val( variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub>
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to TBA", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationPageHTMLContent>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class=" row-fluid tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="row-fluid tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class=" row-fluid tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					TCBA records indicate that you are currently a TCBA member. Please <a href ='/?pg=login'> click here</a> to login.
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					TCBA records indicate that you are currently a TCBA member. Please <a href='/?pg=login'>click here</a> to login.
				<cfelseif arguments.errorCode eq "billedfound">
					You need to renew your TCBA membership. You will be re-directed to your renewal shortly.
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>