<cfinclude template="../commonJS.cfm">

<cfoutput>
<h3>#local.cfcuser_firstname# #local.cfcuser_lastname#, Upload Your Depositions and Documents</h3><hr>
<cfif structKeyExists(local,"notAllowedMessage") and len(local.notAllowedMessage)>
	#local.notAllowedMessage#
<cfelse>
	<div id="docUploadDocFormContainer">
		<form name="frmUploadDoc" id="frmUploadDoc">
			<div class="row-fluid">
				<div class="span6">
					<div style="margin-bottom:10px;">
						<div class="row-fluid depoDocLabel" style="display:flex;margin-bottom:30px;">
							<div class="span4">#local.cfcuser_firstname# #local.cfcuser_lastname#</div>
							<div class="span4">Credits: #dollarFormat(local.qryTotalCreditsAndDepositions.PCTotal)#</div>
							<div class="span4">Total Depositions: #local.qryTotalCreditsAndDepositions.totalDepositions#</div>
						</div>
						<div class="row-fluid depoDocLabel docUpdTool docUploadStepsSummary" style="display:none;margin-bottom:10px;">
							<div class="span4">Award:</div>
							<div class="span7" id="awardCredType"></div>
							<button type="button" name="btnEditAwardType" class="btn span1" onclick="showUploadSteps();" title="Edit">
								<i class="icon-pencil" style="color: ##007bff;"></i>
							</button>
						</div>
						<div class="row-fluid depoDocLabel docUpdTool docUploadStepsSummary" style="display:none;">
							<div class="span4">Organization:</div>
							<div class="span7" id="depoDocOrgDisp"></div>
							<button type="button" name="btnEditDocOrg" class="btn span1" onclick="showUploadSteps();" title="Edit">
								<i class="icon-pencil" style="color: ##007bff;"></i>
							</button>
						</div>
					</div>
				</div>
			</div>
			<div class="row-fluid">
				<div class="span12">
					<div id="docUploadSteps1And2Container" class="docUploadSteps">
						<legend class="depoDocLabel">Step 1: Which trial lawyer group gets Credit for your depositions?</legend>
						<div style="padding-left:10px;margin-bottom:10px;">
							<select name="docState" id="docState" class="span10" onchange="onChangeDepoDocStates();">
								<option value="">Select a Trial Lawyers Association/Litigation Group</option>
								<cfloop query="local.qryDepoStates">
									<option value="#local.qryDepoStates.State#" #((local.defaultSelectedAssociation eq local.qryDepoStates.State) ? "selected" : "")#>#local.qryDepoStates.Description#</option>
								</cfloop>
								<option value="TS">My Association or Group is Not Listed</option>
							</select>
						</div>
						<cfif val(local.qryDepoDocumentSettings.DepoAmazonBucks) AND val(local.qryDepoDocumentSettings.DepoAmazonBucksCredit)>
							<legend class="depoDocLabel">Step 2: What type of Credit do you want for your depositions?</legend>
							<div style="padding-left:10px;">
								<label class="radio" style="margin:10px 0;">
									<input type="radio" name="credType" id="credTypeDepo" value="depo" onclick="onClickCreditPreference(this.value);" checked>
									I prefer Deposition Purchase Credits which I can use at a later date without penalty, up to $30 per depo no limit (best value)
								</label>
								<label class="radio" style="margin:10px 0;">
									<input type="radio" name="credType" id="credTypeAmazonBucks" value="amazon" onclick="onClickCreditPreference(this.value);">
									I prefer Amazon Bonus Cash of up to $10 per deposition, which will be awarded to me in the form of an Amazon Gift Card.
								</label>
								<div id="depoDocAmazonBucksInfo" style="display:none;">
									<div class="row-fluid">
										<div class="span12">
											<label for="depoDocAmazonBucksFullName">Full Name for Amazon Gift Card Recipient</label>
											<input type="text" id="depoDocAmazonBucksFullName" name="depoDocAmazonBucksFullName" value="#local.cfcuser_firstname# #local.cfcuser_lastname#" placeholder="Full Name" maxlength="550" class="span10" autocomplete="off" onblur="validateUploadDocsForm('prevalidate');">
										</div>
									</div>
									<div class="row-fluid" style="margin-top:10px;">
										<div class="span12">
											<label for="depoDocAmazonBucksEmail">Email for Amazon Gift Card Recipient</label>
											<input type="text" id="depoDocAmazonBucksEmail" name="depoDocAmazonBucksEmail" value="#local.cfcuser_email#" placeholder="Email" maxlength="255" class="span10" autocomplete="off" onblur="validateUploadDocsForm('prevalidate');">
										</div>
									</div>
								</div>
							</div>
						<cfelse>
							<input type="radio" name="credType" id="credTypeDepo" value="depo" style="display:none;" checked>
						</cfif>
					</div>
				</div>
			</div>
			<div class="row-fluid">
				<div class="span12">
					<div id="depoDocUploader" class="docUpdTool" style="display:none;">
						<p>Your browser does not have HTML5 support.</p>
					</div>
					<div id="depoDocUploaderError" class="alert alert-error" style="display:none;margin:20px 0;"></div>
					<div class="text-right docUploadSteps" style="margin-top:20px;">
						<button type="button" name="btnContinue" class="btn btn-primary" onclick="showUploadSection();">
							<i class="fa fa-arrow-right" aria-hidden="true"></i>
							Continue to Upload Your Depositions
						</button>
						<button type="button" name="btnLearnMore" class="btn btn-secondary" onclick="showLearnMoreSection();">
							<i class="fa fa-question" aria-hidden="true"></i>
							Learn More / FAQ
						</button>
					</div>				
					<div id="depoDocUploaderButtonContainer" class="docUpdTool" style="display:none;margin-top:20px;">
						<button type="button" name="btnUploadDocs" id="btnUploadDocs" class="btn btn-primary" onclick="startUploadDocs();" disabled>Start Upload of Files</button>
					</div>
				</div>
				<div id="docUploadDocConf" style="display:none;"></div>
			</div>
		</form>
	</div>
	
	<div class="row-fluid" style="display:none;" id="learnMoreSection">
		<div class="span12" style="font-size:16px;">
			<div style="margin-bottom:30px;">#local.qryContentUploadDocumentsStep2Info.rawContent#</div>
			<ul class="nav nav-tabs depoDocNavTab">
				<li class="active"><a href="##aboutCredits" data-toggle="tab">About Credits</a></li>
				<li><a href="##depoDocTermsAgreement" data-toggle="tab">Terms</a></li>
				<li><a href="##depoDocFAQ" data-toggle="tab">About Depo Database</a></li>
			</ul>
			<div class="tab-content" style="min-height:475px;font-size:15px;">
				<div class="tab-pane active" id="aboutCredits">
					<cfset local.aboutCreditsContent = local.qryContentUploadDocumentsStep2.rawContent>
					<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'[[TSEncodedDepoMemberName]]',encodeForHTMLAttribute("#local.cfcuser_firstname# #local.cfcuser_lastname#"), 'all')>
					<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'[[TSDepoMemberDataID]]',local.cfcuser_depomemberdataid, 'all')>
					<cfif val(local.qryDepoDocumentSettings.DepoAmazonBucks) AND val(local.qryDepoDocumentSettings.DepoAmazonBucksCredit)>
						<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'[[DepoDocAmazonCredits]]',"Earn Amazon credits of up to $10 per transcript", 'all')>
					<cfelse>
						<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'<li>[[DepoDocAmazonCredits]]</li>',"", 'all')>
					</cfif>
					#local.aboutCreditsContent#
				</div>
				<div class="tab-pane" id="depoDocTermsAgreement">#local.qryContentUploadDocumentsStep2Terms.rawContent#</div>
				<div class="tab-pane" id="depoDocFAQ">#local.qryContentUploadDocumentsStep2FAQ.rawContent#</div>
			</div>
		</div>
	</div>
</cfif>
</cfoutput>