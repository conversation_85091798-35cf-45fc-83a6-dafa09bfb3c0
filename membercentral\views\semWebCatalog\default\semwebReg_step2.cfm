<cfinclude template="../commonSWRegStep2JS.cfm">

<cfoutput>
<div class="swreg-card swreg-mt-3 swreg-p-3 tsAppBodyText">
	<cfif local.strProgram.qryPrices.recordCount>
		<form name="frmSWRegStep2" id="frmSWRegStep2" autocomplete="off">
			<input type="hidden" name="registrantName" id="registrantName" value="#encodeForHTMLAttribute(local.qryCurrentRegMember.firstname & ' ' & local.qryCurrentRegMember.lastname)#">

			<div id="swRegRatesContainer"<cfif local.rateID GT 0> style="display:none;"</cfif>>
				<div class="swreg-mb-3 swreg-font-size-lg swreg-text-dim">Select your #local.strProgram.programLabel# rate:</div>
				<cfif local.strProgram.qryPrices.recordcount GT 1>
					<cfloop query="local.strProgram.qryPrices">
						<cfset local.thisRegRatePriceDisp = "#local.strProgram.qryPrices.price IS 0 ? local.strProgram.qryPrices.freeRateDisplay : '#dollarformat(local.strProgram.qryPrices.price)##local.strProgram.showUSD ? " USD" : ""#'#">
						<div class="swreg-d-flex swreg-mb-2">
							<input type="radio" name="sw_rateID" id="sw_rateID#local.strProgram.qryPrices.rateID#" class="swreg-align-self-start swreg-mr-1" data-regratepricedisp="#encodeForHTMLAttribute(local.thisRegRatePriceDisp)#" value="#local.strProgram.qryPrices.rateID#" onclick="saveRegRate();"<cfif local.rateID EQ local.strProgram.qryPrices.rateID> checked</cfif>>
							<div class="swreg-col">
								<label for="sw_rateID#local.strProgram.qryPrices.rateID#" class="swreg-d-inline-block swreg-mb-1">#encodeForHTML(local.strProgram.qryPrices.description)#</label>
							</div>
							<div class="swreg-ml-auto swreg-font-size-lg swreg-font-weight-bold">#local.thisRegRatePriceDisp#</div>
						</div>
					</cfloop>
				<cfelse>
					<cfset local.thisRegRatePriceDisp = "#local.strProgram.qryPrices.price IS 0 ? local.strProgram.qryPrices.freeRateDisplay : '#dollarformat(local.strProgram.qryPrices.price)##local.strProgram.showUSD ? " USD" : ""#'#">
					<div class="swreg-d-flex swreg-mb-3">
						<input type="radio" name="sw_rateID" id="sw_rateID#local.strProgram.qryPrices.rateID#" class="swreg-align-self-start swreg-mr-1" data-regratepricedisp="#encodeForHTMLAttribute(local.thisRegRatePriceDisp)#" value="#local.strProgram.qryPrices.rateID#" checked>
						<div class="swreg-col">
							<label for="sw_rateID#local.strProgram.qryPrices.rateID#" class="swreg-d-inline-block swreg-mb-1">#encodeForHTML(local.strProgram.qryPrices.description)#</label>
						</div>
						<div class="swreg-ml-auto swreg-font-size-lg swreg-font-weight-bold">#local.thisRegRatePriceDisp#</div>
					</div>
					<div class="swreg-text-right">
						<button type="button" name="btnContinueRateSelection" id="btnContinueRateSelection" class="tsAppBodyButton" onclick="saveRegRate();">Continue</button>
					</div>
				</cfif>
			</div>
			<div id="swSelectedRegRate"<cfif local.strProgram.qryPrices.recordcount GT 1> class="swRegStepSummary swreg-cursor-pointer" data-swregsummarystep="2"</cfif><cfif NOT local.rateID> style="display:none;"</cfif>>
				<div class="swreg-d-flex">
					<cfif local.strProgram.qryPrices.recordcount GT 1>
						<a href="##" class="swreg-align-self-center swreg-mr-2 swreg-font-size-lg swreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
					</cfif>
					<div class="swreg-col">
						<div id="swSelectedRegRateName" class="swreg-font-size-lg swreg-font-weight-bold">#local.swReg.currentReg.s2.description#</div>
					</div>
					<div id="swSelectedRegRatePrice" class="swreg-ml-auto swreg-font-size-lg swreg-font-weight-bold">#local.swReg.currentReg.s2.ratePriceDisplay#</div>
				</div>
			</div>
			<div id="swRegRateSaveLoading" style="display:none;"></div>
		</form>
	<cfelse>
		<div class="alert"><b>This registrant does not qualify for any pricing options established for this program.</b></div>
	</cfif>
</div>
</cfoutput>