<cfsavecontent variable="local.pghead">
	<cfoutput>
	<style type="text/css">
		.myKBA .nav-tabs > .active > a,.myKBA  .nav-tabs>.active>a:hover {
			background: ##993333 !important;
		}


		.myKBA .nav-tabs > li > a {
			margin-right: 23px;
			background: ##cc9966 !important;
		}

		.myKBA .nav-tabs > li > a {
			color: ##555555!important;
			font-weight: normal;
		}

		.myKBA .nav-tabs > li:last-child > a {
			margin-right: auto;
			color: ##555555!important;
			font-weight: normal;
		}

		.myKBA .nav-tabs a, .myKBA .nav-tabs a:hover {
			color: ##555555;
			background: ##d3d3d3 !important;
			border-color: transparent;
		}

		.myKBA .nav { margin-bottom: 0px; }

		.infoCont {
			padding-top: 20px !important;
			margin-bottom: 30px !important;
		}

		.KBARow {
			margin-left: 0px!important;
			margin-bottom: 0px!important;
		}

		.tab-content {
			border: 2px solid ##ddd;
			min-height: 220px;
			padding: 10px;
			margin-bottom: 20px;
			background: ##fff;
		}

		.showBullets { list-style: inside!important; }

		.myKBA .nav-tabs > li > a {
			border: 1px solid transparent;
			border-radius: 4px 4px 0 0;
			line-height: 1.42857;
			margin-right: 2px;
		}
		.carousel {
			margin-bottom:0 !important;
		}
		.HeaderText {
			color: ##333436;
			font-weight:bold;
			font-size: 16px;
			line-height: 19px;
			margin: 0;
			padding: 0 0 13px;
		}

		.myKBA .myInfo .showBullets a{color:##993333 !important;}

		.carousel-inner p {
			padding: 15px!important;
			margin: 15px 0 20px!important;
		}

		.myKBA .nav-tabs > li.active > a {
			color:##ffffff !important;
			font-weight: 700 !important;
		}

		##myKBA a { color:##993333; }
		##myKBA .memName { color:##333333; }
		##myKBA p { margin:0; }
		
		##myKBA .tab-content .tab-pane {
			overflow-y: auto;
			min-height: 200px;
			max-height: 200px;
		}	
		
		ul>div.item {
			margin-left: -25px;
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pghead#">

<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="Sponsors", type="CONTENTOBJ", desc="Sponsors", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="QuickLinks", type="CONTENTOBJ", desc="Content for quick links section", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="UpcomingEvents", type="CONTENTOBJ", desc="Content for upcoming events section", value="There are currently no upcoming events." };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="QuickLinksTitle", type="STRING", desc="Title for quick links section", value="Quick Links" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MyEvents", type="CONTENTOBJ", desc="Content for my events section", value="You are currently not registered for any Events." };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="OnlineCLE", type="CONTENTOBJ", desc="Content for my Online CLE section", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="myKbaProfileLinks", type="CONTENTOBJ", desc="Content for mykba profile Links", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	local.bulletLg = "<img src='/images/img_bullet_lg.png' align='left' border='0' />";
	local.objReferrals = CreateObject("component","model.referrals.referrals");
	local.qryGetMemberReferrals = local.objReferrals.getMemberReferrals(memberID=session.cfcuser.memberdata.memberid, siteID=arguments.event.getValue('mc_siteinfo.siteid'));
	local.qryGetMemberCases = local.objReferrals.getMemberCases(memberID=session.cfcuser.memberdata.memberid, siteID=arguments.event.getValue('mc_siteinfo.siteid'));
	local.qryGetMemberReferralHistory = local.objReferrals.getMemberReferralHistory(memberID=session.cfcuser.memberdata.memberid, siteID=arguments.event.getValue('mc_siteinfo.siteid'));
	</cfscript>

<cfoutput>
	<div class="container" id="myKBA">
		<div class="row-fluid">
			<div id="mainContent">
				<div class="span12 row-fluid myKBA infoCont">
					<div class="span1 myPhoto">
						<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
							<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
						</cfif>
					</div>
					<div class="span10 myInfo">
						<span class="HeaderText memName">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName# #session.cfcUser.memberData.suffix#</span><br />
						<span class="BodyText">
							<ul style="margin:0px" class="showBullets">
								#replace(replace(local.strPageFields.myKbaProfileLinks, "<ul>", "", "all"), "</ul>", "", "all")#
							</ul>
							<-- ZONE T -->
						</span>
					</div>
				</div>

				<!--- Row 1 --->
				<div class="span12 row-fluid myKBA KBARow">
					<!--- Row 1 Left Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##KBANews" data-toggle="tab" class="MainNavText">KBA News</a></li>
								<li><a href="##Announcements" data-toggle="tab" class="MainNavText">Announcements</a></li>
							</ul>
							<div class="tab-content">
								<!--- KBA News --->
								<div id="KBANews" class="tab-pane active BodyText">
									[[recentBlogEntries pageName=[KBANews] format=[json] jsonvariable=[blogEntries] maxrows=[5] noresultstext=[Currently, there are no new entries.]]]
									<div class="mcMergeTemplate" data-mcjsonvariable="blogEntries" id="blogEntriesID" data-mcmergerunafter="initBlogSlider" data-mcmergereplacecontainer="true">
										<ul class="mc-mergeTagList mc-blogList">
										{{##each blogEntries}}
											<li class="mc-mergeTagListItem">
												<a href="{{{entryLink}}}">{{{blogTitle}}}</a>
											</li>
										{{else}}
											<div class="item">
												<div class="Articles clearfix"> 
													<div class="span12">   
														<p>Currently, there are no new entries.</p>
													</div>	
												</div>
											</div>										
										{{/each}}
										</ul>
										<br>
										<a href="/?pg=KBANews" target="_blank"><strong>View all news items</strong></a>
										<br>
										<a href="/?pg=Blogs" target="_blank"><strong>Click here to read Legal News</strong></a>
									</div>
								</div>
								<!--- Announcements --->
								<div id="Announcements" class="tab-pane BodyText">
									[[myAnnouncements maxrows=[5] noresultstext=[Currently, there are no announcements.]]]
								</div>
							</div>
						</div>
					</div>

					<!--- Row 1 Center Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##quickLinks" data-toggle="tab" class="MainNavText">#local.strPageFields.QuickLinksTitle#</a></li>
								<li><a href="##myRecentSearches" data-toggle="tab" class="MainNavText">My Recent Searches</a></li>
							</ul>
							<div class="tab-content">
								<!--- Quick Links --->
								<div id="quickLinks" class="tab-pane active BodyText">
									#local.strPageFields.QuickLinks#
								</div>
								<!--- My Recent Searches --->
								<div id="myRecentSearches" class="tab-pane BodyText">
									[[mySearchHistory maxrows=[5] noresultstext=[You do not have any previous searches.]]]
								</div>
							</div>
						</div>
					</div>

					<!--- Row 1 Right Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##Sponsors" data-toggle="tab" class="MainNavText">Sponsors</a></li>
							</ul>
							<div class="tab-content">
								<!--- Sponsors --->
								<div id="Sponsors" class="tab-pane active BodyText">
									<div id="myCarousel" class="carousel slide text-center">
										<!-- Carousel items -->
										<div class="carousel-inner text-center">
											<cfif len(trim(local.strPageFields.Sponsors))>
												<cfloop list="#local.strPageFields.Sponsors#" index="local.thisImage" delimiters="||">
													<div class="<cfif ListFirst(local.strPageFields.Sponsors,'||') eq local.thisImage>active </cfif>item">
														<div><a href="<cfif ListLen(local.thisImage , ',') GT 1>#ListLast(local.thisImage , ',')#<cfelse>/</cfif>" target="_blank">#ReReplace(ReReplace(ListFirst(local.thisImage , ','),'<p>',''),'</p>','')#</a></div>
													</div>
												</cfloop>
											</cfif>
										</div>
									</div>
									<cfif len(trim(local.strPageFields.Sponsors))>
										<script type='text/javascript'>
											$(document).ready(function() {
												$('.carousel').carousel({
													interval: 4000
												});
											});
										</script>
									</cfif>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!--- Row 2 --->
				<div class="span12 row-fluid myKBA KBARow">
					<!--- Row 2 Left Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##liveCLE" data-toggle="tab" class="MainNavText">Live CLE</a></li>
								<li><a href="##myRegistrations" data-toggle="tab" class="MainNavText">My Registrations</a></li>
								<li><a href="/?pg=myCLE" class="MainNavText" target="_blank">My CLE History</a></li>
							</ul>
							<div class="tab-content">
								<!--- Live CLE --->
								<div id="liveCLE" class="tab-pane active BodyText">
									[[upcomingEvents pagename=[events] includeRegistered=[false] category=[CLE] includeNotRegistered=[true] maxrows=[5] noresultstext=[There are currently no upcoming events.]]]
									<br/>
									<div>
										<a href="/?pg=events" target="_blank"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
								<!--- My Registrations --->
								<div id="myRegistrations" class="tab-pane BodyText">
									[[upcomingEvents pagename=[events] includeRegistered=[true] category=[CLE] includeNotRegistered=[false] maxrows=[5] noresultstext=[You are currently not registered for any Events.]]]
								</div>
								<!--- My CLE History --->
								<div id="myCLEHistory" class="tab-pane BodyText">
								</div>
							</div>
						</div>
					</div>

					<!--- Row 2 Center Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##upcomingEvents" data-toggle="tab" class="MainNavText">Upcoming Events</a></li>
								<li><a href="##myEvents" data-toggle="tab" class="MainNavText">My Events</a></li>
							</ul>
							<div class="tab-content">
								<!--- Upcoming Events --->
								<div id="upcomingEvents" class="tab-pane active BodyText">
									#local.strPageFields.UpcomingEvents#
									<br/>
									<div>
										<a href="/?pg=events" target="_blank"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
								<!--- My Events --->
								<div id="myEvents" class="tab-pane BodyText">
									#local.strPageFields.MyEvents#
									<br/>
									<div>
										<a href="/?pg=events" target="_blank"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<!--- Row 2 Right Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##myECommunities" data-toggle="tab" class="MainNavText">My E-Communities</a></li>
								<!--- <li><a href="##browseMyListServers" data-toggle="tab" class="MainNavText">Browse My List Servers</a></li> --->
							</ul>
							<div class="tab-content">
								<!--- My E-Communities --->
								[[myECommunities format=[json] jsonvariable=[eCommunity]]]
								<div id="myECommunities" class="tab-pane active BodyText mcMergeTemplate" data-mcjsonvariable="eCommunity">
									{{##if eCommunities}}
										<div class="sectionTitle"><b>You have access to the following groups:</b></div>
										{{##each eCommunities}}
											<ul class="mc-communityList">
												<li>{{{communityName}}} <a href="{{{pageLink}}}">(View)</a></li>
											</ul>
										{{/each}}
									{{else}}
										You do not have access to any groups.
									{{/if}}
								</div>
								<!--- Browse My List Servers --->
								<!--- <div id="browseMyListServers" class="tab-pane BodyText">
								</div> --->
							</div>
						</div>
					</div>
				</div>
				<!--- Row 3 --->
				<div class="span12 row-fluid myKBA KBARow">
					<!--- Row 3 Left Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##newFileshareDocs" data-toggle="tab" class="MainNavText">New Fileshare Docs</a></li>
								<li><a href="##myFileDownloads" data-toggle="tab" class="MainNavText">My File Downloads</a></li>
							</ul>
							<div class="tab-content">
								<!--- New Fileshare Docs --->
								<div id="newFileshareDocs" class="tab-pane active BodyText">
									[[recentFileShareUploads noresultstext=[There are no recent documents.]]]
								</div>
								<!--- My File Downloads --->
								<div id="myFileDownloads" class="tab-pane BodyText">
									[[myRecentFileshareDownloads noresultstext=[There are no recent downloads.]]]
								</div>
							</div>
						</div>
					</div>
					
					<!--- Row 3 Center Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##pastDueInvoices" data-toggle="tab" class="MainNavText">Past Due Invoices</a></li>
							</ul>
							<div class="tab-content">
								<!--- Past Due Invoices --->
								<div id="pastDueInvoices" class="tab-pane active BodyText">
									[[overdueInvoices maxrows=[4] noresultstext=[You do not have any past due invoices.]]]
									<br/>
									<div>
										<a href="/?pg=invoices" target="_blank"><i class="icon-calendar icon2x">&nbsp;</i><strong>View All Past Due Invoices</strong></a>
									</div>
								</div>
							</div>
						</div>
					</div>
					<cfif local.qryGetMemberReferralHistory.recordCount>
					<!--- Row 3 Right Pane --->
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
							<cfif local.qryGetMemberReferrals.recordCount GT 0>
								<li class="active"><a href="##myReferrals" data-toggle="tab" class="MainNavText">My Referrals</a></li>
							</cfif>
							<cfif local.qryGetMemberCases.recordCount GT 0>
								<li><a href="##myRetainedCases" data-toggle="tab" class="MainNavText">My Retained Cases</a></li>
							</cfif>
							<cfif not local.qryGetMemberCases.recordCount and not local.qryGetMemberReferrals.recordCount>
								<li class="<cfif not local.qryGetMemberReferrals.recordCount and not local.qryGetMemberCases.recordCount>active</cfif>"><a href="##myHistorydCases" data-toggle="tab" class="MainNavText">My Referral History </a></li>
							</cfif>							
							</ul>
							<div class="tab-content">
								<cfif local.qryGetMemberReferrals.recordCount>
									<!--- My Referrals --->
									<div id="myReferrals" class="tab-pane active BodyText">
										<cfloop query="local.qryGetMemberReferrals" startrow="1" endrow="3" >
											<li>#local.qryGetMemberReferrals.clientName# <cfif (local.qryGetMemberReferrals.currentRow eq local.qryGetMemberReferrals.recordCount) or (local.qryGetMemberReferrals.currentRow eq 3)><br /><br /><a href="/?pg=referrals">See more</a></cfif></li>
										</cfloop>
									</div>
								</cfif>
								<cfif local.qryGetMemberCases.recordCount>
									<!--- My Retained Cases --->
									<div id="myRetainedCases" class="tab-pane BodyText">
										<cfloop query="local.qryGetMemberCases" startrow="1" endrow="3" >
										<li> #local.qryGetMemberCases.clientName# <cfif (local.qryGetMemberCases.currentRow eq local.qryGetMemberCases.recordCount) or (local.qryGetMemberCases.currentRow eq 3)><br /><br /><a href="/?pg=referrals">See more</a></cfif></li>
										</cfloop>
									</div>
								</cfif>
								<cfif local.qryGetMemberReferralHistory.recordCount>
									<!--- My Retained Cases --->
									<div id="myHistorydCases" class="tab-pane <cfif not local.qryGetMemberReferrals.recordCount and not local.qryGetMemberCases.recordCount>active</cfif> BodyText">
										<cfloop query="local.qryGetMemberReferralHistory" startrow="1" endrow="3" >
										<li> #local.qryGetMemberReferralHistory.clientName# <cfif (local.qryGetMemberReferralHistory.currentRow eq local.qryGetMemberReferralHistory.recordCount) or (local.qryGetMemberReferralHistory.currentRow eq 3)><br /><br /><a href="/?pg=referrals">See more</a></cfif></li>
										</cfloop>
									</div>
								</cfif>								
							</div>
						</div>
					</div>
					</cfif>
				</div>
			</div>
		</div>
	</div>
</cfoutput>