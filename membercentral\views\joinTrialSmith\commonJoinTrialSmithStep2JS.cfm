<cfsavecontent variable="local.joinTrialSmithStep2Head">	
	<cfoutput>
	<script type="text/javascript">          
		function editJoinTrialSmithStep3() {
			$('##joinTrialSmithSelectedPlanContainer').hide();
			$('##joinTrialSmithPlanContainer').show(300);
		}

        function loadStep2(payload){
            $('##joinTrialSmithStep2SaveLoading')
            .html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
            .load('#arguments.event.getValue('jointrialsmithurl')#&action=saves2',payload,
                function(resp) {
                    let respObj = JSON.parse(resp);
                    if(typeof(respObj.redirectlocation) != 'undefined'){
                        window.location = respObj.redirectlocation;
                    }
                    let arrSteps = respObj.loadsteps.split(',');
                    let arrClearSteps = respObj.clearsteps.split(',');
                    var arrReq = new Array();

                    if ($.trim(respObj.codeerr).length) arrReq[arrReq.length] = respObj.codeerr;

                    if (arrReq.length) {
                        $('html, body').animate({
                            scrollTop: $('##joinTrialSmithStep2').offset().top - 75
                        }, 750);

                        $('##joinTrialSmithStep2 ##joinTrialSmithPlanContainer').prepend('<div class="alert alert-danger error planError">'+arrReq.join('</br>')+'</div>')
                    }else{
                        
                        $('##joinTrialSmithSelectedPlan').html(respObj.selectedplanstring);
                        
                        $('##joinTrialSmithPlanContainer,##joinTrialSmithStep2SaveLoading').hide();
                        $('##joinTrialSmithSelectedPlanContainer').show(300);                        
                        
                        arrClearSteps.forEach(function(step,index) {
                            $('##joinTrialSmithStep'+step).html('');
                        });

                        arrSteps.forEach(function(step,index) {
                            loadJoinTrialSmithSteps(step);
                        });

                        if(arrSteps.length){
                            $('html, body').animate({
                                scrollTop: $('##joinTrialSmithStep'+arrSteps[0]).offset().top - 75
                            }, 750);
                        }

                    }
                }
            )
            .show();
        }

        function promoSubmit(mode){
            $(".planError").remove();
            if(mode){
                var arrReq = new Array();
                var payload = {};
                if($.trim($("##promoCode").val()).length){
                    payload = {addpromocode:1,promocode:$.trim($("##promoCode").val())};
                    loadStep2(payload);
                }else{
                    if ($.trim($('##promoCode').val()).length == 0) arrReq[arrReq.length] = 'Enter a valid promotional code.';

                    if (arrReq.length) {
                        $('html, body').animate({
                            scrollTop: $('##joinTrialSmithStep2').offset().top - 75
                        }, 750);

                        $('##joinTrialSmithStep2 ##joinTrialSmithPlanContainer').prepend('<div class="alert alert-danger error planError">'+arrReq.join('</br>')+'</div>')
                    }
                }
                
            }else{
                var payload = {};
                payload = {removepromocode:1};
                loadStep2(payload);
            }
        }
        
		$(function() {
            $('input[type=radio][name=membertype]').change(function() {
                $(".planError").remove();
                var payload = {};
                if(this.value.length){
                    $("##promoCode").val('');
                    if($.trim($("##btnPromo").val()).length){
                        payload = {membertype:this.value,promocode:$.trim($("##btnPromo").val())};
                    }else{
                        payload = {membertype:this.value};
                    }
                    loadStep2(payload);
                }                
            });
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.joinTrialSmithStep2Head)#">