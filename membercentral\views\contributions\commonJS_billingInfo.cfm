<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function validateBillingInfo() {
			$('##frmContribBillingInfo button:submit').html('<i class="icon-spinner icon-spin"></i> Please wait...').prop('disabled',true);
			hideStep2Alert();
			let arrReq = [];
			let stateIDForTax = $('##stateIDForTax').val();
			let zipForTax = $('##zipForTax').val();
			if (stateIDForTax == '') arrReq[arrReq.length] = 'Billing State/Province is required.';
			if (zipForTax == '') arrReq[arrReq.length] = 'Billing Postal Code is required.';
			
			if (stateIDForTax > 0 && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDForTax,''))
				arrReq[arrReq.length] = 'Invalid Billing Postal Code.';
			if (arrReq.length) {
				showStep2Alert(arrReq.join('<br/>'));
				$('##frmContribBillingInfo button:submit').html('Continue').prop('disabled',false);
				return false;
			}

			return true;
		}
		showStep2Alert = function(msg) {
			$('##cp2_err_div').html(msg).addClass('alert').show();
			$('html,body').animate({ scrollTop: $('##cp2_err_div').offset().top - 100}, 'slow');
		};
		hideStep2Alert = function() {
			$('##cp2_err_div').html('').removeClass('alert').hide();
		};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">