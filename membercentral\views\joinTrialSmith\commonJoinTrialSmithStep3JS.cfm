
<cfsavecontent variable="local.joinTrialSmithStep3Head">	
	<cfoutput>
	<script type="text/javascript">          
		function editJoinTrialSmithStep4() {
			$('##joinTrialSmithSelectedTermContainer').hide();
			$('##joinTrialSmithTermContainer').show(300);
		}
        
		function agreeSubmit(isAgree){
            var iagree = {};
            if(isAgree) iagree = {iagree:isAgree};
            $('##joinTrialSmithStep3SaveLoading')
                .html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
                .load('#arguments.event.getValue('jointrialsmithurl')#&action=saves3',iagree,
                    function(resp) {
                        let respObj = JSON.parse(resp);
                        let arrSteps = respObj.loadsteps.split(',');
                        let arrClearSteps = respObj.clearsteps.split(',');
                        
                        $('##joinTrialSmithTermContainer,##joinTrialSmithStep3SaveLoading').hide();
                        $('##joinTrialSmithSelectedTermContainer').show(300);

                        arrClearSteps.forEach(function(step,index) {
                            $('##joinTrialSmithStep'+step).html('');
                        });
                        
                        arrSteps.forEach(function(step,index) {
                            loadJoinTrialSmithSteps(step);
                        });

                        if(arrSteps.length){
                            $('html, body').animate({
                                scrollTop: $('##joinTrialSmithStep'+arrSteps[0]).offset().top - 75
                            }, 750);
                        }
                    }
                )
                .show();
        }
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.joinTrialSmithStep3Head)#">