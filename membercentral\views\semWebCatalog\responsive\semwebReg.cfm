<cfinclude template="../commonSWRegJS.cfm">
<cfinclude template="../commonSWRegStyles.cfm">

<cfoutput>
<div id="swRegContainer" class="container-fluid swReg">
	<div<cfif local.swReg.currentReg.currentStep NEQ 1> class="swreg-card swreg-p-3 swreg-mb-3 swreg-bg-whitesmoke"</cfif>>
		<div class="swreg-d-flex">
			<h3 class="swreg-col swreg-p-0">#encodeForHTML(local.programTitle)#</h3>
			<a class="swreg-col-auto swreg-text-decoration-none swreg-font-size-sm swreg-text-right swreg-align-self-center hidden-phone" href="#arguments.event.getValue('detailLink')#">
				<i class="icon icon-arrow-left"></i> Back to #local.programLabel# details
			</a>
		</div>
		<cfif len(local.programSubTitle)><div class="swreg-pl-2 swreg-text-dim">#encodeForHTML(local.programSubTitle)#</div></cfif>
		<cfif len(local.programSponsorBy)>
			<div class="swreg-pl-2 swreg-mb-3">#local.programSponsorBy#</div>
		</cfif>
		<div class="swreg-mb-2 visible-phone">
			<a class="swreg-col-auto swreg-text-decoration-none swreg-font-size-sm swreg-text-right swreg-align-self-center" href="#arguments.event.getValue('detailLink')#">
				<i class="icon icon-arrow-left"></i> Back to #local.programLabel# details
			</a>
		</div>
	</div>
	<cfif local.swReg.currentReg.currentStep EQ 1>
		<cfinclude template="semwebReg_step1.cfm">
	<cfelse>
		<div class="swreg-card swreg-p-3">
			<div class="swreg-mb-3 swreg-font-size-lg swreg-text-dim">Registration for:</div>
			<div class="swreg-d-flex">
				<div class="swreg-d-flex swreg-col swreg-flex-sm-column">
					<cfif local.showMemberPhoto>
						<div><img src="#local.strRegMember.hasPhoto ? '/memberphotosth/#LCASE(local.strRegMember.memberphoto)#' : '/assets/common/images/directory/default.jpg'#" class="swreg-mr-3 swreg-img-thumbnail"></div>
					</cfif>
					<div>
						<h4 class="swreg-mb-1 swreg-mt-1">
							#local.strRegMember.mc_combinedName#
							<cfif len(local.strRegMember.company)><div class="swreg-p-1"><small>#local.strRegMember.company#</small></div></cfif>
						</h4>
						<div class="swreg-mt-1 swreg-p-1">
							<cfif len(local.strRegMember.mc_combinedAddresses)>#local.strRegMember.mc_combinedAddresses#</cfif>
							<cfif len(local.strRegMember.mc_extraInfo)>#local.strRegMember.mc_extraInfo#</cfif>
							<cfif len(local.strRegMember.mc_recordType)><div>#local.strRegMember.mc_recordType#</div></cfif>
							<cfif len(local.strRegMember.mc_memberType)><div>#local.strRegMember.mc_memberType#</div></cfif>
							<cfif len(local.strRegMember.mc_lastlogin)><div>#local.strRegMember.mc_lastlogin#</div></cfif>
						</div>
					</div>
				</div>
				<div class="swreg-d-flex swreg-flex-column swreg-ml-auto">
					<!--- limitiation: if the association does NOT take the money, all items in the cart must be for the same registrant --->
					<cfif local.qrySWP.handlesOwnPayment is 1 OR NOT arrayLen(local.swReg.regCart)>
						<button type="button" class="swreg-btn-outline-primary" onclick="searchSWReg();">Register Someone Else</button>
					</cfif>
					<button type="button" class="swreg-btn-outline-primary swreg-mt-3" onclick="cancelReg();">Cancel</button>
				</div>
			</div>
			<cfif local.programType EQ 'SWB' AND local.qryEnrolledItems.recordcount GT 0 >	
				<div class="swreg-alert d-flex" role="alert">				
					<p>This registrant is already enrolled in the following programs included in this bundle. If you proceed with this bundle registration, they will not be re-enrolled in these programs:</p>
					<ul>
					<cfloop query="local.qryEnrolledItems">
						<li>#local.qryEnrolledItems.seminarName# (<i>Enrolled on: #DATEFORMAT(local.qryEnrolledItems.dateEnrolled,"m/d/yyyy")#</i>)</li>
					</cfloop>
					</ul>
				</div>
			</cfif>
			<cfif local.programType EQ 'SWB' AND arrayLen(local.arrMatchedSeminarsInCart) GT 0 >	
				<div class="swreg-alert d-flex" role="alert">
					<p>This registrant already added these programs to their cart that are included in this bundle. If you proceed, we'll remove the programs below from your cart to prevent duplicate registrations:</p>
					<ul>
					<cfloop array="#local.arrMatchedSeminarsInCart#" index="local.thisCartProgram">
						<li>#local.thisCartProgram.s2.programname#</li>
					</cfloop>
					</ul>
				</div>
			</cfif>
		</div>

		<div id="SWRegStep2" style="display:none;"></div>
		<div id="SWRegStep3" style="display:none;"></div>
		<div id="SWRegStep4" style="display:none;"></div>
		
		<cfif local.swReg.currentReg.isRegCartItem EQ 1>
			<div class="swreg-mt-5 swreg-text-center">
				<button type="button" name="btnGotoRegCart" id="btnGotoRegCart" class="btn btn-success btnFinalizeReg" onclick="regCartCheckout();" style="width:200px;" disabled>Checkout</button>
			</div>
		</cfif>
	</cfif>
</div>
<div id="SWRegLoading" class="swReg" style="display:none;">
	<div class="swreg-card swreg-mt-3 swreg-p-3">
		<div class="swreg-skeleton swreg-skeleton-text"></div>
		<div class="swreg-skeleton swreg-skeleton-text"></div>
		<div class="swreg-skeleton swreg-skeleton-text"></div>
		<div class="swreg-skeleton swreg-skeleton-text"></div>
	</div>
</div>
</cfoutput>