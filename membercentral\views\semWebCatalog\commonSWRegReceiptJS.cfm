<cfsavecontent variable="local.swRegReceiptHead">
	<cfoutput>
	<script language="javascript">
		<cfif arguments.qrySWP.handlesOwnPayment is 1>
			function sendReceipt() {
				var emailRegEx = new RegExp("#application.regEx.email#","i");
				var btn = $('##btnResendReceipt');
				var msg = $('##swRegSendPmtReceiptMsg');
				var em = $('##sendToEmail');
				msg.html('').removeClass('swreg-text-danger').hide();
				btn.html('Sending...').attr("disabled", true);

				var resendReceiptResult = function(r) {
					btn.html('Send').attr("disabled", false);
					if (r.success && r.success.toLowerCase() == 'true') {
						msg.html('<i class="icon-check"></i> &nbsp; <b>Receipt sent to '+em.val()+'.</b>').show().fadeOut(5000);
						em.val('');
					} else {
						msg.html('There was a problem e-mailing this receipt. Try again.').addClass('swreg-text-danger').show();
					}
				};
				
				em.val($.trim(em.val()));
				if (em.val().length == 0 || !(emailRegEx.test(em.val()))) {
					btn.html('Send').attr("disabled", false);
					msg.html('Enter a valid e-mail address.').addClass('swreg-text-danger').show();
					return false;
				}

				var objParams = { receiptUUID:'#local.receiptUUID#', sendToEmail:em.val() };
				TS_AJX('ADMINSWCOMMON','sendPaymentReceipt',objParams,resendReceiptResult,resendReceiptResult,20000,resendReceiptResult);
			}
		</cfif>
		
		$(function() {
			var tooltipConfig = { container: 'body' };
			$('.tooltip-icon').tooltip(tooltipConfig);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.swRegReceiptHead)#">