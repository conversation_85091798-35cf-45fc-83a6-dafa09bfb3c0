<cfoutput>
<table style="border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
<tr>
	<td style="font:bold 15px Verdana,Helvetica,Arial,sans-serif;padding:6px;color:##333;" colspan="2">Registration(s)</td>
	<td style="font:normal 14px Verdana,Helvetica,Arial,sans-serif;padding:6px;color:##333;float:right;text-align:right;">
		<cfif local.strRegReceipt.keyExists("qryAdditionalFees")>
			<table style="border-collapse:collapse;width:220px;" cellspacing="0" cellpadding="2" border="0">
				<tr>
					<td>Subtotal:</td>
					<td>#DollarFormat(local.totalAmountOnReceipt.totalAmount)#</td>
				</tr>
				<tr>
					<td>#local.strRegReceipt.qryAdditionalFees.additionalFeesLabel#:</td>
					<td>#DollarFormat(local.strRegReceipt.qryAdditionalFees.additionalFees)#</td>
				</tr>
				<tr>
					<td><b>Total:</b></td>
					<td><b>#DollarFormat(local.strRegReceipt.qryPaymentTransaction.amount)#</b></td>
				</tr>
			</table>
		<cfelse>
			<b>Total: #dollarFormat(local.strRegReceipt.qryPaymentTransaction.amount)#</b>
		</cfif>
	</td>
</tr>
</table>
<br/>
<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
</cfoutput>

<cfset local.recCountInd = 0>
<cfoutput query="local.qryItemsForReceiptSorted" group="memberID">
	<tr>
		<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;" colspan="3">
			#local.qryItemsForReceiptSorted.memberName#
		</td>
	</tr>
	<cfoutput>
		<cfset local.showRegRate = 1>
		<cfset local.recCountInd++>
		<tr>
			<td style="padding:6px" width="10" valign="top">
				#local.recCountInd#.
			</td>
			<td style="padding:6px">
				<b>#encodeForHTML(local.qryItemsForReceiptSorted.eventName)#</b><br/>
				<cfif len(local.qryItemsForReceiptSorted.eventSubTitle)><span style="color:##999"><b>#encodeForHTML(local.qryItemsForReceiptSorted.eventSubTitle)#</b></span><br/></cfif>
				#local.strRegEvent[local.qryItemsForReceiptSorted.eventid].eventtime#<br/>
				#local.qryItemsForReceiptSorted.ratename#<br/>
				<cfif isDefined("local.strRegReceipt.qryOversold")>
					<cfquery name="local.qryThisRegOverSoldItems" dbtype="query">
						SELECT message
						FROM local.strRegReceipt.qryOversold
						WHERE eventID = #local.qryItemsForReceiptSorted.eventid#
						AND memberID = #local.qryItemsForReceiptSorted.memberID#
						ORDER BY rowID
					</cfquery>
					<cfif local.qryThisRegOverSoldItems.recordcount>
						<table width="100%" style="margin-top:10px; padding-top:4px;">
						<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;border:1px solid;margin:4px 0px;padding:15px 10px 15px 10px;color:##9F6000;background-color:##FEEFB3;">
							<b>Note:</b> We changed this registration due to availability:
							<ul>
							<cfloop query="local.qryThisRegOverSoldItems">
								<li>#local.qryThisRegOverSoldItems.message#</li>
							</cfloop>
							</ul>
						</td>
						</tr>
						</table>
					</cfif>
				</cfif>
				<cfif isDefined("local.strRegReceipt.qryRegistrationCap")>
					<cfquery name="local.qryThisRegInRegCaps" dbtype="query">
						SELECT memberID
						FROM local.strRegReceipt.qryRegistrationCap
						WHERE eventID = #local.qryItemsForReceiptSorted.eventid#
						AND memberID = #local.qryItemsForReceiptSorted.memberID#
					</cfquery>
					<cfif local.qryThisRegInRegCaps.recordcount>
						<table width="100%" style="padding-top:4px;">
						<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;border:1px solid;margin:4px 0px;padding:15px 10px 15px 10px;color:##9F6000;background-color:##FEEFB3;">
							<b>Note:</b> This event sold out before your checkout was completed.
						</td>
						</tr>
						</table>
						<cfset local.showRegRate = 0>
					</cfif>
				</cfif>
			</td>
			<td align="right" style="padding: 6px" valign="top">
				<cfif local.showRegRate>
					<nobr><a title="Download event to your calendar" style="text-decoration:none;" target="_blank" href="#local.strRegEvent[local.qryItemsForReceiptSorted.eventid].icalURL#"><i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp;Outlook</a></nobr>
					&nbsp;
					<nobr><a title="Add event to your Google calendar" style="text-decoration:none;" target="_blank" href="#local.strRegEvent[local.qryItemsForReceiptSorted.eventid].gcalURL#"><i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp;Google</a></nobr>
					&nbsp;
					<nobr><a title="Download event to your calendar" style="text-decoration:none;" target="_blank" href="#local.strRegEvent[local.qryItemsForReceiptSorted.eventid].icalURL#"><i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp;iCal</a></nobr>
					<br/>
					<b>#dollarFormat(local.qryItemsForReceiptSorted.discountAppliedTotal)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></b>
				<cfelse>
					N/A
				</cfif>
			</td>
		</tr>
	</cfoutput>
</cfoutput>
<cfoutput>
</table>
<br/><br/>
<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid ##999;border-collapse:collapse;">
<tr bgcolor="##DEDEDE">
	<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;"><b>Payment</b></td>
</tr>
<tr valign="top">
	<td style="padding:6px;border-bottom:1px solid ##ccc;">
		<cfif local.totalAmountOnReceipt.totalAmount gt 0 and local.strRegReceipt.qryPaymentTransaction.recordcount gt 0>
			#dollarFormat(local.strRegReceipt.qryPaymentTransaction.amount)# #local.strRegReceipt.qryPaymentTransaction.detail#<br/>
			Payment Date: #dateformat(local.strRegReceipt.qryPaymentTransaction.transactionDate,"m/d/yyyy")# #timeformat(local.strRegReceipt.qryPaymentTransaction.transactionDate,"h:mm tt")#
		<cfelseif local.strRegReceipt.qryPaymentTransaction.recordcount is 0 and local.strRegReceipt.qryPaymentGateway.gatewayID is 11>
			No payment was made.<br/><br/>
			<b>Payment instructions:</b><br/>
			<cfif len(local.strRegReceipt.qryPaymentGateway.paymentInstructions)>
				#local.strRegReceipt.qryPaymentGateway.paymentInstructions#
			<cfelse>
				No instructions have been provided. Contact the association for payment instructions.
			</cfif>
		<cfelseif local.totalAmountOnReceipt.totalAmount gt 0 and local.strRegReceipt.qryPaymentTransaction.recordcount is 0>
			No payment was made.
		<cfelse>
			No payment was due.
		</cfif>
		<br/><br/>
		#local.strReceipt.PurchaserName#<br/>
		<cfif len(local.strReceipt.PurchaserCompany)>#local.strReceipt.PurchaserCompany#<br/></cfif>
		#local.strReceipt.PurchaserAddress#
	</td>
</tr>
</table>
</cfoutput>