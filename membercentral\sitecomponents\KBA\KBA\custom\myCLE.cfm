<cfscript>
variables.applicationReservedURLParams 	= "";
local.customPage.baseURL = "/?#getBaseQueryString(false)#";

arguments.event.paramValue('ca','showList');
arguments.event.paramValue('periodStartDate','1/1/#year(now())#');
arguments.event.paramValue('periodEndDate',dateFormat(now(),'m/d/yyyy'));
arguments.event.paramValue('membernumber','');

local.periodStartDate = arguments.event.getValue('periodStartDate');
local.periodEndDate = arguments.event.getValue('periodEndDate');
local.membernumber = arguments.event.getValue('membernumber');

// CUSTOM FIELD: ------------------------------------------------------------------------------------------------------
	local.arrCustomFields = [];
	local.tmpField = { name="myCLEBottomText", type="CONTENTOBJ", desc="myCLEText", value="<p><strong>Filing for CLE Credit.</strong> The KnoxBar reports credit for paid attendees with completed affidavits to the Tennessee CLE Commission ONLY no later than 28 days after the CLE.</p>" };
	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);	
</cfscript>
<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberDetails">
		select firstName, middleName, lastName, suffix 
		from dbo.ams_members
		where memberNumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR">
		AND orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
	</cfquery>
</cfif>
<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEventsAttended" result="local.qryEventsAttendedResult">
	SET NOCOUNT ON;

	declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
	select e.eventid, r.registrantID, et.startTime as eventStart, cl.contentTitle as eventName, e.reportCode, 
		regFee.totalRegFee-regFee.regFeePaid as amountDue, co.approvalNum,
		case when dateDiff(d,et.endTime,getdate()) >= 28 then 1 else 0 end as Past28Days,
		cast(isnull((
			select req.creditValueAwarded, isnull(ast.ovTypeName,at.typeName) as creditType
			from dbo.crd_requests as req
			inner join dbo.crd_offeringTypes as offt on offt.offeringTypeID = req.offeringTypeID
			inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = offt.ASTID
			inner join dbo.crd_authorityTypes as at on at.typeID = ast.typeID
			where req.registrantID = r.registrantID
			and req.creditAwarded = 1
			for XML AUTO, ROOT('credits')
		),'<credits/>') as xml) as awardedCreditsXML,
		cast(isnull((
			select distinct i.invoiceID, i.invoiceCode, i.fullInvoiceNumber as invoiceNumber
			from dbo.fn_ev_registrantTransactions(r.registrantID) as rt
			inner join dbo.tr_invoiceTransactions as it on it.orgID = rt.ownedByOrgID and it.transactionID = rt.transactionID
			inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			where ins.status in ('Closed','Delinquent')
			and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
			and dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) = 1
			for XML AUTO, ROOT('invoices')
		),'<invoices/>') as xml) as invoicesXML
	from dbo.ev_registrants as r
	inner join dbo.ams_members as m1 on m1.memberID = r.memberID
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
			and m.orgID = @orgID
			AND m.membernumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR"> 	
	<cfelse>
		inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
			and m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
	</cfif>
	inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID and r.recordedOnSiteID = evr.siteID
	inner join dbo.ev_events as e on e.eventid = evr.eventid
	inner join dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
	inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
	inner join dbo.crd_offerings as co on co.eventID = e.eventID
		and et.endTime between co.offeredStartDate and co.offeredEndDate
		and co.offeredStartDate >= <cfqueryparam value="#local.periodStartDate#" cfsqltype="cf_sql_date">
		and co.offeredEndDate <= <cfqueryparam value="#local.periodEndDate# 23:59:59.997" cfsqltype="cf_sql_timestamp">
	OUTER APPLY dbo.fn_ev_totalRegFeeAndPaid(@orgID, r.registrantID) as regFee
	where r.status = 'A'
	and r.attended = 1
	and co.statusID = 4
	order by et.startTime desc, e.eventid
</cfquery>

<cfset local.qryEventsAttendedTotalsTemp = QueryNew("creditYear,creditAmount,creditType","integer,double,varchar")>
<cfloop query="local.qryEventsAttended">
	<cfset local.arrCredits = xmlSearch(local.qryEventsAttended.awardedCreditsXML,"/credits/req")>
	<cfloop array="#local.arrCredits#" index="local.thisAward">
		<cfif QueryAddRow(local.qryEventsAttendedTotalsTemp)>
			<cfset querySetCell(local.qryEventsAttendedTotalsTemp,"creditYear",year(local.qryEventsAttended.eventStart))>
			<cfset querySetCell(local.qryEventsAttendedTotalsTemp,"creditAmount",local.thisAward.xmlAttributes.creditValueAwarded)>
			<cfset querySetCell(local.qryEventsAttendedTotalsTemp,"creditType",local.thisAward.xmlAttributes.creditType)>
		</cfif>
	</cfloop>
</cfloop>

<cfquery name="local.qryEventsAttendedTotals" dbtype="query">
	select creditYear, creditType, sum(creditAmount) as creditAmount
	from [local].qryEventsAttendedTotalsTemp
	group by creditYear, creditType
	order by creditYear DESC, creditType
</cfquery>

<!--- seminarweb history --->
<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails('KBA').qryAssociation>
<cfset local.showSW = false>
<cfset local.memberIDToUse = session.cfcUser.memberData.memberID>

<cfif (application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)) AND len(trim(local.membernumber))>
	<cfset local.memberIDToUse = application.objMember.getMemberIDByMemberNumber(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
</cfif>

<cfif val(local.memberIDToUse)>
	<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
		<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">
		<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="KBA">
		<cfprocresult name="local.qrySWL" resultset="1">
		<cfprocresult name="local.qrySWOD" resultset="2">
		<cfprocresult name="local.qryCertPrograms" resultset="3">
	</cfstoredproc>

	<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWOD" result="local.qrySWODResult">
		SET NOCOUNT ON;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">;
		
		SELECT 
			e.enrollmentID, s.seminarID, s.seminarName, e.dateEnrolled, e.dateCompleted, e.passed, 
			s.isPublished, s.offerCertificate,
			CASE
				WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN 1
				ELSE (SELECT isnull((select TOP 1 CASE
													WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
													ELSE 0
						END as preReqFulfilled
			FROM dbo.tblSeminars as s2
			INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
			LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.MCMemberID = @memberID and e2.isActive = 1
			where pr.seminarID = s.seminarid
			),0))
			END as preReqFulfilled,
			sac.courseApproval as CourseID, sac.wddxCreditsAvailable
		FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID and s.isDeleted = 0
		INNER JOIN dbo.tblEnrollmentsSWOD AS esod ON e.enrollmentID = esod.enrollmentID
		INNER JOIN dbo.swod_SeminarsInMyCatalogMy('kba') as simc on simc.seminarID = s.seminarID
		left outer join dbo.tblEnrollmentsAndCredit AS eac 
		INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
		on eac.enrollmentID = e.enrollmentID
		WHERE e.MCMemberID = @memberID 
		AND e.isActive = 1
		ORDER BY s.seminarName;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	</cfquery>	
	<cfset local.showSW = true>		
</cfif>

<cfsavecontent variable="local.cleCSS">
	<cfoutput>
	<style type="text/css">
	##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
	</style>
	<cfif local.showSW>
		<script language="JavaScript">
		function viewCert(eId) {
			var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
			window.open(certURL,'ViewCertificate','width=990,height=500');
		}
		</script>
	</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.cleCSS#">

<cfsavecontent variable="local.dataHead">
	<cfoutput>
	<script language="javascript">
		$(function(){
			mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
		});

		function _FB_validateForm(){
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				if ($('##membernumber').val() == '') { 
					alert('Must enter MemberNumber before you can filter report.');
					return false;
				}
			</cfif>
			return true;
		}
	</script>
	<style type="text/css">
		##periodStartDate, ##periodEndDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
		.creditTable td, .creditTable th, ##swlhistory td, ##swlhistory th, ##swodhistory td, ##swodhistory th { border:1px solid ##707070; border-collapse:collapse; padding:4px; }
		.balTable td { border:0px; padding:2px; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

<cfform method="POST" action="#local.customPage.baseURL#" name="frmCLE" onsubmit="return _FB_validateForm();">
	<cfoutput>
		<span style="float:right;">
			<button class="btn" type="button" onClick="window.print();"><i class="icon-print"></i> Print</button>
		</span>
		
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfif local.memberDetails.recordcount>
				<h4>KnoxBar CLE History for #local.memberDetails.firstName# #local.memberDetails.middleName# #local.memberDetails.lastName# #local.memberDetails.suffix#</h4>
			</cfif>
		<cfelse>		
			<h4>KnoxBar CLE History for #session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.middlename# #session.cfcuser.memberdata.lastname# #session.cfcuser.memberdata.suffix# </h4></br>
		</cfif>

		<table cellpadding="4" cellspacing="0">
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<tr valign="top">
					<td colspan="4" >
						<b>MemberNumber:</b> &nbsp;
						<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber.">
					</td>
				</tr>
			</cfif>
			<tr valign="top">
				<td>
					<b>Select your dates:</b> &nbsp;
					<cfinput type="text" name="periodStartDate" id="periodStartDate" value="#local.periodStartDate#" size="14"> 
					&nbsp;to&nbsp;
					<cfinput type="text" name="periodEndDate" id="periodEndDate" value="#local.periodEndDate#" size="14">
				</td>
				<td>
					<button class="btn" type="submit">Filter Report</button>
				</td>
			</tr>
		</table>
	</cfoutput>
</cfform>

<cfoutput><h5>Live CLE</h5></cfoutput>
<cfif local.qryEventsAttended.recordcount>
	<cfoutput>
	<table class="creditTable" width="98%">
		<tr>
			<th width="5%">Date</th>
			<th width="43%">Title</th>
			<th width="20%">Credit Reported</th>
			<th width="10%">Course ID</th>
			<th width="22%">Notes</th>
		</tr>
		</cfoutput>
		<cfoutput query="local.qryEventsAttended" group="eventID">
			<cfset local.arrCredits = xmlSearch(local.qryEventsAttended.awardedCreditsXML,"/credits/req")>
			<tr>
				<td>#dateformat(local.qryEventsAttended.eventStart,"m/d/yyyy")#</td>	
				<td>#HTMLEditFormat(local.qryEventsAttended.eventName)#</td>
				<cfif arrayLen(local.arrCredits)>
					<td>
						<cfloop array="#local.arrCredits#" index="local.thisAward">
							#local.thisAward.xmlAttributes.creditValueAwarded# #local.thisAward.xmlAttributes.creditType#<br/>
						</cfloop>
					</td>
				<cfelseif local.qryEventsAttended.amountDue is 0>
					<td><b style="color:red;">No Credit Reported</b></td>
				<cfelse>
					<cfset local.arrInvoices = xmlSearch(local.qryEventsAttended.invoicesXML,"/invoices/i")>
					<td><b style="color:red;">No Credit Reported</b></td>
				</cfif>
				<td>#local.qryEventsAttended.approvalNum#</td>
				<td>&nbsp;</td>
			</tr>
		</cfoutput>
		<cfoutput>
	</table>
	</cfoutput>
<cfelse>
	<cfoutput>Nothing to report during the selected period.</cfoutput>
</cfif>

<cfoutput><br/><br/></cfoutput>

<cfif local.showSW>
	<cfif local.qrySWL.recordcount>
		<cfoutput>
			<div><b>#local.qrySWP.brandSWLTab#</b></div><br/>
			<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swlhistory">
				<tr class="tsAppBodyText">
					<th width="5%">Date</th>
					<th width="43%">Title</th>
					<th width="20%">Credit Reported</th>
					<th width="10%">Course ID</th>
					<th width="22%">Status</th>
				</tr>
				<cfloop query="local.qrySWL">
					<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWL.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
					
					<cfstoredproc procedure="sw_getCreditsforSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySWOD.seminarId#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('mc_siteInfo.siteCode')#">
						<cfprocresult name="local.qryCreditsforSeminar">
					</cfstoredproc>
				
					<cfset local.arrCredits = xmlSearch(local.qryCreditsforSeminar.wddxCreditsAvailable,"/wddxPacket/data/array/struct/var/string")>
					
					<cfset local.creditReportedText = ''>
					<cfset local.len = ArrayLen(local.arrCredits)>
					<cfset local.i = 1>
					
					<cfscript> 
						while (local.i LTE local.len) {
							if (len(trim(local.arrCredits[local.i].XmlText)) gt 1) {
								switch (replace(trim(local.arrCredits[local.i].XmlText),' ','','all')) {
									case "General":
										local.creditReportedText = local.creditReportedText & ' CLE General';
										break;
									case "Self-Study":
										local.creditReportedText = local.creditReportedText & ' Self-Study';
										break;
									case "Ethics/Professionalism":
										local.creditReportedText = local.creditReportedText & ' CLE Ethics';
										break;
									case "Dual":
										local.creditReportedText = local.creditReportedText & ' CLE Dual';
										break;
									case "CME":
										local.creditReportedText = local.creditReportedText & ' CME General';
										break;
									case "EthicsCME":
										local.creditReportedText = local.creditReportedText & ' CME Ethics';
										break;
									default:
										local.creditReportedText = local.creditReportedText & ' ' & local.arrCredits[local.i].XmlText;
										break;
								}
							}
							else {
								local.creditReportedText = local.creditReportedText & local.arrCredits[local.i].XmlText;
							}
							
							if ((local.i MOD 2) eq 0) {
								local.creditReportedText = local.creditReportedText & '<br>';
							}
							local.i = local.i+1;
						}
					</cfscript>
					
					<tr valign="top">
						<td class="tsAppBodyText">#DateFormat(local.qrySWL.dateCompleted,'m/d/yyyy')#</td>
						<td class="tsAppBodyText">#encodeForHTML(local.qrySWL.seminarName)#</td>
						<td class="tsAppBodyText"><cfif creditReportedText eq ''>0<cfelse>#creditReportedText#</cfif></td>
						<td class="tsAppBodyText">#local.qrySWL.enrollmentId#</td>
						<td class="tsAppBodyText" width="120">
							<cfif len(local.qrySWL.dateCompleted) and local.qrySWL.passed is 1 and local.qrySWL.offerCertificate>
								<a href="javascript:viewCert('#local.eID#')">View certificate(s)</a>
							<cfelseif len(local.qrySWL.dateCompleted) and local.qrySWL.passed is 1 and not local.qrySWL.offerCertificate>
								Completed
							<cfelseif len(local.qrySWL.dateCompleted) and local.qrySWL.passed is 0>
								Failed
							<cfelseif now() lt local.qrySWL.dateStart>
								Not yet begun
							<cfelse>
								Did Not Attend
							</cfif>
						</td>
					</tr>
				</cfloop>
			</table>
			<br/><br/>
		</cfoutput>
	</cfif>
	
	<!--- SWOD --->
	<cfoutput>
		<cfif local.qrySWOD.recordcount>
			<div><b>#local.qrySWP.brandSWODTab#</b></div><br/>
			<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swodhistory">
			<tr class="tsAppBodyText">
				<th width="5%">Date</th>
				<th width="43%">Title</th>
				<th width="20%">Credit Reported</th>
				<th width="10%">Course ID</th>
				<th width="22%">Status</th>
			</tr>
			<cfloop query="local.qrySWOD">
				<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWOD.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			
				<cfset local.arrCredits = []>
				<cfif len(trim(local.qrySWOD.wddxCreditsAvailable))>
					<cfset local.arrCredits = xmlSearch(local.qrySWOD.wddxCreditsAvailable,"/wddxPacket/data/array/struct/var/string")>
				</cfif>				
				
				<cfset local.creditReportedText = ''>
				<cfset local.len = ArrayLen(local.arrCredits)>
				<cfset local.i = 1>
				
				<cfscript> 
					while (local.i LTE local.len) {
						if (len(trim(local.arrCredits[local.i].XmlText)) gt 1) {
							switch (replace(trim(local.arrCredits[local.i].XmlText),' ','','all')) {
								case "General":
									local.creditReportedText = local.creditReportedText & ' CLE General';
									break;
								case "Self-Study":
									local.creditReportedText = local.creditReportedText & ' Self-Study';
									break;
								case "Ethics/Professionalism":
									local.creditReportedText = local.creditReportedText & ' CLE Ethics';
									break;
								case "Dual":
									local.creditReportedText = local.creditReportedText & ' CLE Dual';
									break;
								case "CME":
									local.creditReportedText = local.creditReportedText & ' CME General';
									break;
								case "EthicsCME":
									local.creditReportedText = local.creditReportedText & ' CME Ethics';
									break;
								default:
									local.creditReportedText = local.creditReportedText & ' ' & local.arrCredits[local.i].XmlText;
									break;
							}
						}
						else {
							local.creditReportedText = local.creditReportedText & local.arrCredits[local.i].XmlText;
						}
						
						if ((local.i MOD 2) eq 0) {
							local.creditReportedText = local.creditReportedText & '<br>';
						}
						local.i = local.i+1;
					}
				</cfscript> 
	
				<tr valign="top">
					<td class="tsAppBodyText">#DateFormat(local.qrySWOD.dateCompleted,'m/d/yyyy')#</td>
					<td class="tsAppBodyText">#encodeForHTML(local.qrySWOD.seminarName)#</td>
					<td class="tsAppBodyText"><cfif local.creditReportedText eq '' or len(local.qrySWOD.dateCompleted) is 0>0<cfelse>#local.creditReportedText#</cfif></td>
					<td class="tsAppBodyText">#local.qrySWOD.CourseID#</td>
					<td class="tsAppBodyText" width="140">
						<cfif len(local.qrySWOD.dateCompleted) is 0>
							<cfif local.qrySWOD.isPublished and local.qrySWOD.preReqFulfilled>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Begin</a>
							<cfelseif local.qrySWOD.isPublished>
								Awaiting Prereqs
							<cfelse>
								Not available
							</cfif>
						<cfelse>
							<cfif local.qrySWOD.isPublished>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Review</a> |
							</cfif>
							<cfif local.qrySWOD.passed and local.qrySWOD.offerCertificate>
								<a href="javascript:viewCert('#local.eID#');">Certificate</a>
							<cfelseif local.qrySWOD.passed and not local.qrySWOD.offerCertificate>
								Completed
							<cfelseif not local.qrySWOD.passed>
								Failed
							</cfif>
						</cfif>
					</td>
				</tr>
			</cfloop>
			</table>
			<br/><br/>
		</cfif>	
	</cfoutput>
<cfelse>
	<cfoutput>Nothing to report during the selected period.</cfoutput>
</cfif>	
<cfoutput>
	<br><br>
	#local.strPageFields.myCLEBottomText#
</cfoutput>