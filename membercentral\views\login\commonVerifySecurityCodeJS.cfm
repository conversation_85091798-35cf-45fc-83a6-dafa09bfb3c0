<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		let mfasms_attemptdelaytimer, mfasms_reattemptdelayinsecs = 0;

		function validateLoginCode() {
			if ($('##securityCode').val().trim().length == 0) {
				return false;
			}

			$('##frmVerifyLogin button').prop('disabled',true).html('Please Wait...');
			return true;
		}
		<cfif local.arrConfiguredMethods.containsNoCase('MFASMS')>
			function initMFASMSCounter(channel,timeLeft) {
				mfasms_reattemptdelayinsecs = timeLeft;
				let strChannel = getMFASMSChannelInfo(channel);
				$('##resendMFAOTPControl').html('<i class="icon-refresh"></i> '+strChannel.pleaseWaitText+' in <span id="resendMFASMSDelayTime">'+mfasms_reattemptdelayinsecs+'</span> seconds');

				let MFASMSResendOption = function() {
					if (mfasms_reattemptdelayinsecs == -1) {
						clearTimeout(mfasms_attemptdelaytimer);
						$('##resendMFAOTPControl').html('<a href="##" onclick="chooseMFAMethod(\'mfasms\',\''+channel+'\');return false;"><i class="icon-refresh"></i> '+strChannel.resendNowText+'</a>');
						$('.mfasms_channels').removeClass('login-disabled');
					} else {
						$('##resendMFASMSDelayTime').html(mfasms_reattemptdelayinsecs);
						mfasms_reattemptdelayinsecs--;
					}
				};

				clearTimeout(mfasms_attemptdelaytimer);
				mfasms_attemptdelaytimer = setInterval(MFASMSResendOption, 1000);
			}
			function getMFASMSChannelInfo(channel) {
				let strChannel = {};
				switch (channel.toLowerCase()) {
					case 'sms':
						strChannel = { 
							pleaseWaitText:'Haven\'t received the text? You can resend it', 
							resendNowText:'Click to resend the text message now', 
							attempting:'Resending code'
						};
						break;
					case 'call':
						strChannel = { 
							pleaseWaitText:'Haven\'t received the call? You can request another one', 
							resendNowText:'Click to request another phone call now', 
							attempting:'Calling' 
						};
						break;
					case 'whatsapp':
						strChannel = { 
							pleaseWaitText:'Haven\'t received the WhatsApp Message? You can resend it', 
							resendNowText:'Click to resend the WhatsApp message now', 
							attempting:'Resending code'
						};
						break;
				}
				return strChannel;
			}
		</cfif>
		<cfif local.hasAlternateMFAMethods>
			function chooseMFAMethod(mtd,ch) {
				$('##MFAMethodContainer').html('<i class="icon-spinner icon-spin"></i> Please wait...');
				$('##resendMFAOTPControl').html('');
				$('.mfasms_channels').addClass('login-disabled');

				let strData = { mfamtd:mtd };
				if (mtd.toLowerCase() == 'mfasms') strData.mfasms_channel = ch;

				$.ajax({
					url: '#local.chooseMFAMethodLink#',
					type: 'POST',
					data: strData,
					dataType: 'json',
					success: function(response) { 
						if (response.success) {
							$('##MFAMethodMessage').html(response.msg);
							loadMFAMethods(response);
							$('##securityCode').focus();
						} else {
							self.location.href = '/?pg=login&logact=verifySecurityCode';
						}
					}, fail: function(response) { 
						self.location.href = '/?pg=login&logact=verifySecurityCode';
					}
				});
			}
			function loadMFAMethods(r) {
				let MFAMethodTemplate = Handlebars.compile($('##MFAMethodTemplate').html());
				$('##MFAMethodContainer').html(MFAMethodTemplate(r));
				if (r.mfamethod == 'mfasms') {
					initMFASMSCounter(r.mfasms_channel,r.reattemptdelay);
				} else {
					if ($('.mfasms_channels').length) {
						mfasms_reattemptdelayinsecs = r.reattemptdelay;
						clearTimeout(mfasms_attemptdelaytimer);
						
						if (mfasms_reattemptdelayinsecs) {
							let EnableMFASMSResendOption = function() {
								if (mfasms_reattemptdelayinsecs == -1) {
									clearTimeout(mfasms_attemptdelaytimer);
									$('.mfasms_channels').removeClass('login-disabled');
								} else {
									mfasms_reattemptdelayinsecs--;
								}
							};

							mfasms_attemptdelaytimer = setInterval(EnableMFASMSResendOption, 1000);
						} else {
							$('.mfasms_channels').removeClass('login-disabled');
						}
					}
				}
			}
		</cfif>

		$(function() {
			$('##securityCode').focus();
			<cfif local.strVerifyCode.verificationMethod EQ 'MFASMS'>
				mfasms_reattemptdelayinsecs = #val(local.strVerifyCode.secondsBeforeAllowedReattempt)#;
				initMFASMSCounter('#local.strVerifyCode.channel#',mfasms_reattemptdelayinsecs);
			</cfif>
			<cfif local.hasAlternateMFAMethods>
				let strMFAMtd = { mfamethod:'#lCase(local.strVerifyCode.verificationMethod)#', mfasms_channel:'' };
				<cfif local.strVerifyCode.verificationMethod EQ 'MFASMS'>
					strMFAMtd.mfasms_channel = '#lCase(local.strVerifyCode.channel)#';
					strMFAMtd.reattemptdelay = mfasms_reattemptdelayinsecs;
					strMFAMtd.codeattempts = #val(local.strVerifyCode.codeattempts)#;
				</cfif>
				loadMFAMethods(strMFAMtd);
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">