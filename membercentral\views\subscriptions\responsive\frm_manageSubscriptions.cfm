<!--- no caching --->
<cfheader name="Cache-Control" value="no-cache, no-store, must-revalidate">
<cfheader name="Pragma" value="no-cache">
<cfheader name="Expires" value="0">

<cfinclude template="../commonManageSubsStyles.cfm">
<cfinclude template="../commonManageSubsJS.cfm">

<cfoutput>
<div class="manageSubsApp">
	<div class="mcsubs-mb-3">#local.topSubscriptionContent#</div>
	<form name="frmSubs" id="frmSubs" action="#local.formlink#" method="post" autocomplete="off" onsubmit="return validateSubs();">
		<input type="hidden" name="mid" value="#local.memberID#">
		<input type="hidden" name="rootSubscriberID" value="#local.rootSubscriberID#">
		<input type="hidden" name="subV" value="#local.encString#">
		
		<div id="manageSubsContainer">
			<!--- Member Card --->
			<div class="mcsubs-card mcsubs-p-3 mcsubs-mb-2">
				<div class="mcsubs-d-flex mcsubs-col">
					<cfif local.showMemberPhoto>
						<div class="mcsubs-mr-2">
							<cfif local.strMember.hasPhoto>
								<img src="/memberphotosth/#LCASE(local.strMember.memberphoto)#" class="mcsubs-img-thumbnail" style="max-width:80px;">
							<cfelse>
								<i class="fa fa-user-circle-o mcsubs-font-size-xxl mcsubs-opacity-7" aria-hidden="true"></i>
							</cfif>
						</div>
					</cfif>
					<div>
						<h4 class="mcsubs-mb-0 mcsubs-mt-1 mcsubs-text-bold mcsubs-pb-0">#local.strMember.mc_combinedName#</h4>
						<cfif len(local.strMember.company)><div class="mcsubs-text-dim"><small>#local.strMember.company#</small></div></cfif>
						<div class="mcsubs-mt-1 mcsubs-p-1">
							<cfif len(local.strMember.mc_combinedAddresses)>#local.strMember.mc_combinedAddresses#</cfif>
							<cfif len(local.strMember.mc_extraInfo)>#local.strMember.mc_extraInfo#</cfif>
							<cfif len(local.strMember.mc_recordType)><div>#local.strMember.mc_recordType#</div></cfif>
							<cfif len(local.strMember.mc_memberType)><div>#local.strMember.mc_memberType#</div></cfif>
							<cfif len(local.strMember.mc_lastlogin)><div>#local.strMember.mc_lastlogin#</div></cfif>
						</div>
					</div>
				</div>
				<div class="mcsubs-d-flex mcsubs-flex-sm-column-reverse mcsubs-mt-3">
					<div class="mcsubs-col">
						<div id="subRegCartTotal" class="mcsubs-text-bold mcsubs-mb-2 mcsubs-text-sm-center">
							Total: <span class="grandTotal"></span>
						</div>
						<div id="subRegCartTotalSummary" class="mcsubs-card mcsubs-d-none" style="box-shadow:none;border:none;background-color:##f7f7f7;margin-bottom:5px;">
							<div class="mcsubs-card-body">
								<div class="mcsubs-d-flex mcsubs-flex-sm-column">
									<div class="mcsubs-col-auto">
										<div class="mcsubs-d-flex mcsubs-font-weight-bold mcsubs-font-size-xl">
											<div class="mcsubs-col">Total:</div>
											<div class="mcsubs-ml-auto grandTotal"></div>
										</div>
										<div class="mcsubs-text-right mcsubs-font-size-sm">
											<div class="mcsubs-strike actualSubTotal"></div>
											<div><span class="totalSubDiscount"></span> discount applied</div>
										</div>
									</div>
									<div class="mcsubs-col-auto mcsubs-ml-auto">
										<div class="alert alert-success mcsubs-font-size-sm mcsubs-mb-0 mcsubs-text-center mcsubs-p-1 subCouponRedeemDetail" style="max-width:200px;"></div>
										<div class="mcsubs-mt-2 mcsubs-text-right">
											<button type="button" name="btnRemoveCoupon" class="btn btn-small btn-warning btnRemoveCoupon badge" onclick="removeAppliedCoupon();">Remove Promo Code</button>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div>
							<button type="submit" class="btn btn-success btnCheckOut mcsubs-btn155 mcsubs-w-sm-100" disabled>
								<span>Checkout <i class="icon-circle-arrow-right"></i></span>
							</button>
						</div>
					</div>
					<div id="subCouponContainer" class="mcsubs-col-auto mcsubs-ml-auto mcsubs-text-right mcsubs-align-self-end mcsubs-d-none mcsubs-mb-sm-3 mcsubs-w-sm-100">
						<div id="couponCodeResponse" class="alert alert-error mcsubs-p-1 mcsubs-mb-1 mcsubs-font-size-sm mcsubs-text-center" style="display:none;"></div>
						<div class="mcsubs-input-append mcsubs-w-sm-100">
							<input type="text" name="couponCode" id="couponCode" class="mcsubs-formcontrol mcsubs-flex-sm-grow-1" value="" size="18" value="" placeholder="Promo Code" maxlength="15">
							<button type="button" name="btnApplyCouponCode" id="btnApplyCouponCode" class="mcsubs-add-on mcsubs-font-size-md" onclick="validateCouponCode();">Apply</button>
						</div>
					</div>
					<div id="manageSubsHeaderAlert" class="mcsubs-col alert alert-error mcsubs-mb-0 mcsubs-mt-sm-3 mcsubs-align-self-center mcsubs-d-none"></div>
				</div>
			</div>

			<!--- Root Sub --->
			<div id="rootSubWrapper" class="mcsubs-card mcsubs-mt-2 mcsubs-mb-2 mainSubCard" 
				data-displaypriceelement="rootSubCardTotalPrice"
				data-linkedsummarycontainer="rootSubSummary"
				data-linkedformcontainer="rootSubForm"
				data-linkedtitlecontainer="rootSubTitleContainer"
				data-linkedtitleerrorcontainer="rootSubTitleAlert"
				data-errfields="">
				<div class="mcsubs-card-body">
					<div class="mcsubs-pb-1 mcsubs-mb-2 mcsubs-border-bottom mcsubs-border-lightgray rootSubTitleContainer">
						<div class="mcsubs-font-weight-bold">
							#local.strRootSub.subscription[1].subscriptionName# <span class="mcsubs-text-danger">*</span>
							<span class="rootSubCardTotalPrice">
								- #renderPrice(amount=val(local.strRootSub.qryRootSubscriber.lastPrice), freeRateDisplay=local.freeRateDisplay)#
							</span>
						</div>
						<div class="mcsubs-font-size-sm mcsubs-text-nowrap">
							Term: #DateFormat(local.strRootSub.qryRootSubscriber.subStartDate,'m/d/yyyy')# - #DateFormat(local.strRootSub.qryRootSubscriber.subEndDate,'m/d/yyyy')#
						</div>
						<div class="rootSubTitleAlert mcsubs-font-weight-bold mcsubs-font-size-sm"></div>
					</div>
					<div id="rootSubForm" class="mcsubs-d-none">
						<div class="subsContainer">
							<cfif len(local.parentSubscriptionContent)>
								<div class="parentSubFrontEndContent mcsubs-mb-3">#local.parentSubscriptionContent#</div>
							</cfif>
							#renderSubscriptionsForm(arrSubs=local.strRootSub.subscription, strAddOn={}, parentSubscriptionID=0, strParentFreq=local.strRootSub.strParentFreq, 
								recursionLevel=0, freeRateDisplay=local.freeRateDisplay, strEditSubs=local.strRootSub.strEditSub, viewDirectory=local.viewDirectory)#
						</div>
						<div class="mcsubs-mt-3 mcsubs-border-lightgray mcsubs-border-top mcsubs-pt-2">
							<a href="##" class="btn btn-primary mcsubs-btn125 mcsubs-w-sm-100 subCardConfirmBtn" onclick="confirmRootSubChanges();return false;">Confirm</a>
						</div>
					</div>
					<div id="rootSubSummary" class="subCardSummary">
						<cfif len(local.parentSubscriptionContent)><div class="mcsubs-mb-3">#local.parentSubscriptionContent#</div></cfif>
						<div id="rootSubRateInfo">
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						</div>
						<div class="mcsubs-mt-3">
							<button type="button" id="rootSubEditBtn" data-editmode="editrootsub" class="btn btn-default mcsubs-btn125 mcsubs-w-sm-100 subCardEditBtn mcsubs-d-none"></button>
						</div>
					</div>
				</div>
			</div>

			<!--- Sub AddOns --->
			<div id="sub#local.strRootSub.qryRootSubscriber.subscriptionID#_addons"></div>

			<!--- Billing Info --->
			<cfif local.stateIDforTax EQ 0 OR NOT len(local.zipForTax)>
				<div class="mcsubs-card mcsubs-mt-2 mcsubs-mb-2">
					<div class="mcsubs-card-body">
						<div class="mcsubs-d-flex mcsubs-pb-1 mcsubs-mb-2 mcsubs-border-bottom mcsubs-border-lightgray">
							<div class="mcsubs-font-weight-bold mcsubs-col">
								Billing Info
							</div>
						</div>
						<div id="billingInfoForm">
							<div class="mcsubs-d-flex mcsubs-mb-3">
								<div class="mcsubs-w-50 mcsubs-pr-2">
									<div class="mcsubs-font-size-md">State/Province <span class="mcsubs-text-danger">*</span></div>
									<cfset local.qryStates = application.objCommon.getStates()>
									<select id="stateIDforTax" name="stateIDforTax" class="mcsubs-formcontrol mcsubs-w-100">
										<option value=""></option>
										<cfset local.currentCountryID = 0>
										<cfloop query="local.qryStates">
											<cfif local.qryStates.countryID neq local.currentCountryID>
												<cfset local.currentCountryID = local.qryStates.countryID>
												<optgroup label="#local.qryStates.country#">
											</cfif>
											<option value="#local.qryStates.stateID#" <cfif local.stateIDforTax is local.qryStates.stateID>selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
											<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
												</optgroup>
											</cfif>
										</cfloop>
									</select>
									<div id="stateIDforTax_err" class="mcsubs-font-size-sm mcsubs-text-danger" style="display:none;"></div>
								</div>
								<div class="mcsubs-w-50 mcsubs-pl-2">
									<div class="mcsubs-font-size-md">Postal Code <span class="mcsubs-text-danger">*</span></div>
									<input type="text" id="zipForTax" name="zipForTax" class="mcsubs-formcontrol mcsubs-w-100" maxlength="25" value="#local.zipForTax#">
									<div id="zipForTax_err" class="mcsubs-font-size-sm mcsubs-text-danger" style="display:none;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			<cfelse>
				<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.stateIDforTax#">
				<input type="hidden" id="zipForTax" name="zipForTax" value="#local.zipForTax#">
			</cfif>
			
			<!--- Sample Card used by JS for loading --->
			<div id="subLoadingCard" class="mcsubs-d-none">
				<div class="mcsubs-card mcsubs-mt-2 mcsubs-mb-2">
					<div class="mcsubs-card-body">
						<div class="mcsubs-d-flex mcsubs-pb-1 mcsubs-mb-2 mcsubs-border-bottom mcsubs-border-lightgray">
							<div class="mcsubs-font-weight-bold mcsubs-col">
								<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							</div>
							<div class="mcsubs-ml-auto mcsubs-font-weight-bold mcsubs-font-size-sm">
								<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							</div>
						</div>
						<div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
							<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						</div>
					</div>
				</div>
			</div>

			<!---Total and Check Out--->
			<div class="mcsubs-p-3 mcsubs-mb-3">
				<div id="manageSubsFooterAlert" class="mcsubs-col mcsubs-align-self-center mcsubs-flex-sm-column alert alert-error mcsubs-d-none"></div>
				<div class="mcsubs-mt-2">
					<div class="mcsubs-text-bold mcsubs-mb-2 mcsubs-text-sm-center">
						Total: <span class="grandTotal"></span>
					</div>
					<div>
						<button type="submit" class="btn btn-success btnCheckOut mcsubs-btn125 mcsubs-w-sm-100" disabled>
							<span>Checkout <i class="icon-circle-arrow-right"></i></span>
						</button>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>
<div class="mcsubs-overlay mcsubs-d-none"></div>
</cfoutput>