<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function validateAndSearchReg() {
			var #toScript(application.regEx.email, "mc_emailregex")#
			var emailRegEx = new RegExp(mc_emailregex,"i");
			$('##err_frmSWRegIdentifier').html('').hide();
			$('##btnSearchReg').html('<i class="icon-spin icon-spinner"></i> loading...').prop('disabled',true);

			$('##frmSWRegIdentifier input[type="text"]').each(function() {
				$(this).val($(this).val().trim());
			});

			let arrReq = [];
			if (!$('##fSWRegFirstName').val().length) arrReq.push('Enter the First Name.');
			if (!$('##fSWRegLastName').val().length) arrReq.push('Enter the Last Name.');
			if (!$('##fSWRegEmail').val().length || !(emailRegEx.test($('##fSWRegEmail').val()))) arrReq.push('Enter a valid Email Address.');

			if (arrReq.length) {
				onErrorSearchReg(arrReq.join('<br/>'));
				return false;
			} else {
				$.getJSON('#arguments.event.getValue("locatorurl")#', $('##frmSWRegIdentifier').serializeArray())
					.done(showIdentifiedRegistrants)
					.fail(showIdentifiedRegistrants);
			}
		}
		function showIdentifiedRegistrants(respObj) {
			if (respObj.success) {
				if (!respObj.arrMembers.length) {
					showNewRegForm();
				} else {
					let swRegTemplate = Handlebars.compile($('##mc_swIdentifiedReg_template').html());
					$('##SWRegIdentifierFormContainer').hide();
					$('##SWRegIdentifierResults').html(swRegTemplate(respObj)).show(300);
				}
			} else {
				onErrorSearchReg('There was a problem displaying the data. Try again!');
			}
		}
		function onErrorSearchReg(msg) {
			$('##err_frmSWRegIdentifier').html(msg).show();
			$('##btnSearchReg').html('Continue').prop('disabled',false);
		}
		function useMember(mid) {
			self.location.href='#arguments.event.getValue('mainregurl')#&regaction=usemid&mid=' + mid;
		}
		function showNewRegForm() {
			<cfif len(arguments.event.getTrimValue('mc_siteinfo.alternateGuestAccountCreationLink','')) gt 0>
				self.location.href='#arguments.event.getTrimValue('mc_siteinfo.alternateGuestAccountCreationLink')#';
			<cfelse>
				$('##SWRegIdentifierFormContainer').hide();
				$('##SWRegIdentifierResults')
					.html('<img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...')
					.load('#arguments.event.getValue('newregacctformurl')#')
					.show();
			</cfif>
		}
		function useNA() {
			self.location.href='#arguments.event.getValue("mainregurl")#';
		}
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">