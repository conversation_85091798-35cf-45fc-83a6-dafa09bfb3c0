<cfcomponent extends="model.customPage.customPage" output="false">
	<!--- Controller() --->
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			variables.fund = arguments.event.getValue('fund','');
			local.formAction = arguments.event.getValue('fa','showLookup');

			/* ************************* */
			/* Custom Page Custom Fields */
			/* ************************* */
			local.arrCustomFields = [];


			// AccountLocatorHeadline //
			local.tmpField = {
				name="AccountLocatorHeadline",
				type="STRING",
				desc="Account Locator Headline",
				value="Oregon Jury Project Campaign"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// AccountLocatorContent //
			local.tmpField = {
				name="AccountLocatorContent",
				type="CONTENTOBJ",
				desc="Account Locator Content",
				value="<p>Your tax-deductible donations here will go to the Oregon Jury Project - OTLA's public education foundation. We will inform all donors of upcoming Bike Helmet Project or OTLA Cares events in the community to see first-hand the lives this program strives to protect.</p><p>To donate with a credit card, please enter the amount below and click the submit button to be directed to the payment form. You may also write a check <strong>made out to 'Oregon Jury Project'</strong> and mail it to our office. Please note that with a check donation you must include a note to specify where your donation will be allocated, i.e. Bike Helmet Project, OTLA Cares, or general Oregon Jury Project.</p><p><strong><em>If you opt for an online payment,</em></strong> note that your digital receipt will say 'Bike Helmet Project,' but that your donation will be earmarked for the Oregon Jury Project unless otherwise specified.</p><p>Thank you for your contribution! If you have any questions, contact Nora <NAME_EMAIL>.</p>"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// AccountLocatorTitle //
			local.tmpField = {
				name="AccountLocatorTitle",
				type="STRING",
				desc="Account Locator Title",
				value="Account Lookup / Create New Account"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// AccountLocatorInstructions //
			local.tmpField = {
				name="AccountLocatorInstructions",
				type="CONTENTOBJ",
				desc="Account Locator Instruction Text",
				value="<p>Click the <strong>Account Lookup</strong> button to the left.</p><p>Enter the search criteria and click <strong>Continue</strong>.</p><p>If you see your name, please press the <strong>Choose</strong> button next to your name.</p><p>If you do not see your name, click the <strong>Create an Account</strong> link.</p>"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// AccountLocatorButton //
			local.tmpField = {
				name="AccountLocatorButton",
				type="STRING",
				desc="Account Locator Button Text",
				value="Account Lookup"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// FundOption1Name //
			local.tmpField = {
				name="FundOption1Name",
				type="STRING",
				desc="Text for the first fund option",
				value="Bike Helmet Project"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// FundOption1GLCode //
			local.tmpField = {
				name="FundOption1GLCode",
				type="STRING",
				desc="GL Code for the first fund option",
				value="Jury Project"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// FundOption2Name //
			local.tmpField = {
				name="FundOption2Name",
				type="STRING",
				desc="Text for the second fund option",
				value="OTLA Cares"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// FundOption2GLCode //
			local.tmpField = {
				name="FundOption2GLCode",
				type="STRING",
				desc="GL Code for the second fund option",
				value="OTLA Cares"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// FundOption3Name //
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = {
				name="FundOption3Name",
				type="STRING",
				desc="Text for the third fund option",
				value="Jury Project General"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// FundOption3GLCode //
			local.tmpField = {
				name="FundOption3GLCode",
				type="STRING",
				desc="GL Code for the third fund option",
				value="Jury Project"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// StaffConfirmationTo //
			local.tmpField = {
				name="StaffConfirmationTo",
				type="STRING",
				desc="who do we send staff confirmations to",
				value="<EMAIL>;<EMAIL>"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// MemberConfirmationFrom //
			local.tmpField = {
				name="MemberConfirmationFrom",
				type="STRING",
				desc="who do we send member confirmations from",
				value=""
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// CCPayProfileCode //
			local.tmpField = {
				name="CCPayProfileCode",
				type="STRING",
				desc="Pay profile code for CC",
				value="OTLAJPCC"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// CheckPayProfileCode //
			local.tmpField = {
				name="CheckPayProfileCode",
				type="STRING",
				desc="Pay profile code for checks",
				value="PAYLATER"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// DonationFormHeaderTitle //
			local.tmpField = {
				name="DonationFormHeaderTitle",
				type="CONTENTOBJ",
				desc="Donation form header title",
				value="Editable Title"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// DonationFormHeaderContent //
			local.tmpField = {
				name="DonationFormHeaderContent",
				type="CONTENTOBJ",
				desc="Donation form header content",
				value="Editable Content"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			// contentConfirmation //
			local.tmpField = {
				name="contentConfirmation",
				type="CONTENTOBJ",
				desc="Content on Confirmation",
				value="Thank you for your donation."
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(
				siteID=arguments.event.getValue('mc_siteInfo.siteID'),
				siteResourceID=this.siteResourceID,
				arrCustomFields=local.arrCustomFields
			);

			/* ***************** */
			/* set form defaults */
			/* ***************** */
			structAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmDonation',
				formNameDisplay='Donation Form',
				orgEmailTo=variables.strPageFields.StaffConfirmationTo,
				memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
			));

	
			/* ***************** */
			/* Form Process Flow */
			/* ***************** */
			switch (local.formAction) {
				case "processLookup":
	 				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
					break;
				case "processDonationInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processDonationInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='fail');
							break;				
					}
					break;
				case "processPayment":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;
					}
					switch (processPayment(rc=arguments.event.getCollection())) {
						case "success": 
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				default:
					arguments.event.collectionAppend(variables);
					local.returnHTML = showLookup(rc=arguments.event.getCollection());
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>


	<!--- showLookUp() --->
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>	
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.frmContent{ padding:10px; background:##dddddd; }
				.frmRow1{ background:##ffffff; }
				.frmRow2{ background:##dedede; }
				.frmRow3{ background:##aeaeae; }
				.frmTotals{ background:##666666; color:##ffffff; font-weight:bold; }
				.frmText{ font-size:9pt; color:##505050; }
				.frmButtons{ padding:5px 0; border-top:1px solid ##666666; border-bottom:1px solid ##666666; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
				.CPSection{ border:1px solid ##666666; margin-bottom:15px; }
				.CPSectionTitle { font-size:14pt; height:20px; font-weight:bold; color:##ffffff; padding:10px; background:##336699; }
				.CPSectionContent{ padding:0 10px; }
				.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
				.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
				.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
				.subCPSectionTitle { font-size:10pt; font-weight:bold; }
				.subCPSectionText { font-size:9pt; color:##36617d; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.info{ font-style:italic; font-size:8pt; color:##777777; }
				.small{ font-size:7pt;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.r { text-align:right; }
				.l { text-align:left; }
				.c { text-align:center; }
				.i { font-style:italic; }
				.b{ font-weight:bold; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.P{padding:10px;}
				.PL{padding-left:10px;}
				.PR{padding-right:10px;}
				.PB{padding-bottom:10px;}
				.PT{padding-top:10px;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.BB { border-bottom:1px solid ##666666; }
				.BL { border-left:1px solid ##666666; }
				.BT { border-top:1px solid ##666666; }
				.block { display:block; }
				.black{ color:##000000; }
				.red{ color:##ff0000; }
				<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
				.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
				.msgSubHeader{background:##dddddd;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.tsAppBodyText { color:##03608b;}
				select.tsAppBodyText{color:##666666;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.paymentGateway{ background-color:##ededed; padding:10px; }
				##memberNumber{ display:inline-block; width:140px; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.required { color:red; font-weight:bold; }
			</style>

			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				
				window.onhashchange = function() {
					if (location.hash.length > 0) {
						step = parseInt(location.hash.replace('##',''),10);
						if (prevStep > step){
							if(step==1) {
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							}
							if(step==2) {
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processDonationInfo");
							}
							mc_loadDataForForm($('###variables.formName#'),'previous');
						}
					} else {
						step = 1;
					}
					prevStep = step;
				}

				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="">
				<cfinput type="hidden" name="fund" id="fund" value="#arguments.rc.fund#">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
			
				<div class="tsAppSectionContentContainer">
					<h2>#variables.strPageFields.accountLocatorHeadline#</h2>
					#variables.strPageFields.accountLocatorContent#

					<br><br>

					<div class="CPSection">
						<div class="CPSectionTitle BB">#variables.strPageFields.accountLocatorTitle#</div>
						<div class="frmRow1" style="padding:10px;">
							<table cellspacing="0" cellpadding="2" border="0" width="100%">
								<tr>
									<td width="175" class="c">
										<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
											<button name="btnAddAssoc" type="button" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
										</div>
									</td>
									<td>
										<span class="tsAppBodyText">
											#variables.strPageFields.accountLocatorInstructions#
										</span>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<!--- showMemberInfo() --->
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfif structKeyExists(session.formFields.step1, "origMemberID") and structKeyExists(session.formFields.step1, "origMemberID")>
				<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfelseif structKeyExists(arguments.rc, "origMemberID") and structKeyExists(arguments.rc, "origMemberID")>
				<cfset variables.origMemberID = arguments.rc.origMemberID>
			</cfif>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>

		<cfif not len(trim(variables.fund)) and structKeyExists(arguments.rc, "fund")>
			<cfset variables.fund = arguments.rc.fund>
		</cfif>

		<cfset local.donationAmount	= "$0.00">
		<cfif structKeyExists(arguments.rc, "donationAmount") and structKeyExists(arguments.rc, "donationAmount")>
			<cfset local.donationAmount	= arguments.rc.donationAmount>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='319706e2-02ac-4417-8941-a866f2561f82', mode="collection", strData=local.strData)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				var IndividualInfoContainer = '<div class="tsAppSectionHeading">#JSStringFormat(local.strFieldSetContent1.fieldSetTitle)#</div><div class="tsAppSectionContentContainer">#JSStringFormat(local.strFieldSetContent1.fieldSetContent)#</div>';
				
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					var fund_type = $('##fund_type').val();
					if (fund_type == '') arrReq[arrReq.length] = 'Select Fund/Campaign type.';
					
					if ($('##paymentPlan').val() == '') arrReq[arrReq.length] = 'Select an Amount.';
					else if ($('##paymentPlan').val() == 'Other' &&  ( ($.trim($('##otherAmt').val()).length == 0) ||( $.trim($('##otherAmt').val()).length > 0 && Number($('##otherAmt').val().replace(/[^0-9\.]+/g,"")) == 0 ) ) )
						arrReq[arrReq.length] = 'Donation - Enter a valid amount. Only positive amounts are allowed.';
					
					// Fieldset Validation //
					#local.strFieldSetContent1.jsValidation#
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					
					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				function showHidePaymentAmt(){
					if($('##paymentPlan').val() == 'Other') $('##otherAmt').show().val('');
					else $('##otherAmt').hide();
				}

				$(function() {
					prefillData();
				});
			</script>
			<style type="text/css">
				###variables.formName# input[type="text"] {min-height:30px;}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div class="TitleText" style="padding-bottom:15px;">#variables.strPageFields.DonationFormHeaderTitle#</div>
				#variables.strPageFields.DonationFormHeaderContent#
				<div style="clear:both;"></div>
				<br /><br />
				
				<div class="r i frmText"><span class="required">* </span>Denotes required field</div>

				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
					<input type="hidden" name="fa" id="fa" value="processDonationInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<cfinclude template="/model/cfformprotect/cffp.cfm">
					
					<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
					
					<div class="tsAppSectionContentContainer">
						<div class="CPSection">
							<div class="CPSectionTitle BB"><span class="required">* </span>Donation</div>
							<div class="frmRow1 frmText">
								<table width="100%" cellspacing="0" cellpadding="0" border="0">
									<tr class="frmRow1">
										<td colspan="2" class="frmText P c">
											<strong>I would like to donate <input type="text" size="10" value="#local.donationAmount#" name="donationAmount" id="donationAmount" onBlur="this.value = formatCurrency(this.value);"></strong>
										</td>
									</tr>
									<tr class="frmRow1">
										<td colspan="2" id="donationAmount_message" class="frmRow1 frmText P c" style="color:red;display:none;">
											<strong>Please enter a valid amount to donate.</strong>
										</td>
									</tr>
									<tr class="frmRow2">
										<td colspan="2" class="frmText P c">
											<strong>Please select a fund:</strong>
											<select name="fund_type" id="fund_type">
												<option value="fund1">#variables.strPageFields.FundOption1Name#</option>
												<option value="fund2">#variables.strPageFields.FundOption2Name#</option>
												<option value="fund3">#variables.strPageFields.FundOption3Name#</option>
											</select>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</div>

					<div class="tsAppSectionContentContainer">
						<div class="CPSection">
							<div class="CPSectionTitle BB">
								#local.strFieldSetContent1.fieldSetTitle#
							</div>
							<div class="tsAppSectionContentContainer">
								#local.strFieldSetContent1.fieldSetContent#
							</div>
						</div>
					</div>

					<div id="donorInformation"></div>
					<button name="btnContinue" type="submit" class="btn tsAppBodyButton pull-right" onClick="hideAlert();">Next &gt;&gt;</button>
				</form>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>
	
	<!--- processDonationInfo() --->
	<cffunction name="processDonationInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>

		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
	
	<!--- showPayment() --->
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.arrPayMethods = [ variables.strPageFields.CCPayProfileCode, variables.strPageFields.CheckPayProfileCode ]>
		<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
			arrPayMethods=local.arrPayMethods, 
			siteID=variables.siteID, 
			memberID=variables.useMID, 
			title="Donation Form", 
			formName=variables.formName, 
			backStep="processDonationInfo"
		)>

		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_" or ListFindNoCase("donationAmount|fund_type",local.thisField,"|")>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<cfswitch expression="#arguments.rc['fund_type']#">
				<cfcase value="fund1">
					<cfset local.fundToDisplay = variables.strPageFields.FundOption1Name>
				</cfcase> 
				<cfcase value="fund2">
					<cfset local.fundToDisplay = variables.strPageFields.FundOption2Name>
				</cfcase> 
				<cfcase value="fund3">
					<cfset local.fundToDisplay = variables.strPageFields.FundOption3Name>
				</cfcase> 
			</cfswitch>

			<div class="tsAppSectionHeading">Payment for #variables.formNameDisplay#</div>
			<div class="tsAppSectionContentContainer">
				<div class="tsAppBodyText">
					<b>Fund/Campaign :</b>  #local.fundToDisplay# <br />
					<b>Amount :</b> #arguments.rc["donationAmount"]#
				</div>
			</div>
			
			#local.strReturn.paymentHTML#
			
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<!--- processPayment() --->
	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.fundType = ''>
		<cfset local.totalAmount = val(ReReplace(arguments.rc.donationAmount, "[^\d.]", "","ALL"))>
		<cfif structKeyExists(arguments.rc,"fund_type")>
			<cfset local.fundType = arguments.rc.fund_type>
		</cfif>

		<cfset variables.useMID = arguments.rc.MemberID />
	
		<!--- This is the accounting part --->
		<cfset local.strAccTemp = {
			totalPaymentAmount=local.totalAmount,
			assignedToMemberID=variables.useMID,
			recordedByMemberID=variables.useMID,
			rc=arguments.rc
		}>

		<cfif arguments.rc.MCCF_PAYMETHTITLE eq "Credit Card">
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
				siteid=variables.siteID,
				profileCode=variables.strPageFields.CCPayProfileCode
			)>
			<cfset local.strAccTemp.payment = {
				detail="#variables.formNameDisplay#",
				amount=local.strAccTemp.totalPaymentAmount,
				profileID=local.payProfileID,
				profileCode=variables.strPageFields.CCPayProfileCode
			}>
		</cfif>

		<!--- GL Code here comes from a custom field. --->
		<!--- Detail comes from the fund. --->
		<cfswitch expression="#arguments.rc.fund_Type#">
			<cfcase value="fund1">
				<cfset local.strAccTemp.revenue  = [{revenueGLAccountCode=variables.strPageFields.FundOption1GLCode, detail="#variables.strPageFields.FundOption1Name# Donation", amount=local.strAccTemp.totalPaymentAmount}]>
			</cfcase>
			<cfcase value="fund2">
				<cfset local.strAccTemp.revenue  = [{revenueGLAccountCode=variables.strPageFields.FundOption2GLCode, detail="#variables.strPageFields.FundOption2Name# Donation", amount=local.strAccTemp.totalPaymentAmount}]>
			</cfcase>
			<cfcase value="fund3">
				<cfset local.strAccTemp.revenue  = [{revenueGLAccountCode=variables.strPageFields.FundOption3GLCode, detail="#variables.strPageFields.FundOption3Name# Donation", amount=local.strAccTemp.totalPaymentAmount}]>
			</cfcase>
		</cfswitch>

		<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
		<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
		
		<cfset local.response = 'success'>
		
		<cfreturn local.response>
	</cffunction>
	
	<!--- produceConfirmation() --->
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.rc.fund_Type#">
			<cfcase value="fund1">
				<cfset local.Fund = variables.strPageFields.FundOption1Name>
			</cfcase>
			<cfcase value="fund2">
				<cfset local.Fund = variables.strPageFields.FundOption2Name>
			</cfcase>
			<cfcase value="fund3">
				<cfset local.Fund = variables.strPageFields.FundOption3Name>
			</cfcase>
		</cfswitch>

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='319706e2-02ac-4417-8941-a866f2561f82', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.memberPayProfileDetail = "">
		<cfif structKeyExists(arguments.rc,"mccf_payMethID") and structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
			<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
		<cfelse>
			<cfset local.memberPayProfileSelected = 0>
		</cfif>
		<cfif local.memberPayProfileSelected gt 0>
			<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
		</cfif>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.contentConfirmation)>
					<div>#variables.strPageFields.contentConfirmation#</div>
				</cfif>
				
				<!--@@specialcontent@@-->
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation Information</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;width:150px;">
						<b>Fund/Campaign:</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;"><cfif structKeyExists(arguments.rc,"fund_type")>#local.Fund#</cfif></td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Amount :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						#arguments.rc["donationAmount"]#
					</td>
				</tr>
				</table>
				<br/>
				
				#local.strFieldSetContent1.fieldSetContent#
				
				<cfif local.memberPayProfileDetail neq "">
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation - Payment</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="Thank you for your donation",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your donation", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<!--- showConfirmation() --->
	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your donation.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>
