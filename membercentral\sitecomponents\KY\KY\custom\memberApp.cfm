<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	
	local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="SubTypeTest", type="STRING", desc="Check for existing accepted/active/billed subscriptions of this type", value="5B362CF3-308C-4A1C-B892-D1EDC6089C33"}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ActiveAcceptedMessage", type="CONTENTOBJ", desc="Message displayed when account selected has active/accepted subscription", value="You currently have a membership subscription. If you believe that you should be able to sign up for a Membership, please contact KJA" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="BilledMessage", type="STRING", desc="Message displayed when account selected billed subscription", value="You need to renew your KJA membership. You will be re-directed to your renewal shortly." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="KJA Membership Application Form" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="TopFormContent", type="CONTENTOBJ", desc="Content at top of page 1", value="If you prefer to pay by check, please print and fill out <a href='/docDownload/129541'>this PDF Form</a> and return it along with your payment." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Staff Confirmation Email", value="<EMAIL>,<EMAIL>,<EMAIL>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription with Join rate", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please contact <NAME_EMAIL> or 502-339-8890." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="BilledRenewalMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription with Renewal rate", value="Our records indicate either your application is currently under review or you are a renewing member. If you are looking to renew, please [[click here]] to review your renewal statement. If you have questions about your membership, please contact <NAME_EMAIL> or 502-339-8890." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AddOnSubscription",type="STRING",desc="UID for Add on subscription set",value="9B051A86-3CD1-4EF9-8E8D-F8CBED0FA098" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
		
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	
	// set page defaults
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmJoin',
		formNameDisplay=local.strPageFields.FormTitle,
		orgEmailTo=local.strPageFields.StaffConfirmationEmail,
		memberEmailFrom='<EMAIL>'
	));

	// set payment gateways
	local.profile_1 = application.objCustomPageUtils.acct_getProfile(siteid=local.siteID, profileCode='KYCCCIM');

	// custom functions
	local.strLawSchools = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName='Law School');

	// =======================================================================================================================
	// SUBSCRIPTION INFORMATION ----------------------------------------------------------------------------------------------
	local.objSubs									= CreateObject('component','model.admin.subscriptions.subscriptions');
	// =======================================================================================================================
	// LOCAL SITE INFORMATION ------------------------------------------------------------------------------------------------
	local.mainhostName 			= event.getValue('mc_siteInfo.mainhostName');
	local.thisScheme 			= event.getValue('mc_siteinfo.scheme');

	// SUBSCRIPTIONS
	local.memberTypeUID				= local.strPageFields.SubTypeTest;
	local.attySubUID 				= '57D29978-85CA-4C57-8F37-E03EF27C4553';
	local.judicialSubUID			= '0963F510-51C0-43B7-B628-C71EDC260124';
	local.supportSubUID 			= 'E7E0797E-41D7-4634-B04D-4538E0FA8A8E';
	local.affiliateSubUID 			= '5B614FC5-1594-49C4-8E7A-0C0012788F40';
	local.lawStudentSubUID 			= '19F5C068-DE7C-404A-B3E3-2D6F9D977001';

	local.sustainingDuesSubUID		= 'F2C7C3B8-C603-4EC5-AA6A-F6120FD7622F';

	// RATES
	local.atty10YearsRateUID		= '883BB264-71AC-413B-9E59-F6E21E5CBD4D';
	local.atty5YearsRateUID			= '95F85E68-B777-4C9E-81D0-42FEEC7F27AC';
	local.attyUnder5YearsRateUID	= '19B51E0F-9A1E-451A-9357-F3EA9EC27D4F';
	local.attyPS10YearsRateUID		= '3B5D33CA-72EB-4394-B7D2-BF19699BD54F';
	local.attyPS5YearsRateUID		= 'C77E14A6-06DA-4BBF-AA3F-6FF8EACE7469';
	local.attyPSUnder5YearsRateUID	= '56CB0FB9-71F9-4C53-90E4-FE427FC5DDC6';

	local.judicialRateUID			= 'FF9524E5-0CF8-4704-8ABF-4611B1AC1593';
	local.nonAttorneyRateUID		= '9FFBAC8F-5087-409E-AA7B-DF4ABBA0885E';
	local.proAffiliateRateUID		= 'F007D0B3-E1B4-4463-B228-3E207375F155';


	local.lawStudent1L				= '0A5129C6-C58F-4174-853C-8C5A0694D1B8';
	local.lawStudent2L				= '90C9D85E-29BA-4518-A902-2C27D162B92E';
	local.lawStudent3L				= 'F3A95D41-F083-4FAC-851C-57D439325127';
	
	local.sectionRateUID			= '2AB5FBBA-EC08-41F9-8E7E-91788C5D14A3';

	local.sustainingDues100RateUID	= '9BCC0E88-33F7-47CD-A4FF-798572D50193';
	local.sustainingDues200RateUID	= '87C935D0-6A2D-4C47-A560-52B9044292A5';
	local.sustainingDues300RateUID	= '5B0CA8E1-903D-45DA-9171-9D6A4D98298C';
	
	//RATE FREQUENCIES
	local.annualFrequencyUID		= '504CFB0F-665A-4D57-BF10-C530EDE87792';
	

	local.qryStates = application.objCommon.getStates();

	local.scheduleUIDList = '16963B59-30CE-4454-A994-B0213FD6A2C4,2992D1BA-1783-46F7-A2DD-B5C97A8BDF40,1F8EEFDF-FB9D-49A7-9BFC-244FCEE3D7DA,********-8F7E-4300-B62B-07D581E89009,EF1081D8-EA57-4EB0-B9D5-BADDBA6D4D23';
	local.qryRates = application.objCustomPageUtils.sub_getRateSchedule(siteID=local.siteID, scheduleUID=local.scheduleUIDList, activeRatesOnly=true, ignoreRenewalRates=true);

	local.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=local.formName);
</cfscript>

<!--- Build the rate structure in current form --->
<cfset local.strFullDues = StructNew()>
<cfloop query="local.qryRates">
	<cfif local.qryRates.RateUID EQ local.atty10YearsRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.atty10YearsRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.atty5YearsRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.atty5YearsRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.attyUnder5YearsRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.attyUnder5YearsRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.attyPS10YearsRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.attyPS10YearsRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.attyPS5YearsRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.attyPS5YearsRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.attyPSUnder5YearsRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.attyPSUnder5YearsRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.lawStudent1L>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.lawStudent1L#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.lawStudent2L>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.lawStudent2L#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.lawStudent3L>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.lawStudent3L#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.judicialRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.judicialRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.nonAttorneyRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.nonAttorneyRateUID#", local.qryRates.rateAmt)>
		</cfif>
	<cfelseif local.qryRates.RateUID EQ local.proAffiliateRateUID>
		<cfif local.qryRates.FrequencyName EQ "Full">
			<cfset  structInsert(local.strFullDues, "#local.proAffiliateRateUID#", local.qryRates.rateAmt)>
		</cfif>
	</cfif>
</cfloop>

<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID=local.memberTypeUID)>
<cfset local.hasSub = false>
<cfset local.hasSubBilled = false>
<cfset local.hasSubBilledJoined = false >
<cfif (event.getValue('msg','') neq 1 OR event.getValue('msg','') neq 2 OR event.getValue('msg','') neq 3) and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
	<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
</cfif>

<cfif local.hasSub>
	<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=local.siteID, memberID=local.useMID, status='O', distinct=true, includeRate=true)>
	<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.memberTypeUID)>	
		<cfif local.qryBilledSubs.isRenewalRate>
			<cfset local.hasSubBilled = true>
		<cfelse>
			<cfset local.hasSubBilledJoined = true>
		</cfif>
	</cfif>
	
</cfif>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##4B91CD; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##03608B; border-bottom:1px solid ##03608B; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
			.CPSection{ border:1px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:18pt; font-weight:bold; color:##fff; padding:10px; background:##244966; }
			.CPSectionContent { padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ccc; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.tsAppBodyText { color:##000;}
			select.tsAppBodyText{color:##666;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			##date_bar, ##date_grad, ##date_birth { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
		</style>
	</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<cfsavecontent variable="local.pageJS">
		<script type="text/javascript">
			
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
				var cnt = 0;
				for (var i=0; i < obj.length; i++) {
					if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
					else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
				}					
				if (cnt <= optRespLimit) return true;
				else return false;
			}
			function formatCurrency(num) {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				num = Math.floor(num/100).toString();
				if(cents<10) cents = "0" + cents;
				for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
				num.substring(num.length-(4*i+3));
				return (((sign)?'':'-') + '$' + num + '.' + cents);
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
			
			function selectMember() {
				$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
			}
			function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
			function addMember(memObj) {
				$.colorbox.close();
				assignMemberData(memObj);
			}
			
		</script>
	</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	#local.pageJS#
	#local.pageCSS#
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<div id="customPage">
		<div class="TitleText PB">#local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: ========================================================================================================================================= --->
			<cfcase value="0">
				<cfset local.qryAddOnSubscriptionData =  application.objCustomPageUtils.sub_getSetSubscriptions(setUID='#local.strPageFields.AddOnSubscription#',siteID=local.siteID)/>
				<cfif event.getValue('msg',0) EQ "3">		
					<!--- Renewal form is not Open --->
					<div class="bodyText" >
						#local.strPageFields.BilledRenewalMessage#
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=local.siteID,memberID=local.useMID,status='O',distinct=false)>
						
						<script type="text/javascript">
							setTimeout(function(){
								window.location = "/renewsub/#local.qrySubs.directlinkcode#";
							}, 3000);	
						</script>
					</div>				
				<cfelseif event.getValue('msg',0) EQ "2">		
					<!--- Renewal form is not Open --->
					<div class="bodyText" >
						#local.strPageFields.ActiveAcceptedMessage#
					</div>
				<cfelseif event.getValue('msg',0) EQ "1">		
					<!--- Renewal form is not Open --->
					<div class="bodyText" >
						#local.strPageFields.BilledJoinMessage#
					</div>	
				<cfelse>
					<cfif local.hasSubBilled>
						<cflocation url="#local.customPage.baseURL#&msg=3" addtoken="no" />
					<cfelseif local.hasSubBilledJoined>
						<cflocation url="#local.customPage.baseURL#&msg=1" addtoken="no" />		
					<cfelseif local.hasSub>
						<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no" />
					</cfif>
					
					<script type="text/javascript">
						function checkCaptchaAndValidate(){
							var thisForm = document.forms["#local.formName#"];
							var status = false;
							var captcha_callback = function(captcha_response){
								if (captcha_response.response && captcha_response.response != 'success') {
									status = false;
								} else {
									status = true;
								}
							}
							if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							} else {
								#local.captchaDetails.jsvalidationcode#
							}
							if(status){
								return _FB_validateForm();
							} else {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							}
						}
						function _FB_validateForm() {
							var thisForm = document.forms["#local.formName#"];
							var arrReq = new Array();
							var lastMtrxErr = '';
							// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['category'], 'RADIO')) arrReq[arrReq.length] 				= 'Please select a Membership Status.';
							if (thisForm.category.value == 5)
							{
								if (!_FB_hasValue(thisForm['sponsor'], 'TEXT')) arrReq[arrReq.length] 						= 'Please enter your sponsor\'s name.';
							}
							if (thisForm.category.value == 7)
							{
								if (!_FB_hasValue(thisForm['date_grad'], 'TEXT')) arrReq[arrReq.length] 						= 'Please enter your Expected Grad. Date.\'s name.';
							}
	
							if (arrReq.length > 0) {
								var msg = 'The following questions are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}

							$('##btnSubmit').attr('disabled',true);
							$('##formToFill').hide();
							$('##formToFillSubmitting').show();
							$('html,body').animate({scrollTop: $('##formToFillSubmitting').offset().top},100);
							return true;
						}
	
						function assignMemberData(memObj){
							var thisForm = document.forms["#local.formName#"];
							var er_changeErr = function(r) {
								alert('error');
							};
	
							var er_change = function(r) {
								var results = r;
								if( results.success ){
									
									thisForm['memberNumber'].value 	= results.membernumber;
									thisForm['memberID'].value 			= results.memberid;
	
	
									thisForm['name'].value 		= results.fullname;
									thisForm['firm'].value 		= results.company;
									thisForm['address1'].value 	= results.address1;
									thisForm['address2'].value 	= results.address2;
									thisForm['city'].value 		= results.city;
									thisForm['state'].value 	= results.statecode;
									thisForm['zip'].value 		= results.postalcode;
									thisForm['phone'].value 	= results.phone;
									thisForm['email'].value 	= results.email;
									
									var dispBarDate = "" + results["bar date"];
									if (dispBarDate.length > 0)
									{
										var jsDate = new Date(results["bar date"]);
										thisForm['date_bar'].value = ((jsDate.getMonth() + 1) + '/' + jsDate.getDate() + '/' + jsDate.getFullYear());
									}
									
									thisForm['bar_ID'].value = results["bar number"];
									
									
									var lowerCaseMemberTypeUID = '#LCase(local.memberTypeUID)#';
									
									if (r.types[lowerCaseMemberTypeUID] != 1)
									{
										document.getElementById('formToFill').style.display = '';
									}
									else
									{
										// redirect to link if statusCode = 'O' (billed) and there's a directLinkCode
										if(r.isRenewalRate) {
											document.getElementById('BilledJoinedMemberExisting').style.display = '';
										}
										else if (r.statuscode[lowerCaseMemberTypeUID] === 'O' && r.directlinkcode[lowerCaseMemberTypeUID].length > 0) {
											document.getElementById('BilledMemberExisting').style.display = '';											
											setTimeout(function(){
												window.location = "/renewsub/"+r.directlinkcode[lowerCaseMemberTypeUID];
											}, 3000);											
										} else {
											document.getElementById('memberExisting').style.display = '';
										}
									}
									if ( results.memberid > 1 ){									
										AJAXMemberHistory('write');
									}
									showYears();	
									$(".account_lookup").hide();
									
									
								}
								else{ /*alert('not success');*/ }
							};
	
							var customFields = [];
							customFields[0] = 'bar number';
							customFields[1] = 'bar date';
	
							var typeUIDs = '#local.memberTypeUID#'.split(',');
	
							var objParams = { memberNumber:memObj.memberNumber, typeUIDs:typeUIDs, customFields:customFields, includeRate:true };
							TS_AJX('SUBS','getMemberDataByMemberNumberWithSubTypes',objParams,er_change,er_changeErr,20000,er_changeErr);
							showCaptcha();
						}
						
						function loadMember(memNumber){
							var objParams = { memberNumber:memNumber };
							assignMemberData(objParams);
						}
						
						mc_memberhistoryID = 0;
					
						function AJAXGetCategories(subCategory,callback){
							var objTypeParams = { parentCode:'MemberApp', subName:subCategory };
							TS_AJX('CUSTOM_FORM_UTILITIES','mh_getCategoryAJX',objTypeParams,callback);
						}

						function AJAXMemberHistory(writeOrUpdate){
							if ( writeOrUpdate == 'write' ){
								var writeHistoryResult = function(r) {
									mc_memberhistoryID = parseInt(r);
									$("##useHistoryID").val(mc_memberhistoryID);
								};
								var writeHistory = function(r){
									
									var objParams = { memberID:$('##memberID').val(), categoryID:parseInt(r.data.categoryid[0]), subCategoryID:parseInt(r.data.subcategoryid[0]), description:'Member started join form.', enteredByMemberID:$('##memberID').val(), newAccountsOnly:false };
									TS_AJX('CUSTOM_FORM_UTILITIES','mh_addHistory',objParams,writeHistoryResult);	
								};
								AJAXGetCategories('Started',writeHistory);
							}
						}
						function chkContribute(){
							var thisForm = document.forms["#local.formName#"];
							var category = thisForm['category'];
							var cValue = 0;
							
							for (var i=0; i <= category.length-1; i++) {
								if (category[i].checked)
								{
									cValue = category[i].value;
									break;
								}
							}
							
							if ((cValue == 0) || (cValue == 7)) {
								document.getElementById('divContribute').style.display = 'none';
							}
							else {
								document.getElementById('divContribute').style.display = '';
							}
						}
						
						function showStuCat() {
							var gradDate = document.getElementById('date_grad');
							var category = document.getElementsByName('category');
							
							
							if (gradDate != ''){
								category[6].disabled = '';
							}
						
						}
						
						function showSupportCat() {
							var sponsor = document.getElementById('sponsor');
							var category = document.getElementsByName('category');
							
							
							if (sponsor != ''){
								category[4].disabled = '';
							}
						}
						
						function showYears()
						{
							var thisForm = document.forms["#local.formName#"];
							var memAllow = 0;
							
							var re = /^[01]?[0-9]\/[0-3]?[0-9]\/[12][90][0-9][0-9]$/;
							if (re.test(thisForm['date_bar'].value))
							{
								var monthPart = parseInt(thisForm['date_bar'].value.split("/")[0]);
								var dayPart = parseInt(thisForm['date_bar'].value.split("/")[1]);
								var yearPart = parseInt(thisForm['date_bar'].value.split("/")[2]);
	
								if ((monthPart != NaN) && (dayPart != NaN) && (yearPart != NaN))
								{
									var now = new Date();
									var sixMonthsAgo = new Date();
									sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
									var fiveYearsAgo = new Date();
									fiveYearsAgo.setYear(fiveYearsAgo.getFullYear()- 5);
									var tenYearsAgo = new Date();
									tenYearsAgo.setYear(tenYearsAgo.getFullYear()- 10);
									
									var barDate = new Date(yearPart, monthPart-1, dayPart);
									
									
									if (now > barDate)
									{
										if (barDate > fiveYearsAgo)
										{
											memAllow = 1;
										}
										else if ((barDate <= fiveYearsAgo) && (barDate > tenYearsAgo))
										{
											memAllow = 2;
										}
										else if (barDate <= tenYearsAgo)
										{
											memAllow = 3;
										}
									}
								}
							}
							
							var category = thisForm['category'];
							for (var i=0; i <= category.length-1; i++) {
								if (((category[i].value >= 1) && (category[i].value <= 3)) || 
										((category[i].value >= 8) && (category[i].value <= 10))) {
									if ((memAllow == 1) && ((category[i].value == 3) || (category[i].value == 10))) {
										category[i].disabled = false;
										category[0].checked	 = true;
									}
									else if ((memAllow == 2) && ((category[i].value == 2) || (category[i].value == 9))) {
										category[i].disabled = false;
										category[1].checked  = true;
									}
									else if ((memAllow == 3) && ((category[i].value == 1) || (category[i].value == 8))) {
										category[i].disabled = false;
										category[2].checked  = true;
									}
									else {
										category[i].disabled = true;
										category[3].disabled = false;
										category[4].disabled = false;
										category[5].disabled = false;
										category[i].checked  = false;
									}
								}						
							}
						}
						
						function displayRates(x){
							var frequency = x;
							var thisForm = document.forms["#local.formName#"];
							var category = thisForm['category'];
							if ( frequency == 'Annually' ){
								$('.annualRates').show();

								category[3].disabled = false;
								category[4].disabled = false;
								category[5].disabled = false;
								category[6].disabled = false;
							}
						}
						
						$(document).ready(function(){
							mca_setupDatePickerField('date_bar');
							mca_setupDatePickerField('date_grad');
							mca_setupDatePickerField('date_birth');
							displayRates('Annually');
						});							
					</script>
					
					<div class="r i tsAppBodyText">*Denotes required field</div>
					<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#"  onsubmit="return checkCaptchaAndValidate();">
						<input type="hidden" name="isSubmitted" value="1" />
						<input type="hidden" name="memberID"  id="memberID" value="#session.cfcUser.memberData.memberID#" />
						<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
						<input type="checkbox" name="iAgree" id="iAgree" value="1" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
						<input type="hidden" name="useHistoryID" id="useHistoryID" value="0" />	
						<!--- =============================================================================================================================================== --->
						<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
						<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
							<div class="CPSection account_lookup">
								<div class="CPSectionTitle BB">#local.strPageFields.AccountLocatorTitle#</div>
								<div class="frmRow1" style="padding:10px;">
									<table cellspacing="0" cellpadding="2" border="0" width="100%">
										<tr>
											<td width="175" class="c">
												<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
													<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#local.strPageFields.AccountLocatorButton#</button>
												</div>
											</td>
											<td class="tsAppBodyText">
												#local.strPageFields.AccountLocatorInstructions#
											</td>
										</tr>
									</table>
								</div>
							</div>
						</cfif>
						<!--- =============================================================================================================================================== --->
						
						<div id="formToFill" style="display:none;">
						<!--- =============================================================================================================================================== --->
							<p class="tsAppBodyText">#local.strPageFields.TopFormContent#</p>
						<!--- CONTACT INFORMATION: ========================================================================================================================== --->
							<div class="CPSection">
								<div class="CPSectionTitle BB">Contact Information</div>
								<div class="tsAppBodyText frmRow1 frmText">
									<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td class="tsAppBodyText r">Name:</td>
											<td class="tsAppBodyText">
												<cfinput value="" name="name"  id="name" type="text" />
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">Firm Name:</td>
											<td class="tsAppBodyText">
												<cfinput value="" name="firm"  id="firm" type="text" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r">Address:</td>
											<td class="tsAppBodyText">
													<cfinput value="" name="address1"  id="address1" type="text" /><br>
													<cfinput value="" name="address2"  id="address2" type="text" />
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">City:</td>
											<td class="tsAppBodyText frmText">
												<cfinput value="" name="city"  id="city" type="text" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r">State:</td>
											<td class="tsAppBodyText">
												<cfinput value="" name="state"  id="state" type="text" />
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">Zip:</td>
											<td class="tsAppBodyText">
													<cfinput value="" name="zip"  id="zip" type="text" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r">Phone:</td>
											<td class="tsAppBodyText frmText">
												<cfinput value="" name="phone"  id="phone" type="text" />
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">E-mail:</td>
											<td class="tsAppBodyText">
												<cfinput value="" name="email"  id="email" type="text" />
											</td>
										</tr>	
										<tr class="frmRow1">
											<td class="tsAppBodyText r">Nickname for Badges:</td>
											<td class="tsAppBodyText"><input size="40" name="nickname" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">Birth Date:</td>
											<td class="tsAppBodyText"><cfinput value="" class="tsAppBodyText largeBox" name="date_birth" id="date_birth" type="text" validate="date" message="Please enter a valid Birth Date."  /> 
											<a href="javascript:mca_clearDateRangeField('date_birth');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a> (MM/DD/YYYY)
										</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r">Gender:</td>
											<td class="tsAppBodyText">
												<select class="tsAppBodyText" name="gender">
													<option value=""> - Please Select - </option>
													<cfloop list="Male, Female" index="i">
														<option value="#i#">#i#</option>
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">Date Admitted to Practice:</td>
											<td class="tsAppBodyText">
												<cfinput value="" class="tsAppBodyText largeBox" name="date_bar" id="date_bar" type="text" validate="date" message="Please enter a valid Date Admitted to Practice." onBlur="showYears();" /> 
												<a href="javascript:mca_clearDateRangeField('date_bar');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a> (MM/DD/YYYY)
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">&nbsp;</td>
											<td><i>(Required for Attorneys)</i></td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">KY Bar ID:</td>
											<td><input size="40" name="bar_ID" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r">Law Students Expected Grad. Date:</td>
											<td class="tsAppBodyText"><cfinput value="" class="tsAppBodyText largeBox" name="date_grad" id="date_grad" type="text" validate="date" message="Please enter a valid Expected Graduation Date." onBlur="showStuCat();" /> 
											<a href="javascript:mca_clearDateRangeField('date_grad');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a> (MM/DD/YYYY)
										</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">&nbsp;</td>
											<td><i>(Required for Law Students)</i></td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r">Law School:</td>
											<td class="tsAppBodyText">
												<select class="tsAppBodyText" name="lawSchool">
													<option value=""> - Please Select - </option>
													<cfloop array="#local.strLawSchools.columnValueArr#" index="local.thisOpt">
														<option value="#local.thisOpt.columnValueString#">#local.thisOpt.columnValueString# &nbsp;</option>
													</cfloop>
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r">KJA Sponsor:</td>
											<td class="tsAppBodyText"><input size="40" name="sponsor" type="text" value="" class="tsAppBodyText" onBlur="showSupportCat()" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="r">&nbsp;</td>
											<td><i>(Required for Non-attorney Support Staff Members)</i></td>
										</tr>
									</table>
								</div>
							</div>
						
						<!--- =============================================================================================================================================== --->
						<!--- MEMBERSHIP STATUS: ========================================================================================================================= --->
							<div class="CPSection">
								<div class="CPSectionTitle BB">* Membership Status</div>
								
								<div class="tsAppBodyText frmRow1 frmText">
									<!--- Annual Rates Table --->
									<table id="annualRates" cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td width="25" class="c"><input type="radio" value="3" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Attorney (Under 5 Years)</td>
											<td width="200" class="r b P annualRates">#DollarFormat(local.strFullDues["#local.attyUnder5YearsRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="2" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Attorney (5 to 10 Years)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.atty5YearsRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="1" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Attorney (10 Years and over)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.atty10YearsRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="4" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Judicial</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.judicialRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="5" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Non-attorney support staff</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.nonAttorneyRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="6" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Professional Affiliate Member (only Friends of KJA Sponsors are eligible for this category)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.proAffiliateRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="7" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Law Students (duration of law school)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.lawStudent3L#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="10" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Public Sector Attorney (Under 5 Years)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.attyPSUnder5YearsRateUID#"])#</td>
										</tr> 
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="9" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Public Sector Attorney (5 to 10 Years)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.attyPS5YearsRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="8" name="category" onClick="chkContribute()" disabled="true" /></td>
											<td>Public Sector Attorney (10 Years and over)</td>
											<td class="r b P annualRates">#DollarFormat(local.strFullDues["#local.attyPS10YearsRateUID#"])#</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									</table>
								</div>
							</div>	
						<!--- =============================================================================================================================================== --->
						<!--- SECTION PARTICIPATION: ========================================================================================================================= --->
							<div class="CPSection">
								<div class="CPSectionTitle BB">Political Information</div>
								<div class="tsAppBodyText subCPSectionArea1">It is often important to identify member's different legislative districts. We will not use this information for any reason except to ask you to contact legislators appropriately.<br />
								<br />
								Your response is greatly appreciated.</div>
								<div class="tsAppBodyText frmRow1 frmText">
									<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td class="tsAppBodyText l P" width="50%"><strong>Home Address</strong></td>
											<td class="tsAppBodyText l P" width="50%"><strong>Political Party Affiliation</strong></td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText l P"><input type="text" value="Address" name="homeAddress" onFocus="javascript:this.value ='';" /></td>
											<td class="tsAppBodyText l P">
												<select name="politicalParty">
													<option value=""> - Please Select - </option>
													<option value="Democrat">Democrat</option>
													<option value="Independent">Independent</option>
													<option value="Republican">Republican</option>
												</select>
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText l P"><input type="text" value="City" name="homeCity" onFocus="javascript:this.value ='';" /></td>
											<td class="tsAppBodyText l P">&nbsp;</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText l P">
												<cfselect name="homeStateID"  id="homeStateID" class="tsAppBodyText">
												<option value=""></option>
													<cfset local.currentCountryID = 0>
													<cfloop query="local.qryStates">
														<cfif local.qryStates.countryID neq local.currentCountryID>
															<cfset local.currentCountryID = local.qryStates.countryID>
															<optgroup label="#local.qryStates.country#">
														</cfif>
														<cfoutput>
															<option value="#local.qryStates.stateID#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
														</cfoutput>
														<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
															</optgroup>
														</cfif>
													</cfloop>
												</cfselect>
											</td>
											<td class="tsAppBodyText l P">&nbsp;</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText l P"><input type="text" value="Zip" name="homeZip" onFocus="javascript:this.value ='';" /></td>
											<td class="tsAppBodyText l P">&nbsp;</td>
										</tr>										
									</table>
								</div>
							</div>
						<!--- =============================================================================================================================================== --->
						<!--- CONTRIBUTE TO KJA: ========================================================================================================================= --->
							<div class="CPSection" id="divContribute" style="display:none;">
								<div class="CPSectionTitle BB">Contribute to KJA</div>
								<div class="tsAppBodyText subCPSectionArea1">This is a one time contribution.</div>
								<div class="tsAppBodyText frmRow1 frmText">
									<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td class="tsAppBodyText r PR BT"><input type="radio" value="1" name="contribution" class="tsAppBodyText" /></td>
											<td class="tsAppBodyText BT" width="50">$100.00</td>
										</tr>
										<tr class="frmRow2">
											<td class="tsAppBodyText r PR BT"><input type="radio" value="2" name="contribution" class="tsAppBodyText" /></td>
											<td class="tsAppBodyText BT" width="50">$200.00</td>
										</tr>
										<tr class="frmRow1">
											<td class="tsAppBodyText r PR BT"><input type="radio" value="3" name="contribution" class="tsAppBodyText" /></td>
											<td class="tsAppBodyText BT" width="50">$300.00</td>
										</tr>
									</table>
								</div>
							</div>

							<div class="CPSection">
								<div class="frmRow1 frmText" style="padding:10px;">
									#local.captchaDetails.htmlContent#
								</div>
							</div>
							<!--- BUTTONS: ====================================================================================================================================== --->					
							<div id="formButtons">
								<div style="padding:10px;">
									<div align="center" class="frmButtons">
										<input type="submit" value="Continue" id="btnSubmit" name="btnSubmit" class="tsAppBodyButton">
									</div>
								</div>
							</div>
							<!--- =============================================================================================================================================== --->					
						</div>
						<div id="BilledMemberExisting" name="BilledMemberExisting" style="display:none;">
							<div class="tsAppBodyText formClose">
								#local.strPageFields.BilledRenewalMessage#
							</div>
						</div>
						<div id="BilledJoinedMemberExisting" name="BilledJoinedMemberExisting" style="display:none;">
							<div class="tsAppBodyText formClose">
								#local.strPageFields.BilledJoinMessage#
							</div>
						</div>
						<div id="memberExisting" name="memberExisting" style="display:none;">
							<div class="tsAppBodyText formClose">
								#local.strPageFields.ActiveAcceptedMessage#
							</div>
						</div>
						<div id="formToFillSubmitting" style="display:none;">
							<i class="icon-spin icon-spinner icon-3x"></i> <b>Please wait while we process your application.</b>
						</div>
						<cfinclude template="/model/cfformprotect/cffp.cfm" />
					</cfform>
	
					<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
					</cfif>
				</cfif>
			</cfcase>
			
			<cfcase value="1">
				<cfif structKeyExists(event.getCollection(), 'iAgree') OR (NOT structKeyExists(session, "captchaEntered") AND (NOT Len(event.getValue('captcha',''))) OR application.objCustomPageUtils.validateCaptcha(code=event.getValue('captcha'),captcha=event.getValue('captcha_check')).response NEQ "success")>
					<cflocation url="#local.customPage.baseURL#&issubmitted=100" addtoken="no">
				</cfif>
				
				<cfif application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=event.getValue('memberID'), typeID=local.membershipDuesTypeID)>
					<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no">
				</cfif>

				<cfset local.showErrorMsg = false>
				<cfset local.membershipValue = event.getValue('category')>
				<cfset local.contactTypes = ''>
				<cfset local.useHistoryID = event.getValue('useHistoryID',0)>
				
				<cfif (local.membershipValue eq 1) OR (local.membershipValue eq 2) OR (local.membershipValue eq 3) OR	(local.membershipValue eq 11)>
					<cfset local.contactTypes = 'Attorney'>
				<cfelseif (local.membershipValue eq 4)>
					<cfset local.contactTypes = 'Judicial'>
				<cfelseif (local.membershipValue eq 5)>
					<cfset local.contactTypes = 'Legal Support'>
				<cfelseif (local.membershipValue eq 6)>
					<cfset local.contactTypes = 'Professional Affiliate'>
				<cfelseif (local.membershipValue eq 7)>
					<cfset local.contactTypes = 'Law Student'>
				<cfelseif (local.membershipValue eq 8) OR (local.membershipValue eq 9) OR (local.membershipValue eq 10)>
					<cfset local.contactTypes = 'Attorney,Public Sector'>
				</cfif>


				<!--- -------------------- --->
				<!--- UPDATE MEMBER RECORD --->
				<!--- -------------------- --->
				<cfscript>
				local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getValue('memberID'));
				local.objSaveMember.setMemberType(memberType='User');

				local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getTrimValue('homeAddress',''), city=arguments.event.getTrimValue('homeCity',''), stateID=arguments.event.getValue('homeStateID',0), postalCode=arguments.event.getTrimValue('homeZip',''));

				if (Len(arguments.event.getValue('bar_ID')) AND Len(arguments.event.getValue('date_bar')))
					local.objSaveMember.setProLicense(name='Licensed in Kentucky', status='Active', license=arguments.event.getTrimValue('bar_ID'), date=arguments.event.getTrimValue('date_bar'));

				local.objSaveMember.setCustomField(field='Salutation', value=arguments.event.getTrimValue('nickname',''));
				local.objSaveMember.setCustomField(field='Date of Birth', value=arguments.event.getTrimValue('date_birth',''));
				local.objSaveMember.setCustomField(field='Bar Date', value=arguments.event.getTrimValue('date_bar',''));
				local.objSaveMember.setCustomField(field='Law School End Date', value=arguments.event.getTrimValue('date_grad',''));
				local.objSaveMember.setCustomField(field='Sponsor', value=arguments.event.getTrimValue('sponsor',''));
				local.objSaveMember.setCustomField(field='Political Party', value=arguments.event.getTrimValue('politicalParty',''));
				local.objSaveMember.setCustomField(field='Gender', value=arguments.event.getTrimValue('gender',''));
				local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypes);

				local.strResult = local.objSaveMember.saveData();
				local.memberNumber = local.strResult.membernumber;
				</cfscript>
				<cfset local.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=local.siteID, parentCode='MemberApp', subName='Completed')/>

				<cfset application.objCustomPageUtils.mh_updateHistory(memberID=local.useMID, historyID=local.useHistoryID, 
					subCategoryID=local.qryHistoryCompleted.subCategoryID, description='Member completed join form.', 
					newAccountsOnly=false)>	
					
				<cfif NOT local.strResult.success>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
				</cfif>


				<!--- ---------- --->
				<!--- PREP EMAIL --->
				<!--- ---------- --->
				<cfset local.lawStudentCategory = 7>
				<!--- if category == 7, law student, then contribution not allowed, even if passed in --->
				<cfset local.membershipValue = arguments.event.getValue('category')>
				<cfset local.rateFrequency = 'Annually'>
				<cfset local.memberShipName = ''>
				<cfset local.memberShipPrice = 0>
				<cfset local.topSubUID = ''>
				<cfset local.topRateUID = ''>
				<cfif (local.membershipValue eq '1')>
					<cfset local.memberShipName = 'Attorney (10 Years and over)'>
					<cfif local.rateFrequency eq 'Annually'>
						<cfset local.memberShipPrice = local.strFullDues["#local.atty10YearsRateUID#"]>
					</cfif>
					<cfset local.topSubUID = local.attySubUID>
					<cfset local.topRateUID = local.atty10YearsRateUID>
				<cfelseif (local.membershipValue eq '2')>
					<cfset local.memberShipName = 'Attorney (5 to 10 Years)'>
					<cfif local.rateFrequency eq 'Annually'>
						<cfset local.memberShipPrice = local.strFullDues["#local.atty5YearsRateUID#"]>
					</cfif>
					<cfset local.topSubUID = local.attySubUID>
					<cfset local.topRateUID = local.atty5YearsRateUID>
				<cfelseif (local.membershipValue eq '3')>
					<cfset local.memberShipName = 'Attorney (Under 5 Years)'>
					<cfif local.rateFrequency eq 'Annually'>
						<cfset local.memberShipPrice = local.strFullDues["#local.attyUnder5YearsRateUID#"]>
					</cfif>
					<cfset local.topSubUID = local.attySubUID>
					<cfset local.topRateUID = local.attyUnder5YearsRateUID>
				<cfelseif (local.membershipValue eq '4')>
					<cfset local.memberShipName = 'Judicial'>
					<cfset local.memberShipPrice = local.strFullDues["#local.judicialRateUID#"]>
					<cfset local.topSubUID = local.judicialSubUID>
				<cfelseif (local.membershipValue eq '5')>
					<cfset local.memberShipName = 'Non-attorney support staff'>
					<cfset local.memberShipPrice = local.strFullDues["#local.nonAttorneyRateUID#"]>
					<cfset local.topSubUID = local.supportSubUID>
				<cfelseif (local.membershipValue eq '6')>
					<cfset local.memberShipName = 'Professional Affiliate Member'>
					<cfset local.memberShipPrice = local.strFullDues["#local.proAffiliateRateUID#"]>
					<cfset local.topSubUID = local.affiliateSubUID>
				<cfelseif (local.membershipValue eq '7')>
					<cfset local.memberShipName = 'Law Student'>
					<cfset local.memberShipPrice = local.strFullDues["#local.lawStudent3L#"]>
					<cfset local.topSubUID = local.lawStudentSubUID>
					<cfif isDate(arguments.event.getValue('date_grad', Now()))>
						<cfset local.years = DateDiff("yyyy", Now(), arguments.event.getValue('date_grad', Now()))>
					<cfelse>
						<cfset local.years = 0>	
					</cfif>
					<cfset local.topRateUID = local.lawStudent3L>
					<cfset local.memberShipPrice = local.strFullDues["#local.lawStudent3L#"]>					
					<cfif local.years LTE 1>
						<cfset local.topRateUID = local.lawStudent1L>
						<cfset local.memberShipPrice = local.strFullDues["#local.lawStudent1L#"]>					
					<cfelseif local.years GT 1 AND local.years LT 2>
						<cfset local.topRateUID = local.lawStudent2L>
						<cfset local.memberShipPrice = local.strFullDues["#local.lawStudent2L#"]>					
					</cfif>
				<cfelseif (local.membershipValue eq '8')>
					<cfset local.memberShipName = 'Public Sector Attorney (10 Years and over)'>
					<cfif local.rateFrequency eq 'Annually'>
						<cfset local.memberShipPrice = local.strFullDues["#local.attyPS10YearsRateUID#"]>
					</cfif>
					<cfset local.topSubUID = local.attySubUID>
					<cfset local.topRateUID = local.attyPS10YearsRateUID>
				<cfelseif (local.membershipValue eq '9')>
					<cfset local.memberShipName = 'Public Sector Attorney (5 to 10 Years)'>
					<cfif local.rateFrequency eq 'Annually'>
						<cfset local.memberShipPrice = local.strFullDues["#local.attyPS5YearsRateUID#"]>
					</cfif>
					<cfset local.topSubUID = local.attySubUID>
					<cfset local.topRateUID = local.attyPS5YearsRateUID>
				<cfelseif (local.membershipValue eq '10')>
					<cfset local.memberShipName = 'Public Sector Attorney (Under 5 Years)'>
					<cfset local.memberShipPrice = local.strFullDues["#local.attyPSUnder5YearsRateUID#"]>
					<cfset local.topSubUID = local.attySubUID>
					<cfset local.topRateUID = local.attyPSUnder5YearsRateUID>
				</cfif>
				<cfset local.totalAmount = local.memberShipPrice>

				<cfset local.sectionCount = ListLen(arguments.event.getValue('sectionP',''))>
				<cfset local.sectionAmount = local.sectionCount * 25.00>
				<cfset local.totalAmount = local.totalAmount + local.sectionAmount>

				<cfset local.contributionAmt = 0>
				<cfif (arguments.event.getValue('contribution', 0) neq 0) AND (local.membershipValue neq local.lawStudentCategory)>
					<cfif arguments.event.getValue('contribution') eq 1>
						<cfset local.contributionAmt = 100>
					<cfelseif arguments.event.getValue('contribution') eq 2>
						<cfset local.contributionAmt = 200>
					<cfelseif arguments.event.getValue('contribution') eq 3>
						<cfset local.contributionAmt = 300>
					</cfif>
				</cfif>
				<cfset local.totalAmount = local.totalAmount + local.contributionAmt>

				<cfsavecontent variable="local.invoice">				
					#local.pageCSS#
					<!-- @msg@ -->
					<!-- @profile_1.ccResponse@ -->
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
					<tr class="frmRow1"><td class="frmText b">Name:</td><td class="frmText">#arguments.event.getValue('name')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Firm Name:</td><td class="frmText">#arguments.event.getValue('firm')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">Address:</td><td class="frmText">
						#arguments.event.getValue('address1')#&nbsp;
						<cfif arguments.event.getValue('address2') NEQ "">
							<br>#arguments.event.getValue('address2')#
						</cfif>
						</td>
					</tr>
					<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#arguments.event.getValue('city')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#arguments.event.getValue('state')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#arguments.event.getValue('zip')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">Phone:</td><td class="frmText">#arguments.event.getValue('phone')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">E-mail:</td><td class="frmText">#arguments.event.getValue('email')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">Nickname for Badges:</td><td class="frmText">#arguments.event.getValue('nickname')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Birth Date:</td><td class="frmText">#arguments.event.getValue('date_birth')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Date Admitted to Practice:</td><td class="frmText">#arguments.event.getValue('date_bar')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">KY Bar ID:</td><td class="frmText">#arguments.event.getValue('bar_ID')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Law Students Expected Grad. Date:</td><td class="frmText">#arguments.event.getValue('date_grad')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">KJA Sponsor:</td><td class="frmText">#arguments.event.getValue('sponsor')#&nbsp;</td></tr>
						
					<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP STATUS</td></tr>
					<tr class="frmRow1"><td class="frmText b">#local.memberShipName#</td><td class="frmText">#dollarFormat(local.memberShipPrice)#&nbsp;<cfif local.memberShipName eq 'Law Student'>duration of law school</cfif></td></tr>
						
					<tr><td colspan="2">&nbsp;</td></tr>

					<tr class="msgHeader"><td colspan="2" class="b">POLITICAL INFORMATION</td></tr>
					<tr class="frmRow1"><td class="frmText b">Home Address:</td><td class="frmText">#arguments.event.getValue('homeAddress','')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Home Address City:</td><td class="frmText">#arguments.event.getValue('homeCity','')#&nbsp;</td></tr>
					<tr class="frmRow1"><td class="frmText b">Home Address State:</td><td class="frmText">#arguments.event.getValue('homeState','')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Home Address Zip:</td><td class="frmText">#arguments.event.getValue('homeZip','')#&nbsp;</td></tr>
					<tr class="frmRow2"><td class="frmText b">Political Party:</td><td class="frmText">#arguments.event.getValue('politicalParty','')#&nbsp;</td></tr>
					
					<tr><td colspan="2">&nbsp;</td></tr>

					<tr class="msgHeader"><td colspan="2" class="b">CONTRIBUTE TO KJA</td></tr>
					<tr class="frmRow1"><td class="frmText b">Contribution Subtotal:</td><td class="frmText">#dollarFormat(local.contributionAmt)#&nbsp;</td></tr>
					
					<tr><td colspan="2">&nbsp;</td></tr>
					
					<tr class="frmRow1"><td class="frmText b">TOTAL:</td><td class="frmText">#dollarFormat(local.totalAmount)#&nbsp;</td></tr>
					
					<tr><td colspan="2">&nbsp;</td></tr>
					</table>
				</cfsavecontent>
				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & arguments.event.getValue('name','')>


				<!--- ------------- --->
				<!--- SUBSCRIPTIONS --->
				<!--- ------------- --->
				<cfset local.showErrorMsg = false>
				<cfset local.subStruct = structNew()>
				<cfset local.subStruct.children = arrayNew(1)>

				<cfset local.subStruct.uid = local.topSubUID>
				<cfif len(local.topRateUID) gt 0>
					<cfset local.subStruct.rateUID = local.topRateUID>
				</cfif>
				<cfset local.subStruct.overridePerms = true>
					
				<cfif local.rateFrequency eq 'Annually'>
					<cfset local.subStruct.freqUID = local.annualFrequencyUID>
				</cfif>
					
				<cfif (arguments.event.getValue('contribution', 0) neq 0) AND (local.membershipValue neq local.lawStudentCategory)>
					<cfset local.contributionRateUID = ''>
					<cfif arguments.event.getValue('contribution') eq 1>
						<cfset local.contributionRateUID = local.sustainingDues100RateUID>
					<cfelseif arguments.event.getValue('contribution') eq 2>
						<cfset local.contributionRateUID = local.sustainingDues200RateUID>
					<cfelseif arguments.event.getValue('contribution') eq 3>
						<cfset local.contributionRateUID = local.sustainingDues300RateUID>
					</cfif>
					
					<cfif len(local.contributionRateUID) gt 0>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = local.sustainingDuesSubUID>
						<cfset local.childStruct.rateUID = local.contributionRateUID>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfif>
				</cfif>
					
				<!--- Remove child struct if nothing exists, otherwise errors --->
				<cfif structKeyExists(local.substruct, "children") and ArrayLen(local.substruct.children) eq 0>
					<cfset StructDelete(local.substruct,"children")>
				</cfif>

				<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
				<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=local.useMID, subStruct=local.subStruct, newAsBilled=true)>

				<cfif local.subReturn.success eq false>
					<!--- email association ----------------------------------------------------------------------------------------- --->
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							The system was unable to create the subscriptions for this application and #event.getValue('name','')# was not sent an email confirmation.<br />
							<hr />
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(local.ORGEmail.to.replaceAll(',',';'),';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>
					<cfif arrayLen(local.arrEmailTo)>
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle=local.formNameDisplay,
							emailhtmlcontent=local.mailContent,
							siteID=local.siteID,
							memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						)>
					</cfif>
										
					<cfset local.showErrorMsg = true>
					<cfset local.errCode = 3>
				<cfelse>				
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDirectLinkCode">
						select directLinkCode
						from dbo.sub_subscribers
						where subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
					</cfquery>

					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.siteCode)>
					<cfset local.uid = createuuid()>
					<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
						<cfoutput>
							<html><body>#local.invoice#</body></html>
						</cfoutput>
					</cfdocument>			
					
					<cfset local.strPDF = structNew()>
					<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
					<cfset local.strPDF['serverFile'] = "Membership_#local.memberNumber#_#DateFormat(now(),'m-d-yyyy')#.pdf">
					<cfset local.emailAttachFile = "Membership_#DateFormat(now(),'m-d-yyyy')#.pdf">
					<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
					<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
					
					<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=local.useMID,strPDF=local.strPDF,siteID=local.siteID)>

				<!---	<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
					<cfset local.objSection = CreateObject("component","model.system.platform.section")>

					<cfset local.newFile = { serverDirectory=local.strPDF.serverDirectory, clientFile=local.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=local.strPDF.serverFile, serverFileExt='pdf' } >
					<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=local.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
					<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(local.orgDefaultSiteCode).memberAdminSiteResourceID>

					<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=local.orgDefaultSiteID, 
						resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, 
						docTitle="Membership_#local.memberNumber#_ #DateFormat(now(),'m-d-yyyy')#", docDesc="Membership_#local.memberNumber#_ #DateFormat(now(),'m-d-yyyy')#",
						author='', fileData=local.newFile, isActive=1, isVisible=true, contributorMemberID=local.useMID, recordedByMemberID=local.useMID, oldFileExt='pdf')>

					<cfset local.objMemberAdmin.saveMemberDocument(memberID=local.useMID, documentID=local.insertResults.documentID)>---->

					<!--- email member ---------------------------------------------------------------------------------------------- --->
					<cfset local.emailSentToUser = TRUE />
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>Thank you for submitting your application!<br>
							If you have not done so already, you may confirm your application and pay for it <a href="#local.thisScheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#">here</a>.<br>
							If the link does not work for you, please copy the following link and paste into the address bar of your browser:<br>
							#local.thisScheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#</p>	
							<hr />
							#local.invoice#	
						</cfoutput>
					</cfsavecontent>
					
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from },
						emailto=[
							{ name="", email=local.memberEmail.to }
						],
						emailreplyto=local.ORGEmail.to,
						emailsubject=local.memberEmail.subject,
						emailtitle = "#local.formNameDisplay#",
						emailhtmlcontent = local.mailContent,
						siteID = local.siteID,
						memberID = local.useMID,
						messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID = this.siteResourceID
					)/>
					
					<cfset  local.emailSentToUser = local.responseStruct.success>

					<!--- email association ----------------------------------------------------------------------------------------- --->
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<cfif NOT local.emailSentToUser>
								#event.getValue('name')# was not sent email confirmation due to bad Data.<br />
								Please contact, and let them know.
								Their confirmation link is: #local.thisScheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#
								<hr />
							</cfif>
							The member's information was not saved to control panel. Please review and update as needed.
							<br />
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(local.ORGEmail.to.replaceAll(',',';'),';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>
					<cfif arrayLen(local.arrEmailTo)>
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle=local.formNameDisplay,
							emailhtmlcontent=local.mailContent,
							siteID=local.siteID,
							memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						)>
					</cfif>
					<script type="text/javascript">
						setTimeout(function(){
							window.location = "/renewsub/#local.qryDirectLinkCode.directLinkCode#";
						}, 3000);	
					</script>
				</cfif>	

				<cfif local.showErrorMsg>
					<cfoutput>
						There was an error processing your application.  Please contact your association for assistance.
					</cfoutput>
				</cfif>
			</cfcase>
			
			<cfcase value="99">
				<div>There was an error processing your application.  Please contact your association for assistance.</div>
			</cfcase>
			<cfcase value="100">
				<div>Error! you Can't Post Here.</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>