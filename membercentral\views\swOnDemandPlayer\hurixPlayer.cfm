<cfif structKeyExists(application.objCMS,"getPlatformCacheBusterKey")>
    <cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfelse>
    <cfset local.assetCachingKey = "">
</cfif>
<cfoutput>
<!DOCTYPE html>
<html>
    <head>
        <cftry>
            #application.objCMS.getFrontendErrorTrackingCode()#
            <cfcatch type="any"></cfcatch>
        </cftry>
        <title>SeminarWeb</title>
        <base href="">
        <meta http-equiv="x-ua-compatible" content="IE=edge" />
        <meta charset="UTF-8" />
        <meta property="metainf" name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <!-- ...> no-cache meta tags -->
        <meta http-equiv='cache-control' content='no-cache'>
        <meta http-equiv='expires' content='0'>
            <!-- <meta http-equiv='expires' content='-1'> -->
        <meta http-equiv='pragma' content='no-cache'>
        <!-- <... no-cache meta tags -->

        <!-- <link rel="icon" href="favicon.ico" type="image/gif"> -->
        <cfset local.swodPath = '/assets/common/swodplayer/'>
        
        <!-- ...> activity css files include start -->
        <link rel="stylesheet" href="#local.swodPath#css/jquery-ui.css#local.assetCachingKey#" type="text/css" />
        <link rel="stylesheet" href="#local.swodPath#css/jquery-te-1.4.0.css#local.assetCachingKey#" type="text/css" />
        <link rel="stylesheet" href="#local.swodPath#css/activity.css#local.assetCachingKey#" type="text/css" />
        <link rel="stylesheet" href="#local.swodPath#css/foundation.css#local.assetCachingKey#" type="text/css" />
        <link rel="stylesheet" href="#local.swodPath#css/style.css#local.assetCachingKey#" type="text/css" />
        <link rel="stylesheet" href="#local.swodPath#libs/videoJS/css/video-js.css#local.assetCachingKey#" type="text/css" />
        <link rel="stylesheet" href="#local.swodPath#libs/videoJS/css/fantasy/index.css#local.assetCachingKey#" type="text/css" />
        <!-- <... activity css files include ended -->

        <!-- ...> activity lib files include start -->
        <script type="text/javascript" src="#local.swodPath#libs/jquery-2.1.3.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/jquery-ui.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/jquery-migrate.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/jquery-te-1.4.0.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/TweenMax.min.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/jquery.ui.touch-punch.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/jqueryExtendView.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/imagePreload.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/browserDetect.js#local.assetCachingKey#"></script>

        <script type="text/javascript" src="/assets/common/javascript/noty/3.1.4/noty.min.js#local.assetCachingKey#"></script>
        <link href="/assets/common/javascript/noty/3.1.4/noty.css#local.assetCachingKey#" rel="stylesheet">

        <script type="text/javascript" src="#local.swodPath#libs/scaling.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/videoJS/js/video.min.js#local.assetCachingKey#"></script>
        <!-- If you'd like to support IE8 (for Video.js versions prior to v7) -->
        <!-- <script type="text/javascript" src="#local.swodPath#libs/videoJS/js/videojs-ie8.min.js#local.assetCachingKey#"></script> -->
        <!-- <... activity lib files include ended -->

        <!-- ...> activity angular files include start -->
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular-aria.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular-resource.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular-route.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular-cookies.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular-sanitize.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/angular/angular-touch.js"></script>
        <script type="text/javascript" src="#local.swodPath#libs/webfont.js#local.assetCachingKey#"></script>

        <script type="text/javascript" src="#local.swodPath#libs/foundation.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#libs/foundation.topbar.js#local.assetCachingKey#"></script>
        <!-- <... activity angular files include ended -->
        <script type="text/javascript" src="/assets/common/javascript/clipboard.js/1.7.1/clipboard.min.js"></script>	

        <!-- ...> activity .js files include start -->
        <script type="text/javascript" src="#local.swodPath#js/0_modul.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/imagePaths.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/1_services.js#local.assetCachingKey#"></script>
		<script type="text/javascript" src="#local.swodPath#js/preloderService.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/api.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/videoService.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/api.resource.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/api.service.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/timer.service.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/seminardata.service.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/promtTimerService.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/postTestTimer.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/AppManager.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/2_filters.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/3_directives.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/4_mainController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/5_videoController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/6_sideBarController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/7_noteController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/8_learnController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/9_orscController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/10_qaController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/11_cmpltController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/12_helpController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/syncEditorController.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/custom.directive.js#local.assetCachingKey#"></script>

        <link rel="stylesheet" type="text/css" href="/assets/common/images/font-awesome/7.0.1/css/all.min.css" onerror="MCJSErrorReporting.cssLoadError(event)">
        <script>
            //Learning material type - accessing from URL(?videopaper | audiopaper | largevideo)
            var tempMaterialType = window.location.search.split("?")[1];
            if(tempMaterialType == undefined){
                tempMaterialType = "videopaper";
            }

            var queryStr = parseQuery(window.location.search.split("?")[1])
            var seminarID = queryStr.seminarID;
            var enrollmentID = queryStr.enrollmentID;
            var orgCode = queryStr.orgCode;
            var seminarType = queryStr.seminarType;
            var meetingUrlSWL = '';
            var uniqueCode = queryStr.uniqueCode;
            var tempMaterialType = queryStr.type;             
			var #toScript(session.cfcuser.superuser, "userType")#; 
            var swlMobileActive = 0;
			
            function parseQuery(qstr) {
                var query = {};
                var a = qstr.split('&');
                for (var i = 0; i < a.length; i++) {
                    var b = a[i].split('=');
                    query[decodeURIComponent(b[0])] = decodeURIComponent(b[1] || '');
                }
                return query;
            }

            jQuery(document).ready(function(){
                // block right-clicks on video elements
                jQuery('body').on('contextmenu', 'video', function() {return false; });
                $(document).on('click', 'body', function(event) {
                    if($(event.target).parents('##content_Complete').length == 0){                        
                        hideReqTitle();
                    }
                });                
            });


			<cfif NOT application.objPlatform.hasMinimumOSVersion("iphone|10,ipad|9,ipod|10")>
				alert("For tracking purposes, in order to take this course on this mobile device, it must be on iOS 10 or higher. Once you have performed the software update, you may return to this page and enter the program.");
				window.location.href = "/?pg=semwebCatalog&panel=My";
			<cfelseif structKeyExists(session.mcstruct.deviceProfile,"browser_name") and structKeyExists(session.mcstruct.deviceProfile,"browser_version") and session.mcstruct.deviceProfile.browser_name eq 'Chrome' and listFind("76,77",session.mcstruct.deviceProfile.browser_version)>
				alert("Please upgrade your browser to the latest version of Google Chrome. Your current version has a bug affecting video playback.");
				window.location.href = "/?pg=semwebCatalog&panel=My";
            </cfif>
            
            $(function() {
                if (isIEBrowser()){	
                    popupText = '<div class="MC-IEmsg row-fluid">Internet Explorer is no longer supported. Please update to Edge or use an alternative browser such as Chrome or Firefox for best viewing results.</div>';
                    new Noty({
                        type:'success',
                        layout:'topLeft', 
                        theme:'bootstrap-v4', 
                        text:popupText,
                        timeout:false,
                        callbacks: {
                            beforeShow: function() {console.log(this)},
                            onShow: function() {},
                            afterShow: function() {},
                            onClose: function() {},
                            afterClose: function() {},
                            onHover: function() {},
                            onTemplate: function() {
                                this.layoutDom.className = 'noty_layout MC-IEmsgWrapper';
                                this.barDom.classList.add("MC-IEmsgInnerWrapper");
                                this.barDom.innerHTML = '<div class="MC-IEmsgBody noty_body">' + this.options.text + '<div>';
                                // Important: .noty_body class is required for setText API method.
                            }
                        }
                    }).show();

                    $("a,button,input[type=button],input[type=submit]").click(function (e) {
                        e.preventDefault();
                        $(this).removeAttr('onclick').removeAttr('href');
                    });
                    $('div').removeAttr('ng-click');
                }

                $(document).on('click','.sideTogglerIcon',function(){
                    if($('##nav-bar').hasClass('expand')){
                        $('##main-box').removeClass('shrink');
                        $('##nav-bar').removeClass('expand');
                        $('.chapters').removeClass('display');
                    } else {
                        $('##nav-bar').addClass('expand');
                        $('##main-box').addClass('shrink');
                        setTimeout(function(){$('.chapters').addClass('display');},200);

                    }
                    $('##sideToggler').children('i').toggleClass('fa-circle-chevron-right');
                    $('##sideToggler').children('i').toggleClass('fa-circle-chevron-left');
                    
                    if ($('##nav-bar').is(':visible')) {
                        $('body').addClass("fixed-position");
                    } else {
                        $('body').removeClass("fixed-position");
                    }
                });
                

                $(document).on('click','##outline-btn',function(){
                    $('##outline-sec').show();
                    $('##notes-sec').hide();
                    $('##leftBox .nav-item').removeClass('active');
                    $(this).parent().addClass('active');
                });
                $(document).on('click','##notes-btn',function(){
                    $('##outline-sec').hide();
                    $('##notes-sec').show();
                    $('##leftBox .nav-item').removeClass('active');
                    $(this).parent().addClass('active');
                });
                $(document).on('click','##viewModeBtn',function(){
                    var _this = $(this);
                    var _label =  _this.text().trim();
                    angular.element('##viewModeBtn').hide();
                    _this.html('<i class="fa-solid" aria-hidden="true"></i> '+_this.data('label'));
                    
                    _this.data('label',_label);
                    if(_label == 'View Wide Screen'){
                        _this.children('i').removeClass('fa-expand-wide').addClass('fa-compress-wide');
                        $('##right,.playerWell').addClass('wideScreen');
                        $('##leftBox').addClass('hide');
                        angular.element('.mediaSyncWrap').hide();		
				        angular.element('.syncWrapCnt').hide();	
                        angular.element('##right > div.small-12').removeClass('large-6').addClass('large-12');
                    }
                    else{
                        _this.children('i').removeClass('fa-compress-wide').addClass('fa-expand-wide');
                        $('##right,.playerWell').removeClass('wideScreen');
                        $('##leftBox').removeClass('hide');
                    }
                    
                    $('##right ##video-controller,##right .screens').removeClass('large-6');
                    $('##right.wideScreen ##video-controller,##right.wideScreen .screens').addClass('large-6');

                    $('##right .screens .screen.ng-hide').hide();
                    $('##right').removeClass('fullWidth');
                    if(angular.element('##video-controller').attr('type') == 'large_video'){
                        $('##right.wideScreen').addClass('fullWidth');
                    }

                    angular.element(window).trigger('resize');
                });

                
                setTimeout(function(){scrollFunction();},1500);
            });
            function exitWideScreen(){
                var _label =  'Exit Wide Screen';
                $('##viewModeBtn').html('<i class="fa-solid fa-expand-wide" aria-hidden="true"></i> View Wide Screen');
                $('##viewModeBtn').data('label',_label);
                $('##leftBox').removeClass('hide');
                $('##right,.playerWell').removeClass('wideScreen');
                $('##right,.playerWell').removeClass('fullWidth');
                $('##right ##video-controller,##right .screens').removeClass('large-6');

               // $('##right .screens .screen:not(.ng-hide').hide();
                angular.element(window).trigger('resize');
            }
            function isIEBrowser() {
                ua = navigator.userAgent;
                /* MSIE used to detect old browsers and Trident used to newer ones*/
                var is_ie = ua.indexOf("MSIE ") > -1 || ua.indexOf("Trident/") > -1;
                return is_ie; 
            }
            function scrollFunction(){
                var _obj;
                if($('##outline-content')[0] != undefined && $('##outline-content').is(':visible')){
                    _obj = $('##outline-content');
                } else if($('.materials')[0] != undefined && $('.materials').is(':visible')){
                    _obj = $('.materials');
                }
                if($(_obj).length){
                    var clientHeight = $(_obj)[0].clientHeight;
                    var windowHeight = $(_obj)[0].scrollHeight - $(_obj)[0].scrollTop;
                    var scrollPos = windowHeight - clientHeight;
                    if(scrollPos < 5) _obj.next('##scrollArrow').hide('slow'); else _obj.next('##scrollArrow').show('slow');
                    if($('.materials')[0] != undefined && $('.materials').is(':visible')){
                        angular.element('##emailmat').removeClass('displayHide');
                    }
                }
            }
        </script>
        <!-- <... activity .js files include ended -->
        <style>
            .MC-IEmsgWrapper{
                width: 100%!important;
                top: 0!important;
                left: 0!important;
                right: 0!important;
                max-width: 100%!important;
                height: 100%;
            }
            .MC-IEmsgInnerWrapper.noty_bar{
                margin:0!important;
                border-radius: 0!important;
                position:relative!important;
                padding:0!important;
            }
            .MC-IEmsgBody.noty_body{
                background-color: ##FF0000;
                color: ##FFFFFF;
                font-family: Arial;
                text-align: center;
                font-weight:bold;
                height: 50px;
                padding:0px!important;
            }
            .MC-IEmsg{
                line-height:52px!important;
            }

            /**********************/
            /*custom begin */
            :root {
                /*--header-height: 3rem;*/
                --nav-width:10%;
                --first-color: ##FFFFFF;
                --bg-color: ##f9f9f9;
                --tab-sel-color: ##e0dcdc;
                /*--first-color-light: ##afa5d9;
                --white-color: ##f7f6fb;
                --body-font: "Nunito", sans-serif;
                --normal-font-size: 1rem;*/
                --z-fixed: 100;
                --primary-color: #attributes.data.strAssociation.qryAssociation.brandPlayerPrimaryTextColor#;
                --secondary-color:#attributes.data.strAssociation.qryAssociation.brandPlayerSecondaryTextColor#;
                --heading-bgcolor:#attributes.data.strAssociation.qryAssociation.brandPlayerHeadingColor#;
                --button-color:#attributes.data.strAssociation.qryAssociation.brandPlayerButtonColor#;
                --button-hover-color:#attributes.data.strAssociation.qryAssociation.brandPlayerButtonHoverColor#;
                --button-txtcolor:#attributes.data.strAssociation.qryAssociation.brandPlayerButtonTextColor#;
                --button-hover-txtcolor:#attributes.data.strAssociation.qryAssociation.brandPlayerButtonHoverTextColor#;
                --box-border-radius:12px;
                --small-border-radius:7px;
                --border-grey:2px solid lightgrey;
            }
            body,.jqte *,.jqte .jqte_tool_icon,##activity > .patch > .content .saveExit .content,	.mobile-bottom-bar .footer-link,.syncIcons a,.syncIcons i,.syncRowIcons a,.syncRowIcons i{
                color:var(--primary-color);
            }
            ##leftBox .orsc .materials .top,##leftBox .help .accordion .top,##right .screen.orsc .materials .top,##right .screen.help .accordion .top,.Synccontent .heading,.Synccontent thead,.tblSync thead tr th,##right .screen.qa .qaScreen .askQa .top,##right .screen.qa .qaScreen .prevQa .top,##leftBox .qa .qaScreen .askQa .top,##leftBox .qa .qaScreen .prevQa .top,##lm-popup-panel .top{
                color: var(--secondary-color)!important;
                background-color: var(--heading-bgcolor)!important;
            }
            ##complete-controller .top,.evaluationScreen ##formtitle, ##certificateTitle,.cmpltScreen .heading{
                padding-left: 0 !important;
            }

            .buttonColors{background-color: var(--button-color)!important;color:var(--button-txtcolor)!important;border-radius: var(--small-border-radius);}
            .buttonColors i,.buttonColors .lable{color:var(--button-txtcolor)!important;}
            .buttonColors:hover{background-color: var(--button-hover-color)!important;color:var(--button-hover-txtcolor)!important;}
            .buttonColors:hover i,.buttonColors:hover .lable{color:var(--button-hover-txtcolor)!important;}
            ##wrapper{max-width:100%;}
            ##main-box{padding-left: max(110px,calc(var(--nav-width) + 2rem)); padding-right: 45px;max-width: 2000px;margin: 0 auto;}
            ##main-box{
                height: 100%;
                align-items: center;
                justify-content: center;
            }
            .l-navbar {
                position: fixed;
                top: 0;
                width: var(--nav-width);
                height:100%;
                background-color: var(--first-color);
                padding: 0;
                transition: width linear 0.15s;
                z-index: 999;
                min-width:100px;
                display: flex;
            }
            .nav {
                height: auto;
                display: flex;
                flex-direction: column;
                justify-content: start;
                overflow: hidden;
            }
            .nav_link {
                position: relative;
                color: var(--first-color-light);
                margin-bottom: 1.5rem;
                transition: 0.3s;
            }
            .nav .bottom .videoPpt.select {background-color: var(--bg-color);border-radius: 9px;}
            ##sections .tab:hover,{
                background-color: var(--bg-color);
                border-radius: var(--small-border-radius);
            }
            ##sections .tabSelected, .nav-item.active,.mobile-bottom-bar .tabSelected{
                background-color: var(--tab-sel-color);
                border-radius: var(--small-border-radius);
            }
            ##bottom{margin-left:auto;border-radius: var(--box-border-radius);}
            .playerWell{height:100%;background-color:##f1eeee;border-radius:var(--box-border-radius);padding:10px;display:flex;width:100%;border: 2px solid ##d3d3d3;}
            .cmpltPrcent {height: 20px; font-size:1em; padding: 2px; border-radius: var(--small-border-radius); font-weight:900;}
            .cmpltPrcent.success{color: darkgreen;}
            ##note-panel.note > .content {
                border: var(--border-grey);
                border-radius: var(--box-border-radius);
            }
            ##note-panel.note .bottom .content,##note-panel.note .bottom{border-radius: var(--box-border-radius);}
            .jqte_toolbar{   bottom: 0;   width: 100%;background: none; border: none;}
            .jqte_editor{   border-radius: var(--small-border-radius);}
            .jqte_focused .jqte_editor{border:1px solid red;}
            .jqte_tool, .jqte_tool_icon, .jqte_tool_label{border-radius: var(--small-border-radius);-webkit-border-radius:var(--small-border-radius);}
            
            ##right .screen{background-color:##FFFFFF; border-top: var(--border-grey);  border-left: var(--border-grey); border-bottom: var(--border-grey); border-right: var(--border-grey);    border-radius: var(--box-border-radius);}
            
            ##right .screen.qa .qaScreen .elements > .content,##leftBox .screen.qa .qaScreen .elements > .content {border:1px solid lightgrey;}
            .vjs-theme-fantasy .vjs-control-bar{border-radius: var(--box-border-radius);}
            ##leftBox .screen.qa .qaScreen .prevQa .bottom .qaBox, ##leftBox .screen.qa .qaScreen .askQa .bottom .textArea,##leftBox .screen.qa .qaScreen .askQa .bottom textArea{border-radius:var(--box-border-radius); }
            .materials a{color:var(--primary-color);text-decoration:underline;}
            .mobile-bottom-bar{border-top: var(--border-grey);}
            @media screen and (max-height:1024px){
                ##main-box{max-height: calc(100% - 60px);}
            }
            @media screen and (max-width:1200px) and (max-height:700px){
                .nav-inner{max-height:80%;}
                ##sections{max-height:80%;overflow-y:scroll;}
            }
            @media screen and (max-height:700px){
                .saveExit{ font-size: 0.8em!important;}
                ##main-box{max-height: calc(100% - 60px);}
            }
            @media screen and (max-height:700px){
                .nav-inner{max-height:80%;}
                ##sections{max-height:80%;overflow-y:scroll;}
                .icon-box .lable{font-size: .8em;}
                .expand ##sections{max-height:75%;}
                .saveExit{ font-size: 0.7em!important;}
            }
            @media screen and (max-height:500px) and (orientation : landscape){
                ##sections{max-height:200px!important;}
                ##sections .lable {padding: 5px 5px;}
                ##sections i.fa-light{margin:11px;}
                .nav-inner{max-height:100%;overflow: auto;}
                ##left-logo-for-large{max-width: 100%;}
                ##sections i.fa-light{font-size: 0.8em!important;}
                .expand ##sections i.fa-light{font-size: 2em!important;}
                ##sections .tab .img,.expand ##sections .tab .img{height:42px!important;}
                .saveExit{margin:5px;}
            }
            @media only screen and (min-width: 48em){
                ##left{padding-right: 0; margin-right: 0;}
                ##right > .columns{padding-left: 0; padding-right: 0;}
                .leftTabContent{width:100%!important;}
            } 
            @media screen and (min-width:1023px){
                .leftTabContent{width:100%!important;}
            } 
            @media screen and (min-width:1024px){
                .no-left-padding{padding-left:0;padding-right:0;}
                ##activity{position: absolute;}
                ##right .screen > [ng-controller], ##right .screen > [ng-if],##leftBox .screen > [ng-controller], ##leftBox .screen > [ng-if]{max-height:1024px;}
                ##bottom:not(.audioOnly) .panel{height:auto;}
            }  
            @media screen and (min-width:1023px){
                .expand{width:calc(var(--nav-width) + 10rem);}
                ##main-box.shrink{padding-left: calc(var(--nav-width) + 13rem);}
                ##main-box {padding-top: 10px;}
                ##left ##video-controller[type=audio]{height:auto!important;margin-bottom:10px;}
                ##left ##video-controller[type=audio] .flex-video{padding-bottom:10%!important;}
                ##left ##video-controller[type=audio] .controls{margin-top: 0px!important;}
                ##left ##video-controller[type=audio] .video .playPause{margin-top: 10px!important;}
                ##left ##video-controller[type=audio] .mute{height:21px!important;top:8px!important; margin-top: 8px!important;}
                ##left ##video-controller[type=audio] .sliderUi.volume{top:82%!important;margin-top: 10px!important;}
            }
            @media screen and (min-width:1024px){
                ##left ##video-controller[type=audio] .flex-video{padding-bottom:18.5%!important;}
                ##left ##video-controller[type=audio] .controls{margin-top: 25px!important;}
            }
            @media screen and (max-width:1180px){
                ##main-box{padding-right: 45px;}
            }
            @media screen and (max-width:1024px){
                ##main-box.shrink{padding-left: calc(var(--nav-width) + 11rem);}
                .saveExit{margin:5px;padding: 5px;}
                .icon-box .lable{line-height: 1em;}
                ##main-box{padding-right: 15px;}
                ##left{padding-left: 0.5em;padding-right: 0.5em;}
                .no-left-padding{padding-right:0;}
            }
            @media screen and (max-width:1023px){.playerWell{height:100%;margin:0;background:none;border:none;padding-left: 5px;padding-right: 5px;}}
            @media screen and (max-width:1023px){
                .l-navbar{display:none;padding-top:3px;}
                .expand{width:95%;display:flex;}
                ##main-box{padding-left:0px;padding-right:0px;padding-top:0px;}
                img##top-logo,img##left-logo-for-large, .nav-inner ##left-logo{max-height:60px;}
                img##top-logo{padding-top: 7px;padding-bottom: 7px;}
                ##sections .lable{padding:0 5px;}
                ##sections .tab .img{height:50px!important;}
                ##sections i.fa-light{margin:3px;font-size:2em;}
                .content-box, .lable div {font-size:16px;-webkit-text-size-adjust: 100%;}
                .content-box .lable{padding:3px!important;}
                ##openSideBar{ font-size: 24px; color: white; text-align: right; padding: 6px;}
                ##topTitle{float:left;width:90%;}
                ##closeSideBar{ font-size: 28px;position: absolute;right: 15px;top: 10px;}
                .section .icon-box{padding-left: 5px!important;}
                .expand .nav-inner{height:85%;}
                .fixed-position{position: fixed;overflow: hidden;width: 100%;}
                .playerWell{height:100%;margin:0;background:none;border:none;    padding-left: 0px;   padding-right: 0px;}
                ##wrapper{max-height:100%;}
                ##right{padding:0;}
                ##right .screen:not(.qa),##leftBox .screen:not(.qa){min-height:270px;}
                .flex-video .controls{height:22%;}
                ##left{padding-left:0;padding-right:0;}
                ##right .screen{background-color:none;}
                ##right .screen.learn .ppts > .top{bottom:-5px;}
                .navControls{height:38px;}
                ##other-resources-controller .materials .bottom .tableCell{font-size:12px;}
            }
            @media screen and (max-width:1023px) and (orientation: landscape){
                ##right .screen:not(.qa),##leftBox .screen:not(.qa){min-height:400px;}
            }
            @media screen and (max-width:400px) {
                ##right .screen:not(.qa),##leftBox .screen:not(.qa) {min-height: 210px;}
            }
            /*custom end*/
            /*********************/
        </style>
    </head>
    <body spellcheck='false'>
        <div id='preload'>
            <div id='spinner'>
                <div id='percent'>Loading...</div>
            </div>
            <div id='invalidSeminarMsg' class="displayHide">
                <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> <span class="notFoundTitle">We couldn't load your seminar properly. </span><br/>
                <span class="notFoundDesc">Redirecting you to your #attributes.data.strAssociation.qryAssociation.brandMyCLETab# Page</span>
            </div>
        </div>        
        <div id="wrapper_parent">
            <div id='wrapper'>
                <div id='activity' ng-app='module_1' ng-show="preloaded" ng-controller='controller_1' resize>

                    <div id="overlay-box" class='patch parent-box displayHide'>
                        <div id="background-color-patch" class="patch background-color-patch"></div>
                        <div class='content child-box'>
                            <div save-exit-view></div>
                            <div disconnected-view></div>
                            <div focus-out-view></div>
                            <div email-sent-view></div>
                            <div email-input-view></div>
                            <div seminar_completed-view></div>
                            <div credit-expired-view></div>
                            <div play-next-view></div>
                            <div seminar-pending-view></div>
                            <div seminar-instance-view></div>
                            <div exam-in-progress-warning-view></div>                            
                            <div confirm-exam-close-view></div>                      
                        </div>
                    </div>
                    <div id="focus-out-box" class='patch parent-box displayHide'>
                        <div class="patch background-color-patch"></div>
                        <div class='content child-box'>
                            <div focus-out-view></div>
                        </div>
                    </div>
                    <alert class="pageAlert" ng-show="$parent.showAlert" topic="$parent.alertTopic" description="$parent.descriptionTopic" close="closeAlert($parent.alertType)">
                    </alert>
					<!-- 
                    <div id="alert-box" class='patch parent-box displayHide'>
						<div class="patch background-color-patch"></div>
                        <div class='content child-box'>
                            <div id="alert-box-container">
                               <div id="alert-head" class="heading boldSemi" ng-bind="alertBoxHead"></div>
                               <div id="alert-content" ng-bind="alertBoxContent"></div>
                               <div id="alert-button" ng-click='closeAlert($event)' class="button" ng-bind="alertBoxButton"></div>
                            </div>
                        </div>
                    </div> 
                    -->
                    <!-- <div id="userInactiveView"></div> -->
                    <div id="lm-popup" class='patch parent-box displayHide'>
                        <div class='content child-box'>
                            <div class='small-10 small-centered columns nopadding'>
                                <div sw-lm-popup-view></div>
                            </div>
                        </div>
                    </div>

                    <div class="l-navbar" id="nav-bar">
                        <div class="show-for-small-only sideTogglerIcon" id="closeSideBar"><i class="fa-solid fa-xmark"></i></div>
                        <nav class="nav">
                            <div class="nav-inner">
                                <div id="left-logo">
                                    <img id="left-logo-for-large" ng-src="{{topLeftLogo}}" ng-show='topLeftLogo' class="align-center left-logo-for-large">
                                </div>

                                <div class='divider div-transparent' ng-style='cssBottomDivider'></div>
                                <div id="sections">
                                    
                                    <div class='tabs box boldSemi' ng-style='cssBottomRightTabs' data-test="{{tab.class}}">
                                        <div class="tab {{tab.class}}" ng-class="{tabSelected: $index==0}" id='tab_{{$index}}' ng-repeat="tab in data.tabs" ng-if="($index==0 && seminarType == 'swl' && currentLoadPoint == 'preTest') || ($index==0 && seminarType == 'swl' && currentLoadPoint == 'postTest') || ($index==0 && seminarType == 'swl' && currentLoadPoint == 'evaluation') || seminarType != 'swl'" ng-style='cssBottomRightTab' ng-click='tabClick($event, $index, tab);' img-height data-tabname="{{tab.title}}">

                                            <div class="section">
                                                <div class="icon-box"><div class='img'><i class="fa-light"></i></div><div class='lable' ng-bind-html='tab.title'></div></div>
                                            </div>
                                            <div class='divider div-transparent' ng-style='($index != tabCount) ?cssBottomDivider : ""'></div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div style="flex-grow:1;">
                                <div class='saveExit syncManageBtn regular buttonColors show-for-medium-up' ng-show="userType && hasPaper == 1 " ng-style='cssTopSaveExit' ng-click='syncManageBtnClick($event)'>
                                    Audio/Video Sync Points
                                </div>
        
                                <div class='saveExit regular buttonColors show-for-medium-up' ng-style='cssTopSaveExit' ng-click='checkExamInProgress($event)'>
                                    {{saveTextBtn}}
                                </div>
                            </div>
                        </nav>
                    </div>

                    <div id="main-box">

                        <div class="row show-for-large-up" id='top'>
                            <div class='title boldSemi'>
                                {{seminarName | limitTo: 100 }}{{seminarName.length > 100 ? '...' : ''}}
                            </div>
                        </div>

                        <div class="show-for-medium-down mobile-header" sw-header></div>

                        <div class="playerWell transitions" id="wellContainer">
                            <div class="row {{seminarLayout}}" id='bottom' ng-style='cssBottom'>

                                <div ng-if="seminarType != 'swl'" id="leftBox" class="medium-12 {{rightPanelLarge}} " >
                                    
                                    <div class='screen visible {{screen.class}}' id='screen_{{$index}}' ng-repeat='screen in data.tabs' ng-style='cssBottomRightScreen' ng-show='show.screen[$index]' style="-webkit-overflow-scrolling: touch;">
                                        <div ng-if="screen.class==='learn' && seminarType != 'swl'" >
                                            <ul class="nav nav-pills" style="list-style:none;display:inline;padding:0;">
                                                <li class="nav-item  active">
                                                    <a class="nav-link " aria-current="page" id="outline-btn" href="##" ><i class="fa-solid fa-list-check"></i> Outline</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="##" id="notes-btn" > <i class="fa-regular fa-file-pen"></i> Take Notes</a>
                                                </li>
                                            </ul>

                                            <div class="tab-content" >
                                                <div class="tab-pane fade" role="tabpanel" aria-labelledby="outline-tab" id="outline-sec">
                                                    <div class="programIntro"> {{introMessageText.length>0 ? introMessageText : 'Click below to get started. Watch/listen to each media file to complete the program.'}}</div>
                                                    <div id="outline-content" onscroll="scrollFunction();">
                                                        <div ng-include="'#local.swodPath#templates/sideBarController.html#local.assetCachingKey#'" include-replace></div>
                                                    </div>
                                                    <div id="scrollArrow" class="arrow"></div>
													<div class='CertificateLink text-center' ng-if="certificateLink != ''">
														<a class='save items  viewCertBtn' ng-href='{{certificateLink}}' target="_blank" ng-click='downloadClick($event)' ng-style='cssCmpltReturn'>
															<div class='title tableCell'>
															   <i class="fa-regular fa fa-file-certificate"></i> View Certificate
															</div>
														</a> 
													</div>
                                                    <div class='text-center ' ng-if="seminarCompletedFlag == 1">                                                        
                                                        <span  class="tooltip">
                                                            <i class="fa fa-solid fa-info-circle seminarCmpltMsgIcon"></i>  
                                                            <span class="tooltiptextEmail">To repurchase, email {{supportEmail}}</span>
                                                        </span>  
                                                        <span class="seminarCmpltMsgFullText">Program completed on {{dateCompleted}}. Cannot be retaken for credit unless repurchased. </span>                                                      
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" role="tabpanel" aria-labelledby="notes-tab" id="notes-sec" style="display:none;">
                                                    <div class="row leftTabContent {{seminarLayout == 'largeVideo'?'fullheight':''}}" ng-show="$parent.showLeftContent" >
                                                        <!-- note box -->
                                                        <div ng-include="'#local.swodPath#templates/noteController.html#local.assetCachingKey#'" include-replace></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- other resources tab screen -->
                                        <div ng-if="screen.class==='orsc'" ng-include="'#local.swodPath#templates/orscController.html#local.assetCachingKey#'" include-replace></div>
                                        <!-- q&a tab screen -->
                                        <div ng-if="screen.class==='qa'" ng-include="'#local.swodPath#templates/qaController.html#local.assetCachingKey#'" include-replace></div>
                                        <!-- complete tab screen -->
                                        <!-- help tab screen -->
                                        <div ng-if="screen.class==='help'" ng-include="'#local.swodPath#templates/helpController.html#local.assetCachingKey#'" include-replace></div>
                                    </div>                                      
                                </div>
                                <div ng-if="seminarType == 'swl'" id="leftBox" class="medium-12 {{rightPanelLarge}} " >
                                    
                                    <div class='screen visible {{screen.class}}' id='screen_{{$index}}' ng-repeat='screen in data.tabs' ng-style='cssBottomRightScreen' ng-show='show.screen[$index]' style="-webkit-overflow-scrolling: touch;">
                                        <div ng-if="screen.class==='learn' && seminarType == 'swl'" >
                                            <div ng-include="'#local.swodPath#templates/cmpltController.html#local.assetCachingKey#'" include-replace></div>
                                        </div>
                                        <div ng-include="'#local.swodPath#templates/learnController.html#local.assetCachingKey#'" include-replace></div>
                                    </div>                                      
                                </div>
                               
                                <!-- right panel -->
                                <div id='right' ng-if="seminarType != 'swl'" class='panel small-12 medium-12 rightPanelWrap columns {{rightPanelLarge}} {{(seminarLayout == "audioOnly")?"fullAudioWidth":""}}' ng-style='cssBottomRight'>
                                    
                                    <div class='small-12 medium-12 columns {{rightPanelPPTLarge}}'>	
                                        <div>
                                            <!-- showing right logo in right panel for only large-->
                                            <div class="show-for-large-up" ng-show='topRightLogo'>
                                                <img id="right-logo" ng-src="{{topRightLogo}}" class="align-right" >
                                                <div class='space row'></div>
                                            </div>
                                            
                                            <div class='base' id="rightBase" ng-style='cssBottomRightBase'>
                                                <!--  for mobile end -->
                                                <!-- tab screens -->

                                                <div ng-include="'#local.swodPath#templates/videoController.html#local.assetCachingKey#'" include-replace></div>
                                                <span id='afterVideo'></span>

                                                <div class='screens box  {{(seminarLayout == "largeVideo" || seminarLayout == "audioOnly") ?"hide":""}}' ng-style='cssBottomRightScreens' >
                                                    <div class='content'>
                                                        <div class='screen visible learn' id='screen_1'  ng-style='cssBottomRightScreen'  style="-webkit-overflow-scrolling: touch;">
                                                            <!-- learn tab screen -->
                                                            <div ng-include="'#local.swodPath#templates/learnController.html#local.assetCachingKey#'" include-replace></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='regular text-center {{seminarLayout == "audioOnly" ?"hide":""}}' id="viewModeBtn" data-label="Exit Wide Screen"><i class="fa-solid fa-expand-wide" aria-hidden="true"></i> View Wide Screen</div>
                                    </div>
                                    <div class="mediaSyncWrap small-12 medium-12 large-6 hide" audiovideo-sync-view></div>
                                </div>
                                <div id="noteHolder" ng-class="{'noteShow':tabLable == 'Learn'}" ></div>
                            </div>                                     
                        </div>
                        <div id="examWrapper" ng-if="seminarType != 'swl'">
                            <div class="examContent">
                                <div seminar-complete-process-view></div>
                                <div seminar-pretest-complete-process-view></div>
                            </div>
                        </div>
                        
                    </div>

                    <div class="mobile-bottom-bar">
                        <a class="tab footer-link {{tab.class}}" ng-class="{tabSelected: $index==0}" id='tab_{{$index}}' ng-repeat="tab in data.tabs" ng-if="($index==0 && seminarType == 'swl' && currentLoadPoint == 'preTest') || ($index==0 && seminarType == 'swl' && currentLoadPoint == 'postTest') || ($index==0 && seminarType == 'swl' && currentLoadPoint == 'evaluation') || seminarType != 'swl'" ng-style='cssBottomRightTab' ng-click='tabClick($event, $index, tab);' img-height data-tabname="{{tab.title}}">
                            <i class="fa-light"></i> <span class='footer-text'><div class='lable' ng-bind-html='tab.title'></div></span>
                        </a>
                    </div>
                </div>
                <!-- activity-end -->
            </div>
            <!-- wrapper-end -->
            
        </div>
        <!-- wrapper-parent end -->
        <script>
            $(document).foundation();
        </script>
        <script type="text/javascript" src="#local.swodPath#libs/mediaqueryDetect.js#local.assetCachingKey#"></script>
        <script type="text/javascript" src="#local.swodPath#js/resizeWindow.js#local.assetCachingKey#"></script>
    </body>
</html>
</cfoutput>