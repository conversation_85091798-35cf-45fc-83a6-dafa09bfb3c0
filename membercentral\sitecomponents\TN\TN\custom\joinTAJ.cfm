<cfscript>
	variables.applicationReservedURLParams 	= "issubmitted";
	local.customPage.baseURL								= "/?#getBaseQueryString(false)#";

	/* ************************* */
	/* Custom Page Custom Fields */
	/* ************************* */
	
	local.arrCustomFields = [];
	local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Staff Confirmation Email", value="<EMAIL>, <EMAIL>, <EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="memberNotice", type="CONTENTOBJ", desc="Notice Content", value="It looks like you might already be a member! Thank you and please contact TTLA at (615) 329-3000 or <a href = 'mailto: <EMAIL>'><EMAIL></a> for information about your membership or renewing." }; 
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(
	siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
	siteResourceID=this.siteResourceID, 
	arrCustomFields=local.arrCustomFields);

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='TTLA Membership Application',
			orgEmailTo= local.strPageFields.StaffConfirmationEmail,
			memberEmailFrom='<EMAIL>'));
			
	// GATEWAY INFORMATION: 
	local.profile_1._profileCode 	= 'TNAJAuthorizeCIM';
	local.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=local.orgID,profileCode=local.profile_1._profileCode);
	local.profile_1._description 	= '#local.organization# - #local.formNameDisplay#';


	// SUBSCRIPTION INFORMATION 
	local.objSubs									= CreateObject('component','model.admin.subscriptions.subscriptions');
	
	local.aopTypeUID 							= '3B9B4FE5-F5A0-4CBD-812B-72E7EAC3F6FE';
	local.aopTopSubUID 						= 'A9117DC2-3813-4097-814E-FBCC94F42CA9';
	
	local.topAllAttySubUID 				= '5762AF75-5129-4C60-B5CA-A41267759014';
	local.topLawStudentSubUID 		= '9DEF83D0-16B4-4C50-9562-63DD5ADBE5FB';
	local.topParalegalSubUID 			= 'F10D12D5-78F8-4AA8-9403-CE2C83F3A408';
	local.topPubSectorSubUID 			= 'F4E5C06C-9816-4649-A9FA-BD54F245D9A2';
	
	local.childBasicSubUID 				= '7B7C1467-CF4F-4265-B474-70DBCFC70F96';
	local.childLifeSubUID 				= 'F43E2D2B-743F-44A1-9CB8-7C7DB83B4EAD';
	local.childSeasonPassSubUID 	= 'C63C6A09-842A-4926-9C31-8192F094C8A6';
	local.childSustainingSubUID 	= '49726103-B629-47DC-B346-50DDF19652B4';
	
	local.childAssocSubUID 				= '3769757D-D341-4B25-BCC7-DEE08271240E';
	
	local.fullFrequencyID					= '78CC3EF0-BC54-4948-A635-5F642E624AE5';
	local.monthlyFrequencyID			= '02884010-41AF-4F0E-B2C5-C2714E38A8A1';
	local.quarterlyFrequencyID		= '98C4512D-6F13-47AC-945C-81C80CB974D5';	
	
	// this is used to check for existing subs.
	local.SubUIDList = "#local.aopTypeUID#,#local.aopTopSubUID#,#local.topAllAttySubUID#,#local.topLawStudentSubUID#,#local.topParalegalSubUID#,#local.topPubSectorSubUID#,#local.childBasicSubUID#,#local.childLifeSubUID#,#local.childSeasonPassSubUID#,#local.childSustainingSubUID#,#local.childAssocSubUID#";

	local.mainhostName 			= event.getValue('mc_siteInfo.mainhostName');
	local.scheme 			= event.getValue('mc_siteInfo.scheme');

	local.USStates 					= application.objCustomPageUtils.mem_getStatesByCountry('United States');
</cfscript>

<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPDPctSpecial">
	select mdcv.valueID
	from dbo.ams_memberDataColumnValues mdcv
	inner join dbo.ams_memberDataColumns mdc 
		on mdc.columnID = mdcv.columnID
		and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
		and columnName = 'Personal Injury Defense'
	where mdcv.columnValueString = '0%'
	order by mdcv.columnValueString
</cfquery>


<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID="583EABF0-9BA0-4500-AA67-36384271373C")>
<cfset local.hasSub = false>
<cfif event.getValue('msg','') neq 2 and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
	<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
</cfif>


<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;  padding:0 10px;}
			
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##03608B; border-bottom:1px solid ##03608B; }
			
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
			.CPSection{ border:1px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:14pt; height:20px; font-weight:bold; color:##fff; padding:10px; background:##336699; }
			.CPSectionContent { padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			.important{ font-size:10pt; font-style:italic; color:##ff0000;}
			
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			
			.tsAppBodyText { color:##000;}
			select.tsAppBodyText{color:##666;}
			
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			
			##barDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }			
		</style>
	</cfsavecontent>
	
	<cfsavecontent variable="local.pageJS">
		#local.pageJS#
		<script type='text/javascript' src='/assets/common/javascript/date.js'></script>
	</cfsavecontent>
	
	#local.pageJS#
	#local.pageCSS#
	
	<div class="customPage">
		<div class="TitleText PB">#local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM:  --->
			<cfcase value="0">
				
				<cfif event.getValue('msg',0) EQ "2">		
					<!--- Form is not Open --->
					<div class="bodyText" >
						#local.strPageFields.memberNotice#
					</div>
				<cfelse>
					
					<cfif local.hasSub eq true>
						<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no" />
					</cfif>
					
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAOPSubs">
						select subs.subscriptionID, RTRIM(REPLACE(subs.subscriptionName, 'Area of Practice', '')) as subscriptionName
						from dbo.sub_subscriptions subs
						inner join dbo.sub_types t
							on t.typeID = subs.typeID
							and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
							and t.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.aopTypeUID#">
						where subs.soldSeparately <> 1
						order by subs.subscriptionName
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRace">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'Race'
						order by mdcv.columnValueString
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGender">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'Gender'
						order by mdcv.columnValueString
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPIPct">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'Personal Injury Percent'
						order by mdcv.columnValueString
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPDPct">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'Personal Injury Defense'
						order by mdcv.columnValueString
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDefPct">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'Defense Attorney'
						order by mdcv.columnValueString
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPartyAffil">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'Political Affiliation'
						order by mdcv.columnValueString
					</cfquery>				
					
					<script type="text/javascript">
						function _FB_validateForm() {
							var thisForm = document.forms["#local.formName#"];
							var arrReq = new Array();
							var lastMtrxErr = '';
							
								if (!_FB_hasValue(thisForm['payInterval'], 'RADIO')) arrReq[arrReq.length] 		= 'Please Select a payment schedule';
								if ( getSelectedRadio(document.getElementsByName('payInterval')) == '0') if (!_FB_hasValue(thisForm['rateMonthly'], 'RADIO')) arrReq[arrReq.length] 		= 'Please Select a monthly rate';
								if ( getSelectedRadio(document.getElementsByName('payInterval')) == '1') if (!_FB_hasValue(thisForm['rateQuarterly'], 'RADIO')) arrReq[arrReq.length] 	= 'Please Select a quarterly rate';
								if ( getSelectedRadio(document.getElementsByName('payInterval')) == '2') if (!_FB_hasValue(thisForm['rateAnnually'], 'RADIO')) arrReq[arrReq.length] 		= 'Please Select an annual rate';
								
								if ((( getSelectedRadio(document.getElementsByName('payInterval')) == '0') && 
										(getSelectedRadio(document.getElementsByName('rateMonthly')) == '1') ||
										(getSelectedRadio(document.getElementsByName('rateMonthly')) == '2') ||
										(getSelectedRadio(document.getElementsByName('rateMonthly')) == '3') ||
										(getSelectedRadio(document.getElementsByName('rateMonthly')) == '4') ||
										(getSelectedRadio(document.getElementsByName('rateMonthly')) == '5') ||
										(getSelectedRadio(document.getElementsByName('rateMonthly')) == '7')
										) ||
										(( getSelectedRadio(document.getElementsByName('payInterval')) == '1') && 
										(getSelectedRadio(document.getElementsByName('rateQuarterly')) == '1') ||
										(getSelectedRadio(document.getElementsByName('rateQuarterly')) == '2') ||
										(getSelectedRadio(document.getElementsByName('rateQuarterly')) == '3') ||
										(getSelectedRadio(document.getElementsByName('rateQuarterly')) == '4') ||
										(getSelectedRadio(document.getElementsByName('rateQuarterly')) == '5') ||
										(getSelectedRadio(document.getElementsByName('rateQuarterly')) == '7') 
										) ||
										(( getSelectedRadio(document.getElementsByName('payInterval')) == '2') && 
										(getSelectedRadio(document.getElementsByName('rateAnnually')) == '1') ||
										(getSelectedRadio(document.getElementsByName('rateAnnually')) == '2') ||
										(getSelectedRadio(document.getElementsByName('rateAnnually')) == '3') ||
										(getSelectedRadio(document.getElementsByName('rateAnnually')) == '4') ||
										(getSelectedRadio(document.getElementsByName('rateAnnually')) == '5') ||
										(getSelectedRadio(document.getElementsByName('rateAnnually')) == '7')
										))
								{
									if (!_FB_hasValue(thisForm['barDate'], 'TEXT')) arrReq[arrReq.length] 						= 'Bar Admission Date';
									if (!_FB_hasValue(thisForm['lawSchool'], 'TEXT')) arrReq[arrReq.length] 					= 'Law School Attended';
								}	
							
								if ( getSelectedRadio(document.getElementsByName('rateMonthly')) == '6') {if (!_FB_hasValue(thisForm['sponsor'], 'TEXT')) arrReq[arrReq.length] 		= 'Please name a TTLA Member Sponsor';}
								if ( getSelectedRadio(document.getElementsByName('rateQuarterly')) == '6') {if (!_FB_hasValue(thisForm['sponsor'], 'TEXT')) arrReq[arrReq.length] 	= 'Please name a TTLA Member Sponsor';}
								if ( getSelectedRadio(document.getElementsByName('rateAnnually')) == '6') {if (!_FB_hasValue(thisForm['sponsor'], 'TEXT')) arrReq[arrReq.length] 		= 'Please name a TTLA Member Sponsor';}
							
							if (arrReq.length > 0) {
								var msg = 'The following questions are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							return true;
						}
	
						function assignMemberData(memObj){
							var er_changeErr = function(r) { 
								$('##lookupErr').html('There was an error retrieving your account details. Try again.').show(); 
								$('##formToFill').hide();
							};
							var er_change = function(r) {
								if (r.success) {
									$('##memberNumber').val(r.membernumber);
									$('##memberID').val(r.memberid);
	
									$('##name').val(r.fullname);
									$('##divName').html(r.fullname);
	
									$('##firm').val(r.company);
									$('##divFirm').html(r.company);
									
									var dispAddress = r.address1;
									if (r.address2.length > 0) dispAddress += '<br/>' + r.address2;
									
									$('##address').val(dispAddress);
									$('##divAddress').html(dispAddress);
	
									$('##city').val(r.city);
									$('##divCity').html(r.city);
	
									$('##state').val(r.statecode);
									$('##divState').html(r.statecode);
	
									$('##zip').val(r.postalcode);
									$('##divZip').html(r.postalcode);
									
									$('##phone').val(r.phone);
									$('##divPhone').html(r.phone);
	
									$('##email').val(r.email);
									$('##divEmail').html(r.email);
									
									$('##barNum').val(r.tennessee_bpr_number);
	
									var okToCont = true;
									<cfloop list="#local.SubUIDList#" index="local.thisUID">
										if (r.subs['#LCase(local.thisUID)#'] == 1) okToCont = false;
									</cfloop>
	
									if (okToCont) $('##formToFill').show();
									else {
										$('##lookupErr').html("#rereplace(local.strPageFields.memberNotice,'"','\"','ALL')#").show(); 
										$('##formToFill').hide();
									}
								}
							};
	
							var customFields = [];
							customFields[0] = 'Tennessee_BPR_Number';
							var subUIDs = '#local.SubUIDList#'.split(',');
	
							var objParams = { memberNumber:memObj.memberNumber, subUIDs:subUIDs, customFields:customFields };
							TS_AJX('SUBS','getMemberDataByMemberNumberWithSubs',objParams,er_change,er_changeErr,10000,er_changeErr);
						}
						
						function loadMember(memNumber){
							var objParams = { memberNumber:memNumber };
							$('##formToFill').hide();
							assignMemberData(objParams);
						}
						
						function showYears() {
							var thisForm = document.forms["#local.formName#"];
							var memAllow = 0;

							var bd = Date.parse($('##barDate').val());
							if (!bd) {
								$('##barDate').val('');
							} else {
								var monthPart = bd.getMonth()+1;
								var dayPart = bd.getDate();
								var yearPart = bd.getFullYear();
								$('##barDate').val(monthPart+'/'+dayPart+'/'+yearPart);
								
								var now = Date.today();
								var sixMonthsAgo = Date.today().add({months:-6});
								var fourYearsAgo = Date.today().add({years:-4});
								var tenYearsAgo = Date.today().add({years:-10});
								
								if (Date.compare(now,bd) == 1) {
									if (Date.compare(bd,sixMonthsAgo) == 1) memAllow = 1;
									else if (Date.compare(bd,sixMonthsAgo) < 1 && Date.compare(bd,fourYearsAgo) == 1) memAllow = 2;
									else if (Date.compare(bd,fourYearsAgo) < 1 && Date.compare(bd,tenYearsAgo) == 1) memAllow = 3;
									else memAllow = 4;
								}
							}

							var monthly = thisForm['rateMonthly'];
							for (var i=0; i <= monthly.length-1; i++) {
								if ((monthly[i].value >= 1) && (monthly[i].value <= 4)) {
									if (monthly[i].value == memAllow) {
										monthly[i].disabled = false;
									}
									else {
										monthly[i].disabled = true;
										monthly[i].checked = false;
									}
								}
							}
							
							var quarterly = thisForm['rateQuarterly'];
							for (var i=0; i <= quarterly.length-1; i++) {
								if ((quarterly[i].value >= 1) && (quarterly[i].value <= 4)) {
									if (quarterly[i].value == memAllow) {
										quarterly[i].disabled = false;
									}
									else {
										quarterly[i].disabled = true;
										quarterly[i].checked = false;
									}
								}
							}
							
							var annually = thisForm['rateAnnually'];
							for (var i=0; i <= annually.length-1; i++) {
								if ((annually[i].value >= 1) && (annually[i].value <= 4)) {
									if (annually[i].value == memAllow) {
										annually[i].disabled = false;
									}
									else {
										annually[i].disabled = true;
										annually[i].checked = false;
									}
								}
							}
						}
						
						function showDefPercent(){
							var defPercentDiv = document.getElementById('defPercent');
							var a = document.getElementById('defensePercent');
							var strDefPercent = a.options[a.selectedIndex].value;
							if ( strDefPercent != #local.qryPDPctSpecial.valueID#){
									defPercentDiv.style.display = '';
							}
							else {
								defPercentDiv.style.display = 'none';
							}
						}
						
						function showPayInt(){
							var thisForm = document.forms["#local.formName#"];
							var annually = thisForm['rateAnnually'];
							var monthly = thisForm['rateMonthly'];
							var quarterly = thisForm['rateQuarterly'];
							var monthlyDiv = document.getElementById('monthly');
							var quarterlyDiv = document.getElementById('quarterly');
							var annualDiv = document.getElementById('annual');
							var paySched = getSelectedRadio(document.getElementsByName('payInterval'));
							
							if ( paySched == "0" ){
								monthlyDiv.style.display = '';
								quarterlyDiv.style.display = 'none';
								annualDiv.style.display = 'none';
									for (var i=0; i <= annually.length-1; i++){
										annually[i].checked = '';
									}
									for (var i=0; i <= quarterly.length-1; i++){
										quarterly[i].checked = '';
									}
								
							}
							else if ( paySched == "1" ){
								quarterlyDiv.style.display = '';
								monthlyDiv.style.display = 'none';
								annualDiv.style.display = 'none';
									for (var i=0; i <= annually.length-1; i++){
										annually[i].checked = '';
									}
									for (var i=0; i <= monthly.length-1; i++){
										monthly[i].checked = '';
									}
								
							}
							else if ( paySched == "2" ){
								annualDiv.style.display = '';
								quarterlyDiv.style.display = 'none';
								monthlyDiv.style.display = 'none';
									for (var i=0; i <= monthly.length-1; i++){
										monthly[i].checked = '';
									}
									for (var i=0; i <= quarterly.length-1; i++){
										quarterly[i].checked = '';
									}
								
							}
						}
						
						function showSponsors(){
							var sponsorDiv = document.getElementById('tnajSponsor');
							var payMonthly = getSelectedRadio(document.getElementsByName('rateMonthly'));
							var payQuarterly = getSelectedRadio(document.getElementsByName('rateQuarterly'));
							var payYearly = getSelectedRadio(document.getElementsByName('rateAnnually'));
	
							if ( payMonthly == "6" || payQuarterly == "6" || payYearly == "6"){
								sponsorDiv.style.display = "";
								}
							
							else {sponsorDiv.style.display = "none";}						
							}
						
						function setOrig() {
						   	origDate = $('##barDate').val()
						}				
							
						function MCregisterOnLoad(fn) {
							if (window.addEventListener) {
								window.addEventListener("load",fn,false);
							} else {
								if (window.attachEvent) {
									window.attachEvent("onload",fn);
								} else {
									if (document.getElementById) {
										window.onload=fn;
									}
								}
							}
						}
						MCregisterOnLoad(setOrig);		
						
						$(document).ready(function(){
							mca_setupDatePickerField('barDate');
						});								
					</script>
					
					<table width="100%">
						<tr>
							<td class="l b">Click here to <a href="/docDownload/45149">download this application</a>.</td>
							<td class="r"><span class="i frmText">*Denotes required field</span></td>
						</tr>
					</table>
					<br/>
						
					<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
						<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1">
						<cfinput type="hidden" name="memberID"  id="memberID" value="#session.cfcUser.memberData.memberID#">
						<cfinput type="hidden" name="memberNumber"  id="memberNumber" value="#session.cfcUser.memberData.memberNumber#">
						
						<!--- ACCOUNT LOCATOR:  --->
						<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
							<div class="CPSection">
								<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
								<div class="frmRow1" style="padding:10px;">
									<table cellspacing="0" cellpadding="2" border="0" width="100%">
										<tr>
											<td width="175" class="c">
												<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
													<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
												</div>
											</td>
											<td>
												<span class="frmText">
													<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
													<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
													<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
													<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
												</span>
											</td>
										</tr>
									</table>
								</div>
							</div>
							<div id="lookupErr" class="alert" style="display:none;"></div>
						</cfif>
						
						<div id="formToFill" style="display:none;">
						<!--- CONTACT INFORMATION:  --->
							<div class="CPSection">
								<div class="CPSectionTitle BB">Contact Information</div>
								<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
									<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td class="r">Name:</td>
											<td>
												<div id="divName" name="divName"></div>
												<cfinput value="" name="name"  id="name" type="hidden" />
											</td>
										</tr>
	
										<tr class="frmRow1">
											<td class="r">Preferred Salutation:</td>
											<td><input size="40" name="prefSalutation" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="r">Firm Name:</td>
											<td>
												<div id="divFirm" name="divFirm"></div>
												<cfinput value="" name="firm"  id="firm" type="hidden" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">Office Address:</td>
											<td>
													<div id="divAddress" name="divAddress"></div>
													<cfinput value="" name="address"  id="address" type="hidden" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">City:</td>
											<td class="tsAppBodyText frmText">
												<span id="divCity" name="divCity"></span>
												<cfinput value="" name="city"  id="city" type="hidden" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">State:</td>
											<td>
												<span id="divState" name="divState"></span>
												<cfinput value="" name="state"  id="state" type="hidden" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">Zip:</td>
											<td>
													<span id="divZip" name="divZip"></span>
													<cfinput value="" name="zip"  id="zip" type="hidden" />
											</td>
										</tr>
										<tr>
											<td class="r">Office Phone:</td>
											<td class="tsAppBodyText frmText">
												<div id="divPhone" name="divPhone"></div>
												<cfinput value="" name="phone"  id="phone" type="hidden" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">E-mail:</td>
											<td>
												<div id="divEmail" name="divEmail"></div>
												<cfinput value="" name="email"  id="email" type="hidden" />
											</td>
										</tr>	
									</table>
								</div>
							</div>
						<!--- PERSONAL INFORMATION: --->
							<div class="CPSection">
								<div class="CPSectionTitle BB">Personal Information</div>
								<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
									<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td class="r" width="135">Home Address 1:</td>
											<td><cfinput size="60" name="h_address1"  id="h_address1" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="r">Home Address 2:</td>
											<td><cfinput size="60" name="h_address2"  id="h_address2" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="r">City:</td>
											<td class="tsAppBodyText frmText">
												<cfinput size="25" name="h_city"  id="h_city" type="text" value="" class="tsAppBodyText" />
												&nbsp;State:
												<cfselect name="h_state"  id="h_state" class="tsAppBodyText">
													<option value="0"> - Please Select - </option>
													<cfloop query="local.USStates">
														<option value="#local.USStates.stateID#">#local.USStates.name#</option>
													</cfloop>
												</cfselect>
												&nbsp;Zip:
												<cfinput size="10" maxlength="15" name="h_zip"  id="h_zip" type="text" value="" class="tsAppBodyText" />
											</td>
										</tr>
										<tr>
											<td class="r">Home Phone:</td>
											<td class="tsAppBodyText frmText">
												<cfinput size="13" maxlength="13" name="h_phone"  id="h_phone" type="text" value="#local.data.phone.phone#" class="tsAppBodyText" />
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">*Law School Attended:</td>
											<td><cfinput size="60" name="lawSchool"  id="lawSchool" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="r">*Bar Admission Date:</td>
											<td>
												<cfinput value="" class="tsAppBodyText" name="barDate" id="barDate" type="text" size="20" onChange="showYears();" validate="date" message="Please enter a valid Bar Admission Date."  /> 
												<a href="javascript:mca_clearDateRangeField('barDate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -2px 7px;"></i></a>
												&nbsp;&nbsp;<span class="r">MM/DD/YYYY</span>
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="r">BPR Number:</td>
											<td><cfinput size="30" name="barNum"  id="barNum" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr class="frmRow1">
											<td class="r">Referred for Membership by:</td>
											<td><cfinput size="60" name="memberRef"  id="memberRef" type="text" value="" class="tsAppBodyText" /></td>
										</tr>
										<tr><td colspan="2">&nbsp;</td></tr>
										<tr>
											<td colspan="2" class="P">
												<table>
													<tr>
														<td>Please indicate your race (Optional):</td>
														<td>
															<cfselect name="raceEthnicity"  id="raceEthnicity" class="tsAppBodyText">
																<option value="0"> - Please Select - </option>
																<cfloop query="local.qryRace">
																	<option value="#local.qryRace.valueID#">#local.qryRace.columnValueString#</option>
																</cfloop>
															</cfselect>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td colspan="2" class="P">
												<table>
													<tr>
														<td>Please indicate your gender (Optional):</td>
														<td>
															<cfselect name="gender"  id="gender" class="tsAppBodyText">
																<option value="0"> - Please Select - </option>
																<cfloop query="local.qryGender">
																	<option value="#local.qryGender.valueID#">#local.qryGender.columnValueString#</option>
																</cfloop>
															</cfselect>
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>
						<!--- PRACTICE INFORMATION: --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">My Practice is Approximately:</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table width="100%">
									<tr class="frmRow1">
										<td class="P" width="100">
											<strong>Plaintiff</strong>
										</td>
										<td class="P">
											<select name="plaintiffPercent">
												<cfloop query="local.qryPIPct">
													<option value="#local.qryPIPct.valueID#">#local.qryPIPct.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr class="frmRow1">
										<td class="P">
											<strong>Defense</strong>
										</td>
										<td class="P">
											<select name="defensePercent" id="defensePercent" onchange="showDefPercent();">
												<cfloop query="local.qryPDPct">
													<option value="#local.qryPDPct.valueID#">#local.qryPDPct.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
								</table>
								<table id="defPercent" style="display:none;"  width="100%">
										<tr><td class="P"><strong><hr />For Defense:</strong></td></tr>
										<tr class="frmRow1">
											<td class="P" width="100">
												<strong>Defense Attorney</strong>
											</td>
											<td class="P">
												<select name="defenseAttorneyPercent">
													<cfloop query="local.qryDefPct">
														<option value="#local.qryDefPct.valueID#">#local.qryDefPct.columnValueString#</option>
													</cfloop>
												</select>
											</td>
										</tr>
									</table>
							</div>
						</div>
						<!--- POLITICAL INFO: --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Political Information</div>
							<div class="subCPSectionArea1 tsAppBodyText frmText BB">
								It is often important to identify members who are constituents in different legislative districts. 
								Your response is greatly appreciated.<br />
								<div class="info">(The information requested below can be found on your voter registration card or <a href="http://www.capitol.tn.gov/" target="_blank">click here</a> to find your House and Senate District numbers.)</div>
							</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table width="100%" cellpadding="0" cellspacing="0" border="0">
									<tr><td colspan="2">&nbsp;</td></tr>
									<tr class="frmRow1">
										<td class="P" width="135">State House District:</td>
										<td><input type="text" width="50" name="houseDistrict" value="" /></td>
									</tr>
									<tr><td colspan="2">&nbsp;</td></tr>
									<tr>
										<td class="P">State Senate District:</td>
										<td><input type="text" width="50" name="senateDistrict" value="" /></td>
									</tr>
									<tr><td colspan="2">&nbsp;</td></tr>
									<tr>
										<td class="P">Political Party Affiliation</td>
										<td class="P">
												<select name="politAffil">
													<option value="0"> - Please Select - </option>
													<cfloop query="local.qryPartyAffil">
														<option value="#local.qryPartyAffil.valueID#">#local.qryPartyAffil.columnValueString#</option>
													</cfloop>
												</select>
										</td>
									</tr>
									<tr><td colspan="2">&nbsp;</td></tr>
								</table>
							</div>
						</div>					
						<!--- MEMBERSHIP TYPE:--->
							<div class="CPSection">
								<div class="CPSectionTitle BB">*Rate Selection</div>
								<div class="subCPSectionArea1 tsAppBodyText frmText P BB"  style="">
									<ul>
										<li>Membership dues rates in TTLA are based on total number of years in practice. "Years in practice" is defined as the total number of years that have elapsed since your first bar admittance.</li>
										<li>Membership dues at all Attorney membership levels, and at the Paralegal membership level, may be paid monthly, quarterly or annually. If this option is applicable to your level of membership, please indicate your preferred method of payment.</li>
										<li>Paralegal is defined as anyone serving as legal support staff. To be eligible for TTLA membership, Paralegals must be employed by a current TTLA Attorney Member.</li>
										<li>Law Student is defined as any currently enrolled student of a certified law school.</li>
										<li>The option of monthly or quarterly payments is not available at the Law Student or Public Sector membership levels</li>
									</ul>
								</div>
								<div class="subCPSectionArea2 tsAppBodyText frmText BB">
									<table cellspacing="0" cellpadding="0" border="0" align="center" width="80%">
										<tr><td class="important">Credit card enrollment in Tennessee Trial Lawyers Association is maintained automatically until notice is received of the member's intention not to renew.	</td></tr>
									</table>
									<br/>
									<table cellspacing="0" cellpadding="0" border="0" align="center">
										<tr><td colspan="8" class="b">* Choose a payment schedule:</td></tr>
										<tr>
											<td width="5"><input type="radio" name="payInterval" value="Monthly" onclick="showPayInt(); showSponsors()"/></td>
											<td class="l P">Monthly</td>
											<td>&nbsp;</td>
											<td width="5"><input type="radio" name="payInterval" value="Quarterly" onclick="showPayInt(); showSponsors()" /></td>
											<td class="l P">Quarterly</td>
											<td>&nbsp;</td>
											<td width="5"><input type="radio" name="payInterval" value="Annually" onclick="showPayInt(); showSponsors()" /></td>
											<td class="l P">Annually</td>
										</tr>
									</table>
								</div>
								<div class="tsAppBodyText frmRow1 frmText" id="monthly" style="display:none;">
									<table cellspacing="0" cellpadding="0" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td width="25" class="c"><input type="radio" value="1" name="rateMonthly" onClick="showSponsors()" disabled="true" /></td>
											<td>New Bar Admittee (in practice 0 - 6 months)</td>
											<td width="150" class="r b P">$0</td>
										</tr>	
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>								
										<tr class="frmRow2">
											<td width="25" class="c"><input type="radio" value="2" name="rateMonthly" onClick="showSponsors()" disabled="true" /></td>
											<td>New Member (in practice 7 months - 3 years)</td>
											<td width="150" class="r b P">$20</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="3" name="rateMonthly" onClick="showSponsors()" disabled="true" /></td>
											<td>Regular Member 1 (in practice 4 - 9 years)</td>
											<td class="r b P">$30</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="4" name="rateMonthly" onClick="showSponsors()" disabled="true" /></td>
											<td>Regular Member 2 (in practice 10+ years)</td>
											<td class="r b P">$50</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="5" name="rateMonthly" onClick="showSponsors()" /></td>
											<td>Sustaining Member</td>
											<td class="r b P">$90</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="6" name="rateMonthly" onClick="showSponsors()" /></td>
											<td>Season Pass</td>
											<td class="r b P">$150</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="7" name="rateMonthly" onClick="showSponsors()" /></td>
											<td>Paralegal Affiliate Member</td>
											<td class="r b P">$15</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="8" name="rateMonthly" onClick="showSponsors()" disabled="true" /></td>
											<td>Law Student</td>
											<td class="r b P">N/A</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="9" name="rateMonthly" onClick="showSponsors()" disabled="true" /></td>
											<td>Public Sector Member</td>
											<td class="r b P">N/A</td>
										</tr>
									</table>
								</div>
								<div class="tsAppBodyText frmRow1 frmText" id="quarterly" style="display:none;">
									<table cellspacing="0" cellpadding="0" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td width="25" class="c"><input type="radio" value="1" name="rateQuarterly" onClick="showSponsors()" disabled="true" /></td>
											<td>New Bar Admittee (in practice 0 - 6 months)</td>
											<td width="150" class="r b P">$0</td>
										</tr>	
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>										
										<tr class="frmRow2">
											<td width="25" class="c"><input type="radio" value="2" name="rateQuarterly" onClick="showSponsors()" disabled="true" /></td>
											<td>New Member (in practice 7 months - 3 years)</td>
											<td width="150" class="r b P">$60</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="3" name="rateQuarterly" onClick="showSponsors()" disabled="true" /></td>
											<td>Regular Member 1 (in practice 4 - 9 years)</td>
											<td class="r b P">$90</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="4" name="rateQuarterly" onClick="showSponsors()" disabled="true" /></td>
											<td>Regular Member 2 (in practice 10+ years)</td>
											<td class="r b P">$150</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="5" name="rateQuarterly" onClick="showSponsors()" /></td>
											<td>Sustaining Member</td>
											<td class="r b P">$270</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="6" name="rateQuarterly" onClick="showSponsors()" /></td>
											<td>Season Pass</td>
											<td class="r b P">$450</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="7" name="rateQuarterly" onClick="showSponsors()" /></td>
											<td>Paralegal Affiliate Member</td>
											<td class="r b P">$45</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="8" name="rateQuarterly" onClick="showSponsors()" disabled="true" /></td>
											<td>Law Student</td>
											<td class="r b P">N/A</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="9" name="rateQuarterly" onClick="showSponsors()" disabled="true" /></td>
											<td>Public Sector Member</td>
											<td class="r b P">N/A</td>
										</tr>
									</table>
								</div>
								<div class="tsAppBodyText frmRow1 frmText" id="annual" style="display:none;">
									<table cellspacing="0" cellpadding="0" width="100%" border="0" align="center">
										<tr class="frmRow1">
											<td width="25" class="c"><input type="radio" value="1" name="rateAnnually" onClick="showSponsors()" disabled="true" /></td>
											<td>New Bar Admittee (in practice 0 - 6 months)</td>
											<td width="150" class="r b P">$0</td>
										</tr>	
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>										
										<tr class="frmRow2">
											<td width="25" class="c"><input type="radio" value="2" name="rateAnnually" onClick="showSponsors()" disabled="true" /></td>
											<td>New Member (in practice 7 months - 3 years)</td>
											<td width="150" class="r b P">$220</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="3" name="rateAnnually" onClick="showSponsors()" disabled="true" /></td>
											<td>Regular Member 1 (in practice 4 - 9 years)</td>
											<td class="r b P">$330</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="4" name="rateAnnually" onClick="showSponsors()" disabled="true" /></td>
											<td>Regular Member 2 (in practice 10+ years)</td>
											<td class="r b P">$575</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="5" name="rateAnnually" onClick="showSponsors()" /></td>
											<td>Sustaining Member</td>
											<td class="r b P">$950</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="6" name="rateAnnually" onClick="showSponsors()" /></td>
											<td>Season Pass</td>
											<td class="r b P">$1650</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="7" name="rateAnnually" onClick="showSponsors()" /></td>
											<td>Paralegal Affiliate Member</td>
											<td class="r b P">$150</td>
										</tr>
										<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow2">
											<td class="c"><input type="radio" value="8" name="rateAnnually" onClick="showSponsors()" /></td>
											<td>Law Student</td>
											<td class="r b P">$25</td>
										</tr>
										<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
										<tr class="frmRow1">
											<td class="c"><input type="radio" value="9" name="rateAnnually" onClick="showSponsors()" /></td>
											<td>Public Sector Member</td>
											<td class="r b P">$150</td>
										</tr>
									</table>
								</div>
							</div>
							
						<!--- SPONSOR: --->
							<div class="CPSection" id="tnajSponsor" style="display:none;">
								<div class="CPSectionTitle BB">Sponsor</div>
								<div class="tsAppBodyText frmRow1 frmText">
									<table width="100%">
										<tr class="frmRow1">
											<td class="P" width="170">
												<strong>*TTLA Member Sponsor:</strong>
											</td>
											<td class="P">
												<input size="60" name="sponsor" id="sponsor" type="text" value="" class="tsAppBodyText" />
											</td>
										</tr>
									</table>
								</div>
							</div>
							
							<!--- BUTTONS: --->					
							<div id="formButtons">
								<div style="padding:10px;">
									<div align="center" class="frmButtons">
										<input type="submit" value="Continue" name="btnSubmit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
									</div>
								</div>
							</div>
												
						</div>
						<cfinclude template="/model/cfformprotect/cffp.cfm" />
					</cfform>
	
					<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
					</cfif>
				</cfif>	
			</cfcase>
			
			<!--- PAYMENT INFO: --->
			<cfcase value="1">
				
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>

				<cfif application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=event.getValue('memberID'), typeID=local.membershipDuesTypeID)>
					<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no">
				</cfif>

				<cfset local.timeStamp 				= now() />
				<cfset local.showErrorMsg = false>
				
				<cftry>
					<cfset local.recordUpdated = false >
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.useMID) >
					<cfset local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getTrimValue('h_address1',''),
						address2=arguments.event.getTrimValue('h_address2',''), city=arguments.event.getTrimValue('h_city',''),
						stateID=arguments.event.getTrimValue('h_state',0), postalCode=arguments.event.getTrimValue('h_zip','')) >
					<cfset local.objSaveMember.setPhone(addresstype='Home Address', type='Phone', value=arguments.event.getTrimValue('h_phone','')) >
					<cfset local.objSaveMember.setMemberType(memberType='User') >
					<cfset local.objSaveMember.setMemberStatus(memberStatus='Active') >
					<cfif val(arguments.event.getTrimValue('raceEthnicity',0)) neq 0>
						<cfset local.objSaveMember.setCustomField(field='Race', valueID=val(arguments.event.getTrimValue('raceEthnicity',0))) >
					</cfif>
					<cfif val(arguments.event.getTrimValue('gender',0)) neq 0>
						<cfset local.objSaveMember.setCustomField(field='Gender', valueID=val(arguments.event.getTrimValue('gender',0))) >
					</cfif>
					<cfif val(arguments.event.getTrimValue('plaintiffPercent',0)) neq 0>
						<cfset local.objSaveMember.setCustomField(field='Personal Injury Percent', valueID=val(arguments.event.getTrimValue('plaintiffPercent',0))) >
					</cfif>
					<cfif val(arguments.event.getTrimValue('defensePercent',0)) neq 0>
						<cfset local.objSaveMember.setCustomField(field='Personal Injury Defense', valueID=val(arguments.event.getTrimValue('defensePercent',0))) >
					</cfif>
					<cfif val(arguments.event.getTrimValue('defensePercent',0) neq local.qryPDPctSpecial.valueID) AND val(arguments.event.getTrimValue('defenseAttorneyPercent',0)) neq 0>
						<cfset local.objSaveMember.setCustomField(field='Defense Attorney', valueID=val(arguments.event.getTrimValue('defenseAttorneyPercent',0))) >
					</cfif>
					<cfif val(arguments.event.getTrimValue('politAffil',0)) neq 0>
						<cfset local.objSaveMember.setCustomField(field='Political Affiliation', valueID=val(arguments.event.getTrimValue('politAffil',0))) >
					</cfif>
					<cfset local.objSaveMember.setCustomField(field='Oldest Bar Date', value=arguments.event.getTrimValue('barDate',0)) >
					<cfset local.objSaveMember.setCustomField(field='Preferred Salutation', value=arguments.event.getTrimValue('prefSalutation','')) >
					<cfset local.objSaveMember.setCustomField(field='Law School', value=arguments.event.getTrimValue('lawSchool','')) >
					<cfset local.objSaveMember.setCustomField(field='Referred By', value=arguments.event.getTrimValue('memberRef','')) >
					<cfset local.objSaveMember.setCustomField(field='House District No', value=arguments.event.getTrimValue('houseDistrict',0)) >
					<cfset local.objSaveMember.setCustomField(field='Senate District No', value=arguments.event.getTrimValue('senateDistrict',0)) >
					<cfset local.objSaveMember.setCustomField(field='Paralegal Attorney Sponsor', value=arguments.event.getTrimValue('sponsor','')) >
					<cfif (Len(arguments.event.getTrimValue('barNum',0)) gt 0) AND (Len(arguments.event.getTrimValue('barDate',0)) gt 0)>
						<cfset local.objSaveMember.setProLicense(name='Tennessee', status='Active', license=arguments.event.getTrimValue('barNum',''), date=arguments.event.getTrimValue('barDate',0)) >
					</cfif> 
					<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
					<cfif not local.strResult.success>
						<cfthrow message="Unable to save member info." >
					<cfelse>
						<cfset local.recordUpdated = true >
					</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch) >
						<cfset local.recordUpdated = false >
					</cfcatch>
				</cftry>	
				<cfif local.recordUpdated>
					<cfoutput>
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?#cgi.QUERY_STRING#">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
						</cfform>
						<script>
							document.forms['#local.formName#'].submit();
						</script>
					</cfoutput>

				<cfelse>
					<cfset local.showErrorMsg = true>
					<cfset local.errCode = 4>
				</cfif>
				
				<cfif local.showErrorMsg>
					<cfoutput>
						There was an error processing your application.  Please contact your association for assistance.
					</cfoutput>
				</cfif>
				
			</cfcase>
		
			<!--- PROCESS:  --->
			<cfcase value="2">

				<cfoutput>
					<script>
						function updateMember()
						{
							var er_changeErr = function(r) {
								document.getElementById('waitDiv').style.display 	= 'none'; 
								document.getElementById('errDiv').style.display 	= ''; 
							};
								
							var er_change = function(r) {
								var results = r;
								if( results.success ){
									document.forms['#local.formName#'].submit();
								} else { 
									document.getElementById('waitDiv').style.display 	= 'none'; 
									document.getElementById('errDiv').style.display 	= ''; 
								}
							};
	
							var objParams = { memberID:#event.getValue('memberID')# };
							TS_AJX('SUBS','updateGroupsForMember',objParams,er_change,er_changeErr,1000000,er_changeErr);
						}
					</script>
					<div id="waitDiv" name="waitDiv">
					<h4>Updating member record and processing application.</h4>
					<img src="/assets/common/images/loading-dots.gif">
					</div>
					<div id="errDiv" name="errDiv" style="display:none;">
					<h4>There was an error processing your application.  Please contact your association for assistance.</h4>
					</div>
					<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?#cgi.QUERY_STRING#">
						<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
						<cfloop collection="#arguments.event.getCollection()#" item="local.key">
							<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
								and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
								and left(local.key,4) neq "fld_">
								<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
							</cfif>
						</cfloop>
					</cfform>
					<script>
						updateMember();
					</script>
				</cfoutput>

			</cfcase>

			<cfcase value="3">

				<!--- Do subscriptions --->
				<cfset local.timeStamp 				= now() />
				<cfset local.showErrorMsg = false>

				<cfscript>
					if (event.getValue('payInterval') == 'Monthly'){
						switch(event.getValue('rateMonthly')){
							case '1': local.memberShipDues = 0;		local.memberShipName = 'New Bar Admittee';	break;
							case '2': local.memberShipDues = 20; 	local.memberShipName = 'New Member'; 		break;
							case '3': local.memberShipDues = 30; 	local.memberShipName = 'Regular Member 1'; 	break;
							case '4': local.memberShipDues = 50; 	local.memberShipName = 'Regular Member 2'; 	break;
							case '5': local.memberShipDues = 90; 	local.memberShipName = 'Sustaining Member'; break;
							case '6': local.memberShipDues = 150; 	local.memberShipName = 'Season Pass'; 		break;
							case '7': local.memberShipDues = 15; 	local.memberShipName = 'Paralegal Affiliate Member'; break;
						}
					}
					else if (event.getValue('payInterval') == 'Quarterly'){
						switch(event.getValue('rateQuarterly')){
							case '1': local.memberShipDues = 0;		local.memberShipName = 'New Bar Admittee';	break;
							case '2': local.memberShipDues = 60; 	local.memberShipName = 'New Member'; 	break;
							case '3': local.memberShipDues = 90; 	local.memberShipName = 'Regular Member 1'; 	break;
							case '4': local.memberShipDues = 150; 	local.memberShipName = 'Regular Member 2'; 	break;
							case '5': local.memberShipDues = 270; 	local.memberShipName = 'Sustaining Member'; break;
							case '6': local.memberShipDues = 450; 	local.memberShipName = 'Season Pass'; 		break;
							case '7': local.memberShipDues = 45; 	local.memberShipName = 'Paralegal Affiliate Member'; break;
						}
					}
					else if (event.getValue('payInterval') == 'Annually'){
						switch(event.getValue('rateAnnually')){
							case '1': local.memberShipDues = 0;		local.memberShipName = 'New Bar Admittee';	break;
							case '2': local.memberShipDues = 220; 	local.memberShipName = 'New Member'; 		break;
							case '3': local.memberShipDues = 330; 	local.memberShipName = 'Regular Member 1'; 	break;
							case '4': local.memberShipDues = 575; 	local.memberShipName = 'Regular Member 2'; 	break;
							case '5': local.memberShipDues = 950; 	local.memberShipName = 'Sustaining Member'; break;
							case '6': local.memberShipDues = 1650; 	local.memberShipName = 'Season Pass'; 		break;
							case '7': local.memberShipDues = 150; 	local.memberShipName = 'Paralegal Affiliate Member'; break;
							case '8': local.memberShipDues = 25; 	local.memberShipName = 'Law Student'; 		break;
							case '9': local.memberShipDues = 150; 	local.memberShipName = 'Public Sector Member'; 	break;
						}
					}
					local.totalAmount = local.memberShipDues;
				</cfscript>

				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & event.getValue('name','') />

				<cfquery name="local.qryCustomValues" datasource="#application.dsn.membercentral.dsn#">
					set nocount on
					declare @homeStateCode varchar(10), @raceEthnicity varchar(100), @gender varchar(100),
						@plaintiffPercent varchar(100), @defensePercent varchar(100), @defenseAttorneyPercent varchar(100),
						@politAffil varchar(100)
					
					Select @homeStateCode=s.code
					FROM dbo.ams_states s
					where s.stateid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('h_state')#">
					
					select @raceEthnicity=mdcv.columnValueString
					from dbo.ams_memberDataColumnValues mdcv
					inner join dbo.ams_memberDataColumns mdc 
						on mdc.columnID = mdcv.columnID
						and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						and columnName = 'Race'
						and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('raceEthnicity','0')#">
					order by mdcv.columnValueString

					select @gender=mdcv.columnValueString
					from dbo.ams_memberDataColumnValues mdcv
					inner join dbo.ams_memberDataColumns mdc 
						on mdc.columnID = mdcv.columnID
						and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						and columnName = 'Gender'
						and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('gender','0')#">
					order by mdcv.columnValueString

					select @plaintiffPercent=mdcv.columnValueString
					from dbo.ams_memberDataColumnValues mdcv
					inner join dbo.ams_memberDataColumns mdc 
						on mdc.columnID = mdcv.columnID
						and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						and columnName = 'Personal Injury Percent'
						and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('plaintiffPercent','0')#">
					order by mdcv.columnValueString

					select @defensePercent=mdcv.columnValueString
					from dbo.ams_memberDataColumnValues mdcv
					inner join dbo.ams_memberDataColumns mdc 
						on mdc.columnID = mdcv.columnID
						and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						and columnName = 'Personal Injury Defense'
						and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('defensePercent','0')#">
					order by mdcv.columnValueString

					select @defenseAttorneyPercent=mdcv.columnValueString
					from dbo.ams_memberDataColumnValues mdcv
					inner join dbo.ams_memberDataColumns mdc 
						on mdc.columnID = mdcv.columnID
						and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						and columnName = 'Defense Attorney'
						and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('defenseAttorneyPercent','0')#">
					order by mdcv.columnValueString

					select @politAffil=mdcv.columnValueString
					from dbo.ams_memberDataColumnValues mdcv
					inner join dbo.ams_memberDataColumns mdc 
						on mdc.columnID = mdcv.columnID
						and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						and columnName = 'Political Affiliation'
						and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('politAffil','0')#">
					order by mdcv.columnValueString

					select @homeStateCode as homeStateCode, @raceEthnicity as raceEthnicity, @gender as gender,
						@plaintiffPercent as plaintiffPercent, @defensePercent as defensePercent, 
						@defenseAttorneyPercent as defenseAttorneyPercent, @politAffil as politAffil
					set nocount off
					
				</cfquery>
				
				<cfset local.isAssociate = false>
				<cfif local.qryCustomValues.defenseAttorneyPercent eq '51% - 100% Civil Defense'>
					<cfset local.isAssociate = true>
				</cfif>				

				<cfsavecontent variable="local.invoice">
					#local.pageCSS#					
					<p>#local.formNameDisplay# submitted on #dateformat(local.timeStamp,"dddd, m/d/yyyy")# #timeformat(local.timeStamp,"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					
						<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">Name:</td><td class="frmText">#event.getValue('name')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Preferred Salutation:</td><td class="frmText">#event.getValue('prefSalutation')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm Name:</td><td class="frmText">#event.getValue('firm')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Office Address:</td><td class="frmText">#event.getValue('address')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Office Phone:</td><td class="frmText">#event.getValue('phone')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">E-mail:</td><td class="frmText">#event.getValue('email')#&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">PERSONAL INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">Home Address 1:</td><td class="frmText">#event.getValue('h_address1')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home Address 2:</td><td class="frmText">#event.getValue('h_address2')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">City:</td><td class="frmText">#event.getValue('h_city')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">State:</td><td class="frmText">#local.qryCustomValues.homeStateCode#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('h_zip')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home Phone:</td><td class="frmText">#event.getValue('h_phone')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Law School Attended:</td><td class="frmText">#event.getValue('lawSchool')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Bar Admission Date:</td><td class="frmText">#event.getValue('barDate')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">BPR Number:</td><td class="frmText">#event.getValue('barNum')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Referred for Membership by:</td><td class="frmText">#event.getValue('memberRef')#&nbsp;</td></tr>

						<tr class="frmRow1"><td class="frmText b">Race:</td><td class="frmText">#local.qryCustomValues.raceEthnicity#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Gender:</td><td class="frmText">#local.qryCustomValues.gender#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Plaintiff Work %:</td><td class="frmText">#local.qryCustomValues.plaintiffPercent#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Defense Work %:</td><td class="frmText">#local.qryCustomValues.defensePercent#&nbsp;</td></tr>

						<cfif event.getValue('defensePercent') neq local.qryPDPctSpecial.valueID>
							<tr class="frmRow1"><td class="frmText b">Defense Attorney Work %:</td><td class="frmText">#local.qryCustomValues.defenseAttorneyPercent#&nbsp;</td></tr>
						</cfif>
						<tr class="frmRow1"><td class="frmText b">State House District:</td><td class="frmText">#event.getValue('houseDistrict')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">State Senate District:</td><td class="frmText">#event.getValue('senateDistrict')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Political Affiliation:</td><td class="frmText">#local.qryCustomValues.politAffil#&nbsp;</td></tr>
						
						
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP RATE</td></tr>
						<tr class="frmRow1"><td class="frmText b">Payment Schedule:</td><td class="frmText">#event.getValue('payInterval')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">#local.memberShipName#:</td><td class="frmText">#dollarFormat(local.memberShipDues)#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">TOTAL:</td><td class="frmText">#dollarFormat(local.totalAmount)#&nbsp;</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>

					</table>
				</cfsavecontent>

				<cfset local.payInterval = event.getValue('payInterval')>
				<cfif local.payInterval eq 'Monthly'>
					<cfset local.memberValue = event.getValue('rateMonthly')>
					<cfset local.subFreqUID = local.monthlyFrequencyID>
				<cfelseif local.payInterval eq 'Quarterly'>
					<cfset local.memberValue = event.getValue('rateQuarterly')>
					<cfset local.subFreqUID = local.quarterlyFrequencyID>
				<cfelseif local.payInterval eq 'Annually'>
					<cfset local.memberValue = event.getValue('rateAnnually')>
					<cfset local.subFreqUID = local.fullFrequencyID>
				</cfif>
				
				<!--- CREATE SUBSCRIPTION --->
				<cfset local.subStruct = structNew()>
				<cfset local.subStruct.children = arrayNew(1)>
				<cfset local.subStruct.freqUID = local.subFreqUID>

				<cfif listFind("1,2,3,4,5,6",local.memberValue)>
					<cfset local.subStruct.uid = local.topAllAttySubUID>
						<cfif listFind("1,2,3,4",local.memberValue)>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = local.childBasicSubUID>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					<cfelseif local.memberValue eq 5>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = local.childSustainingSubUID>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					<cfelseif local.memberValue eq 6>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = local.childSeasonPassSubUID>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfif>
					<cfif local.isAssociate>
							<cfset local.childStruct = structNew()>
							<cfset local.childStruct.uid = local.childAssocSubUID>
							<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfif>
				<cfelseif local.memberValue eq 7>
					<cfset local.subStruct.uid = local.topParalegalSubUID>
				<cfelseif local.memberValue eq 8>
					<cfset local.subStruct.uid = local.topLawStudentSubUID>
				<cfelseif local.memberValue eq 9>
					<cfset local.subStruct.uid = local.topPubSectorSubUID>
				</cfif>

				<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
				<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=local.useMID, subStruct=local.subStruct, newAsBilled=true)>
				<cfif local.subReturn.success eq false>
					<!--- email association --->
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							The system was unable to create the subscriptions for this application and #event.getValue('name','')# was not sent an email confirmation.<br />
								<hr />
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
						if (arrayLen(local.arrEmailTo)) {
							local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="", email=local.ORGEmail.from },
								emailto=local.arrEmailTo,
								emailreplyto=local.ORGEmail.from,
								emailsubject=local.ORGEmail.SUBJECT,
								emailtitle=local.formNameDisplay,
								emailhtmlcontent=local.mailContent,
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
								messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
								sendingSiteResourceID=this.siteResourceID
							);
						}
					</cfscript>
					
					<cfset local.showErrorMsg = true>
					<cfset local.errCode = 3>
				<cfelse>				
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDirectLinkCode">
						select directLinkCode
						from dbo.sub_subscribers
						where subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
					</cfquery>

					<!--- email member --->
					<cfset local.emailSentToUser = TRUE />
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>Thank you for submitting your application!</p>
								<p>Below are your initial selections for your membership application. </p>
								<p>If you have NOT already done so, please <a href="#local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#">click here</a> 
										to pay and finalize your membership. 
								</p>								
								<p>
								If the link does not work for you, please copy the following link and paste into the address bar of your browser:<br>
								#local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#
								</p> 
								<p>
								If you have any questions, please contact the TTLA Member Services Department at (615) 329-3000. 
								</p> 
							<hr />
							#local.invoice#	
						</cfoutput>
					</cfsavecontent>
					
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from },
						emailto=[{ name="", email=local.memberEmail.to }],
						emailreplyto=local.ORGEmail.to,
						emailsubject=local.memberEmail.SUBJECT,
						emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=val(local.useMID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>
					
					<cfset local.emailSentToUser = local.responseStruct.success>

					<!--- email association --->
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<cfif NOT local.emailSentToUser>
								#event.getValue('name')# was not sent email confirmation due to bad Data.<br />
								Please contact, and let them know.
								Their confirmation link is: #local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#
								<hr />
							</cfif>
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
						if (arrayLen(local.arrEmailTo)) {
							local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="", email=local.ORGEmail.from },
								emailto=local.arrEmailTo,
								emailreplyto=local.ORGEmail.from,
								emailsubject=local.ORGEmail.SUBJECT,
								emailtitle=local.formNameDisplay,
								emailhtmlcontent=local.mailContent,
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
								messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
								sendingSiteResourceID=this.siteResourceID
							);
						}
					</cfscript>
					
					<cflocation url="/renewsub/#local.qryDirectLinkCode.directLinkCode#" addtoken="no">
				</cfif>					

				<cfif local.showErrorMsg>
					<cfoutput>
						There was an error processing your application.  Please contact your association for assistance.
					</cfoutput>
				</cfif>

			</cfcase>
			
			<!--- SPAM MESSAGE: --->
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>

