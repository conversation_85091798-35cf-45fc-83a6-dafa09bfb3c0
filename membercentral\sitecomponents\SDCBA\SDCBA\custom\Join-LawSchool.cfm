<cfscript>
	variables.applicationReservedURLParams 	= "TestMode";
	local.customPage.baseURL								= "/?#getBaseQueryString(false)#";
	
	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	local.arrCustomFields = [];
	
	local.tmpField = { name="formNameDisplay", type="STRING", desc="formNameDisplay", value="Law Student Membership Application" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="memberTypeUID", type="STRING", desc="memberTypeUID", value="D46DFDEA-6E46-4043-BA8F-F4AF38DE1B21" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="sectionTypeUID", type="STRING", desc="sectionTypeUID", value="061D8C07-2443-4424-B032-8BD7D6D3860C" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="lawStudentSubUID", type="STRING", desc="lawStudentSubUID", value="5179E656-B391-4EB3-9405-BD88FFF87FA0" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="donationSubUID", type="STRING", desc="donationSubUID", value="A740B467-29EB-4AD0-BCBC-C24AB5A5DB19" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="otherSchoolRateUID", type="STRING", desc="otherSchoolRateUID", value="DD51A684-E080-4B40-A7CB-EC5DA2A54034" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);	
	
	local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Staff Confirmation Email", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="memberEmailFrom", type="STRING", desc="Member Email From", value="<EMAIL>" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Payment Confirmation Message", value='
		<div class="HeaderText">Thank you for submitting your application!</div>
		<br/>
	' }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AccountLookUpTitle", type="STRING", desc="Account look up title", value="Account Lookup / Create New Account" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AccountLookUpButton", type="STRING", desc="Account look up Button Name", value="Account Lookup" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AccountLookUpContent", type="CONTENTOBJ", desc="Account look up Content", value='
		<div style="padding-bottom:5px;">Click the <span ><b>Account Lookup</b></span> button to the left.</div>
		<div style="padding-bottom:5px;">Enter the search criteria and click <span><b>Continue</b></span>.</div>
		<div style="padding-bottom:5px;">If you see your name, please press the <span ><b>Choose</b></span> button next to your name.</div>
		<div style="padding-bottom:5px;">If you do not see your name, click the <span ><b>Create an Account</b></span> link.</div>
	' };  
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);	

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
		formName='frmFB190',
		formNameDisplay='#local.strPageFields.FormNameDisplay#',
		orgEmailTo='#local.strPageFields.StaffConfirmationEmail#',
		memberEmailFrom='#local.strPageFields.memberEmailFrom#'
	));
	
	// LOCAL SITE INFORMATION ------------------------------------------------------------------------------------------------
	local.mainhostName 			= event.getValue('mc_siteInfo.mainhostName');	
	local.scheme 			= event.getValue('mc_siteInfo.scheme');	

	// custom data: ----------------------------------------------------------------------------------------------------------
	local.USStates 					= application.objCustomPageUtils.mem_getStatesByCountry('United States');	
</cfscript>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.backLink{background: ##e8e8e8;padding: 2px 10px;border: 1px solid gray;color: gray;border-radius: 2px;text-decoration: none;cursor: pointer;}
			.customPage{font-family: Verdana, Arial, Helvetica, sans-serif;}
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##eaeaea; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; font-weight:none; color:##666;}
			.frmButtons{ padding:5px 0; border-top:3px solid ##520e0d; border-bottom:3px solid ##520e0d; }
			.TitleText {  font-size:16pt; color:##4f6550; font-weight:bold;}
			.CPSection{  margin-bottom:15px; }
			a.calnavright,a.calnavleft{ text-decoration:none; }
			.CPSectionTitle { font-size:10pt; font-weight:bold; color:##fff; padding:8px; background-color:##0E568D; }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:5px; background-color:##cccccc; }
			.subCPSectionArea2 { padding:5px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:5px; background-color:##aaa;}
			.subCPSectionArea4 { padding:5px; background-color:##eaeaea;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold;}
			.subCPSectionText { font-size:8.5pt;}
			.info{ font-style:italic; font-size:8pt; color:##555; font-weight:normal;}
			.small{ font-size:7.5pt;}
			.r { text-align:right; }
			.l { text-align:left; }
			.cent { text-align:center; }
			.i { font-style:italic; }
			.bld { font-weight:bold; }
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			.msgHeader{background:##0E568D; color:##ffffff; font-weight:Bold; text-transform:uppercase; padding:5px;}
			.msgSubHeader{background:##dddddd;}
			select.tsAppBodyText{color:##666;}
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			.totalsContent{ width:450px; }
			.totalsItem{ border-bottom:1px solid ##777777; }
			.grandTotal{ font-weight:bold; }
			.totalsText{ display:inline-block; width:70%;padding:3px 0 0 5px; }
			.totalsValue{ display:inline-block; width:25%; text-align:right; padding:3px 5px 0 0; }
			##graddate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
			.info_div{ margin-left: 25px;margin-top: 15px;}
		</style>
	</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<cfsavecontent variable="local.pageJS">
		#local.pageJS#
		<script type="text/javascript">
			function resizeBox(newW,newH) {
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				$(function() {
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:360});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:360});
						}
					});
				});
		</script>
</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	#local.pageJS#
	#local.pageCSS#
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<div id="customPage">
		<div class="tsAppHeading formTitle">Law Student Membership Application</div>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryLawSchools">
			select mdcv.valueID, mdcv.columnValueString
			from dbo.ams_memberDataColumnValues mdcv
			inner join dbo.ams_memberDataColumns mdc 
				on mdc.columnID = mdcv.columnID
				and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
				and columnName = 'law_school'
			order by mdcv.columnValueString
		</cfquery>	
		<cfquery dbtype="query" name="local.otherShool">
			select valueID
			from [local].qryLawSchools
			where columnValueString = 'Other (not listed)'
		</cfquery>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">

			<cfcase value="-1">
				<cfset local.lawSchoolName = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=local.orgID, columnName='law_school', valueIDList=val(event.getValue('lawschool'))) />
				<!--- UPDATE MEMBER RECORD  --->
				<cftry>
					<cfif NOT isDate(event.getValue('graddate'))>
						<cflocation url="/?pg=Join-LawSchool" addtoken="false">
					</cfif>
					<cfset local.recordUpdated = false>
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid')) />
					<cfset local.objSaveMember.setCustomField(field='Member_Type', value='LS') />
					<cfset local.objSaveMember.setCustomField(field='law_school', value=local.lawSchoolName) />
					<cfset local.objSaveMember.setCustomField(field='law_school_date', value=event.getValue('graddate')) />
					<cfset local.objSaveMember.setCustomField(field='Gender', value=event.getValue('gender')) />
					<cfset local.objSaveMember.setMemberType(memberType='User') />
					<cfset local.newMemberNumber = 'LS' & event.getValue('memberNumber')>
					<cfset local.objSaveMember.setDemo(membernumber=local.newMemberNumber)>
					<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
					<cfif local.strResult.success>
						<cfset local.recordUpdated = true>	
					<cfelse>					
						<cfset local.recordUpdated = false>	
					</cfif>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.recordUpdated = false>
				</cfcatch>
				</cftry>

				<cfset local.accreditedQry = application.objCustomPageUtils.mem_getGroups(event.getValue('memberID'),local.orgID,'StudentUN')>		
				
				<cfif local.accreditedQry.recordCount eq 0>
					<cfset local.accreditedSchool = false>
				<cfelse>
					<cfset local.accreditedSchool = true>
				</cfif>

				<!--- UPDATE MEMBER RECORD 2  --->
				<cftry>
					<cfset local.recordUpdated2 = false>
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid')) />
					<cfif local.accreditedSchool>	
						<cfset local.objSaveMember.setCustomField(field='Member_Category', value='LS|LSUN') />
					<cfelse>	
						<cfset local.objSaveMember.setCustomField(field='Member_Category', value='LS|LS') />
					</cfif>
					<cfset local.strResult2 = local.objSaveMember.saveData(runImmediately=1)>
					<cfif local.strResult2.success>
						<cfset local.recordUpdated2 = true>	
					<cfelse>					
						<cfset local.recordUpdated2 = false>	
					</cfif>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.recordUpdated2 = false>
				</cfcatch>
				</cftry>

				<script type="text/javascript">

					function getTotalDue(){
						
						var thisForm = document.forms["frmFB190"];
						var mLevel 	= 0;
						var sCount 	= 0;				
						var mTotal 	= 0;
						var sTotal 	= 0;
						var dTotal 	= 0;
						var total		= 0;				
						for (var i=0; i < thisForm.membership.length; i++){ if (thisForm.membership[i].checked){ mLevel = thisForm.membership[i].value; } }
						// membership total  memberShipTotal

						switch (mLevel){
							case "1": 	mTotal = 0;  	break;	// First Year Students
							case "2": 	mTotal = 40;  break;	// Second Year Students
								// Other Law School Students
							default:	mTotal = 0;						// default value
						}				
						// section total  sectionTotal
						// get total sections
						// 10-27-2014 Sections are free per ticket 8441182
						var sectionPrice = 0;
						for (var i=0; i < thisForm.sections.length; i++){ if (thisForm.sections[i].checked){ sCount = sCount + 1 ; } }
						if( sCount > 3 ){ sCount = sCount - 3; }
						else{ sCount = 0; }
						sTotal = sCount * sectionPrice;

						<!--- donation total --->
						
						dTotal = 0;
						//totalDue
						total = mTotal + sTotal + dTotal;
						document.getElementById('memberShipTotal').innerHTML = mTotal;
						document.getElementById('sectionTotal').innerHTML = sTotal;
						document.getElementById('totalDue').innerHTML = total;
					}
					function _FB_validateLSForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						
						if (!_FB_hasValue(thisForm['membership'], 'RADIO')) arrReq[arrReq.length] 		= 'Dues Arrangement';

						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						
						return true;
					}
					$(document).ready(function(){
						getTotalDue();
					});
				</script>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.sections">
					set nocount on
					declare @typeUID varchar(max), @siteID int
					
					select @typeUID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strPageFields.sectionTypeUID#">
					select @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
					
					select subs.uid, REPLACE(subscriptionName, ' Section', '') as subscriptionName
					from dbo.sub_subscriptions subs
					inner join dbo.sub_types t
						on t.typeID = subs.typeID
						and t.uid = @typeUID
						and t.siteID = @siteID
					where subscriptionID IN (
						select subscriptionID
						from sub_subscriptionSets subSets
						where setID = (select top 1 setID from sub_sets where setName = 'Sections' and siteID=@siteID)
					)
					order by subs.subscriptionName
					set nocount off
				</cfquery>
				<cfscript>
					local.section.recordCount		= local.sections.recordCount;
					local.section.column1				= round((local.section.recordCount + .49) / 2); 
				</cfscript>
				<div class="tsAppBodyText formClose">

				<div class="tsAppBodyText formIntro"><span class="TSAppsBodyText"<em>*<small>Denotes required field</small></em></span></div>

				<cfform name="frmFB190" id="frmFB190" method="POST" action="/?#cgi.QUERY_STRING#" onsubmit="return _FB_validateLSForm();">
					<cfif local.recordUpdated>
						<cfinput type="hidden" name="hasUpdated" value="1" />
					<cfelse>
						<cfinput type="hidden" name="hasUpdated" value="0" />	
					</cfif>
					<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1" />
					<cfset fieldArr = 	{}>

					<cfloop collection="#arguments.event.getCollection()#" item="local.key">
						<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
							and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
							and left(local.key,9) neq "formfield"
							and left(local.key,4) neq "fld_">
							<cfset fieldArr[local.key] = 	arguments.event.getValue(local.key)>
							<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
						</cfif>
					</cfloop>
					<cfset session.fieldArr = fieldArr>
					
					<div id="step3" >
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">Membership Dues Schedule</div>
									<div class="tsAppBodyText sectionDesc">
									Law student membership dues run on a modified school-year calendar year from September through August and are renewable annually.
									</div>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr>
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText">
												<table>
													<tr>
														<td>
															<cfinput class="tsAppBodyText optionsRadio" name="membership"  id="membership" type="radio" onClick="getTotalDue()" checked="checked" value="1"  disabled="true"> Law Students enrolled in ABA accredited and CA State Bar approved law schools: Free<br />
															<div class="info_div">
																Law Students in the State Bar of CA approved judicial/attorney study program should call the SDCBA Member Services Dept. or use the <a target="_blank" href="/docDownload/43411">PDF copy</a> for Law Student application and send the form and document from the State Bar: Free
															</div>
															<cfinput class="tsAppBodyText optionsRadio" name="membership"  id="membership" type="radio" checked="#local.accreditedSchool#" onClick="getTotalDue()" value="2"  disabled="true"> Law Students at other schools:  $40<br />
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
								<br />
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">SDCBA Sections</div>
									<table cellspacing="0" cellpadding="2" border="0" width="600">
										<tr valign="top">
											<td class="tsAppBodyText questionText" colspan="2">Sections are formed by the SDCBA for the presentation, discussion and study of subjects by an interested group of members. Now with your basic membership, dues for section membership is free.</td>
										</tr>
										<tr>
											<td class="tsAppBodyText questionNumber" width="10"></td>
											<td class="tsAppBodyText optionsVertical">
												<table width="100%" cellspacing="0" cellpadding="0" border="0">
													<tr>
														<td class="tsAppBodyText optionsText" width="50%" valign="top">
															<cfloop query="local.sections" startRow="1" endRow="#local.section.column1#">
																<input  class="tsAppBodyText optionsCheckbox" name="sections" type="checkbox" onClick="getTotalDue()" value="#local.sections.uid#">#local.sections.subscriptionName#<br />
															</cfloop>
														</td>
														<td class="tsAppBodyText optionsText" width="50%" valign="top">
															<cfloop query="local.sections" startRow="#local.section.column1 + 1#" endRow="#local.section.recordCount#">
																<input  class="tsAppBodyText optionsCheckbox" name="sections" type="checkbox" onClick="getTotalDue()" value="#local.sections.uid#">#local.sections.subscriptionName#<br />
															</cfloop>
														</td>
													</tr>									
												</table>
											</td>
										</tr>
									</table>
								</div>
								<br />
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">SDCBA Committees</div>
									<table cellspacing="0" cellpadding="2">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText">
												<a href="/?pg=CommitteeList" onClick="window.open(this, 'Test', 'width=350,height=575,scrollbars=yes');return false;" class="tsAppBodyText">Click here</a> for more information about SDCBA committees which can be joined at no additional charge.
											</td>
										</tr>
									</table>	
								</div>
								<br />
								
								<br />
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">Dues Information *</div>
									<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
										<tr>
											<td class="tsAppBodyText">
												<table border="0">
													<tr>
														<td class="tsAppBodyText" width="125">Membership Dues:</td>
														<td class="tsAppBodyText" width="75" align="right">$<span id="memberShipTotal"></span></td>
													</tr>
													<tr>
														<td class="tsAppBodyText">Sections:</td>
														<td class="tsAppBodyText" align="right">$<span id="sectionTotal"></span></td>
													</tr>
														
													<tr style="border-top:1px solid ##ccc;">
														<td class="tsAppBodyText"><strong>Total Amount Due:</strong></td>
														<td class="tsAppBodyText" align="right"><strong>$<span id="totalDue"></span></strong></td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
								<div class="tsAppBodyText formClose">SDCBA is a tax-exempt 501(c)(6) organization.  Dues are not tax deductible as a charitable contribution for federal income tax purposes but may be tax deductible under other provisions of the Internal Revenue Service Code.  The SDCBA estimates that up to 1.0% of your membership dues may support trial court funding lobbying efforts and therefore is not deductible.   For more information, please consult your tax advisor.</div>
								<br />
								<div class="actionBox" id="step3">
									<a id="gotoStep2" class="backLink" href="/?pg=join-LawSchool&isSubmitted=back">Back</a>
									<input class="tsAppBodyText formButton" name="btnSubmit" type="Submit" value="Submit">
								</div>
								<div>
									<input name="FBFormID" type="hidden" value="190">
									<input name="FBAction" type="hidden" value="postForm">
								</div>
					</div>
					
				</cfform>
				
			</cfcase>

			<!--- FORM: --->
			<cfcase value="0">
				
				<script type="text/javascript">

					
					function _FB_validateLSForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						if (!_FB_hasValue(thisForm['memberNumber'], 'TEXT')) arrReq[arrReq.length] 		= 'Please Create an Account';

						if (!_FB_hasValue(thisForm['year'], 'RADIO')) arrReq[arrReq.length] 					= 'Student Year';
						
						if ((!_FB_hasValue(thisForm['lawschool'], 'TEXT')) || (thisForm['lawschool'].value == '' )) {
							arrReq[arrReq.length] 			= 'Law School';
						} 

						if(thisForm['lawschool'].value == #local.otherShool.valueID#){
							if ((!_FB_hasValue(thisForm['other_school_name'], 'TEXT')) || (thisForm['other_school_name'].value == '' )) {
								arrReq[arrReq.length] 			= 'Other Law School';
							} 
						}
						
						if (!_FB_hasValue(thisForm['graddate'], 'TEXT')) arrReq[arrReq.length] 				= 'Graduation Date';
						if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] 				= 'Address';
						if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] 						= 'City';

						if ((!_FB_hasValue(thisForm['state'], 'TEXT')) || (thisForm['state'].value == '' )) {
							arrReq[arrReq.length] 			= 'State';
						} 

						if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] 						= 'Zip';
						if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] 					= 'Phone Number';
						if (!_FB_hasValue(thisForm['gender'], 'RADIO')) arrReq[arrReq.length] 				= 'Gender';
						if (!_FB_hasValue(thisForm['email'], 'TEXT')) {
							arrReq[arrReq.length] = 'Primary Email';
						} else {
							var urlRegEx = new RegExp("#application.regEx.email#", "gi");
							if(!(urlRegEx.test(thisForm['email'].value))) 
								arrReq[arrReq.length] = "Primary Email";
						}
							
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						
						return true;
					}

					function assignMemberData(memObj){
						var thisForm = document.forms["#local.formName#"];
						var er_changeErr = function(r) {
							alert('error');
						};
						
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								thisForm['firstName'].value 		= results.firstname;
								thisForm['lastName'].value 			= results.lastname;
								
								document.getElementById('divName').innerHTML = results.firstname + ' ' + results.lastname;
								
								thisForm['email'].value = results.email;								
								
								thisForm['address'].value 			= results.address1;
								thisForm['address2'].value 			= results.address2;
								thisForm['city'].value 				= results.city;
								thisForm['zip'].value 				= results.postalcode;
								thisForm['phone'].value 			= results.phone;
								thisForm['memberNumber'].value 		= results.membernumber;
								thisForm['memberID'].value 			= results.memberid;
								
								var str = results.law_school_date;
								if(str!="" && typeof str!='undefined'){
									var date = new Date(str),mnth = ("0"+(date.getMonth()+1)).slice(-2),day=("0"+(date.getDate())).slice(-2);
									var newDate = [mnth,day,date.getFullYear()].join("/");									
									thisForm['graddate'].value 			= newDate;
								}
								
								var gender = thisForm['gender'];
								for (var i=0; i <= gender.length-1; i++) {
									if (gender[i].value == results.gender) gender[i].checked = true;
								}

								var lawschool = thisForm['lawschool'];
								for (var i=0; i <= lawschool.length-1; i++) {
									if (lawschool[i].text == results.law_school) lawschool[i].selected = true;
								}

								var stateCode = thisForm['state'];
								for (var i=0; i <= stateCode.length-1; i++) {
									if (stateCode[i].value == results.statecode) stateCode[i].selected = true;
								}

								// un hide form   
								if (r.types['#LCase(local.strPageFields.memberTypeUID)#'] != 1)
								{
									document.getElementById('studentInfo').style.display 				= '';
									document.getElementById('memberExisting').style.display 			= 'none';
								}
								else
								{
									document.getElementById('studentInfo').style.display 				= 'none';
									document.getElementById('memberExisting').style.display 			= '';
								}
							}
							else{ alert('not success'); }
							
						};

						var customFields = [];
						customFields[0] = 'law_school';
						customFields[1] = 'law_school_date';
						customFields[2] = 'gender';
						var typeUIDs = '#local.strPageFields.memberTypeUID#'.split(',');

						var objParams = { memberNumber:memObj.memberNumber, typeUIDs:typeUIDs, customFields:customFields };
						console.log(objParams);
						TS_AJX('SUBS','getMemberDataByMemberNumberWithSubTypes',objParams,er_change,er_changeErr,20000,er_changeErr);
					}
					
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					
					$(document).ready(function(){

						$("##lawschool").change(function(){
							var selSchool = $(this).val();

							if(selSchool == #local.otherShool.valueID#){
								$("##other_school_id").show();
								
							}else{
								$("##other_school_id").hide();
								
							}
						});
						
						mca_setupDatePickerField('graddate');
						
					});					
				</script>
				
				<br />
				<div class="tsAppBodyText formClose">

				<div class="tsAppBodyText formIntro"><span class="TSAppsBodyText"><em>*<small>Denotes required field</small></em></span></div>

				<cfform name="frmFB190" id="frmFB190" method="POST" action="/?#cgi.QUERY_STRING#" onsubmit="return _FB_validateLSForm();">
					<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="-1" />
					<cfinput type="hidden" name="memberID"  id="memberID" value="#session.cfcUser.memberData.memberID#">
					<cfinput type="hidden" name="memberNumber"  id="memberNumber" value="#session.cfcUser.memberData.memberNumber#" size="30" />

					<!--- CONTACT INFORMATION:  --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">#local.strPageFields.AccountLookUpTitle#</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="cent">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">#local.strPageFields.AccountLookUpButton#</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												#local.strPageFields.AccountLookUpContent#
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>
					<div id="studentInfo" name="studentInfo" style="display:none;">			
						<div class="page">
							<div id="step2">
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">Student Information</div>
									<table cellspacing="0" cellpadding="2" border="0" width="90%">
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">Full Name:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<div id="divName" name="divName"></div>
												<cfinput value="" name="firstName"  id="firstName" type="hidden" />
												<cfinput value="" name="lastName"  id="lastName" type="hidden" />
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Primary Email:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">												
												<cfinput value="" name="email"  id="email" type="text" />
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right"  width="35%">* Student Year:&nbsp;</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfinput value="1st" class="tsAppBodyText largeBox" name="year"  id="year" type="radio"> 1st Year Student<br />
												<cfinput value="2nd" class="tsAppBodyText largeBox" name="year"  id="year" type="radio"> 2nd Year Student<br />
												<cfinput value="3rd" class="tsAppBodyText largeBox" name="year"  id="year" type="radio"> 3rd Year Student<br />
												<cfinput value="Other" class="tsAppBodyText largeBox" name="year"  id="year" type="radio"> Other Law Student &nbsp;&nbsp;<cfinput value="" class="tsAppBodyText largeBox" name="year_txt"  id="year_txt" type="text">
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Law School:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfselect class="tsAppBodyText largeBox" name="lawschool"  id="lawschool">
													<option value = ''>--Select a Law School</option>
													<cfloop query="local.qryLawSchools">
														<option value="#local.qryLawSchools.valueID#">#local.qryLawSchools.columnValueString#</option>
													</cfloop>
												</cfselect>
											</td>
										</tr>
										<tr style="display:none" id="other_school_id" valign="top">
											<td class="tsAppBodyText questionText" align="right">* State School name:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfinput value="" class="tsAppBodyText largeBox" name="other_school_name"  id="other_school_name" type="text">
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Anticipated Completion Date:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfinput value="" class="tsAppBodyText largeBox" name="graddate" id="graddate" type="text" validate="date" message="Please enter a valid Graduation Date." />
												<a href="javascript:mca_clearDateRangeField('graddate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right" colspan="4"></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="left" colspan="4">SDCBA Law Student Members receive monthly publications. Send mail to: </td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Address:</td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="" class="tsAppBodyText largeBox" name="address"  id="address" type="text" size="65"></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">Address 2:</td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="" class="tsAppBodyText largeBox" name="address2"  id="address2" type="text" size="65"></td>
										</tr>
										<tr>
											<td class="tsAppBodyText questionText" align="right">* City:</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="city"  id="city" type="text" /></td>
											<td class="tsAppBodyText questionText" align="right">* State:</td>
											<td class="tsAppBodyText optionsInline">
												<select name="state">
													<option value = ''>--Select a State</option>
													<cfloop query="local.USStates">
														<option value="#local.USStates.code#">#local.USStates.Name#</option>
													</cfloop>
												</select>
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Zip:</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="zip"  id="zip" type="text"></td>
											<td class="tsAppBodyText questionText" align="right">* Phone:</td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="" class="tsAppBodyText largeBox" name="phone"  id="phone" type="text" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right" colspan="4"><br><i>Mailing address provided will not be saved. This is for SDCBA staff use only.</i></td>
										</tr>										
									</table>
								</div>
								<br />
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">Demographic Information *</div>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" align="right" width="205" valign="middle">Gender:</td>
											<td class="tsAppBodyText optionsInline">
												<table border="0">
													<tr>
														<td class="tsAppBodyText optionsText"><cfinput class="tsAppBodyText optionsRadio" name="gender"  id="gender" type="radio" value="Male" /></td>
														<td class="tsAppBodyText optionsText">Male</td>
														<td class="tsAppBodyText optionsText"><cfinput class="tsAppBodyText optionsRadio" name="gender"  id="gender" type="radio" value="Female"></td>
														<td class="tsAppBodyText optionsText">Female</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
								<br />

								<div class="actionBox">
									<input type="submit" value="Continue">
								</div>
							</div>	
						</div>
					</div>
					<div id="memberExisting" name="memberExisting" style="display:none;">
						<div class="tsAppBodyText formClose">
							You currently have a membership subscription.  If you believe that you should be able to sign up for a Law Student Membership, please contact SDCBA.
						</div>
					</div>
					<cfinclude template="/model/cfformprotect/cffp.cfm" />
				</cfform>
				</div>
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
			</cfcase>

			<cfcase value="back">
				<script type="text/javascript">
					function _FB_validateLSForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						
						if (!_FB_hasValue(thisForm['memberNumber'], 'TEXT')) arrReq[arrReq.length] 		= 'Please Create an Account';

						if (!_FB_hasValue(thisForm['year'], 'RADIO')) arrReq[arrReq.length] 					= 'Student Year';
						
						if ((!_FB_hasValue(thisForm['lawschool'], 'TEXT')) || (thisForm['lawschool'].value == '' )) {
							arrReq[arrReq.length] 			= 'Law School';
						} 

						if(thisForm['lawschool'].value == #local.otherShool.valueID#){
							if ((!_FB_hasValue(thisForm['other_school_name'], 'TEXT')) || (thisForm['other_school_name'].value == '' )) {
								arrReq[arrReq.length] 			= 'Other Law School';
							} 
						}
						
						if (!_FB_hasValue(thisForm['graddate'], 'TEXT')) arrReq[arrReq.length] 				= 'Graduation Date';
						if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] 				= 'Address';
						if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] 						= 'City';

						if ((!_FB_hasValue(thisForm['state'], 'TEXT')) || (thisForm['state'].value == '' )) {
							arrReq[arrReq.length] 			= 'State';
						} 

						if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] 						= 'Zip';
						if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] 					= 'Phone Number';
						if (!_FB_hasValue(thisForm['gender'], 'RADIO')) arrReq[arrReq.length] 				= 'Gender';
						if (!_FB_hasValue(thisForm['email'], 'TEXT')) {
							arrReq[arrReq.length] = 'Primary Email';
						} else {
							var urlRegEx = new RegExp("#application.regEx.email#", "gi");
							if(!(urlRegEx.test(thisForm['email'].value))) 
								arrReq[arrReq.length] = "Primary Email";
						}
							
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						
						return true;
					}

					function assignMemberData(memObj){
						var thisForm = document.forms["#local.formName#"];
						var er_changeErr = function(r) {
							alert('error');
						};
						
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								thisForm['firstName'].value 		= results.firstname;
								thisForm['lastName'].value 			= results.lastname;
								
								document.getElementById('divName').innerHTML = results.firstname + ' ' + results.lastname;
								
								thisForm['email'].value = results.email;							
								
								thisForm['address'].value 			= results.address1;
								thisForm['address2'].value 			= results.address2;
								thisForm['city'].value 				= results.city;
								thisForm['zip'].value 				= results.postalcode;
								thisForm['phone'].value 			= results.phone;
								thisForm['memberNumber'].value 		= results.membernumber;
								thisForm['memberID'].value 			= results.memberid;
								
								var str = results.law_school_date;
								if(str!="" && typeof str!='undefined'){
									var date = new Date(str),mnth = ("0"+(date.getMonth()+1)).slice(-2),day=("0"+(date.getDate()+1)).slice(-2);
									var newDate = [day,mnth,date.getFullYear()].join("/");
									
									thisForm['graddate'].value 			= newDate; 

								}
								
								var gender = thisForm['gender'];
								for (var i=0; i <= gender.length-1; i++) {
									if (gender[i].value == results.gender) gender[i].checked = true;
								}

								var lawschool = thisForm['lawschool'];
								for (var i=0; i <= lawschool.length-1; i++) {
									if (lawschool[i].text == results.law_school) lawschool[i].selected = true;
								}

								var stateCode = thisForm['state'];
								for (var i=0; i <= stateCode.length-1; i++) {
									if (stateCode[i].value == results.statecode) stateCode[i].selected = true;
								}

								// un hide form   
								if (r.types['#LCase(local.strPageFields.memberTypeUID)#'] != 1)
								{
									document.getElementById('studentInfo').style.display 				= '';
									document.getElementById('memberExisting').style.display 			= 'none';
								}
								else
								{
									document.getElementById('studentInfo').style.display 				= 'none';
									document.getElementById('memberExisting').style.display 			= '';
								}
							}
							else{ alert('not success'); }
							
						};

						var customFields = [];
						customFields[0] = 'law_school';
						customFields[1] = 'law_school_date';
						customFields[2] = 'gender';
						var typeUIDs = '#local.strPageFields.memberTypeUID#'.split(',');

						var objParams = { memberNumber:memObj.memberNumber, typeUIDs:typeUIDs, customFields:customFields };
						console.log(objParams);
						TS_AJX('SUBS','getMemberDataByMemberNumberWithSubTypes',objParams,er_change,er_changeErr,20000,er_changeErr);
					}
					
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}			
					
					$(document).ready(function(){

						$("##lawschool").change(function(){
							var selSchool = $(this).val();
							if(selSchool != '' && selSchool == #local.otherShool.valueID#){
								$("##other_school_id").show();
								selMembership = '2';
							}else{
								$("##other_school_id").hide();
								selMembership = '1';
							}
						});
						
						var selSchool = $("##lawschool").val();
						if(selSchool != '' && selSchool == #local.otherShool.valueID#){
							$("##other_school_id").show();
							selMembership = '2';
						}else{
							$("##other_school_id").hide();
							selMembership = '1';
						}						
	
						mca_setupDatePickerField('graddate');
					});					
				</script>
				
				<br />
				<div class="tsAppBodyText formClose">

				<div class="tsAppBodyText formIntro"><span class="TSAppsBodyText"><em>*<small>Denotes required field</small></em></span></div>

				<cfform name="frmFB190" id="frmFB190" method="POST" action="/?#cgi.QUERY_STRING#" onsubmit="return _FB_validateLSForm();">
					<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="-1" />
					<cfinput type="hidden" name="memberID"  id="memberID" value="#session.fieldArr.memberID#">
					<cfinput type="hidden" name="memberNumber"  id="memberNumber" value="#session.fieldArr.memberNumber#" size="30" />

					<!--- CONTACT INFORMATION:  --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">#local.strPageFields.AccountLookUpTitle#</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="cent">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">#local.strPageFields.AccountLookUpButton#</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												#local.strPageFields.AccountLookUpContent#
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>
					<div id="studentInfo" name="studentInfo">			
						<div class="page">
							<div id="step2">
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">Student Information</div>
									<table cellspacing="0" cellpadding="2" border="0" width="90%">
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">Full Name:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<div id="divName" name="divName">#session.fieldArr.firstname# #session.fieldArr.lastname#</div>
												<cfinput name="firstName"  id="firstName" type="hidden" value="#session.fieldArr.firstname#"/>
												<cfinput name="lastName"  id="lastName" type="hidden" value="#session.fieldArr.lastname#" />
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Primary Email:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												
												<cfinput  name="email"  id="email" type="text" value="#session.fieldArr.email#"/>
											</td>
										</tr>

										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right"  width="35%">* Student Year:&nbsp;</td>
											<td class="tsAppBodyText optionsInline" colspan="3">

												<input value="1st" class="tsAppBodyText largeBox" name="year" id="year" type="radio" <cfif #session.fieldArr.year# eq '1st'><cfoutput>checked=true</cfoutput></cfif> > 1st Year Student<br />
												<input value="2nd" class="tsAppBodyText largeBox" name="year"  id="year" type="radio"<cfif #session.fieldArr.year# eq '2nd'><cfoutput>checked=true</cfoutput></cfif>> 2nd Year Student<br />
												<input value="3rd" class="tsAppBodyText largeBox" name="year"  id="year" type="radio" <cfif #session.fieldArr.year# eq '3rd'><cfoutput>checked=true</cfoutput></cfif>> 3rd Year Student<br />
												<input value="Other" class="tsAppBodyText largeBox" name="year"  id="year" type="radio" <cfif #session.fieldArr.year# eq 'other'><cfoutput>checked=true</cfoutput></cfif> >Other Law Student &nbsp;&nbsp;
												<cfinput value="#session.fieldArr.year_txt#" class="tsAppBodyText largeBox" name="year_txt"  id="year_txt" type="text">
											</td>
										</tr>

										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Law School:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfselect class="tsAppBodyText largeBox" name="lawschool"  id="lawschool">
													<option value = ''>--Select a Law School</option>
													<cfloop query="local.qryLawSchools">
														<option value="#local.qryLawSchools.valueID#" <cfif session.fieldArr.lawschool eq local.qryLawSchools.valueID><cfoutput>selected=selected</cfoutput></cfif>>#local.qryLawSchools.columnValueString#</option>
													</cfloop>
												</cfselect>
											</td>
										</tr>
										
											
										
										<tr <cfif #session.fieldArr.lawschool# neq "other">style="display:none"</cfif> id="other_school_id" valign="top">
											<td class="tsAppBodyText questionText" align="right">* State School name:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfinput value="#session.fieldArr.other_school_name#" class="tsAppBodyText largeBox" name="other_school_name"  id="other_school_name" type="text">
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Anticipated Completion Date:</td>
											<td class="tsAppBodyText optionsInline" colspan="3">
												<cfinput value="#session.fieldArr.graddate#" class="tsAppBodyText largeBox" name="graddate" id="graddate" type="text" validate="date" message="Please enter a valid Graduation Date." />
												<a href="javascript:mca_clearDateRangeField('graddate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right" colspan="4"></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="left" colspan="4">SDCBA Law Student Members receive monthly publications. Send mail to: </td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Address:</td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="#session.fieldArr.address#" class="tsAppBodyText largeBox" name="address"  id="address" type="text" size="65"></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">Address 2:</td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="#session.fieldArr.address2#" class="tsAppBodyText largeBox" name="address2"  id="address2" type="text" size="65"></td>
										</tr>
										<tr>
											<td class="tsAppBodyText questionText" align="right">* City:</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="#session.fieldArr.city#" class="tsAppBodyText largeBox" name="city"  id="city" type="text" /></td>
											<td class="tsAppBodyText questionText" align="right">* State:</td>
											<td class="tsAppBodyText optionsInline">
												<select name="state">
													<option value = ''>--Select a State</option>
													<cfloop query="local.USStates">
														<option value="#local.USStates.code#" <cfif #session.fieldArr.state# eq #local.USStates.code#>selected=selected</cfif>>#local.USStates.Name#</option>
													</cfloop>
												</select>
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText" align="right">* Zip:</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="#session.fieldArr.zip#" class="tsAppBodyText largeBox" name="zip"  id="zip" type="text"></td>
											<td class="tsAppBodyText questionText" align="right">* Phone:</td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="#session.fieldArr.phone#" class="tsAppBodyText largeBox" name="phone"  id="phone" type="text" /></td>
										</tr>
									</table>
								</div>
								<br />
								<div class="section">
									<div class="tsAppLegendTitle sectionTitle">Demographic Information *</div>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" align="right" width="205" valign="middle">Gender:</td>
											<td class="tsAppBodyText optionsInline">
												<table border="0">
													<tr>
														<td class="tsAppBodyText optionsText"><input class="tsAppBodyText optionsRadio" name="gender"  id="gender" type="radio" value="Male" <cfif #session.fieldArr.gender# eq "Male">checked=checked</cfif> /></td>
														<td class="tsAppBodyText optionsText">Male</td>
														<td class="tsAppBodyText optionsText"><input class="tsAppBodyText optionsRadio" name="gender"  id="gender" type="radio" value="Female" <cfif #session.fieldArr.gender# eq "Female">checked=checked</cfif>></td>
														<td class="tsAppBodyText optionsText">Female</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
								<br />

								<div class="actionBox">
									<input type="submit" value="Continue">
								</div>
							</div>	
						</div>
					</div>
					<div id="memberExisting" name="memberExisting" style="display:none;">
						<div class="tsAppBodyText formClose">
							You currently have a membership subscription.  If you believe that you should be able to sign up for a Law Student Membership, please contact SDCBA.
						</div>
					</div>
					<cfinclude template="/model/cfformprotect/cffp.cfm" />
				</cfform>
				</div>
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
			</cfcase>
			
			<!--- PAYMENT INFO: --->
			<cfcase value="1">
				
				<!--- Build the subscriptions, status Billed and redirect to Front end subscription payment
					
					Member should be created at this point, so get the memberID
					Update the law school and graduation date custom fields
					send the member through group processing
					build subscriptions
					If error, show error and contact info
					if success, redirect to Front end subscription payment
				--->
				<cfset local.timeStamp 		= now() />
				<cfset local.showErrorMsg = false>

				<cfif event.getValue('hasUpdated','') eq 1>

					<cfoutput>
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?#cgi.QUERY_STRING#">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
						</cfform>
						<script>
							document.forms['#local.formName#'].submit();
						</script>
					</cfoutput>
				<cfelse>				
					<cfset local.showErrorMsg = true>
					<cfset local.errCode = 4>
				</cfif>
				
				<cfif local.showErrorMsg>
					<cfoutput>
						There was an error processing your application.  Please contact SDCBA for assistance.
					</cfoutput>
				</cfif>

			</cfcase>

			<cfcase value="2">

				<!--- Show wait screen, call javascript function that calls ajax method.  On success submits form below.
					On timeout or error, change wait screen innerHTML to "sorry, contact SDCBA" message.
				--->
				<cfoutput>
					<script>
						function updateMember()
						{
							var er_changeErr = function(r) {
								document.getElementById('waitDiv').style.display 	= 'none'; 
								document.getElementById('errDiv').style.display 	= ''; 
							};
								
							var er_change = function() {
								document.forms['#local.formName#'].submit();
							};
	
							er_change();
						}
					</script>
					<div id="waitDiv" name="waitDiv">
					<h4>Updating member record and processing application.</h4>
					<img src="/assets/common/images/loading-dots.gif">
					</div>
					<div id="errDiv" name="errDiv" style="display:none;">
					<h4>There was an error processing your application.  Please contact SDCBA for assistance.</h4>
					</div>
					<script>
						$('document').ready(function(){
							updateMember();
						});
					</script>
					<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?#cgi.QUERY_STRING#">
						<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
						<cfloop collection="#arguments.event.getCollection()#" item="local.key">
							<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
								and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
								and left(local.key,4) neq "fld_">
								<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
							</cfif>
						</cfloop>
					</cfform>
				</cfoutput>

			</cfcase>		
			
			<cfcase value="3">

				<cfset local.timeStamp 		= now() />
				<cfset local.showErrorMsg = false>
			
				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<!-- @msg@ -->
					<p>#local.formNameDisplay# submitted on #dateformat(local.timeStamp,"dddd, m/d/yyyy")# #timeformat(local.timeStamp,"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					
					<tr class="msgHeader"><td colspan="2" class="b">Personal Information</td></tr>
					<tr>
						<td colspan="2">
							<table width="100%">
								<tr>
									<td width="48%" valign="top">
										<table  cellpadding="2" cellspacing="0" width="100%" border="1">
											<tr><td class="frmText b">Name: </td><td class="frmText">#event.getValue('firstName','')# #event.getValue('lastName','')#&nbsp;</td></tr>
											<tr><td class="frmText b">Primary Email: </td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
											<tr><td class="frmText b">Gender: </td><td class="frmText">#event.getValue('gender','')#&nbsp;</td></tr>
										</table>
									</td>
									<td width="2%"></td>
									<td width="48%" valign="top">
										<table  cellpadding="2" cellspacing="0" width="100%" border="1">
											<tr><td class="frmText b">Address Line 1: </td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
											<tr><td class="frmText b">Address Line 2: </td><td class="frmText">#event.getValue('address2','')#&nbsp;</td></tr>
											<tr><td class="frmText b">City: </td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
											<tr><td class="frmText b">State: </td><td class="frmText">#event.getValue('stateCode','')#&nbsp;</td></tr>
											<tr><td class="frmText b">Zip Code: </td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
											<tr><td class="frmText b">Phone: </td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					
					<tr>
						<td colspan="2">
							<table width="100%">
								<tr>
									<td width="48%" valign="top">
										<table  cellpadding="2" cellspacing="0" width="100%" border="1">
											<tr><td class="frmText b">Student Year: </td><td class="frmText">#event.getValue('year','')#&nbsp;</td></tr>
										<cfif (event.getValue('year','') eq 'Other') AND (len(event.getValue('year_txt','')) gt 0)>
											<tr><td class="frmText b">Student Year Other: </td><td class="frmText">#event.getValue('year_txt','')#&nbsp;</td></tr>
										</cfif>
										<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryLawSchoolName">
											select mdcv.columnValueString
											from dbo.ams_memberDataColumnValues mdcv
											inner join dbo.ams_memberDataColumns mdc 
												on mdc.columnID = mdcv.columnID
												and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
												and columnName = 'law_school'
												and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('lawschool','0')#">
											order by mdcv.columnValueString
										</cfquery>
											<tr><td class="frmText b">Law School: </td><td class="frmText">#local.qryLawSchoolName.columnValueString#&nbsp;</td>
											</tr>
											<cfif  local.otherShool.valueID eq event.getValue('lawschool','0')>
												<tr>
													<td class="frmText b">Other Law School: </td><td class="frmText">#event.getValue('other_school_name','')#&nbsp;</td>
												</tr>
											</cfif>
											
											<tr><td class="frmText b">Graduation Date: </td><td class="frmText">#event.getValue('graddate','')#&nbsp;</td></tr>
											<tr><td class="frmText b">Gender: </td><td class="frmText">#event.getValue('gender','')#&nbsp;</td></tr>
										</table>
									</td>
									<td width="2%"></td>
									<td width="48%" valign="top">
										<table  cellpadding="2" cellspacing="0" width="100%" border="1">
											<tr class="msgHeader"><td colspan="2" class="b">SECTIONS</td></tr>
										<cfif listlen(event.getValue('sections', '')) gt 0>
											<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySectionNames">
												select subscriptionName
												from dbo.sub_subscriptions
												where uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#event.getValue('sections')#">)
												order by subscriptionName
											</cfquery>
											<cfloop query="local.qrySectionNames">
												<tr><td class="frmText b" colspan="2">#local.qrySectionNames.subscriptionName#</td></tr>
											</cfloop>
										<cfelse>
											<tr><td class="frmText b" colspan="2">No sections selected.</td></tr>
										</cfif>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					
					</table>
				</cfsavecontent>				
				
				<!--- CREATE SUBSCRIPTION ----------------------------------------------------------------------------- --->
				<cfset local.subStruct = structNew()>
				<cfset local.subStruct.uid = local.strPageFields.lawStudentSubUID>

				<!--- Get Subscription Rate --->
				<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.rootSubRate">
					set nocount on;

					declare @subscriptionID int, @memberID int, @isRenewalRate bit, @FID int;
					set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
					set @isRenewalRate = 0;
					set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;

					select @subscriptionID = subscriptionID
					from dbo.sub_subscriptions 
					where uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strPageFields.lawStudentSubUID#">;

					select uid
					from dbo.fn_sub_getMostExclusiveRateInfo(@memberID,@subscriptionID,@isRenewalRate,@FID);
				</cfquery>

				<cfset local.subStruct.rateUID = local.rootSubRate.uid>

				<cfif event.getValue('lawschool','') eq local.otherShool.valueID>
					<cfset local.substruct.rateuid = local.otherSchoolRateUID />
				</cfif>
				
				<cfif local.substruct.rateuid neq "">
					
				
				<cfset local.subStruct.overridePerms = true />
				
				<cfset local.subStruct.children = arrayNew(1)>
				
				<cfloop list="#event.getValue('sections','')#" index="local.thisSectionUID">
					<cfset local.childStruct = structNew()>
					<cfset local.childStruct.uid = local.thisSectionUID>
					<cfset local.childStruct.overridePerms = true />
					<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
				</cfloop>
				
				<cfif (event.getValue('donation','') eq 'Yes')>
					<!--- add this last for aesthetic purposes --->
					<cfset local.childStruct = structNew()>
					<cfset local.childStruct.uid = local.strPageFields.donationSubUID>
					<cfset local.childStruct.overridePerms = true />
					<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
				</cfif>
				
				<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
				<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=local.useMID, subStruct=local.subStruct, newAsBilled=true)>
				<cfif local.subReturn.success eq false>
					<!--- email association --->
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							The system was unable to create the subscriptions for this application and #event.getValue('firstName','')# #event.getValue('lastName','')# was not sent an email confirmation.<br />
							<hr />
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfif len(trim(local.ORGEmail.to))>
						<cfscript>
							local.arrEmailTo = [];
							local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
							local.toEmailArr = listToArray(local.ORGEmail.to,';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}
						</cfscript>
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
							emailhtmlcontent=local.mailContent,
							siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
							memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						)>
					</cfif>
					
					<cfset local.showErrorMsg = true>
					<cfset local.errCode = 3>
				<cfelse>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDirectLinkCode">
						select directLinkCode
						from dbo.sub_subscribers
						where subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
					</cfquery>

					<!--- email member  --->
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>Thank you for applying for membership in the SDCBA! </p>
							<p>Look for a separate email providing further information.  </p>
							<p>Should you have questions about membership or benefits, please call the Member Services Team at 619.231.0781 X3505 or email <a href="mailto:<EMAIL>" target="_top"><EMAIL></a></p>
							<hr />
							#local.invoice#			
						</cfoutput>
					</cfsavecontent>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from },
						emailto=[{ name="", email=local.memberEmail.to }],
						emailreplyto=local.ORGEmail.to,
						emailsubject=local.memberEmail.SUBJECT,
						emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=val(local.useMID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>

					<cfset local.emailSentToUser = local.responseStruct.success>

					<!--- email association --->

					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<cfif NOT local.emailSentToUser>
								#event.getValue('firstName','')# #event.getValue('lastName','')# was not sent an email confirmation due to bad Data.<br />
								Please contact them and let them know.<br>
								Their confirmation link is: #local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#
								<hr />
							</cfif>
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfif len(trim(local.ORGEmail.to))>
						<cfscript>
							local.arrEmailTo = [];
							local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
							local.toEmailArr = listToArray(local.ORGEmail.to,';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}
						</cfscript>
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
							emailhtmlcontent=local.mailContent,
							siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
							memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						)>
					</cfif>
					
					<cflocation url="/renewsub/#local.qryDirectLinkCode.directLinkCode#" addtoken="no">
				</cfif>	
				</cfif>				
					
				<cfif local.showErrorMsg>
					<cfoutput>
						There was an error processing your application.  Please contact SDCBA for assistance.
					</cfoutput>
				</cfif>

			</cfcase>
			
			<!--- SPAM MESSAGE:  --->
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>
