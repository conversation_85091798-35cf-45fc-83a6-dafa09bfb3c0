<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function validateAndSearchReg() {
			var #toScript(application.regEx.email, "mc_emailregex")#
			var emailRegEx = new RegExp(mc_emailregex,"i");
			$('##err_frmEvRegIdentifier').html('').hide();
			$('##btnSearchReg').html('<i class="icon-spin icon-spinner"></i> loading...').prop('disabled',true);

			$('##frmEvRegIdentifier input[type="text"]').each(function() {
				$(this).val($(this).val().trim());
			});

			let arrReq = [];
			if (!$('##fEvRegFirstName').val().length) arrReq.push('Enter the First Name.');
			if (!$('##fEvRegLastName').val().length) arrReq.push('Enter the Last Name.');
			if (!$('##fEvRegEmail').val().length || !(emailRegEx.test($('##fEvRegEmail').val()))) arrReq.push('Enter a valid Email Address.');

			if (arrReq.length) {
				onErrorSearchReg(arrReq.join('<br/>'));
				return false;
			} else {
				$.getJSON('#arguments.event.getValue("locatorurl")#', $('##frmEvRegIdentifier').serializeArray())
					.done(showIdentifiedRegistrants)
					.fail(showIdentifiedRegistrants);
			}
		}
		function showIdentifiedRegistrants(respObj) {
			if (respObj.success) {
				if (!respObj.arrMembers.length) {
					showNewRegForm();
				} else {
					let evRegTemplate = Handlebars.compile($('##mc_evIdentifiedReg_template').html());
					$('##EvRegIdentifierFormContainer').hide();
					$('##EvRegIdentifierResults').html(evRegTemplate(respObj)).show(300);
				}
			} else {
				onErrorSearchReg('There was a problem displaying the data. Try again!');
			}
		}
		function onErrorSearchReg(msg) {
			$('##err_frmEvRegIdentifier').html(msg).show();
			$('##btnSearchReg').html('Continue').prop('disabled',false);
		}
		function useMember(mid,regselmode) {
			self.location.href='#arguments.event.getValue('mainregurl')#&regaction=usemid&mid=' + mid + (regselmode ? '&regselmode='+regselmode : '');
		}
		function showNewRegForm() {
			<cfif len(arguments.event.getTrimValue('mc_siteinfo.alternateGuestAccountCreationLink','')) gt 0>
				self.location.href='#arguments.event.getTrimValue('mc_siteinfo.alternateGuestAccountCreationLink')#';
			<cfelse>
				$('##EvRegIdentifierFormContainer').hide();
				$('##EvRegIdentifierResults')
					.html('<img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...')
					.load('#arguments.event.getValue('newregacctformurl')#')
					.show();
			</cfif>
		}
		function useNA() {
			self.location.href='#arguments.event.getValue("mainregurl")#';
		}
		function goToRegCart() {
			self.location.href='#arguments.event.getValue('mainurl')#&regcartv2';
		}
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<div id="EvRegIdentifierFormContainer">
	<div class="evreg-font-size-lg evreg-font-weight-bold evreg-mb-3 evreg-border-top evreg-pt-3">Welcome to Express Registration!</div>
	<div style="margin-bottom:18px;">Enter registrant's First Name, Last Name, and Email<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>, or <a href="/?pg=login">login</a> </cfif> to register:</div>

	<div id="err_frmEvRegIdentifier" class="alert alert-error" style="display:none;margin:10px 0;"></div>

	<form name="frmEvRegIdentifier" id="frmEvRegIdentifier" class="form-horizontal" onsubmit="validateAndSearchReg();return false;" autocomplete="off">
		<div class="control-group">
			<label class="control-label" for="fEvRegFirstName">First Name: <span class="evreg-text-danger">*</span></label>
			<div class="controls">
				<input type="text" name="fEvRegFirstName" id="fEvRegFirstName" value="">
			</div>
		</div>
		<div class="control-group">
			<label class="control-label" for="fEvRegLastName">Last Name: <span class="evreg-text-danger">*</span></label>
			<div class="controls">
				<input type="text" name="fEvRegLastName" id="fEvRegLastName" value="">
			</div>
		</div>
		<div class="control-group">
			<label class="control-label" for="fEvRegEmail">Email: <span class="evreg-text-danger">*</span></label>
			<div class="controls">
				<input type="text" name="fEvRegEmail" id="fEvRegEmail" value="">
			</div>
		</div>
		<div class="control-group">
			<div class="controls">
				<button type="submit" name="btnSearchReg" id="btnSearchReg" class="btn btn-primary">Continue</button>
				<button type="button" name="btnCancelReg" id="btnCancelReg" class="btn btn-link" onclick="doCancelEventReg();">Cancel</button>
				<div class="evreg-font-size-sm evreg-mt-2 evreg-text-dim">
					<span class="evreg-text-danger">*</span> = required
				</div>
			</div>
		</div>
	</form>
</div>
<div id="EvRegIdentifierResults" style="display:none;"></div>

<script id="mc_evIdentifiedReg_template" type="text/x-handlebars-template">
	<div class="evreg-font-size-lg evreg-font-weight-bold evreg-border-top evreg-pt-3">You may already have an account.</div>
	<div class="evreg-mt-3 evreg-pt-3">
	{{##each arrMembers}}
		<div class="row-fluid evRegSearchMem">
			<div class="span12 evreg-mb-3">
				<div class="evreg-d-flex">
					<div class="evreg-col">
						<div class="evreg-d-flex">
							<div class="evreg-mr-3 evreg-text-center" style="width:155px">
								{{##switch evRegStatus}}
									{{##case 'NC'}}<a href="javascript:goToRegCart();" class="evreg-text-decoration-none" style="border-bottom: 1px solid;">Registration Pending</a>{{/case}}
									{{##case 'NA'}}<span class="label label-dark">Already Registered</span>{{/case}}
									{{##case 'NQ'}}<span class="label label-dark">Not Eligible for Registration</span>{{/case}}
									{{##default}}
										<button type="button" class="btn btn-success" onclick="useMember({{memberID}})">Register</button>
									{{/default}}
								{{/switch}}
							</div>
							<div class="evreg-col evreg-d-flex evreg-flex-sm-column">
								{{##if ../showMemberPhoto}}
									<div class="evreg-mr-3">
										{{##if hasPhoto}}
											<img style="max-width:80px;" src="/memberphotosth/{{memberphoto}}">
										{{else}}
											<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
										{{/if}}
									</div>
								{{/if}}
								<div>
									<b>{{mc_combinedName}}</b>
									<div class="evreg-mt-1">
										<div>{{company}}</div>
										{{{mc_combinedAddresses}}}
										{{{mc_extraInfo}}}
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="evreg-col-auto">
						{{##compare mc_recordType.length '>' 0}}{{mc_recordType}}<br/>{{/compare}}
						{{##compare mc_memberType.length '>' 0}}{{mc_memberType}}<br/>{{/compare}}
						{{##compare mcaccountstatus '==' 'I'}}<span class="evreg-text-danger evreg-font-weight-bold">ACCOUNT INACTIVE</span><br/>{{/compare}}
						{{##compare mcaccountstatus '!=' 'I'}}
							{{##compare mc_memberStatus.length '>' 0}}{{mc_memberStatus}}<br/>{{/compare}}
						{{/compare}}
						{{##compare mc_lastlogin.length '>' 0}}{{{mc_lastlogin}}}<br/>{{/compare}}
					</div>	
				</div>
			</div>
		</div>
	{{/each}}
	</div>
	<div class="evreg-font-weight-bold evreg-pt-3">
		Not you? <a href="javascript:showNewRegForm();" title="Create New Account">Create a New Account</a> to register.
	</div>
</script>
</cfoutput>