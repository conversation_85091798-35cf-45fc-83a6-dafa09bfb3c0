<cfsavecontent variable="local.css">
	<style type="text/css">
	ul.groupImage { width:100%; text-align:left; float:left; margin:0px; padding:0; padding-left:0;list-style-type:none; }
	ul.groupImage li { display:inline; padding:0; padding-left:0; }
	div#mc_md_stats dl { margin-left:0px; padding-top:8px; font-size:1.1em; }
	div#mc_md_stats dd { margin-left:10px; display:inline; font-weight:bold; }
	div#mc_md_stats dt { display:inline; padding-left:10px; }
	div#linkedRecordsListDiv div{margin-left:0 !important;}
	div#linkedRecordsListDiv .table td {border-top:0 !important;}
	div#linkedRecordsList div{margin:0;padding:1px;}
	div.rightPanelBorder {border-right: 1px solid #bdb9ab;}
	.MCDirectoryFieldsWrapper {margin-bottom: 0.5em}
	@media only screen and (max-width: 767px) {
		div.rightPanelBorder {border-right:none;}
	}
	</style>
</cfsavecontent>
<cfhtmlhead text="#local.css#">
<cfsavecontent variable="local.pageJS">
<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/polyfills/media-match/2.0.2/media.match.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/enquire.js/2.0.2/enquire.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/inview/jquery.inview.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/mcapps/listviewer/listviewer-common.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/mcapps/listviewer/responsive-listviewer.js"></script>
	<script language="javascript">
		var jsonURL = '/?event=proxy.ts_json&c=MEM&m=getLinkRecordsList';
		var endOfListRecordslist=false;
		var isInitialLoadDone = false;
		var currentlyLoadingMessages = false;
		var autoLoadFirstMessage = true;
		var currentlyLoadingMessages = false;
		var row = 0;
		var posStart = 0;
		
		$(function() {
			if(!posStart) loadMembers();
			
			$('##endOfRecordsList').bind('inview', function(event, isInView, visiblePartX, visiblePartY) {
				if (isInView) {/*element is now visible in the viewport*/
					loadMembers();
				}
			});
		});
		
		function loadMembers() {
			if (!currentlyLoadingMessages && !endOfListRecordslist) {
				currentlyLoadingMessages = true;					
				$('##loadingIndicator').show();
				$.ajax({
					url: jsonURL,
					type: 'POST',
					data: {orgID:#attributes.data.instanceSettings.orgID#, siteResourceID:#attributes.data.instanceSettings.siteResourceID#, memberDirectoryID:#attributes.data.instanceSettings.memberDirectoryID#, memberID:#attributes.data.activeMemberID#, showPhotoRule:#attributes.data.memberDirectoryInfo.showPhotoRule#, baseQryStr:'#attributes.data.baseQueryString#', posStart:posStart},
					dataType: 'json',
					success: function(response) {
						if($('##initialLoadingIndicator').is(':visible')) 
							$('##initialLoadingIndicator').hide();
						
						if(response.SUCCESS == true) {
							if(response.TOTALCOUNT == 0) {
								endOfListRecordslist = true;
								if($('##recordListContainer tr').length == 0){
									$('##recordListContainer').append('<tr><td>No Linked Records found.</td></tr>');
								}
							} else {
								var msg = [];
								for(var i=0;i<response.DATA.length;i++) {
									msg.push('<tr><td>'+ response.DATA[i] + '</td></tr>');
								}
								$('##recordListContainer').append(msg.join(''));
								
								posStart += response.DATA.length;
								
								if(response.DATA.length > 0 && ! $('##linkedRecordsList').is(':visible')) 
									$('##linkedRecordsList').show();
							}
							$('##loadingIndicator').hide();
							currentlyLoadingMessages = false;	
						}
					},
					error: function(ErrorMsg) {
						$('##initialLoadingIndicator').hide();
						$('##loadingIndicator').hide();
						alert(ErrorMsg);
					}
				});
				currentlyLoadingMessages = false;
			}
		}
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageJS#">
<cfoutput>
<div class="container-fluid">
	<div class="row-fluid">
		<div class="page-header span12"><h2>#attributes.data.memberDirectoryInfo.applicationInstanceName#</h2></div>
	</div>
	<div class="row-fluid">
		<ul class="breadcrumb span12">
			<li><a href="/?#attributes.data.baseQueryString#&dirAction=search">Search</a> <span class="divider">/</span></li>
			<li class="active"><a href="javascript:window.history.back();">Results</a> <span class="divider">/</span></li>
			<li class="active"><a href="##">Details</a></li>
		</ul>
	</div>
	<cfif arraylen(attributes.data.arrMembers)>
		<cfset local.gridClasses = structNew() />		
		<cfset local.thisMember = attributes.data.arrMembers[1]>
		<cfset local.gridClassRemaining = 12>
		<cfif arrayLen(local.thisMember.arrAboveClassifications) OR len(trim(local.thisMember.imgToUse)) OR arrayLen(local.thisMember.arrBelowClassifications)>
			<cfset local.gridClasses.photo = "span2">
			<cfset local.gridClassRemaining = local.gridClassRemaining - 2>
		<cfelse>
			<cfset local.gridClasses.photo = "">		
		</cfif>
		<cfif arrayLen(local.thisMember.arrClValues) OR arrayLen(local.thisMember.arrUnderClassifications)>
			<cfset local.gridClasses.classifications = "span5">	
			<cfset local.gridClassRemaining = local.gridClassRemaining - 5>
		<cfelse>
			<cfset local.gridClasses.classifications = "">	
		</cfif>
		<cfset local.gridClasses.fieldset = "span#local.gridClassRemaining#">

		<div class="row-fluid">
			<div class="span12 well">
			<div class="memDetailContainer">
				<cfif attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowPhoto>
					<cfif local.gridClasses.photo neq ''>
						<div class="#local.gridClasses.photo#" style="padding-bottom: 15px;">
							<cfif arrayLen(local.thisMember.arrAboveClassifications)>
								<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-aboveClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								<br clear="all"/>
							</cfif>
							<cfset local.imgToUse = local.thisMember.imgToUse>
							<cfif len(trim(local.imgToUse))>#local.imgToUse#</cfif>
							<cfif arrayLen(local.thisMember.arrBelowClassifications)>
								<br clear="all"/>
								<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-belowClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								<br clear="all"/>
							</cfif>
						</div>
					</cfif>
				<cfelse>
					<cfif local.gridClasses.photo neq ''>
						<div class="#local.gridClasses.photo#">
							<cfif arrayLen(local.thisMember.arrAboveClassifications)>
								<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-aboveClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
							</cfif>
							<cfif arrayLen(local.thisMember.arrBelowClassifications)>
								<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-belowClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
							</cfif>
						</div>
					</cfif>
				</cfif>
				<div class="#local.gridClasses.fieldset# <cfif local.gridClasses.classifications NEQ ''>rightPanelBorder</cfif>" style="margin:0;padding-right:15px;">
					<!--- name/company --->				
					<div class="MCDirectoryNameBlock">
						<cfif len(local.thisMember.stFullName)>
							<span class="MCDirectoryName"><b>#local.thisMember.stFullName#</b></span><br>
						</cfif>
						<span class="MCDirectoryCompany">
							<cfif not len(local.thisMember.stFullName) and len(local.thisMember.stCompany)>
								<b>#local.thisMember.stCompany#</b><br>
							<cfelseif len(local.thisMember.stCompany)>
								#local.thisMember.stCompany#<br>
							</cfif>
						</span>
					</div>
							
					<!--- addresses --->
					<cfif arrayLen(local.thisMember.mc_combinedAddresses)>
						<cfloop index="local.addri" from="1" to="#arrayLen(local.thisMember.mc_combinedAddresses)#">
							<cfif len(local.thisMember.mc_combinedAddresses[local.addri].addr)>
								<br/>
								<cfif attributes.data.multipleAddressTypesDetected>
									<strong>#local.thisMember.mc_combinedAddresses[local.addri].label#</strong><br/>
								</cfif>
								#local.thisMember.mc_combinedAddresses[local.addri].addr#
							</cfif>	
						</cfloop>
						<br/>
					</cfif>

					<!--- phones --->
					<cfloop array="#local.thisMember.arrPhones#" index="local.thisField">
						<cfif len(local.thisField.phone) and attributes.data.makePhoneNumbersClckable>
							<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
								<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue"><a href="tel:#local.thisField.phone#">#local.thisField.phone#</a></span>
							</div></div>
						<cfelseif len(local.thisField.phone)>
							<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
								<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue">#local.thisField.phone#</span>
							</div></div>
						</cfif>
					</cfloop>
					
					<!--- emails --->
					<cfloop array="#local.thisMember.arrEmails#" index="local.thisField">
						<cfif len(local.thisField.email)>
							<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField" style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
								<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue"><a href="mailto:#local.thisField.email#">#local.thisField.email#</a></span>
							</div></div>
						</cfif>
					</cfloop>

					<!--- websites --->
					<cfloop array="#local.thisMember.arrWebsites#" index="local.thisField">
						<cfif len(local.thisField.website)>
							<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField" style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
								<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue"><a href="#local.thisField.website#" target="_blank">#local.thisField.website#</a></span>
							</div></div>
						</cfif>
					</cfloop>
					
					<!--- membernumber --->
					<cfif len(local.thisMember.stMemberNumber)>#local.thisMember.stMemberNumber#<br/></cfif>

					<!--- memberdata / districting --->
					<cfloop array="#local.thisMember.arrMemberDataDistricting#" index="local.thisField">
						<cfif len(local.thisField.value)>
							<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
								<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span>
								<span class="MCDirectoryFieldValue">
									<cfif local.thisField.allowMultiple is 1>
										#ReplaceNoCase(local.thisField.value,"|",", ","ALL")#
									<cfelseif local.thisField.dataTypeCode EQ "DOCUMENTOBJ">
										<cfset local.stDocEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.thisField.columnID#|#right(GetTickCount(),5)#|#local.thisMember.memberID#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
										<cfoutput><a href="/?event=cms.showResource&dirAction=memberDetailsDocDownload&resID=#attributes.data.instanceSettings.siteResourceID#&mode=stream&doc=#local.stDocEnc#">#local.thisField.value#</a></cfoutput>
									<cfelse>
										#local.thisField.value#
									</cfif>
								</span>
							</div></div>
						</cfif>
					</cfloop>

					<!--- member prof license data --->
					<cfloop array="#local.thisMember.arrLicenseData#" index="local.thisField">
						<cfif len(local.thisField.value)>
							<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
								<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue">#local.thisField.value#</span>
							</div></div>
						</cfif>
					</cfloop>

					<!--- website social icons --->
					<cfif ArrayLen(local.thisMember.arrWebsitesSocialIcons) gt 0 OR
							attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowVcard or 
							(attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr))>
						<div style="padding:2px;">
							<cfloop array="#local.thisMember.arrWebsitesSocialIcons#" index="local.thisField">
									#local.thisField.socialLink#
							</cfloop>

							<!--- VCARD / Google Maps link --->
							<cfif attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowVcard>
								<a href="/?event=cms.showResource&dirAction=memberDetailsVCard&resID=#attributes.data.instanceSettings.siteResourceID#&mode=stream&dirMemberid=#local.thisMember.memberID#">
									<img src="/assets/common/images/vcard-icon3.png" alt="[vCard]" width="32" height="32" border="0" align="absmiddle"></a>
							</cfif>
							<cfif attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr)>
								<cfset local.thisMap = attributes.data.mappingBaseLink & URLEncodedFormat(local.thisMember.mc_combinedAddresses[1].mapaddr)>
								<a href="#local.thisMap#" target="_blank"><img src="/assets/common/images/Maps-icon.png" alt="[View Map]" width="32" height="32" border="0" align="absmiddle"></a>
							</cfif>
						</div>
					</cfif>

					<!--- contact form --->
					<cfif attributes.data.offerContactForm and (len(local.thisMember.stFullName) or len(local.thisMember.stCompany))>
						<br/><br/>
						<fieldset class="tsApp">
						<legend class="tsAppLegend">Contact <cfif len(local.thisMember.stFullName)>#local.thisMember.stFullName#<cfelseif len(local.thisMember.stCompany)>#local.thisMember.stCompany#</cfif></legend>
						<cfif attributes.data.contactMsg eq "sent">
							Message has been sent.
						<!--- exceeded 10 contact submissions per session --->
						<cfelseif structKeyExists(session,"MCMemDirSendContactCounter") AND session.MCMemDirSendContactCounter GT 9>
							<div class="alert alert-error">You have reached the maximum number of messages allowed.</div>
						<cfelse>
							<cfif len(attributes.data.memberDirectoryInfo.contactContent)>
								<div>#attributes.data.memberDirectoryInfo.contactContent#</div>
							</cfif>
							<cfform name="frmContactMember" action="/?#attributes.data.baseQueryString#&dirAction=sendContact" method="post" onSubmit="return validateContactFrm();">
							<cfinput type="hidden" name="dirMemberID" value="#local.thisMember.qryMember.memberID#">
							<div id="divContactForm" class="tsAppBodyText" style="width:96%;">
								<div>
									Name:<br/>
									<cfinput type="text" id="frmName" name="frmName" style="width:100%;" value="#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#">
								</div>
								<div>
									E-mail:<br/>
									<cfinput type="text" id="frmEmail" name="frmEmail" style="width:100%;" value="#session.cfcuser.memberdata.email#">
								</div>
								<div>
									Phone:<br/>
									<cfinput type="text" id="frmPhone" name="frmPhone" style="width:100%;" value="">
								</div>
								<div>
									Message:<br/>
									<textarea id="frmMsg" name="frmMsg" style="width:100%;height:140px;"></textarea>
								</div>
								<div id="divContactErr" style="display:none;margin-bottom:6px;"></div>
								<div>
									<button type="submit" class="btn">Send Message</button>
								</div>
							</div>
							<cfset local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init()>
							<cfinclude template="/model/cfformprotect/cffp.cfm" />
							</cfform>
							</fieldset>

							<cfsavecontent variable="local.contactJS">
								<cfoutput>
								<style type="text/css">
								.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
								</style>
								<script language="JavaScript">
								function hideAlert() { $('##divContactErr').html('').hide(); };
								function showAlert(msg) { $('##divContactErr').html(msg).attr('class','alert').show(); };

								function validateContactFrm() {
									hideAlert();
									var arrReq = new Array();

									if ($.trim($('##frmName').val()).length == 0) arrReq[arrReq.length] = 'Enter your name.';
									if ($.trim($('##frmEmail').val()).length == 0 && $.trim($('##frmPhone').val()).length == 0) arrReq[arrReq.length] = 'Enter a valid e-mail address or contact phone number.';
									if ($.trim($('##frmEmail').val()).length > 0) {
										var urlRegEx = new RegExp("#application.regEx.email#", "gi");
										if(!(urlRegEx.test($('##frmEmail').val()))) arrReq[arrReq.length] = 'Enter a valid e-mail address.';
									}
									if ($.trim($('##frmMsg').val()).length == 0) arrReq[arrReq.length] = 'Enter your message.';

									if (arrReq.length > 0) {
										var msg = '';
										for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
										showAlert(msg);
										return false;
									}
									trigersubmissionEvent();
									return true;
								}
								function trigersubmissionEvent(){
									MCLoader.loadJS('/assets/common/javascript/mcapps/members/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
									.then( () => {
										try {
											trigerSearchDetailssubmissionEvent('#attributes.data.pageName#');						
										} catch (error) {
											console.error("Error parsing JSON:", error.message);
										}
									});
								}
								</script>
								</cfoutput>
							</cfsavecontent>
							<cfhtmlhead text="#local.contactJS#">
						</cfif>
					</cfif>

					<!--- profile stats --->
					<cfif attributes.data.showStats>
						<fieldset class="tsApp">
						<legend class="tsAppLegend">Statistics (Last 30 Days)</legend>
						<div id="mc_md_stats">
						<cfloop array="#attributes.data.arrListingStats#" index="local.thisEl">
							<dl><dd>#local.thisEl.count#</dd><dt>#local.thisEl.label#</dt></dl>
						</cfloop>
						</div>
						</fieldset>
						<br/><br/>
					</cfif>
					
					<div class="clear"></div>
					<br /><br />
					<div class="span10">
						<div id="initialLoadingIndicator" class="progress progress-striped active">
							<div class="bar" style="width: 100%;"><strong>Loading Linked Records</strong></div>
						</div>
					</div>
					
					<div id="linkedRecordsList" class="span10" style="display:none;">
						<h4>Linked Records</h4>
						<div id="linkedRecordsListDivContainer">
							<div id="linkedRecordsListDiv">
								<table class="table">
									<tbody id="recordListContainer"></tbody>
								</table>
								<div id="endOfRecordsList">
									<div id="loadingIndicator" class="progress progress-striped active">
									  <div class="bar" style="width: 100%;"><strong>Loading Records</strong></div>
									</div>
								</div>
							</div>
						</div>
					</div>					
				</div>
				<cfif local.gridClasses.classifications NEQ ''>
					<div class="#local.gridClasses.classifications#" style="margin:0;padding-left:15px!important;">
						<div>
						<cfif arrayLen(local.thisMember.arrClValues)>
							<cfset local.clTitle = "">
							<cfloop array="#local.thisMember.arrClValues#" index="local.thisClItem">
								<cfif local.clTitle neq local.thisClItem.clName>
									<cfif local.clTitle neq ""><br/></cfif>
									<cfset local.clTitle = local.thisClItem.clName>
									<b>#local.thisClItem.clName#</b><br>
								</cfif>
								#local.thisClItem.groupName#<br/>
							</cfloop>
							<br/>
						</cfif>
						&nbsp;
						<cfif arrayLen(local.thisMember.arrUnderClassifications)>
							<ul class="groupImage">
							<cfloop array="#local.thisMember.arrUnderClassifications#" index="local.thisImg">
								<li><img class="directory-groupImage directory-groupImage-underClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></li>
							</cfloop>
							</ul>
						</cfif>
						</div>
					</div>
				</cfif>
			</div>
			</div>
		</div>
	</cfif>
</div>
</cfoutput>
