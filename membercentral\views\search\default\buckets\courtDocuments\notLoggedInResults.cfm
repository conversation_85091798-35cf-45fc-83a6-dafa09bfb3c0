<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	<div style="padding-bottom:10px;">
		<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
		<button class="tsAppBodyButton bucketHeaderButton" onclick="document.location.href='/?pg=uploadDocuments'"><i class="icon-upload"></i> Upload a document</button>
	</div>
</cfoutput>			
<cfif (not variables.cfcuser_isSiteAdmin and val(local.qryBucketInfo.restrictToGroupID) gt 0 and local.qryBucketInfo.isMemberInRestrictedGroup is not 1)>
	<cfoutput>
	<cfif not variables.cfcuser_isLoggedIn>
		<div class="tsAppBodyText tsAppBB" style="padding-bottom:3px;">You are not authorized to see these results.</div>
		#showNotLoggedInResults()#					
	<cfelse>
		<div class="tsAppBodyText" style="padding-bottom:3px;">You are not authorized to see these results.</div>
	</cfif>
	</cfoutput>
<cfelseif local.strResultsCount.itemCount EQ 0>
	<!--- <div class="s_rnfne">No documents matched your search criteria.</div> --->
	<div class="s_rnfne"><cfoutput>#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName)#</cfoutput></div>
<cfelse>
	<cfoutput>
	<cfif val(local.strResultsCount.itemCount) gt 0>
		<div class="tsAppBodyText tsAppBB" style="padding-bottom:3px;">#Numberformat(local.strResultsCount.itemCount)# documents found</div>
	</cfif>
	<cfif not variables.cfcuser_isLoggedIn>
		#showNotLoggedInResults()#					
	</cfif>				
	</cfoutput>
</cfif>