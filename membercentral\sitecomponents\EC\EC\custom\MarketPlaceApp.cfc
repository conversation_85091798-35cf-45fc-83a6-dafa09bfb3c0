<cfcomponent extends="model.customPage.customPage" output="true">
    <cfset variables.objCustomPageUtils = application.objCustomPageUtils>
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
        <cfargument name="Event" type="any">
        <cfscript>
            var local = structNew();
            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=MarketPlaceApp";
            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
            
            local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Click Here to Begin" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button below. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="Pay profile code for CC", value="CC" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentProfileCodePayLater",type="STRING",desc="pay profile code for check",value="Paybyck" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationEmailTo", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationEmailReplyTo", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationText",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="According to our records, you already have an active CELA Marketplace membership. If you have questions about your membership status, please contact CELA at (818) 703-0587." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please contact CELA at (818) 703-0587." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MainSubscription",type="STRING",desc="UID for Marketplace subscription rates",value="5AC26BF4-C7C2-4519-9380-09A7458E22F0" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="AD520521-EDA0-466B-8E8D-5B529FB7BA7C" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,    arrCustomFields=local.arrCustomFields);

            StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmMarketPlace',
                formNameDisplay='CELA Marketplace Application',
                orgEmailTo= variables.strPageFields.StaffConfirmationEmailTo,
                memberEmailFrom= variables.strPageFields.ConfirmationEmailReplyTo
            ));

            variables.useHistoryID = 0;
			if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
				variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
			if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
				variables.useHistoryID = int(val(session.useHistoryID));
				
			variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='marketplacejoin', subName='Started');
			variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='marketplacejoin', subName='Completed');
			variables.historyStartedText = "Member started Marketplace Application form.";
			variables.historyCompletedText = "Member completed Marketplace Application form.";
            local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
			variables.qryImpactRatesAndSubscriptions = getImpactRatesAndAddonSubscriptions();

            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin'); 
            } else {
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup()) {
                            case "success":
                                local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
                                break;		
                            case "activefound":
                                local.returnHTML = showError(errorCode='activefound');
                                break;	
							case "billedjoinfound":
								local.returnHTML = showError(errorCode='billedjoinfound');
								break;	
                            case "acceptedfound":
                                local.returnHTML = showError(errorCode='acceptedfound');
                                break;		
                            case "billedfound":
                                local.returnHTML = showError(errorCode='billedfound');
                                break;		
                            default:
                                application.objCommon.redirect(variables.baselink);
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        switch (processMemberInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                if(processMembershipInfo(rc=arguments.event.getCollection()) EQ "success")
                                    local.returnHTML = showPayment(rc=arguments.event.getCollection());
                                break;
                            case "spam":
                                local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                            local.returnHTML = showError(errorCode='failsavemember',rc=arguments.event.getCollection());
                                break;				
                        }
                        break;
                    case "processPayment":
                        local.processStatus = processPayment(rc=arguments.event.getCollection());
                        switch (local.processStatus.response) {
                            case "success":
                                local.returnHTML = showConfirmation();
                                application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
                                structDelete(session, "formFields");
                                break;
                            default:
                                local.returnHTML = showError(errorCode='failpayment');
                                break;				
                        }
                        break;
                    default:
                        local.returnHTML = showLookup();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica;overflow: hidden; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.radio input[type="radio"] { -webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{-webkit-appearance: checkbox !important;}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
                .CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
                .CPSection .BodyText p{margin-bottom:5px;}
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				.acntLookUpBtn,.acntLookUpMessage{display:inline!important;float: left;width: 48%!important;}
				.acntLookUpBtn{	margin-right: 5px; }
				.acntLookUpMessage .span12{margin-left:0px !important;}
				@media screen and (min-width: 632px) and (max-width: 980px){
					.acntLookUpBtn {margin-top: 52px;}
				}
				@media screen and (min-width: 980px){
					.acntLookUpBtn {margin-top: 45px;}
				}				
				.acntLookUpBtn  {position: absolute;width: 50%;height: 100%;min-height: 100%;}
				.centerit {position: absolute;top: 50%;width: 100%;}
				.centerit button {position: relative;top: -15px;height: 30px;}
				.center-holder{position: relative;}
				.acntLookUpMessage {margin-left: 48%!important;}
				@media screen and (min-width: 0px){
					.acntLookUpBtn {margin-top: 0px!important;}
				}
				@media screen and (min-width: 359px) and (max-width: 368px){
					.acntLookUpBtn button{font-size:13px;}
				}
				@media screen and (max-width: 359px){
					.acntLookUpBtn button{font-size:11px;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],input[type="datetime"],input[type="email"],input[type="number"],input[type="password"],input[type="search"],input[type="text"],select,textarea {
						font-size: 16px!important;
					}
				}
				div{-moz-box-sizing: border-box;-ms-box-sizing: border-box;-o-box-sizing: border-box;-webkit-box-sizing: border-box;box-sizing: border-box;}
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
                .subAddonsArrayWrapper{margin-top:10px;}
                .subAddonsArrayWrapper, .subAddonsArrayWrapper label.subLabel{display:none;}
                .membershipTypeContainer{margin-bottom:20px;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				$(function() {
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});
						}
					});
				});
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
				}
				function selectMember() {
					var windowWidth = $(window).width();
					var _popupWidth = 550;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					} 
					$.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}
				function resizeBox(newW,newH) {
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				window.onhashchange = function() {       
                    if (location.hash.length > 0) {        
                        step = parseInt(location.hash.replace('##',''),10);     
                        if (prevStep > step){
                            if(step==1)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
                            if(step==2)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
                            mc_loadDataForForm($('###variables.formName#'),'previous');			        	
                        }
                    } else {
                        step = 1;
                    }
                    prevStep = step;				    
				}				
				$(document).ready(function() {
                    $('.tsAppBodyText').addClass('BodyText');
					$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText');
                    
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="customTable form-horizontal">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			
				<div class="CPSection row-fluid BodyText">
					<div class="CPSectionTitle BB">#variables.strPageFields.AccountLocatorTitle#</div>
					<div class="frmRow1 row-fluid" style="padding:10px;">
						<p>#variables.strPageFields.AccountLocatorIntroText#</p>
						<div class="row-fluid center-holder">
							<div class="span6 acntLookUpBtn c">
                                <div class="centerit">
									<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
								</div>
							</div>
							<div class="span6 acntLookUpMessage pull-right">
								#variables.strPageFields.AccountLocatorInstructions#
							</div>
						</div>
					</div>
				</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		
		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		
		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>

        <cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID = variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
        <cfset local.result = variables.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>

		<cfset local.primryCntctFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='8EFC39D1-9257-47A8-99F2-5BDE331DE8ED', mode="collection", strData=local.strData)>
		<cfset local.drctryStngsFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='16B21E86-B57C-428C-BDBB-7CF6D43FEBD1', mode="collection", strData=local.strData)>
		<cfset local.companyDescFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='11D0074D-E980-462F-8746-34F103CC91FE', mode="collection", strData=local.strData)>
		<cfset local.uploadPhotoFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='F6D93679-5BBE-4771-B6DF-79B97118140F', mode="collection", strData=local.strData)>
		<cfset local.uploadBannerAdLogoFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='C1D50D72-6571-40E5-8900-CCF5C28C8B17', mode="collection", strData=local.strData)>
		<cfset local.mailingListFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='290EBFED-8399-4616-9D29-7498FE408226', mode="collection", strData=local.strData)>
		<cfset local.mrktPlcDrctryCatgryFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='A0ECE8B9-C647-407F-8E74-8B20EFFC2010', mode="collection", strData=local.strData)>
        <cfset local.mbrDscntFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='*************-4FB5-8102-4D924FAF2713', mode="collection", strData=local.strData)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
                .CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
				.radio input[type="radio"] {-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{-webkit-appearance: checkbox !important;}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{max-width:435px;}
				.customForm .radio.subRateLabel{display:block;}
                @media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
                }
				@media screen and (max-width:767px){
					.collapsible-table td{display:flex;border:none}
					.collapsible-table td.visible-phone{display:block!important;}
					.collapsible-table tr{border:1px solid grey;}
				}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;
				}
				@media screen and (max-width:980px) and (min-width:900px){
					input[type=text],select,.ui-multiselect{width:300px;}
				}
                .membershipTypeContainer{margin-bottom:20px;}
			</style>
			
			<script language="javascript">
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
				}
                #local.result.jsAddonValidation#
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
                    #local.result.JSVALIDATION#
                    <cfif val(local.subscriptionID)>
                        if($("*[id^='sub#local.subscriptionID#_rate']").length <= 1 || ($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){

                            arrReq[arrReq.length] = " Select Membership.";
                        }
                    </cfif>	
                    #local.primryCntctFieldSetContent.jsValidation#
					#local.drctryStngsFieldSetContent.jsValidation#
					#local.companyDescFieldSetContent.jsValidation#
					#local.uploadPhotoFieldSetContent.jsValidation#
					#local.uploadBannerAdLogoFieldSetContent.jsValidation#
					#local.mailingListFieldSetContent.jsValidation#
					#local.mrktPlcDrctryCatgryFieldSetContent.jsValidation#
                    #local.mbrDscntFieldSetContent.jsValidation#
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
                function showHideAddOns(){
                    var _selectedVal = $('.membershipTypeContainer .subRateCheckbox:checked');
                    $('.subAddonsArrayWrapper').hide();
                    $('.subAddonsArrayWrapper label.subLabel').hide();
                    if(_selectedVal.length){
                        if(_selectedVal.val() == parseInt(#variables.qryImpactRatesAndSubscriptions.ImpactRateID#)) {
                            <cfif len(variables.qryImpactRatesAndSubscriptions.BulletinSubscriptionID) OR len(variables.qryImpactRatesAndSubscriptions.AdSponsorshipSubIDSubscriptionID)>
                                $('.subAddonsArrayWrapper').show();
                                $('input[name=sub#variables.qryImpactRatesAndSubscriptions.BulletinSubscriptionID#]').parent().show();
                                $('input[name=sub#variables.qryImpactRatesAndSubscriptions.AdSponsorshipSubIDSubscriptionID#]').parent().show();
                            </cfif>
                        } else {
                            $('.subAddonsArrayWrapper').show();
                            $('.subAddonsArrayWrapper label.subLabel').show();

                        }
                    }
                }
				function showHideFieldSets(){
					var _selectedVal = $('.membershipTypeContainer .subRateCheckbox:checked');
					if(_selectedVal.length && _selectedVal.val() == parseInt(#variables.qryImpactRatesAndSubscriptions.ImpactRateID#)){
                        $('##bannerAdLogo,##mailingListImpact').show();
                    } else {
						$('##bannerAdLogo,##mailingListImpact').hide();
						clear_form_elements('bannerAdLogo');
						clear_form_elements('mailingListImpact');
						if($('input[name=sub#variables.qryImpactRatesAndSubscriptions.BannerAdForImpactSubscriptionID#]').is(':checked')){
							$('##bannerAdLogo').show();
						}
						if($('input[name=sub#variables.qryImpactRatesAndSubscriptions.MailingListForImpactSubscriptionID#]').is(':checked')){
							$('##mailingListImpact').show();
						}
					}
				}
				function clear_form_elements(div_id) {
					$("##"+div_id).find(':input').each(function() {
						switch(this.type) {
							case 'password':
							case 'text':
							case 'textarea':
							case 'file':
							case 'select-one':
							case 'select-multiple':
							case 'date':
							case 'number':
							case 'tel':
							case 'email':
							case 'hidden':
								$(this).val('');
								break;
							case 'checkbox':
							case 'radio':
								this.checked = false;
								break;
						}
					});
				}

				$(document).ready(function() {
                    $('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText').addClass('BodyText');
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>

					responsiveCorrection();
                    showHideAddOns();
					showHideFieldSets()
                    <cfif len(variables.qryImpactRatesAndSubscriptions.ImpactRateID)>
                    $('.membershipTypeContainer .subRateCheckbox').change(function(){
                        $('.subAddonsArrayWrapper label.subLabel input').removeAttr('checked');
                        showHideAddOns();
						showHideFieldSets();
                    });
                    <cfelse>
                        $('.subAddonsArrayWrapper').show();
                        $('.subAddonsArrayWrapper label.subLabel').show();
                    </cfif>
					<cfif len(variables.qryImpactRatesAndSubscriptions.BannerAdForImpactSubscriptionID)>
					$('input[name=sub#variables.qryImpactRatesAndSubscriptions.BannerAdForImpactSubscriptionID#]').change(function(){
						if($(this).is(':checked')){
							$('##bannerAdLogo').show();
						} else {
							$('##bannerAdLogo').hide();
							clear_form_elements('bannerAdLogo');
						}
					});
					</cfif>
					<cfif len(variables.qryImpactRatesAndSubscriptions.MailingListForImpactSubscriptionID)>
					$('input[name=sub#variables.qryImpactRatesAndSubscriptions.MailingListForImpactSubscriptionID#]').change(function(){
						if($(this).is(':checked')){
							$('##mailingListImpact').show();
						} else {
							$('##mailingListImpact').hide();
							clear_form_elements('mailingListImpact');
						}
					});
					</cfif>
					$('span.joinFrequency_F').each(function(k,val){
						$(this).html($(this).html().split(" ")[0]+')');
					});
				});
				$(window).on("load resize scroll",function(e){
					responsiveCorrection();
				});
				function responsiveCorrection(){
					var _width = $(window).width() ;
					if(_width <= 900){
						var _adjustedWidth = _width - 70;
						$('.form-horizontal div.CPSectionContent > table tr > td').css('display','flex');
						$('.form-horizontal div.CPSectionContent table tr > td:nth-child(2)').css({'display':'inline-block','white-space':'inherit'});
						$('.form-horizontal div.CPSectionContent > table tr > td:nth-child(1)').css('display','inline');
						$('.form-horizontal div.CPSectionContent > table tr > td:nth-child(3)').hide();
						$('.form-horizontal div.CPSectionContent > table tr > td,.form-horizontal div.CPSectionContent table tr > td *,.form-horizontal div.CPSectionContent table tr > td input,.form-horizontal div.CPSectionContent table tr > td select,.ui-multiselect').css('max-width',_adjustedWidth+'px');

						$('.form-horizontal div.CPSectionContent table tr > td,.form-horizontal div.CPSectionContent table tr > td *').css('white-space','inherit');
						$('.form-horizontal div.CPSectionContent.membershipCategoryFieldSet table tr > td table td').css('display','table-cell');
					} else {
						$('.form-horizontal div.CPSectionContent > table tr > td').css('display','table-cell');
						$('.form-horizontal div.CPSectionContent table tr > td').css('white-space','inherit');
					}
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm();" class="form-horizontal customForm">
				<input type="hidden" name="fa" id="fa" value="processMemberInfo">
				<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
				<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
				<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
				<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
				
                <div>* Required Data</div>

                <div class="CPSection row-fluid BodyText">
                    <div class="CPSectionTitle BB">Membership Type</div>
                    <div class="CPSectionContent PT">
                        #replace(local.result.formContent,"well","membershipTypeContainer")#
                    </div>
                </div>	

				<div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.primryCntctFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent" id="memberInfoFieldFields">
							#local.primryCntctFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.drctryStngsFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.drctryStngsFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.companyDescFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.companyDescFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.uploadPhotoFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.uploadPhotoFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid" id="bannerAdLogo">
						<div class="CPSectionTitle BB">#local.uploadBannerAdLogoFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.uploadBannerAdLogoFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid" id="mailingListImpact">
						<div class="CPSectionTitle BB">#local.mailingListFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.mailingListFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.mrktPlcDrctryCatgryFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.mrktPlcDrctryCatgryFieldSetContent.fieldSetContent#
						</div>
					</div>
                    
                    <div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.mbrDscntFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.mbrDscntFieldSetContent.fieldSetContent#
						</div>
					</div>

					<div class="frmButtons">
						<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
					</div>
				</div>			
			</form>
            #application.objWebEditor.showEditorHeadScripts()#
            <script>
            function editContentBlock(cid,srid,tname) {
                var editMember = function(r) {
                    if (r.success && r.success.toLowerCase() == 'true') {
                        $('##frmmd_'+cid).html(r.html);
                        var x = div.getElementsByTagName("script");
                        for(var i=0;i<x.length;i++) eval(x[i].text); 
                    }
                };
                var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
                TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
            }
            </script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

        <cfset local.response = "failure">
		<cfif (structKeyExists(arguments.rc, "iAgree")) >
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	
		<!--- save member info and record history --->		
		<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>					
				<script type="text/javascript">
					$(document).ready(function(){
						if (typeof arrUploaders !== 'undefined') {
							$.each(arrUploaders, function() {
								this.uploader.bind('BeforeUpload', function(uploader, file) {
									uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
								});
								this.uploader.start();								
							});
						}
					});
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfif local.strResult.success>
			<cfset local.objSaveMember = variables.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
			<cfset local.strResult1 = local.objSaveMember.saveData()>

			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = variables.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.strResult = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice  gt 0)>
		<cfset local.arrPayMethods = []>
		<cfif len(variables.strPageFields.PaymentProfileCodeCC) gt 0 AND variables.strPageFields.PaymentProfileCodeCC neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PaymentProfileCodeCC)>
		</cfif>
		<cfif len(variables.strPageFields.PaymentProfileCodePayLater) gt 0 AND variables.strPageFields.PaymentProfileCodePayLater neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PaymentProfileCodePayLater)>
		</cfif>
		
		<cfset local.strReturn = 
			variables.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMemberInfo"
			)>
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#
			<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
                .CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
				.radio input[type="radio"] {  -webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{-webkit-appearance: checkbox !important;}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
                @media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{max-width:435px;}
				.customForm .radio.subRateLabel{display:block;}
				.customForm .tsAppSectionHeading{background:##990000;color: ##FFF;}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;
				}
                .subAddonsArrayWrapper{display:block;}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<div class="customForm">				
				<div class="CPSection row-fluid BodyText">
					<div class="CPSectionTitle">Membership Type Selection Confirmation</div>
					<div class="row-fluid CPSectionContent PT">
						#local.strResult.formContent#
					</div>
				</div>
				<div class="CPSection row-fluid BodyText">
					<div class="CPSectionTitle">Total Price</div>
					<div class="row-fluid CPSectionContent PT">
						<div class="span4">Total Amount Due:</div>
						<div class="span4">#dollarFormat(local.strResult.totalFullPrice)#</div>
					</div>
				</div>
				<cfif local.paymentRequired>
					<div class="CPSectionNoBottom row-fluid">
						#local.strReturn.paymentHTML#
					</div>
				<cfelse>
					<div>
						<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();" disabled>Continue</button>
						<button name="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMemberInfo');">&lt;&lt; Back</button>
					</div>
				</cfif>
			</div>
						
			<script>
				$(document).ready(function(){
					$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText').addClass('BodyText');
					setTimeout(function() {
						$('button').attr('disabled',false);
					}, 1200);

					$('span.joinFrequency_F').each(function(k,val){
						$(this).html($(this).html().split(" ")[0]+')');
					});
				});
			</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

    <cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset variables.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, newAccountsOnly=false)>

        <cfset local.primryCntctFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='8EFC39D1-9257-47A8-99F2-5BDE331DE8ED', mode="confirmation", strData=arguments.rc)>
		<cfset local.drctryStngsFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='16B21E86-B57C-428C-BDBB-7CF6D43FEBD1', mode="confirmation", strData=arguments.rc)>
		<cfset local.companyDescFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='11D0074D-E980-462F-8746-34F103CC91FE', mode="confirmation", strData=arguments.rc)>
		<cfset local.uploadPhotoFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='F6D93679-5BBE-4771-B6DF-79B97118140F', mode="confirmation", strData=arguments.rc)>
		<cfset local.uploadBannerAdLogoFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='C1D50D72-6571-40E5-8900-CCF5C28C8B17', mode="confirmation", strData=arguments.rc)>
		<cfset local.mailingListFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='290EBFED-8399-4616-9D29-7498FE408226', mode="confirmation", strData=arguments.rc)>
		<cfset local.mrktPlcDrctryCatgryFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='A0ECE8B9-C647-407F-8E74-8B20EFFC2010', mode="confirmation", strData=arguments.rc)>
        <cfset local.mbrDscntFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='*************-4FB5-8102-4D924FAF2713', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
    	<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationText)>
					<div class="row-fluid tsAppSectionContentContainer">
						<div class="span12 BodyText">#variables.strPageFields.ConfirmationText#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div class="row-fluid tsAppSectionContentContainer">
					<div class="span12 BodyText">You have received an application for Marketplace.</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.primryCntctFieldSetContent.fieldSetContent#
                #local.drctryStngsFieldSetContent.fieldSetContent#
                #local.companyDescFieldSetContent.fieldSetContent#
                #local.uploadPhotoFieldSetContent.fieldSetContent#
				<cfif structKeyExists(arguments.rc,"sub#variables.qryImpactRatesAndSubscriptions.BannerAdForImpactSubscriptionID#") AND len(arguments.rc["sub#variables.qryImpactRatesAndSubscriptions.BannerAdForImpactSubscriptionID#"])>
				#local.uploadBannerAdLogoFieldSetContent.fieldSetContent#
				</cfif>
				<cfif structKeyExists(arguments.rc,"sub#variables.qryImpactRatesAndSubscriptions.MailingListForImpactSubscriptionID#") AND  len(arguments.rc["sub#variables.qryImpactRatesAndSubscriptions.MailingListForImpactSubscriptionID#"])>
				#local.mailingListFieldSetContent.fieldSetContent#
				</cfif>
                #local.mrktPlcDrctryCatgryFieldSetContent.fieldSetContent#
                #local.mbrDscntFieldSetContent.fieldSetContent#

				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">CELA Marketplace - Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>
								#replaceList(local.strResult.formContent,' Full, Monthly, Quarterly','',',')#
                            </div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<table  style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.strPageFields.ConfirmationEmailReplyTo,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to CELA",
			emailhtmlcontent = local.confirmationHTMLToMember,
			siteID = variables.siteID,
			memberID = val(variables.useMID),
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
                .CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
				.radio input[type="radio"] {-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{-webkit-appearance: checkbox !important;}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
                @media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],input[type="datetime"],input[type="email"],input[type="number"],input[type="password"],input[type="search"],input[type="text"],select,textarea {font-size: 16px!important;}
				}
				.customForm input[type="text"],.customTable select{max-width:435px;}
				.customForm .radio.subRateLabel{display:block;}
				div{-moz-box-sizing: border-box;-ms-box-sizing: border-box;-o-box-sizing: border-box;-webkit-box-sizing: border-box;box-sizing: border-box;}
                .subAddonsArrayWrapper{display:block;}
				</style>
				</head>
				<body>
					#local.confirmationHTMLToMember#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MarketPlaceApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Marketplace_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset variables.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Marketplace Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Marketplace Application Confirmation')>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
        
		<cfset local.responseStruct = variables.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle=arguments.rc.mc_siteinfo.sitename & " - " & variables.formNameDisplay, emailContent=local.confirmationHTMLToStaff)>

        <cfsavecontent variable="session.invoice">
            <cfoutput>
                <fieldset>
                    <legend>Confirmation</legend>
                    #local.confirmationHTMLToMember#
                </fieldset
            </cfoutput>
        </cfsavecontent>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
    
    <cffunction name="showConfirmation" access="private" output="false" returntype="string">
        <cfset var local = structNew()>

        <cfset local.doNotIncludeList = "fa">
        <cfset local.strData = {}>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                #session.invoice#
            </cfoutput>
        </cfsavecontent>

        <cfreturn local.returnHTML>
    </cffunction>
    
    <cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
        <cfargument name="errorCode" type="string" required="true">

        <cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>    
                <div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
                <div class="tsAppSectionContentContainer">
                    <cfif arguments.errorCode eq "activefound">
                        #variables.strPageFields.ActiveAcceptedMessage#
                    <cfelseif arguments.errorCode eq "acceptedfound">
                        #variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
                    <cfelseif arguments.errorCode eq "billedfound">
                        <cfset local.qrySubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
                        <cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
                        <cflocation url="#local.redirectLink#" addtoken="false">
                    <cfelseif arguments.errorCode eq "failsavemember">
                        We were unable to save the member information provided. Please contact the association or try again later.
                    <cfelseif arguments.errorCode eq "failsavemembership">
                        We were unable to process the membership information provided. Please contact the association or try again later.
                    <cfelseif arguments.errorCode eq "failpayment">
                        We were unable to process your selected payment method. Please contact the association or try again later.
                    <cfelseif arguments.errorCode eq "spam">
                        Your submission was blocked and will not be processed at this time.
                    <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
                    <cfelse>
                        An error occurred. Please contact the association or try again later.
                    </cfif>
                </div>
            </cfoutput>
        </cfsavecontent>
        <cfreturn local.returnHTML>
    </cffunction>
	
	<cffunction name="getImpactRatesAndAddonSubscriptions" access="private" returntype="query" output="false">
		<cfset var local= structNew()>
		<cfquery name="local.qryImpactRatesAndSubscriptions" datasource="#application.dsn.membercentral.dsn#">
			DECLARE @impactRateID int, @bulletinSubID int, @bannerAdSubID int, @mailingListSubID int, @AdSponsorshipSubID int;
			SELECT @impactRateID = rateID from sub_rates where uid = '62E769EB-0A91-4B76-9A64-C1D087F1DF93' and status ='A';
			SELECT @bulletinSubID = subscriptionID from sub_subscriptions where uid = 'EA991B9A-9279-4442-A166-67B968794D7D' and status ='A';
			SELECT @AdSponsorshipSubID = subscriptionID from sub_subscriptions where uid = '2242E62D-0DA5-40ED-A341-37F87AFB94AA' and status ='A';
			SELECT @bannerAdSubID = subscriptionID from sub_subscriptions where uid = '9747F408-0AA6-4BC2-A22A-BAA1EE1D62A5' and status ='A';
			SELECT @mailingListSubID = subscriptionID from sub_subscriptions where uid = 'C887B460-21D0-42B7-9526-30AE63636317' and status ='A';

			SELECT @impactRateID as ImpactRateID, @bulletinSubID as BulletinSubscriptionID,  @bannerAdSubID as BannerAdForImpactSubscriptionID,  @mailingListSubID as MailingListForImpactSubscriptionID, @AdSponsorshipSubID as AdSponsorshipSubIDSubscriptionID;
		</cfquery>
		<cfreturn local.qryImpactRatesAndSubscriptions>
	</cffunction>
</cfcomponent>