<cfinclude template="../commonLoginStyles.cfm">
<cfinclude template="../commonVerifySecurityCodeJS.cfm">

<cfoutput>
<div class="mc_loginApp">
	<div class="login-card login-mt-3">
		<div class="login-card-header login-bg-whitesmoke login-pb-1">
			<div class="login-font-size-lg login-font-weight-bold">Verify Your Identity - Enter Security Code</div>
		</div>
		<div class="login-card-body">
			<cfif arguments.event.valueExists('invalidSecurityCode')>
				<div class="alert">Invalid Code. Try again.</div>
			</cfif>
			<form name="frmVerifyLogin" id="frmVerifyLogin" method="post" action="/?pg=login&logact=verifySecurityCode" onsubmit="return validateLoginCode();" autocomplete="off">
				<label id="MFAMethodMessage" class="login-mb-3 login-instructional-text">#local.message#</label>
				<input type="text" name="securityCode" id="securityCode" class="login-m-0" size="25" value="" placeholder="Enter Security Code" autocomplete="one-time-code" maxlength="6">
				<button class="tsAppBodyButton" type="submit" name="btnVerifyLogin">Verify</button>
				<div class="login-mt-2 login-mb-1">
					<input type="checkbox" class="login-m-0" name="saveBrowser" value="1" checked> <label class="login-d-inline-block login-m-0" for="saveBrowser">Remember this device</label>
				</div>
				<div id="resendMFAOTPControl" class="login-mt-3 login-mb-3"></div>
			</form>
			<div id="MFAMethodContainer" class="login-mt-3"></div>
		</div>
	</div>
</div>
<cfif local.hasAlternateMFAMethods>
	<script id="MFAMethodTemplate" type="text/x-handlebars-template">
		<div class="login-text-dim">Choose another method to receive your security code</div>
		<div class="login-d-flex login-mt-2">
			<cfif local.arrConfiguredMethods.containsNoCase('MFASMS')>
				{{##compare mfasms_channel '!=' 'sms'}}
					<a href="##" class="login-mr-4 mfasms_channels{{##compare codeattempts '>' 1}} login-disabled{{/compare}}" onclick="chooseMFAMethod('MFASMS','sms');return false;"><i class="icon-mobile-phone"></i> SMS</a>
				{{/compare}}
				{{##compare mfasms_channel '!=' 'call'}}
					<a href="##" class="login-mr-4 mfasms_channels{{##compare codeattempts '>' 1}} login-disabled{{/compare}}" onclick="chooseMFAMethod('MFASMS','call');return false;"><i class="icon-phone"></i> Receive a call</a>
				{{/compare}}
				{{##compare mfasms_channel '!=' 'whatsapp'}}
					<a href="##" class="login-mr-4 mfasms_channels{{##compare codeattempts '>' 1}} login-disabled{{/compare}}" onclick="chooseMFAMethod('MFASMS','whatsapp');return false;">
						<img src="assets/common/images/whatsapp-icon.png" style="width:15px;"> WhatsApp
					</a>
				{{/compare}}
			</cfif>
			<cfif local.arrConfiguredMethods.containsNoCase('MFATOTP')>
				{{##compare mfamethod '!=' 'mfatotp'}}
					<a href="##" class="login-mr-4" onclick="chooseMFAMethod('MFATOTP');return false;"><i class="icon-qrcode"></i> Authenticator App</a>
				{{/compare}}
			</cfif>
		</div>
	</script>
</cfif>
</cfoutput>