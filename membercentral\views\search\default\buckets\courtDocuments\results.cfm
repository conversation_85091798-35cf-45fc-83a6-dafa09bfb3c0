<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	
	<div style="padding-bottom:10px;">
		<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
		<button class="tsAppBodyButton bucketHeaderButton" onclick="document.location.href='/?pg=uploadDocuments'"><i class="icon-upload"></i> Upload a document</button>
	</div>						
</cfoutput>
<cfif local.qryResults.recordcount EQ 0>
	<!--- <div class="s_rnfne">No documents matched your search criteria.</div> --->
	<cfoutput>
		<div class="s_rnfne">#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName)#</div>
	</cfoutput>
<cfelse>
	<cfoutput>
	#showSearchResultsPaging('top',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.documentsFound,'document','s',arrayNew(1),'',arguments.filter)#
	</cfoutput>
	<!--- crumbtrail --->
	<cfoutput>
	<div class="tsAppBodyText">
		Documents are organized into folders. You are viewing:<br/>
		<a href="/?pg=search&bid=#arguments.bucketid#&s_a=doSearch&sid=#arguments.searchid#">#local.qryBucketInfo.bucketName#</a>
		<cfif local.strSearch.filter_group gt 0>
			/ <a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|0|0');">#local.qryResults.grpLabel#</a>
			<cfif local.strSearch.filter_casetype gt 0>
				/ <a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|0');">#local.qryResults.caseLabel#</a>
				<cfif local.strSearch.filter_doctype gt 0>
					/ <a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|#local.strSearch.filter_doctype#');">#local.qryResults.docLabel#</a>
				</cfif>
			</cfif>
		</cfif>
	</div>
	<br/>
	</cfoutput>
	
	<cfif local.strSearch.filter_doctype gt 0>
		<cfset local.currentDocument = CreateObject('component','model.system.platform.tsDocument')>
		<cfloop query="local.qryResults">
			<cfset local.currentDocument.load(local.qryResults.documentid)>
			<cfset local.mycost = local.currentDocument.getPrice(
				membertype=variables.cfcuser_membertype,
				billingstate=variables.cfcuser_billingstate,
				billingzip=variables.cfcuser_billingzip,
				websiteorgcode=session.mcstruct.sitecode,
				depomemberdataid=variables.cfcuser_depomemberdataid)>

			<cfif local.qryResults.inCart>
				<cfset local.inCartStyle = "" />
				<cfset local.addToCartStyle = "display:none;" />
			<cfelse>
				<cfset local.inCartStyle = "display:none;" />
				<cfset local.addToCartStyle = "" />
			</cfif>

			<cfoutput>
			<div style="clear:both;"></div>
			<div id="searchSponsorRightSideContainer"></div>
			<div class="tsAppBodyText s_row tsDocViewerDocument <cfif local.qryResults.currentrow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.qryResults.documentid#">
				<div class="s_act">
					<div class="s_opt" style="#local.inCartStyle#" id="s_incart#local.qryResults.documentID#">
						<a href="javascript:viewCart();" title="View your cart/checkout"><img src="#application.paths.images.url#images/search/cart.png" width="16" height="16" align="left" border="0" />In Cart</a>
					</div>
					<cfif not local.qryResults.owned>
						<div class="s_opt" style="#local.addToCartStyle#" id="s_addtocart#local.qryResults.documentID#">
							<a href="javascript:addToCart(#local.qryResults.documentid#,#arguments.searchid#);" title="Buy Document"><img src="#application.paths.images.url#images/search/cart.png" width="16" height="16" align="left" border="0">Buy</a> (#replace(dollarformat(local.mycost.price),".00","")#)
						</div>
					</cfif>
					<cfif local.qryResults.owned or listfind(local.memberGroups,local.qryResults.groupid)>
						<div class="s_opt"><a href="javascript:downloadDoc(#local.qryResults.documentid#);" title="Download Document"><img src="#application.paths.images.url#images/search/page_white_put.png" width="16" height="16" align="left" border="0" />Download</a></div>
					</cfif>
				</div>
				<div>
				<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchid#);" title="Click to view document"><img src="#application.paths.images.url#images/search/page_acrobat.png" width="16" height="16" border="0" align="left" /></a>
				<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchid#);" title="Click to view document"><b><cfif len(trim(local.qryResults.expertname))>#trim(local.qryResults.expertname)#<cfelse>Unknown Expert</cfif></b></a>
				<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchid#);" title="Click to view document" class="ctv">(click to view)</a><br/>
				</div>
				<div class="s_dtl">
					Document ###local.qryResults.documentid# - #DateFormat(local.qryResults.DocumentDate, "mmm d, yyyy")# - #local.qryResults.state# <cfif local.qryResults.pages gt 0>- #local.qryResults.pages# pages</cfif><br/>
					#local.qryResults.style# ... <cfif len(local.qryResults.causedesc)>... #local.qryResults.causedesc#</cfif>
					<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned or listfind(local.memberGroups,local.qryResults.groupid)>
						<cfif len(local.qryResults.notes)><br/>Notes: #local.qryResults.notes#</cfif>
						<br/>Contributed by: <cfif len(trim(local.qryResults.email))><a href="mailto:#trim(local.qryResults.email)#"></cfif>#local.qryResults.FirstName# #local.qryResults.LastName#<cfif len(trim(local.qryResults.email))></a></cfif>, 
						<cfset local.tmp = "">
						<cfif len(local.qryResults.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingfirm)></cfif>
						<cfif len(local.qryResults.phone)><cfset local.tmp = listAppend(local.tmp,"Ph: " & local.qryResults.phone)></cfif>
						#replace(local.tmp,",",", ","ALL")#<br/>
					<cfelse>
						<br/>Notes: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
						<br/>Contributed by: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
					</cfif>
				</div>
				<cfif isnumeric(local.qryResults.numdocflags) and local.qryResults.numdocflags gt 0>
					<cfquery name="local.getFlags" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						select f.name
						from dbo.docflags f
						inner join dbo.docflaglinks as fl on fl.docflagid = f.docflagid
						where f.groupid = #local.qryResults.groupid#
						and fl.documentid = #local.qryResults.documentid#
						order by name
					</cfquery>
					<div class="s_dtl">
						Flags: #replace(valuelist(local.getflags.name), ",", ", ")#
					</div>
				</cfif>
			</div>
			</cfoutput>
		</cfloop>
	<cfelse>
		<!--- if more than 20 records, show in 2 columns --->
		<table width="100%" cellpadding="1" cellspacing="0">
		<tr valign="top">
			<td class="tsAppBodyText">
			<cfoutput query="local.qryResults">
				<cfif listlen(arguments.filter,"|") is 3>
					<cfif local.strSearch.filter_casetype gt 0>
						<div class="s_lh2"><a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|#local.qryResults.doctypeid#');"><img src="#application.paths.images.url#images/search/folder_close.png" border="0" width="19" height="16" align="absmiddle">#local.qryResults.doctypedesc#</a> (#numberformat(local.qryResults.doccount)#)</div>
					<cfelseif local.strSearch.filter_group gte 0>
						<div class="s_lh2"><a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.qryResults.casetypeid#|0');"><img src="#application.paths.images.url#images/search/folder_close.png" border="0" width="19" height="16" align="absmiddle">#local.qryResults.casetypedesc#</a> (#numberformat(local.qryResults.doccount)#)</div>
					<cfelse>
						<div class="s_lh2"><a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.qryResults.groupid#|0|0');"><img src="#application.paths.images.url#images/search/folder_close.png" border="0" width="19" height="16" align="absmiddle">#local.qryResults.description#</a> (#numberformat(local.qryResults.doccount)#)</div>
					</cfif>
				<cfelse>
					<div class="s_lh2"><a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.qryResults.groupid#|0|0');"><img src="#application.paths.images.url#images/search/folder_close.png" border="0" width="19" height="16" align="absmiddle">#local.qryResults.description#</a> (#numberformat(local.qryResults.doccount)#)</div>
				</cfif>
				<cfif local.qryResults.recordcount gt 20 and local.qryResults.currentrow is int(local.qryResults.recordcount/2)+1></td><td class="tsAppBodyText"></cfif>
			</cfoutput>
		</tr>
		</table>
		<br/>
	</cfif>

	<cfoutput>
	<br clear="all"/>
	#showSearchResultsPaging('bottom',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.documentsFound,'document','s',arrayNew(1),'',arguments.filter)#
	</cfoutput>
</cfif>