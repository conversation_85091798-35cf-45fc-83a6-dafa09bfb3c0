<cfoutput>
<div class="login-pl-3 login-pb-3">
	<div id="editMFAPhNo" class="MFAPhNoTool">
		<div class="login-mb-2">Enter your phone number:</div>
		<div class="login-d-flex">
			<div class="login-col-auto login-p-0">
				<input type="tel" id="MFAPhNo" name="MFAPhNo" value="#local.MFAPhoneNumber#" style="height:auto;">
			</div>
			<div id="MFAPhNoValidMsg" class="login-col-auto login-text-success login-align-self-center" style="display:none;">Valid</div>
			<div id="MFAPhNoErrMsg" class="login-col-auto login-text-danger login-align-self-center" style="display:none;"></div>
		</div>
		<div class="login-d-flex login-mt-3">
			<button type="button" id="btnVerifyMFAPhNo" class="btn btn-success login-mr-2" onclick="verifyMFAPhNo();">Verify</button>
			<cfif len(local.MFAPhoneNumber)>
				<button type="button" id="delMFAPhNo" class="btn btn-danger login-mr-2" title="Delete Phone Number" onclick="deleteMFAPhoneNo();return false;">Remove</button>
			</cfif>
			<button type="button" id="btnCancel" class="btn btn-default" onclick="returnToManageSettings('mfasms');">Cancel</button>
		</div>
	</div>
	<div id="verifyMFAPhNoOTP" class="MFAPhNoTool" style="display:none;"></div>
</div>

<script id="MFASMSVerifyTemplate" type="text/x-handlebars-template">
	<input type="hidden" name="mfasms_channel" id="mfasms_channel" value="{{channel}}">
	<div class="login-text-dim">
		{{##switch channel}}
			{{##case "sms"}}
				We're texting you now at {{mfaphno}}
			{{/case}}
			{{##case "call"}}
				We're calling you now at {{mfaphno}}
			{{/case}}
			{{##case "whatsapp"}}
				We're messaging you on WhatsApp now at {{mfaphno}}
			{{/case}}
		{{/switch}}
		<a href="##" class="login-anchor login-ml-2" onclick="editMFAPhNo();return false;"><i class="icon-pencil"></i></a>
	</div>
	<div class="login-mb-1 login-mt-3">Enter the verification code:</div>
	<div class="login-d-flex">
		<div class="login-col-auto login-p-0">
			<input type="text" id="securityCode" name="securityCode" value="" maxlength="6" placeholder="Enter Security Code" autocomplete="one-time-code">
		</div>
		<div id="MFAPhNoOTPValidMsg" class="login-col-auto login-text-success login-align-self-center" style="display:none;"></div>
		<div id="MFAPhNoOTPErrMsg" class="login-col-auto login-text-danger login-align-self-center" style="display:none;"></div>
	</div>
	<div id="resendMFAPhNoOTPControl" class="login-mt-2 login-mb-2">{{{resendcodetext}}}</div>
	<div class="login-d-flex login-mt-3">
		<button type="button" id="btnVerifyCode" class="btn btn-success login-mr-2" onclick="verifySecurityCode();">Verify</button>
		<button type="button" id="btnCancel" class="btn btn-default" onclick="returnToManageSettings('mfasms');">Cancel</button>
	</div>
	<div id="MFASMSChannelContainer" class="login-mt-5">
		<div class="login-text-dim">Choose another method to receive your security code</div>
		<div class="login-d-flex login-mt-2">
			{{##compare channel '!=' 'sms'}}
				<a href="##" class="login-mr-4 mfasms_channels{{##if disablesmschannels}} login-disabled{{/if}}" onclick="chooseMFASMSChannel('sms');return false;"><i class="icon-mobile-phone"></i> SMS</a>
			{{/compare}}
			{{##compare channel '!=' 'call'}}
				<a href="##" class="login-mr-4 mfasms_channels{{##if disablesmschannels}} login-disabled{{/if}}" onclick="chooseMFASMSChannel('call');return false;"><i class="icon-phone"></i> Receive a call</a>
			{{/compare}}
			{{##compare channel '!=' 'whatsapp'}}
				<a href="##" class="login-mr-4 mfasms_channels{{##if disablesmschannels}} login-disabled{{/if}}" onclick="chooseMFASMSChannel('whatsapp');return false;">
					<img src="assets/common/images/whatsapp-icon.png" style="width:15px;"> WhatsApp
				</a>
			{{/compare}}
		</div>
	</div>
</script>
</cfoutput>

<cfinclude template="../commonMFASMSJS.cfm">