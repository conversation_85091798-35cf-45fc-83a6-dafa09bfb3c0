<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=join";
            local.formAction = arguments.event.getValue('fa','showLookup');
			variables.cfcuser = structNew();
			variables.formFields = structNew();
			
			variables.cfcuser = session.cfcuser;
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=variables.cfcuser);
			
			if(application.mcCacheManager.sessionValueExists('formFields')){
				variables.formFields = application.mcCacheManager.sessionGetValue('formFields',{});
			}
						
			variables.personalInformationFieldSetUID = "ECAF8742-EAAB-4B55-B884-03C5A96A700D";
			variables.homeAddressFieldSetUID = "FB0BB1CA-1A67-47DA-8321-1AFAABA28EE7";			
			variables.businessAddressFieldSetUID = "145583B5-4363-4270-9C40-499EA59C6915";			
			variables.addressPreferenceFieldSetUID = "8A149F5D-50BB-47AA-90B8-1427758E6B38";
			variables.membershipTypeFieldSetUID = "85D421C2-2F1E-4F46-A29E-126A89C800B2";
			variables.organizationGroupUID = "4C8850AE-F5FA-42AE-9A4C-EF8A5F969236";														   

            variables.currentDate = dateTimeFormat(now());

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];

            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			    arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="A9911414-D1CE-4522-A86D-F3211B2CC3CB" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already an MSBA member. If you have questions about your membership, please call 612-333-1183." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 612-333-1183." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="MSBA Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalLicContent",type="CONTENTOBJ",desc="Content at top of Professional License fields",value="Please enter your bar admission date(s) and license number(s)." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);            
			local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="2DC7F68D-9F4B-412B-A2C3-C918456B69F3" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="MSBA_CC" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="MSBA_Checks" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="MSBA Membership Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CompanyFirmSelector",type="CONTENTOBJ",desc="Business/Company Information tool",value="Please find your business/company in the search tool below. If you cannot find your business/company, we will save the name that you enter on the form." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	

            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo= variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
            ));

			variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");

            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
            variables.origMemberID = variables.useMID;
            if(local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
				(structCount(variables.formFields) gt 0) and 
                structKeyExists(variables.formFields, "step0") and 
                structKeyExists(variables.formFields.step0, "memberID") and 
                int(val(variables.formFields.step0.memberID)) gt 0){            
                    variables.useMID = variables.formFields.step0.memberID;
                    if(structKeyExists(variables.formFields.step0, "origMemberID") and int(val(variables.formFields.step0.origMemberID)) gt 0){
                        variables.origMemberID = variables.formFields.step0.origMemberID;
                    }              

            }else if(variables.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = variables.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }
            if( local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                local.formAction neq "showMemberInfo" and 
                local.formAction neq "processMemberInfo" and 
                structKeyExists(variables.formFields, "step1") and 
                structKeyExists(variables.formFields.step1, "useHistoryID") and 
                int(val(variables.formFields.step1.useHistoryID)) gt 0){
                    variables.useHistoryID = int(val(variables.formFields.step1.useHistoryID));
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=variables.cfcuser);
            local.subStatus = "";
            if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
				local.subStatus = hasSub(int(variables.useMID),local.formAction);
            }

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin');  
            }else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
                local.returnHTML = showError(errorCode=local.subStatus);  
            }else{
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMemberInfo();
                                break;		
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        switch (processMemberInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMembershipInfo();
                                break;
                            case "spam":
                                local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMembershipInfo":
                        switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showPayment();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;                   
                    case "processPayment":
                        local.processStatus = processPayment(event=arguments.event);
                        switch (local.processStatus) {
                            case "success":
                                local.returnHTML = showConfirmation();
                                application.objUser.setIdentifiedMemberIDfromID(cfcuser=variables.cfcuser, memberID=0);
                                variables.formFields = structNew();
								application.mcCacheManager.sessionDeleteValue("formFields")
                                break;
							case "paymentFailed":
                                local.returnHTML = showPayment();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='failpayment');
                                break;				
                        }
                        break;
                    case "showMembershipInfo":
                        local.returnHTML = showMembershipInfo();
                        break;	
                    case "showMemberInfo":
                        local.returnHTML = showMemberInfo();
                        break;                    		
                    default:
						if(application.mcCacheManager.sessionValueExists("captchaEntered")) {	
							application.mcCacheManager.sessionDeleteValue("captchaEntered")
						}
						
						variables.formFields = structNew();
		                if(application.mcCacheManager.sessionValueExists("formFields")) {
							application.mcCacheManager.sessionDeleteValue("formFields")
						}
                        local.returnHTML = showLookup();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");        
    	</cfscript>
    </cffunction>


    <cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>          

            #variables.pageJS# 

			<style type="text/css">
			</style>

			<script type="text/javascript">

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}

				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);

					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				

				function addMember(memObj) {
					assignMemberData(memObj);
				}

				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");                            

				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
                <cfinput type="hidden" name="fa" id="fa" value="processLookup">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
                <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">

                <cfif len(variables.strPageFields.FormTitle)>
                    <div class="row-fluid" id="FormTitleId">
                        <div class="span12">
                            <h1>#variables.strPageFields.FormTitle#</h1>
                        </div>
                    </div>
                </cfif>	

				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>	
			</cfform>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step0")>
            <cfset structDelete(variables.formFields, "step0")>
        </cfif>	

		<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
			<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
		</cfif>
		<cfif application.mcCacheManager.sessionValueExists("subReturn")>
			<cfset application.mcCacheManager.sessionDeleteValue("subReturn")>
		</cfif>

        <cfset variables.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.fieldSetUIDlist = '#variables.personalInformationFieldSetUID#,#variables.homeAddressFieldSetUID#,#variables.businessAddressFieldSetUID#,#variables.membershipTypeFieldSetUID#'>
		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>
		
        <cfset local.memberTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddressCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddressstateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddresspostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddresscountyField = {fieldCode="",fieldLabel=""}>
		<cfset local.businessAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.businessAddress2Field = {fieldCode="",fieldLabel=""}>
		<cfset local.businessCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.businessstateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.businesspostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.businesscountyField = {fieldCode="",fieldLabel=""}>
		<cfset local.businessphoneField = {fieldCode="",fieldLabel=""}>
		<cfset local.lawSchoolField = {fieldCode="",fieldLabel=""}>
		<cfset local.lawSchoolOtherField = {fieldCode="",fieldLabel=""}>
		<cfset local.graduationDateField = {fieldCode="",fieldLabel=""}>
		<cfset local.appearinFindaLawyerDirectoryField = {fieldCode="",fieldLabel=""}>

        <cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
         <cfset local.strData.one = checkSessionExist("step1")/>
		
        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
			<cfset local.memberTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Member Type']")/>
            <cfif arrayLen(local.memberTypeData)>				
                <cfset local.memberTypeField.fieldCode = local.memberTypeData[1].XmlAttributes.fieldCode>
                <cfset local.memberTypeField.fieldLabel = local.memberTypeData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.homeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_address1']")/>
			<cfif arrayLen(local.homeAddress1FieldData)>
				<cfset local.homeAddress1Field.fieldCode = local.homeAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddress1Field.fieldLabel = local.homeAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.homeAddressCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_city']")/>
			<cfif arrayLen(local.homeAddressCityFieldData)>				
				<cfset local.homeAddressCityField.fieldCode = local.homeAddressCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddressCityField.fieldLabel = local.homeAddressCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.homeAddressstateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_stateprov']")/>
			<cfif arrayLen(local.homeAddressstateprovFieldData)>				
				<cfset local.homeAddressstateprovField.fieldCode = local.homeAddressstateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddressstateprovField.fieldLabel = local.homeAddressstateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.homeAddresspostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_postalcode']")/>
			<cfif arrayLen(local.homeAddresspostalcodeFieldData)>				
				<cfset local.homeAddresspostalcodeField.fieldCode = local.homeAddresspostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddresspostalcodeField.fieldLabel = local.homeAddresspostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.homeAddresscountyFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_county']")/>
			<cfif arrayLen(local.homeAddresscountyFieldData)>				
				<cfset local.homeAddresscountyField.fieldCode = local.homeAddresscountyFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddresscountyField.fieldLabel = local.homeAddresscountyFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.businessAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_address1']")/>
			<cfif arrayLen(local.businessAddress1FieldData)>				
				<cfset local.businessAddress1Field.fieldCode = local.businessAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.businessAddress1Field.fieldLabel = local.businessAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.businessAddress2FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_address2']")/>
			<cfif arrayLen(local.businessAddress2FieldData)>				
				<cfset local.businessAddress2Field.fieldCode = local.businessAddress2FieldData[1].XmlAttributes.fieldCode >
				<cfset local.businessAddress2Field.fieldLabel = local.businessAddress2FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.businessCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_city']")/>
			<cfif arrayLen(local.businessCityFieldData)>				
				<cfset local.businessCityField.fieldCode = local.businessCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.businessCityField.fieldLabel = local.businessCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.businessstateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_stateprov']")/>
			<cfif arrayLen(local.businessstateprovFieldData)>				
				<cfset local.businessstateprovField.fieldCode = local.businessstateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.businessstateprovField.fieldLabel = local.businessstateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.businesspostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_postalcode']")/>
			<cfif arrayLen(local.businesspostalcodeFieldData)>				
				<cfset local.businesspostalcodeField.fieldCode = local.businesspostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.businesspostalcodeField.fieldLabel = local.businesspostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.businesscountyFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_county']")/>
			<cfif arrayLen(local.businesscountyFieldData)>				
				<cfset local.businesscountyField.fieldCode = local.businesscountyFieldData[1].XmlAttributes.fieldCode >
				<cfset local.businesscountyField.fieldLabel = local.businesscountyFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.businessphoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Business_Phone']")/>
			<cfif arrayLen(local.businessphoneFieldData)>				
				<cfset local.businessphoneField.fieldCode = local.businessphoneFieldData[1].XmlAttributes.fieldCode >
				<cfset local.businessphoneField.fieldLabel = local.businessphoneFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.lawSchoolData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Law School Minnesota']")/>
            <cfif arrayLen(local.lawSchoolData)>				
                <cfset local.lawSchoolField.fieldCode = local.lawSchoolData[1].XmlAttributes.fieldCode>
                <cfset local.lawSchoolField.fieldLabel = local.lawSchoolData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.lawSchoolOtherData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Law School Other']")/>
            <cfif arrayLen(local.lawSchoolOtherData)>				
                <cfset local.lawSchoolOtherField.fieldCode = local.lawSchoolOtherData[1].XmlAttributes.fieldCode>
                <cfset local.lawSchoolOtherField.fieldLabel = local.lawSchoolOtherData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.graduationDateData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Law School Expected Graduation Date']")/>
            <cfif arrayLen(local.graduationDateData)>				
                <cfset local.graduationDateField.fieldCode = local.graduationDateData[1].XmlAttributes.fieldCode>
                <cfset local.graduationDateField.fieldLabel = local.graduationDateData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.appearinFindaLawyerDirectoryData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Appear in Find a Lawyer Directory']")/>
            <cfif arrayLen(local.appearinFindaLawyerDirectoryData)>				
                <cfset local.appearinFindaLawyerDirectoryField.fieldCode = local.appearinFindaLawyerDirectoryData[1].XmlAttributes.fieldCode>
                <cfset local.appearinFindaLawyerDirectoryField.fieldLabel = local.appearinFindaLawyerDirectoryData[1].XmlAttributes.fieldLabel>
            </cfif>

			<cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.personalInformationFieldSetUID#,#variables.homeAddressFieldSetUID#,#variables.businessAddressFieldSetUID#,#variables.addressPreferenceFieldSetUID#',local.fieldSetUid)>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				<cfif NOT variables.isLoggedIn AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>				
					<cfloop collection="#local.memberFieldData#" item="local.key" >
						<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
							<cfset StructDelete(local.memberFieldData, local.key)>
						</cfif>					
					</cfloop>
				</cfif>
			</cfif>
        </cfloop>
        <cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
        <cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID = variables.orgid)>

		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.businessAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.businessAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.membershipTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.membershipTypeFieldSetUID, mode="collection", strData=local.strData.one)>																																						   
       
		<cfset local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
        <cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>
		</cfif>

        <cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
        <cfset local.licenseStatus = {}>
        <cfset local.index = 1>
        <cfloop query="local.qryOrgProLicenseStatuses">
            <cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
            <cfset local.index = local.index + 1>
        </cfloop> 
        <cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID='#variables.organizationGroupUID#', orgID=variables.orgID)>
		<cfquery name="local.qryCompanyByGroup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
            DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;
			SELECT distinct parentMember.memberNumber, parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber,
			rt.recordTypeCode,rt.recordTypeName from
            dbo.ams_recordRelationships AS rr  
            INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
            INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
            INNER JOIN dbo.ams_recordTypes AS rt ON rt.recordTypeID = rtrt.masterRecordTypeID		
			
			INNER JOIN dbo.ams_members AS parentMember ON parentMember.orgID = @orgID and parentMember.memberID = rr.masterMemberID
            INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID and mg.memberID = parentMember.memberID
            INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = mg.groupID
            WHERE rr.orgID = @orgID 
            AND g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
            AND rt.recordTypeCode IN ('Organization')
			AND g.status = 'A' AND ISNULL(parentMember.company,'') <> '' ORDER BY parentMember.company

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
        
        <cfquery name="local.qryLinkedParentCompany" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;
			SELECT parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber, rt.recordTypeCode
			FROM dbo.ams_members AS childMember
			INNER JOIN dbo.ams_recordRelationships AS rr ON rr.childMemberID = childMember.memberID and rr.isActive = 1 AND rr.orgID = @orgID
			INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
			INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
			INNER JOIN dbo.ams_recordTypes AS rt ON rt.recordTypeID = rtrt.childRecordTypeID
			INNER JOIN dbo.ams_members AS parentMember ON parentMember.memberID = rr.masterMemberID
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = <cfqueryparam value="#variables.orgID#" cfsqltype="CF_SQL_INTEGER"> and mg.memberID = parentMember.memberID
			INNER JOIN dbo.ams_groups AS g ON g.groupID = mg.groupID
			WHERE childMember.memberID = <cfqueryparam value="#local.strData.one.memberID#" cfsqltype="CF_SQL_INTEGER">
			AND rrt.relationshipTypeCode = 'Staff' 
			AND g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

        </cfquery>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}

				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}

					##content-wrapper div.ui-multiselect-menu{width:auto!important;}

					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}

				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							

				div.alert-danger{padding: 10px !important;}

			</style>
			<script language="javascript">
				function afterFormLoad(){
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}
				
				function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					$(".professionalLicensesHolder .mpl_pltypeid").parent().parent().children().first().text('');
					$("###local.lawSchoolField.fieldCode#").parents('tr').show();
					$("###local.lawSchoolOtherField.fieldCode#").parents('tr').show();
					$("###local.graduationDateField.fieldCode#").parents('tr').show();
					$("###local.appearinFindaLawyerDirectoryField.fieldCode#").parents('tr').show();
					switch(memType) {
						case 'Attorney':
							$(".professionalLicensesHolder .mpl_pltypeid").parent().parent().children().first().text('*');
							showFieldsByContainerClass('professionalLicenses');							
							resetFormFieldsByContainerClass('');
							$("###local.lawSchoolField.fieldCode#").parents('tr').hide();
							$("###local.lawSchoolOtherField.fieldCode#").parents('tr').hide();
							$("###local.graduationDateField.fieldCode#").parents('tr').hide();
							break
						case 'Student':
							showFieldsByContainerClass('');
							resetFormFieldsByContainerClass('');
							$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							$("###local.appearinFindaLawyerDirectoryField.fieldCode#").parents('tr').hide();
							break
						default:
							showFieldsByContainerClass('');
							resetFormFieldsByContainerClass('');
							$("###local.lawSchoolField.fieldCode#").parents('tr').hide();
							$("###local.lawSchoolOtherField.fieldCode#").parents('tr').hide();
							$("###local.graduationDateField.fieldCode#").parents('tr').hide();
							break;
					}
				}
				
				function reloadElements(){
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						var _function = _this.data('function');

						if(typeof _function != "undefined" &&_function.includes('multiSelect')){
							_this.next().remove();
						} 
						eval(_function)();
					});
				}
			
				function showFieldsByContainerClass(classList)				
				{
					var elementArray = [];
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						elementArray.push(_this);
					});
					
					$(".fieldSetHolder").html('');
					
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());							
						}			
					});					
					reloadElements();
					elementArray.forEach(function(element) {
						var elementId = element.attr('id');
						$("##"+elementId).val(element.val());
					});
				}

				function showFieldByFieldList(fieldName)				
				{
					var _CF_this = document.forms['#variables.formName#'];
					var fieldNameListArray = (fieldName).split(",");
					$.each(fieldNameListArray,function(i){	
						$(_CF_this[fieldNameListArray[i]]).parents('tr').show();
					});			
				}

				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]).html('');
						});
					}
				}

				function resetFormFieldByFieldList(fieldName){
					var _CF_this = document.forms['#variables.formName#'];
					if(fieldName.length){
						var fieldNameListArray = (fieldName).split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}else{
						var fieldNameListArray=("").split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}
				}

				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());					
					
					#local.membershipTypeFieldSet.jsValidation#
                    #local.personalInformationFieldSet.jsValidation#
					if(memType.length > 0 && (memType == 'Attorney')){

						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();    
									if($("##mpl_"+val+"_licenseNumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' #local.strProfLicenseLabels.profLicenseNumberLabel#.'; }                                
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' #local.strProfLicenseLabels.profLicenseDateLabel#.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}

					}
					
					var homeAddressRequired = true;
					var addressRequired = false;
					
                    
                    
                    #local.homeAddressFieldSet.jsValidation#    

					var isSetAllbusinessAddress = 0;
					if ($('.organizationFirmFieldSetHolder').text() == "" && ($(".organizationsFirmsHolder ##companyField").val() == 'Please Select' || $(".organizationsFirmsHolder ##companyField").val() === null )) 
						{

						}
                    else {
					
					#local.businessAddressFieldSet.jsValidation#
					if(_CF_hasValue(_CF_this['m_company'], "TEXT", false)){ isSetAllbusinessAddress = isSetAllbusinessAddress + 1;}
						var missingBusinessFields = []; // Array to track missing business address fields

						<cfif len(trim(local.businessAddress1Field.fieldCode))>
							if (!_CF_hasValue(_CF_this['#local.businessAddress1Field.fieldCode#'], "TEXT", false)) {
								missingBusinessFields.push("Address");
							} else {
								isSetAllbusinessAddress++;
							}
						</cfif>
						<cfif len(trim(local.businessCityField.fieldCode))>
							if (!_CF_hasValue(_CF_this['#local.businessCityField.fieldCode#'], "TEXT", false)) {
								missingBusinessFields.push("City");
							} else {
								isSetAllbusinessAddress++;
							}
						</cfif>
						<cfif len(trim(local.businessstateprovField.fieldCode))>
							if (!_CF_hasValue(_CF_this['#local.businessstateprovField.fieldCode#'], "TEXT", false)) {
								missingBusinessFields.push("State");
							} else {
								isSetAllbusinessAddress++;
							}
						</cfif>
						<cfif len(trim(local.businesspostalcodeField.fieldCode))>
							if (!_CF_hasValue(_CF_this['#local.businesspostalcodeField.fieldCode#'], "TEXT", false)) {
								missingBusinessFields.push("Postal Code");
							} else {
								isSetAllbusinessAddress++;
							}
						</cfif>
						<cfif len(trim(local.businesscountyField.fieldCode))>
							if (!_CF_hasValue(_CF_this['#local.businesscountyField.fieldCode#'], "TEXT", false)) {
								missingBusinessFields.push("County");
							} else {
								isSetAllbusinessAddress++;
							}
						</cfif>
						var addressRequired = true;
					}

					var isSetAllHomeAddress = 0;
					var missingHomeFields = []; // Array to track missing home address fields
					<cfif len(trim(local.homeAddress1Field.fieldCode))>
						if (!_CF_hasValue(_CF_this['#local.homeAddress1Field.fieldCode#'], "TEXT", false)) {
							missingHomeFields.push("Address");
						} else {
							isSetAllHomeAddress++;
						}
					</cfif>
					<cfif len(trim(local.homeAddressCityField.fieldCode))>
						if (!_CF_hasValue(_CF_this['#local.homeAddressCityField.fieldCode#'], "TEXT", false)) {
							missingHomeFields.push("City");
						} else {
							isSetAllHomeAddress++;
						}
					</cfif>
					<cfif len(trim(local.homeAddressstateprovField.fieldCode))>
						if (!_CF_hasValue(_CF_this['#local.homeAddressstateprovField.fieldCode#'], "TEXT", false)) {
							missingHomeFields.push("State");
						} else {
							isSetAllHomeAddress++;
						}
					</cfif>
					<cfif len(trim(local.homeAddresspostalcodeField.fieldCode))>
						if (!_CF_hasValue(_CF_this['#local.homeAddresspostalcodeField.fieldCode#'], "TEXT", false)) {
							missingHomeFields.push("Postal Code");
						} else {
							isSetAllHomeAddress++;
						}
					</cfif>
					<cfif len(trim(local.homeAddresscountyField.fieldCode))>
						if (!_CF_hasValue(_CF_this['#local.homeAddresscountyField.fieldCode#'], "TEXT", false)) {
							missingHomeFields.push("County");
						} else {
							isSetAllHomeAddress++;
						}
					</cfif>
					
					if(addressRequired ){
						if (isSetAllbusinessAddress > 0 && isSetAllbusinessAddress != 6) {
							if (missingBusinessFields.length === 1) {
								// If only one field is missing, show a single field-specific message
								arrReq[arrReq.length] = "Business " + missingBusinessFields[0] + " is required.";
							} else {
								// For multiple missing fields, show a detailed list
								arrReq[arrReq.length] = "The following fields are required for Business Address:<ul><li>" + missingBusinessFields.join("</li><li>") + "</li></ul>";
							}
						}
					}

					if(homeAddressRequired ){
						if (isSetAllHomeAddress > 0 && isSetAllHomeAddress != 5) {
							if (missingHomeFields.length === 1) {
								// If only one field is missing, show a single field-specific message
								arrReq[arrReq.length] = "Home " + missingHomeFields[0] + " is required.";
							} else {
								// For multiple missing fields, show a detailed list
								arrReq[arrReq.length] = "The following fields are required for Home Address:<ul><li>" + missingHomeFields.join("</li><li>") + "</li></ul>";
							}
						}
					}
					if(isSetAllbusinessAddress == 0 && isSetAllHomeAddress == 0){
						arrReq[arrReq.length] = "Address is required";
					}
					
                    #local.addressPreferencesFieldSet.jsValidation#
					
					if(memType.length > 0 && (memType == 'Student')){
						<cfif len(trim(local.lawSchoolField.fieldCode)) OR len(trim(local.lawSchoolOtherField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.lawSchoolField.fieldCode#'], "TEXT", false) && !_CF_hasValue(_CF_this['#local.lawSchoolOtherField.fieldCode#'], "TEXT", false)){
								arrReq[arrReq.length] = "Law School is required.";
							}
                        </cfif>
						<cfif len(trim(local.graduationDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.graduationDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Law School Graduation Date is required.";
                        </cfif>
					}

					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

                 function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
                    $("##state_table").show();
                    if(status == ''){
                        status = 'Active';
                    }
                    strOption = '';
                    <cfloop collection="#local.licenseStatus#" item="local.i" >
                        strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
                    </cfloop>
                    if(isChecked){
                        $('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
                                '<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
                                '<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
                                '<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
                                '<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" ></div>'+
                                '<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
                                '</div>');
                        $('##mpl_'+val+'_status').val(status);
                        mca_setupDatePickerField('mpl_'+val+'_activeDate');
                        $('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
                    }									
                    else{
                        $("##tr_state_"+val).remove();								
                    }
                    if($('##selectedLicense .row-fluid').length == 0){
                        $("##state_table").hide();
                    }	
                }
				
				function professionalLicensesmultiSelectReload(){									

					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					<cfelseif local.qryProLicenses.recordCount GT 0>
						<cfloop query="local.qryProLicenses">
							licenseChange(true,'#local.qryProLicenses.PLTypeID#','#local.qryProLicenses.PLName#','#local.qryProLicenses.LicenseNumber#','#local.qryProLicenses.ActiveDate#','#local.qryProLicenses.StatusName#');	
						</cfloop>
						<cfloop query="local.qryProLicenses">
							<cfset local.profLicenseIDList = listAppend(local.profLicenseIDList, local.qryProLicenses.PLTypeID)>
						</cfloop>						
					</cfif>
					
					$(".professionalLicensesHolder .mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' Please Select ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".professionalLicensesHolder .mpl_pltypeid option").each(function(){
								$('.professionalLicensesHolder ##tr_state_'+$(this).attr("value")).remove();
							});
							if($('.professionalLicensesHolder ##selectedLicense .row-fluid').length == 0){
								$(".professionalLicensesHolder ##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".professionalLicensesHolder .mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
				}
				
				function assignCompanyMemberData(memberNumber){
					var er_change = function(r) {
						var results = r;
						if( results.success ){
							var fieldMapping = {
								address1: "###local.businessAddress1Field.fieldCode#",
								address2: "###local.businessAddress2Field.fieldCode#",
								city: "###local.businessCityField.fieldCode#",
								stateid: "###local.businessstateprovField.fieldCode#",
								postalcode: "###local.businesspostalcodeField.fieldCode#",
								county: "###local.businesscountyField.fieldCode#",
								phone: "###local.businessphoneField.fieldCode#"
							};

							for (var key in fieldMapping) {
								if (results[key]) {
									var selector = fieldMapping[key];
									$(selector).val(results[key]);
									$(selector).change();
								}
							}
						}
					};

					var objParams = { memberNumber:memberNumber };
					TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
				}
				
				function changeCompany(value,text,firstName,lastName,memberNumber,recordTypeCode) {	
					if(value != "Please Select" && value != 0){
						$(".organizationFirmFieldSetHolder").html($(".organizationFirmFieldSetWrapper").html());
						if(text.length==0 && $(".organizationFirmFieldSetHolder ##m_company").length && $(".organizationFirmFieldSetHolder ##m_company").val().length){
							
						}else{
							$(".organizationFirmFieldSetHolder ##m_company").val(text);
						}
						assignCompanyMemberData(memberNumber);
						$("##orgMemberID").val(value);
						$("##orgCompanyName").val(text);						
						$("##orgFirstName").val(firstName);
						$("##orgLastName").val(lastName);
						$("##orgMemberNumber").val(memberNumber);
						$("##recordTypeCode").val(recordTypeCode);
                        if($(".organizationFirmFieldSetHolder ##m_company").parent().find('##changeCompany').length == 0){
                            $(".organizationFirmFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Firm</a>');					
                        }
						$("##changeCompany").show();
						$("##changeCompany").unbind("click");
                        $(".organizationsFirmsHolder").hide();
						$("##changeCompany").click(function(){
                            $("##changeCompany").hide();
                            if($('##m_company').val() != ''){
                                $(".organizationFirmFieldSetHolder").show();                                
                            }else{
                                $(".organizationFirmFieldSetHolder").hide();
                            }
							$(".organizationsFirmsHolder").show();
                            
                            $("##companyField").val("");
                            $("##companyField").multiselect( 'refresh' );
                            $(".organizationFirmFieldSetHolder").html('');
						});

                        if($('##m_company').val() != ''){
                            $(".organizationFirmFieldSetHolder").show();                                
                        }else{
                            $(".organizationFirmFieldSetHolder").hide();
                        }
						$("##m_company").unbind("change");
						$("##m_company").on('change', function() {		
							var company = $('##companyField option').filter(function () { return $(this).html() == $("##m_company").val(); });					
							if(company.length){
								$("##orgMemberID").val(company.val());
								$("##orgCompanyName").val(company.text());
								$("##orgFirstName").val(company.attr("firstName"));
								$("##orgLastName").val(company.attr("lastName"));
								$("##orgMemberNumber").val(company.attr("memberNumber"));
								$("##recordTypeCode").val(company.attr("recordTypeCode"));
							}else{								
								$("##orgMemberID").val(0);
								$("##orgCompanyName").val('');
								$("##orgFirstName").val('');
								$("##orgLastName").val('');
								$("##orgMemberNumber").val('');
								$("##recordTypeCode").val('');
							}
						});
					}else if(value == 0){
                        $(".organizationFirmFieldSetHolder").html($(".organizationFirmFieldSetWrapper").html());
                        if($(".organizationFirmFieldSetHolder ##m_company").parent().find('##changeCompany').length == 0){
                            $(".organizationFirmFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Firm</a>');					
                        }
                        $("##changeCompany").show();
                        $("##orgMemberID").val(0);
						$("##orgCompanyName").val('');
						$("##orgFirstName").val('');
						$("##orgLastName").val('');
						$("##orgMemberNumber").val('');
						$("##recordTypeCode").val('');
                        $(".organizationFirmFieldSetHolder").show();  
                        $("##changeCompany").click(function(){
                            $("##changeCompany").hide();
                            if($('##m_company').val() != ''){
                                $(".organizationFirmFieldSetHolder").show();                                
                            }else{
                                $(".organizationFirmFieldSetHolder").hide();
                            }
							$(".organizationsFirmsHolder").show();
                            
                            $("##companyField").val("");
                            $("##companyField").multiselect( 'refresh' );
                            $(".organizationFirmFieldSetHolder").html('');
						});     
                    }else{
                        $("##changeCompany").hide();
                        if(text != undefined){
                            if(text.length==0 && $(".organizationFirmFieldSetHolder ##m_company").length && $(".organizationFirmFieldSetHolder ##m_company").val().length){

                            }else{
                                $(".organizationFirmFieldSetHolder").html('');
                            }
                        }else{
                            $(".organizationFirmFieldSetHolder").html('');
                        }						
						$("##orgMemberID").val(0);
						$("##orgCompanyName").val('');
						$("##orgFirstName").val('');
						$("##orgLastName").val('');
						$("##orgMemberNumber").val('');
						$("##recordTypeCode").val('');
					}
				}
				$(document).ready(function() {	

					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>                 
                    
                    <cfif structKeyExists(local.strData.one, "orgMemberID") and val(local.strData.one["orgMemberID"]) NEQ 0>
						$("##orgMemberID").val('#val(local.strData.one["orgMemberID"])#');
						$("##orgCompanyName").val('#local.strData.one["orgCompanyName"]#');
						$("##orgFirstName").val('#local.strData.one["orgFirstName"]#');
						$("##orgLastName").val('#local.strData.one["orgLastName"]#');
						$("##orgMemberNumber").val('#local.strData.one["orgMemberNumber"]#');
						$("##recordTypeCode").val('#local.strData.one["recordTypeCode"]#');
					</cfif>

					$(".organizationFirmFieldSetHolder").html($(".organizationFirmFieldSetWrapper").html());
                    if($(".organizationFirmFieldSetHolder ##m_company").parent().find('##changeCompany').length == 0){
					    $(".organizationFirmFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Firm</a>');					
                    }

                    $("##companyField").multiselect({
						header: "",
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							var companyOption = $("option[value='"+ui.value+"']",this);
							var firstName = "";
							var lastName = "";
							var memberNumber = "";
							var recordTypeCode = "";
							if(companyOption.length){
								firstName = companyOption.attr("firstName");
								lastName = companyOption.attr("lastName");
								memberNumber = companyOption.attr("memberNumber");
								recordTypeCode = companyOption.attr("recordTypeCode");
							}
							changeCompany(ui.value,ui.text,firstName,lastName,memberNumber,recordTypeCode);
						}
					}).multiselectfilter({filterRule: 'beginsWith' });
					
					<cfif NOT application.mcCacheManager.sessionValueExists('captchaEntered') >
						showCaptcha();
					</cfif>
					var orgMemberID = $("##orgMemberID").val();
					var firstName = $("##orgFirstName").val();
					var lastName = $("##orgLastName").val();
					var memberNumber = $("##orgMemberNumber").val();
					var orgCompanyName = $("##orgCompanyName").val();					
					var recordTypeCode = $("##recordTypeCode").val();					
					if(parseInt(orgMemberID) == 0){
						orgMemberID = "Please Select";
						firstName = "";
						lastName = "";
						memberNumber = "";
						orgCompanyName = "";
						recordTypeCode = "";
					}
					changeCompany(orgMemberID,orgCompanyName,firstName,lastName,memberNumber,recordTypeCode);

					$("##newCompany").click(function(){
                        $('##m_company').val('');
                        $('##m_company').focus();
                        $(".organizationsFirmsHolder").hide();
						changeCompany(0,'','','','');
					});

					if($("##m_company").length && $("##m_company").val().length){
						$("##m_company").trigger('change');
					}	

					var memberTypeField = $('##'+"#local.memberTypeField.fieldCode#");
					$(memberTypeField).change(function(){adjustFieldsetDisplay();});
					$(memberTypeField).trigger('change');
					
					
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif application.mcCacheManager.sessionValueExists("captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif> enctype="multipart/form-data">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                    <input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<cfif local.qryLinkedParentCompany.recordCount>
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="#local.qryLinkedParentCompany.memberID#">
						<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="#local.qryLinkedParentCompany.company#">
						<input type="hidden" name="orgFirstName" id="orgFirstName" value="#local.qryLinkedParentCompany.firstName#">
						<input type="hidden" name="orgLastName" id="orgLastName" value="#local.qryLinkedParentCompany.lastName#">
						<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="#local.qryLinkedParentCompany.memberNumber#">
						<input type="hidden" name="recordTypeCode" id="recordTypeCode" value="#local.qryLinkedParentCompany.recordTypeCode#">
					<cfelse>
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">
						<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="">
						<input type="hidden" name="orgFirstName" id="orgFirstName" value="">
						<input type="hidden" name="orgLastName" id="orgLastName" value="">
						<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="">
						<input type="hidden" name="recordTypeCode" id="recordTypeCode" value="">
					</cfif>
                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<h1>#variables.strPageFields.FormTitle#</h1>
							</div>
						</div>
					</cfif>		

					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>						
						
						<span class="membershipTypeFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.membershipTypeFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.membershipTypeFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
						
						<span class="personalInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.personalInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.personalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<span class="organizationsFirmsHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">Business/Company Information</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">	
									<p>#variables.strPageFields.CompanyFirmSelector#</p>								
									<table cellpadding="3" border="0" cellspacing="0" >									
										<tr align="top">
											<td class="tsAppBodyText" width="10">&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Firm Lookup</td>
											<td class="tsAppBodyText">											
												<select name="companyField" class="companyField" id="companyField">
													<option "">Please Select</option>
													<cfloop query="local.qryCompanyByGroup">
														<option recordTypeCode="#local.qryCompanyByGroup.recordTypeCode#" value="#local.qryCompanyByGroup.memberID#" firstName="#local.qryCompanyByGroup.firstName#" lastname="#local.qryCompanyByGroup.lastName#" memberNumber="#local.qryCompanyByGroup.memberNumber#"  <cfif local.qryLinkedParentCompany.recordCount and local.qryLinkedParentCompany.company EQ local.qryCompanyByGroup.company>Selected</cfif>>#local.qryCompanyByGroup.company#</option>
													</cfloop>
												</select>
												<a href="javascript:void(0)" id="newCompany">New Business/Company</a>
											</td>
										</tr>
									</table>									
								</div>
							</div>
						</span>	
						<span class="organizationFirmFieldSetHolder">
						</span> 

						<span class="homeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.homeAddressFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>						
						
                        <span class="addressPreferencesFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressPreferencesFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.addressPreferencesFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
						
						<span class="professionalLicensesHolder fieldSetHolder"></span>

						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>
				<span class="organizationFirmFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.businessAddressFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.businessAddressFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>  
				<span class="professionalLicensesWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">Professional License Information</div>
						<div class="tsAppSectionContentContainer fieldSetContainer">
							<p>#variables.strPageFields.ProfessionalLicContent#</p>									
							<table cellpadding="3" border="0" cellspacing="0" >									
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText" >Professional License</td>
									<td class="tsAppBodyText">&nbsp;</td>
									<td class="tsAppBodyText">
										<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple" data-function="professionalLicensesmultiSelectReload">
											<cfloop query="local.qryOrgPlTypes">	
												<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif> <cfif local.qryOrgPlTypes.PLName EQ "Pennsylvania">required="true"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
											</cfloop>
										</select>
									</td>
								</tr>
								<tr class="top">
									<td class="tsAppBodyText" width="10"></td>
									<td class="tsAppBodyText"></td>
									<td class="tsAppBodyText"></td>
								</tr>
							</table>
							<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
								<tr>
									<td>
										<div class="row-fluid hide" id="state_table">
											<div class="span3 proLicenseLabel">
												<b>Type</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseNumberLabel#</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseDateLabel#</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseStatusLabel#</b>
											</div>
										</div>
										<span id="selectedLicense">
										</span>
									</td>
								</tr>					
							</table>
						</div>
					</div>
				</span>

				#application.objWebEditor.showEditorHeadScripts()#
				
				<script language="javascript">
					$(document).ready(function(){						
						<cfloop query="local.qryOrgAddressTypes">                       
							<cfif ListFindNoCase('Business,Home',local.qryOrgAddressTypes.addressType)>
								function businessAddressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _CF_this = document.forms['#variables.formName#'];
									var _address = _this.val();
									if(_address.length > 0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}

										if($('*[id^=mat_]').val() == ''){
											if($('*[id^=mat_] option:eq(1)').val() != undefined)
												$('*[id^=mat_]').val($('*[id^=mat_] option:eq(1)').val());
										}
										var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
										if($('*[id^=mat_]').val() == ''){
											if($('*[id^=mat_] option:eq(1)').val() != undefined)
												$('*[id^=mat_]').val($('*[id^=mat_] option:eq(1)').val());
										}
									}
								}

								businessAddressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));

								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									businessAddressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});

								$(document).on('change','.organizationFirmFieldSetHolder ##ma_#local.qryOrgAddressTypes.addresstypeid#_address1',function(){
									businessAddressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
							<cfelse>
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							</cfif>
							
						</cfloop>
					});

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>		

        <cfset local.response = "failure">

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step1")>
            <cfset structDelete(variables.formFields, "step1")>
        </cfif>			

        <cfset variables.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>	
        <cfset local.strData.one = checkSessionExist("step1")/>

		<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
			<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
		</cfif>
		<cfif application.mcCacheManager.sessionValueExists("subReturn")>
			<cfset application.mcCacheManager.sessionDeleteValue("subReturn")>
		</cfif>

		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>
				
		<cfif variables.isLoggedIn OR variables.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
            <cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
            <cfset local.strData.one.memberID = 0>
			<cfif structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0>
				<cfset local.membershipDroppedDate = getCustomFieldValueForMember(variables.orgID, variables.useMID, "795B71CE-284B-4630-A208-6281DF7F86A4")>
				<cfset local.strData.one["md_" & local.membershipDroppedDate.id] = DateFormat(local.membershipDroppedDate.value, "mm/dd/yyyy")>
			</cfif>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>

        <cfsavecontent variable="local.headCode">
			<cfoutput>					
				<script type="text/javascript">
					$(document).ready(function(){
						if (typeof arrUploaders !== 'undefined') {
							$.each(arrUploaders, function() {
								this.uploader.bind('BeforeUpload', function(uploader, file) {
									uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
								});
								this.uploader.start();
							});
						}
					});
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfset variables.formFields.step0.origMemberID = variables.useMID/>
        <cfset variables.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>
            <cfset variables.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
                                                subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
                                                enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
            
			<cfset application.mcCacheManager.sessionSetValue("captchaEntered", 1)>	
			<cfset local.response = "success">
		</cfif>	
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset variables.useMID = local.strData.zero.memberID/>

        <cfif StructIsEmpty(local.strData.one)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.thisAddonSubscriptionID = "">
		<cfset local.thisAddonSubscriptionIDArray = []>
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
			<cfset arrayAppend(local.thisAddonSubscriptionIDArray, local.thisAddonSubscriptionID)>
		</cfloop>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
        <cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>	
		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
        <cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Member Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style type="text/css">
					div.alert-danger{padding: 10px !important;}
                </style>

                <script type="text/javascript">
					
                    #local.result.jsAddonValidation#
				    function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = "Select Membership.";
						}
						
						$('div [data-setuid="0494C182-21E8-4F7C-B625-6AB3FFC6FFAC"] input:checkbox, div [data-setuid="78E0C0F8-3A43-4719-A3F6-86DE5BAA020C"] input:checkbox, div [data-setuid="965D5E45-3574-4637-BC4C-85B5F27D63B3"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
							var minlp = $(this).data("minallowed");
							if($("##"+$(this).attr("id").split('_')[0]).length){
								if($("input:checkbox:checked",this).length < minlp){
									arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $(this).data("setname") +".";
								}
							}
						});

						$('div [data-setuid="0494C182-21E8-4F7C-B625-6AB3FFC6FFAC"] input:checkbox, div [data-setuid="78E0C0F8-3A43-4719-A3F6-86DE5BAA020C"] input:checkbox, div [data-setuid="965D5E45-3574-4637-BC4C-85B5F27D63B3"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
							var maxlp = $(this).data("maxallowed");
							if($("##"+$(this).attr("id").split('_')[0]).length){
								if($("input:checkbox:checked",this).length > maxlp){
									arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $(this).data("setname") +".";
								}							
							}
						});
								
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
						$(".subRateOverrideBox").parent().siblings('input.subRateCheckbox[type=radio]').each(function(){
							if($(this).is(':checked') == false){
								$(this).parent().find('.subRateOverrideBox').remove();
							}
						});

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					
					function rateReset(){
						$('div [data-setuid="0494C182-21E8-4F7C-B625-6AB3FFC6FFAC"] input:checkbox, div [data-setuid="78E0C0F8-3A43-4719-A3F6-86DE5BAA020C"] input:checkbox, div [data-setuid="965D5E45-3574-4637-BC4C-85B5F27D63B3"] input:checkbox').each(function(){							
							if($(this).is(":checked")){
								$("##"+$(this).attr("name").split('_')[0]+"_addons").show();
								if($("[type='radio'][name='"+$(this).attr("name").split('_')[0]+"_rate']:visible").length){
									// Find all visible radio buttons in the group and check the one with the largest data-rate									
									var radioButtons = $("[type='radio'][name='"+$(this).attr("name").split('_')[0]+"_rate']:visible");
									if (!radioButtons.is(":checked")) {
										var maxPriceRadioButton = radioButtons
											.toArray()
											.reduce((max, radio) => {
												// Parse data-rate value, remove currency symbol, and convert to a number for comparison
												var currentRate = parseFloat($(radio).siblings(".labelText").find("[data-rate]").data("rate").replace("$", ""));
												var maxRate = parseFloat($(max).siblings(".labelText").find("[data-rate]").data("rate").replace("$", ""));
												return currentRate > maxRate ? radio : max;
											});

										// Set the radio button with the largest data-rate to checked
										$(maxPriceRadioButton).prop('checked', true);
										}
								}
							}else{
								$("##"+$(this).attr("name").split('_')[0]+"_addons").hide();
								$("##"+$(this).attr('id')+"_addons").find('input:checkbox').each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
								$("##"+$(this).attr('id')+"_addons").find('input:radio').each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
								$("[name="+$(this).attr('id')+"_rate]").each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
							}
						});
					}
					function frequencyBasedSelection(thisObj){
						if (thisObj.is(':checked') || (thisObj.attr("type") == "hidden" && thisObj.val().length)) {
							var selectedFrequencyUID = thisObj.data('frequencyuid');
							$('input[type="radio"][data-selectedfrequencyid]:not([name="sub#local.subscriptionID#_rate"]),input[type="hidden"][data-selectedfrequencyid]:not([name="sub#local.subscriptionID#_rate"])').each(function() {
								if($(this).parents('.subAddonWrapper').data("setuid") != 'E2768C29-54DA-4620-8196-3D136903831C' && $(this).parents('.subAddonWrapper').data("setuid") != 'BC0E87E0-3A04-40F9-AF83-95A44133EC59'){
									if ($(this).data('frequencyuid') !== selectedFrequencyUID) {
										$(this).prop('checked', false).parents('label').hide();
										$('label[for='+$(this).attr("id")+']').hide();									
										$("##"+$(this).attr("name").split("_")[0]).prop('checked', false);									
									} else {
										$(this).parents('label').show();
										$('label[for='+$(this).attr("id")+']').show();
									}
								}
							});
						}
					}
					
					$(document).ready(function(){
						<cfloop array="#local.thisAddonSubscriptionIDArray#" index="local.subscriptionID">
							$("##sub#local.subscriptionID#").css('visibility', 'hidden');
						</cfloop>
						
						$('div [data-setuid="0494C182-21E8-4F7C-B625-6AB3FFC6FFAC"] .subRateCheckbox, div [data-setuid="78E0C0F8-3A43-4719-A3F6-86DE5BAA020C"] .subRateCheckbox, div [data-setuid="965D5E45-3574-4637-BC4C-85B5F27D63B3"] .subRateCheckbox').change(function(){
							if($(this).is(":checked")){
								if($(this).closest('.subAvailableRates').parent().find('.subCheckbox:checked').length == 0){
									$(this).closest('.subAvailableRates').parent().find('.subCheckbox').trigger('click');
								}
							}
						});

						$('div [data-setuid="0494C182-21E8-4F7C-B625-6AB3FFC6FFAC"] input:checkbox, div [data-setuid="78E0C0F8-3A43-4719-A3F6-86DE5BAA020C"] input:checkbox, div [data-setuid="965D5E45-3574-4637-BC4C-85B5F27D63B3"] input:checkbox').change(function(){
							rateReset();
						});

						rateReset();
						<cfif local.memberTypeSelected EQ "Student">
							$('div [data-setuid="965D5E45-3574-4637-BC4C-85B5F27D63B3"] input:checkbox').parents("[data-maxallowed]").attr('data-maxallowed', 3);
							$('input[type="hidden"][data-subscriptionuid="D1E5972D-76F7-48F5-A657-352CA3AC1F7C"]').parents('.subAddonWrapper').find('[data-subscriptionuid]').each(function(){
								if($(this).data('subscriptionuid') == 'D1E5972D-76F7-48F5-A657-352CA3AC1F7C' || $(this).data('subscriptionuid') == 'A5F6CD9D-783E-4931-8A35-D5C08C9A9DD8'){
									$("##"+$(this).attr("name").split("_")[0]).prop('checked', true);
									$("##"+$(this).attr("name").split("_")[0]).css('visibility', 'hidden');
								}else{
									$("##"+$(this).attr("name").split("_")[0]).prop('checked', false).attr("disabled","disabled");
									$("##"+$(this).attr("name").split("_")[0]).css('visibility', 'visible');
								}
							});
						</cfif>
						$('input[type="radio"][name="sub#local.subscriptionID#_rate"]').on('change', function() {
							frequencyBasedSelection($(this));
						});
						if($('input[type="radio"][name="sub#local.subscriptionID#_rate"]:checked').length){
							$('input[type="radio"][name="sub#local.subscriptionID#_rate"]:checked').each(function(){
								frequencyBasedSelection($(this));						
							});
						}else if ($('input[type="hidden"][name="sub#local.subscriptionID#_rate"]').length){
							if ($('input[type="hidden"][name="sub#local.subscriptionID#_rate"]').val().length){
							$('input[type="hidden"][name="sub#local.subscriptionID#_rate"]').each(function(){
								frequencyBasedSelection($(this));						
							});
							}
						}
						
						// Select the element with the attribute data-rate
						$("[data-rate]").each(function() {
							// Get the current text of the element
							let currentText = $(this).text();

							// Replace the word "Full" with an empty string
							let updatedText = currentText.replace(" Full", "").trim();

							// Set the updated text back to the element
							$(this).text(updatedText);
						});
					});
					
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<h1>#variables.strPageFields.FormTitle#</h1>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>	
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step2")>
            <cfset structDelete(variables.formFields, "step2")>
        </cfif>			
        <cfset variables.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

        <cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>

        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>
		</cfif>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>
				<style>
					.payErrP{
						display: inline-block !important;
    					margin-left: 10px !important;
					}
				</style>
                <script type="text/javascript">	
                    function validatePaymentForm(isPaymentRequired) {

						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
                    $('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
							

						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
							
						});
						
					});
					$(document).ready(function(){
						<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
							setTimeout(function(){ 
								$("button[name='btnBack']").remove();
								strErr = $('.payErrMsg').html()
								$("button[name='btnContinue']").closest('div').append(strErr);
								$("button[name='btnContinue']").closest('div').find('> p').addClass('payErrP');
							}, 1000);
							setTimeout(function(){ 
                    
								$("button[name='btnContinue']").closest('div').find('.payErrP').remove();
							}, 10000);
						</cfif>
					});
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
                    <cfinput type="hidden" name="fa" id="fa" value="processPayment">
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
                    <cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <h1>#variables.strPageFields.FormTitle#</h1>
                            </div>
                        </div>
                    </cfif>	

                    <cfif len(variables.strPageFields.Step3TopContent)>
                        <div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
                    </cfif>

                    <div class="tsAppSectionHeading">Membership Selections Confirmation</div>
                    <div class="tsAppSectionContentContainer">						
                        #local.strResult.formContent#
                    </div>
                    <br/>

                    <div class="tsAppSectionHeading">Total Price</div>
                    <div class="tsAppSectionContentContainer">						
                        Amount Due: #dollarFormat(local.strResult.totalPrice)#<cfif local.strResult.strTax.totalTaxAmt gt 0> + #dollarFormat(local.strResult.strTax.totalTaxAmt)# tax</cfif>
                    </div>

                    <br/><br/>

                    <cfif local.paymentRequired>
                        #local.strReturn.paymentHTML#
						
						<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
                        	<cfset local.paymentObj = application.mcCacheManager.sessionGetValue('paymentObj',{})>
							<span class="payErrMsg hide">#local.paymentObj.accResponseMessage#</span>
						</cfif>
                    <cfelse>
                        <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>

					</cfif>

                </cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processPayment" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step3")>
            <cfset structDelete(variables.formFields, "step3")>
        </cfif>			
        <cfset variables.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

        <cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>

        <cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>

        <cfset local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>


		<cfset local.strData.one.memberID = variables.useMID>
        <cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
        <cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.strData.two.memberID = variables.useMID>
		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>
			<cfset application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>		
			<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)>
		
			<cfif val(local.strData.one["orgMemberID"]) NEQ 0>
				<cfset local.objAdminMember = CreateObject("component","model.admin.members.members") />
				<cfset local.availableRecordRelationships = local.objAdminMember.getAvailableRecordRelationships(orgID=variables.orgID, masterMemberID=val(local.strData.one["orgMemberID"]), childMemberID=variables.useMID) />
				
				<cfquery dbtype="query" name="local.qryStaffRecordRelationship">
					select recordTypeRelationshipTypeID, relationshipTypeName
					from [local].availableRecordRelationships.qryRecordRelationshipTypes
					where relationshipTypeName='Staff'
				</cfquery>
				<cfset local.objAdminMember.addRecordRelationship(mcproxy_orgID=variables.orgID ,masterMemberID=val(local.strData.one["orgMemberID"]),childMemberID=variables.useMID,recordTypeRelationshipTypeID=local.qryStaffRecordRelationship.recordTypeRelationshipTypeID,isActive=1)>
			</cfif>
		<cfelse>
			<cfset local.subReturn = application.mcCacheManager.sessionGetValue('subReturn',{})>
		</cfif>
		
		

		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(local.strData.three, 'mccf_payMethID') and structKeyExists(local.strData.three, 'p_#local.strData.three.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.three['p_#local.strData.three.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>
		
		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>
		<cfset local.processPaymentResponse = structnew()>

		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#",local.strData.three.mccf_payMeth)>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=local.strData.three } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.strData.three.mccf_payMethID, profileCode=local.strData.three.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
				<cfif local.strACCResponse.paymentResponse.RESPONSECODE neq 1>
					<cfset application.mcCacheManager.sessionSetValue("paymentObj", local.strACCResponse)>
					<cfset application.mcCacheManager.sessionSetValue("subReturn", local.subReturn)>
				<cfelse>
					<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
						<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
					</cfif>
					<cfif application.mcCacheManager.sessionValueExists("subReturn")>
						<cfset application.mcCacheManager.sessionDeleteValue("subReturn")>
					</cfif>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
				<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
					<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
				</cfif>
				<cfif application.mcCacheManager.sessionValueExists("subReturn")>
					<cfset application.mcCacheManager.sessionDeleteValue("subReturn")>
				</cfif>
			</cfif>
			<cfset local.processPaymentResponse = local.strACCResponse>
		</cfif>        

        <cfset local.paymentRequired = (local.strResult.totalPrice gt 0) >

        <cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
			</cfif>
		</cfif>

        <cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
        <cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Member Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.businessAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.businessAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.membershipTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.membershipTypeFieldSetUID, mode="confirmation", strData=local.strData.one)>

		<cfset local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		
        <cfsavecontent variable="local.invoice">
            <cfoutput>
				
				#local.membershipTypeFieldSet.fieldSetContent#
				#local.personalInformationFieldSet.fieldSetContent#
				<cfif local.memberTypeSelected EQ 'Attorney'>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
						</tr>				
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
												<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
													<thead>
														<tr valign="top">
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseNumberLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseDateLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseStatusLabel#</th>
														</tr>
													</thead>
													<tbody>
													<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
														<tr id="tr_state_#local.key#">
															<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
														</tr>
													</cfloop>
													</tbody>
												</table>
											</cfif>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					<br>
				</cfif>
				#local.homeAddressFieldSet.fieldSetContent#
				#local.businessAddressFieldSet.fieldSetContent#				
				#local.addressPreferencesFieldSet.fieldSetContent#
								
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            #local.strResult.formContent#
                            <br/>
                        </div>
                        <br/>
                        <strong>Total Price:</strong> #dollarFormat(local.strResult.totalPrice)#<cfif local.strResult.strTax.totalTaxAmt gt 0> + #dollarFormat(local.strResult.strTax.totalTaxAmt)# tax</cfif>
                        <br/>
                        </td>
                    </tr>
                </table>

                <cfif local.paymentRequired>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(local.strData.three,"mccf_payMeth")>
                                <table cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif>

            </cfoutput>
        </cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>

                <p>Here are the details of your application:</p>	

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>


		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>
			<!--- email to member --->
			<cfif application.MCEnvironment eq "production">
				<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
			</cfif>

			<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.memberEmail.from },
				emailto=[{ name="", email=variables.memberEmail.to }],
				emailreplyto=variables.ORGEmail.to,
				emailsubject=variables.memberEmail.subject,
				emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
				emailhtmlcontent=local.mailContent,
				siteID=variables.siteID,
				memberID=val(variables.useMID),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>
			<cfset local.emailSentToUser = local.responseStruct.success>
			<cfset local.response = 'success'>

			<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
				<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
			</cfif>
			<cfif application.mcCacheManager.sessionValueExists("subReturn")>
				<cfset application.mcCacheManager.sessionDeleteValue("subReturn")>
			</cfif>
		<cfelse>
			<cfset local.emailSentToUser = false>
			<cfset local.response = 'paymentFailed'>
		</cfif>
		
        <cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>												  
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>				   
		</cfif>

		<!--- email to association --->
        <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
        <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

        <cfsavecontent variable="local.specialText">
			<cfoutput>

            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(local.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#local.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>
                 #local.specialText#
                <p>Here are the details of your application:</p>

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

        <cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
                    <cfif len(variables.strPageFields.ConfirmationContent)>
                        <p>#variables.strPageFields.ConfirmationContent#</p>
                    </cfif>
                    <p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>

        <!--- relocate to message page --->
        <cfset session.invoice = local.invoice />
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="getCustomFieldValueForMember" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="UID" type="string" required="true">
		
		<cfquery name="local.qryGetCustomFieldValue" datasource="#application.dsn.membercentral.dsn#">
			SELECT mdcv.columnvalueDate as value, mdc.columnID as id
			FROM ams_memberDataColumns mdc
			INNER JOIN ams_memberdatacolumnvalues mdcv ON mdcv.columnID = mdc.columnID AND mdc.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.UID#">
			INNER JOIN ams_memberdata amd ON amd.valueID = mdcv.valueID AND amd.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">		
        </cfquery>
		
		<cfreturn local.qryGetCustomFieldValue>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
                </cfif>
                <div class="tsAppSectionContentContainer">
                    <p>Here are the details of your application:</p>						
                    #session.invoice#
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="hasSub" access="private" output="false" returntype="string">
        <cfargument name="memberID" type="Number" required="true">
        <cfargument name="formAction" type="string" required="false" default="">

		<cfset var local = structNew()>

        <cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfif application.mcCacheManager.sessionValueExists("paymentObj") AND arguments.formAction eq 'processPayment'>
			<cfset local.stReturn = "success">
			<cfreturn local.stReturn>
		</cfif>

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>
	
	<cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(subscriptionID=arguments.subscriptionID, memberID=arguments.memberID, isRenewalRate=arguments.isRenewalRate, siteID=arguments.siteID)>
		<cfset local.strReturn = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct, strData=arguments.strData, memberID=arguments.memberID)>
		
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
		<cfset local.maxFrequencyInstallments = local.objSubReg.getMaxFrequencyInstallments(siteID=arguments.siteID)>

		<cfset local.arrPaySchedule = arrayNew(1)>
		<cfset local.strPS = { date='', amount='' }>
		<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
			<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
		</cfloop>
		
		<cfset local.numPaymentsToUse = val(local.strReturn.subPaymentDates.NUMPAYMENTS)>
        <cfset local.NonUpFrontTotalAmt = 0>
        <cfif local.strReturn.totalMonthlyPrice GT 0>
		    <cfset local.NonUpFrontTotalAmt = local.strReturn.totalMonthlyPrice * local.numPaymentsToUse>
        <cfelseif local.strReturn.totalSemiAnnualPrice GT 0>
            <cfset local.NonUpFrontTotalAmt = local.strReturn.totalSemiAnnualPrice * local.numPaymentsToUse >
        </cfif>
		<cfset local.UpFrontTotalAmt = local.strReturn.totalFullPrice >
		
		<cfif local.UpFrontTotalAmt gt 0>
			<cfset local.arrPaySchedule[1] = { date=now(), amount=numberformat(local.UpFrontTotalAmt, "_.__") }>
			<cfset local.firstPaymentMinimum = numberformat(local.UpFrontTotalAmt, "_.__")>
		</cfif>

		<cfset local.distribAmt = local.NonUpFrontTotalAmt / local.numPaymentsToUse>
		<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.NonUpFrontTotalAmt - (numberformat((local.NonUpFrontTotalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

		<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
			<cfset local.loopAmt = 0>
			<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
			<cfset local.arrPaySchedule[local.loopCnt] = { date=now(), amount=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
		</cfloop>

		<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >
	
		<cfset local.strReturn.totalPrice = numberformat(local.arrPaySchedule[1].amount + local.strReturn.totalFullPriceRateOverridden + local.strReturn.totalMonthlyPriceRateOverridden + local.strReturn.totalSemiAnnualPriceRateOverridden, "_.__") />

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', strTax={ totalTaxAmt = 0 }, subTermDates = {}, subPaymentDates = {}, totalFullPriceRateOverridden = 0, totalMonthlyPriceRateOverridden = 0, totalSemiAnnualPriceRateOverridden = 0, totalFullPrice = 0, totalMonthlyPrice = 0, totalSemiAnnualPrice = 0, totalPrice = 0, subtotal = 0, subtotalRateOverridden = 0 }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">
		</cfif>

		<cfset local.qryMemberBillingAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=variables.orgID, memberID=arguments.memberID)>

		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<cfset local.rootSubTermFlag = arguments.subDefinitionStruct.rateTermDateFlag>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
							-
							<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
								<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
									<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>								
										<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">											
											<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>											
												#local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
											
												<cfif local.thisRateFrequency.frequencyName eq "Full">
													<cfset local.strReturn.totalFullPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
													<cfset local.strReturn.totalMonthlyPriceRateOverridden = 0/>
													<cfset local.strReturn.totalSemiAnnualPriceRateOverridden = 0/>
												<cfelseif local.thisRateFrequency.frequencyName eq "Monthly Installments">
													<cfset local.strReturn.totalMonthlyPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
													<cfset local.strReturn.totalFullPriceRateOverridden = 0/>
													<cfset local.strReturn.totalSemiAnnualPriceRateOverridden = 0/>
												<cfelseif local.thisRateFrequency.frequencyName eq "Semi-Annual">
													<cfset local.strReturn.totalSemiAnnualPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
													<cfset local.strReturn.totalFullPriceRateOverridden = 0/>
													<cfset local.strReturn.totalMonthlyPriceRateOverridden = 0/>													
												</cfif>
												<cfset local.strReturn.subtotal = local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments/>
												<cfif arguments.recursionLevel eq 1>														
													<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
													<cfset local.rootTermDateRFID = local.thisRateFrequency.rfid>														
													<cfset local.rootNumPayments = local.thisRateFrequency.numInstallments>
													<cfset local.rootRateInterval = local.thisRateFrequency.monthlyInterval>
													<cfset local.strReturn.subTermDates = local.objSubReg.getSubTermDates(termDateRFID=local.rootTermDateRFID, subTermFlag=local.rootSubTermFlag)>
													<cfset local.strReturn.subPaymentDates = local.objSubReg.getSubPaymentDates(local.strReturn.subTermDates.subTermStartDate,local.strReturn.subTermDates.subTermEndDate,local.rootNumPayments,local.rootRateInterval)>
												</cfif>
											</cfif>																																		
											</cfloop>
									<cfelse>
										<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
											<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
												<cfset local.maxFrequencyInstallments = local.thisRateFrequency.numInstallments />
												<cfif local.thisRateFrequency.frequencyName eq "Full">														
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
													<cfset local.strReturn.totalMonthlyPrice = 0/>
													<cfset local.strReturn.totalSemiAnnualPrice = 0/>
												<cfelseif local.thisRateFrequency.frequencyName eq "Monthly Installments">
													<cfset local.strReturn.totalMonthlyPrice = local.thisRateFrequency.rateAmt />
													<cfset local.strReturn.totalFullPrice = 0/>
													<cfset local.strReturn.totalSemiAnnualPrice = 0/>
												<cfelseif local.thisRateFrequency.frequencyName eq "Semi-Annual">
													<cfset local.strReturn.totalSemiAnnualPrice = local.thisRateFrequency.rateAmt />
													<cfset local.strReturn.totalFullPrice = 0/>
													<cfset local.strReturn.totalMonthlyPrice = 0/>											
												</cfif>	
												<cfset local.strReturn.subtotal = local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments/>
												
												<cfif arguments.recursionLevel eq 1>
													<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
													<cfset local.rootTermDateRFID = local.thisRateFrequency.rfid>														
													<cfset local.rootNumPayments = local.thisRateFrequency.numInstallments>
													<cfset local.rootRateInterval = local.thisRateFrequency.monthlyInterval>
													<cfset local.strReturn.subTermDates = local.objSubReg.getSubTermDates(termDateRFID=local.rootTermDateRFID, subTermFlag=local.rootSubTermFlag)>
													<cfset local.strReturn.subPaymentDates = local.objSubReg.getSubPaymentDates(local.strReturn.subTermDates.subTermStartDate,local.strReturn.subTermDates.subTermEndDate,local.rootNumPayments,local.rootRateInterval)>														
												</cfif>
												#local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
											</cfif>																																		
										</cfloop>
									</cfif>
									<cfset local.strReturn.strTax = createObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=arguments.subDefinitionStruct.GLAccountID, saleAmount=local.strReturn.totalFullPrice, transactionDate=now(), stateIDForTax=val(local.qryMemberBillingAddress.stateID), zipForTax=local.qryMemberBillingAddress.postalCode)>
								</cfif>
							</cfloop>
							</span>
						</div>
					</div>
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false />                            
									<cfset local.pcNumCounter = 1 />                                    
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false />
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true />
										</cfif>                                        
										<cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree, memberID=arguments.memberID) />
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
										<cfif len(trim(local.thisAddonSubForm.formContent))>                                        
											<cfset local.selectionFound = true />
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>
												<cfset local.strReturn.totalFullPriceRateOverridden = local.strReturn.totalFullPriceRateOverridden + local.thisAddonSubForm.totalFullPriceRateOverridden />
												<cfset local.strReturn.totalMonthlyPriceRateOverridden = local.strReturn.totalMonthlyPriceRateOverridden + local.thisAddonSubForm.totalMonthlyPriceRateOverridden />
												<cfset local.strReturn.totalSemiAnnualPriceRateOverridden = local.strReturn.totalSemiAnnualPriceRateOverridden + local.thisAddonSubForm.totalSemiAnnualPriceRateOverridden />
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
												<cfset local.strReturn.totalMonthlyPrice = local.strReturn.totalMonthlyPrice + local.thisAddonSubForm.totalMonthlyPrice />
												<cfset local.strReturn.totalSemiAnnualPrice = local.strReturn.totalSemiAnnualPrice + local.thisAddonSubForm.totalSemiAnnualPrice />
												<cfset local.strReturn.strTax.totalTaxAmt = local.strReturn.strTax.totalTaxAmt + local.thisAddonSubForm.strTax.totalTaxAmt>
											</cfif>
											<cfset local.pcNumCounter = local.pcNumCounter + 1 />                                            
											#local.thisAddonSubForm.formContent#
										</cfif>
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>
						</div>
					</cfif>                        
				</cfif>
			</cfoutput>                
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

    <cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
        <cfset local.isExist = false/>
        <cfset local.strData = {}>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, arguments.step)>
            <cfset local.strData = variables.formFields[arguments.step]/>
        </cfif>			

		<cfreturn local.strData>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						<cflocation url="#local.redirectLink#" addtoken="false">
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
                     <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>