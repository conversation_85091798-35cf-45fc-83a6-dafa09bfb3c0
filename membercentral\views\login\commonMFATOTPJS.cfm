<cfsavecontent variable="local.pageHeadJS">
	<cfoutput>
		<script type="text/javascript">
			function editMFATOTP() {
				$('.MFATOTPTool').hide();
				$('##editMFATOTP').show();
				$('##MFATOTPQRCode')
					.html('<i class="icon icon-spinner icon-spin"></i> Please wait...')
					.load('#local.manageSettingsResourceLink#&la=mfatotp&totpact=createfactor', initMFATOTPQRCode);
			}
			function showVerifyCodeContainer() {
				$('.MFATOTPTool').hide();
				$('##verifyMFATOTP').show();
			}
			function initMFATOTPQRCode() {
				if (Clipboard.isSupported()) {
					var cpyTOTPKey = new Clipboard('a##copyTOTPSecretKey', {
						text: function(trigger) {
							return $('##MFATOTPSecretKey').text();
						}
					});
					cpyTOTPKey.on('success', function(e) {
						Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
							var mc_noty_msg = '<div class=\"mc-noty-item\">Copied ' + e.text + '</div>';
							new Noty({ 
								type:'success',
								layout:'bottomLeft',
								text:mc_noty_msg,
								closeWith:['button','click'],
								timeout:3000 
							}).show();
						});
					});
					
				}
				$('##btnContinueToVerifyCode').prop('disabled',false);
			}
			function verifySecurityCode() {
				/* invalid */
				if ($('##MFA_TOTPInfoEnc').length == 0 || $('##securityCode').val().trim().length == 0)
					return false;
				
				/* verify */
				$('##btnVerifyCode').html('Please wait...').prop('disabled',true);
				$('##MFATOTPErrMsg').hide();
				
				$.ajax({
					url: '#local.manageSettingsResourceLink#&la=mfatotp&totpact=verifyfactor',
					type: 'POST',
					data: { "MFATOTPInfoEnc":$('##MFA_TOTPInfoEnc').val(), securityCode:$('##securityCode').val().trim() },
					dataType: 'json',
					success: function(response) { 
						if (response.success) {
							if (response.verified) {
								$('##MFATOTPValidMsg').html('Valid Code');
								gotoManageSettings();
							} else {
								$('##MFATOTPErrMsg').html('Invalid Code').show();
								$('##btnVerifyCode').html('Verify').prop('disabled',false);
							}
						} else {
							alert('We were unable to verify this code. Please try again.');
							$('##btnVerifyCode').html('Verify').prop('disabled',false);
						}
						
					}, fail: function(response) { 
						alert('We were unable to verify this code. Please try again.');
						$('##btnVerifyCode').html('Verify').prop('disabled',false);
					}
				});
			}
			function deleteMFATOTP() {
				$('##delMFATOTP').html('<i class="icon-spinner icon-spin"></i>');
				self.location.href = '/?pg=login&logact=manageSettings&la=mfatotp&totpact=delete';
			}
			function gotoManageSettings() {
				self.location.href = '/?pg=login&logact=manageSettings';
			}

			$(function() {
				editMFATOTP();
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageHeadJS)#">