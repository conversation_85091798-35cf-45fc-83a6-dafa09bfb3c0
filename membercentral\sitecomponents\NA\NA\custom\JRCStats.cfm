		<cfscript>
		arguments.event.paramValue('f_startdate','#month(now())#/1/#year(now())#');
		arguments.event.paramValue('f_enddate','');
		arguments.event.paramValue('reportAction','');

		variables.applicationReservedURLParams = "";
		variables.JRCSectionID = 580;
		local.customPage.baseURL	= "/?#getBaseQueryString(false)#";

		</cfscript>
		

		<cfsavecontent variable="local.js">
			<cfoutput>
			<script language="javascript">
				function runreport() { 
					document.forms['frmReport'].reportAction.value = 'run';	
					return true;
				}
				function csvreport() { 
					document.forms['frmReport'].reportAction.value = 'csv';	
					return true;
				}
				function pdfreport() { 
					document.forms['frmReport'].reportAction.value = 'pdf';	
					return true;
				}
				
				
				$(function() {
					mca_setupDatePickerRangeFields('f_startdate','f_enddate');	
					mca_setupDatePickerField('_paidthrudate');
						
				});
				
			</script>
			<style type="text/css">
			##f_startdate, ##f_enddate, ##f_paidthrudate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.js)#">


<cfoutput>
	
		<cfif arguments.event.getValue('reportAction') eq "csv">
			<cfset csvReport(event=arguments.event)>
		<cfelseif arguments.event.getValue('reportAction') eq "pdf">
			<cfset pdfReport(event=arguments.event)>
		<cfelseif arguments.event.getValue('reportAction') eq "run">
			<cfset local.report = screenReport(event=arguments.event)>
		</cfif>

	
	
				<h4>JRC Statistics</h4>
				
				<cfif arguments.event.getValue('reportAction','') EQ ''  >
				
				<cfform name="frmReport"  id="frmReport" method="post" action="#local.customPage.baseURL#" target="_blank">
						<cfinput type="hidden" name="reportAction"  id="reportAction" value="#arguments.event.getValue('reportAction')#">
					<fieldset>
					<legend><i class="icon-search"></i> Report Parameters </legend>

						<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
							<tr valign="top">
								<td colspan="3">Date Filter:
									<cfinput type="text" name="f_startdate"  id="f_startdate" value="#arguments.event.getValue('f_startdate')#" size="16"> to 
									<cfinput type="text" name="f_enddate"  id="f_enddate" value="#arguments.event.getValue('f_enddate')#" size="16"></td>
							</tr>
						</table>	
						<div>
						<br/>
								<button type="submit" class="tsAppBodyButton" onClick="return runreport();"><i class="icon-list"></i> Run Report</button>
								<button type="submit" class="tsAppBodyButton" onClick="return csvreport();"><i class="icon-list-alt"></i> Download CSV</button>
						</div>
					</fieldset>				
				</cfform>
			</cfif>
					
			<cfif arguments.event.getValue('reportAction') eq "run">
				#local.report#
			</cfif>
								
</cfoutput>



	<cffunction name="screenReport" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		

		<cfset local.qryReportData = generateData(event=arguments.event)>

		<cfsavecontent variable="local.screenReport">
			<cfif local.qryReportData.recordcount is 0>
				<cfoutput>Nothing found to report.<br/></cfoutput>
			<cfelse>
				<table class="tsAppBodyText" cellpadding="4" cellspacing="2" width="100%">
				<cfoutput query="local.qryReportData" group="contributorMemberID">
				<tr><td colspan="3"><b>#local.qryReportData.contributorName# - #local.qryReportData.TotalDocs#</b></td></tr>
					<cfoutput>
						<tr><td></td><td>#dateFormat(local.qryReportData.dateCreated, "mm/dd/yy")# #timeFormat(local.qryReportData.dateCreated, "hh:MM TT")#</td><td>#local.qryReportData.docTitle#</td></tr>
					</cfoutput>
				</cfoutput>
				</table>
				

				<cfoutput>
					<br/>
					<div><i>Report generated: #dateformat(now(),"dddd, mmmm d, yyyy")# at #timeformat(now(),"h:mm tt")# Central</i></div>	
				</cfoutput>
			</cfif>		
		</cfsavecontent>
	
		<cfreturn local.screenReport>
	</cffunction>

	<cffunction name="csvReport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "JRCStats.csv">

		<cfset arguments.event.setValue('exportFileName', "#local.strFolder.folderPathUNC#\#local.reportFileName#")>
		<cfset generateData(event=arguments.event)>		

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.showReport#" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="generateData" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryReportData" result="local.stats">
			set nocount on;

			<cfif arguments.event.getValue('reportAction') eq "csv" and arguments.event.getValue('exportFileName','') NEQ "">
				declare @filename varchar(400);
				select @filename = <cfqueryparam cfsqltype="CF_SQL_varchar" value="#application.objCommon.convertFileSeparator("#arguments.event.getValue('exportFileName')#",'\')#">;

				IF OBJECT_ID('tempdb..##OBReport') IS NOT NULL 
					DROP TABLE ##OBReport;
			</cfif>

			select d.documentid, d.dateCreated, docTitle, dv.contributorMemberID, m2.firstname + ' ' + m2.lastname as contributorName, Author, docs.TotalDocs,
						issues.Issue, resource.Resource
			<cfif arguments.event.getValue('reportAction') eq "csv" and arguments.event.getValue('exportFileName','') NEQ "">
				into ##OBReport
			</cfif>			
			from dbo.cms_documents d
			inner join dbo.cms_documentLanguages dl on dl.documentid  = d.documentID
			inner join dbo.cms_documentVersions dv on dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			inner join dbo.ams_members m on m.memberid = contributorMemberID
			inner join dbo.ams_members m2 on m2.memberid = m.activeMemberID
			inner join (
				select contributorMemberID, count(d.documentid) as TotalDocs
				from dbo.cms_documents d
				inner join dbo.cms_documentLanguages dl on dl.documentid  = d.documentID
				inner join dbo.cms_documentVersions dv on dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
				inner join dbo.ams_members m on m.memberid = contributorMemberID
				inner join dbo.ams_members m2 on m2.memberid = m.activeMemberID
				where d.sectionid =  <cfqueryparam value="#variables.JRCSectionID#" cfsqltype="CF_SQL_INTEGER">
			<cfif arguments.event.getValue('f_startdate','') NEQ ''>
				and d.dateCreated >=  <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_startdate')#">	
			</cfif>
			<cfif arguments.event.getValue('f_enddate','') NEQ ''>
				and d.dateCreated <= <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_enddate')#">	
			</cfif>
				group by contributorMemberID
			) docs on docs.contributorMemberID = dv.contributorMemberID
			inner join (
				select d.documentid, STRING_AGG(c.categoryName,', ') as Issue
				from dbo.cms_documents d
				left outer join dbo.cms_categorySiteResources csr on csr.siteResourceID = d.siteResourceID
				inner join dbo.cms_categories c on c.categoryid = csr.categoryid
				inner join dbo.cms_categoryTrees t on c.categoryTreeID = t.categoryTreeID and categoryTreeCode = 'Issue'
				where d.sectionid =  <cfqueryparam value="#variables.JRCSectionID#" cfsqltype="CF_SQL_INTEGER">
				<cfif arguments.event.getValue('f_startdate','') NEQ ''>
					and  d.dateCreated >=  <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_startdate')#">	
				</cfif>
				<cfif arguments.event.getValue('f_enddate','') NEQ ''>
					and  d.dateCreated <= <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_enddate')#">	
				</cfif>
				group by documentid
			) issues on issues.documentid = d.documentID
			inner join (
				select d.documentid, STRING_AGG(c.categoryName,', ') as Resource
				from dbo.cms_documents d
				left outer join dbo.cms_categorySiteResources csr on csr.siteResourceID = d.siteResourceID
				inner join dbo.cms_categories c on c.categoryid = csr.categoryid
				inner join dbo.cms_categoryTrees t on c.categoryTreeID = t.categoryTreeID and categoryTreeCode = 'Resource'
				where d.sectionid =  <cfqueryparam value="#variables.JRCSectionID#" cfsqltype="CF_SQL_INTEGER">
				<cfif arguments.event.getValue('f_startdate','') NEQ ''>
					and  d.dateCreated >=  <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_startdate')#">	
				</cfif>
				<cfif arguments.event.getValue('f_enddate','') NEQ ''>
					and  d.dateCreated <= <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_enddate')#">	
				</cfif>
				group by documentid
			) resource on resource.documentid = d.documentID			
			where d.sectionid =  <cfqueryparam value="#variables.JRCSectionID#" cfsqltype="CF_SQL_INTEGER">
			<cfif arguments.event.getValue('f_startdate','') NEQ ''>
				and  d.dateCreated >=  <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_startdate')#">	
			</cfif>
			<cfif arguments.event.getValue('f_enddate','') NEQ ''>
				and  d.dateCreated <= <cfqueryparam cfsqltype="CF_SQL_varchar" value="#arguments.event.getValue('f_enddate')#">	
			</cfif>
			order by contributorName, dateCreated;
			
			<cfif arguments.event.getValue('reportAction') eq "csv" and arguments.event.getValue('exportFileName','') NEQ "">
				DECLARE @selectsql varchar(max) = '
					SELECT *, ROW_NUMBER() OVER(order by contributorName, dateCreated) as mcCSVorder 
					*FROM* ##OBReport';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@filename, @returnColumns=0;

				select * from ##OBReport;

				IF OBJECT_ID('tempdb..##OBReport') IS NOT NULL 
					DROP TABLE ##OBReport;
			</cfif>
		</cfquery>		

		<cfreturn local.qryReportData>
	</cffunction>
	


