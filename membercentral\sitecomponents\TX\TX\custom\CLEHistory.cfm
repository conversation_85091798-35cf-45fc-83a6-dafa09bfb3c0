<cfscript>
	variables.applicationReservedURLParams 	= "";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";

	arguments.event.paramValue('ca','showList');
	local.today = now();
	arguments.event.paramValue('periodStartDate','1/1/#year(now())#');
	arguments.event.paramValue('periodEndDate',dateFormat(local.today,'m/dd/yyyy'));
	arguments.event.paramValue('membernumber','');
	
	local.orgCode	= event.getValue('mc_siteInfo.orgCode');
	local.periodStartDate = arguments.event.getValue('periodStartDate');
	local.periodEndDate = arguments.event.getValue('periodEndDate');
	local.membernumber = arguments.event.getValue('membernumber');

	local.arrCustomFields = [];
	local.tmpField = { name="LiveEventsInfo", type="CONTENTOBJ", desc="Editable content for Live Events Section", value="<h3> Live Conferences & Events </h3> <p>Editable content goes here</p>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="WebinarsInfo", type="CONTENTOBJ", desc="Editable content for Webinar Section", value="<h3> Live Webinars </h3> <p>Editable content goes here</p>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="OnDemandInfo", type="CONTENTOBJ", desc="Editable content for On Demand Online Seminars", value="<h3> Self-Paced Online Seminars </h3> <p>Editable content goes here</p>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
    
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

</cfscript>

<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>

<cfif arguments.event.getValue('panel') eq "viewCert">
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>

	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=CLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div class="tsAppHeading">CLE History</div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact TTLA for assistance.
	</div>
	</cfoutput>
<cfelse>

	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberDetails">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select m.membernumber, m.firstName, m.middleName, m.lastName, m.suffix, m.professionalSuffix, m.company, me.email
			from dbo.ams_members as m 
			inner join dbo.ams_memberEmails as me on me.orgID = @orgID and m.memberID = me.memberID
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
			where m.orgID = @orgID
			and m.memberNumber = <cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	</cfif>

	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		SELECT distinct a.authorityID, a.authorityName as authorityName, eco.ApprovalNum, 
			r.attended,e.eventid,et.startTime as eventStart,evr.startDate,evr.endDate,r.registrantID, r.dateRegistered, 
			cl.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,et.startTime) as CLEYear
			,ce.fieldvalue,ce.fieldtitle
		FROM dbo.ev_registrants as r
		INNER JOIN dbo.ev_registration as evr on evr.registrationID = r.registrationID AND r.recordedOnSiteID = evr.siteID
		INNER JOIN dbo.ev_events as e on e.eventid = evr.eventid AND e.siteID = evr.siteID
		INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
		INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID AND cl.languageID = 1
		INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID AND rc.creditAwarded = 1 AND r.status='A'
		INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		INNER JOIN dbo.crd_offerings as eco on eco.offeringID = ect.offeringID
		INNER JOIN dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		INNER JOIN dbo.crd_authoritySponsors as ecas on ecas.asid = ast.asid
		INNER JOIN dbo.crd_authorities as a on a.authorityID = ecas.authorityID	
		INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID 
		INNER JOIN dbo.ams_members as m1 on m1.memberID = r.memberID	
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			INNER JOIN dbo.ams_members as m on m.memberID = m1.activeMemberID AND m.orgID = #arguments.event.getValue('mc_siteinfo.orgID')# 
			<cfif local.membernumber NEQ ''>
				AND m.membernumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR">
			<cfelse>
				AND m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
		<cfelse>
			INNER JOIN dbo.ams_members as m on m.memberID = m1.activeMemberID
			AND m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>	
		LEFT JOIN 
		(
			SELECT fd.itemID, fv.valueString as fieldvalue,f.fieldText as fieldtitle FROM dbo.cf_fieldData as fd  
			INNER JOIN dbo.cf_fieldValues as fv on  fv.valueID  = fd.valueID 
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID  
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID 
			WHERE fd.itemType = 'CrossEvent'
			AND f.isActive = 1 
			AND f.uid = 'CCE0A2CF-6006-4FDB-BE28-E7A8EC2873C8'	
		) as ce on ce.itemID = e.siteResourceID
		WHERE isnull(ast.ovTypeName,cat.typeName) IN ('Ethics','General')
	    AND a.authorityName LIKE '%State Bar Of Texas Minimum Continuing Legal Education%'	
		<cfif len(trim(local.periodStartDate))>
			AND et.startTime >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
		</cfif> 
		<cfif len(trim(local.periodEndDate))>
			AND et.startTime <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodEndDate,"m/d/yyyy")# 23:59:59.997">
		</cfif> 
		ORDER BY et.startTime desc, e.eventid, a.authorityName, creditType
	</cfquery>
    <cfquery name="local.qryCLETotals" dbtype="query">
		SELECT CLEYear, creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by CLEYear, creditType
		order by CLEYear desc, creditType asc
	</cfquery>

	<cfquery name="local.qryCLECreditTypes" datasource="#application.dsn.membercentral.dsn#">
		SELECT distinct isnull(ast.ovTypeName,cat.typeName ) as creditType
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID AND r.recordedOnSiteID = evr.siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid AND e.siteID = evr.siteID
		inner join dbo.ev_times as et on et.eventID = e.eventID 
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		inner join dbo.ams_members as m on m.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
		inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID
		inner join dbo.crd_offerings as co on co.eventID = e.eventID
		where r.memberid = mMerged.memberID and isnull(ast.ovTypeName,cat.typeName) IN ('Ethics','General')
		order by creditType asc
	</cfquery>
	
	<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.orgCode).qryAssociation>
	<cfset local.showSW = false>
	<cfset local.depoMemberDataID = val(session.cfcUser.memberData.depomemberdataid)>
	<cfset local.memberIDToUse = session.cfcUser.memberData.memberID>

	<cfif (application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)) AND len(trim(local.membernumber))>
		<cfset local.qryGetDepoMemberData = application.objCustomPageUtils.mem_DepoMemberData(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.depoMemberDataID = val(local.qryGetDepoMemberData.depomemberdataid)>
		<cfset local.memberIDToUse = application.objMember.getMemberIDByMemberNumber(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
	</cfif>
	
	<cfif local.memberIDToUse>
		<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.orgCode#">
			<cfprocresult name="local.qrySWL" resultset="1">
			<cfprocresult name="local.qrySWOD" resultset="2">
			<cfprocresult name="local.qryCertPrograms" resultset="3">
		</cfstoredproc>
		
		<cfset local.showSW = true>	
		<!--- CLE Totals for Live Webinars --->
		<cfquery name="local.qrySWLCreditAwarded" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			-- swl
			SELECT ca.authorityID, ca.authorityName, datepart(year,sswl.dateStart) as CLEYear, 
				CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') as creditValueAwarded,
				CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as creditType, e.seminarID
			FROM dbo.tblEnrollments as e
			INNER JOIN dbo.tblEnrollmentsSWLive as eswl on eswl.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID 
			INNER JOIN dbo.swl_SeminarsInMyCatalogMy('#local.orgCode#') as simc on simc.seminarID = e.seminarID
			INNER JOIN dbo.tblUsers as u on u.userID = e.userID
			INNER JOIN dbo.tblEnrollmentsAndCredit AS eac on eac.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarCreditID = eac.seminarCreditID 
            INNER JOIN dbo.tblSeminarsSWLive AS sswl ON sswl.seminarID = sac.seminarID
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID 
			cross apply sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') as CRDA(credittype)
			cross apply ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') as CRDT(credittype)
			WHERE 1=1
			AND u.depomemberdataID = #local.depoMemberDataID#
			AND ca.authorityName LIKE '%State Bar Of Texas Minimum Continuing Legal Education%'	
			AND eac.earnedCertificate = 1
			AND e.isActive = 1 
			AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0	
			AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
			<cfif len(trim(local.periodStartDate))>
				AND sswl.dateStart >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
			</cfif> 
			<cfif len(trim(local.periodEndDate))>
				AND sswl.dateStart <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodEndDate,"m/d/yyyy")# 23:59:59.997">
			</cfif> 
			ORDER BY ca.authorityName, ca.authorityID, CLEYear, creditType
		</cfquery>
		
		<!--- CLE Totals for Live Conferences & Events --->
		<cfquery name="local.qrySWLCreditTotals" dbtype="query">
			SELECT DISTINCT CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
			FROM [local].qrySWLCreditAwarded
			GROUP BY CLEYear,  authorityName, creditType
			ORDER BY CLEYear, totalCLE
		</cfquery> 

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWOD" result="local.qrySWODResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">;
			
			SELECT e.enrollmentID, s.seminarID, s.seminarName, e.dateEnrolled, e.dateCompleted, e.passed, 
				s.isPublished, s.offerCertificate,
				CASE
					WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN 1
					ELSE (SELECT isnull((select TOP 1 CASE
										WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
										ELSE 0
							END as preReqFulfilled
				FROM dbo.tblSeminars as s2
				INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
				LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.MCMemberID = @memberID and e2.isActive = 1
				where pr.seminarID = s.seminarid
				),0))
				END as preReqFulfilled,
				sac.courseApproval as CourseID, sac.wddxCreditsAvailable
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID and s.isDeleted = 0
			INNER JOIN dbo.tblEnrollmentsSWOD AS esod ON e.enrollmentID = esod.enrollmentID
			INNER JOIN dbo.swod_SeminarsInMyCatalogMy('#local.orgCode#') as simc on simc.seminarID = s.seminarID
			left outer join dbo.tblEnrollmentsAndCredit AS eac 
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
			on eac.enrollmentID = e.enrollmentID
			WHERE e.MCMemberID = @memberID
			AND e.isActive = 1
			ORDER BY s.seminarName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<!--- CLE Totals for Self-Paced Online Seminars --->
		<cfquery name="local.qrySWODCreditAwarded" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			-- swod
			SELECT ca.authorityID, ca.authorityName, datepart(year,e.dateenrolled) as CLEYear, 
				CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') as creditValueAwarded,
				CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as creditType
			FROM dbo.tblEnrollments as e
			INNER JOIN dbo.tblEnrollmentsSWOD as eswod on eswod.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID 
			INNER JOIN dbo.swod_SeminarsInMyCatalogMy('#local.orgCode#') as simc on simc.seminarID = e.seminarID
			INNER JOIN dbo.tblUsers as u on u.userID = e.userID
			INNER JOIN dbo.tblEnrollmentsAndCredit AS eac on eac.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarCreditID = eac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID 
			cross apply sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') as CRDA(credittype)
			cross apply ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') as CRDT(credittype)
			WHERE 1=1 
			AND u.depomemberdataID = #local.depoMemberDataID#
			AND ca.authorityName LIKE '%State Bar Of Texas Minimum Continuing Legal Education%'	
			AND e.passed = 1
			AND e.isActive = 1
			AND eac.earnedCertificate = 1
			AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
			<cfif len(trim(local.periodStartDate))>
				AND e.dateenrolled >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
			</cfif> 
			<cfif len(trim(local.periodEndDate))>
				AND e.dateenrolled <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodEndDate,"m/d/yyyy")# 23:59:59.997">
			</cfif> 
			ORDER BY ca.authorityName, ca.authorityID, CLEYear, creditType
		</cfquery>
			
		<cfquery name="local.qrySWODCreditTotals" dbtype="query">
			SELECT CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
			FROM [local].qrySWODCreditAwarded
			GROUP BY CLEYear,  authorityName, creditType
			ORDER BY CLEYear, totalCLE
		</cfquery>
		
		<cfset local.CLEYearStruct = structNew()>
		<cfloop query="local.qryCLETotals">
			<cfif not structKeyExists(local.CLEYearStruct,local.qryCLETotals.CLEYear)>
				<cfset local.CLEYearStruct[local.qryCLETotals.CLEYear] = structNew()>
			</cfif>
			<cfif not structKeyExists(local.CLEYearStruct[local.qryCLETotals.CLEYear],local.qryCLETotals.creditType)>
				<cfif local.qryCLETotals.totalCLE gt 0>
					<cfset local.CLEYearStruct[local.qryCLETotals.CLEYear][local.qryCLETotals.creditType] = local.qryCLETotals.totalCLE>
				<cfelse>
					<cfset local.CLEYearStruct[local.qryCLETotals.CLEYear][local.qryCLETotals.creditType] = "---">
				</cfif>
			</cfif>
		</cfloop>	
	</cfif>
    	
	<cfsavecontent variable="local.CLEHead">
		<cfoutput>
			<style type="text/css">
				##clehistory th, ##swlhistory th, ##swodhistory th { text-align:center; border-bottom:1px solid ##666; }
				##periodStartDate, ##periodEndDate { 
					margin:0px!important;
					background-image:url("/assets/common/images/calendar/monthView.gif"); 
					background-position:right center; background-repeat:no-repeat; 
				}
				.Heading{font-size: 16px; font-family: 'Open Sans', Helvetica, sans-serif;color: ##3383af; letter-spacing: 0px; line-height: 18px;}
				.BodyTextTitle{text-transform:uppercase;font-size:16px;}
				.alertMsg {
					text-align: left;
					padding: 5px 20px 5px 45px;
					border: 2px solid ##fc6;
				}
				.printLogo{display:none}
				.b { font-weight: bold; }
				.table-heading { border-bottom: 1px solid ##666; }
				.row-fluid.cleHistorySummary {
					color: ##666;
				}
				.HeaderText{padding-top:10px;padding-bottom:10px;}
				.span3.visible-phone { padding-left: 0px; }
				.row-fluid.inDesktop { display:block; }
				.row-fluid.inPhone { display:none; }
				.cleHistorySummaryLiveConferences tr td{padding:10px;}				
				.cleHistoryDetail tr td,.LiveWebinarSummaryTotal tr td,##swlhistory tr td, ##swodhistory tr td, .swodsummary tr td,##swodhistory tr th{padding:10px; }

				@media screen and (min-width: 320px) and (max-width: 767px){
					##swodhistory tr.mobileHide,##swlhistory tr.swlhidephone,##clehistory tr.clehidephone{display:none;}
					##swodhistory tbody tr td,##swlhistory tbody tr td,##clehistory tbody tr td {
					display: block;
					clear: both;
					text-align: left;
					position: relative;
					padding-top: 5px;
					font-family:'Open Sans', Helvetica, sans-serif!important;
				}
					
				##swodhistory tbody tr td:before,##swlhistory tbody tr td:before,##clehistory tbody tr td:before{
					content: attr(data-title);					
					top:0;
					left:0;
					width: 100%;
					font-weight: bold;
					}				
				}
				
				@media screen and (min-width: 320px) and (max-width: 767px){
					.DatePosition {width: 100%;  float: left;  clear: both;}
					.DateFieldPosition{float:left;clear: both;}
					.periodDate{float:left;clear:both;}
					.clearPosition{float: left; margin-left: 10px;}
					.searchPosition{vertical-align: bottom; width: 100%;float: left;}
				}
				
			</style>
			<script language="JavaScript">
				function viewEVCert(rid) {
					var certURL = '/?pg=CLEHistory&panel=viewCert&mode=stream&rid=' + rid;
					window.open(certURL,'ViewCertificate','width=990,height=500');
				}
				
				function _FB_hasValue(obj, obj_type){
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
					else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
					else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
					else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
					else{ return true; }
				}
				function hideAlert() { $('##issuemsg').html('').hide(); };
				function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alertMsg').show(); };
				function _FB_validateForm(){
					var theForm = document.forms["frmCLE"];
					var arrReq = new Array();
					if (typeof $('##membernumber') != "undefined" && !_FB_hasValue(theForm['membernumber'], 'TEXT')) arrReq[arrReq.length] ='Must enter MemberNumber before you can filter report.';
					if (arrReq.length > 0) {
						var msg = 'The following fields are required:\n\n';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
						showAlert(msg);
						return false;
					}
				}
				function viewCert(eId) {
					var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
					window.open(certURL,'ViewCertificate','width=990,height=500');
				}
				
				$(function() {
					mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
				});
				
			</script>
		</cfoutput>
	</cfsavecontent>
	
	<cfhtmlhead text="#local.CLEHead#">
	
	<cfform method="POST" action="#local.customPage.baseURL#" name="frmCLE" onsubmit="return _FB_validateForm();" class="frmCLE">	
		<cfoutput>
			<span class="TitleText">My CLE History</span>
			<br>
			<img class="divider" style="width:100%;height:1px;" src="/images/dottedLine.png">
			<br><br>
			<span style="float:right;" class="hiddenPrint">
				<button class="btn" type="button" onClick="window.print();"><i class="icon-print"></i> Print</button>
			</span>
			<table cellpadding="4" cellspacing="0" width="100%">	
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<tr valign="top">
						<td colspan="4" >
							<b>MemberNumber:</b> &nbsp;
							<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber.">
						</td>
					</tr>
					<tr><td>&nbsp;</td></tr>
				</cfif>
								
				<cfif local.membernumber NEQ ''>
					<cfset local.firstName = local.memberDetails.firstName/>
					<cfset local.middleName = local.memberDetails.middleName/>
					<cfset local.lastName = local.memberDetails.lastName/>
					<cfset local.suffix = local.memberDetails.suffix/>
					<cfset local.professionalSuffix = local.memberDetails.professionalSuffix/>
					<cfset local.company = local.memberDetails.company/>
					<cfset local.email = local.memberDetails.email/>
					<cfset local.membernumberValue = local.memberDetails.membernumber/>
				<cfelse>
					<cfset local.firstName = session.cfcuser.memberdata.firstname/>
					<cfset local.middleName = session.cfcuser.memberdata.middlename/>
					<cfset local.lastName = session.cfcuser.memberdata.lastname/>
					<cfset local.suffix = session.cfcuser.memberdata.suffix/>
					<cfset local.professionalSuffix = session.cfcuser.memberdata.professionalSuffix/>
					<cfset local.company = session.cfcuser.memberdata.company/>
					<cfset local.email = session.cfcuser.memberdata.email/>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfset local.membernumberValue = ""/>
					<cfelse>
						<cfset local.membernumberValue = session.cfcuser.memberdata.membernumber/>
					</cfif>
				</cfif>
				<tr valign="top">
					<td colspan="4" >
						<cfquery name="local.qryFS" datasource="#application.dsn.membercentral.dsn#">
							select fieldSetID
							from dbo.ams_memberFieldSets
							where uid = '4935A421-8043-40B8-B8C7-B188E8379430'
						</cfquery>
						<cfstoredproc procedure="ams_getMemberFields" datasource="#application.dsn.membercentral.dsn#">	
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryFS.fieldSetID#">
							<cfprocresult name="local.xmlFields" resultset="1">
						</cfstoredproc>
						<cfset local.xmlFields = XMLParse(local.xmlFields.fieldsXML)>
						<cfset local.fsDescriptionContentID = xmlSearch(local.xmlFields,"string(/fields/@descriptionContentID)")>
						<cfquery name="local.qryGetFSContent" datasource="#application.dsn.membercentral.dsn#">
							select rawContent
							from dbo.fn_getContent(<cfqueryparam value="#local.fsDescriptionContentID#" cfsqltype="CF_SQL_INTEGER">,1)			
						</cfquery>
						<cfset local.strFields=structNew()>

						<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
							<cfset local.strFields[local.thisfield.xmlattributes.fieldCode] = local.thisfield.xmlattributes.fieldLabel>
						</cfloop>
						
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.emailType">
							SELECT TOP 1 AMET.emailTypeID 
							FROM ams_memberEmailTypes AMET 
							WHERE AMET.orgID=#arguments.event.getValue('mc_siteinfo.orgID')# AND AMET.emailTypeOrder = 1
						</cfquery>
						
						<cfset local.fullName = "">
						<cfif StructKeyExists(local.strFields,"m_firstname")>
							<cfset local.fullName = local.fullName & local.firstName>
							<cfif StructKeyExists(local.strFields,"m_middlename")>
								<cfset local.fullName = local.fullName & ' ' & local.middleName>
							</cfif>
							<cfif StructKeyExists(local.strFields,"m_lastname")>
								<cfset local.fullName = local.fullName & ' ' & local.lastName>
							</cfif>
							<cfif StructKeyExists(local.strFields,"m_suffix")>
								<cfset local.fullName = local.fullName & ' ' & local.suffix>
							</cfif>								
							<cfif StructKeyExists(local.strFields,"m_professionalsuffix")>
								<cfset local.fullName = local.fullName & ' ' & local.professionalSuffix>
							</cfif>
						</cfif>							
	
						<cfset local.companyName = "">
						<cfif StructKeyExists(local.strFields,"m_company")>
							<cfset local.companyName = local.company>
						</cfif>
						<cfset local.emailAdd = "">
						<cfif StructKeyExists(local.strFields,"me_#local.emailType.emailTypeID#_email")>
							<cfset local.emailAdd = local.email>
						</cfif>	
						
						<cfif StructKeyExists(local.strFields,"m_membernumber") and len(trim(local.membernumberValue)) GT 0>
							<cfset local.membernumberValue = "- (" & local.membernumberValue & ")">
						<cfelse>
							<cfset local.membernumberValue = "">
						</cfif>

						<table>
							<tr><td colspan="2">#local.qryGetFSContent.rawContent#</td></tr>
							<tr><td colspan="2"></td></tr>
							<cfif len(trim(local.fullName)) GT 0>
								<tr><td>#local.fullName# #local.membernumberValue#</td></tr>
							<cfelse>
								<tr><td>#local.firstName# #local.middleName# #local.lastName#</td></tr>
							</cfif>
							<cfif  len(trim(local.companyName)) GT 0>
								<tr><td>#local.companyName#</td></tr>
							</cfif>
							<cfif  len(trim(local.emailAdd)) GT 0>
								<tr><td>#local.emailAdd#</td></tr>
							</cfif>
						</table>
					</td>
				</tr> 		
				<tr>
					<td class="BodyTextTitle">
						<b>Date Filter:</b><br> <br>
					</td>
				</tr>
				<tr valign="top">
					<td rowspan="2" class="DatePosition">
						<b class="DateFieldPosition">Start Date </b><cfinput type="text" class="periodDate input-medium" name="periodStartDate" id="periodStartDate" value="#event.getValue('periodStartDate')#" autocomplete="off"  size="14"> 
						<a href="javascript:void(0)" class="clearPosition hiddenPrint" onclick="javascript:mca_clearDateRangeField('periodStartDate');">clear</a>&nbsp;
						<b class="DateFieldPosition" style="margin-top: 10px;">End Date </b><cfinput type="text" class="periodDate input-medium"  name="periodEndDate" id="periodEndDate" value="#local.periodEndDate#" size="14">
						 <a href="javascript:void(0)" class="clearPosition hiddenPrint"  onclick="javascript:mca_clearDateRangeField('periodEndDate');">clear</a>
					</td>
					<td colspan="6" class="searchPosition">
						<button type="submit" class="btn hiddenPrint" name="btnSubmit">Search</button>
					</td>
				</tr>
			</table>
			<br>
		</cfoutput>
	</cfform>
	<!--- Live Conferences & Events --->
	
	<cfsavecontent variable="local.CLEContent">
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" data-title="Date: ">#dateformat(local.qryCLE.eventStart,"mm/dd/yyyy")#</td>
				<td class="tsAppBodyText" data-title="Title: ">#local.qryCLE.contentTitle#</td>
				<td class="tsAppBodyText" data-title="Certificate: "><a href="javascript:viewEVCert('#local.rID#');"><i class="icon-print" style="max-width: 14px;"></i></a></td>
				<td class="tsAppBodyText" data-title="Course Materials Link">
					<cfif len(trim(local.qryCLE.fieldvalue))>
						<a href="#trim(local.qryCLE.fieldvalue)#" target="_blank" title="Course Materials Link"><i class="icon-link" style="max-width: 14px;"></i></a>
					</cfif>
				</td>
                <cfset local.EthicsCredit = 0>
				<cfset local.GeneralCredit = 0>
				<cfoutput>
					<cfif local.qryCLE.credittype EQ "Ethics">
						<cfset local.EthicsCredit =local.qryCLE.creditValueAwarded>
					</cfif>
					<cfif local.qryCLE.credittype EQ "General">
						<cfset local.GeneralCredit =local.qryCLE.creditValueAwarded>
					</cfif>                    
				</cfoutput>
				<cfif local.EthicsCredit EQ 0 
                    AND local.GeneralCredit EQ 0>
					<cfset local.EthicsCredit ="No Credit Awarded">
					<cfset local.GeneralCredit ="No Credit Awarded">                   
				</cfif>
				<td class="tsAppBodyText" data-title="Ethics: ">#local.EthicsCredit#</td>
				<td class="tsAppBodyText" data-title="General: ">#local.GeneralCredit#</td>
			</tr>	
		</cfoutput>
	</cfsavecontent>
			
    <cfoutput>
        <p style="padding-top:20px;"><img class="divider" style="width:100%;height:1px;" src="/images/dottedLine.png"></p>				
        <div>				
            #local.strPageFields.LiveEventsInfo#
        </div>
    </cfoutput>
	<cfif local.qryCLE.recordcount>
		<cfoutput>		
            <h4 class="Heading">Summary</h4>
            <table class="cleHistorySummaryLiveConferences" style="text-align:center">
                <tbody>
                    <tr style="font-weight:bold; border-bottom:1px solid ##000;">
                        <td >Year</td>
                        <td style="text-align:center">Ethics</td>
                        <td style="text-align:center">General</td>
                    </tr>
                    
                    <cfset local.yearList = "">
                    <cfloop collection="#local.CLEYearStruct#" item="local.thisYear">
                        <cfset local.yearList = ListAppend(local.yearList,local.thisYear)>
                    </cfloop>
                        
                    <cfset local.yearList =  ListSort(local.yearList,"Numeric", "Desc")>
                        
                    <cfloop list="#local.yearList#" index="local.thisYear">
                        <tr>
                            <td>#local.thisYear#</td>
                            <cfloop query="local.qryCLECreditTypes">
                                <cfif structKeyExists(local.CLEYearStruct[local.thisYear],local.qryCLECreditTypes.creditType)>
                                    <td>#local.CLEYearStruct[local.thisYear][local.qryCLECreditTypes.creditType]#</td>
                                <cfelse>
                                    <td>0</td>
                                </cfif>
                            </cfloop>							
                        </tr>
                    </cfloop>						
                </tbody>
            </table>
            <br>				
            <h4 class="Heading">DETAILS</h4>				
            <div class="table-responsive">		
                <table  cellpadding="2" cellspacing="0" border="0" id="clehistory" class="cleHistoryDetail" style="text-align:center; width: 100%;">
                    <thead>
                        <tr style="border-bottom: 1px solid ##000;font-weight: bold;" class="clehidephone">
                            <th >Date</th>
                            <th >Title</th>
                            <th >Certificate</th>	
							<th >Course Materials Link</th>					
                            <th >Ethics</th>
                            <th >General</th>
                        </tr>
                    </thead>
                    #local.CLEContent#
                </table>
            </div>
            <br>
			
		</cfoutput>
	<cfelse>
		<cfoutput>
			<div class="tsAppBodyText">
				There are no Live Conferences & Events to display.
			</div>
			<br>	
		</cfoutput>
	</cfif>	
		
	<!--- Live Webinars --->
	<cfif local.showSW>
		
		<cfquery name="local.qrySWLOrder"  dbtype="query">
			SELECT * FROM  [local].qrySWL where 
			datecompleted	>= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
			ORDER BY dateStart DESC
		</cfquery>
        <cfoutput>
            <p style="padding-top:20px;"><img class="divider" style="width:100%;height:1px;" src="/images/dottedLine.png"></p>				
            <div>				
                #local.strPageFields.WebinarsInfo#
            </div>
        </cfoutput>
				
		<cfif local.qrySWLOrder.recordcount>
			<cfoutput>
				<cfif local.qrySWLCreditAwarded.recordcount> 
                    <h4 class="Heading" id="LiveWebinarSummary">Summary</h4>   <!--- Live Seminar Summary--->
					<table cellpadding="2" cellspacing="0" border="0">
						<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
						<cfloop query="local.qrySWLCreditTotals">
							<cfset local.arr[local.qrySWLCreditTotals.cleYear][local.qrySWLCreditTotals.authorityName][local.qrySWLCreditTotals.creditType] = local.qrySWLCreditTotals.totalCLE>
						</cfloop>
						<tr>
							<td>
								<table class="block LiveWebinarSummaryTotal" style="text-align:center;">
									<thead>
										<tr style="border-bottom:1px solid ##000;font-weight:bold;">
											<td>Year</td>
											<td >Ethics</td>
                                            <td >General</td>
										</tr>
									</thead>
									<tbody>
										<cfloop query="local.qrySWLCreditTotals">
											<tr>
												<td colspan="1">#local.qrySWLCreditTotals.cleYear#</td>
												<td><cfif local.qrySWLCreditTotals.creditType EQ "Ethics">#NumberFormat(local.qrySWLCreditTotals.totalCLE,"0.00")#<cfelse>0</cfif></td>
												<td><cfif local.qrySWLCreditTotals.creditType EQ "General">#NumberFormat(local.qrySWLCreditTotals.totalCLE,"0.00")#<cfelse>0</cfif> <td>										        
                                            </tr>
                                        </cfloop>
									
									</tbody>
								</table>	
							</td>
						</tr>
					</table>
					
				</cfif>
				<br/>
			</cfoutput>
			<cfoutput>
				<h4 class="Heading">DETAILS</h4>
				<div class="table-responsive">
				<table cellpadding="2" cellspacing="0" border="0" id="swlhistory" style="text-align:center; width: 100%;">
					<thead>
						<tr style="border-bottom:1px solid ##000;font-weight:bold;" class="swlhidephone">
							<th>DATE</th>
							<th>TITLE</th>
							<th>CERTIFICATE</th>
							<th >Ethics</th>
                            <th >General</th>
						</tr>
					</thead>
					<cfset local.oddeven = 0>
		
					<cfloop query="local.qrySWLOrder">
						<cfquery name="local.qryCheckEarned" dbtype="query">
							SELECT *
							FROM [local].qrySWLCreditAwarded
							WHERE seminarID = <cfqueryparam value="#local.qrySWLOrder.seminarID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
						<cfstoredproc procedure="sw_getCreditsforSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySWLOrder.seminarID#" null="No">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('mc_siteInfo.siteCode')#">
							<cfprocresult name="local.qryCredit" resultset="1">
						</cfstoredproc>
						
						<cfquery name="local.qryCreditDistinct" dbtype="query">
							SELECT distinct *
							FROM [local].qryCredit
							WHERE authorityCode = '#local.orgCode#'
							ORDER BY authorityCode
						</cfquery>	
						<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWLOrder.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
						
						<cfset local.oddeven = local.oddeven + 1>
						<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
							<td class="tsAppBodyText"  data-title="DATE: ">#DateFormat(local.qrySWLOrder.dateStart,'m/d/yyyy')#</td>
							<td class="tsAppBodyText"  data-title="TITLE: ">#encodeForHTML(local.qrySWLOrder.seminarName)#</td>
							<td class="tsAppBodyText" data-title= "CERTIFICATE: ">
								<cfif len(local.qrySWLOrder.dateCompleted) AND local.qrySWLOrder.passed is 1 AND local.qrySWLOrder.offerCertificate>
									<a href="javascript:viewCert('#local.eID#')" title="Print Certificate"><i class="icon-print"></i></a>
								<cfelseif len(local.qrySWLOrder.dateCompleted) AND local.qrySWLOrder.passed is 1 AND not local.qrySWLOrder.offerCertificate>
									Completed
								<cfelseif len(local.qrySWLOrder.dateCompleted) AND local.qrySWLOrder.passed is 0>
									Failed
								<cfelseif now() lt local.qrySWLOrder.dateStart>
									Not yet begun
								<cfelse>
									Did Not Attend
								</cfif>
							</td>
                 
                            <cfset local.EthicsCredit = 0>
							<cfset local.GeneralCredit = 0>
							
							<cfoutput>
								<cfif local.qryCheckEarned.creditType EQ "Ethics" AND local.qryCheckEarned.recordcount>
									<cfset local.EthicsCredit = local.qryCheckEarned.creditValueAwarded>
								</cfif>	
								<cfif local.qryCheckEarned.creditType EQ "General" AND local.qryCheckEarned.recordcount>
									<cfset local.GeneralCredit = local.qryCheckEarned.creditValueAwarded>
								</cfif>
							</cfoutput>

                            <cfif local.EthicsCredit EQ 0 
                                AND local.GeneralCredit EQ 0>
                                <cfset local.EthicsCredit ="No Credit Awarded">
                                <cfset local.GeneralCredit ="No Credit Awarded">
                            </cfif>
                            <td class="tsAppBodyText" data-title="Ethics: "><cfif IsNumeric(local.EthicsCredit) AND local.EthicsCredit NEQ 0>#NumberFormat(local.EthicsCredit,"0.00")#<cfelse>#local.EthicsCredit#</cfif></td>
                            <td class="tsAppBodyText" data-title="General: "><cfif IsNumeric(local.GeneralCredit) AND local.GeneralCredit NEQ 0>#NumberFormat(local.GeneralCredit,"0.00")#<cfelse>#local.GeneralCredit#</cfif></td>
                        </tr>
					</cfloop>
				</table>
				</div><br/><br/>	
			</cfoutput>
        <cfelse>
            <cfoutput>
                <div class="tsAppBodyText">
                    There are no Live Webinars to display.
                </div>
                <br>	
            </cfoutput>
		</cfif>
		
		<!--- SWOD --->
		<!--- Self-Paced Online Seminars--->
        <cfoutput>
            <p style="padding-top:20px;"><img class="divider" style="width:100%;height:1px;" src="/images/dottedLine.png"></p>				
            <div>				
                #local.strPageFields.OnDemandInfo#
            </div>
        </cfoutput>
		<cfoutput>
            
			<cfif local.qrySWOD.recordcount>
				<cfif local.qrySWODCreditAwarded.recordcount>
					<h4 class="Heading">Summary</h4>
                    <table cellpadding="2" cellspacing="0" border="0">
						<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
						<cfloop query="local.qrySWODCreditTotals">
							<cfset local.arr[local.qrySWODCreditTotals.cleYear][local.qrySWODCreditTotals.authorityName][local.qrySWODCreditTotals.creditType] = local.qrySWODCreditTotals.totalCLE>
						</cfloop>
						<tr>
							<td>
								<table class="block LiveWebinarSummaryTotal" style="text-align:center;">
									<thead>
										<tr style="border-bottom:1px solid ##000;font-weight:bold;">
											<td>Year</td>
											<td >Ethics</td>
                                            <td >General</td>
										</tr>
									</thead>
									<tbody>
										<cfloop query="local.qrySWODCreditTotals">
											<tr>
												<td colspan="1">#local.qrySWODCreditTotals.cleYear#</td>
												<td><cfif local.qrySWODCreditTotals.creditType EQ "Ethics">#NumberFormat(local.qrySWODCreditTotals.totalCLE,"0.00")#<cfelse>0</cfif></td>
												<td><cfif local.qrySWODCreditTotals.creditType EQ "General">#NumberFormat(local.qrySWODCreditTotals.totalCLE,"0.00")#<cfelse>0</cfif> <td>
                                            </tr>										
                                        </cfloop>
									
									</tbody>
								</table>	
							</td>
						</tr>
					</table>
				</cfif>
				<br>
				<h4 class="Heading">DETAILS</h4>
				<div class="table-responsive">
				<table cellpadding="2" cellspacing="0" border="0" id="swodhistory" style="text-align:center ;width: 100%;" >	
					<thead>
						 <tr style="border-bottom:1px solid ##000;font-weight:bold;" class="mobileHide">
							<th>ENROLLED DATE</th>
							<th>TITLE</th>					
							<th>CERTIFICATE</th>
							<th >Ethics</th>
                            <th >General</th>						
							<th>OPTIONS</th>
						</tr>
					</thead>
				<cfset local.oddeven = 0>	
				<cfloop query="local.qrySWOD">
				<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWOD.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			
				<cfset local.arrCredits = []>
				
				<cfif len(trim(local.qrySWOD.wddxCreditsAvailable))>
					<cfset local.arrCredits = xmlSearch(local.qrySWOD.wddxCreditsAvailable,"/wddxPacket/data/array/struct/var/string")>
				</cfif>	
				<cfset local.creditReportedText = ''>
				<cfset local.len = ArrayLen(local.arrCredits)>
				<cfset local.i = 1>
				
				<cfscript> 
						if(local.i LTE local.len){
							local.creditReportedText = local.creditReportedText & local.arrCredits[local.i].XmlText;
						}
				</cfscript>
				
				<cfset local.oddeven = local.oddeven + 1>
				<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
					<td class="tsAppBodyText" data-title="ENROLLED DATE: ">#DateFormat(local.qrySWOD.dateenrolled,'m/d/yyyy')#</td>
					<td class="tsAppBodyText" data-title="TITLE: ">#encodeForHTML(local.qrySWOD.seminarName)#</td>
					<td class="tsAppBodyText" data-title="CERTIFICATE: "><cfif local.qrySWOD.passed AND local.qrySWOD.offercertificate><a href="javascript:viewCert('#local.eID#');" title="Print Certificate"><i class="icon-print"></i></a></cfif></td>
										
					<cfset local.EthicsCredit = 0>
                    <cfset local.GeneralCredit = 0>
					<cfoutput>
                        <cfif local.qrySWLCreditTotals.creditType EQ "Ethics">
                            <cfset local.EthicsCredit = local.creditReportedText>
                        </cfif>	
                        <cfif local.qrySWLCreditTotals.creditType EQ "General">
                            <cfset local.GeneralCredit = local.creditReportedText>
                        </cfif>
                       
					</cfoutput>
					<cfif local.qrySWOD.PASSED eq 1>
						<td class="tsAppBodyText" data-title="Ethics: "><cfif IsNumeric(local.EthicsCredit) AND local.EthicsCredit NEQ 0>#NumberFormat(local.EthicsCredit,"0.00")#<cfelse>0</cfif></td>
                        <td class="tsAppBodyText" data-title="General: "><cfif IsNumeric(local.GeneralCredit) AND local.GeneralCredit NEQ 0>#NumberFormat(local.GeneralCredit,"0.00")#<cfelse>0</cfif></td>
					<cfelse>
						<td class="tsAppBodyText" data-title="Ethics: ">&nbsp;</td>
						<td class="tsAppBodyText" data-title="General: ">&nbsp;</td>		
					</cfif>	
				
					<td class="tsAppBodyText">
						<cfif len(local.qrySWOD.dateCompleted) is 0>
							
							<cfif local.qrySWOD.isPublished and local.qrySWOD.preReqFulfilled>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Begin</a>
							<cfelseif local.qrySWOD.isPublished>
								Awaiting Prereqs
							<cfelse>
								Not available
							</cfif>
						<cfelse>
							<cfif local.qrySWOD.isPublished>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Review</a>
							</cfif>
							
						</cfif>
					</td>
				</tr>
			    </cfloop>	
				</table>
				</div><br/><br/>
            <cfelse>
                <cfoutput>
                    <div class="tsAppBodyText">
                        There are Self-Paced Online Seminars to display.
                    </div>
                    <br>	
                </cfoutput>
			</cfif>	
		</cfoutput>
	<cfelse>
	<!---	<cfoutput>Nothing to report during the SELECTed period.</cfoutput>--->
	</cfif>	
</cfif>