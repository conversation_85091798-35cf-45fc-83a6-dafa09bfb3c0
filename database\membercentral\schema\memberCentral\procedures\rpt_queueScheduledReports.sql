ALTER PROC dbo.rpt_queueScheduledReports

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @2MinFromNowDate datetime = DATEADD(MINUTE, 2, GETDATE());
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='scheduledReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- Queue active reports for processing
	INSERT INTO platformQueue.dbo.queue_scheduledReport (srItemID, reportID, resetByMemberID, isReset, statusID, dateAdded, dateUpdated)
	SELECT sch.itemID, sch.reportID, null, 0, @statusReady, GETDATE(), GETDATE()
	FROM dbo.rpt_scheduledReports as sch
	INNER JOIN dbo.rpt_SavedReports as sr ON sr.reportID = sch.reportID
	WHERE sch.isRunning = 0
	AND sch.nextRunDate <= @2MinFromNowDate
	AND sr.Status = 'A' -- Only queue active reports
	AND NOT EXISTS (
		SELECT itemID
		FROM platformQueue.dbo.queue_scheduledReport
		where srItemID = sch.itemID
	);

	-- Advance schedule dates for inactive reports without queuing them
	UPDATE sch
	SET nextRunDate = CASE
		WHEN sch.endRunDate IS NOT NULL AND sch.nextRunDate >= sch.endRunDate THEN NULL
		ELSE dbo.fn_getNextScheduledDate(sch.nextRunDate, sch.interval, sch.intervalTypeID)
	END
	FROM dbo.rpt_scheduledReports as sch
	INNER JOIN dbo.rpt_SavedReports as sr ON sr.reportID = sch.reportID
	WHERE sch.isRunning = 0
	AND sch.nextRunDate <= @2MinFromNowDate
	AND sr.Status = 'I' -- Only advance dates for inactive reports
	AND NOT EXISTS (
		SELECT itemID
		FROM platformQueue.dbo.queue_scheduledReport
		where srItemID = sch.itemID
	);

	IF @@ROWCOUNT > 0
		EXEC dbo.sched_resumeTask @name='Process Scheduled Report Queue', @engine='MCLuceeLinux';

    RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
