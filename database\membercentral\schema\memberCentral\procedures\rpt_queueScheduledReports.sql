ALTER PROC dbo.rpt_queueScheduledReports

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @2MinFromNowDate datetime = DATEADD(MINUTE, 2, GETDATE()), srItemID int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='scheduledReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- Advance schedule dates for inactive reports without queuing them
	CREATE TABLE #tmpInactiveReports (
		srItemID int PRIMARY KEY,
		processed bit DEFAULT 0
	);

	-- Queue active reports for processing
	INSERT INTO platformQueue.dbo.queue_scheduledReport (srItemID, reportID, resetByMemberID, isReset, statusID, dateAdded, dateUpdated)
	SELECT sch.itemID, sch.reportID, null, 0, @statusReady, GETDATE(), GETDATE()
	FROM dbo.rpt_scheduledReports as sch
	INNER JOIN dbo.rpt_SavedReports as sr ON sr.reportID = sch.reportID
	WHERE sch.isRunning = 0
	AND sch.nextRunDate <= @2MinFromNowDate
	AND sr.Status = 'A' -- Only queue active reports
	AND NOT EXISTS (
		SELECT itemID
		FROM platformQueue.dbo.queue_scheduledReport
		where srItemID = sch.itemID
	);

	INSERT INTO #tmpInactiveReports (srItemID)
	SELECT sch.itemID
	FROM dbo.rpt_scheduledReports as sch
	INNER JOIN dbo.rpt_SavedReports as sr ON sr.reportID = sch.reportID
	WHERE sch.isRunning = 0
	AND sch.nextRunDate <= @2MinFromNowDate
	AND sr.Status = 'I' -- Only advance dates for inactive reports
	AND NOT EXISTS (
		SELECT itemID
		FROM platformQueue.dbo.queue_scheduledReport
		where srItemID = sch.itemID
	);

	WHILE EXISTS (SELECT 1 FROM #tmpInactiveReports WHERE processed = 0)
	BEGIN
		SELECT TOP 1 @srItemID = srItemID
		FROM #tmpInactiveReports
		WHERE processed = 0;

		EXEC dbo.rpt_setScheduledReportNextRunDate @srItemID=@srItemID;

		UPDATE #tmpInactiveReports
		SET processed = 1
		WHERE srItemID = @srItemID;
	END

	IF @@ROWCOUNT > 0
		EXEC dbo.sched_resumeTask @name='Process Scheduled Report Queue', @engine='MCLuceeLinux';
		
	IF OBJECT_ID('tempdb..#tmpInactiveReports') IS NOT NULL 
		DROP TABLE #tmpInactiveReports;

    RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
