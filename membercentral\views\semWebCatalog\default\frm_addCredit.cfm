<cfset local.qryGetCreditAuthorities = attributes.data.qryGetCreditAuthorities>
<cfset local.pageName = attributes.data.pageName>
<cfset local.formlink = attributes.data.formlink>

<cfsavecontent variable="local.zoneJS">
	<cfoutput>
		<style type="text/css">
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
		##eventDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
		</style>
		<script language="javascript">
		_validateForm = function(thisForm) {
			hideAlert();
			var arrReq = new Array();
			var i=5;
			var l=5;
			var ctcheck = 1;
			var numElements = document.frmZone.elements.length - 1;
			if (!_CF_hasValue(thisForm['eventName'], "TEXT", false)  
			&& !_CF_hasValue(thisForm['courseNumber'], "TEXT", false)) arrReq[arrReq.length] = 'Enter the name of the program or a program/credit identifier.';
			if (!_CF_hasValue(thisForm['eventDate'], "TEXT", false)) arrReq[arrReq.length] = 'Enter the date of the program.';
        	if (!_CF_checkdate(thisForm['eventDate'].value, false)) arrReq[arrReq.length] = 'Enter a valid date of the program.';
			
			//makes sure the at least one credit type is entered	
			while (i<numElements) {
				if (_CF_hasValue(thisForm[i], "TEXT", false)) ctcheck = 0;  
				i++;
			}
			if (ctcheck==1) arrReq[arrReq.length] = 'Enter Credit Information.';

        	while (l<numElements) {
				if (_CF_hasValue(thisForm[l], "TEXT", false) && !_CF_checknumber(thisForm[l].value, false)) arrReq[arrReq.length] = 'Enter valid credit amount for ' + thisForm[l].name.substring(10,thisForm[l].name.length) + '.';  
				l++;
			}

			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				return false;
			}
			return true;
			
		};		
		
		hideAlert = function() {
			var abox = document.getElementById('validateMessage');
				abox.innerHTML = '';
				abox.style.display = 'none';
		};
		showAlert = function(msg) {
			var abox = document.getElementById('validateMessage');
				abox.innerHTML 			= msg;
				abox.className 			= 'alert';
				abox.style.display 	= '';
		};

		$(document).ready(function(){
			mca_setupDatePickerField('eventDate');
		});
		</script>

	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.zoneJS#">

<cfif local.qryGetCreditAuthorities.recordcount GT 1>
	<cfform name="pickCA"  id="pickCA" action="/?#cgi.query_string#" method="post">
	<cfoutput>

	<div class="tsAppHeading">#local.pageName#</div><br/>
	<div class="tsAppBodyText">Complete the information below to record this self-reported credit in your totals.</div>
	<br/>
	<div class="tsAppBodyText">
		<cfselect name="CSALinkID" id="CSALinkID" >
		<cfloop query="local.qryGetCreditAuthorities">
			<option value="#local.qryGetCreditAuthorities.CSALinkID#">#local.qryGetCreditAuthorities.authorityName#</option>
		</cfloop>
		</cfselect>
	</div>
	<br/>
	<button name="btnPickCA" id="btnPickCA" type="submit" class="tsAppBodyButton">Choose Credit Authority</button>

	</cfoutput>
	</cfform>

<cfelse>
	<!---Convert the CreditTypes XML to array for use in form --->
	<cfwddx action="wddx2cfml" input="#local.qryGetCreditAuthorities.wddxCreditTypes#" output="CreditTypes">

	<cfform name="frmZone"  id="frmZone" action="#local.formlink#" method="post" onsubmit="return _validateForm(this);cleanupAddCredit();closeAddCredit();">
	<cfinput type="hidden" name="CSALinkID"  id="CSALinkID" value="#local.qryGetCreditAuthorities.CSALinkID#">

	<cfoutput>
	<div class="tsAppHeading">#local.pageName#</div><br/>
	<div class="tsAppBodyText">Complete the information below to record this self-reported credit in your totals.</div>
	<br/>
	<table cellpadding="3" cellspacing="0" class="tsAppBodyText" border="0">
	<tr>
		<td class="tsAppBodyText"><b>Credit Authority</b></td>
		<td class="tsAppBodyText" style="padding:6px 0;">#local.qryGetCreditAuthorities.authorityName#</td>
	</tr>
	<tr>
		<td class="tsAppBodyText"><b>Name of Program</b></td>
		<td><cfinput type="text" name="eventName"  id="eventName" class="tsAppBodyText" size="55" maxlength="100" value=""></td>
	</tr>
	<tr>
		<td class="tsAppBodyText"><b>Course/Credit Identifier</b></td>
		<td class="tsAppBodyText"><cfinput type="text" name="courseNumber"  id="courseNumber" class="tsAppBodyText" size="30" maxlength="50" value=""> (if available)</td>
	</tr>
	<tr>
		<td class="tsAppBodyText"><b>Date of Program</b></td>
		<td class="tsAppBodyText">
			<cfinput type="text" name="eventDate" id="eventDate" class="tsAppBodyText" size="13">
			<a href="javascript:mca_clearDateRangeField('eventDate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 3px;"></i></a>
		</td>
	</tr>
			
	<tr>
		<td colspan="2"></td>
	</tr>
	<cfif len(local.qryGetCreditAuthorities.creditIDText)>
		<tr>
			<td class="tsAppBodyText"><b>#local.qryGetCreditAuthorities.creditIDText#</b></td>
			<td class="tsAppBodyText"><cfinput type="text" name="IDNumber"  id="IDNumber" class="tsAppBodyText" size="30" maxlength="50" value=""></td>
		</tr>
	<cfelse>
		<cfinput type="hidden" name="IDNumber"  id="IDNumber" value="">
	</cfif>

	<tr valign="top">
		<td class="tsAppBodyText"><b>Credits Earned</b></td>
		<td>
			<table cellpadding="3" cellspacing="0">
			<cfloop index="local.ct" array="#CreditTypes#">
				<tr>
					<td><cfinput type="text" size="3" maxlength="4" class="tsAppBodyText" name="CreditType#local.ct['fieldname']#" id="CreditType#local.ct['fieldname']#" ></td>
					<td class="tsAppBodyText">#local.ct['displayname']#</td>
				</tr>
			</cfloop>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="2"></td>
	</tr>
	<tr>
		<td></td>
		<td class="tsAppBodyText">
			<div id="validateMessage" style="display:none;margin:6px 0;"></div>
			<div style="margin:6px;">
				<button name="btnSaveCredit" id="btnSaveCredit" type="submit" class="tsAppBodyButton">Save Information</button>
			</div>
		</td>
	</tr>
	</table>
		
	</cfoutput>
	</cfform>	
</cfif>

