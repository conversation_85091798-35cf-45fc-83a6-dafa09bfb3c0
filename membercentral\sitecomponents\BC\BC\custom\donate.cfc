<cfcomponent extends="model.customPage.customPage" output="false">
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		variables.fund = arguments.event.getValue('fund','');
		local.formAction = arguments.event.getValue('fa','showLookup');

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Donate" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for stored CC", value="PACCreditCard" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCheck", type="STRING", desc="pay profile code for check", value="PayByCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of confirmation page and email", value="Thank you for your donation!" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed confirmation", value="Thank you!" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="Online Donation To Be Processed" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationContent", type="STRING", desc="Content at top of staff confirmation page and email", value="An online donation has been submitted and needs processing." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="Frequency", type="STRING", desc="Frequency", value="Frequency" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FrequencyOnetime", type="STRING", desc="Frequency One-time", value="One-time" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FrequencyMonthly", type="STRING", desc="Frequency Monthly", value="Monthly" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FrequencyQuarterly", type="STRING", desc="Frequency Quarterly", value="Quarterly" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FrequencyAnnually", type="STRING", desc="Frequency Annually", value="Annually" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FrequencyHelp", type="STRING", desc="Frequency help note", value="recurring gifts will be processed until cancelled" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DonationAmount", type="STRING", desc="Donation amount", value="Donation Amount" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="DonationContact", type="STRING", desc="Donation Contact", value="For information on the various donation levels, please contact Megan Ejack: ************ or <EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentOptions", type="STRING", desc="Payment options", value="Payment Options" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentOptionsCard", type="STRING", desc="Pay with card provided", value="Pay with card provided" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentOptionsInvoice", type="STRING", desc="Send me an invoice", value="Send me an invoice" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DonorComments", type="STRING", desc="Donor comments", value="Donor Comments" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = { name="DonationFormHeaderTitle", type="CONTENTOBJ", desc="Donation form header title", value="Editable Title" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DonationFormHeaderContent", type="CONTENTOBJ", desc="Donation form header content", value="Editable Content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
			
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmDonation',
			formNameDisplay='Donation Form',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));
		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="#variables.formname#");
		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
				break;
			case "processDonationInfo":
				switch (processDonationInfo(rc=arguments.event.getCollection())) {
					case "showPayment":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					case "spam":
						local.returnHTML = showError(errorCode='spam');
						break;
					default:
						local.returnHTML = showError(errorCode='fail');
						break;				
				}
				break;
			case "processPayment":
				switch (processPayment(rc=arguments.event.getCollection())) {
					case "success": 
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");						
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;

			default:
				arguments.event.collectionAppend(variables);
				local.returnHTML = showLookup(rc=arguments.event.getCollection());
				if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<!--- get identified memberid if available to bypass lookup --->
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>

		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##div#variables.formName#loading [class^="icon-"], [class*=" icon-"] {width:auto;}
				.captchaWrap img{display: unset !important;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				
				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processDonationInfo");
							mc_loadDataForForm($('###variables.formName#'),'previous');			        	
						}
					} else {
						step = 1;
					}
					prevStep = step;				    
				}

				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="">
				<cfinput type="hidden" name="fund" id="fund" value="#arguments.rc.fund#">
				
				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>

				</cfform>
			</cfoutput>
		</cfsavecontent>		
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		<cfset local.strData = {}>
		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>

		<cfif not len(trim(variables.fund)) and structKeyExists(arguments.rc, "fund")>
			<cfset variables.fund = arguments.rc.fund>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='ffeaca01-9c22-4a74-8968-95686c5b4cf2', mode="collection", strData=local.strData)>
		
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='50a4a68a-fabd-4852-8dee-5f905f06e6bb', mode="collection", strData=local.strData)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}	
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					if ($('##paymentPlan').val() == '') arrReq[arrReq.length] = 'Enter Donation Amount.';
					if($('input[name="pmtOption"]:checked').val() == undefined)
						arrReq[arrReq.length] = 'Select Payment Option.';
					
					#local.strFieldSetContent2.jsValidation#

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
				
					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function appendHiddenFieldsForMultiSelects(){
					$('select').each(function(){
						var _this= $(this);
						_this.parents('form').append('<input type="hidden" value="" name="selectMultiple'+_this.attr('id')+'" id="selectMultiple'+_this.attr('id')+'" >');	
					});
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				function reloadElements(){
					$('##donorInformation input,##donorInformation select').each(function(){
						var _this = $(this);
						var _function = _this.data('function');
						
						if(typeof _function != "undefined")
							eval(_function+"()");
					});
					appendHiddenFieldsForMultiSelects();
				}
				
				$(function() {
					<cfif structKeyExists(local.strData, "paymentPlan") AND local.strData.paymentPlan neq ''>
						$('##paymentPlan').val('#local.strData.paymentPlan#');
					</cfif>
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
					reloadElements();
					
					$(document).on('change','select',function(){
						var _this = $(this);
						var _relatedElementId = "selectMultiple"+ _this.attr('id');
						var i=0;
						$('##'+_relatedElementId).val('');
						_this.children("option:selected").each(function(){
							i++;
							if(i==1)
								$('##'+_relatedElementId).val('');
							
							if(i<_this.children("option:selected").length){
								$('##'+_relatedElementId).val($('##'+_relatedElementId).val() + $(this).text() + ',');
							}else{
								$('##'+_relatedElementId).val($('##'+_relatedElementId).val() + $(this).text());
							}
						});
					});
					
					$('select').each(function(){
						var _this = $(this);
						var _relatedElementId = "selectMultiple"+ _this.attr('id');
						var i=0;
						$('##'+_relatedElementId).val('');
						_this.children("option:selected").each(function(){
							i++;
							if(i==1)
								$('##'+_relatedElementId).val('');
							
							if(i<_this.children("option:selected").length){
								$('##'+_relatedElementId).val($('##'+_relatedElementId).val() + $(this).text() + ',');
							}else{
								$('##'+_relatedElementId).val($('##'+_relatedElementId).val() + $(this).text());
							}
						});
					});
				});
			</script>
			<style type="text/css">
				###variables.formName# input[type="text"] {min-height:30px;}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div style="clear:both;"></div>
				<div style="width:75%;margin:0 auto;">
					<div class="TitleText">#variables.strPageFields.DonationFormHeaderTitle#</div>
					#variables.strPageFields.DonationFormHeaderContent#
					<div style="clear:both;"></div>
					<br /><br />
					
					<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" 
					<cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
						<input type="hidden" name="fa" id="fa" value="processDonationInfo">
						<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
						<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
						<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
						<input type="hidden" name="contributionType" id="contributionType2" value="Law Office" >
						<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
						
						<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
						
						<div class="tsAppSectionContentContainer">
							<table cellspacing="0" cellpadding="3" border="0" style="width:100%;">
								<tbody>
									<tr valign="top">
										<td class="tsAppBodyText" width="10">&nbsp;</td>
										<td class="tsAppBodyText" width="150">#variables.strPageFields.Frequency#</td>
										<td class="tsAppBodyText">
											<select name="frequency" id="frequency" style="width:185px;">
												<option value="One-time" <cfif (structKeyExists(local.strData, "frequency") and local.strData.frequency eq "One-time")>selected="true"</cfif>>
												#variables.strPageFields.FrequencyOnetime#</option>
												<option value="Monthly" <cfif (structKeyExists(local.strData, "frequency") and local.strData.frequency eq "Monthly")>selected="true"</cfif>>
												#variables.strPageFields.FrequencyMonthly#</option>
												<option value="Quarterly" <cfif (structKeyExists(local.strData, "frequency") and local.strData.frequency eq "Quarterly")>selected="true"</cfif>>
												#variables.strPageFields.FrequencyQuarterly#</option>
												<option value="Annually" <cfif (structKeyExists(local.strData, "frequency") and local.strData.frequency eq "Annually")>selected="true"</cfif>>
												#variables.strPageFields.FrequencyAnnually#</option>
											</select>
											<span class="info">#variables.strPageFields.FrequencyHelp#</span>
										</td>
									</tr>
									<tr valign="top">
										<td class="tsAppBodyText" width="10">*&nbsp;</td>
										<td class="tsAppBodyText" nowrap="" width="150">#variables.strPageFields.DonationAmount#</td>
										<td class="tsAppBodyText">
											<div class="row-fluid">
												<div class="span4" style="padding: 0px;">
													<input type="text"  class="span12" name="paymentPlan" id="paymentPlan" style="width: 100%;">
												</div>
												<div class="span8" style="padding: 0px;">
													<div class="span12 info" style="padding: 0 0 0 10px;line-height: 15px;">
														#variables.strPageFields.DonationContact#
													</div>
												</div>
											</div>
										</td>
									</tr>
									<tr id="pmtOptionRow" valign="top">
										<td class="tsAppBodyText" width="10">*&nbsp;</td>
										<td class="tsAppBodyText">#variables.strPageFields.PaymentOptions#</td>
										<td class="tsAppBodyText">
											<label class="radio inline tsAppBodyText">
												<input type="radio" name="pmtOption" id="pmtOption1" value="card" <cfif structKeyExists(local.strData, "pmtOption") and local.strData.pmtOption eq 'card'>checked="checked"</cfif>>#variables.strPageFields.PaymentOptionsCard# &nbsp;
											</label>
											<label class="radio inline tsAppBodyText">
												<input type="radio" name="pmtOption" id="pmtOption2" value="invoice" <cfif structKeyExists(local.strData, "pmtOption") and local.strData.pmtOption eq 'invoice'>checked="checked"</cfif>> #variables.strPageFields.PaymentOptionsInvoice#
											</label>
										</td>
									</tr>
									<tr valign="top">
										<td class="tsAppBodyText" width="10">&nbsp;</td>
										<td class="tsAppBodyText">#variables.strPageFields.DonorComments#</td>
										<td class="tsAppBodyText">
											<textarea name="donorComments" id="donorComments" rows="5" style="width:310px;"><cfif structKeyExists(local.strData, "donorComments")>#local.strData.donorComments#</cfif></textarea>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div id="donorInformation">
							<div class="tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div><div class="tsAppSectionContentContainer">#local.strFieldSetContent2.fieldSetContent#</div>
						</div>
						<div class="row-fluid fieldsetFormWrapper">
							<div class="span12 tsAppSectionContentContainer captchaWrap">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div> 
						<button name="btnContinue" type="submit" class="btn tsAppBodyButton pull-right" onClick="hideAlert();">Next &gt;&gt;</button>
					</form>
				</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processDonationInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>
		<cfset local.response = ''>
		<cfset local.response = 'showPayment'>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>


		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>
		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset session.captchaEntered = 1>
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		
		
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0)>
		<cfset local.company = ''>
		<cfif structKeyExists(arguments.rc,"m_company")>
			<cfset local.company = arguments.rc.m_company>
		</cfif>
		<cfset local.objSaveMember.setDemo(prefix='', firstName='Firm', middleName='', lastName='Account', suffix='', company=local.company)>
		<cfset local.objSaveMember.setRecordType(recordType='Law Office')>
		<cfset local.objSaveMember.setMemberType(memberType='User')>
		<cfset local.objSaveMember.setCustomField(field="Member Type - Other", value="Law Office")>
		<!--- address/phone --->
		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>
		<cfset local.qryOrgPhoneTypes = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.rc.mc_siteinfo.orgid)>
		<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.rc.mc_siteinfo.orgid)>		
		<cfloop query="local.qryOrgAddressTypes">
			<cfset local.tmpAddressTypeID = local.qryOrgAddressTypes.addressTypeID>
			<cfset local.tmpAddressType = local.qryOrgAddressTypes.addressType>

			<cfset local.strAddrArgs = {}>
			<cfset local.strAddrArgs.type = local.tmpAddressType>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_attn")>
				<cfset local.strAddrArgs.attn = arguments.rc["ma_#local.tmpAddressTypeID#_attn"]>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_address1")>
				<cfset local.strAddrArgs.address1 = left(arguments.rc["ma_#local.tmpAddressTypeID#_address1"],100)>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_address2")>
				<cfset local.strAddrArgs.address2 = left(arguments.rc["ma_#local.tmpAddressTypeID#_address2"],100)>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_address3")>
				<cfset local.strAddrArgs.address3 = left(arguments.rc["ma_#local.tmpAddressTypeID#_address3"],100)>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_city")>
				<cfset local.strAddrArgs.city = left(arguments.rc["ma_#local.tmpAddressTypeID#_city"],75)>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_stateprov")>
				<cfset local.strAddrArgs.stateID = val(arguments.rc["ma_#local.tmpAddressTypeID#_stateprov"])>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_postalCode")>
				<cfset local.strAddrArgs.postalCode = left(arguments.rc["ma_#local.tmpAddressTypeID#_postalCode"],25)>
			</cfif>
			<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_county")>
				<cfset local.strAddrArgs.county = left(arguments.rc["ma_#local.tmpAddressTypeID#_county"],50)>
			</cfif>
			<cfif structCount(local.strAddrArgs) gt 1>
				<cfset local.objSaveMember.setAddress(argumentcollection=local.strAddrArgs)>
			</cfif>

			<cfloop query="local.qryOrgPhoneTypes">
				<cfif isDefined("arguments.rc.mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#")>
					<cfset local.tmpVal = left(arguments.rc["mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#"],40)>
					<cfif len(local.tmpVal)>
						<cfset local.objSaveMember.setPhone(addresstype=local.tmpAddressType, type=local.qryOrgPhoneTypes.phoneType, value=local.tmpVal)>
					</cfif>
				</cfif>
			</cfloop>
		</cfloop>
		<cfloop query="local.qryOrgEmailTypes">
			<cfif isDefined("arguments.rc.me_#local.qryOrgEmailTypes.emailTypeID#_email")>
				<cfset local.objSaveMember.setEmail(type='Email', value=arguments.rc["me_#local.qryOrgEmailTypes.emailTypeID#_email"])>
			</cfif>
		</cfloop>	

		<!--- custom fields --->
		<cfset local.rcCopy = duplicate(arguments.rc)>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=local.rcCopy.mc_siteinfo.orgid).additionalDataXML>
		<cfloop collection="#arguments.rc#" item="local.thisField">
			<cfif left(local.thisField,3) eq "md_" and listLen(local.thisField,'_') eq 2>
				<cfset local.xmlFieldNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnID="#val(GetToken(local.thisField,2,'_'))#"]')>
				<cfset local.tmpval = local.rcCopy[local.thisField]>
				<cfset local.tmpStr = { field=local.xmlFieldNode[1].xmlAttributes.columnName }>
				<cfif listFindNoCase("RADIO,CHECKBOX,SELECT",local.xmlFieldNode[1].xmlAttributes.displayTypeCode)>
					<cfset local.tmpStr.valueID = local.tmpval>
				<cfelse>
					<cfset local.tmpStr.value = local.tmpval>
				</cfif>
				<cfset local.objSaveMember.setCustomField(argumentcollection=local.tmpStr)>
			</cfif>
		</cfloop>

		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
		</cfif>
				
		<cfif arguments.rc.pmtOption eq 'card'>
			<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC ]>
			
			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																						siteID=variables.siteID, 
																						memberID=variables.useMID, 
																						title="Donation Form", 
																						formName=variables.formName, 
																						backStep="processDonationInfo")>
		<cfelseif arguments.rc.pmtOption eq 'invoice'>																				
			<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCheck ]>
			
			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																					siteID=variables.siteID, 
																					memberID=variables.useMID, 
																					title="Donation Form", 
																					formName=variables.formName, 
																					backStep="processDonationInfo")>
		</cfif>
		
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_" or left(local.thisField,14) eq "selectMultiple" or ListFindNoCase("contributionType|frequency|paymentPlan|pmtOption|otherAmt|donorComments",local.thisField,"|")>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Payment for #variables.formNameDisplay#</div>
			<div class="tsAppSectionContentContainer">
				<div class="tsAppBodyText">
						<b>Amount :</b> #arguments.rc["paymentPlan"]#
				</div>
			</div>
			#local.strReturn.paymentHTML#
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		<!--- ---------------------- --->
		<!--- 	 Member-History 	 --->
		<!--- ---------------------- --->

		<cfset local.fundType = ''>
		<cfset local.totalAmount = 0>
		<cfset local.paymentPlanInYears = 0>
		<cfset local.donorComments = ''>
		<cfset local.paymentOption = ''>
		<cfset local.pmtOptionCategory = ''>
		<cfset local.paymentPlan = ''>
		<cfset local.confirmationHTMLForUser = '' >
		<cfset local.confirmationHTMLForStaff = '' >
		
		<cfif structKeyExists(arguments.rc,"donorComments")>
			<cfset local.donorComments = arguments.rc.donorComments>
		</cfif>
		<cfif structKeyExists(arguments.rc,"pmtOption")>
			<cfset local.paymentOption = arguments.rc.pmtOption>
			<cfif local.paymentOption eq 'card'>
				<cfset local.pmtOptionCategory = 'Auto-Pay'>
			<cfelseif local.paymentOption eq 'invoice'>
				<cfset local.pmtOptionCategory = 'Send Invoice'>
			</cfif>
		</cfif>
		<cfif structKeyExists(arguments.rc,"paymentPlan")>
			<cfset local.paymentPlan = arguments.rc.paymentPlan>
		</cfif>
		
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		<cfset local.frequency = ''>
		<cfif structKeyExists(arguments.rc,"frequency")>
			<cfset local.frequency = arguments.rc.frequency>
		</cfif>
		
		<cfset local.description = ''>
		
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
		</cfif>	
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='ffeaca01-9c22-4a74-8968-95686c5b4cf2', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='50a4a68a-fabd-4852-8dee-5f905f06e6bb', mode="collection", strData=local.strData)>

		<cfset local.br = "#chr(13)##chr(10)#">
		<cfset local.description = 'Donor Type : ' & local.contributionType & local.br &'Frequency : ' & local.frequency & local.br &'Payment Options : ' & local.pmtOptionCategory & local.br &'Donor Comments : ' & local.donorComments>
		
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.key">
			<cfif StructKeyExists(arguments.rc,"selectMultiple#local.key#")>
				<cfset local.description = local.description & local.br & local.strFieldSetContent2.strFields[local.key] &' : '& arguments.rc['selectMultiple#local.key#']>
			<cfelse>
				<cfset local.description = local.description & local.br & local.strFieldSetContent2.strFields[local.key] &' : '& arguments.rc['#local.key#']>
			</cfif>
		</cfloop>
		
		<cfset local.totalAmount = val(ReReplace(local.paymentPlan, "[^\d.]", "","ALL"))>
		<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Donation', subName='Submitted')>
		<cfif local.qryHistory.recordcount>
			<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																categoryID=local.qryHistory.categoryID, 
																subCategoryID=local.qryHistory.subCategoryID, 
																description=local.description, 
																enteredByMemberID=variables.useMID, 
																newAccountsOnly=false,
																dollarAmt=local.totalAmount)>
		</cfif>	
		<cfset local.response = 'success'>
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='ffeaca01-9c22-4a74-8968-95686c5b4cf2', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='50a4a68a-fabd-4852-8dee-5f905f06e6bb', mode="confirmation", strData=arguments.rc)> 
		
		<cfset local.memberPayProfileDetail = "">
		<cfif structKeyExists(arguments.rc,"mccf_payMethID") and structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
			<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
		<cfelse>
			<cfset local.memberPayProfileSelected = 0>
		</cfif>
		
		<cfset variables.useMID = arguments.rc.memberid>
		
		<cfif local.memberPayProfileSelected gt 0>
			<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
		</cfif>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
							
				<!--@@specialcontent@@-->
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation Information</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Frequency :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"frequency")>#arguments.rc["frequency"]#</cfif>
					</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Amount :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"paymentPlan")>#arguments.rc["paymentPlan"]#</cfif>
					</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Payment Options :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"pmtOption")>#arguments.rc["pmtOption"]#</cfif>
					</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Donor Comments :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"donorComments")>#arguments.rc["donorComments"]#</cfif>
					</td>
				</tr>
				</table>
				<br/>
				
				#local.strFieldSetContent2.fieldSetContent#
				
				<cfif local.memberPayProfileDetail neq "">
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation - Payment</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<!--- email to member --->

		<cfif structKeyExists(arguments.rc, "me_#variables.orgid#_email")>
			<cfset variables.memberEmail.TO = arguments.rc["me_#variables.orgid#_email"] >
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLForUser = '<div>#variables.strPageFields.ConfirmationContent#</div>' & local.confirmationHTML >

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from},
			emailto=[{ name="", email=variables.memberEmail.to}],
			emailreplyto= variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTMLForUser,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLForStaff = '<div>#variables.strPageFields.StaffConfirmationContent#</div>' & local.confirmationHTML >
		<cfset variables.ORGEmail.Subject = variables.strPageFields.StaffConfirmationSub >
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTMLForStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your donation", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>
		
		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.confirmationHTMLDisplay = '<div>#variables.strPageFields.ConfirmationContent#</div>' & arguments.confirmationHTML >
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your donation.</div>
			<div class="tsAppSectionContentContainer">						
				#local.confirmationHTMLDisplay#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>
