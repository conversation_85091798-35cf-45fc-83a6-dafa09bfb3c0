<cfscript>
	variables.applicationReservedURLParams 	= "issubmitted";
	local.customPage.baseURL				= "/?#getBaseQueryString(false)#";

	local.arrCustomFields = [];
	local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Staff Confirmation Email", value="<EMAIL>,<EMAIL>,<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Footer", type="CONTENTOBJ", desc="Footer Content", value="Thirty four percent (34%) of dues and/or contributions to NYSTLA are not deductible as an ordinary and necessary business expense for this tax year because they are used to support lobbying. Your non-deductible voluntary contribution to Lawyers Political Action Committee is our key to working to protect the legal rights of consumers." }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmJoin',
		formNameDisplay='NYSTLA Membership Application Form',
		orgEmailTo= local.strPageFields.StaffConfirmationEmail,
		memberEmailFrom='<EMAIL>'
	));
	
	local.profile_1._profileCode 	= 'NYCCCIM';
	local.USStates 					= application.objCustomPageUtils.mem_getStatesByCountry('United States');
	local.nameOfHiddenField = "memid" & hash(dateformat(now(),'yyyymmdd'));
	local.colPlaintiffOrDefense = 'Plaintiff or Defense';
	local.colBirthDate = 'Birth Date';
	local.colGraduationDate = 'Law School Grad Date';
	local.colAoP = 'Areas of Practice';
	
	// Member History Variables for tracking join form progress
	local.useHistoryID = 0;
	local.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=local.siteID, parentCode='JoinForm', subName='Started');
	local.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=local.siteID, parentCode='JoinForm', subName='Completed');
	local.historyStartedText = "Member started NYSTLA join form.";
	local.historyCompletedText = "Member completed NYSTLA join form.";
</cfscript>

<cfsetting requesttimeout="400" />

<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID="2E7F7025-C38B-4F6B-8C88-D81BF0FCDF0A")>
<cfset local.hasSub = false>
<cfif event.getValue('msg','') neq 2 and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
	<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
</cfif>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##03608B; border-bottom:1px solid ##03608B; }
			
			.CPSection{ border:1px solid ##086bad; margin-bottom:15px; }
			.CPSectionTitle { font-size:18pt; font-weight:bold; color:##fff; padding:10px; background:##086bad; }
			.CPSectionContent { padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ccc; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
		
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
		
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			
			.tsAppBodyText { color:##000;}
			select.tsAppBodyText{color:##666;}
			
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			
			.TitleText {margin-top:15px;}
			##customPage tr.hidden-phone{display:table-row;}
			.visible-phone{display:none;}
			.nyPractice{width:500px;}
			.ui-multiselect-checkboxes label input{margin-top:3px!important;}
			@media screen and (max-width:768px){

				##wrapper{width:100%;}
				.nyMenuWidth{width:100%;}
				##headerContainer{display:none;}
				.logoHeightSpacer{height:75px;}
				##menu li{background: url(/images/navBG.png) repeat-x;}
				##customPage form td,##mainNav td{display:block;width:100%;}
				##masthead td{display:block;}
				.hidden-phone{display:none!important;}
				##navigation,##menu{padding:0px;}
				.nyNewsBar,.mastHeadSpacer{width:100%;}
				.nyNewsBar{padding-top:5px;padding-left:5px;}
				form input[type=text],form textarea, form select{
					width:95%;
				}
				
				form input[type=text],form select{
					height:25px;
				}
				form select[multiple="multiple"]{
					height:auto;
				}
				form input[name^=date_]{width:85%;}
				.visible-phone{display:block; padding-top:5px;}
				.nyPractice{width:100%;}
				.CPSection table{width:100%;}
				##divRegRates td{display:table-cell!important;}
				##divRegRates .questionNumber{display:none!important;}
				##divRegRates tr td{width:auto;}
				##practiceAreasWrapper button.ui-multiselect {width:220px!important;}
				##practiceAreasWrapper div.ui-multiselect-menu {width:214px!important;}
				div##CCInfo td{display:table-cell!important;}
				##footer{height: 100%;}
			}
			
		</style>

		<script>
		if(navigator.userAgent.match(/Android/i)
			|| navigator.userAgent.match(/webOS/i)
			|| navigator.userAgent.match(/iPhone/i)
			|| navigator.userAgent.match(/iPad/i)
			|| navigator.userAgent.match(/iPod/i)
			|| navigator.userAgent.match(/BlackBerry/i)
			|| navigator.userAgent.match(/Windows Phone/i)) {
				create_responsive_viewport();
		}
		function create_responsive_viewport() {
			var ele = document.createElement("meta");
			ele.name = "viewport";
			if(window.outerWidth < 768) {
				ele.content = "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no";
			}
			
			document.head.appendChild(ele);
		}
		$(window).resize(function(){
			create_responsive_viewport();
		});
		$(document).ready(function(){
			$("##practiceAreas").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400
			});
		});
		</script>
	</cfsavecontent>
	<cfhtmlhead text="#local.pageJS##local.pageCSS#">
	
	<div id="customPage" class="container-fluid">
		<div class="TitleText PB"><strong>#local.formNameDisplay#</strong></div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			
			<cfcase value="0">
				
				<cfif event.getValue('msg',0) EQ "2">		
					
					<div class="bodyText" >
						It looks like you might already be a member! Thank you and please contact NYSTLA at (212) 349-5890 or <a href="mailto:<EMAIL>"><EMAIL></a> for information about your membership or renewing.
					</div>
				<cfelse>
					
					<cfif local.hasSub eq true>
						<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no" />
					</cfif>
						
					<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=local.orgID)>
					<cfif local.qryOrgMemberFields.usePrefixList is 1>
						<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.orgID)>
					</cfif>
					<cfset local.qryPlaintiffOrDefense = application.objCustomPageUtils.getCustomFieldData(orgID=local.orgID,columnName='#local.colPlaintiffOrDefense#')>
					<cfset local.qryAoP = application.objCustomPageUtils.getCustomFieldData(orgID=local.orgID,columnName='#local.colAoP#')>
					<cfset local.qryStates = application.objCommon.getStates()>
				
					<cfsavecontent variable="local.js">
						<style type="text/css">
							##date_bar, ##date_birth, ##date_graduation { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
						</style>
											
						<script type="text/javascript" src="/javascript/global.js"></script>
						
						<script type="text/javascript">
							function _FB_hasValue(obj, obj_type){
								if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
								else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
								else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
								else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
								else{ return true; }
							}
							
							function _FB_validateForm()
							{
								var theForm = document.forms["#local.formName#"];
								
								
								var proType = "";
								var proRate = "";
								var optDCExclude = "";
								
								for (var i=0; i < theForm.proRate.length; i++) {
									if (theForm.proRate[i].checked) {
										proRate = theForm.proRate[i].value;
									}
								}
								
								var arrReq = new Array();
								
								if (!_FB_hasValue(theForm['firstname'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your First Name.';
								if (!_FB_hasValue(theForm['lastname'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Last Name.';
								if (!_FB_hasValue(theForm['maddress'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Mailing Address.';
								if (!_FB_hasValue(theForm['mcity'], 'TEXT')) arrReq[arrReq.length] 			= 'Please provide your Mailing City.';
								if (!_FB_hasValue(theForm['mstateID'], 'SELECT')) arrReq[arrReq.length] 	= 'Please provide your Mailing State.';
								if (!_FB_hasValue(theForm['mzip'], 'TEXT')) arrReq[arrReq.length] 			= 'Please provide your Mailing Zip Code.';
								if (!_FB_hasValue(theForm['mphone'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Phone number.';	
								if (!_FB_hasValue(theForm['email'], 'TEXT')) {
									arrReq[arrReq.length] = 'Please provide a valid Email address.';
								} else {
									var urlRegEx = new RegExp("#application.regEx.email#", "gi");
									if(!(urlRegEx.test(theForm['email'].value))) 
										arrReq[arrReq.length] = "Please provide a valid Email address.";
								}
								if (!_FB_hasValue(theForm['proRate'], 'RADIO')) arrReq[arrReq.length] 		= 'Please select a Level of Membership.';
								if (!_FB_hasValue(theForm['date_birth'], 'TEXT')) arrReq[arrReq.length] 	= 'Please enter a valid Birth Date.';						
								
								if (proRate == "FYM" || proRate == "2-5Yrs" || proRate == "5YP" || proRate == "SM" ){
									if (!_FB_hasValue(theForm['PlaintiffOrDefense'], 'SELECT')) arrReq[arrReq.length]	= 'Please select Plaintiff or Defense.';
									if (!_FB_hasValue(theForm['barNumber'], 'TEXT')) arrReq[arrReq.length] 	= 'Please provide your State Bar Number.';
									if (!_FB_hasValue(theForm['date_bar'], 'TEXT')) arrReq[arrReq.length] 	= 'Please enter a valid Bar Date.';
									
								}
								if (proRate == "LS"){
									if (!_FB_hasValue(theForm['date_graduation'], 'TEXT')) arrReq[arrReq.length] 	= 'Please enter an Expected Graduation Date.';
								}
								
								if (_FB_hasValue(theForm['legislativeAssessment'], 'CHECKBOX') && $('select##typeOfFunds').val() == "")	arrReq[arrReq.length] 		= 'Please indicate the type of funds.';
								
								if (arrReq.length > 0) {
									var msg = 'The following fields are required:\n\n';
									for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
									alert(msg);
									return false;
								}
								
								return true;
								
							}
							
							function getSelectedRadio(buttonGroup) {
								if (buttonGroup[0]) {
									for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
								} else { if (buttonGroup.checked) return 0; }
								return -1;
							}
	
							function toggleTOF() {
								var elem = document.getElementById('legislativeAssessment');
								if (elem.length == undefined && elem.checked) {
									document.getElementById('divTypeOfFunds').style.display	= '';
								} else {
									document.getElementById('divTypeOfFunds').style.display	= 'none';
								}
							}
				
							$(document).ready(function(){
								mca_setupDatePickerField('date_bar');
								mca_setupDatePickerField('date_birth');
								mca_setupDatePickerField('date_graduation');
							});				
						</script>
					</cfsavecontent>
				<cfhtmlhead text="#local.js#">
				
						<div class="form">				
							<div class="r i frmText">*Denotes required field</div>
							<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();" class="form-horizontal">
								<input type="hidden" name="isSubmitted" value="1" />
								<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="" />
								<div class="page">					
									<div class="CPSection">
										<div class="CPSectionTitle BB">Member Information</div>
										<div class="tsAppBodyText subCPSectionArea1"></div>
										<div class="tsAppBodyText frmRow1 frmText">	
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top" class="hidden-phone">
												<td class="tsAppBodyText questionNumber"></td>
												<cfif local.qryOrgMemberFields.hasPrefix is 1>
												<td class="tsAppBodyText questionText" width="65">Prefix:</td>
												</cfif>
												<td class="tsAppBodyText questionText" width="150">* First Name:</td>
												<cfif local.qryOrgMemberFields.hasMiddleName is 1>
												<td class="tsAppBodyText questionText" width="150">Middle Name:</td>
												</cfif>
												<td class="tsAppBodyText questionText" width="150">* Last Name:</td>
												<cfif local.qryOrgMemberFields.hasSuffix is 1>
												<td class="tsAppBodyText questionText" >Suffix:</td>
												</cfif>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
												<cfif local.qryOrgMemberFields.hasPrefix is 1>
													<div class="tsAppBodyText questionText visible-phone" >Prefix:</div>
													<cfif local.qryOrgMemberFields.usePrefixList is 1>
														<cfselect class="tsAppBodyText" id="prefix" name="prefix" query="local.qryOrgPrefixes" value="prefix" display="prefix" selected="" queryPosition="below">
														<option value=""></option>
														</cfselect>
													<cfelse>
														<cfinput value="" class="tsAppBodyText largeBox" name="prefix"  id="prefix" type="text" size="5" />
													</cfif>
												</cfif>
												</td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* First Name:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="firstname"  id="firstname" type="text" /></td>
												<cfif local.qryOrgMemberFields.hasMiddleName is 1>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >Middle Name:</div>
														<cfinput value="" class="tsAppBodyText largeBox" name="middlename"  id="middlename" type="text" /></td>
												</cfif>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* Last Name:</div>
												<cfinput value="" class="tsAppBodyText largeBox" name="lastname"  id="lastname" type="text" /></td>
												<cfif local.qryOrgMemberFields.hasSuffix is 1>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >Suffix:</div>
														<cfinput value="" class="tsAppBodyText largeBox" name="suffix"  id="suffix" type="text" size="5" /></td>
												</cfif>
											</tr>
											<tr><td coslpan="5">&nbsp;</tr>
										</table>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" colspan="4">Firm/Business Name:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="firm"  id="firm" type="text"  /></td>
											</tr>
											<tr><td coslpan="5">&nbsp;</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" colspan="4">* Mailing Address:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="maddress"  id="maddress" type="text" size="44" message="" /></td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="maddress2"  id="maddress2" type="text" size="44" message="" /></td>
											</tr>
											<tr valign="top" class="hidden-phone">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" >* City:</td>
												<td class="tsAppBodyText questionText" >* State:</td>
												<td class="tsAppBodyText questionText" >* Zip:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* City:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="mcity"  id="mcity" type="text" /></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* State:</div>
													<cfselect name="mstateID"  id="mstateID" class="tsAppBodyText">
													<option value=""></option>
														<cfset local.currentCountryID = 0>
														<cfloop query="local.qryStates">
															<cfif local.qryStates.countryID neq local.currentCountryID>
																<cfset local.currentCountryID = local.qryStates.countryID>
																<optgroup label="#local.qryStates.country#">
															</cfif>
															<cfoutput>
																<option value="#local.qryStates.stateID#" <cfif local.qryStates.stateCode EQ 'NY'>selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
															</cfoutput>
															<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
																</optgroup>
															</cfif>
														</cfloop>
													</cfselect>
												</td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* Zip:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="mzip"  id="mzip" type="text" /></td>
											</tr>
											<tr valign="top" class="hidden-phone">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" >* Phone:</td>
												<td class="tsAppBodyText questionText" >Fax:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* Phone:</div>
													<cfinput value="" validate="telephone" message = "Enter telephone number, formatted xxx-xxx-xxxx (e.g. ************)" class="tsAppBodyText largeBox" name="mphone"  id="mphone" type="text" /></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >Fax:</div>
													<cfinput value="" validate="telephone" message = "Enter telephone number, formatted xxx-xxx-xxxx (e.g. ************)" class="tsAppBodyText largeBox" name="mfax"  id="mfax" type="text" /></td>
											</tr>
											<tr valign="top" class="hidden-phone">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" >* Email:</td>
												<td class="tsAppBodyText questionText" >Website:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >* Email:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="email"  id="email" type="text" /></td>
												<td class="tsAppBodyText optionsInline" colspan="2">
													<div class="tsAppBodyText questionText visible-phone" >Website:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="website"  id="website" type="text" size="40" validate="regular_expression" pattern="#application.regEx.url#" message="Please enter a valid website address." /></td>
											</tr>
											
											<tr><td coslpan="5">&nbsp;</tr>
											<tr><td coslpan="5">&nbsp;</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" colspan="4">Home Address:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="haddress"  id="haddress" type="text" size="44" /></td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="haddress2"  id="haddress2" type="text" size="44" /></td>
											</tr>
											<tr valign="top" class="hidden-phone">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" >City:</td>
												<td class="tsAppBodyText questionText" >State:</td>
												<td class="tsAppBodyText questionText" >Zip:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >City:</div>
														<cfinput value="" class="tsAppBodyText largeBox" name="hcity"  id="hcity" type="text" /></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >State:</div>
													<cfselect name="hstateID"  id="hstateID" class="tsAppBodyText">
													<option value=""></option>
														<cfset local.currentCountryID = 0>
														<cfloop query="local.qryStates">
															<cfif local.qryStates.countryID neq local.currentCountryID>
																<cfset local.currentCountryID = local.qryStates.countryID>
																<optgroup label="#local.qryStates.country#">
															</cfif>
															<cfoutput>
																<option value="#local.qryStates.stateID#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
															</cfoutput>
															<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
																</optgroup>
															</cfif>
														</cfloop>
													</cfselect>
												</td>
												<td class="tsAppBodyText optionsInline"><div class="tsAppBodyText questionText visible-phone" >Zip:</div>
												<cfinput value="" class="tsAppBodyText largeBox" name="hzip"  id="hzip" type="text" /></td>
											</tr>
											<tr valign="top" class="hidden-phone">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" >Home Phone:</td>
												<td class="tsAppBodyText questionText" >Mobile Phone:</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >Home Phone:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="hphone"  id="hphone" type="text" />
												</td>
												<td class="tsAppBodyText optionsInline">
													<div class="tsAppBodyText questionText visible-phone" >Mobile Phone:</div>
													<cfinput value="" class="tsAppBodyText largeBox" name="hcell"  id="hcell" type="text" />
												</td>
											</tr>
											<tr><td coslpan="5">&nbsp;</tr>
										</table>
										</div>
									</div>
									<br />
									<div class="CPSection">
										<div class="CPSectionTitle BB">Demograpic Information</div>
										<div class="tsAppBodyText subCPSectionArea1"></div>
										<div class="tsAppBodyText frmRow1 frmText">
										<table cellspacing="0" cellpadding="2" border="0" style="margin-top:7px;"> 
											<tr valign="top">
												<td class="tsAppBodyText questionText">* Plaintiff or Defense</td>
												<td class="tsAppBodyText questionText">
													<cfselect name="PlaintiffOrDefense"  id="PlaintiffOrDefense" required="true" message="Please select Plaintiff or Defense" class="tsAppBodyText">
														<option value=""></option>
														<cfloop query="local.qryPlaintiffOrDefense">
															<cfoutput>
																<option value="#local.qryPlaintiffOrDefense.valueID#">#local.qryPlaintiffOrDefense.columnValueString#</option>
															</cfoutput>
														</cfloop>
													</cfselect>
												</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionText">* Bar Number:</td>
												<td class="tsAppBodyText optionsInline">
													<cfinput value="" class="tsAppBodyText largeBox" name="barNumber"  id="barNumber" type="text">
												</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionText">* Bar Date:</td>
												<td class="tsAppBodyText">
													<cfinput value="" class="tsAppBodyText largeBox" name="date_bar" id="date_bar" type="text" validate="date"  />
													<a href="javascript:mca_clearDateRangeField('date_bar');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a>															
												</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionText">* Birth Date:</td>
												<td class="tsAppBodyText">
													<cfinput value="" class="tsAppBodyText largeBox" name="date_birth" id="date_birth" type="text" validate="date"  />	
													<a href="javascript:mca_clearDateRangeField('date_birth');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a>								
												</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionText">* Expected Graduation Date (Law Students Only):</td>
												<td class="tsAppBodyText">
													<cfinput value="" class="tsAppBodyText largeBox" name="date_graduation" id="date_graduation" type="text" validate="date" />		
													<a href="javascript:mca_clearDateRangeField('date_graduation');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a>							
												</td>
											</tr>
											
										</table>
										</div>
									</div>
									<br/>
									<br/>
									<div class="CPSection">
										<div class="CPSectionTitle BB">Areas of Practice</div>
										<div class="tsAppBodyText subCPSectionArea1">You may select multiple areas of practice by pressing the Ctrl key.</div>
										<div class="tsAppBodyText frmRow1 frmText">								
	
										<br>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" id="practiceAreasWrapper">
													<cfselect name="practiceAreas"  id="practiceAreas" class="tsAppBodyText nyPractice" multiple="true" size="10" >
														<cfloop query="local.qryAoP">
															<cfoutput>
																<option value="#local.qryAoP.valueID#">#local.qryAoP.columnValueString#</option>
															</cfoutput>
														</cfloop>
													</cfselect>
												</td>
											</tr>
											<tr>
												<td colspan="3">&nbsp;&nbsp;</td>
											</tr>
										</table>
									</div>
								</div>
									<br/>
									<br/>
									<div id="divRates" class="CPSection">
										<div class="CPSectionTitle BB">Membership</div>
										<div class="tsAppBodyText subCPSectionArea1"></div>
										<div id="divRegRates" >
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4">
													<b>Regular Member Fees *</b>
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="FYM"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$50
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													New Admittee <span class="info">(Admitted less than 1 year)</span>
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="2-5Yrs"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$225
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Practicing 1-5 Years
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="5YP"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$395
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Practicing 5+ Years
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="SM"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$790
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Sustaining
													<span class="info">(Supports Law Student and New Admittee memberships)</span>
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="GE"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$200
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Government Employee
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="OUT"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$200
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Out-of-State Attorney
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="RET"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$50
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Retiree
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="PL"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$125
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Paralegal
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="JM"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													Free
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Judicial Member
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="LS"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													Free
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Law Student <span class="info">(3L Only)</span>
												</td>
											</tr>
											
											
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4">
													<b>Justice Campaign Assessment *</b>
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" valign="top">
													<cfinput type="checkbox" name="legislativeAssessment" id="legislativeAssessment" value="1" checked="true" onclick="javascript:toggleTOF();"> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right" valign="top">
													$75
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline" valign="top">
													Non-Deductible Voluntary Contribution to LawPAC
													<div id="divTypeOfFunds" style="display:block;">
														<br/>
														Indicate Type of Funds: 		
														<select name="typeOfFunds" id="typeOfFunds">
															<option value="">-- Select Type of Funds --</option>
															<option value="Individual/Personal - Non Corporate Funds">Individual/Personal - Non Corporate Funds</option>
															<option value="Individual - Sole Proprietor Funds">Individual - Sole Proprietor Funds</option>
															<option value="Partnership Funds (LLP)">Partnership Funds (LLP)</option>
															<option value="Corporate Funds (PC)">Corporate Funds (PC)</option>
															<option value="Incorporated Funds (Inc)">Incorporated Funds (Inc)</option>
															<option value="Limited Liability Corp Funds (LLC)">Limited Liability Corp Funds (LLC)</option>
															<option value="Professional Limited Liability Corp Funds (PLLC)">Professional Limited Liability Corp Funds (PLLC)</option>
														</select>
														<br/><br/>
	
													</div>
												</td>
											</tr>
											
										</table>
										</div>
									</div>
				
									<br />
									<div class="tsAppBodyText" style="padding-left:15px; padding-right:15px;">
										#local.strPageFields.Footer#
									</div>
									<br />
													
									<div id="formButtons">
										<div style="padding:10px;">
											<div align="center" class="frmButtons">
												<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp;
											</div>
										</div>
									</div>
								</div>
								<cfinclude template="/model/cfformprotect/cffp.cfm" />
							</cfform>
						</div>
				</cfif>
			</cfcase>
			
			<cfcase value="1">
			
				<cfif application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
					<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no">
				</cfif>
			
				<!--- CHECK FOR SPAM SUBMISSION: -------------------------------------------------------------------------------- --->
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>

				<cfset local.timeStamp 				= now() />

				<!--- get org structure from MC --->
				<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=local.orgID)>
				
				<cfset arguments.event.setValue('memberid', 0)>
				<cfset local.memberName = trim(arguments.event.getValue('prefix') & ' ' & arguments.event.getValue('firstname'))>
				<cfset local.memberName = trim(local.memberName & ' ' & arguments.event.getValue('middlename'))>
				<cfset local.memberName = trim(local.memberName & ' ' & arguments.event.getValue('lastname') & ' ' & arguments.event.getValue('suffix'))>
				
				<!--- -------------------- --->
				<!--- UPDATE MEMBER RECORD --->
				<!--- -------------------- --->
				
				<cfset local.recordUpdated = false>
				<cftry>
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0)> 
					<cfset local.objSaveMember.setDemo(prefix=arguments.event.getTrimValue('prefix',''),
														firstName=arguments.event.getTrimValue('firstname',''),
														middleName=arguments.event.getTrimValue('middlename',''),
														lastName=arguments.event.getTrimValue('lastname',''),
														suffix=arguments.event.getTrimValue('suffix',''),
														company=arguments.event.getTrimValue('firm',''))> 
					
					<cfset local.objSaveMember.setMemberType(memberType='User')>
					<cfset local.objSaveMember.setMemberStatus(memberStatus='Active')>
					<cfset local.objSaveMember.setRecordType(recordType='Individual')>
					
					<cfset local.objSaveMember.setAddress(type='Mailing', address1=arguments.event.getTrimValue('maddress',''),address2=arguments.event.getTrimValue('maddress2',''), city=arguments.event.getTrimValue('mcity',''), stateID=arguments.event.getTrimValue('mstateID',0), postalCode=arguments.event.getTrimValue('mzip',''))> 
					
					<cfset local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getTrimValue('haddress',''),address2=arguments.event.getTrimValue('haddress2',''), city=arguments.event.getTrimValue('hcity',''), stateID=arguments.event.getTrimValue('hstateID',0), postalCode=arguments.event.getTrimValue('hzip',''))> 
					
					<cfset local.objSaveMember.setPhone(addresstype='Mailing', type='Business', value=arguments.event.getTrimValue('mphone',''))>	
					<cfset local.objSaveMember.setPhone(addresstype='Mailing', type='Fax', value=arguments.event.getTrimValue('mfax',''))>
					<cfset local.objSaveMember.setPhone(addresstype='Mailing', type='Home', value=arguments.event.getTrimValue('hphone',''))>
					<cfset local.objSaveMember.setPhone(addresstype='Mailing', type='Mobile', value=arguments.event.getTrimValue('hcell',''))>					
					
					<cfset local.objSaveMember.setEmail(type='Email', value=arguments.event.getTrimValue('email',''))>
					
					<cfset local.objSaveMember.setWebsite(type='Website', value=arguments.event.getTrimValue('website',''))>
					
					 <cfif len(arguments.event.getValue('date_birth')) gt 0>						
						 <cfset local.objSaveMember.setCustomField(field='Birth Date', value=event.getTrimValue('date_birth',''))>								
					</cfif>

					<cfif len(arguments.event.getValue('date_graduation')) gt 0>								
						 <cfset local.objSaveMember.setCustomField(field='Law School Grad Date', value=event.getTrimValue('date_graduation',''))>								
					</cfif>
					
					<cfif len(arguments.event.getValue('PlaintiffOrDefense')) gt 0>			
						 <cfset local.objSaveMember.setCustomField(field='Plaintiff or Defense', valueID=event.getTrimValue('PlaintiffOrDefense',0))>
					</cfif> 
							
					<cfset local.objSaveMember.setProLicense(name='New York', status='Active', license=event.getTrimValue('barNumber',''), date=event.getTrimValue('date_bar',0))> 
				
					 <cfif len(arguments.event.getValue('practiceAreas','')) gt 0>			
						<cfset local.objSaveMember.setCustomField(field='Areas of Practice', valueID=event.getTrimValue('practiceAreas',''))>								
					</cfif>
					
					 <cfset local.strResult = local.objSaveMember.saveData()>
					<cfif local.strResult.success>
						<cfset local.recordUpdated = true>
						<!--- Add history entry for form started --->
						<cfif val(local.strResult.memberID) gt 0>
							<cfset local.useHistoryID = application.objCustomPageUtils.mh_addHistory(
								memberID=val(local.strResult.memberID),
								categoryID=local.qryHistoryStarted.categoryID,
								subCategoryID=local.qryHistoryStarted.subCategoryID,
								description=local.historyStartedText,
								enteredByMemberID=val(local.strResult.memberID),
								newAccountsOnly=false
							)>
							<cfset session.useHistoryID = local.useHistoryID>
							<cfset session.memberID = local.strResult.memberID>
						</cfif>
					<cfelse>				
						<cfthrow message="Unable to save member.">
					</cfif>		
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						<cfset local.recordUpdated = false>
					</cfcatch>
				</cftry>	
				<cfif NOT local.recordUpdated>
					<cfoutput>
						<div class="tsAppBodyText">
							<b>An error occurred while setting up your membership.  We are unable to process your membership online at this time.</b>
							<br>
							Please contact customer support for assistance.
							<br><br>
							We apologize for the inconvenience. 
						</div>
					</cfoutput>
				<cfelse>

					 <cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberLookups">
						set nocount on
						
						declare @orgID int, @mStateName varchar(50), @hStateName varchar(50), @hcName varchar(max), @lsName varchar(max), @dcName varchar(max), @wcPCtName varchar(max), @aopName varchar(max), @bnName varchar(max)
	
						select @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						
						select @mStateName=Name
						from dbo.ams_states 
						where stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(event.getValue('mstateID'))#">
	
						select @hStateName=Name
						from dbo.ams_states 
						where stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(event.getValue('hstateID'))#">
						
					select @hcName = ''
					
					<cfif len(arguments.event.getValue('PlaintiffOrDefense')) gt 0>
						select @lsName = columnValueString
						from dbo.ams_memberDataColumnValues
						where valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('PlaintiffOrDefense')#">
					<cfelse>
						select @lsName = ''
					</cfif>					
					
					select @wcPctName = ''
				
					select @dcName = ''
					
					<cfif len(arguments.event.getValue('practiceAreas','')) gt 0>
						SELECT @aopName = COALESCE(@aopName + ',', '') + mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = @orgID
							and columnName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.colAoP#">
							and mdcv.valueID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.event.getValue('practiceAreas')#">)
						order by mdcv.columnValueString
					<cfelse>
						select @aopName = ''
					</cfif>	
					
						select @bnName = ''
					
						select @mStateName as mStateName, @hStateName as hStateName, @hcName as hcName, @lsName as lsName, @wcPctName as wcPctName, @dcName as dcName, @aopName as aopName, @bnName as bnName
						
						set nocount off
					</cfquery>
	
					<cfsavecontent variable="local.invoice">
						#local.pageCSS#
						<!-- @msg@ -->
						<!-- @profile_1.ccResponse@ -->
						<p>#local.formNameDisplay# submitted on #dateformat(local.timeStamp,"dddd, m/d/yyyy")# #timeformat(local.timeStamp,"h:mm tt")#.</p>

						<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
							<tr class="msgHeader"><td colspan="2" class="b">Member Information</td></tr>
							<tr><td valign="top" class="docText">Name:</td><td valign="top" class="docText">#local.memberName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Firm / Business Name:</td><td valign="top" class="docText">#trim(event.getValue('firm',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Mailing Address:</td><td valign="top" class="docText">#trim(event.getValue('maddress',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Mailing Address2:</td><td valign="top" class="docText">#trim(event.getValue('maddress2',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">City:</td><td valign="top" class="docText">#trim(event.getValue('mcity',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">State:</td><td valign="top" class="docText">#local.qryMemberLookups.mStateName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Zip:</td><td valign="top" class="docText">#trim(event.getValue('mzip',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Phone:</td><td valign="top" class="docText">#trim(event.getValue('mphone',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Fax:</td><td valign="top" class="docText">#trim(event.getValue('mfax',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Email:</td><td valign="top" class="docText">#trim(event.getValue('email',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Website:</td><td valign="top" class="docText">#trim(event.getValue('website',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Home Address:</td><td valign="top" class="docText">#trim(event.getValue('haddress',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Home Address2:</td><td valign="top" class="docText">#trim(event.getValue('haddress2',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">City:</td><td valign="top" class="docText">#trim(event.getValue('hcity',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">State:</td><td valign="top" class="docText">#local.qryMemberLookups.hStateName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Zip:</td><td valign="top" class="docText">#trim(event.getValue('hzip',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Phone:</td><td valign="top" class="docText">#trim(event.getValue('hphone',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Mobile:</td><td valign="top" class="docText">#trim(event.getValue('hcell',''))#&nbsp;</td></tr>
							<tr><td colspan="2" height="25"></td></tr>
							<tr class="msgHeader"><td colspan="2" class="docText"><strong>Optional Information</strong></td></tr>
							<tr><td valign="top" class="docText">Plaintiff or Defense:</td><td valign="top" class="docText">#local.qryMemberLookups.lsName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Bar Number:</td><td valign="top" class="docText">#trim(event.getValue('barNumber',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Bar Date:</td><td valign="top" class="docText">#event.getValue('date_bar','')#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Birth Date:</td><td valign="top" class="docText">#event.getValue('date_birth')#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Expected Graduation Date:</td><td valign="top" class="docText">#event.getValue('date_graduation','')#&nbsp;</td></tr>
							<tr>
								<td valign="top" class="docText">Areas of Practice:</td>
								<td valign="top" class="docText">
									<cfloop list="#local.qryMemberLookups.aopName#" index="local.thisAOP">
										#local.thisAOP#<br>
									</cfloop>&nbsp;
								</td>
							</tr>
							<tr><td colspan="2" height="25"></td></tr>
							<tr class="msgHeader"><td colspan="2" class="docText"><strong>Member Fees</strong></td></tr>
							<tr>
								<td valign="top" class="docText">Rate:</td>
								<td valign="top" class="docText">
									<cfswitch expression="#event.getValue('proRate')#">
										<cfcase value="FYM">
											$50	New Admittee (Admitted less than 1 year)
										</cfcase>
										<cfcase value="2-5Yrs">
											$225	Practicing 1-5 Years
										</cfcase>
										<cfcase value="5YP">
											$395	Practicing 5+ Years
										</cfcase>
										<cfcase value="SM">
											$790	Sustaining 	<i>(Supports Law Student and New Admittee memberships)</i>
										</cfcase>
										<cfcase value="OUT">
											$200 Out-of-State Attorney
										</cfcase>
										<cfcase value="PL">
											$125 Paralegal
										</cfcase>
										<cfcase value="LS">
											Free Law Student
										</cfcase>
										<cfcase value="GE">
											$200 Government Employee
										</cfcase>
										<cfcase value="RET">
											$50 Retiree
										</cfcase>
										<cfcase value="JM">
											Free Judicial Member
										</cfcase>
									</cfswitch>&nbsp;
								</td>
							</tr>
							
						<cfif #event.getValue('legislativeAssessment', 0)# eq 1>
							<tr>
								<td valign="top" class="docText">Justice Campaign Assessment:</td>
								<td valign="top" class="docText">
									$75	Non-Deductible Voluntary Contribution to LawPAC
								</td>
							</tr>
							<tr>
								<td valign="top" class="docText">Type of Funds:</td>
								<td valign="top" class="docText">
									#event.getValue('typeOfFunds', '')#
								</td>
							</tr>
						</cfif>
							<tr><td colspan="2" height="25">
								#local.strPageFields.Footer#								
							</td></tr>
						
						</table>
					</cfsavecontent>
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>Applications are reviewed and vetted before membership is approved.  Please allow three business days for this vetting process.  You will be contacted by email with the results of the approval process.</p><br><br>
							<hr />
							#local.invoice#		
						</cfoutput>
					</cfsavecontent>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from },
						emailto=[{ name="", email=local.memberEmail.to }],
						emailreplyto=local.ORGEmail.to,
						emailsubject=local.memberEmail.SUBJECT,
						emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=val(local.strResult.memberID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>
					
					<cfset local.emailSentToUser = local.responseStruct.success>
					<cfset session.invoice = local.invoice>
					<cfset session.emailSentToUser = local.emailSentToUser ? true : false>
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<cfif NOT local.emailSentToUser>
								#arguments.event.getValue('firstname')# was not sent email confirmation due to bad Data.<br />
								Please contact them and let them know.
								<hr />
							</cfif>
							#local.invoice#
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
						local.toEmailArr = listToArray(local.ORGEmail.to,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}

						if (arrayLen(local.arrEmailTo)) {
							local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="", email=local.ORGEmail.from},
								emailto=local.arrEmailTo,
								emailreplyto=local.ORGEmail.from,
								emailsubject=local.ORGEmail.SUBJECT,
								emailtitle=local.formNameDisplay,
								emailhtmlcontent=local.mailContent,
								siteID=local.siteID,
								memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
								messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
								sendingSiteResourceID=this.siteResourceID
							);
						}
					</cfscript>

					<!--- GATEWAY INFORMATION: --->
					<cfset local.formName = "frmNYCC">
					<cfset local._paymentProfileCode = '#local.profile_1._profileCode#'>
					<cfset local.strPaymentForm 		=	application.objPayments.showGatewayInputForm(
																						siteid=local.siteID,
																						profilecode=local._paymentProfileCode,
																						pmid = local.strResult.memberID,
																						usePopupDIVName='paymentTable'
																						)>

		
					<cfoutput>
					<script type="text/javascript">
						
						function getMethodOfPayment() {
							var btnGrp = document.forms['#local.formName#'].payMeth;
							var i = getSelectedRadio(btnGrp);
							if (i == -1) return "";
							else {
								if (btnGrp[i]) return btnGrp[i].value;
								else return btnGrp.value;
							}
						}
						
						function _validate() {
							var thisForm = document.forms["#local.formName#"];
							var arrReq = new Array();
							thisForm.btnSubmit.disabled=true;
							
							#local.strPaymentForm.jsvalidation#
							
							if (arrReq.length > 0) {
								var msg = 'The following fields are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								thisForm.btnSubmit.disabled=false;
								return false;
							}
							return true;
						}
					</script>
					<script>
						function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
					</script>
					<cfif len(local.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headCode)#">
					</cfif>
		
					<div id="paymentTable">
						<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
						<div class="form">
							<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?pg=memberApp" onSubmit="return _validate();">
								<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
								<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="" />
								<div>
									
									<div id="CCInfo" class="">
										<div class="tsAppLegendTitle sectionTitle">Credit Card Information</div>
										<div class="tsAppBodyText optionsInline">
											<cfif len(local.strPaymentForm.inputForm)>
												<div>#local.strPaymentForm.inputForm#</div>
											</cfif>
											<br />
										</div>
									</div>
		
									<div class="PB"><button type="submit" class="tsAppBodyText formButton" name="btnSubmit">Continue</button></div>
		
								</div>
							</cfform>
						</div>
					</div>
					</cfoutput>
				</cfif>
			</cfcase>
		
			<cfcase value="2">
				<!--- Update history to completed and store PDF --->
				<cfset application.objCustomPageUtils.mh_updateHistory(
					memberID=val(session.memberID),
					historyID=val(session.useHistoryID),
					subCategoryID=local.qryHistoryCompleted.subCategoryID,
					description=local.historyCompletedText,
					newAccountsOnly=false
				)>
				<cftry>
					<!--- Create temporary folder for PDF generation --->
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="NY")>
					<cfset local.uid = createUUID()>

					<!--- Generate PDF from invoice content --->
					<cfdocument format="PDF" filename="#local.strFolder.folderPath#/un_#local.uid#.pdf"  pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
						<cfoutput>
							<html>
							<head>
							<style>

							</style>
							</head>
							<body>
			                    <p>Thank you for your application.</p>
			                    <p>Here are the details of your application:</p>
								#session.invoice#
							</body>
							</html>
						</cfoutput>
					</cfdocument>

					<!--- Prepare PDF structure for storage --->
					<cfset local.strPDF = structNew()>
					<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
					<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
					<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
					<!--- Encrypt and rename PDF --->
					<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
					<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
					<!--- Store PDF to member's DOCS tab --->
					<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=session.memberID, strPDF=local.strPDF, siteID=local.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>

				<cfoutput>
					Thank you for submitting your request for membership.<br /><br />
					<cfif IsDefined("session.emailSentToUser") and session.emailSentToUser NEQ true>
						Please print the following for your records:
						#session.invoice#	
					<cfelse>
						You have been sent an email for your records.
					</cfif>
				</cfoutput>				
				<cfset session.invoice = "">
				<cfset StructDelete(session,"emailSentToUser")>
				<cfset StructDelete(session,"memberID")>

			</cfcase>
			
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>