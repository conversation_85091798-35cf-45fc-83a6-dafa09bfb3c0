<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();

			if (!$('input[name="s_ft"]').is(':checked')) {
				$('#searchBoxError').html('Select at least one program format.').show();
				return false;
			} else {
				var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
				if (isEmptyForm){
					$('#searchBoxError').html('Search using at least one criteria below.').show();
					$("form#frms_Search input:text").first().focus();
					return false;
				} else {
					$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
					return true;
				}
			}
		}
		$(function(){
			mca_setupDatePickerRangeFields('s_datefrom','s_dateto');
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>

<cfform name="frms_Search" id="frms_Search" class="form-horizontal form-medium-lg" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();">
	<div class="control-group">
		<label class="control-label" for="sw_s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_all" id="sw_s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>

	<cfif (arrayLen(local.arrAllowedFormats) gt 1)>
		<div class="control-group">
			<label class="control-label" for="s_cle">Credit offered in:</label>
			<div class="controls">
				<cfselect name="s_cle" id="s_cle" query="local.qryAuthorities" value="authorityID" display="jurisdiction" selected="#local.strSearchForm.s_cle#" queryPosition="below">
					<option value="">All/No jurisdictions</option>
				</cfselect>
			</div>
		</div>
	<cfelse>
		<input type="hidden" name="s_cle" value="">
	</cfif>

	<div class="control-group">
		<label class="control-label" for="s_aut">Author/Speaker:</label>
		<div class="controls">
			<cfselect name="s_aut" id="s_aut">
				<option value="">All authors/speakers</option>
				<cfloop query="local.qryAuthors">
					<option value="#local.qryAuthors.authorID#" <cfif local.strSearchForm.s_aut is local.qryAuthors.authorid>selected</cfif>>#local.qryAuthors.prefix# #local.qryAuthors.firstname# #local.qryAuthors.middlename# #local.qryAuthors.lastname#<cfif len(local.qryAuthors.suffix)>, #local.qryAuthors.suffix#</cfif></option>
				</cfloop>
			</cfselect>
		</div>
	</div>

	<div class="control-group">
		<label class="control-label" for="s_cat">Subject:</label>
		<div class="controls">
			<cfselect name="s_cat" id="s_cat" query="local.qryCategories" value="categoryName" display="categoryName" selected="#local.strSearchForm.s_cat#" queryPosition="below">
				<option value="">All subjects</option>
			</cfselect>
		</div>
	</div>

	<cfif local.qryPublishers.recordcount gt 1>
		<div class="control-group">
			<label class="control-label" for="s_pub">Content publisher:</label>
			<div class="controls">
				<cfselect name="s_pub" id="s_pub" query="local.qryPublishers" value="participantID" display="publisherName" selected="#local.strSearchForm.s_pub#" queryPosition="below">
					<option value="">All publishers</option>
				</cfselect>
			</div>
		</div>
	<cfelse>
		<cfinput type="hidden" name="s_pub"  id="s_pub" value="#local.qryPublishers.participantID#">
	</cfif>

	<div class="control-group">
		<label class="control-label" for="s_datefrom">Date published Between:</label>
		<div class="controls">
			<cfinput type="text" id="s_datefrom" name="s_datefrom" value="#DateFormat(local.strSearchForm.s_datefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol" validate="date" message="Enter a valid message date"> and 
			<cfinput type="text" id="s_dateto" name="s_dateto" value="#DateFormat(local.strSearchForm.s_dateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol" validate="date" message="Enter a valid message date">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_datefrom','s_dateto');return false;">clear dates</a>
		</div>
	</div>

	<div class="control-group">
		<label class="control-label">Program formats:</label>
		<div class="controls">
			<cfloop from="1" to="#arrayLen(local.arrAllowedFormats)#" index="local.thisFmt">
				<label class="checkbox inline">
					<input type="checkbox" name="s_ft" class="mc_inlinecheckbox" value="#local.arrAllowedFormats[local.thisFmt]#"<cfif listFindNoCase(local.strSearchForm.s_ft,local.arrAllowedFormats[local.thisFmt]) OR local.strSearchForm.newsearch> checked</cfif>>#evaluate("local.qryAssociation.brand" & local.arrAllowedFormats[local.thisFmt] & "Tab")#
				</label>
			</cfloop>
			<input type="hidden" name="s_ft" value="SWX">
		</div>
	</div>

	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</cfform>
</cfoutput>