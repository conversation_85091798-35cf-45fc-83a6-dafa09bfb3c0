<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.evRegHead">	
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/resourceFields.js#local.assetCachingKey#"></script>
	<script type="text/javascript">
		function doCancelEventReg() {
			window.location.href='#arguments.event.getValue('mainregurl')#&regaction=cancel';
		}
		function searchEventReg() {
			window.location.href='#arguments.event.getValue('mainregurl')#&regaction=reset';
		}
		function reloadEventReg() {
			window.location.href='#arguments.event.getValue('mainregurl')#';
		}
		function loadEventRegSteps(regstep,mode) {
			let stepMode = typeof mode != "undefined" ? mode : 'singlestep';
			let arrSteps = [], arrPromises = [], startStep = 2, endStep = #local.evRegV2.currentReg.registrantID GT 0 ? 5 : 4#;
			
			/* stepMode = all; load all steps (editing cart item) */
			regstep = stepMode == 'all' ? endStep : Number(regstep);
			
			if (regstep >= startStep && regstep <= endStep) {
				/* stepMode == 'init'; load those covered steps */
				startStep = ['all','init'].indexOf(stepMode) != -1 ? startStep : regstep;
				arrSteps = Array(regstep - startStep + 1).fill().map((n,i) => startStep + i);
			}

			arrSteps.forEach(function(step,index) {
				arrPromises.push(
					new Promise(function(resolve, reject) {
						$('##EvRegStep'+step)
							.html($('##EvRegLoading').html())
							.load('#arguments.event.getValue('evregresourceurl')#&regaction=showstep&evstp='+step,
								function() {
									resolve();
								}
							)
							.show();
					})
				);
			});

			if (arrPromises)
				Promise.all(arrPromises).then(function() { onCompleteLoadingEventRegSteps(mode); });
		}
		function onCompleteLoadingEventRegSteps(mode) {
			if (['all','init'].indexOf(mode) != -1) {
				mca_setupCalendarIcons('evRegContainer');
				<cfif local.evRegV2.currentReg.registrantID GT 0>getEditRegTotals();</cfif>
			}
			$('.evRegStepSummary').off('click').on('click', function() {
				window['editRegStep'+$(this).data('evregsummarystep')]();
			});
			if ($('.btnFinalizeReg').length) $('.btnFinalizeReg').prop('disabled',false);
		}
		function evReg_validate_fieldIsRequired(thisField) {
			var fld = $(thisField);
			var fldName = $(thisField).attr('name');
			var displayTypeCode = fld.data('displaytypecode');
			var dataTypeCode = fld.data('datatypecode');
			var fldContainer = $('##'+fldName+'_container');
			var fldErr = $('##'+fldName+'_err');
			var returnMsg = '';
			switch(displayTypeCode) {
				case 'TEXTBOX':
				case 'TEXTAREA':
				case 'DATE':
					if (fld.val().trim() == '') {
						returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
				case 'SELECT':
					if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
						returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
				case 'RADIO':
				case 'CHECKBOX':
					if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
						returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
				case 'DOCUMENT':
					var oldFileName = fldName.replace('cf_','cf_oldDoc_');
					var deleteFileName = fldName.replace('cf_','cf_remDoc_');
					if (fld.val().trim() == '' && ( $('input[name="' + oldFileName + '"]').val() == '' || Number($('input[name="' + deleteFileName + '"]').val()) == 1 )) {
						returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
			}
			
			if (returnMsg.length) {
				fldErr.html(returnMsg).show();
				fldContainer.addClass('alert alert-danger');
			} else {
				fldErr.html('').hide();
				fldContainer.removeClass('alert alert-danger');
			}
			
			return returnMsg;
		}
		function evReg_validate_textControlValidInteger(thisField) {
			var returnMsg = '';
			var dataTypeDisplay = 'whole number';
			var fld = $(thisField);
			var fldName = $(thisField).attr('name');
			var fldContainer = $('##'+fldName+'_container');
			var fldErr = $('##'+fldName+'_err');
			var fldval = Number(fld.val().trim());
			var supportqty = fld.data('supportqty');
			if(supportqty == 1) {
				dataTypeDisplay = 'quantity';
			}

			if (fldval != '') {
				if (fldval !== parseInt(fldval)) {
					returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('evreg_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				} else if (supportqty == 1) {
					if (fldval < 0) {
						returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('evreg_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					} else if (fldval > Number(fld.data('fieldqtymaxallowed'))) {
						returnMsg = 'Enter a quantity between 0 and ' + Number(fld.data('fieldqtymaxallowed')) + ' for ' + fld.data('evreg_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					}
				}
			}

			if (returnMsg.length) {
				fldErr.html(returnMsg).show();
				fldContainer.addClass('alert alert-danger');
			} else {
				fldErr.html('').hide();
				fldContainer.removeClass('alert alert-danger');
			}

			return returnMsg;
		}
		function evReg_validate_textControlValidDecimal(thisField) {
			var returnMsg = '';
			var dataTypeDisplay = 'decimal number';
			var fld = $(thisField);
			var fldName = $(thisField).attr('name');
			var fldContainer = $('##'+fldName+'_container');
			var fldErr = $('##'+fldName+'_err');
			var fldval = Number(fld.val().trim());
			var supportamt = fld.data('supportamt');
			if(supportamt == 1) {
				dataTypeDisplay = 'amount';
			}

			if (fldval != '') {
				if (fldval !== parseFloat(fldval)) {
					returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('evreg_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				} else if (supportamt == 1 && parseFloat(fldval) < 0) {
					returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('evreg_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				}
			}

			if (returnMsg.length) {
				fldErr.html(returnMsg).show();
				fldContainer.addClass('alert alert-danger');
			} else {
				fldErr.html('').hide();
				fldContainer.removeClass('alert alert-danger');
			}

			return returnMsg;
		}
		function formatCurrency(num) {
			if (num.length == 0)
				return "";
			else {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				if (num ==0 && cents == 0) {
					return "";
				} else {
					num = Math.floor(num/100).toString();
					if(cents<10) cents = "0" + cents;
					for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
					return (((sign)?'':'-') + num + '.' + cents);
				}
			}
		}
		function getEditRegTotals() {
			var regResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					$('##evRegStep3TotalPriceDisplay').html(r.regfieldstotaldisp);
					$('##evRegStep4TotalPriceDisplay').html(r.ticketseltotaldisp);
				} else {
					alert('We were unable to get the registrant totals.');
				}
			};

			var objParams = { registrantID:#local.evRegV2.currentReg.registrantID# };
			TS_AJX('EVREGV2','getRegistrantTotals',objParams,regResult,regResult,10000,regResult);
		}
		function regCartCheckout(eventJSON,totalAmount) {
			$('##evRegContainer').hide();
			$('##EvRegLoading').show();
			$('html, body').animate({
				scrollTop: $('##EvRegLoading').offset().top - 175
			}, 750);
			MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
			.then( () => {
				try {
					triggerEventsCart(eventJSON,totalAmount, 'add');
					window.location.href='#arguments.event.getValue('mainregurl')#&regaction=checkout';
				} catch (error) {
					console.error("Error parsing JSON:", error.message);
					window.location.href='#arguments.event.getValue('mainregurl')#&regaction=checkout';
				}
			});			
		}
		<cfif local.evRegV2.currentReg.registrantID GT 0>
			function saveEditReg() {
				$('.btnFinalizeReg').hide();
				$('##saveEditRegSaveLoading').html('<i class="icon-spin icon-spinner icon-lg"></i> Please wait while we validate and save the details.').show();
				
				let arrSaveRegPromises = [];
				if (typeof doS3ValidateAndSave == 'function') arrSaveRegPromises.push(doS3ValidateAndSave);
				if (typeof doS4ValidateAndSave == 'function') arrSaveRegPromises.push(doS4ValidateAndSave);
				if (typeof doS5ValidateAndSave == 'function') arrSaveRegPromises.push(doS5ValidateAndSave);

				if (arrSaveRegPromises.length) {
					Promise.mapSeries(arrSaveRegPromises, function(evRegStepSave,index) {
						return evRegStepSave();
					}).then(function() {
						doSaveEditReg();
					}).catch(function(err) {
						$('.btnFinalizeReg').show();
						$('##saveEditRegSaveLoading').html('').hide();
						$('##saveEditRegErr').html(typeof err == 'string' ? err : 'We were unable to save registration details. Try again.');
					});
				} else {
					doSaveEditReg();
				}
			}
			function doSaveEditReg() {
				$('##evRegContainer').hide();
				$('##EvRegLoading').show();
				$('html, body').animate({
					scrollTop: $('##EvRegLoading').offset().top - 175
				}, 750);
				window.location.href='#arguments.event.getValue('mainregurl')#&regaction=checkout';
			}
		</cfif>
		function closeBox() { $.colorbox.close(); }
		
		$(function() {
			loadEventRegSteps(
				#local.evRegV2.currentReg.currentStep#,
				"#local.evRegV2.currentReg.isRegCartItem EQ 1 OR local.evRegV2.currentReg.registrantID GT 0 ? 'all' : 'init'#"
			);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evRegHead)#">