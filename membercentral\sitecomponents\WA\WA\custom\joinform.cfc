<cfcomponent extends="model.customPage.customPage" output="false">

	<!--- <cfset variables.objCustomPageUtils = application.objCustomPageUtils> --->
	<cfset variables.objCustomPageUtils = createObject("component","model.customPage.customPageUtils")>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Click Here to Begin" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you. Your membership application has been submitted for review. You will receive an email from Washington State Association for Justice once your application has been approved and processed. You will receive an email from Washington State Association for Justice once your application has been approved and processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="Washington State Association for Justice records indicate you are currently a WSAJ member. Please click <a href='/?pg=login'>here</a> to log in." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Error message when billed subscription found", value="You need to renew your WSAJ membership. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="MembershipType", type="CONTENTOBJ", desc="Membership Type Intro", value="Per the information provided on the previous page, below is your WSAJ Membership selection and dues amount." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="AUTHCIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeBankDraft", type="STRING", desc="pay profile code for Check", value="WSAJBD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ProfessionalLicense", type="CONTENTOBJ", desc="Professional License", value="Please add all State Licensures. For License Number, please be sure to use any leading zeroes on the number.  You must provide your admittance date for each license type (even if it is an estimate)." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="RequiredStatement", type="CONTENTOBJ", desc="Required Reminder Statement", value="* Required Data" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="TrueCertification", type="CONTENTOBJ", desc="True Certification Statement", value="I certify that the statements I made herein are true. By providing my mailing address, email address, telephone and fax number, I agree to receive communications sent by or on behalf of the Washington State Association for Justice via mail, email, telephone or fax." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PracticeCertification", type="CONTENTOBJ", desc="Practice Certification Statement", value="By payment of these dues, I hereby CERTIFY I am an attorney and a member in good standing with the Washington State Bar Association. I do hereby certify that on an annual basis, of the personal injury cases handled, 50% or more are for the plaintiff; criminal practice involves representation of defendants; represents claimants in employment, workers' comp, social security and longshoreworkers' actions." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CertificationParagraphOne", type="CONTENTOBJ", desc="Certification Paragraph One", value="I further agree that in the event that the nature of my practice or my firm or other practice arrangement changes such that I may no longer meet the membership criteria set forth herein, I will immediately and voluntarily notify WSAJ and will either voluntarily withdraw from my membership in WSAJ or, on request, provide the Board or any committee appointed regarding membership eligibility with sufficient details regarding such matters to allow verification that the interest of WSAJ and its members are being and will be adequately protected." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CertificationParagraphTwo", type="CONTENTOBJ", desc="Certification Paragraph Two", value="I further agree that if other attorneys in my firm or other practice arrangements are NOT eligible for general membership in WSAJ pursuant to the above-state criteria, I will make all reasonable and necessary efforts to implement specific institutional mechanisms (e.g., 'Chinese Walls') to effectively prevent any flow of confidential or sensitive information generated by WSAJ or its members to such attorneys in my firm or other practice arrangement; and will, on request, provide the Board or any committee appointed regarding membership eligibility with sufficient details regarding such institutional mechanisms to verify that the interest of WSAJ and its members are being and will be adequately protected." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CertificationParagraphThree", type="CONTENTOBJ", desc="Certification Paragraph Three", value="I further agree to not use information obtained from WSAJ or its members, whether the same is disseminated or made available by means of the WSAJ litigation sections, website, listserves, seminars, document exchange, or otherwise, to advance interests opposed to those of WSAJ, its members, or any injured victim or wronged consumer; and acknowledge my understanding that such information would include, but not limited to, information about experts, insurers, defense attorneys, corporate defendants, or judges, or about political goals or strategies of the organization. Finally, I do hereby swear or affirm that I shall fully and faithfully abide by the By-Laws and all rules or policies of WSAJ which are now or which may hereinafter be in effect; acknowledge that by signing this application, I represent, as an attorney and member of the Washington State Bar Association that the certifications, agreements, and statements herein are truthful and correct; and acknowledge my understanding that any false or misleading certifications, statements, or agreements made as part of a Membership application may serve as ground for expulsion, suspension, censure or restrictions. Your dues payment to WSAJ is not tax deductible as a charitable contribution for federal income tax purposes. They may be tax deductible as ordinary and necessary business expenses subject to restrictions imposed as a result of association lobbying activities." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MembershipCategory", type="CONTENTOBJ", desc="Membership Category Options", value="<p><span class=""HeaderText"">WSAJ Attorney Member (Voting):</span><br />
<span class=""BodyText"">Of the personal injury cases handled, more than 50% are for the plaintiff; represents claimants in employment, workers&rsquo; compensation, social security, longshoreworkers&rsquo; action, consumer protection and civil rights cases. Criminal practice involves representation of defendants.</span><br />
&nbsp;<br />
<span class=""HeaderText"">Other Membership Types (Non-Voting):</span><br />
&nbsp;<br />
<span class=""BodyText""><strong>Public Interest Member</strong> - Practicing attorney employed by a public agency or non-profit organization who supports the goals and mission of WSAJ and does NOT do any defense work in civil cases.<br />
&nbsp;<br />
<strong>WSAJ Retired Member</strong> - Any plaintiff attorney in good standing with bar (active or inactive) who no longer practices law (excluding pro bono).<br />
&nbsp;<br />
<strong>EAGLE Legal Staff Member</strong> - Law clerks, paralegals, assistants or secretaries employed by a current WSAJ EAGLE Member and working in some support capacity. Employer name required. Contract work is limited to work for WSAJ voting members.<br />
&nbsp;<br />
<strong>Law Student Member</strong> - Students enrolled at an accredited school of law, or registered for the WSBA Rule 6 or Rule 9 Apprenticeship programs and work for a Plaintiff attorney. &nbsp;Membership runs from the date of approved application to the date admitted to the bar.</span></p>
" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
		variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Membership Application',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MembershipApplicationHistory', subName='Started');
		variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MembershipApplicationHistory', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;
				}
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				@media only screen and (min-width: 768px) and (max-width: 979px){
					input:not(.mc_inlinecheckbox) {
						width: auto!important;
					}
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
				}

				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
						
			<div class="row-fluid tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="row-fluid tsAppBodyText">#variables.strPageFields.AccountLocatorIntroText#</div>
			<div class="row-fluid tsAppSectionContentContainer">
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.annualMembershipDuesUID = "7abc5432-f567-4490-bbf3-b0d2fe64b5cf">

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.annualMembershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.annualMembershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.annualMembershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Join WSAJ - Member Information --->
		<cfset local.strFieldSetContent1 = variables.objCustomPageUtils.renderFieldSet(uid='a451dc5b-898a-4c89-b614-6a4dd7dc6c3d', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Social Media --->
		<cfset local.strFieldSetContent2 = variables.objCustomPageUtils.renderFieldSet(uid='99bebb1f-38b6-4081-8253-7407bfbd2db3', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Mailing Address Information --->
		<cfset local.strFieldSetContent3 = variables.objCustomPageUtils.renderFieldSet(uid='fe98e972-c59b-4c7d-9775-edcda37810ef', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Physical Address Information --->
		<cfset local.strFieldSetContent4 = variables.objCustomPageUtils.renderFieldSet(uid='449ed838-1e75-42e4-9044-88b1f1eb10b5', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Home Address Information --->
		<cfset local.strFieldSetContent5 = variables.objCustomPageUtils.renderFieldSet(uid='44b0d725-e047-4c38-a2fa-6b1bfd6b2123', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Address Preferences --->
		<cfset local.strFieldSetContent6 = variables.objCustomPageUtils.renderFieldSet(uid='3e4d0f47-3fe0-45e5-bbd9-d88b9884f801', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Additional Information --->
		<cfset local.strFieldSetContent7 = variables.objCustomPageUtils.renderFieldSet(uid='f9342973-32df-488c-93c6-b95e72bbf3b3', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Membership Category --->
		<cfset local.strFieldSetContent8 = variables.objCustomPageUtils.renderFieldSet(uid='4546dd12-6264-4bfd-b181-d6df3234f1c7', mode="collection", strData=local.strData)>

		<!--- get Membership Category --->
		<cfset local.memberCatNum = "">
		<cfloop collection="#local.strFieldSetContent8.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent8.strFields[local.thisField] eq "Membership Category you qualify for">
				<cfset local.memberCatNum = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<!--- get Law School, Graduation Date --->
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif findNoCase("Law School", local.strFieldSetContent1.strFields[local.thisField])>
				<cfset local.lawSchool = local.thisField>
			<cfelseif local.strFieldSetContent1.strFields[local.thisField] eq "Graduation Date (or expected date)">
				<cfset local.gradDate = local.thisField>
			</cfif>
		</cfloop>

		<!--- get Mailing Address --->
		<cfloop collection="#local.strFieldSetContent3.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent3.strFields[local.thisField] eq "Address Line 1">
				<cfset local.memberMailAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<!--- get Physical Address --->
		<cfloop collection="#local.strFieldSetContent4.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent4.strFields[local.thisField] eq "Address Line 1">
				<cfset local.memberPhisAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<!--- get Home Address --->
		<cfloop collection="#local.strFieldSetContent5.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent5.strFields[local.thisField] eq "Address Line 1">
				<cfset local.memberHomeAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<!--- get  Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.strFieldSetContent6.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent6.strFields[local.thisField] eq "Use this address for Primary">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.strFieldSetContent6.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>			

		<!--- get Membership Category --->
		<cfset local.memberCatNum = "">
		<cfloop collection="#local.strFieldSetContent8.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent8.strFields[local.thisField] eq "Membership Category you qualify for">
				<cfset local.memberCatNum = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<!--- get Membership Category --->
		<cfset local.EagleMemberNote = "">
		<cfloop collection="#local.strFieldSetContent7.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent7.strFields[local.thisField] eq "WSAJ EAGLE Member (for Legal Staff Membership)">
				<cfset local.EagleMemberNote = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>		

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#
					#local.strFieldSetContent2.jsValidation#
					#local.strFieldSetContent3.jsValidation#
					#local.strFieldSetContent4.jsValidation#
					#local.strFieldSetContent5.jsValidation#
					#local.strFieldSetContent6.jsValidation#
					#local.strFieldSetContent7.jsValidation#					
					#local.strFieldSetContent8.jsValidation#

					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					if(prof_license != "" && prof_license != null){
						isProfLicenseSelected = true;
						$.each(prof_license,function(i,val){
							var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
							if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
							if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
						});

					}

					var memberPrefPrimAddress = "";
					<cfif len(trim(local.memberPrefPrimAddress))>
						memberPrefPrimAddress = $('###variables.formName# ###local.memberPrefPrimAddress# option:selected').text();
					</cfif>

					var memberPrefBillAddress = "";
					<cfif len(trim(local.memberPrefBillAddress))>
						memberPrefBillAddress = $('###variables.formName# ###local.memberPrefBillAddress# option:selected').text();
					</cfif>		

					
					<cfif isDefined("local.memberMailAddress1")>						
						if ( (memberPrefPrimAddress == "Address" || memberPrefBillAddress == "Address") && $.trim($('###variables.formName# ###local.memberMailAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Mailing Address is required.";
					</cfif>

					<cfif isDefined("local.memberHomeAddress1")>						
						if ( (memberPrefPrimAddress == "Home" || memberPrefBillAddress == "Home") && $.trim($('###variables.formName# ###local.memberHomeAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Home Address is required.";
					</cfif>

					<cfif isDefined("local.memberPhisAddress1")>						
						if ( (memberPrefPrimAddress == "Physical" || memberPrefBillAddress == "Physical") && $.trim($('###variables.formName# ###local.memberPhisAddress1#').val()) == '')			
							arrReq[arrReq.length] = "Physical Address is required.";
					</cfif>	

					<cfif isDefined("local.memberCatNum") and len(trim(local.memberCatNum))>
						if ($.trim($('###variables.formName# ###local.memberCatNum#').val()) == '') arrReq[arrReq.length] = "Membership Category is required.";
					</cfif>					

					<cfif len(trim(local.memberCatNum))>
						var mcSel = $('###variables.formName# ###local.memberCatNum# option:selected').text();
						if (mcSel == 'Law Student') {
							<cfif (isDefined("local.lawSchool") and len(trim(local.lawSchool))) AND (isDefined("local.gradDate") and len(trim(local.gradDate)))>
								if ($.trim($('###variables.formName# ###local.lawSchool#').val()) == '' && $.trim($('###variables.formName# ###local.gradDate#').val()) == '') arrReq[arrReq.length] = "Enter Law School or Graduation Date.";
							<cfelse>
								<cfif isDefined("local.lawSchool") and len(trim(local.lawSchool))>
									if ($.trim($('###variables.formName# ###local.lawSchool#').val()) == '') arrReq[arrReq.length] = "Law School is required.";
								</cfif>
								<cfif isDefined("local.gradDate") and len(trim(local.gradDate))>
									if ($.trim($('###variables.formName# ###local.gradDate#').val()) == '') arrReq[arrReq.length] = "Graduation Date1 is required.";
								</cfif>
							</cfif>
						} else if (mcSel == 'EAGLE Legal Staff') {
							<cfif isDefined("local.EagleMemberNote") and len(trim(local.EagleMemberNote))>
								if ($.trim($('###variables.formName# ###local.EagleMemberNote#').val()) == '') arrReq[arrReq.length] = "WSAJ EAGLE Member (for Legal Staff Membership) is required.";
							</cfif>
						}
						else{ 
							if (mcSel != '' && !isProfLicenseSelected)
								arrReq[arrReq.length] = "Professional License is required.";
						}					
					</cfif>								

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>

					$("##mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							if(ui.checked){
								$("##state_table").show();
								$('##state_table tr:last').after('<tr id="tr_state_'+ui.value+'">'
																+'<td align="right" class="tsAppBodyText">'+ui.text+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" />'
																+'	<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
																+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="tsAppBodyText" />'
																+'</td>'
																+'<td align="center" class="tsAppBodyText" style="display:none">'
																+'	<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="">Please Select</option><option value="active" selected="selected">Active</option><option value="inactive">Inactive</option><option value="disbarred">Disbarred</option><option value="suspended">Suspended</option></select>'
																+'</td>'
														+'</tr>');
								if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
									mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
								}
								$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
								}else{
								$("##tr_state_"+ui.value).remove();
							}
					   },
					});					
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
							
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<cfif len(variables.strPageFields.RequiredStatement)>
				<div>#variables.strPageFields.RequiredStatement#</div>
				<br/>
			</cfif>	
			
			<div class="row-fluid">
				<div class=" span12 tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
				<div class="span6 tsAppSectionContentContainer">
					#local.strFieldSetContent1.fieldSetContent#
				</div>
			</div></br>	
			
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">
				#local.strFieldSetContent2.fieldSetContent#
				</div>
			</div>
			
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">Professional License Information</div>
				<div class="row-fluid tsAppSectionContentContainer">
					<cfif len(variables.strPageFields.ProfessionalLicense)>
						<div id="ProfessionalLicense">#variables.strPageFields.ProfessionalLicense#</div><br/>
					</cfif>
					<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
					<table cellpadding="3" border="0" cellspacing="0">		
						<tr class="top">
							<th class="tsAppBodyText" colspan="3" align="left">
								&nbsp;
							</th>
						</tr>							
						<tr align="top">
							<td class="tsAppBodyText" width="10">&nbsp;</td>
							<td class="tsAppBodyText" width="365">Professional License:</td>
							<td class="tsAppBodyText">
								<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
									<cfloop query="local.qryOrgPlTypes">
										<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
										<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr class="top">
							<td class="tsAppBodyText" width="10"></td>
							<td class="tsAppBodyText"></td>
							<td class="tsAppBodyText"></td>
						</tr>
					</table>
					<table cellpadding="3" border="0" cellspacing="0">
						<tr>
							<td class="tsAppBodyText" width="375">&nbsp;</td>
							<td>
								<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
									<thead>
										<tr valign="top">
											<th align="center" class="tsAppBodyText">State Name</th>
											<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
											<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
										</tr>
									</thead>
									<tbody>
									<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
										<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
											<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
											<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
											<cfset  local.license_status  = 'Active'>
											<tr id="tr_state_#local.thisItem#">
												<td align="right" class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</td>
												<td align="center" class="tsAppBodyText">
													<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
													<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
												</td>
												<td align="center" class="tsAppBodyText">
													<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" />
													<cfsavecontent variable="local.datejs">
														<cfoutput>
														<script language="javascript">
															$(document).ready(function() { 
																mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
															});
														</script>
														<style type="text/css">
														##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
														</style>
														</cfoutput>
													</cfsavecontent>
													<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
												</td>
												<td align="center" class="tsAppBodyText">
													<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText">
														<option value="">Please Select</option>
														<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>
														<option <cfif local.license_status eq "inactive">selected="selected"</cfif>value="inactive">Inactive</option>
														<option <cfif local.license_status eq "disbarred">selected="selected"</cfif>value="disbarred">Disbarred</option>
														<option <cfif local.license_status eq "suspended">selected="selected"</cfif>value="suspended">Suspended</option>
													</select>
												</td>
											</tr>
										</cfloop>
									</cfif>									
									</tbody>
								</table>
							</td>
						</tr>					
					</table>
				</div>
			</div>
			
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">	
					#local.strFieldSetContent3.fieldSetContent#
				</div>
			</div>
			
			<div class="row-fluid" id="lawStudent">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent4.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">
					#local.strFieldSetContent4.fieldSetContent#
				</div>
			</div>
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent5.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">
					#local.strFieldSetContent5.fieldSetContent#
				</div>
			</div>	
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent6.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">
					#local.strFieldSetContent6.fieldSetContent#
				</div>
			</div>
			<div class="row-fluid" id="addresspreference">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent7.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">
					#local.strFieldSetContent7.fieldSetContent#
				</div>
			</div>
			<div class="row-fluid" id="addresspreference">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent8.fieldSetTitle#</div>
				<cfif len(variables.strPageFields.MembershipCategory)>
					<div id="MembershipCategoryContent">#variables.strPageFields.MembershipCategory#</div><br/>
				</cfif>
				<div class="row-fluid tsAppSectionContentContainer">
					#local.strFieldSetContent8.fieldSetContent#
				</div>
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->		
		<cfset local.memberShipCategoryColumnName = "Membership Category ">
		<cfset local.memberShipCategoryStruct = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName=local.memberShipCategoryColumnName)>
		<cfset local.membershipCategoryColumnId = local.memberShipCategoryStruct.columnId>
		<cfif local.membershipCategoryColumnId NEQ '' AND local.membershipCategoryColumnId NEQ 0>
			<cfset local.membershipCategorySelected = arguments.rc['md_'&local.membershipCategoryColumnId]>
			<cfset local.membersgipCategorySelectedValue = variables.objCustomPageUtils.mem_getCustomFieldValueFromValueID(variables.orgid,local.memberShipCategoryColumnName,local.membershipCategorySelected)>
			
			<cfif ListFindNoCase("Regular Attorney,Public Interest Attorney,WSAJ Retired Attorney",local.membersgipCategorySelectedValue)>
				<cfset local.contactTypeValue = "Attorney">
			<cfelseif local.membersgipCategorySelectedValue EQ "EAGLE Legal Staff">
				<cfset local.contactTypeValue = "Legal Staff">
			<cfelseif local.membersgipCategorySelectedValue EQ "Law Student">
				<cfset local.contactTypeValue = "Law Student">
			</cfif>
		</cfif>
	
		<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfset local.objSaveMember = variables.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
		<cfset local.strResult1 = local.objSaveMember.saveData()>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = variables.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '742124B8-A5AA-4A0C-B052-D4E09F419F2C')>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = variables.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>
 		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();
				        if (!$('###variables.formName# ##TrueCertification').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}

						#local.result.jsValidation#

						mc_continueForm($('###variables.formName#'));
						return false;
					}
				</script>
 			</cfoutput>
 		</cfsavecontent>


 		<cfhtmlhead text="#local.resultHtmlHeadText#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#
						<div class="well subAddonWrapper" id="CommitteeViceChair">
							<legend>Agreement Confirmation</legend>
							<cfif len(variables.strPageFields.PracticeCertification)>
								<div id="PracticeCertification">#variables.strPageFields.PracticeCertification#</div><br/>
							</cfif>
							<cfif len(variables.strPageFields.CertificationParagraphOne)>
								<div id="CertificationParagraphOne">#variables.strPageFields.CertificationParagraphOne#</div><br/>
							</cfif>
							<cfif len(variables.strPageFields.CertificationParagraphTwo)>
								<div id="CertificationParagraphTwo">#variables.strPageFields.CertificationParagraphTwo#</div><br/>
							</cfif>
							<cfif len(variables.strPageFields.CertificationParagraphThree)>
								<div id="CertificationParagraphThree">#variables.strPageFields.CertificationParagraphThree#</div><br/>
							</cfif>
							<cfif len(variables.strPageFields.TrueCertification)>
								<div id="TrueCertification">
									<label class="checkbox subLabel" for="TrueCertification">
									<input class="subCheckbox" type="checkbox" name="TrueCertification" id="TrueCertification" value="1">
									#variables.strPageFields.TrueCertification#
								</div><br/>
							</cfif>

						</div>
					</div>
				</div>
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '742124B8-A5AA-4A0C-B052-D4E09F419F2C')>
		<cfset local.strResult = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '742124B8-A5AA-4A0C-B052-D4E09F419F2C')>

		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.frequencyShortName = "F">
		<cfset local.frequencyUID = arguments.rc["SUB#local.subscriptionID#_RATEFREQUENCYSELECTED"]>
		<cfquery name="local.qrySelectedFreq" datasource="#application.dsn.memberCentral.dsn#">
			select frequencyShortName from sub_frequencies  where uid = <cfqueryparam value="#local.frequencyUID#" cfsqltype="cf_sql_varchar" />
		</cfquery>

		<cfif len(trim(local.qrySelectedFreq.frequencyShortName))>
			<cfset local.frequencyShortName = trim(local.qrySelectedFreq.frequencyShortName)>
		</cfif>
		
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC ]>
		<cfif local.frequencyShortName eq "M">
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC, variables.strPageFields.PaymentProfileCodeBankDraft ]>
		</cfif>
		
		<cfset local.strReturn = 
			variables.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<cfif len(variables.strPageFields.MembershipType)>
				<div>#variables.strPageFields.MembershipType#</div>
				<br/>
			</cfif>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
				<br/>
			</div>
			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Total Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();" disabled>Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>
			<script>
				$(document).ready(function(){
					setTimeout(function() {
						$('button').attr('disabled',false);
					}, 1200);
				});
			</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset variables.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Join WSAJ - Member Information --->
		<cfset local.strFieldSetContent1 = variables.objCustomPageUtils.renderFieldSet(uid='a451dc5b-898a-4c89-b614-6a4dd7dc6c3d', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Social Media --->
		<cfset local.strFieldSetContent2 = variables.objCustomPageUtils.renderFieldSet(uid='99bebb1f-38b6-4081-8253-7407bfbd2db3', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Mailing Address Information --->
		<cfset local.strFieldSetContent3 = variables.objCustomPageUtils.renderFieldSet(uid='fe98e972-c59b-4c7d-9775-edcda37810ef', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Physical Address Information --->
		<cfset local.strFieldSetContent4 = variables.objCustomPageUtils.renderFieldSet(uid='449ed838-1e75-42e4-9044-88b1f1eb10b5', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Home Address Information --->
		<cfset local.strFieldSetContent5 = variables.objCustomPageUtils.renderFieldSet(uid='44b0d725-e047-4c38-a2fa-6b1bfd6b2123', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Address Preferences --->
		<cfset local.strFieldSetContent6 = variables.objCustomPageUtils.renderFieldSet(uid='3e4d0f47-3fe0-45e5-bbd9-d88b9884f801', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Additional Information --->
		<cfset local.strFieldSetContent7 = variables.objCustomPageUtils.renderFieldSet(uid='f9342973-32df-488c-93c6-b95e72bbf3b3', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Membership Category --->
		<cfset local.strFieldSetContent8 = variables.objCustomPageUtils.renderFieldSet(uid='4546dd12-6264-4bfd-b181-d6df3234f1c7', mode="confirmation", strData=arguments.rc)>
	
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '742124B8-A5AA-4A0C-B052-D4E09F419F2C')>
		
		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<fieldset>
						<legend>Form submission</legend>
						<div class="row-fluid tsAppSectionContentContainer">#variables.strPageFields.ConfirmationMessage#</br></div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<div class="row-fluid tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.ConfirmationMessage#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div class="row-fluid tsAppSectionContentContainer">
					<div class="span12">You have received an application for membership.</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.strFieldSetContent1.fieldSetContent#
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join WSAJ - Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br/>
				
				#local.strFieldSetContent2.fieldSetContent#
				#local.strFieldSetContent3.fieldSetContent#
				#local.strFieldSetContent4.fieldSetContent#
				#local.strFieldSetContent5.fieldSetContent#
				#local.strFieldSetContent6.fieldSetContent#
				#local.strFieldSetContent7.fieldSetContent#
				#local.strFieldSetContent8.fieldSetContent#

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join WSAJ - Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.memberEmail.from },
                            emailto=[{ name="", email=variables.memberEmail.to }],
                            emailreplyto=variables.ORGEmail.to,
                            emailsubject=variables.memberEmail.SUBJECT,
                            emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
                            emailhtmlcontent=local.confirmationHTMLToMember,
                            siteID=variables.siteID,
                            memberID=val(variables.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

		<cfset local.emailSentToUser = local.responseStruct.success>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = variables.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to WSAJ", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>

		<cfreturn local.confirmationPageHTMLContent>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>