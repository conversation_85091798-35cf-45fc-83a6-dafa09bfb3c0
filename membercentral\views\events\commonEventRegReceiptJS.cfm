<cfsavecontent variable="local.evRegReceiptHead">
	<cfoutput>
	<script language="javascript">
		function sendReceipt() {
			var emailRegEx = new RegExp("#application.regEx.email#","i");
			var btn = $('##btnResendReceipt');
			var msg = $('##evRegSendPmtReceiptMsg');
			var em = $('##sendToEmail');
			msg.html('').removeClass('evreg-text-danger').hide();
			btn.html('Sending...').attr("disabled", true);

			var resendReceiptResult = function(r) {
				btn.html('Send').attr("disabled", false);
				if (r.success && r.success.toLowerCase() == 'true') {
					msg.html('<i class="icon-check"></i> &nbsp; <b>Receipt sent to '+em.val()+'.</b>').show().fadeOut(5000);
					em.val('');
				} else {
					msg.html('There was a problem e-mailing this receipt. Try again.').addClass('evreg-text-danger').show();
				}
			};
			
			em.val($.trim(em.val()));
			if (em.val().length == 0 || !(emailRegEx.test(em.val()))) {
				btn.html('Send').attr("disabled", false);
				msg.html('Enter a valid e-mail address.').addClass('evreg-text-danger').show();
				return false;
			}

			var objParams = { receiptUUID:'#local.receiptUUID#', sendToEmail:em.val() };
			TS_AJX('ADMINEVENT','sendPaymentReceipt',objParams,resendReceiptResult,resendReceiptResult,20000,resendReceiptResult);
		}
		function editRegConfirmEmail(mid) {
			$('##evRegConfirmSummary'+mid).hide();
			$('##evRegConfirmFrmContainer'+mid).show(300);
		}
		function sendRegConfirmMail(mid) {
			var emailRegEx = new RegExp("#application.regEx.email#","i");
			var btn = $('##btnResendConfirm'+mid);
			var msg = $('##evRegSendConfirmErr'+mid);
			var em = $('##regConfirmEmail'+mid);
			msg.html('').hide();
			btn.html('Sending...').attr("disabled", true);

			var resendConfirmResult = function(r) {
				btn.html('Send').attr("disabled", false);
				if (r.success && r.success.toLowerCase() == 'true') {
					$('##evRegConfirmFrmContainer'+mid).html('').remove();
					$('##evRegConfirmSummary'+mid).html('Confirmation emailed to ' + r.emailtolist.replaceAll(',',', ')).show(300);
				} else {
					msg.html('There was a problem e-mailing this confirmation. Try again.').show();
				}
			};
			
			em.val($.trim(em.val()));
			if (em.val().length == 0 || !(emailRegEx.test(em.val()))) {
				btn.html('Send').attr("disabled", false);
				msg.html('Enter a valid e-mail address.').show();
				return false;
			}

			var objParams = { receiptUUID:'#local.receiptUUID#', memberID:mid, email:em.val() };
			TS_AJX('EVREGV2','sendRegConfirmation',objParams,resendConfirmResult,resendConfirmResult,20000,resendConfirmResult);
		}

		<cfif arguments.event.getValue('viewDirectory','default') EQ 'responsive'>
			$(function() {
				var tooltipConfig = { container: 'body' };
				$('.tooltip-icon').tooltip(tooltipConfig);
			});
		</cfif>

		<cfif local.strReceipt.keyExists('eventPurchaseJSON') AND IsJSON(local.strReceipt.eventPurchaseJSON) AND local.strReceipt.receiptViewCount EQ 0>
			try {
				if (typeof triggerPurchaseEvent === "function") {
					triggerPurchaseEvent(#local.strReceipt.eventPurchaseJSON#);
				} else {
					MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
					.then( () => {					
						triggerPurchaseEvent(#local.strReceipt.eventPurchaseJSON#);
					});
				}			
			} catch (error) {
				console.error("Error parsing JSON:", error.message);
			}
		</cfif>
		
		function handleGtagCalendarAction(eventID, eventTitle, calendarType){
			if (typeof triggerAddToMyCalendar === "function") {
				triggerAddToMyCalendar(eventID, eventTitle, calendarType);
			} else {
				MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {triggerAddToMyCalendar(eventID, eventTitle, calendarType);});
			}			
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evRegReceiptHead)#">