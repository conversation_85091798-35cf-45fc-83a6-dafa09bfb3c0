<cfoutput>
    <div style="padding:5px">
        <h5>Purchased Expert Challenges</h5>

        #getPaginationHTML(topOrBottom="top",recordCount=local.qryAllDaubert.recordCount,itemCount=val(local.qryAllDaubert.itemCount), docType="expert challenge",maxRows=local.maxRows,startRow=local.startrow,tab='PDT',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any expert challenge reports.")#

        <!--- results table --->
        <cfoutput query="local.qryAllDaubert">
            <div class="row-fluid s_row <cfif local.qryAllDaubert.currentrow mod 2 is 0>s_row_alt</cfif>">
                <div class="span12">
                    <div class="row-fluid">
                        <div class="span10">
                            <i class="icon icon-file-text" style="line-height:23px;"></i><b>
                            #local.qryAllDaubert.salutation# #local.qryAllDaubert.firstname# #local.qryAllDaubert.middlename# #local.qryAllDaubert.lastname# #local.qryAllDaubert.suffix#</b>
                            <div class="s_dtl">
                                <div class="hidden-phone">
                                Document ###local.qryAllDaubert.mcRecordID# - #DateFormat(local.qryAllDaubert.OpinionDate, "mmm d, yyyy")# - #local.qryAllDaubert.jurisdiction#<br/>
                                #local.qryAllDaubert.casecaption#
                                </div>
                                <div align="left"><b class="s_label">Purchased:</b> #dateformat(local.qryAllDaubert.dateEntered,"m/d/yyyy")#</div>
                            </div>
                        </div>
                        <div class="span2 s_act">
                            <div class="s_opt"><a href="javascript:viewDaubertReport(#local.qryAllDaubert.reportID#);" title="View Action Report"><i class="icon icon-file-text" style="line-height:23px;"></i> View Report</a></div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </cfoutput>
        #getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllDaubert.recordCount,itemCount=val(local.qryAllDaubert.itemCount), docType="expert challenge",maxRows=local.maxRows,startRow=local.startrow,tab='PDT',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any expert challenge reports.")#
    </div>
</cfoutput>