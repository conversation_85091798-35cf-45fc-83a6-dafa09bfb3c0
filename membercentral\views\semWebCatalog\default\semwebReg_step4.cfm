<cfinclude template="../commonSWRegStep4JS.cfm">

<cfoutput>
<div class="swreg-card swreg-mt-3 swreg-p-3 tsAppBodyText">
	<form name="frmSWRegStep4" id="frmSWRegStep4" onsubmit="doS4ValidateAndSave();return false;"<cfif local.swReg.currentReg.currentStep NEQ 4> style="display:none;"</cfif>>
		<cfif GetToken(local.rc.item,1,"-") eq "SWB">
			<div class="swreg-d-flex">
				<div class="swreg-col">
					Select the programs you would like credit for: </br> <p style="margin-top:5px;"><input type="checkbox" id="parent" style="margin:0px;" /> Select All</p>
				</div>
				<div class="swreg-col">                                    
				</div>
			</div>
			<cfloop query="local.qryDistSeminars">
				<cfquery name="local.qryCreditBySeminar" dbtype="query">
					select *
					from [local].strCredit.qryCredit
					where seminarID = #local.qryDistSeminars.contentID#
				</cfquery>
				<cfquery name="local.qryCreditDistinct" dbtype="query">
					select distinct authorityID
					from [local].qryCreditBySeminar
				</cfquery>
				<cfquery name="local.qryCredRequired" dbtype="query">
					select distinct seminarCreditID
					from [local].qryCreditBySeminar
					where isCreditRequired = 1
				</cfquery>
				<cfquery name="local.qryCredDefaulted" dbtype="query">
					select distinct seminarCreditID
					from [local].qryCreditBySeminar
					where isCreditDefaulted = 1
				</cfquery>
				<div class="swreg-d-flex">
					<div class="swreg-col-auto">
						<cfif local.qryCreditDistinct.recordcount is 0>	
							&nbsp;&nbsp;&nbsp;
						<cfelse>
							<cfif local.qryCredRequired.recordcount gt 0>
								<input type="checkbox" data-isCredRequired="1"  data-linkedcredits="#valueList(local.qryCreditBySeminar.seminarCreditID)#" class="child" id="creditSelect#local.qryDistSeminars.contentID#" name="creditSelect#local.qryDistSeminars.contentID#" value="1" checked onclick="return false" style="margin:6px 0 0 0;" >																					
							<cfelse>											
								<input type="checkbox" data-isCredRequired="0" data-linkedcredits="#valueList(local.qryCreditBySeminar.seminarCreditID)#" class="child" id="creditSelect#local.qryDistSeminars.contentID#" name="creditSelect#local.qryDistSeminars.contentID#" <cfif local.qryCredDefaulted.recordcount GT 0>checked </cfif> onclick="showhideCreditSelect(this,#local.qryDistSeminars.contentID#)" style="margin:6px 0 0 0;">															
							</cfif>		
						</cfif>	
					</div>
					<div class="swreg-col">     					
						<div class="swreg-mt-2">#local.qryDistSeminars.currentrow#. #local.qryDistSeminars.contentName#</div>
							<cfif local.qryCreditDistinct.recordcount is 0>
							This program <i>has not been submitted</i> for credit in any jurisdiction.
							<cfif local.qryDistSeminars.offerCertificate>
								 Registrants will receive a Certificate of Attendance/Completion that may or may not meet credit requiremements in various jurisdictions.
							</cfif>
							 Where applicable, credit will be only awarded to a paid registrant completing the program at their own computer and phone.
							<cfelse>
								<div class="well creditSelContainer" id="creditDIV#local.qryDistSeminars.contentID#" <cfif local.qryCredRequired.recordcount is 0>data-linkedcreditselect="creditSelect#local.qryDistSeminars.contentID#" <cfif local.qryCredDefaulted.recordcount is 0>style="display:none;"</cfif> <cfelse> data-linkedcreditselect="" </cfif>>
									<div style="border-bottom:1px solid ##ccc;padding-bottom:4px;">
										<b>Select the jurisdictions in which you wish to apply for credit.</b>
									</div>
									<table cellspacing="0" cellpadding="2" width="90%">
									<cfloop query="local.qryCreditBySeminar">
										<tr valign="top" bgcolor="#IIF(local.qryCreditBySeminar.currentrow mod 2 is 0,DE('dddddd'),DE('ffffff'))#">
											<td class="tsAppBodyText" width="20">
												<input class="tsAppBodyText creditcbox"  <cfif local.qryCreditBySeminar.isCreditRequired >data-isCredRequired="1"<cfelse> data-isCredRequired="0"</cfif> type="checkbox" value="#local.qryCreditBySeminar.seminarCreditID#" onClick="checkIDNum(this,#local.qryCreditBySeminar.isCreditRequired#)" id="frmcreditlink#local.qryCreditBySeminar.seminarCreditID#" name="frmcreditlink" <cfif local.qryCreditBySeminar.isCreditRequired or local.qryCreditBySeminar.isCreditDefaulted>checked</cfif>>
											</td>
											<td class="tsAppBodyText" style="line-height:1.4em;">
												<cfset local.qryCreditMatch = local.objCredit.getCreditsFromWDDX(local.qryCreditBySeminar.wddxCreditTypes,local.qryCreditBySeminar.wddxCreditsAvailable,true)>
												<b>#local.qryCreditBySeminar.authorityJurisdiction#</b> via #local.qryCreditBySeminar.sponsorName#<br/>
												#local.qryCreditBySeminar.status# credits: 
												<cfloop query="local.qryCreditMatch">
													<cfif local.qryCreditMatch.numcredits gt 0>#local.qryCreditMatch.numcredits# #local.qryCreditMatch.displayname#<cfif local.qryCreditMatch.currentrow is not local.qryCreditMatch.recordcount>, </cfif></cfif>
												</cfloop>
												<div id="dividnum#local.qryCreditBySeminar.seminarCreditID#" <cfif local.qryCreditBySeminar.isCreditRequired or local.qryCreditBySeminar.isCreditDefaulted><cfelse>style="display:none;"</cfif>>
													<cfif len(local.qryCreditBySeminar.creditIDText)>
														#local.qryCreditBySeminar.creditIDText#: 
														<input type="text"   name="frm#local.qryCreditBySeminar.seminarCreditID#ID" id="frm#local.qryCreditBySeminar.seminarCreditID#ID" value="" size="25" maxlength="50" class="tsAppBodyText frmTxt#local.qryCreditBySeminar.authorityID#" onKeyUp="showCopyDown(#local.qryCreditBySeminar.seminarCreditID#);"><a style="display:none;cursor: pointer;" id="frmlink#local.qryCreditBySeminar.seminarCreditID#ID" onClick="copyDown($('##frm#local.qryCreditBySeminar.seminarCreditID#ID'),#local.qryCreditBySeminar.authorityID#);">Apply to All </a>
														<cfif local.qryCreditBySeminar.isIDRequired>(required)</cfif>
													</cfif>
												</div>
											</td>
										</tr>
									</cfloop>
									</table>
									<br/>
								</div>	 
							</cfif>	                              
					</div>
				</div>
										
				
			</cfloop>
				
		<cfelse>

			<cfif local.strCredit.qryCreditDistinct.recordcount is 0>
				This program <i>has not been submitted</i> for credit in any jurisdiction. Registrants will receive a 
				Certificate of Attendance/Completion that may or may not meet credit requiremements in various jurisdictions. Where 
				applicable, credit will be only awarded to a paid registrant completing the program at their 
				own computer and phone.
				<br/><br/>
			<cfelse>
				<cfif local.qryCredRequired.recordcount is 0>
					If you are interested in earning credit for this program, select the appropriate response below.
					If you do not select credit now, your participation in this program may not be accepted for 
					credit at the conclusion of the program.
					<br/><br/>
					<input type="radio" value="0" name="creditSelect" onclick="hideCreditSelect()"> I am NOT interested in applying for credit for this program.<br/>
					<input type="radio" value="1" name="creditSelect" #local.radioChecked# onclick="showCreditSelect()"> I am interested in applying for credit for this program.<br/><br/>
					<div style="margin:0 20px;display:none;" id="creditDIV" class="creditSelContainer" data-linkedcreditselect="creditSelect">
				<cfelse>
					This program requires credit selection in one or more of the following jurisdictions. 
					These jurisdictions have been pre-selected for you.
					<div style="display:none;"><input type="radio" value="0" name="creditSelect"> <input type="radio" name="creditSelect" value="1" checked></div>
					<br/><br/>
					<div style="margin:0 20px;" id="creditDIV" class="creditSelContainer" data-linkedcreditselect="">
				</cfif>
			
				<div style="border-bottom:1px solid ##ccc;padding-bottom:4px;">
					<b>Select the jurisdictions in which you wish to apply for credit.</b>
				</div>
		
				<table cellspacing="0" cellpadding="2" width="90%">
				<cfloop query="local.strCredit.qryCredit">
					<tr valign="top" bgcolor="#IIF(local.strCredit.qryCredit.currentrow mod 2 is 0,DE('dddddd'),DE('ffffff'))#">
						<td class="tsAppBodyText" width="20">
							<input class="tsAppBodyText creditcbox" type="checkbox" value="#local.strCredit.qryCredit.seminarCreditID#" onClick="checkIDNum(this,#local.strCredit.qryCredit.isCreditRequired#)" id="frmcreditlink#local.strCredit.qryCredit.seminarCreditID#" name="frmcreditlink" <cfif local.strCredit.qryCredit.isCreditRequired or local.strCredit.qryCredit.isCreditDefaulted>checked</cfif>>
						</td>
						<td class="tsAppBodyText" style="line-height:1.4em;">
							<cfset local.qryCreditMatch = local.objCredit.getCreditsFromWDDX(local.strCredit.qryCredit.wddxCreditTypes,local.strCredit.qryCredit.wddxCreditsAvailable,true)>
							<b>#local.strCredit.qryCredit.authorityJurisdiction#</b> via #local.strCredit.qryCredit.sponsorName#<br/>
							#local.strCredit.qryCredit.status# credits: 
							<cfloop query="local.qryCreditMatch">
								<cfif local.qryCreditMatch.numcredits gt 0>#local.qryCreditMatch.numcredits# #local.qryCreditMatch.displayname#<cfif local.qryCreditMatch.currentrow is not local.qryCreditMatch.recordcount>, </cfif></cfif>
							</cfloop>
							<div id="dividnum#local.strCredit.qryCredit.seminarCreditID#" <cfif local.strCredit.qryCredit.isCreditRequired or local.strCredit.qryCredit.isCreditDefaulted><cfelse>style="display:none;"</cfif>>
								<cfif len(local.strCredit.qryCredit.creditIDText)>
									#local.strCredit.qryCredit.creditIDText#: 
									<input type="text" name="frm#local.strCredit.qryCredit.seminarCreditID#ID" id="frm#local.strCredit.qryCredit.seminarCreditID#ID" value="" size="25" maxlength="50" class="tsAppBodyText">
									<cfif local.strCredit.qryCredit.isIDRequired>(required)</cfif>
								</cfif>
							</div>
						</td>
					</tr>
				</cfloop>
				</table>
				
				</div>
				
			</cfif>
		</cfif>

		<div class="swreg-mt-5">
			<div id="step4Err" class="alert" style="display:none;"></div>
			<button type="button" name="btnSaveStep4" id="btnSaveStep4" class="tsAppBodyButton" onclick="doS4ValidateAndSave();" disabled>
				<cfif local.swReg.currentReg.isRegCartItem EQ 1>
					Save Changes
				<cfelse>
					<i class="icon-arrow-right"></i> Continue
				</cfif>
			</button>
		</div>
		<div id="swRegStep4SaveLoading" style="display:none;"></div>
	</form>
	<div id="swRegStep4Summary" class="swRegStepSummary swreg-cursor-pointer" data-swregsummarystep="4"<cfif local.swReg.currentReg.currentStep EQ 4> style="display:none;"</cfif>>
		<div class="swreg-d-flex">
			<a href="##" class="swreg-align-self-center swreg-mr-2 swreg-font-size-lg swreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="swreg-col">
				<div class="swreg-font-size-lg swreg-font-weight-bold">Credit Selection</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>
