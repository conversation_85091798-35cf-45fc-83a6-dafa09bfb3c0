<cfoutput>
<div class="form-group row-fluid">
	<div class="span12" style="text-align:left;">
		<h4><b>My Outstanding Fees</b></h4>
	</div>
</div>
<div class="form-group row-fluid step1">
	<div class="span12">
		<form class="form-inline refFilterForm" action="/" method="get">
			<input type="hidden" name="pg" value="referrals">
			<input type="hidden" name="panel" value="browse">
			<input type="hidden" name="tab" value="#variables.structData.tabToShow#">
			<input type="hidden" name="sort" value="#variables.structData.sort#">
			<input type="hidden" name="count" value="#variables.structData.count#">
			<input type="hidden" name="orderBy" value="#variables.structData.orderBy#">
			<input type="hidden" name="start" value="1">
			<input type="hidden" name="checkedAll" id="checkedAll" value="0">
			<label class="refDateRange"><br/>
				<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
					<option <cfif variables.structData.refDateRanges eq 0 > selected </cfif> value="0">My Outstanding Fees</option>
					<option <cfif variables.structData.refDateRanges eq 30 > selected </cfif>  value="30">Last 30 Days</option>
					<option <cfif variables.structData.refDateRanges eq 60 > selected </cfif> value="60">Last 60 Days</option>
					<option <cfif variables.structData.refDateRanges eq 90 > selected </cfif> value="90">Last 90 Days</option>
				</select>
			</label>
			<label class="refferalID"><b>Referral ID</b><br/>
				<input type="text" name="filterReferralID" class="input-small refferalID" <cfif variables.structData.filterReferralID GT 0> value="#variables.structData.filterReferralID#" </cfif>>
			</label>
			<label class="clientLastName"><b>Client Last Name</b><br/>
				<input type="text" name="filterClientLastName" class="input-medium clientLastName" value="#variables.structData.filterClientLastName#">
			</label>
			<label class="clientFirstName"><b>Client First Name</b><br/>
				<input type="text" name="filterClientFirstName" class="input-medium clientFirstName" value="#variables.structData.filterClientFirstName#">
			</label>								
			<button type="submit" class="btn filterRefferals">Search</button>
			<button type="reset" class="btn filterClear">Clear Filters</button>
		</form>
	</div>							
</div>

<div class="form-group row-fluid step1 <cfif arrayLen(variables.structData.rfpData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>	
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>		
</div>
<cfif arrayLen(variables.structData.rfpData) gt 0> 
	<div class="dataResp step1">
		<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.rfpData)#">
			<div class="row-fluid" bgcolor="##DEDEDE">
				<div class="span12 clearfix well well-small">
					<div class="row-fluid">
						<div class="span8 eventLeft list-text">			
							<p>														
								<input type="checkbox" name="checkRef" class="checkRef" data-dues="#variables.structData.rfpData[local.Counter].totalFeesDue#" data-crid="#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#" id="checkRef#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#">
								<a href="javascript:#val(variables.structData.rfpData[local.Counter].caseID) GT 0 ? 'editCase' : 'editReferral'#(#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>Referral ##  #variables.structData.rfpData[local.Counter].CLIENTREFERRALID#</b></a>
							</p>
							<p><b>Client Name :</b>  #variables.structData.rfpData[local.Counter].CLIENTNAME#</p>													
							<p><b>Referral Date :</b> #variables.structData.rfpData[local.Counter].clientReferralDate#</p>
							<p><b>Status :</b> #variables.structData.rfpData[local.Counter].clientReferralDate#</p>
							<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
								<p><b>Client Fees :</b> #dollarFormat(variables.structData.rfpData[local.Counter].clientFeeDue)#</p>
							</cfif>
							<p><b>Referral Fees :</b> #dollarFormat(variables.structData.rfpData[local.Counter].amtToBePaidTotal)#</p>
							<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
								<p><b>Total Fees Due :</b> #dollarFormat(variables.structData.rfpData[local.Counter].totalFeesDue)#</p>
							</cfif>
						</div>
						<div class="span4 " style="text-align:right">
							<cfif variables.structData.rfpData[local.Counter].amtToBePaidTotal gt 0>
								<a href="javascript:applyPayment(#variables.structData.rfpData[local.Counter].clientReferralID#)" title="Apply Payment to this Case" ><i class="icon-money"></i> </a>
							<cfelse>
								<a href="javascript:void(0);"  ><img src="/assets/common/images/spacer.gif" /> </a>
							</cfif>	
							<a href="javascript:sendStatement(#variables.structData.rfpData[local.Counter].clientReferralID#)" title="View and E-mail Statement" ><i class="icon-envelope"></i> </a>
						</div>
					</div>
				</div>
			</div>
		</cfloop>	
	</div>
	<div class="row-fluid  dataTable hide step1">
		<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
			<tr class="gridHead">
				<td>
					<input type="checkbox" class="checkAll">
				</td>

				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referralid">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				
				<td class="sortColumn" data-sort="referralid" data-order="#local.order#"><b>Referral ID##</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "clientname">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				
				<td class="sortColumn" data-sort="clientname" data-order="#local.order#"><b>Client Name</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referraldate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="referraldate" data-order="#local.order#"><b>Referral Date</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "collectedfromdate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="collectedfromdate" data-order="#local.order#"><b>Status</b>&nbsp;<i class="fa fa-sort"></i></td>

				<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
					<cfset local.order = 'desc'>
					<cfif variables.structData.sort EQ "clientfeesdue">
						<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
					</cfif>
					<td class="sortColumn" data-sort="clientfeesdue" data-order="#local.order#"><b>Client Fees</b>&nbsp;<i class="fa fa-sort"></i></td>
				</cfif>

				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "feesdue">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="feesdue" data-order="#local.order#"><b>Referral Fees</b>&nbsp;<i class="fa fa-sort"></i></td>

				<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
					<td><b>Total Fees Due</b></td>
				</cfif>
				
			</tr>
			<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.rfpData)#">
				<tr class="gridData lgScreen">
					<td>
						<input type="checkbox" name="checkRef" class="checkRef" data-dues="#variables.structData.rfpData[local.Counter].totalFeesDue#" data-crid="#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#" id="checkRef#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#">
					</td>
					
					<td>
						<a href="javascript:#val(variables.structData.rfpData[local.Counter].caseID) GT 0 ? 'editCase' : 'editReferral'#(#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>#variables.structData.rfpData[local.Counter].CLIENTREFERRALID#</b></a>
					</td>
					<td>
						#variables.structData.rfpData[local.Counter].CLIENTNAME#
					</td>
					<td>
						#variables.structData.rfpData[local.Counter].clientReferralDate#
					</td>
					<td>
						<cfif NOT IsNull(variables.structData.rfpData[local.Counter].statusName)>
							#variables.structData.rfpData[local.Counter].statusName#
						</cfif>
					</td>
					<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
						<td>#dollarFormat(variables.structData.rfpData[local.Counter].clientFeeDue)#</td>
					</cfif>
					<td>#dollarFormat(variables.structData.rfpData[local.Counter].amtToBePaidTotal)#</td>
					<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
						<td>#dollarFormat(variables.structData.rfpData[local.Counter].totalFeesDue)#</td>
					</cfif>
				</tr>
			</cfloop>
		</table>
	</div>
</cfif>	
<div class="form-group row-fluid step1 <cfif arrayLen(variables.structData.rfpData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>		
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>		
</div>
<div class="row-fluid step1 <cfif arrayLen(variables.structData.rfpData) gt 0> hide</cfif> noReferrals" bgcolor="##DEDEDE">
	<div class="span12 clearfix well well-small">
		<div class="row-fluid">
			<p>No Referrals found.</p>
		</div>
	</div>
</div>
<cfif arrayLen(variables.structData.rfpData) gt 0>

	<!--- prompt for missing tax information --->
	<cfif variables.structData.stateIDforTax EQ 0 OR NOT len(variables.structData.zipForTax)>
		<fieldset id="billingInfoContainer" class="step1">
			<legend>Billing Information</legend>
			<div style="display:flex;">
				<div>
					<div>State/Province *</div>
					<cfset local.qryStates = application.objCommon.getStates()>
					<select id="stateIDForTax" name="stateIDForTax">
						<option value=""></option>
						<cfset local.currentCountryID = 0>
						<cfloop query="local.qryStates">
							<cfif local.qryStates.countryID neq local.currentCountryID>
								<cfset local.currentCountryID = local.qryStates.countryID>
								<optgroup label="#local.qryStates.country#">
							</cfif>
							<option value="#local.qryStates.stateID#" <cfif variables.structData.stateIDforTax is local.qryStates.stateID>selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
							<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
								</optgroup>
							</cfif>
						</cfloop>
					</select>
				</div>
				<div style="padding-left:20px;">
					<div>Postal Code *</div>
					<input type="text" id="zipForTax" name="zipForTax" maxlength="25" value="#variables.structData.zipForTax#">
				</div>
			</div>
		</fieldset>
	<cfelse>
		<input type="hidden" id="stateIDForTax" name="stateIDForTax" value="#variables.structData.stateIDforTax#">
		<input type="hidden" id="zipForTax" name="zipForTax" value="#variables.structData.zipForTax#">
	</cfif>

	<fieldset class="dataFieldSet mt-30 step1 makePaymentSection">
		<div class="table-responsive">
			<table class="table" width="100%">	
				<tbody>
					<tr class="odd_modern">
						<td class="text-right" >
							Current Total: <span id="currentTotal">00.00</span>
						</td>
					</tr>
					<tr class="odd_modern">
						<td class="text-right" colspan="2">
							<button type="button" id="paymentBtn" class="btn">Make a payment</button>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</fieldset>	
	<div class="paymentWrap">
		
	</div>
</cfif>
</cfoutput>