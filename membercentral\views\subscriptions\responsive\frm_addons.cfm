<cfoutput>
<cfloop collection="#local.addOns#" item="local.addOnID">
	<cfset local.thisSubAddOn = duplicate(local.addOns[local.addOnID])>
	<cfset local.thisAddOnSubsCount = arrayLen(local.thisSubAddOn.subscriptions)>
	
	<cfset local.recursionLevel = 0>
	<cfif structKeyExists(local.thisSubAddOn, 'recursionlevel')>
		<cfset local.recursionLevel = local.thisSubAddOn.recursionLevel>
	</cfif>

	<cfset local.selectionMessage = "">
	<cfif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0 AND local.thisAddOnSubsCount eq 1>
		<cfset local.selectionMessage = 'You may optionally select this item:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0>
		<cfset local.selectionMessage = 'You may optionally select one or more of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed is 1>
		<cfset local.selectionMessage = 'You may optionally select only one of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed GT 0>
		<cfset local.selectionMessage = 'You may optionally select up to #min(local.thisSubAddOn.strAddOn.maxAllowed,local.thisAddOnSubsCount)# of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed is 1 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0>
		<cfset local.selectionMessage = 'You must select at least one of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed GT 0 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0>
		<cfset local.selectionMessage = 'You must select at least #local.thisSubAddOn.strAddOn.minAllowed# of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed is 1 AND local.thisSubAddOn.strAddOn.maxAllowed is 1>
		<cfset local.selectionMessage = 'You must select only one of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed gt 1 AND local.thisSubAddOn.strAddOn.minAllowed eq local.thisSubAddOn.strAddOn.maxAllowed>
		<cfset local.selectionMessage = 'You must select #min(local.thisSubAddOn.strAddOn.maxAllowed,local.thisAddOnSubsCount)# of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed gt 0 AND local.thisSubAddOn.strAddOn.maxAllowed GT 0>
		<cfset local.selectionMessage = 'You must select #local.thisSubAddOn.strAddOn.minAllowed# to #min(local.thisSubAddOn.strAddOn.maxAllowed,local.thisAddOnSubsCount)# of the following:'>
	</cfif>

	<cfset local.addOnCardClasses = "mcsubs-mb-2">
	<cfset local.addOnCardBodyClasses = "subAddOnFormContainer sub#arguments.subscriptionID#_addons">
	<cfset local.addOnCardTitleClasses = "mcsubs-d-flex mcsubs-pb-1 mcsubs-border-bottom mcsubs-border-lightgray sub#arguments.subscriptionID#_addonID#local.addOnID#_titleContainer subAddOn#local.addOnID#TitleContainer">

	<cfif arguments.inlineAddOn>
		<cfset local.addOnCardClasses = "#local.addOnCardClasses# inlineAddOn">
		<cfset local.addOnCardBodyClasses = "#local.addOnCardBodyClasses# mcsubs-pl-4">
		<cfset local.addOnCardTitleClasses = "#local.addOnCardTitleClasses# innerAddOnTitleContainer">
		<cfset local.addOnCardDataAttributes = 'data-setname="#encodeForHTMLAttribute(local.thisSubAddOn.strAddOn.setname)#"'>
	<cfelse>
		<cfset local.addOnCardClasses = "#local.addOnCardClasses# mcsubs-mt-2 mcsubs-card addOnCards mainSubCard">
		<cfset local.addOnCardBodyClasses = "#local.addOnCardBodyClasses# mcsubs-card-body addOnCardBody mcsubs-d-none">
		<cfset local.addOnCardDataAttributes = 'data-errfields="" data-setname="#encodeForHTMLAttribute(local.thisSubAddOn.strAddOn.setname)#" 
												data-linkedsummarycontainer="sub#arguments.subscriptionID#_addonID#local.addOnID#_summary" 
												data-linkedformcontainer="sub#arguments.subscriptionID#_addonID#local.addOnID#_formWrapper" 
												data-linkedtitlecontainer="subAddOn#local.addOnID#TitleContainer" 
												data-linkedtitleerrorcontainer="addOnTitleAlert"'>
	</cfif>

	<div id="sub#arguments.subscriptionID#_addonID#local.addOnID#_card" class="#local.addOnCardClasses#" #local.addOnCardDataAttributes#>
		<div id="sub#arguments.subscriptionID#_addonID#local.addOnID#_formWrapper" 
			class="#local.addOnCardBodyClasses#"
			data-addonid="#local.addOnID#"
			data-subscriptionid="#arguments.subscriptionID#" 
			data-childsetid="#local.thisSubAddOn.strAddOn.setID#" 
			data-minallowed="#local.thisSubAddOn.strAddOn.minAllowed#"
			data-maxallowed="#local.thisSubAddOn.strAddOn.maxAllowed#"
			data-itemsfree="#local.thisSubAddOn.strAddOn.PCNum#"
			data-displaypriceelement="subAddOn#local.addOnID#TotalPrice">
			
			<div class="#local.addOnCardTitleClasses#">
				<div id="subSet#local.thisSubAddOn.strAddOn.setID#" 
					class="mcsubs-font-weight-bold mcsubs-col" 
					data-setname="#encodeForHTMLAttribute(local.thisSubAddOn.strAddOn.setname)#" 
					data-recursionlevel="#local.recursionLevel#"
					data-inlineaddon="#arguments.inlineAddOn ? 1 : 0#">
					<span<cfif arguments.inlineAddOn> class="mcsubs-font-size-md"</cfif>>#local.thisSubAddOn.strAddOn.setname#</span>
					<cfif local.thisSubAddOn.strAddOn.minAllowed GT 0>
						<span class="mcsubs-text-danger">*</span>
					</cfif>
					<cfif NOT arguments.inlineAddOn>
						<span class="subAddOn#local.addOnID#TotalPrice mcsubs-text-nowrap"></span>
					</cfif>
					<span class="mcsubs-text-normal selectedSubsCount#local.addOnID# mcsubs-ml-2 mcsubs-font-size-sm mcsubs-text-nowrap"></span>
					<div class="addOnTitleAlert mcsubs-font-size-sm"></div>
				</div>
				<cfif NOT arguments.inlineAddOn>
					<a href="##" class="btn btn-link mcsubs-text-danger visible-phone subCardCancelBtn mcsubs-d-none" onclick="cancelSubAddOnChanges(#local.addOnID#,#arguments.subscriptionID#);return false" title="Cancel">
						<i class="fa fa-times" aria-hidden="true"></i>
					</a>
				</cfif>
			</div>
			<div class="mcsubs-mt-2">
				<div class="addOnSubFrontEndContent">
					#local.thisSubAddOn.strAddOn.frontEndContent#
				</div>
				<cfif structKeyExists(local.thisSubAddOn, 'subscriptions') AND local.thisAddOnSubsCount>
					<cfif len(local.selectionMessage)>
						<div class="mcsubs-mb-3 mcsubs-font-weight-bold mcsubs-font-size-sm addOnSelMsg">#local.selectionMessage#</div>
					</cfif>
					<div class="mcsubs-mt-3 subsContainer">
						#renderSubscriptionsForm(arrSubs=local.thisSubAddOn.subscriptions, strAddOn=local.thisSubAddOn.strAddOn, parentSubscriptionID=arguments.subscriptionID, 
							strParentFreq=arguments.strParentFreq, recursionlevel=local.recursionLevel, freeRateDisplay=arguments.freeRateDisplay, strEditSubs=arguments.strEditSubs, 
							viewDirectory=arguments.viewDirectory)#
					</div>
					<cfif NOT arguments.inlineAddOn AND local.thisSubAddOn.strAddOn.frontEndAddAdditional EQ 1 AND local.thisSubAddOn.strAddOn.frontEndAllowSelect EQ 1>
						<div class="mcsubs-d-flex addOnFooterContainer mcsubs-mt-3 mcsubs-border-lightgray mcsubs-border-top mcsubs-pt-2 mcsubs-flex-sm-column">
							<div class="mcsubs-col-auto">
								<button type="button" class="btn btn-primary mcsubs-btn125 mcsubs-w-sm-100 subCardConfirmBtn" onclick="confirmSubAddOn(#local.addOnID#,#arguments.subscriptionID#,true);return false;">
									Confirm
								</button>
							</div>
							<div class="mcsubs-col-auto">
								<button type="button" name="btnCancelAddOnChanges" class="btn btn-default hidden-phone subCardCancelBtn mcsubs-d-none" onclick="cancelSubAddOnChanges(#local.addOnID#,#arguments.subscriptionID#);">Cancel</button>
							</div>
							<div class="addOnBottomAlert mcsubs-mt-sm-2"></div>
						</div>
					</cfif>
				</cfif>
			</div>
		</div>
		<cfif NOT arguments.inlineAddOn>
			<div id="sub#arguments.subscriptionID#_addonID#local.addOnID#_summary" class="mcsubs-card-body subCardSummary">
				<div class="mcsubs-d-flex mcsubs-pb-1 mcsubs-border-bottom mcsubs-border-lightgray">
					<div class="mcsubs-font-weight-bold mcsubs-col">
						<span>#local.thisSubAddOn.strAddOn.setname#</span>
						<cfif local.thisSubAddOn.strAddOn.minAllowed GT 0>
							<span class="mcsubs-text-danger">*</span>
						</cfif>
						<span class="subAddOn#local.addOnID#TotalPrice mcsubs-text-nowrap"></span> 
						<span class="mcsubs-text-normal selectedSubsCount#local.addOnID# mcsubs-ml-2 mcsubs-font-size-sm mcsubs-text-nowrap"></span>
					</div>
				</div>
				<div class="mcsubs-mt-2">
					#local.thisSubAddOn.strAddOn.frontEndContent#
					<div class="addOnSummary">
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
					</div>
					<cfif local.thisSubAddOn.strAddOn.frontEndAddAdditional EQ 1 AND local.thisSubAddOn.strAddOn.frontEndAllowSelect EQ 1>
						<div class="mcsubs-mt-3">
							<button type="button" data-editmode="editsubaddon" data-addonid="#local.addOnID#" data-subscriptionid="#arguments.subscriptionID#" class="btn btn-default mcsubs-btn125 mcsubs-w-sm-100 subCardEditBtn mcsubs-d-none"></button>
						</div>
					</cfif>
				</div>
			</div>
		</cfif>
	</div>

	<!--- Subscription AddOns --->
	<cfloop from="1" to="#arrayLen(local.thisSubAddOn.subscriptions)#" index="local.i">
		<cfset local.thisSub = duplicate(local.thisSubAddOn.subscriptions[local.i])>
			
		<cfif structKeyExists(local.thisSub,'addOns') AND StructCount(local.thisSub.addOns) AND local.thisSub.currAddOnRecursionLevel + 1 NEQ local.thisSub.maxAddOnRecursionLevel>
			<cfset local.thisSubSelected = structKeyExists(local.thisSub,'isSelected') and local.thisSub.isSelected>

			<div id="sub#local.thisSub.subscriptionID#_addons"<cfif NOT local.thisSubSelected> class="mcsubs-d-none"</cfif>>
				#renderSubscriptionAddOnForm(strSubAddOns=local.thisSub.addOns, subscriptionID=local.thisSub.subscriptionID, strParentFreq=arguments.strParentFreq,
					freeRateDisplay=arguments.freeRateDisplay, strEditSubs=arguments.strEditSubs, viewDirectory=arguments.viewDirectory, inlineAddOn=false)#
			</div>
		</cfif>
	</cfloop>
</cfloop>
</cfoutput>