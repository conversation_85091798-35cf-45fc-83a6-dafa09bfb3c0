<cfsavecontent variable="local.evRegStep3JS">
	<cfoutput>
	<cfif local.strEventSpecificFields.hasFields>
		#local.strEventSpecificFields.head#
	</cfif>
	<script type="text/javascript">
		function doS3ValidateAndSave() {
			$('##btnSaveStep3').html('<i class="icon icon-spinner"></i> Please wait..').prop('disabled',true);
			var strErr = '';
			var errorMsgArray = [];
			var emailRegEx = new RegExp("#application.regEx.email#", "gi");

			if ($('##regEmail').length) {
				if ($('##regEmail').val().trim() == '' || !(emailRegEx.test($('##regEmail').val().trim()))) {
					strErr += '#JSStringFormat('Enter a valid E-mail Address.')#<br/>';
					$('##regEmail_err').html('Enter a valid E-mail Address.').show();
				} else {
					$('##regEmail_err').html('').hide();
				}
			}

			/*required fields*/
			var evReg_fldsRequired = $('##frmEventRegStep3').find('input:text[data-IsRequired="1"], input:file[data-IsRequired="1"], select[data-IsRequired="1"], textarea[data-IsRequired="1"]').not(':disabled').not(':hidden');

			/*distinct radio, checkbox elements*/
			var radioCheckBoxElements = $('##frmEventRegStep3').find('input:radio[data-IsRequired="1"], input:checkbox[data-IsRequired="1"]').not(':hidden');
			
			var elemArr = [];
			$.each( radioCheckBoxElements, function() {
				var elemName = this.name;
				if( $.inArray( elemName, elemArr ) < 0 ){
					elemArr.push(elemName);
					evReg_fldsRequired.push(this);
				}
			});

			var evReg_fldsRequiredErrorMsgArray = $.map(evReg_fldsRequired,evReg_validate_fieldIsRequired);
			Array.prototype.push.apply(errorMsgArray, evReg_fldsRequiredErrorMsgArray);

			var integerTextControls = $('##frmEventRegStep3').find('input[data-displayTypeCode="TEXTBOX"][data-dataTypeCode="INTEGER"]').not(':disabled').not(':hidden');
			var integerTextControlsErrorMsgArray = $.map(integerTextControls,evReg_validate_textControlValidInteger);
			Array.prototype.push.apply(errorMsgArray, integerTextControlsErrorMsgArray);

			var decimalTextControls = $('##frmEventRegStep3').find('input[data-displayTypeCode="TEXTBOX"][data-dataTypeCode="DECIMAL2"]').not(':disabled').not(':hidden');
			var decimalTextControlsErrorMsgArray = $.map(decimalTextControls,evReg_validate_textControlValidDecimal);
			Array.prototype.push.apply(errorMsgArray, decimalTextControlsErrorMsgArray);

			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return thisError;
				else return null;
			});
			
			strErr += finalErrors.join('<br/>');

			/* billing zip */
			if (strErr.length == 0 && !mc_isValidBillingZip($('##zipForTax').val(),$('##stateIDforTax').val(),'')) {
				$('##zipForTax_err').html('Invalid Billing Postal Code').show();
				strErr = 'Invalid Billing Postal Code';
			}

			return new Promise(function(resolve, reject) {
				if (strErr.length > 0) {
					if ($('##frmEventRegStep3').is(':hidden')) editRegStep3();
					$('##step3Err').html('Please complete all the required fields.').show();
					$('##btnSaveStep3').html('<i class="icon-arrow-right"></i> Continue').prop('disabled',false);
					reject('Please complete all the required fields in Other Details and Options.');
					return false;
				} else {
					$('##step3Err').html('').hide();
					$('##evRegStep3SaveLoading')
						.html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
						.load('#arguments.event.getValue('evregresourceurl')#&regaction=saves3', $('##frmEventRegStep3').serializeArray(),
							function(resp) {
								let respObj = JSON.parse(resp);
								let arrSteps = respObj.loadsteps.split(',');
								$('##evRegStep3TotalPriceDisplay').html(respObj.totalamtdisplay);
								$('##frmEventRegStep3').hide();
								$('##btnSaveStep3').html('<i class="icon-arrow-right"></i> Continue').prop('disabled',false);
								$('##evRegStep3Summary').show(300);
								$('html, body').animate({
									scrollTop: $('##EvRegStep3').offset().top - 175
								}, 750);
								arrSteps.forEach(function(step,index) {
									loadEventRegSteps(step);
								});
								resolve();
							}
						);
				}
			});
		}
		function editRegStep3() {
			$('##evRegStep3Summary').hide();
			$('##frmEventRegStep3').show(300);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evRegStep3JS)#">