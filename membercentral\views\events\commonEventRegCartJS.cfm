<cfsavecontent variable="local.regCartJS">
	<cfoutput>
	<script type="text/javascript">
		<cfset local.eventsJSON = SerializeJSON(local.eventList)>
		<cfif IsJSON(local.eventsJSON)>
			try {
				var parsedEventsJSON = #local.eventsJSON#; 
			} catch(e) {
				console.error("Error parsing JSON:", e.message);
			};
		</cfif>
		function hideAlert() { $('##evRegPmtErr').html('').hide(); };
		function showAlert(msg) { $('##evRegPmtErr').html(msg).attr('class','alert').show(); };

		function registerOthers(eid) {
			window.location.href = '#arguments.event.getValue('mainurl')#&eid=' + eid + '&evaction=regV2';
		}
		function editReg(eid,rk) {
			$('##edit_evrk_'+rk).html('<i class="icon-spin icon-spinner"></i>');
			window.location.href = '#arguments.event.getValue('mainurl')#&eid=' + eid + '&evaction=regV2&regaction=editRegCartItem&evrk=' + rk;
		}
		function removeReg(rk) {
			var msg = 'Are you sure you want to remove this pending registration?';
			if (confirm(msg)) {
				var resultEventJSON = parsedEventsJSON.filter(function(item) {
					return item.itemKey === rk;
				});
				<cfif isDefined("local.qryEvRegCartTotals")>
					triggerEventsCart(resultEventJSON,#local.qryEvRegCartTotals.actualTotal#, 'remove');
				</cfif>
				$('##del_evrk_'+rk).html('<i class="icon-spin icon-spinner"></i>');
				window.location.href = '#arguments.event.getValue('mainurl')#&evaction=regV2&regaction=remreg&evrk=' + rk;
			}
		}
		function showHideOverSoldItemPanel(rk) {
			$('div##overSoldItemAccordion'+rk+' div.issuePanel').toggle();
			$('div##overSoldItemAccordion'+rk+' span.showOverSoldItems').toggle();
			$('div##overSoldItemAccordion'+rk+' span.hideOverSoldItems').toggle();
		}
		function selectPayment(pid) {
			hideAlert();
			$('##profileid').val(pid);
		}
		function evRegMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
						var iframeWin = document.querySelector('iframe[name=iframeManageForm'+event.data.profileid+']').contentWindow;
						var message = { success:true, 
										messagetype:"MCFrontEndPaymentEvent", 
										paymentbuttonname:$('##divBtnWrapper'+event.data.profileid+' button[type="submit"]').text(), 
										profileid:event.data.profileid };
						iframeWin.postMessage(message, event.origin);
						break;

					case "mcgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.payprofileid) {
							showEvRegPaymentProcessing(event.data.profileid);
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmPurchaseEvReg');
							setEvRegPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchaseEvReg').submit();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							showEvRegPaymentProcessing(event.data.profileid);
							setEvRegPaymentTokenData(event.data.tokendata,event.data.profileid);
							setEvRegPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchaseEvReg').submit();
						}
						break;
				};

			} else {
				return false;
			}
		}
		function showEvRegPaymentProcessing(pid) {
			$('##divPaymentTable' + pid)
				.html('<i class="icon icon-spinner"></i> Please Wait...')
				.css({'height':'75px', 'padding':'5px'});
		}
		function setEvRegPaymentProcessingFeesField(obj) {
			if (typeof obj.enableprocessingfee != "undefined") {
				let processFeeDonationElement = $('##processFeeDonation'+obj.profileid);
				if (processFeeDonationElement.length) {
					processFeeDonationElement.val(obj.enableprocessingfee);
				} else {
					$('<input>').attr({ type: 'hidden', name: 'processFeeDonation'+obj.profileid, id: 'processFeeDonation'+obj.profileid }).val(obj.enableprocessingfee).appendTo('form##frmPurchaseEvReg');
				}
			}
		}
		function setEvRegPaymentTokenData(tokendata,profileid) {
			let tokenDataElement = $('##p_'+profileid+'_tokenData');
			if (tokenDataElement.length) {
				tokenDataElement.val(JSON.stringify(tokendata));
			} else {
				$('<input>').attr({ type: 'hidden', name: 'p_'+profileid+'_tokenData', id: 'p_'+profileid+'_tokenData' }).val(JSON.stringify(tokendata)).appendTo('form##frmPurchaseEvReg');
			}
		}
		function validateCouponCodeOnEnterKey(event){
			/* Number 13 is the "Enter" key on the keyboard */
			if (event.keyCode === 13) {
				/* Cancel the default action, if needed */
				event.preventDefault();
				validateCouponCode();
			}
		}
		function validateCouponCode() {
			var validateResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.isvalidcoupon.toString().toLowerCase() == 'true') {
						self.location.href = '#arguments.event.getValue('mainurl')#&regcartv2';
					} else {
						$('##couponCodeResponse').html(r.couponresponse).show();
						$('##btnApplyCouponCode').prop('disabled',false).text('Apply');
					}
					
				} else {
					$('##btnApplyCouponCode').prop('disabled',false).text('Apply');
					$('##couponCode').val('');

					if (r.success && r.success.toLowerCase() == 'false' && r.couponresponse) {
						$('##couponCodeResponse').html(r.couponresponse).show();
					} else {
						$('##couponCodeResponse').html('Unable to apply coupon code. Try again.').show();
					}
				}
			};

			var couponCode = $('##couponCode').val().trim();

			if (couponCode.length) {
				$('##btnApplyCouponCode').prop('disabled',true).text('Applying...');
				$('##couponCodeResponse').html('').hide();

				var objParams = { couponCode:couponCode };
				TS_AJX('EVREGV2','validateCouponCode',objParams,validateResult,validateResult,10000,validateResult);
			} else {
				validateResult({ success:'false', couponresponse:'Invalid Promo Code' });
			}
		}
		function removeAppliedCoupon() {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					self.location.href = '#arguments.event.getValue('mainurl')#&regcartv2';
				} else {
					$('.btnRemoveCoupon').prop('disabled',false).text('Remove Promo Code');
					alert('Unable to remove applied coupon. Try again.');
				}
			};

			$('.btnRemoveCoupon').prop('disabled',true).text('Removing...');
			TS_AJX('EVREGV2','removeAppliedCoupon',{},removeResult,removeResult,10000,removeResult);
		}
		function onchangeEvRegPaymentTabHandler(pid) {
			$('.evRegCartTotals').hide();
			$('.evRegCartItemResponse').html('').hide();
			$('.evRegCartItemTotal').removeClass('evreg-opacity-1');

			let nonmatchingitemkeys = $('##divPaymentTable'+pid).data('nonmatchingitemkeys');
			if (nonmatchingitemkeys && nonmatchingitemkeys.length) {
				let arrNonMatchingItems = nonmatchingitemkeys.split(',');
				
				arrNonMatchingItems.forEach(function(rk,index) {
					$('##evRegKey' + rk + ' .evRegCartItemResponse').html('This pending registration does not accept this payment method.').show();
					$('##evRegKey' + rk + ' .evRegCartItemTotal').addClass('evreg-opacity-1');
				});

				$('##evRegCartTotalSummary'+pid).show();

			} else {
				$('##evRegCartTotalSummary').show();
			}
		}
		
		$(function() {
			<cfif arguments.event.valueExists('perr')>
				showAlert('There was a problem processing the payment for this purchase.<br/>#JSStringFormat(HTMLEditFormat(arguments.event.getValue("perr")))#');
			</cfif>

			if (window.addEventListener) {
				window.addEventListener("message", evRegMessageHandler, false);
			} else if (window.attachEvent) {
				window.attachEvent("message", evRegMessageHandler);
			}

			<cfif arguments.event.getValue('viewDirectory','default') EQ 'default'>
				initCollapsibleDivSet('paymentTypePills');

				$('##paymentTypeTabs li.tsAppNavButton a.paymentTypePills').on('click',function() {
					onchangeEvRegPaymentTabHandler($(this).data('mccollapsibledivshow').replace('profile',''));
				});

				if ($('##paymentTypeTabs li.tsAppNavButton a.paymentTypePills').length) {
					onchangeEvRegPaymentTabHandler($('##paymentTypeTabs li.tsAppNavButton a.paymentTypePills:first').data('mccollapsibledivshow').replace('profile',''));
				}
			<cfelse>
				$('##evRegPmtTabs a[data-toggle="tab"]').on('shown', function (e) {
					onchangeEvRegPaymentTabHandler($(e.target).attr('href').replace('##profile',''));
				});

				if ($('##evRegPmtTabs a[data-toggle="tab"]').length) {
					onchangeEvRegPaymentTabHandler($('##evRegPmtTabs a[data-toggle="tab"]:first').attr('href').replace('##profile',''));
				}
			</cfif>
			
			<cfif IsJSON(local.eventsJSON) and isDefined("local.qryEvRegCartTotals")>
				MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggerEventsCart(parsedEventsJSON,#local.qryEvRegCartTotals.actualTotal#, 'view');
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
	</script>
	<style type="text/css">
		div.issuePanel { font-size:13px; padding-top:10px; }
		div.issuePanel table th, div.issuePanel table td { padding-top:10px; }
		span.showOverSoldItems, span.hideOverSoldItems { cursor:pointer; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.regCartJS)#">