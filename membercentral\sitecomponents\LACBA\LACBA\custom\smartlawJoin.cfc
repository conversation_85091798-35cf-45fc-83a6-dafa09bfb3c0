<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=smartlawJoin";
            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			
			variables.professionalInformationFieldSetUID = "F6219A47-3FB0-4377-A4AA-3DBC7068A034";
			variables.primaryAddressFieldSetUID = "02B376BB-F5F2-40A9-AF87-8B91111B67B8";
			variables.secondaryAddressFieldSetUID = "8E53E36E-41A0-4096-A068-CAF92835CF43";
			variables.tertiaryAddressFieldSetUID = "FE7E3BCF-FE8D-4441-A398-7B31ACEBDDF2";
			variables.addressPreferencesFieldSetUID = "*************-494E-9813-BB62A7610024";
			variables.emailFieldSetUID = "B021019C-C2E0-450D-A54F-20A52C8456F4";
			variables.emailAddressPreferencesFieldSetUID = "580FAF98-F9F8-4221-BC86-4F7ADE5B27B9";
			variables.insuranceInformationFieldSetUID = "FC34389A-43E2-4FDA-8BA8-644CE1A52F12";
			variables.disciplineFieldSetUID = "4C0E8936-844A-4301-828F-768B8FEBE970";
			variables.clientSuitsFieldSetUID = "802334DB-28A9-458D-ACC7-F4F7B7120DE0";
			variables.feeDisputesFieldSetUID = "DECBAD5E-DE28-4AE0-9664-B70EFC42E6B1";
			variables.educationInformationFieldSetUID = "429F0BA6-BD70-49E8-940E-DBACDB10D236";
			variables.additionalProfessionalDemographicDataFieldSetUID = "D3E2D943-CA0E-44F9-9EC8-8021329D861B";
			variables.smartlawSourceFieldSetUID = "38B8DD05-3054-4B5E-A64C-341B1CFDABEE";

            variables.currentDate = dateTimeFormat(now());

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];

            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			    arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="20DD661E-5C22-46C4-9DDF-6229C2B5243D" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a member of our Smartlaw service. If you have questions about your membership, please call ************." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Smartlaw Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TitleContent",type="CONTENTOBJ",desc="Content below top content of page 3",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3AdditionalContent",type="CONTENTOBJ",desc="Content midway through page 3",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step4TopContent",type="CONTENTOBJ",desc="Content at top of page 4",value="Step 4 - Please review your selections and proceed with payment." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalLicContent",type="CONTENTOBJ",desc="Content at top of Professional License Fields",value="Please enter your bar dates." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3AffirmationContent",type="CONTENTOBJ",desc="Acknowledgement content",value="Please acknowledge that you have read, understand, and will abide by the Rules of Operations of SmartLaw." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="2CDC0A0E-4778-423F-B4C0-26E975D0281D" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="LACBACC" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="LACBACashCheck" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="Smartlaw Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Smartlaw Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);       

            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo= variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
            ));

			 variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");
 
            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;

            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='SmartlawJoin', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='SmartlawJoin', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
            variables.origMemberID = variables.useMID;
            if(local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                structKeyExists(session, "formFields") and 
                structKeyExists(session.formFields, "step0") and 
                structKeyExists(session.formFields.step0, "memberID") and 
                int(val(session.formFields.step0.memberID)) gt 0){            
                    variables.useMID = session.formFields.step0.memberID;
                    if(structKeyExists(session.formFields.step0, "origMemberID") and int(val(session.formFields.step0.origMemberID)) gt 0){
                        variables.origMemberID = session.formFields.step0.origMemberID;
                    }              

            }else if(session.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }
            if( local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                local.formAction neq "showMemberInfo" and 
                local.formAction neq "processMemberInfo" and 
                structKeyExists(session.formFields, "step1") and 
                structKeyExists(session.formFields.step1, "useHistoryID") and 
                int(val(session.formFields.step1.useHistoryID)) gt 0){
                    variables.useHistoryID = int(val(session.formFields.step1.useHistoryID));
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
            local.subStatus = "";
            if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
                local.subStatus = hasSub(int(variables.useMID));
            }

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin');  
            }else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
                local.returnHTML = showError(errorCode=local.subStatus);  
            }else{
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMemberInfo();
                                break;		
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        switch (processMemberInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMembershipInfo();
                                break;
                            case "spam":
                                local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMembershipInfo":
                        switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showSmartlawMajorPanels();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
					case "processSmartlawMajorPanels":
                        switch (processSmartlawMajorPanels(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showPayment();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;               
                    case "processPayment":
                        local.processStatus = processPayment(event=arguments.event);
                        switch (local.processStatus) {
                            case "success":
                                local.returnHTML = showConfirmation();
                                application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
                                structDelete(session, "formFields");
                                break;
                            default:
                                local.returnHTML = showError(errorCode='failpayment');
                                break;				
                        }
                        break;
					case "showSmartlawMajorPanels":
                        local.returnHTML = showSmartlawMajorPanels();
                        break;
                    case "showMembershipInfo":
                        local.returnHTML = showMembershipInfo();
                        break;	
                    case "showMemberInfo":
                        local.returnHTML = showMemberInfo();
                        break;                    		
                    default:
                        if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
		                if(structKeyExists(session, "formFields")) structDelete(session, "formFields");
                        local.returnHTML = showLookup();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");        
    	</cfscript>
    </cffunction>


    <cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.headCode">
			<cfoutput>          

            #variables.pageJS# 

			<style type="text/css">
			</style>

			<script type="text/javascript">

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}

				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				

				function addMember(memObj) {
					assignMemberData(memObj);
				}

				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							if(step==3)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showSmartlawMajorPanels");                            

				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
                <cfinput type="hidden" name="fa" id="fa" value="processLookup">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
                <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">

                <cfif len(variables.strPageFields.FormTitle)>
                    <div class="row-fluid" id="FormTitleId">
                        <div class="span12">
                            <span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
                        </div>
                    </div>
                </cfif>	

				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>	
			</cfform>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step0")>
            <cfset structDelete(session.formFields, "step0")>
        </cfif>	

        <cfset session.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        

		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->

		<cfset local.fieldSetUIDlist = '#variables.professionalInformationFieldSetUID#'>

		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>

        <cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset local.strData.one = checkSessionExist("step1")/>       

        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

            
            <cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.professionalInformationFieldSetUID#',local.fieldSetUid)>
                <cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				<cfif NOT variables.isLoggedIn AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>				
					<cfloop collection="#local.memberFieldData#" item="local.key" >
						<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
							<cfset StructDelete(local.memberFieldData, local.key)>
						</cfif>					
					</cfloop>
				</cfif>
		    </cfif>                
        </cfloop>

        <cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.professionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.primaryAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.primaryAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.secondaryAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.secondaryAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.tertiaryAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.tertiaryAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferencesFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.emailFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.emailFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.emailAddressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.emailAddressPreferencesFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.insuranceInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.insuranceInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.disciplineFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.disciplineFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.clientSuitsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.clientSuitsFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.feeDisputesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.feeDisputesFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.educationInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.educationInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.additionalProfessionalDemographicDataFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalProfessionalDemographicDataFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.smartlawSourceFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.smartlawSourceFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.smartLawSourceFieldInfo = application.objCustomPageUtils.mem_getCustomFieldByUID(orgID=variables.orgid,UID='1F69736A-AD86-4B4C-975E-4CBFC064D65E')>
		<cfset local.smartLawSourceFieldCode = "md_" & local.smartLawSourceFieldInfo.COLUMNID/>

		<cfset local.smartLawSourceOtherFieldInfo = application.objCustomPageUtils.mem_getCustomFieldByUID(orgID=variables.orgid,UID='43118DEE-7A43-400B-A7D7-18DE85ABFD6D')>
		<cfset local.smartLawSourceOtherFieldCode = "md_" & local.smartLawSourceOtherFieldInfo.COLUMNID/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>	
		</cfif>

        <cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
        <cfset local.licenseStatus = {}>
        <cfset local.index = 1>
        <cfloop query="local.qryOrgProLicenseStatuses">
            <cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
            <cfset local.index = local.index + 1>
        </cfloop> 
        <cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}

				div.tsAppSectionHeading{margin-bottom:20px}
				.disciplineFieldSetHolder table, .clientSuitsFieldSetHolder table, .feeDisputesFieldSetHolder table{
					width:100%;
				}
				.disciplineFieldSetHolder table td:nth-child(4), .clientSuitsFieldSetHolder table td:nth-child(4), .feeDisputesFieldSetHolder table td:nth-child(4){
					width: 30%;
				}				  
				##content-wrapper table td:nth-child(2) {
					white-space: nowrap;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					div.tsAppSectionContentContainer{
						padding:0px!important;
					}
					.disciplineFieldSetHolder table, .clientSuitsFieldSetHolder table, .feeDisputesFieldSetHolder table, .disciplineFieldSetHolder table td:nth-child(4), .clientSuitsFieldSetHolder table td:nth-child(4), .feeDisputesFieldSetHolder table td:nth-child(4){
						width: unset!important;
					}
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
						padding: 0;
						white-space: unset!important;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}

					##content-wrapper div.ui-multiselect-menu{width:auto!important;}

					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}

				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							

				div.alert-danger{padding: 10px !important;}				

			</style>
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}

				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					#local.professionalInformationFieldSet.jsValidation#
					var isProfLicenseRequired = false;

					if(isProfLicenseRequired){
						var prof_license = $('.mpl_pltypeid').val();
						var isProfLicenseSelected = false;
						if(prof_license != "" && prof_license != null){
							isProfLicenseSelected = true;
							$.each(prof_license,function(i,val){
								var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();
								if($("##mpl_"+val+"_status").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' #variables.strProfLicenseLabels.profLicenseStatusLabel#.'; }                                
								if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' #variables.strProfLicenseLabels.profLicenseDateLabel#.'; }
							});
						}
						if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
					}
					#local.primaryAddressFieldSet.jsValidation#
					#local.secondaryAddressFieldSet.jsValidation#
					#local.tertiaryAddressFieldSet.jsValidation#
					#local.addressPreferencesFieldSet.jsValidation#
					#local.emailFieldSet.jsValidation#
					#local.emailAddressPreferencesFieldSet.jsValidation#
					#local.insuranceInformationFieldSet.jsValidation#
					#local.disciplineFieldSet.jsValidation#
					#local.clientSuitsFieldSet.jsValidation#
					#local.feeDisputesFieldSet.jsValidation#
					#local.educationInformationFieldSet.jsValidation#
					#local.smartlawSourceFieldSet.jsValidation#
					#local.additionalProfessionalDemographicDataFieldSet.jsValidation#
					
					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
					$("##state_table").show();
					if(status == ''){
						status = 'Active';
					}
					strOption = '';
					<cfloop collection="#local.licenseStatus#" item="local.i" >
						strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
					</cfloop>
					if(isChecked){
						$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
									'<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
									'<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
									'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
									'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
									'<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
									'</div>');
						$('##mpl_'+val+'_status').val(status);
						mca_setupDatePickerField('mpl_'+val+'_activeDate');
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
					}									
					else{
						$("##tr_state_"+val).remove();								
					}
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}	
				}
				
				$(document).ready(function() {	

					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>		

					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					</cfif>				

					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>

					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' Please Select ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
					
					var _CF_this = document.forms['#variables.formName#'];
					

					$(_CF_this['#local.smartLawSourceOtherFieldCode#']).parents('tr').hide();

					$(_CF_this['#local.smartLawSourceFieldCode#']).on('change',function(){
						var _smartLawSource = $.trim($(_CF_this['#local.smartLawSourceFieldCode#']).find('option:selected').text());
						if(_smartLawSource == 'Other')
							$(_CF_this['#local.smartLawSourceOtherFieldCode#']).parents('tr').show();
							else $(_CF_this['#local.smartLawSourceOtherFieldCode#']).parents('tr').hide();
					})
					

				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>		

					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif> 

						<fieldset>
							<legend>Vital Information</legend>

							<span class="professionalInformationFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.professionalInformationFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.professionalInformationFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>											                   		

							<span class="professionalLicensesHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">Professional License Information</div>
									<div class="tsAppSectionContentContainer fieldSetContainer">	
										<p>#variables.strPageFields.ProfessionalLicContent#</p>
										<table cellpadding="3" border="0" cellspacing="0" >									
											<tr align="top">
												<td class="tsAppBodyText" width="10">&nbsp;</td>
												<td class="tsAppBodyText" nowrap>Professional License</td>
												<td class="tsAppBodyText">
													<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
														<cfloop query="local.qryOrgPlTypes">	
															<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
														</cfloop>
													</select>
												</td>
											</tr>
											<tr class="top">
												<td class="tsAppBodyText" width="10"></td>
												<td class="tsAppBodyText"></td>
												<td class="tsAppBodyText"></td>
											</tr>
										</table>
										<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
											<tr>
												<td>
													<div class="row-fluid hide" id="state_table">
														<div class="span3 proLicenseLabel">
															<b>Type</b>
														</div>
														<div class="span3 proLicenseLabel">
															<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
														</div>
														<div class="span3 proLicenseLabel">
															<b>#variables.strProfLicenseLabels.profLicenseDateLabel#</b>
														</div>
														<div class="span3 proLicenseLabel">
															<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
														</div>
													</div>
													<span id="selectedLicense">
													</span>
												</td>
											</tr>					
										</table>
									</div>
								</div>
							</span>

							<span class="primaryAddressFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.primaryAddressFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.primaryAddressFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="secondaryAddressFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.secondaryAddressFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.secondaryAddressFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="tertiaryAddressFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.tertiaryAddressFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.tertiaryAddressFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="addressPreferencesFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.addressPreferencesFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.addressPreferencesFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="emailFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.emailFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.emailFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="emailAddressPreferencesFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.emailAddressPreferencesFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.emailAddressPreferencesFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>
						</fieldset>

						<fieldset>
							<legend>Vital Information</legend>

							<span class="insuranceInformationFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.insuranceInformationFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.insuranceInformationFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="disciplineFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.disciplineFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.disciplineFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="clientSuitsFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.clientSuitsFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.clientSuitsFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="feeDisputesFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.feeDisputesFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.feeDisputesFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="educationInformationFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.educationInformationFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.educationInformationFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>
							
							<span class="smartLawSourceFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.smartLawSourceFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.smartLawSourceFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>

							<span class="additionalProfessionalDemographicDataFieldSetHolder">
								<div class="row-fluid">
									<div class="tsAppSectionHeading">#local.additionalProfessionalDemographicDataFieldSet.fieldSetTitle#</div>								
									<div class="tsAppSectionContentContainer">										
										#local.additionalProfessionalDemographicDataFieldSet.fieldSetContent#									
									</div>
								</div>
							</span>
						</fieldset>				

                        <div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>

				#application.objWebEditor.showEditorHeadScripts()#
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
                <cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID = variables.orgid)>

				<script language="javascript">
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">                       
                            <cfif ListFindNoCase('Primary Address,Secondary Address,Tertiary Address',local.qryOrgAddressTypes.addressType)>
                                function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									if(_this.length){
										var _address = _this.val();
										var _CF_this = document.forms['#variables.formName#'];
										if(_address.length > 0){
											if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
												$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
											}
										} else {
											$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
										}										
									}
                                }

                               addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));

                                $('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
                                	addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
                                });
                            <cfelse>
                                $("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
                            </cfif>
						</cfloop>
						<cfloop query="local.qryOrgEmailTypes"> 
							<cfif ListFindNoCase('Email,Alternate Email,Second Alternate,Third Alternative',local.qryOrgEmailTypes.emailType)>                      
								function emailFieldUpdate#local.qryOrgEmailTypes.emailTypeID#(_this){
									if(_this.length){
										var _email = _this.val();

										if(_email.length > 0){
											if($("select[id^=met_] option[value='#local.qryOrgEmailTypes.emailTypeID#']").length==0) {
												$('*[id^=met_]').append('<option value="#local.qryOrgEmailTypes.emailTypeID#">#local.qryOrgEmailTypes.emailType#</option>');
											}
										} else {
											$("select[id^=met_] option[value='#local.qryOrgEmailTypes.emailTypeID#']").remove();
										}
									}
								}

								emailFieldUpdate#local.qryOrgEmailTypes.emailTypeID#($('##me_#local.qryOrgEmailTypes.emailTypeID#_email'));

								$('##me_#local.qryOrgEmailTypes.emailTypeID#_email').on('change',function(){
									emailFieldUpdate#local.qryOrgEmailTypes.emailTypeID#($(this));
								});
							</cfif>
						</cfloop>
					});

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>		

        <cfset local.response = "failure">

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
            <cfset structDelete(session.formFields, "step1")>
        </cfif>			

        <cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>	
        <cfset local.strData.one = checkSessionExist("step1")/>

		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>

        <cfif variables.isLoggedIn OR session.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
            <cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
            <cfset local.strData.one.memberID = 0>         
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>

		<cfif local.strResult.memberID>
			<cfsavecontent variable="local.headCode">
				<cfoutput>					
					<script type="text/javascript">
						$(document).ready(function(){
							if (typeof arrUploaders !== 'undefined') {
								$.each(arrUploaders, function() {
									this.uploader.bind('BeforeUpload', function(uploader, file) {
										uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
									});
									this.uploader.start();								
								});
							}
						});
					</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headCode#">
		</cfif>

		<cfset session.formFields.step0.origMemberID = variables.useMID/>
        <cfset session.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>	
            <cfset session.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
                                                subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
                                                enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
            <cfset session.captchaEntered = 1>		
			<cfset local.response = "success">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset variables.useMID = local.strData.zero.memberID/>

        <cfif StructIsEmpty(local.strData.one)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.thisAddonSubscriptionID = "">
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
		</cfloop>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>
		<cfset local.subscriptionSectionMembershipID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = "0C9EEAAE-8C06-4CFB-B923-EED13CCEB322")>

		<cfset local.smartlawPanelsRates = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID='1370037B-8CC4-49DC-9681-F41775889250', activeRatesOnly=true, ignoreRenewalRates=true,isFrequencyFull=true)>
		<cfset local.smartlawMembership = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID='C152B672-21FD-4A4A-ABB1-3ED4962890C3', activeRatesOnly=true, ignoreRenewalRates=true,isFrequencyFull=true)>
        <cfset local.rateRelationStruct = structNew()>
		<cfset local.rateRelationStruct['3B558E28-FB35-4366-9946-7B952D7DAA91'] = '4A58AABA-7A09-4EEB-8ABD-86ED34F4F5DF'>
		<cfset local.rateRelationStruct['19A35E75-07B6-4361-9CB9-53807FDCBA22'] = '4A58AABA-7A09-4EEB-8ABD-86ED34F4F5DF'>
		<cfset local.rateRelationStruct['DDB46719-BF09-47C1-AB3A-57C40C7022F2'] = 'D106BF13-F1E7-45B1-AD0B-54468B150C5F'>
		<cfset local.rateRelationStruct['234C12F9-8A4C-4BA6-9885-7A873DE92B59'] = 'D106BF13-F1E7-45B1-AD0B-54468B150C5F'>
		<cfset local.rateRelationStruct['DE0DFAF7-4274-4BB2-AB58-B7F796B43D48'] = '62A05801-EBC0-4DC7-B0AD-2068215B4750'>
		<cfset local.rateRelationStruct['2003C1E5-1155-47E5-96AB-6F99F020B62A'] = '62A05801-EBC0-4DC7-B0AD-2068215B4750'>
		<cfset local.rateRelationStruct['C214CEFC-63A2-46C8-97E1-A98632CC8F57'] = '9E4B7E57-A867-418A-8D04-A9FAB4D78D07'>
		<cfset local.rateRelationStruct['C037BEBD-C910-40F9-868C-7005E4106E23'] = '9E4B7E57-A867-418A-8D04-A9FAB4D78D07'>
		<cfset local.rateRelationStruct['262F6140-BEBD-42A1-8020-A9C1F2117ABE'] = 'A641342D-DA26-42D7-9DCD-AE26E7C5153F'>
		<cfset local.rateRelationStruct['63964501-F582-4CA0-A18B-D81464BC146B'] = 'A641342D-DA26-42D7-9DCD-AE26E7C5153F'>
		<cfset local.rateRelationStruct['077C94D3-B57D-41E7-91FA-40B522A5D794'] = '7A534153-585E-40BF-B9DE-038827BDD31A'>
		<cfset local.rateRelationStruct['EA11F538-459B-406E-A784-2108D68E3986'] = '7A534153-585E-40BF-B9DE-038827BDD31A'>
		<cfset local.rateRelationStruct['2335A672-6050-43B1-8FAC-4877332C3691'] = '047DBFBB-7BCE-406A-ABC6-111B8EC6197F'>
		<cfset local.rateRelationStruct['3A4317E5-D7C0-4F31-8500-DCAF84B77059'] = '047DBFBB-7BCE-406A-ABC6-111B8EC6197F'>
		<cfset local.rateRelationStruct['6ED699EA-F803-4520-A596-98C8722015ED'] = '3D65ED6A-E7FB-43C0-ABC2-817CAEDCDF90'>
		<cfset local.rateRelationStruct['0DD55119-3FCD-4916-9A2D-7C11603DDC55'] = '3D65ED6A-E7FB-43C0-ABC2-817CAEDCDF90'>
		<cfset local.rateRelationStruct['0C1AEC32-A09D-4B32-95C3-E500964E6030'] = '6CF2F6AA-48D6-4BD8-B503-19A389098A35'>
		<cfset local.rateRelationStruct['B4F59C2B-6DE0-4E70-97C8-550CFB5C5945'] = '6CF2F6AA-48D6-4BD8-B503-19A389098A35'>
		<cfset local.rateRelationStruct['A94E7E71-A920-4C6E-B112-6B32D0336C79'] = '17DE8DDA-C274-4E0D-81A0-D316DBC3C218'>
		<cfset local.rateRelationStruct['E666856D-2F3C-4571-8ABA-E0584C620734'] = '17DE8DDA-C274-4E0D-81A0-D316DBC3C218'>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style type="text/css">
					div.alert-danger{padding: 10px !important;}
                </style>

                <script type="text/javascript">	
                    #local.result.jsAddonValidation#
				    function validateMembershipInfoForm(){
						var arrReq = new Array();						
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}
						var minlp = $('div [data-setname="Smartlaw Major Panels"]').data('minallowed');
						var maxlp = $('div [data-setname="Smartlaw Major Panels"]').data('maxallowed');

						if(minlp>0){
							if($('div [data-setname="Smartlaw Major Panels"] >.subLabelHolder input:checkbox:checked').length == 0){
								arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $('div [data-setname="Smartlaw Major Panels"] >legend').html()+".";
							}
						}
						

						$('div [data-setname="Smartlaw Major Panels"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
							var minlp = $(this).data("minallowed");
							if($("##"+$(this).attr("id").split('_')[0]+":checked").length){
								if($("##"+$(this).attr("id").split('_')[0]+"_addons>div>div.subLabelHolder input:checkbox:checked").length == 0){
									arrReq[arrReq.length] = "Please select no more than " + minlp + " " + $("##"+$(this).attr("id").split('_')[0]+"_addons legend").eq(0).text()+".";
								}							
							}
						});

						if(maxlp>0){						
							if($('div [data-setname="Smartlaw Major Panels"] >.subLabelHolder input:checkbox:checked').length > maxlp){
								arrReq[arrReq.length] = "Please select a minimum of " + maxlp + " " + $('div [data-setname="Smartlaw Major Panels"] >legend').html()+".";
							}
						}

						$('div [data-setname="Smartlaw Major Panels"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
							var maxlp = $(this).data("maxallowed");
							if($("##"+$(this).attr("id").split('_')[0]+":checked").length){
								if($("##"+$(this).attr("id").split('_')[0]+"_addons>div>div.subLabelHolder input:checkbox:checked").length > maxlp){
									arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $("##"+$(this).attr("id").split('_')[0]+"_addons legend").eq(0).text()+".";
								}							
							}
						});

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}

					function rateReset(){
						$('div [data-setname="Smartlaw Major Panels"] input:checkbox').each(function(){							
							if($(this).is(":checked")){
								$("##"+$(this).attr("name").split('_')[0]+"_addons").show();
								if($("[name=sub#local.subscriptionID#_rate]:checked").length){
									$(this).parents('.subLabelHolder').find('input:radio[value='+$("[name=sub#local.subscriptionID#_rate]:checked").attr("linkrateid")+']').each(function(){
										if($(this).is(":checked") == false){
											$(this).prop('checked', true);
										}
									});
								}
							}else{
								$("##"+$(this).attr("name").split('_')[0]+"_addons").hide();
								$.merge($(this).parents('.subLabelHolder').find('input:checkbox'),$("##"+$(this).attr("name").split('_')[0]+"_addons").find('input:checkbox')).each(function(){
									if($(this).is(":checked")){
										$(this).prop('checked', false);
									}
								});
								$.merge($(this).parents('.subLabelHolder').find('input:radio'),$("##"+$(this).attr("name").split('_')[0]+"_addons").find('input:radio')).each(function(){
									if($(this).is(":checked")){
										$(this).prop('checked', false);
									}
								});
							}
						});		

						if($("[name=sub#local.subscriptionID#_rate]:checked").length){
							$("[data-setname='Smartlaw Major Panels'] input:radio").removeAttr("disabled").parent().show();
							$("[data-setname='Smartlaw Major Panels'] input:radio[value!='"+$("[name=sub#local.subscriptionID#_rate]:checked").attr("linkrateid")+"']").attr("disabled","disabled").parent().hide();
						}	
						
					}
					$(document).ready(function(){
						$("[name='sub#local.subscriptionID#_rate']").change(function(){
							rateReset();
						});

						$('div [data-setname="Smartlaw Major Panels"] input:checkbox').change(function(){
							rateReset();						
						});

						$('div [data-setname="Smartlaw Major Panels"] input:radio').change(function(){
							if($(this).is(":checked")){
								if($(this).parents('.subLabelHolder').find('.subCheckbox:checked').length == 0){
									$(this).parents('.subLabelHolder').find('.subCheckbox').trigger("click");
								}

								if($("[name='sub#local.subscriptionID#_rate']:checked").length==0){
									$("[linkRateId='"+$(this).val()+"']").prop('checked', true);
								}
								rateReset();
							}
						});

						<cfloop query="local.smartlawMembership">
							<cfif StructKeyExists(local.rateRelationStruct,local.smartlawMembership.rateUID)>
								<cfquery dbtype="query" name="local.qrySmartlawPanelsRates">
									select Distinct r.rateID
									from [local].smartlawPanelsRates r 
									where r.rateUID = '#local.rateRelationStruct['#local.smartlawMembership.rateUID#']#'
								</cfquery>
								$("[value='#local.smartlawMembership.rateID#']").attr("linkRateId",'#local.qrySmartlawPanelsRates.rateID#');
							</cfif>
						</cfloop>

						rateReset();
					
					});
					
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>	
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
            <cfset structDelete(session.formFields, "step2")>
        </cfif>			
        <cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showSmartlawMajorPanels" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

		<cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset variables.useMID = local.strData.zero.memberID/>

        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>
										
		<cfset local.subscriptionDetails = application.objCustomPageUtils.sub_getSubscriptionsDetailsFromSubsetUID(setUID='248CEEEB-D1C3-421D-9CE1-E7FBBE75604A',siteID=variables.siteID,activeRatesOnly=true,ignoreRenewalRates=true,isFrequencyFull=true)/>
		<cfset local.subList = ListRemoveDuplicates(valueList(local.subscriptionDetails.subscriptionID))/>

		<cfset local.selectedList = "">
		<cfloop list="#local.subList#" index="local.sub">
			<cfif structKeyExists(local.strData.two, 'sub#local.sub#')>
				<cfset local.selectedList = ListAppend(local.selectedList,local.sub,",")>
			</cfif>
		</cfloop>
		<cfquery dbtype="query" name="local.qrySelectedSubscriptionDetails">
			select Distinct s.subscriptionID, s.subscriptionName
			from [local].subscriptionDetails s
			where s.subscriptionID in (#local.selectedList#)
		</cfquery>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style type="text/css">
					div.alert-danger{padding: 10px !important;}
                </style>

                <script type="text/javascript">	

				    function validateSmartlawMajorPanels(){
						var arrReq = new Array();
						var _CF_this = document.forms['#variables.formName#'];
						var _isCheckedExcemption = document.forms['frmJoin'].mccf_verify.checked;
						if(!_isCheckedExcemption){
							<cfloop query="local.qrySelectedSubscriptionDetails">
								if(!_CF_hasValue(_CF_this['sub#local.qrySelectedSubscriptionDetails.subscriptionID#_desc'], "TEXT", false)) arrReq[arrReq.length] = "#local.qrySelectedSubscriptionDetails.subscriptionName# is required.";							
							</cfloop>
						}
						var _isCheckedAck = document.forms['frmJoin'].mccf_agree.checked;
						if(!_isCheckedAck) arrReq[arrReq.length] = "Please check and acknowledge your provided information.";
						
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}

					function prefillStep3Data() {
						var objPrefill = new Object();
						<cfloop collection="#local.strData.three#" item="local.thisKey">
							<cfif structKeyExists(local.strData.three, '#local.thisKey#')>
								#toScript(local.strData.three[local.thisKey],"objPrefill.#local.thisKey#")#
							</cfif>
						</cfloop>
						for (var key in objPrefill) {
							$("###variables.formName# [name='"+key+"']").val(objPrefill[key]);
						}
					}

					$(document).ready(function() {	
						prefillStep3Data();

						$('##mccf_verify').change(function(){
							if($('##mccf_verify').is(':checked')){
								$('.requiredTxt').html('');
							} else {
								$('.requiredTxt').html('*&nbsp;');
							}
						});
						
					});
					
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateSmartlawMajorPanels()">
					<cfinput type="hidden" name="fa" id="fa" value="processSmartlawMajorPanels">			
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step3TopContent)>
						<div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
					</cfif>					

					<cfif len(variables.strPageFields.Step3AdditionalContent)>
						<div class="row-fluid" id="Step3AdditionalContent"><div class="span12">#variables.strPageFields.Step3AdditionalContent#</div></div>
					</cfif>	

					<cfif len(variables.strPageFields.Step3TitleContent)>
						<div class="row-fluid" id="Step3TitleContent"><div class="span12">#variables.strPageFields.Step3TitleContent#</div></div>
					</cfif>	

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>
									<fieldset>
									<legend>Smartlaw Major Panels</legend>
									<table cellpadding="3" border="0" cellspacing="0" style="width: 100%;" id="subWrapper">
										<tbody>
											<cfloop query="local.qrySelectedSubscriptionDetails">
											<tr valign="top">
												<td class="tsAppBodyText requiredTxt" width="10">*&nbsp;</td>
												<td class="tsAppBodyText" nowrap="" style="width: 21%;">#local.qrySelectedSubscriptionDetails.subscriptionName#</td>
												<td class="tsAppBodyText">
												&nbsp;
												</td>
												<td class="tsAppBodyText">
													<textarea name="sub#local.qrySelectedSubscriptionDetails.subscriptionID#_desc" id="#local.qrySelectedSubscriptionDetails.subscriptionID#_desc" type="text" rows="10" style="width:100%"></textarea>
												</td>
											</tr>												
											</cfloop>
										</tbody>
									</table>
									</fieldset>
								</cfoutput>
							</div>
						</div>
					</div>	
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12"><br>
								#variables.strPageFields.Step3AffirmationContent#
								<label class="checkbox subLabel" for="mccf_agree">
									<input type="checkbox" class="subCheckbox" name="mccf_agree" id="mccf_agree" value="0"> 
									I Acknowledge
								</label>
							</div>
						</div>
					</div>
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12"><br>
								<p>If you believe wish to request an exemption from this qualification standard please check the box below:</p>
								<label class="checkbox subLabel" for="mccf_verify">
									<input type="checkbox" class="subCheckbox" name="mccf_verify" id="mccf_verify" value="0"> 
									Request for Exemption<br><br>
								</label>
							</div>
						</div>
					</div>
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processMembershipInfo');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processSmartlawMajorPanels" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">

		<cfset var local = structNew()>	

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step3")>
            <cfset structDelete(session.formFields, "step3")>
        </cfif>			
        <cfset session.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.three = checkSessionExist("step3")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

        <cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>

        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showSmartlawMajorPanels"
				)
			>
		</cfif>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>

                <script type="text/javascript">	
                    function validatePaymentForm(isPaymentRequired) {

						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
                    $('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);

						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});                                

                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
                    <cfinput type="hidden" name="fa" id="fa" value="processPayment">
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
                    <cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
                            </div>
                        </div>
                    </cfif>	

                    <cfif len(variables.strPageFields.Step4TopContent)>
                        <div class="row-fluid" id="Step4TopContent"><div class="span12">#variables.strPageFields.Step4TopContent#</div></div>
                    </cfif>

                    <div class="tsAppSectionHeading">Membership Selections Confirmation</div>
                    <div class="tsAppSectionContentContainer">						
                        #local.strResult.formContent#
                    </div>
                    <br/>

                    <div class="tsAppSectionHeading">Total Price</div>
                    <div class="tsAppSectionContentContainer">						
                        Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
                    </div>

                    <br/><br/>

                    <cfif local.paymentRequired>
                        #local.strReturn.paymentHTML#
                    <cfelse>
                        <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                        <button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showSmartlawMajorPanels');">&lt;&lt; Back</button>
                    </cfif>

                </cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processPayment" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step4")>
            <cfset structDelete(session.formFields, "step4")>
        </cfif>			
        <cfset session.formFields.step4 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

        <cfset local.strData.four = checkSessionExist("step4")/>
		<cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>

        <cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three) OR StructIsEmpty(local.strData.four)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>

        <cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>


		<cfset local.strData.one.memberID = variables.useMID>
        <cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
        <cfset local.strData.four.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.strData.two.memberID = variables.useMID>

		<cfset application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>

		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)> 

		<cfif structKeyExists(local.strData.four, 'mccf_payMethID') and structKeyExists(local.strData.four, 'p_#local.strData.four.mccf_payMethID#_mppid')>
            <cfset local.memberPayProfileID = local.strData.four['p_#local.strData.four.mccf_payMethID#_mppid']>
        <cfelse>
            <cfset local.memberPayProfileID = 0 >
        </cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.strResult.totalFullPrice gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.four.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

        <cfset local.response = "success">

        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

        <cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.four,"p_#local.strData.four.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.four["p_#local.strData.four.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.four.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfset local.professionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.primaryAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.primaryAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.secondaryAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.secondaryAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.tertiaryAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.tertiaryAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferencesFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.emailFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.emailFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.emailAddressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.emailAddressPreferencesFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.insuranceInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.insuranceInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.disciplineFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.disciplineFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.clientSuitsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.clientSuitsFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.feeDisputesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.feeDisputesFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.educationInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.educationInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.additionalProfessionalDemographicDataFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalProfessionalDemographicDataFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.smartLawSourceFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.smartLawSourceFieldSetUID, mode="confirmation", strData=local.strData.one)>
	
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		
        <cfsavecontent variable="local.invoice">
            <cfoutput>
				#local.professionalInformationFieldSet.fieldSetContent#
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br>
				#local.primaryAddressFieldSet.fieldSetContent#
				#local.secondaryAddressFieldSet.fieldSetContent#
				#local.tertiaryAddressFieldSet.fieldSetContent#
				#local.addressPreferencesFieldSet.fieldSetContent#
				#local.emailFieldSet.fieldSetContent#
				#local.emailAddressPreferencesFieldSet.fieldSetContent#
				#local.insuranceInformationFieldSet.fieldSetContent#
				#local.disciplineFieldSet.fieldSetContent#
				#local.clientSuitsFieldSet.fieldSetContent#
				#local.feeDisputesFieldSet.fieldSetContent#
				#local.educationInformationFieldSet.fieldSetContent#
				#local.smartLawSourceFieldSet.fieldSetContent#
				#local.additionalProfessionalDemographicDataFieldSet.fieldSetContent#

				<cfoutput>								
					<cfset local.subscriptionDetails = application.objCustomPageUtils.sub_getSubscriptionsDetailsFromSubsetUID(setUID='248CEEEB-D1C3-421D-9CE1-E7FBBE75604A',siteID=variables.siteID,activeRatesOnly=true,ignoreRenewalRates=true,isFrequencyFull=true)/>
					<cfset local.subList = ListRemoveDuplicates(valueList(local.subscriptionDetails.subscriptionID))/>

					<cfset local.selectedList = "">
					<cfloop list="#local.subList#" index="local.sub">
						<cfif structKeyExists(local.strData.two, 'sub#local.sub#')>
							<cfset local.selectedList = ListAppend(local.selectedList,local.sub,",")>
						</cfif>
					</cfloop>
					<cfquery dbtype="query" name="local.qrySelectedSubscriptionDetails">
						select Distinct s.subscriptionID, s.subscriptionName
						from [local].subscriptionDetails s
						where s.subscriptionID in (#local.selectedList#)
					</cfquery>
					
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tbody>
							<tr>
								<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Smartlaw Major Panels</td>
							</tr>
							<tr>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
									<table cellpadding="3" border="0" cellspacing="0">
									<tbody>		
										<cfloop query="local.qrySelectedSubscriptionDetails">									
											<tr valign="top">
												<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">#local.qrySelectedSubscriptionDetails.subscriptionName#: &nbsp;</td>
												<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
													#local.strData.three['sub#local.qrySelectedSubscriptionDetails.subscriptionID#_desc']#
												</td>
											</tr>											
										</cfloop>										
									</tbody>
									</table>
								</td>
							</tr>
						</tbody>
					</table>
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tbody>
							<tr>
								<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Smartlaw Rules of Operation Acknowledgement</td>
							</tr>
							<tr>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
									<table cellpadding="3" border="0" cellspacing="0">
									<tbody>		
										<tr valign="top">
											<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">
											<div>#replace(variables.strPageFields.Step3AffirmationContent,"<p>","<p class='tsAppBodyText'>","ALL")#</div>

											<cfset local.imagesURL = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).internalAssetsURL & "images/checkedbox.jpg">
											<div style="line-height:20px;"> <img width="20px" height="20px" src="#local.imagesURL#"> I Acknowledge</div>
											</td>
										</tr>											
									</tbody>
									</table>
								</td>
							</tr>
						</tbody>
					</table><br/>
				</cfoutput>



                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            #local.strResult.formContent#
                            <br/>
                        </div>
                        <br/>
                        <strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
                        <br/>
                        </td>
                    </tr>
                </table>

                <cfif local.paymentRequired>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(local.strData.four,"mccf_payMeth")>
                                <table cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #local.strData.four.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif>

            </cfoutput>
        </cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>

                <p>Here are the details of your application:</p>	

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

        <cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
        <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
        <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

        <cfsavecontent variable="local.specialText">
			<cfoutput>

            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>
				#local.specialText#
                <p>Here are the details of your application:</p>

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

        <cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>

        <!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
                    <cfif len(variables.strPageFields.ConfirmationContent)>
                        <p>#variables.strPageFields.ConfirmationContent#</p>
                    </cfif>
                    <p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>

        <!--- relocate to message page --->
        <cfset session.invoice = local.invoice />

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
                </cfif>
                <div class="tsAppSectionContentContainer">
                    <p>Here are the details of your application:</p>						
                    #session.invoice#
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="hasSub" access="private" output="false" returntype="string">
        <cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

        <cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
        <cfset local.isExist = false/>
        <cfset local.strData = {}>

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, arguments.step)>
            <cfset local.strData = session.formFields[arguments.step]/>
        </cfif>			

		<cfreturn local.strData>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						<cflocation url="#local.redirectLink#" addtoken="false">
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
                     <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>