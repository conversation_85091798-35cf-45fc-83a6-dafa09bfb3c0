<cfcomponent extends="model.customPage.customPage" output="true">
    <cfset variables.objCustomPageUtils = application.objCustomPageUtils>
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
        <cfargument name="Event" type="any">
        <cfscript>
            var local = structNew();
            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";

            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
            
            local.arrCustomFields = [];
            local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="
                <p style='padding-bottom:5px;'>Click the <b>Account Lookup</b> button to the left.</p>
                <p style='padding-bottom:5px;'>Enter the search criteria and click <b>Continue</b>.</p>
                <p style='padding-bottom:5px;'>If you see your name, please press the <b>Choose</b> button next to your name.</p>
                <p style='padding-bottom:5px;'>If you do not see your name, click the <b>Create an Account</b> link.</p>										
            " }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName = 'frmJoin',
                formNameDisplay = 'Join / Renew Application',
                orgEmailTo = '<EMAIL>',
                memberEmailFrom = '<EMAIL>'
            ));
            
            variables.profile_1 = structNew();
            variables.profile_1._profileCode 	= 'SKCCCIM';
            variables.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID, profileCode=variables.profile_1._profileCode);
            variables.profile_1._description 	= '#variables.Organization# - #variables.formNameDisplay#';

            variables.taxRate				    = val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='f5e37297-d39c-44ad-adae-01370a076b12').columnValueString)/100;	
            local.objFieldsets			        = CreateObject('component','model.system.platform.memberFieldsets');
            local.isSuperUser                   = application.objUser.isSuperUser(cfcuser=session.cfcuser);
            variables.captchaDetails            = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=variables.formName);
        </cfscript>   

        <!--- this pricing info is used both in CF and javascript.  Dont make any changes to this without verifying both CF and JavaScript code! --->
        <cfset variables.memDues = StructNew()>
        <cfset variables.memDues["Regular1-4years"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='c729e958-3ee9-4f0a-afc0-775fc168acc8').columnValueString)>
        <cfset variables.memDues["Regular5-10years"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='532e1546-88b0-47e9-bcae-b2d06ac814e8').columnValueString)>
        <cfset variables.memDues["Regular10+years"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='1e221415-6eef-4609-8ed7-306827da50f7').columnValueString)>
        <cfset variables.memDues["Associate1-4years"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='fb105f40-a180-4470-9778-d3d794ed7315').columnValueString)>
        <cfset variables.memDues["Associate5-10years"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='5e569724-23d1-44c6-9336-1c68f748e9f2').columnValueString)>
        <cfset variables.memDues["Associate10+years"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='3c112be8-6ba5-4de5-b502-d8f869000363').columnValueString)>
        <cfset variables.memDues["ArticlingStudent"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='696b8991-c9e6-4a40-a78c-6bfad701c347').columnValueString)>
        <cfset variables.memDues["Professor"] 			= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='30f8dc26-318f-4a80-8eb1-66a0afd96ca1').columnValueString)>
        <cfset variables.memDues["NonLawyer"] 			= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='5b726ce3-2dcb-48fd-b4a8-eb5f2f815f8a').columnValueString)>
        <cfset variables.memDues["Judicial"] 			= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='cde657ba-9e7e-4ce8-8164-219887401e30').columnValueString)>
        <cfset variables.memDues["UniversityStudent"] 	= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='d0e8153f-e56f-40d7-b5b9-3eccd65ca577').columnValueString)>
        <cfset variables.memDues['PLAA'] 				= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='b130a6ac-1b6c-4aad-a176-585d8dee7569').columnValueString)>
        <cfset variables.memDues["Sustaining"] 			= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='f116ce10-9a2e-4090-9129-65051902c35c').columnValueString)>
        <cfset variables.memDues["Retired"] 			= val(application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='75DA16C3-CF8B-43FD-A464-F60C163901BA').columnValueString)>


        <cfset variables.memDesc = StructNew()>
        <cfset variables.memDesc["Regular1-4years"]     = "Regular Membership - Less than 5 years">
        <cfset variables.memDesc["Regular5-10years"] 	= "Regular Membership - Between 5 and 10 years">
        <cfset variables.memDesc["Regular10+years"] 	= "Regular Membership - More than 10 years">
        <cfset variables.memDesc["Associate1-4years"] 	= "Associate Membership - Less than 5 years">
        <cfset variables.memDesc["Associate5-10years"] 	= "Associate Membership - Between 5 and 10 years">
        <cfset variables.memDesc["Associate10+years"] 	= "Associate Membership - More than 10 years">
        <cfset variables.memDesc["ArticlingStudent"] 	= "Law Graduate & Articling Student Membership">
        <cfset variables.memDesc["Professor"] 			= "Law Professor Membership">
        <cfset variables.memDesc["NonLawyer"] 			= "Non-Lawyer Membership">
        <cfset variables.memDesc["Judicial"] 			= "Judicial Membership">
        <cfset variables.memDesc["UniversityStudent"] 	= "University Student Membership">
        <cfset variables.memDesc['PLAA'] 				= "Paralegal/Legal Assistant">
        <cfset variables.memDesc["Sustaining"] 			= "Sustaining Membership">
        <cfset variables.memDesc["Retired"] 			= "Retired Member">


        <cfscript>          
            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin'); 
            } else {
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup()) {
                            case "success":
                                local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
                                break;		
                            default:
                                application.objCommon.redirect(variables.baselink);
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        local.returnHTML = showPayment(arguments.event);
                        if(local.returnHTML eq "spam"){
                            local.returnHTML = showError(errorCode='spam'); 
                        }
                        break;
                    case "processPayment":
                        local.returnHTML = showConfirmation(arguments.event);
                        application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
                        structDelete(session, "formFields");
                        break;
                    default:
                        local.returnHTML = showLookup();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
                .frmContent{ padding:10px; background:##dddddd; }
                .frmRow1{ background:##ffffff; }
                .frmRow2{ background:##dedede; }
                .frmRow3{ background:##aeaeae; }
                .frmTotals{ background:##666666; color:##ffffff; font-weight:bold; }
                .frmText{ font-size:9pt; color:##505050; }
                .frmButtons{ padding:5px 0; border-top:1px solid ##666666; border-bottom:1px solid ##666666; }
                .TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
                .CPSection{ border:1px solid ##666666; margin-bottom:15px; }
                .CPSectionTitle { font-size:14pt; height:40px!important; font-weight:bold; color:##ffffff; padding:10px; background:##336699; }
                .CPSectionContent{ padding:0 10px; }
                .subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
                .subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
                .subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
                .subCPSectionTitle { font-size:10pt; font-weight:bold; }
                .subCPSectionText { font-size:9pt; color:##36617d; }
        
                .info{ font-style:italic; font-size:7pt; color:##777777; }
                .small{ font-size:7pt;}
                
                .r { text-align:right; }
                .l { text-align:left; }
                .c { text-align:center; }
                .i { font-style:italic; }
                .b{ font-weight:bold; }
                
                .P{padding:10px;}
                .PL{padding-left:10px;}
                .PR{padding-right:10px;}
                .PB{padding-bottom:10px;}
                .PT{padding-top:10px;}
                
                .BB { border-bottom:1px solid ##666666; }
                .BL { border-left:1px solid ##666666; }
                .BT { border-top:1px solid ##666666; }
                .block { display:block; }
                .black{ color:##000000; }
                .red{ color:##ff0000; }
                
                .msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
                .msgSubHeader{background:##dddddd;}
                
                .tsAppBodyText { color:##03608b;}
                select.tsAppBodyText{color:##666666;}
                
                .alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
                .paymentGateway{ background-color:##ededed; padding:10px; }
                ##memberNumber{ display:inline-block; width:140px; }
                ##mailContent > table > tbody > tr:first-child > td {
                    background:##224563!important; color:##fff!important; font-weight:bold!important; text-transform:uppercase!important; padding:5px!important;}
                ##mailContent > table > tbody > tr:last-child table tr::nth-child(odd){ background:##dedede!important;}
                ##mailContent > table > tbody > tr:last-child table tr:nth-child(even){ background:##fff!important; }
                ##mailContent > table > tbody > tr:last-child table{ width:100%!important;}
                ##mailContent > table > tbody > tr:last-child > td:first-child{ padding:0!important;}
                ##mailContent > table > tbody > tr:last-child table tr td:first-child{ width:1%!important; white-space: nowrap!important;}
			</style>
			#variables.pageCSS#
			#variables.pageJS#
			<script type="text/javascript">
				$(function() {
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});
						}
					});
				});
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
				}
				function selectMember() {
					var windowWidth = $(window).width();
					var _popupWidth = 550;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					} 
					$.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}
				function resizeBox(newW,newH) {
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				window.onhashchange = function() {       
                    if (location.hash.length > 0) {        
                        step = parseInt(location.hash.replace('##',''),10);     
                        if (prevStep > step){
                            if(step==1)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
                            if(step==2)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
                            mc_loadDataForForm($('###variables.formName#'),'previous');			        	
                        }
                    } else {
                        step = 1;
                    }
                    prevStep = step;				    
				}				
				$(document).ready(function() {
                    $('.tsAppBodyText').addClass('BodyText');
					$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText');
                    
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
            <div class="TitleText" style="padding-bottom:15px;">#variables.Organization# - #variables.formNameDisplay#</div>
			<div class="r i frmText">*Denotes required field</div>
            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="customTable form-horizontal">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" >
			
                <div class="CPSection">
                    <div class="CPSectionTitle BB">#variables.strPageFields.AccountLocatorTitle#</div>
                    <div class="frmRow1 frmText" style="padding:10px;">
                        <table cellspacing="0" cellpadding="2" border="0" width="100%">
                            <tr>
                                <td width="175" class="c">
                                    <div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
                                        <button name="btnAddAssoc" type="button" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
                                    </div>
                                </td>
                                <td>
                                    <span class="frmText">
                                        #variables.strPageFields.AccountLocatorInstructions#
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		
		<cfset local.stReturn = "success">

		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
        <cfset local.strData = {}>
        <cfset local.strData.memberID = 0>
        <cfset local.strData.orgID = variables.orgID>
        <cfset local.strData.siteID = variables.siteID>

        <cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>
        <cfset local.fieldSetUIDlist = 'AE502242-72A2-478C-98DE-2B634B51384B'>
        <cfset local.memberFieldDetails = structNew()>
        <cfset local.memberFieldData = structNew()>
        <cfset local.PLAAFileField = {fieldCode="",fieldLabel="",columnID=""}>

        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
            <cfset local.PLAAFileData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Paralegal/Legal Assistant Agreement']")/>
            <cfif arrayLen(local.PLAAFileData)>				
                <cfset local.PLAAFileField.fieldCode = local.PLAAFileData[1].XmlAttributes.fieldCode>
                <cfset local.PLAAFileField.fieldLabel = local.PLAAFileData[1].XmlAttributes.fieldLabel>
                <cfset local.PLAAFileField.columnID = local.PLAAFileData[1].XmlAttributes.mdColumnID>
            </cfif>
        </cfloop>
        <cfset local.membershipCategoryTypeField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
        <cfset local.yearofcallTypeField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='b10161d3-ae90-4eea-b5ef-6ff13408fba0')> 		
        <cfset local.otherJurisdictionField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='66C0BA2F-FE8B-41A4-85BC-E290BA6635B3')> 		
        <!--- Contact Information --->
        <cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='3e5ec6e9-5f61-43fd-8d94-aa5a54a6506a', mode="collection", strData=local.strData)>
        
        <!--- Membership Categories --->
        <cfset local.membershipCategoriesFieldSet = application.objCustomPageUtils.renderFieldSet(uid='ae502242-72a2-478c-98de-2b634b51384b', mode="collection", strData=local.strData)>
        <!--- Membership Information --->
        <cfset local.membershipInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='F007A528-B3E3-4152-8260-A329EAB2BFC4', mode="collection", strData=local.strData)>

        <!--- Member Profile --->
        <cfset local.memberProfileFieldSet = application.objCustomPageUtils.renderFieldSet(uid='6818755e-55d3-436e-80bf-d55dd2e15ea1', mode="collection", strData=local.strData)>

        <!--- Areas of Practice --->
        <cfset local.areasofPracticeFieldSet = application.objCustomPageUtils.renderFieldSet(uid='7fdc34d5-4aff-4227-860d-d5498c940898', mode="collection", strData=local.strData)>

        <!--- Questions --->
        <cfset local.questionsFieldSet = application.objCustomPageUtils.renderFieldSet(uid='0fc44481-0ec3-4320-b022-6af848f1e7da', mode="collection", strData=local.strData)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		
		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		
		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>
        
		<cfsavecontent variable="local.headCode">
			<cfoutput>			
			<script language="javascript">
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
				}
                function _FB_validateForm() {
						var _CF_this = document.forms["#variables.formName#"]; 					
						var isExempt = $('input:radio[name=isExempt]:checked').val();  
						var categorySelected = $('##md_#local.membershipCategoryTypeField.COLUMNID# option:selected').text();
						var arrReq = new Array();						

						if (!_FB_hasValue(_CF_this['joinRenew'], 'RADIO')) arrReq[arrReq.length] = 'Please select Type of Application';
                    
						#local.contactInformationFieldSet.jsValidation#

						if (isExempt == 'Yes') {
							if (!_FB_hasValue(_CF_this['GSTExemptionNumber'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your GST Exemption Number.';
						}
						
						#local.membershipCategoriesFieldSet.jsValidation#
                        if (categorySelected != 'Paralegal/Legal Assistant'){
                            #local.membershipInformationFieldSet.jsValidation#
                        }
						if (categorySelected == 'Sustaining' ||
							categorySelected == 'Regular' ||
							categorySelected == 'Associate' ||
							categorySelected == 'Articling Student' ||
							categorySelected == 'Law Professor' || 
							categorySelected == 'University Student' ||
                            categorySelected == 'Retired Member'){

							#local.memberProfileFieldSet.jsValidation#

							#local.areasofPracticeFieldSet.jsValidation#
						}
                        
						if(categorySelected == 'Paralegal/Legal Assistant'){
							if($('###local.PLAAFileField.fieldCode#').val() == ''){
								arrReq[arrReq.length] = 'Please download the Paralegal/Legal Assistant agreement form in red above, fill out the form, and then attach it via the *Select file* button under *Membership Categories';
							}
						}					
					
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);							
							return false;
						}
						$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

                        mc_continueForm($('###variables.formName#'),afterFormLoad);
                        return false;
					}
                    function assignMemberData(memObj){
                        var thisForm = document.forms["#variables.formName#"];
                        var er_change = function(r) {
                            var results = r;
                            if( results.success ){						
								$("###variables.formName#")[0].reset();
								$("##formToFill").show();
                                thisForm['memberNumber'].value = results.membernumber;
                                thisForm['memberID'].value = results.memberid;
								if(parseInt(memObj.isNewRecord)){
									thisForm['m_prefix'].value = results.prefix;
									thisForm['m_firstname'].value = results.firstname;
									thisForm['m_middlename'].value = results.middlename;
									thisForm['m_lastname'].value = results.lastname;
									thisForm['m_professionalsuffix'].value = results.professionalsuffix;
									thisForm['m_company'].value = results.company;
									thisForm['ma_'+results.addresstypeid+'_address1'].value = results.address1;
									thisForm['ma_'+results.addresstypeid+'_address2'].value = results.address2;
									thisForm['ma_'+results.addresstypeid+'_city'].value = results.city;
									thisForm['ma_'+results.addresstypeid+'_stateprov'].value = results.stateid;
									thisForm['ma_'+results.addresstypeid+'_postalcode'].value = results.postalcode;
									
									for (var i = 0; i<results.phoneobj.length; i++) {
										thisForm['mp_'+results.addresstypeid+'_'+results.phoneobj[i].phonetypeid].value = results.phoneobj[i].phone;
									}
									
									thisForm['me_'+results.emailtypeid+'_email'].value = results.email;
									thisForm['mw_'+results.websitetypeid+'_website'].value = results.website;
								}else{
									thisForm['m_firstname'].value = results.firstname;
									thisForm['m_lastname'].value = results.lastname;
								}		                              	
                            }
                            else{ /*alert('not success');*/ }
                        };
                        var objParams = { memberNumber:memObj.memberNumber };
                        TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
                    }                    
                    function loadMember(memNumber){
                        var objParams = { memberNumber:memNumber };
                        assignMemberData(objParams);
                    }
					function checkCaptchaAndValidate(){
						var thisForm = document.forms["#variables.formName#"];
						var status = false;
						var captcha_callback = function(captcha_response){
							if (captcha_response.response && captcha_response.response != 'success') {
								status = false;
							} else {
								status = true;
							}
						}
						if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
							alert('Please enter the correct code shown in the graphic.');
							return false;
						} else {
							#variables.captchaDetails.jsvalidationcode#
						}
						if(status){
							return _FB_validateForm();
						} else {
							alert('Please enter the correct code shown in the graphic.');
							return false;
						}
					}
					function showAdditional(){
						var _CF_this = document.forms["#variables.formName#"]; 	
						var categorySelected = $('##md_#local.membershipCategoryTypeField.COLUMNID# option:selected').text();    
						var references = document.getElementById('references');
						
						if (categorySelected == 'Sustaining' ||
							categorySelected == 'Regular' ||
							categorySelected == 'Associate' ||
							categorySelected == 'Articling Student' ||
							categorySelected == 'Law Professor' ||
							categorySelected == 'University Student' ||
                            categorySelected == 'Retired Member'){

							references.style.display = '';
						} else {
							references.style.display = 'none';
						}
						
						trAfter = $('###local.PLAAFileField.fieldCode#').closest('tr');
						if(categorySelected == 'Paralegal/Legal Assistant'){
							$(trAfter).show();
                            $('##paralegalInfoSection input,##paralegalInfoSection select').val('');
                            $('##paralegalInfoSection').hide();
						} else {
							$(trAfter).hide();
							$('###local.PLAAFileField.fieldCode#').val('');
							$('###local.PLAAFileField.fieldCode#_newFileDetails').html('');
                            $('##paralegalInfoSection').show();
						}
						calcMemCat(categorySelected);
					}
					function calcMemCat(membership) {
						var thisYear = #year(now())#;  
						var callYear = $('##md_#local.yearofcallTypeField.COLUMNID#').val();  
						var isExempt = $('input:radio[name=isExempt]:checked').val();    						
						var membershipDesc = '';						
						
						if (membership == 'Associate') {
							if ((thisYear - callYear) < 5) {
								pricepaid = #variables.memDues["Associate1-4years"]#; 
								membershipDesc =  '#variables.memDesc["Associate1-4years"]#'; 
							} else if (((thisYear - callYear) >= 5) && (thisYear - callYear) < 10) {
								pricepaid = #variables.memDues["Associate5-10years"]#; 
								membershipDesc =  '#variables.memDesc["Associate5-10years"]#'; 
							} else {
								pricepaid = #variables.memDues["Associate10+years"]#; 
								membershipDesc =  '#variables.memDesc["Associate10+years"]#'; 
							}
						} else if (membership == 'Regular') {
							if ((thisYear - callYear) < 5) {
								pricepaid = #variables.memDues["Regular1-4years"]#; 
								membershipDesc =  '#variables.memDesc["Regular1-4years"]#'; 
							} else if (((thisYear - callYear) >= 5) && (thisYear - callYear) < 10) {
								pricepaid = #variables.memDues["Regular5-10years"]#; 
								membershipDesc =  '#variables.memDesc["Regular5-10years"]#'; 
							} else {
								pricepaid = #variables.memDues["Regular10+years"]#; 
								membershipDesc =  '#variables.memDesc["Regular10+years"]#'; 
							}
						} else if (membership == 'Articling Student') { 
							pricepaid = #variables.memDues['ArticlingStudent']#;
							membershipDesc =  '#variables.memDesc["ArticlingStudent"]#'; 
						} else if (membership == 'Law Professor') { 
							pricepaid = #variables.memDues['Professor']#;
							membershipDesc =  '#variables.memDesc["Professor"]#'; 
						} else if (membership == 'Non-Lawyer') {
							pricepaid = #variables.memDues['NonLawyer']#;
							membershipDesc =  '#variables.memDesc["NonLawyer"]#'; 
						} else if (membership == 'Judicial') {
							pricepaid = #variables.memDues['Judicial']#;
							membershipDesc =  '#variables.memDesc["Judicial"]#'; 
						} else if (membership == 'University Student') {
							pricepaid = #variables.memDues['UniversityStudent']#;					
							membershipDesc =  '#variables.memDesc["UniversityStudent"]#'; 
						} else if (membership == 'Paralegal/Legal Assistant') {
							pricepaid = #variables.memDues['PLAA']#;
							membershipDesc =  '#variables.memDesc['PLAA']#'; 
						} else if (membership == 'Sustaining') {
							pricepaid = #variables.memDues['Sustaining']#;	
							membershipDesc =  '#variables.memDesc["Sustaining"]#'; 
                        } else if (membership == 'Retired Member') {
							pricepaid = #variables.memDues['Retired']#;	
							membershipDesc =  '#variables.memDesc["Retired"]#'; 
						} else pricepaid = -1;
						var hst = 0;
						var dynhtml = '<table align="center">';
							if (pricepaid > 0)
								dynhtml += '<tr><td class="bodytext">' + membershipDesc + ': </td><td class="bodyText" align="right">' + formatCurrency(pricepaid) + ' CAD</td></tr>';
							else 
								pricepaid = 0;
						var subtotal = (pricepaid);
						if (isExempt == 'No')
							var hst = parseFloat(((pricepaid) * #variables.taxRate#));
						var total = (subtotal + hst);
							dynhtml += '<tr><td class="bodytext">GST Tax 5%: </td><td class="bodyText" align="right">' + formatCurrency(hst) + ' CAD</td></tr>';
							dynhtml += '<tr><td class="bodytext"><b>Total: </b></td><td class="bodyText" align="right"><b>' + formatCurrency(total) + '</b> CAD</td></tr>';
						dynhtml += '</table>';
						document.getElementById('dyninfo').innerHTML = dynhtml;
						document.getElementById('totalCost').style.display = '';
						if(parseInt(total) == 0){
							$('##isSubmittedVal').val(2);
						} else {
							$('##isSubmittedVal').val(1);
						}
					}					
					function updateCategories(){
						var joinRenew = getSelectedRadio(document.getElementsByName('joinRenew'));

						if (joinRenew == 0){
							$("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('Sustaining')").attr('disabled','disabled');
						}
						
						if (joinRenew == 1){
							var membershipCategory = $('##membershipCategory').val(); 
							$("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('Sustaining')").removeAttr('disabled');
							$("##md_#local.membershipCategoryTypeField.COLUMNID# option[value='']").attr("selected", "selected");
							if(membershipCategory.length > 0) {
								$("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('"+membershipCategory+"')").attr("selected", "selected");	
							}

							showAdditional();

							var categorySelected = $('##md_#local.membershipCategoryTypeField.COLUMNID# option:selected').text();    
							calcMemCat(categorySelected);							
						}
					}
                    $(document).ready(function() {
                        prefillData();
                        $("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('Cancelled Member')").remove();
                        $("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('Honourary Lifetime')").remove();
                        $("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('Non-Member')").remove();
                        $("##md_#local.membershipCategoryTypeField.COLUMNID# option:contains('Staff')").remove();
                        $('##md_#local.membershipCategoryTypeField.COLUMNID#').on('change', function (e) {
                            showAdditional();				
                        });
                        
                        $('##md_#local.yearofcallTypeField.COLUMNID#').on('blur', function() {
                            showAdditional();	
                        });
                        var categorySelected = $('##md_#local.membershipCategoryTypeField.COLUMNID# option:selected').text();    
                        trAfter = $('###local.PLAAFileField.fieldCode#').closest('tr');
                        if(categorySelected == 'Paralegal/Legal Assistant'){
                            $(trAfter).show();
                            $('##paralegalInfoSection input,##paralegalInfoSection select').val('');
                            $('##paralegalInfoSection').hide();
                        }else{							
                            $('###local.PLAAFileField.fieldCode#').val('');
                            $('###local.PLAAFileField.fieldCode#_newFileDetails').html('');
                            $(trAfter).hide();
                            $('##paralegalInfoSection').show();
                        } 				
                    });
                    function prefillData() {
                        var objPrefill = new Object();
                        <cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
                            #toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
                        </cfloop>
                        for (var key in objPrefill) {
                            if (objPrefill.hasOwnProperty(key)) { 
                                $('###variables.formName# ##'+key).val(objPrefill[key]);
                            }
                        }                    
                        showCaptcha();
                    }
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">		
        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <div class="TitleText" style="padding-bottom:15px;">#variables.Organization# - #variables.formNameDisplay#</div>
                <div class="r i frmText">*Denotes required field</div>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="customTable form-horizontal" onsubmit="return checkCaptchaAndValidate();">
                <cfinput type="hidden" name="fa" id="fa" value="processMemberInfo">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                <cfinput type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" > 
                <input type="checkbox" name="iAgree" id="iAgree" value="1" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">               
                    <div class="CPSection">
                        <div class="CPSectionTitle BB">#variables.strPageFields.AccountLocatorTitle#</div>
                        <div class="frmRow1 frmText" style="padding:10px;">
                            <table cellspacing="0" cellpadding="2" border="0" width="100%">
                                <tr>
                                    <td width="175" class="c">
                                        <div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
                                            <button name="btnAddAssoc" type="button" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="frmText">
                                            #variables.strPageFields.AccountLocatorInstructions#
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="CPSection">
                        <div class="CPSectionTitle BB">* Type of Application</div>
                        <div class=" frmRow1 frmText" style="padding:10px;">
                            <table cellspacing="0" cellpadding="4" width="65%" border="0" align="center" style="text-align:center;">
                                <tr>
                                    <td><input name="joinRenew" type="radio" value="join" class="tsAppBodyText" onclick="javascript:updateCategories();" />&nbsp;Join</td>
                                    <td><input name="joinRenew" type="radio" value="renew" class="tsAppBodyText" onclick="javascript:updateCategories();" />&nbsp;Renew</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="CPSection">
                        <div class="CPSectionTitle BB">#local.contactInformationFieldSet.fieldSetTitle#</div>
                        <div class=" frmRow1 frmText" style="padding:10px;">
                            #local.contactInformationFieldSet.fieldSetContent#								
                        </div>
                    </div>

                    <div class="CPSection">
                        <div class="CPSectionTitle BB">GST Exemption</div>
                        <div class="frmRow1 frmText" style="padding:10px;">		
                            <table cellpadding="3" border="0" cellspacing="0">
                                <tbody>
                                    <tr valign="top">
                                        <td class="tsAppBodyText" width="10">*&nbsp;</td>
                                        <td class="tsAppBodyText" nowrap="">Are you GST Exempt?</td>
                                        <td class="tsAppBodyText">
                                        &nbsp;
                                        </td>
                                        <td class="tsAppBodyText">
                                            <cfinput value="Yes" class="tsAppBodyText optionsRadio" name="isExempt"  id="isExempt" type="radio" onClick="showAdditional();"> Yes &nbsp;&nbsp;
                                            <cfinput value="No" class="tsAppBodyText optionsRadio" name="isExempt"  id="isExempt" checked="true" type="radio" onClick="showAdditional();"> No
                                        </td>
                                    </tr>
                                    <tr valign="top">
                                        <td class="tsAppBodyText" width="10">&nbsp;</td>
                                        <td class="tsAppBodyText" nowrap="">Please provide your GST Exemption Number.</td>
                                        <td class="tsAppBodyText">
                                        &nbsp;
                                        </td>
                                        <td class="tsAppBodyText">
                                            <cfinput type="text" name="GSTExemptionNumber"  id="GSTExemptionNumber" class="tsAppBodyText smallbox" maxlength="50" size="50" />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>															
                        </div>
                    </div>
                    
                    <div class="CPSection">
                        <div class="CPSectionTitle BB">*#local.membershipCategoriesFieldSet.fieldSetTitle#</div>
                        <div class="subCPSectionArea1 BB">
                            <div class="popup frmText"><em>Please</em> <a href="/?pg=MembershipCategories" class="popup" target="_blank">click here</a> <em>to view the descriptions of all the membership categories and their fees.</em></div>
                        </div>
                        <div class="frmRow1 frmText" style="padding:10px;">
                            #local.membershipCategoriesFieldSet.fieldSetContent#
                            <div class="frmRow1 frmText" style="padding:10px;">
                                <strong>The STLA membership year is from February 1st to January 31st of each year. Members joining part way through the year will be invoiced for the remainder of the year (pro-rated) and will pre-pay for the following membership year.</strong>
                            </div>								
                        </div>
                    </div>

                    <div class="CPSection" id="paralegalInfoSection">
                        <div class="CPSectionTitle BB">#local.membershipInformationFieldSet.fieldSetTitle#</div>
                        <div class="frmRow1 frmText" style="padding:10px;">
                            #local.membershipInformationFieldSet.fieldSetContent#
                        </div>
                    </div>

                    <div class="CPSection" id="totalCost" style="display:none;">
                        <div class="CPSectionTitle BB">Total Cost</div>
                        <div class="frmText frmRow1 BB">
                            <div id="dyninfo" class="BodyText"></div>
                        </div>
                    </div>	
                    
                    <div id="references" style="display: none;">
                        <div class="CPSection">
                            <div class="CPSectionTitle BB">#local.memberProfileFieldSet.fieldSetTitle#</div>
                            <div class="frmRow1 frmText" style="padding:10px;">
                                <div class="BodyText subSectionArea subSectionTitle">General Practice Areas:</div>
                                <p>
                                    Please indicate the percentage split of your practice dedicated to the following 3 areas of law. <br/>
                                    For example 25 Civil Litigation, 25 Criminal Law, and 50 Family Law for a total of 100%.  <br/>
                                    <br/>
                                    If any categories are not applicable, please enter <strong>0</strong>.
                                </p>
                                #local.memberProfileFieldSet.fieldSetContent#
                            </div>
                        </div>

                        <div class="CPSection">
                            <div class="CPSectionTitle BB">#local.areasofPracticeFieldSet.fieldSetTitle#</div>
                            <div class=" frmRow1 frmText" style="padding:10px;">
                                #local.areasofPracticeFieldSet.fieldSetContent#
                            </div>
                        </div>
                    </div>
                    
                    <div class="CPSection">
                        <div class="CPSectionTitle BB">#local.questionsFieldSet.fieldSetTitle#</div>
                        <div class="frmRow1 frmText" style="padding:10px;">
                            <strong>Please contact the STLA office with questions regarding fees in the Comment box below.</strong>
                            #local.questionsFieldSet.fieldSetContent#
                        </div>
                    </div>
                    <div class="CPSection">
                        <div class="frmRow1 frmText" style="padding:10px;">
                            #variables.captchaDetails.htmlContent#
                        </div>
                    </div>
                                        
                    <div id="formButtons">
                        <div style="padding:10px;">
                            <div align="center" class="frmButtons">
                                <input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
                            </div>
                        </div>
                    </div>
                </cfform>
                <cfif  NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) AND NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                    <script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
                </cfif>              
            </cfoutput>
        </cfsavecontent>		
		<cfreturn local.returnHTML>
	</cffunction>
    <cffunction name="showPayment" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">
		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

        <cfset local.strData = {}>        

        <cfif structKeyExists(event.getCollection(), 'iAgree') OR (NOT structKeyExists(session, "captchaEntered") AND (NOT Len(event.getValue('captcha',''))) OR application.objCustomPageUtils.validateCaptcha(code=event.getValue('captcha'),captcha=event.getValue('captcha_check')).response NEQ "success")>
            <cfset local.response = "spam">
            <cfreturn local.response>
        </cfif>

        <cfscript>				
            variables.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
                siteid=variables.siteID,
                profilecode=variables.profile_1._profileCode,
                pmid = variables.useMID,
                showCOF = variables.useMID EQ session.cfcUser.memberData.memberID,
                usePopupDIVName='paymentTable'
            );
        </cfscript>
		
        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <script>
                    function checkPaymentMethod() {
						var rdo = document.forms["#variables.formName#"].payMeth;
						if (rdo[0].checked) {//credit card
							document.getElementById('CCInfo').style.display = '';
							document.getElementById('CheckInfo').style.display = 'none';
						}  
						else if (rdo[1].checked) {//cheque
							document.getElementById('CCInfo').style.display = 'none';
							document.getElementById('CheckInfo').style.display = '';
						}  						
					}
					function getMethodOfPayment() {
						var btnGrp = document.forms['#variables.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					function _validate() {
						var _CF_this = document.forms["#variables.formName#"];
						var arrReq = new Array();
				
						if (!_FB_hasValue(_CF_this['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#variables.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= _CF_this['confirmationStatement'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}						
				
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
                        mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
                </script>
                <cfif len(variables.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(variables.profile_1.strPaymentForm.headCode)#">
				</cfif>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <div id="paymentTable">
                    <div id="payerrDIV" style="display:none;margin:6px 0;"></div>
                    <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _validate()">
                        <cfinput type="hidden" name="fa" id="fa" value="processPayment">
                        <cfloop collection="#arguments.event.getCollection()#" item="local.key">
                            <cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
                                and NOT listFindNoCase("fa,isSubmitted,btnSubmit",local.key) 
                                and left(local.key,4) neq "fld_">
                                <cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
                            </cfif>
                        </cfloop>
                        <div class="CPSection">
                            <div class="CPSectionTitle">*Method of Payment</div>
                            <div class="P">
                                <table cellpadding="2" cellspacing="0" width="100%" border="0">
                                    <tr valign="top">
                                        <td colspan="2">Please select your preferred method of payment from the options below.</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table cellpadding="2" cellspacing="0" width="100%" border="0">
                                                <tr>
                                                    <td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
                                                    <td>Credit Card</td>
                                                </tr>
                                                <tr>
                                                    <td width="25"><input value="Cheque" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
                                                    <td>Cheque</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div id="CCInfo" style="display:none;" class="CPSection">
                            <div class="CPSectionTitle">Credit Card Information</div>
                            <div class="PL PR frmText paymentGateway BT BB">
                                <cfif len(variables.profile_1.strPaymentForm.inputForm)>
                                    <div>#variables.profile_1.strPaymentForm.inputForm#</div>
                                </cfif>
                            </div>
                            
                            <div class="P">
                                <div class="PB">* Please confirm the statement below:</div>
                                <table width="100%">
                                    <tr>
                                        <td width="25"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
                                        <td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">AUTHORIZE</button></div>
                        </div>
                        
                        <div id="CheckInfo" style="display:none;" class="CPSection">
                            <div class="CPSectionTitle">Cheque Information</div>
                            <div class="P">                        
                                    Please <strong>print</strong> the confirmation and <strong>send</strong> it with your cheque to the following address:<br /><br />
                                
                                    <strong>Saskatchewan Trial Lawyers Association</strong><br />
                                    P.O. Box 1482<br />
                                    Saskatoon, SK, S7K 3P7	
                                        
                            </div>
                            <div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button></div>
                        </div>
                    </cfform>
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">
		
        <cfset var local = structNew()>

		<cfset local.membershipCategoryTypeField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
        <cfset local.yearofcallTypeField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='b10161d3-ae90-4eea-b5ef-6ff13408fba0')> 
        <cfset local.commentsTypeField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='833c92b7-0cf4-4fe0-bd0f-74f6723f137c')> 
                
        <cfset local.membershipCategorySelectedValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(variables.orgid,'Membership Category',event.getValue('md_#local.membershipCategoryTypeField.COLUMNID#', ''))>
        <cfset local.yearOfCall = event.getValue('md_#local.yearofcallTypeField.COLUMNID#', '')/>
        <cfset local.comments = event.getValue('md_#local.commentsTypeField.COLUMNID#', '')/>

        <cfset local.membershipInformationFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='F007A528-B3E3-4152-8260-A329EAB2BFC4', mode="confirmation", strData=arguments.event.getCollection())>

        <!--- Contact Information --->
        <cfset local.contactInformationFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='3e5ec6e9-5f61-43fd-8d94-aa5a54a6506a', mode="confirmation", strData=arguments.event.getCollection())>
        
        <!--- Membership Categories --->
        <cfset local.membershipCategoriesFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='ae502242-72a2-478c-98de-2b634b51384b', mode="confirmation", strData=arguments.event.getCollection())>

        <!--- Member Profile --->
        <cfset local.memberProfileFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='6818755e-55d3-436e-80bf-d55dd2e15ea1', mode="confirmation", strData=arguments.event.getCollection())>

        <!--- Areas of Practice --->
        <cfset local.areasofPracticeFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='7fdc34d5-4aff-4227-860d-d5498c940898', mode="confirmation", strData=arguments.event.getCollection())>

        <!--- Questions --->
        <cfset local.questionsFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='0fc44481-0ec3-4320-b022-6af848f1e7da', mode="confirmation", strData=arguments.event.getCollection())>

        <cfscript>
            local.memberShipDues = 0;	
            local.memberShipName = 	local.membershipCategorySelectedValue;	
            local.gst = 0.00;
            switch(local.membershipCategorySelectedValue){	
                    
                case 'Regular': 
                    local.memberShipName = 'Regular Membership';							
                    
                    if ((year(now()) - local.yearOfCall) < 5) {
                        local.memberShipDues = variables.memDues["Regular1-4years"]; 
                        local.memberShipName = local.memberShipName & " - Less than 5 years at the bar";															
                    } else if (((year(now()) - local.yearOfCall) >= 5) AND ((year(now()) - local.yearOfCall)) < 10) {
                        local.memberShipDues = variables.memDues["Regular5-10years"]; 
                        local.memberShipName = local.memberShipName & " - Less than 10 years at the bar";
                    } else {
                        local.memberShipDues = variables.memDues["Regular10+years"]; 
                        local.memberShipName = local.memberShipName & " - 10 or more years at the bar";																	
                    }
                    break;
                case 'Associate': 
                    local.memberShipName = 'Associate Membership'; 	
                    if ((year(now()) - local.yearOfCall) < 5) {
                        local.memberShipDues = variables.memDues["Associate1-4years"]; 
                        local.memberShipName = local.memberShipName & " - Less than 5 years at the bar";
                    } else if (((year(now()) - local.yearOfCall) >= 5) AND ((year(now()) - local.yearOfCall)) < 10) {
                        local.memberShipDues = variables.memDues["Associate5-10years"]; 
                        local.memberShipName = local.memberShipName & " - Less than 10 years at the bar";
                    } else {
                        local.memberShipDues = variables.memDues["Associate10+years"]; 
                        local.memberShipName = local.memberShipName & " - 10 or more years at the bar";																	
                    }
                    break;
                case 'Articling Student': local.memberShipDues = variables.memDues["ArticlingStudent"]; local.memberShipName = 'Law Graduate & Articling Student Membership'; break;
                case 'Law Professor': local.memberShipDues = variables.memDues["Professor"]; 		local.memberShipName = 'Law Professor Membership'; break;
                case 'Non-Lawyer': local.memberShipDues = variables.memDues["NonLawyer"] ; 	local.memberShipName = 'Non-Lawyer Membership'; break;
                case 'Judicial': local.memberShipDues = variables.memDues["Judicial"]; 		local.memberShipName = 'Judicial Membership'; break;
                case 'University Student': local.memberShipDues = variables.memDues["UniversityStudent"]; 	local.memberShipName = 'University Student Membership';	break;
                case 'Paralegal/Legal Assistant': local.memberShipDues = variables.memDues['PLAA']; 			local.memberShipName = 'Paralegal/Legal Assistant';	break;
                case 'Sustaining': local.memberShipDues = variables.memDues["Sustaining"]; 	local.memberShipName = 'Sustaining Membership'; break;
                case 'Retired Member': local.memberShipDues = variables.memDues["Retired"]; 	local.memberShipName = 'Retired Member'; break;

            }
            
            if (event.getValue('isExempt') EQ "No") {
                local.gst =  local.memberShipDues * variables.taxRate;
            }
            local.totalAmount = local.memberShipDues + local.gst;
        </cfscript>
        <cfsavecontent variable="local.name">
            #event.getValue('m_firstname','')# <cfif len(trim(event.getValue('m_middlename','')))>#event.getValue('m_middlename','')# </cfif>#event.getValue('m_lastname','')#
        </cfsavecontent>
        <cfset variables.ORGEmail.SUBJECT = variables.ORGEmail.SUBJECT & " - From: " & local.name />

        <cfsavecontent variable="local.invoice">
        <cfoutput>
            #variables.pageCSS#
            <!-- @accResponseMessage@ -->
            <!-- @accMessageToOrg@ -->
            <p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
            <div id="mailContent"> 
                #local.contactInformationFieldSetConfirmation.fieldSetContent#
                #local.membershipCategoriesFieldSetConfirmation.fieldSetContent#
                <cfif local.membershipCategorySelectedValue NEQ 'Paralegal/Legal Assistant'>
                    #local.membershipInformationFieldSetConfirmation.fieldSetContent#
                </cfif>
                <cfif local.membershipCategorySelectedValue EQ 'Sustaining' OR
                    local.membershipCategorySelectedValue EQ 'Regular' OR
                    local.membershipCategorySelectedValue EQ 'Associate' OR
                    local.membershipCategorySelectedValue EQ 'Articling Student' OR
                    local.membershipCategorySelectedValue EQ 'Law Professor' OR
                    local.membershipCategorySelectedValue EQ 'University Student' OR
                    local.membershipCategorySelectedValue EQ 'Retired Member'>

                    #local.memberProfileFieldSetConfirmation.fieldSetContent#
                    #local.areasofPracticeFieldSetConfirmation.fieldSetContent#							
                </cfif>
                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">GST NUMBER</td>
                        </tr>
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">						
                                    <tbody>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">GST Number: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                R107956377&nbsp;
                                            </td>
                                        </tr>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Are you GST Exempt?: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #event.getValue('isExempt')#&nbsp;
                                            </td>
                                        </tr>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">GST Exemption Number: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #event.getValue('GSTExemptionNumber')#&nbsp;
                                            </td>
                                        </tr>
                                        
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <br/>
                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">MEMBERSHIP STATEMENT</td>
                        </tr>
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">						
                                    <tbody>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">I Agree to the membership statement: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #event.getValue('consent','I Agree')#&nbsp;
                                            </td>
                                        </tr>
                                        
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <br/>
                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">MEMBERSHIP LEVEL</td>
                        </tr>
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">						
                                    <tbody>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">#local.memberShipName#: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #dollarFormat(local.memberShipDues)#
                                            </td>
                                        </tr>
                                        
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <br/>
                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">TOTAL</td>
                        </tr>
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">						
                                    <tbody>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Subtotal Amount: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #dollarFormat(local.memberShipDues)#&nbsp;
                                            </td>
                                        </tr>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">GST Tax 5%: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #dollarFormat(local.gst)#&nbsp;
                                            </td>
                                        </tr>
                                        <tr valign="top">
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Total Amount: &nbsp;</td>
                                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                #dollarFormat(local.totalAmount)#&nbsp;
                                            </td>
                                        </tr>										
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <cfif local.totalAmount gt 0>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                        <tbody>
                            <tr>
                                <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">PAYMENT INFORMATION</td>
                            </tr>
                            <tr>
                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                    <table cellpadding="3" border="0" cellspacing="0">						
                                        <tbody>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Payment Type: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    <cfif event.getValue('payMeth','CC') EQ 'CC'>Credit Card
                                                        <cfset arguments.event.setValue('p_#variables.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)))) />
                                                        <cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid') gt 0>
                                                            <cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
                                                                    mppid     = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'),
                                                                    memberID  = val(variables.useMID),
                                                                    profileID = variables.profile_1._profileID) />
                                                            - #local.qrySavedInfoOnFile.detail#
                                                        </cfif>
                                                    <cfelse>Cheque</cfif>
                                                </td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </cfif>
                <cfif len(local.comments)>
                    <br/>
                    #local.questionsFieldSetConfirmation.fieldSetContent#
                </cfif>
            </div>
        </cfoutput>
        </cfsavecontent>

        <!--- ---------------------- --->
        <!--- Payment and accounting --->
        <!--- ---------------------- --->
        <cfset local.strAccTemp = { totalPaymentAmount=local.totalAmount, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.event.getCollection() } >

        <cfif local.strAccTemp.totalPaymentAmount gt 0 and event.getValue('payMeth','') eq "CC">
            <cfset local.strAccTemp.payment = { detail=variables.profile_1._description, amount=local.strAccTemp.totalPaymentAmount, profileID=variables.profile_1._profileID, profileCode=variables.profile_1._profileCode }>
        </cfif>			
        <cfif event.getValue('isExempt') EQ "No">
            <cfset local.accountCode = "DUESTAX">
        <cfelse>
            <cfset local.accountCode = "DUES">
        </cfif>
        <cfset local.strAccTemp.revenue = [ { revenueGLAccountCode=local.accountCode, detail=variables.profile_1._description, amount=local.totalAmount } ]>

        <cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
        <cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
        <cfset local.newMemberNumber = ''>	

        <!--- -------------------- --->
        <!--- UPDATE MEMBER RECORD --->
        <!--- -------------------- --->				
        <cftry>
            <cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.event.getCollection())>
            
            <cfif local.membershipCategorySelectedValue EQ 'Paralegal/Legal Assistant'>                
                <cfsavecontent variable="local.headCode">
                    <cfoutput>					
                        <script type="text/javascript">
                            $(document).ready(function(){
                                if (typeof arrUploaders !== 'undefined') {
                                    $.each(arrUploaders, function() {
                                        this.uploader.bind('BeforeUpload', function(uploader, file) {
                                            uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
                                        });
                                        this.uploader.start();								
                                    });
                                }
                            });
                        </script>
                    </cfoutput>
                </cfsavecontent>
                <cfhtmlhead text="#local.headCode#">
            </cfif>
            
            <cfif local.strResult.success>
                <cfset local.recordUpdated = true>
                <cfset local.newMemberNumber = local.strResult.membernumber>
            <cfelse>
                <cfthrow message="Unable to save member.">
            </cfif>
            <cfcatch type="Any">
                <cfset application.objError.sendError(cfcatch=cfcatch)>
                <cfset local.recordUpdated = false>
            </cfcatch>
        </cftry>

        <!--- email to member --->
        <cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
                <hr />
                #local.invoice#
            </cfoutput>
        </cfsavecontent>

        <cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
            emailfrom={ name="", email=variables.memberEmail.from },
            emailto=[{ name="", email=variables.memberEmail.to }],
            emailreplyto=variables.ORGEmail.to,
            emailsubject=variables.memberEmail.SUBJECT,
            emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #variables.formNameDisplay#",
            emailhtmlcontent=local.mailContent,
            siteID=variables.siteID,
            memberID=val(variables.useMID),
            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
            sendingSiteResourceID=this.siteResourceID
            )>

        <cfset local.emailSentToUser = local.responseStruct.success>

        <cfif local.newMemberNumber eq ''>
            <cfset local.newMemberNumber = event.getValue('membernumber', '')>			
        </cfif>	
        <cfsavecontent variable="local.memberNumberOrg">
            <p>MemberNumber Found/Created in Account Lookup: <b>#event.getValue('membernumber', '')#</b></p>
            <p>MemberNumber of Final Member Record: <b>#local.newMemberNumber#</b></p>
        </cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif NOT local.emailSentToUser>
                    <p style="color:red;">We were not able to send #local.name# an e-mail confirmation.</p>
                </cfif>
                
                #replaceNoCase(replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage),'<!-- @accMessageToOrg@ -->',local.memberNumberOrg)#	
            </cfoutput>
        </cfsavecontent>

        <cfscript>
            local.arrEmailTo = [];
            local.ORGEmail.to = replace(variables.ORGEmail.to,",",";","all");
            local.toEmailArr = listToArray(variables.ORGEmail.to,';');
            for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
                local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
            }
            local.responseStruct = application.objEmailWrapper.sendMailESQ(
                emailfrom={ name="", email=variables.ORGEmail.from },
                emailto=local.arrEmailTo,
                emailreplyto=variables.ORGEmail.from,
                emailsubject=variables.ORGEmail.SUBJECT,
                emailtitle=event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
                emailhtmlcontent=local.mailContent,
                siteID=event.getTrimValue('mc_siteinfo.siteID'),
                memberID=val(event.getTrimValue('mc_siteinfo.sysMemberID')),
                messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                sendingSiteResourceID=this.siteResourceID
            );
        </cfscript>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<cfif isDefined("local.invoice")>
					<div>This page has been emailed to the email address on file. If you would like you could also print the page out as a receipt.</div>
					<br />
					<br />
					<div class="BodyText">#replaceNoCase(replaceNoCase(replaceNoCase(local.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
				</cfif>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
    
    <cffunction name="showError" access="private" output="false" returntype="string">
        <cfargument name="errorCode" type="string" required="true">

        <cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>    
                <div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
                <div class="tsAppSectionContentContainer">
                    <cfif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
                    <cfelseif arguments.errorCode eq "spam"> 
                        Your submission was blocked and will not be processed at this time.   
                    <cfelse>
                        An error occurred. Please contact the association or try again later.
                    </cfif>
                </div>
            </cfoutput>
        </cfsavecontent>
        <cfreturn local.returnHTML>
    </cffunction>
    <cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
        <cfset local.isExist = false/>
        <cfset local.strData = {}>

        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, arguments.step)>
            <cfset local.strData = session.formFields[arguments.step]/>
        </cfif>			

		<cfreturn local.strData>
	</cffunction>
</cfcomponent>