<cfinclude template="../commonSWRegReceiptJS.cfm">
<cfinclude template="../commonSWRegStyles.cfm">

<cfoutput>
<div class="swReg">
	<div class="swreg-d-flex swreg-mb-2">
		<div class="swreg-font-size-xl swreg-font-weight-bold">Registration Complete</div>
		<a href="javascript:window.print();" class="swreg-ml-auto"><i class="icon-print icon-large"></i></a>
	</div>
	<div class="swreg-mb-3">#local.onReceiptStatement#</div>
	<div class="swreg-mb-5 regsuccess">#local.onReceiptStatementEmail#</div>

	<cfif arguments.qrySWP.handlesOwnPayment is 1>
		<div class="swreg-d-flex swreg-mb-5">
			<div class="swreg-col">
				<form name="frmResendReceipt" id="frmResendReceipt" onsubmit="sendReceipt();return false;">
					<div class="swreg-d-flex swreg-flex-sm-column">
						<div class="swreg-col-auto swreg-align-self-center swreg-align-self-sm-start">Email this receipt to: </div>
						<div class="swreg-col-auto">
							<div class="swreg-input-append">
								<input type="text" name="sendToEmail" id="sendToEmail" class="swreg-formcontrol" value="" size="30" value="" placeholder="Email Address" maxlength="200" autocomplete="off">
								<button type="submit" name="btnResendReceipt" id="btnResendReceipt" class="swreg-add-on swreg-font-size-md swreg-appendbtn" style="width:65px!important;">Send</button>
							</div>
							<div id="swRegSendPmtReceiptMsg" class="swreg-font-size-sm" style="display:none;"></div>
						</div>
					</div>
				</form>
			</div>
			<a href="/?pg=semwebCatalog&panel=browse" class="swreg-ml-auto swreg-text-decoration-none swreg-font-size-sm hidden-phone">
				<i class="icon-arrow-left"></i> Back to #arguments.qrySWP.brandHomeTab#
			</a>
		</div>
	</cfif>

	<div class="swreg-d-flex swreg-mb-3">
		<div class="swreg-font-size-xl swreg-font-weight-bold">Registration(s)</div>
		<cfif local.strReceipt.keyExists("qryAdditionalFees")>
			<div class="swreg-card swreg-ml-auto" style="box-shadow:none;border:none;background-color:##f7f7f7;">
				<div class="swreg-card-body" style="width:220px;">
					<div class="swreg-d-flex swreg-font-weight-bold swreg-mb-2">
						<div class="swreg-col">Subtotal:</div>
						<div class="swreg-ml-auto">#DollarFormat(local.strReceipt.totalAmountOnReceipt)##local.strReceipt.displayedCurrencyType#</div>
					</div>
					<div class="swreg-d-flex swreg-font-weight-bold swreg-mb-2">
						<div class="swreg-col">#local.strReceipt.qryAdditionalFees.additionalFeesLabel#:</div>
						<div class="swreg-ml-auto">#DollarFormat(local.strReceipt.qryAdditionalFees.additionalFees)##local.strReceipt.displayedCurrencyType#</div>
					</div>
					<div class="swreg-d-flex swreg-font-weight-bold">
						<div class="swreg-col">Total:</div>
						<div class="swreg-ml-auto">#DollarFormat(local.strReceipt.qryPaymentTransaction.amount)##local.strReceipt.displayedCurrencyType#</div>
					</div>
				</div>
			</div>
		<cfelse>
			<div class="swreg-ml-auto swreg-font-weight-bold swreg-font-size-xl">Total: #DollarFormat(local.strReceipt.totalAmountOnReceipt)##local.strReceipt.displayedCurrencyType#</div>
		</cfif>
	</div>

	<cfoutput query="local.strReceipt.qryItemsForReceiptSorted" group="memberID">
		<div class="swreg-card swreg-mt-3">
			<div class="swreg-card-header swreg-bg-whitesmoke swreg-pb-1">
				<div class="swreg-font-size-lg swreg-font-weight-bold">#local.strReceipt.qryItemsForReceiptSorted.memberName#</div>
			</div>
			<div class="swreg-card-body">
				<cfset local.thisRowNum = 0>
				<cfoutput>
					<cfset local.thisRowNum++>
					<cfset local.swItemType = getToken(local.strReceipt.qryItemsForReceiptSorted.item,1,'-')>
					<cfset local.strThisProgram = duplicate(local.strReceipt.strRegProgram[local.strReceipt.qryItemsForReceiptSorted.item])>

					<div id="swRegKey#local.strReceipt.qryItemsForReceiptSorted.itemKey#" class="swreg-d-flex swreg-mb-3 swRegReceiptItem">
						<div class="swreg-col-auto swreg-font-weight-bold">#local.thisRowNum#.</div>
						<div class="swreg-col">
							<div class="swreg-font-weight-bold">
								#encodeForHTML(local.strThisProgram.programName)#
							</div>
							<cfif len(local.strThisProgram.programSubTitle)><div class="swreg-font-size-sm swreg-text-dim">#encodeForHTML(local.strThisProgram.programSubTitle)#</div></cfif>
							<cfif local.swItemType EQ 'SWL'>
								<div class="swreg-font-size-sm swreg-mt-1">
									#DateFormat(local.strThisProgram.StartDate,"dddd, mmmm d, yyyy")#<br/>
									#replace(TimeFormat(local.strThisProgram.startDate,"h:mm TT"),":00","")#-#replace(TimeFormat(local.strThisProgram.enddate,"h:mm TT"),":00","")# #ucase(local.strThisProgram.tz)# #local.strThisProgram.tzstr#
								</div>
							</cfif>
						</div>
						<div class="swreg-col-auto swreg-mw-25 swreg-text-right">
							<cfif local.swItemType EQ 'SWL'>
								<div class="swreg-d-flex swreg-mb-2">
									<span class="swreg-col swreg-text-dim swreg-font-size-sm hidden-phone">Add to Calendar:</span>
									<div class="swreg-col-auto swreg-ml-auto">
										<a href="#local.strThisProgram.icalURL#" class="swreg-text-decoration-none swreg-ml-1 swreg-mr-1" target="_blank">
											<i class="icon-windows icon-large swreg-text-dim" title="Outlook Calendar"></i>
										</a>
										<a href="#local.strThisProgram.gcalURL#" class="swreg-text-decoration-none swreg-ml-1 swreg-mr-1" target="_blank">
											<i class="icon-google-plus-sign icon-large swreg-text-dim" title="Google Calendar"></i>
										</a>
										<a href="#local.strThisProgram.icalURL#" class="swreg-text-decoration-none swreg-ml-1 swreg-mr-1" target="_blank">
											<i class="icon-apple icon-large swreg-text-dim" title="iCal Calendar"></i>
										</a>
									</div>
								</div>
							</cfif>
							<span class="swreg-font-weight-bold swreg-font-size-lg">#DollarFormat(local.strReceipt.qryItemsForReceiptSorted.discountAppliedTotal)##local.strReceipt.displayedCurrencyType#</span>
						</div>
					</div>
				</cfoutput>
			</div>
		</div>
	</cfoutput>

	<div class="swreg-card swreg-mt-5">
		<div class="swreg-card-header swreg-bg-whitesmoke swreg-pb-1">
			<div class="swreg-font-size-lg swreg-font-weight-bold">Payment Information</div>
		</div>
		<div class="swreg-card-body swreg-pb-2">
			<cfif arguments.qrySWP.handlesOwnPayment is 1>
				<cfif local.strReceipt.totalAmountOnReceipt gt 0 and local.strReceipt.qryPaymentTransaction.recordcount gt 0>
					#dollarFormat(local.strReceipt.qryPaymentTransaction.amount)# #local.strReceipt.qryPaymentTransaction.detail#<br/>
					Payment Date: #dateformat(local.strReceipt.qryPaymentTransaction.transactionDate,"m/d/yyyy")# #timeformat(local.strReceipt.qryPaymentTransaction.transactionDate,"h:mm tt")#
				<cfelseif local.strReceipt.qryPaymentTransaction.recordcount is 0 and local.strReceipt.qryPaymentGateway.gatewayID is 11>
					No payment was made.
					<cfif local.strReceipt.totalAmountOnReceipt gt 0>
						<br/><br/>
						<b>Payment instructions:</b><br/>
						<cfif len(local.strReceipt.qryPaymentGateway.paymentInstructions)>
							#local.strReceipt.qryPaymentGateway.paymentInstructions#
						<cfelse>
							No instructions have been provided. Contact the association for payment instructions.
						</cfif>
					</cfif>
				<cfelseif local.strReceipt.totalAmountOnReceipt gt 0 and local.strReceipt.qryPaymentTransaction.recordcount is 0>
					No payment was made.
				<cfelse>
					No payment was due.
				</cfif>
			<cfelse>
				<cfif local.strReceipt.totalAmountOnReceipt gt 0 and local.strReceipt.qryDepoTransactions.recordCount>
					Amount: #dollarFormat(local.strReceipt.totalAmountOnReceipt)##local.strReceipt.displayedCurrencyType#<br/>
					<cfset local.SWChargeDetail = application.mcCacheManager.sessionGetValue(keyname='SWChargeDetail', defaultValue="")>
					<cfif len(local.SWChargeDetail)>
						Paid by <b>#local.SWChargeDetail# <span style="padding-left:2em;">#DateFormat(local.strReceipt.qryDepoTransactions.datePurchased,"m/d/yyyy")#</span></b><br/>
						<div class="swreg-font-size-sm swreg-text-dim">PLEASE KEEP A COPY OF THIS RECEIPT FOR YOUR RECORDS</div>
					</cfif>
				<cfelse>
					No payment was due.
				</cfif>
			</cfif>
			<br/><br/>
			#local.strReceipt.PurchaserName#<br/>
			<cfif len(local.strReceipt.PurchaserCompany)>#local.strReceipt.PurchaserCompany#<br/></cfif>
			#local.strReceipt.PurchaserAddress#
		</div>
	</div>
</div>
</cfoutput>