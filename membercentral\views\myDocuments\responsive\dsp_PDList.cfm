<cfoutput>
    <div style="padding:5px">
        <h5>Purchased Documents</h5>   

        #getPaginationHTML(topOrBottom="top",recordCount=local.qryAllPurchasedDocuments.recordCount,itemCount=val(local.qryAllPurchasedDocuments.itemCount), docType="document",maxRows=local.maxRows,startRow=local.startrow,tab='PD',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any documents.", dspSaveBtn="#len(local.dropboxAppKey) gt 0#")#
        <!--- results table --->
        <cfoutput query="local.qryAllPurchasedDocuments">
            <cfset local.expertFileName = local.objTSDocument.getDownloadDocFileName(documentID=local.qryAllPurchasedDocuments.documentid, expertname=local.qryAllPurchasedDocuments.expertname, documentDate=local.qryAllPurchasedDocuments.DocumentDate)>
            <div class="row-fluid tsDocViewerDocument s_row <cfif local.qryAllPurchasedDocuments.currentrow mod 2 is 0>s_row_alt</cfif>" style="clear:both;overflow:auto;" data-tsdocumentid="#local.qryAllPurchasedDocuments.documentid#">
                <div class="span12">
                    <div class="row-fluid">
                        <div class="span10">
                            <a href="javascript:docViewer(#local.qryAllPurchasedDocuments.documentid#,1);"><img src="/assets/common/images/search/page_acrobat.png" width="20" height="20" border="0" align="left" alt="[Adobe Acrobat Document]"><b><cfif len(trim(local.qryAllPurchasedDocuments.expertname))>#trim(local.qryAllPurchasedDocuments.expertname)#<cfelse>Unknown Expert</cfif></b></a>
                            <div class="s_dtl">
                                <div class="hidden-phone">
                                Document ###local.qryAllPurchasedDocuments.documentid# - #DateFormat(local.qryAllPurchasedDocuments.DocumentDate, "mmm d, yyyy")# - #local.qryAllPurchasedDocuments.state#<br/>
                                #local.qryAllPurchasedDocuments.style# ... <cfif len(trim(local.qryAllPurchasedDocuments.causedesc))>... #local.qryAllPurchasedDocuments.causedesc#</cfif>
                                <cfif len(trim(local.qryAllPurchasedDocuments.notes))><br/>Notes: #local.qryAllPurchasedDocuments.notes#</cfif>
                                </div>
                                <div align="left"><b class="s_label">Purchased: #dateformat(local.qryAllPurchasedDocuments.dateadded,"m/d/yyyy")#</b></div>
                            </div>
                        </div>
                        <div class="span2 s_act">
                            <div class="s_opt" style="line-height: 1.5em;">
                                <cfif len(local.qryAllPurchasedDocuments.uploadpdfdate)>
                                    <button name="pdf_btn_#local.qryAllPurchasedDocuments.currentRow#" type="button" class="btn btn-secondary btn-sm" onClick="downloadDocImmedate(#local.qryAllPurchasedDocuments.DocumentID#,'pdf');" style="width:150px;"><i class="icon-download-alt" title="Download PDF"></i> Download PDF</button>
                                </cfif>
                                <cfif len(local.qryAllPurchasedDocuments.uploadpdfdate) and len(local.dropboxAppKey)>
                                    <button name="dropb_btn_#local.qryAllPurchasedDocuments.currentRow#" id="dropb_btn_#local.qryAllPurchasedDocuments.currentRow#" type="button" class="btn btn-secondary btn-sm drop-box-file-list" onClick="doDropboxSave(#local.qryAllPurchasedDocuments.documentID#,'#local.expertFileName#.pdf','pdf',#local.qryAllPurchasedDocuments.currentRow#);"  data-elmid="#local.qryAllPurchasedDocuments.documentID#" data-elmname="#local.expertFileName#.pdf" data-elmext="pdf" data-rowid="#local.qryAllPurchasedDocuments.currentRow#" style="width:150px;margin-top:10px;"><img src="/assets/common/images/dropbox-icon-ios.png" id="dropb_img_#local.qryAllPurchasedDocuments.currentRow#" data-toggle="tooltip" width="15" height="15" border="0" title="Save to Dropbox"/> Save to Dropbox</button>
                                 </cfif>
                            </div>
                        </div>
                    </div>
                </div>                
            </div>
        </cfoutput>
        #getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllPurchasedDocuments.recordCount,itemCount=val(local.qryAllPurchasedDocuments.itemCount), docType="document",maxRows=local.maxRows,startRow=local.startrow,tab='PD',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any documents.")#
    </div>
</cfoutput>