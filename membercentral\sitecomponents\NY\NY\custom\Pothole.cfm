<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";

	// default and defined custom page custom fields
	local.arrCustomFields = [];

	local.tmpField = { name="InstructionText", type="STRING", desc="Instruction text", value="Please complete the following Big Apple Research Request Order Form. If you have questions about your order, please call Big Apple Pothole Protection Corporation at 212-349-5890 during business hours." };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Staff Confirmation Email", value="<EMAIL>,<EMAIL>,<EMAIL>" };
	arrayAppend(local.arrCustomFields, local.tmpField);	

	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	// set page defaults
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmBigApple',
		formNameDisplay='Big Apple Search',
		orgEmailTo='#local.strPageFields.StaffConfirmationEmail#',
		memberEmailFrom='<EMAIL>'
	));

	// set payment gateways
	local.profile_1 = application.objCustomPageUtils.acct_getProfile(siteid=local.siteID, profileCode='NYCCCIM');

	// other data
	local.qryStates = application.objCommon.getStates();
    local.qryGroupMembership = application.objMember.getMemberGroups(local.memberID, local.orgID);
	local.searchGLCode = 'B-4020';
	local.testimonyGLCode = 'B-4040';

</cfscript>

<cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID='4632781D-39E1-475A-AEC8-E8BFE02FB7E5', orgID=local.orgID)>

<cfquery dbtype="query" name="local.qryIsMember">
	select groupID 
	from [local].qryGroupMembership
	where groupID = #local.qryMemberGroup.groupID#
</cfquery>

<cfif val(local.qryIsMember.groupID) gt 0>
	<cfset local.isMember = true>
<cfelse>
	<cfset local.isMember = false>
</cfif>

<cfsavecontent variable="local.stateHTML">
	<cfoutput>
	<select class="tsAppBodyText" name="stateIDForTax" id="stateIDForTax">
		<option value=""></option>
	</cfoutput>
	<cfoutput query="local.qryStates" group="countryID">
		<optgroup label="#local.qryStates.country#">
			<cfoutput><option value="#local.qryStates.stateID#">#local.qryStates.stateName# &nbsp;</option></cfoutput>
		</optgroup>
	</cfoutput>
	<cfoutput>
	</select>
	</cfoutput>
</cfsavecontent>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##03608B; border-bottom:1px solid ##03608B; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.CPSection{ border:1px solid ##086bad; margin-bottom:15px; }
			.CPSectionTitle { font-size:18pt; font-weight:bold; color:##fff; padding:10px; background:##086bad; }
			.CPSectionContent { padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ccc; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b { font-weight:bold; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.tsAppBodyText { color:##000;}
			select.tsAppBodyText{color:##666;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			##date_accident { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
		</style>
	</cfsavecontent>

	#local.pageJS#
	#local.pageCSS#
	
	<div id="customPage">
		<div class="TitleText PB">#local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: ========================================================================================================================================= --->
			<cfcase value="0">
				<script type="text/javascript">
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();

						$("input[type='submit']", thisForm).val("Please Wait...").attr('disabled', 'disabled');

						if (!_FB_hasValue(thisForm['name'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Name.';
						if (!_FB_hasValue(thisForm['firm'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Firm Name.';
						if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Address.';
						if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the City.';
						if (!_FB_hasValue(thisForm['state'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the State.';
						if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Zip.';
						if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Phone.';
						if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the E-mail.';
						if (!_FB_hasValue(thisForm['reference'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Reference Information.';
						if (!_FB_hasValue(thisForm['location'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Accident Location.';
						if (!_FB_hasValue(thisForm['date_accident'], 'TEXT')) arrReq[arrReq.length] = 'Please enter the Accident Date.';
						if (!_FB_hasValue(thisForm['borough'], 'SELECT')) arrReq[arrReq.length] = 'Please select the Accident Borough.';
						if (!_FB_hasValue(thisForm['accDesc'], 'SELECT')) arrReq[arrReq.length] = 'Please select the Accident Description.';

						if($('input[name=searchcategory]:checked').val() == undefined && $('input[name=documentFee]:checked').val() == undefined) arrReq[arrReq.length] = 'A selection in both the Search and Documentation fields is required to proceed.';
						else if($('input[name=searchcategory]:checked').val() == undefined) arrReq[arrReq.length] = 'Please select a Search.';
						else if( $('input[name=documentFee]:checked').val() == undefined)arrReq[arrReq.length] = 'Please select a Docmentation Fee.'; 

						let stateIDforTax = $('##stateIDForTax').val();
						let zipForTax = $('##zipForTax').val();

						if (stateIDforTax == '' || zipForTax == '') arrReq[arrReq.length] = 'Enter the requested billing information.';
						if (stateIDforTax > 0 && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDforTax,''))
							arrReq[arrReq.length] = 'Invalid Billing Postal Code.';

						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';

							$("input[type='submit']", thisForm).val("Continue").removeAttr('disabled');

							alert(msg);
							return false;
						}
						return true;
					}
					
					function closeBox() { $.colorbox.close(); }
					
					function assignMemberData(memObj){
						var thisForm = document.forms["#local.formName#"];
						var er_changeErr = function(r) {
							alert('error');
						};

						var er_change = function(r) {
							var results = r;
							if( results.success ){
								
								thisForm['memberNumber'].value 	= results.membernumber;
								thisForm['memberID'].value 			= results.memberid;

								thisForm['name'].value = results.fullname;
								thisForm['firm'].value = results.company;

								var dispAddress = results.address1;
								if (results.address2.length > 0)
									dispAddress += '<br>' + results.address2;
								
								thisForm['address'].value = dispAddress;
								thisForm['city'].value = results.city;
								thisForm['state'].value = results.statecode;
								thisForm['zip'].value = results.postalcode;
								thisForm['phone'].value = results.phone;
								thisForm['email'].value = results.email;
								
								//---------------------------------------------------------------------------------------
								// un hide form   
								$('##formToFill').show();
								
								if (results.groups && results.groups['#val(local.qryMemberGroup.groupID)#'] == true)
								{
									document.getElementById('spPrice1').innerHTML = '$75.00';
									thisForm['searchcategory'][0].disabled = false;
									document.getElementById('spPrice2').innerHTML = '$375.00';
									thisForm['documentFee'][0].disabled = false;
								}
								else
								{
									document.getElementById('spPrice1').innerHTML = '&nbsp;';
									thisForm['searchcategory'][0].disabled = true;
									document.getElementById('spPrice2').innerHTML = '&nbsp;';
									thisForm['documentFee'][0].disabled = true;
								}

								$('##stateIDForTax').val(results.stateid);
								$('##zipForTax').val(results.postalcode);
							}
							else{ /*alert('not success');*/ }
						};

						var objParams = { memberNumber:memObj.memberNumber, membergroups:[#val(local.qryMemberGroup.groupID)#] };
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_changeErr,20000,er_changeErr);
						
					}
					
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}					
					
					$(document).ready(function(){
						mca_setupDatePickerField('date_accident');
					});					
										
				</script>
				
				
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" id="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					<!--- =============================================================================================================================================== --->
					<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="c">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
												<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
												<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
												<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>
					<div id="formToFill" style="display:none;">
					<div>
						#local.strPageFields.InstructionText#
					</div>
					<div class="r i frmText">*Denotes required field</div>
					<!--- =============================================================================================================================================== --->
					<!--- CONTACT INFORMATION: ========================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Contact Information</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr class="frmRow1">
										<td class="tsAppBodyText r">Name:</td>
										<td class="tsAppBodyText">
											<input id="name" name="name" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>
									<tr class="frmRow2">
										<td class="tsAppBodyText r">Firm Name:</td>
										<td class="tsAppBodyText">
											<input id="firm" name="firm" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>
									<tr class="frmRow1">
										<td class="tsAppBodyText r">Address:</td>
										<td class="tsAppBodyText">
											<textarea id="address" name="address" class="tsAppBodyText" rows="4" cols="60" ></textarea>
										</td>
									</tr>
									<tr class="frmRow2">
										<td class="tsAppBodyText r">City:</td>
										<td class="tsAppBodyText frmText">
											<input id="city" name="city" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>
									<tr class="frmRow1">
										<td class="tsAppBodyText r">State:</td>
										<td class="tsAppBodyText">
											<input id="state" name="state" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>
									<tr class="frmRow2">
										<td class="tsAppBodyText r">Zip:</td>
										<td class="tsAppBodyText">
											<input id="zip" name="zip" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>
									<tr class="frmRow1">
										<td class="tsAppBodyText r">Phone:</td>
										<td class="tsAppBodyText frmText">
											<input id="phone" name="phone" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>
									<tr class="frmRow2">
										<td class="tsAppBodyText r">E-mail:</td>
										<td class="tsAppBodyText">
											<input id="email" name="email" type="text" class="tsAppBodyText" size="60" />
										</td>
									</tr>	
									<tr class="frmRow1">
										<td class="tsAppBodyText b c" colspan="2">Please provide details about your case for this specific order. ( Please complete a separate order form for each case. )</td>
									</tr>
									<tr class="frmRow1">
										<td class="tsAppBodyText r">* Reference (client file name or number):</td>
										<td class="tsAppBodyText"><input size="60" id="reference" name="reference" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2">
										<td class="tsAppBodyText r">* Location of Accident (Precise Address):</td>
										<td class="tsAppBodyText"><input size="60" id="location" name="location" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow1">
										<td class="tsAppBodyText r">* Accident Date:</td>
										<td class="tsAppBodyText">
											<cfinput value="" class="tsAppBodyText largeBox" name="date_accident" id="date_accident" type="text" validate="date" message="Please enter a valid Accident Date." />
											<a href="javascript:mca_clearDateRangeField('date_accident');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 7px;"></i></a>
										</td>
									</tr>
									<tr class="frmRow2">
										<td class="tsAppBodyText r">* Accident Borough:</td>
										<td class="tsAppBodyText">
											<select id="borough" name="borough"  class="tsAppBodyText">
												<option value="">-- Please select --</option>
												<option value="Manhattan">Manhattan</option>
												<option value="Brooklyn">Brooklyn</option>
												<option value="Queens">Queens</option>
												<option value="Bronx">Bronx</option>
												<option value="Staten Island">Staten Island</option>
											</select>
									</tr>
									<tr class="frmRow1">
										<td class="tsAppBodyText r">* Accident Description:</td>
										<td class="tsAppBodyText">
											<select id="accDesc" name="accDesc"  class="tsAppBodyText">
												<option value="">-- Please select --</option>
												<option value="Sidewalk">Sidewalk</option>
												<option value="Curb">Curb</option>
												<option value="Crosswalk">Crosswalk</option>
											</select>
									</tr>
								</table>
							</div>
						</div>
					<!--- =============================================================================================================================================== --->
					<!--- ORDER A SEARCH: ========================================================================================================================= --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">* Order a Search for Your Case</div>
							<div class="tsAppBodyText subCPSectionArea1">
								We provide a thorough and professional search of sidewalk, curb, and crosswalk defects. A search is required initially for all orders including those requesting documentation. If you have already purchased a search and simply wish to order documentation, please skip this section.							
							</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr class="frmRow1">
										<td width="25" class="c"><input type="radio" value="1" name="searchcategory" id="searchMember" <cfif local.isMember eq false>disabled="true"</cfif> /></td>
										<td>Search - Member </td>
										<td width="150" class="r b P"><span id="spPrice1"><cfif local.isMember eq false>&nbsp;<cfelse>$75.00</cfif></span></td>
									</tr>
									<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="c"><input type="radio" value="2" name="searchcategory" /></td>
										<td>Search - Non-Member</td>
										<td class="r b P">$125.00</td>
									</tr>
									<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
					<!--- =============================================================================================================================================== --->
					<!--- DOCUMENTATION FEE: ========================================================================================================================= --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">* Documentation Fee</div>
							<div class="tsAppBodyText subCPSectionArea1">
								If you have already paid or are currently paying for the Search Fee and wish to order documentation/map, you may do so here.  
								The NYSTLA Member Rate is for members only; to join NYSTLA <a href="/?pg=memberApp">click here</a>.
							</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr class="frmRow1">
										<td width="25" class="c"><input type="checkbox" value="1" name="documentFee" <cfif local.isMember eq false>disabled="true"</cfif> /></td>
										<td>Documentation - Member </td>
										<td class="r b P"><span id="spPrice2"><cfif local.isMember eq false>&nbsp;<cfelse>$375.00</cfif></span></td>
									</tr>
									<tr><td colspan="3" class="frmRow1 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="c"><input type="checkbox" value="2" name="documentFee" /></td>
										<td>Documentation - Non-Member</td>
										<td class="r b P">$525.00</td>
									</tr>
									<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
					<!--- =============================================================================================================================================== --->
					<!--- TESTIMONY: ========================================================================================================================= --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Court Testimony</div>
							<div class="tsAppBodyText subCPSectionArea1">
								<b>If you would like NYSTLA to testify in your case, please also choose this option.</b> An additional $100 Fee will be charged if your case is settled and the Big Apple witness is not notified in time.
							</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr class="frmRow1">
										<td width="25" class="c"><input type="checkbox" value="Yes" name="courtTestimony" /></td>
										<td>Fee for Big Apple Witness</td>
										<td width="150" class="r b P">$3000.00</td>
									</tr>
									<tr><td colspan="3" class="frmRow2 BB"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
						
					<!--- ============================ --->
					<!--- FOR TAX PURPOSES: ======== --->
						<div class="CPSection" id="taxSection">
							<div class="CPSectionTitle BB">Additional Information Needed</div>
							<div class="tsAppBodyText subCPSectionArea1">
								Complete the following missing information:
							</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<div style="margin:10px;">
									<table>
									<tbody id="stateIDForTaxTBODY">
										<tr>
											<td class="tsAppBodyText">Billing State</td>
											<td class="tsAppBodyText">
												#local.stateHTML#
											</td>
										</tr>
									</tbody>
									<tbody id="zipForTaxTBODY">
										<tr>
											<td class="tsAppBodyText">Billing Zip</td>
											<td class="tsAppBodyText"><input type="text" name="zipForTax" id="zipForTax" class="tsAppBodyText" value=""></td>
										</tr>
									</tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- BUTTONS: ====================================================================================================================================== --->					
						<div id="formButtons">
							<div style="padding:10px;">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp;
								</div>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->					
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
				
			</cfcase>
			
			<!--- PAYMENT INFO: ================================================================================================================================= --->
			<cfcase value="1">

				<cfset local.timeStamp 				= now() />
				
				<!--- GATEWAY INFORMATION: --->
				<cfset local.formName = "frmNYCC">
				<cfset local.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																					siteid=local.siteID,
																					profilecode=local.profile_1._profileCode,
																					pmid=local.useMID,
																					showCOF=local.useMID EQ session.cfcUser.memberData.memberID,
																					usePopupDIVName='paymentinfo'
																				)>

				<!--- check billing zip --->
				<cfset local.stateIDForTax = val(arguments.event.getValue('stateIDforTax',0))>
				<cfset local.zipForTax = arguments.event.getTrimValue('zipForTax','')>
				<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax)>
				<cfif local.strBillingZip.isValidZip>
					<cfset arguments.event.setValue('zipForTax',local.strBillingZip.billingzip)>
				<cfelse>
					<cfthrow message="Invalid State/Zip.">
				</cfif>
	
				<cfoutput>
					<cfsavecontent variable="local.js">
						<script type="text/javascript">
							
							function _FB_validate() {
								var thisForm = document.forms['#local.formName#'];
								var arrReq = new Array();

								$("input[type='submit']", thisForm).val("Please Wait...").attr('disabled', 'disabled');
								
								#local.strPaymentForm.jsvalidation#
								if (arrReq.length > 0) {
									var msg = 'The following questions are required:\n\n';
									for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
									$("input[type='submit']", thisForm).val("Continue").removeAttr('disabled');
									alert(msg);
									return false;
								}
								return true;
							}

							function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
						</script>
					</cfsavecontent>
					<cfhtmlhead text="#application.objCommon.minText(local.js)#">

					<cfif len(local.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headCode)#">
					</cfif>

					<cfswitch expression="#event.getValue('searchcategory','0')#">
						<cfcase value="1">
							<cfset local.searchNeeded = "Search - Member">
							<cfset local.searchPrice = 75>
						</cfcase>
						<cfcase value="2">
							<cfset local.searchNeeded = "Search - Non-Member">
							<cfset local.searchPrice = 125>
						</cfcase>
						<cfdefaultcase>
							<cfset local.searchNeeded = "">
							<cfset local.searchPrice = 0>
						</cfdefaultcase>
					</cfswitch>
					
					<cfswitch expression="#event.getValue('documentFee','0')#">
						<cfcase value="1">
							<cfset local.documentNeeded = "Documentation - Member">
							<cfset local.documentPrice = 375>
						</cfcase>
						<cfcase value="2">
							<cfset local.documentNeeded = "Documentation - Non-Member">
							<cfset local.documentPrice = 525>
						</cfcase>
						<cfdefaultcase>
							<cfset local.documentNeeded = "">
							<cfset local.documentPrice = 0>
						</cfdefaultcase>
					</cfswitch>
					
					<cfif isDefined('courtTestimony')>
						<cfset local.testimonyNeeded = 'I would like NYSTLA to testify in my case.'>
						<cfset local.testimonyPrice = 3000>
					<cfelse>
						<cfset local.testimonyNeeded = "">
						<cfset local.testimonyPrice = 0>
					</cfif>
					
					<!--- there are two sales so we need to calculate tax for each separately --->
					<cfset local.totalTax = 0>
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
					<cfset local.searchGLTotal = local.searchPrice + local.documentPrice>
					<cfif local.searchGLTotal gt 0>
						<cfset local.glAccountID = local.objAccounting.getGLAccountByAccountCode(orgID=local.orgID, accountCode=local.searchGLCode)>
						<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.glAccountID, saleAmount=local.searchGLTotal, 
								transactionDate=now(), stateIDForTax=arguments.event.getValue('stateIDForTax',0), zipForTax=arguments.event.getValue('zipForTax',''))>
						<cfset local.totalTax = local.totalTax + local.strTax.totalTaxAmt>
					</cfif>
					<cfif isDefined('courtTestimony')>
						<cfset local.glAccountID = local.objAccounting.getGLAccountByAccountCode(orgID=local.orgID, accountCode=local.testimonyGLCode)>
						<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.glAccountID, saleAmount=local.testimonyPrice, 
								transactionDate=now(), stateIDForTax=arguments.event.getValue('stateIDForTax',0), zipForTax=arguments.event.getValue('zipForTax',''))>
						<cfset local.totalTax = local.totalTax + local.strTax.totalTaxAmt>
					</cfif>

					<cfset local.totalDue = local.searchPrice + local.documentPrice + local.testimonyPrice + local.totalTax>

					<div class="CPSection">
						<div class="CPSectionTitle BB">Confirmation</div>
						<div class="tsAppBodyText PL PR frmText">
							<br/>
							<table cellpadding="4" cellspacing="0" width="100%">
							<cfif len(local.searchNeeded)>
								<tr><td class="tsAppBodyText">#local.searchNeeded#</td><td class="tsAppBodyText r">#dollarFormat(local.searchPrice)#</td></tr>
							</cfif>
							<cfif len(local.documentNeeded)>
								<tr><td class="tsAppBodyText">#local.documentNeeded#</td><td class="tsAppBodyText r">#dollarFormat(local.documentPrice)#</td></tr>
							</cfif>
							<cfif len(local.testimonyNeeded)>
								<tr><td class="tsAppBodyText">#local.testimonyNeeded#</td><td class="tsAppBodyText r">#dollarFormat(local.testimonyPrice)#</td></tr>
							</cfif>							
							<cfif local.totalTax gt 0>
								<tr><td class="tsAppBodyText">TAX</td><td class="tsAppBodyText r">#dollarFormat(local.totalTax)#</td></tr>
							</cfif>							
							<tr><td class="tsAppBodyText b">TOTAL</td><td class="tsAppBodyText r"><strong>#dollarFormat(local.totalDue)#</strong></td></tr>
							</table>
							<br/>
						</div>
					</div>

					<div id="paymentTable">
						<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
						<div class="form">
							<cfform name="#local.formName#" id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onsubmit="return _FB_validate();">
								<cfinput type="hidden" name="isSubmitted" id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
								<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
										and left(local.key,4) neq "fld_">
										<cfinput type="hidden" name="#local.key#" id="#local.key#" value="#arguments.event.getValue(local.key)#">
									</cfif>
								</cfloop>

								<!--- Credit Card Info--->
								<div id="paymentinfo" class="CPSection">
									<div class="CPSectionTitle">Credit Card Information</div>
									<div class="PL PR frmText paymentGateway BT">
										#local.strPaymentForm.inputForm#
									</div>
								</div>
								<br/>

								<!--- BUTTONS: ====================================================================================================================================== --->					
								<div id="formButtons">
									<div style="padding:10px;">
										<div align="center" class="frmButtons">
											<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
										</div>
									</div>
								</div>
								<!--- =============================================================================================================================================== --->					
							</cfform>
						</div>
					</div>				
				</cfoutput>
				
			</cfcase>
		
			<!--- PROCESS: ====================================================================================================================================== --->
			<cfcase value="2">
				
				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & event.getValue('name','') />

				<cfswitch expression="#event.getValue('searchcategory','0')#">
					<cfcase value="1">
						<cfset local.searchNeeded = "Search - Member">
						<cfset local.searchPrice = 75>
					</cfcase>
					<cfcase value="2">
						<cfset local.searchNeeded = "Search - Non-Member">
						<cfset local.searchPrice = 125>
					</cfcase>
					<cfdefaultcase>
						<cfset local.searchNeeded = "[No search selected]">
						<cfset local.searchPrice = 0>
					</cfdefaultcase>
				</cfswitch>
				
				<cfswitch expression="#event.getValue('documentFee','0')#">
					<cfcase value="1">
						<cfset local.documentNeeded = "Documentation - Member">
						<cfset local.documentPrice = 375>
					</cfcase>
					<cfcase value="2">
						<cfset local.documentNeeded = "Documentation - Non-Member">
						<cfset local.documentPrice = 525>
					</cfcase>
					<cfdefaultcase>
						<cfset local.documentNeeded = "[No documentation selected]">
						<cfset local.documentPrice = 0>
					</cfdefaultcase>
				</cfswitch>
				
				<cfif isDefined('courtTestimony')>
					<cfset local.testimonyPrice = 3000>
					<cfset local.testimonyNeeded = 'I would like NYSTLA to testify in my case.'>
				<cfelse>
					<cfset local.testimonyPrice = 0>
					<cfset local.testimonyNeeded = 'I do not want NYSTLA to testify in my case.'>
				</cfif>
				
				<!--- there are two sales so we need to calculate tax for each separately --->
				<cfset local.totalTax = 0>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
				<cfset local.searchGLTotal = local.searchPrice + local.documentPrice>
				<cfif local.searchGLTotal gt 0>
					<cfset local.glAccountID = local.objAccounting.getGLAccountByAccountCode(orgID=local.orgID, accountCode=local.searchGLCode)>
					<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.glAccountID, saleAmount=local.searchGLTotal, 
							transactionDate=now(), stateIDForTax=arguments.event.getValue('stateIDForTax',0), zipForTax=arguments.event.getValue('zipForTax',''))>
					<cfset local.totalTax = local.totalTax + local.strTax.totalTaxAmt>
				</cfif>
				<cfif isDefined('courtTestimony')>
					<cfset local.glAccountID = local.objAccounting.getGLAccountByAccountCode(orgID=local.orgID, accountCode=local.testimonyGLCode)>
					<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.glAccountID, saleAmount=local.testimonyPrice, 
							transactionDate=now(), stateIDForTax=arguments.event.getValue('stateIDForTax',0), zipForTax=arguments.event.getValue('zipForTax',''))>
					<cfset local.totalTax = local.totalTax + local.strTax.totalTaxAmt>
				</cfif>

				<cfset local.totalDue = local.searchPrice + local.documentPrice + local.testimonyPrice + local.totalTax>

				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<!-- @accResponseMessage@ -->
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					
						<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">Name:</td><td class="frmText">#event.getValue('name')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm Name:</td><td class="frmText">#event.getValue('firm')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Address:</td><td class="frmText">#event.getValue('address')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('phone')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">E-mail:</td><td class="frmText">#event.getValue('email')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Reference (client file name or number):</td><td class="frmText">#event.getValue('reference')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Location of Accident (Precise Address):</td><td class="frmText">#event.getValue('location')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Accident Date:</td><td class="frmText">#event.getValue('date_accident')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Accident Borough:</td><td class="frmText">#event.getValue('borough')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Accident Description:</td><td class="frmText">#event.getValue('accDesc')#&nbsp;</td></tr>

						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">SEARCH NEEDED</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.searchNeeded#</td><td class="frmText">#dollarFormat(local.searchPrice)#&nbsp;</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">DOCUMENTATION FEE</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.documentNeeded#</td><td class="frmText">#dollarFormat(local.documentPrice)#&nbsp;</td></tr>

						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">COURT TESTIMONY</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.testimonyNeeded#</td><td class="frmText">#dollarFormat(local.testimonyPrice)#&nbsp;</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">TAX</td></tr>
						<tr class="frmRow1"><td class="frmText b" style="text-align:right;" colspan="2"><strong>#dollarFormat(local.totalTax)#</strong></td></tr>

						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">TOTAL</td></tr>
						<tr class="frmRow1"><td class="frmText b" style="text-align:right;" colspan="2"><strong>#dollarFormat(local.totalDue)#</strong></td></tr>

					</table>
				</cfsavecontent>


				<!--- ---------------------- --->
				<!--- Payment and accounting --->
				<!--- ---------------------- --->
				<cfset local.strAccTemp = { totalPaymentAmount=local.totalDue, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=arguments.event.getCollection() } >
				<cfif local.strAccTemp.totalPaymentAmount gt 0>
					<cfset local.strAccTemp.payment = { detail=local.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.profile_1._profileID, profileCode=local.profile_1._profileCode }>
				</cfif>			
				<cfset local.strAccTemp.revenue = ArrayNew(1)>
				<cfset local.strAccTemp.revenue[1] = { revenueGLAccountCode=local.searchGLCode, detail=local.formNameDisplay & ' - ' & event.getValue('reference'), amount=local.searchGLTotal, stateIDForTax=arguments.event.getValue('stateIDForTax',0), zipForTax=arguments.event.getValue('zipForTax',0) } >
				<cfif isDefined('courtTestimony')>
					<cfset local.strAccTemp.revenue[2] = { revenueGLAccountCode=local.testimonyGLCode, detail='NYSTLA - Big Apple Testimony', amount=local.testimonyPrice, stateIDForTax=arguments.event.getValue('stateIDForTax',0), zipForTax=arguments.event.getValue('zipForTax',0) } >
				</cfif>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>


				<!--- email member ---------------------------------------------------------------------------------------------- --->
				<cfset local.emailSentToUser = false />

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>
						#local.strPageFields.InstructionText#<br><br>
						</p>
						<p>
							Thank you for submitting your Big Apple Search request!<br>
						</p>
						<hr />
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>
				
				<cfif len(trim(local.memberEmail.to))>
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from },
						emailto=[{ name="", email=local.memberEmail.to }],
						emailreplyto=local.ORGEmail.to,
						emailsubject=local.memberEmail.SUBJECT,
						emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=val(local.useMID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>
					
					<cfset local.emailSentToUser = local.responseStruct.success>
				</cfif>

				<!--- email association ----------------------------------------------------------------------------------------- --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p style="color:red;">We were not able to send #event.getValue('name')# an e-mail confirmation.</p>
						</cfif>
						#replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)#
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}

					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from},
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle=arguments.event.getValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
							emailhtmlcontent=local.mailContent,
							siteID=local.siteID,
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					}
				</cfscript>
										
				<cfoutput>
					<p>
						#local.strPageFields.InstructionText#<br><br>
					</p>
					<p>
						Thank you for submitting your Big Apple Search request.<br><br>
					</p>
					<cfif NOT local.emailSentToUser>
						Please print the following for your records:
						#local.invoice#	
					<cfelse>
					You have been sent an email for your records.
					</cfif>
				</cfoutput>			
				
				
			</cfcase>
			
			<!--- SPAM MESSAGE: ================================================================================================================================= --->
			<cfcase value="100">
					<div>
						Error! you Can't Post Here.
					</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>
