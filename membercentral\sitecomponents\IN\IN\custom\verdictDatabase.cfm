<cfscript>
	local.vAction = event.getValue('action','search');
	
	local.baseLink 		= '/?pg=verdictDatabase';
	local.viewLink		= local.baseLink & '&action=view';
	local.editLink		= local.baseLink & '&action=edit';
	local.deleteLink	= local.baseLink & '&action=delete';
	local.saveLink		= local.baseLink & '&action=save';
	local.resultsLink	= local.baseLink & '&action=results';
	
	local.associationEmail	= '<EMAIL>,<EMAIL>,<EMAIL>';
	
</cfscript>


	
	<cfswitch expression="#local.vAction#">
	
		<!--- view record --->
		<cfcase value="view">
			<cfoutput>
				<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
				<cfif local.qryVerdict.recordcount is 0>
					<cflocation url="#local.baseLink#" addtoken="No">
				</cfif>
				<p class="headerText">Viewing Information in ITLA's Verdict and Settlement Exchange Database</p>
				<div><input type="button" value="Back to Listing" onclick="history.go(-1);" class="bodyText" /></div>
				<br/>
				<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
					<cfif len(local.qryVerdict.date) and isdate(local.qryVerdict.date)>
						<tr valign="top">
							<td><strong>Date:</strong></td>
							<td>#dateformat(local.qryVerdict.date,"mm/dd/yyyy")#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.resolutiontype)>
						<tr valign="top">
							<td><strong>Resolution:</strong></td>
							<td>#local.qryVerdict.resolutiontype#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.casetype) or len(local.qryVerdict.casetypedetail)>
						<tr valign="top">
							<td><strong>Category:</strong></td>
							<td>[#local.qryVerdict.casetype#]: #local.qryVerdict.casetypedetail#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.casetitle)>
						<tr valign="top">
							<td><strong>Case:</strong></td>
							<td>#local.qryVerdict.casetitle#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.courtname)>
						<tr valign="top">
							<td><strong>Court:</strong></td>
							<td>#local.qryVerdict.courtname#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.countyname)>
						<tr valign="top">
							<td><strong>County:</strong></td>
							<td>#local.qryVerdict.countyname#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.docketnumber)>
						<tr valign="top">
							<td nowrap><strong>Docket Number:</strong></td>
							<td>#local.qryVerdict.docketnumber#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.sex)>
						<tr valign="top">
							<td><strong>Sex:</strong></td>
							<td>#local.qryVerdict.sex#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.age)>
						<tr valign="top">
							<td><strong>Age:</strong></td>
							<td>#local.qryVerdict.age#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.Occupation)>
						<tr valign="top">
							<td><strong>Occupation:</strong></td>
							<td>#local.qryVerdict.Occupation#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.amount) or len(local.qryVerdict.amountdetail)>
						<tr valign="top">
							<td nowrap><strong>Amount Awarded:</strong></td>
							<td>#dollarformat(local.qryVerdict.amount)# <cfif len(trim(local.qryVerdict.amountdetail)) gt 0> - #local.qryVerdict.amountdetail#</cfif></td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.settlementoffer)>
					<tr valign="top">
						<td nowrap><strong>Settlement Offer:</strong></td>
						<td>#local.qryVerdict.settlementoffer#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.facts)>
					<tr valign="top">
						<td><strong>Facts:</strong></td>
						<td>#local.qryVerdict.facts#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.Injuries)>
					<tr valign="top">
						<td><strong>Injuries:</strong></td>
						<td>#local.qryVerdict.Injuries#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.medicals)>
					<tr valign="top">
						<td><strong>Medicals:</strong></td>
						<td>#local.qryVerdict.medicals#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.lostwages)>
					<tr valign="top">
						<td nowrap><strong>Lost Wages:</strong></td>
						<td>#local.qryVerdict.lostwages#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.coverage)>
					<tr valign="top">
						<td><strong>Coverage:</strong></td>
						<td>#local.qryVerdict.coverage#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.experts)>
					<tr valign="top">
						<td><strong>Experts:</strong></td>
						<td>#local.qryVerdict.experts#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.plaintiffattorney)>
					<tr valign="top">
						<td nowrap><strong>Plaintiff Attorney:</strong></td>
						<td>#local.qryVerdict.plaintiffattorney#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.defenseattorney)>
					<tr valign="top">
						<td nowrap><strong>Defense Attorney:</strong></td>
						<td>#local.qryVerdict.defenseattorney#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.submittingattorney)>
					<tr valign="top">
						<td nowrap><strong>Submitting Attorney:</strong></td>
						<td>#local.qryVerdict.submittingattorney#</td>
					</tr>
					</cfif>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="edit">
			<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
			<cfset allowForm = false>
			
			<cfoutput>
				
				<!--- 
					if verdictID is 0
						ADD
					else
						EDIT
				 --->
				
				<cfif val(event.getValue('verdictID',0))>
					<!--- EDIT Verdict: --->
					<p class="headerText">Edit Information in ITLA's Verdict and Settlement Exchange Database</p>
					<p class="bodyText">Complete the form below to edit this record.</p>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<cfset allowForm = true>
					<cfelse>
						<p class="bodyText">You do not have permission to edit this information.</p>
					</cfif>
					
					
				<cfelse>
					<!--- ADD Verdict: --->
					<p class="headerText">Add to ITLA's Verdict and Settlement Exchange Database</p>
					<p class="bodyText">Complete the form below to add a verdict or settlement to the database.</p>
					
					<cfif val(event.getValue('customPage.myRights.view',0))>
						<cfset allowForm = true>
					<cfelse>
						<p class="bodyText">You do not have permission to add to this database.</p>
					</cfif>
					
				</cfif>
			</cfoutput>
			
			<cfif allowForm>
				<cfoutput>
				<cfset local.qryCaseTypes 			= getCaseTypes()>
				<cfset local.qryResolutionTypes = getResolutionTypes()>
				<cfset local.qryCountyNames 		= getCountyNames()>
				<cfset qryCourtNames 			= getCourtNames()>
				<cfsavecontent variable="local.JS">
					<style type="text/css">
						##date { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
					</style>			
				
					<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
					<!--
					function changeAvailability(formfieldid,disableflag) {
						var ff = document.getElementById(formfieldid);
						ff.disabled = disableflag;
						if (disableflag) ff.value='disabled';
						else ff.value='';
					}  
					//-->
					
					$(document).ready(function(){
						mca_setupDatePickerField('date');
					});					
					</SCRIPT>
				</cfsavecontent>
				<cfhtmlhead text="#local.JS#">
					<cfform name="verdictForm"  id="verdictForm" action="#local.saveLink#" method="post">
						<cfinput type="hidden" name="verdictid"  id="verdictid" value="#val(local.qryVerdict.verdictid)#">
						<div>
							<input type="submit" value="Save Verdict" name="btnSave" class="bodyText" /> &nbsp;
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) AND val(local.qryVerdict.verdictID) gt 0>
								<input type="button" name="btnDelete" value="Delete Verdict" class="bodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(local.qryVerdict.verdictID)#';"/> &nbsp;
							</cfif>
							<input type="button" value="Cancel" onclick="history.go(-1);" class="bodyText" />
						</div>
						<br/>
						<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and val(local.qryVerdict.verdictID) gt 0>
								<tr valign="top">
									<td nowrap><strong>Approved Status:</strong></td>
									<td>
										<cfinput required="yes" message="Select an approval status." type="radio" value="1" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 1#"> Approved - available for viewing<br/>
										<cfinput type="radio" value="0" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 0#"> Not Approved - not available for viewing<br/>
									</td>
								</tr>
							<cfelse>
								<cfinput type="hidden" name="isApproved"  id="isApproved" value="#val(local.qryVerdict.isApproved)#">
							</cfif>
							<tr valign="top">
								<td nowrap><strong>Verdict Date:</strong></td>
								<td>
									<cfinput type="text" name="date" id="date" value="#dateFormat(local.qryVerdict.date,'mm/dd/yyyy')#">
									<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 5px 7px;"></i></a>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Resolution:</strong></td>
								<td>
									<select name="resolutiontype" class="bodyText" onchange="changeAvailability('resolutiontypeNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qryresolutiontypes">
										<option value="#local.qryresolutiontypes.resolutiontype#" <cfif local.qryVerdict.Resolutiontype eq local.qryresolutiontypes.resolutiontype>selected</cfif>>#local.qryresolutiontypes.resolutiontype#</option>
									</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="resolutiontypeNew" id="resolutiontypeNew" maxlength="15" size="20">
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Category:</strong></td>
								<td>
									<select name="casetype" class="bodyText" onchange="changeAvailability('casetypeNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qrycasetypes">
										<option value="#local.qrycasetypes.casetype#" <cfif local.qryVerdict.casetype eq local.qrycasetypes.casetype>selected</cfif>>#local.qrycasetypes.casetype#</option>
									</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="casetypeNew" id="casetypeNew" maxlength="50" size="30">
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Type of Case:</strong></td>
								<td><cfinput class="bodyText" type="text" name="casetypedetail"  id="casetypedetail" maxlength="250" size="70" value="#local.qryVerdict.casetypedetail#"></td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Case:</strong></td>
								<td><cfinput class="bodyText" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70" value="#local.qryVerdict.casetitle#"></td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Court:</strong></td>
								<td>
									<cfselect class="bodyText" query="qryCourtNames" name="courtID"  id="courtID" display="courtname" value="courtID" selected="#local.qryVerdict.courtID#" queryPosition="below"><option value="0">Add new entry or select from list</option></cfselect>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>County:</strong></td>
								<td>
									<select name="countyname" class="bodyText" onchange="changeAvailability('countynameNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qrycountynames">
										<option value="#trim(local.qrycountynames.countyname)#" <cfif trim(local.qryVerdict.countyname) eq trim(local.qrycountynames.countyname)>selected</cfif>>#trim(local.qrycountynames.countyname)#</option>
									</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="countynameNew" id="countynameNew" maxlength="20" size="20">
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Docket Number:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="docketnumber"  id="docketnumber" maxlength="30" size="30" value="#local.qryVerdict.docketnumber#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Sex:</strong></td>
								<td valign="top"><select name="sex" class="bodyText"><option value=""></option><option value="M" <cfif local.qryVerdict.sex eq "M">selected</cfif>>Male</option><option value="F" <cfif local.qryVerdict.sex eq "F">selected</cfif>>Female</option></select></td>
							</tr>
							<tr>
								<td valign="top"><strong>Age:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="age"  id="age" validate="integer" maxlength="2" size="5" value="#local.qryVerdict.age#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Occupation:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="Occupation"  id="Occupation" maxlength="75" size="30" value="#local.qryVerdict.Occupation#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Amount Awarded:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="amount"  id="amount" validate="float" message="Please enter a numerical amount in the 'Amount Awarded' field. Only numerals, commas, and periods allowed." size="10" value="#local.qryVerdict.amount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Amount Detail:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="amountdetail"  id="amountdetail" maxlength="500" size="70" value="#local.qryVerdict.amountdetail#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Settlement Offer:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="settlementoffer"  id="settlementoffer" maxlength="250" size="70" value="#local.qryVerdict.settlementoffer#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Facts:</strong></td>
								<td valign="top"><textarea class="bodyText" name="facts" cols="70" rows="10">#local.qryVerdict.facts#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Injuries:</strong></td>
								<td valign="top"><textarea class="bodyText" name="injuries" cols="70" rows="10">#local.qryVerdict.injuries#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Medicals:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="medicals"  id="medicals" maxlength="400" size="70" value="#local.qryVerdict.medicals#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Lost Wages:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="lostwages"  id="lostwages" maxlength="30" size="30" value="#local.qryVerdict.lostwages#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Coverage:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="coverage"  id="coverage" maxlength="100" size="70" value="#local.qryVerdict.coverage#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Experts:</strong></td>
								<td valign="top"><textarea class="bodyText" name="experts" cols="70" rows="10">#local.qryVerdict.experts#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Plaintiff Attorney:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="PlaintiffAttorney"  id="PlaintiffAttorney" maxlength="200" size="70" value="#local.qryVerdict.PlaintiffAttorney#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Defense Attorney:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="DefenseAttorney"  id="DefenseAttorney" maxlength="300" size="70" value="#local.qryVerdict.DefenseAttorney#"></td>
							</tr>
							<tr>
								<td nowrap><strong>Submitting Attorney:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="SubmittingAttorney"  id="SubmittingAttorney" maxlength="200" size="70" value="#local.qryVerdict.SubmittingAttorney#"></td>
							</tr>
						</table>
					</cfform>
				</cfoutput>
			</cfif>
		</cfcase>
	
		<cfcase value="delete">
			<cfset local.verdictID = int(val(event.getValue('verdictID',0)))>
			<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
				<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
					delete from IN_verdicts
					where verdictID = <cfqueryparam value="#local.verdictID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
			</cfif>
			<cfoutput>
				<p class="headerText">Information Updated</p>
				<br />
				<table border="0" cellpadding="2">
					<tr>
					<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
						<form action="#local.resultsLink#" method="post">
						<CFLOOP INDEX="local.form_element" LIST="#session.lastverdictsearch.fieldnames#">
							<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#session.lastverdictsearch[local.form_element]#">
						</CFLOOP>
						<td><input type="submit" value="Return to Results" class="bodyText"/></td>
						</form>
					</cfif>
					<td><input type="button" onclick="document.location.href='#local.baseLink#';" value="New Search" class="bodyText" /></td>
					</tr>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="save">
			<cfset local.verdictID = int(val(event.getValue('verdictID',0)))>
			<!--- INSERT RECORD --->
			<cfif local.verdictID is 0>
				<cfquery name="insertVerdict" datasource="#application.dsn.customApps.dsn#">
					set nocount on
					insert into dbo.IN_verdicts (resolutiontype, date, amount, amountDetail, casetype, casetypeDetail, casetitle,
						courtID, countyname, docketnumber, facts, sex, age, occupation, injuries, medicals, lostWages,
						Coverage, SettlementOffer, Experts, PlaintiffAttorney, DefenseAttorney, SubmittingAttorney, DepoMemberDataID, 
						dateLastModified, isApproved)
					VALUES (
						<cfif len(trim(event.getValue('resolutiontypeNew',''))) and trim(event.getValue('resolutiontypeNew','')) neq "disabled">
							<cfqueryparam value="#trim(replace(event.getValue('resolutiontypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							<cfqueryparam value="#trim(replace(event.getValue('resolutiontype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
						<cfif isDate(event.getValue('date'))>
							<cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
						</cfif>
						<cfif len(event.getValue('amount'))>
							<cfqueryparam value="#RereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
						<cfelse>
							<cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
						</cfif>
						<cfqueryparam value="#trim(event.getValue('amountDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif len(trim(event.getValue('casetypeNew',''))) and trim(event.getValue('casetypeNew','')) neq "disabled">
							<cfqueryparam value="#trim(replace(event.getValue('casetypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							<cfqueryparam value="#trim(replace(event.getValue('casetype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
						<cfqueryparam value="#trim(event.getValue('casetypeDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
						
						<cfqueryparam value="#trim(event.getValue('courtID'))#" cfsqltype="CF_SQL_INTEGER">,
						
						<cfif len(trim(event.getValue('countynameNew',''))) and trim(event.getValue('countynameNew','')) neq "disabled">
							<cfqueryparam value="#trim(replace(event.getValue('countynameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							<cfqueryparam value="#trim(replace(event.getValue('countyname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
						<cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('sex'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('age'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('occupation'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('injuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('medicals'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('lostWages'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('Coverage'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('SettlementOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('Experts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('PlaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#session.cfcuser.memberdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
						getdate(),
						<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
							<cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
						<cfelse>
							<cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
						</cfif>
					)
					select SCOPE_IDENTITY() as verdictid
					set nocount off
				</cfquery>
				<cfset local.thisVerdictID = insertVerdict.verdictid>
				<!--- Email IN about verdict --->
				<cfif NOT val(event.getValue('customPage.myRights.customAddDatabase',0))>					

					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>A verdict has been added to the Verdict Database.</p>
							<p>VerdictID: #local.thisverdictID#</p>
							<p><a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editLink#&verdictID=#local.thisverdictid#">Click here</a> to review the verdict and approve it for display.</p>
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(local.associationEmail,',');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>

					<cfif arrayLen(local.arrEmailTo)>
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email="<EMAIL>"},
							emailto=local.arrEmailTo,
							emailreplyto="<EMAIL>",
							emailsubject="ITLA Verdict and Settlement Database Updated",
							emailtitle="ITLA Verdict and Settlement Database",
							emailhtmlcontent=local.mailContent,
							siteID=event.getTrimValue('mc_siteinfo.siteid'),
							memberID=event.getTrimValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						)>
					</cfif>
				</cfif>
			<cfelse>
				<!--- UPDATE RECORD --->
				<cfquery name="updateVerdict" datasource="#application.dsn.customApps.dsn#">
					update dbo.IN_verdicts
					set <cfif len(trim(event.getValue('resolutiontypeNew',''))) and trim(event.getValue('resolutiontypeNew','')) neq "disabled">
							resolutiontype = <cfqueryparam value="#trim(replace(event.getValue('resolutiontypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							resolutiontype = <cfqueryparam value="#trim(replace(event.getValue('resolutiontype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
						<cfif isDate(event.getValue('date'))>
							date = <cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							date = <cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
						</cfif>
						<cfif len(event.getValue('amount'))>
							amount = <cfqueryparam value="#rereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
						<cfelse>
							amount = <cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
						</cfif>
						amountDetail = <cfqueryparam value="#trim(event.getValue('amountDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif len(trim(event.getValue('casetypeNew',''))) and trim(event.getValue('casetypeNew','')) neq "disabled">
							casetype = <cfqueryparam value="#trim(replace(event.getValue('casetypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							casetype = <cfqueryparam value="#trim(replace(event.getValue('casetype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
						casetypeDetail = <cfqueryparam value="#trim(event.getValue('casetypeDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
						casetitle = <cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
	
						courtID = <cfqueryparam value="#trim(event.getValue('courtID'))#" cfsqltype="CF_SQL_INTEGER">,
						
						<cfif len(trim(event.getValue('countynameNew',''))) and trim(event.getValue('countynameNew','')) neq "disabled">
							countyname = <cfqueryparam value="#trim(replace(event.getValue('countynameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							countyname = <cfqueryparam value="#trim(replace(event.getValue('countyname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
						docketnumber = <cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
						facts = <cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						sex = <cfqueryparam value="#trim(event.getValue('sex'))#" cfsqltype="CF_SQL_VARCHAR">,
						age = <cfqueryparam value="#trim(event.getValue('age'))#" cfsqltype="CF_SQL_VARCHAR">,
						occupation = <cfqueryparam value="#trim(event.getValue('occupation'))#" cfsqltype="CF_SQL_VARCHAR">,
						injuries = <cfqueryparam value="#trim(event.getValue('injuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						medicals = <cfqueryparam value="#trim(event.getValue('medicals'))#" cfsqltype="CF_SQL_VARCHAR">,
						lostWages = <cfqueryparam value="#trim(event.getValue('lostWages'))#" cfsqltype="CF_SQL_VARCHAR">,
						Coverage = <cfqueryparam value="#trim(event.getValue('Coverage'))#" cfsqltype="CF_SQL_VARCHAR">,
						SettlementOffer = <cfqueryparam value="#trim(event.getValue('SettlementOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
						Experts = <cfqueryparam value="#trim(event.getValue('Experts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						PlaintiffAttorney = <cfqueryparam value="#trim(event.getValue('PlaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						DefenseAttorney = <cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						SubmittingAttorney = <cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						dateLastModified = getdate(),
						isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="cf_sql_BIT">
					where 
						verdictid = <cfqueryparam value="#event.getValue('verdictid')#" cfsqltype="cf_sql_integer">
				</cfquery>
			</cfif>
			<cfoutput>
				<p class="headerText">Information Saved. 
				<cfif NOT val(event.getValue('customPage.myRights.customAddDatabase',0))>Once approved, the information you entered will be searchable in the database.</cfif>
				</p>
				<br />
				<table border="0" cellpadding="2">
					<tr>
						<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
							<form action="#local.resultsLink#" method="post">
							<CFLOOP INDEX="local.form_element" LIST="#session.lastverdictsearch.fieldnames#">
								<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#session.lastverdictsearch[local.form_element]#">
							</CFLOOP>
							<td><input type="submit" value="Return to Results" class="bodyText"/></td>
							</form>
						</cfif>
						<td><input type="button" onclick="parent.location='#local.baseLink#';" value="New Search" class="bodyText" /></td>
					</tr>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="results">
			<cfset local.pageID = int(val(event.getValue('page',0)))>
			<cfset local.maxrows = 10>
			<cfquery name="local.qryMatches" datasource="#application.dsn.customApps.dsn#">
				select *
				from IN_verdicts v
				left outer  join IN_VerdictsCourts c on c.courtID = v.courtID
				where 
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and len(event.getValue('isApproved'))>
					isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="CF_SQL_BIT">
				<cfelseif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					1=1
				<cfelse>
					isApproved = 1
				</cfif>
				<cfif len(event.getValue('resolutiontypes',''))>
					and resolutiontype = <cfqueryparam value="#event.getValue('resolutiontypes')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('casetypes',''))>
					and casetype = <cfqueryparam value="#event.getValue('casetypes')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('countyname',''))>
					and countyname = <cfqueryparam value="#event.getValue('countyname')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('distinctyears',''))>
					and year(date) = <cfqueryparam value="#event.getValue('distinctyears')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif len(event.getValue('keywords',''))>
					and (
						isnull(resolutiontype,'') + ' ' + isnull(cast(amount as varchar(30)),'') + ' ' +
						isnull(amountdetail,'') + ' ' + isnull(casetype,'') + ' ' + isnull(casetypedetail,'') + ' ' + 
						isnull(casetitle,'') + ' ' + isnull(courtname,'') + ' ' + isnull(docketnumber,'') + ' ' + 
						isnull(facts,'') + ' ' + isnull(sex,'') + ' ' + isnull(age,'') + ' ' + 
						isnull(occupation,'') + ' ' + isnull(injuries,'') + ' ' + isnull(medicals,'') + ' ' +
						isnull(lostwages,'') + ' ' + isnull(coverage,'') + ' ' + isnull(settlementoffer,'') + ' ' + 
						isnull(experts,'') + ' ' + isnull(plaintiffAttorney,'') + ' ' + isnull(defenseAttorney,'') + ' ' +
						isnull(submittingattorney,'') + ' ' + isnull(countyname,'')
						LIKE <cfqueryparam value="%#event.getValue('keywords')#%" cfsqltype="CF_SQL_VARCHAR">
						)
				</cfif>
				order by verdictID
			</cfquery>
	
			<cfset session.lastVerdictSearch = duplicate(form)>
			<cfif local.qryMatches.recordcount>
				<cfset local.numpages = ceiling(local.qryMatches.recordcount / local.maxrows)>
				<cfset local.startrow = ((local.pageID-1) * local.maxrows) + 1>
				<cfset local.endrow = local.startrow + local.maxrows - 1>
				<cfif local.qryMatches.recordcount lt local.endrow>
					<cfset local.endrow = local.qryMatches.recordcount>
				</cfif>
			<cfelse>
				<cfset local.numpages = 0>
				<cfset local.startrow = 0>
				<cfset local.endrow = 0>
			</cfif>
			<cfoutput>
				<div class="headerText">Verdicts and Settlements Search Results</div>
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					<div class="bodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
				</cfif>
				<script language="JavaScript">
					function prevPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')-1#';
						objForm.submit();
					}
					function nextPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')+1#';
						objForm.submit();
					}
				</script>
				<br/>
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td class="bodyText">Showing #local.startrow# to #local.endrow# of #local.qryMatches.recordcount# matches</td>
						<td align="right">
							<cfif form.page gt 1>
								<input type="button" value="&lt;&lt; Previous Page" class="bodyText" onclick="prevPage();">
							</cfif>
							<cfif local.qryMatches.recordcount gt (form.page*local.maxrows)>
								<input type="button" value="Next Page &gt;&gt;" class="bodyText" onclick="nextPage();">
							</cfif>
							<input type="button" onclick="self.location.href='#local.baseLink#';" value="New Search" class="bodyText">
							<!--- ADD BUTTON: --->
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Verdict" class="bodyText">
							</cfif>
							
						</td>
					</tr>
				</table>
				<br/>
			</cfoutput>
			<cfif local.qryMatches.recordcount eq 0>
				<cfoutput><div class="bodyText">No records match your search criteria.</div></cfoutput>
			<cfelse>
				<cfoutput>
				<table border="0" class="bodyText" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
					<tr bgcolor="##999999">
						<td colspan="2"></td>
						<th align="left">Category</th>
						<th align="left">Date</th>
						<th align="left">Court / Case</th>
						<th align="left">Resolution</th>
					</tr>
				</cfoutput>
				<cfoutput query="local.qryMatches" startrow="#local.startrow#" maxrows="#local.maxrows#">
					<tr valign="top" <cfif local.qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#local.qryMatches.currentrow#.</td>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>>
							<a href="#local.viewLink#&verdictID=#local.qryMatches.verdictID#">View</a>
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<br/><a href="#local.editLink#&verdictID=#local.qryMatches.verdictID#">Edit</a>
							</cfif>
						</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.casetype#&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;"><cfif isdate(local.qryMatches.date)>#dateformat(local.qryMatches.date,"m/d/yyyy")#</cfif>&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">
							#local.qryMatches.courtname#
							<cfif len(local.qryMatches.courtname) and len(local.qryMatches.casetitle)><br/></cfif>
							#left(local.qryMatches.casetitle,100)#<cfif len(local.qryMatches.casetitle) gt 100>...</cfif>
							&nbsp;
						</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-right:2px solid ##FF0000;<cfelse>border-right:1px solid ##666;</cfif>">#local.qryMatches.resolutiontype#&nbsp;</td>
					</tr>
				</cfoutput>
				<cfoutput></table></cfoutput>
			</cfif>
			<cfoutput>	
				<form name="frmHidden" action="#local.resultsLink#" method="post">
				<input type="hidden" name="page" value="">
				<cfloop INDEX="local.form_element" LIST="#FORM.fieldnames#">
					<cfif local.form_element neq "page">
						<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#form[local.form_element]#">
					</cfif>
				</CFLOOP>
				</form>
			</cfoutput>
		</cfcase>
	
		<cfcase value="search">
			<cfset local.qryCaseTypes = getCaseTypes()>
			<cfset local.qryResolutionTypes = getResolutionTypes()>
			<cfset local.qryCountyNames = getCountyNames()>
			<cfset local.qryYears = getYears()>
			<cfoutput>
				<p><span class="headerText">Search ITLA Verdict and Settlement Exchange Database</span></p>
				<p><span class="bodyText">ITLA's Verdict and Settlement Database contains reports 
				submitted by members for publication in Verdict. The database records include 
				information regarding a verdict or a settlement as provided by ITLA members. 
				The Verdict and Settlement Database is a valuable service to our members and 
				we welcome all reports.</span></p>
			
				<cfform action="#local.resultsLink#" method="post">
					<input type="hidden" name="page" value="1" />
					<table class="bodyText">
					<tr>
						<td>Resolution Type:</td>
						<td>
							<select name="resolutiontypes" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryResolutionTypes">
								<option value="#local.qryResolutionTypes.resolutiontype#">#local.qryResolutionTypes.resolutiontype#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Case Category:</td>
						<td>
							<select name="casetypes" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryCaseTypes">
								<option value="#local.qryCaseTypes.casetype#">#local.qryCaseTypes.casetype#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Verdict Year:</td>
						<td>
							<select name="distinctyears" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryYears">
								<option value="#local.qryYears.year#">#local.qryYears.year#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>County:</td>
						<td>
							<select name="countyname" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryCountyNames">
								<option value="#local.qryCountyNames.countyname#">#local.qryCountyNames.countyname#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Keywords (optional):</td>
						<td><input type="text" name="keywords" class="bodyText" maxlength="70" size="70" /></td>
					</tr>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<tr>
							<td>Approval Status:</td>
							<td>
								<select name="isApproved" class="bodyText">
								<option value="">All</option>
								<option value="1">Approved Verdicts Only</option>
								<option value="0">Non-Approved Verdicts Only</option>
								</select>
							</td>
						</tr>
					</cfif>
					</table>
					<br />
					<input type="submit" value="Search Reports" class="bodyText"/>
					<!--- ADD BUTTON: --->
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.editLink#';" value="Add Verdict" class="bodyText" />
					</cfif>
					
				</cfform>
			</cfoutput>
		</cfcase>
	
		<!--- search --->
		<cfdefaultcase></cfdefaultcase>
	
	</cfswitch>



<cffunction name="getCaseTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT casetype
		FROM IN_verdicts
		WHERE casetype IS NOT NULL AND casetype <> ''
		ORDER BY casetype
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getResolutionTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT resolutiontype
		FROM IN_verdicts
		WHERE resolutiontype IS NOT NULL AND resolutiontype <> ''
		ORDER BY resolutiontype
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCountyNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT countyname
		FROM IN_verdicts
		WHERE countyname IS NOT NULL AND countyname <> ''
		ORDER BY countyname
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCourtNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT courtID, courtname
		FROM IN_verdictsCourts
		WHERE courtname IS NOT NULL AND courtname <> ''
		ORDER BY courtname
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getYears" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT YEAR(DATE) as year
		FROM IN_verdicts
		WHERE DATE IS NOT NULL AND DATE <> ''
		ORDER BY YEAR(DATE) DESC
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getVerdict" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select * 
		from IN_verdicts v
		left outer  join IN_VerdictsCourts c on c.courtID = v.courtID		
		where v.verdictid = <cfqueryparam value="#arguments.verdictID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfreturn qry>
</cffunction>