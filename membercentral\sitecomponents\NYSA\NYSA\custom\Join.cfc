<cfcomponent extends="model.customPage.customPage" output="false">
    <cfset variables.objCustomPageUtils = application.objCustomPageUtils>
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.crlf = chr(13) & chr(10);
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		local.arrCustomFields = [];

		local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ApplicationIntroText",type="CONTENTOBJ",desc="Application introduction text",value="Thank you for your interest in joining our association." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ApplicationTitle",type="CONTENTOBJ",desc="Application Title",value="Trial Academy Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberTypeTitle",type="STRING",desc="Membership Rate Title",value="Membership Type" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MembershipAgreement",type="CONTENTOBJ",desc="Required Membership Agreement",value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCC",type="STRING",desc="pay profile code for Credit Card",value="NYSACIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayLater",type="STRING",desc="pay profile code for Pay Later",value="PAYLTRCHK" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberActive",type="CONTENTOBJ",desc="Display message for active members",value="Trial Academy records indicate that you are currently a member. Please <a href='/?pg=login'>click here</a> to login." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberBilled",type="CONTENTOBJ",desc="Display message for billed members",value="You need to renew your Trial Academy membership. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
        local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
        local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmation to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
        local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="Trial Academy - Membership Application Form received" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
        local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="Trial Academy - Membership Application Form" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
	    local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page",value="Thank you for submitting your application. This Page has been emailed to the email address on file. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
        local.tmpField = { name="EmailConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation email",value="Thank you for submitting your application. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

		StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Join NYSA',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");

		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('historyID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('historyID')));
		variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
		variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
		variables.historyStartedText = "Member started join form.";
		variables.historyCompletedText = "Member completed join form.";

		switch (local.formAction) {
			case "processLookup":
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					case "spam":
						local.returnHTML = showError(errorCode='spam');
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;
			default:
				local.returnHTML = showLookup(memberKey=arguments.event.getTrimValue('mk',''));
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				.subRateLabel {font-weight:normal;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				var memberTypeField;

				function adjustFieldsetDisplay(contactTypeField) {
					var memType = contactTypeField.find('option:selected').text();
					switch(memType.trim()) {
						case 'Attorney': 
							showFieldsContactType('attorneyFieldSetHolder');
							break;
						default:
							showFieldsContactType('othersFieldSetHolder');
							break;
					}
				}

				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}

				function validateMembershipInfoForm(){
					var arrReq = new Array();

					//make sure selected subscriptions all have selected rates.
					$('input.subCheckbox:checkbox:checked').each(function(x,item){
						var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
						var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
						if ( numRates > 0 && numRatesChecked==0) {
							arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
						}
					});


					//make sure any chosen editable rates have amounts greater than zero.

					$('input.subRateCheckbox:radio:checked').each(function(index,item){
						var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
						if ($(rateOverrideField).length) {
							var overridePrice = parseFloat($(rateOverrideField)[0].value);
							if (!overridePrice) {
								var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
								subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
								arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
							}
						}
					});

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm(ispaymentrequired) {
					if(ispaymentrequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>

			<script type="text/javascript">
				function subscriptionRateOverrideBoxHandler(event) {
					var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());

					//check subscription if not already checked
					if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
						$(subRateRadioButton)[0].click();
						$(this).focus();
					} else if (subRateRadioButton) {
						$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
					}
				}

				function subscriptionCheckboxHandler() {
					if ($(this)[0].checked) {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
					} else {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
					}
				}

				function subscriptionRateRadioButtonHandler() {

					if ($(this)[0].checked) {
						var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
						var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
						var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();

						if (rateOverrideBox.length) {
							//rateoverride box is present
							rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
						}

						//put label of selected rate radio button next to subscription
						rateDescription = ' - ' + rateDescription;
						$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);

						//check subscription if not already checked
						if (!$(subCheckbox)[0].checked)
							$(subCheckbox)[0].click();
					}
				}

				function initializeAddons() {
					$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
				}

				function selectAllSubscriptionsIfRequired() {
					var addonData = $(this).data();
					// select all addons if minimum required by set is gte available count
					// hide checkboxes so they can not be unselected
					if (addonData.minallowed >= $('.subCheckbox',this).length) {
						$('.subCheckbox:not(:checked)',this).click().hide();
						$('.subCheckbox',this).hide();
					}
				}
			</script>


			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
		
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
			<cfif len(variables.strPageFields.ApplicationTitle)>
				<div class="row-fluid TitleText" id="ApplicationTitleId"><h2>#variables.strPageFields.ApplicationTitle#</h2></div>
			</cfif>
			<cfif len(variables.strPageFields.ApplicationIntroText)>
				<div class="row-fluid InfoText" id="ApplicationIntroText" style="margin-bottom:20px;">#variables.strPageFields.ApplicationIntroText#</div>
			</cfif>
			<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>

				
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfset local.SubTypeTest = 'C141F612-CC31-44A7-97EF-804FEC895091'>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
		
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>

					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>	
			</cfif>
		</cfif>
		
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>			
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.MemberActive#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.MemberActive#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						#variables.strPageFields.MemberBilled#
						<script type="text/javascript">
							setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
							function AJAXRenewSub(member){
								var redirect = function(r) {
									redirectLink = '/renewsub/' + r.data.directlinkcode[0];
									window.location = redirectLink;								
								};		
								
								var params = { memberID:member, status:'O', distinct:false };
								TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
							}						
						</script>
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structKeyExists(session,"useHistoryID")>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
			<cfset local.strPrefillMemberData = local.strData>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		
		<cfset local.applicantInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='B1CC6BD7-5F9F-403C-8F48-CD8F29EEA8A5', mode="collection", strData=local.strData)>
		<cfset local.homeAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='6A825AA2-A178-4E94-82A5-F8540D2A508C', mode="collection", strData=local.strData)>
		<cfset local.demographicFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='7674FAA9-B57D-4374-8D95-D7E7F8897D2B', mode="collection", strData=local.strData)>
	
		<cfset local.attorneyFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='02198ECA-8F8A-4C47-86C1-E047A170466E', mode="collection", strData=local.strData)>
		<cfset local.othersFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='83B3915C-A0E9-4D47-BE8E-3F3FDDCB5556', mode="collection", strData=local.strData)>
		
		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
		
		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				.content input[type="text"] {width:206px!important;}
				.content select{width:220px!important;}
				##ProfessionalLicenseFields input[type="text"] {width:auto!important;}
				##ProfessionalLicenseFields select{width:auto!important;}
				##ProfessionalLicenseFields{width:100%!important;}
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {white-space: initial!important;}
				@media screen and (max-width: 767px){
					##content-wrapper table td {display: block;margin-bottom:0px;}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##ProfessionalLicenseFields input[type="text"] {width:206px!important;}
					##ProfessionalLicenseFields select{width:220px!important;}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{padding: 10px !important;}
				@media (min-width: 1200px){
					.eachRow{margin-bottom: 5px;}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.areaStatus{
						text-align:left;
						margin-left: 0px!important;
					}
					.areaState{text-align:left;}
					##state_table  .proLicenseLabel{display:block;}
					.wrapLeft{display: table-cell!important;}
					.span3 input {width: 90%!important;}
				}
				.wrapLeft{display:none;}
				.jsLabel{
					display:none !important;
					font-weight:700;
					font-size: 9pt;
				}
				.areaStatus{margin-left:0 !important;}
				.span3{
					margin-left:0 !important;
					margin-right:10px !important;
				}
				@media  (min-width: 767px) and  (max-width: 1200px){
					.span3 input{width: 90%!important;}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.eachRow{margin-bottom: 5px;}
					.areaState{text-align:left;}
				}
				@media (max-width: 979px) and (min-width: 768px){
					.span3{
						margin-left:0 !important;
						margin-right:10px !important;
					}
					.span3 input{width: 90%!important;}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.eachRow{margin-bottom: 5px;}
					.areaState{text-align:left;}
				}
				@media (max-width: 767px){
					.eachRow{margin-bottom: 5px;}
					##state_table  .proLicenseLabel{display:none !important;}
					.jsLabel{display:block !important;margin-top: -5px;}
				}				
			</style>
			
			<script language="javascript">
				function afterFormLoad(){					
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				function checkCaptchaAndValidate(){
					var thisForm = document.forms['#variables.formName#'];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					var mcSel = $('##membertype option:selected').val();
					if(mcSel == '') {
						arrReq[arrReq.length] = "Member Type is required.";	
					} else {
						mcSel = $('##membertype option:selected').val().trim();
					}
		
					#local.applicantInfoFieldSet.jsValidation#
					#local.homeAddressFieldSet.jsValidation#					
					#local.demographicFieldSet.jsValidation#
					if(mcSel == 'Attorney'){
						#local.attorneyFieldSet.jsValidation#
					} else {
						#local.othersFieldSet.jsValidation#
					}
	
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}
					changeFa('processMemberInfo');
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function adjustFieldsetDisplay(contactTypeField) {
					var memType = contactTypeField.find('option:selected').val();
					switch(memType.trim()) {
						case 'Attorney': 
							showFieldsContactType('attorneyFieldSetHolder');
							moveContentToPlace('attorneyFieldSetTempHolder','attorneyFieldSetHolder');
							break;
						default:
							showFieldsContactType('othersFieldSetHolder');
							moveContentToPlace('nonAttorneyFieldSetTempHolder','othersFieldSetHolder');
							break;
					}
				}
				function showFieldsContactType(classList) {
					$(".attorneyFieldSetHolder").hide();
					$(".othersFieldSetHolder").hide();

					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]).show();							
						}			
					});							
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						<cfif FindNoCase("m_",local.thisKey)
							or FindNoCase("ma_",local.thisKey) 
							or FindNoCase("mat_",local.thisKey) 
							or FindNoCase("me_",local.thisKey) 
							or FindNoCase("mpl_",local.thisKey)							
							or FindNoCase("mp_",local.thisKey) 
							or FindNoCase("mw_",local.thisKey) 
							or FindNoCase("md_",local.thisKey)
							or FindNoCase("mccf_",local.thisKey)>
							#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfif>
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				function moveContentToPlace(tempLocation,actualLocation){
					var _html = $('##'+tempLocation).html();
					$('.attorneyFieldSetHolder .fieldSetContainer').html('');
					$('.othersFieldSetHolder .fieldSetContainer').html('');
					$('.'+actualLocation+' .fieldSetContainer').html(_html);

					
					<cfloop collection="#local.attorneyFieldSet.STRFIELDS#" item="local.key">
						<cfif local.key CONTAINS "activeDate">
							mca_setupDatePickerField('#local.key#');
						</cfif>
					</cfloop>

				}
				$(document).ready(function() {
					prefillData();
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
					memberTypeField = $('##membertype');
					$(memberTypeField).change(function(){adjustFieldsetDisplay($(this));});
					adjustFieldsetDisplay(memberTypeField);
				});
				function changeFa(fa){
					$('###variables.formName# ##fa').val(fa);
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.ApplicationTitle)>
					<div class="row-fluid TitleText" id="ApplicationTitleId"><h2>#variables.strPageFields.ApplicationTitle#</h2></div>
				</cfif>

		
					
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<cfif NOT local.isLoggedIn AND local.identifiedMemberID GT 0>
						<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
					</cfif>
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<cfif NOT local.isLoggedIn AND local.identifiedMemberID EQ 0>
						<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
						<div class="tsAppSectionContentContainer">
							<table cellspacing="0" cellpadding="2" border="0" width="100%">
							<tr>
								<td width="175" style="text-align:center;">
									<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="changeFa('processLookUp');selectMember();">#variables.strPageFields.AccountLocatorButton#</button>
								</td>
								<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
							</tr>
							</table>
						</div>
					</cfif>
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					
					<div id="content-wrapper" class="row-fluid">
						<span class="membertypeHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">Member Type</div>
								<div class="tsAppSectionContentContainer text-center">	
									<select class="tsAppBodyText largeBox" name="membertype" id="membertype">
										<option value="">Select Member Type</option>
										<cfloop array="#local.memberTypeFieldInfo.columnValueArr#" index="local.thisAOP">
											<option value="#local.thisAOP.columnValueString#">&nbsp;#local.thisAOP.columnValueString#</option>
										</cfloop>
									</select>															
								</div>
							</div>
						</span>

						<div class="row-fluid">						
							<div class="span12 tsAppSectionHeading">#local.applicantInfoFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer" id="applicantInfoFieldSet">
								#local.applicantInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<span class="homeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.homeAddressFieldSet.fieldSetContent#
								</div>
							</div>	
						</span>
						<span class="demographicFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.demographicFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.demographicFieldSet.fieldSetContent#															
								</div>
							</div>
						</span>
						<span class="attorneyFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.attorneyFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">											
								</div>
							</div>
						</span>
						<span class="othersFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.othersFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">												
								</div>
							</div>
						</span>	

						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div> 				
						
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					</div>
					#application.objWebEditor.showEditorHeadScripts()#
					<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = arguments.rc.mc_siteinfo.orgid, includeTags=0)>
					<script language="javascript">
						$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
							function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
								var _address = _this.val();
								
								if(_address.length >0){
									if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
										$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
									}
								} else {
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
								}
							}
							
							$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
							});
						</cfloop>
					});
					
						function editContentBlock(cid,srid,tname) {
							var editMember = function(r) {
								if (r.success && r.success.toLowerCase() == 'true') {
									$('##frmmd_'+cid).html(r.html);
									var x = div.getElementsByTagName("script");
									for(var i=0;i<x.length;i++) eval(x[i].text); 
								}
							};
							var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
							TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
						}
					</script>
					
					
				</form>
				<div id="nonAttorneyFieldSetTempHolder" class="hide">
					#local.othersFieldSet.fieldSetContent#	
				</div>
				<div id="attorneyFieldSetTempHolder"  class="hide">
					#local.attorneyFieldSet.fieldSetContent#	
				</div>

			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>
		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		<cfset local.objSaveMember.setMemberType(memberType='User')>

		<cfset local.membertypeField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
		<cfif structKeyExists(arguments.rc, "membertype") AND listLen(arguments.rc['membertype'])>
			<cfset local.contactTypeColumnValueIDlist = "">
			<cfloop array="#local.membertypeField.columnValueArr#" index="local.thisOption">
				<cfif listFindNoCase(arguments.rc['membertype'], local.thisOption.columnValueString)>
					<cfset local.contactTypeColumnValueIDlist = listAppend(local.contactTypeColumnValueIDlist,local.thisOption.valueID)>
				</cfif>
			</cfloop>
			<cfif listLen(local.contactTypeColumnValueIDlist)>
				<cfset local.objSaveMember.setCustomField(field='Member Type', valueID=local.contactTypeColumnValueIDlist)>
			</cfif>
		</cfif>
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		<cfif local.strResult.success>
			<cfset local.response = "success">
			<cfset variables.useMID = local.strResult.memberID>
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		
		<cfif local.response EQ "success">

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

		</cfif>

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '40A2296A-FAB8-4B20-9B52-3B7257FDD6B0')>

		<cfset local.qryRateInfo = variables.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.useMID, subscriptionUID='40A2296A-FAB8-4B20-9B52-3B7257FDD6B0',frequencyShortName='F', isRenewal=false)>
		<cfquery name="local.qryFirmMembershipRatesSelected" dbtype="query">
			select rfid, rateAmt, rateName, FREQUENCYNAME
			from [local].qryRateInfo					
			where RATEUID in ('D711761E-AE37-479E-8A6A-C98902D69B11','5BC835E1-EDB8-4343-A868-2A9DE64F9963')			
		</cfquery>
	
		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>		
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	
		<cfset local.membershipListFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='CE3000AD-DB92-4858-88AC-2E220C22E552', mode="collection", strData=local.strData)>
		        
		<cfsavecontent variable="local.headcode">
 			<cfoutput>	
			 	<style>
					div.alert-danger{
						padding: 10px !important;
					}
					##content-wrapper table td:nth-child(2) {
						white-space: nowrap;
						width: 1%;
					}

					@media screen and (max-width: 767px){
						##content-wrapper table td {
							display: block;
							margin-bottom:0px;
						}
						##content-wrapper table td:nth-child(1) {
							display: inline;
							margin: 0;
							padding: 0;
						}
						##content-wrapper table td:nth-child(2) {
							display: inline;
							margin: 0;
							padding: 0;
							white-space: initial!important;
						}
						##content-wrapper table td:nth-child(3) {
							display: inline;
							margin: 0;
							padding: 0;
						}
						##content-wrapper table td:nth-child(4) {
							margin-bottom: 12px;
							margin-left: 0;
							padding-left: 0;
						}
								
						##content-wrapper div.ui-multiselect-menu{width:auto!important;}
		
					}	
					##content-wrapper button.ui-multiselect {width:220px!important;}
					##content-wrapper div.ui-multiselect-menu {width:214px!important;}	
				</style>		 	
 				<script type="text/javascript">
				 	#local.result.jsAddonValidation#
					 <cfif local.qryFirmMembershipRatesSelected.recordCount GT 0>
						var _firmmembershipRateFrequencyIDs = [#valueList(local.qryFirmMembershipRatesSelected.rfid)#];	
					</cfif>

				 	function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();	
						var _CF_this = document.forms['#variables.formName#'];
						#local.result.JSVALIDATION#						

						<cfif val(local.subscriptionID)>
							if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
								arrReq[arrReq.length] = "Please select Membership.";
							}
						</cfif>
						$(".subRateOverrideBox").each(function(){
							var subId = $(this).attr("name").split("_")[0];
							if($("##"+subId).is(":checked")){
								var subscriptionName = $(this).parent().text().replace(/\$/g, '').trim();
								var minval = parseFloat($(this).data("ratemin"));
								var maxval = parseFloat($(this).data("ratemax"));
								var subval = parseFloat($(this).val());
								if(subval > 0 && minval > 0 && subval < minval){
									arrReq[arrReq.length] = 'Amount for the rate '+subscriptionName+' is below '+minval+'.';
								}
								if(subval > 0 && maxval > 0 && subval > maxval){
									arrReq[arrReq.length] = 'Amount for the rate '+subscriptionName+' is exceeds '+maxval+'.';
								}
							}
						});
						if($('input[data-subscriptionuid="40A2296A-FAB8-4B20-9B52-3B7257FDD6B0"]:checked').length){								
							if($.inArray($('input[data-subscriptionuid="40A2296A-FAB8-4B20-9B52-3B7257FDD6B0"]:checked').data('rfid'),_firmmembershipRateFrequencyIDs) >=0){
								#local.membershipListFieldSet.jsValidation#
							}
						}
						if($('##esignature').val().length == 0){
							arrReq[arrReq.length] = "Signature is required.";
						}

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}

						#local.result.jsValidation#

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					$(document).ready(function(){
						$('.membershipListFieldSetHolder').hide();
												
						if($('input[data-subscriptionuid="40A2296A-FAB8-4B20-9B52-3B7257FDD6B0"]').length > 1) {
							$('.subRateCheckbox').change(function(){
								var _this = $(this);

								if(_this.is(':checked')){
									if($.inArray(_this.data('rfid'),_firmmembershipRateFrequencyIDs)>=0){
										$('.membershipListFieldSetHolder').show();
									} else {
										$('.membershipListFieldSetHolder').hide();
									}
								}
							});
						} 
						if($('input[data-subscriptionuid="40A2296A-FAB8-4B20-9B52-3B7257FDD6B0"]:checked').length){
							
							if($.inArray($('input[data-subscriptionuid="40A2296A-FAB8-4B20-9B52-3B7257FDD6B0"]:checked').data('rfid'),_firmmembershipRateFrequencyIDs) >=0){
								$('.membershipListFieldSetHolder').show();
							} else {
								$('.membershipListFieldSetHolder').hide();
							}

						}
						if($('.documentField').length){
							$('.documentField').each(function(){
								$("##"+$(this).attr("id")+"_newFileDetails").html($(this).val());
							})
						}
					});
				</script>
 			</cfoutput>
 		</cfsavecontent>
 		<cfhtmlhead text="#local.headcode#">
				
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>		
				
			<cfform name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_" or local.thisField eq "membertype">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#
					</div>
				</div>

				<cfif arguments.rc['membertype'] EQ "Attorney">
					<cfset local.practiceAreasStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Practice Areas") >
					<cfset local.totalCount = ArrayLen(local.practiceAreasStruct.columnValueArr)>	
					<cfset local.numberofRows = Int(local.totalCount/2)+ (local.totalCount MOD 2)>
					<div class="row-fluid">
						<div class="span12 well">
							<div class="span12"><legend>Practice Areas</legend></div>
							<div class="tsAppSectionContentContainer">
								<table width="100%" cellpadding="3" border="0" cellspacing="0">	
									<cfloop from="1" to="#local.numberofRows#" index="local.currentRow" >
										<cfset local.secondColumnCount = local.currentRow + local.numberofRows>
										<tr>
										<td width="50%">
											<label class="checkbox subLabel" for="pa#local.currentRow#">
													<input class="subCheckbox" type="checkbox" name="practiceAreas" id="pa#local.secondColumnCount#" value="#local.practiceAreasStruct.columnValueArr[local.secondColumnCount].columnValueString#"> 
												<input class="subCheckbox" type="checkbox" name="practiceAreas" id="pa#local.currentRow#" value="#local.practiceAreasStruct.columnValueArr[local.currentRow].columnValueString#"> 
												#local.practiceAreasStruct.columnValueArr[local.currentRow].columnValueString#
											</label>
										</td> 
										<cfif local.secondColumnCount LTE local.totalCount>
											<td width="50%">
												<label class="checkbox subLabel" for="pa#local.secondColumnCount#">
													<input class="subCheckbox" type="checkbox" name="practiceAreas" id="pa#local.secondColumnCount#" value="#local.practiceAreasStruct.columnValueArr[local.secondColumnCount].columnValueString#"> 
													#local.practiceAreasStruct.columnValueArr[local.secondColumnCount].columnValueString#
												</label>
											</td> 
										</cfif>
										</tr>		

									</cfloop>
								</table>													
							</div>
						</div>
					</div>
				</cfif>

				<span class="membershipListFieldSetHolder">
					<div class="row-fluid well">
						<div class="span12 tsAppSectionHeading"><legend>#local.membershipListFieldSet.fieldSetTitle#</legend></div>
						<div class="tsAppSectionContentContainer fieldSetContainer" id="content-wrapper">	
							#local.membershipListFieldSet.fieldSetContent#												
						</div>
					</div>
				</span>

				<div class="row-fluid">
					<div class="span12">
						<div class="well control-group tsAppSectionContentContainer">	
							<label class="control-label">Signature*</label>
							<input type="text" name="esignature" id="esignature" class="input-xlarge">														
						</div>
					</div>
				</div>
				
			</div>

			<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
				#application.objWebEditor.showEditorHeadScripts()#
				
				<script language="javascript">			
					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid ='40A2296A-FAB8-4B20-9B52-3B7257FDD6B0')>
		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>					
				<script type="text/javascript">
					$(document).ready(function(){
						if (typeof arrUploaders !== 'undefined') {
							$.each(arrUploaders, function() {
								this.uploader.bind('BeforeUpload', function(uploader, file) {
									uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
								});
								this.uploader.start();								
							});
						}
					});
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		<cfset local.practiceAreasField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Practice Areas')>

		<cfif structKeyExists(arguments.rc, "practiceAreas") AND listLen(arguments.rc['practiceAreas'])>
			<cfset local.practiceAreasColumnValueIDlist = "">
			<cfloop array="#local.practiceAreasField.columnValueArr#" index="local.thisOption">
				<cfif listFindNoCase(arguments.rc['practiceAreas'], local.thisOption.columnValueString)>
					<cfset local.practiceAreasColumnValueIDlist = listAppend(local.practiceAreasColumnValueIDlist,local.thisOption.valueID)>
				</cfif>
			</cfloop>
			<cfif listLen(local.practiceAreasColumnValueIDlist)>
				<cfset local.objSaveMember.setCustomField(field='Practice Areas', valueID=local.practiceAreasColumnValueIDlist)>
			</cfif>
		</cfif>
		<cfset local.strResult1 = local.objSaveMember.saveData(runImmediately=1)>

		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
	
		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid ='40A2296A-FAB8-4B20-9B52-3B7257FDD6B0')>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		<cfset local.arrPayMethods = []>
		<cfif len(variables.strPageFields.ProfileCodePayCC) gt 0 AND variables.strPageFields.ProfileCodePayCC neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodePayCC)>		
		</cfif>
		<cfif len(variables.strPageFields.ProfileCodePayLater) gt 0 AND variables.strPageFields.ProfileCodePayLater neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodePayLater)>		
		</cfif>

		<cfset local.strReturn = 
			application.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
			
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
		
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
				<script type="text/javascript">
					$('##mccfdiv_#variables.strPageFields.ProfileCodePayCC# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
												
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub" or local.thisField eq "membertype" or local.thisField eq "practiceAreas"  >
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
	
			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
				<div class="tsAppSectionContentContainer">						
					#local.strResult.formContent#
					<br/><br/>
				</div>
			</div>	
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Total Price</div><br/>
				<div class="tsAppSectionContentContainer">						
					#dollarFormat(local.strResult.totalFullPrice)#
					<br/><br/>					
				</div>
			</div>
			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
		
		<!--- create subscriptions--->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = '40A2296A-FAB8-4B20-9B52-3B7257FDD6B0')>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = arguments.rc)>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>	

		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)>
	
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
		
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
		
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.applicantInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='B1CC6BD7-5F9F-403C-8F48-CD8F29EEA8A5', mode="confirmation", strData=arguments.rc)>
		<cfset local.homeAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='6A825AA2-A178-4E94-82A5-F8540D2A508C', mode="confirmation", strData=arguments.rc)>
		<cfset local.demographicFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='7674FAA9-B57D-4374-8D95-D7E7F8897D2B', mode="confirmation", strData=arguments.rc)>
		<cfset local.attorneyFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='02198ECA-8F8A-4C47-86C1-E047A170466E', mode="confirmation", strData=arguments.rc)>
		<cfset local.othersFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='83B3915C-A0E9-4D47-BE8E-3F3FDDCB5556', mode="confirmation", strData=arguments.rc)>
		<cfset local.membershipListFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='CE3000AD-DB92-4858-88AC-2E220C22E552', mode="confirmation", strData=arguments.rc)>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '40A2296A-FAB8-4B20-9B52-3B7257FDD6B0')>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div>
						<div>#variables.strPageFields.ConfirmationContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
					<div>
						<div>You have received an application for membership through the online membership application form.</br></br></div>
					</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.applicantInfoFieldSet.fieldSetContent#
				#local.homeAddressFieldSet.fieldSetContent#
				#local.demographicFieldSet.fieldSetContent#
				<cfif arguments.rc.membertype EQ "Attorney">
					#local.attorneyFieldSet.fieldSetContent#
					<cfif structKeyExists(arguments.rc, "practiceAreas") AND listLen(arguments.rc['practiceAreas'])>	
						<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
							<tr> <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-top:1px solid ##999;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Practice Areas</td> </tr>
							<tr>
							<td colspan="2" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
								#arguments.rc['practiceAreas']#
							</td>
							</tr>
						</table>
					</cfif>
				<cfelse>		
					#local.othersFieldSet.fieldSetContent#
				</cfif>	

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-top:1px solid ##999;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-top:1px solid ##999;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(arguments.rc, "m_firstname")>
			<cfset local.Name = arguments.rc['m_firstname']/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(arguments.rc, "m_lastname")>
			<cfset local.Name = local.Name & arguments.rc['m_lastname']/>
		</cfif>	
		
		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from},
			emailto=[{ name="", email=variables.memberEmail.to}],
			emailreplyto= variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTMLToMember,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#arguments.rc.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>
			
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="#variables.ORGEmail.subject#", emailContent=local.confirmationHTMLToStaff)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
					
				</style>
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
		
		<cfreturn local.confirmationHTMLToMember>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>