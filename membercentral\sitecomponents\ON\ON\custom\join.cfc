<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=join";
            local.formAction = arguments.event.getValue('fa','showLookup');
			variables.cfcuser = structNew();
			variables.formFields = structNew();
			
			variables.cfcuser = session.cfcuser;
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=variables.cfcuser);
			
			if(application.mcCacheManager.sessionValueExists('formFields')){
				variables.formFields = application.mcCacheManager.sessionGetValue('formFields',{});
			}
						
			variables.contactTypeFieldSetUID = "C7A35CFD-0632-4409-91FA-763E9D6A7C12";
			variables.yourInformationFieldSetUID = "295B38E9-070F-4D1B-91A0-2280CBF48298";
			variables.schoolInformationFieldSetUID = "BAD0A10D-152B-41D9-B2FD-1DDFBD2CD89E";						
			variables.addressFieldSetUID = "5734D05C-1FD9-4AB5-9B8A-FC7CBD6C98DB";
			variables.otherAddressFieldSetUID = "813304F4-08A1-43CC-8195-F407398B3654";
			variables.addressPreferenceFieldSetUID = "F1540077-BC0B-4497-85B3-8EAE8276C87F";
			variables.additionalInformationFieldSetUID = "4212EFDF-AF12-4C85-B807-66A58345EBDB";
			variables.paralegalProfessionalInformationFieldSetUID = "255FDB0B-7919-4761-B160-394B793339AF";
			variables.practiceCompositionInformationFieldSetUID = "E3DA042A-D5C6-4F39-8A7E-A8CE1EDD9C46";
			variables.areasofPracticeFieldSetUID = "7B267AFC-0920-4513-9DCF-DD875D2A5CAA";
			variables.certificationbySolicitorFieldSetUID = "B2871753-DB76-4089-95E5-1A3F9EE55C66";
			variables.lawClerkCertificationsFieldSetUID = "ACEFB7E5-DB82-417D-B452-CEE8F34518E1";
			variables.paralegalCertificationsFieldSetUID = "DAC55062-E6A0-47A0-BF6A-6EA8BCF5E73B";
			variables.articlingStudentCertificationFieldSetUID = "FAC0ED25-130F-4BD0-BCE8-2E81365DFEF5";
			variables.certificationbyLPPCandidateFieldSetUID = "07A82499-422E-47CE-AC94-EDEC170C05C8";
			variables.certificationbyNCACandidateFieldSetUID = "4640785B-384C-4718-8796-E4637E2684C7";
			variables.associateMembershipReasonFieldSetUID = "48C1534E-3D6A-42D8-9B23-5221C793301F";														   

            variables.currentDate = dateTimeFormat(now());

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];

            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			    arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="D5B2C8C6-DD36-4D32-80B8-5AA66CD76714" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a OTLA member. If you have questions about your membership, please call (905) 639-6852." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (905) 639-6852." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="OTLA Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalLicContent",type="CONTENTOBJ",desc="Content at top of Professional License fields",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);            
			local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="8CD41D9B-E968-47B3-951B-11963FEA3145" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="976B5BF9-0A5E-4166-80F1-095E0D6F1010" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="SageCIM" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="OTLA Membership Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipDuesSummary",type="CONTENTOBJ",desc="Membership Dues Summary",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);			

            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo= variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
            ));

			variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");

            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
            variables.origMemberID = variables.useMID;
            if(local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
				(structCount(variables.formFields) gt 0) and 
                structKeyExists(variables.formFields, "step0") and 
                structKeyExists(variables.formFields.step0, "memberID") and 
                int(val(variables.formFields.step0.memberID)) gt 0){            
                    variables.useMID = variables.formFields.step0.memberID;
                    if(structKeyExists(variables.formFields.step0, "origMemberID") and int(val(variables.formFields.step0.origMemberID)) gt 0){
                        variables.origMemberID = variables.formFields.step0.origMemberID;
                    }              

            }else if(variables.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = variables.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }
            if( local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                local.formAction neq "showMemberInfo" and 
                local.formAction neq "processMemberInfo" and 
                structKeyExists(variables.formFields, "step1") and 
                structKeyExists(variables.formFields.step1, "useHistoryID") and 
                int(val(variables.formFields.step1.useHistoryID)) gt 0){
                    variables.useHistoryID = int(val(variables.formFields.step1.useHistoryID));
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=variables.cfcuser);
            local.subStatus = "";
            if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
                local.subStatus = hasSub(int(variables.useMID));
            }

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin');  
            }else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
                local.returnHTML = showError(errorCode=local.subStatus);  
            }else{
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMemberInfo();
                                break;		
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        switch (processMemberInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMembershipInfo();
                                break;
                            case "spam":
                                local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMembershipInfo":
                        switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showPayment();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;                   
                    case "processPayment":
                        local.processStatus = processPayment(event=arguments.event);
                        switch (local.processStatus) {
                            case "success":
                                local.returnHTML = showConfirmation();
                                application.objUser.setIdentifiedMemberIDfromID(cfcuser=variables.cfcuser, memberID=0);
                                variables.formFields = structNew();
								application.mcCacheManager.sessionDeleteValue("formFields")
                                break;
                            default:
                                local.returnHTML = showError(errorCode='failpayment');
                                break;				
                        }
                        break;
                    case "showMembershipInfo":
                        local.returnHTML = showMembershipInfo();
                        break;	
                    case "showMemberInfo":
                        local.returnHTML = showMemberInfo();
                        break;                    		
                    default:
						if(application.mcCacheManager.sessionValueExists("captchaEntered")) {	
							application.mcCacheManager.sessionDeleteValue("captchaEntered")
						}
						
						variables.formFields = structNew();
		                if(application.mcCacheManager.sessionValueExists("formFields")) {
							application.mcCacheManager.sessionDeleteValue("formFields")
						}
                        local.returnHTML = showLookup();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");        
    	</cfscript>
    </cffunction>


    <cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>          

            #variables.pageJS# 

			<style type="text/css">
			</style>

			<script type="text/javascript">

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}

				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);

					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				

				function addMember(memObj) {
					assignMemberData(memObj);
				}

				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");                            

				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
                <cfinput type="hidden" name="fa" id="fa" value="processLookup">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
                <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">

                <cfif len(variables.strPageFields.FormTitle)>
                    <div class="row-fluid" id="FormTitleId">
                        <div class="span12">
                            <h1>#variables.strPageFields.FormTitle#</h1>
                        </div>
                    </div>
                </cfif>	

				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>	
			</cfform>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step0")>
            <cfset structDelete(variables.formFields, "step0")>
        </cfif>	

        <cfset variables.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.fieldSetUIDlist = '#variables.contactTypeFieldSetUID#,#variables.yourInformationFieldSetUID#,#variables.schoolInformationFieldSetUID#,#variables.addressFieldSetUID#,#variables.otherAddressFieldSetUID#,#variables.additionalInformationFieldSetUID#,#variables.associateMembershipReasonFieldSetUID#'>
		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>
		
        <cfset local.memberTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.graduationDateField = {fieldCode="",fieldLabel=""}>
		<cfset local.anticipatedCallToBarDateField = {fieldCode="",fieldLabel=""}>
		<cfset local.officeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.officestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.officepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.otherAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.otherAddressCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.otherAddressstateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.otherAddresspostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.requestingAssociateMembershipField = {fieldCode="",fieldLabel=""}>
		<cfset local.illnessField = {fieldCode="",fieldLabel=""}>
		<cfset local.parentalField = {fieldCode="",fieldLabel=""}>
		<cfset local.firmField = {fieldCode="",fieldLabel=""}>

        <cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
         <cfset local.strData.one = checkSessionExist("step1")/>
		
        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
            <cfset local.requestingAssociateMembershipData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='I am requesting Associate Membership due to the following:']")/>
            <cfif arrayLen(local.requestingAssociateMembershipData)>
                <cfset local.requestingAssociateMembershipField.fieldCode = local.requestingAssociateMembershipData[1].XmlAttributes.fieldCode>
                <cfset local.requestingAssociateMembershipField.fieldLabel = local.requestingAssociateMembershipData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.illnessData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Illness Anticipated Return Date']")/>
            <cfif arrayLen(local.illnessData)>				
                <cfset local.illnessField.fieldCode = local.illnessData[1].XmlAttributes.fieldCode>
                <cfset local.illnessField.fieldLabel = local.illnessData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.parentalData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Parental Leave Anticipated Return Date']")/>
            <cfif arrayLen(local.parentalData)>				
                <cfset local.parentalField.fieldCode = local.parentalData[1].XmlAttributes.fieldCode>
                <cfset local.parentalField.fieldLabel = local.parentalData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.firmData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Firm Retiring From']")/>
            <cfif arrayLen(local.firmData)>				
                <cfset local.firmField.fieldCode = local.firmData[1].XmlAttributes.fieldCode>
                <cfset local.firmField.fieldLabel = local.firmData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.memberTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Contact Type']")/>
            <cfif arrayLen(local.memberTypeData)>				
                <cfset local.memberTypeField.fieldCode = local.memberTypeData[1].XmlAttributes.fieldCode>
                <cfset local.memberTypeField.fieldLabel = local.memberTypeData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.graduationDateData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Graduation Date']")/>
            <cfif arrayLen(local.graduationDateData)>				
                <cfset local.graduationDateField.fieldCode = local.graduationDateData[1].XmlAttributes.fieldCode>
                <cfset local.graduationDateField.fieldLabel = local.graduationDateData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.anticipatedCallToBarDateData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Anticipated Call to Bar Date']")/>
            <cfif arrayLen(local.anticipatedCallToBarDateData)>				
                <cfset local.anticipatedCallToBarDateField.fieldCode = local.anticipatedCallToBarDateData[1].XmlAttributes.fieldCode>
                <cfset local.anticipatedCallToBarDateField.fieldLabel = local.anticipatedCallToBarDateData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.officeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_address1']")/>
			<cfif arrayLen(local.officeAddress1FieldData)>				
				<cfset local.officeAddress1Field.fieldCode = local.officeAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeAddress1Field.fieldLabel = local.officeAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.officeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_city']")/>
			<cfif arrayLen(local.officeCityFieldData)>				
				<cfset local.officeCityField.fieldCode = local.officeCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeCityField.fieldLabel = local.officeCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.officestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_stateprov']")/>
			<cfif arrayLen(local.officestateprovFieldData)>				
				<cfset local.officestateprovField.fieldCode = local.officestateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officestateprovField.fieldLabel = local.officestateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.officepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_postalcode']")/>
			<cfif arrayLen(local.officepostalcodeFieldData)>				
				<cfset local.officepostalcodeField.fieldCode = local.officepostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officepostalcodeField.fieldLabel = local.officepostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.otherAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Other Address_address1']")/>
			<cfif arrayLen(local.otherAddress1FieldData)>
				<cfset local.otherAddress1Field.fieldCode = local.otherAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.otherAddress1Field.fieldLabel = local.otherAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.otherAddressCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Other Address_city']")/>
			<cfif arrayLen(local.otherAddressCityFieldData)>				
				<cfset local.otherAddressCityField.fieldCode = local.otherAddressCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.otherAddressCityField.fieldLabel = local.otherAddressCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.otherAddressstateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Other Address_stateprov']")/>
			<cfif arrayLen(local.otherAddressstateprovFieldData)>				
				<cfset local.otherAddressstateprovField.fieldCode = local.otherAddressstateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.otherAddressstateprovField.fieldLabel = local.otherAddressstateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.otherAddresspostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Other Address_postalcode']")/>
			<cfif arrayLen(local.otherAddresspostalcodeFieldData)>				
				<cfset local.otherAddresspostalcodeField.fieldCode = local.otherAddresspostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.otherAddresspostalcodeField.fieldLabel = local.otherAddresspostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>

			<cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.contactTypeFieldSetUID#,#variables.yourInformationFieldSetUID#,#variables.schoolInformationFieldSetUID#,#variables.addressFieldSetUID#,#variables.otherAddressFieldSetUID#,#variables.addressPreferenceFieldSetUID#',local.fieldSetUid)>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				<cfif NOT variables.isLoggedIn AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>				
					<cfloop collection="#local.memberFieldData#" item="local.key" >
						<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
							<cfset StructDelete(local.memberFieldData, local.key)>
						</cfif>					
					</cfloop>
				</cfif>
			</cfif>
        </cfloop>
        <cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
        <cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID = variables.orgid)>

		<cfset local.contactTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactTypeFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.yourInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.yourInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.schoolInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.schoolInformationFieldSetUID, mode="collection", strData=local.strData.one)>		
		<cfset local.addressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.otherAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.otherAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.additionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.paralegalProfessionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.paralegalProfessionalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.practiceCompositionInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.practiceCompositionInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.areasofPracticeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.areasofPracticeFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.certificationbySolicitorFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.certificationbySolicitorFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.lawClerkCertificationsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.lawClerkCertificationsFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.paralegalCertificationsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.paralegalCertificationsFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.articlingStudentCertificationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.articlingStudentCertificationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.certificationbyLPPCandidateFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.certificationbyLPPCandidateFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.certificationbyNCACandidateFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.certificationbyNCACandidateFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.associateMembershipReasonFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.associateMembershipReasonFieldSetUID, mode="collection", strData=local.strData.one)>																																								   
       
		<cfset local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
        <cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>
		</cfif>

        <cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
        <cfset local.licenseStatus = {}>
        <cfset local.index = 1>
        <cfloop query="local.qryOrgProLicenseStatuses">
            <cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
            <cfset local.index = local.index + 1>
        </cfloop> 
        <cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}

				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}

					##content-wrapper div.ui-multiselect-menu{width:auto!important;}

					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}

				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							

				div.alert-danger{padding: 10px !important;}
				.yourInformationFieldSetHolder [type=button]{margin-bottom: 10px;}

			</style>
			<script language="javascript">
				function afterFormLoad(){
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}
				function requestingAssociateMembershipFieldDisplay(){
					var _CF_this = document.forms['#variables.formName#'];
					var requestingAssociateMembership = $.trim($(_CF_this['#local.requestingAssociateMembershipField.fieldCode#']).find('option:selected').text());
					switch(requestingAssociateMembership) {
						case 'Illness':
							$("###local.illnessField.fieldCode#").parents('tr').show();
							$("###local.parentalField.fieldCode#").parents('tr').hide();
							$("###local.firmField.fieldCode#").parents('tr').hide();
							break;
						case 'Parental leave':
							$("###local.illnessField.fieldCode#").parents('tr').hide();
							$("###local.parentalField.fieldCode#").parents('tr').show();
							$("###local.firmField.fieldCode#").parents('tr').hide();		
							break
						case 'Retiring from the practice of law':
							$("###local.illnessField.fieldCode#").parents('tr').hide();
							$("###local.parentalField.fieldCode#").parents('tr').hide();
							$("###local.firmField.fieldCode#").parents('tr').show();
							break
						default:
							$("###local.illnessField.fieldCode#").parents('tr').hide();
							$("###local.parentalField.fieldCode#").parents('tr').hide();
							$("###local.firmField.fieldCode#").parents('tr').hide();
							break;
					}
				}
				function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					$(".professionalLicensesHolder .mpl_pltypeid").parent().parent().children().first().text('');
					$("###local.graduationDateField.fieldCode#").parents('tr').show();
					$("###local.anticipatedCallToBarDateField.fieldCode#").parents('tr').show();
					$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('');
					$("###local.anticipatedCallToBarDateField.fieldCode#").parent().parent().parent().children().first().text('');
					switch(memType) {
						case 'Lawyer':
							$(".professionalLicensesHolder .mpl_pltypeid").parent().parent().children().first().text('*');
							showFieldsByContainerClass('schoolInformationFieldSet,professionalLicenses,practiceCompositionInformationFieldSet,areasofPracticeFieldSet,certificationbySolicitorFieldSet');
							resetFormFieldsByContainerClass('lawClerkCertificationsFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder');
							$("###local.graduationDateField.fieldCode#").parents('tr').hide();
							$("###local.anticipatedCallToBarDateField.fieldCode#").parents('tr').hide();
							break;							
						case 'Law Student':
							showFieldsByContainerClass('schoolInformationFieldSet');
							resetFormFieldsByContainerClass('areasofPracticeFieldSetHolder,lawClerkCertificationsFieldSetHolder,practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							$("###local.anticipatedCallToBarDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							break
						case 'Law Clerk':
							showFieldsByContainerClass('areasofPracticeFieldSet,lawClerkCertificationsFieldSet');
							resetFormFieldsByContainerClass('schoolInformationFieldSetHolder,practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							break
						case 'Paralegal':
							showFieldsByContainerClass('paralegalProfessionalInformationFieldSet,areasofPracticeFieldSet,paralegalCertificationsFieldSet');
							resetFormFieldsByContainerClass('schoolInformationFieldSetHolder,practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,lawClerkCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							break
						case 'Articling Student':
							showFieldsByContainerClass('schoolInformationFieldSet,areasofPracticeFieldSet,articlingStudentCertificationFieldSet');
							resetFormFieldsByContainerClass('practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,lawClerkCertificationsFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							$("###local.anticipatedCallToBarDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							break
						case 'LPP Candidate Training Program':
							showFieldsByContainerClass('schoolInformationFieldSet,areasofPracticeFieldSet,certificationbyLPPCandidateFieldSet');
							resetFormFieldsByContainerClass('practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,lawClerkCertificationsFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							$("###local.anticipatedCallToBarDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							break
						case 'LPP Candidate Workplace Training':
							showFieldsByContainerClass('schoolInformationFieldSet,areasofPracticeFieldSet,certificationbyLPPCandidateFieldSet');
							resetFormFieldsByContainerClass('practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,lawClerkCertificationsFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyNCACandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							$("###local.anticipatedCallToBarDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							break
						case 'NCA Candidate':
							showFieldsByContainerClass('schoolInformationFieldSet,areasofPracticeFieldSet,certificationbyNCACandidateFieldSet');
							resetFormFieldsByContainerClass('practiceCompositionInformationFieldSetHolder,certificationbySolicitorFieldSetHolder,lawClerkCertificationsFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,associateMembershipReasonFieldSetHolder,professionalLicensesHolder');					
							$("###local.graduationDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							$("###local.anticipatedCallToBarDateField.fieldCode#").parent().parent().parent().children().first().text('*');
							break
						case 'Associate Member*':
							showFieldsByContainerClass('schoolInformationFieldSet,associateMembershipReasonFieldSet');
							resetFormFieldsByContainerClass('practiceCompositionInformationFieldSetHolder,areasofPracticeFieldSetHolder,certificationbySolicitorFieldSetHolder,lawClerkCertificationsFieldSetHolder,paralegalProfessionalInformationFieldSetHolder,paralegalCertificationsFieldSetHolder,articlingStudentCertificationFieldSetHolder,certificationbyLPPCandidateFieldSetHolder,certificationbyNCACandidateFieldSetHolder,professionalLicensesHolder');
							$("###local.graduationDateField.fieldCode#").parents('tr').hide();
							$("###local.anticipatedCallToBarDateField.fieldCode#").parents('tr').hide();
							
							var requestingAssociateMembershipField = $('##'+"#local.requestingAssociateMembershipField.fieldCode#");
							$(requestingAssociateMembershipField).change(function(){requestingAssociateMembershipFieldDisplay();});
							$(requestingAssociateMembershipField).trigger('change');
							break
						default:
							showFieldsByContainerClass('');
							break;
					}
				}
				
				function reloadElements(){
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						var _function = _this.data('function');

						if(typeof _function != "undefined" &&_function.includes('multiSelect')){
							_this.next().remove();
						} 
						eval(_function)();
					});
				}
			
				function showFieldsByContainerClass(classList)				
				{
					var elementArray = [];
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						elementArray.push(_this);
					});
					
					$(".fieldSetHolder").html('');
					
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());							
						}			
					});					
					reloadElements();
					elementArray.forEach(function(element) {
						var elementId = element.attr('id');
						$("##"+elementId).val(element.val());
					});
				}

				function showFieldByFieldList(fieldName)				
				{
					var _CF_this = document.forms['#variables.formName#'];
					var fieldNameListArray = (fieldName).split(",");
					$.each(fieldNameListArray,function(i){	
						$(_CF_this[fieldNameListArray[i]]).parents('tr').show();
					});			
				}

				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]).html('');
						});
					}
				}

				function resetFormFieldByFieldList(fieldName){
					var _CF_this = document.forms['#variables.formName#'];
					if(fieldName.length){
						var fieldNameListArray = (fieldName).split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}else{
						var fieldNameListArray=("").split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}
				}

				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					
                    #local.contactTypeFieldSet.jsValidation#
					#local.yourInformationFieldSet.jsValidation#
					#local.schoolInformationFieldSet.jsValidation#
					if(memType.length > 0 && (memType == 'Lawyer')){

						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();    
									if($("##mpl_"+val+"_licenseNumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' #local.strProfLicenseLabels.profLicenseNumberLabel#.'; }                                
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' #local.strProfLicenseLabels.profLicenseDateLabel#.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}

					}
					
					var otherAddressRequired = false;
					var addressRequired = false;
					#local.addressFieldSet.jsValidation#
                    #local.otherAddressFieldSet.jsValidation#    

					var isSetAllOfficeAddress = 0;		
					<cfif len(trim(local.officeAddress1Field.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllOfficeAddress = isSetAllOfficeAddress + 1;}
					</cfif>
					<cfif len(trim(local.officeCityField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officeCityField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
					</cfif>
					<cfif len(trim(local.officestateprovField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officestateprovField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
					</cfif>
					<cfif len(trim(local.officepostalcodeField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
					</cfif>

					var isSetAllOtherAddress = 0;		
					<cfif len(trim(local.otherAddress1Field.fieldCode))>                        
						if(_CF_hasValue(_CF_this['#local.otherAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllOtherAddress = isSetAllOtherAddress + 1;}
					</cfif>
					<cfif len(trim(local.otherAddressCityField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.otherAddressCityField.fieldCode#'], "TEXT", false)) isSetAllOtherAddress = isSetAllOtherAddress + 1;
					</cfif>
					<cfif len(trim(local.otherAddressstateprovField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.otherAddressstateprovField.fieldCode#'], "TEXT", false)) isSetAllOtherAddress = isSetAllOtherAddress + 1;
					</cfif>
					<cfif len(trim(local.otherAddresspostalcodeField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.otherAddresspostalcodeField.fieldCode#'], "TEXT", false)) isSetAllOtherAddress = isSetAllOtherAddress + 1;
					</cfif>

					if(addressRequired && isSetAllOfficeAddress !=4){
						arrReq[arrReq.length] = "The following fields are required for Address:<ul><li>Address</li><li>City</li><li>State</li><li>Postal Code</li></ul>";
					}else if(isSetAllOfficeAddress > 0 && isSetAllOfficeAddress !=4){
						arrReq[arrReq.length] = "The following fields are required for Address:<ul><li>Address</li><li>City</li><li>State</li><li>Postal Code</li></ul>";                        
					}

					if(otherAddressRequired && isSetAllOtherAddress !=4){
						arrReq[arrReq.length] = "The following fields are required for Other Address:<ul><li>Address</li><li>City</li><li>State</li><li>Postal Code</li></ul>";
					}else if(isSetAllOtherAddress > 0 && isSetAllOtherAddress !=4){
						arrReq[arrReq.length] = "The following fields are required for Other Address:<ul><li>Address</li><li>City</li><li>State</li><li>Postal Code</li></ul>";                      
					}
					
                    #local.addressPreferencesFieldSet.jsValidation#
					#local.additionalInformationFieldSet.jsValidation#
					
					if(memType.length > 0 && (memType == 'Paralegal')){
						#local.paralegalProfessionalInformationFieldSet.jsValidation#
					}
					if(memType.length > 0 && (memType == 'Lawyer')){
						#local.practiceCompositionInformationFieldSet.jsValidation#
					}
					if(memType.length > 0 && (memType == 'Lawyer' || memType == 'Law Clerk' || memType == 'Paralegal' || memType == 'Articling Student' || memType == 'LPP Candidate Training Program' || memType == 'LPP Candidate Workplace Training' || memType == 'NCA Candidate')){
						#local.areasofPracticeFieldSet.jsValidation#
					}
					if(memType.length > 0 && (memType == 'Lawyer')){
						#local.certificationbySolicitorFieldSet.jsValidation#
					}
					if(memType.length > 0 && (memType == 'Law Clerk')){
						#local.lawClerkCertificationsFieldSet.jsValidation#
					}
					if(memType.length > 0 && (memType == 'Law Student')){
						<cfif len(trim(local.graduationDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.graduationDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Graduation Date is required.";
                        </cfif>
						<cfif len(trim(local.anticipatedCallToBarDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.anticipatedCallToBarDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Anticipated Call to Bar Date is required.";
                        </cfif>
					}
					if(memType.length > 0 && (memType == 'Paralegal')){
						#local.paralegalCertificationsFieldSet.jsValidation#
					}
					if(memType.length > 0 && (memType == 'Articling Student')){
						#local.articlingStudentCertificationFieldSet.jsValidation#
						<cfif len(trim(local.graduationDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.graduationDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Graduation Date is required.";
                        </cfif>
						<cfif len(trim(local.anticipatedCallToBarDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.anticipatedCallToBarDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Anticipated Call to Bar Date is required.";
                        </cfif>
					}
					if(memType.length > 0 && (memType == 'LPP Candidate Training Program' || memType == 'LPP Candidate Workplace Training')){
						#local.certificationbyLPPCandidateFieldSet.jsValidation#
						<cfif len(trim(local.graduationDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.graduationDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Graduation Date is required.";
                        </cfif>
						<cfif len(trim(local.anticipatedCallToBarDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.anticipatedCallToBarDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Anticipated Call to Bar Date is required.";
                        </cfif>
					}
					if(memType.length > 0 && (memType == 'NCA Candidate')){
						#local.certificationbyNCACandidateFieldSet.jsValidation#
						
						<cfif len(trim(local.graduationDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.graduationDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Graduation Date is required.";
                        </cfif>
						<cfif len(trim(local.anticipatedCallToBarDateField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.anticipatedCallToBarDateField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Anticipated Call to Bar Date is required.";
                        </cfif>
					}
					if(memType.length > 0 && (memType == 'Associate Member*')){
						#local.associateMembershipReasonFieldSet.jsValidation#
					}

					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

                function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
                    $("##state_table").show();
                    if(status == ''){
                        status = 'Active';
                    }
                    strOption = '';
                    <cfloop collection="#local.licenseStatus#" item="local.i" >
                        strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
                    </cfloop>
                    if(isChecked){
                        $('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
                                '<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
                                '<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
                                '<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
                                '<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
                                '<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
                                '</div>');
                        $('##mpl_'+val+'_status').val(status);
                        mca_setupDatePickerField('mpl_'+val+'_activeDate');
                        $('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
                    }									
                    else{
                        $("##tr_state_"+val).remove();								
                    }
                    if($('##selectedLicense .row-fluid').length == 0){
                        $("##state_table").hide();
                    }	
                }
				
				function professionalLicensesmultiSelectReload(){									

					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					<cfelseif local.qryProLicenses.recordCount GT 0>
						<cfloop query="local.qryProLicenses">
							licenseChange(true,'#local.qryProLicenses.PLTypeID#','#local.qryProLicenses.PLName#','#local.qryProLicenses.LicenseNumber#','#local.qryProLicenses.ActiveDate#','#local.qryProLicenses.StatusName#');	
						</cfloop>
						<cfloop query="local.qryProLicenses">
							<cfset local.profLicenseIDList = listAppend(local.profLicenseIDList, local.qryProLicenses.PLTypeID)>
						</cfloop>						
					</cfif>
					
					$(".professionalLicensesHolder .mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' Please Select ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".professionalLicensesHolder .mpl_pltypeid option").each(function(){
								$('.professionalLicensesHolder ##tr_state_'+$(this).attr("value")).remove();
							});
							if($('.professionalLicensesHolder ##selectedLicense .row-fluid').length == 0){
								$(".professionalLicensesHolder ##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".professionalLicensesHolder .mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
				}
				
				$(document).ready(function() {	

					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>
					
					<cfif NOT application.mcCacheManager.sessionValueExists('captchaEntered') >
						showCaptcha();
					</cfif>

					var memberTypeField = $('##'+"#local.memberTypeField.fieldCode#");
					$(memberTypeField).change(function(){adjustFieldsetDisplay();});
					$(memberTypeField).trigger('change');
					
					
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif application.mcCacheManager.sessionValueExists("captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif> enctype="multipart/form-data">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                    <input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<h1>#variables.strPageFields.FormTitle#</h1>
							</div>
						</div>
					</cfif>		

					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>
						
						<span class="contactTypeFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.contactTypeFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.contactTypeFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
						
						<span class="yourInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.yourInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.yourInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
						
						<span class="schoolInformationFieldSetHolder fieldSetHolder"></span>
						
						<span class="professionalLicensesHolder fieldSetHolder"></span>				
						
						<span class="addressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.addressFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

                        <span class="otherAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.otherAddressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.otherAddressFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

                        <span class="addressPreferencesFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressPreferencesFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.addressPreferencesFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
						
						<span class="additionalInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.additionalInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.additionalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>			

						<span class="paralegalProfessionalInformationFieldSetHolder fieldSetHolder"></span>
						<span class="practiceCompositionInformationFieldSetHolder fieldSetHolder"></span>
						<span class="areasofPracticeFieldSetHolder fieldSetHolder"></span>
						<span class="certificationbySolicitorFieldSetHolder fieldSetHolder"></span>
						<span class="lawClerkCertificationsFieldSetHolder fieldSetHolder"></span>
						<span class="paralegalCertificationsFieldSetHolder fieldSetHolder"></span>
						<span class="articlingStudentCertificationFieldSetHolder fieldSetHolder"></span>
						<span class="certificationbyLPPCandidateFieldSetHolder fieldSetHolder"></span>
						<span class="certificationbyNCACandidateFieldSetHolder fieldSetHolder"></span>
						<span class="associateMembershipReasonFieldSetHolder fieldSetHolder"></span>

						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>
				
				<span class="schoolInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.schoolInformationFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.schoolInformationFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
						
				<span class="paralegalProfessionalInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.paralegalProfessionalInformationFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.paralegalProfessionalInformationFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="practiceCompositionInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.practiceCompositionInformationFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.practiceCompositionInformationFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="areasofPracticeFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.areasofPracticeFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.areasofPracticeFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="certificationbySolicitorFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.certificationbySolicitorFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.certificationbySolicitorFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="lawClerkCertificationsFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.lawClerkCertificationsFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.lawClerkCertificationsFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="paralegalCertificationsFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.paralegalCertificationsFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.paralegalCertificationsFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="articlingStudentCertificationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.articlingStudentCertificationFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.articlingStudentCertificationFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="certificationbyLPPCandidateFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.certificationbyLPPCandidateFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.certificationbyLPPCandidateFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="certificationbyNCACandidateFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.certificationbyNCACandidateFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.certificationbyNCACandidateFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="associateMembershipReasonFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.associateMembershipReasonFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.associateMembershipReasonFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				
				<span class="professionalLicensesWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">Professional License Information</div>
						<div class="tsAppSectionContentContainer fieldSetContainer">
							<p>#variables.strPageFields.ProfessionalLicContent#</p>									
							<table cellpadding="3" border="0" cellspacing="0" >									
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText" >Professional License</td>
									<td class="tsAppBodyText">&nbsp;</td>
									<td class="tsAppBodyText">
										<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple" data-function="professionalLicensesmultiSelectReload">
											<cfloop query="local.qryOrgPlTypes">	
												<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif> <cfif local.qryOrgPlTypes.PLName EQ "Pennsylvania">required="true"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
											</cfloop>
										</select>
									</td>
								</tr>
								<tr class="top">
									<td class="tsAppBodyText" width="10"></td>
									<td class="tsAppBodyText"></td>
									<td class="tsAppBodyText"></td>
								</tr>
							</table>
							<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
								<tr>
									<td>
										<div class="row-fluid hide" id="state_table">
											<div class="span3 proLicenseLabel">
												<b>Type</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseNumberLabel#</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseDateLabel#</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseStatusLabel#</b>
											</div>
										</div>
										<span id="selectedLicense">
										</span>
									</td>
								</tr>					
							</table>
						</div>
					</div>
				</span>

				#application.objWebEditor.showEditorHeadScripts()#
				
				<script language="javascript">
					$(document).ready(function(){						
						<cfloop query="local.qryOrgAddressTypes">                       
							<cfif ListFindNoCase('Address,Other Address',local.qryOrgAddressTypes.addressType)>
								function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _CF_this = document.forms['#variables.formName#'];
									var _address = _this.val();
									if(_address.length > 0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}

										if($('*[id^=mat_]').val() == ''){
											if($('*[id^=mat_] option:eq(1)').val() != undefined)
												$('*[id^=mat_]').val($('*[id^=mat_] option:eq(1)').val());
										}
										var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
										if($('*[id^=mat_]').val() == ''){
											if($('*[id^=mat_] option:eq(1)').val() != undefined)
												$('*[id^=mat_]').val($('*[id^=mat_] option:eq(1)').val());
										}
									}
								}

								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));

								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});

								$(document).on('change','.addressFieldSetHolder ##ma_#local.qryOrgAddressTypes.addresstypeid#_address1',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
							<cfelse>
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							</cfif>
							
						</cfloop>
					});

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>		

        <cfset local.response = "failure">

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step1")>
            <cfset structDelete(variables.formFields, "step1")>
        </cfif>			

        <cfset variables.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>	
        <cfset local.strData.one = checkSessionExist("step1")/>

		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>
				
		<cfif variables.isLoggedIn OR variables.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
            <cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
            <cfset local.strData.one.memberID = 0>
			<cfif structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0>
				<cfset local.membershipDroppedDate = getCustomFieldValueForMember(variables.orgID, variables.useMID, "795B71CE-284B-4630-A208-6281DF7F86A4")>
				<cfset local.strData.one["md_" & local.membershipDroppedDate.id] = DateFormat(local.membershipDroppedDate.value, "mm/dd/yyyy")>
			</cfif>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>

        <cfsavecontent variable="local.headCode">
			<cfoutput>					
				<script type="text/javascript">
					$(document).ready(function(){
						if (typeof arrUploaders !== 'undefined') {
							$.each(arrUploaders, function() {
								this.uploader.bind('BeforeUpload', function(uploader, file) {
									uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
								});
								this.uploader.start();
							});
						}
					});
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfset variables.formFields.step0.origMemberID = variables.useMID/>
        <cfset variables.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>
            <cfset variables.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
                                                subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
                                                enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
            
			<cfset application.mcCacheManager.sessionSetValue("captchaEntered", 1)>	
			<cfset local.response = "success">
		</cfif>	
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset variables.useMID = local.strData.zero.memberID/>

        <cfif StructIsEmpty(local.strData.one)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.thisAddonSubscriptionID = "">
		<cfset local.thisAddonSubscriptionIDArray = []>
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
			<cfset arrayAppend(local.thisAddonSubscriptionIDArray, local.thisAddonSubscriptionID)>
		</cfloop>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
        <cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>			

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style type="text/css">
					div.alert-danger{padding: 10px !important;}
                </style>

                <script type="text/javascript">
					
                    #local.result.jsAddonValidation#
				    function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = "Select Membership.";
						}
						
						$('div [data-setname="Voluntary Sections"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
							var minlp = $(this).data("minallowed");
							if($("##"+$(this).attr("id").split('_')[0]+":checked").length){
								if($("input:checkbox:checked",this).length < minlp){
									arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $(this).data("setname") +".";
								}
							}
						});

						$('div [data-setname="Voluntary Sections"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
							var maxlp = $(this).data("maxallowed");
							if($("##"+$(this).attr("id").split('_')[0]+":checked").length){
								if($("input:checkbox:checked",this).length > maxlp){
									arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $(this).data("setname") +".";
								}							
							}
						});
												
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
						$(".subRateOverrideBox").parent().siblings('input.subRateCheckbox[type=radio]').each(function(){
							if($(this).is(':checked') == false){
								$(this).parent().find('.subRateOverrideBox').remove();
							}
						});

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					
					function rateReset(){
						$('div [data-setname="Voluntary Sections"] input:checkbox').each(function(){							
							if($(this).is(":checked")){
								$("##"+$(this).attr("name").split('_')[0]+"_addons").show();
							}else{
								$("##"+$(this).attr("name").split('_')[0]+"_addons").hide();
								$("##"+$(this).attr('id')+"_addons").find('input:checkbox').each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
								$("##"+$(this).attr('id')+"_addons").find('input:radio').each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
								$("[name="+$(this).attr('id')+"_rate]").each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
							}
						});
					}
					
					$(document).ready(function(){
						
						$("##sub"+"#local.subscriptionID#").parent().parent().find('legend').after('#variables.strPageFields.MembershipDuesSummary#')
						<cfloop array="#local.thisAddonSubscriptionIDArray#" index="local.subscriptionID">
							$("##sub#local.subscriptionID#").css('display', 'none');
						</cfloop>
						
						$('div [data-setname="Voluntary Sections"] .subRateCheckbox').change(function(){
							if($(this).is(":checked")){
								if($(this).closest('.subAvailableRates').parent().find('.subCheckbox:checked').length == 0){
									$(this).closest('.subAvailableRates').parent().find('.subCheckbox').trigger('click');
								}
							}
						});

						$('div [data-setname="Voluntary Sections"] input:checkbox').change(function(){
							rateReset();
						});

						rateReset();
					});
					
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<h1>#variables.strPageFields.FormTitle#</h1>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>	
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step2")>
            <cfset structDelete(variables.formFields, "step2")>
        </cfif>			
        <cfset variables.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

        <cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>

        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>
		</cfif>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>

                <script type="text/javascript">	
                    function validatePaymentForm(isPaymentRequired) {

						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
                    $('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);

						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
                    <cfinput type="hidden" name="fa" id="fa" value="processPayment">
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
                    <cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <h1>#variables.strPageFields.FormTitle#</h1>
                            </div>
                        </div>
                    </cfif>	

                    <cfif len(variables.strPageFields.Step3TopContent)>
                        <div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
                    </cfif>

                    <div class="tsAppSectionHeading">Membership Selections Confirmation</div>
                    <div class="tsAppSectionContentContainer">						
                        #local.strResult.formContent#
                    </div>
                    <br/>

                    <div class="tsAppSectionHeading">Total Price</div>
                    <div class="tsAppSectionContentContainer">						
                        Amount Due: #dollarFormat(local.strResult.totalFullPrice)#<cfif local.strResult.strTax.totalTaxAmt gt 0> + #dollarFormat(local.strResult.strTax.totalTaxAmt)# tax</cfif>
                    </div>

                    <br/><br/>

                    <cfif local.paymentRequired>
                        #local.strReturn.paymentHTML#
                    <cfelse>
                        <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                        <button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
                    </cfif>

                </cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processPayment" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step3")>
            <cfset structDelete(variables.formFields, "step3")>
        </cfif>			
        <cfset variables.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

        <cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>

        <cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>

        <cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>


		<cfset local.strData.one.memberID = variables.useMID>
        <cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
        <cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.strData.two.memberID = variables.useMID>

		<cfset application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>
	
        <cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)>

		<cfif structKeyExists(local.strData.three, 'mccf_payMethID') and structKeyExists(local.strData.three, 'p_#local.strData.three.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.three['p_#local.strData.three.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>
		
		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.strResult.totalFullPrice gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

        <cfset local.response = "success">

        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

        <cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
			</cfif>
		</cfif>

        <cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
        <cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

		<cfset local.contactTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactTypeFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.yourInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.yourInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.schoolInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.schoolInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.addressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.otherAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.otherAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.additionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.paralegalProfessionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.paralegalProfessionalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.practiceCompositionInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.practiceCompositionInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.areasofPracticeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.areasofPracticeFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.certificationbySolicitorFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.certificationbySolicitorFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.lawClerkCertificationsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.lawClerkCertificationsFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.paralegalCertificationsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.paralegalCertificationsFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.articlingStudentCertificationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.articlingStudentCertificationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.certificationbyLPPCandidateFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.certificationbyLPPCandidateFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.certificationbyNCACandidateFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.certificationbyNCACandidateFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.associateMembershipReasonFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.associateMembershipReasonFieldSetUID, mode="confirmation", strData=local.strData.one)>

		<cfset local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		
        <cfsavecontent variable="local.invoice">
            <cfoutput>
				#local.contactTypeFieldSet.fieldSetContent#
				#local.yourInformationFieldSet.fieldSetContent#
				#local.schoolInformationFieldSet.fieldSetContent#
				<cfif local.memberTypeSelected EQ 'Lawyer'>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
						</tr>				
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
												<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
													<thead>
														<tr valign="top">
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseNumberLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseDateLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseStatusLabel#</th>
														</tr>
													</thead>
													<tbody>
													<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
														<tr id="tr_state_#local.key#">
															<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
														</tr>
													</cfloop>
													</tbody>
												</table>
											</cfif>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					<br>
				</cfif>
				#local.addressFieldSet.fieldSetContent#
				#local.otherAddressFieldSet.fieldSetContent#						
				#local.addressPreferencesFieldSet.fieldSetContent#
				#local.additionalInformationFieldSet.fieldSetContent#
				
				<cfif local.memberTypeSelected EQ 'Paralegal'>
					#local.paralegalProfessionalInformationFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Lawyer'>
					#local.practiceCompositionInformationFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Lawyer' OR local.memberTypeSelected EQ 'Law Clerk' OR local.memberTypeSelected EQ 'Paralegal' OR local.memberTypeSelected EQ 'Articling Student' OR local.memberTypeSelected EQ 'LPP Candidate Training Program' OR local.memberTypeSelected EQ 'LPP Candidate Workplace Training' OR local.memberTypeSelected EQ 'NCA Candidate'>
					#local.areasofPracticeFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Lawyer'>
					#local.certificationbySolicitorFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Law Clerk'>
					#local.lawClerkCertificationsFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Paralegal'>
					#local.paralegalCertificationsFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Articling Student'>
					#local.articlingStudentCertificationFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'LPP Candidate Training Program' OR local.memberTypeSelected EQ 'LPP Candidate Workplace Training'>
					#local.certificationbyLPPCandidateFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'NCA Candidate'>
					#local.certificationbyNCACandidateFieldSet.fieldSetContent#
				</cfif>
				<cfif local.memberTypeSelected EQ 'Associate Member*'>
					#local.associateMembershipReasonFieldSet.fieldSetContent#
				</cfif>
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            #local.strResult.formContent#
                            <br/>
                        </div>
                        <br/>
                        <strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#<cfif local.strResult.strTax.totalTaxAmt gt 0> + #dollarFormat(local.strResult.strTax.totalTaxAmt)# tax</cfif>
                        <br/>
                        </td>
                    </tr>
                </table>

                <cfif local.paymentRequired>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(local.strData.three,"mccf_payMeth")>
                                <table cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif>

            </cfoutput>
        </cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>

                <p>Here are the details of your application:</p>	

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

        <cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>
		
        <cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>												  
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>				   
		</cfif>

		<!--- email to association --->
        <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
        <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

        <cfsavecontent variable="local.specialText">
			<cfoutput>

            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>
                 #local.specialText#
                <p>Here are the details of your application:</p>

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

        <cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
                    <cfif len(variables.strPageFields.ConfirmationContent)>
                        <p>#variables.strPageFields.ConfirmationContent#</p>
                    </cfif>
                    <p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>

        <!--- relocate to message page --->
        <cfset session.invoice = local.invoice />
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="getCustomFieldValueForMember" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="UID" type="string" required="true">
		
		<cfquery name="local.qryGetCustomFieldValue" datasource="#application.dsn.membercentral.dsn#">
			SELECT mdcv.columnvalueDate as value, mdc.columnID as id
			FROM ams_memberDataColumns mdc
			INNER JOIN ams_memberdatacolumnvalues mdcv ON mdcv.columnID = mdc.columnID AND mdc.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.UID#">
			INNER JOIN ams_memberdata amd ON amd.valueID = mdcv.valueID AND amd.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">		
        </cfquery>
		
		<cfreturn local.qryGetCustomFieldValue>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
                </cfif>
                <div class="tsAppSectionContentContainer">
                    <p>Here are the details of your application:</p>						
                    #session.invoice#
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="hasSub" access="private" output="false" returntype="string">
        <cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

        <cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
        <cfset local.isExist = false/>
        <cfset local.strData = {}>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, arguments.step)>
            <cfset local.strData = variables.formFields[arguments.step]/>
        </cfif>			

		<cfreturn local.strData>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						<cflocation url="#local.redirectLink#" addtoken="false">
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
                     <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>