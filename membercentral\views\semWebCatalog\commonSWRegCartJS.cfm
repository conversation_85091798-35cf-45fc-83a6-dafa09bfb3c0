<cfsavecontent variable="local.regCartJS">
	<cfoutput>
	<script type="text/javascript">
		function hideAlert() { $('##swRegPmtErr').html('').hide(); };
		function showAlert(msg) { $('##swRegPmtErr').html(msg).attr('class','alert').show(); };

		function registerOthers(item) {
			window.location.href = '#local.strData.semweb.mainurl#&item=' + item + '&panel=reg';
		}
		function editReg(item,rk) {
			$('##edit_swrk_'+rk).html('<i class="icon-spin icon-spinner"></i>');
			window.location.href = '#local.strData.semweb.mainurl#&item=' + item + '&panel=reg&regaction=editRegCartItem&swrk=' + rk;
		}
		function removeReg(rk) {
			var msg = 'Are you sure you want to remove this pending registration?';
			if (confirm(msg)) {
				$('##del_swrk_'+rk).html('<i class="icon-spin icon-spinner"></i>');
				window.location.href = '#local.strData.semweb.mainurl#&panel=reg&regaction=remreg&swrk=' + rk;
			}
		}
		function selectPayment(pid) {
			hideAlert();
			$('##profileid').val(pid);
		}
		function selectPaymentAndSubmit(ppid,append,orgcode) {
			/* prevent double submission */
			if ($('##frmPurchaseSWReg').data('swregformsubmitted') === true) return false;

			selectPayment(ppid);
			if(append) $('<input>').attr({ type: 'hidden', name: 'p_'+orgcode+'_mppid' }).val(ppid).appendTo('form##frmPurchaseSWReg');
			$('##frmPurchaseSWReg').submit();
		}
		function swRegMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
						var iframeWin = document.querySelector('iframe[name=iframeManageForm'+event.data.profileid+']').contentWindow;
						var message = { success:true, 
										messagetype:"MCFrontEndPaymentEvent", 
										paymentbuttonname:$('##divBtnWrapper'+event.data.profileid+' button[type="submit"]').text(), 
										profileid:event.data.profileid };
						iframeWin.postMessage(message, event.origin);
						break;

					case "tspaymentformloadevent":
						var iframeWin = window.frames.iframeBuyNow;
						var appBtn = $('button.appContinueBtn');
						var message = { success:true, messagetype:"TSFrontEndPaymentEvent", paymentbuttonname:appBtn.text() };
						iframeWin.postMessage(message, event.origin);
						appBtn.hide();
						break;

					case "mcgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.payprofileid) {
							showSWRegPaymentProcessing(event.data.profileid);
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmPurchaseSWReg');
							setSWRegPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchaseSWReg').submit();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							showSWRegPaymentProcessing(event.data.profileid);
							setSWRegPaymentTokenData(event.data.tokendata,event.data.profileid);
							setSWRegPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchaseSWReg').submit();
						}
						break;

					case "tsgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.tspayprofileid) {
							$('##CIMTable')
								.html('<i class="icon icon-spinner"></i> Please Wait...')
								.css({'height':'75px', 'padding':'5px'});

							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.merchantorgcode+'_mppid' }).val(event.data.tspayprofileid).appendTo('form##frmPurchaseSWReg');

							selectPayment(event.data.profileid);
							$('button.appContinueBtn').hide();
							$('##frmPurchaseSWReg').submit();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							$('##CIMTable')
								.html('<i class="icon icon-spinner"></i> Please Wait...')
								.css({'height':'75px', 'padding':'5px'});
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.merchantorgcode+'_tokenData' }).val(JSON.stringify(event.data.tokendata)).appendTo('form##frmPurchaseSWReg');
							selectPayment(event.data.profileid);
							$('button.appContinueBtn').hide();
							$('##frmPurchaseSWReg').submit();
						}
						break;

				};

			} else {
				return false;
			}
		}
		function showSWRegPaymentProcessing(pid) {
			$('##divPaymentTable' + pid)
				.html('<i class="icon icon-spinner"></i> Please Wait...')
				.css({'height':'75px', 'padding':'5px'});
		}
		function setSWRegPaymentProcessingFeesField(obj) {
			if (typeof obj.enableprocessingfee != "undefined") {
				let processFeeDonationElement = $('##processFeeDonation'+obj.profileid);
				if (processFeeDonationElement.length) {
					processFeeDonationElement.val(obj.enableprocessingfee);
				} else {
					$('<input>').attr({ type: 'hidden', name: 'processFeeDonation'+obj.profileid, id: 'processFeeDonation'+obj.profileid }).val(obj.enableprocessingfee).appendTo('form##frmPurchaseSWReg');
				}
			}
		}
		function setSWRegPaymentTokenData(tokendata,profileid) {
			let tokenDataElement = $('##p_'+profileid+'_tokenData');
			if (tokenDataElement.length) {
				tokenDataElement.val(JSON.stringify(tokendata));
			} else {
				$('<input>').attr({ type: 'hidden', name: 'p_'+profileid+'_tokenData', id: 'p_'+profileid+'_tokenData' }).val(JSON.stringify(tokendata)).appendTo('form##frmPurchaseSWReg');
			}
		}
		function validateCouponCodeOnEnterKey(event){
			/* Number 13 is the "Enter" key on the keyboard */
			if (event.keyCode === 13) {
				/* Cancel the default action, if needed */
				event.preventDefault();
				validateCouponCode();
			}
		}
		function validateCouponCode() {
			var validateResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.isvalidcoupon.toString().toLowerCase() == 'true') {
						self.location.href = '#local.strData.semweb.mainurl#&panel=showCart';
					} else {
						$('##couponCodeResponse').html(r.couponresponse).show();
						$('##btnApplyCouponCode').prop('disabled',false).text('Apply');
					}
					
				} else {
					$('##btnApplyCouponCode').prop('disabled',false).text('Apply');
					$('##couponCode').val('');

					if (r.success && r.success.toLowerCase() == 'false' && r.couponresponse) {
						$('##couponCodeResponse').html(r.couponresponse).show();
					} else {
						$('##couponCodeResponse').html('Unable to apply coupon code. Try again.').show();
					}
				}
			};

			var couponCode = $('##couponCode').val().trim();

			if (couponCode.length) {
				$('##btnApplyCouponCode').prop('disabled',true).text('Applying...');
				$('##couponCodeResponse').html('').hide();

				var objParams = { couponCode:couponCode };
				TS_AJX('SWREG','validateCouponCode',objParams,validateResult,validateResult,10000,validateResult);
			} else {
				validateResult({ success:'false', couponresponse:'Invalid Promo Code' });
			}
		}
		function removeAppliedCoupon() {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					self.location.href = '#local.strData.semweb.mainurl#&panel=showCart';
				} else {
					$('.btnRemoveCoupon').prop('disabled',false).text('Remove Promo Code');
					alert('Unable to remove applied coupon. Try again.');
				}
			};

			$('.btnRemoveCoupon').prop('disabled',true).text('Removing...');
			TS_AJX('SWREG','removeAppliedCoupon',{},removeResult,removeResult,10000,removeResult);
		}
		
		$(function() {
			<cfif attributes.event.valueExists('perr')>
				showAlert('There was a problem processing the payment for this purchase.<br/>#JSStringFormat(HTMLEditFormat(attributes.event.getValue("perr")))#');
			</cfif>

			if (window.addEventListener) {
				window.addEventListener("message", swRegMessageHandler, false);
			} else if (window.attachEvent) {
				window.attachEvent("message", swRegMessageHandler);
			}

			<cfif attributes.event.getValue('viewDirectory','default') EQ 'default'>
				initCollapsibleDivSet('paymentTypePills');
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.regCartJS)#">