<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa,sid";
			variables.baselink = "/?#getBaseQueryString(false)#";

			variables.orgID = arguments.event.getValue('mc_siteinfo.orgID');
			variables.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			variables.orgCode = arguments.event.getValue('mc_siteinfo.orgCode');
			variables.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=variables.orgID);
			variables.orgDefaultSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=variables.orgDefaultSiteID);

			if (arguments.event.valueExists('ovMemberID')) {
				variables.memberIDToUse = int(val(arguments.event.getValue('ovMemberID')));
			} else {
				variables.memberIDToUse = session.cfcUser.memberData.memberID;
			}

			variables.StudentDuesSubUID = '15ae5d59-b690-4020-ad5a-acebec0d85ef';
			variables.indyAttorneysNetworkUID = "0102897C-86E8-43A8-B5DB-A26CCB8EB5E0";
			variables.barFoundationDonationUID = "bc3fc8ec-4196-4769-8a06-604a01be1b1d";

			variables.qryRateClasses = queryNew('');
			local.arrUIDs = [
				'e61bb310-e88d-4138-9bf7-acd7b5c94176',
				'60a13029-50f0-4e6c-aa31-46ec8fd4c598',
				'5aae1081-767f-4185-97df-4885970865f8',
				'd5d64575-bfd2-4368-8722-ba782649118b',
				'a6845f25-23cf-4768-a394-c93e6c22ff1d',
				'fbdb61a1-ff3f-46b9-aded-56866938beff',
				'59ca38e0-48ef-4e5f-ad93-c328c92f3eae',
				'a1214f10-36ff-4bc0-a883-36ff0eacffc6',
				'62e0131f-8229-4bc2-b229-eb17ba36e60a',
				'f34efcd3-de1a-40dc-89b6-3009ad3b12fc',
				'61b9cba0-3b23-44ae-93af-4d687c5579a7',
				'021e5ce8-3245-41eb-af1f-c4305ac194fc',
				'129b7c8d-c8b3-4abb-a53e-1ac4d00fae36',
				'e69a9b9d-df3e-41fa-a821-db3edcd4ed53',
				'c98618ad-e96e-4420-90eb-69b8a45b9bef',
				'9aeb8931-5e8d-4da5-8930-d2c5d7651a3d',
				'791deb19-0a76-42a7-a2b9-ef8ed3efafb7',
				'45f3b8f7-5e16-4ccf-854e-acaa43cfcf41',
				'bbd1b62c-7053-40b7-8dbb-b02ff03aa361',
				'1d86b6cb-b6e7-4ca5-8121-fc752f20057f',
				'ed6b65e1-5c54-4b55-a30e-9b92db4c656b'
				];
			local.arrClasses = [
				'Basic',
				'Basic',
				'Basic',
				'Basic',
				'Basic',
				'Basic',
				'Sustaining',
				'Sustaining',
				'Sustaining',
				'Sustaining',
				'Sustaining',
				'Sustaining',
				'Plus',
				'Plus',
				'Plus',
				'Plus',
				'Plus',
				'Plus',
				'Plus',
				'Plus',
				'Plus'
				];
			local.arrUpgradeRates = [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				1,
				1,
				0,
				0,
				0,
				0,
				1,
				1,
				1,
				1,
				0,
				0
				];
			queryAddColumn(variables.qryRateClasses,'UID','varchar',local.arrUIDs);
			queryAddColumn(variables.qryRateClasses,'Class','varchar',local.arrClasses);
			queryAddColumn(variables.qryRateClasses,'UpgradeRate','bit',local.arrUpgradeRates);

			/* event info for Plus */
			local.qryCalendars = application.objCustomPageUtils.ev_getCalendars(siteID=variables.siteID, limitToCalendar='IndyBar Calendar of Events');
			local.qryCategories = application.objCustomPageUtils.ev_getCategoriesOnCalendar(siteID=variables.siteID, calendarID=local.qryCalendars.calendarID, limitToCategory='Bar Review Prep');
			variables.nextEvents = application.objCustomPageUtils.ev_getUpcomingEvents(siteID=variables.siteID, limitTop=20, limitCategoryIDList=valuelist(local.qryCategories.categoryID));

			variables.formName = "frmUpgrade";
			variables.formNameDisplay = "Upgrade Your Law Student Membership";

			// default and defined custom page custom fields
			local.arrCustomFields = [];
			local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content above current membership information", value="PUT CONTENT HERE" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step1MiddleContent", type="CONTENTOBJ", desc="Content above membership type selection", value="PUT CONTENT HERE" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who to send staff confirmations to", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who to send member confirmations from", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PayProfileCode", type="STRING", desc="pay profile code for credit cards", value="INDYCC" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=variables.siteID, siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

			variables.payProfileCode = variables.strPageFields.PayProfileCode;
			variables.payProfileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID, profileCode=variables.payProfileCode);

			/* Email confirmation settings */
			variables.strEMailSettings_staff = {
				from="<EMAIL>", 
				to=variables.strPageFields.StaffConfirmationTo, 
				subject=variables.formNameDisplay,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			};
			variables.strEMailSettings_member = {
				from=variables.strPageFields.MemberConfirmationFrom, 
				to=arguments.event.getTrimValue('email',''), 
				subject=variables.formNameDisplay,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			};
		</cfscript>
	</cffunction>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			init(event=arguments.Event);
			local.returnHTML = "";
			local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init();

			local.strMsgCodes = structNew();
			local.strMsgCodes['C7A0CE9D-A89B-D2E0-6706DA80FAD703EA'] = { err=1, msg="<h2>Error Upgrading Membership</h2><p class='tsAppBodyText'>We've found an issue upgrading a Law Student Membership. If you have any questions, please contact the Member Services Department by calling (317) 269-2000 or via email at <a href='mailto:<EMAIL>'><EMAIL></a></p>" };
			local.strMsgCodes['C7A52C26-B030-8B38-03002A5FB1023AE2'] = { err=1, msg="<h2>Error Upgrading Membership</h2><p class='tsAppBodyText'>Our records indicate you already have the highest upgrade level of Law Student membership. If you have any questions, please contact the Member Services Department by calling (317) 269-2000 or via email at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>" };
			local.strMsgCodes['A616D408-09E6-2F7A-A9928ECD29278689'] = { err=1, msg="<h2>Error Upgrading Membership</h2><p class='tsAppBodyText'>Our records indicate you already have an accepted membership for the next membership year. If you have any questions, please contact the Member Services Department by calling (317) 269-2000 or via email at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>" };
		</cfscript>

		<cfswitch expression="#arguments.event.getValue('fa','showStep1')#">
			<cfcase value="showError">
				<cfif arguments.event.valueExists('sid') and StructKeyExists(local.strMsgCodes,arguments.event.getTrimValue('sid',''))>
					<cfset local.theMessage = local.strMsgCodes[arguments.event.getTrimValue('sid','')].msg>
				<cfelse>
					<cfset local.theMessage = "We've run into issues recording your submission. Please try again.">
				</cfif>
				<cfset local.returnHTML = local.theMessage>
			</cfcase>

			<cfcase value="getEventsRates">
				<cfset local.returnHTML = showEventRates(eventID=arguments.event.getTrimValue('eventID',0))>
			</cfcase>

			<cfcase value="processStep1">
				<cfset local.returnHTML = processStep1(rateUID=arguments.event.getTrimValue('rateUID',''), gradDate=arguments.event.getTrimValue('gradDate',''))>
			</cfcase>

			<cfcase value="processStep2">
				<cfset local.payOptionText = "">
				<cfset local.payOption = "">
				<cfset local.eventRateID = arguments.event.getTrimValue('eventSelected',0)>
				<cfset local.selRadio = arguments.event.getValue('bar_amount_#local.eventRateID#')>

				<cfswitch expression="#local.selRadio#">
					<cfcase value="other">
						<cfset local.payOptionText = arguments.event.getTrimValue('other_amt_text_#local.eventRateID#')>
						<cfset local.payOption = arguments.event.getTrimValue('other_txt_amt_#local.eventRateID#',0)>
					</cfcase>
					<cfcase value="25">
						<cfset local.payOptionText = arguments.event.getTrimValue('min_amt_text_#local.eventRateID#')>
						<cfset local.payOption = local.selRadio>
					</cfcase>
					<cfdefaultcase>
						<cfset local.payOptionText = arguments.event.getTrimValue('bar_amount_text_#local.eventRateID#')>
						<cfset local.payOption = local.selRadio>
					</cfdefaultcase>
				</cfswitch>

				<cfset local.returnHTML = processStep2(rateUID=arguments.event.getTrimValue('rateUID',''), gradDate=arguments.event.getTrimValue('gradDate',''),
													   eventID=arguments.event.getTrimValue('cal_event',0), eventRateID=local.eventRateID,
													   eventRate=arguments.event.getTrimValue('study_option',0), payOption=local.payoption, payOptionText=local.payOptionText)>
			</cfcase>

			<cfcase value="processStep3">
				<cfset local.returnHTML = processStep3(rateUID=arguments.event.getTrimValue('rateUID',''), gradDate=arguments.event.getTrimValue('gradDate',''),
													   eventID=arguments.event.getTrimValue('eventID',0), eventRateID=arguments.event.getTrimValue('eventRateID',0), 
													   eventRate=arguments.event.getTrimValue('eventRate',0), payOption=arguments.event.getTrimValue('payOption',''), 
													   payOptionText=arguments.event.getTrimValue('payOptionText',''), subsToExpire=arguments.event.getTrimValue('subsToExpire',''), 
													   newSubs=arguments.event.getTrimValue('newSubs',''), 
													   old_indys_attorney=arguments.event.getTrimValue('old_indys_attorney',0), indys_attorney=arguments.event.getTrimValue('indys_attorney',0))>
			</cfcase>

			<cfcase value="processStep4">
				<cfset local.returnHTML = processStep4(event=arguments.event, rateUID=arguments.event.getTrimValue('rateUID',''), gradDate=arguments.event.getTrimValue('gradDate',''),
													   eventID=arguments.event.getTrimValue('eventID',0), eventRateID=arguments.event.getTrimValue('eventRateID',0),
													   eventRate=arguments.event.getTrimValue('eventRate',0), payOption=arguments.event.getTrimValue('payOption',''), 
													   payOptionText=arguments.event.getTrimValue('payOptionText',''), subsToExpire=arguments.event.getTrimValue('subsToExpire',''),
													   newSubs=arguments.event.getTrimValue('newSubs',''), indys_attorney=arguments.event.getTrimValue('indys_attorney',0), 
													   payMethod=arguments.event.getTrimValue('payMeth',''), mppid=arguments.event.getTrimValue('p_#variables.payProfileID#_mppid',0))>
			</cfcase>

			<cfdefaultcase>
				<cfset local.returnHTML = showStep1()>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>

	<cffunction name="showStep1" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset var qryEligibleRates = "">

		<!--- get active/accepted student subscription --->
		<cfset local.qryActiveStudentSub = getMembershipByStatus(statusCode='A')>
		<cfset local.qryAcceptedStudentSub = getMembershipByStatus(statusCode='P')>

		<!--- If the user does not have an active Student Dues membership, show message. --->
		<!--- If the user has an accepted Student Dues membership, show message. --->
		<cfif NOT local.qryActiveStudentSub.recordCount>
			<cflocation url="#variables.baselink#&fa=showError&sid=C7A0CE9D-A89B-D2E0-6706DA80FAD703EA" addtoken="no">
		<cfelseif local.qryAcceptedStudentSub.recordCount>
			<cflocation url="#variables.baselink#&fa=showError&sid=A616D408-09E6-2F7A-A9928ECD29278689" addtoken="no">
		</cfif>

		<!--- get class of existing student membership based on their rate --->
		<cfquery name="local.qryStudentClass" dbtype="query">
			SELECT Class
			FROM variables.qryRateClasses
			WHERE upper(UID) = '#UCASE(local.qryActiveStudentSub.rateUID)#'
		</cfquery>

		<!--- If the user is already at the top tier and no upgrade paths are available, show message. --->
		<cfif local.qryStudentClass.Class eq 'Plus'>
			<cflocation url="#variables.baselink#&fa=showError&sid=C7A52C26-B030-8B38-03002A5FB1023AE2" addtoken="no">
		</cfif>

		<!--- get eligible rates for upgrading --->
		<cfset qryEligibleRates = application.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.memberIDToUse, subscriptionUID=variables.StudentDuesSubUID, isRenewal=false)>

		<!--- Remove the rate the user already belongs to and keep only the eligible upgrade paths. --->
		<cfquery name="qryEligibleRates" dbtype="query">
			SELECT qryEligibleRates.*
			FROM qryEligibleRates, variables.qryRateClasses
			WHERE UPPER(rateUID) <> '#UCASE(local.qryActiveStudentSub.rateUID)#'
			and rateUID = UID
			and UpgradeRate = 1
		</cfquery>

		<!--- For sustaining users, also then remove the basic rates since they cannot downgrade. --->
		<!--- Only check sustaining users since basic users have nowhere to go but up, and plus users would have already been redirected. --->
		<cfif local.qryStudentClass.Class eq 'Sustaining'>
			<cfquery name="qryEligibleRates" dbtype="query">
				SELECT qryEligibleRates.*
				FROM qryEligibleRates, variables.qryRateClasses
				WHERE rateUID = UID
				and Class <> 'Basic'
			</cfquery>
		</cfif>

		<!--- get existing grad date --->
		<cfset local.gradDate = application.objMember.getMemberViewData(memberID=variables.memberIDToUse,columnName='Expected Graduation Date',orgID=variables.orgID)>

		<cfsavecontent variable="local.headJS">
			<cfoutput>
			<script language="javascript">
				function _FB_validateForm() {
					hideAlert();
					$('##btnToStep2').attr('disabled',true).addClass('disabled');
					
					var arrReq = new Array();
					if ($('##gradDate').val().length == 0) arrReq[arrReq.length] = 'Select Expected Graduation Date.';

					var membership_type	= document.getElementsByName('rateUID');
					var selectedMembership = getSelectedRadio(membership_type);
					if (selectedMembership == -1) arrReq[arrReq.length] = 'Select Membership Type.';
					
					if (arrReq.length > 0) {
						var msg = 'Please address the following issues with your application:<br/>';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						$('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
						$('##btnToStep2').attr('disabled',false).removeClass('disabled');
						return false;
					}

					$('##submitTxt').show();
					return true;
				}
				
				function hideAlert() { $('##issuemsg').html('').hide(); };
				function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };

				function getSelectedRadio(buttonGroup) {
					if (buttonGroup[0]) {
						for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
					} else { if (buttonGroup.checked) return 0; }
					return -1;
				}

				$(function() { 
					mca_setupDatePickerField('gradDate');
				});
			</script>
			<style type="text/css">
				.alert { background:##4d78716bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
				div.CPSection div.CPSectionTitle { font-size:14pt; font-weight:bold; color:##fff; padding:10px; background:##899461; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }
				div.CPSection div.frmText, div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right;width:30%; }
				div.frmButtons{ padding:12px; border-top:1px solid ##6C793E; border-bottom:1px solid ##6C793E; font-family: Calibri,Arial,Helvetica; }
				div.frmButtons span.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				.frmRow1{ background:##fff; }
				.frmRow2{ background:##e2e4d5; }
				##gradDate { cursor: pointer; background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
				.innerEventBox .row-fluid.CPSection {
					padding: 0px 0px !important;
				}
				.text-left{
					text-align:left !important;
				
				}
				.CMISection .control-group{
					padding:3px 10px 3px 10px;
					margin-bottom:0px;
				}
				
				
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.headJS)#">	

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<h2 class="TitleText">#variables.formNameDisplay#</h2>
			<div id="issuemsg" style="display:none;padding: 10px;" ></div>
			
			
			<cfform class="form-horizontal" name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
			
			<cfinput type="hidden" name="fa" id="fa" value="processStep1">
									

			<div class="row-fluid  BodyText" style="margin-bottom:10px;">
				#variables.strPageFields.step1TopContent#
			</div>

			<div class="row-fluid BodyText CPSection CMISection">
				<div class="row-fluid  CPSectionTitle BB">Current Membership Information</div>
				<div class="control-group">
					<label class="control-label">Name:</label>
					<div class="controls">		
						<label class="control-label text-left" >
							#session.cfcUser.memberData.prefix#
							#session.cfcUser.memberData.firstname#
							#session.cfcUser.memberData.middlename#
							#session.cfcUser.memberData.lastname#
							#session.cfcUser.memberData.suffix#
						</label>						
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">Membership Level:</label>
					<div class="controls">		
						<label class="control-label text-left" >#local.qryStudentClass.Class# Student</label>						
					</div>
				</div>
				<div class="control-group">
					<label class="control-label" for="firstName">Expected Graduation Date: <span class="required">*</span></label>
					<div class="controls text-left">		
						<cfinput class="form-control" type="text" name="gradDate" id="gradDate" value="#dateformat(local.gradDate,"m/d/yyyy")#" autocomplete="off" size="16">						
					</div>
				</div>
													
				
			</div>
			<div class="row-fluid  BodyText" style="margin-bottom:10px;">
				#variables.strPageFields.step1MiddleContent#
			</div>

			<div class="row-fluid CPSection">
				<div class="row-fluid  CPSectionTitle BB">Membership Type</div>
				<div class="row-fluid  frmRow1 frmText" style="padding:10px;">
					<cfloop query="qryEligibleRates">
						<!--- "cant" show rate name here so we have to do this fucked up lookup just for marketing verbiage --->
						<cfquery name="local.qryStudentOptionClass" dbtype="query">
							SELECT Class
							FROM variables.qryRateClasses
							WHERE upper(UID) = '#UCASE(qryEligibleRates.rateUID)#'
						</cfquery>
						<cfparam name="session.Responses.step1.rateUID" default="">

						<div>
							<label for="rateUID_#qryEligibleRates.rateUID#">
								<cfif qryEligibleRates.rateUID eq session.Responses.step1.rateUID>
									<input type="radio" name="rateUID" id="rateUID_#qryEligibleRates.rateUID#" value="#qryEligibleRates.rateUID#" checked>
								<cfelse>
									<input type="radio" name="rateUID" id="rateUID_#qryEligibleRates.rateUID#" value="#qryEligibleRates.rateUID#">
								</cfif>
								&nbsp;
								<cfif local.qryStudentOptionClass.Class eq "Sustaining">
									Sustaining Membership (expires upon graduation) - #dollarFormat(qryEligibleRates.rateAmt)#
								<cfelseif local.qryStudentOptionClass.Class eq "Plus">
									Sustaining Plus Bar Review (includes Sustaining Membership plus reserves your seat for future IndyBar Review course at current rate) - #dollarFormat(qryEligibleRates.rateAmt+25)# includes $25 course deposit
								<cfelse>
									#qryEligibleRates.rateName# - #dollarFormat(qryEligibleRates.rateAmt)#
								</cfif>
							</label>
						</div>
					</cfloop>
				</div>
			</div>																	
			<div class="frmButtons">
				<button type="submit" id="btnToStep2" name="btnToStep2" class="btn btn-default">Continue</button>
				<span id="submitTxt" class="frmText" style="display:none;"><i class="icon-spin icon-spinner"></i> Please wait...</span>
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep1" access="private" output="false" returntype="string">
		<cfargument name="rateUID" type="string" required="true">
		<cfargument name="gradDate" type="string" required="true">

		<cfset var local = structNew()>

		<!--- This session based object stores values entered from the form so that when the user goes back, it can be used to pre-populate the checked and selected fields. --->
		<cfset storeArgumentsInSession(arguments)>

		<!--- was a sustaining or Plus rate selected? --->
		<cfquery name="local.qryStudentClass" dbtype="query">
			SELECT Class
			FROM variables.qryRateClasses
			WHERE upper(UID) = '#UCASE(arguments.rateUID)#'
		</cfquery>

		<!--- if no date or not sustaining or plus then kick back to first page --->
		<cfif NOT len(arguments.gradDate) OR NOT listFindNoCase("Sustaining,Plus",local.qryStudentClass.Class)>
			<cflocation url="#variables.baselink#" addtoken="no">
		</cfif>

		<!--- get existing grad date and Law Student Plus Bar Review --->
		<cfset local.LSPBR = application.objMember.getMemberViewData(memberID=variables.memberIDToUse,columnName='Law Student Plus Bar Review',orgID=variables.orgID)>
		<cfset local.gradDate = dateformat(application.objMember.getMemberViewData(memberID=variables.memberIDToUse,columnName='Expected Graduation Date',orgID=variables.orgID),"m/d/yyyy")>

		<!--- save the grad date and the law student custom field (if changed) which determine rates for event selection --->
		<cfif (local.gradDate neq dateformat(arguments.gradDate,"m/d/yyyy"))
			OR (local.qryStudentClass.Class eq "Plus" and local.LSPBR neq "1")
			OR (local.qryStudentClass.Class eq "Sustaining" and local.LSPBR eq "1")>
			<cfset local.saveMemberData = true>
		<cfelse>
			<cfset local.saveMemberData = false>
		</cfif>

		<cfif local.saveMemberData>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.memberIDToUse)>	

			<cfif local.qryStudentClass.Class eq "Plus" and local.LSPBR neq "1">
				<cfset local.objSaveMember.setCustomField(field='Law Student Plus Bar Review', value=1)>
			<cfelseif local.LSPBR eq "1">
				<cfset local.objSaveMember.setCustomField(field='Law Student Plus Bar Review', value=0)>
			</cfif>

			<cfif local.gradDate neq arguments.gradDate>
				<cfset local.objSaveMember.setCustomField(field='Expected Graduation Date', value=arguments.gradDate)>
			</cfif>
			
			<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			<cfif NOT local.strResult.success>
				<cflocation url="#variables.baselink#" addtoken="no">
			</cfif>
		</cfif>

		<!--- determine the next step --->
		<cfswitch expression="#local.qryStudentClass.Class#">
			<cfcase value="Plus">
				<cfset local.strTemp = { rateUID=arguments.rateUID, gradDate=arguments.gradDate, class="Plus" }>
				<cfset local.returnHTML = showStep2(formvars=local.strTemp)>
			</cfcase>			
			<cfcase value="Sustaining">
				<cfset local.strTemp = { rateUID=arguments.rateUID, gradDate=arguments.gradDate, class="Sustaining" }>
				<cfset local.returnHTML = showStep3(formvars=local.strTemp)>
			</cfcase>			
			<cfdefaultcase>
				<cflocation url="#variables.baselink#" addtoken="no">
			</cfdefaultcase>
		</cfswitch>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showStep2" access="private" output="false" returntype="string">
		<cfargument name="formvars" type="struct" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.headJS">
			<cfoutput>
			<script type="text/javascript">
				function _FB_validateForm() {
					hideAlert();
								
					$('##btnToStep3').attr('disabled',true).addClass('disabled');

					var arrReq = new Array();
					if ($('##cal_event').val() == '') arrReq[arrReq.length] = 'Choose a Course.';

					var study_option = $("input[name='study_option']:checked").length;
					if (study_option==0) arrReq[arrReq.length] = 'Choose a Study option for your course.';

					var checkAmt = [];
					var otherInputs = [];
					$(".amountRadio").each(function(){
						if($(this).is(":checked")){
							var str = $(this).attr("name");

							var array = str.split('_');
							checkAmt.push($(this).val());
							var test = 'min_amt_'+array[2];
							$("##eventSelected").val(array[2]);
							if( $(this).attr("id")== test){
								$("##minSelected").val('yes');
							}else{
								$("##minSelected").val('no');
							}

							if($(this).val()=='other'){
								var otherAmt = $.trim($("##other_txt_amt_"+array[2]).val());
								otherInputs.push("##other_txt_amt_"+array[2]);
								if(otherAmt==''){
									arrReq[arrReq.length] = 'Please Enter Other Amount.';
								} else {
									var amount = otherAmt;
									amount = Number(amount.replace(/[^0-9\.]+/g,""));
									if(amount==0)  {
										arrReq[arrReq.length] = 'Enter a valid Offer Amount. Only positive amounts are allowed.';
									}else{
										var maxAmt = $("##total_amt_"+array[2]).val();
										maxAmt = Number(maxAmt.replace(/[^0-9\.]+/g,""));
										var minAmt = $("##min_amt_"+array[2]).val();
										minAmt = Number(minAmt.replace(/[^0-9\.]+/g,""));
										if(amount>=maxAmt || amount<=minAmt){
											arrReq[arrReq.length] = 'You must pay a minimum amount of $25 to proceed with your course registration.';
										}
									}
								}
							}
						}
					});
					if(checkAmt.length==0) arrReq[arrReq.length] = 'Select which amount you are paying today.';
					if (arrReq.length > 0) {
						var msg = 'Please address the following issues with your application:<br/>';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						$('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
						$('##btnToStep3').attr('disabled',false).removeClass('disabled');
						return false;
					}else{
						$.each(otherInputs, function( index, value ) {
						  	var temp = $(value).val();
						  	temp = Number(temp.replace(/[^0-9\.]+/g,""));
						  	$(value).val(temp);
						})
					}

					$('##submitTxt').show();
					return true;
				}

				function hideAlert() { $('##issuemsg').html('').hide(); };
				function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };

				function getEventRates(obj) {
					if ($.isNumeric($(obj).val())){
						$.ajax({
							'url':'/?event=cms.showResource&resID=#this.siteResourceID#&fa=getEventsRates&mode=stream&ovMemberID=#variables.memberIDToUse#',
							'data':'eventID='+$(obj).val(),
							'type':'POST',
							'success':function(data){
								$("##event_rates_section").html(data).show();
							}
						});
					} else {
						$("##event_rates_section").html('').hide();
					}
				}

				$(document).ready(function(){
					$(".amountRadio").live('click',function(){
						var str 	= $(this).attr("name");
						var array 	= str.split('_');
						if($(this).val()=='other'){
							$("##other_txt_amt_"+array[2]).show();
							$("##other_txt_amt_"+array[2]).val("");
							$("##otherAmtDiv_"+array[2]).show();
						}else{
							$("##other_txt_amt_"+array[2]).hide();
							$("##other_txt_amt_"+array[2]).val("");
							$("##otherAmtDiv_"+array[2]).hide();
						}
					});
					 <cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr, "cal_event")>
						getEventRates($("##cal_event"));
					</cfif>

					$(".otherAmt").live('change',function(event) {	
						var selectionAmt = $(this).val();
						selectionAmt = Number(selectionAmt.replace(/[^0-9\.]+/g,""));
						var totalAmount = selectionAmt;
						totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
						    return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
						});	
						$(this).val("$ " + totalAmount);	
										
					});
				});
			</script>
			<style type="text/css">
				.alert { background:##4d78716bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
				div.CPSection div.CPSectionTitle { font-size:14pt; font-weight:bold; color:##fff; padding:10px; background:##899461; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }
				div.CPSection div.frmText, div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right;width:30%; }
				div.frmButtons{ padding:12px; border-top:1px solid ##6C793E; border-bottom:1px solid ##6C793E; font-family: Calibri,Arial,Helvetica; }
				div.frmButtons span.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				.frmRow1{ background:##fff; }
				.frmRow2{ background:##e2e4d5; }
				##gradDate { cursor: pointer; background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
				
				##cal_event{
					width: 100%;
				}
				.inline.span12{
					 margin-left: 0px !important;
				}
				.innerEventBox .row-fluid {
					padding: 0!important;
				}
				.TitleTextRC{
					padding: 10px!important;
				}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.headJS)#">	

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<h2 class="TitleText">#variables.formNameDisplay#</h2>
			<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
			<cfform class="form-horizontal" name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
			<cfinput type="hidden" name="fa" id="fa" value="processStep2">

			<cfloop collection="#arguments.formvars#" item="local.key">
				<cfinput type="hidden" name="#lcase(local.key)#" id="#lcase(local.key)#" value="#arguments.formvars[local.key]#">
				<cfset session.fieldArr[local.key] = arguments.formvars[local.key]>
			</cfloop>

			<cfset local.minSelected = "no">
			<cfset local.eventSelected = 0>
			<cfif structKeyExists(session,"fieldArr") AND structKeyExists(session.fieldArr,"minSelected")>
				<cfset local.minSelected = session.fieldArr.minSelected>
			</cfif>
			<cfif structKeyExists(session,"fieldArr") AND structKeyExists(session.fieldArr,"eventSelected")>
				<cfset local.eventSelected = session.fieldArr.eventSelected>
			</cfif>
			<cfinput type="hidden" name="minSelected" id="minSelected" value="#local.minSelected#">
			<cfinput type="hidden" name="eventSelected" id="eventSelected" value="#local.eventSelected#">
			<br/>
			<div class="row-fluid BodyText CPSection reviewCourseSection">
				<div class="row-fluid CPSectionTitle BB TitleTextRC" style="padding:10px!important">Register for Bar Review Course:</div>
				<div style="padding:10px;">
					<div class="control-group">
						<label class="control-label" for="cal_event"><span class="required">*</span>Please choose a course</label>
						<div class="controls">
							<select onChange="getEventRates(this)" class="form-control" id="cal_event" name="cal_event">
								<option value="">Please Select</option>
								<cfloop query="variables.nextEvents">
									<option <cfif structKeyExists(session,"fieldArr") AND structKeyExists(session.fieldArr,"cal_event") AND (session.fieldArr.cal_event eq variables.nextEvents.eventID)>selected</cfif> value="#variables.nextEvents.eventID#">#variables.nextEvents.eventtitle#</option>
								</cfloop>
							</select>
						</div>
					</div>
				</div>
				<div id="event_rates_section" class="row-fluid" style="display:none;">
				</div>
			</div>
			<div class="frmButtons">
				<a id="gotoStep1" class="btn" href="#variables.baselink#" style="float:right;">Back</a>
				<button type="submit" id="btnToStep3" name="btnToStep3" class="btn">Continue</button>
				<span id="submitTxt" class="frmText" style="display:none;"><i class="icon-spin icon-spinner"></i> Please wait...</span>
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showEventRates" access="private" output="false" returntype="string">
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.eventRates = application.objCustomPageUtils.ev_getRatesForEvent(
			eventID=arguments.eventID,
			memberID=variables.memberIDToUse,
			showAllRates=0
		)>

		<cfif local.eventRates.recordCount>
			<cfsavecontent variable="local.headCode">
				<cfoutput>
				<script>
					$("document").ready(function(){
						$("input[name='study_option']").click(function(){
							$(".evtBox").hide();
							$(".otherAmt").hide();
							
							$(".evtBox input[type='radio']").attr("checked",false);
							if($(this).is(":checked")){
								var box = $(this).attr('data-value');
								$("."+box).show();
							}
						});
					});
				</script>
				<style type="text/css">
					div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
					div.CPSection div.CPSectionTitle { font-size:14pt;  font-weight:bold; color:##fff; padding:10px; background:##899461; font-family: Calibri,Arial,Helvetica;}
					div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }
					div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
					div.CPSection span.frmText span.block { display:block; }
					div.CPSection span.frmText span.b { font-weight:bold; }
					div.CPSection td.r { text-align:right;width:30%; }
					.inline{padding: 0 10px;}
					.inline label{display: inline;}
					.inline input{margin-right: 5px;}
					.CPSectionTitle {font-size: 25px;}
					.CPSectionTitle.BB{margin-bottom:0px !important }
					.studyOptionWrap{
						padding: 0px 10px 0px 10px !important;
					}
				</style>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headCode#">

			<cfsavecontent variable="local.returnHTML">
				<cfoutput>
				<div class="control-group studyOptionWrap">
					<label class="control-label" for="">Choose a Study Option:</label>
					<div class="controls">
						<cfloop query="local.eventRates">
							<div>
								<input name="study_option" <cfif structKeyExists(session,"fieldArr") AND structKeyExists(session.fieldArr,"eventSelected") AND (session.fieldArr.eventSelected eq "#local.eventRates.rateID#")>checked=checked</cfif> id="box_#local.eventRates.rateID#" type="radio" value="#local.eventRates.rate#" data-value="box_#local.eventRates.rateID#">
								<label style="display:inline;" for="box_#local.eventRates.rateID#">#local.eventRates.rateName#</label> 
							</div>
						</cfloop>
					</div>
					<div class="row-fluid c frmText">
						<br/>The Study Option you select now will serve as a placeholder only and can be changed later.
					</div>
				</div>
				<cfloop query="local.eventRates">
					<div class="row-fluid frmRow1">
						<cfset local.breakLoop = 0 />
						 <cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr, "eventSelected") AND (session.fieldArr.eventSelected eq "#local.eventRates.rateID#")>
							<cfset local.showBox = "display:block">
							<cfset local.breakLoop = 1 />
						 <cfelse>
							<cfset local.showBox = "display:none">
						 </cfif>
						 <div class="box_#local.eventRates.rateID# evtBox" style="#local.showBox#">
							<div class="inline span12">
								<input name="bar_amount_text_#local.eventRates.rateID#" id="total_amt_text_#local.eventRates.rateID#"  type="hidden"  value="#local.eventRates.rateName#" />
								<input size="13" maxlength="13" name="bar_amount_#local.eventRates.rateID#" id="total_amt_#local.eventRates.rateID#" class="amountRadio" 													
								<cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr,"bar_amount_#local.eventRates.rateID#") AND (session.fieldArr["bar_amount_#local.eventRates.rateID#"] eq #local.eventRates.rate#)>checked=checked
								</cfif>
								 type="radio" data-value="#local.eventRates.rateID#" value="#local.eventRates.rate#" /><label for="total_amt_#local.eventRates.rateID#">Pay the Total Amount of - #dollarFormat(local.eventRates.rate)# </label>
							</div>
							<div class="inline span12">
								<input name="min_amt_text_#local.eventRates.rateID#" id="min_amt_text_#local.eventRates.rateID#"  type="hidden"  value="Pay Minimum Amount Of 25" />
								<input size="13" maxlength="13" name="bar_amount_#local.eventRates.rateID#" id="min_amt_#local.eventRates.rateID#" class="amountRadio"
								<cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr,"bar_amount_#local.eventRates.rateID#") AND (session.fieldArr["bar_amount_#local.eventRates.rateID#"]  eq 25)>checked=checked
								</cfif>
								 type="radio" data-value="#local.eventRates.rateID#" value="25" /><label for="min_amt_#local.eventRates.rateID#">Pay the Minimum Amount of $25</label>
							</div>
							<div class="inline span12">
								<input name="other_amt_text_#local.eventRates.rateID#" id="other_amt_text_#local.eventRates.rateID#"  type="hidden"  value="Other Amount" />
								<input size="13" maxlength="13" name="bar_amount_#local.eventRates.rateID#" id="other_amt_#local.eventRates.rateID#" class="amountRadio" 
								<cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr,"bar_amount_#local.eventRates.rateID#") AND (session.fieldArr["bar_amount_#local.eventRates.rateID#"]  eq 'other')>checked=checked
								</cfif>
								type="radio" data-value="#local.eventRates.rateID#" value="other" /><label for="other_amt_#local.eventRates.rateID#">Pay Other Amount</label>
							</div>
						</div>
					</div>
					
					<cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr,"bar_amount_#local.eventRates.rateID#") AND (session.fieldArr["bar_amount_#local.eventRates.rateID#"]  eq 'other')>
						<cfset showTxt = "display:block">
					<cfelse>
						<cfset showTxt = "display:none">
					</cfif>
					<div id="otherAmtDiv_#local.eventRates.rateID#" style="#showTxt#" class="frmRow1 row-fluid">
						<cfif structKeyExists(session, "fieldArr") AND structKeyExists(session.fieldArr,"other_txt_amt_#local.eventRates.rateID#")>
								<cfset temp = session.fieldArr["other_txt_amt_#local.eventRates.rateID#"] >
							<cfelse>
								<cfset temp = "" >
							</cfif>
							<div class="control-group">
								<div class="controls" style="margin-left: 27px;">
									<input class="otherAmt form-control" name="other_txt_amt_#local.eventRates.rateID#" id="other_txt_amt_#local.eventRates.rateID#" type="text" value="#temp#" />
								</div>
							</div>
					</div>
				</cfloop>									
				</table>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.returnHTML = "<div class='frmText row-fluid'>There are no study options available for this event.</div>">
		</cfif>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep2" access="private" output="false" returntype="string">
		<cfargument name="rateUID" type="string" required="true">
		<cfargument name="gradDate" type="date" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventRateID" type="numeric" required="true">
		<cfargument name="eventRate" type="numeric" required="true">
		<cfargument name="payOption" type="string" required="true">
		<cfargument name="payOptionText" type="string" required="true">

		<cfset var local = structNew()>
		<cfset storeArgumentsInSession(arguments)>

		<!--- was a sustaining or Plus rate selected? --->
		<cfquery name="local.qryStudentClass" dbtype="query">
			SELECT Class
			FROM variables.qryRateClasses
			WHERE upper(UID) = '#UCASE(arguments.rateUID)#'
		</cfquery>

		<!--- if no date or not sustaining or plus then kick back to first page --->
		<cfif NOT len(arguments.gradDate) OR NOT listFindNoCase("Sustaining,Plus",local.qryStudentClass.Class) OR NOT arguments.eventID OR NOT arguments.eventRate>
			<cflocation url="#variables.baselink#" addtoken="no">
		</cfif>

		<cfset local.returnHTML = showStep3(formvars=arguments)>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showStep3" access="private" output="false" returntype="string">
		<cfargument name="formvars" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.qryCurrentSectionSubscriptions = getCurrentSectionSubscriptions()>
		<cfset local.qryAvailableSections = getAvailableSections(qryCurrentSubscriptions=local.qryCurrentSectionSubscriptions)>

		<!--- look for active indy network subscription --->
		<cfset local.qryGetSubscriptions = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberid=variables.memberIDToUse, status='A', includeRate=false)>
		<cfquery name="local.qryIndyNetworkSub" dbtype="query">
			select *
			from [local].qryGetSubscriptions
			where upper(UID) = '#UCASE(variables.indyAttorneysNetworkUID)#'
		</cfquery>
		<cfif local.qryIndyNetworkSub.recordcount is 0>
			<cfset local.attorneyRateAmt =  application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=variables.memberIDToUse, subscriptionUID=variables.indyAttorneysNetworkUID, isRenewalRate=0).rateAmt />	
		</cfif>

		<cfset local.indyCheckedattorney = "">
		<cfif structKeyExists(session,"fieldArr") && structKeyExists(session.fieldArr,"indys_attorney") && (session.fieldArr.indys_attorney eq 1) >
			<cfset local.indyCheckedattorney = "checked">
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function _FB_validateForm() {
					$('##btnToStep4').attr('disabled',true).addClass('disabled');
					$('##submitTxt').show();
					return true;
				}
			</script>
			<style type="text/css">
				div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
				div.CPSection div.CPSectionTitle { font-size:14pt;  font-weight:bold; color:##fff; padding:10px; background:##899461; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }
				div.CPSection span.frmText, div.CPSection td.frmText, div.frmButtons span.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right;width:30%; }
				.required { color:red; }
				.frmRow1{ background:##fff; }
				##divTotals {font-weight:bold;}		
				.mrg0{margin-left: 0 !important}
				.pad10{padding: 10px;}	
				##subbox input{margin: 2px;}
				.checkboxLabel{
					font-weight: 300;
					line-height: 26px;
					font-size: 18px;
					color: ##434343;
				}
				
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="TitleText">#variables.formNameDisplay#</div>
			<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
			<cfform class="form-horizontal" name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
			<cfinput type="hidden" name="fa" id="fa" value="processStep3">

			<cfloop collection="#arguments.formvars#" item="local.key">
				<cfinput type="hidden" name="#lcase(local.key)#" id="#lcase(local.key)#" value="#arguments.formvars[local.key]#">
				<cfset session.fieldArr[local.key] = arguments.formvars[local.key]>
			</cfloop>

			<br/>
			<div class="BodyText CPSection">
				<div class="row-fluid CPSectionTitle BB">Sections/Divisions:</div>
				<div class="row-fluid  frmRow1 frmText" style="padding:10px;">
					<p>Join and network with other members who share the same interest or practice area. As a member of a section or division, you will receive advance notice of the events sponsored by the group and gain access to resources and special educational and social events designated only for members of specific sections and divisions.</p>

					<cfif local.qryCurrentSectionSubscriptions.recordcount>
						<p>Your IndyBar membership for this year includes affiliation with the following section(s) or division(s). 
							To remove a section or division, check the box next to the group name.</p>
						<div class=" row-fluid CPSection mrg0">
							<div class="span12 frmRow1 pad10" id="remsubbox">
								<cfloop query="local.qryCurrentSectionSubscriptions">
									<div class="form-group">
										<div class="checkbox">
											<input style="margin-top:7px;" type="checkbox" name="subsToExpire" id="subsToExpire#local.qryCurrentSectionSubscriptions.subscriberID#" 
												value="#local.qryCurrentSectionSubscriptions.subscriberID#"  
												<cfif structKeyExists(session.fieldArr,"subsToExpire") && listFind(session.fieldArr.subsToExpire,local.qryCurrentSectionSubscriptions.subscriberID)>checked='checked'</cfif>>
												<label class="checkboxLabel" for="subsToExpire#local.qryCurrentSectionSubscriptions.subscriberID#">#local.qryCurrentSectionSubscriptions.subscriptionName#</label>
										</div>  												
									</div>
								</cfloop>
							</div>
						</div>	
					</cfif>

					<p>If you would like to add group affiliations, check the box next to the group(s) you wish to join. Students receive free membership in two sections or divisions free each year with additional groups charged at $25 each.</p>

					<div class="row-fluid CPSection mrg0">
						<div class="span12 frmRow1 pad10" id="remsubbox">
							<cfloop query="local.qryAvailableSections">
								<div class="form-group">
									<div class="checkbox">
										<input style="margin-top:7px;" type="checkbox" name="newSubs" id="sectionDivision#local.qryAvailableSections.subIDrateID#" 
											value="#local.qryAvailableSections.subIDrateID#" 
											<cfif structKeyExists(session.fieldArr,"newSubs") && listFind(session.fieldArr.newSubs,local.qryAvailableSections.subIDrateID)>checked='checked'</cfif>>
											
											<label class="checkboxLabel" for="sectionDivision#local.qryAvailableSections.subIDrateID#">#local.qryAvailableSections.subscriptionName#</label>
									</div>
								</div>
							</cfloop>
						</div>
					</div>

				</div>
			</div>
			<cfif local.qryIndyNetworkSub.recordcount is 0>
				<cfinput type="hidden" name="old_indys_attorney" id="old_indys_attorney" value="0">
				<div class="BodyText CPSection">
					<div class="NVTitle CPSectionTitle BB">Indy Attorneys Network:</div>
					<div class="frmRow1 frmText" style="padding:10px;">
						<table width="100%" cellspacing="0" cellpadding="0" border="0">
							<tbody>
								<tr> 
									<td colspan="3">
										Take the "work" out of "networking" with the Indy Attorneys Network! Choose this add-on service and get matched with a fellow IndyBar member each month for informal networking. Plus, get access to special events.
									</td>
								</tr>
								<tr>
									<td width="30" class="col1">								
										<input type="checkbox" class="checkbox" value="1" id="indys_attorney" name="indys_attorney" <cfif isDefined("session.Responses.indyAttyNetwork") and session.Responses.indyAttyNetwork is 1>checked</cfif>>
									</td>
									<td class="col2">
										<p>I would like to join the Indy Attorneys Network for #dollarFormat(local.attorneyRateAmt)# per year.</p>
									</td>
									<td width="100px" style="text-align:right"></td>
								</tr>
							</tbody>
						</table>								
					</div>
				</div>		
			<cfelse>
				<cfinput type="hidden" name="old_indys_attorney" id="old_indys_attorney" value="1">
				<cfinput type="hidden" name="indys_attorney" id="indys_attorney" value="1">
			</cfif>
			<div class="frmButtons">
				<a id="gotoStep1" class="btn" href="#variables.baselink#" style="float:right;">Back</a>
				<button type="submit" id="btnToStep4" name="btnToStep4" class="btn">Continue</button>
				<span id="submitTxt" class="frmText" style="display:none;"><i class="icon-spin icon-spinner"></i> Please wait...</span>
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep3" access="private" output="false" returntype="string">
		<cfargument name="rateUID" type="string" required="true">
		<cfargument name="gradDate" type="date" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventRateID" type="numeric" required="true">
		<cfargument name="eventRate" type="numeric" required="true">
		<cfargument name="payOption" type="string" required="true">
		<cfargument name="payOptionText" type="string" required="true">
		<cfargument name="subsToExpire" type="string" required="true">
		<cfargument name="newSubs" type="string" required="true">
		<cfargument name="old_indys_attorney" type="boolean" required="true">
		<cfargument name="indys_attorney" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset storeArgumentsInSession(arguments)>

		<!--- was a sustaining or Plus rate selected? --->
		<cfquery name="local.qryStudentClass" dbtype="query">
			SELECT Class
			FROM variables.qryRateClasses
			WHERE upper(UID) = '#UCASE(arguments.rateUID)#'
		</cfquery>

		<!--- if no date or not sustaining or plus then kick back to first page --->
		<cfif NOT len(arguments.gradDate) OR NOT listFindNoCase("Sustaining,Plus",local.qryStudentClass.Class)>
			<cflocation url="#variables.baselink#" addtoken="no">
		<cfelseif local.qryStudentClass.Class eq "Plus" and (NOT arguments.eventID OR NOT arguments.eventRate)>
			<cflocation url="#variables.baselink#" addtoken="no">
		</cfif>

		<cfset local.returnHTML = showStep4(formvars=arguments)>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showStep4" access="private" output="false" returntype="string">
		<cfargument name="formvars" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.formvars.subsToExpire)>
		<cfset local.strNewSubs = validateNewSubs(newSubs=arguments.formvars.newSubs, subsToExpire=local.strsubsToExpire.subsToExpire)>
		<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=variables.siteID, profilecode=variables.payProfileCode, 
											pmid=variables.memberIDToUse, showCOF=true, usePopup=false, usePopupDIVName='ccForm', autoShowForm=true)>

		<!--- was a sustaining or Plus rate selected? --->
		<cfquery name="local.qryStudentClass" dbtype="query">
			SELECT Class
			FROM variables.qryRateClasses
			WHERE upper(UID) = '#UCASE(arguments.formvars.rateUID)#'
		</cfquery>

		<!--- get the rate amt --->
		<cfset local.qryEligibleRates = application.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.memberIDToUse, subscriptionUID=variables.StudentDuesSubUID, isRenewal=false)>
		<cfquery name="local.qryEligibleRates" dbtype="query">
			SELECT *
			FROM [local].qryEligibleRates
			WHERE UPPER(rateUID) = '#UCASE(arguments.formvars.rateUID)#'
		</cfquery>

		<!--- plus: get event selected --->
		<cfif local.qryStudentClass.class eq "Plus">
			<cfquery name="local.qryEventSelected" dbtype="query">
				select eventTitle
				from variables.nextEvents
				where eventID = #arguments.formvars.eventID#
			</cfquery>
		</cfif>

		<!--- get total due --->
		<cfset local.totalDue = local.qryEligibleRates.rateAmt + local.strNewSubs.newSubsAmount>
		<cfif local.qryStudentClass.class eq "Plus">
			<cfset local.totalDue = local.totalDue + val(arguments.formVars.payOption)>
		</cfif>
		<cfif arguments.formVars.indys_attorney is 1 and arguments.formVars.old_indys_attorney is 0>
			<cfset local.totalDue = local.totalDue + 25>
		</cfif>


		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<cfif len(local.strPaymentForm.headCode)>
				#local.strPaymentForm.headCode#
			</cfif>
			<script type="text/javascript"> 
				$(document).ready(function(){
					$(".backLink").click(function(){
						$("##fa").val("processStep1");
						$("###variables.formName#").submit();
					});
					 $(document).on('click','.cof_edit',function(){
						listenToFrame();
					});
				});
				function listenToFrame(){
					var windowWidth = $(window).width();
					var _scrollPx = 300;
					if(windowWidth < 767) {
						_scrollPx = 600;
					}
					var formListener = setInterval(function(){
						if(!$('div[id^="divManageFormWrapper"]').is(':visible') ){
							$('html, body').animate({scrollTop : _scrollPx},800);
							clearInterval(formListener);
						}
					},100);
				};
				function checkPaymentMethod() {							
					if ($('##payMethCC').is(':checked')) {
						$('##CCInfo').show();
						$('##CheckInfo').hide();
						if(!$('##ccForm').is(':visible')) {
							listenToFrame();
						}
					}else{
						$('##CCInfo').hide();
						$('##CheckInfo').show();
					}
				}

				function _FB_validateForm() {
					var thisForm = document.forms["#variables.formName#"];
					var arrReq = new Array();

					var MethodOfPaymentValue = $('input[name=payMeth]:checked', '###variables.formName#').val();
					if (MethodOfPaymentValue == 'CC') {
						#local.strPaymentForm.jsvalidation#	
					}
					
					if (arrReq.length > 0) {
						var msg = 'Please address the following issues with your application:\n\n';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
						alert(msg);
						return false;
					}

					if (MethodOfPaymentValue == 'CC') {
						$('##btnToFinalCC').attr('disabled',true).addClass('disabled');
						$('##submitTxtCC').show();
					} else {
						$('##btnToFinalCheck').attr('disabled',true).addClass('disabled');
						$('##submitTxtCheck').show();
					}

					return true;
				}					
			</script>
			<style type="text/css">
				div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
				div.CPSection div.CPSectionTitle { font-size:14pt;  font-weight:bold; color:##fff; padding:10px; background:##899461; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }
				div.CPSection span.frmText, div.CPSection td.frmText, div.frmButtons span.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right;width:30%; }
				.required { color:red; }
				.frmRow1{ background:##fff; }
				##divTotals {font-weight:bold;}		
				.mrg0{margin-left: 0 !important}
				.pad10{padding: 10px;}	
				##subbox input{margin: 2px;}
				.paymentControls{
					margin-left: 0 !important;
				}
				.reviewWrap{
					padding:0px !important;
				}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="TitleText">#variables.formNameDisplay#</div>
			<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
			<cfform class="form-horizontal" name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
			<cfinput type="hidden" name="fa" id="fa" value="processStep4">
			<cfinput type="hidden" name="subsToExpire" id="subsToExpire" value="#local.strsubsToExpire.subsToExpire#">
			<cfinput type="hidden" name="newSubs" id="newSubs" value="#local.strNewSubs.newSubs#">

			<cfloop collection="#arguments.formvars#" item="local.key">
				<cfif NOT listFindNoCase("subsToExpire,newSubs",local.key)>
					<cfinput type="hidden" name="#lcase(local.key)#" id="#lcase(local.key)#" value="#arguments.formvars[local.key]#">
					<cfset session.fieldArr[local.key] = arguments.formvars[local.key]>
				</cfif>
			</cfloop>

			<br/>
			<div class="CPSection frmRow1 mrg0 row-fluid BodyText reviewWrap">
				<div class="row-fluid CPSectionTitle BB">Review Changes</div>
				<div class="row-fluid pad10" style="padding:10px !important">
					<cfif local.qryStudentClass.class eq "Sustaining">
						<div class="mrg0 span12">
							<div class="span10">Sustaining Membership</div>
							<div class="span2 pull-right">#dollarFormat(local.qryEligibleRates.rateAmt)#</div>
						</div>
					<cfelseif local.qryStudentClass.class eq "Plus">
						<div class="mrg0 span12">
							<div class="span10">Sustaining Membership Plus Bar Review</div>
							<div class="span2 pull-right">#dollarFormat(local.qryEligibleRates.rateAmt)#</div>
						</div>
						<div class="mrg0 span12">
							<div class="span10">#local.qryEventSelected.eventTitle# - #arguments.formVars.PAYOPTIONTEXT#</div>
							<div class="span2 pull-right">#dollarFormat(arguments.formVars.payOption)#</div>
						</div>
					</cfif>
					<cfif listLen(local.strsubsToExpire.subsToExpire)>
						<cfloop query="local.strSubsToExpire.qrySubsToExpire">
							<div class="mrg0 span12">
								<div class="span10">REMOVE SECTION: #local.strSubsToExpire.qrySubsToExpire.subscriptionName#</div>
								<div class="span2 pull-right">#dollarFormat(0)#</div>
							</div>
						</cfloop>
					</cfif>
					<cfif listLen(local.strNewSubs.newSubs)>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<div class="mrg0 span12">
								<div class="span10">ADD SECTION: #local.strNewSubs.qrySubsToAdd.subscriptionName#</div>
								<div class="span2 pull-right">#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#</div>
							</div>
						</cfloop>
					</cfif>
					<cfif arguments.formVars.indys_attorney is 1 and arguments.formVars.old_indys_attorney is 0>
						<div class="mrg0 span12">
							<div class="span10">ADD: Indy Attorneys Network</div>
							<div class="span2 pull-right">#dollarFormat(25)#</div>
						</div>
					</cfif>
					<div class="mrg0 span12">
						<div class="span10 BodyTextLarge"><b>Total Due:</b></div>
						<div class="span2 pull-right BodyTextLarge"><b>#dollarFormat(local.totalDue)#</b></div>
					</div>
				</div>
			</div>

			<br/><br/>
			<div class="row-fluid" id="payerrDIV" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid CPSection frmRow1  mrg0 BodyText reviewWrap">
				<div class="row-fluid CPSectionTitle BB">Method of Payment</div>
				<div class="row-fluid" style="padding:10px !important">
					<p>Please select your preferred method of payment from the options.</p>
					<div class="control-group">
						<div class="controls paymentControls">
							<div>
								<input value="CC" class=" optionsRadio" name="payMeth" id="payMethCC" type="radio" onClick="checkPaymentMethod();">
								<label style="display:inline;" for="payMethCC">Credit Card</label> 
							</div>
							<div>
								<input value="Check" class=" optionsRadio" name="payMeth" id="payMethCC1" type="radio" onClick="checkPaymentMethod();">
								<label style="display:inline;" for="payMethCC1">Check</label> 
							</div>
						</div>
						<div class="row-fluid c frmText">
							<br/>The Study Option you select now will serve as a placeholder only and can be changed later.
						</div>
					</div>
				</div>
			</div>

			<br/>
			<div id="CCInfo" class="CPSection frmRow2 span12 mrg0 BodyText" style="display:none;">
				<div class="CPSectionTitle BB">Credit Card Information</div>
				<div id="ccForm" class="pad10">
					<div>#local.strPaymentForm.inputForm#</div>
					<br/>
					<div class="frmButtons">
						<button type="submit" id="btnToFinalCC" name="btnToFinal" class="btn">Finalize Changes</button>
						<span id="submitTxtCC" class="frmText" style="display:none;"><i class="icon-spin icon-spinner"></i> Please wait...</span>
					</div>
				</div>
			</div>
			<div id="CheckInfo" class="CPSection frmRow2 span12 mrg0 BodyText" style="display:none;">
				<div class="CPSectionTitle">Check Information</div>
				<div class="P pad10">
					<p style="font-size:18px;font-weight:bold;">If you choose "Check" as your payment method, you will not gain access to IndyBar membership and member benefits until payment is received. For immediate access, please pay by credit card.</p>
					<p>Please make your check payable to the Indianapolis Bar Association and mail to:<br/>
					<div style="padding-left:25px;">135 N. Pennsylvania St.<br/>Suite 1500<br/>Indianapolis, IN 46204</div></p>
					<p>Please include a copy of the receipt with your payment.</p>
					<p>If you have any questions, please do not hesitate to call 317-269-2000 <NAME_EMAIL>.</p>
					<div class="frmButtons">
						<button type="submit" id="btnToFinalCheck" name="btnToFinal" class="btn">Finalize Changes</button>
						<span id="submitTxtCheck" class="frmText" style="display:none;"><i class="icon-spin icon-spinner"></i> Please wait...</span>
					</div>
				</div>
			</div>
			<div >
				<a style="float:right;" id="gotoStep2" class="btn backLink" href="##">Back To Previous Step</a>
			</div>			
			
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep4" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="rateUID" type="string" required="true">
		<cfargument name="gradDate" type="date" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventRateID" type="numeric" required="true">
		<cfargument name="eventRate" type="numeric" required="true">
		<cfargument name="payOption" type="string" required="true">
		<cfargument name="payOptionText" type="string" required="true">
		<cfargument name="subsToExpire" type="string" required="true">
		<cfargument name="newSubs" type="string" required="true">
		<cfargument name="indys_attorney" type="boolean" required="true">
		<cfargument name="payMethod" type="string" required="true">
		<cfargument name="mppid" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset storeArgumentsInSession(arguments)>

		<!--- was a sustaining or Plus rate selected? --->
		<cfquery name="local.qryStudentClass" dbtype="query">
			SELECT Class
			FROM variables.qryRateClasses
			WHERE upper(UID) = '#UCASE(arguments.rateUID)#'
		</cfquery>

		<!--- if no date or not sustaining or plus then kick back to first page --->
		<cfif NOT len(arguments.gradDate) OR NOT listFindNoCase("Sustaining,Plus",local.qryStudentClass.Class) OR NOT ListFindNoCase("CC,Check",arguments.payMethod)>
			<cflocation url="#variables.baselink#" addtoken="no">
		<cfelseif local.qryStudentClass.Class eq "Plus" and (NOT arguments.eventID OR NOT arguments.eventRate)>
			<cflocation url="#variables.baselink#" addtoken="no">
		</cfif>

		<cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.subsToExpire)>
		<cfset local.strNewSubs = validateNewSubs(newSubs=arguments.newSubs, subsToExpire=local.strsubsToExpire.subsToExpire)>

		<!--- get the rate amt --->
		<cfset local.qryEligibleRates = application.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.memberIDToUse, subscriptionUID=variables.StudentDuesSubUID, isRenewal=false)>
		<cfquery name="local.qryEligibleRates" dbtype="query">
			SELECT rateAmt
			FROM [local].qryEligibleRates
			WHERE UPPER(rateUID) = '#UCASE(arguments.rateUID)#'
		</cfquery>

		<!--- get active subscription --->
		<cfset local.qryActiveStudentSub = getMembershipByStatus(statusCode='A')>

		<!--- get entire subscription tree --->
		<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberid=variables.memberIDToUse, status='A', includeRate=true)>
		<cfquery name="local.qryActiveStudentSubTree" dbtype="query">
			select subscriberID, uid, typeName
			from [local].qryActiveSubs
			where rootSubscriberID = #local.qryActiveStudentSub.rootSubscriberID#
		</cfquery>

		<!--- get total due --->
		<cfset local.totalDue = local.qryEligibleRates.rateAmt + local.strNewSubs.newSubsAmount>
		<cfif local.qryStudentClass.class eq "Plus">
			<cfset local.totalDue = local.totalDue + val(arguments.payOption)>
		</cfif>
		<cfif arguments.indys_attorney is 1 and NOT listFindNoCase(valueList(local.qryActiveStudentSubTree.uid),variables.indyAttorneysNetworkUID)>
			<cfset local.totalDue = local.totalDue + 25>
		</cfif>

		<!--- plus: get event selected --->
		<cfif local.qryStudentClass.class eq "Plus">
			<cfquery name="local.qryEventSelected" dbtype="query">
				select eventTitle
				from variables.nextEvents
				where eventID = #arguments.eventID#
			</cfquery>
		</cfif>



		<!--- --------------------------------------------- --->
		<!--- create what will be the new subscription tree --->
		<!--- --------------------------------------------- --->
		<!--- Need to override perms because the expiration of the active tree will make them "ineligible" for the new rates --->
		<cfset local.subStruct = structNew()>
		<cfset local.subStruct.uid = variables.StudentDuesSubUID>
		<cfset local.subStruct.rateUID = arguments.rateUID>
		<cfset local.subStruct.overridePerms = true>
		<cfset local.subStruct.children = arrayNew(1)>

		<!--- if the foundation donation is on the active sub, add it to the new sub tree as $0 --->
		<cfif local.qryActiveStudentSubTree.recordcount and listFindNoCase(valueList(local.qryActiveStudentSubTree.uid),variables.barFoundationDonationUID)>
			<cfset local.qryRateInfo = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=variables.memberIDToUse, subscriptionUID=variables.barFoundationDonationUID, isRenewalRate=0)>	
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = variables.barFoundationDonationUID>
			<cfset local.childStruct.rateUID = local.qryRateInfo.rateUID>
			<cfset local.childStruct.rateOverride = 0>
			<cfset local.childStruct.overridePerms = true>
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>

		<!--- if the indybar atty network is on the active sub, add it to the new sub tree as $0 if they still want it --->
		<!--- if the indybar atty network is NOT on the active sub, add it to the new sub tree if they want it --->
		<cfif arguments.indys_attorney is 1>
			<cfset local.qryRateInfo = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=variables.memberIDToUse, subscriptionUID=variables.indyAttorneysNetworkUID, isRenewalRate=0)>	
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = variables.indyAttorneysNetworkUID>
			<cfset local.childStruct.rateUID = local.qryRateInfo.rateUID>
			<cfif listFindNoCase(valueList(local.qryActiveStudentSubTree.uid),variables.indyAttorneysNetworkUID)>
				<cfset local.childStruct.rateOverride = 0>
			</cfif>
			<cfset local.childStruct.overridePerms = true>
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>

		<!--- sections: add in the selected sections and the sections they had and are not removing. Consider the 2 free sections when adding amounts --->
		<cfloop query="local.qryActiveStudentSubTree">
			<cfif local.qryActiveStudentSubTree.typeName eq "Sections">
				<cfif NOT listLen(local.strsubsToExpire.subsToExpire) OR
					(listLen(local.strsubsToExpire.subsToExpire) and NOT listFindNoCase(valueList(local.strsubsToExpire.qrySubsToExpire.subscriberID),local.qryActiveStudentSubTree.subscriberID))>
					<cfset local.qryRateInfo = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=variables.memberIDToUse, subscriptionUID=local.qryActiveStudentSubTree.uid, isRenewalRate=0)>	
					<cfset local.childStruct = structNew()>
					<cfset local.childStruct.uid = local.qryActiveStudentSubTree.uid>
					<cfset local.childStruct.rateUID = local.qryRateInfo.rateUID>
					<cfset local.childStruct.rateOverride = 0>
					<cfset local.childStruct.overridePerms = true>
					<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
				</cfif>
			</cfif>
		</cfloop>
		<cfif listLen(local.strNewSubs.newSubs)>
			<cfloop query="local.strNewSubs.qrySubsToAdd">
				<cfset local.qryRateInfo = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=variables.memberIDToUse, subscriptionUID=local.strNewSubs.qrySubsToAdd.uid, isRenewalRate=0)>	
				<cfset local.childStruct = structNew()>
				<cfset local.childStruct.uid = local.strNewSubs.qrySubsToAdd.uid>
				<cfset local.childStruct.rateUID = local.qryRateInfo.rateUID>
				<cfif local.strNewSubs.qrySubsToAdd.rateAmt is 0>
					<cfset local.childStruct.rateOverride = 0>
				</cfif>
				<cfset local.childStruct.overridePerms = true>
				<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
			</cfloop>
		</cfif>


		<!--- create active Student Dues subscription --->
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.memberIDToUse, subStruct=local.subStruct, newAsBilled=false)>
		<cfset local.qrySubscriptionInvoices = application.objCustomPageUtils.sub_getInvoicesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID)>


		<!--- If user has a billed/renewal not sent subscription, delete it and regenerate it based on new active sub --->
		<cfset local.subscriberIDToDelete = 0>
		<cfset local.prevOfferStartDate = ''>
		<cfset local.prevOfferRescindDate = ''>
		<cfset local.prevOfferStatus = ''>
		<cfset local.qryBilledStudentSub = getMembershipByStatus(statusCode='O')>
		<cfif local.qryBilledStudentSub.recordcount>
			<cfset local.subscriberIDToDelete = local.qryBilledStudentSub.subscriberID>
			<cfset local.prevOfferStartDate = local.qryBilledStudentSub.subStartDate>
			<cfset local.prevOfferRescindDate = local.qryBilledStudentSub.offerRescindDate>
			<cfset local.prevOfferStatus = 'O'>
		<cfelse>
			<cfset local.qryRenewalNotSentStudentSub = getMembershipByStatus(statusCode='R')>
			<cfif local.qryRenewalNotSentStudentSub.recordcount>
				<cfset local.subscriberIDToDelete = local.qryRenewalNotSentStudentSub.subscriberID>
				<cfset local.prevOfferStartDate = local.qryRenewalNotSentStudentSub.subStartDate>
				<cfset local.prevOfferRescindDate = local.qryRenewalNotSentStudentSub.offerRescindDate>
				<cfset local.prevOfferStatus = 'R'>
			</cfif>
		</cfif>
		<cfif local.subscriberIDToDelete gt 0>
			<cfset deleteSubscriber(memberID=variables.memberIDToUse, subscriberID=local.subscriberIDToDelete)>
			<cfset application.objMember.insertIntoMemberGroupQueue(orgID=variables.orgID, memberID=variables.memberIDToUse, processImmediately=1)>
			<cfset rebuildSubscriptionOffer(memberID=variables.memberIDToUse, activesubscriberID=local.subReturn.rootSubscriberID, offerRescindDate=local.prevOfferRescindDate, overrideStartDate=local.prevOfferStartDate, offeredStatus=local.prevOfferStatus)>
		</cfif>


		<!--- ------------------------ --->
		<!--- plus: register for event --->
		<!--- ------------------------ --->
		<cfif local.qryStudentClass.class eq "Plus">
			<cfquery name="local.qryEventSelected" dbtype="query">
				select eventTitle
				from variables.nextEvents
				where eventID = #arguments.eventID#
			</cfquery>

			<cfset local.objEventReg = application.objCustomPageUtils.ev_objEventReg(eventID=arguments.eventID)>
			<cfset local.objEventReg.setRegistrant(memberID=variables.memberIDToUse, identificationMethod="CF_DIRECT")>
			<cfset local.objEventReg.setRate(rateID=arguments.eventRateID)>
			<cfset local.strResult = local.objEventReg.register()>
			<cfif local.strResult.success>
				<cfset local.qryEventInvoices = application.objCustomPageUtils.ev_getInvoicesForRegistration(registrantID=local.strResult.registrantID)>
			</cfif>					    	        	
		</cfif>


		<!--- ---------------------- --->
		<!--- Payment and accounting --->
		<!--- ---------------------- --->
		<cfif local.totalDue gt 0 and arguments.paymethod eq "CC">
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalDue, assignedToMemberID=variables.memberIDToUse, recordedByMemberID=variables.memberIDToUse, rc=arguments.event.getCollection() } >
			<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=variables.payProfileID, profileCode=variables.payProfileCode }>
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

			<!--- since we already have the invoices we will pay against, set them in the pool --->
			<cfloop query="local.qrySubscriptionInvoices">
				<cfif QueryAddRow(local.objAccounting.invoicePool)>
					<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.qrySubscriptionInvoices.invoiceID)>
					<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.qrySubscriptionInvoices.invoiceProfileID)>
					<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.qrySubscriptionInvoices.sumTotal)>
				</cfif>
			</cfloop>

			<!--- event invoices can be partially paid by this custom form --->
			<cfif local.qryStudentClass.class eq "Plus" and arguments.eventRate gt 0 and isDefined("local.qryEventInvoices")>
				<cfset local.eventAmtLeftToAllocate = arguments.eventRate>
				<cfloop query="local.qryEventInvoices">
					<cfif local.eventAmtLeftToAllocate gt 0>
						<cfif local.qryEventInvoices.amtDue gt local.eventAmtLeftToAllocate>
							<cfset local.invAmount = local.eventAmtLeftToAllocate>
						<cfelse>
							<cfset local.invAmount = local.qryEventInvoices.amtDue>
						</cfif>
						<cfset local.eventAmtLeftToAllocate = local.eventAmtLeftToAllocate - local.invAmount>
						<cfif QueryAddRow(local.objAccounting.invoicePool)>
							<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.qryEventInvoices.invoiceID)>
							<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.qryEventInvoices.invoiceProfileID)>
							<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.invAmount)>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>

			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

			<!--- Set the activation status --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_overrideActivationMemberSubscription">
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#local.subReturn.rootSubscriberID#">
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#variables.siteID#">
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#variables.memberIDToUse#">
				<cfprocparam type="in" cfsqltype="cf_sql_bit" value="0">
				<cfprocparam type="out" cfsqltype="cf_sql_bit" variable="local.statusUpdated">
			</cfstoredproc>

			<!--- get card used for this submission --->
			<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.mppid, memberID=variables.memberIDToUse, profileID=variables.payProfileID)>
		<cfelse>
			<cfset local.strACCResponse.accResponseMessage = "">
		</cfif>


		<!--- construct email / confirmation --->
		<cfsavecontent variable="local.pageCSS">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.msgHeader{ background:##224563; font-weight:bold; padding:5px;color:##ffffff; }
				.frmText{ font-size:12pt; color:##505050; } 
				.b{ font-weight:bold; }
				.CPSectionTitle {font-size: 25px;margin-bottom: 15px;}
				table td{max-width:50%;}
			</style>
			</cfoutput>
		</cfsavecontent>	

		<cfset local.qryMemberInfo = application.objMember.getMemberInfo(memberID=variables.memberIDToUse, orgID=variables.orgID)>

		<cfsavecontent variable="local.invoice">
			<cfoutput>
			<!-- @accResponseMessage@ -->
			<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>

			<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
			<tr class="msgHeader"><td colspan="2" class="b">MEMBER INFORMATION</td></tr>
			<tr><td width="50%" class="frmText b">MemberNumber:</td><td class="frmText">#local.qryMemberInfo.memberNumber#&nbsp;</td></tr>
			<tr><td class="frmText b">Expected Graduation Date:</td><td class="frmText">#arguments.gradDate#&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>

			<cfif local.qryStudentClass.class eq "Sustaining">
				<tr><td class="frmText b">Membership Type:</td><td class="frmText">Sustaining Membership - #dollarFormat(local.qryEligibleRates.rateAmt)#&nbsp;</td></tr>
			<cfelseif local.qryStudentClass.class eq "Plus">
				<tr><td class="frmText b">Membership Type:</td><td class="frmText">Sustaining Membership Plus Bar Review - #dollarFormat(local.qryEligibleRates.rateAmt)#&nbsp;</td></tr>
				<tr><td class="frmText b">Course:</td><td class="frmText">#local.qryEventSelected.eventTitle# - #arguments.payOptionText# - #dollarFormat(arguments.payOption)#&nbsp;</td></tr>
			</cfif>
			<tr><td colspan="2">&nbsp;</td></tr>

			<cfif listLen(local.strsubsToExpire.subsToExpire)>
				<cfloop query="local.strSubsToExpire.qrySubsToExpire">
					<tr><td class="frmText b">REMOVE SECTION:</td><td class="frmText">#local.strSubsToExpire.qrySubsToExpire.subscriptionName#&nbsp;</td></tr>
				</cfloop>
			</cfif>

			<cfif listLen(local.strNewSubs.newSubs)>
				<cfloop query="local.strNewSubs.qrySubsToAdd">
					<tr><td class="frmText b">ADD SECTION:</td><td class="frmText">#local.strNewSubs.qrySubsToAdd.subscriptionName# - #dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#&nbsp;</td></tr>
				</cfloop>
			</cfif>

			<cfif arguments.indys_attorney is 1 and NOT listFindNoCase(valueList(local.qryActiveStudentSubTree.uid),variables.indyAttorneysNetworkUID)>
				<tr><td class="frmText b">ADD:</td><td class="frmText">Indy Attorneys Network - #dollarFormat(25)#&nbsp;</td></tr>
			</cfif>

			<tr><td colspan="2">&nbsp;</td></tr>
			<tr><td class="frmText b">Total Due:</td><td class="frmText">#dollarFormat(local.totalDue)#&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>

			<tr><td class="frmText">Payment Method:</td><td class="frmText"><cfif arguments.paymethod eq "CC">Credit Card - #local.qrySavedInfoOnFile.detail#<cfelse>Check</cfif></td></tr>
			</table>
			</cfoutput>
		</cfsavecontent>

		<cfset local.arrInvoice = arrayNew(1)>
		
		<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='INDY')>
		<cfloop query="local.qrySubscriptionInvoices">
			<cfset local.strInvoiceForEmail= structNew()>
			<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=variables.siteID, invoiceID=local.qrySubscriptionInvoices.invoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
			<cfset local.strInvoiceForEmail.displayName = local.strInvoice.displayName>
			<cfset local.strInvoiceForEmail.invoicePath = local.strInvoice.invoicePath>
			<cfset arrayAppend(local.arrInvoice,local.strInvoiceForEmail)>
		</cfloop>


		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				#local.pageCSS#
				</head>
				<body>
				#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "LawStudentUpgradeConfirmation_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Law_Student_Upgrade_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset storeLawStudentApplication(memberID=variables.memberIDToUse, strPDF=local.strPDF)>

		<!--- email submitter (no error shown to user) --->	
		
		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				#local.pageCSS#
				<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
				#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<cfset local.mailAttach = arrayNew(1)>
		<cfset local.i = 1>
		<cfloop array="#local.arrInvoice#" index="local.thisInvoice">
			<cfif FileExists("#local.thisInvoice.invoicePath#")>
				<cfset local.mailAttach[local.i] = structNew()>
				<cfset local.mailAttach[local.i]["file"] = local.thisInvoice.displayName>
				<cfset local.mailAttach[local.i]["folderpath"] = local.strFolder.folderPath>
				<cfset local.i = local.i + 1>
			</cfif>
		</cfloop>

		<cfscript>
			local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.strEMailSettings_member.from },
				emailto=[
					{ name="", email=variables.strEMailSettings_member.to }
				],
				emailreplyto= variables.strEMailSettings_staff.to,
				emailsubject= variables.strEMailSettings_member.subject,
				emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
				emailhtmlcontent=local.mailContent,
				emailAttachments=local.mailAttach,
				siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
				memberID=val(variables.memberIDToUse),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			);
			local.emailSentToUser = local.responseStruct.success;
		</cfscript>
		
		<!--- email staff (no error shown to user) --->

		<cfset local.invoice = replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>					
				<cfif NOT local.emailSentToUser>
					<p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
				</cfif>
				#local.pageCSS#
				#local.invoice#
			</cfoutput>
		</cfsavecontent>

		<cfscript>
			local.arrEmailTo = [];
			variables.strEMailSettings_staff.to = replace(variables.strEMailSettings_staff.to,",",";","all");
			local.toEmailArr = listToArray(variables.strEMailSettings_staff.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		</cfscript>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.strEMailSettings_staff.from},
			emailto=local.arrEmailTo,
			emailreplyto=variables.strEMailSettings_staff.from,
			emailsubject=variables.strEMailSettings_staff.subject,
			emailtitle=event.getTrimValue('mc_siteinfo.sitename') & " - " &  variables.formNameDisplay,
			emailhtmlcontent=local.mailContent,
			siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfreturn local.invoice>
	</cffunction>

	<cffunction name="getCurrentSectionSubscriptions" access="private" output="false" returntype="query">
		<cfargument name="subsToExpire" type="string" required="false" default="">

		<cfset var memberSubs = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="memberSubs">
			select subscriberID, subscriptionID, subscriptionName, rateName, canBeFree
			from (
				select s.subscriberID, s.subscriptionID, subs.subscriptionName, r.rateName, 0 as canBeFree
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
				inner join dbo.sub_rateFrequencies as rf on rf.RFID = s.RFID
				inner join dbo.sub_rates as r on r.rateID = rf.rateID
				inner join dbo.sub_types t on t.typeID = subs.typeID
					and t.siteID = #variables.siteID#
					AND t.typeName = 'Sections'
				inner join dbo.ams_members m on m.memberID = s.memberID
					and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.memberIDToUse#">
				inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
			) as tmp
			<cfif listlen(arguments.subsToExpire)>
				where subscriberID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subsToExpire#" list="true">)
			</cfif>
			order by subscriptionName
		</cfquery>

		<cfreturn memberSubs>
	</cffunction>

	<cffunction name="getAvailableSections" access="private" output="false" returntype="query">
		<cfargument name="qryCurrentSubscriptions" type="query" required="true">
		<cfargument name="newSubs" type="string" required="false" default="">

		<cfset var local = structNew()>

		<cfset local.lstExistingSubs = ""> 
		<cfloop query="arguments.qryCurrentSubscriptions">
			<cfset local.lstExistingSubs = listAppend(local.lstExistingSubs, arguments.qryCurrentSubscriptions.subscriptionID)>
		</cfloop>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAvailableSections">
			select *
			from (
				select r.rateID, r.rateName, s.uid, rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, 1 as canBeFree, 
					cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID
				from dbo.sub_subscriptionSets st
				inner join dbo.sub_sets s2 on st.setId = s2.setID
				inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID 
					and s.status = 'A'
				inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
					AND sch.status = 'A'
				inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
					and r.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.frequencyShortName = 'F'
					and f.status = 'A'
				WHERE s2.siteID = #variables.siteID#
				AND s2.uid = '43F5E259-75FA-414F-8BFF-81B22F9B8F3C' 
				AND r.uid = 'FAC0C60E-665F-493C-9367-BFEB92664C1C'
			) as tmp
			WHERE 1=1
			<cfif listlen(local.lstExistingSubs)>
				AND subscriptionID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.lstExistingSubs#" list="true">)
			</cfif>
			<cfif listlen(arguments.newSubs)>
				and subIDrateID in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.newSubs#" list="true">)
			</cfif>
			order by subscriptionName, subscriptionID, rateName
		</cfquery>

		<cfreturn local.qryAvailableSections>
	</cffunction>

	<!--- validateSubsToExpire --->
	<cffunction name="validateSubsToExpire" access="private" output="false" returntype="struct">
		<cfargument name="subsToExpire" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfset local.strReturn.subsToExpire = arguments.subsToExpire>
		<cfif listlen(local.strReturn.subsToExpire)>
			<cfset local.strReturn.qrySubsToExpire = getCurrentSectionSubscriptions(subsToExpire=local.strReturn.subsToExpire)>
			<cfset local.strReturn.subsToExpire = valueList(local.strReturn.qrySubsToExpire.subscriberID)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<!--- validateNewSubs --->
	<cffunction name="validateNewSubs" access="private" output="false" returntype="struct">
		<cfargument name="newSubs" type="string" required="true">
		<cfargument name="subsToExpire" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfset local.strReturn.newSubsAmount = 0>
		<cfset local.strReturn.newSubs = arguments.newSubs>
		<cfif listlen(local.strReturn.newSubs)>
			<cfset local.qryCurrentSubscriptions = getCurrentSectionSubscriptions()>
			<cfset local.strReturn.qrySubsToAdd = getAvailableSections(qryCurrentSubscriptions=local.qryCurrentSubscriptions, newSubs=local.strReturn.newSubs)>
			<cfset local.strReturn.newSubs = valueList(local.strReturn.qrySubsToAdd.subIdRateId)>

			<!--- for students, first two subs are free. so change the price of them to be free if needed. --->
			<!--- this form does not give credit when removing sections, so dont take into account the ones we are removing --->
			<cfset local.secCount = local.qryCurrentSubscriptions.recordcount>
			<cfloop query="local.qryCurrentSubscriptions">
				<cfif NOT listFind(arguments.subsToExpire,local.qryCurrentSubscriptions.subscriberID) AND local.qryCurrentSubscriptions.canbefree is 1>
					<cfset local.secCount = local.secCount - 1>
				</cfif>
			</cfloop>
			<cfloop query="local.strReturn.qrySubsToAdd">
				<cfif (local.secCount lt 2 and local.strReturn.qrySubsToAdd.canbefree is 1) or (listLen(arguments.subsToExpire) gte 2 and local.strReturn.qrySubsToAdd.currentRow lte 2)>
					<cfset querySetCell(local.strReturn.qrySubsToAdd, "rateAmt", 0.00, local.strReturn.qrySubsToAdd.currentrow)>
					<cfset local.secCount = local.secCount + 1>
				</cfif>
			</cfloop>

			<cfquery name="local.qrySubsToAddTotal" dbtype="query">
				select sum(rateAmt) as total
				from [local].strReturn.qrySubsToAdd
			</cfquery>
			<cfset local.strReturn.newSubsAmount = val(local.qrySubsToAddTotal.total)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getMembershipByStatus" access="private" output="false" returntype="query">
		<cfargument name="statusCode" type="string" required="true">

		<cfset var local = structNew()>

		<!--- get active subscriptions --->
		<cfset local.qryGetSubscriptions = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberid=variables.memberIDToUse, status=arguments.statusCode, includeRate=true)>

		<!--- check for student subscription --->
		<cfquery name="local.qryStudentSub" dbtype="query">
			select *
			from [local].qryGetSubscriptions
			where upper(UID) = '#UCASE(variables.StudentDuesSubUID)#'
		</cfquery>

		<cfreturn local.qryStudentSub>
	</cffunction>

	<cffunction name="storeArgumentsInSession" access="package" output="false" returntype="void">
		<cfargument name="Input" type="struct" required="true">
		<cfparam name="session.Responses" default="#structNew()#">
		<cfset structAppend(session.Responses,arguments.Input,true)>
	</cffunction> 

	<cffunction name="deleteSubscriber" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptions">
			select subscriberID, status
			from dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">)
			where status <> 'D'
			order by thePath				
		</cfquery>
						
		<cfloop query="local.qrySubscriptions">
			<cfset local.newStatus = "">
			<cfif local.qrySubscriptions.status eq "R">
				<cfset local.newStatus = "D">
			<cfelseif local.qrySubscriptions.status eq "O">
				<cfset local.newStatus = "X">
			</cfif>

			<cfstoredproc procedure="sub_updateSubscriberStatus" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscriptions.subscriberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.newStatus#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.tmpResult">
			</cfstoredproc>
		</cfloop>
	</cffunction>

	<cffunction name="rebuildSubscriptionOffer" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="activeSubscriberID" type="numeric" required="true">
		<cfargument name="offerRescindDate" type="string" required="true">
		<cfargument name="overrideStartDate" type="date" required="true">
		<cfargument name="offeredStatus" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objSubRenew = CreateObject("component","model.admin.subscriptions.subscriptionRenew")>

		<cfset local.objSubRenew.doSubscribeRenew(subscriberID=arguments.activeSubscriberID, 
												  rescindDate=Dateformat(arguments.offerRescindDate,"m/d/yyyy"), 
												  overrideStartDate=Dateformat(arguments.overrideStartDate,"m/d/yyyy"), 
												  siteID=variables.siteID, orgID=variables.orgID, actorMemberID=arguments.memberID,
												  renewMode="customRenew")>

		<cfset local.qryRenewalNotSentStudentSub = getMembershipByStatus(statusCode='R')>

		<!--- if previous offer was Billed then advance new offer to Billed --->
		<cfif arguments.offeredStatus eq "O">
			<cfstoredproc procedure="sub_updateSubscriberStatus" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryRenewalNotSentStudentSub.subscriberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="O">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.tmpResult">
			</cfstoredproc>
		</cfif>
	</cffunction>

	<cffunction name="storeLawStudentApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument',
				parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle='Law Student Upgrade Confirmation - #DateFormat(now(),'m-d-yyyy')#', 
				docDesc='Law Student Upgrade Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
				contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

</cfcomponent>