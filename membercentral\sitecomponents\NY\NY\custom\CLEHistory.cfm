<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);

	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=CLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>

<cfelseif arguments.event.getValue('panel') eq "viewCertStore">
	<cfscript>
	// get encrypted registrantid
	local.encryptedAID = arguments.event.getValue('aid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedAID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedAID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedAID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.store.certificate").generateCertificate(affirmationID=local.decryptedAID);
	local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.strCertificate.certificatePath, displayName=ListLast(local.strCertificate.certificatePath,"/"), deleteSourceFile=1);
	</cfscript>

	<cfif not local.docResult>
		<cflocation url="/?pg=CLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>	

<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div><span class="TitleText">My CLE History</span></div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact NYSTLA for assistance.
	</div>
	</cfoutput>

<cfelse>
	<!--- CLE history based on store affirmations with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLEStore">
		SET NOCOUNT ON;

		declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
		declare @storeID int;

		SELECT @storeID = storeID
		FROM dbo.store
		WHERE siteID = @siteID;

		select @siteID as siteid, ca.affirmationid, ca.affirmationCode, ca.dateClaimed, ca.issuedByMemberID, ca.orderID, ca.productFormatID, ca.offeringID,
			ca.status, ca.assignToMemberID, spf.itemid, spf.name as formatName, cl.contentTitle as productTitle, 
			isNull(creditValue, 0.00) as creditValueAwarded, st.typeName as creditType, datepart(year,ca.dateClaimed) as CLEYear,
			m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as assigneeName
		from dbo.store_orders so
		inner join dbo.crd_affirmations ca on ca.orderid = so.orderid
		inner join dbo.crd_affirmationTypes as cat on cat.affirmationTypeID = ca.affirmationTypeID and cat.affirmationType = 'paper'
		inner join dbo.crd_requests cr on cr.affirmationID = ca.affirmationID
		inner join dbo.store_productFormats spf on spf.formatID = ca.productFormatID
		inner join dbo.store_products sp on sp.storeID = @storeID and sp.itemid = spf.itemid 
		inner join dbo.cms_contentLanguages cl on cl.contentid = sp.productContentID
		inner join dbo.ams_members m on m.memberid = ca.assignToMemberID
		inner join dbo.ams_members m2 on m2.memberid = m.activememberid	
			and m2.memberid = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER"> 
		inner join dbo.crd_offeringTypes et on ca.offeringID = et.offeringID and cr.offeringTypeID = et.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes c on c.astid = et.astid
		inner join dbo.crd_authorityTypes st on st.typeID = c.typeID
		where so.storeID = @storeID
		order by ca.dateClaimed desc;
	</cfquery>
	<cfquery name="local.qryCLEStoreTotals" dbtype="query">
		select CLEYear, creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLEStore
		group by CLEYear, creditType
		order by CLEYear, totalCLE
	</cfquery>

	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		select e.eventid, r.registrantID, r.dateRegistered, c.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,r.dateRegistered) as CLEYear
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID AND r.recordedOnSiteID = evr.siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid AND evr.siteID = e.siteID
		inner join dbo.cms_contentLanguages as c on c.contentID = e.eventContentID and c.languageID = 1
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		inner join dbo.ams_members as m on m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID
		where r.memberid = mMerged.memberID
		order by r.dateRegistered desc, e.eventid
	</cfquery>
	<cfquery name="local.qryCLETotals" dbtype="query">
		select CLEYear, creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by CLEYear, creditType
		order by CLEYear, totalCLE
	</cfquery>
	
	<!--- seminarweb history --->
	<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails('NY').qryAssociation>
	<cfset local.memberIDToUse = session.cfcUser.memberData.memberID>
	<cfset local.showSW = false>
	<cfif val(local.memberIDToUse) gt 0>
		<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="NY">
			<cfprocresult name="local.qrySWL" resultset="1">
			<cfprocresult name="local.qrySWOD" resultset="2">
			<cfprocresult name="local.qryCertPrograms" resultset="3">
		</cfstoredproc>
		<cfset local.showSW = true>
	</cfif>

	
	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<style type="text/css">
		##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		<script language="JavaScript">
			function viewEVCert(rid) {
				var certURL = '/?pg=CLEHistory&panel=viewCert&mode=stream&rid=' + rid;
				window.open(certURL,'ViewCertificate');
			}
			function viewStoreCert(aid) {
				var certURL = '/?pg=CLEHistory&panel=viewCertStore&mode=stream&aid=' + aid;
				window.open(certURL,'ViewCertificate');
			}
		</script>
		<cfif local.showSW>
			<script language="JavaScript">
			function viewCert(eId) {
				var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			</script>
		</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">
	
	<cfoutput>
	<div><span class="TitleText">My CLE History</span></div>
	<br/>
	
	<cfif local.showSW>
	
		<!--- SWL --->
		<cfif local.qrySWL.recordcount>
			<div><b>#local.qrySWP.brandSWLTab#</b></div><br/>
			<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swlhistory">
			<tr class="tsAppBodyText">
				<th width="90">Date</th>
				<th>Title</th>
				<th>Status</th>
			</tr>
			<cfloop query="local.qrySWL">
				<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWL.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
				<tr valign="top">
					<td class="tsAppBodyText">#DateFormat(local.qrySWL.dateStart,'m/d/yyyy')#</td>
					<td class="tsAppBodyText">#encodeForHTML(local.qrySWL.seminarName)#</td>
					<td class="tsAppBodyText" width="120">
						<cfif len(local.qrySWL.dateCompleted) and local.qrySWL.passed is 1 and local.qrySWL.offerCertificate>
							<a href="javascript:viewCert('#local.eID#')">View certificate(s)</a>
						<cfelseif len(local.qrySWL.dateCompleted) and local.qrySWL.passed is 1 and not local.qrySWL.offerCertificate>
							Completed
						<cfelseif len(local.qrySWL.dateCompleted) and local.qrySWL.passed is 0>
							Failed
						<cfelseif now() lt local.qrySWL.dateStart>
							Not yet begun
						<cfelse>
							Did Not Attend
						</cfif>
					</td>
				</tr>
			</cfloop>
			</table>
			<br/><br/>
		</cfif>
	
		<!--- SWOD --->
		<cfif local.qrySWOD.recordcount>
			<div><b>#local.qrySWP.brandSWODTab#</b></div><br/>
			<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swodhistory">
			<tr class="tsAppBodyText">
				<th>Title</th>
				<th>Status</th>
			</tr>
			<cfloop query="local.qrySWOD">
				<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWOD.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
				<tr valign="top">
					<td class="tsAppBodyText">#encodeForHTML(local.qrySWOD.seminarName)#</td>
					<td class="tsAppBodyText" width="140">
						<cfif len(local.qrySWOD.dateCompleted) is 0>
							<cfif local.qrySWOD.isPublished and local.qrySWOD.preReqFulfilled>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Begin</a>
							<cfelseif local.qrySWOD.isPublished>
								Awaiting Prereqs
							<cfelse>
								Not available
							</cfif>
						<cfelse>
							<cfif local.qrySWOD.isPublished>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Review</a> |
							</cfif>
							<cfif local.qrySWOD.passed and local.qrySWOD.offerCertificate>
								<a href="javascript:viewCert('#local.eID#');">Certificate</a>
							<cfelseif local.qrySWOD.passed and not local.qrySWOD.offerCertificate>
								Completed
							<cfelseif not local.qrySWOD.passed>
								Failed
							</cfif>
						</cfif>
					</td>
				</tr>
			</cfloop>
			</table>
			<br/><br/>
		</cfif>	
	
	</cfif>
	</cfoutput>
	
	<cfoutput><div><span class="myotlaInsideHeader">#local.qrySWP.brandConfTab#</span></div></cfoutput>
	
	<cfif local.qryCLE.recordcount>
	
		<cfoutput>
		<table cellpadding="2" cellspacing="0" border="0" id="myotlaLinks">
		<tr><td colspan="2" class="bodyText"><b>Credit Totals</b></td></tr>
		<cfset local.totalYear = "">
		<cfloop query="local.qryCLETotals">
			<tr valign="top">
				<td class="bodyText"><cfif local.qryCLETotals.cleYear neq local.totalYear>#local.qryCLETotals.CLEYear#<cfset local.totalYear = local.qryCLETotals.cleYear><cfelse>&nbsp;</cfif></td>
				<td class="bodyText">#local.qryCLETotals.totalCLE# #local.qryCLETotals.creditType#</td>
			</tr>
		</cfloop>
		</table>
		<br/>
	
		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory">
		<tr class="tsAppBodyText">
			<th width="90">Date</th>
			<th width="350">Title</th>
			<th colspan="2">Credit Awarded</th>
		</tr>
		</cfoutput>
		
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
	
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="bodyText">#dateformat(local.qryCLE.dateRegistered,"mm/d/yyyy")#</td>
				<td class="bodyText">#local.qryCLE.contentTitle#</td>
				<td class="bodyText"><a href="javascript:viewEVCert('#local.rID#');" title="Print Certificate"><i class="icon-print"></i></a></td>
				<td class="bodyText" nowrap><cfoutput>#local.qryCLE.creditValueAwarded# #local.qryCLE.creditType#<br/></cfoutput></td>
			</tr>	
		</cfoutput>
		
		<cfoutput>
		</table>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText">
			There are no Live Conferences & Events to display.
		</div>
		</cfoutput>
	</cfif>

	<cfoutput><br/><br/></cfoutput>

	<cfif local.qryCLEStore.recordcount>
		<cfoutput>
		<table cellpadding="2" cellspacing="0" border="0" id="myotlaLinks">
		<tr><td colspan="2" class="bodyText"><b>Store Credit Totals</b></td></tr>
		<cfset local.totalYear = "">
		<cfloop query="local.qryCLEStoreTotals">
			<tr valign="top">
				<td class="bodyText"><cfif local.qryCLEStoreTotals.cleYear neq local.totalYear>#local.qryCLEStoreTotals.CLEYear#<cfset local.totalYear = local.qryCLEStoreTotals.cleYear><cfelse>&nbsp;</cfif></td>
				<td class="bodyText">#local.qryCLEStoreTotals.totalCLE# #local.qryCLEStoreTotals.creditType#</td>
			</tr>
		</cfloop>
		</table>
		<br/>
	
		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory">
		<tr class="tsAppBodyText">
			<th width="90">Date</th>
			<th width="350">Title</th>
			<th colspan="2">Store Credit Awarded</th>
		</tr>
		</cfoutput>
		
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLEStore" group="affirmationid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.aID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLEStore.affirmationID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
	
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="bodyText">#dateformat(local.qryCLEStore.dateClaimed,"mm/d/yyyy")#</td>
				<td class="bodyText">#local.qryCLEStore.productTitle#</td>
				<td class="bodyText"><a href="javascript:viewStoreCert('#local.aID#');" title="Print Certificate"><i class="icon-print"></i></a></td>
				<td class="bodyText" nowrap><cfoutput>#local.qryCLEStore.creditValueAwarded# #local.qryCLEStore.creditType#<br/></cfoutput></td>
			</tr>	
		</cfoutput>
		
		<cfoutput>
		</table>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText">
			There are no Store CLE credits to display.
		</div>
		</cfoutput>
	</cfif>


</cfif>
