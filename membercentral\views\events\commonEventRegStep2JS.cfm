<cfsavecontent variable="local.evRegStep2JS">
	<cfoutput>
	<script type="text/javascript">
		function saveRegRate() {
			let rateID = $('input[type="radio"][name="ev_rateID"]:checked').val();
			if (rateID) {
				$('##evSelectedRegRateName').html($('label[for="ev_rateID'+rateID+'"]').html());
				$('##evSelectedRegRateMessage').html($('##ev_rateMsg'+rateID).length ? $('##ev_rateMsg'+rateID).html() : '');
				$('##evSelectedRegRatePrice').html($('##ev_rateID'+rateID).data('regratepricedisp'));
				if ($('##btnContinueRateSelection').length) $('##btnContinueRateSelection').prop('disabled',true);
				$('##evRegRateSaveLoading')
					.html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
					.load('#arguments.event.getValue('evregresourceurl')#&regaction=saves2',{ev_rateID:rateID},
						function(resp) {
							let respObj = JSON.parse(resp);
							let arrSteps = respObj.loadsteps.split(',');
							
							$('##evRegRatesContainer,##evRegRateSaveLoading').hide();
							$('##evSelectedRegRate').show(300);

							$('html, body').animate({
								scrollTop: $('##EvRegStep2').offset().top - 175
							}, 750);
							
							arrSteps.forEach(function(step,index) {
								loadEventRegSteps(step);
							});
						}
					)
					.show();
			}
		}
		function editRegStep2() {
			$('##evSelectedRegRate').hide();
			$('##evRegRatesContainer').show(300);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evRegStep2JS)#">