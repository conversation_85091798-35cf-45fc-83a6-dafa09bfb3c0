<cfset local.stepNum = 4>

<cfinclude template="navigation.cfm">
<cfinclude template="../commonJS_step4.cfm">

<cfoutput>
<div class="tsAppBodyText">
	<div class="TitleText">#attributes.data.strConfirmation.qryContributionInfo.intakeFormTitle#</div>
	<div style="margin:10px 0;">#attributes.data.strConfirmation.programConfirmContent#</div>
	
	<cfform action="##" method="GET" name="frmSendConfirmation" id="frmSendConfirmation">
		<div>
			If you have an e-mail address on file, we have already e-mailed you a copy of this confirmation.
			You may send this confirmation to another address here:
		</div>
		<div>
			<cfinput type="text" name="sendToEmail" style="width:200px;margin:0;" id="sendToEmail" placeholder="<EMAIL>" required="true" validate="regular_expression" pattern="#application.regEx.email#" message="Enter a valid e-mail address.">
			<button type="button" class="tsAppBodyButton" name="btnResendConfirmation" id="btnResendConfirmation" onClick="sendConfirmation();">Send</button>
			<span id="spanResendInd" style="display:none;"><i class="icon-spinner icon-spin"></i> Sending...</span>
			<span id="spanResendMsg" style="display:none;"></span>
		</div>
	</cfform>

	<div>#attributes.data.strConfirmation.contributionData#</div>
</div>
</cfoutput>
