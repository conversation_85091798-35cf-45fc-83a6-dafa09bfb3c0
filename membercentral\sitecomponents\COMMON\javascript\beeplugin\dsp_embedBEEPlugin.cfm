<cfsavecontent variable="local.embedPluginJS">
	<cfoutput>
	<cfif arguments.editorMode NEQ "inlineEditor">
		<script src="https://app-rsrc.getbee.io/plugin/BeePlugin.js" type="text/javascript"></script>
	</cfif>
	<script type="text/javascript">
		<cfif len(arguments.serializedJSONTemplate)>
			var #toScript(deserializeJSON(arguments.serializedJSONTemplate),"mcbeejsontemplate")#
			mcbeejsontemplate = JSON.parse(mcbeejsontemplate);
		<cfelse>
			var mcbeejsontemplate = {"template":{}};
		</cfif>

		var mc_bee_instance = mc_bee_instance || {};
		mc_bee_instance['#arguments.objName#'] = {
			token:JSON.parse('#local.token#'),
			uid:'#variables.configUID#',
			editor:{},
			container:'mc_bee_#arguments.objName#_container',
			errContainer:'mc_bee_#arguments.objName#_err',
			arrMergeCodes:[],
			jsonTemplate:mcbeejsontemplate
		};

		function initBEEPlugin_#arguments.objName#() {
			embedBEEPlugin_#arguments.objName#()
				.then(startBEEPlugin_#arguments.objName#)
/*
				Commenting loading Merge Tags out for now while we await word from Bee Fee on if our desired setup is possible
				Desired Setup: Content Dialog Merge Tags and Special Links while maintaining merge tag Smart Merge Tags and Autocomplete
				.then(loadMergeTags_#arguments.objName#)
*/				
				.catch(onErrorBEEPlugin_#arguments.objName#);
		}

		function embedBEEPlugin_#arguments.objName#() {
			return new Promise(function(resolve,reject) {
				try {
					const config = {
						uid: mc_bee_instance['#arguments.objName#'].uid,
						container: mc_bee_instance['#arguments.objName#'].container,
						trackChanges: true,
/*	commenting out SuperPowers plan features for now.
						advancedPermissions: {
							content: {
								title: {
									textEditor: {
										toolbar: [
											'bold italic underline strikethrough | superscript subscript | forecolor backcolor',
											'link unlink | specialLinks | externalItem'
										]
									},
								},
								text: {
									textEditor: {
										toolbar: [
											'fontselect fontsizeselect | bold italic underline strikethrough superscript subscript removeformat | alignleft aligncenter alignright alignjustify | charmap | undo redo',
											'numlist bullist | forecolor backcolor | ltr rtl | link unlink | specialLinks | externalItem'
										]
									},
								}
							}
						},
*/					
						<cfif local.qGetCustomFonts.recordcount>
							<cfset local.counter =0>
							editorFonts: {
								showDefaultFonts: true,
								customFonts: [
									<cfoutput query = "local.qGetCustomFonts">
										<cfset local.counter = counter +1 >
										{
											name: "#local.qGetCustomFonts.name#",
											fontFamily: "#local.qGetCustomFonts.fontFamily#",
											url: "#local.qGetCustomFonts.fontCssUrl#"
										}
										<cfif local.counter NEQ local.qGetCustomFonts.recordcount>
										,
										</cfif>
									</cfoutput>
									]
							},
						</cfif>
						contentDialog: {
							specialLinks: {
								label: 'Special Links',
								handler: function(resolve,reject) {
									showSpecialLinks_#arguments.objName#().then(function(r) {
										resolve(r);
									}).catch(function(e) {
										reject(e);
									});
								}
							}
							<cfif arguments.allowMergeCodes>
							, mergeTags: {
								label: 'Find Merge Tags',
								handler: function(resolve,reject) {
									findMergeTags_#arguments.objName#().then(function(r) {
										resolve(r);
									}).catch(function(e) {
										reject(e);
									});
								}
							}
							</cfif>
						},
						<cfif arguments.editorMode EQ "testEditor">
						onChange: function (jsonFile, response) {
							mc_bee_instance['#arguments.objName#'].jsonTemplate = JSON.parse(jsonFile);
							console.log('onChange - json', jsonFile);
							console.log('onChange - response', response);
						},
						onSave: function (jsonFile, htmlFile) {
							mc_bee_instance['#arguments.objName#'].jsonTemplate = JSON.parse(jsonFile);

							console.log('onSave - json', jsonFile);
							console.log('onSave - html', htmlFile);
						},
						onSaveAsTemplate: function (jsonFile) {
							mc_bee_instance['#arguments.objName#'].jsonTemplate = JSON.parse(jsonFile);
							console.log('onSaveAsTemplate - json', jsonFile);
						},
						onAutoSave: function (jsonFile) { 
							mc_bee_instance['#arguments.objName#'].jsonTemplate = JSON.parse(jsonFile);
							console.log('onAutoSave - json', jsonFile);
						},
						onSend: function (htmlFile) {
							console.log('onSend - html', htmlFile);
						},
						onTogglePreview:function(e) {
							console.log('onTogglePreview ', e);
						},
						<cfelse>
						onChange: function (jsonFile, response) {
							mc_bee_instance['#arguments.objName#'].jsonTemplate = JSON.parse(jsonFile);
						},
						onSave: function (jsonFile, htmlFile) {
							mc_bee_instance['#arguments.objName#'].jsonTemplate = JSON.parse(jsonFile);
							if (typeof window['#arguments.objName#_save'] == 'function') {
								window['#arguments.objName#_save'](jsonFile, htmlFile);
							}
						},
						</cfif>
						onLoad: function(jsonFile) {
							if ($('###arguments.objName#Overlay').length) 
								$('###arguments.objName#Overlay').html('').addClass('d-none');
						},
						onError: function (errorMessage) {
							console.log('onError ', errorMessage);
						}
					};

					window.BeePlugin.create(mc_bee_instance['#arguments.objName#'].token, config, function(instance) {
						resolve(instance);
					});
				} catch(e) {
					reject(e,'Unable to embed BEE Plugin.');
				}
			});
		}

		function startBEEPlugin_#arguments.objName#(instance) {
			mc_bee_instance['#arguments.objName#'].editor = instance;
			mc_bee_instance['#arguments.objName#'].editor.start(mc_bee_instance['#arguments.objName#'].jsonTemplate);
		}

		function loadMergeTags_#arguments.objName#() {
			return new Promise(function(resolve,reject) {
				let getMergeTagResults = function(r) {
					if (r.success) {
						mc_bee_instance['#arguments.objName#'].arrMergeCodes = r.arrMergeCodes;
						let mergeTags = [];
						$.each(r.arrMergeCodes, function(i,thisArea) {
							mergeTags = mergeTags.concat(thisArea.arrcodes);
						});
						mc_bee_instance['#arguments.objName#'].editor.loadConfig({
							mergeTags:mergeTags
						});
						resolve(r);
					} else {
						reject(r,'We were unable to load Merge Tags.');
					}
				};
				$.getJSON('/?event=proxy.ts_json&c=CONTENT&m=getMergeTags', {})
					.done(getMergeTagResults)
					.fail(getMergeTagResults);
			});
		}

		function onErrorBEEPlugin_#arguments.objName#(err,msg) {
			console.log('error',err);
			if (msg && msg.length) {
				mca_showAlert(mc_bee_instance['#arguments.objName#'].errContainer,msg);
			}
		}

		/* preview member selector */
		function choosePreviewMember_#arguments.objName#() {
			var selhref = '#local.memSelectLink#&fldName=mc_bee_#arguments.objName#_previewMemberID&retFunction=top.onSelectPreviewMember_#arguments.objName#&dispTitle=' + escape('Choose Member');
			$.colorbox( {innerWidth:660, innerHeight:560, href:selhref, iframe:true, overlayClose:false} );
		}
		function onSelectPreviewMember_#arguments.objName#(fldID,mID,mNum,mName) {
			var fldName = $('##mc_bee_#arguments.objName#_previewMemberName');
			$('##'+fldID).val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				fldName.html(mName + ' (' + mNum + ')');
				$('.mc_bee_#arguments.objName#_prevMember').removeClass('d-none');
			} else {
				fldName.html('');
				$('.mc_bee_#arguments.objName#_prevMember').addClass('d-none');
			}
			$('input.#arguments.objName#PreviewField[data-previewtemplatefield="memberID"]').val(mID);

			previewTemplate_#arguments.objName#();
		}
		function clearSelectedMember_#arguments.objName#() {
			$('##mc_bee_#arguments.objName#_previewMemberID').val(0);
			$('input.#arguments.objName#PreviewField[data-previewtemplatefield="memberID"]').val(0);
			$('##mc_bee_#arguments.objName#_previewMemberName').val("");
			$('.mc_bee_#arguments.objName#_prevMember').addClass('d-none');
		}
		function previewTemplate_#arguments.objName#() {
			if ($('##mc_bee_#arguments.objName#_previewMemberID').val() == 0) {
				mca_showAlert(mc_bee_instance['#arguments.objName#'].errContainer,"No Member Selected.");
				return false;
			} else {
				mca_hideAlert(mc_bee_instance['#arguments.objName#'].errContainer);
			}

			let apiActionLink = '#local.callBeePluginContentAPILink#';
			if ($('##mc_bee_#arguments.objName#_previewMemberID').val() > 0) {
				apiActionLink += '&ovMemberIDForMergeCodes='+$('##mc_bee_#arguments.objName#_previewMemberID').val();
			}

			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Preview Template',
				strmodalbody: {
					content: mca_getLoadingHTML()
				}
			});
			$('##MCModalBody').html('<iframe name="MCModalBodyIframe" id="MCModalBodyIframe" class="w-100 h-100 border-0 jvectormap-spinner"></iframe>');
			
			$('##MCModalBodyIframe')
				.load(apiActionLink, { apiAction:'html', jsonTemplateStringified:JSON.stringify(mc_bee_instance['#arguments.objName#'].jsonTemplate) },
					function(r) {
						mca_hideAlert(mc_bee_instance['#arguments.objName#'].errContainer);
						let responseObj = JSON.parse(r);
						if (responseObj.success) {
							let msgFrame = frames['MCModalBodyIframe'].document;
							msgFrame.open();
							msgFrame.write(responseObj.html);
							msgFrame.close();
							$('##MCModalBodyIframe').removeClass('jvectormap-spinner');
						} else {
							mca_showAlert(mc_bee_instance['#arguments.objName#'].errContainer,responseObj.errmsg);
						}
					});

		}
		function closeBox() { $.colorbox.close(); }

		<cfif arguments.editorMode EQ "testEditor">
		/* api action */
		function mc_bee_apiAction_#arguments.objName#(action) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'API Action - ' + action.toUpperCase(),
				strmodalbody: {
					content: mca_getLoadingHTML()
				}
			});
			$('##MCModalBody').html('<iframe name="MCModalBodyIframe" id="MCModalBodyIframe" class="w-100 h-100 border-0 jvectormap-spinner"></iframe>');

			let apiActionLink = '#local.callBeePluginContentAPILink#';
			if ($('##mc_bee_#arguments.objName#_previewMemberID').val() > 0) {
				apiActionLink += '&ovMemberIDForMergeCodes='+$('##mc_bee_#arguments.objName#_previewMemberID').val();
			}

			$('##MCModalBodyIframe')
				.load(apiActionLink, { apiAction:action, jsonTemplateStringified:JSON.stringify(mc_bee_instance['#arguments.objName#'].jsonTemplate) },
					function(r) {
						mca_hideAlert(mc_bee_instance['#arguments.objName#'].errContainer);
						let responseObj = JSON.parse(r);
						if (responseObj.success) {
							let msgFrame = frames['MCModalBodyIframe'].document;
							switch(action) {
								case "html":
									msgFrame.open();
									msgFrame.write(responseObj.html);
									msgFrame.close();
									break;
								case "pdf":
									$('##MCModalBodyIframe').attr('src',responseObj.pdfurl);
									break;
								case "image":
									let imgHTML = '<img src="data:image/png;base64,'+responseObj.imgbase64encoded+'" />';
									msgFrame.open();
									msgFrame.write(imgHTML);
									msgFrame.close();
									break;
							};
							$('##MCModalBodyIframe').removeClass('jvectormap-spinner');
						} else {
							mca_showAlert(mc_bee_instance['#arguments.objName#'].errContainer,responseObj.errmsg);
						}
					});
		}
		</cfif>
		function showSpecialLinks_#arguments.objName#() {
			return new Promise(function(resolve,reject) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'Insert a Special Link',
					contenturl: '/?pg=contentEditor&ceAction=showSpecialLinks&mode=stream',
					strmodalbody: {
						classlist: 'p-0',
						content: mca_getLoadingHTML()
					},
					strmodalfooter : {
						classlist: 'd-none',
						showextrabutton: true
					}
				});

				$('##btnMCModalSave').off('click');
				$('##btnMCModalSave').on('click',function() {
					let strSelectedLink = {label:$(this).data('label'), link:$(this).data('link'), type:$(this).data('type')};
					resolve(strSelectedLink);
					MCModalUtils.hideModal();
				});

				$('##MCModal [data-dismiss="modal"]').off('click');
				$('##MCModal [data-dismiss="modal"]').on('click',function() {
					reject({warn:false});
				});
			});
		}
		<cfif arguments.allowMergeCodes>
			<cfif len(arguments.mergeCodeList)>
				<cfset arguments.mergeCodeList = "&" & listChangeDelims(arguments.mergeCodeList, "=1&") & "=1">
			</cfif>
			function findMergeTags_#arguments.objName#() {
				return new Promise(function(resolve,reject) {
					MCModalUtils.showModal({
						isslideout: true,
						size: 'lg',
						modaloptions: {
							backdrop: 'static',
							keyboard: false
						},
						title: 'Find Merge Tag',
						contenturl: '/?pg=contentEditor&ceAction=showMergeCodeInstructions&usageApp=emailEditor&mode=stream#arguments.mergeCodeList#',
						strmodalbody: {
							classlist: 'p-0',
							content: mca_getLoadingHTML()
						},
						strmodalfooter : {
							classlist: 'd-none',
							showextrabutton: true
						}
					});

					$('##btnMCModalSave').off('click');
					$('##btnMCModalSave').on('click',function() {
						let strSelectedMergeTag = {name:$(this).data('name'), value:$(this).data('value') };
						if ($(this).data('previewvalue')) {
							strSelectedMergeTag.previewValue = $(this).data('previewvalue');
						}
						resolve(strSelectedMergeTag);
						MCModalUtils.hideModal();
					});

					$('##MCModal [data-dismiss="modal"]').off('click');
					$('##MCModal [data-dismiss="modal"]').on('click',function() {
						reject({warn:false});
					});
				});
			}
		</cfif>
		<cfif local.allowVersioning>
			function showContentVersions_#arguments.objName#(vlimit) {
				$('##versionContainer_#arguments.objName#')
					.html('<div class="spinner-border m-2 text-primary" role="status"><span class="sr-only">Loading...</span></div>')
					.load('/?pg=contentEditor&ceAction=showVersionHistory&cid=#arguments.contentID#&lID=1&bs4=true&vlimit='+vlimit+'&mode=stream', 
							function() { 
								mca_setupScrollable($('##versionContainer_#arguments.objName#'));
								setTargetEditorInstance#arguments.contentID#('#jsStringFormat(arguments.objName)#','');
							});
			}
		</cfif>

		$(function() {
			if ($('###arguments.objName#Overlay').length) $('###arguments.objName#Overlay').html(mca_getOverlayHTML()).removeClass('d-none');
			initBEEPlugin_#arguments.objName#();
		});
	</script>
	<style type="text/css">
		##mc_bee_#arguments.objName#_container {height:#arguments.height#px;}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.embedPluginJS#">

<cfoutput>
<div id="mc_bee_#arguments.objName#_err" class="alert alert-danger d-none"></div>
<div class="row no-gutters mb-3">
	<input type="hidden" name="mc_bee_#arguments.objName#_previewMemberID" id="mc_bee_#arguments.objName#_previewMemberID" value="0">
	<div class="col-auto pr-3 d-flex">
		<cfif arguments.showMemberSelector>
			<button type="button" name="btnChooseMember" id="btnChooseMember" class="btn btn-sm btn-secondary mr-2" onclick="choosePreviewMember_#arguments.objName#();">Choose Member</button>
			<div class="mr-2 align-self-center d-none mc_bee_#arguments.objName#_prevMember">
				<div class="badge badge-primary"><span id="mc_bee_#arguments.objName#_previewMemberName"></span> <i class="fa-solid fa-minus-circle fa-md cursor-pointer" onclick="clearSelectedMember_#arguments.objName#();" title="Remove Member"></i></div>
			</div>
			<button type="button" name="btnPrevMember" id="btnPrevMember" class="btn btn-sm mr-2 btn-secondary mc_bee_#arguments.objName#_prevMember d-none" onclick="previewTemplate_#arguments.objName#();">Preview Template</button>
		</cfif>
		<cfif arguments.editorMode EQ "testEditor">
			<div class="btn-group">
				<button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
					API Action
				</button>
				<div class="dropdown-menu">
					<a class="dropdown-item" href="##" onclick="mc_bee_apiAction_#arguments.objName#('html');return false;">HTML</a>
					<a class="dropdown-item" href="##" onclick="mc_bee_apiAction_#arguments.objName#('pdf');return false;">PDF</a>
					<a class="dropdown-item" href="##" onclick="mc_bee_apiAction_#arguments.objName#('image');return false;">Image</a>
				</div>
			</div>
		</cfif>
	</div>
	<div class="col text-right">
		<cfif local.allowVersioning>
			<span id="versionContainer_#arguments.objName#" class="mx-2">
				<a href="javascript:showContentVersions_#arguments.objName#(10);" class="mx-2"><i class="far fa-regular fa-history fa-lg"></i> Version History</a>
			</span>
		</cfif>
	</div>
</div>
<div id="mc_bee_#arguments.objName#_container"></div>
</cfoutput>