<cfinclude template="../commonLoginStyles.cfm">
<cfinclude template="../commonPasswordJS.cfm">

<cfoutput>
<div id="mc_loginApp" class="mc_loginApp">
	<h3>Manage Login Settings</h3>

	<cfif arguments.event.valueExists('invpwd')>
		<div class="alert">Invalid Password. Try again.</div>
	</cfif>

	<form name="frmPassword" id="frmPassword" class="login-mt-4" method="post" action="/?pg=login&logact=manageSettings&la=vp" onsubmit="return validatePassword();">
		<div class="login-input-prepend">
			<span class="login-add-on"><i class="icon-user"></i></span>
			<input type="text" class="login-formcontrol" value="#session.cfcuser.memberData.username#" size="29" readonly>
		</div>
		<div class="login-mt-3 login-mb-1">Please re-enter your password</div>
		<div class="login-input-prepend">
			<span class="login-add-on"><i class="icon-lock"></i></span>
			<input type="password" name="mcpwd" id="mcpwd" class="login-formcontrol" value="" placeholder="Password" maxlength="100" size="25">
			<span class="login-add-on">
				<a href="##" id="mcPwdDsp" class="login-anchor" onclick="togglePwdDisplay();return false;">
					<i class="icon-eye-open"></i>
					<i class="icon-eye-close" style="display:none;"></i>
				</a>
			</span>
		</div>
		<div class="login-mt-3">
			<button type="submit" class="tsAppBodyButton">Continue</button>
		</div>
	</form>
</div>
</cfoutput>