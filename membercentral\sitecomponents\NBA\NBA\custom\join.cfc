<cfcomponent extends="model.customPage.customPage" output="false">
	<cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
	<cfargument name="Event" type="any">

	<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		variables.redirectlink = "/?pg=join";
		local.formAction = arguments.event.getValue('fa','showLookup');
		variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		
		variables.membershipCategoryFSUID = '862E5032-F8ED-4825-8083-90815FAF50DE';
		variables.lawStudentFSUID = '1ECDC204-2399-4D46-A151-42E72862E351';
		variables.contactInformationFSUID = '95E8FCEF-C885-4F54-B8A2-6599985D15B2';
		variables.businessInformationFSUID = '3930EFA9-D3EE-4341-9ADF-471254A1F4D0';
		variables.homeAddressFSUID = 'B03B71E2-9D28-4CEA-85D5-DE6FA7F43D80';
		variables.physicalAddressFSUID = '2848CCFE-3167-4A39-9187-2E6391FE05B3';
		variables.addressPreferenceSFSUID = '98C00780-F473-4AEC-9DC3-C25B4352C3A4';
		variables.profInfoFSUID = 'A7379F90-B11A-4B37-BC6A-C1FA3C923195';
		variables.demoInfoFSUID = 'ADFDEE42-CB55-4C98-99CD-EAD1EB8D6F67';
		variables.additionalInfoFSUID = '08E2AD84-AFDF-4887-92F4-B050D844C22D';
		variables.organizationGroupUID = 'D11BFA5C-F6A2-4BF5-9463-2157E8D56A498';
		
		variables.currentDate = dateTimeFormat(now());

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];

		local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Join button to the left.<br>
								Enter the search criteria and click Continue.<br>
								If you see your name, please press the Choose button next to your name.<br>
								If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="1365B828-60D1-4239-B2AE-66EC1FFBC0D8" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a Nashville Bar Association member. If you have questions about your membership, please contact <a href='mailto:<EMAIL>'><EMAIL></a>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please contact <a href='mailto:<EMAIL>'><EMAIL></a>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FormTitle", type="CONTENTOBJ", desc="Form Title at top", value="<p><span class='TitleText_black'>NBA Membership Form</span></p>" };
		local.tmpField = { name="FormTitleDescription", type="CONTENTOBJ", desc="Form Title Description at top", value="<p>Please complete/verify the following personal information. This data will be imported into our database for use in our member directory and all other communications. Be sure to fill in any blanks and make any updates necessary.</p>
		<p>You may also download our Membership Form and mail it with your payment to Nashville Bar Association. Once your application has been received and processed, you will receive a confirmation email. At that time, you will be able to set up your login information on <a href='NashvilleBar.org'>NashvilleBar.org</a> and complete your unique My NBA profile page.</p>
		" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - General information." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfessionalLicContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Please enter your bar date and license number for the state of Tennessee." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="1C4A0BD1-E04A-4770-8C35-AB16A5C029BE" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="NBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Confirmation Content", value="" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="NBA Application Receipt" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="NBA New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);   
		local.tmpField = { name="CompanyFirmSelector", type="CONTENTOBJ", desc="Company/Firm Information tool", value="Please find your company/firm in the search tool below. If you cannot find your company/firm, we will save the name that you enter on the form." };
			arrayAppend(local.arrCustomFields, local.tmpField);      
		local.tmpField = { name="NBAMemberServicesTitle",type="STRING",desc="NBA MemberServices Title",value="Get Involved" }; 
			arrayAppend(local.arrCustomFields, local.tmpField); 
		local.tmpField = { name="NBAMemberServicesDesc",type="CONTENTOBJ",desc="NBA MemberServices Description",value="Check all of interest" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);  

		for (i = 1; i <= 10; i++) {
			local.tmpField = {
				name = "NBAMemberServices_" & i,
				type = "STRING",
				desc = "NBA Member Service " & i,
				value = ""
			};
			arrayAppend(local.arrCustomFields, local.tmpField);
		}

		local.tmpField = { name="YoungLawyersLawStudentUID",type="STRING",desc="UID for Young Lawyers Division Dues for Law Students Subscription Set",value="CBBCDBF7-D1AA-4725-8D45-BE214C3BA65B" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmJoin',
				formNameDisplay=variables.strPageFields.FormTitle,
				orgEmailTo= variables.strPageFields.StaffConfirmationTo,
				memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
		));

		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;

		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemberAppHistory', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemberAppHistory', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form."
		variables.origMemberID = variables.useMID;
		if(local.formAction neq "showLookup" and 
				local.formAction neq "processLookup" and 
				structKeyExists(session, "formFields") and 
				structKeyExists(session.formFields, "step0") and 
				structKeyExists(session.formFields.step0, "memberID") and 
				int(val(session.formFields.step0.memberID)) gt 0){            
					variables.useMID = session.formFields.step0.memberID;
					if(structKeyExists(session.formFields.step0, "origMemberID") and int(val(session.formFields.step0.origMemberID)) gt 0){
						variables.origMemberID = session.formFields.step0.origMemberID;
					}              

		}else if(session.cfcuser.memberdata.identifiedAsMemberID){
				variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
				variables.origMemberID = variables.useMID;
		}
		if( local.formAction neq "showLookup" and 
				local.formAction neq "processLookup" and 
				local.formAction neq "showMemberInfo" and 
				local.formAction neq "processMemberInfo" and 
				structKeyExists(session.formFields, "step1") and 
				structKeyExists(session.formFields.step1, "useHistoryID") and 
				int(val(session.formFields.step1.useHistoryID)) gt 0){
					variables.useHistoryID = int(val(session.formFields.step1.useHistoryID));
		}

		local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
		local.subStatus = "";
		if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
			local.subStatus = hasSub(int(variables.useMID));
		}

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		if(local.isSuperUser){
			local.returnHTML = showError(errorCode='admin');  
		}else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
			local.returnHTML = showError(errorCode=local.subStatus);  
		}else{
			switch (local.formAction) {
				case "processLookup":
					switch (processLookup(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMemberInfo();
							break;		
						default:
							local.returnHTML = showError(errorCode='error');
							break;				
					}
					break;
				case "processMemberInfo":
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo();
							break;
						case "spam":
							local.returnHTML = showError(errorCode='spam');
							break;
						default:
							local.returnHTML = showError(errorCode='error');
							break;				
					}
					break;
				case "processMembershipInfo":
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment();
							break;
						default:
							local.returnHTML = showError(errorCode='error');
							break;				
					}
					break;                   
				case "processPayment":
					local.processStatus = processPayment(event=arguments.event);
					switch (local.processStatus) {
						case "success":
							local.returnHTML = showConfirmation();
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo();
					break;	
				case "showMemberInfo":
					local.returnHTML = showMemberInfo();
					break;                    		
				default:
					if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
					if(structKeyExists(session, "formFields")) structDelete(session, "formFields");
					local.returnHTML = showLookup();
					break;
			}
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");        
	</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>

		<!--- get identified memberid if available to bypass lookup --->
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
            #variables.pageJS# 
			<style type="text/css">
			.tab-content{padding: 1.8rem 1.35rem;}
			</style>

			<script type="text/javascript">

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				
				function addMember(memObj) {
					assignMemberData(memObj);
				}
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				
				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
						if(step==1)
							$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
						if(step==2)
							$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");                            

						mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
						}
					} else {
						step = 1;
					}
					prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
					<cfinput type="hidden" name="fa" id="fa" value="processLookup">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	
					<cfif len(variables.strPageFields.FormTitleDescription)>
						<div class="row-fluid" id="FormTitleDesc">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitleDescription#</span>
							</div>
						</div>
					</cfif>	

				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>	
			</cfform>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step0")>
			<cfset structDelete(session.formFields, "step0")>
		</cfif>	

		<cfset session.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        

		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		
		<cfset local.fieldSetUIDlist = '#variables.membershipCategoryFSUID#,#variables.lawStudentFSUID#,#variables.contactInformationFSUID#,#variables.businessInformationFSUID#,#variables.homeAddressFSUID#,#variables.physicalAddressFSUID#,#variables.addressPreferencesFSUID#,#variables.profInfoFSUID#,#variables.demoInfoFSUID#,#variables.additionalInfoFSUID#'>
		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>
	
		<cfset local.memberTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.companyField = {fieldCode="",fieldLabel=""}>
		
		<cfset local.designatedBillingAddressTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.designatedMailingAddressTypeField = {fieldCode="",fieldLabel=""}>

		<cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		
		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
			<cfset local.memberTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Membership Category']")/>
			<cfif arrayLen(local.memberTypeData)>				
				<cfset local.memberTypeField.fieldCode = local.memberTypeData[1].XmlAttributes.fieldCode>
				<cfset local.memberTypeField.fieldLabel = local.memberTypeData[1].XmlAttributes.fieldLabel>
			</cfif> 
			<cfset local.companyData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='company']")/>
			<cfif arrayLen(local.companyData)>				
				<cfset local.companyField.fieldCode = local.companyData[1].XmlAttributes.fieldCode>
				<cfset local.companyField.fieldLabel = local.companyData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.designatedBillingAddressTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Designated Billing_addressType']")/>
			<cfif arrayLen(local.designatedBillingAddressTypeData)>				
				<cfset local.designatedBillingAddressTypeField.fieldCode = local.designatedBillingAddressTypeData[1].XmlAttributes.fieldCode>
				<cfset local.designatedBillingAddressTypeField.fieldLabel = local.designatedBillingAddressTypeData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.designatedMailingAddressTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Designated Mailing_addressType']")/>
			<cfif arrayLen(local.designatedMailingAddressTypeData)>				
				<cfset local.designatedMailingAddressTypeField.fieldCode = local.designatedMailingAddressTypeData[1].XmlAttributes.fieldCode>
				<cfset local.designatedMailingAddressTypeField.fieldLabel = local.designatedMailingAddressTypeData[1].XmlAttributes.fieldLabel>
			</cfif>
		
			<cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.membershipCategoryFSUID#,#variables.lawStudentFSUID#,#variables.contactInformationFSUID#,#variables.businessInformationFSUID#,#variables.homeAddressFSUID#,#variables.physicalAddressFSUID#,#variables.addressPreferencesFSUID#,#variables.profInfoFSUID#,#variables.demoInfoFSUID#,#variables.additionalInfoFSUID#',local.fieldSetUid)>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
			</cfif>                
		</cfloop>
		<cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
		<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID = variables.orgid)>

		<cfset local.membershipCategoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.membershipCategoryFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.lawStudentFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.lawStudentFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.businessInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.businessInformationFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.physicalAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.physicalAddressFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferencesFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.profInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.profInfoFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.demoInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demoInfoFSUID, mode="collection", strData=local.strData.one)>
		<cfset local.additionalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalInfoFSUID, mode="collection", strData=local.strData.one)>

		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>	
		</cfif>

		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset local.index = 1>
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
			<cfset local.index = local.index + 1>
		</cfloop> 
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>

		<cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID='#variables.organizationGroupUID#', orgID=variables.orgID)>
		<cfquery name="local.qryCompanyByGroup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT distinct m.memberNumber, m.memberID, m.company, m.firstName, m.lastName, m.memberNumber
			FROM dbo.cache_members_groups AS mg
			INNER JOIN dbo.ams_groups AS g ON g.groupID = mg.groupID
			INNER JOIN dbo.ams_members AS m ON m.memberID = mg.memberID
			WHERE mg.orgID = <cfqueryparam value="#variables.orgID#" cfsqltype="CF_SQL_INTEGER">
			and g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
			AND g.status = 'A' AND ISNULL(m.company,'') <> '' ORDER BY m.company

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfquery name="local.qryLinkedParentCompany" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

			SELECT parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber
			FROM dbo.ams_members AS childMember
			INNER JOIN dbo.ams_recordRelationships AS rr ON rr.orgID = @orgID and rr.childMemberID = childMember.memberID and rr.isActive = 1
			INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
			INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
			INNER JOIN dbo.ams_members AS parentMember ON parentMember.orgID = @orgID and parentMember.memberID = rr.masterMemberID
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID and mg.memberID = parentMember.memberID
			INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = mg.groupID
			WHERE childMember.memberID = <cfqueryparam value="#local.strData.one.memberID#" cfsqltype="CF_SQL_INTEGER">
			/*AND rrt.relationshipTypeCode = 'Staff' */
			AND g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}
				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
				div.alert-danger{padding: 10px !important;}
				.ui-multiselect-filter{width:86%;}		
				.ui-multiselect-filter input{margin:5px;}
				.ui-multiselect-filter-label{margin:0;}
				.ui-icon-triangle-1-s { background-position: -65px -11px;}
			</style>
			<script src="/javascript/widget.min.js"></script>
			<link href="/css/jquery.multiselect.filter.css" rel="stylesheet" type="text/css">
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}
				function changeCompany(value,text,firstName,lastName,memberNumber) {					
					if(value != "Please Select"){
						$(".businessInformationFieldSetHolder").html($(".businessInformationFieldSetWrapper").html());
						if(text.length==0 && $(".businessInformationFieldSetHolder ##m_company").length && $(".businessInformationFieldSetHolder ##m_company").val().length){
							
						}else{
							$(".businessInformationFieldSetHolder ##m_company").val(text);
						}
						
						$("##orgMemberID").val(value);
						$("##orgCompanyName").val(text);						
						$("##orgFirstName").val(firstName);
						$("##orgLastName").val(lastName);
						$("##orgMemberNumber").val(memberNumber);
						$(".businessInformationFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">New Company/Firm</a>');
						$(".organizationsFirmsHolder").hide();
						$("##changeCompany").unbind("click");
						$("##changeCompany").click(function(){
							$(".organizationsFirmsHolder").show();
						});
						$("##m_company").unbind("change");
						$("##m_company").on('change', function() {		
							var company = $('##companyField option').filter(function () { return $(this).html() == $("##m_company").val(); });					
							if(company.length){
								$("##orgMemberID").val(company.val());
								$("##orgCompanyName").val(company.text());
								$("##orgFirstName").val(company.attr("firstName"));
								$("##orgLastName").val(company.attr("lastName"));
								$("##orgMemberNumber").val(company.attr("memberNumber"));
							}else{								
								$("##orgMemberID").val(0);
								$("##orgCompanyName").val('');
								$("##orgFirstName").val('');
								$("##orgLastName").val('');
								$("##orgMemberNumber").val('');
							}
						});
					}else{
						if(text.length==0 && $(".businessInformationFieldSetHolder ##m_company").length && $(".businessInformationFieldSetHolder ##m_company").val().length){

						}else{
							$(".businessInformationFieldSetHolder").html('');
						}						
						$("##orgMemberID").val(0);
						$("##orgCompanyName").val('');
						$("##orgFirstName").val('');
						$("##orgLastName").val('');
						$("##orgMemberNumber").val('');
					}
				}
				function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					$(".mpl_pltypeid").parent().parent().children().first().text('');
					
					if(memType.indexOf('Law Student (Currently Enrolled in a Law School)') != -1){
						showFieldsByContainerClass('lawStudentFieldSet');
						resetProfessionalLicenses();
						resetFormFieldsByContainerClass('profInfoFieldSetHolder');
					}else if(memType.indexOf('Associate (Paralegal or Legal Assistant)') != -1){
						resetFormFieldsByContainerClass('profInfoFieldSetHolder');
						resetProfessionalLicenses();
						$('.professionalLicensesHolder').hide();	
					} else {
						showFieldsByContainerClass('profInfoFieldSet');
						resetFormFieldsByContainerClass('lawStudentFieldSetHolder');
						if(memType.indexOf('Associate (Paralegal or Legal Assistant)') != -1){
							resetProfessionalLicenses();
						} else
						$('.professionalLicensesHolder').show();	
					}
					reloadElements();
				}
				function reloadElements(){
					$('.lawStudentFieldSetHolder input,.lawStudentFieldSetHolder select,.profInfoFieldSetHolder input,.profInfoFieldSetHolder select').each(function(){
						var _this = $(this);
						var _function = _this.data('function');
						
						if(typeof _function != "undefined" &&_function.includes('multiSelect')){
							_this.next().remove();
							eval(_function)();
						} else if(typeof _function != 'undefined' && _function.includes('readyPicker'))
							mca_setupDatePickerField(_this.attr('id'));
					});
				}
				function showFieldsByContainerClass(classList){
					$(".professionalLicensesHolder").hide();
					
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());							
						}			
					});							
				}
				function showFieldByFieldList(fieldName) {
					var _CF_this = document.forms['#variables.formName#'];
					var fieldNameListArray = (fieldName).split(",");
					$.each(fieldNameListArray,function(i){	
						$(_CF_this[fieldNameListArray[i]]).parents('tr').show();
					});			
				}
				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]).html('');
						});
					}
				}
				function resetFormFieldByFieldList(fieldName){
					var _CF_this = document.forms['#variables.formName#'];
					if(fieldName.length){
						var fieldNameListArray = (fieldName).split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}else{
						var fieldNameListArray=("").split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}
				}
				function resetProfessionalLicenses(){
					$('.mpl_pltypeid').multiselect("uncheckAll"); 
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}
				}
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				function validateMemberInfoForm(){
					
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();	
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					
					#local.membershipCategoryFieldSet.jsValidation#

					if(memType.length > 0 && (memType.indexOf('Attorney') != -1  || memType.indexOf('Emeritus (50+ years of Legal Service)') != -1)){
						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();    
									if($("##mpl_"+val+"_licenseNumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' #variables.strProfLicenseLabels.profLicenseNumberLabel#.'; }                                
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' #variables.strProfLicenseLabels.profLicenseDateLabel#.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}
					}

					if(memType.length > 0 && (memType.indexOf('Law Student (Currently Enrolled in a Law School)') != -1)){
						#local.lawStudentFieldSet.jsValidation#
					}

					#local.contactInformationFieldSet.jsValidation#
					#local.businessInformationFieldSet.jsValidation#
					#local.homeAddressFieldSet.jsValidation#
					#local.physicalAddressFieldSet.jsValidation#
					#local.addressPreferencesFieldSet.jsValidation#

					if(memType.length > 0 && (memType.indexOf('Law Student (Currently Enrolled in a Law School)') != -1 && memType.indexOf('Associate (Paralegal or Legal Assistant)') != -1)){
						#local.profInfoFieldSet.jsValidation#
					}
						

					#local.demoInfoFieldSet.jsValidation#
					#local.additionalInfoFieldSet.jsValidation#	
					
					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
					$("##state_table").show();
					if(status == ''){
						status = 'Active';
					}
					strOption = '';
					<cfloop collection="#local.licenseStatus#" item="local.i" >
						strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
					</cfloop>
					if(isChecked){
						$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
									'<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
									'<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
									'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
									'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10"></div>'+
									'<div class="span3" ><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
									'</div>');
						$('##mpl_'+val+'_status').val(status);
						mca_setupDatePickerField('mpl_'+val+'_activeDate');
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
					}									
					else{
						$("##tr_state_"+val).remove();								
					}
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}	
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strData.one#" item="local.thisKey">
						<cfif FindNoCase("m_",local.thisKey)
							or FindNoCase("ma_",local.thisKey) 
							or FindNoCase("mat_",local.thisKey)
							or FindNoCase("met_",local.thisKey) 
							or FindNoCase("me_",local.thisKey) 
							or FindNoCase("mpl_",local.thisKey)							
							or FindNoCase("mp_",local.thisKey) 
							or FindNoCase("mw_",local.thisKey) 
							or FindNoCase("md_",local.thisKey)
							or FindNoCase("mccf_",local.thisKey)>
							#toScript(local.strData.one[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfif>
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key) && $("###variables.formName# [name='"+key+"']").length) { 
								if($("###variables.formName# [name='"+key+"']").prop("tagName").toLowerCase() == "input" && $("###variables.formName# [name='"+key+"']").attr("type").toLowerCase() == "radio"){

									$("###variables.formName# [name='"+key+"'][value='"+objPrefill[key]+"']").prop("checked", true);

								}else if($("###variables.formName# [name='"+key+"']").prop("tagName").toLowerCase() == "select" && $("###variables.formName# [name='"+key+"']").prop('multiple')){
									var selectString = objPrefill[key];
									$.each(selectString.split(','), function(){
										$("###variables.formName# [name='"+key+"'] option[value='"+this+"']").prop('selected', true);
									});
									$("###variables.formName# [name='"+key+"']").multiselect("refresh");
								}else{
									$("###variables.formName# [name='"+key+"']").val(objPrefill[key]);
								}
						}
					}
				}
				$(document).ready(function() {	

					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>		

					<cfif structKeyExists(local.strData.one, "orgMemberID") and val(local.strData.one["orgMemberID"]) NEQ 0>
						$("##orgMemberID").val('#val(local.strData.one["orgMemberID"])#');
						$("##orgCompanyName").val('#local.strData.one["orgCompanyName"]#');
						$("##orgFirstName").val('#local.strData.one["orgFirstName"]#');
						$("##orgLastName").val('#local.strData.one["orgLastName"]#');
						$("##orgMemberNumber").val('#local.strData.one["orgMemberNumber"]#');
					</cfif>

					$(".businessInformationFieldSetHolder").html($(".businessInformationFieldSetWrapper").html());
					$(".businessInformationFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">New Company/Firm</a>');					

					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					</cfif>				

					$("##companyField").multiselect({
						header: "",
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							var companyOption = $("option[value='"+ui.value+"']",this);
							var firstName = "";
							var lastName = "";
							var memberNumber = "";
							if(companyOption.length){
								firstName = companyOption.attr("firstName");
								lastName = companyOption.attr("lastName");
								memberNumber = companyOption.attr("memberNumber");
							}
							changeCompany(ui.value,ui.text,firstName,lastName,memberNumber);
						}
					}).multiselectfilter();

					prefillData();
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>

					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' Please Select ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
					
					var orgMemberID = $("##orgMemberID").val();
					var firstName = $("##orgFirstName").val();
					var lastName = $("##orgLastName").val();
					var memberNumber = $("##orgMemberNumber").val();
					var orgCompanyName = $("##orgCompanyName").val();					
					if(parseInt(orgMemberID) == 0){
						orgMemberID = "Please Select";
						firstName = "";
						lastName = "";
						memberNumber = "";
						orgCompanyName = "";
					}
					changeCompany(orgMemberID,orgCompanyName,firstName,lastName,memberNumber);

					$(".mpl_pltypeid").multiselect("refresh");
					$("##companyField").multiselect("refresh");
					
					$("##newCompany").click(function(){
						changeCompany(0,'','','');
					});

					if($("##m_company").length && $("##m_company").val().length){
						$("##m_company").trigger('change');
					}
					
					var memberTypeField = $('##'+"#local.memberTypeField.fieldCode#");	
					$(memberTypeField).change(adjustFieldsetDisplay);
					$(memberTypeField).trigger('change');
					$(".businessInformationFieldSetHolder").html($(".businessInformationFieldSetWrapper").html());
					if($("##m_company").length && $("##m_company").val().length){
						$(".businessInformationFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">New Company/Firm</a>');					
					}
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<cfif local.qryLinkedParentCompany.recordCount>
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="#local.qryLinkedParentCompany.memberID#">
						<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="#local.qryLinkedParentCompany.company#">
						<input type="hidden" name="orgFirstName" id="orgFirstName" value="#local.qryLinkedParentCompany.firstName#">
						<input type="hidden" name="orgLastName" id="orgLastName" value="#local.qryLinkedParentCompany.lastName#">
						<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="#local.qryLinkedParentCompany.memberNumber#">
					<cfelse>
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">
						<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="">
						<input type="hidden" name="orgFirstName" id="orgFirstName" value="">
						<input type="hidden" name="orgLastName" id="orgLastName" value="">
						<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="">
					</cfif>
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>		
					<cfif len(variables.strPageFields.FormTitleDescription)>
						<div class="row-fluid" id="FormTitleDes">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitleDescription#</span>
							</div>
						</div>
					</cfif>		

					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif> 

						<span class="membershipCategoryFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.membershipCategoryFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">							
									#local.membershipCategoryFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>	

						<span class="professionalLicensesHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">Professional License Information</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">
									<p class="tsAppBodyText">#variables.strPageFields.ProfessionalLicContent#</p>									
									<table cellpadding="3" border="0" cellspacing="0" >									
										<tr align="top">
											<td class="tsAppBodyText" width="10">&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Professional License</td>
											<td class="tsAppBodyText">
												<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
													<cfloop query="local.qryOrgPlTypes">	
														<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif> <cfif local.qryOrgPlTypes.PLName EQ "Pennsylvania">required="true"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="top">
											<td class="tsAppBodyText" width="10"></td>
											<td class="tsAppBodyText"></td>
											<td class="tsAppBodyText"></td>
										</tr>
									</table>
									<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
										<tr>
											<td>
												<div class="row-fluid hide" id="state_table">
													<div class="span3 proLicenseLabel">
														<b>Type</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseDateLabel#</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
													</div>
												</div>
												<span id="selectedLicense">
												</span>
											</td>
										</tr>					
									</table>
								</div>
							</div>
						</span>

						<span class="lawStudentFieldSetHolder">
						</span>

						<span class="contactInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.contactInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.contactInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<span class="organizationsFirmsHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">Company/Firm Information</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">	
									<p class="tsAppBodyText">#variables.strPageFields.CompanyFirmSelector#</p>	
									<table cellpadding="3" border="0" cellspacing="0" >									
										<tr align="top">
											<td class="tsAppBodyText" width="10">&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Firm Lookup</td>
											<td class="tsAppBodyText">											
												<select name="companyField" class="companyField" id="companyField">
													<option "">Please Select</option>
													<cfloop query="local.qryCompanyByGroup">
														<option value="#local.qryCompanyByGroup.memberID#" firstName="#local.qryCompanyByGroup.firstName#" lastname="#local.qryCompanyByGroup.lastName#" memberNumber="#local.qryCompanyByGroup.memberNumber#"  <cfif local.qryLinkedParentCompany.recordCount and local.qryLinkedParentCompany.company EQ local.qryCompanyByGroup.company>Selected</cfif>>#local.qryCompanyByGroup.company#</option>
													</cfloop>
												</select>
												<a href="javascript:void(0)" id="newCompany"> New Company/Firm</a>
											</td>
										</tr>
									</table>									
								</div>
							</div>
						</span>	

						<span class="businessInformationFieldSetHolder">
						</span>
						
						<span class="homeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.homeAddressFieldSet.fieldSetContent#									
								</div>
							</div>							
						</span>

						<span class="physicalAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.physicalAddressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.physicalAddressFieldSet.fieldSetContent#									
								</div>
							</div>							
						</span>

						<span class="addressPreferencesFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressPreferencesFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.addressPreferencesFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<span class="profInfoFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.profInfoFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.profInfoFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<span class="demoInfoFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.demoInfoFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.demoInfoFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<cfset local.cntNBAMemberServices = ''>
						<cfloop from="1" to="10" index="local.idx">
							<cfif structKeyExists(variables.strPageFields,"NBAMemberServices_" & local.idx)>
								<cfset local.arrNBAMemberServices = ListToArray(variables.strPageFields["NBAMemberServices_" & local.idx], "|")>
								<cfif ArrayLen(local.arrNBAMemberServices)>
									<cfif len(variables.strPageFields["NBAMemberServices_" & local.idx])>
										<cfset local.serviceArr = ''>
										<cfif structKeyExists(local.strData.one,"memberservices")>
											<cfset local.serviceArr = local.strData.one["memberservices"]>
										</cfif>
										<cfset local.NBAMemberServicesTxt = local.arrNBAMemberServices[1]>
										<cfset local.validEntry = 1>
										<cfif ArrayLen(local.arrNBAMemberServices) GTE 2>							
											<cfset local.NBAMemberServicesCode = local.arrNBAMemberServices[2]>
										<cfelse>
											<cfset local.validEntry = 0>
										</cfif>
										<cfif local.validEntry eq 1>
											<cfset local.checked="">
											<cfif ListContains(local.serviceArr, local.NBAMemberServicesCode)>
												<cfset local.checked="checked">
											</cfif>
											<cfset local.cntNBAMemberServices &= '<label class="tsAppBodyText"><input type="checkbox" name="memberservices" id="#local.NBAMemberServicesCode#_#local.idx#" value="#local.NBAMemberServicesCode#" #local.checked# >#local.NBAMemberServicesTxt#</label>'>
										</cfif>
										
									</cfif>
								</cfif>
							</cfif>
						</cfloop>
						<cfif len(local.cntNBAMemberServices)>
								<span class="memberServicesFieldSetHolder">
									<div class="row-fluid">
										<div class="tsAppSectionHeading">#variables.strPageFields.NBAMemberServicesTitle#</div>								
										<div class="tsAppSectionContentContainer">	
											<div class="BodyText_blue" style=" margin-bottom: 10px;margin-top: -20px;">#variables.strPageFields.NBAMemberServicesDesc#</div>									
											#local.cntNBAMemberServices#
										</div>
									</div>
								</span>
						</cfif>

						<span class="additionalInfoFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.additionalInfoFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.additionalInfoFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>

				<span class="lawStudentFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.lawStudentFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
								#local.lawStudentFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>

				<span class="profInfoFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.profInfoFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.profInfoFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>

				<span class="businessInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.businessInformationFieldSet.fieldSetTitle#</div>								
						<div class="tsAppSectionContentContainer">										
							#local.businessInformationFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>

				#application.objWebEditor.showEditorHeadScripts()#

				<script language="javascript">
					$(document).ready(function(){						
						<cfloop query="local.qryOrgAddressTypes">                       
							<cfif ListFindNoCase('Business Address,Physical Address,Home Address',local.qryOrgAddressTypes.addressType)>
								function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _CF_this = document.forms['#variables.formName#'];
									var _address = _this.val();		
									if(_address.length > 0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
									}
								}

								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));

								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});

							<cfelse>
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							</cfif>
							
						</cfloop>
					});

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>		

		<cfset local.response = "failure">

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>			

		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>	
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>

		<!--- save member info and record history --->		
		<cfset local.memberShipCategoryColumnName = "Membership Category">
		<cfset local.memberShipCategoryStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName=local.memberShipCategoryColumnName)>
		<cfset local.membershipCategoryColumnId = local.memberShipCategoryStruct.columnId>
		<cfif local.membershipCategoryColumnId NEQ '' AND local.membershipCategoryColumnId NEQ 0>
			<cfset local.membershipCategorySelected = arguments.rc['md_'&local.membershipCategoryColumnId]>
			<cfset local.membershipCategorySelectedValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(variables.orgid,local.memberShipCategoryColumnName,local.membershipCategorySelected)>


			<cfif findNoCase("Law Student (Currently Enrolled in a Law School)",local.membershipCategorySelectedValue) GT 0>
				<cfset local.contactTypeValue = "Law Student">
			<cfelseif findNoCase("Attorney",local.membershipCategorySelectedValue) GT 0>
				<cfset local.contactTypeValue = "Attorney">
			<cfelseif findNoCase("Associate (Paralegal or Legal Assistant)",local.membershipCategorySelectedValue) GT 0>
				<cfset local.contactTypeValue = "Paralegal">
			<cfelseif findNoCase("Emeritus (50+ years of Legal Service)",local.membershipCategorySelectedValue) GT 0>
				<cfset local.contactTypeValue = "Emeritus">
			</cfif>
		</cfif>

		<cfif variables.isLoggedIn OR session.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
			<cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
			<cfset local.strData.one.memberID = 0>         
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>

		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
		<cfset local.strResult1 = local.objSaveMember.saveData(runImmediately=1)>

		<cfset session.formFields.step0.origMemberID = variables.useMID/>
		<cfset session.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>	
			<cfset session.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
															subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
															enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
			<cfset session.captchaEntered = 1>		
			<cfset local.response = "success">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset variables.useMID = local.strData.zero.memberID/>

		<cfif StructIsEmpty(local.strData.one)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>

		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
		<cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Membership Category',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
				div.alert-danger{padding: 10px !important;}
				.radio, .checkbox{padding-left: 20px!important;}
				</style>

				<script type="text/javascript">	
				
					#local.result.jsAddonValidation#

					function validateMembershipInfoForm(){
						var arrReq = new Array();						
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}
						
						<cfif findNoCase('Law Student (Currently Enrolled in a Law School)',local.memberTypeSelected) GT 0>
							YLLSObj = $(".subAddonWrapper[data-setuid='#variables.strPageFields.YoungLawyersLawStudentUID#']");
							if(YLLSObj.length > 0){
								minallowed = isNaN(parseInt(YLLSObj.data('minallowed')))?'':parseInt(YLLSObj.data('minallowed'));
								maxallowed = isNaN(parseInt(YLLSObj.data('maxallowed')))?'':parseInt(YLLSObj.data('maxallowed'));
								YLLSelectedlength = YLLSObj.find('input.subCheckbox:checked').length;
								if(YLLSelectedlength < minallowed && minallowed != ''){
									arrReq[arrReq.length] = "Please select atleast "+minallowed+" rate(s) for Young Lawyers Division Dues for Law Students.";
								}
								if(YLLSelectedlength > maxallowed && maxallowed != ''){
									arrReq[arrReq.length] = "Please select no more than "+maxallowed+" rate(s) for Young Lawyers Division Dues for Law Students.";
								}
							}
						</cfif>
						//make sure selected subscriptions all have selected rates.
						$('input.subCheckbox:checkbox:checked').each(function(x,item){
							var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
							var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
							if ( numRates > 0 && numRatesChecked==0) {
								arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
							}
						});
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
						$(".subRateOverrideBox").parent().siblings('input.subRateCheckbox[type=radio]').each(function(){
							if($(this).is(':checked') == false){
								$(this).parent().find('.subRateOverrideBox').remove();
							}
						});

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					$(document).ready(function () {
						<cfif findNoCase('Law Student (Currently Enrolled in a Law School)',local.memberTypeSelected) GT 0>
							YLLSObj = $(".subAddonWrapper[data-setuid='#variables.strPageFields.YoungLawyersLawStudentUID#']");
							if(YLLSObj.length > 0){
								if(YLLSObj.find('input.subCheckbox')[0] != undefined){
									$(YLLSObj.find('input.subCheckbox')[0]).prop('checked', true);
								}
							}
						</cfif>

					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	
					<cfif len(variables.strPageFields.FormTitleDescription)>
						<div class="row-fluid" id="FormTitleDesc">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitleDescription#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="row-fluid">
						<div class="span12">
							<cfoutput>#local.result.formcontent#</cfoutput>
						</div>
					</div>
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>			
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfif StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>
			<cfset local.billingInfo = application.objCustomPageUtils.getBillingInfoFromFormData(siteID=variables.siteID, formData=local.strData.one, fieldSetUID=variables.businessInformationFSUID)>
			<cfset local.qryMerchantProfile = application.objCustomPageUtils.getMerchantProfileDetails(siteid=variables.siteid, profileID=application.objCustomPageUtils.acct_getProfileID(siteID=variables.siteID, profileCode=variables.strPageFields.ProfileCodeCredit))>
			<cfset local.paymentArgs = application.objPayments.getChargeInfoAndPaymentFeatures(
				"amt": local.strResult.totalFullPrice, 
				"strprocessingfee":{ 
					"enable": local.qryMerchantProfile.enableProcessingFeeDonation,
					"feepct": val(local.qryMerchantProfile.processFeeDonationFeePercent),
					"select": val(local.qryMerchantProfile.processFeeDonationDefaultSelect) EQ 1 ? 1 : 0, 
					"label": local.qryMerchantProfile.processFeeOtherPaymentsFELabel,
					"denylabel": local.qryMerchantProfile.processFeeOtherPaymentsFEDenyLabel,
					"title": local.qryMerchantProfile.processFeeDonationFETitle,
					"msg": local.qryMerchantProfile.processFeeDonationFEMsg,
					"gl": local.qryMerchantProfile.processFeeDonationRenevueGLAccountID
				},
				"enableSurcharge": 0,
				"enableApplePay": 0,
				"enableGooglePay": 0,
				"stateIDForTax": val(local.billingInfo.state),
				"zipForTax": local.billingInfo.zip
			)>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo",
					paymentFeaturesAndChargeInfo=local.paymentArgs
				)>
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>

				<script type="text/javascript">	
					function validatePaymentForm(isPaymentRequired) {

						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					$('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);

						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
					</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
					<cfinput type="hidden" name="fa" id="fa" value="processPayment">
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	
					<cfif len(variables.strPageFields.FormTitleDescription)>
						<div class="row-fluid" id="FormTitleDescription">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitleDescription#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step3TopContent)>
						<div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
					</cfif>

					<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
					<div class="tsAppSectionContentContainer">						
						#local.strResult.formContent#
					</div>
					<br/>

					<div class="tsAppSectionHeading">Total Price</div>
					<div class="tsAppSectionContentContainer">						
						Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
					</div>

					<br/><br/>

					<cfif local.paymentRequired>
						#local.strReturn.paymentHTML#
					<cfelse>
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
						<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
					</cfif>

				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step3")>
			<cfset structDelete(session.formFields, "step3")>
		</cfif>			
		<cfset session.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

		<cfset local.strData.three = checkSessionExist("step3")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.MainSubscription)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.strData.two.memberID = variables.useMID>

		<cfset application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>
	
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
		<cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Membership Category',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

		<cfif val(local.strData.one["orgMemberID"]) NEQ 0>
			<cfset local.objAdminMember = CreateObject("component","model.admin.members.members") />
			<cfset local.availableRecordRelationships = local.objAdminMember.getAvailableRecordRelationships(orgID=variables.orgID, masterMemberID=val(local.strData.one["orgMemberID"]), childMemberID=variables.useMID) />
			<cfquery dbtype="query" name="local.qryStaffRecordRelationship">
				select recordTypeRelationshipTypeID, relationshipTypeName
				from [local].availableRecordRelationships.qryRecordRelationshipTypes
				where 1=1 AND 
				<cfif findNoCase('Attorney',local.memberTypeSelected) GT 0 OR  findNoCase('Emeritus (50+ years of Legal Service)',local.memberTypeSelected) GT 0>
				relationshipTypeName='Attorney'
				<cfelseif findNoCase('Law Student (Currently Enrolled in a Law School)',local.memberTypeSelected) GT 0>
				relationshipTypeName='Law Student'
				<cfelseif findNoCase('Associate (Paralegal or Legal Assistant)',local.memberTypeSelected) GT 0>
				relationshipTypeName='Associate'
				<cfelseif findNoCase('Government Attorney',local.memberTypeSelected) GT 0>
				relationshipTypeName='Government Attorney'
				</cfif>
			</cfquery>
			<cfif local.qryStaffRecordRelationship.recordCount>
				<cfset local.objAdminMember.addRecordRelationship(mcproxy_orgID=variables.orgID, mcproxy_siteID=variables.siteID, masterMemberID=val(local.strData.one["orgMemberID"]), childMemberID=variables.useMID, recordTypeRelationshipTypeID=local.qryStaffRecordRelationship.recordTypeRelationshipTypeID, isActive=1)>
			</cfif>
		</cfif>
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(local.strData.three, 'mccf_payMethID') and structKeyExists(local.strData.three, 'p_#local.strData.three.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.three['p_#local.strData.three.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfset local.processPaymentResponse = structnew()>
		<cfset local.totalPaymentAmountDsp = dollarFormat(local.strResult.totalFullPrice)>
		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#,#variables.strPageFields.ProfileCodeACH#",local.strData.three.mccf_payMeth)>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.qryMerchantProfile = application.objCustomPageUtils.getMerchantProfileDetails(siteID=variables.siteID, profileID=local.strData.three.mccf_payMethID)>
         	<cfset local.qryMemberBillingAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=variables.orgid, memberID=variables.memberID)>

			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=local.strData.three } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.strData.three.mccf_payMethID, profileCode=local.strData.three.mccf_payMeth,supportPaymentFees=1,
            stopOnError=1,qryMerchantProfile=local.qryMerchantProfile,
										taxInfo: { stateIDForTax=val(local.qryMemberBillingAddress.stateID), zipForTax=local.qryMemberBillingAddress.postalCode } }>
				
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
				<cfif local.strACCResponse.keyExists("recordAdditionalPmtFees") AND local.strACCResponse.recordAdditionalPmtFees.success>
					<cfset local.totalPaymentAmountDsp = "#dollarFormat(local.strResult.totalFullPrice)# + #local.strACCResponse.recordAdditionalPmtFees.amtdsp#">
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
            <cfset local.processPaymentResponse = local.strACCResponse>
		</cfif> 
		<cfset local.response = "success">

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfset local.membershipCategoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.membershipCategoryFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.lawStudentFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.lawStudentFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.businessInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.businessInformationFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.physicalAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.physicalAddressFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferencesFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.profInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.profInfoFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.demoInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demoInfoFSUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.additionalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalInfoFSUID, mode="confirmation", strData=local.strData.one)>

		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		
		<cfsavecontent variable="local.invoice">
            <cfoutput>
				#local.membershipCategoryFieldSet.fieldSetContent#

				<cfif local.memberTypeSelected NEQ 'Law Student (Currently Enrolled in a Law School)' AND local.memberTypeSelected NEQ 'Associate (Paralegal or Legal Assistant)'>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
						</tr>				
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
												<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
													<thead>
														<tr valign="top">
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
														</tr>
													</thead>
													<tbody>
													<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
														<tr id="tr_state_#local.key#">
															<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
														</tr>
													</cfloop>
													</tbody>
												</table>
											</cfif>
										</td>
									</tr>						
								</table>
							</td>
						</tr>
					</table>
					<br>
				</cfif>

				#local.lawStudentFieldSet.fieldSetContent#
				#local.contactInformationFieldSet.fieldSetContent#
				#local.businessInformationFieldSet.fieldSetContent#		
				#local.homeAddressFieldSet.fieldSetContent#	
				#local.physicalAddressFieldSet.fieldSetContent#	
				#local.addressPreferencesFieldSet.fieldSetContent#
				#local.profInfoFieldSet.fieldSetContent#
				#local.demoInfoFieldSet.fieldSetContent#

				<cfif structKeyExists(local.strData.one,'memberservices')>
					<cfset local.cntNBAMemberServices = ''>
					<cfset local.serviceArr = local.strData.one['memberservices']>
					<cfif len(local.serviceArr)>
						<cfloop from="1" to="10" index="local.idx">
							<cfif structKeyExists(variables.strPageFields,"NBAMemberServices_" & local.idx)>
								<cfset local.arrNBAMemberServices = ListToArray(variables.strPageFields["NBAMemberServices_" & local.idx], "|")>
								<cfif ArrayLen(local.arrNBAMemberServices)>
									<cfif len(variables.strPageFields["NBAMemberServices_" & local.idx])>
										<cfset local.NBAMemberServicesTxt = local.arrNBAMemberServices[1]>
										<cfset local.validEntry = 1>
										<cfif ArrayLen(local.arrNBAMemberServices) GTE 2>							
											<cfset local.NBAMemberServicesCode = local.arrNBAMemberServices[2]>
										<cfelse>
											<cfset local.validEntry = 0>
										</cfif>
										<cfif local.validEntry eq 1>
											<cfset local.checked="">
											<cfif ListContains(local.serviceArr, local.NBAMemberServicesCode)>
												<cfset local.cntNBAMemberServices &= '<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">#local.NBAMemberServicesTxt#</td></tr>'>
											</cfif>											
										</cfif>										
									</cfif>
								</cfif>
							</cfif>
						</cfloop>

						<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
							<tr>
								<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">#variables.strPageFields.NBAMemberServicesTitle#</td>
							</tr>
							<tr>	
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
									<table cellpadding="3" border="0" cellspacing="0">
										#local.cntNBAMemberServices#
									</table>					
								</td>
							</tr>
						</table><br/>
					</cfif>
				</cfif>
				#local.additionalInfoFieldSet.fieldSetContent#
			
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								#local.strResult.formContent#
								<br/>
						</div>
						<br/>
						<strong>Total Price:</strong> #local.totalPaymentAmountDsp#
						<br/>
						</td>
					</tr>
				</table>

				<cfif local.paymentRequired>
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(local.strData.three,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											#local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>
			</cfoutput>
		</cfsavecontent>


		<cfif structKeyExists(local.strData.one,'memberservices')>
			<cfset local.cntNBAMemberServices = ''>
			<cfset local.serviceArr = local.strData.one['memberservices']>
			<cfif len(local.serviceArr)>
				<cfloop from="1" to="10" index="local.idx">
					<cfif structKeyExists(variables.strPageFields,"NBAMemberServices_" & local.idx)>
						<cfset local.arrNBAMemberServices = ListToArray(variables.strPageFields["NBAMemberServices_" & local.idx], "|")>
					
						<cfif ArrayLen(local.arrNBAMemberServices)>
							<cfif len(variables.strPageFields["NBAMemberServices_" & local.idx])>
								<cfset local.NBAMemberServicesTxt = local.arrNBAMemberServices[1]>
								<cfset local.validEntry = 1>
								<cfif ArrayLen(local.arrNBAMemberServices) GTE 2>							
									<cfset local.NBAMemberServicesCode = local.arrNBAMemberServices[2]>
								<cfelse>
									<cfset local.validEntry = 0>
								</cfif>
								<cfset local.hasSubCat = 0>
								<cfif ArrayLen(local.arrNBAMemberServices) GTE 3>							
									<cfset local.NBAMemberServicesSubCat = local.arrNBAMemberServices[3]>
									<cfset local.hasSubCat = 1>
								</cfif>						
										
								<cfif local.validEntry eq 1>
									<cfset local.checked="">
									<cfif ListContains(local.serviceArr, local.NBAMemberServicesCode)>
										<cfset local.memId = variables.useMID>
										<cfif local.hasSubCat eq 1>
											<cfset variables.getCategory =application.objCustomPageUtils.mh_getCategory(variables.siteID,'#local.NBAMemberServicesCode#','#trim(local.NBAMemberServicesSubCat)#')>
										<cfelse>
											<cfset variables.getCategory =application.objCustomPageUtils.mh_getCategory(variables.siteID,'#local.NBAMemberServicesCode#')>
										</cfif>
										
										<cfif variables.getCategory.recordcount GT 0>
											<cfset application.objCustomPageUtils.mh_addHistory(memberID=local.memId, categoryID=variables.getCategory.CATEGORYID,
												subCategoryID=variables.getCategory.SUBCATEGORYID, description='#trim(local.NBAMemberServicesTxt)#.', linkMemberID=0, enteredByMemberID=local.memId,
												newAccountsOnly=false)>
										</cfif>
									</cfif>											
								</cfif>										
							</cfif>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.mailContent">
			<cfoutput>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<p>#variables.strPageFields.ConfirmationContent#</p>
					</cfif>

					<p>Here are the details of your application:</p>	

					#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>
		<cfset local.emailSentToUser = local.responseStruct.success>
		
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfsavecontent variable="local.specialText">
			<cfoutput>

            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(local.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#local.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<p>#variables.strPageFields.ConfirmationContent#</p>
					</cfif>
					#local.specialText#
					<p>Here are the details of your application:</p>

					#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
						<cfif len(variables.strPageFields.ConfirmationContent)>
							<p>#variables.strPageFields.ConfirmationContent#</p>
						</cfif>
						<p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>

		<!--- relocate to message page --->
		<cfset session.invoice = local.invoice />

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
					</cfif>
					<div class="tsAppSectionContentContainer">
						<p class="tsAppBodyText">Here are the details of your application:</p>						
						#session.invoice#
					</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="hasSub" access="private" output="false" returntype="string">
		<cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

		<cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.isExist = false/>
		<cfset local.strData = {}>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, arguments.step)>
			<cfset local.strData = session.formFields[arguments.step]/>
		</cfif>			

		<cfreturn local.strData>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						<cflocation url="#local.redirectLink#" addtoken="false">
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelseif arguments.errorCode eq "admin">
						<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>