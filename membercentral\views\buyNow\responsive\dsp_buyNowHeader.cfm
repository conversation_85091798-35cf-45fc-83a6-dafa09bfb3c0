<cfif arguments.event.getValue('mc_pageDefinition.layoutmode') neq "stream">
	<cfoutput>
		<div class="buyNowV2">
			<cfif len(local.pageTitle) or listFindNoCase("receipt",local.contentToShow) or len(local.strItem.phoneSupport)>
				<div class="buyNow-d-flex buyNow-mb-3">
					<div class="buyNow-font-size-xl buyNow-font-weight-bold">#local.pageTitle#</div>
					<cfif listFindNoCase("receipt",local.contentToShow)>
						<a href="javascript:window.print();" class="buyNow-noprint buyNow-ml-auto"><i class="icon-print icon-large"></i></a>
					<cfelseif len(local.strItem.phoneSupport)>
						<div class="legendTitle buyNow-ml-auto">Questions? Call #local.strItem.phoneSupport#</div>
					</cfif>
				</div>
			</cfif>
	</cfoutput>
</cfif>