<cfcomponent extends="model.customPage.customPage" output="true">
	<cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
	<cfargument name="Event" type="any">

	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=join";
            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

            variables.personalInformationFieldSetUID = "2F114BEC-BC8A-4D28-A1BA-039155A97DF3";
				variables.homeAddressFieldSetUID = "3187753B-FAFF-4249-823C-AB519E2F4C9D";
				variables.professionalAddressFieldSetUID = "1A4A70ED-E434-44F9-9CD1-7AC436737338";			
				variables.addressPreferencesFieldSetUID = "47C26421-2937-48EC-974C-BDF81589CE81";
				variables.categoryFieldSetUID = "9D8A9BE9-0C33-443E-A1A1-5C59C9E88822";
				variables.studentInformationFieldSetUID = "8E594998-1844-461F-8AEF-7D3B654D3DA5";
				variables.attorneyInformationFieldSetUID = "CDC6F6FE-D490-4D2B-9631-88A11D42FB88";
				variables.demographicInformationFieldSetUID = "0C4AD471-FBFD-4CCD-9E2E-C3B08B8B8889";
            variables.additionalInformationFieldSetUID = "800A3C5D-6215-4664-AF23-935A6C36EE72";
				variables.preCheckedAddons = "C7BB671E-39C9-4D92-8056-DE7800115BFC";
            variables.currentDate = dateTimeFormat(now());

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];

            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
					arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="C19EA1D8-6653-4947-A62C-193B40C9F944" };
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already an MBA member. If you have questions about your membership, please call 610-279-9660 or email <a href='mailto:<EMAIL>'><EMAIL></a>." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="BilledMessage",type="CONTENTOBJ",desc="Message displayed when account selected billed subscription",value="Our records indicate either your application is currently under review or you are a renewing member. If you are looking to renew, please click [[click here]] to review your renewal statement. If you have questions about your membership, please call 610-279-9660 or email <a href='mailto:<EMAIL>'><EMAIL></a>." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 610-279-9660 or email <a href='mailto:<EMAIL>'><EMAIL></a>." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);	
				local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="MBA Membership Application" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="983152AC-CCC0-4B86-A558-13E86650CCBD" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="CreditCards" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application. After your application is reviewed, if it is approved, the payment will be processed on the card you have provided. If your application is not approved, you will hear from Montgomery Bar directly." }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="MBA Membership Application Confirmation" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
						arrayAppend(local.arrCustomFields, local.tmpField);       

				variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

				StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
						formName='frmJoin',
						formNameDisplay=variables.strPageFields.FormTitle,
						orgEmailTo= variables.strPageFields.StaffConfirmationTo,
						memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
				));

            variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");

            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;

            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
            variables.origMemberID = variables.useMID;
            if(local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                structKeyExists(session, "formFields") and 
                structKeyExists(session.formFields, "step0") and 
                structKeyExists(session.formFields.step0, "memberID") and 
                int(val(session.formFields.step0.memberID)) gt 0){            
                    variables.useMID = session.formFields.step0.memberID;
                    if(structKeyExists(session.formFields.step0, "origMemberID") and int(val(session.formFields.step0.origMemberID)) gt 0){
                        variables.origMemberID = session.formFields.step0.origMemberID;
                    }              

            }else if(session.cfcuser.memberdata.identifiedAsMemberID){
					variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
					variables.origMemberID = variables.useMID;
            }
            if( local.formAction neq "showLookup" and 
					local.formAction neq "processLookup" and 
					local.formAction neq "showMemberInfo" and 
					local.formAction neq "processMemberInfo" and 
					structKeyExists(session.formFields, "step1") and 
					structKeyExists(session.formFields.step1, "useHistoryID") and 
					int(val(session.formFields.step1.useHistoryID)) gt 0){
						variables.useHistoryID = int(val(session.formFields.step1.useHistoryID));
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
            local.subStatus = "";
            if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
					local.subStatus = hasSub(int(variables.useMID));
            }

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            if(local.isSuperUser){
					local.returnHTML = showError(errorCode='admin');  
            }else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
					local.returnHTML = showError(errorCode=local.subStatus);  
            }else{
					switch (local.formAction) {
						case "processLookup":
							switch (processLookup(rc=arguments.event.getCollection())) {
								case "success":
									local.returnHTML = showMemberInfo();
									break;		
								default:
									local.returnHTML = showError(errorCode='error');
									break;				
							}
							break;
						case "processMemberInfo":
							switch (processMemberInfo(rc=arguments.event.getCollection())) {
								case "success":
									local.returnHTML = showMembershipInfo();
									break;
								case "spam":
									local.returnHTML = showError(errorCode='spam');
									break;
								default:
									local.returnHTML = showError(errorCode='error');
									break;				
							}
							break;
						case "processMembershipInfo":
							switch (processMembershipInfo(rc=arguments.event.getCollection())) {
								case "success":
									local.returnHTML = showPayment();
									break;
								default:
									local.returnHTML = showError(errorCode='error');
									break;				
							}
							break;                   
						case "processPayment":
							local.processStatus = processPayment(event=arguments.event);
							switch (local.processStatus) {
								case "success":
									local.returnHTML = showConfirmation();
									application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
									structDelete(session, "formFields");
									break;
								default:
									local.returnHTML = showError(errorCode='failpayment');
									break;				
							}
							break;
						case "showMembershipInfo":
							local.returnHTML = showMembershipInfo();
							break;	
						case "showMemberInfo":
							local.returnHTML = showMemberInfo();
							break;                    		
						default:
							if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
							if(structKeyExists(session, "formFields")) structDelete(session, "formFields");
							local.returnHTML = showLookup();
							break;
					}
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");        
		</cfscript>
	</cffunction>


	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.headCode">
			<cfoutput>          

            #variables.pageJS# 

			<style type="text/css">
			</style>

			<script type="text/javascript">

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}

				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				

				function addMember(memObj) {
					assignMemberData(memObj);
				}

				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				

				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
						if(step==1)
							$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
						if(step==2)
							$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");                            

						mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
						}
					} else {
						step = 1;
					}
					prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
					<cfinput type="hidden" name="fa" id="fa" value="processLookup">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
									<span class="HeaderText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	

					<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
					<div class="tsAppSectionContentContainer">
						<table cellspacing="0" cellpadding="2" border="0" width="100%">
							<tr>
								<td width="175" style="text-align:center;">
									<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
								</td>
								<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
							</tr>
						</table>
				</div>	
			</cfform>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step0")>
				<cfset structDelete(session.formFields, "step0")>
			</cfif>	

			<cfset session.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        

		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->

		<cfset local.fieldSetUIDlist = '#variables.personalInformationFieldSetUID#,#variables.categoryFieldSetUID#,#variables.homeAddressFieldSetUID#,#variables.professionalAddressFieldSetUID#'>

		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>

		<cfset local.memberTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.officeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.officestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.officepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.homeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.homestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.homepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.OfficePhoneField = {fieldCode="",fieldLabel=""}>
		<cfset local.HomePhoneField = {fieldCode="",fieldLabel=""}>

		<cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset local.strData.one = checkSessionExist("step1")/>       

		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

            <cfset local.memberTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Contact Type']")/>
            <cfif arrayLen(local.memberTypeData)>				
					<cfset local.memberTypeField.fieldCode = local.memberTypeData[1].XmlAttributes.fieldCode>
					<cfset local.memberTypeField.fieldLabel = local.memberTypeData[1].XmlAttributes.fieldLabel>
            </cfif>            
            <cfset local.officeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Professional_address1']")/>
            <cfif arrayLen(local.officeAddress1FieldData)>				
					<cfset local.officeAddress1Field.fieldCode = local.officeAddress1FieldData[1].XmlAttributes.fieldCode >
					<cfset local.officeAddress1Field.fieldLabel = local.officeAddress1FieldData[1].XmlAttributes.fieldLabel >
            </cfif>
            <cfset local.officeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Professional_city']")/>
            <cfif arrayLen(local.officeCityFieldData)>				
					<cfset local.officeCityField.fieldCode = local.officeCityFieldData[1].XmlAttributes.fieldCode >
					<cfset local.officeCityField.fieldLabel = local.officeCityFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Professional_stateprov']")/>
            <cfif arrayLen(local.officestateprovFieldData)>				
					<cfset local.officestateprovField.fieldCode = local.officestateprovFieldData[1].XmlAttributes.fieldCode >
					<cfset local.officestateprovField.fieldLabel = local.officestateprovFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Professional_postalcode']")/>
            <cfif arrayLen(local.officepostalcodeFieldData)>				
					<cfset local.officepostalcodeField.fieldCode = local.officepostalcodeFieldData[1].XmlAttributes.fieldCode >
					<cfset local.officepostalcodeField.fieldLabel = local.officepostalcodeFieldData[1].XmlAttributes.fieldLabel >
            </cfif>    
			<cfset local.OfficePhoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Professional_Phone']")/>
            <cfif arrayLen(local.OfficePhoneFieldData)>				
					<cfset local.OfficePhoneField.fieldCode = local.OfficePhoneFieldData[1].XmlAttributes.fieldCode >
					<cfset local.OfficePhoneField.fieldLabel = local.OfficePhoneFieldData[1].XmlAttributes.fieldLabel >
            </cfif>        
            <cfset local.homeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_address1']")/>
            <cfif arrayLen(local.homeAddress1FieldData)>				
					<cfset local.homeAddress1Field.fieldCode = local.homeAddress1FieldData[1].XmlAttributes.fieldCode >
					<cfset local.homeAddress1Field.fieldLabel = local.homeAddress1FieldData[1].XmlAttributes.fieldLabel >
            </cfif>
            <cfset local.homeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_city']")/>
            <cfif arrayLen(local.homeCityFieldData)>				
					<cfset local.homeCityField.fieldCode = local.homeCityFieldData[1].XmlAttributes.fieldCode >
					<cfset local.homeCityField.fieldLabel = local.homeCityFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.homestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_stateprov']")/>
            <cfif arrayLen(local.homestateprovFieldData)>				
					<cfset local.homestateprovField.fieldCode = local.homestateprovFieldData[1].XmlAttributes.fieldCode >
					<cfset local.homestateprovField.fieldLabel = local.homestateprovFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.homepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_postalcode']")/>
            <cfif arrayLen(local.homepostalcodeFieldData)>				
					<cfset local.homepostalcodeField.fieldCode = local.homepostalcodeFieldData[1].XmlAttributes.fieldCode >
					<cfset local.homepostalcodeField.fieldLabel = local.homepostalcodeFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.HomePhoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_Phone']")/>
            <cfif arrayLen(local.HomePhoneFieldData)>				
					<cfset local.HomePhoneField.fieldCode = local.HomePhoneFieldData[1].XmlAttributes.fieldCode >
					<cfset local.HomePhoneField.fieldLabel = local.HomePhoneFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
            
            <cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.personalInformationFieldSetUID#',local.fieldSetUid)>
					<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				
					<cfif NOT variables.isLoggedIn AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>				
						<cfloop collection="#local.memberFieldData#" item="local.key" >
							<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
								<cfset StructDelete(local.memberFieldData, local.key)>
							</cfif>					
						</cfloop>
					</cfif>
            </cfif>                
		</cfloop>
		<cfif StructIsEmpty(local.strData.one)>
			<cfset local.customXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#variables.attorneyInformationFieldSetUID#', usage='CustomPage')>
			<cfset local.radioData = XMLSearch(local.customXMLFields,"/fields/mf[@displayTypeCode='RADIO']")/>
			<cfset local.fieldSetData = application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.customXMLFields)/>
			<cfloop array="#local.radioData#" index="local.thisItem">
				<cfset local.fieldData=application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnID='#local.thisItem.XmlAttributes.mdColumnID#')>
				<cfloop array="#local.fieldData.COLUMNVALUEARR#" index="local.thisItemInner">
					<cfif structKeyExists(local.thisItemInner,"ISDEFAULT") and local.thisItemInner.ISDEFAULT>
						<cfset local.fieldSetData['md_#local.thisItem.XmlAttributes.mdColumnID#'] = local.thisItemInner.VALUEID/>
					</cfif>
				</cfloop>
			</cfloop>
			<cfset StructAppend(local.strData.one,local.fieldSetData)/>
		</cfif>

		<cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.professionalAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferencesFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.categoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.categoryFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.studentInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.studentInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.attorneyInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.attorneyInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.demographicInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demographicInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.additionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset local.index = 1>
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
			<cfset local.index = local.index + 1>
		</cfloop> 
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}
				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
						padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
				div.alert-danger{padding: 10px !important;}				
			</style>
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}		
				function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					switch(memType) {
						case 'Law Student':	
							$('.attorneyInformationFieldSetHolder').hide();
							resetFormFieldsByContainerClass('attorneyInformationFieldSetHolder','hide');
							break;								
						default:
							$('.attorneyInformationFieldSetHolder').show();
							resetFormFieldsByContainerClass('attorneyInformationFieldSetHolder','show');
							break;
					}
				}
				function showFieldsByContainerClass(classList) {
					$(".attorneyInformationFieldSetHolder").html('');

					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());							
						}			
					});							
				}
				function resetFormFieldsByContainerClass(containerClass,display){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]+" input,."+containerClassArray[i]+" select,."+containerClassArray[i]+" textarea").each(function(){
								if(display == 'hide') {
									$(this).attr('disabled','disabled');
									$(this).attr('readonly','readonly');
								} else {
									$(this).removeAttr('disabled');
									$(this).removeAttr('readonly');
								}
							});
						});
					}
				}	
				function resetProfessionalLicenses(){
					$('.mpl_pltypeid').multiselect("uncheckAll"); 
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}

				}			
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();	
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					
					#local.personalInformationFieldSet.jsValidation#
					#local.homeAddressFieldSet.jsValidation#
					#local.professionalAddressFieldSet.jsValidation#
					#local.addressPreferencesFieldSet.jsValidation#
					#local.categoryFieldSet.jsValidation#
					#local.studentInformationFieldSet.jsValidation#
					if(memType.length > 0 && (memType != 'Law Student')){
					#local.attorneyInformationFieldSet.jsValidation#
					}
					#local.demographicInformationFieldSet.jsValidation#
					#local.additionalInformationFieldSet.jsValidation#    

					var isSetAllOfficeAddress = 0;
					<cfif len(trim(local.officeAddress1Field.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllOfficeAddress = isSetAllOfficeAddress + 1;}
					</cfif>
					<cfif len(trim(local.officeCityField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officeCityField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
					</cfif>
					<cfif len(trim(local.officestateprovField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officestateprovField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
					</cfif>
					<cfif len(trim(local.officepostalcodeField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
					</cfif>

					var isSetAllHomeAddress = 0;		
					<cfif len(trim(local.homeAddress1Field.fieldCode))>                        
						if(_CF_hasValue(_CF_this['#local.homeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllHomeAddress = isSetAllHomeAddress + 1;}
					</cfif>
					<cfif len(trim(local.homeCityField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homeCityField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
					</cfif>
					<cfif len(trim(local.homestateprovField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homestateprovField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
					</cfif>
					<cfif len(trim(local.homepostalcodeField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
					</cfif>

					var addressSet = false;

					if(addressSet == false && isSetAllOfficeAddress == 0 && isSetAllHomeAddress == 0){
						arrReq[arrReq.length] = "Either Home Address or Office Address is required:<ul><li>Address</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
					}else if(addressSet == false){
						var isSet = 0;
						if(isSetAllOfficeAddress == 4){
							isSet = 1;								
						}else if(isSetAllHomeAddress == 4){
							isSet = 1;								
						}

						if(isSet == 0){
							arrReq[arrReq.length] = "Either Home Address or Office Address is required:<ul><li>Address</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
						}
					}

					var isSetOfficePhone = 0;
					<cfif len(trim(local.OfficePhoneField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.OfficePhoneField.fieldCode#'], "TEXT", false)){ isSetOfficePhone = isSetOfficePhone + 1;}
					</cfif>
					var isSetHomePhone = 0;
					<cfif len(trim(local.homePhoneField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homePhoneField.fieldCode#'], "TEXT", false)){ isSetHomePhone = isSetHomePhone + 1;}
					</cfif>

					if(isSetOfficePhone == 0 && isSetHomePhone == 0){
						arrReq[arrReq.length] = "Office Phone or Home Phone is required";
					}

					if(memType.length > 0 && (memType == 'Attorney')){

						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();                                    
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}
					} 

					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
					$("##state_table").show();
					if(status == ''){
						status = 'Active';
					}
					strOption = '';
					<cfloop collection="#local.licenseStatus#" item="local.i" >
						strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
					</cfloop>
					if(isChecked){
						$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
									'<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
									'<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
									'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
									'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
									'<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
									'</div>');
						$('##mpl_'+val+'_status').val(status);
						mca_setupDatePickerField('mpl_'+val+'_activeDate');
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
					}									
					else{
						$("##tr_state_"+val).remove();								
					}
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}	
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strData.one#" item="local.thisKey">
						<cfif FindNoCase("m_",local.thisKey)
							or FindNoCase("ma_",local.thisKey) 
							or FindNoCase("mat_",local.thisKey)
							or FindNoCase("met_",local.thisKey) 
							or FindNoCase("me_",local.thisKey) 
							or FindNoCase("mpl_",local.thisKey)							
							or FindNoCase("mp_",local.thisKey) 
							or FindNoCase("mw_",local.thisKey) 
							or FindNoCase("md_",local.thisKey)
							or FindNoCase("mccf_",local.thisKey)>
							#toScript(local.strData.one[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfif>
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							if($("###variables.formName# [name='"+key+"']").prop("tagName").toLowerCase() == "input" && $("###variables.formName# [name='"+key+"']").attr("type").toLowerCase() == "radio"){

								$("###variables.formName# [name='"+key+"'][value='"+objPrefill[key]+"']").prop("checked", true);

							}else if($("###variables.formName# [name='"+key+"']").prop("tagName").toLowerCase() == "select" && $("###variables.formName# [name='"+key+"']").prop('multiple')){
								var selectString = objPrefill[key];
								$.each(selectString.split(','), function(){
									$("###variables.formName# [name='"+key+"'] option[value='"+this+"']").prop('selected', true);
								});
								$("###variables.formName# [name='"+key+"']").multiselect("refresh");
							}else{
								$("###variables.formName# [name='"+key+"']").val(objPrefill[key]);
							}
						}
					}
				}

				$(document).ready(function() {	

					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>		

					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					</cfif>				

					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>

					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
					var memberTypeField = $('##'+"#local.memberTypeField.fieldCode#");	
					$(memberTypeField).change(adjustFieldsetDisplay);				
					$(memberTypeField).trigger('change');
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="HeaderText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>		

					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>

                        <span class="personalInformationFieldSetHolder">
							<cfif len(trim(local.personalInformationFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.personalInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.personalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="homeAddressFieldSetHolder">
							<cfif len(trim(local.homeAddressFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.homeAddressFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="professionalAddressFieldSetHolder">
							<cfif len(trim(local.professionalAddressFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.professionalAddressFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.professionalAddressFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="addressPreferencesFieldSetHolder">
							<cfif len(trim(local.addressPreferencesFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressPreferencesFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.addressPreferencesFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="categoryFieldSetHolder">
							<cfif len(trim(local.categoryFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.categoryFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.categoryFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="studentInformationFieldSetHolder">
							<cfif len(trim(local.studentInformationFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.studentInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.studentInformationFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="attorneyInformationFieldSetHolder">
							<cfif len(trim(local.attorneyInformationFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.attorneyInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.attorneyInformationFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

						<span class="demographicInformationFieldSetHolder">
							<cfif len(trim(local.demographicInformationFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.demographicInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.demographicInformationFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>

                        <span class="additionalInformationFieldSetHolder">
							<cfif len(trim(local.additionalInformationFieldSet.fieldSetTitle))>
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.additionalInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.additionalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
							</cfif>
						</span>    

                        <span class="professionalLicensesHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">Professional License Information</div>
								<div class="tsAppSectionContentContainer fieldSetContainer">									
									<table cellpadding="3" border="0" cellspacing="0" >									
										<tr align="top">
											<td class="tsAppBodyText" width="10">&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Professional License</td>
											<td class="tsAppBodyText">
												<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
													<cfloop query="local.qryOrgPlTypes">	
														<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="top">
											<td class="tsAppBodyText" width="10"></td>
											<td class="tsAppBodyText"></td>
											<td class="tsAppBodyText"></td>
										</tr>
									</table>
									<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
										<tr>
											<td>
												<div class="row-fluid hide" id="state_table">
													<div class="span3 proLicenseLabel tsAppBodyText">
														<b>Type</b>
													</div>
													<div class="span3 proLicenseLabel tsAppBodyText">
														<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
													</div>
													<div class="span3 proLicenseLabel tsAppBodyText">
														<b>#variables.strProfLicenseLabels.profLicenseDateLabel#</b>
													</div>
													<div class="span3 proLicenseLabel tsAppBodyText">
														<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
													</div>
												</div>
												<span id="selectedLicense">
												</span>
											</td>
										</tr>					
									</table>
								</div>
							</div>
						</span>

                        <div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>
				#application.objWebEditor.showEditorHeadScripts()#
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
				<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID = variables.orgid)>

				<script language="javascript">
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">                       
							<cfif ListFindNoCase('Professional,Home',local.qryOrgAddressTypes.addressType)>
								function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _address = _this.val();

									if(_address.length > 0){
											if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
												$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
											}
									} else {
											$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
									}
								}

								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));

								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
							<cfelse>
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							</cfif>
						</cfloop>
					});
					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>		

		<cfset local.response = "failure">

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>			

		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>	
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>
		
		<cfif variables.isLoggedIn OR session.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
            <cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
            <cfset local.strData.one.memberID = 0>         
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>

		<cfset session.formFields.step0.origMemberID = variables.useMID/>
		<cfset session.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>	
            <cfset session.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
                                                subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
                                                enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
            <cfset session.captchaEntered = 1>		
			<cfset local.response = "success">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset variables.useMID = local.strData.zero.memberID/>

		<cfif StructIsEmpty(local.strData.one)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.thisAddonSubscriptionID = "">
		<cfloop list="#variables.preCheckedAddons#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
		</cfloop>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.MainSubscription)>
		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>	 
				

		<cfsavecontent variable="local.headCode">
			<cfoutput>
					<style type="text/css">
				div.alert-danger{padding: 10px !important;}
					</style>

					<script type="text/javascript">
					var minlp = $('div [data-setname="Lawyer Referral Service"] .subAddonsArrayWrapper .subAddonWrapper[data-setname="LRS Panels"]').data('minallowed');
					var minlg = $('div [data-setname="Lawyer Referral Service"] .subAddonsArrayWrapper .subAddonWrapper[data-setname="LRS Geographical Areas"]').data('minallowed');
					var maxlp = $('div [data-setname="Lawyer Referral Service"] .subAddonsArrayWrapper .subAddonWrapper[data-setname="LRS Panels"]').data('maxallowed');
					var maxlg = $('div [data-setname="Lawyer Referral Service"] .subAddonsArrayWrapper .subAddonWrapper[data-setname="LRS Geographical Areas"]').data('maxallowed');

					#local.result.jsAddonValidation#
					function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}

						if($('div [data-setname="Lawyer Referral Service"] input:checkbox:checked').length > 0){							
							if($('div [data-setname="Lawyer Referral Service"] div [data-setname="LRS Panels"] > div:not(.addonMessageArea):not(.subAddonsArrayWrapper) .subCheckbox:checked').length < minlp){
								arrReq[arrReq.length] = "Please select a minimum of "+minlp+" LRS panel.";
							}else if($('div [data-setname="Lawyer Referral Service"] div [data-setname="LRS Panels"] > div:not(.addonMessageArea):not(.subAddonsArrayWrapper) .subCheckbox:checked').length > maxlp && maxlp > 0){
								arrReq[arrReq.length] = "Please select no more than " + maxlp + " LRS Panel.";
							}

							if($('div [data-setname="Lawyer Referral Service"] div [data-setname="LRS Geographical Areas"] > div:not(.addonMessageArea):not(.subAddonsArrayWrapper) .subCheckbox:checked').length < minlg){
								arrReq[arrReq.length] = "Please select a minimum of "+minlg+" LRS Geographical Areas.";
							}else if($('div [data-setname="Lawyer Referral Service"] div [data-setname="LRS Geographical Areas"] > div:not(.addonMessageArea):not(.subAddonsArrayWrapper) .subCheckbox:checked').length > maxlg && maxlg > 0){
								arrReq[arrReq.length] = "Please select no more than " + maxlg + " LRS Geographical Areas.";
							}
						}

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}		

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}	

					$(document).ready(function(){

						$('div [data-setname="Lawyer Referral Service"] input:checkbox').change(function(){
							if($(this).is(":checked")){
								$("##"+$(this).attr("name").split('_')[0]+"_addons").show();
							}else{
								$("##"+$(this).attr("name").split('_')[0]+"_addons").hide();
								$("##"+$(this).attr("name").split('_')[0]+"_addons input:checkbox").each(function(){
									if($(this).is(":checked")){
										$(this).trigger('click');
									}
								});
							}
						});

						$('div [data-setname="Lawyer Referral Service"] .subAddonsArrayWrapper').hide();                       
					});
					</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="HeaderText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>	
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>			
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>        

		<cfset local.strData.two = checkSessionExist("step2")/>

		<cfif StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>
				<script type="text/javascript">	
					function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				$('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
					var iframeThis = this;

					$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
						setTimeout(function(){ 
							if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
								$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
							}	
						}, 100);

					});
					$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
						$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
					});
				});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
					<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
						<cfinput type="hidden" name="fa" id="fa" value="processPayment">
						<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

						<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
						<cfif len(variables.strPageFields.FormTitle)>
							<div class="row-fluid" id="FormTitleId">
									<div class="span12">
										<span class="HeaderText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
									</div>
							</div>
						</cfif>	

						<cfif len(variables.strPageFields.Step3TopContent)>
							<div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
						</cfif>

					<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
						<div class="tsAppSectionContentContainer">						
							#local.strResult.formContent#
						</div>
						<br/>

						<div class="tsAppSectionHeading">Total Price</div>
						<div class="tsAppSectionContentContainer">						
							Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
						</div>

						<br/><br/>

						<cfif local.paymentRequired>
							#local.strReturn.paymentHTML#
						<cfelse>
							<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
						</cfif>

					</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="event" type="any">

	<cfset var local = structNew()>
	<cfset local.response = "failure">

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step3")>
			<cfset structDelete(session.formFields, "step3")>
		</cfif>			
		<cfset session.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

		<cfset local.strData.three = checkSessionExist("step3")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = local.strData.two)/>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>


		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.strData.two.memberID = variables.useMID>

		<cfset application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>
	
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)> 		

		<cfif structKeyExists(local.strData.three, 'mccf_payMethID') and structKeyExists(local.strData.three, 'p_#local.strData.three.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.three['p_#local.strData.three.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.strResult.totalFullPrice gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.strResult.totalFullPrice gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#",local.strData.three.mccf_payMeth)>
			<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
		</cfif>
		<cfset local.response = "success">

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.professionalAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferencesFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.categoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.categoryFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.studentInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.studentInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.attorneyInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.attorneyInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.demographicInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demographicInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.additionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.additionalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		
		<cfsavecontent variable="local.invoice">
				<cfoutput>
				
				#local.personalInformationFieldSet.fieldSetContent#
				#local.homeAddressFieldSet.fieldSetContent#
				#local.professionalAddressFieldSet.fieldSetContent#
				#local.addressPreferencesFieldSet.fieldSetContent#
				#local.categoryFieldSet.fieldSetContent#
				#local.studentInformationFieldSet.fieldSetContent#
				<cfif local.memberTypeSelected NEQ 'Law Student'>
					#local.attorneyInformationFieldSet.fieldSetContent#
				</cfif>
				#local.demographicInformationFieldSet.fieldSetContent#
                #local.additionalInformationFieldSet.fieldSetContent#
                
                <cfif local.memberTypeSelected EQ 'Attorney'>

                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
                        </tr>				
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">						
                                    <tr valign="top">
                                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                            <cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
                                                <table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
                                                    <thead>
                                                        <tr valign="top">
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    <cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
                                                        <tr id="tr_state_#local.key#">
                                                            <td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
                                                            <td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
                                                            <td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
                                                            <td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
                                                        </tr>
                                                    </cfloop>
                                                    </tbody>
                                                </table>
                                            </cfif>
                                        </td>
                                    </tr>						
                                </table>
                            </td>
                        </tr>
                    </table>
					<br>
                </cfif>

                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            #local.strResult.formContent#
                            <br/>
                        </div>
                        <br/>
                        <strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
                        <br/>
                        </td>
                    </tr>
                </table>

                <cfif local.paymentRequired>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(local.strData.three,"mccf_payMeth")>
                                <table cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif>

            </cfoutput>
        </cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<p>#variables.strPageFields.ConfirmationContent#</p>
					</cfif>

					<p>Here are the details of your application:</p>	

					#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
			)>
		<cfset local.emailSentToUser = local.responseStruct.success>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfsavecontent variable="local.specialText">
			<cfoutput>
            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<p>#variables.strPageFields.ConfirmationContent#</p>
					</cfif>
			#local.specialText#
					<p>Here are the details of your application:</p>

					#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>

		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
						<cfif len(variables.strPageFields.ConfirmationContent)>
							<p>#variables.strPageFields.ConfirmationContent#</p>
						</cfif>
						<p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
	
		<!--- relocate to message page --->
		<cfset session.invoice = local.invoice />

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
					</cfif>
					<div class="tsAppSectionContentContainer">
						<p>Here are the details of your application:</p>						
						#session.invoice#
					</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="hasSub" access="private" output="false" returntype="string">
		<cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

		<cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
			<cfset local.isExist = false/>
			<cfset local.strData = {}>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, arguments.step)>
				<cfset local.strData = session.formFields[arguments.step]/>
			</cfif>			

		<cfreturn local.strData>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						#replaceNoCase(variables.strPageFields.BilledMessage,"[[click here]]","<a href='#local.redirectLink#'>click here</a>")#
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent> 