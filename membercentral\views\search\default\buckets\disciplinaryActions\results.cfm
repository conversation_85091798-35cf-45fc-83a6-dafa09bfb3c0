<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div style="padding-bottom:10px;">
	<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
</div>
</cfoutput>

<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		<div class="s_rnfne">#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName)#</div>
	</cfoutput>
<cfelse>
	<cfoutput>
	#showSearchResultsPaging('top',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'professional','s')#
	</cfoutput>
	<div style="clear:both;"></div>
	<div id="searchSponsorRightSideContainer"></div>
	<!--- results table --->
	<cfoutput query="local.qryResults">
		<cfquery name="local.qryGetDAForName" dbtype="query" maxrows="1">
			select DiscActionCount, jurisdictions
			from [local].qryDiscActions
			where uniqueNameID = #local.qryResults.uniqueNameID#
		</cfquery>

		<cfif local.qryResults.inCart>
			<cfset local.inCartStyle = "" />
			<cfset local.addToCartStyle = "display:none;" />
		<cfelse>
			<cfset local.inCartStyle = "display:none;" />
			<cfset local.addToCartStyle = "" />
		</cfif>
		<div class="tsAppBodyText s_row <cfif local.qryResults.currentrow mod 2 is 0>s_row_alt</cfif>">
			<div class="s_act">
				<div class="s_opt" style="#local.inCartStyle#" id="s_incart#local.qryResults.uniqueNameID#">
					<a href="javascript:viewCart();" title="View your cart/checkout"><img src="#application.paths.images.url#images/search/cart.png" width="16" height="16" align="left" border="0" />In Cart</a>
				</div>
				<cfif not local.qryResults.owned and variables.cfcuser_DisplayVersion gt 1 >
					<div class="s_opt" style="#local.addToCartStyle#" id="s_addtocart#local.qryResults.uniqueNameID#">
						<a href="javascript:oneClickPurchaseDAReport(#local.qryResults.uniqueNameID#,#arguments.searchid#);" title="Buy Actions"><img src="#application.paths.images.url#images/search/page_white_put.png" width="16" height="16" align="left" border="0">View Report</a>
					</div>
				<cfelseif local.qryResults.owned>
					<div class="s_opt"><a href="javascript:viewDAReport(#local.qryResults.owned#);" title="View Report"><img src="#application.paths.images.url#images/search/page_white_put.png" width="16" height="16" align="left" border="0" />View Report</a></div>
				</cfif>
			</div>
			<div>
			<img src="#application.paths.images.url#images/search/page.png" width="16" height="16" border="0" align="left" alt="[Document]">
				<cfif len(local.qryResults.companyName) and len(local.qryResults.lastname) eq 0 and len(local.qryResults.firstname) eq 0>
					<b>#local.qryResults.companyName#</b>  (#local.qryGetDAForName.DiscActionCount# action<cfif local.qryGetDAForName.discActionCount is not 1>s</cfif>)
				<cfelse>
					<b>#local.qryResults.lastname#, #local.qryResults.firstname#<cfif len(local.qryResults.middlename)> #local.qryResults.middlename#</cfif></b> (#local.qryGetDAForName.DiscActionCount# action<cfif local.qryGetDAForName.discActionCount is not 1>s</cfif>)
				</cfif>
			</div>
			<div class="s_dtl">
				<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned>
					<cfif len(local.qryResults.companyName)>
						Company Name: #local.qryResults.companyName#
						<br/>
					</cfif>
					<cfif len(local.qryResults.lastname) GT 0 OR len(local.qryResults.firstname) GT 0>								
						Full Name: #local.qryResults.salutation# #local.qryResults.firstname# #local.qryResults.middlename# #local.qryResults.lastname# #local.qryResults.suffix# 
							<cfif len(local.qryResults.professionalSuffix)>, #local.qryResults.professionalSuffix#</cfif>
							<br/>
					</cfif>	
					<cfif len(local.qryGetDAForName.jurisdictions)>
						Jurisdiction<cfif listLen(local.qryGetDAForName.jurisdictions) gt 1>s</cfif>: #local.qryGetDAForName.jurisdictions#<br/>
					</cfif>
				<cfelse>
					<a href="/?pg=upgradeTrialSmith">Hidden - Click here to upgrade your TrialSmith Plan and view ALL Disciplinary Action reports</a>
				</cfif>
			</div>
		</div>
	</cfoutput>
	<cfoutput>
	<br clear="all"/>
	#showSearchResultsPaging('bottom',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'action','s')#
	</cfoutput>
</cfif>