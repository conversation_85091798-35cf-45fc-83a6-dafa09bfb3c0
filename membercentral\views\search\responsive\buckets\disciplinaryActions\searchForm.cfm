<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

 <div id="searchBoxError" class="alert alert-error hide"></div> 

<cfform name="frms_Search" id="frms_Search" class="form-horizontal form-medium-lg" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();">
	<div class="control-group">
		<label class="control-label" for="s_fname">Professional's First Name:</label>
		<div class="controls">
			<cfinput type="text" name="s_fname" id="s_fname" value="#local.strSearchForm.s_fname#" size="14" maxlength="50">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_lname">Professional's Last Name:</label>
		<div class="controls">
			<cfinput type="text" name="s_lname" id="s_lname" value="#local.strSearchForm.s_lname#" size="14" maxlength="50">
		</div>
	</div>
	<div class="control-group">
		<strong>Tip:</strong> Looking for a Company or Institution? Use the following Text Fields to search for the company or institution.
	</div>
	<div class="control-group">
		<label class="control-label" for="da_s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_all" id="da_s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_phrase"  id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_one"  id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_x"><b>Without</b> the words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_x"  id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<cfinput type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</cfform>
</cfoutput>