<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
		$(document).ready(function(){
			mca_setupDatePickerRangeFields('s_moddatefrom','s_moddateto');
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>
<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	<cfif (not local.settingsStruct.allowSelectors.communities) and local.settingsStruct.restrictcommunity.applicationInstanceID gt 0>
		<input type="hidden" name="s_applicationinstanceid"  id="s_applicationinstanceid" value="#local.settingsStruct.restrictcommunity.applicationInstanceID#">
	<cfelseif not local.settingsStruct.allowSelectors.communities>
		<input type="hidden" name="s_applicationinstanceid"  id="s_applicationinstanceid" value="">
	</cfif>
	<cfif (not local.settingsStruct.allowSelectors.sections) and local.settingsStruct.restrictsection.siteResourceID gt 0>
		<input type="hidden" name="s_siteresourceid"  id="s_siteresourceid" value="#local.settingsStruct.restrictsection.siteResourceID#">
	<cfelseif (not local.settingsStruct.allowSelectors.sections)>
		<input type="hidden" name="s_siteresourceid"  id="s_siteresourceid" value="">
	</cfif>
	<cfif local.settingsStruct.restrictType NEQ "">
		<input type="hidden" name="s_type"  id="s_type" value="#local.settingsStruct.restrictType#">
	</cfif>

	<div class="control-group">
		<label class="control-label" for="s_fname">First Name:</label>
		<div class="controls">
			<input type="text" name="s_fname"  id="s_fname" value="#local.strSearchForm.s_fname#" size="14" maxlength="50">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_lname">Last Name:</label>
		<div class="controls">
			<input type="text" name="s_lname"  id="s_lname" value="#local.strSearchForm.s_lname#" size="14" maxlength="50">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label">Last Modified Between:</label>
		<div class="controls">
			<input type="text" id="s_moddatefrom" name="s_moddatefrom" value="#DateFormat(local.strSearchForm.s_moddatefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol"> and 
			<input type="text" id="s_moddateto" name="s_moddateto" value="#DateFormat(local.strSearchForm.s_moddateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_moddatefrom','s_moddateto');return false;">clear dates</a>
		</div>
	</div>
	<cfif local.settingsStruct.restrictType EQ "">
		<div class="control-group">
			<label class="control-label" for="s_type">Limit to:</label>
			<div class="controls">
				<select name="s_type"  id="s_type">
					<option value="">Both pages and documents</option>
					<option value="P" <cfif local.strSearchForm.s_type eq "P">selected</cfif>>Pages only</option>
					<option value="D" <cfif local.strSearchForm.s_type eq "D">selected</cfif>>Documents only</option>
				</select>
			</div>
		</div>
	</cfif>
	<cfif local.settingsStruct.allowSelectors.sections>
		<div class="control-group">
			<label class="control-label" for="s_siteresourceid">Section:</label>
			<div class="controls">
				<select name="s_siteresourceid"  id="s_siteresourceid">
					<cfloop query="local.sections">
						<cfif local.strSearchForm.s_siteresourceid eq local.sections.siteResourceID>
							<option value="#local.sections.siteResourceID#" selected="selected">#local.sections.thePathExpanded#</option>
						<cfelse>
							<option value="#local.sections.siteResourceID#">#local.sections.thePathExpanded#</option>
						</cfif>
					</cfloop>
				</select>
			</div>
		</div>
	</cfif>
	<cfif local.settingsStruct.allowSelectors.communities>
		<div class="control-group">
			<label class="control-label" for="s_applicationinstanceid">Community:</label>
			<div class="controls">
				<select name="s_applicationinstanceid"  id="s_applicationinstanceid">
					<option value=""></option>
					<cfloop query="local.communities">
						<cfif local.strSearchForm.s_applicationinstanceid eq local.communities.applicationInstanceID>
							<option value="#local.communities.applicationInstanceID#" selected="selected">#left(local.communities.applicationInstanceName,60)#</option>
						<cfelse>
							<option value="#local.communities.applicationInstanceID#">#left(local.communities.applicationInstanceName,60)#</option>
						</cfif>
					</cfloop>
				</select>
			</div>
		</div>
	</cfif>

	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one"><b>Without</b> the words:</label>
		<div class="controls">
			<input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>

</cfoutput>