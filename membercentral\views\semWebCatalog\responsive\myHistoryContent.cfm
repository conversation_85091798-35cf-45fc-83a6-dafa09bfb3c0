<cfset local.featuredProgramsCount = arrayLen(attributes.data.strFeaturedPrograms.arrPrograms)>

<cfinclude template="/views/semwebCatalog/responsive/swCatalogcommonCSS.cfm">
<cfsavecontent variable="local.myHistoryJS">
	<cfoutput>
	<script type="text/javascript">
		function viewCert(eId) {
			var certURL = '#attributes.event.getValue('mainurl')#&panel=viewCert&mode=direct&eId=' + eId;
			window.open(certURL,'ViewCertificate','width=670,height=500');
		}
		function viewCPCert(pId) {
			var certURL = '#attributes.event.getValue('mainurl')#&panel=viewCPCert&mode=direct&pId=' + pId + '&did=#attributes.data.depomemberdataid#';
			window.open(certURL,'ViewCertificate','width=670,height=500');
		}
		function showBlockedPopup(pg) { self.location.href=pg + '&nopop=1'; }
			
		function addCredit() {
			$.colorbox( {innerWidth:700, innerHeight:450, href:'#attributes.event.getValue('mainurl')#&panel=addCredit&mode=direct', iframe:true, overlayClose:false} );
		}	
		function confirmDelete(srcid) {
			if (confirm("Are you sure you want to delete this Self-Reported Credit?")) self.location.href = '#attributes.event.getValue('mainurl')#&panel=deleteSelfCLE&ID=' + srcid;
		}
		function toggleProgramsList(f, type) {
			var el = $('##programsAccordion ##collapse' + type + '.collapse');
			if(el.siblings().find('.collapsed').length) el.collapse('show');
		}
		function onSubmitMyHistoryFilter() {
			$('##btnFilterMyHistory').html('<div><i class="icon-refresh icon-spin"></i> Please Wait...</div>').prop('disabled',true);
			return true;
		}
		function exportMyHistoryPDF(){
			var filtersUrlString = $(".swHistory ##frmFilter :input").filter(function(index, element) { return $(element).val() != '';}).serialize();
			self.location.href = '#attributes.event.getValue('mainurl')#&panel=My&exportPDF=1&mode=stream' + (filtersUrlString.length ? '&' + filtersUrlString : '');
		}
		function closeBox() { $.colorbox.close(); }

		$(function() {
			mca_setupDatePickerRangeFields('fStartDate','fEndDate');
			mca_setupCalendarIcons('frmFilter');

			$(".swHistory .swCatalogTabList a").click(function(){
				var type = $(this).data('programtype'); 
				toggleProgramsList(true, type);
				$('html,body').animate({ scrollTop: $('##accordion' + type).offset().top - 100}, 500);
			});
			
			<cfif attributes.data.semWeb.qrySWP.isSWOD AND arguments.event.getTrimValue('_swft','') EQ 'SWOD'>
				$('.swPrimaryHover[data-programtype="SWOD"]').click();
			<cfelseif attributes.data.semWeb.qrySWP.isSWL AND arguments.event.getTrimValue('_swft','') EQ 'SWL'>
				$('.swPrimaryHover[data-programtype="SWL"]').click();
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.myHistoryJS#">

<cfoutput>
<div class="swCatalogWrapper swCatalog swHistory">
	<div id="swHeaderDiv">
		<cfinclude template="/views/semwebCatalog/responsive/swPanelHeaderSectionCommon.cfm">
	</div>
	<div class="row-fluid">
		<div class="<cfif local.featuredProgramsCount eq 0>span12<cfelse>span9</cfif>">
			<p>Access all of your purchased programs and titles, reprint certificates, and review supporting materials discussed in your programs.</p>
			<div class="row-fluid sw-d-print-none">
				<div class="span12 dateFilterSection">
					<div class="dateFilterLabel">
						<small><strong>FILTER BY DATE </strong></small> <i class="bi bi-info-circle-fill tooltip-icon swPrimary" aria-hidden="true" title="Filter looks at start date for live programs and enrollment date for on demand programs."></i>
					</div>
					<form name="frmFilter" id="frmFilter" class="form-inline" method="post" action="#attributes.event.getValue('mainurl')#&panel=My" onsubmit="return onSubmitMyHistoryFilter();">
						<div class="sw-d-inline-block">
							<div class="swCatalogSearchKey">
								<input type="text" name="fStartDate" id="fStartDate" value="#attributes.data.strHistory.fStartDate#" class="dateControl" placeholder="Start Date">
								<button type="button" class="swPrimaryBkgd swWhite calendar-button" data-target="fStartDate"><i class="bi bi-calendar-fill" aria-hidden="true"></i></button>
							</div>
							<div class="text-right"><small><a href="javascript:mca_clearDateRangeField('fStartDate');" class="clear muted">clear</a></small></div>
						</div>
						<div class="sw-d-inline-block">
							<div class="swCatalogSearchKey">
								<input type="text" name="fEndDate" id="fEndDate" value="#attributes.data.strHistory.fEndDate#" class="dateControl" placeholder="End Date">
								<button type="button" class="swPrimaryBkgd swWhite calendar-button" data-target="fEndDate"><i class="bi bi-calendar-fill" aria-hidden="true"></i></button>
							</div>
							<div class="text-right"><small><a href="javascript:mca_clearDateRangeField('fEndDate');" class="clear muted">clear</a></small></div>
						</div>
						<div class="sw-d-inline-block sw-align-top sw-ml-2">
							<button type="submit" name="btnFilterMyHistory" id="btnFilterMyHistory" class="btn swPrimaryBkgd swWhite"><i class="bi-funnel-fill"></i> Filter</button>
						</div>
						<div class="sw-d-inline-block pull-right swCatalogPrintBtn">
							<a href="javascript:exportMyHistoryPDF();" class="swPrimaryHover"><i class="bi bi-file-earmark-text" aria-hidden="true"></i>PDF</a>
						</div>
					</form>
				</div>
			</div>

			<div class="row-fluid sw-d-print-none">
				<div class="span12">
					<small><strong>JUMP TO:</strong></small>
					<ul class="swCatalogTabList">
						<cfif attributes.data.semWeb.qrySWP.isSWL>
							<li><a href="javascript:void(0);" class="muted swPrimaryHover" data-programtype="SWL" data-label="#attributes.data.semWeb.qrySWP.brandSWLTab#">#attributes.data.semWeb.qrySWP.brandSWLTab#</a></li>
						</cfif>
						<cfif attributes.data.semWeb.qrySWP.isSWOD>
							<li><a href="javascript:void(0);" class="muted swPrimaryHover" data-programtype="SWOD" data-label="#attributes.data.semWeb.qrySWP.brandSWODTab#">#attributes.data.semWeb.qrySWP.brandSWODTab#</a></li>
						</cfif>
						<cfif attributes.data.strHistory.qryCertPrograms.recordCount>
							<li><a href="javascript:void(0);" class="muted swPrimaryHover" data-programtype="CP" data-label="Certificate Programs">Certificate Programs</a></li>
						</cfif>
						<cfif attributes.data.semWeb.qrySWP.isSelfReportCredit>
							<li><a href="javascript:void(0);" class="muted swPrimaryHover" data-programtype="SRC" data-label="Self Reported Credits">Self Reported Credits</a></li>
						</cfif>
					</ul>
				</div>
			</div>

			<div class="row-fluid">
				<div class="span12">
					<div class="accordion" id="programsAccordion">
						<cfif attributes.data.semWeb.qrySWP.isSWL>
							<div class="accordion-group" id="accordionSWL">
								<div class="accordion-heading">
									<a class="accordion-toggle sw-d-flex swPrimary" data-toggle="collapse" href="##collapseSWL" aria-expanded="true">
										<h4 class="swPrimary mr-auto">#attributes.data.semWeb.qrySWP.brandSWLTab#</h4> <i class="bi bi-chevron-compact-down accordion-toggle-icon sw-ml-2"></i>
									</a>
								</div>
								<div id="collapseSWL" class="accordion-body collapse in">
									<div class="accordion-inner">
										<table class="table table-striped swCatalogPrgmsTbl">
											<thead>
												<tr>
													<th class="muted" width="15%" nowrap><small>Program Date</small></th>
													<th class="muted" width="60%" nowrap><small>Program Title / Credits</small></th>
													<th class="muted" width="10%"><small>Status</small></th>
													<th class="muted text-center sw-d-print-none" width="15%"><small>Options</small></th>
												</tr>
											</thead>
											<tbody>
												<cfif arrayLen(attributes.data.strHistory.arrSWL)>
													<cfloop array="#attributes.data.strHistory.arrSWL#" index="local.thisProgram">
														<tr>
															<td>#local.thisProgram.date#</td>
															<td>
																<cfif local.thisProgram.linkToDetailPage>
																	<a href="#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.seminarID#" class="swPrimary">#local.thisProgram.title#</a>
																<cfelse>
																	#local.thisProgram.title#
																</cfif>
																<cfif len(local.thisProgram.creditDetails)>
																	<div><small>#local.thisProgram.creditDetails#</small></div>
																</cfif>
															</td>
															<td class="swCatalogPrgmStatus" nowrap>
																<small>
																	<cfif len(local.thisProgram.enterProgramLink)>
																		<a href="#local.thisProgram.SWLPlayerLink#" class="swPrimary" target="_blank">Enter Program</a>
																	<cfelse>
																		#local.thisProgram.status#
																	</cfif>
																	<cfif len(local.thisProgram.completedDate)><br/>#local.thisProgram.completedDate#</cfif>
																</small>
															</td>
															<td class="swCatalogPrgmActions swPrimary text-center sw-d-print-none" nowrap>
																<cfif local.thisProgram.canViewCertificate>
																	<a href="##" onclick="viewCert('#local.thisProgram.encryptedEID#');return false;" class="swPrimary"><i class="bi bi-award tooltip-icon" aria-hidden="true" title="View Certificate"></i></a>
																<cfelse>
																	<i class="bi bi-award tooltip-icon text-light-grey" aria-hidden="true" title="View Certificate"></i>
																</cfif>
																<cfif local.thisProgram.canViewReplayLink AND len(local.thisProgram.replayVideoLink)>
																	<a href="#local.thisProgram.SWLPlayerLink#" target="_blank" class="swPrimary"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Replay Video"></i></a>
																<cfelseif attributes.data.semWeb.qrySWP.offerSWLReplays is 1>
																	<i class="bi bi-arrow-counterclockwise tooltip-icon text-light-grey" aria-hidden="true" title="Replay Video"></i>
																</cfif>
																<cfif local.thisProgram.materialsDoc gt 0>
																	<a href="#local.thisProgram.SWLPlayerLink#" target="_blank" class="swPrimary"><i class="bi bi-download tooltip-icon" aria-hidden="true" title="Download Materials"></i></a>
																<cfelse>
																	<i class="bi bi-download tooltip-icon text-light-grey" aria-hidden="true" title="Download Materials"></i>
																</cfif>
															</td>
														</tr>
													</cfloop>
												<cfelse>
													<tr>
														<td colspan="4" class="text-center">No Registrations Found.</td>
													</tr>
												</cfif>
											</tbody>
										</table>

										<div class="swCatalogPrgmsWell sw-d-print-none">
											<cfif arrayLen(attributes.data.strHistory.arrSWL)>
												<cfloop array="#attributes.data.strHistory.arrSWL#" index="local.thisProgram">
													<div class="well well-small">
														<p>
															<cfif local.thisProgram.linkToDetailPage>
																<h5><a href="#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.seminarID#" class="swPrimary">#local.thisProgram.title#</a></h5>
															<cfelse>
																<h5>#local.thisProgram.title#</h5>
															</cfif>
														</p>
														<p><b>Program Date:</b> #local.thisProgram.date#</p>
														<cfif len(local.thisProgram.creditDetails)>
															<p><small>#local.thisProgram.creditDetails#</small></p>
														</cfif>
														<p>
															<cfif len(local.thisProgram.enterProgramLink)>
																<a href="#local.thisProgram.SWLPlayerLink#" class="swPrimary" target="_blank">Enter Program</a>
															<cfelse>
																<b>Status:</b> #local.thisProgram.status#<cfif len(local.thisProgram.completedDate)> - #local.thisProgram.completedDate#</cfif>
															</cfif>
														</p>
														<p class="swCatalogPrgmActions">
															<b>Options:</b>
															<span class="swPrimary">
																<cfif local.thisProgram.canViewCertificate>
																	<a href="##" onclick="viewCert('#local.thisProgram.encryptedEID#');return false;" class="action sw-d-inline-block swPrimary"><i class="bi bi-award" aria-hidden="true" title="View Certificate"></i><small>View Certificate</small></a>
																<cfelse>
																	<span class="action sw-d-inline-block text-light-grey"><i class="bi bi-award" aria-hidden="true" title="View Certificate"></i><small>View Certificate</small></span>
																</cfif>
																<cfif len(local.thisProgram.replayVideoLink)>
																	<a href="#local.thisProgram.SWLPlayerLink#" target="_blank" class="action sw-d-inline-block swPrimary"><i class="bi bi-arrow-counterclockwise" aria-hidden="true" title="Replay Video"></i><small>Replay Video</small></a>
																<cfelseif attributes.data.semWeb.qrySWP.offerSWLReplays is 1>
																	<span class="action sw-d-inline-block text-light-grey"><i class="bi bi-arrow-counterclockwise" aria-hidden="true" title="Replay Video"></i><small>Replay Video</small></span>
																</cfif>
																<cfif local.thisProgram.materialsDoc gt 0>
																	<a href="#local.thisProgram.SWLPlayerLink#" target="_blank" class="action sw-d-inline-block swPrimary"><i class="bi bi-download" aria-hidden="true" title="Download Materials"></i><small>Download Materials</small></a>
																<cfelse>
																	<span class="action sw-d-inline-block text-light-grey"><i class="bi bi-download" aria-hidden="true" title="Download Materials"></i><small>Download Materials</small></span>
																</cfif>
															</span>
														</p>
													</div>
												</cfloop>
											<cfelse>
												No Registrations Found.
											</cfif>
										</div>
									</div>
								</div>
							</div>
						</cfif>

						<cfif attributes.data.semWeb.qrySWP.isSWOD>
							<div class="accordion-group" id="accordionSWOD">
								<div class="accordion-heading">
									<a class="accordion-toggle sw-d-flex swPrimary" data-toggle="collapse" href="##collapseSWOD" aria-expanded="true"> <h4 class="swPrimary mr-auto">#attributes.data.semWeb.qrySWP.brandSWODTab#</h4> <i class="bi bi-chevron-compact-down accordion-toggle-icon sw-ml-2"></i> </a> 
								</div>
								<div id="collapseSWOD" class="accordion-body collapse in">
									<div class="accordion-inner">
										<table class="table table-striped swCatalogPrgmsTbl">
											<thead>
												<tr>
													<th class="muted" width="15%" nowrap><small>Enrolled Date</small></th>
													<th class="muted" width="60%" nowrap><small>Program Title / Credits</small></th>
													<th class="muted" width="10%"><small>Status</small></th>
													<th class="muted text-center sw-d-print-none" width="15%"><small>Options</small></th>
												</tr>
											</thead>
											<tbody>
												<cfif arrayLen(attributes.data.strHistory.arrSWOD)>
													<cfloop array="#attributes.data.strHistory.arrSWOD#" index="local.thisProgram">
														<tr>
															<td>#local.thisProgram.dateEnrolled#</td>
															<td>
																<span<cfif len(local.thisProgram.creditCompleteByDate)> class="sw-mr-2"</cfif>>
																	<cfif local.thisProgram.linkToDetailPage>
																		<a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.seminarID#" class="swPrimary">#local.thisProgram.title#</a>
																	<cfelse>
																		#local.thisProgram.title#
																	</cfif>
																</span>
																<cfif len(local.thisProgram.creditCompleteByDate)><span class="text-warning sw-d-inline-block small">Must be completed by #local.thisProgram.creditCompleteByDate# #local.thisProgram.creditCompleteByDateTime# CT to earn credit.</span></cfif>
																<cfif len(local.thisProgram.creditDetails)>
																	<div><small>#local.thisProgram.creditDetails#</small></div>
																</cfif>
																
															</td>
															<td class="swCatalogPrgmStatus" nowrap>
																<small>
																	<cfif len(local.thisProgram.enterProgramLink)>
																		<a href="#local.thisProgram.enterProgramLink#" class="swPrimary" target="_blank">Enter Program</a>
																	<cfelseif len(local.thisProgram.status)>
																		#local.thisProgram.status#
																	</cfif>
																	<cfif len(local.thisProgram.completedDate)><br/>#local.thisProgram.completedDate#</cfif>
																</small>
															</td>
															<td class="swCatalogPrgmActions swPrimary text-center sw-d-print-none" nowrap>
																<cfif local.thisProgram.canViewCertificate>
																	<a href="##" onclick="viewCert('#local.thisProgram.encryptedEID#');return false;" class="swPrimary"><i class="bi bi-award tooltip-icon" aria-hidden="true" title="View Certificate"></i></a>
																<cfelse>
																	<i class="bi bi-award tooltip-icon text-light-grey" aria-hidden="true" title="View Certificate"></i>
																</cfif>
																<cfif len(local.thisProgram.reviewProgramLink)>
																	<a href="#local.thisProgram.reviewProgramLink#" target="_blank" class="swPrimary"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Review Program"></i></a>
																<cfelse>
																	<i class="bi bi-arrow-counterclockwise tooltip-icon text-light-grey" aria-hidden="true" title="Review Program"></i>
																</cfif>
															</td>
														</tr>
													</cfloop>
												<cfelse>
													<tr>
														<td colspan="4" class="text-center">No Registrations Found.</td>
													</tr>
												</cfif>
											</tbody>
										</table>

										<div class="swCatalogPrgmsWell sw-d-print-none">
											<cfif arrayLen(attributes.data.strHistory.arrSWOD)>
												<cfloop array="#attributes.data.strHistory.arrSWOD#" index="local.thisProgram">
													<div class="well well-small">
														<p>
															<cfif local.thisProgram.linkToDetailPage>
																<h5><a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.seminarID#" class="swPrimary">#local.thisProgram.title#</a></h5>
															<cfelse>
																<h5>#local.thisProgram.title#</h5>
															</cfif>
															<cfif len(local.thisProgram.creditCompleteByDate)><span class="text-warning sw-d-inline-block">Must be completed by #local.thisProgram.creditCompleteByDate# #local.thisProgram.creditCompleteByDateTime# CT to earn credit</span></cfif>
														</p>
														<p><b>Enrolled Date :</b>  #local.thisProgram.dateEnrolled#</p>
														<cfif len(local.thisProgram.creditDetails)><p><small>#local.thisProgram.creditDetails#</small></p></cfif>
														
														<p>
															<cfif len(local.thisProgram.enterProgramLink)>
																<a href="#local.thisProgram.enterProgramLink#" class="swPrimary" target="_blank">Enter Program</a>
															<cfelseif len(local.thisProgram.status)>
																<b>Status :</b>
																<span>#local.thisProgram.status# <cfif len(local.thisProgram.completedDate)> - #local.thisProgram.completedDate#</cfif></span>
															</cfif>
														</p>
														<cfif local.thisProgram.canViewCertificate or len(local.thisProgram.reviewProgramLink)>
															<p class="swCatalogPrgmActions">
																<b>Options :</b>
																<span class="swPrimary">
																	<cfif local.thisProgram.canViewCertificate>
																		<a href="##" onclick="viewCert('#local.thisProgram.encryptedEID#');return false;" class="action sw-d-inline-block swPrimary"><i class="bi bi-award" aria-hidden="true" title="View Certificate"></i><small>View Certificate</small></a>
																	<cfelse>
																		<span class="action sw-d-inline-block text-light-grey"><i class="bi bi-award" aria-hidden="true" title="Certificate Not Available"></i><small>View Certificate</small></span>
																	</cfif>
																	<cfif len(local.thisProgram.reviewProgramLink)>
																		<a href="#local.thisProgram.reviewProgramLink#" target="_blank" class="action sw-d-inline-block swPrimary"><i class="bi bi-arrow-counterclockwise" aria-hidden="true" title="Review Program"></i><small>Review Program</small></a>
																	<cfelse>
																		<span class="action sw-d-inline-block text-light-grey"><i class="bi bi-arrow-counterclockwise" aria-hidden="true" title="Review Program"></i><small>Review Program</small></span>
																	</cfif>
																</span>
															</p>
														</cfif>
													</div>
												</cfloop>
											<cfelse>
												No Registrations Found.
											</cfif>
										</div>
									</div>
								</div>
							</div>
						</cfif>

						<cfif attributes.data.strHistory.qryCertPrograms.recordCount>
							<div class="accordion-group" id="accordionCP">
								<div class="accordion-heading">
									<a class="accordion-toggle sw-d-flex swPrimary" data-toggle="collapse" href="##collapseCP" aria-expanded="true">
										<h4 class="swPrimary mr-auto">Certificate Programs</h4>
										<i class="bi bi-chevron-compact-down accordion-toggle-icon sw-ml-2"></i>
									</a>
								</div>
								<div id="collapseCP" class="accordion-body collapse in">
									<div class="accordion-inner">
										<table class="table swCatalogPrgmsTbl">
											<thead>
												<tr>
													<th class="muted" width="60%" nowrap><small>Program Name</small></th>
													<th class="muted" width="15%" nowrap><small>Enrolled Date</small></th>
													<th class="muted" width="10%"><small>Status</small></th>
													<th class="muted text-center sw-d-print-none" width="15%"><small>Options</small></th>
												</tr>
											</thead>
											<tbody>
												<cfif arrayLen(attributes.data.strHistory.arrCertPrograms)>
													<cfloop array="#attributes.data.strHistory.arrCertPrograms#" index="local.thisProgram">
														<tr>
															<td>#local.thisProgram.programName#</td>
															<td></td>
															<td class="swCatalogPrgmStatus" nowrap><small>#local.thisProgram.status#</small></td>
															<td class="swCatalogPrgmActions swPrimary text-center sw-d-print-none" nowrap>
																<cfif local.thisProgram.canViewCertificate>
																	<a href="##" onclick="viewCPCert('#local.thisProgram.programID#');return false;" class="swPrimary"><i class="bi bi-award tooltip-icon" aria-hidden="true" title="View Certificate"></i></a>
																<cfelse>
																	<i class="bi bi-award tooltip-icon text-light-grey" aria-hidden="true" title="Certificate Not Earned"></i>
																</cfif>
															</td>
														</tr>
														<cfloop array="#local.thisProgram.arrEnrollments#" index="local.thisEnrollment">
															<tr>
																<td>
																	<cfif len(local.thisEnrollment.dateCompleted)>
																		<i class="icon-check"></i>
																	<cfelse>
																		<i class="icon-check-empty"></i>
																	</cfif>
																	#encodeForHTML(local.thisEnrollment.seminarname)#
																</td>
																<td>#local.thisEnrollment.dateEnrolled#</td>
																<td>
																	<small>#local.thisEnrollment.status#</small>
																	<cfif len(local.thisEnrollment.seminarLink)>
																		<a href="#local.thisEnrollment.seminarLink#" target="_blank" class="swPrimary">#local.thisEnrollment.seminarLinkText#</a>
																	</cfif>
																</td>
																<td class="swCatalogPrgmActions swPrimary text-center sw-d-print-none" nowrap>
																	<cfif len(local.thisEnrollment.swodReviewLink)>
																		<a href="#local.thisEnrollment.swodReviewLink#" target="_blank" class="swPrimary"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Review Program"></i></a>
																	</cfif>
																</td>
															</tr>
														</cfloop>
													</cfloop>
												<cfelse>
													<tr><td colspan="4" class="text-center">You have not enrolled in or completed any certificate programs.</td></tr>
												</cfif>
											</tbody>
										</table>

										<div class="swCatalogPrgmsWell sw-d-print-none">
											<cfif arrayLen(attributes.data.strHistory.arrCertPrograms)>
												<cfloop array="#attributes.data.strHistory.arrCertPrograms#" index="local.thisProgram">
													<div class="well well-small">
														<p><h5>#local.thisProgram.programName#</h5></p>
														<p><b>Status:</b> #local.thisProgram.status#</p>
														<p class="swCatalogPrgmActions">
															<b>Options :</b>
															<span class="swPrimary">
																<cfif local.thisProgram.canViewCertificate>
																	<a href="##" onclick="viewCPCert('#local.thisProgram.programID#');return false;" class="action sw-d-inline-block swPrimary"><i class="bi bi-award" aria-hidden="true" title="View Certificate"></i><small>View Certificate</small></a>
																<cfelse>
																	<span class="action sw-d-inline-block text-light-grey"><i class="bi bi-award" aria-hidden="true" title="View Certificate"></i><small>View Certificate</small></span>
																</cfif>
															</span>
														</p>
														<cfloop array="#local.thisProgram.arrEnrollments#" index="local.thisEnrollment">
															<p>
																<h6>
																	<cfif len(local.thisEnrollment.dateCompleted)>
																		<img src="/assets/common/images/checkmark12x12.gif" width="12" height="12">
																	<cfelse>
																		<strong class="dot">&middot;</strong>
																	</cfif>
																	#encodeForHTML(local.thisEnrollment.seminarname)#
																</h6>
															</p>
															<p><b>Enrolled Date :</b> #local.thisEnrollment.dateEnrolled#</p>
															<p>
																#local.thisEnrollment.status#
																<cfif len(local.thisEnrollment.seminarLink)>
																	<a href="#local.thisEnrollment.seminarLink#" target="_blank" class="swPrimary">#local.thisEnrollment.seminarLinkText#</a>
																</cfif>
															</p>
															<cfif len(local.thisEnrollment.swodReviewLink)>
																<p class="swCatalogPrgmActions">
																	<b>Options :</b>
																	<span class="swPrimary">
																		<a href="#local.thisEnrollment.swodReviewLink#" target="_blank" class="swPrimary"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Review Program"></i></a>
																	</span>
																</p>
															</cfif>
														</cfloop>
													</div>
												</cfloop>
											<cfelse>
												You have not enrolled in or completed any certificate programs.
											</cfif>
										</div>
									</div>
								</div>
							</div>
						</cfif>

						<cfif attributes.data.semWeb.qrySWP.isSelfReportCredit>
							<div class="accordion-group" id="accordionSRC">
								<div class="accordion-heading">
									<a class="accordion-toggle sw-d-flex swPrimary" data-toggle="collapse" href="##collapseSRC" aria-expanded="true">
										<h4 class="swPrimary mr-auto">Self Reported Credits</h4> <i class="bi bi-chevron-compact-down accordion-toggle-icon sw-ml-2"></i>
									</a>
								</div>
								<div id="collapseSRC" class="accordion-body collapse in">
									<div class="accordion-inner">
										<table class="table table-striped swCatalogPrgmsTbl">
											<thead>
												<tr>
													<th class="muted" width="15%" nowrap><small>Event Date</small></th>
													<th class="muted" width="60%" nowrap><small>Event Name / Course Identifier</small></th>
													<th class="muted" width="10%"><small>Credits Earned</small></th>
													<th class="muted text-center sw-d-print-none" width="15%"><small>Options</small></th>
												</tr>
											</thead>
											<tbody>
												<cfif arrayLen(attributes.data.strHistory.strSelfReportedCredits.arrSelfReportedCredits)>
													<cfloop array="#attributes.data.strHistory.strSelfReportedCredits.arrSelfReportedCredits#" index="local.thisCredit">
														<tr>
															<td>#local.thisCredit.eventDate#</td>
															<td>#local.thisCredit.eventName#<cfif len(local.thisCredit.eventName) and len(local.thisCredit.courseNumber)> / </cfif>#local.thisCredit.courseNumber#</td>
															<td class="swCatalogPrgmStatus" nowrap>
																<cfloop array="#local.thisCredit.arrCreditTypes#" index="local.thisCreditType">
																	<small>#local.thisCreditType.value# #local.thisCreditType.displayname#</small>
																	<br/>
																</cfloop>
															</td>
															<td class="swCatalogPrgmActions swPrimary text-center sw-d-print-none" nowrap>
																<a href="##" onclick="confirmDelete(#local.thisCredit.selfID#);return false;" class="swPrimary"><i class="bi bi-trash-fill sw-text-danger" aria-hidden="true" title="Delete Self Reported Credit"></i></a>
															</td>
														</tr>
													</cfloop>
													<tr><td colspan="4"></td></tr>
													<tr>
														<td colspan="2">Self-Reported Credit Totals:</td>
														<td colspan="2">
															<cfloop collection="#attributes.data.strHistory.strSelfReportedCredits.strCredits#" item="local.fieldname">
																<p>
																	#NumberFormat(attributes.data.strHistory.strSelfReportedCredits.strCredits[local.fieldname].value,"0.00")#
																	#attributes.data.strHistory.strSelfReportedCredits.strCredits[local.fieldname].displayname#
																</p>
															</cfloop>
														</td>
													</tr>
												<cfelse>
													<tr><td colspan="4" class="text-center">No Self Reported Credits Found.</td></tr>
												</cfif>
											</tbody>
										</table>

										<div class="swCatalogPrgmsWell sw-d-print-none">
											<cfif arrayLen(attributes.data.strHistory.strSelfReportedCredits.arrSelfReportedCredits)>
												<cfloop array="#attributes.data.strHistory.strSelfReportedCredits.arrSelfReportedCredits#" index="local.thisCredit">
													<div class="well well-small">
														<p><h5>#local.thisCredit.eventName#<cfif len(local.thisCredit.eventName) and len(local.thisCredit.courseNumber)> / </cfif>#local.thisCredit.courseNumber#</h5></p>
														<p><b>Event Date:</b> #local.thisCredit.eventDate#</p>
														<p>
															<cfloop array="#local.thisCredit.arrCreditTypes#" index="local.thisCreditType">
																<small>#local.thisCreditType.value# #local.thisCreditType.displayname#</small>
																<br/>
															</cfloop>
														</p>
														<p class="swCatalogPrgmActions">
															<b>Options :</b>
															<span class="sw-text-danger">
																<a href="##" onclick="confirmDelete(#local.thisCredit.selfID#);return false;" class="sw-text-danger"><i class="bi bi-trash-fill" aria-hidden="true" title="Delete Self Reported Credit"></i> Delete</a>
															</span>
														</p>
													</div>
												</cfloop>
												<cfloop collection="#attributes.data.strHistory.strSelfReportedCredits.strCredits#" item="local.fieldname">
													<p>
														#NumberFormat(attributes.data.strHistory.strSelfReportedCredits.strCredits[local.fieldname].value,"0.00")#
														#attributes.data.strHistory.strSelfReportedCredits.strCredits[local.fieldname].displayname#
													</p>
												</cfloop>
											<cfelse>
												No Self Reported Credits Found.
											</cfif>
										</div>
										<div class="row-fluid text-right sw-d-print-none">
											<button type="button" name="btnAdd" class="btn btn-primary" onclick="addCredit();">Add Self-Reported Credit</button>
										</div>
									</div>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</div>
		
		<cfif local.featuredProgramsCount>
			<div class="span3 featuredPrograms sw-d-print-none">
				<h5 class="swPrimary text-center">Featured Offerings</h5>
				<cfloop array="#attributes.data.strFeaturedPrograms.arrPrograms#" index="local.thisProgram">
					<cfset local.programTitleDisplay = attributes.data.objSWBrowse.getTrimmedProgramTitleDisplay(programTitle=local.thisProgram.programTitle, programSubTitle=local.thisProgram.programSubTitle)>
					<div class="thumbnails text-center">
						<div class="span12">
							<cfswitch expression="#local.thisProgram.ft#">
								<cfcase value="SWL">
									<a href="#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.programID#" class="thumbnail swPrimaryBorderHover">
										<cfif len(local.thisProgram.featuredImagePath)>
											<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
										</cfif>
										<span class="swWebinar"><i class="bi bi-laptop" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
										<h5 class="swPrimary text-center">#local.programTitleDisplay#</h5>
										<p><span class="muted">#DateFormat(local.thisProgram.dspStartDate,"mmmm d, yyyy")#</span></p>
									</a>
								</cfcase>
								<cfcase value="SWOD">
									<a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.programID#" class="thumbnail swPrimaryBorderHover">
										<cfif len(local.thisProgram.featuredImagePath)>
											<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
										</cfif>
										<span class="swOnDemand"><i class="bi bi-play-circle" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
										<h5 class="swPrimary text-center">#local.programTitleDisplay#</h5>
										<p><span class="muted">Anytime</span></p>
									</a>
								</cfcase>
								<cfcase value="SWB">
									<a href="#attributes.event.getValue('mainurl')#&panel=showBundle&bundleid=#local.thisProgram.programID#" class="thumbnail swPrimaryBorderHover">
										<cfif len(local.thisProgram.featuredImagePath)>
											<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
										</cfif>
										<span class="swBundle"><i class="bi bi-basket-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
										<h5 class="swPrimary text-center">#local.programTitleDisplay#</h5>
										<cfif local.thisProgram.isSWOD>
											<p><span class="muted">Anytime</span></p>
										</cfif>
									</a>
								</cfcase>
								<cfcase value="EV">
									<a href="#local.thisProgram.eventDetailLink#" class="thumbnail swPrimaryBorderHover">
										<cfif len(local.thisProgram.featuredImagePath)>
											<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
										</cfif>
										<span class="mcEvent"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
										<h5 class="swPrimary text-center">#local.programTitleDisplay#</h5>
										<p><span class="muted">#DateFormat(local.thisProgram.displayStartTime,"mmmm d, yyyy")#</span></p>
									</a>
								</cfcase>
							</cfswitch>
						</div>
					</div>
				</cfloop>
				<div class="row-fluid">
					<div class="span12">
						<a href="#attributes.event.getValue('mainurl')#&panel=browse" class="swPrimaryBkgd swWhite swPrimaryBorder viewCatalogBtn btn btn-primary">View Full Catalog <i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
			</div>
		</cfif>
	</div>
</div>
</cfoutput>