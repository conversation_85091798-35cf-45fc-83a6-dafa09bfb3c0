<cfoutput>		
    <cfif bucketText NEQ "" and structKeyExists(local.appRightsStruct, 'fsAddDocuments') and local.appRightsStruct.fsAddDocuments eq 1>
        <span class="hidden-phone"><br/></span>
        <div class="visible-desktop pull-right" style="margin-left:10px;">
            <a href="/?#variables.thisBaseLink#&fsAction=addDocuments"><img src="#application.paths.images.url#images/search/up24.png" border="0" align="absmiddle">Upload a document</a>
        </div>
        <div class="hidden-desktop pull-right hidden-phone">
            <a class="btn" href="/?#variables.thisBaseLink#&fsAction=addDocuments"><i class="icon-upload icon-large"></i></a>
        </div>
    </cfif>
    <h3>#ucase(arguments.bucketName)#</h3>
    <cfif applicationInstanceName NEQ "">
        This search scans ALL files from the #applicationInstanceName#.<br/>
    </cfif>
    <cfif bucketText NEQ "">
        You may also Browse by #arguments.bucketText#.<br/>
    </cfif>
    <br/>
</cfoutput>