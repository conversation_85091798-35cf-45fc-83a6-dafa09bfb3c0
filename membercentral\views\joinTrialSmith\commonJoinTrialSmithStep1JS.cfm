<cfsavecontent variable="local.joinTrialSmithHead">	
	<cfoutput>
	<script type="text/javascript">
        function loadJoinTrialSmithSteps(step,mode) {
			let stepMode = typeof mode != "undefined" ? mode : 'singlestep';
			let arrSteps = [], arrPromises = [], startStep = 2, endStep = 4;
			
			/* stepMode = all; load all steps (editing cart item) */
			step = stepMode == 'all' ? endStep : Number(step);
			
			if (step >= startStep && step <= endStep) {
				/* stepMode == 'init'; load those covered steps */
				startStep = ['all','init'].indexOf(stepMode) != -1 ? startStep : step;
				arrSteps = Array(step - startStep + 1).fill().map((n,i) => startStep + i);
			}

			arrSteps.forEach(function(stepVal,index) {
				arrPromises.push(
					new Promise(function(resolve, reject) {
						$('##joinTrialSmithStep'+stepVal)
							.html($('##joinTrialSmithLoading').html())
							.load('#arguments.event.getValue('jointrialsmithurl')#&action=showstep&stp='+stepVal,
								function() {
									resolve();
								}
							)
							.show();
					})
				);
			});

			if (arrPromises)
				Promise.all(arrPromises).then(function() { onCompleteLoadingJoinTrialSmithSteps(mode); });
		}

		function onCompleteLoadingJoinTrialSmithSteps(mode) {
			$('.joinTrialSmithSummary').off('click').on('click', function() {
				window['editJoinTrialSmithStep'+$(this).data('jointrialsmithsummarystep')]();
			});
		}

        function assocChange(value){
            var assc = {};
            var arrReq = new Array();
            $(".assocError").remove();
			if (value.length){ 
                assc = {tlastate:value};
            }else{
                if ($.trim($('##tlastate').val()).length == 0) arrReq[arrReq.length] = 'Choose your association from the list provided.';

                if (arrReq.length) {
                    $('html, body').animate({
                        scrollTop: $('##joinTrialSmithStep1').offset().top - 75
                    }, 750);

				    $('##joinTrialSmithStep1 ##joinTrialSmithAsscContainer').prepend('<div class="alert alert-danger error assocError">'+arrReq.join('</br>')+'</div>')
                }
            }
            
            $('##joinTrialSmithStep1SaveLoading')
                .html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
                .load('#arguments.event.getValue('jointrialsmithurl')#&action=saves1',assc,
                    function(resp) {
                        let respObj = JSON.parse(resp);
                        let arrSteps = respObj.loadsteps.split(',');
                        let arrClearSteps = respObj.clearsteps.split(',');
                        
                        $('.joinTrialSmithAsscHide,##joinTrialSmithStep1SaveLoading').hide();

                        arrClearSteps.forEach(function(step,index) {
                            $('##joinTrialSmithStep'+step).html('');
                        });
                        
                        arrSteps.forEach(function(step,index) {
                            loadJoinTrialSmithSteps(step);
                        });
						if(arrSteps.length){
							$('html, body').animate({
								scrollTop: $('##joinTrialSmithStep'+arrSteps[0]).offset().top - 75
							}, 750);
						}						
                    }
                )
                .show();
        }
        
		$(function() {
			try {if ($('##tlaState').val().length) assocChange($('##tlaState').val());}
			catch (e) {}
            <cfif len(session.cfcuser.subscriptionData['4'].strJoinTS.currentStep)>
                loadJoinTrialSmithSteps(#session.cfcuser.subscriptionData['4'].strJoinTS.currentStep#, 'init');
            </cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.joinTrialSmithHead)#">