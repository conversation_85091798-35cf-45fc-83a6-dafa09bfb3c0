<cfscript>
	vAction = event.getValue('action','search');
	local.baseLink 		= '/?pg=verdictDatabase';
	local.viewLink		= local.baseLink & '&action=view';
	local.editLink		= local.baseLink & '&action=edit';
	local.deleteLink	= local.baseLink & '&action=delete';
	local.saveLink		= local.baseLink & '&action=save';
	local.resultsLink	= local.baseLink & '&action=results';
	local.associationEmail	= '<EMAIL>';
	local.divStyle		= "border: 1px solid ##ccc; background-color: ##fff; padding: 8px;";	

</cfscript>
<cfoutput>
	<script>
		function gotoListing(){				
			window.location.href='#local.resultsLink#';
		}			
	</script>
</cfoutput>
<cfswitch expression="#variables.vAction#">
	<!--- view record --->
	<cfcase value="view">
		<cfoutput>
			<div style="#local.divStyle#">
				<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
				<cfif local.qryVerdict.recordcount is 0>
					<cflocation url="#local.baseLink#" addtoken="No">
				</cfif>
				<p><span class="TitleText">Viewing Information in TELA's Verdict and Settlement Exchange Database</span></p>
				<div><input type="button" value="Back to Listing" onclick="gotoListing();" class="bodyText" /></div>
				<br/>
				<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
					<cfif len(local.qryVerdict.date) and isdate(local.qryVerdict.date)>
						<tr valign="top">
							<td><strong>Date:</strong></td>
							<td>#dateformat(local.qryVerdict.date,"mm/dd/yyyy")#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.resolutiontype)>
						<tr valign="top">
							<td><strong>Resolution:</strong></td>
							<td>#local.qryVerdict.resolutiontype#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.categoryName) or len(local.qryVerdict.casetypedetail)>
						<tr valign="top">
							<td><strong>Category:</strong></td>
							<td>[#local.qryVerdict.categoryName#]: #local.qryVerdict.casetypedetail#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.casetitle)>
						<tr valign="top">
							<td><strong>Case:</strong></td>
							<td>#local.qryVerdict.casetitle#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.courtname)>
						<tr valign="top">
							<td><strong>Court:</strong></td>
							<td>#local.qryVerdict.courtname#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.countyname)>
						<tr valign="top">
							<td><strong>County:</strong></td>
							<td>#local.qryVerdict.countyname#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.docketnumber)>
						<tr valign="top">
							<td nowrap><strong>Docket Number:</strong></td>
							<td>#local.qryVerdict.docketnumber#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.sex)>
						<tr valign="top">
							<td><strong>Sex:</strong></td>
							<td>#local.qryVerdict.sex#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.age)>
						<tr valign="top">
							<td><strong>Age:</strong></td>
							<td>#local.qryVerdict.age#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.Occupation)>
						<tr valign="top">
							<td><strong>Occupation:</strong></td>
							<td>#local.qryVerdict.Occupation#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.amount) or len(local.qryVerdict.amountdetail)>
						<tr valign="top">
							<td nowrap><strong>Amount Awarded:</strong></td>
							<td>#dollarformat(local.qryVerdict.amount)# <cfif len(trim(local.qryVerdict.amountdetail)) gt 0> - #local.qryVerdict.amountdetail#</cfif></td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.settlementoffer)>
						<tr valign="top">
							<td nowrap><strong>Settlement Offer:</strong></td>
							<td>#local.qryVerdict.settlementoffer#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.facts)>
						<tr valign="top">
							<td><strong>Facts:</strong></td>
							<td>#local.qryVerdict.facts#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.damages)>
						<tr valign="top">
							<td><strong>Damages:</strong></td>
							<td>#local.qryVerdict.damages#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.experts)>
						<tr valign="top">
							<td><strong>Experts:</strong></td>
							<td>#local.qryVerdict.experts#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.plaintiffattorney)>
						<tr valign="top">
							<td nowrap><strong>Plaintiff Attorney:</strong></td>
							<td>#local.qryVerdict.plaintiffattorney#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.defenseattorney)>
						<tr valign="top">
							<td nowrap><strong>Defense Attorney:</strong></td>
							<td>#local.qryVerdict.defenseattorney#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.submittingattorney)>
						<tr valign="top">
							<td nowrap><strong>Submitting Attorney:</strong></td>
							<td>#local.qryVerdict.submittingattorney#</td>
						</tr>
					</cfif>
				</table>
			</div>	
		</cfoutput>
	</cfcase>

	<cfcase value="edit">
		<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
		<cfset local.allowForm = false>
		
		<cfoutput>
		<div style="#local.divStyle#">
			<!--- if verdictID is 0 ADD	else EDIT --->
		
			<cfif val(event.getValue('verdictID',0))>
				<!--- EDIT Verdict: --->
				<p><span class="TitleText">Edit Information in TELA's Verdict and Settlement Exchange Database</span></p>
				<p class="BodyText">Complete the form below to add a verdict or settlement to the database.</p>
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					<cfset local.allowForm = true>
				<cfelse>
					<p class="bodyText">You do not have permission to edit this information.</p>
				</cfif>
			<cfelse>
				<!--- ADD Verdict: --->
				<p><span class="TitleText">Add to TELA's Verdict and Settlement Exchange Database</span></p>
				<p class="BodyText">Complete the form below to add a verdict or settlement to the database.</p>				
				<cfif val(event.getValue('customPage.myRights.view',0))>
					<cfset local.allowForm = true>
				<cfelse>
					<p class="BodyText">You do not have permission to add to this database.</p>
				</cfif>
			</cfif>
			</cfoutput>
			
			<cfif local.allowForm>
				<cfoutput>
				<cfset local.qryCategories = getCategories()>				
				<cfset local.qryResolutionTypes = getResolutionTypes()>
				<cfset local.qryCountyNames = getCountyNames()>
				<cfset local.qrycourtnames = getcourtnames()>
				
				<cfsavecontent variable="local.JS">
					<style type="text/css">
						##date { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
					</style>
					<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
						<!--
						function changeAvailability(formfieldid,disableflag) {
							var ff = document.getElementById(formfieldid);
							ff.disabled = disableflag;
							if (disableflag) ff.value='disabled';
							else ff.value='';
						}  
						
						$(document).ready(function(){
							mca_setupDatePickerField('date');
						});	
						function gotoSearch(){				
							window.location.href='#local.baseLink#';
						}						
						//-->
					</SCRIPT>
				</cfsavecontent>
				<cfhtmlhead text="#local.JS#">
				<cfform name="verdictForm"  id="verdictForm" action="#local.saveLink#" method="post">
					<cfinput type="hidden" name="verdictid"  id="verdictid" value="#val(local.qryVerdict.verdictid)#">
					<div>
						<input type="submit" value="Save Verdict" name="btnSave" class="bodyText" /> &nbsp;
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND val(local.qryVerdict.verdictID) gt 0>
							<input type="button" name="btnDelete" value="Delete Verdict" class="bodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(local.qryVerdict.verdictID)#';"/> &nbsp;
						</cfif>
						<input type="button" value="Cancel" onclick="gotoSearch();" class="bodyText" />
					</div>
					<br/>
					<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and val(local.qryVerdict.verdictID) gt 0>
							<tr valign="top">
								<td nowrap><strong>Approved Status:</strong></td>
								<td>
									<cfinput required="yes" message="Select an approval status." type="radio" value="1" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 1#"> Approved - available for viewing<br/>
									<cfinput type="radio" value="0" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 0#"> Not Approved - not available for viewing<br/>
								</td>
							</tr>
						<cfelse>
							<cfinput type="hidden" name="isApproved"  id="isApproved" value="#val(local.qryVerdict.isApproved)#">
						</cfif>
						<tr valign="top">
							<td nowrap><strong>Verdict Date:</strong></td>
							<td>
								<cfinput type="text" name="date" id="date" value="#dateFormat(local.qryVerdict.date,'mm/dd/yyyy')#">
								<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 5px 7px;"></i></a>
							</td>
						</tr>
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
							<tr valign="top">
								<td nowrap><strong>Resolution:</strong></td>
								<td>
									<select name="resolutiontype" class="bodyText" onchange="changeAvailability('resolutiontypeNew',this.value.length);">
									<option value=""> - Please Select - </option>
									<cfloop query="local.qryresolutiontypes">
										<option value="#local.qryresolutiontypes.resolutiontype#" <cfif local.qryVerdict.Resolutiontype eq local.qryresolutiontypes.resolutiontype>selected</cfif>>#local.qryresolutiontypes.resolutiontype#</option>
									</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="resolutiontypeNew" id="resolutiontypeNew" maxlength="15" size="20">
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Category:</strong></td>
								<td>
									<cfselect class="bodyText" query="local.qryCategories" name="categoryID"  id="categoryID" display="categoryName"  value="categoryID" selected="#local.qryVerdict.categoryID#"  onchange="changeAvailability('categoryNameNew',this.value.length);" queryPosition="below"><option value=""> - Please Select - </option></cfselect>
									<cfinput class="bodyText" type="text" name="categoryNameNew" id="categoryNameNew" maxlength="50" size="30">
								</td>
							</tr>
						</cfif>
						<tr valign="top">
							<td nowrap><strong>Type of Case:</strong></td>
							<td><cfinput class="bodyText" type="text" name="casetypedetail"  id="casetypedetail" maxlength="400" size="70" value="#local.qryVerdict.casetypedetail#"></td>
						</tr>
						<tr valign="top">
							<td nowrap><strong>Case:</strong></td>
							<td><cfinput class="bodyText" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70" value="#local.qryVerdict.casetitle#"></td>
						</tr>
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
							<tr valign="top">
								<td nowrap><strong>Court:</strong></td>
								<td>
									<cfselect class="bodyText" query="local.qrycourtnames" name="courtID"  value="courtID" id="courtID" display="courtname"  selected="#local.qryVerdict.courtID#"  onchange="changeAvailability('courtnameNew',this.value.length);" queryPosition="below"><option value=""> - Please Select - </option></cfselect>
									<cfinput class="bodyText" type="text" name="courtnameNew" id="courtnameNew" maxlength="75" size="30">
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>County:</strong></td>
								<td>
									<select name="countyname" class="bodyText" onchange="changeAvailability('countynameNew',this.value.length);">
										<option value=""> - Please Select - </option>
										<cfloop query="local.qryCountyNames">
											<option value="#trim(local.qryCountyNames.countyname)#" <cfif trim(local.qryVerdict.countyname) eq trim(local.qryCountyNames.countyname)>selected</cfif>>#trim(local.qryCountyNames.countyname)#</option>
										</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="countynameNew" id="countynameNew" maxlength="20" size="20">
								</td>
							</tr>
						</cfif>	
						<tr>
							<td valign="top"><strong>Docket Number:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="docketnumber"  id="docketnumber" maxlength="400" size="30" value="#local.qryVerdict.docketnumber#"></td>
						</tr>
						<tr>
							<td valign="top"><strong>Sex:</strong></td>
							<td valign="top"><select name="sex" class="bodyText"><option value=""></option><option value="M" <cfif local.qryVerdict.sex eq "M">selected</cfif>>Male</option><option value="F" <cfif local.qryVerdict.sex eq "F">selected</cfif>>Female</option></select></td>
						</tr>
						<tr>
							<td valign="top"><strong>Age:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="age"  id="age" validate="integer" maxlength="2" size="5" value="#local.qryVerdict.age#"></td>
						</tr>
						<tr>
							<td valign="top"><strong>Occupation:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="Occupation"  id="Occupation" maxlength="400" size="30" value="#local.qryVerdict.Occupation#"></td>
						</tr>
						<tr>
							<td valign="top"><strong>Amount Awarded:</strong></td>
							<td valign="top">$<cfinput class="bodyText" type="text" name="amount"  id="amount" validate="float" message="Please enter a numerical amount in the 'Amount Awarded' field. Only numerals, commas, and periods allowed." size="10" value="#local.qryVerdict.amount#"></td>
						</tr>
						<tr>
							<td valign="top"><strong>Amount Detail:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="amountdetail"  id="amountdetail" maxlength="500" size="70" value="#local.qryVerdict.amountdetail#"></td>
						</tr>
						<tr>
							<td valign="top"><strong>Settlement Offer:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="settlementoffer"  id="settlementoffer" maxlength="400" size="70" value="#local.qryVerdict.settlementoffer#"></td>
						</tr>
						<tr>
							<td valign="top"><strong>Facts:</strong></td>
							<td valign="top"><textarea class="bodyText" name="facts" cols="70" rows="10">#local.qryVerdict.facts#</textarea></td>
						</tr>
						<tr>
							<td valign="top"><strong>Damages:</strong></td>
							<td valign="top"><textarea class="bodyText" name="damages" cols="70" rows="10">#local.qryVerdict.damages#</textarea></td>
						</tr>
						<tr>
							<td valign="top"><strong>Experts:</strong></td>
							<td valign="top"><textarea class="bodyText" name="experts" cols="70" rows="10">#local.qryVerdict.experts#</textarea></td>
						</tr>
						<tr>
							<td valign="top"><small><i class="icon-asterisk" style="color: darkRed;"></i></small> <strong>Plaintiff Attorney:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="PlaintiffAttorney"  id="PlaintiffAttorney" maxlength="400" size="70" value="#local.qryVerdict.PlaintiffAttorney#" required="true" message="Plaintiff Attorney is required."></td>
						</tr>
						<tr>
							<td valign="top"><small><i class="icon-asterisk" style="color: darkRed;"></i></small> <strong>Defense Attorney:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="DefenseAttorney"  id="DefenseAttorney" maxlength="400" size="70" value="#local.qryVerdict.DefenseAttorney#" required="true" message="Defense Attorney is required."></td>
						</tr>
						<tr>
							<td nowrap><small><i class="icon-asterisk" style="color: darkRed;"></i></small> <strong>Submitting Attorney:</strong></td>
							<td valign="top"><cfinput class="bodyText" type="text" name="SubmittingAttorney"  id="SubmittingAttorney" maxlength="400" size="70" value="#local.qryVerdict.SubmittingAttorney#" required="true" message="Submitting Attorney is required."></td>
						</tr>
					</table>
					<br>
					<div>
						<input type="submit" value="Save Verdict" name="btnSave" class="bodyText" /> &nbsp;
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND val(local.qryVerdict.verdictID) gt 0>
							<input type="button" name="btnDelete" value="Delete Verdict" class="bodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(local.qryVerdict.verdictID)#';"/> &nbsp;
						</cfif>
						<input type="button" value="Cancel" onclick="gotoListing()" class="bodyText" />
					</div>
				</cfform>
				<br>
				<br><small><i class="icon-asterisk" style="color: darkRed;"></i></small> indicates a required field.
			</div>
			</cfoutput>
		</cfif>
	</cfcase>

	<cfcase value="delete">
		<cfset local.verdictID = int(val(event.getValue('verdictID',0)))>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
				delete from TE_Verdicts
				where verdictID = <cfqueryparam value="#local.verdictID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		<cfoutput>
			<div style="#local.divStyle#">
				<p><span class="TitleText">Information Updated</span></p>
				<br/>
				<table border="0" cellpadding="2">
					<tr>
						<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
							<form action="#local.resultsLink#" method="post">
								<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
									<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
								</CFLOOP>
								<td><input type="submit" value="Return to Results" class="bodyText"/></td>
							</form>
						</cfif>
						<td><input type="button" onclick="document.location.href='#local.baseLink#';" value="New Search" class="bodyText" /></td>
					</tr>
				</table>
			</div>
		</cfoutput>
	</cfcase>

	<cfcase value="save">
		<cfset local.verdictID = int(val(event.getValue('verdictID',0)))>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<!--- Create a new court entry --->
			<cfif len(trim(event.getValue('courtnameNew',''))) and trim(event.getValue('courtnameNew','')) neq "disabled">
				<cfset local.courtID = addCourt(trim(event.getValue('courtnameNew','')))>
				<cfset event.setValue('courtID', local.courtID)>
			</cfif>
			<!--- Create a new category entry --->
			<cfif len(trim(event.getValue('categoryNameNew',''))) and trim(event.getValue('categoryNameNew','')) neq "disabled">
				<cfset local.categoryID = addCategory(trim(event.getValue('categoryNameNew','')))>
				<cfset event.setValue('categoryID', local.categoryID)>
			</cfif>
		</cfif>
		<!--- INSERT RECORD --->
		<cfif local.verdictID is 0>
			<cfquery name="insertVerdict" datasource="#application.dsn.customApps.dsn#">
				set nocount on
				insert into dbo.TE_Verdicts (resolutiontype, date, amount, amountDetail, categoryID, casetypeDetail, casetitle,
					courtID, countyname, docketnumber, facts, sex, age, occupation, damages, SettlementOffer, Experts, PlaintiffAttorney, DefenseAttorney, SubmittingAttorney, DepoMemberDataID, 
					dateLastModified, isApproved)
				VALUES (
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfif len(trim(event.getValue('resolutiontypeNew',''))) and trim(event.getValue('resolutiontypeNew','')) neq "disabled">
							<cfqueryparam value="#trim(replace(event.getValue('resolutiontypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							<cfqueryparam value="#trim(replace(event.getValue('resolutiontype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
					<cfelse>
						<cfqueryparam null="yes" cfsqltype="CF_SQL_VARCHAR">,
					</cfif>
					<cfif isDate(event.getValue('date','null'))>
						<cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
					<cfelse>
						<cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
					</cfif>
					<cfif len(event.getValue('amount'))>
						<cfqueryparam value="#RereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
					<cfelse>
						<cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
					</cfif>
					<cfqueryparam value="#trim(event.getValue('amountDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfif event.getValue('categoryID','') eq ''>
						<cfset local.categoryID = 0>
						<cfelse>
						<cfset local.categoryID =  event.getValue('categoryID','')>
					</cfif>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>	
						<cfqueryparam  value="#local.categoryID#" cfsqltype="CF_SQL_INTEGER">,
					<cfelse>
						<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					<cfqueryparam value="#trim(event.getValue('casetypeDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
					
					<cfif event.getValue('courtID','') eq ''>
						<cfset local.courtID = 0>
						<cfelse>
						<cfset local.courtID =  event.getValue('courtID','')>
					</cfif>
					
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfqueryparam   value="#local.courtID#" cfsqltype="CF_SQL_INTEGER">,
					<cfelse>
						<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfif len(trim(event.getValue('countynameNew',''))) and trim(event.getValue('countynameNew','')) neq "disabled">
							<cfqueryparam value="#trim(replace(event.getValue('countynameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							<cfqueryparam value="#trim(replace(event.getValue('countyname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
					<cfelse>
						<cfqueryparam null="yes" cfsqltype="CF_SQL_VARCHAR">,
					</cfif>
					<cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
					<cfqueryparam value="#trim(event.getValue('sex'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('age'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('occupation'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('damages'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
					<cfqueryparam value="#trim(event.getValue('SettlementOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('Experts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
					<cfqueryparam value="#trim(event.getValue('PlaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#session.cfcuser.memberdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
					getdate(),
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
					<cfelse>
						<cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
					</cfif>
					
				)
				select SCOPE_IDENTITY() as verdictid
				set nocount off
			</cfquery>
			
			<cfset local.thisVerdictID = insertVerdict.verdictid>
			<!--- Email IN about verdict --->
			<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
				<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode)>
				<cfset local.mailContent = '<p>A verdict has been added to the Verdict Database.</p>
					<p>VerdictID: #local.thisVerdictID#</p>
					<p><a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editLink#&verdictID=#local.thisverdictID#">Click here</a> to review the verdict and approve it for display.</p>'>

				<cfscript>
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(replace(local.associationEmail,",",";","all"),';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email='<EMAIL>' },
							emailto=local.arrEmailTo,
							emailreplyto='<EMAIL>',
							emailsubject="TELA Verdict and Settlement Database Updated",
							emailtitle='Texas Employment Lawyers Association',
							emailhtmlcontent=local.mailContent,
							siteID=local.mc_siteInfo.siteID,
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
						);
					}
				</cfscript>
			</cfif>
		<cfelse>
			<!--- UPDATE RECORD --->
			<cfquery name="updateVerdict" datasource="#application.dsn.customApps.dsn#">
				update dbo.TE_Verdicts
				set <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfif len(trim(event.getValue('resolutiontypeNew',''))) and trim(event.getValue('resolutiontypeNew','')) neq "disabled">
							resolutiontype = <cfqueryparam value="#trim(replace(event.getValue('resolutiontypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							resolutiontype = <cfqueryparam value="#trim(replace(event.getValue('resolutiontype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
					<cfelse>
						resolutiontype = <cfqueryparam null="yes" cfsqltype="CF_SQL_VARCHAR">,
					</cfif>
					<cfif isDate(event.getValue('date'))>
						date = <cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
					<cfelse>
						date = <cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
					</cfif>
					<cfif len(event.getValue('amount'))>
						amount = <cfqueryparam value="#rereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
					<cfelse>
						amount = <cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
					</cfif>
					amountDetail = <cfqueryparam value="#trim(event.getValue('amountDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
					
					<cfif event.getValue('categoryID','') eq ''>
						<cfset local.categoryID = 0>
						<cfelse>
						<cfset local.categoryID =  event.getValue('categoryID','')>
					</cfif>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						categoryID = <cfqueryparam  value="#local.categoryID#" cfsqltype="CF_SQL_INTEGER">,
					<cfelse>
						categoryID = <cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					casetypeDetail = <cfqueryparam value="#trim(event.getValue('casetypeDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
					casetitle = <cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
					
					<cfif event.getValue('courtID','') eq ''>
						<cfset local.courtID = 0>
						<cfelse>
						<cfset local.courtID =  event.getValue('courtID','')>
					</cfif>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>	
						courtID = <cfqueryparam  value="#local.courtID#" cfsqltype="CF_SQL_INTEGER">,
					<cfelse>
						courtID = <cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfif len(trim(event.getValue('countynameNew',''))) and trim(event.getValue('countynameNew','')) neq "disabled">
							countyname = <cfqueryparam value="#trim(replace(event.getValue('countynameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							countyname = <cfqueryparam value="#trim(replace(event.getValue('countyname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
						</cfif>
					<cfelse>
						countyname = <cfqueryparam null="yes" cfsqltype="CF_SQL_VARCHAR">,
					</cfif>
					docketnumber = <cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
					facts = <cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
					sex = <cfqueryparam value="#trim(event.getValue('sex'))#" cfsqltype="CF_SQL_VARCHAR">,
					age = <cfqueryparam value="#trim(event.getValue('age'))#" cfsqltype="CF_SQL_VARCHAR">,
					occupation = <cfqueryparam value="#trim(event.getValue('occupation'))#" cfsqltype="CF_SQL_VARCHAR">,
					damages = <cfqueryparam value="#trim(event.getValue('damages'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
					SettlementOffer = <cfqueryparam value="#trim(event.getValue('SettlementOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
					Experts = <cfqueryparam value="#trim(event.getValue('Experts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
					PlaintiffAttorney = <cfqueryparam value="#trim(event.getValue('PlaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
					DefenseAttorney = <cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
					SubmittingAttorney = <cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
					dateLastModified = getdate(),
					isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="cf_sql_BIT">
				where 
					verdictid = <cfqueryparam value="#event.getValue('verdictid')#" cfsqltype="cf_sql_integer">
			</cfquery>
			<cfset local.thisverdictID = event.getValue('verdictid')>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode)>
			<cfset local.mailContent = '<p>A verdict has been updated in the Verdict Database.</p>
				<p>VerdictID: #local.thisVerdictID#</p>
				<p><a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editLink#&verdictID=#local.thisverdictID#">Click here</a> to review the verdict and approve it for display.</p>'>

			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(replace(local.associationEmail,",",";","all"),';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
				if (arrayLen(local.arrEmailTo)) {
					local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email='<EMAIL>' },
						emailto=local.arrEmailTo,
						emailreplyto='<EMAIL>',
						emailsubject="TELA Verdict and Settlement Database Updated",
						emailtitle='Texas Employment Lawyers Association',
						emailhtmlcontent=local.mailContent,
						siteID=local.mc_siteInfo.siteID,
						memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
					);
				}
			</cfscript>
		</cfif>
		<cfoutput>
			<div style="#local.divStyle#">
				<p><span class="TitleText">Information Saved. 
					<cfif not val(event.getValue('isApproved'))>Once approved, the information you entered will be searchable in the database.</cfif>
				</span></p>
				<br/>
				<table border="0" cellpadding="2">
					<tr>
						<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
							<form action="#local.resultsLink#" method="post">
								<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
									<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
								</CFLOOP>
								<td><input type="submit" value="Return to Results" class="bodyText"/></td>
							</form>
						</cfif>
						<td><input type="button" onclick="parent.location='#local.baseLink#';" value="New Search" class="bodyText" /></td>
					</tr>
				</table>
			</div>
		</cfoutput>
	</cfcase>

	<cfcase value="results">
		<cfset pageID = int(val(event.getValue('page',1)))>
		<cfset maxrows = 10>
		<cfquery name="local.qryMatches" datasource="#application.dsn.customApps.dsn#">
			select *
			from TE_Verdicts v
			left outer  join TE_verdictsCourts j on j.courtID = v.courtID
			left outer  join TE_verdictsCategories c on c.categoryID = v.categoryID
			where 
			<cfif val(event.getValue('isApproved',-1)) eq -1>
				<cfif not structKeyExists(form, "page")>
					<cfset form.page = session.lastverdictsearch.page>					
				</cfif>
				<cfif not structKeyExists(form, "fieldnames")>
					<cfset form.fieldnames = session.lastverdictsearch.fieldnames>
				</cfif>
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and len(session.lastverdictsearch.isApproved)>
					isApproved = <cfqueryparam value="#session.lastverdictsearch.isApproved#" cfsqltype="CF_SQL_BIT">
					<cfset form.isApproved = session.lastverdictsearch.isApproved>
				<cfelse>
					1=1
					
					<cfset form.isApproved = ''>
				</cfif>
				<cfif len(session.lastverdictsearch.resolutiontypes)>
					and resolutiontype = <cfqueryparam value="#session.lastverdictsearch.resolutiontypes#" cfsqltype="CF_SQL_VARCHAR">
					<cfset form.resolutiontypes = session.lastverdictsearch.resolutiontypes>
					<cfelse>
					<cfset form.resolutiontypes = ''>
				</cfif>
				<cfif len(session.lastverdictsearch.casetypes)>
					and c.categoryName = <cfqueryparam value="#session.lastverdictsearch.casetypes#" cfsqltype="CF_SQL_VARCHAR">
					<cfset form.casetypes = session.lastverdictsearch.casetypes>
					<cfelse>
					<cfset form.casetypes = ''>
				</cfif>
				<cfif len(session.lastverdictsearch.countyname)>
					and countyname = <cfqueryparam value="#session.lastverdictsearch.countyname#" cfsqltype="CF_SQL_VARCHAR">
					<cfset form.countyname = session.lastverdictsearch.countyname>
					<cfelse>
					<cfset form.countyname = ''>
				</cfif>
				<cfif len(session.lastverdictsearch.distinctyears)>
					and year(date) = <cfqueryparam value="#session.lastverdictsearch.distinctyears#" cfsqltype="CF_SQL_INTEGER">
					<cfset form.distinctyears = session.lastverdictsearch.distinctyears>
					<cfelse>
					<cfset form.distinctyears = ''>
				</cfif>
				<cfif len(session.lastverdictsearch.keywords)>
					and (
						isnull(resolutiontype,'') + ' ' + isnull(cast(amount as varchar(30)),'') + ' ' +
						isnull(amountdetail,'') + ' ' + isnull(c.categoryName,'') + ' ' + isnull(casetypedetail,'') + ' ' + 
						isnull(casetitle,'') + ' ' + isnull(j.courtname,'') + ' ' + isnull(docketnumber,'') + ' ' + 
						isnull(facts,'') + ' ' + isnull(sex,'') + ' ' + isnull(age,'') + ' ' + 
						isnull(occupation,'') + ' ' + isnull(damages,'') +' ' + isnull(settlementoffer,'') + ' ' + 
						isnull(experts,'') + ' ' + isnull(plaintiffAttorney,'') + ' ' + isnull(defenseAttorney,'') + ' ' +
						isnull(submittingattorney,'') + ' ' + isnull(countyname,'')
						LIKE <cfqueryparam value="%#session.lastverdictsearch.keywords#%" cfsqltype="CF_SQL_VARCHAR">
						)
						<cfset form.keywords = session.lastverdictsearch.keywords>
						<cfelse>
						<cfset form.keywords = ''>
				</cfif>	
				<cfelse>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and len(event.getValue('isApproved'))>
						isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="CF_SQL_BIT">
					<cfelse>
						1=1
					</cfif>
					<cfif len(event.getValue('resolutiontypes',''))>
						and resolutiontype = <cfqueryparam value="#event.getValue('resolutiontypes')#" cfsqltype="CF_SQL_VARCHAR">
					</cfif>
					<cfif len(event.getValue('casetypes',''))>
						and c.categoryName = <cfqueryparam value="#event.getValue('casetypes')#" cfsqltype="CF_SQL_VARCHAR">
					</cfif>
					<cfif len(event.getValue('countyname',''))>
						and countyname = <cfqueryparam value="#event.getValue('countyname')#" cfsqltype="CF_SQL_VARCHAR">
					</cfif>
					<cfif len(event.getValue('distinctyears',''))>
						and year(date) = <cfqueryparam value="#event.getValue('distinctyears')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif len(event.getValue('keywords',''))>
						and (
							isnull(resolutiontype,'') + ' ' + isnull(cast(amount as varchar(30)),'') + ' ' +
							isnull(amountdetail,'') + ' ' + isnull(c.categoryName,'') + ' ' + isnull(casetypedetail,'') + ' ' + 
							isnull(casetitle,'') + ' ' + isnull(j.courtname,'') + ' ' + isnull(docketnumber,'') + ' ' + 
							isnull(facts,'') + ' ' + isnull(sex,'') + ' ' + isnull(age,'') + ' ' + 
							isnull(occupation,'') + ' ' + isnull(damages,'') +' ' + isnull(settlementoffer,'') + ' ' + 
							isnull(experts,'') + ' ' + isnull(plaintiffAttorney,'') + ' ' + isnull(defenseAttorney,'') + ' ' +
							isnull(submittingattorney,'') + ' ' + isnull(countyname,'')
							LIKE <cfqueryparam value="%#event.getValue('keywords')#%" cfsqltype="CF_SQL_VARCHAR">
							)
					</cfif>
					<cfset session.lastVerdictSearch = duplicate(form)>	
					
			</cfif>
			order by verdictID
		</cfquery>
		
		<cfif local.qryMatches.recordcount>
			<cfset numpages = ceiling(local.qryMatches.recordcount / variables.maxrows)>
			<cfset startrow = ((variables.pageID-1) * variables.maxrows) + 1>
			<cfset endrow = variables.startrow + variables.maxrows - 1>
			<cfif local.qryMatches.recordcount lt variables.endrow>
				<cfset endrow = local.qryMatches.recordcount>
			</cfif>
		<cfelse>
			<cfset numpages = 0>
			<cfset startrow = 0>
			<cfset endrow = 0>
		</cfif>
		
		<cfoutput>
			<div style="#local.divStyle#">
				<div class="TitleText">Verdicts and Settlements Search Results</div>
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<div class="BodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
				</cfif>
				
				<script language="JavaScript">
					function prevPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page',1)-1#';
						objForm.submit();
					}
					function nextPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page',1)+1#';
						objForm.submit();
					}
				</script>
				<br/>
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td class="bodyText">Showing #startrow# to #endrow# of #local.qryMatches.recordcount# matches</td>
						<td align="right">
							<cfif form.page gt 1>
								<input type="button" value="&lt;&lt; Previous Page" class="bodyText" onclick="prevPage();">
							</cfif>
							<cfif local.qryMatches.recordcount gt (form.page*maxrows)>
								<input type="button" value="Next Page &gt;&gt;" class="bodyText" onclick="nextPage();">
							</cfif>
							<input type="button" onclick="self.location.href='#local.baseLink#';" value="New Search" class="bodyText">
							<!--- ADD BUTTON: --->
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Verdict" class="bodyText">
							</cfif>
						</td>
					</tr>
				</table>
				<br/>
				</cfoutput>
				<cfif local.qryMatches.recordcount eq 0>
					<cfoutput><div class="BodyText">No records match your search criteria.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<table border="0" class="bodyText" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
						<tr bgcolor="##999999">
							<td colspan="2"></td>
							<th align="left">Category</th>
							<th align="left">Date</th>
							<th align="left">Court / Case</th>
							<th align="left">Resolution</th>
						</tr>
						</cfoutput>
						
						<cfoutput query="local.qryMatches" startrow="#variables.startrow#" maxrows="#variables.maxrows#">
							<tr valign="top" <cfif local.qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
								<td <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#local.qryMatches.currentrow#.</td>
								<td <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>>
									<a href="#local.viewLink#&verdictID=#local.qryMatches.verdictID#">View</a>
									<cfif (application.objUser.isSiteAdmin(cfcuser=session.cfcuser)) or ((session.cfcuser.memberdata.depoMemberDataID EQ local.qryMatches.depoMemberDataID) and NOT local.qryMatches.isApproved) >
										<br/><a href="#local.editLink#&verdictID=#local.qryMatches.verdictID#">Edit</a>
									</cfif>
								</td>
								<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.categoryName#&nbsp;</td>
								<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;"><cfif isdate(local.qryMatches.date)>#dateformat(local.qryMatches.date,"m/d/yyyy")#</cfif>&nbsp;</td>
								<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">
									#local.qryMatches.courtname#
									<cfif len(local.qryMatches.courtname) and len(local.qryMatches.casetitle)><br/></cfif>
									#left(local.qryMatches.casetitle,100)#<cfif len(local.qryMatches.casetitle) gt 100>...</cfif>
									&nbsp;
								</td>
								<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-right:2px solid ##FF0000;<cfelse>border-right:1px solid ##666;</cfif>">#local.qryMatches.resolutiontype#&nbsp;</td>
							</tr>
						</cfoutput>
						<cfoutput>
					</table></cfoutput>
				</cfif>
				<cfoutput>	
				
				<form name="frmHidden" action="#local.resultsLink#" method="post">
					<input type="hidden" name="page" value="">
					<cfloop INDEX="form_element" LIST="#FORM.fieldnames#">
						<cfif form_element neq "page">
							<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
						</cfif>
					</CFLOOP>
				</form>
			</div>
		</cfoutput>
	</cfcase>

	<cfcase value="search">
		<cfset qryCategories = getSearchCategories()>
		<cfset qryResolutionTypes = getResolutionTypes()>
		<cfset qryCountyNames = getCountyNames()>
		<cfset qryYears = getYears()>
		<cfset session.lastverdictsearch = false>
		<cfoutput>
			<div style="#local.divStyle#">
				<p><span class="TitleText">Search TELA Verdict and Settlement Exchange Database</span></p>
				<p class="BodyText">TELA's Verdict and Settlement Exchange Database contains reports submitted by members for publication in The Prairie Barrister. The database records include all information provided by members and is more detailed than the Barrister's published reports. The Verdict and Settlement Exchange is a valuable service to our members and we welcome all reports.</p>
				<br>
				<cfform action="#local.resultsLink#" method="post">
					<input type="hidden" name="page" value="1" />
					<input type="submit" value="Search Reports" class="bodyText"/>
					<!--- ADD BUTTON: --->
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.editLink#';" value="Add Verdict" class="bodyText" />
					</cfif>
					<br>
					<br>
					<table class="bodyText">
						<tr>
							<td>Resolution Type:</td>
							<td>
								<select name="resolutiontypes" class="bodyText">
									<option value="">All</option>
									<cfloop query="qryResolutionTypes">
										<option value="#qryResolutionTypes.resolutiontype#">#qryResolutionTypes.resolutiontype#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>Case Category:</td>
							<td>
								<select name="casetypes" class="bodyText">
									<option value="">All</option>
									<cfloop query="qryCategories">
										<option value="#qryCategories.categoryName#">#qryCategories.categoryName#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>Verdict Year:</td>
							<td>
								<select name="distinctyears" class="bodyText">
									<option value="">All</option>
									<cfloop query="qryYears">
										<option value="#qryYears.year#">#qryYears.year#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>County:</td>
							<td>
								<select name="countyname" class="bodyText">
									<option value="">All</option>
									<cfloop query="qryCountyNames">
										<option value="#qryCountyNames.countyname#">#qryCountyNames.countyname#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>Keywords (optional):</td>
							<td><input type="text" name="keywords" class="bodyText" maxlength="70" size="70" /></td>
						</tr>
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
							<tr>
								<td>Approval Status:</td>
								<td>
									<select name="isApproved" class="bodyText">
										<option value="">All</option>
										<option value="1">Approved Verdicts Only</option>
										<option value="0">Non-Approved Verdicts Only</option>
									</select>
								</td>
							</tr>
						</cfif>
					</table>
				</cfform>
			</div>
		</cfoutput>
	</cfcase>
	<!--- search --->
	<cfdefaultcase></cfdefaultcase>
</cfswitch>

<!--- Look up court, if found return courtID, otherwise create new entry and return courtID --->
<cffunction name="addCourt" returntype="string" output="No">
	<cfargument name="courtname" type="string" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT courtID
		FROM TE_verdictsCourts
		WHERE courtname = <cfqueryparam value="#arguments.courtname#" cfsqltype="CF_SQL_VARCHAR">
				and status = 'A'
	</cfquery>
	
	<cfif qry.recordCount GT 0>
		<cfreturn qry.courtID>
	<cfelse>
		<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
			set nocount on
			insert into TE_verdictsCourts (courtname, status)
			values(<cfqueryparam value="#arguments.courtname#" cfsqltype="CF_SQL_VARCHAR">, 'A')
			
			select SCOPE_IDENTITY() as courtID
			set nocount off
		</cfquery>
		<cfreturn qry.courtID>
	</cfif>
</cffunction>

<cffunction name="addCategory" returntype="string" output="No">
	<cfargument name="categoryName" type="string" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT categoryID
		FROM TE_verdictsCategories
		WHERE categoryName = <cfqueryparam value="#arguments.categoryName#" cfsqltype="CF_SQL_VARCHAR">
	</cfquery>
	
	<cfif qry.recordCount GT 0>
		<cfreturn qry.categoryID>
	<cfelse>
		<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
			set nocount on
			insert into TE_verdictsCategories (categoryName)
			values(<cfqueryparam value="#arguments.categoryName#" cfsqltype="CF_SQL_VARCHAR">)
			
			select SCOPE_IDENTITY() as categoryID
			set nocount off
		</cfquery>
		<cfreturn qry.categoryID>
	</cfif>
</cffunction>

<cffunction name="getSearchCategories" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT c.categoryID, c.categoryName
		FROM TE_verdictsCategories c
		INNER JOIN TE_Verdicts v on v.categoryID = c.categoryID
		ORDER BY categoryName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCategories" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT categoryID, categoryName
		FROM TE_verdictsCategories
		WHERE categoryName IS NOT NULL AND categoryName <> ''
		ORDER BY categoryName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getResolutionTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT resolutiontype
		FROM TE_verdicts
		WHERE resolutiontype IS NOT NULL AND resolutiontype <> ''
		ORDER BY resolutiontype
	</cfquery>

	<cfscript>
		queryAddRow(qry,1);
		querySetCell(qry,'resolutionType','Arbitration',qry.recordCount);
		queryAddRow(qry,1);
		querySetCell(qry,'resolutionType','Court Order',qry.recordCount);
		queryAddRow(qry,1);
		querySetCell(qry,'resolutionType','Jury Verdict',qry.recordCount);
		queryAddRow(qry,1);
		querySetCell(qry,'resolutionType','Mediation',qry.recordCount);
		queryAddRow(qry,1);
		querySetCell(qry,'resolutionType','Settlement After Case Filed',qry.recordCount);
		queryAddRow(qry,1);
		querySetCell(qry,'resolutionType','Settlement Before Case Filed',qry.recordCount);
	</cfscript>

	<cfquery name="qry" dbtype="query">
		SELECT DISTINCT resolutionType
		FROM qry
		ORDER BY resolutionType
	</cfquery>

	<cfreturn qry>
</cffunction>
<cffunction name="getCountyNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT countyname
		FROM TE_verdicts
		WHERE countyname IS NOT NULL AND countyname <> ''
		ORDER BY countyname
	</cfquery>

	<cfscript>
		queryAddRow(qry,1);
		querySetCell(qry,'countyname','Out of State',qry.recordCount);
	</cfscript>

	<cfreturn qry>
</cffunction>
<cffunction name="getcourtnames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT courtID, courtname
		FROM TE_verdictsCourts
		WHERE courtname IS NOT NULL AND courtname <> '' and status = 'A'
		ORDER BY courtname
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getYears" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT YEAR(DATE) as year
		FROM TE_Verdicts
		WHERE DATE IS NOT NULL AND DATE <> ''
		ORDER BY YEAR(DATE) DESC
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getVerdict" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select * 
		from TE_verdicts v
		left outer  join TE_verdictsCourts j on j.courtID = v.courtID		
		left outer  join TE_VerdictsCategories c on c.categoryID = v.categoryID	
		where verdictid = <cfqueryparam value="#arguments.verdictID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfreturn qry>
</cffunction>