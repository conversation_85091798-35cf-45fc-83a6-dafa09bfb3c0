<cfsavecontent variable="local.pghead">
	<cfoutput>
	<style type="text/css">
		.myMAJ .nav-tabs > .active > a,.myMAJ  .nav-tabs>.active>a:hover { background:##E8C826 !important; }
		.myMAJ .nav-tabs a, .myMAJ .nav-tabs a:hover { color:##455b68; background:##d3d3d3 !important; font-weight: bolder;}
		.myMAJ .nav-tabs>li>a { margin-right:23px; background:##ececec !important; }
		.myMAJ .nav-tabs>li:last-child>a { margin-right:auto; color:##455b68 !important;font-weight: normal;}
		.myMAJ .nav { margin-bottom:0px; }
		.infoCont{padding-top:20px !important;margin-bottom:10px !important;}
		.MAJRow{margin-left:0px !important;margin-bottom:0px !important;}
		.tab-content { border:2px solid ##ddd; min-height:220px; padding:10px; margin-bottom:20px; background:##fff;}
		.showBullets{list-style: inside !important;}
		.myMAJ .nav-tabs > li > a { border: 1px solid transparent; border-radius: 4px 4px 0 0; line-height: 1.42857; margin-right: 2px;}
		.HeaderText {color: ##333436;font-weight:bold; font-size: 16px; line-height: 19px; margin: 0; padding: 0 0 13px;}
		.myMAJ .myInfo .showBullets a{color:##66838c !important;}
		.carousel-inner p { padding: 15px !important; margin: 15px 0 20px !important; }
		.myMAJ .nav-tabs > li.active > a {color:##0c4f67 !important;font-weight: bolder !important;}
		##myccba a {color:##66838c;}
		##myccba .memName {color:##E8C826;}
		##myccba p {margin:0;}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pghead#">

<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="Sponsors", type="CONTENTOBJ", desc="Sponsors", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="EditableBoxOneTitle", type="CONTENTOBJ", desc="Editable Box One Title", value="Editable Box One Title" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="EditableBoxOneContent", type="CONTENTOBJ", desc="Editable Box One Content", value="Editable Box One Content" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="EditableBoxTwoTitle", type="CONTENTOBJ", desc="", value="Editable Box Two Title" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="EditableBoxTwoContent", type="CONTENTOBJ", desc="", value="Editable Box Two Content" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
</cfscript>

<cfoutput>
	<div class="container" id="myccba">
		<div class="row-fluid">
			<div id="mainContent">
				<div class="span12 row-fluid myMAJ infoCont">
					<div class="span1 myPhoto">
						<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
							<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
						</cfif>
					</div>
					<div class="span10 myInfo">
						<span class="HeaderText memName">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName# #session.cfcUser.memberData.suffix#</span><br />
						<span class="BodyText">
							<ul style="margin:0px" class="showBullets">
								<li><a href="/?pg=updateMember">Update My Profile</a></li>
								<li><a href="/?pg=updateMember##memberUpdateSectionLoginInfo">Change My Login</a></li>
								<li><a href="/?pg=updatemember&memaction=updatePhoto">Update Directory Photo</a></li>
							</ul>
						</span>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##upcomingEvents" data-toggle="tab" class="MainNavText">Upcoming Events</a></li>
								<li><a href="##registeredEvents" data-toggle="tab" class="MainNavText">My Events</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="upcomingEvents">
									[[upcomingEvents pagename=[events] includeRegistered=[false] includeNotRegistered=[true] maxrows=[5] noresultstext=[There are currently no upcoming events.]]]
									<br/>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
								<div class="tab-pane BodyText" id="registeredEvents">
									[[upcomingEvents pagename=[events] includeRegistered=[true] includeNotRegistered=[false] maxrows=[5] noresultstext=[You are currently not registered for any Events.]]]
									<br/>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>				
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##invoices" data-toggle="tab" class="MainNavText">Past Due Invoices</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="invoices">
									[[overdueInvoices maxrows=[4] noresultstext=[You do not have any past due invoices.]]]
									<br/>
									<div>
										<a href="/?pg=invoices"><i class="icon-calendar icon2x">&nbsp;</i><strong>View All Past Due Invoices</strong></a>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##sponsors" data-toggle="tab" class="MainNavText">Sponsors</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="sponsors">
									<div id="myCarousel" class="carousel slide text-center">
										<!-- Carousel items -->
										<div class="carousel-inner text-center">
											<cfif len(trim(local.strPageFields.Sponsors))>
												<cfloop list="#local.strPageFields.Sponsors#" index="local.thisImage" delimiters="||">
													<div class="<cfif ListFirst(local.strPageFields.Sponsors,'||') eq local.thisImage>active </cfif>item">
														<p><a href="/" target="_blank">#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#</a></p>
													</div>
												</cfloop>
											</cfif>
										</div>
									</div>
									<cfif len(trim(local.strPageFields.Sponsors))>
										<script type='text/javascript'>
										    $(document).ready(function() {
										         $('.carousel').carousel({
										             interval: 4000
										         });
										    });
										</script>
									</cfif>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##announcements" data-toggle="tab" class="MainNavText">Announcements</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="announcements">
									<-- ZONE C -->
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##myPayments" data-toggle="tab" class="MainNavText">Recent Payments</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="myPayments">
									[[myRecentPayments maxrows=[5] noresultstext=[There are no recent payments.]]]
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##editBoxOne" data-toggle="tab" class="MainNavText">#local.strPageFields.EditableBoxOneTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="editBoxOne">
									#local.strPageFields.EditableBoxOneContent#
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##newstoreproducts" data-toggle="tab" class="MainNavText">New Store Items</a></li>
								<li><a href="##store" data-toggle="tab" class="MainNavText">Recent Purchases</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="newstoreproducts">
									<div class="sectionTitle"><strong>These are the latest products added to the Online Store</strong></div>
									<br />
									[[latestStoreProducts maxrows=[5] noresultstext=[There are no recent store products.]]]
									<div class="sectionContent">
										<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top:35px;" class="">
										<tr>
											<td width="5%" style="vertical-align:top;">
												<div class="circle-tile-heading-small green pull-right">
													<div class="circle-text-small">
														<a href="/?pg=store"><i class="icon-book" style="color:##00b">&nbsp;</i></a>
													</div>
												</div>
											</td>
											<td style="text-align:left;padding-left:3px;"><a href="/?pg=store">Browse the Online Store</a></td> 
										</tr>
										</table>
									</div>
								</div>
								<div class="tab-pane BodyText" id="store">
									[[myStorePurchases maxrows=[5] noresultstext=[You have no recent purchases to display.]]]
									<div class="sectionContent">
										<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top:35px;" class="bottomLinks">
											<tr>
												<td width="5%" style="vertical-align:top;">
													<div class="circle-tile-heading-small green pull-right">
														<div class="circle-text-small">
															<a href="/?pg=store&sa=myPurchases"><i class="icon-shopping-cart" style="color:##00b">&nbsp;</i></a>
														</div>
													</div>
												</td>
												<td style="text-align:left;padding-left:3px;"><a href="/?pg=store&sa=myPurchases">Browse All My Purchases</td> 
											</tr>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##editBoxTwo" data-toggle="tab" class="MainNavText">#local.strPageFields.EditableBoxTwoTitle#</a></li>
								<li><a href="##recentSearch" data-toggle="tab" class="MainNavText">My Recent Searches</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="editBoxTwo">
									#local.strPageFields.EditableBoxTwoContent#
								</div>
								<div class="tab-pane BodyText" id="recentSearch">
									[[mySearchHistory maxrows=[5] noresultstext=[You do not have any previous searches.]]]
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTabs">
								<li class="active"><a href="##newfilesharedocs" data-toggle="tab" class="MainNavText">New Fileshare Docs</a></li>
								<li><a href="##myfiledownloads" data-toggle="tab" class="MainNavText">My File Downloads</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="newfilesharedocs">
									[[recentFileShareUploads noresultstext=[There are no recent documents.]]]
								</div>
								<div class="tab-pane BodyText" id="myfiledownloads">
									[[myRecentFileshareDownloads noresultstext=[There are no recent downloads.]]]
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfoutput>