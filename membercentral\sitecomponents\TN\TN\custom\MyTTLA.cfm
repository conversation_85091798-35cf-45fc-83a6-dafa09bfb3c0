<cfsavecontent variable="local.pghead">
	<cfoutput>
	<style type="text/css">
		.myMAJ .nav-tabs > .active > a,.myMAJ  .nav-tabs>.active>a:hover { background:##ca9937 !important; }
		.myMAJ .nav-tabs a, .myMAJ .nav-tabs a:hover { color:##FFFFFF; background:##d3d3d3 !important; font-weight: lighter;}
		.myMAJ .nav-tabs>li>a { margin-right:23px; background:##ececec !important; }
		.myMAJ .nav-tabs>li:last-child>a { margin-right:auto; color:##094669 !important;font-weight: lighter;}
		.myMAJ .nav { margin-bottom:0px; }
		.infoCont{padding-top:20px !important;margin-bottom:10px !important;padding: 10px;}
		.MAJRow{margin-left:0px !important;margin-bottom:0px !important;}
		.tab-content { border:2px solid ##ddd; min-height:220px; padding:10px; margin-bottom:20px; background:##fff;}
		.showBullets{list-style: none !important; padding:5px;}
		.myMAJ .nav-tabs > li > a { border: 1px solid transparent; border-radius: 4px 4px 0 0; line-height: 1.42857; margin-right: 2px; padding: 4px 0px;color: ##094669 !important;}
		.HeaderText {color: ##333436;font-weight:bold; font-size: 16px; line-height: 19px; margin: 0; padding: 0 0 13px;}
		.myMAJ .myInfo .showBullets a{color:##66838c !important;}	
		.myMAJ .nav-tabs > li.active > a {color:##FFFFFF !important;font-weight: normal !important;}	
		##myttla .memName {color:##094669;}
		##myttla p {margin:0;}
		ul.owl-carousel.owl-theme {
			padding: 0px!important;
		}		
		div.mc-mergeTagList.mc-invoiceList,.mc-eventList,.mc-paymentsList,.mc-searchList,.mc-uploadList,.mc-downloadList
		{
			margin:10px 0px;
		}		
	
		ul.mc-mergeTagList {
			padding: 5px 0px!important;
		}
		ul li { list-style:none; }
		li a:active {
			text-shadow:none!important;
		}	
		ul.nav-tabs {
			padding-bottom: 20px;
			padding-left: 0px!important;
		}	
		
		.nav-tabs >li {float:left; }
		ul.nav-tabs>li {
			list-style: none; 
		}
		.span10.myInfo {
			min-height: 100px;
		}
		}				
		##newfilesharedocs ul,##newfilesharedocs ul li  { padding: 5px 0px;  list-style: none; margin:0px;}
		
		##quicklinks{
			padding:5px 0px;
		}
		##myttla .tab-content .tab-pane {
			overflow-y: auto;
			min-height: 200px;
			max-height: 200px;
		}
		div##sponsors{overflow:hidden;}	
		img {
			height: auto;
			max-width: 100%;
			vertical-align: middle;
			border: 0;
		}
		##myttla ul.nav-tabs {
			padding-bottom: 0px!important;
		}
		@media only screen and (max-width: 1024px){
			##myttla .nav {
				position: relative!important;
				left: 0!important;
				top: 0!important;
				width: auto!important;
				height: auto!important;
				bottom: 0!important;
				background: none!important;
				list-style: outside none none !important;
				display:block !important;
			}
			.myMAJ {
				display: inline-flex !important;
			}
			.myMAJ .myPhoto{
				width:auto;
			}
			.myInfo {
				margin-left: 5px !important;
			}
		}
		@media only screen and (max-width: 360px){
			.myMAJ {
				display: block !important;
			}
			.myMAJ .myPhoto{
				width:100%;
			}
		}
		@media only screen and (min-width: 1024px){
			.myInfo {
				margin-left: 0px !important;
			}
		}
	</style>
	<link rel="stylesheet" href="/css/owl.carousel.css" />		
	<script src="/javascript/owl.carousel.js" type="text/javascript"></script>
	<script>
		function viewEvent(eid) {
				window.location = '/?pg=events&evAction=showDetail&eID=' + eid;
				return false;
			}
		$(document).ready(function(){		
			$('##myEvents li a,##myInvoices li a,##mySponsors li a,##myLinks li a,##myBenefits li a,##myFile li a').on('click',function(e){
				e.preventDefault();
				var _this = $(this);
				_this.parents('ul').next().children('.tab-pane').hide();
				_this.attr("href");
				$(_this.attr("href")).show();
				$(_this).parents('ul').children('li').removeClass('active');
				$(_this).parent().addClass('active');
			});
			$('.tab-content .tab-pane').hide();
			$('##myEvents>li:first-child >a,##myInvoices>li:first-child>a,##mySponsors>li:first-child>a,##myLinks>li:first-child>a,##myBenefits>li:first-child>a,##myFile>li:first-child>a').trigger('click');			
			
		});
	</script>	
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pghead#">

<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="UpcomingEventTitle", type="STRING", desc="Editable Title", value="Upcoming Events" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="UpcomingEvent", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="EventTitle", type="STRING", desc="Editable Title", value="My Events" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="EventContent", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="InvoicesTitle", type="STRING", desc="Editable Title", value="Past Due Invoices" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="PastInvoices", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="PaymentTitle", type="STRING", desc="Editable Title", value="Recent Payments" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="Payment", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="SponsorTitle", type="STRING", desc="Editable Title", value="Sponsors" };
		arrayAppend(local.arrCustomFields, local.tmpField);		
	local.tmpField = { name="Sponsors", type="CONTENTOBJ", desc="Sponsors", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="quickLinksTitle", type="STRING", desc="Editable Title", value="Quick Links" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="quickLinksContent", type="CONTENTOBJ", desc="Content for quick links section", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="MemberBenefitsTitle", type="STRING", desc="Editable Title", value="Member Benefits" };
		arrayAppend(local.arrCustomFields, local.tmpField);			
	local.tmpField = { name="TtlaMemberBenefitsContent", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField); 
	local.tmpField = { name="RecentSearchesTitle", type="STRING", desc="Editable Title", value="My Recent Searches" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="RecentSearchesContent", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="FileshareTitle", type="STRING", desc="Editable Title", value="New Fileshare Docs" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="FileshareContent", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField); 	
	local.tmpField = { name="FiledownloadTitle", type="STRING", desc="Editable Title", value="My File Downloads" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FiledownloadContent", type="CONTENTOBJ", desc="Editable Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField); 	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
</cfscript> 

<cfoutput>
	<div class="container span12" id="myttla">
		<div class="row-fluid">
			<div id="mainContent">
				<div class="span12 row-fluid myMAJ infoCont">
					<div class="span2 myPhoto">
						<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
							<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
						</cfif>
					</div>
					<div class="span10 myInfo">
						<span class="HeaderText memName">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName# #session.cfcUser.memberData.suffix#</span><br />
						<span class="BodyText">
							<ul style="margin:0px" class="showBullets">
								<li><a href="/?pg=updateMember">Update My Profile</a></li>
								<li><a href="/?pg=updateMember##memberUpdateSectionLoginInfo">Change My Login</a></li>
								<li><a href="/?pg=updatemember&memaction=updatePhoto">Update My Photo</a></li>
							</ul>
						</span>
					</div>
				</div>

				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myEvents">
								<li class="active"><a href="##upcomingEvents" data-toggle="tab" class="MainNavText">#local.strPageFields.UpcomingEventTitle#</a></li>
								<li><a href="##registeredEvents" data-toggle="tab" class="MainNavText">#local.strPageFields.EventTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="upcomingEvents">
									#local.strPageFields.UpcomingEvent#
									<div class="mcMergeTemplate" data-mcjsonvariable="upcomingevents">									
										{{##if events}}	
											<div class="row-fluid">
												{{##events}}													
													<div>													
														<span><a href="##" onClick="return viewEvent('{{{id}}}');">{{moment startDate format='M/D/YYYY'}}&nbsp;&nbsp;{{{title}}}</a></span>
													</div>									
												{{/events}}
											</div>
										{{else}}
											<div class="row-fluid">
												<div>There are currently no upcoming events.</div>
											</div>
										{{/if}}
									</div>
									<br/>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
								<div class="tab-pane BodyText" id="registeredEvents">
									#local.strPageFields.EventContent#
									<div class="mcMergeTemplate" data-mcjsonvariable="registeredevents">								
										{{##if events}}	
											<div class="row-fluid">
												{{##events}}
													<div>													
														<span><a href="##" onClick="return viewEvent('{{{id}}}');">{{moment startDate format='M/D/YYYY'}}&nbsp;&nbsp;{{{title}}}</a></span>
													</div>										
												{{/events}}
											</div>
											{{else}}
												<div class="row-fluid">
													<div>You are currently not registered for any Events.</div>
												</div>
											{{/if}}									
									</div>
									<br/>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>				
							</div>
						</div>
					</div>
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myInvoices">
								<li class="active"><a href="##invoices" data-toggle="tab" class="MainNavText">#local.strPageFields.InvoicesTitle#</a></li>
								<li><a href="##myPayments" data-toggle="tab" class="MainNavText">#local.strPageFields.PaymentTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="invoices">	
									#local.strPageFields.PastInvoices#
									<br/>
									<div>
										<a href="/?pg=invoices"><i class="icon-calendar icon2x">&nbsp;</i><strong>View All Past Due Invoices</strong></a>
									</div>
								</div>
								<div class="tab-pane active BodyText" id="myPayments">
									#local.strPageFields.Payment#
								</div>
							</div>
						</div>
					</div>

					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="mySponsors">
								<li class="active"><a href="##sponsors" data-toggle="tab" class="MainNavText">#local.strPageFields.SponsorTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="sponsors">									
									#local.strPageFields.Sponsors#
								</div>
							</div>
						</div>
					</div>
				</div>			
				<div class="span12 row-fluid myMAJ MAJRow">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myLinks">
								<li class="active"><a href="##quicklinks" data-toggle="tab" class="MainNavText">#local.strPageFields.quickLinksTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="quicklinks">
									#local.strPageFields.quickLinksContent#
								</div>
							</div>
						</div>
					</div>
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myBenefits">
								<li class="active"><a href="##MemberBenefit" data-toggle="tab" class="MainNavText">#local.strPageFields.MemberBenefitsTitle#</a></li>
								<li><a href="##recentSearch" data-toggle="tab" class="MainNavText">#local.strPageFields.RecentSearchesTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="MemberBenefit">
									#local.strPageFields.TtlaMemberBenefitsContent#
								</div>
								<div class="tab-pane BodyText" id="recentSearch">
									#local.strPageFields.RecentSearchesContent#
								</div>
							</div>
						</div>
					</div>
			
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myFile">
								<li class="active"><a href="##newfilesharedocs" data-toggle="tab" class="MainNavText">#local.strPageFields.FileshareTitle#</a></li>
								<li><a href="##myfiledownloads" data-toggle="tab" class="MainNavText">#local.strPageFields.FiledownloadTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="newfilesharedocs">
									#local.strPageFields.FileshareContent#
								</div>
								<div class="tab-pane BodyText" id="myfiledownloads">
									#local.strPageFields.FiledownloadContent#
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfoutput>