<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
        <cfargument name="Event" type="any">
        <cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=join";
            local.formAction = arguments.event.getValue('fa','showLookup');
			variables.cfcuser = structNew();
			variables.formFields = structNew();			
			variables.cfcuser = session.cfcuser;
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=variables.cfcuser);
			if(application.mcCacheManager.sessionValueExists('formFields')){
				variables.formFields = application.mcCacheManager.sessionGetValue('formFields',{});
			}
            variables.currentDate = dateTimeFormat(now());
            local.arrCustomFields = [];

            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			    arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="<p><span class='block' style='padding-bottom:5px;'>Click the <span class='b'>Account Lookup</span> button to the left.</span> <span class='block' style='padding-bottom:5px;'>Enter the search criteria and click <span class='b'>Continue</span>.</span> <span class='block' style='padding-bottom:5px;'>If you see your name, please press the <span class='b'>Choose</span> button next to your name.</span> <span class='block' style='padding-bottom:5px;'>If you do not see your name, click the <span class='b'>Create an Account</span> link.</span></p>" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="********-451E-41E6-9266-C9BF658C4E0E" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a NCAJ member. If you have questions about your membership, please call (919) 832-1413 <NAME_EMAIL>." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="BilledMessage",type="CONTENTOBJ",desc="Message displayed when account selected billed subscription",value="Our records indicate either your application is currently under review or you are a renewing member. If you are looking to renew, please click [here] to review your renewal statement. If you have questions about your membership, please call (919) 832-1413 <NAME_EMAIL>." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (919) 832-1413 <NAME_EMAIL>." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="NCAJ Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please agree to all of the following Certification Statements." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step4TopContent",type="CONTENTOBJ",desc="Content at top of page 4",value="Step 4 - Please review your selections and proceed with payment." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="4F85236A-A48C-4A23-AE2B-FA36EF57FD6D" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);
                local.tmpField = { name="PreCheckedAddOns",type="STRING",desc="UIDs for Add-Ons that qualified users will have to opt out of",value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AutoTortsSectionCertificationTitle", type="STRING", desc="Auto Torts Section Certification Title", value="Auto Torts Section Certification" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AutoTortsSectionCertification", type="CONTENTOBJ", desc="Legal Assistant Certification Statement", value="I certify that I meet the following membership criteria of the Auto Torts Section of the NCAJ and that 
                                            I will immediately notify the Chair of the Auto Torts Section if I no longer meet these criteria:<br />
                                            <br />
                                            Auto Torts Section membership is limited to NCAJ members engaged in representing plaintiffs in motor vehicle personal injury cases. Membership is limited to those who 
                                            do not and whose firms do not participate in the defense of motor vehicle personal injury claims unless approved for membership by the section Chair. Motor vehicle personal 
                                            injury claims include claims for sickness, bodily injury (physical and mental) and wrongful death that arise out of a motor vehicle claim, but do not include counterclaims.<br />
                                            <br />
                                            Further, I certify that should either I or members of my firm undertake participation in the defense of motor vehicle personal injury claims, I agree to immediately notify the 
                                            Chair of the Auto Torts Section, and I understand that my membership in the section and/or participation on the listserv may continue only with the specific approval of the section chair. 
                                            I understand no exceptions will be allowed to participation on the listserv if a member of the Auto Torts Section represents a claimant in the claim being defended, unless the member 
                                            of the Auto Torts Section representing the plaintiff consents in writing, and neither I, nor a member of my firm, represent an insurance company in the defense of the motor 
                                            vehicle personal injury claim. I understand reinstatement requests shall be made to the section Chair.<br />
                                            <br />
                                            Further, I certify that I will not forward or otherwise distribute Auto Torts Listserv messages to non-members of the Auto Torts Section. These messages are intended 
                                            for the exclusive use of Auto Torts Section members representing plaintiffs in civil actions. Messages may not be furnished to a defendant, defense interest, or 
                                            any other person not assisting in your case, except as required under the law. If the materials are sought in discovery, I will oppose the discovery, 
                                            and contact the Chair of the Auto Torts Section.<br />
                                            <br />" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="EmploymentLawSectionCertificationTitle", type="STRING", desc="Employment Law Section Certification Title", value="Employment Law Section Certification" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="EmploymentLawSectionCertification", type="CONTENTOBJ", desc="Employment Law Section Certification Statement", value="I hereby certify that I meet the following membership criteria of the Employment Law Section of the 
                                            NCAJ and that I will immediately notify the Chair of the Employment Law Section if I no longer meet these criteria:<br />
                                            <br />
                                            Membership in the Employment Law Section is limited to NCAJ members who are primarily engaged in representing plaintiff-employees in employment and labor matters. 
                                            For purposes of this requirement, 'primarily' means that more than sixty percent (60%) of a member's employment or labor practice is on behalf of employees.<br />
                                            <br />
                                            I further certify that I will not forward, distribute or share messages or information obtained through the Employment Section Listserv or the Document Bank with 
                                            non-members of the Employment Law Section, including those members of my law firm who are not members of the Employment Law Section. I agree that such information 
                                            is intended for the exclusive use of Employment Law Section members engaged in the representation of plaintiff-employees and is the work product of the attorneys 
                                            participating in the Listserv and/or Document Bank. I will not furnish such information to a defendant, defense interest, employer, insurance company, or any other 
                                            person who is not a member of the Employment Law Section, except as required under the law. If such information is sought in discovery, I will oppose the discovery, 
                                            and I will notify the Chair of the Employment Law Section of the discovery request.<br />
                                            <br />
                                            I will notify the Chair of the Employment Law Section of any suspected or known violation of these membership criteria by any other member or non-member of the Employment Law Section.<br />
                                            <br />
                                            No exceptions will be allowed to the above stated membership criteria, except as approved in advance by the Chair of the Employment Law Section.<br />
                                            <br />" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="LegalAssistantCertificationTitle", type="STRING", desc="Legal Assistant Certification Title", value="Legal Assistant Certification" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="LegalAssistantCertification", type="CONTENTOBJ", desc="Legal Assistant Certification Statement", value="I am currently employed by a member in good standing of the North Carolina Advocates for Justice; 
                                                I have successfully completed a curriculum of training as a paralegal; or; my attorney/employer attests that I am qualified by training or experience.<br />
                                                <br />" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="NursingHomeLitigationSectionCertificationTitle", type="STRING", desc="Nursing Home Litigation Section Certification Title", value="Nursing Home Litigation Section Certification" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="NursingHomeLitigationSectionCertification", type="CONTENTOBJ", desc="Nursing Home Litigation Section Certification Statement", value="I certify that I meet the following membership criteria of the Nursing Home Negligence 
                                            Section of the NCAJ and that I will notify the NCAJ if I no longer meet these criteria. This certification is in addition to any previous certification between 
                                            NCAJ and me. I understand that the use of the Listserv is a privilege of NCAJ membership and that this privilege may be revoked if I breach the terms of this 
                                            agreement. The provisions of this agreement shall be enforced by the Executive Committee of the Nursing Home Negligence Section, which has the authority to 
                                            suspend my access to the Listserv.<br />
                                            <br />
                                            Nursing Home Negligence Section membership is limited to Academy members engaged in representing plaintiffs in negligence cases. Membership is limited to those 
                                            who do not currently represent or work on behalf of any nursing home, rest home (including assisted living facilities), group home, or any healthcare provider 
                                            defined by N.C. Gen. Stat. 90-21.11 or their agents or employees concerning the defense of negligence claims against such entities or medical malpractice claims 
                                            as defined in N.C. Gen. Stat. 90-21.11.<br />
                                            <br />
                                            Further, I certify that should either I or members of my firm undertake participation in the defense of any nursing home, rest home (including assisted living facilities), 
                                            group home, or any entity whose actions are governed by N.C. Gen. Stat. 90-21.11 or their agents or employees, I agree to immediately notify the Chair of the 
                                            Nursing Home Negligence Section, and I understand that my membership in the section may continue only with the specific approval of the Section Chair. I understand 
                                            no exceptions will be allowed to participation on the listserv during the pendency of the litigation being defended by the Section member. I understand reinstatement 
                                            requests shall be made to the Section Chair.<br />
                                            <br />
                                            I certify that I will not forward or otherwise distribute Nursing Home Negligence Listserv messages to non-members of the Nursing Home Negligence Section. 
                                            These messages, or the substance or existence of such messages, are intended for the exclusive use of Nursing Home Negligence Section members representing 
                                            plaintiffs in civil actions. Messages may not be furnished to a defendant, defense interest, or any other person not assisting in my case, except as required under 
                                            the law. If the materials are sought in discovery, I will oppose the discovery, and immediately contact the Chair of the Nursing Home Negligence Section. Any provision 
                                            or distribution of materials obtained from the Listserv to anyone who is not a member of the Listserv is explicitly prohibited and any such use may expose the person 
                                            or persons using these materials in such a manner to ethical sanctions by the NCAJ and / or the appropriate state or local bar association.<br />
                                            <br />
                                            I further certify that should any of my employees who had access to the Listserv leave my employment, I will notify the Section Chair within 3 business days and will 
                                            agree to immediately change my login and password information if that employee had access to that information.<br />
                                            <br />
                                            I further certify that should any of my employees who are members of the Listserv leave my employment, I will notify the Section Chair within 3 business days.<br />
                                            <br />" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfessionalNegligenceSectionCertificationTitle", type="STRING", desc="Professional Negligence Section Certification Title", value="Professional Negligence Section Certification" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfessionalNegligenceSectionCertification", type="CONTENTOBJ", desc="Professional Negligence Section Certification Statement", value="I certify that I meet the following membership criteria of the Professional Negligence 
                                            Section of the NCAJ and that I will notify the NCAJ if I no longer meet these criteria. This certification supercedes any previous certification between me 
                                            and NCAJ. I understand that the use of the Listserv is a privilege of NCAJ membership and that this privilege may be revoked if I breach the terms of this 
                                            agreement. The provisions of this agreement shall be enforced by the Executive Committee of the Professional Negligence Section, which has the authority to 
                                            suspend my access to the Listserv.<br />
                                            <br />
                                            Professional Negligence Section membership is limited to Academy members engaged in representing plaintiffs in professional negligence cases. Membership is 
                                            limited to those who do not currently represent or work for, and whose partners and firm members do not currently represent or work for, insurance carriers, 
                                            self-insured institutions or professionals concerning the defense of professional negligence claims.<br />
                                            <br />
                                            I certify that I will not forward or otherwise distribute Professional Negligence Listserv messages to non-members of the Professional Negligence Section. 
                                            These messages, or the substance or the existence of such messages, are intended for the exclusive use of Professional Negligence Section members representing 
                                            plaintiffs in civil actions. Messages may not be furnished to a defendant, defense interest, or any other person not assisting in my case, except as required 
                                            under the law. If the materials are sought in discovery, i will oppose the discovery, and contact the Chair of the Professional Negligence Section. Any provision 
                                            or distribution of materials obtained from the Listserv to anyone who is not a member of the Listserv is explicitly prohibited and any such use may expose the 
                                            person or persons using these materials in such a manner to ethical sanctions by the NCAJ and/or the appropriate state or local bar association.<br />
                                            <br />" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="WorkersCompensationSectionCertificationTitle", type="STRING", desc="Workers' Compensation Section Certification Title", value="Workers' Compensation Section Certification" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="WorkersCompensationSectionCertification", type="CONTENTOBJ", desc="Workers' Compensation Section Certification Statement", value="I certify that I meet the following membership criteria of the Workers' Compensation Section of the NCAJ and that I will immediately notify the chair of the Workers' Compensation Section if I no longer meet these criteria:<br /><br />
                
                Workers' Compensation Section membership is limited to NCAJ members engaged in representing plaintiffs in workers' compensation cases. Membership is limited to those who do not and whose firms do not participate in the defense of insurance companies and employers in workers' compensation and/or tort claims unless approved for membership by the Section Chair. Workers' compensation claims include claims for injuries by accident, occupational disease claims, death claims, medical claims and any other claims arising under Chapter 97 of the North Carolina General Statutes.<br/> <br/>
                
                Further, I certify that should either I or members of my firm undertake participation in the defense of employers, self-insured employers, insurance companies, carriers or adjusting companies in workers' compensation matters and/or tort claims, I agree to immediately notify the Chair of the Workers' Compensation Section, both orally and in writing, and understand that my membership in the Section and/or participation on the listserv may continue only with the specific approval of the Section Chair. I understand reinstatement requests shall be made to the Section Chair.<br/> <br/>
                
                Further, I certify that I will not forward or otherwise distribute Workers' Compensation Section listserv messages or their content to non-members of the Workers' Compensation Section. These messages are intended for the exclusive use of Workers' Compensation Section members representing plaintiffs in workers' compensation cases. Messages shall not be furnished to a defendant, defense interest, or any other person not assisting in your case except as required by law.<br/><br/>
                
                I also certify that if I am a legal assistant, I will immediately notify my attorney if I leave NCAJ or the Workers' Compensation Section.<br/><br/>
                
                If I am an attorney who has a paralegal or legal assistant using the Workers' Compensation Section listserv, I will immediately notify NCAJ if that paralegal or legal assistant leaves my employment.<br/><br/>
                
                " }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="NCAJAgreement", type="CONTENTOBJ", desc="NCAJ Agreement", value="<strong>Agreement to the following certification is required for membership in NCAJ. Please read the certification and check I Agree to continue. <br />
                If you cannot agree with NCAJ's certification then you are not eligible for membership.</strong>
                <br />
                <br />
                I apply for membership in the North Carolina Advocates for Justice, Inc. I believe in the organization's mission of Protecting People's Rights through professional 
                and community legal education; championing individual rights; and protecting the safety of North Carolina's families in the workplace, in the home, and in the environment. 
                I represent the rights of those injured by the wrongful acts of others, the rights of workers or disabled people, the rights of parties in family disputes, the rights of 
                consumers in debt; the rights of those accused of criminal offenses, and/or the civil rights of individuals or otherwise meet the membership qualifications of the 
                North Carolina Advocates for Justice (NCAJ). I am dedicated to the preservation of trial by jury and the advancement of the mission of the organization. 
                I understand that no person shall be eligible for or continue membership in the association, who, for the most part, represents insurance defense in civil matters 
                or the prosecution in criminal matters.
                <br />" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="NCAJDuesMessage", type="CONTENTOBJ", desc="NCAJ Dues Message", value="<strong>Dues are not deductible as Charitable Contributions for Income Tax Purposes. Dues may be considered ordinary and necessary business deductions, except for that portion allocated for lobby expenses. NCAJ estimates that the nondeductible portion of your 2015-2016 dues is 12%.</strong>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for CC",value="NCCCCIM" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for check",value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for submitting your application!
            Please print this page for your records. A receipt will be emailed to you once your dues have been processed." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="NCAJ Membership Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="NCAJ New Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="CompanyFirmSelector",type="CONTENTOBJ",desc="Company/Firm Information tool",value="Please find your company/firm in the search tool below. If you cannot find your company/firm, we will save the name that you enter on the form" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="productsLiabilityCertificationTitle",type="STRING",desc="Title for Product Liability Certification",value="<strong>Products Liability, Class Action & Mass Torts Section Certification</strong>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="productsLiabilityCertificationContent",type="CONTENTOBJ",desc="Product Liability Certification Content",value="I certify that I meet the following membership criteria of the Products Liability, Class Action, and Mass Torts (“PLCAMT”) Section and will notify NCAJ if I no longer meet these criteria. This certification supersedes any previous certification between me and NCAJ. I understand that the use of the Listserv is a privilege of both NCAJ membership and the PLCAMT section and that this privilege may be revoked if I breach the terms of this agreement. The provisions of this agreement shall be enforced by the Executive Committee of the PLCAMT Section, which has the authority to suspend my access to the Listserv.<br/><br/>

            I understand that the PLCAMT Section membership is limited solely to NCAJ members engaged in representing plaintiffs in products liability, class action, or mass tort cases. Membership is limited to those who do not, and have not for at least 24 consecutive months, defended any products liability, class action, or mass tort claims. Further, I certify that no member of my firm undertakes participation in the defense of any products liability, class action, or mass tort claim.<br/><br/>
           
           Further, I certify that should either I or members of my firm undertake participation in the defense of any products liability, class action, or mass tort claim, I agree to immediately notify the Chair of the PLCAMT Section, and I understand that my membership in the section may continue only with the specific approval of the Section Chair. I understand no exceptions will be allowed to participation on the listserv during the pendency of the litigation being defended by the Section member or any member of their firm. I understand reinstatement requests shall be made to the Section Chair.<br/><br/>
           
           I certify that I will not forward or otherwise distribute PLCAMT Listserv messages to non-members of the PLCAMT Section. These messages, or the substance or existence of such messages, are intended for the exclusive use of PLCAMT Section members representing plaintiffs in civil actions. Messages may not be furnished to a defendant, defense interest, or any other person not assisting in my case, except as required under the law. If the materials are sought in discovery, I will oppose the discovery, and immediately contact the Chair of the PLCAMT Section. Any provision or distribution of materials obtained from the Listserv to anyone who is not a member of the Listserv is explicitly prohibited and any such use may expose the person or persons using these materials in such a manner to ethical sanctions by the NCAJ and / or the appropriate state or local bar association.<br/><br/>
           
           I further certify that should any of my employees who had access to the Listserv leave my employment, I will notify the Section Chair within 3 business days and will agree to immediately change my login and password information if that employee had access to that information.<br/><br/>
           
           I further certify that should any of my employees who are members of the Listserv leave my employment, I will notify the Section Chair within 3 business days.<br/><br/>
           
           " }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            
            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo= variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
            ));
            
            variables.personalInformationFieldSetUID = "21855BB5-35ED-475B-9B17-0CAD77FCE827";
            variables.contactInformationFieldSetUID = "5F23105C-6A5C-4805-8C8F-D6DCCC3966E9";
            variables.professionalInformationFieldSetUID = "4192B113-C79C-4AB7-A848-7503C7F34E72";
            variables.demographicInformationFieldSetUID = "B0C67834-B0A5-4EA1-AF8E-3DA5A8C10F53";
            variables.optionalDirectoryListingsFieldSetUID = "BC7EE5A1-F2E0-405D-AE95-E22ED9738AE5";
            variables.communicationPreferencesFieldSetUID = "419E0A4B-DD9E-4571-A0C2-ACCB7FB010DE";
			variables.NCMockTrialFieldSetUID = "5A4AEB0B-E9F8-409D-893B-8C4AA53FB46A";
            variables.practiceCodeOptionsFieldSetUID = "DD425BE4-A9D1-4E6F-82EC-9C59E86E9865";
            variables.appCredentialingFieldSetUID = "47A8F486-DEF6-44A0-8306-EBA485C6BFD9";
            variables.paraAsstntCertificationFieldSetUID = "95793689-6A1E-46D8-8ADA-DCB6C7A5F584";
            variables.legalAssistantStudentFieldSetUID = "B3147CD8-B782-4F3A-B2AB-F1ACABBA5785";
            variables.organizationGroupUID = "9D742ADB-8EEB-45AD-ACB3-B03ED669C0F0";
			variables.addressFieldSetUID = "ACAD2EC6-F904-4A5A-93C7-3546C3F375C9";
			variables.homeAddressFieldSetUID = "F30646C5-3AE7-41F9-9064-EDC2CC7A5CFD";
			variables.addressPreferenceFieldSetUID = "68AF56F2-067A-4D3F-8D93-9ADA62801D49";
            
            variables.firstAttorneyRateUID = "ABDFFDA7-1951-4139-BD19-FDFF5889774C";
            variables.newLawysersDivisionSetUID = "2E85A4A7-08D6-4394-8F64-88413B9949B9";
            variables.newLawyersDivisionSubscription = "AA5D2230-6DD6-4E67-B3EB-401F73C1CD3A";

            variables.productLiabilityCertUID = "304C538E-C0E6-4AE5-8B38-43D7C739DA3F";

            variables.sustainingMemberUpgradeFieldSetUID = "020B8C10-077C-4FFB-A44E-A9AFBB55195A";

            variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");
            
            /* Member History Vars */
            variables.useHistoryID = 0;
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";

            variables.origMemberID = variables.useMID;
            if(local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
				(structCount(variables.formFields) gt 0) and 
                structKeyExists(variables.formFields, "step0") and 
                structKeyExists(variables.formFields.step0, "memberID") and 
                int(val(variables.formFields.step0.memberID)) gt 0){            
                    variables.useMID = variables.formFields.step0.memberID;
                    if(structKeyExists(variables.formFields.step0, "origMemberID") and int(val(variables.formFields.step0.origMemberID)) gt 0){
                        variables.origMemberID = variables.formFields.step0.origMemberID;
                    }              

            }else if(variables.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = variables.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }
            if( local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                local.formAction neq "showMemberInfo" and 
                local.formAction neq "processMemberInfo" and 
                structKeyExists(variables.formFields, "step1") and 
                structKeyExists(variables.formFields.step1, "useHistoryID") and 
                int(val(variables.formFields.step1.useHistoryID)) gt 0){
                    variables.useHistoryID = int(val(variables.formFields.step1.useHistoryID));
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=variables.cfcuser);
            local.subStatus = "";
            if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
                local.subStatus = hasSub(int(variables.useMID));
            }

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin');  
            }else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
                local.returnHTML = showError(errorCode=local.subStatus);  
            }else{
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMemberInfo();
                                break;		
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        switch (processMemberInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMembershipInfo();
                                break;
							case "spam":
							local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMembershipInfo":
                        switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showCertificationStatements();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processCertificationStatements":
                        switch (processCertificationStatements(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showPayment();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;             
                    case "processPayment":
                        local.processStatus = processPayment(event=arguments.event);
                        switch (local.processStatus) {
                            case "success":
                                local.returnHTML = showConfirmation();
                                application.objUser.setIdentifiedMemberIDfromID(cfcuser=variables.cfcuser, memberID=0);
                                variables.formFields = structNew();
								application.mcCacheManager.sessionDeleteValue("formFields")
                                break;
                            default:
                                local.returnHTML = showError(errorCode='failpayment');
                                break;				
                        }
                        break;
                    case "showMembershipInfo":
                        local.returnHTML = showMembershipInfo();
                        break;	
                    case "showMemberInfo":
                        local.returnHTML = showMemberInfo();
                        break;
                    case "showCertificationStatements":
                        local.returnHTML = showCertificationStatements();
                        break;                  		
                    default:
                        if(application.mcCacheManager.sessionValueExists("captchaEntered")) {	
							application.mcCacheManager.sessionDeleteValue("captchaEntered")
						}
						
						variables.formFields = structNew();
		                if(application.mcCacheManager.sessionValueExists("formFields")) {
							application.mcCacheManager.sessionDeleteValue("formFields")
						}
                        local.returnHTML = showLookup();
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");
        </cfscript>

    </cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

        <cfset local.fieldSetUIDlist = '#variables.personalInformationFieldSetUID#'>

        <cfset local.memberFieldDetails = structNew()>
        <cfset local.memberFieldData = structNew()>

        <cfset local.contactTypeField = {fieldCode="",fieldLabel=""}>
        <cfset local.strData = {}>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>	
        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

            <cfset local.contactTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldLabel='Membership Category']")/>
            <cfif arrayLen(local.contactTypeData)>				
                <cfset local.contactTypeField.fieldCode = local.contactTypeData[1].XmlAttributes.fieldCode>
                <cfset local.contactTypeField.fieldLabel = local.contactTypeData[1].XmlAttributes.fieldLabel>
            </cfif>
        </cfloop>
        
		<cfsavecontent variable="local.headCode">
			<cfoutput>          
                #variables.pageJS# 
                #variables.pageCSS#
            <style type="text/css">
                @media screen and (max-width: 767px){
                    .borderLeft{border: unset!important;}				
                }		
                ##divFrmErr{
                    font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;
                    font-size: 9pt;
                    padding:8px;
                }
                .CPSectionTitle{height:unset!important;}
                input[type="checkbox"], input[type="radio"] {
                    -webkit-appearance: auto !important;
                }
                .CPSection{border:0.5px solid;}
                .CPSectionContainer{padding: 10px;}
            </style>
            <script type="text/javascript">
				var step = 0;
				var prevStep = 0;
                function afterFormLoad() {
                    $('html, body').animate({ scrollTop: 500 }, 500);
                }	
				function afterFormLoadOne(obj){
                    var _CF_this = document.forms['#variables.formName#'];

					afterFormLoad();

                    var thisForm = document.forms["#variables.formName#"];
                    var er_change = function(r) {
                        var results = r;
                        if( results.success ){
                            if (results.isnewmember && typeof results.licensenumber != "undefined" && results.licensenumber != '') {
                                $('.professionalLicensesHolder').show();
                                if($('.mpl_pltypeid').length > 0){
                                    $('.mpl_pltypeid').multiselect("widget").find(":input[value=" + results.licenses[0].pltypeid +"]").each(function() {
                                        this.click();
                                    });
                                    $('##mpl_'+ results.licenses[0].pltypeid +'_licenseNumber').val(results.licenses[0].licensenumber);
                                    $('##mpl_'+ results.licenses[0].pltypeid +'_activeDate').val(results.licenses[0].activedate);
                                    $('##mpl_'+ results.licenses[0].pltypeid +'_status option[value="active"]').attr('selected','selected');	
                                    $('###local.contactTypeField.fieldCode# option:contains(Attorney)').first().attr('selected','selected');

                                    if(typeof adjustFieldsetDisplay != 'undefined')
                                        adjustFieldsetDisplay();									
                                }
                            }
                        }
                        else{ /*alert('not success');*/ }
                    };
                    
                    var objParams = { memberNumber:obj.memberNumber };
                    TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change); 
                    
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID').val(memObj.memberID);
                    $('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
                    mc_continueForm($('###variables.formName#'),function(){afterFormLoadOne(memObj);});
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				
				function addMember(memObj) {
					$.colorbox.close();
					assignMemberData(memObj);
				}
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585)
						_popupWidth = windowWidth - 30;
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}		
				window.onhashchange = function() {       
                    if (location.hash.length > 0) {        
                        step = parseInt(location.hash.replace('##',''),10);     
                        if (prevStep > step){
                            if(step==1)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
                            if(step==2)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
                            if(step==3)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("showCertificationStatements");           
                            mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
                        }
                    } else
                        step = 1;
                    prevStep = step;				    
				}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585)
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						else
							$.colorbox.resize({innerWidth:550, innerHeight:330});
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
            <div id="customPage" class="customPage">
                <cfif len(variables.strPageFields.FormTitle)>
                    <div class="row-fluid" id="FormTitleId">
                        <div class="span12">
                            <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                        </div>
                    </div>
                </cfif>			
                <div class="r i frmText">*Denotes required field</div>	
                
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
                    <cfinput type="hidden" name="fa" id="fa" value="processLookup">
                    <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                    <cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
                    <div class="CPSection row-fluid">
                        <div class="CPSectionTitle">#variables.strPageFields.AccountLocatorTitle#</div>
                        <div class="frmRow1" style="padding:10px;">
                            <table cellspacing="0" cellpadding="2" border="0" width="100%">
                                <tr>
                                    <td width="50%" class="c">
                                        <div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
                                            <button name="btnAddAssoc" type="button" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="frmText">
                                            #variables.strPageFields.AccountLocatorInstructions#
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>				
                </cfform>
            </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step0")>
            <cfset structDelete(variables.formFields, "step0")>
        </cfif>	

        <cfset variables.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">

        <cfset var local = structNew()>
        <cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

        <!--- Load prefill data: either the fields from new acct form or from the members table --->
        <cfset local.fieldSetUIDlist = '#variables.personalInformationFieldSetUID#,#variables.contactInformationFieldSetUID#,#variables.professionalInformationFieldSetUID#,#variables.demographicInformationFieldSetUID#,#variables.optionalDirectoryListingsFieldSetUID#,#variables.practiceCodeOptionsFieldSetUID#,#variables.appCredentialingFieldSetUID#,#variables.paraAsstntCertificationFieldSetUID#,#variables.legalAssistantStudentFieldSetUID#,#variables.sustainingMemberUpgradeFieldSetUID#,#variables.addressFieldSetUID#,#variables.homeAddressFieldSetUID#'>

        <cfset local.memberFieldDetails = structNew()>
        <cfset local.memberFieldData = structNew()>

        <cfset local.contactTypeField = {fieldCode="",fieldLabel=""}>
		
		<cfset local.mailingAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.officestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.officepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddressCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddressstateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddresspostalcodeField = {fieldCode="",fieldLabel=""}>
        <cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset local.strData.one = checkSessionExist("step1")/>

        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

            <cfset local.contactTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldLabel='Membership Category']")/>
            <cfif arrayLen(local.contactTypeData)>				
                <cfset local.contactTypeField.fieldCode = local.contactTypeData[1].XmlAttributes.fieldCode>
                <cfset local.contactTypeField.fieldLabel = local.contactTypeData[1].XmlAttributes.fieldLabel>
            </cfif>
            <cfset local.lawSchoolGraduationTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldLabel='Graduation Date']")/>
            <cfif arrayLen(local.lawSchoolGraduationTypeData)>				
                <cfset local.lawSchoolGraduationTypeField.fieldCode = local.lawSchoolGraduationTypeData[1].XmlAttributes.fieldCode>
                <cfset local.lawSchoolGraduationTypeField.fieldLabel = local.lawSchoolGraduationTypeData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.mailingAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Mailing_address1']")/>
			<cfif arrayLen(local.mailingAddress1FieldData)>				
				<cfset local.mailingAddress1Field.fieldCode = local.mailingAddress1FieldData[1].XmlAttributes.fieldCode>
				<cfset local.mailingAddress1Field.fieldLabel = local.mailingAddress1FieldData[1].XmlAttributes.fieldLabel>
			</cfif>
			<cfset local.officeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Physical_address1']")/>
			<cfif arrayLen(local.officeAddress1FieldData)>				
				<cfset local.officeAddress1Field.fieldCode = local.officeAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeAddress1Field.fieldLabel = local.officeAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.officeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Physical_city']")/>
			<cfif arrayLen(local.officeCityFieldData)>				
				<cfset local.officeCityField.fieldCode = local.officeCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeCityField.fieldLabel = local.officeCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.officestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Physical_stateprov']")/>
			<cfif arrayLen(local.officestateprovFieldData)>				
				<cfset local.officestateprovField.fieldCode = local.officestateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officestateprovField.fieldLabel = local.officestateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.officepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Physical_postalcode']")/>
			<cfif arrayLen(local.officepostalcodeFieldData)>				
				<cfset local.officepostalcodeField.fieldCode = local.officepostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officepostalcodeField.fieldLabel = local.officepostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.homeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_address1']")/>
			<cfif arrayLen(local.homeAddress1FieldData)>
				<cfset local.homeAddress1Field.fieldCode = local.homeAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddress1Field.fieldLabel = local.homeAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.homeAddressCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_city']")/>
			<cfif arrayLen(local.homeAddressCityFieldData)>				
				<cfset local.homeAddressCityField.fieldCode = local.homeAddressCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddressCityField.fieldLabel = local.homeAddressCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.homeAddressstateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_stateprov']")/>
			<cfif arrayLen(local.homeAddressstateprovFieldData)>				
				<cfset local.homeAddressstateprovField.fieldCode = local.homeAddressstateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddressstateprovField.fieldLabel = local.homeAddressstateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.homeAddresspostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_postalcode']")/>
			<cfif arrayLen(local.homeAddresspostalcodeFieldData)>				
				<cfset local.homeAddresspostalcodeField.fieldCode = local.homeAddresspostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homeAddresspostalcodeField.fieldLabel = local.homeAddresspostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>			
            <cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.personalInformationFieldSetUID#,#variables.contactInformationFieldSetUID#,#variables.professionalInformationFieldSetUID#,#variables.demographicInformationFieldSetUID#,#variables.appCredentialingFieldSetUID#,#variables.addressFieldSetUID#,#variables.homeAddressFieldSetUID#',local.fieldSetUid)>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				<cfif NOT variables.isLoggedIn AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>				
					<cfloop collection="#local.memberFieldData#" item="local.key" >
						<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
							<cfset StructDelete(local.memberFieldData, local.key)>
						</cfif>					
					</cfloop>
				</cfif>
			</cfif>          
        </cfloop>

        <cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

        <cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.professionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.demographicInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demographicInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.optionalDirectoryListingsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.optionalDirectoryListingsFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.communicationPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.communicationPreferencesFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.NCMockTrialFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.NCMockTrialFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.practiceCodeOptionsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.practiceCodeOptionsFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.appCredentialingFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.appCredentialingFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.paraAsstntCertificationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.paraAsstntCertificationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.legalAssistantStudentFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.legalAssistantStudentFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.sustainingMemberUpgradeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.sustainingMemberUpgradeFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="collection", strData=local.strData.one)>
		       
	   <cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>
		</cfif>

        <cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
        <cfset local.licenseStatus = {}>
        <cfset local.index = 1>
        <cfloop query="local.qryOrgProLicenseStatuses">
            <cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
            <cfset local.index = local.index + 1>
        </cfloop> 
        <cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>

        <cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID='#variables.organizationGroupUID#', orgID=variables.orgID)>
		<cfquery name="local.qryCompanyByGroup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
            DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;
			SELECT distinct parentMember.memberNumber, parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber,
			rt.recordTypeCode,rt.recordTypeName from
            dbo.ams_recordRelationships AS rr  
            INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
            INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
            INNER JOIN dbo.ams_recordTypes AS rt ON rt.recordTypeID = rtrt.masterRecordTypeID		
			INNER JOIN dbo.ams_members AS parentMember ON parentMember.orgID = @orgID and parentMember.memberID = rr.masterMemberID
            INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID and mg.memberID = parentMember.memberID
            INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = mg.groupID
            WHERE rr.orgID = @orgID AND
			g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
            AND rt.recordTypeCode IN ('LawFirm','PublicAgency','Government','School')
			AND g.status = 'A' AND ISNULL(parentMember.company,'') <> '' ORDER BY parentMember.company

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
        
        <cfquery name="local.qryLinkedParentCompany" datasource="#application.dsn.membercentral.dsn#">
            SET NOCOUNT ON;
            SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

            DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

            SELECT parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber,rt.recordTypeCode
            FROM dbo.ams_members AS childMember
            INNER JOIN dbo.ams_recordRelationships AS rr ON rr.orgID = @orgID and rr.childMemberID = childMember.memberID and rr.isActive = 1
            INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
            INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
            INNER JOIN dbo.ams_recordTypes AS rt ON rt.recordTypeID = rtrt.childRecordTypeID
            INNER JOIN dbo.ams_members AS parentMember ON parentMember.orgID = @orgID and parentMember.memberID = rr.masterMemberID
            INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID and mg.memberID = parentMember.memberID
            INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = mg.groupID
            WHERE childMember.memberID = <cfqueryparam value="#local.strData.one.memberID#" cfsqltype="CF_SQL_INTEGER">
            AND rt.recordTypeCode IN ('LawFirm','PublicAgency','Government','School')
            AND g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">

            SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
        </cfquery>
		
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
        
        <cfsavecontent variable="local.headCode">
            <cfoutput>
			<style type="text/css">	
                .mini{width:10px;}
                .CPSectionContainer tr:nth-child(even) {background:##dedede;}
                .CPSectionContainer tr:nth-child(odd) {background:white;}
                .CPSectionContainer input[type="text"],.CPSectionContainer textarea {width:206px!important;margin: 0px!important;}
                .CPSectionContainer select{width:220px!important;margin: 0px!important;}
                .CPSectionContainer table td:nth-child(2) {white-space: initial!important;}
                .CPSectionContainer table td:nth-child(1){color:red!important;}
                .CPSectionContainer table{width: 100%!important;}	
                .CPSectionContainer .tsAppBodyText{color:black!important;padding: 5px!important;vertical-align: middle!important;}				
                .optionalDirectoryListingsFieldSet .CPSectionContainer table tr:first-child td:nth-child(1), .practiceCodeOptionsFieldSet .CPSectionContainer table tr:first-child td:nth-child(1){color:black!important;}
                .CPSectionContainer button.ui-multiselect {width:220px!important;}
                .CPSectionContainer div.ui-multiselect-menu {width:214px!important;}	
                .CPSectionTitle{height:unset!important;}
                ##selectedLicense > div.row-fluid{margin-bottom:5px;}
                ##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
                @media screen and (max-width: 767px){
					.CPSectionContainer table td {display: block;margin-bottom:0px;}
					.CPSectionContainer table td:nth-child(1) {display: inline;margin: 0;padding: 0;}
					.CPSectionContainer table td:nth-child(2) {display: inline;margin: 0;padding: 0;}
					.CPSectionContainer table td:nth-child(3) {display: inline;margin: 0;padding: 0;}
					.CPSectionContainer table td:nth-child(4) {margin-bottom: 12px;margin-left: 0;padding-left: 0;}							
					.CPSectionContainer div.ui-multiselect-menu{width:auto!important;}
                    .borderLeft{border: unset!important;}		
                    ##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}		
				}			
			</style>
            <script language="javascript">
				function afterShowMemberInfo(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 300 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}	
                
                function changeCompany(value,text,firstName,lastName,memberNumber,recordTypeCode) {	
                   if(value != "Please Select" && value != 0){
						$(".organizationFirmFieldSetHolder").html($(".organizationFirmFieldSetWrapper").html());
						if(text.length==0 && $(".organizationFirmFieldSetHolder ##m_company").length && $(".organizationFirmFieldSetHolder ##m_company").val().length){
							
						}else{
							$(".organizationFirmFieldSetHolder ##m_company").val(text);
						}
						
						$("##orgMemberID").val(value);
						$("##orgCompanyName").val(text);						
						$("##orgFirstName").val(firstName);
						$("##orgLastName").val(lastName);
						$("##orgMemberNumber").val(memberNumber);
						$("##recordTypeCode").val(recordTypeCode);
                        if($(".organizationFirmFieldSetHolder ##m_company").parent().find('##changeCompany').length == 0){
                            $(".organizationFirmFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Firm</a>');					
                        }
						$("##changeCompany").show();
						$("##changeCompany").unbind("click");
                        $(".organizationsFirmsHolder").hide();
						$("##changeCompany").click(function(){
                            $("##changeCompany").hide();
                            if($('##m_company').val() != ''){
                                $(".organizationFirmFieldSetHolder").show();                                
                            }else{
                                $(".organizationFirmFieldSetHolder").hide();
                            }
							$(".organizationsFirmsHolder").show();
                            
                            $("##companyField").val("");
                            $("##companyField").multiselect( 'refresh' );
                            $(".organizationFirmFieldSetHolder").html('');
							<cfif ListFindNoCase('Office Mailing',local.qryOrgAddressTypes.addressType)>
								if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length){
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();	
								}						
							</cfif>
						});

                        if($('##m_company').val() != ''){
                            $(".organizationFirmFieldSetHolder").show();                                
                        }else{
                            $(".organizationFirmFieldSetHolder").hide();
                        }
						$("##m_company").unbind("change");
						$("##m_company").on('change', function() {		
							var company = $('##companyField option').filter(function () { return $(this).html() == $("##m_company").val(); });					
							if(company.length){
								$("##orgMemberID").val(company.val());
								$("##orgCompanyName").val(company.text());
								$("##orgFirstName").val(company.attr("firstName"));
								$("##orgLastName").val(company.attr("lastName"));
								$("##orgMemberNumber").val(company.attr("memberNumber"));
								$("##recordTypeCode").val(company.attr("recordTypeCode"));
							}else{								
								$("##orgMemberID").val(0);
								$("##orgCompanyName").val('');
								$("##orgFirstName").val('');
								$("##orgLastName").val('');
								$("##orgMemberNumber").val('');
								$("##recordTypeCode").val('');
							}
						});
					}else if(value == 0){
                        $(".organizationFirmFieldSetHolder").html($(".organizationFirmFieldSetWrapper").html());
                        if($(".organizationFirmFieldSetHolder ##m_company").parent().find('##changeCompany').length == 0){
                            $(".organizationFirmFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Firm</a>');					
                        }
                        $("##changeCompany").show();
                        $("##orgMemberID").val(0);
						$("##orgCompanyName").val('');
						$("##orgFirstName").val('');
						$("##orgLastName").val('');
						$("##orgMemberNumber").val('');
						$("##recordTypeCode").val('');
                        $(".organizationFirmFieldSetHolder").show();  
                        $("##changeCompany").click(function(){
                            $("##changeCompany").hide();
                            if($('##m_company').val() != ''){
                                $(".organizationFirmFieldSetHolder").show();                                
                            }else{
                                $(".organizationFirmFieldSetHolder").hide();
                            }
							$(".organizationsFirmsHolder").show();
                            
                            $("##companyField").val("");
                            $("##companyField").multiselect( 'refresh' );
                            $(".organizationFirmFieldSetHolder").html('');
							<cfif ListFindNoCase('Office Mailing',local.qryOrgAddressTypes.addressType)>
								if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length){
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();	
								}						
							</cfif>
						});     
                    }else{
                        $("##changeCompany").hide();
                        if(text != undefined){
                            if(text.length==0 && $(".organizationFirmFieldSetHolder ##m_company").length && $(".organizationFirmFieldSetHolder ##m_company").val().length){

                            }else{
                                $(".organizationFirmFieldSetHolder").html('');
                            }
                        }else{
                            $(".organizationFirmFieldSetHolder").html('');
                        }						
						$("##orgMemberID").val(0);
						$("##orgCompanyName").val('');
						$("##orgFirstName").val('');
						$("##orgLastName").val('');
						$("##orgMemberNumber").val('');
						$("##recordTypeCode").val('');
					}
					bindMailingAddressChange();
					$('###local.mailingAddress1Field.fieldCode#').change();
				}
				function bindMailingAddressChange(){
					$(document).on('change','.organizationFirmFieldSetHolder ###local.mailingAddress1Field.fieldCode#',function(){
						addressFieldUpdate#ListGetAt(local.mailingAddress1Field.fieldCode, 2, "_")#($(this));
					});
				}
                function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.contactTypeField.fieldCode#']).find('option:selected').text());
                    if(parseInt($("##orgMemberID").val()) == 0){
						changeCompany("Please Select","","","");
					}
					switch(memType) {						
						case 'Attorney':
                        case 'Public Service Attorney':
                            showFieldsByContainerClass('professionalInformationFieldSet,sustainingMemberUpgradeFieldSet,optionalDirectoryListingsFieldSet,communicationPreferencesFieldSet,NCMockTrialFieldSet,addressFieldSet');
                            $('.professionalLicensesHolder').show();
							resetFormFieldsByContainerClass('');
							break;
                        case 'Retired Attorney':
                            showFieldsByContainerClass('professionalInformationFieldSet,addressFieldSet');
                            $('.professionalLicensesHolder').show();
							resetFormFieldsByContainerClass('');
							break;
                        case 'LANC Attorney':
						case 'IDS Attorney':
							showFieldsByContainerClass('professionalInformationFieldSet,optionalDirectoryListingsFieldSet,addressFieldSet');
                            $('.professionalLicensesHolder').show();
							resetFormFieldsByContainerClass('');
							break;
                        case 'Paralegal':
							showFieldsByContainerClass('associateCredentialingFieldSet,paralegalVolunteeringFieldSet,paraAsstntCertificationFieldSet,addressFieldSet');
							resetFormFieldsByContainerClass('');
                            resetProfessionalLicenses();				
							break;
                        case 'LANC Paralegal':
							showFieldsByContainerClass('paraAsstntCertificationFieldSet,addressFieldSet');
							resetFormFieldsByContainerClass('');
                            resetProfessionalLicenses();				
							break;
						case 'IDS Paralegal':
							showFieldsByContainerClass('paraAsstntCertificationFieldSet,addressFieldSet');
							resetFormFieldsByContainerClass('');
                            resetProfessionalLicenses();				
							break;
                        case 'Paralegal Student':
							showFieldsByContainerClass('legalAssistantStudentFieldSet,addressFieldSet');
							resetFormFieldsByContainerClass('');
                            resetProfessionalLicenses();					
							break;
                        case 'Law School Student':
							showFieldsByContainerClass('professionalInformationFieldSet');
							resetFormFieldsByContainerClass('addressFieldSetHolder');
                            resetProfessionalLicenses();	
							break;					
						default:
							showFieldsByContainerClass('');
                            resetFormFieldsByContainerClass('');
                            resetProfessionalLicenses();
							break;
					}
				}
				function showFieldsByContainerClass(classList){
					$(".professionalLicensesHolder").hide();
                    $(".paraAsstntCertificationFieldSetHolder").html('');
                    $(".legalAssistantStudentFieldSetHolder").html('');
                    $(".professionalInformationFieldSetHolder").html('');
                    $(".sustainingMemberUpgradeFieldSetHolder").html('');
                    $(".optionalDirectoryListingsFieldSetHolder").html('');
					$(".communicationPreferencesFieldSetHolder").html('');
					$(".NCMockTrialFieldSetHolder").html('');
					$(".addressFieldSetHolder").html('');

					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());
                            $("."+classListArray[i]+"Holder input[type=text]").each(function(){
                                if(typeof $(this).data('function') != 'undefined' && $(this).data('function').includes('readyPicker')){
                                    mca_setupDatePickerField($(this).attr('id'));
                                }
                            });
                            $("."+classListArray[i]+"Holder .ui-multiselect").each(function(){
								$(this).remove();
							});							
							$("."+classListArray[i]+"Holder select[data-function*='multiSelect']").each(function(){
								$(this).multiselect({ header:"Choose options below", selectedList:10, minWidth:400 })
							});	
						}

					});					
				}
				function showFieldByFieldList(fieldName){
					var _CF_this = document.forms['#variables.formName#'];

					var fieldNameListArray = (fieldName).split(",");
					$.each(fieldNameListArray,function(i){
						$(_CF_this[fieldNameListArray[i]]).parents('tr').show();
					});
				}
				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){
							$("."+containerClassArray[i]).html('');
						});
					}
				}
				function resetFormFieldByFieldList(fieldName){
					var _CF_this = document.forms['#variables.formName#'];
					if(fieldName.length){
						var fieldNameListArray = (fieldName).split(",");
						$.each(fieldNameListArray,function(i){
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});
						});
					}else{
						var fieldNameListArray=("").split(",");
						$.each(fieldNameListArray,function(i){
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}
				}
                function resetProfessionalLicenses(){
					$('.mpl_pltypeid').multiselect("uncheckAll"); 
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}
				}			
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
                function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();	
                    var memType = $.trim($(_CF_this['#local.contactTypeField.fieldCode#']).find('option:selected').text());	

                    #local.appCredentialingFieldSet.jsValidation#

                    if(memType == 'Attorney' || memType == 'Public Service Attorney' || memType == 'Retired Attorney' || memType == 'LANC Attorney' || memType == 'IDS Attorney'){

						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();
                                    if($("##mpl_"+val+"_licenseNumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}
					}
                    if(memType != '' && (memType == 'Paralegal' || memType == 'LANC Paralegal' || memType == 'IDS Paralegal')){
                        #local.paraAsstntCertificationFieldSet.jsValidation#
                    }
                    if(memType != '' && (memType == 'Paralegal Student')){
                        #local.legalAssistantStudentFieldSet.jsValidation#
                    }
                    #local.personalInformationFieldSet.jsValidation#

                    if ($('.organizationFirmFieldSetHolder').text() == "" && ($(".organizationsFirmsHolder ##companyField").val() == 'Please Select' || $(".organizationsFirmsHolder ##companyField").val() === null )) 
                        arrReq[arrReq.length] = 'Firm Contact Information is required.';
                    else {
                        #local.contactInformationFieldSet.jsValidation#
                    }
					
					var homeAddressRequired = true;
					var addressRequired = false;
					var isSetAllOfficeAddress = 0;
					if(memType.length > 0 && (memType != 'Law School Student')){
						#local.addressFieldSet.jsValidation#								
						<cfif len(trim(local.officeAddress1Field.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllOfficeAddress = isSetAllOfficeAddress + 1;}
						</cfif>
						<cfif len(trim(local.officeCityField.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officeCityField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						</cfif>
						<cfif len(trim(local.officestateprovField.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officestateprovField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						</cfif>
						<cfif len(trim(local.officepostalcodeField.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						</cfif>
						var addressRequired = true;
					}
                    #local.homeAddressFieldSet.jsValidation#   					

					var isSetAllhomeAddress = 0;		
					<cfif len(trim(local.homeAddress1Field.fieldCode))>                        
						if(_CF_hasValue(_CF_this['#local.homeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllhomeAddress = isSetAllhomeAddress + 1;}
					</cfif>
					<cfif len(trim(local.homeAddressCityField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homeAddressCityField.fieldCode#'], "TEXT", false)) isSetAllhomeAddress = isSetAllhomeAddress + 1;
					</cfif>
					<cfif len(trim(local.homeAddressstateprovField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homeAddressstateprovField.fieldCode#'], "TEXT", false)) isSetAllhomeAddress = isSetAllhomeAddress + 1;
					</cfif>
					<cfif len(trim(local.homeAddresspostalcodeField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homeAddresspostalcodeField.fieldCode#'], "TEXT", false)) isSetAllhomeAddress = isSetAllhomeAddress + 1;
					</cfif>
					
					if(addressRequired ){
						if(isSetAllOfficeAddress > 0 && isSetAllOfficeAddress !=4){
							arrReq[arrReq.length] = "The following fields are required for Business Address:<ul><li>Address</li><li>City</li><li>State</li><li>Postal Code</li></ul>";                        
						}
					}

					if(homeAddressRequired ){
						if(isSetAllhomeAddress > 0 && isSetAllhomeAddress !=4){
							arrReq[arrReq.length] = "The following fields are required for Home Address:<ul><li>Address</li><li>City</li><li>State</li><li>Postal Code</li></ul>";                      
						}
					}
					if(isSetAllOfficeAddress == 0 && isSetAllhomeAddress == 0 && $('###local.mailingAddress1Field.fieldCode#').val() == ''){
						arrReq[arrReq.length] = "Physical Address is required";
					}
					
                    #local.addressPreferencesFieldSet.jsValidation#

                    if(memType == 'Attorney' || memType == 'Public Service Attorney'){
                        #local.professionalInformationFieldSet.jsValidation#
                        #local.sustainingMemberUpgradeFieldSet.jsValidation#
                    }

                    if(memType == 'Retired Attorney' || memType == 'Law School Student' || memType == 'LANC Attorney' || memType == 'IDS Attorney'){
                        #local.professionalInformationFieldSet.jsValidation#
                    }

                    if(memType != '' && memType == 'Law School Student'){
						if(!_CF_hasValue(_CF_this['#local.lawSchoolGraduationTypeField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Graduation Date is required.";
					}

                    #local.demographicInformationFieldSet.jsValidation#

                    if(memType == 'Attorney'  || memType == 'LANC Attorney' || memType == 'IDS Attorney' || memType == 'Public Service Attorney'){
                        #local.optionalDirectoryListingsFieldSet.jsValidation#
                    }
					if(memType == 'Attorney' || memType == 'Public Service Attorney'){
                    #local.communicationPreferencesFieldSet.jsValidation#
					#local.NCMockTrialFieldSet.jsValidation#
					}
                    #local.practiceCodeOptionsFieldSet.jsValidation#
                    
					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterShowMemberInfo);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterShowMemberInfo);
					return false;
				}
                function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
                    $("##state_table").show();
                    if(status == ''){
                        status = 'Active';
                    }
                    strOption = '';
                    <cfloop collection="#local.licenseStatus#" item="local.i" >
                        strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
                    </cfloop>
                    if(isChecked){
                        $('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
                                '<div class="span3"><span class="tsAppBodyText"><b><i style="color: red!important;">'+licenseName+'</i></b></span></div>'+
                                '<div class="span3 visible-phone"><span class="tsAppBodyText"><b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b></span></div>'+
                                '<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
                                '<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
                                '<div class="span3 visible-phone"><span class="tsAppBodyText"><b>#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</b></span></div>'+
                                '<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" autocomplete="off"></div>'+
                                '<div class="span3 visible-phone"><span class="tsAppBodyText"><b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b></span></div>'+
                                '<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
                                '</div>');
                        $('##mpl_'+val+'_status').val(status);
                        mca_setupDatePickerField('mpl_'+val+'_activeDate');
                        $('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
                    }else{
                        $("##tr_state_"+val).remove();								
                    }
                    if($('##selectedLicense .row-fluid').length == 0){
                        $("##state_table").hide();
                    }	
                }
				
				$(document).ready(function() {		
					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>		
                
                    <cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					</cfif>                   
                    
                    <cfif structKeyExists(local.strData.one, "orgMemberID") and val(local.strData.one["orgMemberID"]) NEQ 0>
						$("##orgMemberID").val('#val(local.strData.one["orgMemberID"])#');
						$("##orgCompanyName").val('#local.strData.one["orgCompanyName"]#');
						$("##orgFirstName").val('#local.strData.one["orgFirstName"]#');
						$("##orgLastName").val('#local.strData.one["orgLastName"]#');
						$("##orgMemberNumber").val('#local.strData.one["orgMemberNumber"]#');
						$("##recordTypeCode").val('#local.strData.one["recordTypeCode"]#');
					</cfif>

					$(".organizationFirmFieldSetHolder").html($(".organizationFirmFieldSetWrapper").html());
					bindMailingAddressChange();
                    if($(".organizationFirmFieldSetHolder ##m_company").parent().find('##changeCompany').length == 0){
					    $(".organizationFirmFieldSetHolder ##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Firm</a>');					
                    }

                    $("##companyField").multiselect({
						header: "",
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							var companyOption = $("option[value='"+ui.value+"']",this);
							var firstName = "";
							var lastName = "";
							var memberNumber = "";
							var recordTypeCode = "";
							if(companyOption.length){
								firstName = companyOption.attr("firstName");
								lastName = companyOption.attr("lastName");
								memberNumber = companyOption.attr("memberNumber");
								recordTypeCode = companyOption.attr("recordTypeCode");
							}
							changeCompany(ui.value,ui.text,firstName,lastName,memberNumber,recordTypeCode);
						}
					}).multiselectfilter();
                    
                    <cfif NOT application.mcCacheManager.sessionValueExists('captchaEntered') >
						showCaptcha();
					</cfif>
                    var orgMemberID = $("##orgMemberID").val();
					var firstName = $("##orgFirstName").val();
					var lastName = $("##orgLastName").val();
					var memberNumber = $("##orgMemberNumber").val();
					var orgCompanyName = $("##orgCompanyName").val();					
					var recordTypeCode = $("##recordTypeCode").val();					
					if(parseInt(orgMemberID) == 0){
						orgMemberID = "Please Select";
						firstName = "";
						lastName = "";
						memberNumber = "";
						orgCompanyName = "";
						recordTypeCode = "";
					}
					changeCompany(orgMemberID,orgCompanyName,firstName,lastName,memberNumber,recordTypeCode);

					$("##newCompany").click(function(){
                        $('##m_company').val('');
                        $('##m_company').focus();
                        $(".organizationsFirmsHolder").hide();
						changeCompany(0,'','','','');
					});

					if($("##m_company").length && $("##m_company").val().length){
						$("##m_company").trigger('change');
					}	

                    $(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
                        },
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					})
                   
                    var contactTypeField = $('##'+"#local.contactTypeField.fieldCode#");	
					$(contactTypeField).change(adjustFieldsetDisplay);				
					$(contactTypeField).trigger('change');
				});
            </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">
            <cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif application.mcCacheManager.sessionValueExists('captchaEntered') > onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
                    <cfif local.qryLinkedParentCompany.recordCount>
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="#local.qryLinkedParentCompany.memberID#">
						<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="#local.qryLinkedParentCompany.company#">
						<input type="hidden" name="orgFirstName" id="orgFirstName" value="#local.qryLinkedParentCompany.firstName#">
						<input type="hidden" name="orgLastName" id="orgLastName" value="#local.qryLinkedParentCompany.lastName#">
						<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="#local.qryLinkedParentCompany.memberNumber#">
						<input type="hidden" name="recordTypeCode" id="recordTypeCode" value="#local.qryLinkedParentCompany.recordTypeCode#">
					<cfelse>
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">
						<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="">
						<input type="hidden" name="orgFirstName" id="orgFirstName" value="">
						<input type="hidden" name="orgLastName" id="orgLastName" value="">
						<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="">
						<input type="hidden" name="recordTypeCode" id="recordTypeCode" value="">
					</cfif>
                    <cfif NOT variables.isLoggedIn AND application.objUser.getIdentifiedMemberID(cfcuser=variables.cfcuser, orgID=variables.orgID) GT 0>
                        <input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
                    </cfif>
                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                            </div>
                        </div>
                    </cfif>			
                    <div class="r i frmText">*Denotes required field</div>
					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>

						<span class="applicationCredentialingFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.appCredentialingFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.appCredentialingFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
                        <span class="sustainingMemberUpgradeFieldSetHolder"></span>                       

                        <span class="professionalLicensesHolder hide">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">Professional License Information</div>
								<div class="CPSectionContainer fieldSetContainer">									
									<table cellpadding="3" border="0" cellspacing="0" >	
                                        <tr class="top">
                                            <th class="tsAppBodyText" colspan="3" align="left">
                                                &nbsp;
                                            </th>
                                        </tr>									
										<tr align="top">
											<td class="tsAppBodyText" width="10">*&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Professional License</td>
											<td class="tsAppBodyText">
												<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
													<cfloop query="local.qryOrgPlTypes">	
														<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>			
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="top">
											<td class="tsAppBodyText" width="10"></td>
											<td class="tsAppBodyText"></td>
											<td class="tsAppBodyText"></td>
										</tr>
									</table>
									<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
										<tr>
											<td>
												<div class="row-fluid hide" id="state_table">
													<div class="span3 proLicenseLabel">
														<b>State Name</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
													</div>
												</div>
												<span id="selectedLicense">
												</span>
											</td>
										</tr>					
									</table>
								</div>
							</div>
						</span>		

                        <span class="paraAsstntCertificationFieldSetHolder"></span>
                        <span class="legalAssistantStudentFieldSetHolder"></span>

                        <span class="personalInformationFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.personalInformationFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.personalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

                        <span class="organizationsFirmsHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">Company/Firm Information</div>
								<div class="CPSectionContainer fieldSetContainer">	
									<p>#variables.strPageFields.CompanyFirmSelector#</p>								
									<table cellpadding="3" border="0" cellspacing="0" >									
										<tr align="top">
											<td class="tsAppBodyText" width="10">&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Firm Lookup</td>
											<td class="tsAppBodyText">											
												<select name="companyField" class="companyField" id="companyField">
													<option "">Please Select</option>
													<cfloop query="local.qryCompanyByGroup">
														<option recordTypeCode="#local.qryCompanyByGroup.recordTypeCode#" value="#local.qryCompanyByGroup.memberID#" firstName="#local.qryCompanyByGroup.firstName#" lastname="#local.qryCompanyByGroup.lastName#" memberNumber="#local.qryCompanyByGroup.memberNumber#"  <cfif local.qryLinkedParentCompany.recordCount and local.qryLinkedParentCompany.company EQ local.qryCompanyByGroup.company>Selected</cfif>>#local.qryCompanyByGroup.company#</option>
													</cfloop>
												</select>
												<a href="javascript:void(0)" id="newCompany">New Company/Firm</a>
											</td>
										</tr>
									</table>									
								</div>
							</div>
						</span>	

						<span class="organizationFirmFieldSetHolder"></span>
						<span class="addressFieldSetHolder addressHolder fieldSetHolder"></span>

                        <span class="homeAddressFieldSetHolder addressHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.homeAddressFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.homeAddressFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

                        <span class="addressPreferencesFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.addressPreferencesFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.addressPreferencesFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

                        <span class="professionalInformationFieldSetHolder"></span>

                        <span class="demographicInformationFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.demographicInformationFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.demographicInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
                        
                        <span class="optionalDirectoryListingsFieldSetHolder"> </span>
						<span class="communicationPreferencesFieldSetHolder"> </span>
						<span class="NCMockTrialFieldSetHolder"> </span>

                        <span class="practiceCodeOptionsFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.practiceCodeOptionsFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.practiceCodeOptionsFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>
                <span class="sustainingMemberUpgradeFieldSetWrapper hide">
                    <div class="CPSection row-fluid">
                        <div class="CPSectionTitle">#local.sustainingMemberUpgradeFieldSet.fieldSetTitle#</div>								
                        <div class="CPSectionContainer">										
                            #local.sustainingMemberUpgradeFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
                <span class="organizationFirmFieldSetWrapper hide">
                    <div class="CPSection row-fluid">
						<div class="CPSectionTitle">#local.contactInformationFieldSet.fieldSetTitle#</div>								
						<div class="CPSectionContainer">										
							#local.contactInformationFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
				<span class="addressFieldSetWrapper hide">
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle">#local.addressFieldSet.fieldSetTitle#</div>								
						<div class="CPSectionContainer">										
							#local.addressFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>
                <span class="paraAsstntCertificationFieldSetWrapper hide">
                    <div class="CPSection row-fluid">
                        <div class="CPSectionTitle">#local.paraAsstntCertificationFieldSet.fieldSetTitle#</div>								
                        <div class="CPSectionContainer">										
                            #local.paraAsstntCertificationFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
                <span class="legalAssistantStudentFieldSetWrapper hide">
                    <div class="CPSection row-fluid">
                        <div class="CPSectionTitle">#local.legalAssistantStudentFieldSet.fieldSetTitle#</div>								
                        <div class="CPSectionContainer">										
                            #local.legalAssistantStudentFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
                <span class="professionalInformationFieldSetWrapper hide">
                    <div class="CPSection row-fluid">
                        <div class="CPSectionTitle">#local.professionalInformationFieldSet.fieldSetTitle#</div>								
                        <div class="CPSectionContainer">										
                            #local.professionalInformationFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
                <span class="optionalDirectoryListingsFieldSetWrapper hide">
                    <div class="CPSection row-fluid">
                        <div class="CPSectionTitle">#local.optionalDirectoryListingsFieldSet.fieldSetTitle#</div>								
                        <div class="CPSectionContainer">										
                            #local.optionalDirectoryListingsFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
				<span class="communicationPreferencesFieldSetWrapper hide">
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle">#local.communicationPreferencesFieldSet.fieldSetTitle#</div>								
						<div class="CPSectionContainer">										
							#local.communicationPreferencesFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>				
				<span class="NCMockTrialFieldSetWrapper hide">
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle">#local.NCMockTrialFieldSet.fieldSetTitle#</div>								
						<div class="CPSectionContainer">										
							#local.NCMockTrialFieldSet.fieldSetContent#									
						</div>
					</div>
				</span>

				#application.objWebEditor.showEditorHeadScripts()#

				<script language="javascript">											
						<cfloop query="local.qryOrgAddressTypes">                       
							<cfif ListFindNoCase('Office Mailing,Office Physical,Home',local.qryOrgAddressTypes.addressType)>
								function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _CF_this = document.forms['#variables.formName#'];
									_this = _this || {};
									var _address = _this.val();
									if(_address.length > 0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}
										var memType = $.trim($(_CF_this['#local.contactTypeField.fieldCode#']).find('option:selected').text());
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
									}
								}

								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
								
								$(document).on('change','.addressHolder ##ma_#local.qryOrgAddressTypes.addresstypeid#_address1',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
							<cfelse>
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();								
							</cfif>
							
						</cfloop>

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>	

        <cfset local.isBot = 0>
        <cfif structKeyExists(arguments.rc, 'iAgree')>
            <cfset local.isBot = 1>
        </cfif>
        <cfif (local.isBot) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.response = "failure">

		<cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step1")>
            <cfset structDelete(variables.formFields, "step1")>
        </cfif>

		<cfset variables.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>	
		<cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>
		
		<cfset local.membershipCatFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
        <cfset local.membershipCatFieldCode = "md_" & local.membershipCatFieldInfo.COLUMNID/>
		<cfset local.membershipCatSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Membership Category',valueIDList=local.strData.one["#local.membershipCatFieldCode#"])>

		<cfset local.contactTypeValue = "">
		<cfif local.membershipCatSelected EQ "Attorney">
			<cfset local.contactTypeValue = "Attorney">
        <cfelseif local.membershipCatSelected EQ "IDS Attorney">
			<cfset local.contactTypeValue = "IDS,Attorney">
		<cfelseif local.membershipCatSelected EQ "IDS Paralegal">
			<cfset local.contactTypeValue = "IDS,Paralegal/Legal Assistant">
		<cfelseif local.membershipCatSelected EQ "LANC Attorney">
			<cfset local.contactTypeValue = "LANC,Attorney">
		<cfelseif local.membershipCatSelected EQ "LANC Paralegal">
			<cfset local.contactTypeValue = "LANC,Paralegal/Legal Assistant">
        <cfelseif local.membershipCatSelected EQ "Law School Student">
			<cfset local.contactTypeValue = "Law School Student">
        <cfelseif local.membershipCatSelected EQ "Paralegal">
			<cfset local.contactTypeValue = "Paralegal/Legal Assistant"> 
        <cfelseif local.membershipCatSelected EQ "Paralegal Student">
			<cfset local.contactTypeValue = "Paralegal Student"> 
        <cfelseif local.membershipCatSelected EQ "Public Service Attorney">
			<cfset local.contactTypeValue = "Attorney,Public Service">   
        <cfelseif local.membershipCatSelected EQ "Retired Attorney">
			<cfset local.contactTypeValue = "Attorney,Retired">            
		</cfif>
		
		<cfif variables.isLoggedIn OR variables.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
            <cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
            <cfset local.strData.one.memberID = 0>         
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>
		
		<cfif local.strResult.success and len(trim(local.contactTypeValue))>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
			<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>			
			<cfset local.strResult1 = local.objSaveMember.saveData(runImmediately=1)>
		</cfif>

		<cfset variables.formFields.step0.origMemberID = variables.useMID/>
		<cfset variables.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>	
			<cfset variables.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
												subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
												enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
			<cfset application.mcCacheManager.sessionSetValue("captchaEntered", 1)>
			<cfset local.response = "success">
		</cfif>	
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>        
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset variables.useMID = local.strData.zero.memberID/>

        <cfif StructIsEmpty(local.strData.one)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>
		
		<cfset local.thisAddonSubscriptionID = "">
		<cfset local.thisAddonSubscriptionIDArray = []>
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
			<cfset arrayAppend(local.thisAddonSubscriptionIDArray, local.thisAddonSubscriptionID)>
		</cfloop>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.newLawyersDivisionSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.newLawyersDivisionSubscription)>       
        
        <cfset local.qryFirstAttorneyRates = CreateObject("component","model.admin.subscriptions.subscriptionReg").getRates(siteID=variables.siteID, memberID=variables.useMID, subscriptionID=local.newLawyersDivisionSubscriptionID, isRenewal=false,
				rateUID='#variables.firstAttorneyRateUID#', overridePerms=false)>        
        
        <cfset local.qryEligibleForNewLawyersDivisionJoin = application.objCustomPageUtils.mem_getGroups(memberID=variables.useMID,orgID=variables.orgID,UID='********-3795-4984-825D-8FC464528D46')/>

        <cfset local.membershipCatFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
        <cfset local.membershipCatFieldCode = "md_" & local.membershipCatFieldInfo.COLUMNID/>
		<cfset local.membershipCatSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Membership Category',valueIDList=local.strData.one["#local.membershipCatFieldCode#"])>
        <cfset local.disableNewLawyer = false>
        <cfif local.membershipCatSelected EQ 'Attorney' AND local.qryFirstAttorneyRates.recordCount AND local.qryEligibleForNewLawyersDivisionJoin.recordCount>
            <cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
                <cfloop list="#local.strData.one.mpl_pltypeid#" index="local.pltypeid">
                    <cfif DateCompare(now(),DateAdd("yyyy",10,local.strData.one['mpl_' & '#local.pltypeid#' & '_activeDate'])) NEQ 1 AND local.disableNewLawyer EQ false>
                        <cfset local.disableNewLawyer = true>
                    </cfif>
                </cfloop>            
            </cfif>
        </cfif>        

        <cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
                                subscriptionID = local.subscriptionID,
                                memberID = variables.useMID,
                                isRenewalRate = false,
                                siteID = variables.siteID, 
                                strData=local.strData.two
                                )>	        

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style>
                    input.subRateOverrideBox{margin-bottom:0px;}
                </style>
                <script type="text/javascript">	
                    function afterShowMembershipInfo(){
						$('html, body').animate({ scrollTop: 300 }, 500);
					}
                    #local.result.jsAddonValidation#
                    function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterShowMembershipInfo);
							return false;
						}
                        <cfif local.disableNewLawyer>
                        if($("[data-rfid='#local.qryFirstAttorneyRates.rfid#']").parent().parent().find('.subCheckbox').length){
                            $("[data-setuid='#variables.newLawysersDivisionSetUID#'] input:checkbox").removeAttr("disabled");
                        }	
                        </cfif>	
						mc_continueForm($('###variables.formName#'),afterShowMembershipInfo);
						return false;
					}
						
					$(document).ready(function(){
                        $('input[name^=sub#local.subscriptionID#_rateOverride]').attr('disabled',true);
                        $('input[name^=sub#local.subscriptionID#_rateOverride]').attr('readonly','readonly');
                        
                        if($("[name='sub#local.subscriptionID#_rate']").length){                        
                            $("[name='sub#local.subscriptionID#_rate']").change(function(){
                                if($(this).prop("checked")){
                                    <cfif local.disableNewLawyer>
                                        if($(this).data('rfid') == '#local.qryFirstAttorneyRates.rfid#'){
                                            if($("[data-setuid='#variables.newLawysersDivisionSetUID#'] input:checkbox").prop("checked") == true){
                                                $("[data-setuid='#variables.newLawysersDivisionSetUID#'] input:checkbox").attr('checked',true).attr("disabled", true);
                                            }
                                        }
                                    </cfif>
                                    $('input[name^=sub#local.subscriptionID#_rateOverride]').attr('disabled',true);
                                    $('input[name^=sub#local.subscriptionID#_rateOverride]').attr('readonly','readonly');
                                    $(this).next().children('.subRateOverrideBox').removeAttr('disabled');
                                    $(this).next().children('.subRateOverrideBox').removeAttr('readonly');
                                }
                            });
                        }
                        <cfif local.disableNewLawyer>
                            if($("[data-rfid='#local.qryFirstAttorneyRates.rfid#']").parent().parent().find('.subCheckbox').length){
                                $("[data-setuid='#variables.newLawysersDivisionSetUID#'] input:checkbox").attr('checked',true).attr("disabled", true);
                            }
                        </cfif>

                        $("[name='sub#local.subscriptionID#_rate']").each(function(){
                            if($(this).prop("checked")){
                                $(this).next().children('.subRateOverrideBox').removeAttr('disabled');
                                $(this).next().children('.subRateOverrideBox').removeAttr('readonly');
                            };
                        });
					});
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                            </div>
                        </div>
                    </cfif>		
                    <div class="r i frmText">*Denotes required field</div>
					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid" id="memberShipDiv">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>

					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step2")>
            <cfset structDelete(variables.formFields, "step2")>
        </cfif>			
        <cfset variables.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showCertificationStatements" access="private" output="false" returntype="string">
        <cfset var local = structNew()>
        
        <cfset local.doNotIncludeList = "fa">
        <cfset local.strData = {}>
        <cfset local.memberFieldData = structNew()>
        <cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>	
        
        <cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        
        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

        <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#variables.productLiabilityCertUID#', usage='CustomPage')>
        <cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
        <cfset StructAppend(local.strData.three,local.memberFieldData)/>

        <cfset local.productLiabilityyCertFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.productLiabilityCertUID, mode="collection", strData=local.strData.three)>
        <cfset local.selectedSubs = ''>
        <cfset local.subscriptionAddonsSelected = ''>
        <cfloop collection="#local.strData.two#" item="local.key" >
            <cfif FindNoCase("rateFrequencySelected",local.key)>
                <cfset local.subId = replace(replace(local.key,"sub",''),"_rateFrequencySelected",'')>
                <cfif structKeyExists(local.strData.two,"sub#local.subId#")>
                <cfset local.selectedSubs = ListAppend(local.selectedSubs,replace(replace(local.key,"sub",''),"_rateFrequencySelected",''))>
                </cfif>
            </cfif>
        </cfloop>

        <cfif len(local.selectedSubs)>
            <cfquery name="local.qryGetAddonsSelected" datasource="#application.dsn.membercentral.dsn#">
                SELECT subscriptionName FROM dbo.sub_subscriptions WHERE subscriptionID in (#local.selectedSubs#) and orgID = #variables.orgID#
            </cfquery>
            <cfset local.subscriptionAddonsSelected = valueList(local.qryGetAddonsSelected.subscriptionName)>
        </cfif>

        <cfset local.membershipCatFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
        <cfset local.membershipCatFieldCode = "md_" & local.membershipCatFieldInfo.COLUMNID/>
		<cfset local.membershipCatSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Membership Category',valueIDList=local.strData.one["#local.membershipCatFieldCode#"])>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style type="text/css">		
                    .certificationStatements .P,.certificationStatements .tsAppBodyText{color:black!important;padding: 5px!important;vertical-align: middle!important;}
                    .frmText label{font-size:9pt;}
                </style>
                <script type="text/javascript">	
                    function afterShowCertificationStatements(){
                        $('html, body').animate({ scrollTop: 300 }, 500);
                    }
                    function validateCertificationStatementsForm(){
                        var thisForm = document.forms["#variables.formName#"];
                        var arrReq = new Array();  
                        #local.productLiabilityyCertFieldSet.jsValidation#
                        if($("input[type='checkbox'][value='I Agree']:checked").length != $("input[type='checkbox'][value='I Agree']").length){
                            arrReq[arrReq.length] = "Please agree to all of the following.";
                        }
                        if (arrReq.length == 0){
                            if( "#local.membershipCatSelected#" == "Paralegal"){
                                if (!_FB_hasValue(thisForm['ncajMemberName'], 'TEXT')) arrReq[arrReq.length] = 'Please enter Paralegal Member Sponsor';
                            }
                        }
                        if (arrReq.length > 0) {
                            var msg = '';
                            for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                            showAlert(msg,afterShowCertificationStatements);
                            return false;
                        }		
                        mc_continueForm($('###variables.formName#'),afterShowCertificationStatements);
                        return false;
                    }
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <div id="customPage" class="customPage">
                    <cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                            </div>
                        </div>
                    </cfif>			
                    <div class="r i frmText">*Denotes required field</div>
                    <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateCertificationStatementsForm()">
                    <cfinput type="hidden" name="fa" id="fa" value="processCertificationStatements">
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
                        <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

                        <div class="CPSection certificationStatements">
                            <div class="CPSectionTitle"><span class="red">*</span> Certification Statements</div>
                            <div class="subCPSectionArea1">
                                <div class="subCPSectionText">Please agree to all of the following.</div>                            
                            </div>
                            
                            <cfif local.membershipCatSelected EQ "Paralegal" OR local.membershipCatSelected EQ "LANC Paralegal" OR local.membershipCatSelected EQ "IDS Paralegal">
                                <div class="P frmRow1 frmText" id="legalAsstCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.LegalAssistantCertificationTitle#</span><br /><br />
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="legalAsstCertification" <cfif StructKeyExists(local.strData.three,"legalAsstCertification") AND local.strData.three.legalAsstCertification EQ "I Agree">checked</cfif>><i>#variables.strPageFields.LegalAssistantCertification#</i></label>
                                    <i>I (Attorney Member Name) <input type="text" name="ncajMemberName" value="<cfif StructKeyExists(local.strData.three,"ncajMemberName")>#local.strData.three.ncajMemberName#</cfif>" size="30"> verify the applicant is employed by my firm as a paralegal.</i>
                                </div>
                            </cfif>
                             <cfif local.subscriptionAddonsSelected contains "Auto Torts and Premises Liability Section">
                                <div class="P frmRow1 frmText" id="autoTortsCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.AutoTortsSectionCertificationTitle#</span><br /><br />
                                    #variables.strPageFields.AutoTortsSectionCertification#<br />
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="autoTortsCertification" <cfif StructKeyExists(local.strData.three,"autoTortsCertification") AND local.strData.three.autoTortsCertification EQ "I Agree">checked</cfif>>&nbsp;<b>Yes, I certify that I meet the following membership criteria of the Auto Torts Section</b></label>
                                    <label class="checkbox"><input type="checkbox" value="Yes" name="autoTortsList" <cfif StructKeyExists(local.strData.three,"autoTortsList") AND local.strData.three.autoTortsList EQ "Yes">checked</cfif>>&nbsp;<b>Yes, I want to participate in the Auto Torts Section Listserv.</b></label>
                                </div>
                            </cfif>
                            <cfif local.subscriptionAddonsSelected contains "Products Liability, Class Actions & Mass Torts Section">
                                <div class="P frmRow1 frmText" id="productLiabilityCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.productsLiabilityCertificationTitle#</span><br /><br />
                                    #variables.strPageFields.productsLiabilityCertificationContent#
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="productLiabilityCertification" <cfif StructKeyExists(local.strData.three,"productLiabilityCertification") AND local.strData.three.productLiabilityCertification EQ "I Agree">checked</cfif>>&nbsp;<b>Yes, I certify that I meet the following membership criteria of the Products Liability, Class Action & Mass Torts Section</b></label>
                                    <label class="checkbox"><input type="checkbox" value="Yes" name="productLiabilityList" <cfif StructKeyExists(local.strData.three,"productLiabilityList") AND local.strData.three.productLiabilityList EQ "Yes">checked</cfif>>&nbsp;<b>Yes I want to participate in the Products Liability, Class Action & Mass Torts Section Listserv.</b></label>
                                    
                                </div>
                            </cfif>
                            <cfif local.subscriptionAddonsSelected contains "Employment Law Section">
                                <div class="P frmRow1 frmText" id="employmentLawCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.EmploymentLawSectionCertificationTitle#</span><br /><br />
                                    #variables.strPageFields.EmploymentLawSectionCertification#<br />		
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="employmentLawCertification" <cfif StructKeyExists(local.strData.three,"employmentLawCertification") AND local.strData.three.employmentLawCertification EQ "I Agree">checked</cfif>>&nbsp;<b>Yes, I certify that I meet the following membership criteria of the Employment Law Section</b></label>						
                                    <label class="checkbox"><input type="checkbox" value="Yes" name="employmentLawList" <cfif StructKeyExists(local.strData.three,"employmentLawList") AND local.strData.three.employmentLawList EQ "Yes">checked</cfif>>&nbsp;<b>Yes, I want to participate in the Employment Law Section Listserv.</b></label>
                                </div>
                            </cfif>
                            <cfif local.subscriptionAddonsSelected contains "Nursing Home Litigation Section">
                                <div class="P frmRow1 frmText" id="nursingHomeCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.NursingHomeLitigationSectionCertificationTitle#</span><br /><br />
                                    #variables.strPageFields.NursingHomeLitigationSectionCertification#	<br />
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="nursingHomeCertification" <cfif StructKeyExists(local.strData.three,"nursingHomeCertification") AND local.strData.three.nursingHomeCertification EQ "I Agree">checked</cfif>>&nbsp;<b>Yes, I certify that I meet the following membership criteria of the Nursing Home Litigation Section</b></label>
                                    <label class="checkbox"><input type="checkbox" value="Yes" name="nursingHomeList" <cfif StructKeyExists(local.strData.three,"nursingHomeList") AND local.strData.three.nursingHomeList EQ "Yes">checked</cfif>>&nbsp;<b>Yes, I want to participate in the Nursing Home Litigation Section Listserv.</b></label>
                                </div>
                            </cfif>
                            <cfif local.subscriptionAddonsSelected contains "Professional Negligence Section">               
                                <div class="P frmRow1 frmText" id="profNegCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.ProfessionalNegligenceSectionCertificationTitle#</span><br /><br />
                                    #variables.strPageFields.ProfessionalNegligenceSectionCertification#<br />
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="profNegCertification" <cfif StructKeyExists(local.strData.three,"profNegCertification") AND local.strData.three.profNegCertification EQ "I Agree">checked</cfif>>&nbsp;<b>Yes, I certify that I meet the following membership criteria of the Professional Negligence Section</b></label>
                                    <label class="checkbox"><input type="checkbox" value="Yes" name="profNegList" <cfif StructKeyExists(local.strData.three,"profNegList") AND local.strData.three.profNegList EQ "Yes">checked</cfif>>&nbsp;<b>Yes, I want to participate in the Professional Negligence Section Listserv.</b></label>
                                </div>
                            </cfif>
                            <cfif local.subscriptionAddonsSelected contains "Workers' Compensation Section">
                                <div class="P frmRow1 frmText" id="workersCompCertDiv">
                                    <span class="subCPSectionTitle">#variables.strPageFields.WorkersCompensationSectionCertificationTitle#</span><br /><br />
                                    #variables.strPageFields.WorkersCompensationSectionCertification#<br />
                                    <label class="checkbox"><input type="checkbox" value="I Agree" name="workersCompCertification" <cfif StructKeyExists(local.strData.three,"workersCompCertification") AND local.strData.three.workersCompCertification EQ "I Agree">checked</cfif>>&nbsp;<b>Yes, I certify that I meet the following membership criteria of the Workers' Compensation Section</b></label>
                                    <label class="checkbox"><input type="checkbox" value="Yes" name="workersCompList" <cfif StructKeyExists(local.strData.three,"workersCompList") AND local.strData.three.workersCompList EQ "Yes">checked</cfif>>&nbsp;<b>Yes, I want to participate in the Workers' Compensation Section Listserv.</b></label>
                                </div>
                            </cfif>
                                
                            <div class="P frmRow1 frmText" id="membershipCertDiv">
                                #variables.strPageFields.NCAJAgreement#	
                                <label class="checkbox"><input type="checkbox" value="I Agree" name="membershipCertification" <cfif StructKeyExists(local.strData.three,"membershipCertification") AND local.strData.three.membershipCertification EQ "I Agree">checked</cfif>>&nbsp;<b>I Agree</b></label>
                            </div>
                            <div class="P frmRow1 frmText" id="membershipCertDiv">
                                #variables.strPageFields.NCAJDuesMessage#
                                <br />
                                <br />
                            </div>	
                        </div>
                        <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                        <button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
                    </cfform>
                </div>
            </cfoutput>
        </cfsavecontent>

        <cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="processCertificationStatements" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step3")>
            <cfset structDelete(variables.formFields, "step3")>
        </cfif>		        
        <cfset variables.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
		<cfset local.strData.three = checkSessionExist("step3")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
        <cfset var local = structNew()>

        <cfset local.doNotIncludeList = "fa">
        <cfset local.strData = {}>        
        <cfset local.strData.two = checkSessionExist("step2")/>

        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
                siteID=variables.siteID,
                uid = variables.strPageFields.MainSubscription)>
        <cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
                subscriptionID = local.subscriptionID,
                memberID = variables.useMID,
                isRenewalRate = false,
                siteID = variables.siteID,
                strdata = local.strData.two)/>

        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

        <cfif local.paymentRequired>
            <cfset local.arrPayMethods = []>
            <cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
                <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
            </cfif>
            <cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
                <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
            </cfif>
            <cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
                <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
            </cfif>

            <cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
                                        arrPayMethods=local.arrPayMethods, 
                                        siteID=variables.siteID, 
                                        memberID=variables.useMID, 
                                        title="Choose Your Payment Method", 
                                        formName=variables.formName, 
                                        backStep="showCertificationStatements"
                                    )>
        </cfif>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <cfif local.paymentRequired>
                    #local.strReturn.headcode#
                </cfif>
                <script type="text/javascript">	
                    function afterShowPayment(){
                        $('html, body').animate({ scrollTop: 300 }, 500);
                    }
                    function validatePaymentForm(isPaymentRequired) {
                        if(isPaymentRequired == 'YES'){
                            var arrReq = mccf_validatePPForm();
                            if (arrReq.length > 0) {
                                var msg = '';
                                for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                                showAlert(msg,afterShowPayment);
                                return false;
                            }
                        }
                        mc_continueForm($('###variables.formName#'),afterShowPayment);
                        return false;
                    }
                    $('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
                        var iframeThis = this;

                        $(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
                            setTimeout(function(){ 
                                if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
                                    $('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
                                }	
                            }, 100);
                        });
                        $(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
                            $('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
                        });
                    });
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
                    <cfinput type="hidden" name="fa" id="fa" value="processPayment">
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
                    <cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                            </div>
                        </div>
                    </cfif>		
                    <div class="r i frmText">*Denotes required field</div>
                    <cfif len(variables.strPageFields.Step3TopContent)>
                        <div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step4TopContent#</div></div>
                    </cfif>

                    <div class="tsAppSectionHeading">Membership Selections Confirmation</div>
                    <div class="tsAppSectionContentContainer">						
                        #local.strResult.formContent#
                    </div>
                    <br/>

                    <div class="tsAppSectionHeading">Total Price</div>
                    <div class="tsAppSectionContentContainer">						
                        Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
                    </div>

                    <br/><br/>

                    <cfif local.paymentRequired>
                        #local.strReturn.paymentHTML#
                    <cfelse>
                        <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                        <button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showCertificationStatements');">&lt;&lt; Back</button>
                    </cfif>

                </cfform>
            </cfoutput>
        </cfsavecontent>

        <cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="processPayment" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step4")>
            <cfset structDelete(variables.formFields, "step4")>
        </cfif>			
        <cfset variables.formFields.step4 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

        <cfset local.strData.four = checkSessionExist("step4")/>
        <cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.productLiabilityCertSelected = 0>
        <cfset local.productLiabilityCertFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Products Liability Class Action & Mass Torts Section Certification')>
        <cfset local.productLiabilityCertFieldCode = "md_" & local.productLiabilityCertFieldInfo.COLUMNID/>
        <cfif structKeyExists(local.strData.three, "#local.productLiabilityCertFieldCode#")>
            <cfset local.productLiabilityCertSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Products Liability Class Action & Mass Torts Section Certification',valueIDList=local.strData.three["#local.productLiabilityCertFieldCode#"])>
        </cfif>
		
        <cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three) OR StructIsEmpty(local.strData.four)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>
		
        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>

        <cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>

		<cfset local.strData.one.memberID = variables.useMID>
        <cfset local.strData.three.memberID = variables.useMID>
        <cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
        <cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
        <cfset local.strData.four.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

        <cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)> 
        <cfset local.membershipCatFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>
        <cfset local.membershipCatFieldCode = "md_" & local.membershipCatFieldInfo.COLUMNID/>
		<cfset local.membershipCatSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Membership Category',valueIDList=local.strData.one["#local.membershipCatFieldCode#"])>

        <cfif val(local.strData.one["orgMemberID"]) NEQ 0>
			<cfset local.objAdminMember = CreateObject("component","model.admin.members.members") />
			<cfset local.availableRecordRelationships = local.objAdminMember.getAvailableRecordRelationships(orgID=variables.orgID, masterMemberID=val(local.strData.one["orgMemberID"]), childMemberID=variables.useMID) />

            <cfset local.relationShipCode = "">
            <cfswitch expression="#local.membershipCatSelected#">                
                <cfcase value='Attorney'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'LawFirm'>
                        <cfset local.relationShipCode = "Attorney">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'Government'>
                        <cfset local.relationShipCode = "GovernmentAttorney">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'School'>
                        <cfset local.relationShipCode = "SchoolFaculty">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyAttorney">
                    </cfif>
                </cfcase>
                <cfcase value='LANC Attorney'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyAttorney">
                    </cfif>
                </cfcase>
				 <cfcase value='IDS Attorney'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyAttorney">
					<cfelseif  local.strData.one["recordTypeCode"] eq 'Government'>
                        <cfset local.relationShipCode = "GovernmentAttorney">
                    </cfif>
                </cfcase>
                <cfcase value='Law School Student'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'LawFirm'>
                        <cfset local.relationShipCode = "LawStudent">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'School'>
                        <cfset local.relationShipCode = "LawStudent">
                    </cfif>
                </cfcase>
                <cfcase value='Paralegal Student'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'LawFirm'>
                        <cfset local.relationShipCode = "Paralegal">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyParalegal">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'Government'>
                        <cfset local.relationShipCode = "GovernmentLegalStaff">
                    </cfif>
                </cfcase>
                <cfcase value='Paralegal'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'LawFirm'>
                        <cfset local.relationShipCode = "Paralegal">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyParalegal">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'Government'>
                        <cfset local.relationShipCode = "GovernmentLegalStaff">
                    </cfif>
                </cfcase>
				<cfcase value='LANC Paralegal'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyParalegal">
                    </cfif>
                </cfcase>
				<cfcase value='IDS Paralegal'>
                    <cfif local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyParalegal">
					<cfelseif  local.strData.one["recordTypeCode"] eq 'Government'>
                        <cfset local.relationShipCode = "GovernmentLegalStaff">
                    </cfif>
                </cfcase>
                <cfcase value='Public Service Attorney'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'PublicAgency'>
                        <cfset local.relationShipCode = "PublicAgencyAttorney">
                    </cfif>
                </cfcase>
                <cfcase value='Retired Attorney'>
                    <cfif  local.strData.one["recordTypeCode"] eq 'LawFirm'>
                        <cfset local.relationShipCode = "Attorney">
                    <cfelseif  local.strData.one["recordTypeCode"] eq 'School'>
                        <cfset local.relationShipCode = "SchoolFaculty">
                    </cfif>
                </cfcase>
            </cfswitch>           
           
            <cfif len(local.relationShipCode)>
                <cfquery dbtype="query" name="local.qryStaffRecordRelationship">
                    select recordTypeRelationshipTypeID, relationshipTypeName
                    from [local].availableRecordRelationships.qryRecordRelationshipTypes
                    where relationshipTypeCode = '#local.relationShipCode#' and recordTypeCode = '#local.strData.one["recordTypeCode"]#'
                </cfquery>
                <cfif len(local.qryStaffRecordRelationship)>
                    <cfset local.objAdminMember.addRecordRelationship(mcproxy_orgID=variables.orgID, mcproxy_siteID=variables.siteID, masterMemberID=val(local.strData.one["orgMemberID"]), childMemberID=variables.useMID, recordTypeRelationshipTypeID=local.qryStaffRecordRelationship.recordTypeRelationshipTypeID, isActive=1)>
                </cfif>
            </cfif>
        </cfif>
       
       
       
        <cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID)>
        <cfif structKeyExists(local.strData.four, 'mccf_payMethID') and structKeyExists(local.strData.four, 'p_#local.strData.four.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.four['p_#local.strData.four.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>
        <!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
        <cfif local.strResult.totalFullPrice gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.four.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

        <cfif local.strResult.totalFullPrice gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#",local.strData.four.mccf_payMeth)>
			<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
		</cfif>

        <cfset local.response = "success">

        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

        <cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.four,"p_#local.strData.four.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.four["p_#local.strData.four.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.four.mccf_payMethID).detail>
			</cfif>
		</cfif>

		
        <cfset local.appCredentialingFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.appCredentialingFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.paraAsstntCertificationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.paraAsstntCertificationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.legalAssistantStudentFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.legalAssistantStudentFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.professionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.demographicInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demographicInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.optionalDirectoryListingsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.optionalDirectoryListingsFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.communicationPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.communicationPreferencesFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.NCMockTrialFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.NCMockTrialFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.practiceCodeOptionsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.practiceCodeOptionsFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.productLiabilityyCertFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.productLiabilityCertUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.sustainingMemberUpgradeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.sustainingMemberUpgradeFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.addressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="confirmation", strData=local.strData.one)>
	
        <cfif StructKeyExists(local.strData.three,"productLiabilityCertification") AND local.strData.three.productLiabilityCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Products Liability Class Action & Mass Torts Section Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Products Liability Class Action & Mass Torts Section Certification', value=0)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"autoTortsCertification") AND local.strData.three.autoTortsCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Auto Torts and Premises Liability Section Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Auto Torts and Premises Liability Section Certification', value=0)>
        </cfif> 
        <cfif StructKeyExists(local.strData.three,"ncajMemberName") AND LEN(local.strData.three.ncajMemberName)>
            <cfset local.objSaveMember.setCustomField(field='Paralegal NCAJ Member Sponsor', value=local.strData.three.ncajMemberName)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"employmentLawCertification") AND local.strData.three.employmentLawCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Employment Law Section Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Employment Law Section Certification', value=0)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"nursingHomeCertification") AND local.strData.three.nursingHomeCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Nursing Home Litigation Section Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Nursing Home Litigation Section Certification', value=0)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"profNegCertification") AND local.strData.three.profNegCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Professional Negligence Section Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Professional Negligence Section Certification', value=0)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"workersCompCertification") AND local.strData.three.workersCompCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Workers Compensation Section Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Workers Compensation Section Certification', value=0)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"legalAsstCertification") AND local.strData.three.legalAsstCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='Legal Assistant Certification', value=1)>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='Legal Assistant Certification', value=0)>
        </cfif>
        <cfif StructKeyExists(local.strData.three,"membershipCertification") AND local.strData.three.membershipCertification EQ "I Agree">
            <cfset local.objSaveMember.setCustomField(field='NCAJ Member Certification', value="Yes")>
        <cfelse>
            <cfset local.objSaveMember.setCustomField(field='NCAJ Member Certification', value="")>
        </cfif>
        <cfset local.objSaveMember.saveData(runImmediately=1)> 
        <cfsavecontent variable="local.invoice">
            <cfoutput>              
                #local.appCredentialingFieldSet.fieldSetContent#
                <cfif ListFindNoCase("Attorney,Public Service Attorney",local.membershipCatSelected)>
                    #local.sustainingMemberUpgradeFieldSet.fieldSetContent#
                </cfif>
                
                <cfif local.membershipCatSelected EQ 'Paralegal' OR local.membershipCatSelected EQ 'LANC Paralegal' OR local.membershipCatSelected EQ 'IDS Paralegal'>
                    #local.paraAsstntCertificationFieldSet.fieldSetContent#
                </cfif>

                <cfif local.membershipCatSelected EQ 'Paralegal Student'>
                    #local.legalAssistantStudentFieldSet.fieldSetContent#
                </cfif>

                <cfif ListFindNoCase("Attorney,Public Service Attorney,Retired Attorney,LANC Attorney,IDS Attorney",local.membershipCatSelected)>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
                        </tr>				
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">						
                                    <tr valign="top">
                                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                            <cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
                                                <table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
                                                    <thead>
                                                        <tr valign="top">
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
                                                            <th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    <cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
                                                        <tr id="tr_state_#local.key#">
                                                            <td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
                                                            <td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
                                                            <td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
                                                            <td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
                                                        </tr>
                                                    </cfloop>
                                                    </tbody>
                                                </table>
                                            </cfif>
                                        </td>
                                    </tr>						
                                </table>
                            </td>
                        </tr>
                    </table>
                    <br>
                </cfif>

                #local.personalInformationFieldSet.fieldSetContent#
                <cfif val(local.strData.one["orgMemberID"]) GT 0>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                        <tr>
                            <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Linked Record</td>
                        </tr>				
                        <tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">
									<tbody>
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">#local.strData.one["orgLastName"]#, #local.strData.one["orgFirstName"]# (#local.strData.one["orgMemberNumber"]#) <br><br> #local.strData.one["orgCompanyName"]# &nbsp;</td>
									</tr>
									</tbody>
								</table>
							</td>
						</tr>
                    </table>
					<br>
				</cfif>	
                #local.contactInformationFieldSet.fieldSetContent#
				<cfif local.membershipCatSelected NEQ 'Law School Student'>
					#local.addressFieldSet.fieldSetContent#
				</cfif>
				#local.homeAddressFieldSet.fieldSetContent#						
				#local.addressPreferencesFieldSet.fieldSetContent#

                <cfif ListFindNoCase("Attorney,Public Service Attorney,Retired Attorney,Law School Student,LANC Attorney,IDS Attorney",local.membershipCatSelected)>
                    #local.professionalInformationFieldSet.fieldSetContent#
                </cfif>
                #local.demographicInformationFieldSet.fieldSetContent#
                <cfif ListFindNoCase("Attorney,Public Service Attorney,LANC Attorney,IDS Attorney",local.membershipCatSelected)>
                    #local.optionalDirectoryListingsFieldSet.fieldSetContent#
                </cfif>
				<cfif ListFindNoCase("Attorney,Public Service Attorney",local.membershipCatSelected)>
                #local.communicationPreferencesFieldSet.fieldSetContent#
				#local.NCMockTrialFieldSet.fieldSetContent#
				</cfif>
                #local.practiceCodeOptionsFieldSet.fieldSetContent#
                
                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tbody>
                        <tr><td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Certification Statements</td></tr>
                        <tr>
                            <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                                <table cellpadding="3" border="0" cellspacing="0">
                                    <tbody>
                                        <cfif StructKeyExists(local.strData.three,"legalAsstCertification") AND local.strData.three.legalAsstCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">#variables.strPageFields.LegalAssistantCertificationTitle#: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.legalAsstCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"ncajMemberName") AND len(local.strData.three.ncajMemberName)>
                                                <tr valign="top">
                                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Member Sponsor: &nbsp;</td>
                                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                        #local.strData.three.ncajMemberName#
                                                    </td>
                                                </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"autoTortsCertification") AND local.strData.three.autoTortsCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Auto Torts Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.autoTortsCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"autoTortsList") AND local.strData.three.autoTortsList EQ 'Yes'>
                                                <tr valign="top">
                                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Auto Torts Section Listserv: &nbsp;</td>
                                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                        #local.strData.three.autoTortsList#
                                                    </td>
                                                </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"productLiabilityCertification") AND local.strData.three.productLiabilityCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Product Liability Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.productLiabilityCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"productLiabilityList") AND local.strData.three.productLiabilityList EQ 'Yes'>
                                                <tr valign="top">
                                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Product Liability Section Listserv: &nbsp;</td>
                                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                        #local.strData.three.productLiabilityList#
                                                    </td>
                                                </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"employmentLawCertification") AND local.strData.three.employmentLawCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Employment Law Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.employmentLawCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"employmentLawList") AND local.strData.three.employmentLawList EQ 'Yes'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Employment Law Section Listserv: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.employmentLawList#
                                                </td>
                                            </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"nursingHomeCertification") AND local.strData.three.nursingHomeCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Nursing Home Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.nursingHomeCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"nursingHomeList") AND local.strData.three.nursingHomeList EQ 'Yes'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Nursing Home Section Listserv: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.nursingHomeList#
                                                </td>
                                            </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"profNegCertification") AND local.strData.three.profNegCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Professional Negligence Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.profNegCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"profNegList") AND local.strData.three.profNegList EQ 'Yes'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Professional Negligence Section Listserv: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.profNegList#
                                                </td>
                                            </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"workersCompCertification") AND local.strData.three.workersCompCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Workers Compensation Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.workersCompCertification#
                                                </td>
                                            </tr>
                                            <cfif StructKeyExists(local.strData.three,"workersCompList") AND local.strData.three.workersCompList EQ 'Yes'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">Workers Compensation Section Listserv: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.workersCompList#
                                                </td>
                                            </tr>
                                            </cfif>
                                        </cfif>
                                        <cfif StructKeyExists(local.strData.three,"membershipCertification") AND local.strData.three.membershipCertification EQ 'I Agree'>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap="">NCAJ Membership Certification: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
                                                    #local.strData.three.membershipCertification#
                                                </td>
                                            </tr>
                                        </cfif>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table><br>

                <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            #local.strResult.formContent#
                            <br/>
                        </div>
                        <br/>
                        <strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
                        <br/>
                        </td>
                    </tr>
                </table>

                <cfif local.paymentRequired>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(local.strData.four,"mccf_payMeth")>
                                <table cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #local.strData.four.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif>

            </cfoutput>
        </cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>

                <p>Here are the details of your application:</p>	

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

        <cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>

        <cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
        <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
        <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

        <cfsavecontent variable="local.specialText">
			<cfoutput>

            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>
                 #local.specialText#
                <p>Here are the details of your application:</p>

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

        <cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>

        <!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
                    <cfif len(variables.strPageFields.ConfirmationContent)>
                        <p>#variables.strPageFields.ConfirmationContent#</p>
                    </cfif>
                    <p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID= variables.siteID)>

        <!--- relocate to message page --->
        <cfset session.invoice = local.invoice />
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
                </cfif>
                <div class="tsAppSectionContentContainer">
                    <p>Here are the details of your application:</p>						
                    #session.invoice#
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="hasSub" access="private" output="false" returntype="string">
        <cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

        <cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
        <cfset local.isExist = false/>
        <cfset local.strData = {}>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, arguments.step)>
            <cfset local.strData = variables.formFields[arguments.step]/>
        </cfif>			

		<cfreturn local.strData>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="CPSectionContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
                    <cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						#replaceNoCase(variables.strPageFields.BilledMessage,"[here]","<a href='#local.redirectLink#'>here</a>")#
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
                    <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>