<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	
	//Upcoming Events
	local.tmpField = { name="UpcomingEventTitle", type="STRING", desc="Editable Upcoming Events Title", value="Upcoming Events" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="UpcomingEventDesc", type="CONTENTOBJ", desc="Editable Upcoming Events Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	
	//Quick Links
	local.tmpField = { name="QuickLinksTitle", type="STRING", desc="Editable Quick Links Title", value="Quick Links" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="QuickLinksDesc", type="CONTENTOBJ", desc="Editable Quick Links Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//Outstanding & Past Due Invoices
	local.tmpField = { name="PastDueInvoicesTitle", type="STRING", desc="Editable Past Due Invoices Title", value="Outstanding & Past Due Invoices" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="PastDueInvoicesDesc", type="CONTENTOBJ", desc="Editable Past Due Invoices Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//My E-Communities
	local.tmpField = { name="CommunitiesTitle", type="STRING", desc="Editable E-Communities Title", value="My eCommunities" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="CommunitiesDesc", type="CONTENTOBJ", desc="Editable E-Communities Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//Announcements
	local.tmpField = { name="AnnouncementTitle", type="STRING", desc="Editable Announcements Title", value="Announcements" };
	arrayAppend(local.arrCustomFields, local.tmpField);		
	local.tmpField = { name="AnnouncementDesc", type="CONTENTOBJ", desc="Editable Announcement Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//My File Downloads
	local.tmpField = { name="MyFileDownloadsTitle", type="STRING", desc="Editable My File Downloads Title", value="My File Downloads" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="MyFileDownloadsDesc", type="CONTENTOBJ", desc="Editable My File Downloads Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//New Fileshare Docs
	local.tmpField = { name="NewFileshareDocsTitle", type="STRING", desc="Editable New Fileshare Docs Title", value="New Fileshare Docs" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="NewFileshareDocsDesc", type="CONTENTOBJ", desc="Editable New Fileshare Docs Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//My Recent Searches
	local.tmpField = { name="MySearchesTitle", type="STRING", desc="Editable My Recent Searches Title", value="My Recent Searches" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MySearchesDesc", type="CONTENTOBJ", desc="Editable My Recent Searches Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//TBD
	local.tmpField = { name="TBDTitle", type="STRING", desc="Editable TBD Title", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="TBDDesc", type="CONTENTOBJ", desc="Editable TBD Desc", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//Business Partners
	local.tmpField = { name="BusinessPartners", type="CONTENTOBJ", desc="Business Partners", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="BusinessPartnersTitle", type="STRING", desc="Editable Business Partners Title", value="Business Partners" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	//Renew Message
	local.tmpField = { name="RenewMessage", type="CONTENTOBJ", desc="Message for those with Billed Membership sub.", value="<div id='RenewNow'>
		<p style='text-align: center;'><strong>[[Salutation]]: Our records indicate that you have not yet paid your '17-'18 dues.</strong></p>
		<p style='text-align: center;'>Please click to <a href='/?pg=manageSubscriptions&suba=renew'>RENEW ONLINE</a> and pay them now. Thank you!</p></div>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="RenewMessageGroup", type="STRING", desc="UID of group with view permission for Renew Message.", value="9f266b70-d010-4ae0-8ea7-9203c81ed40e" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	local.qryGroupMembership = application.objMember.getMemberGroups(session.cfcuser.memberData.memberID, arguments.event.getValue('mc_siteInfo.ORGID'));

</cfscript>

<cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID=local.strPageFields.RenewMessageGroup, orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

<cfquery dbtype="query" name="local.qryIsMemberGroupExist">
	select groupID 
	from [local].qryGroupMembership
	where groupID =  <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
</cfquery>

<cfif val(local.qryIsMemberGroupExist.groupID) gt 0>
	<cfset local.isMemberGroupExist = true>
<cfelse>
	<cfset local.isMemberGroupExist = false>
</cfif>

<cfsavecontent variable="local.evhead">
	<cfoutput>
		<style type="text/css">	
			##myNJAJ.container-fluid{ padding:0px!important;}
			##myNJAJ .myNJAJ .nav-tabs > .active > a,.myNJAJ  .nav-tabs>.active>a:hover { color:##fff!important; background:##26426D!important; font-weight: bold; text-decoration: none; }
			##myNJAJ .myNJAJ .nav-tabs a, .myNJAJ .nav-tabs a:hover { color:##26426D; background:##d3d3d3 !important; font-weight: lighter; text-decoration: none;  }		
			##myNJAJ .myNJAJ .nav-tabs>li>a { margin-right:23px; background:##ececec!important; }
			##myNJAJ .myNJAJ .nav-tabs>li:last-child>a { margin-right:auto; }
			##myNJAJ .myNJAJ .nav { margin-bottom:0px; }
			##myNJAJ .infoCont{margin-bottom:10px !important;}
			##myNJAJ .row-section{margin-left:0px !important;margin-bottom:0px !important;}
			##myNJAJ .tab-content { border:2px solid ##ddd; min-height:200px; padding:10px; margin-bottom:20px; background:##fff;}
			##myNJAJ .showBullets,##myNJAJ .nav-tabs li{list-style: none !important;}
			##myNJAJ .myNJAJ .nav-tabs > li > a { border: 1px solid transparent; border-radius: 4px 4px 0 0; line-height: 1.42857; margin-right: 2px;}
			##myNJAJ .HeaderText {color: ##333436;font-size: 16px; line-height: 18px; margin: 0; padding: 0 0 13px;font-weight: bold;}			
			##myNJAJ .memName {color:##DDB76E;}
			##myNJAJ a:hover  { text-decoration:underline; }
			##myNJAJ a {text-decoration: none;}		
		/*tab fix*/
			##myNJAJ .nav-tabs li{
				margin-bottom: 0!important;
			}
			##myNJAJ .nav-tabs li.active a{
				text-decoration: none!important;
			}
			##myNJAJ .nav-tabs>li>a, .nav-pills>li>a{padding-right: 7px!important;padding-left: 7px!important;}
			##myNJAJ .tab-content .tab-pane{
				overflow-y: auto;
				min-height: 185px;
				max-height: 200px;
			}	
			##myNJAJ ##sponsers .carousel-inner .item p {
				float: left;
				display: table;
				width: 100%;
				margin-top: 27px;
			}
			##myNJAJ ##sponsers .carousel{
				margin-bottom: 0!important;
			}
			##myNJAJ .item{
				margin-top:30px;
			}
			##myNJAJ .item p{
				padding-bottom:0px;
				margin:0px;
			}
			##myNJAJ .item img{
				vertical-align: middle;
			}
			##myNJAJ ##sponsers .carousel-inner .item p a{
				display: table-cell;
				vertical-align: middle;
			}
			##myNJAJ ##sponsers .carousel-inner{
				max-width:250px;
			}
			##myNJAJ .BodyText{font-size:14px!important;}
			span.BodyTextSmall{sont-size:12px!important;}
			
			@media screen and (max-width: 550px) and (min-width: 320px){
				ul.nav-tabs li a {padding:5px!important; font-size:13px!important;}
			}
			@media screen and (max-width: 924px) and (min-width: 881px){
				ul.nav-tabs li a { padding:5px!important; }
			}
			@media screen and (max-width: 1280px) and (min-width: 980px){
				.section-inner div.container div.row-fluid {
					padding-left: 0px!important; 
					padding-right:0px!important; 
				}
			}
			@media screen and (max-width: 550px) and (min-width: 401px){
				.active img {
					position: relative!important;
					left: 0%!important;
					min-width: 0px!important;
					-webkit-clip-path: inset(0px 0px 0px 0)!important;
					min-height: 0px!important;
				}
			}
			@media screen and (max-width: 400px) and (min-width: 351px){			
				.active img {
					position: relative!important;
					left: 0px!important;
					min-width: 0px!important;
					-webkit-clip-path: inset(0px 0px 0px 0px)!important;
					min-height: 0px;
				}
			}
			@media screen and (max-width: 880px) and (min-width: 750px){			
				.active img {
					position: relative!important;
					left: 0px!important;
					min-width: 0px!important;
					-webkit-clip-path: inset(0px 0px 0px 0px)!important;
					min-height:0px!important;
				}
			}
			@media screen and (max-width: 749px) and (min-width: 551px){
				.active img {
					position: relative!important;
					left: 0px!important;
					min-width: 0px!important;
					-webkit-clip-path: inset(0px 0px 0px 0px)!important;
				}
			}
			@media screen and (max-width: 350px){
				.active img {
					position: relative!important;
					left: 0px!important;
					min-width: 0px!important;
					-webkit-clip-path: inset(0px 0px 0px 0px)!important;
					min-height: 0px;
				}
			}
			@media (min-width: 1200px){
				.row-fluid .span1 {
					width: 7.982906%;
				}
			}
			@media screen and (max-width: 1200px) and (min-width: 881px){
				.row-fluid .span1 {
					width: 11.382979%;
				}
			}	
	
		</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.evhead#">
<cfoutput>
	<cfif local.isMemberGroupExist>
		#local.strPageFields.RenewMessage#
	</cfif>
	<div class="container-fluid" id="myNJAJ">
		<div class="row-fluid">
			<div id="mainContent">
				<div class="span12 row-fluid myNJAJ infoCont padding-clear">
					<div class="span1 myPhoto">					
						<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
							<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
						</cfif>
					</div>
					<div class="span10">
						<div class="row-fluid">
							<div class="span12 welcome">
								Welcome, #session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#<cfif len(session.cfcUser.memberData.suffix)>, #session.cfcUser.memberData.suffix#</cfif>!
							</div>
						</div>
						<div class="row-fluid">
							<div class="span6 welcomeLinks">
								<ul>
									<li><a href="/?pg=updateMember">Update My Profile</a></li>
									<li><a href="/?pg=updateMember##memberUpdateSectionLoginInfo">Change My Login</a></li>
									<li><a href="/?pg=updatemember&memaction=updatePhoto">Update My Photo</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
				
				<div class="span12 row-fluid row-section myNJAJ">
					<div class="span4"><!---upcomingEvents--->
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myEventsTabs">
								<li class="active">
									<a href="##upcomingEvents" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.UpcomingEventTitle,'<p>',''),'</p>','')#</a>
								</li>
							</ul>
							
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="upcomingEvents">									
									#ReReplace(ReReplace(local.strPageFields.UpcomingEventDesc,'<p>',''),'</p>','')#
									<br>
									<div>
										<a href="/?pg=events"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myQuickLinksTabs">
								<li class="active"><a href="##quicklink" data-toggle="tab" >#local.strPageFields.QuickLinksTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="quicklink">
									#ReReplace(ReReplace(local.strPageFields.QuickLinksDesc,'<p>',''),'</p>','')#
								</div>
							</div>
						</div>
					</div>
					
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myDueInvoicesTabs">
								<li class="active"><a href="##invoices" data-toggle="tab" >#local.strPageFields.PastDueInvoicesTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="invoices">
									#ReReplace(ReReplace(local.strPageFields.PastDueInvoicesDesc,'<p>',''),'</p>','')#
									<br>
									<div>
										<a href="/?pg=invoices"><i class="icon-calendar icon2x">&nbsp;</i><strong>View All</strong></a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				
				<div class="span12 row-fluid row-section myNJAJ">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myCommunityTabs">
								<li class="active"><a href="##community" data-toggle="tab" >#local.strPageFields.CommunitiesTitle#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="community">
									#ReReplace(ReReplace(local.strPageFields.CommunitiesDesc,'<p>',''),'</p>','')#
									<div id="noCommunity">You do not have access to any groups.</div>
								</div>
							</div>
						</div>
					</div>
					<script type="text/javascript">
						$(document).ready(function(){	
							$("##noCommunity").hide();
							if($("##community .mcMergeTemplate").html().trim().length == 0) {
								$("##noCommunity").delay(100).show();
							}
						});	
					</script>
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myAnnouncementsTabs">
								<li class="active"><a href="##announcements" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.AnnouncementTitle,'<p>',''),'</p>','')#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="announcements">
									#ReReplace(ReReplace(local.strPageFields.AnnouncementDesc,'<p>',''),'</p>','')#
								</div>
							</div>
						</div>
					</div>
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myFileShareTabs">
								<li class="active"><a href="##newfilesharedocs" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.NewFileshareDocsTitle,'<p>',''),'</p>','')#</a></li>
								<li><a href="##myfiledownloads" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.MyFileDownloadsTitle,'<p>',''),'</p>','')#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="newfilesharedocs">
									#ReReplace(ReReplace(local.strPageFields.NewFileshareDocsDesc,'<p>',''),'</p>','')#
								</div>
								<div class="tab-pane BodyText" id="myfiledownloads">
									#ReReplace(ReReplace(local.strPageFields.MyFileDownloadsDesc,'<p>',''),'</p>','')#									
								</div>
							</div>
						</div>
					</div>
				</div>
				
				<div class="span12 row-fluid row-section myNJAJ">
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="mySearchesTabs">
								<li class="active"><a href="##searches" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.MySearchesTitle,'<p>',''),'</p>','')#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="searches">
									#ReReplace(ReReplace(local.strPageFields.MySearchesDesc,'<p>',''),'</p>','')#
								</div>
							</div>
						</div>
					</div>
					
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myTBDTabs">
								<li class="active"><a href="##TBD" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.TBDTitle,'<p>',''),'</p>','')#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active BodyText" id="TBD">
									#ReReplace(ReReplace(local.strPageFields.TBDDesc,'<p>',''),'</p>','')#
								</div>
							</div>
						</div>
					</div>
					
					<div class="span4">
						<div class="row-fluid">
							<ul class="nav nav-tabs" id="myPartnersTabs">
								<li class="active"><a href="##" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.BusinessPartnersTitle,'<p>',''),'</p>','')#</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active" id="myPartnersTabs">
									<div id="myCarousel" class="carousel slide text-center">
										<!-- Carousel items -->
										<div class="carousel-inner text-center">
											<cfif structKeyExists(local.strPageFields,"BusinessPartners") AND len(trim(local.strPageFields.BusinessPartners))>
											<cfloop list="#local.strPageFields.BusinessPartners#" index="local.thisImage" delimiters="||">
												<div class="<cfif ListFirst(local.strPageFields.BusinessPartners,'||') eq local.thisImage>active </cfif>item">
													<p><a href="/" target="_blank">#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#</a></p>
												</div>
											</cfloop>
											</cfif>
										</div>
									</div>
									<cfif structKeyExists(local.strPageFields,"BusinessPartners") AND len(trim(local.strPageFields.BusinessPartners))>
									<script type='text/javascript'>
										$(document).ready(function() {
											 $('.carousel').carousel({
												 interval: 3000
											 })
										});    
									</script>
									</cfif>
								</div>
							</div>
						</div>
					</div>
				</div>
				
			</div>
		</div>
	</div>
</cfoutput>