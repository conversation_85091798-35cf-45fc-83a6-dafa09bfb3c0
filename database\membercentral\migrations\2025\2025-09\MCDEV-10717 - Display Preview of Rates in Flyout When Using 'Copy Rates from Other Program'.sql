USE membercentral;
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @methodID int, @adminRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

	SELECT @adminRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES ('getRatePreviewData', @adminRTFID);
	
	EXEC dbo.ajax_addComponentMethodRightsBulk
  		@componentName='ADMINSWCOMMON',
  		@requestCFC='model.admin.seminarWeb.seminarWebSWCommon',
  		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
  		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

USE seminarWeb;
GO

ALTER PROC dbo.sw_copyRates
@participantID int,
@copyFromSeminarID int,
@copyToSeminarID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @siteID int, @orgID int, @minRateID int, @rateGroupingID int, @rateName varchar(100),
		@rate money, @revenueGLAccountID int, @newRateID int, @newRateSiteResourceID int, @rateIsHidden bit, @siteResourceID int,
		@newRateGroupingID int, @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_include bit,
		@srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @copyFromSeminarProgramType varchar(5),
		@copyToSeminarProgramType varchar(5), @msgjson varchar(max) = '';

	select @siteID = ss.siteID, @orgID = ss.orgID
	from dbo.tblSeminars as s
	inner join dbo.tblParticipants as p on p.participantID = s.participantID
	inner join memberCentral.dbo.sites as ss on ss.siteCode = p.orgcode
	where s.seminarID = @copyFromSeminarID
	and p.participantID = @participantID;

	-- Phase 1: Cleanup existing rates and groupings (separate transaction)
	BEGIN TRAN;
		-- delete existing rate groupings
		UPDATE dbo.tblSeminarsAndRates
		SET rateGroupingID = NULL
		WHERE seminarID = @copyToSeminarID
		AND participantID = @participantID;

		DELETE FROM dbo.tblSeminarsAndRatesGrouping
		WHERE seminarID = @copyToSeminarID
		AND participantID = @participantID;

		-- delete existing rates
		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM memberCentral.dbo.cms_siteResources as sr
		INNER JOIN dbo.tblSeminarsAndRates as r on r.siteResourceID = sr.siteResourceID AND r.participantID = @participantID
		where r.seminarID = @copyToSeminarID
		and sr.siteResourceStatusID <> 3;
	COMMIT TRAN;

	-- Phase 2: Copy rate groupings (separate transaction)
	insert into dbo.tblSeminarsAndRatesGrouping (participantID, rateGrouping, seminarID, rateGroupingOrder)
	select @participantID, rateGrouping, @copyToSeminarID, rateGroupingOrder
	from dbo.tblSeminarsAndRatesGrouping
	where seminarID = @copyFromseminarID;

	-- Phase 3: Copy active rates and permissions
	select @minRateID = min(r.rateID)
		from dbo.tblSeminarsAndRates as r
		inner join memberCentral.dbo.cms_siteResources as sr on sr.siteID = @siteID 
			and sr.siteResourceID = r.siteResourceID
			and sr.siteResourceStatusID = 1
		where r.seminarID = @copyFromseminarID
		and r.participantID = @participantID;

	while @minRateID is not null BEGIN
		select @rateGroupingID = null, @rateName = null, @rate = null, @siteResourceID = null, @rateIsHidden = null, @revenueGLAccountID = null, @newRateGroupingID=null;

		select @rateGroupingID = rateGroupingID, @rateName = rateName, @rate = rate, @siteResourceID = siteResourceID,
			@rateIsHidden = isHidden, @revenueGLAccountID = revenueGLAccountID
		from dbo.tblSeminarsAndRates
		where rateID = @minRateID;

		select @newRateGroupingID = rg1.rateGroupingID
		from dbo.tblSeminarsAndRatesGrouping as rg1
		inner join dbo.tblSeminarsAndRatesGrouping as rg2
			on rg2.rateGrouping = rg1.rateGrouping
			and rg1.seminarID = @copyToSeminarID
			and rg2.seminarID = @copyFromseminarID
			and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			and rg1.participantID = @participantID
			and rg2.participantID = @participantID;

		IF @rateName is not null and @rate is not null BEGIN
			EXEC dbo.sw_createSeminarRate @participantID=@participantID, @seminarID=@copyToSeminarID, @rateGroupingID=@newRateGroupingID,
				@rateName=@rateName, @rate=@rate, @isHidden=@rateIsHidden, @revenueGLAccountID=@revenueGLAccountID,
				@recordedByMemberID=@recordedByMemberID, @rateID=@newRateID OUTPUT, @siteResourceID=@newRateSiteResourceID OUTPUT;

			-- copy resource rights for this resource using NOLOCK to prevent deadlocks
			SET @srr_rightsID = null;
			SELECT @srr_rightsID = min(resourceRightsID) from memberCentral.dbo.cms_siteResourceRights WITH(NOLOCK) where resourceID = @siteResourceID and siteID = @siteID;
			WHILE @srr_rightsID IS NOT NULL BEGIN
				SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID,
					@srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID,
					@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
				FROM memberCentral.dbo.cms_siteResourceRights WITH(NOLOCK)
				WHERE resourceRightsID = @srr_rightsID and siteID = @siteID;

				EXEC memberCentral.dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRateSiteResourceID, @include=@srr_include,
					@functionIDList=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @inheritedRightsResourceID=@srr_inheritedRightsResourceID,
					@inheritedRightsFunctionID=@srr_inheritedRightsFunctionID;

				select @srr_rightsID = min(resourceRightsID)
					from memberCentral.dbo.cms_siteResourceRights WITH(NOLOCK)
					where resourceID = @siteResourceID
					and resourceRightsID > @srr_rightsID and siteID = @siteID;
			END
		END

		-- Get next rate outside of transaction
		select @minRateID = min(r.rateID)
		from dbo.tblSeminarsAndRates as r
		inner join memberCentral.dbo.cms_siteResources as sr on sr.siteID = @siteID 
			and sr.siteResourceID = r.siteResourceID
			and sr.siteResourceStatusID = 1
		where r.seminarID = @copyFromseminarID
		and r.rateID > @minRateID
		and r.participantID = @participantID;
	END

	-- audit log
	SELECT @copyFromSeminarProgramType = CASE WHEN swl.seminarID IS NULL THEN 'SWOD' ELSE 'SWL' END
	FROM dbo.tblSeminars AS s
	LEFT JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
	WHERE s.seminarID = @copyFromSeminarID;

	SELECT @copyToSeminarProgramType = CASE WHEN swl.seminarID IS NULL THEN 'SWOD' ELSE 'SWL' END
	FROM dbo.tblSeminars AS s
	LEFT JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
	WHERE s.seminarID = @copyToSeminarID;

	SELECT @msgjson = 'Program Rates have been copied to ' + @copyToSeminarProgramType + '-' + CAST(@copyToSeminarID AS VARCHAR(10))
		+ ' from ' + @copyFromSeminarProgramType + '-' + CAST(@copyFromSeminarID AS VARCHAR(10)) + '.'	+ CHAR(13) + CHAR(10)
		+ 'Existing rates are removed and copied the following rates: ' + ISNULL(NULLIF(STRING_AGG('[' + r.rateName + ']',', ') WITHIN GROUP (ORDER BY r.rateName ASC),''),'(No rates found)') + '.'
	FROM dbo.tblSeminarsAndRates AS r
	INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = r.siteResourceID 
		AND sr.siteResourceStatusID = 1
	WHERE r.seminarID = @copyFromseminarID
	AND r.participantID = @participantID
	AND r.rateName IS NOT NULL
	AND r.rate IS NOT NULL;

	IF LEN(@msgjson) > 0 BEGIN
		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swb_copyRates
@participantID int,
@copyFromBundleID INT,
@copyToBundleID INT,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @siteID int, @orgID int, @minRateID int, @rateGroupingID int, @rateName varchar(100),
		@rate money, @revenueGLAccountID int, @newRateID int, @newRateSiteResourceID int, @rateIsHidden bit, @siteResourceID int,
		@newRateGroupingID int, @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_include bit,
		@srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @msgjson varchar(max) = '';

	select @siteID = ss.siteID, @orgID = ss.orgID
	from dbo.tblBundles as b
	inner join dbo.tblParticipants as p on p.participantID = b.participantID
	inner join memberCentral.dbo.sites as ss on ss.siteCode = p.orgcode
	where b.bundleID = @copyFromBundleID;

	-- Phase 1: Cleanup existing rates and groupings (separate transaction)
	BEGIN TRAN;
		-- delete existing rate groupings
		UPDATE dbo.tblBundlesAndRates SET rateGroupingID = NULL WHERE bundleID = @copyToBundleID AND participantID = @participantID;
		DELETE FROM dbo.tblBundlesAndRatesGrouping WHERE bundleID = @copyToBundleID AND participantID = @participantID;

		-- delete existing rates
		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM memberCentral.dbo.cms_siteResources as sr
		INNER JOIN dbo.tblBundlesAndRates as r on r.siteResourceID = sr.siteResourceID
		where r.bundleID = @copyToBundleID
		AND r.participantID = @participantID
		and sr.siteResourceStatusID <> 3;
	COMMIT TRAN;

	-- Phase 2: Copy rate groupings
	insert into dbo.tblBundlesAndRatesGrouping (participantID, rateGrouping, bundleID, rateGroupingOrder)
	select @participantID, rateGrouping, @copyToBundleID, rateGroupingOrder
	from dbo.tblBundlesAndRatesGrouping
	where bundleID = @copyFromBundleID;

	-- Phase 3: Copy active rates and permissions
	select @minRateID = min(r.rateID)
	from dbo.tblBundlesAndRates as r
	inner join memberCentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
	where r.bundleID = @copyFromBundleID
	and r.participantID = @participantID;

	while @minRateID is not null BEGIN
		select @rateGroupingID = null, @rateName = null, @rate = null, @siteResourceID = null, @rateIsHidden = null, @revenueGLAccountID = null, @newRateGroupingID=null;

		select @rateGroupingID = rateGroupingID, @rateName = rateName, @rate = rate, @siteResourceID = siteResourceID,
			@rateIsHidden = isHidden, @revenueGLAccountID = revenueGLAccountID
		from dbo.tblBundlesAndRates
		where rateID = @minRateID;

		select @newRateGroupingID = rg1.rateGroupingID
		from dbo.tblBundlesAndRatesGrouping as rg1
		inner join dbo.tblBundlesAndRatesGrouping as rg2
			on rg2.rateGrouping = rg1.rateGrouping
			and rg1.bundleID = @copyToBundleID
			and rg2.bundleID = @copyFromBundleID
			and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			and rg1.participantID = @participantID
			and rg2.participantID = @participantID;

		IF @rateName is not null and @rate is not null BEGIN
			EXEC dbo.sw_createBundleRate @participantID=@participantID, @bundleID=@copyToBundleID, @rateGroupingID=@newRateGroupingID,
				@rateName=@rateName, @rate=@rate, @isHidden=@rateIsHidden, @revenueGLAccountID=@revenueGLAccountID,
				@recordedByMemberID=@recordedByMemberID, @rateID=@newRateID OUTPUT, @siteResourceID=@newRateSiteResourceID OUTPUT;

			-- copy resource rights for this resource using NOLOCK to prevent deadlocks
			SET @srr_rightsID = null;
			SELECT @srr_rightsID = min(resourceRightsID) from memberCentral.dbo.cms_siteResourceRights WITH(NOLOCK) where resourceID = @siteResourceID and siteID = @siteID;
			WHILE @srr_rightsID IS NOT NULL BEGIN
				SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID,
					@srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID,
					@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
				FROM memberCentral.dbo.cms_siteResourceRights WITH(NOLOCK)
				WHERE resourceRightsID = @srr_rightsID and siteID = @siteID;

				EXEC memberCentral.dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRateSiteResourceID, @include=@srr_include,
					@functionIDList=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @inheritedRightsResourceID=@srr_inheritedRightsResourceID,
					@inheritedRightsFunctionID=@srr_inheritedRightsFunctionID;

				select @srr_rightsID = min(resourceRightsID)
				from memberCentral.dbo.cms_siteResourceRights WITH(NOLOCK)
				where resourceID = @siteResourceID
				and resourceRightsID > @srr_rightsID and siteID = @siteID;
			END
		END

		-- Get next rate outside of transaction
		select @minRateID = min(r.rateID)
		from dbo.tblBundlesAndRates as r
		inner join memberCentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
		where r.bundleID = @copyFromBundleID
		and r.rateID > @minRateID
		and r.participantID = @participantID;
	END

	-- audit log
	SELECT @msgjson = 'Program Rates have been copied to SWB-' + CAST(@copyToBundleID AS VARCHAR(10)) + ' from SWB-' + CAST(@copyFromBundleID AS VARCHAR(10)) + '.'	+ CHAR(13) + CHAR(10)
		+ 'Existing rates are removed and copied the following rates: ' + ISNULL(NULLIF(STRING_AGG('[' + r.rateName + ']',', ') WITHIN GROUP (ORDER BY r.rateName ASC),''),'(No rates found)') + '.'
	FROM dbo.tblBundlesAndRates as r
	INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID AND sr.siteResourceStatusID = 1
	WHERE r.bundleID = @copyFromBundleID
	AND r.participantID = @participantID
	AND r.rateName IS NOT NULL
	AND r.rate IS NOT NULL;

	IF LEN(@msgjson) > 0 BEGIN
		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

