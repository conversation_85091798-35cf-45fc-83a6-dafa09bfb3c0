<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAnnouncements">
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">, @now datetime = getdate();
	SELECT top(3) n2.noticeID, dbo.fn_getResourcePagePlacementXML(@siteID, ai2.siteResourceID) as placementXML, tmp.contentTitle, ai2.applicationInstanceID, tmp.startDate, tmp.endDate
	FROM dbo.an_notices n2
	inner join dbo.an_centers c2 on n2.siteID = @siteID and n2.centerID = c2.centerID
	inner join dbo.cms_applicationInstances ai2 on ai2.applicationInstanceID = c2.applicationInstanceID and ai2.siteID = @siteID
	inner join 
		(
		select min(n.noticeID) as noticeID,
			noticeContent.contentTitle, startDate, endDate
		from dbo.an_notices n
		inner join dbo.cms_content content on n.siteID = @siteID and content.siteID = @siteID and content.contentID = n.noticeContentID
		inner join dbo.cms_siteResources sr on sr.siteID = @siteID and content.siteResourceID = sr.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		inner join dbo.an_centers c on n.centerID = c.centerID
		inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID and ai.siteID = @siteID
		inner join dbo.cms_siteResources sr2 on sr2.siteID = @siteID and n.siteResourceID = sr2.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs2 on srs2.siteResourceStatusID = sr2.siteResourceStatusID and srs2.siteResourceStatusDesc = 'Active'
		inner join dbo.cms_contentLanguages as noticeContent on noticeContent.contentID = n.noticeContentID and noticeContent.languageID = <cfqueryparam value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#" cfsqltype="CF_SQL_INTEGER">
		where @now between n.startdate and n.enddate
		 and dbo.fn_cache_perms_getResourceRightsXML(n.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,@siteID).exist('(/rights/right[@allowed="1"])[1]') = 1
		group by noticeContent.contentTitle, startDate, endDate
		) tmp
	on n2.noticeID = tmp.noticeID

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
</cfquery>

<cfset local.data.address = application.objMember.getCompleteAddresses(session.cfcUser.memberData.memberID) />
<cfset local.data.phone = application.objMember.getMemberPhones(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=session.cfcUser.memberData.memberID)>

<cfoutput>		
		<div class="row-fluid">
			<div class="container-fluid">
				<div class="span8 memberInfo">
					<div class="myPhoto span2">
						<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
							<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" class="memberPhoto" width="80" height="100">
							<div class="overlayText text-center">
								<a href="/?pg=updatemember&memaction=updatePhoto">
									<i class="fa fa-pencil">&nbsp;</i><br />Update Photo
								</a>
							</div>
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" class="memberPhoto" width="80" height="100">
							<div class="overlayText text-center">
								<a href="/?pg=updatemember&memaction=updatePhoto">
									<i class="fa fa-pencil">&nbsp;</i><br />Update Photo
								</a>
							</div>
						</cfif>
					</div>
					<div class="span10">
						<div class="hoverRow">
							<h4>#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#</h4>
							<div class="hoverIcon"><a href="/?pg=memberrecertification"><i class="fa fa-pencil">&nbsp;</i>Update my information</a></div>
						</div>
						<div class="hoverRow">
							<h2>#session.cfcUser.memberData.company#</h2>
							<cfif #local.data.address.address1[1]# neq ''>
								#local.data.address.address1[1]#<br />
								<cfif #local.data.address.address2[1]# neq ''>
									#local.data.address.address2[1]#<br />
								</cfif>
								#local.data.address.city[1]#, #local.data.address.stateCode[1]# #local.data.address.postalCode[1]# <br />
							</cfif>
							<div class="hoverIcon"><a href="/?pg=memberrecertification"><i class="fa fa-pencil">&nbsp;</i>Update my address</a></div>
						</div>
						<div class="hoverRow">
							<cfif session.cfcUser.memberData.email neq ''>
								<a href="mailto:#session.cfcUser.memberData.email#">#session.cfcUser.memberData.email#</a><br />
							</cfif>
							<div class="hoverIcon"><a href="/?pg=memberrecertification"><i class="fa fa-pencil">&nbsp;</i>Update my email address</a></div>
						</div>
						<div class="hoverRow">
							<cfif #local.data.phone.phone[1]# neq ''>
								#local.data.phone.phone[1]#<br />
							</cfif>
							<div class="hoverIcon"><a href="/?pg=memberrecertification"><i class="fa fa-pencil">&nbsp;</i>Update my phone number</a></div>
						</div>
					</div>
				</div>
				<div class="span4 pull-right">
					<div class="sameheight-block">
						<div class="row-fluid icons">
							<div class="span4 text-center">
								<!-- ZONE D -->
							</div>
							<div class="span4 text-center">
								<!-- ZONE E -->
							</div>
							<div class="span4 text-center">
								<!-- ZONE F -->
							</div>
						</div>
					</div>
					<div class="sameheight-block">
						<div class="row-fluid icons">
							<div class="span4 text-center">
								<!-- ZONE G -->
							</div>
							<div class="span4 text-center">
								<!-- ZONE H -->
							</div>
							<div class="span4 text-center">
								<!-- ZONE I -->
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

				<div class="thumbnails">
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12 text-center">
								<!-- ZONE J -->
							</div>
						</div>						
						<div class="row-fluid">
							<div class="span12">
								<div class="item">
									<div class="title text-center bg-1"><!-- ZONE K --></div>
									<!-- ZONE L -->
								</div>
							</div>
						</div>
					</div>
				</div>

<br>
				<div class="sameheight-block">
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span8">

								<div class="row-fluid">
									<div class="span12">
										<div class="item item-2">
											<div class="title text-center bg-3"><!-- ZONE M --></div>
											<div class="content-item eventDisplay">
													<cfif local.qryAnnouncements.recordcount NEQ 0>
														<cfloop query="local.qryAnnouncements">
															<cfset local.placementXML = xmlParse(local.qryAnnouncements.placementXML)>
															<cftry>
																<cfset local.representivePageTitle = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageTitle>
																<cfcatch type="any">
																	<cfset local.representivePageTitle = "Untitled Page"/>
																</cfcatch>	
															</cftry>
															<cftry>
																<cfset local.representivePageName = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageName>
																<cfcatch type="any">
																	<cfset local.representivePageName = "Unnamed Page"/>
																</cfcatch>	
															</cftry>
															
															<cftry>
																<cfif len(local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName)>
																	<cfset local.representiveCommunityPageName = local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName/>
																	<cfset local.pageLink = "/?pg=#local.representiveCommunityPageName#&commpg=#local.representivePageName#">
																<cfelse>
																	<cfset local.pageLink = "/?#getAppBaseLink(local.qryAnnouncements.applicationInstanceID,arguments.event.getValue('mc_siteinfo.siteid'))#">
																</cfif>
																<cfcatch type="any">
																	<cfset local.representivePageName = ""/>
																	<cfset local.pageLink = "##">
																</cfcatch>	
															</cftry>
															
															
															<div class="box" id="#noticeID#">
																<div class="events text-center">
																	<div class="holder">
																		<a href="#local.pageLink#"><span class="month">#DateFormat(startDate,"mmm")#</span><br />
																		<span class="date">#DateFormat(startDate,"dd")#</span> </a>
																	</div>
																</div>
																<div class="text titleDiv">
																	<h2><a class="eventTitle" href="#local.pageLink#">#contentTitle#</a></h2>
																</div>
																<div class="well arrowBox">
																	<a href="#local.pageLink#"><i class="fa fa-chevron-right">&nbsp;</i></a>
																</div>
															</div>
															<br />
															
														</cfloop>
													<cfelse>
														Currently, there are no announcements.
													</cfif>	
											</div>
										</div>										
									</div>
								</div>
<br>
								<div class="row-fluid">
									<div class="span6">
										<div class="item sameheight">
											<div class="title text-center bg-1"><!-- ZONE N --></div>
											<div class="content-item eventDisplay">
												[[UpcomingEvents maxrows=[4] format=[json] jsonvariable=[upcomingevents]]]
												<div class="mcMergeTemplate" data-mcjsonvariable="upcomingevents">
													{{##if events}} 
														{{##events}}
															<div class="content-item eventDisplay">
																<div class="box{{##if (moment isAfter=endDate)}} pastEvent{{/if}}" id="ev{{{id}}}">
																	<div class="events text-center">
																		<div class="holder">
																			<a href="/?pg=events&evAction=showDetail&eid={{{id}}}">
																				<span class="month">{{moment startDate format='MMM'}}</span>
																				<br>
																				<span class="date">{{moment startDate format='DD'}}</span>
																			</a>
																		</div>
																	</div>
																	<div class="text">
																		<h2><a href="/?pg=events&evAction=showDetail&eid={{{id}}}" class="eventTitle">{{{truncate title 40}}}</a></h2>
																		<p>{{##if categories}} {{##each categories}}{{this.name}}{{##unless @last}}, {{/unless}}{{/each}}{{/if}}</p>
																		<ul class="link">
																			<li><a href="/?pg=events&evAction=showDetail&eid={{{id}}}">Register</a></li>
																			<li><a href="/?pg=events&evAction=showDetail&eid={{{id}}}">View Details</a></li>
																		</ul>
																	</div>
																</div>
															</div>																																								
														{{/events}} 
													{{/if}}	
												</div>
											</div>
										</div>
									</div>
									<div class="span6">
										<div class="item item-1 sameheight">
											<div class="title text-center bg-2"><!-- ZONE O --></div>
											<div class="content-item myPagePublications">
													<!-- ZONE P -->													
											</div>
										</div>
									</div>
								</div>



							</div>
							<div class="span4 twitterWidget text-center">
									<!-- ZONE R -->										
							</div>
						</div>
					</div>
				</div>

<br>
				<div class="thumbnails">
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12 text-center">
								<!-- ZONE Q -->		
							</div>
						</div>						
					</div>
				</div>
<style type="text/css">
.titleDiv{width:81%;}
</style>


</cfoutput>

