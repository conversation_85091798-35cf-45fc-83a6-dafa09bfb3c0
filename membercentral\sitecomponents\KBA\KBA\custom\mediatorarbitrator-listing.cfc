<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="KBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCheck", type="STRING", desc="pay profile code for Check", value="PaybyCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="Knoxville Bar Association records indicate you are currently have a Mediator Listing on the Knoxbar.org website." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Error message when billed subscription found", value="You need to renew your Mediator Listing. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you.  Your Mediator Listing application has been submitted.   You will receive an email from Knoxville Bar Association with the information you provided." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorAlreadyMemberMessage", type="CONTENTOBJ", desc="Error message when user already a member", value="Our records indicate you are a KBA Member. Please <a href='/?pg=login&logact=mediatorarbitrator-listing'>Login</a> to receive a discounted rate for the Mediator Listing fee." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmMediator',
			formNameDisplay='Mediator Arbitrator Listing',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));
		variables.membershipDuesTypeUID = '25a454f8-cf5b-482f-b313-88b025698820';
        variables.mediatorListingTypeUID = 'd357427f-5712-4497-a515-aa8c58f23a5a';
		variables.mediatorListingSubUID = '57D29C3F-D525-4D91-8BD5-36E18E22D35D';


		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('historyID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('historyID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='mediatorlisting', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='mediatorlisting', subName='Completed');
		variables.historyStartedText = "Member started Mediator Arbitrator Listing form.";
		variables.historyCompletedText = "Member completed Mediator Arbitrator Listing form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				local.checkSubscriptionStatus = processLookUp();
				switch (local.checkSubscriptionStatus.status) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;
                    case "activemembershipfound":
                        local.returnHTML = showError(errorCode='activemembershipfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound',redirecturl=local.checkSubscriptionStatus.redirectUrl);
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");						
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
					var mcSel = $('###variables.formName# ##mccf_memberCategory').val();
					if (mcSel == 'Attorney' || mcSel == 'Defense Attorney' || mcSel == 'Government Attorney') {
						$('##tbodymccf_firstTimeMember').show();
					} else {
						$('##tbodymccf_firstTimeMember').hide();
						$('###variables.formName# ##mccf_firstTimeMember').prop('checked', false);
					}
				}
				function validateMembershipInfoForm(){
					var arrReq = new Array();
					if (!$('###variables.formName# ##mccf_RFID').is(':checked')) arrReq[arrReq.length] = "Make a membership type selection.";
					if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							mc_loadDataForForm($('###variables.formName#'),'previous');			        	
						}
					} else {
						step = 1;
					}
					prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
						
			<cfif len(variables.strPageFields.AccountLocatorIntroText)>
				<div id="AccountLocatorIntroText">#variables.strPageFields.AccountLocatorIntroText#</div><br/>
			</cfif>											
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.stReturn.status = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn.status = "nomatch">
		<cfelse>
			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfquery dbtype="query" name="local.qryActiveSubsActvnReqMet">
                SELECT * FROM [local].qryActiveSubs WHERE paymentStatus = 'P' <!---Activation Requirement Met--->
            </cfquery>
            <cfset local.activeSubList = valueList(local.qryActiveSubs.typeUID)>
            <cfset local.activeSubListActvnReqMet = valueList(local.qryActiveSubsActvnReqMet.typeUID)>
            
            <cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
            <cfquery dbtype="query" name="local.qryActiveSubsActvnReqMet">
                SELECT * FROM [local].qryAcceptedSubs WHERE paymentStatus = 'P' <!---Activation Requirement Met--->
            </cfquery>
            <cfset local.acceptedSubList = valueList(local.qryAcceptedSubs.typeUID)>
            <cfset local.acceptedSubListActvnReqMet = valueList(local.qryActiveSubsActvnReqMet.typeUID)>
            
            <cfset local.qryAllBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
            <cfset local.billedSubList = valueList(local.qryAllBilledSubs.typeUID)>
            
            <cfset local.isActiveMember = listFindNoCase(local.activeSubListActvnReqMet, variables.membershipDuesTypeUID)>
            <cfset local.isAcceptedMember = listFindNoCase(local.acceptedSubListActvnReqMet, variables.membershipDuesTypeUID)>
            
            <cfset local.isActiveMediatorList = listFindNoCase(local.activeSubList, variables.mediatorListingTypeUID)>
            <cfset local.isAcceptedMediatorList = listFindNoCase(local.acceptedSubList, variables.mediatorListingTypeUID)>
            <cfset local.isBilledMediatorList = listFindNoCase(local.billedSubList, variables.mediatorListingTypeUID)>
            <cfset local.renewRedirectLinkCode = ''>
			<cfif local.isBilledMediatorList>
				<cfquery name="local.qryBilledSubs" dbtype="query">
					SELECT * FROM local.qryAllBilledSubs WHERE UID = '#variables.mediatorListingSubUID#'
				</cfquery>
				<cfset local.renewRedirectLinkCode = local.qryBilledSubs.directLinkCode>
			</cfif>

            <cfif ((local.isActiveMember OR local.isAcceptedMember) AND NOT (local.isActiveMediatorList OR local.isAcceptedMediatorList)) AND NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)><!--- Active/Accepted Membership Dues subscription in the Activation Requirement Met status AND no Mediator Listing Subscription --->
                <cfset local.stReturn.status = "activemembershipfound">
            <cfelseif ((local.isActiveMember OR local.isAcceptedMember) AND (local.isActiveMediatorList OR local.isAcceptedMediatorList)) AND NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)><!--- Active/Accepted Membership Dues subscription in the Activation Requirement Met status AND an Active/Accepted Mediator Listing Subscription. --->
                <cfif local.isActiveMediatorList>
                    <cfset local.stReturn.status = "activefound">
                <cfelse>
                    <cfset local.stReturn.status = "acceptedfound">
                </cfif>
            <cfelseif (local.isActiveMediatorList OR local.isAcceptedMediatorList)><!--- the Mediator Listing subscription in Active/Accepted status--->
                <cfif local.isActiveMediatorList>
                    <cfset local.stReturn.status = "activefound">
                <cfelse>
                    <cfset local.stReturn.status = "acceptedfound">
                </cfif>
            <cfelseif local.isBilledMediatorList><!---the Mediator Listing subscription in Billed status --->
				<cfif local.qryBilledSubs.isRenewalRate>					
					<cfset local.stReturn.redirecturl="/renewsub/#local.renewRedirectLinkCode#" >
					<cfset local.stReturn.status = "billedfound">
				<cfelse>
					<cfset local.stReturn.status = "billedjoinfound">
				</cfif>
            <cfelse>
                <cfset local.stReturn.status = "success">
            </cfif>
        
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='cdc08571-404f-407c-8c53-87a7345d6a1a', mode="collection", strData=local.strData)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent1.fieldSetContent#
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- set Contact Type based on selection of Licensed Attorney --->
		<cfset local.kbaContactTypeValueIDList = "">
		<cfset local.kbaContactTypeNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnName="Contact Type"]')>
		<cfset local.kbaContactTypeValueID = XMLSearch(local.kbaContactTypeNode[1],'string(columnvalue[@columnValueString="Mediator"]/@valueID)')>
		<cfset local.kbaContactTypeValueIDList = local.kbaContactTypeValueID>			

		<cfset local.kbaLicensedAttorneyNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnName="Mediator Licensed Attorney"]')>
		<cfset local.kbaLicensedAttorneyValueID = XMLSearch(local.kbaLicensedAttorneyNode[1],'string(columnvalue[@columnValueBit=1]/@valueID)')>
		<cfif isDefined("arguments.rc.MD_#local.kbaLicensedAttorneyNode[1].xmlAttributes.columnID#") and evaluate("arguments.rc.MD_#local.kbaLicensedAttorneyNode[1].xmlAttributes.columnID#") eq local.kbaLicensedAttorneyValueID>
			<cfset local.kbaContactTypeValueID = XMLSearch(local.kbaContactTypeNode[1],'string(columnvalue[@columnValueString="Attorney"]/@valueID)')>
			<cfset local.kbaContactTypeValueIDList = listAppend(local.kbaContactTypeValueIDList, local.kbaContactTypeValueID)>			
		</cfif>
		<cfset structInsert(arguments.rc, "MD_#local.kbaContactTypeNode[1].xmlAttributes.columnID#", local.kbaContactTypeValueIDList)>		

		<!--- save member info and record history --->		
		<cfset variables.useMID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif local.isLoggedIn>
			<cfset variables.useMID = session.cfcUser.memberData.memberID>
		</cfif>
	
		<!--- We need to make sure a new user is created when saving the data --->
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=variables.siteID,
																					memberID=variables.useMID, 
																					subscriptionUID='57d29c3f-d525-4d91-8bd5-36e18e22d35d', 
																					isRenewal=0,
																					frequencyShortName='F',
																					allowFrontEnd="true")>
		<cfif not local.qryRates.recordCount>		
			<cfreturn showError(errorCode='failsavemembership')>
		</cfif>

		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>	

		<cfif local.qryRates.rateAmt gt 0>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC, variables.strPageFields.PaymentProfileCodeCheck ]>
			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																						siteID=variables.siteID, 
																						memberID=variables.useMID, 
																						title="Mediator Arbitrator-Listing- Payment Method", 
																						formName=variables.formName, 
																						backStep="processLookup")>		
		</cfif>

		<cfif local.qryRates.rateAmt gt 0>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Mediator Listing Confirmation</div>
			<div class="tsAppSectionContentContainer">
				<table cellpadding="3" border="0" cellspacing="0" width="70%">	
				<tr valign="top">
					<th class="tsAppBodyText" width="50%" align="left">
						Item
					</th>
					<th class="tsAppBodyText" align="left">
						Price
					</th>
				</tr>
				<cfloop query="local.qryRates">
					<tr valign="top">
						<td class="tsAppBodyText">
							#local.qryRates.rateName#
							<cfinput type="hidden" name="mccf_RFID" id="mccf_RFID" value="#local.qryRates.RFID#">
						</td>
						<td class="tsAppBodyText">
							#dollarFormat(local.qryRates.rateAmt)#
						</td>
					</tr>
					<cfbreak>
				</cfloop>	
				</table>
			</div>

			<cfif local.qryRates.rateAmt gt 0>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
			</cfif>	
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>			
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>				
		<cfset local.response = "success">		

		<cfreturn local.response>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='cdc08571-404f-407c-8c53-87a7345d6a1a', mode="confirmation", strData=arguments.rc)>

		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=variables.siteID, 
																					memberID=variables.useMID, 
																					subscriptionUID='57d29c3f-d525-4d91-8bd5-36e18e22d35d', 
																					isRenewal=0)>
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>

		<cfset local.memberPayProfileDetail = "">
		<cfif local.qryRatesSelected.rateAmt gt 0>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfset application.objCustomPageUtils.mh_updateHistory(	memberID=variables.useMID, historyID=variables.useHistoryID, 
																subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
																newAccountsOnly=false)>	

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>	

		<cfset local.strURL = { d="#dateformat(now(),"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", s=variables.siteID, m=variables.useMID, memberUpdateLink=1 }>
		<cfset local.encString = encrypt(serializeJSON(local.strURL),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
		<cfset local.updateurl = "#local.thisScheme#://#local.thisHostname#/?pg=updatemember&id=#local.encString#">

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationMessage)>
				<div>#variables.strPageFields.ConfirmationMessage#</div>
			</cfif>

			<div>Please <a href="#local.updateurl#">Click Here</a> to update any information that you may have missed or want to update. We ask that you also provide a digital PHOTO for your listing. 
			The digital photo size to upload is restricted to 83 pixels wide by 124 pixels high.</div>	

			<!--@@specialcontent@@-->

			<div>Here are the details of your application:</div><br/>

			#local.strFieldSetContent1.fieldSetContent#

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Mediator Arbitrator-Listing - Membership Type</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<div>#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
				</td>
			</tr>
			</table>
			<br/>
			<cfif local.qryRatesSelected.rateAmt gt 0>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Mediator Arbitrator-Listing - Payment</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to Knoxville Bar Association",
			emailhtmlcontent = local.confirmationHTML,
			siteID = variables.siteID,
			memberID = variables.useMID,
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to Knoxville Bar Association", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">
		<cfargument name="redirecturl" type="string" required="false">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
                <cfif arguments.errorCode eq "activemembershipfound">
                    #variables.strPageFields.ErrorAlreadyMemberMessage#
                <cfelseif arguments.errorCode eq "activefound">
					#variables.strPageFields.errorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script language="javascript">
						function callback() {
							return function() {
								window.location = "#arguments.redirecturl#";
							}
						}
						setTimeout(callback(), 5000);
					</script>
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>