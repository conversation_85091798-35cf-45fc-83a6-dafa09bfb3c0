<cfoutput>
<div class="row-fluid">
	<div class="span12">
		<div id="seachResultsPaginationBar" class="well well-small <cfif arguments.numTotalPages lte 1 and arguments.topOrBottom eq "bottom">hide</cfif>" style="margin:3px 0px;padding:5px;" data-totalPages="#arguments.numTotalPages#" data-currentPage="#arguments.NumCurrentPage#">
			<cfif arguments.numTotalPages gt 1>
				<cfset local.pagingLinkPrevious = "b_doSearchPN(#arguments.searchid#,#arguments.startrow - variables.thisBucketMaxPerPage#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">
				<cfset local.pagingLinkNext = "b_doSearchPN(#arguments.searchid#,#arguments.startrow + variables.thisBucketMaxPerPage#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">
				<cfset local.currentPageLabel = "Page #arguments.NumCurrentPage# of #numberformat(arguments.NumTotalPages)#">

				<cfif arguments.topOrBottom eq "top">
					<div id="tsSearchTopPagingControls" class="pull-right">	
						<div class="visible-desktop">
							<ul class="pager">
								<cfif arguments.NumCurrentPage gt 1>
									<li><a class="bucketPagingButtonPreviousPage" href="javascript:#local.pagingLinkPrevious#"><strong>Previous</strong></a></li>
								</cfif>
								<li><span style="border-radius:5px;">#local.currentPageLabel#</span></li>
								<cfif arguments.NumCurrentPage lt arguments.NumTotalPages>
									<li><a class="bucketPagingButtonNextPage" href="javascript:#local.pagingLinkNext#"><strong>Next</strong></a></li>
								</cfif>
							</ul>
						</div>
						<div class="hidden-desktop mobileViewPager">
							<ul class="pager">								
								<li><span>#local.currentPageLabel#</span></li>
								<li style="margin-top:2px;">
									<cfif arguments.NumCurrentPage gt 1>
										<a class="bucketPagingButtonPreviousPage" href="javascript:#local.pagingLinkPrevious#" title="Previous Page">&lt;&lt;</a>
									</cfif>
									<cfif arguments.NumCurrentPage lt arguments.NumTotalPages>
										<a class="bucketPagingButtonNextPage" href="javascript:#local.pagingLinkNext#" title="Next Page">&gt;&gt;</a>
									</cfif>
								</li>
							</ul>
						</div>
					</div>					
				<cfelse>
					<cfset local.pagingLinkFirst = "b_doSearchPN(#arguments.searchid#,1,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">
					<cfset local.pagingLinkLast = "b_doSearchPN(#arguments.searchid#, #(variables.thisBucketMaxPerPage * (arguments.NumTotalPages-1)) + 1#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">

					<div class="text-center">
						<div class="pagination" style="margin:10px 0px 0px;">
							<ul>
								<cfif arguments.NumCurrentPage gt 1>
									<li><a href="javascript:#local.pagingLinkFirst#" title="First Page" class="bucketPagingButtonFirstPage">&lt;&lt;</a></li>
									<li><a href="javascript:#local.pagingLinkPrevious#" title="Previous Page" class="bucketPagingButtonPreviousPage">&lt;</a></li>
								</cfif>

								<cfloop index="local.i" from="#local.startPage#" to="#local.endPage#">
									<cfset local.thisStartRow = local.i>
									<cfif local.i gt 1>
										<cfset local.thisStartRow = (variables.thisBucketMaxPerPage * (local.i-1)) + 1>
									</cfif>
									<cfset local.thisPageLink = "b_doSearchPN(#arguments.searchid#,#local.thisStartRow#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">
									<cfif arguments.NumCurrentPage eq local.i>										
										<li><span class="text-error"><strong>#local.i#</strong></span></li>
									<cfelse>
										<li <cfif Abs(arguments.NumCurrentPage - i) gt 1>class="hidden-phone hidden-tablet"</cfif>><a href="javascript:#local.thisPageLink#">#local.i#</a></li>
									</cfif>
								</cfloop>
									
								<cfif arguments.NumCurrentPage lt arguments.NumTotalPages>
									<li><a href="javascript:#local.pagingLinkNext#" title="Next Page" class="bucketPagingButtonNextPage">&gt;</a></li>
									<li><a href="javascript:#local.pagingLinkLast#" title="Last Page" class="bucketPagingButtonLastPage">&gt;&gt;</a></li>
								</cfif>
							</ul>
						</div>
						<div>#local.currentPageLabel#</div>
					</div>
				</cfif>	
			</cfif>
			<cfif arguments.topOrBottom eq "top">
				<div>
					<p style="margin-bottom:6px;">
						<b>#numberformat(arguments.itemCount)#</b> #arguments.itemword#<cfif arguments.itemCount is not 1>#arguments.itemwordSuffix#</cfif> found
					</p>
					<cfif ArrayLen(arguments.arrSort) and arguments.itemCount gt 1>
						<cfsavecontent  variable="local.sortLinks">
							<cfloop from="1" to="#arraylen(arguments.arrSort)#" index="local.arrEl">
								<cfset local.sortIcon = "">							
								<cfif listLen(arguments.arrSort[local.arrEl],"|") gt 2 and len(trim(arguments.currentSort)) and arguments.currentSort eq getToken(arguments.arrSort[local.arrEl],1,"|")> 
									<cfif getToken(arguments.arrSort[local.arrEl],3,"|") eq "ASC">
										<cfset local.sortIcon = "icon-caret-down">
									<cfelse>
										<cfset local.sortIcon = "icon-caret-up">
									</cfif>
								</cfif>
								<li>
									<a href="javascript:b_doSearchSort(#arguments.searchid#,'#GetToken(arguments.arrSort[local.arrEl],1,"|")#<cfif listLen(arguments.arrSort[local.arrEl],"|") gt 2>|#GetToken(arguments.arrSort[local.arrEl],3,"|")#</cfif>'<cfif len(#arguments.filter#)>,'#arguments.filter#'</cfif><cfif len(#arguments.postfilter#)>,'#arguments.postfilter#'</cfif>);" 
										class="<cfif GetToken(arguments.arrSort[local.arrEl],1,"|") eq arguments.currentSort>currSort</cfif>">
										#GetToken(arguments.arrSort[local.arrEl],2,"|")# <i class="#local.sortIcon#" style="vertical-align:middle;"></i>
									</a>
								</li>
							</cfloop>
						</cfsavecontent>
						<div id="searchResultsSortOptions">
							<div class="visible-desktop">
								<ul class="nav nav-pills" style="margin:0;">
									<li class="disabled"><a href="javascript:void(0)" style="padding:8px 0px;">Sort by:</a></li>
									#local.sortLinks#
								</ul>
							</div>
							<div class="hidden-desktop">
								<div class="btn-group">
									<button class="btn btn-small dropdown-toggle" data-toggle="dropdown">Sort by: <span class="caret"></span></button>
									<ul class="dropdown-menu">
										#local.sortLinks#
									</ul>
								</div>
							</div>
						</div>
					<cfelseif arguments.numTotalPages gt 1>
						<div class="hidden-desktop" style="min-height:26px"></div>
					</cfif>
				</div>
			</cfif>			
		</div>
	</div>
</div>
</cfoutput>