<cfcomponent extends="model.customPage.customPage" output="false">
	<cfset variables.objCustomPageUtils = createObject("component","model.customPage.customPageUtils")>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			
			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Click Here to Begin" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="Pay profile code for CC", value="CC" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentProfileCodeCheck", type="STRING", desc="Pay profile code for Pay by Check", value="Paybyck" };
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="FormIntro", type="CONTENTOBJ", desc="Form Introduction", value="CELA exists to protect and expand the legal rights and opportunities of all California workers and to strengthen the community of lawyers who represent them. We accomplish this through education and advocacy for worker justice. In determining whether CELA membership and its privileges will be provided to an applicant, CELA will review and consider various factors, including but not limited to your statements under penalty of prejury, any public records and court filings that may reflect an employment law defense practice, your or your firm's practice of soliciting and advertising employment defense litigation work, and input from CELA members. We will also consider your responses to possible follow-up questions we may have in the course of our review of your application." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="MembershipType", type="CONTENTOBJ", desc="Membership Type Intro", value="Per the information provided on the previous page, below is your CELA Membership options." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="AgreementConfirmaiton", type="CONTENTOBJ", desc="Agreement Confirmation section top content", value="I certify that the statements I made herein are true. By providing my mailing address, email address, telephone and fax number, I agree to receive communications sent by or on behalf of the California Employment Lawyers Association via mail, email, telephone or fax." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="ProfessionalLicense", type="CONTENTOBJ", desc="Professional License", value="Please add all State Licensures. For Other State, please include state abbrevation in the License Number along with number (if known).  You can add several entries in the Other State License Number. You must enter the 'earliest' License Date for the Other State Active Date field." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you. Your membership application has been submitted for review. You will receive an email from California Employment Lawyers Association once your application has been approved and processed.<br></br>If you selected to Pay by check, please remit your check along with a copy of your email confirmation notice to:  CELA, 5955 De Soto Ave., Suite 136, Woodland Hills, CA  91367.  Your Membership application will not be reviewed or approved until payment has been received." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="RequiredStatement", type="CONTENTOBJ", desc="Required Reminder Statement", value="* Required Data" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Active Subscription found error message", value="California Employment Lawyers Association records indicate you are currently a CELA member. Please click <a href='/?pg=login'>here</a> to log in."}; 
				arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="ErrorAcceptedSubscriptionFound", type="CONTENTOBJ", desc="Accepted Subscription found error message", value="California Employment Lawyers Association records indicate you are currently a CELA member. Please click <a href='/?pg=login'>here</a> to log in." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Link to use when billed subscription found", value="/?pg=manageSubscriptions&suba=renew" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			
				local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
				
			local.tmpField = { name="ArbitratorOrMediatorWarning", type="CONTENTOBJ", desc="Arbitrator/Mediator Warning", value="If you are a full time Arbitrator/Mediator, click here to fill out a different form for the CELAMarketplace instead.  If you are not a full time Arbitrator or Meditator, then change this percentage to less than 100 and add a percentage for another practice area as well to total 100% as required for CELA Membership." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="confirmationCheckBoxText", type="CONTENTOBJ", desc="confirmationCheckBoxText", value="I understand that my practice percentages & employer caseload determine whether I qualify for membership and am eligible for listserv & Wiki/Brief Bank access" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="PracticeCertificatonBody", type="CONTENTOBJ", desc="Practice Certificaton Section Top Text", value="Please provide the approximate percentages of your professional time devoted to representing clients. Enter 0% for any fields that do not apply.&nbsp;<strong><span style='color:##FF0000;'>These percentages MUST add up to 100% and will determine what Member Level you are granted with CELA.</span></strong></span>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);

			variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
			StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmJoin',
				formNameDisplay='Membership Application',
				orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
				memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
			));
		
			variables.useHistoryID = 0;
			if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
				variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
			if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
				variables.useHistoryID = int(val(session.useHistoryID));
				
			variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MembershipApplicationHistory', subName='Started');
			variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MembershipApplicationHistory', subName='Completed');
			variables.historyStartedText = "Member started Membership form.";
			variables.historyCompletedText = "Member completed Membership form.";
			
			switch (local.formAction) {
				case "processLookup":
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn AND NOT arguments.event.valueExists('isBack')) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":						
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember',rc=arguments.event.getCollection());
							break;				
					}
					break;
				case "processMembershipInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;
					}
					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			
			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica;overflow: hidden; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
			
				.radio input[type="radio"] {   
					-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{
					-webkit-appearance: checkbox !important;
				}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				
				.acntLookUpBtn,.acntLookUpMessage{
					display:inline!important;
					float: left;
					width: 48%!important;
				}
				.acntLookUpBtn{	margin-right: 5px; }
				.acntLookUpMessage .span12{
					margin-left:0px !important;
				}
				@media screen and (min-width: 632px) and (max-width: 980px){
					.acntLookUpBtn {
						margin-top: 52px;
					}
				}
				@media screen and (min-width: 980px){
					.acntLookUpBtn {
						margin-top: 45px;
					}
				}				
				.acntLookUpBtn  {
					position: absolute;
					width: 50%;
					height: 100%;
					min-height: 100%;
				}
				.centerit {
					position: absolute;
					top: 50%;
					width: 100%;
				}
				.centerit button {
					position: relative;
					top: -15px;
					height: 30px;
				}
				.center-holder{
					position: relative;
				}
				.acntLookUpMessage {
					margin-left: 48%!important;
				}
				@media screen and (min-width: 0px){
					.acntLookUpBtn {
						margin-top: 0px!important;
					}
				}
				@media screen and (min-width: 359px) and (max-width: 368px){
					.acntLookUpBtn button{
						font-size:13px;
					}
				}
				@media screen and (max-width: 359px){
					.acntLookUpBtn button{
						font-size:11px;
					}
				}						
				
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;

				}
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">

				$(function() {
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});
						}
					});
				});

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
				}

				function selectMember() {
					var windowWidth = $(window).width();
					var _popupWidth = 550;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					} 
					$.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}

				function resizeBox(newW,newH) {
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
						
				var step = 0;
				var prevStep = 0;

				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function toggleFTM() {
				}

				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							mc_loadDataForForm($('###variables.formName#'),'previous');			        	
						}
					} else {
						step = 1;
					}
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
					$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText').addClass('BodyText');
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="customTable form-horizontal">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
				<div class="CPSection row-fluid BodyText">
					<div class="CPSectionTitle BB">#variables.strPageFields.AccountLocatorTitle#</div>
					<div class="frmRow1 row-fluid" style="padding:10px;">
						<p>#variables.strPageFields.AccountLocatorIntroText#</p>
						<div class="row-fluid center-holder">
							<div class="span6 acntLookUpBtn c">
								 <div class="centerit">
									<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
								</div>
							</div>
							<div class="span6 acntLookUpMessage pull-right">
								#variables.strPageFields.AccountLocatorInstructions#
							</div>
						</div>
					</div>
				</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.annualMembershipDuesUID = "c32b0dd0-f396-4944-8faf-01d81b741af5">

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.annualMembershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.annualMembershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.annualMembershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	
		<cfset local.memberInfoFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='25aefc62-f0c6-4ae6-bf76-15e7e98ee68d', mode="collection", strData=local.strData)>
		<cfset local.directorySettingsFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='656833dc-c57d-4f5e-a98a-b06e13ff3419', mode="collection", strData=local.strData)>
		<cfset local.socialMediaFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='c699829f-308b-4cec-9d42-91fb613b8cf7', mode="collection", strData=local.strData)>
		<cfset local.mailingAddressFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='78864eaa-a376-49e8-b9f3-c4f36cc0cf72', mode="collection", strData=local.strData)>
		<cfset local.homeAddressFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='a0b90b50-9d11-47bf-a149-2fb7f02acb3c', mode="collection", strData=local.strData)>
		<cfset local.addressPreferencesFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='ae3bde66-a2b6-4753-902a-c2558ddd19a4', mode="collection", strData=local.strData)>
		<cfset local.additionalInfoFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='a9c749b4-0427-4fc3-b7ce-dbad90c37eef', mode="collection", strData=local.strData)>
		<cfset local.practiceCertificationFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='cc0118e2-4d57-4ded-812b-3a0c083d042d', mode="collection", strData=local.strData)>
		<cfset local.reverseAuctionAgreementFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='969F5A63-97A7-4389-8F46-77DCACF24026', mode="collection", strData=local.strData)>
		<cfset local.membershipCategoryFieldSetContent = variables.objCustomPageUtils.renderFieldSet(uid='01420bc8-fe12-411d-8321-b8551c896df9', mode="collection", strData=local.strData)>
		<cfset local.arbitrateId = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='8FDEE2CD-0FE6-41D4-9F30-AFE5FC927FEB').columnID>
		
		<!--- get Law School, Graduation Date --->
		<cfloop collection="#local.memberInfoFieldSetContent.strFields#" item="local.thisField">
			<cfif findNoCase("Law School (for Law Student applicants)", local.memberInfoFieldSetContent.strFields[local.thisField])>
				<cfset local.lawSchool = local.thisField>
			<cfelseif local.memberInfoFieldSetContent.strFields[local.thisField] eq "Law School Graduation Date">
				<cfset local.gradDate = local.thisField>
			</cfif>
		</cfloop>

		<!--- get Mailing Address --->
		<cfloop collection="#local.mailingAddressFieldSetContent.strFields#" item="local.thisField">
			<cfif local.mailingAddressFieldSetContent.strFields[local.thisField] eq "Address">
				<cfset local.memberMailAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<!--- get Home Address --->
		<cfloop collection="#local.homeAddressFieldSetContent.strFields#" item="local.thisField">
			<cfif local.homeAddressFieldSetContent.strFields[local.thisField] eq "Address">
				<cfset local.memberHomeAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<!--- get  Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.addressPreferencesFieldSetContent.strFields#" item="local.thisField">
			<cfif local.addressPreferencesFieldSetContent.strFields[local.thisField] eq "Select address to display in Membership Directory">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.addressPreferencesFieldSetContent.strFields[local.thisField] eq "Select the address you prefer for Billing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>		

		<cfset local.numberOfCasesField = ""/>
		<cfset local.attorneyNameField = ""/>
		<cfset local.caseNameField = ""/>
		<cfset local.emeritusPercentageField = ""/>
		<cfloop collection="#local.practiceCertificationFieldSetContent.strFields#" item="local.thisField">
			<cfif local.practiceCertificationFieldSetContent.strFields[local.thisField] eq "NUMBER OF CASES representing employer in a judicial or administrative forum?">
				<cfset local.numberOfCasesField = local.thisField>
			<cfelseif local.practiceCertificationFieldSetContent.strFields[local.thisField] eq "If currently representing employer(s), provide Plaintiff Attorney name(s)">
				<cfset local.attorneyNameField = local.thisField>
			<cfelseif local.practiceCertificationFieldSetContent.strFields[local.thisField] eq "and the case name(s)">
				<cfset local.caseNameField = local.thisField>
			</cfif>
			<cfif local.practiceCertificationFieldSetContent.strFields[local.thisField] eq "Emeritus Percentage">
				<cfset local.emeritusPercentageField = local.thisField>
			</cfif>
		</cfloop>	

		<!--- get Membership Category --->
		<cfset local.memberCatNum = "">
		<cfloop collection="#local.membershipCategoryFieldSetContent.strFields#" item="local.thisField">
			<cfif local.membershipCategoryFieldSetContent.strFields[local.thisField] eq "Membership Category you qualify for">
				<cfset local.memberCatNum = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>
		
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
				.radio input[type="radio"] {   
					-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{
					-webkit-appearance: checkbox !important;
				}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
					.CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
					##reverseAuctionAgreementSection > table { width : 100% !important; }
				}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{
					max-width:435px;
				}
				
				.customForm .radio.subRateLabel{
					display:block;
				}
				@media screen and (max-width:767px){
					.collapsible-table td{display:flex;border:none}
					.collapsible-table td.visible-phone{display:block!important;}
					.collapsible-table tr{border:1px solid grey;}
				}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;

				}
				@media screen and (max-width:980px) and (min-width:900px){
					input[type=text],select,.ui-multiselect{width:300px;}
				}
				.confirmationCheckBoxText input{
					vertical-align: text-top;
					margin-right: 5px;
					margin-top: 3px;
				}
				.confirmationCheckBoxText{
					margin:0 !important;
					font-family: Roboto, sans-serif;
					font-size: 16px;
					font-weight: bold;
					color: ##FF0000;
				}
				##reverseAuctionAgreementSection > table { width : 80%; margin-left: auto; margin-right: auto; }
			</style>
			
			<script language="javascript">
				$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText').addClass('BodyText');

				var _ll = $('##practiceCertificationFields input[type=text]').length-3;//3 last fields are not for percentage calculation
			
				for(var i=0;i <_ll;i++){
					if($.trim($('##practiceCertificationFields input[type=text]')[i].value) == 0){
						$('##practiceCertificationFields input[type=text]')[i].value = 0;
					}											
				}

				$('##addressPreferencesFieldSet select option:contains("Alternate")').remove();
				$('##addressPreferencesFieldSet select option:contains("Physical")').remove();

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var mcSel = '';
					<cfif len(trim(local.memberCatNum))>
						mcSel = $('###variables.formName# ###local.memberCatNum# option:selected').text();
					</cfif>
					#local.memberInfoFieldSetContent.jsValidation#
					if($('##memberInfoFieldFields select option:contains("CELA Member"):selected').length && $.trim($('##memberInfoFieldFields td:contains("CELA Member, please provide name")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'If you select CELA Member, please provide name is required.';
					}
					#local.directorySettingsFieldSetContent.jsValidation#
					#local.additionalInfoFieldSetContent.jsValidation#
					#local.mailingAddressFieldSetContent.jsValidation#
					#local.homeAddressFieldSetContent.jsValidation#
					#local.addressPreferencesFieldSetContent.jsValidation#
					
					if(($('##confirmationCheckBox:checked').length == 0 && mcSel.length == 0) || ($('##confirmationCheckBox:checked').length == 0 && mcSel != 'Paralegal')){
						arrReq[arrReq.length] = 'Please check the practice percentage and employer caseload acknowledgement to proceed.';
					}
					#local.socialMediaFieldSetContent.jsValidation#
					#local.practiceCertificationFieldSetContent.jsValidation#					
					
					var _l = $('##practiceCertificationFields input[type=text]').length-3;//3 last fields are not for percentage calculation
					var _total = 0;
					for(var i=0;i <_l;i++){
						var _value = $('##practiceCertificationFields input[type=text]')[i].value;
						if(!isNaN(_value)){
							_total = _total + parseFloat(_value);
						}
					}
					if(_total < 100) {
						arrReq[arrReq.length] = 'Your percentages do not add up to 100. Please update so total of all percentages equal 100.';
					}
					if(_total > 100) {
						arrReq[arrReq.length] = 'Your percentages add up to be over 100. Please update so total of all percentages equal 100.';
					}
					var arbitrateVal = parseInt($('##md_#local.arbitrateId#').val());
					if(arbitrateVal >= 100){
						arrReq[arrReq.length] = '#variables.strPageFields.ArbitratorOrMediatorWarning#';					
					}

					#replace(local.reverseAuctionAgreementFieldSetContent.jsValidation,"Your Name is required","Your Name is required for the Reverse Auction Policy")#
					#local.membershipCategoryFieldSetContent.jsValidation#				
					
					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					
					if(prof_license != "" && prof_license != null){
						isProfLicenseSelected = true;
						$('[name=multiselect_mpl_pltypeid]').each(function(){
							var val = $(this).val();
							if($("##mpl_"+val+"_licensenumber").val() != undefined) {
								var text = $(this).attr('title');
								if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
								if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
							}
						});
					} 

					if($.trim($(_CF_this['#local.numberOfCasesField#']).val()).length > 0 && $.trim($(_CF_this['#local.numberOfCasesField#']).val()) != 0){
						if(!_CF_hasValue(_CF_this['#local.attorneyNameField#'], "TEXT", false)) arrReq[arrReq.length] = "If currently representing employer(s), provide Plaintiff Attorney name(s) is required.";
						if(!_CF_hasValue(_CF_this['#local.attorneyNameField#'], "TEXT", false) && !_CF_hasValue(_CF_this['#local.caseNameField#'], "TEXT", false)) arrReq[arrReq.length] = "and the case name(s) is required.";
						else if(!_CF_hasValue(_CF_this['#local.caseNameField#'], "TEXT", false))  arrReq[arrReq.length] = "The case name(s) is required.";
					}					
					
					<cfif len(trim(local.memberCatNum))>
						if(mcSel == 'Attorney' || mcSel == 'Full Time Arbitrator/Mediator' || mcSel == 'Full Time Law Professor' || mcSel == 'Non-Profit Group Membership'|| mcSel == 'Non-Profit Individual Member') {
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								var isCaliforniaSelected = false;
								$('[name=multiselect_mpl_pltypeid]:checked').each(function(){
									if($(this).attr('title') == 'California'){
										isCaliforniaSelected = true;
										return;
									}
								});
						
								if(!isCaliforniaSelected) {
									arrReq[arrReq.length] = 'Select California state in Professional License for ' + mcSel + ' Membership.';
								}
							} else {
								arrReq[arrReq.length] = 'Professional License is required.';
							}

						}
						if (mcSel == 'Emeritus') {
							var hasInactiveStatus = 0;
							if(prof_license != "" && prof_license != null){
								$('[name=multiselect_mpl_pltypeid]').each(function(){
									if($("##mpl_"+$(this).val()+"_status").val() == 'inactive') {
										hasInactiveStatus = 1;
									}
								});
								if(hasInactiveStatus == 0){
									arrReq[arrReq.length] = 'Professional License in the inactive status is required.';
								}
							} else {
								arrReq[arrReq.length] = 'Professional License in the inactive status is required.';
							}
							if($.trim($(_CF_this['#local.emeritusPercentageField#']).val()) != 100){
								arrReq[arrReq.length] = 'Emeritus Percentage must equal 100%.';
							}
						}
						if (mcSel == 'Law Student') {
							<cfif (isDefined("local.lawSchool") and len(trim(local.lawSchool))) AND (isDefined("local.gradDate") and len(trim(local.gradDate)))>
								if ($.trim($('###variables.formName# ###local.lawSchool#').val()) == '' || $.trim($('###variables.formName# ###local.gradDate#').val()) == '') arrReq[arrReq.length] = "Enter Law School and Graduation Date.";
							<cfelse>
								<cfif isDefined("local.lawSchool") and len(trim(local.lawSchool))>
									if ($.trim($('###variables.formName# ###local.lawSchool#').val()) == '') arrReq[arrReq.length] = "Law School is required.";
								</cfif>
								<cfif isDefined("local.gradDate") and len(trim(local.gradDate))>
									if ($.trim($('###variables.formName# ###local.gradDate#').val()) == '') arrReq[arrReq.length] = "Law School Graduation Date is required.";
								</cfif>
							</cfif>
						} 
					</cfif>				
														

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>
					confirmationCheckBoxText = $('.confirmationCheckBoxWrap').html();
					if($('##practiceCertificationFields').find('tr[id^="MC_fieldSetDesc_"]').length)
						$('##practiceCertificationFields').find('tr[id^="MC_fieldSetDesc_"]').children('td').append(confirmationCheckBoxText);
					else
						$('##practiceCertificationFields').find('[id="PracticeCertificatonBody"]').append(confirmationCheckBoxText);

					$("##mpl_pltypeid").multiselect({
						header: false,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							if(ui.checked){
								$("##state_table").show();
								$('##state_table tbody').append('<tr id="tr_state_'+ui.value+'">'
								+'<td class="visible-phone">State Name:</td>'
																+'<td align="right" class="tsAppBodyText">'+ui.text+'</td>'
																+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseNumberLabel#:</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" class="tsAppBodyText"/>'
																+'	<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
																+'</td>'
																+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy):</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="tsAppBodyText" />'
																+'</td>'
																+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseStatusLabel#</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="active">Active</option><option value="inactive">Inactive</option></select>'
																+'</td>'
														+'</tr>');
								if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
									mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
								}
								$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
								}else{
								$("##tr_state_"+ui.value).remove();
							}
					   },
					});		
					responsiveCorrection();
				});
				$(window).on("load resize scroll",function(e){
					responsiveCorrection();
				});
				function responsiveCorrection(){
					var _width = $(window).width() ;
					if(_width <= 900){
						var _adjustedWidth = _width - 70;
						$('.form-horizontal div.CPSectionContent > table tr > td').css('display','flex');
						$('.form-horizontal div.CPSectionContent table tr > td:nth-child(2)').css({'display':'inline-block','white-space':'inherit'});
						$('.form-horizontal div.CPSectionContent > table tr > td:nth-child(1)').css('display','inline');
						$('.form-horizontal div.CPSectionContent > table tr > td:nth-child(3)').hide();
						$('.form-horizontal div.CPSectionContent > table tr > td,.form-horizontal div.CPSectionContent table tr > td *,.form-horizontal div.CPSectionContent table tr > td input,.form-horizontal div.CPSectionContent table tr > td select,.ui-multiselect').css('max-width',_adjustedWidth+'px');

						$('.form-horizontal div.CPSectionContent table tr > td,.form-horizontal div.CPSectionContent table tr > td *').css('white-space','inherit');
						$('.form-horizontal div.CPSectionContent.membershipCategoryFieldSet table tr > td table td').css('display','table-cell');
					} else {
						$('.form-horizontal div.CPSectionContent > table tr > td').css('display','table-cell');
					}
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()" class="form-horizontal customForm">
				<input type="hidden" name="fa" id="fa" value="processMemberInfo">
				<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
				<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
				<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				
				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
				
				<cfif len(variables.strPageFields.RequiredStatement)>
					<div>#variables.strPageFields.RequiredStatement#</div>
					<br/>
				</cfif>
				
				<div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.memberInfoFieldSetContent.fieldSetTitle#</div>
						<div class="PT BodyText">#variables.strPageFields.FormIntro#</div>
						<div class="PT CPSectionContent" id="memberInfoFieldFields">
							#local.memberInfoFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.directorySettingsFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.directorySettingsFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.additionalInfoFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.additionalInfoFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.mailingAddressFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.mailingAddressFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.homeAddressFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.homeAddressFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.addressPreferencesFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent" id="addressPreferencesFieldSet">
							#local.addressPreferencesFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.socialMediaFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent">
							#local.socialMediaFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">Professional License Information</div>
						<div class="PT BodyText">#variables.strPageFields.ProfessionalLicense#</div>
						
						<div class="PT CPSectionContent">
							<div class="control-group">
								<label class="control-label" for="mpl_pltypeid">Professional License:</label>
								<div class="controls span6">
									<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
										<cfloop query="local.qryOrgPlTypes">
											<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
											<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
										</cfloop>
									</select>
								</div>
							</div>
							<div>	
								<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0" class="table collapsible-table">
									<thead class="hidden-phone">
										<tr valign="top">
											<th align="center" >State Name</th>
											<th align="center" >#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
											<th align="center" >#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
											<th align="center" >#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
										</tr>
									</thead>
									<tbody>
									<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
										<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
											<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
											<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
											<cfset  local.license_status  = local.strData['mpl_#local.thisItem#_status']>
											<tr id="tr_state_#local.thisItem#">
												<td class="visible-phone">State Name:</td>
												<td align="right" class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</td>
												<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseNumberLabel#:</td>
												<td align="center" class="tsAppBodyText">
													<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
													<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
												</td>
												<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy):</td>
												<td align="center" class="tsAppBodyText">
													<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" />
													<cfsavecontent variable="local.datejs">
														<cfoutput>
														<script language="javascript">
															$(document).ready(function() { 
																mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
															});
														</script>
														<style type="text/css">
														##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
														</style>
														</cfoutput>
													</cfsavecontent>
													<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
												</td>
												<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseStatusLabel#:</td>
												<td align="center" class="tsAppBodyText">
													<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status"><option value="active">Active</option><option value="inactive" <cfif local.license_status eq 'inactive'>selected="selected"</cfif> >Inactive</option></select>
												</td>
											</tr>
										</cfloop>
									</cfif>									
									</tbody>
								</table>
										
							</div>
						</div>
					</div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.practiceCertificationFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent" id="practiceCertificationFields">
							<div class="hide confirmationCheckBoxWrap">
								<div class="span12 confirmationCheckBoxText">
									<label class="checkbox">
										<input type="checkbox" name="confirmationCheckBox" id="confirmationCheckBox">
										#variables.strPageFields.confirmationCheckBoxText#
									</label>
								</div>
							</div>
							<div id="PracticeCertificatonBody">#variables.strPageFields.PracticeCertificatonBody#</div>
							#local.practiceCertificationFieldSetContent.fieldSetContent#
						</div>
					</div>
					
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.reverseAuctionAgreementFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent" id="reverseAuctionAgreementSection">
							#local.reverseAuctionAgreementFieldSetContent.fieldSetContent#
						</div>
					</div>
					
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle BB">#local.membershipCategoryFieldSetContent.fieldSetTitle#</div>
						<div class="PT CPSectionContent membershipCategoryFieldSet">
							#local.membershipCategoryFieldSetContent.fieldSetContent#
						</div>
					</div>
					<div class="frmButtons">
						<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
					</div>
				</div>			
			</form>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->		
		<cfset local.memberShipCategoryColumnName = "Membership Category">
		<cfset local.memberShipCategoryStruct = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName=local.memberShipCategoryColumnName)>
		<cfset local.membershipCategoryColumnId = local.memberShipCategoryStruct.columnId>
		<cfset local.contactTypeValue = "">
		<cfif local.membershipCategoryColumnId NEQ '' AND local.membershipCategoryColumnId NEQ 0>
			<cfset local.membershipCategorySelected = arguments.rc['md_'&local.membershipCategoryColumnId]>
			<cfset local.membershipCategorySelectedValue = variables.objCustomPageUtils.mem_getCustomFieldValueFromValueID(variables.orgid,local.memberShipCategoryColumnName,local.membershipCategorySelected)>
			
			<cfif ListFindNoCase("Attorney,Full Time Arbitrator/Mediator",local.membershipCategorySelectedValue)>
				<cfset local.contactTypeValue = "Attorney">
			<cfelseif ListFindNoCase("Non-Profit Group Membership,Non-Profit Individual Member",local.membershipCategorySelectedValue)>
				<cfset local.contactTypeValue = "Attorney,Non-Profit">
			<cfelseif local.membershipCategorySelectedValue EQ "Full Time Law Professor">
				<cfset local.contactTypeValue = "Attorney,Law School Professor">			
			<cfelseif local.membershipCategorySelectedValue EQ "Legal Staff" OR local.membershipCategorySelectedValue EQ "Paralegal">
				<cfset local.contactTypeValue = "Paralegal">
			<cfelseif local.membershipCategorySelectedValue EQ "Law Student">
				<cfset local.contactTypeValue = "Law Student">
			<cfelseif local.membershipCategorySelectedValue EQ "Emeritus">
				<cfset local.contactTypeValue = "Emeritus">
			</cfif>
		</cfif>
	
		<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
	
		<cfif local.strResult.success>
			<cfset local.objSaveMember = variables.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
			<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
			<cfset local.strResult1 = local.objSaveMember.saveData()>

			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = variables.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>		

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>	

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '597a90e6-5eec-4052-954c-dd8c557890bf')>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = variables.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>
 		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
				<style>
					body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
					h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
					div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
					div.CPSectionNoBottom { border:0.5px solid ##666666; }
					div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
					div.CPSection div.BB { border-bottom:1px solid ##666666; }
					div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
					div.CPSection span.frmText span.block { display:block; }
					div.CPSection span.frmText span.b { font-weight:bold; }
					div.CPSection td.r { text-align:right; }
					div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
					.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
					.CPSectionContent table td{padding-bottom: 15px;}
					.radio input[type="radio"] {   
						-webkit-appearance: radio !important;}
					.checkbox input[type="checkbox"]{
						-webkit-appearance: checkbox !important;
					}
					.b{ font-weight:bold; }
					.c { text-align:center; }
					.PT { padding:10px;}
					@media screen and (max-width:901px){
						.CPSectionContent table td{padding-bottom: 0px;}
						.CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
					}
					@media screen and (max-width: 979px){ 
						.input-xxlarge{width:100%;}
					}
					@media screen and (max-width: 420px){
						input[type="date"],
						input[type="datetime"],
						input[type="email"],
						input[type="number"],
						input[type="password"],
						input[type="search"],
						input[type="text"],
						select,
						textarea {
							font-size: 16px!important;
						}
					}
					.customForm input[type="text"],.customTable select{
						max-width:435px;
					}
					.customForm .radio.subRateLabel{
						display:block;
					}
					div{
						-moz-box-sizing: border-box;
						-ms-box-sizing: border-box;
						-o-box-sizing: border-box;
						-webkit-box-sizing: border-box;
						box-sizing: border-box;
					}
				</style>
				
				<script type="text/javascript">
					$(document).ready(function(){
						$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText').addClass('BodyText');
					});

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);					
					}
					function validateMembershipInfoForm(){
						var arrReq = new Array();				        
						
						<cfif val(local.subscriptionID)>
							if($("*[id^='sub#local.subscriptionID#_rate']").length <= 1 || ($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){

								arrReq[arrReq.length] = " Select Membership.";
							}
						</cfif>	
						
						if (!$('###variables.formName# ##TrueCertification').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";
						
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
						#local.result.jsValidation#	

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfhtmlhead text="#local.resultHtmlHeadText#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()" class="form-horizontal">
				<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
				<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
				<cfloop collection="#session.formFields.step1#" item="local.thisField">
					<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
						or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
						or left(local.thisField,5) eq "mccf_">
						<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
						<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
					</cfif>
				</cfloop>
				<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
				<cfinput type="hidden" name="isBack" id="isBack" value="1">
				<cfinclude template="/model/cfformprotect/cffp.cfm">

				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
				<div>
					<div class="CPSection row-fluid BodyText">
						<div class="CPSectionTitle BB">Membership Type</div>
						<div class="PT">#variables.strPageFields.MembershipType#</div>
						<div class="CPSectionContent PT">
							#local.result.formcontent#
						</div>
					</div>	
					<div class="CPSection row-fluid BodyText">
						<div class="CPSectionTitle BB">Agreement Confirmation</div>
						<div class="PT">#variables.strPageFields.AgreementConfirmaiton#</div>
						<div class="row-fluid CPSectionContent PT">
							<div id="TrueCertification">
								<label class="checkbox subLabel" for="TrueCertification">
									<input class="subCheckbox" type="checkbox" name="TrueCertification" id="TrueCertification" value="1">
									I verify all information contained in this membership application is current
								</label>
							</div>
							
						</div>
					</div>	
					<div>
						<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
						<button name="btnBack" id="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
					</div>
				</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '597a90e6-5eec-4052-954c-dd8c557890bf')>
		<cfset local.strResult = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '597a90e6-5eec-4052-954c-dd8c557890bf')>

		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.frequencyShortName = "F">
		<cfset local.frequencyUID = arguments.rc["SUB#local.subscriptionID#_RATEFREQUENCYSELECTED"]>
		<cfif len(local.frequencyUID)>
			<cfquery name="local.qrySelectedFreq" datasource="#application.dsn.memberCentral.dsn#">
				select frequencyShortName from sub_frequencies  where uid = <cfqueryparam value="#local.frequencyUID#" cfsqltype="cf_sql_varchar" />
			</cfquery>

			<cfif len(trim(local.qrySelectedFreq.frequencyShortName))>
				<cfset local.frequencyShortName = trim(local.qrySelectedFreq.frequencyShortName)>
			</cfif>
		</cfif>
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC,variables.strPageFields.PaymentProfileCodeCheck ]>
		
		<cfset local.strReturn = 
			variables.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#
			<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
				.radio input[type="radio"] {   
					-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{
					-webkit-appearance: checkbox !important;
				}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
					.CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{
					max-width:435px;
				}
				
				.customForm .radio.subRateLabel{
					display:block;
				}
				.customForm .tsAppSectionHeading{
					background:##990000;
					color: ##FFF;
				}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;
				}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<div class="customForm">				
				<div class="CPSection row-fluid BodyText">
					<div class="CPSectionTitle">Membership Selections Confirmation</div>
					<div class="row-fluid CPSectionContent PT">
						#local.strResult.formContent#
					</div>
				</div>
				<div class="CPSection row-fluid BodyText">
					<div class="CPSectionTitle">Total Price</div>
					<div class="row-fluid CPSectionContent PT">
						<div class="span4">Total Amount Due:</div>
						<div class="span4">#dollarFormat(local.strResult.totalFullPrice)#</div>
					</div>
				</div>
				<cfif local.paymentRequired>
					<div class="CPSectionNoBottom row-fluid">
						#local.strReturn.paymentHTML#
					</div>
				<cfelse>
					<div>
						<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();" disabled>Continue</button>
						<button name="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
					</div>
				</cfif>
			</div>
						
			<script>
				$(document).ready(function(){
					$('.tsAppBodyText,label,span.subLabel,.control-label,.disclaimer').removeClass('tsAppBodyText').addClass('BodyText');
					setTimeout(function() {
						$('button').attr('disabled',false);
					}, 1200);
				});
			</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset variables.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.memberInfoFieldSetContent 				= variables.objCustomPageUtils.renderFieldSet(uid='25aefc62-f0c6-4ae6-bf76-15e7e98ee68d', mode="confirmation", strData=arguments.rc)>
		<cfset local.directorySettingsFieldSetContent 		= variables.objCustomPageUtils.renderFieldSet(uid='656833dc-c57d-4f5e-a98a-b06e13ff3419', mode="confirmation", strData=arguments.rc)>
		<cfset local.socialMediaFieldSetContent 			= variables.objCustomPageUtils.renderFieldSet(uid='c699829f-308b-4cec-9d42-91fb613b8cf7', mode="confirmation", strData=arguments.rc)>
		<cfset local.mailingAddressFieldSetContent 			= variables.objCustomPageUtils.renderFieldSet(uid='78864eaa-a376-49e8-b9f3-c4f36cc0cf72', mode="confirmation", strData=arguments.rc)>
		<cfset local.homeAddressFieldSetContent 			= variables.objCustomPageUtils.renderFieldSet(uid='a0b90b50-9d11-47bf-a149-2fb7f02acb3c', mode="confirmation", strData=arguments.rc)>
		<cfset local.addressPreferencesFieldSetContent 		= variables.objCustomPageUtils.renderFieldSet(uid='ae3bde66-a2b6-4753-902a-c2558ddd19a4', mode="confirmation", strData=arguments.rc)>
		<cfset local.additionalInfoFieldSetContent 			= variables.objCustomPageUtils.renderFieldSet(uid='a9c749b4-0427-4fc3-b7ce-dbad90c37eef', mode="confirmation", strData=arguments.rc)>
		<cfset local.practiceCertificationFieldSetContent 	= variables.objCustomPageUtils.renderFieldSet(uid='cc0118e2-4d57-4ded-812b-3a0c083d042d', mode="confirmation", strData=arguments.rc)>
		<cfset local.reverseAuctionAgreementFieldSetContent 	= variables.objCustomPageUtils.renderFieldSet(uid='969F5A63-97A7-4389-8F46-77DCACF24026', mode="confirmation", strData=arguments.rc)>
		<cfset local.membershipCategoryFieldSetContent 		= variables.objCustomPageUtils.renderFieldSet(uid='01420bc8-fe12-411d-8321-b8551c896df9', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '597a90e6-5eec-4052-954c-dd8c557890bf')>
		
		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<fieldset>
						<legend>Confirmation</legend>
						<div class="row-fluid tsAppSectionContentContainer BodyText">#variables.strPageFields.ConfirmationMessage#</br></div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<div class="row-fluid tsAppSectionContentContainer">
						<div class="span12 BodyText">#variables.strPageFields.ConfirmationMessage#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div class="row-fluid tsAppSectionContentContainer">
					<div class="span12 BodyText">You have received an application for membership.</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.memberInfoFieldSetContent.fieldSetContent#
				
				#local.directorySettingsFieldSetContent.fieldSetContent#
				#local.additionalInfoFieldSetContent.fieldSetContent#
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join CELA - Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#DateFormat(arguments.rc['mpl_#local.key#_activeDate'],'mm/dd/yyyy')#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_status']#</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br/>
				
				#local.socialMediaFieldSetContent.fieldSetContent#
				#local.mailingAddressFieldSetContent.fieldSetContent#
				#local.homeAddressFieldSetContent.fieldSetContent#
				#local.addressPreferencesFieldSetContent.fieldSetContent#
				#local.practiceCertificationFieldSetContent.fieldSetContent#
				#local.reverseAuctionAgreementFieldSetContent.fieldSetContent#

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join CELA - Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to CELA",
			emailhtmlcontent = local.confirmationHTMLToMember,
			siteID = variables.siteID,
			memberID = val(variables.useMID),
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
				.radio input[type="radio"] {   
					-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{
					-webkit-appearance: checkbox !important;
				}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
					.CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{
					max-width:435px;
				}
				
				.customForm .radio.subRateLabel{
					display:block;
				}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;

				}
				</style>
				</head>
				<body>
					#local.confirmationHTMLToMember#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset variables.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID)>	
		
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = variables.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to CELA", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationPageHTMLContent>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
				.radio input[type="radio"] {   
					-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{
					-webkit-appearance: checkbox !important;
				}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
					.CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{
					max-width:435px;
				}
				
				.customForm .radio.subRateLabel{
					display:block;
				}
				div{
					-moz-box-sizing: border-box;
					-ms-box-sizing: border-box;
					-o-box-sizing: border-box;
					-webkit-box-sizing: border-box;
					box-sizing: border-box;

				}
			</style>
			<div class="CPSection">
				<div class="CPSectionTitle">Thank you for your application</div>
				<div class="CPSectionContent PT">						
					#arguments.confirmationHTML#
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">
		<cfargument name="rc" type="struct" required="false">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<style>
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:0.5px solid ##666666; margin-bottom:15px; }
				div.CPSectionNoBottom { border:0.5px solid ##666666; }
				div.CPSection div.CPSectionTitle { font-size:14pt; height:auto; font-weight:bold; color:##ffffff; padding:10px; background:##990000; font-family: Calibri,Arial,Helvetica;}
				div.CPSection div.BB { border-bottom:1px solid ##666666; }
				div.CPSection span.frmText, div.CPSection td.frmText, div.CPSection div.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
				.CPSectionContent table td{padding-bottom: 15px;}
				.radio input[type="radio"] {   
					-webkit-appearance: radio !important;}
				.checkbox input[type="checkbox"]{
					-webkit-appearance: checkbox !important;
				}
				.b{ font-weight:bold; }
				.c { text-align:center; }
				.PT { padding:10px;}
				@media screen and (max-width:901px){
                    .CPSectionContent table td{padding-bottom: 0px;}
					.CPSection .BodyText label,.CPSection .BodyText{margin-bottom:5px;}
                }
				@media screen and (max-width: 979px){ 
					.input-xxlarge{width:100%;}
				}
				@media screen and (max-width: 420px){
					input[type="date"],
					input[type="datetime"],
					input[type="email"],
					input[type="number"],
					input[type="password"],
					input[type="search"],
					input[type="text"],
					select,
					textarea {
						font-size: 16px!important;
					}
				}
				.customForm input[type="text"],.customTable select{
					max-width:435px;
				}
				.customForm .radio.subRateLabel{
					display:block;
				}
			</style>
			<cfoutput>
			<cfif arguments.errorCode eq "failsavemember">
				<div class="CPSection">
					<div class="CPSectionTitle">There was an issue continuing with your application.</div>
					<div class="CPSectionContent PT">
						We were unable to save the member information provided. Please contact the association or try again later.
					</div>				
				</div>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="form-horizontal">
					<cfinput type="hidden" name="fa" id="fa" value="">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
					<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
					<cfloop collection="#arguments.rc#" item="local.thisField">
						<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
							or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
							or left(local.thisField,5) eq "mccf_">
							<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
						</cfif>
					</cfloop>
					<cfinput type="hidden" name="isBack" id="isBack" value="1">

					<div class="frmButtons" >
						<button name="btnBack" id="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
					</div>
				</cfform>
				
			<cfelse>
				<div class="CPSection">
					<div class="CPSectionTitle">There was an issue continuing with your application.</div>
					<div class="CPSectionContent PT">						
						<cfif arguments.errorCode eq "activefound">
							#variables.strPageFields.ErrorActiveSubscriptionFound#
						<cfelseif arguments.errorCode eq "acceptedfound">
							#variables.strPageFields.ErrorAcceptedSubscriptionFound#
						<cfelseif arguments.errorCode eq "billedjoinfound">
							#variables.strPageFields.BilledJoinMessage#	
						<cfelseif arguments.errorCode eq "billedfound">
							<script>
								window.location = '#Replace(Replace(variables.strPageFields.ErrorBilledSubscriptionFound,"<p>",""),"</p>","")#';
							</script>
						<cfelseif arguments.errorCode eq "failsavemembership">
							We were unable to process the membership information provided. Please contact the association or try again later.
						<cfelseif arguments.errorCode eq "failpayment">
							We were unable to process your selected payment method. Please contact the association or try again later.
						<cfelseif arguments.errorCode eq "spam">
							Your submission was blocked and will not be processed at this time.
						<cfelse>
							An error occurred. Please contact the association or try again later.
						</cfif>
					</div>				
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>