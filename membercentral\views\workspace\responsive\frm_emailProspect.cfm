<cfoutput>
<div>
	<div><b>Email Logs</b></div>
	<table id="tblProspectEmailLogs" class="table table-bordered">
	<tr>
		<th>Created / Sent</th>
		<th>Subject / Reply To</th>
		<th>Sent By</th>
		<th>Opened</th>
	</tr>
	<cfif attributes.data.qryEmailActivityLogs.recordCount>
		<!-- MCSkipMergeStart -->
		<cfloop query="attributes.data.qryEmailActivityLogs">
			<tr>
				<td>
					<div class="bn txtCenter">
						#dateFormat(attributes.data.qryEmailActivityLogs.dateEntered,"m/d/yyyy")# #timeFormat(attributes.data.qryEmailActivityLogs.dateEntered,"h:mm tt")#
						<cfif dateCompare(attributes.data.qryEmailActivityLogs.dateEntered,attributes.data.qryEmailActivityLogs.sendOnDate,'n')>
							<div class="bn2">							
								#dateFormat(attributes.data.qryEmailActivityLogs.sendOnDate,"m/d/yyyy")# #timeFormat(attributes.data.qryEmailActivityLogs.sendOnDate,"h:mm tt")#
								&nbsp;
							</div>
						</cfif>
						<cfif attributes.data.qryEmailActivityLogs.isProjectTemplate>
							<div class="bn2">
								<button type="button" name="btnReSendEmailToProspect" id="btnReSendEmailToProspect" class="btn btn-mini" onclick="resendEmailTaskProspect(#attributes.data.qryEmailActivityLogs.messageID#);"><i class="icon-mail-forward"></i> Resend</button>
							</div>
						</cfif>
					</div>
				</td>
				<td>
					<div class="bn">
						#htmleditformat(attributes.data.qryEmailActivityLogs.subject)#
						<div class="bn2">#attributes.data.qryEmailActivityLogs.replyToEmail# (#attributes.data.qryEmailActivityLogs.fromName#)</div>
						<div class="bn2">Sent To: #attributes.data.qryEmailActivityLogs.toEmail#</div>
					</div>
				</td>
				<td>#attributes.data.qryEmailActivityLogs.sentBy#</td>
				<td>
					<cfif not val(attributes.data.qryEmailActivityLogs.sg_open_total)>Not </cfif>Opened
				</td>
			</tr>
		</cfloop>
		<!-- MCSkipMergeEnd -->
	<cfelse>
		<tr><td colspan="4">No Email Logs Found</td></tr>
	</cfif>
	</table>
</div>

<div style="border-bottom:1px solid ##ddd;margin-bottom:10px;">
	<b>Email #attributes.data.labels.prospectFieldLabel#</b>
</div>

<div id="mc_tskprospect_err" class="alert" style="display:none;margin:6px 0;"></div>

<cfif attributes.data.qryEmailTemplates.recordcount>
	<form name="frmEmailProspect" id="frmEmailProspect" class="form-horizontal" method="post" action="#attributes.data.formLink#" onsubmit="return validateProspectEmailForm();">
		<div class="control-group">
			<label class="control-label" for="fEmailTemplateID">Select a template:</label>
			<div class="controls">
				<select name="fEmailTemplateID" id="fEmailTemplateID" style="width:300px;" onchange="loadEmailTemplateContent();">
					<option value="">Select a template</option>
					#attributes.data.emailTemplateOptions#
				</select>
				<span id="loadingTemp" style="margin-top:10px;display:none;"><i class="icon-spin icon-spinner"></i><b>Please Wait...</b></span>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label" for="recipientEmail">E-mail to:</label>
			<div class="controls">
				<input type="text" name="recipientEmail" id="recipientEmail" value="#attributes.data.qryMainEmail.email#" style="width:300px;" autocomplete="off">
			</div>
		</div>

		<div id="divPreviewEmailMessageLoading" class="text-center" style="display:none;"><i class="icon-spin icon-spinner"></i><b> Loading Preview...</b></div>

		<div id="divPreviewEmailMessage" style="margin:15px 0;">
			<div id="divTestEmail" style="border:1px dashed ##666;padding:10px;display:none;overflow:auto;height:530px;">
				<table cellpadding="1" cellspacing="0">
				<tr><td class="r">From:</td><td width="3"></td><td><span id="spFrom"></span></td></tr>
				<tr><td class="r">Subject:</td><td width="3"></td><td><span id="spSubject"></span></td></tr>
				</table>
				<div style="margin:12px 0;border-bottom:1px solid ##ccc;"></div>
				#application.objWebEditor.embed(objname="emailProspectContent", objValue="", tools="ContentEditor", LinkUpload=false, imageupload=attributes.data.allowImageUpload, flashupload=false, ImageBrowser=attributes.data.allowImageUpload)#
			</div>
		</div>

		<div id="divSendEmailBtnHold" class="r">
			<button type="button" class="btn" name="btnCancelEmailProspect" id="btnCancelEmailProspect" onclick="cancelEmailForm();">Cancel</button>
			<button type="button" class="btn" name="btnEmailProspect" id="btnEmailProspect" onclick="validateProspectEmailForm();" style="display:none;"><i class="icon-envelope"></i> Send Email</button>
		</div>
	</form>
<cfelse>
	<div>No Email Templates are defined in this Project Settings.</div>
</cfif>
</cfoutput>