﻿<cfscript>

	/* ************************* */
	/* Custom Page Custom Fields */
	/* ************************* */
	local.arrCustomFields = [];
	local.tmpField = { name="ProfileCode", type="STRING", desc="Profile Code", value="TNAJAuthorizeCIM" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="formNameDisplay", type="STRING", desc="Form name Display", value="Join An Area" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="OrgEmailRecipient", type="STRING", desc="Organization Email Recipient", value="<EMAIL>,<EMAIL>,<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="memberEmailFrom", type="STRING", desc="Member Email From", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
		formName='frmJoinAnArea',
		formNameDisplay='#local.strPageFields.formNameDisplay#',
		orgEmailTo='#local.strPageFields.OrgEmailRecipient#',
		orgEmailFrom='<EMAIL>',
		memberEmailFrom='#local.strPageFields.memberEmailFrom#'
	));

	local.objTNFunctions 				= createObject('component','generalFunctions');
	
	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	local.Organization				= arguments.event.getValue('mc_siteInfo.ORGShortName');
	
	// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
	local.profile_1._profileCode 	= '#local.strPageFields.ProfileCode#';
	local.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),profileCode=local.profile_1._profileCode);
	local.profile_1._description 	= "#local.organization# - #local.formNameDisplay#";
	// LOCAL VARIABLES: --------------------------------------------------------------------------------------------------
		
	local.currAoPSubscriberID 		= event.getValue('topAoPSub',0);
	local.errorCode = event.getValue('eCode',0);
	
	local.aopTypeUID = "3B9B4FE5-F5A0-4CBD-812B-72E7EAC3F6FE";
	local.aopListingUID = "A9117DC2-3813-4097-814E-FBCC94F42CA9";
	local.globalRate = 15;

	local.strPaymentForm = 	application.objPayments.showGatewayInputForm(	siteid=local.siteid,
																			profilecode=local.profile_1._profileCode,
																			pmid = local.memberID,
																			showCOF = local.memberID EQ session.cfcUser.memberData.memberID,
																			usePopupDIVName='paymentinfo');

	// LOCAL MEMBERDATA: -----------------------------------------------------------------------------------------------------
	
	local.memberData 				= application.objMember.getMemberInfo(local.memberID);
	

</cfscript>

<!--- Get member's Top Subscriptions --->
<cfset local.memberTopAoPSubs = local.objTNFunctions.getMemberSubsBySubID(siteID=local.siteID, memberID=local.memberID, subUID=local.aopListingUID) />

<cfif (local.memberTopAoPSubs.recordCount lte 1)>
	<cfif local.currAoPSubscriberID eq 0>
		<cfset local.currAoPSubscriberID = val(local.memberTopAoPSubs.subscriberID)>
	<cfelseif local.currAoPSubscriberID neq local.memberTopAoPSubs.subscriberID>
		<cfset local.errorCode = 15>
	</cfif>
<cfelse>
	<!--- should only be one Active at a time --->
	<cfset local.errorCode = 14>
</cfif>

<cfif (local.errorCode eq 0)>

	<!--- Get member's Area of Practice Subscriptions --->	
	<cfset local.memberAoPs = local.objTNFunctions.getMemberSubsByTypeID(siteID=local.siteID, memberID=local.memberid, typeUID=local.aopTypeUID) />
	
	<cfset local.memberAoPList	= valueList(local.memberAoPs.subscriptionID) />
	<cfset local.memberSectionCount = listLen(local.memberAoPList) />

	<!--- add a custom hidden field to help prevent bot submission. the value must be blank and the name is generated by a hash of today's date. --->
	<cfset local.nameOfHiddenField = "memid" & hash(dateformat(now(),'yyyymmdd'))>
	<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}

			.form{ padding-left:25px}
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##5e7b97;}
			.frmText{ font-size:8pt; color:##000; }
			.dim { color:##aaaaaa; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##666666; border-bottom:1px solid ##666666; }

			.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
			.CPSection{ border:1px solid ##666666; margin-bottom:15px; }
			.CPSectionTitle { font-size:14pt; height:20px; font-weight:bold; color:##ffffff; padding:10px; background:##336699; }
			.CPSectionContent { padding:0 10px; }
			.subCPSectionArea1 { background-color:##cde4f3; }
			.subCPSectionArea2 { background-color:##9ec8e4; }
			.subCPSectionArea3 { background-color:##aaaaaa;}
			.subCPSectionTotal { font-size:11pt; font-weight:bold; color:##ffffff; }
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }

			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}

			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }

			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}

			.BB { border-bottom:1px solid ##666666; }
			.BL { border-left:1px solid ##666666; }
			.BT { border-top:1px solid ##666666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }

			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}

			select.tsAppBodyText{color:##666;}

			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }			
		</style>
	</cfsavecontent>

	<cfsavecontent variable="local.pageJS">
		<script type="text/javascript">				
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
				var cnt = 0;
				for (var i=0; i < obj.length; i++) {
					if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
					else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
				}					
				if (cnt <= optRespLimit) return true;
				else return false;
			}
			function formatCurrency(num) {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				num = Math.floor(num/100).toString();
				if(cents<10) cents = "0" + cents;
				for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
				num.substring(num.length-(4*i+3));
				return (((sign)?'':'-') + '$' + num + '.' + cents);
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
			
			function selectMember() {
				$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
			}
			function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
			function addMember(memObj) {
				$.colorbox.close();
				assignMemberData(memObj);
			}				
		</script>
	</cfsavecontent>
	</cfoutput>		
	<cfhtmlhead text="#local.pageJS#">	
	<cfhtmlhead text="#local.pageCSS#">
	
	<cfswitch expression="#event.getValue('isSubmitted', 0)#">
		
		<!--- FORM: ========================================================================================================================================= --->
		<cfcase value="0">
			<!--- DISPLAY THE FORM --->
	
			<!--- Get Sections --->			
			<cfset local.qryAoPs = local.objTNFunctions.getTopSubscriptionData(siteID=local.siteID, typeUID=local.aopTypeUID) />
			
			<!--- Local variables from above queries populated towards bottom of page --->
			<cfscript>
			local.column1 = round((local.qryAoPs.recordCount + .49) / 2);
			</cfscript>
		
			<!--- JavaScript functions to calculate totals --->
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script src="/assets/common/javascript/jquery.counter-2.1.min.js"></script>
				<script type="text/javascript">
					function _FB_hasValue(obj, obj_type) {
						if (obj_type == 'TEXT' || obj_type == 'TEXTAREA') {
							tmp = obj.value;
							tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
							if (tmp.length == 0) return false;
							else return true;
						} else if (obj_type == 'SELECT') {
							for (var i=0; i < obj.length; i++) {
								if (obj.options[i].selected) {
									tmp = obj.options[i].value;
									tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
									if (tmp.length > 0) return true;
								}
							}
							return false;	
						} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX') {
							if (obj.checked) return true;
							else return false;	
						} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX') {
							if (obj.length == undefined && obj.checked) return true;
							else {
								for (var i=0; i < obj.length; i++) {
									if (obj[i].checked) return true;
								}
							}
							return false;
						} else {
							return true;
						}
					}
					function _FB_validate() {
						var obj = getTotalDue();
						if (obj.sCount > 0) {
							if (obj.sTotal > 0) {
								var thisForm = document.forms["#local.formName#"];
								var arrReq = new Array();
								#local.strPaymentForm.jsvalidation#
								if (arrReq.length > 0) {
									var msg = 'The following questions are required:\n\n';
									for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
									alert(msg);
									return false;
								}
							}
							return true;
						} else if (obj.bCount == 0){
							alert('Select an area of practice to join.');
							return false;
						}
					}
					function getTotalDue() {
						var thisForm = document.forms["#local.formName#"];
						var sTotal = 0;
		
						var bCount = 0;				
						var sCount = 0;				
						for (var i=0; i < thisForm.sections.length; i++){ 
							if (thisForm.sections[i].checked && !thisForm.sections[i].disabled) sCount = sCount + 1;
						}
		
						sTotal = sCount * #local.globalRate#;
	
						var obj = new Object();
							obj.sTotal = sTotal;
							obj.sCount = sCount;
							obj.bCount = bCount;
						return obj;
					}
		
					function checkSections() {
						var obj = getTotalDue();
						if (obj.sTotal <= 0) document.getElementById('paymentinfo').style.display = 'none';
						else document.getElementById('paymentinfo').style.display = '';
						document.getElementById('totalDue').innerHTML = obj.sTotal;
					}
					
					function checkBio()
					{
						var thisForm = document.forms["#local.formName#"];
	
						var obj = getTotalDue();
						if (!thisForm.bio.checked) document.getElementById('divBio').style.display = 'none';
						else document.getElementById('divBio').style.display = '';
						if (obj.sTotal <= 0) document.getElementById('paymentinfo').style.display = 'none';
						else document.getElementById('paymentinfo').style.display = '';
						document.getElementById('totalDue').innerHTML = obj.sTotal;
					}
					
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
				
			<cfoutput>
			<div class="form">
				<h1 class="tsAppHeading" align="left">
					Areas of Practice listing in Website Member/Public Directories
	 			</h1>	
				<p class="tsAppBodyText">
					Complete this form, including payment information, to display the areas of practice for your directory listing.
	 			</p>	
				<br/>	
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="/?#cgi.QUERY_STRING#" onsubmit="return _FB_validate();">
				<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1">
				<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="">
				<cfinput type="hidden" name="topAoPSub"  id="topAoPSub" value="#local.currAoPSubscriberID#">
				<div class="page">
					<div class="section">
						<h2 class="tsAppLegendTitle sectionTitle">TTLA Areas of Practice Request Form</h2>
						
						<table cellspacing="0" cellpadding="2" border="0" width="100%">
						<tr>
							<td class="tsAppBodyText questionNumber"></td>
							<td class="tsAppBodyText optionsVertical"><b>Full Name (First, Middle Initial, and Last Name):</b> #local.memberData.firstName#<cfif len(trim(local.memberData.middlename))> #local.memberData.middlename#</cfif> #local.memberData.lastName#</td>
						</tr>							
						<tr>
							<td class="tsAppBodyText questionNumber"></td>
							<td class="tsAppBodyText optionsVertical">
								<table border="0" cellpadding="6" cellspacing="0" width="100%">
									<tr>
										<td class="tsAppBodyText" valign="top" nowrap>
											<cfset local.thisX = 0>
											<cfloop query="local.qryAoPs">
												<cfset local.thisX = local.thisX + 1>
		
												<cfset local.strInput = { class="tsAppBodyText optionsCheckbox", name="sections", type="checkbox", onClick="checkSections()", value="#local.qryAoPs.subscriptionID#" }>
												<cfif listFind(local.memberAoPList,local.qryAoPs.subscriptionID)>
													<cfset local.strInput['checked'] = "true">
													<cfset local.strInput['disabled'] = "true">
												</cfif>
												<cfinput attributeCollection="#local.strInput#" id="chk_#local.qryAoPs.subscriptionid#" style="margin-top:10px;"><label for="chk_#local.qryAoPs.subscriptionID#">#local.qryAoPs.subscriptionName#</label><br />
												
												<cfif local.thisX is local.column1>
													<cfset local.thisX = 0>
													</td>
													<td class="tsAppBodyText" valign="top" nowrap>
												</cfif>
											</cfloop>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						</table>
					</div>
					<br />
					<div class="CPSection">
						<div class="CPSectionTitle">Payment Information</div>			
						<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
						<tr>
							<td class="tsAppBodyText">
								<b>Total Amount Due:</b> &nbsp; <b>$<span id="totalDue">0</span></b>
							</td>
						</tr>
						</table>
					</div>
					<br/>
					<script>
						function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
					</script>					
					<cfif len(local.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headCode)#">
					</cfif>
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">					
					<!--- Credit Card Info--->
					<div id="paymentinfo" class="CPSection">
						<div class="CPSectionTitle">Credit Card Information</div>			
						<div class="PL PR frmText paymentGateway BT">
							#local.strPaymentForm.inputForm#
						</div>
					</div>
					</div>
				</div>
					<br/>
	
					<!--- BUTTONS: ====================================================================================================================================== --->					
					<div id="formButtons">
						<div style="padding:10px;">
							<div align="center" class="frmButtons">
								<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
							</div>
						</div>
					</div>
				</div>
				</cfform>
			</div>
		
			<!--- Total payment based on choices via javascript --->
			<script>checkSections();</script>
		
			</cfoutput>				
	
		</cfcase>
	
		<!--- PROCESS: ====================================================================================================================================== --->
		<cfcase value="1">
				<cfscript>
					// param form values
					arguments.event.paramValue('sections','');			
					arguments.event.paramValue('bio','0');			
					arguments.event.paramValue('directoryBioID','0');			
					arguments.event.paramValue('directoryBio','');			
					local.sTotal 			= 0;
					local.sectionList = arguments.event.getValue('sections');
					local.sCount 			= listLen(local.sectionList);
				</cfscript>
				
				<cfif (local.sCount gt 0) OR (local.saveBio eq 1)>
					
					<cfset local.qryNewSubscriptions = QueryNew("subscriptionid,subscriptionName,amount,rfid,pcfree,GLAccountID,rateTermDateFlag,uid","integer,varchar,integer,integer,integer,integer,varchar,varchar")>
					
					<cfif (local.sCount gt 0)>	
						
						<cfquery dbtype="query" name="local.qryTopAopSubInfo">
							select subscriberID, subscriptionID, subEndDate, subStartDate, graceEndDate, status, GLAccountID, uid
							from [local].memberTopAoPSubs
							where subscriberID = #local.currAoPSubscriberID#
						</cfquery>						
										
						<!--- do they have a top subscription? --->
						<cfif local.qryTopAopSubInfo.recordcount eq 0>
							
							<!--- add top subscription --->
							<cfquery name="local.qryNewAoPListing" datasource="#application.dsn.membercentral.dsn#">
								select subs.subscriptionID, subs.subscriptionName, subs.GLAccountID, subs.rateTermDateFlag, subs.uid
								from dbo.sub_subscriptions subs
								inner join dbo.sub_types t on t.typeID = subs.typeID 
									and t.siteID = <cfqueryparam  value="#local.siteID#" cfsqltype="cf_sql_integer"> 
									and t.uid = <cfqueryparam  value="#local.aopTypeUID#" cfsqltype="cf_sql_varchar">
								where subs.status <> 'D'
								and subs.uid = <cfqueryparam value="#local.aopListingUID#" cfsqltype="cf_sql_varchar">
							</cfquery>
	
							<!--- find the RFID for this subscription --->
							<cfset local.rfid = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
							<cfquery name="local.qryAoPListingRate" datasource="#application.dsn.membercentral.dsn#" result="local.qryAoPListingRateResult">
								set nocount on;
								
								declare @FID int, @memberid int, @siteID int, @subscriptionid int;
								select @FID = <cfqueryparam value="#local.rfid#" cfsqltype="cf_sql_integer">;
								select @siteid = <cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer">;
								select @memberid = <cfqueryparam value="#local.memberID#" cfsqltype="cf_sql_integer">;
								select @subscriptionid = <cfqueryparam value="#local.qryNewAoPListing.subscriptionID#" cfsqltype="cf_sql_integer">;
		
								select rfid, rateAmt, numInstallments, frequencyName, frequency, frequencyID, rateName
								from (
									select rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequency, f.frequencyID, r.rateName, count(rfmp.rfmpid) as rfmpidCount
									from dbo.sub_subscriptions as s
									inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID 
										and rs.status = 'A'
									inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID 
										and r.status = 'A' 
										and r.isRenewalRate = 0 
										and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
									inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteResourceID = r.siteResourceID
									 	and srfrp.functionID = @FID
										and srfrp.siteID = @siteID
									inner join dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID and gprp.siteID = @siteID
									inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
								    	and m.memberID = @memberID						
									inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.rateAmt >= 0 
										and rf.status = 'A'
									inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
									left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid 
										and rfmp.status = 'A' 
									where s.subscriptionID = @subscriptionid
									group by rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequency, f.frequencyID, r.rateName
								) x 
								where x.rfmpidCount > 0;
							</cfquery>
		
							<cfif local.qryAoPListingRate.recordcount is not 1>
								<cflocation url="/?#cgi.QUERY_STRING#&eCode=18" addtoken="no">			
							</cfif>
							
							<cfset QueryAddRow(local.qryNewSubscriptions)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"subscriptionid",local.qryNewAoPListing.subscriptionid)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"subscriptionName",local.qryNewAoPListing.subscriptionName)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"rfid",local.qryAoPListingRate.rfid)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"GLAccountID",local.qryNewAoPListing.GLAccountID)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"amount",0)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"pcfree",0)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"rateTermDateFlag",local.qryNewAoPListing.rateTermDateFlag)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"uid",local.qryNewAoPListing.uid)>
							
						</cfif>
		
						<!--- get new section info --->
						<cfquery name="local.qryNewSections" datasource="#application.dsn.membercentral.dsn#">
							select subs.subscriptionID, subs.subscriptionName, subs.GLAccountID, subs.rateTermDateFlag, subs.uid
							from dbo.sub_subscriptions subs
							inner join dbo.sub_types t on t.typeID = subs.typeID 
								and t.siteID = <cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer">
								and t.uid = <cfqueryparam value="#local.aopTypeUID#" cfsqltype="cf_sql_varchar">
							where subs.status <> 'D'
							and subs.subscriptionID in (<cfqueryparam value="#arguments.event.getValue('sections')#" cfsqltype="CF_SQL_INTEGER" list="true">)
						</cfquery>
						
						<!--- determine price per section and total price for new sections --->
						<cfloop query="local.qryNewSections">
										
							<!--- find the RFID for this subscription --->
							<cfset local.rfid = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
							<cfquery name="local.qryRate" datasource="#application.dsn.membercentral.dsn#" result="local.qryRateResult">
								set nocount on;
								
								declare @FID int, @memberid int, @siteID int, @subscriptionid int;
								select @FID = <cfqueryparam value="#local.rfid#" cfsqltype="cf_sql_integer">;
								select @siteid = <cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer">;
								select @memberid = <cfqueryparam value="#local.memberID#" cfsqltype="cf_sql_integer">;
								select @subscriptionid = <cfqueryparam value="#local.qryNewSections.subscriptionID#" cfsqltype="cf_sql_integer">;
		
								select rfid, rateAmt, numInstallments, frequencyName, frequency, frequencyID, rateName
								from (
									select rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequency, f.frequencyID, r.rateName, count(rfmp.rfmpid) as rfmpidCount
									from dbo.sub_subscriptions as s
									inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID 
										and rs.status = 'A'
									inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID 
										and r.status = 'A' and r.isRenewalRate = 0 
										and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
									inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = r.siteResourceID
									    and srfrp.functionID = @FID and srfrp.siteID = @siteID
									inner join dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID and gprp.siteID = @siteID
									inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
									    and m.memberID = @memberID						
									inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID 
										and rf.rateAmt >= 0 
										and rf.status = 'A'
									inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
									left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid 
										and rfmp.status = 'A' 
									where s.subscriptionID = @subscriptionid
									group by rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequency, f.frequencyID, r.rateName
								) x 
								where x.rfmpidCount > 0;
							</cfquery>

							<cfif local.qryRate.recordcount is not 1>
								<cflocation url="/?#cgi.QUERY_STRING#&eCode=13" addtoken="no">			
							</cfif>
							
							<cfset QueryAddRow(local.qryNewSubscriptions)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"subscriptionid",local.qryNewSections.subscriptionid)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"subscriptionName",local.qryNewSections.subscriptionName)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"rfid",local.qryRate.rfid)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"GLAccountID",local.qryNewSections.GLAccountID)>
							
							<cfset QuerySetCell(local.qryNewSubscriptions,"amount",local.globalRate)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"pcfree",0)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"rateTermDateFlag",local.qryNewSections.rateTermDateFlag)>
							<cfset QuerySetCell(local.qryNewSubscriptions,"uid",local.qryNewSections.uid)>
	
						</cfloop>					
						
					</cfif>
				
				</cfif>

					<cfquery name="local.qrySectionTotal" dbtype="query">
						select sum(amount) as amt
						from [local].qryNewSubscriptions
						where pcfree <> 1
					</cfquery>
					<cfset local.sTotal = val(local.qrySectionTotal.amt)>
		

					<!--- ---------------------- --->
					<!--- Payment and accounting --->
					<!--- ---------------------- --->
					<cfset local.strAccTemp = { totalPaymentAmount=local.sTotal, assignedToMemberID=local.memberID, recordedByMemberID=local.memberID, rc=arguments.event.getCollection() } >
					<cfif local.strAccTemp.totalPaymentAmount gt 0>
						<cfset local.strAccTemp.payment = { detail=local.profile_1._description, amount=local.strAccTemp.totalPaymentAmount, profileID=local.profile_1._profileID, profileCode=local.profile_1._profileCode, stopOnError=1 }>
					</cfif>			
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>


					<!--- if payment not successful --->
					<cfif local.strAccTemp.totalPaymentAmount gt 0 and NOT (local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0)>
						<cfoutput>
						<script type="text/javascript">
							function _FB_hasValue(obj, obj_type){
								if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
									tmp = obj.value;
									tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
									if (tmp.length == 0) return false;
									else return true;
								} 
								else if (obj_type == 'SELECT'){
									for (var i=0; i < obj.length; i++) {
										if (obj.options[i].selected){
											tmp = obj.options[i].value;
											tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
											if (tmp.length > 0) return true;
										}
									}
									return false;	
								} 
								else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
									if (obj.checked) return true;
									else return false;	
								} 
								else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
									if (obj.length == undefined && obj.checked) return true;
									else{
										for (var i=0; i < obj.length; i++){
											if (obj[i].checked) return true;
										}
									}
									return false;
								}
								else{
									return true;
								}
							}
						
							function _FB_validateForm() {
								var thisForm = document.forms["frmDonate"];
								var arrReq = new Array();
								var lastMtrxErr = '';				
								
								#local.strPaymentForm.jsvalidation#
								
								if (arrReq.length > 0) {
									var msg = 'The following questions are required:\n\n';
									for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
									alert(msg);
									return false;
								}
								return true;
							}
						</script>
				
						<div class="form">
						<div class="tsAppHeading formTitle" align="center">
							Areas of Practice to List in Member Directory and Public Directory<br>
							on the Tennessee Association For Justice
						</div>
						<br/>
						<cfform name="frmDonate"  id="frmDonate" method="POST" action="/?pg=joinAnArea" onSubmit="return _FB_validateForm();">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1">
							<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(arguments.event.getValue('fieldnames',''),local.key) 
									and NOT listFindNoCase("isSubmitted,#local.nameOfHiddenField#,btnSubmit",local.key) 
									and NOT findNoCase("p_#local.profile_1._profileID#_fld_",local.key)>
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
							<div class="page">
								<br/>
								<div class="tsAppBodyText tsAppBodyTextImportant" style="padding:6px;border:1px solid ##f00;">Payment failed. #local.strACCResponse.paymentResponse.publicResponseReasontext#</div>
								<div id="paymentinfo">#local.strPaymentForm.inputForm#</div>
							</div>
							<div align="center"><button type="submit" class="tsAppBodyButton" name="btnSubmit">Join New Areas of Practice</button></div>
						</cfform>
						</cfoutput>
				
					<cfelse>
	
						<!--- save info to local database --->
						<cftransaction>
						<cftry>
							<cfset local.subscriberIDs = StructNew()>
	
							<cfloop query="local.qryNewSubscriptions">
								<cfif (local.qryNewSubscriptions.uid eq local.aopListingUID)>
									<cfquery name="local.qryGetAoPListingRateEndDate" datasource="#application.dsn.membercentral.dsn#">
										select 
											top 1 
											r.termAFStartDate, 
											r.termAFEndDate, 
											r.graceEndDate
										from dbo.sub_rates as r
										inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
											and rf.rfid = <cfqueryparam value="#local.qryNewSubscriptions.rfid#" cfsqltype="cf_sql_integer" />
									</cfquery>
									<cfset local.subStartDate = local.qryGetAoPListingRateEndDate.termAFStartDate>
									<cfset local.subEndDate = local.qryGetAoPListingRateEndDate.termAFEndDate>
									<cfset local.graceEndDate = local.qryGetAoPListingRateEndDate.graceEndDate>
								
									<!--- preserves the hour, thus the TZ --->
									<cfset local.daysDiff = DateDiff("d", local.subStartDate, Now())>
									<cfset local.subCurrDate = DateAdd("d", local.daysDiff, local.subStartDate)>
									<cfset local.subCurrDate = DateFormat(local.subCurrDate, "yyyy-mm-dd") & " " & TimeFormat(local.subCurrDate, "HH:mm:ss.l")>
									
									<cfset local.subStartCompare = DateCompare(local.subStartDate, local.subCurrDate)>
							
									<cfswitch expression="#local.qryNewSubscriptions.rateTermDateFlag#">
										<cfcase value="A">
											<cfif local.subStartCompare lt 0>
												<cfset local.subStartDate = local.subCurrDate>
											</cfif>					
										</cfcase>
										<cfcase value="S">
											<cfset local.subStartDate = local.subCurrDate>
										</cfcase>
										<cfcase value="C">
											<!--- Calculate the range...base on days, months --->
											<cfset local.termMonths = DateDiff("m", DateAdd("d", (1 - Day(local.subStartDate)), local.subStartDate), DateAdd("d", (DaysInMonth(local.subEndDate) - Day(local.subEndDate)), local.subEndDate))>
											<cfset local.termGraceDays = DateDiff("d", local.subEndDate, local.graceEndDate)>
											
											<cfset local.subStartDate = local.subCurrDate>
											<cfset local.subEndDate = DateAdd("m", local.termMonths, local.subStartDate)>
											<cfset local.subEndDate = DateFormat(local.subEndDate, "yyyy-mm-dd") & " " & TimeFormat(local.subEndDate, "HH:mm:ss.l")>
				
											<cfif len(local.graceEndDate) gt 0>
												<cfset local.graceEndDate = DateAdd("d", local.termGraceDays, local.subEndDate)>
												<cfset local.graceEndDate = DateFormat(local.graceEndDate, "yyyy-mm-dd") & " " & TimeFormat(local.graceEndDate, "HH:mm:ss.l")>
											</cfif>
										</cfcase>
									</cfswitch>
	
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddNewAoPSubscription">
										set nocount on
										
										declare @subscriberID int, @rc int, @activationOptionCode char(1), @siteID int
									
										select @siteID = #local.siteid#
										
										select @activationOptionCode=a.subActivationCode
										from sub_subscriptions subs
										inner join sub_activationOptions a on a.subActivationID = subs.subAlternateActivationID
										where subs.subscriptionID = #local.qryNewSubscriptions.subscriptionID#
										
										EXEC dbo.sub_addSubscriber @orgID=#local.orgid#,
												@memberID=#local.memberID#, 
												@subscriptionID=#local.qryNewSubscriptions.subscriptionID#,
												@parentSubscriberID=NULL, 
												@RFID=#local.qryNewSubscriptions.rfid#, 
												@GLAccountID=#local.qryNewSubscriptions.GLAccountID#,
												@status='O',
												@subStartDate='#local.subStartDate#', 
												@subEndDate='#local.subEndDate#', 
												<cfif len(local.graceEndDate) gt 0>
												@graceEndDate='#local.graceEndDate#', 
												<cfelse>
												@graceEndDate=NULL,
												</cfif>
												@recogStartDate='#local.subStartDate#', @recogEndDate='#local.subEndDate#',
												<cfif local.qryNewSubscriptions.pcfree eq 1>
												@pcfree=1,
												<cfelse>
												@pcfree=0,
												</cfif>
												@activationOptionCode=@activationOptionCode,
												@recordedByMemberID=#local.memberID#, 
												@bypassQueue=0,
												@subscriberID=@subscriberID OUTPUT
			
										update dbo.sub_subscribers
										set lastPrice = convert(decimal(18,2), '#local.qryNewSubscriptions.amount#')
										where subscriberID = @subscriberID
			
										<cfif local.subStartCompare gt 0>
											EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID,@newStatusCode="P",@siteID=@siteID,@enteredByMemberID=#local.memberID#,@bypassQueue=0,@result=@rc OUTPUT
										<cfelse>								
											EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID,@newStatusCode="P",@siteID=@siteID,@enteredByMemberID=#local.memberID#,@bypassQueue=0,@result=@rc OUTPUT
											IF @rc = 1
											begin
												EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID,@newStatusCode="A",@siteID=@siteID,@enteredByMemberID=#local.memberID#,@bypassQueue=0,@result=@rc OUTPUT
											end
										</cfif>
		
										select @subscriberID as subscriberID
										
										set nocount off
									</cfquery>
									
									<cfset local.subscriberIDs[local.qryNewSubscriptions.subscriptionID] = local.qryAddNewAoPSubscription.subscriberID>
									
									<cfset local.currAoPSubscriberID = local.qryAddNewAoPSubscription.subscriberID>
									
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTopAopSubInfo">
									select 
										s.subscriberID, s.subscriptionID, s.subEndDate, s.subStartDate, s.graceEndDate, st.statusCode as status, s.GLAccountID, subs.uid
									from 
										dbo.sub_subscribers s
										inner join dbo.sub_statuses st on 
											st.statusID = s.statusID
										inner join dbo.sub_subscriptions subs on 
											subs.subscriptionID = s.subscriptionID 
											and subs.uid = <cfqueryparam value="#local.aopListingUID#" cfsqltype="cf_sql_varchar" />
										inner join dbo.sub_types t on 
											t.typeID = subs.typeID 
											and t.siteID = <cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer" />
									where 
										s.parentSubscriberID is null
										and s.subscriberID = <cfqueryparam value="#local.currAoPSubscriberID#" cfsqltype="cf_sql_integer" />
										and s.memberID = <cfqueryparam value="#local.memberID#" cfsqltype="cf_sql_integer">
									ORDER BY 
										s.subEndDate
									</cfquery>
	
								<cfelse>
									
									<cfquery name="local.memberSubscriptions" datasource="#application.dsn.membercentral.dsn#">
										select 
											grs.subscriberID, subs.subscriptionID, subs.subscriptionID, t.typeID, t.typeName
										from 
											dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer" />, <cfqueryparam value="#local.currAoPSubscriberID#" cfsqltype="cf_sql_integer" />) grs
											inner join dbo.sub_subscriptions subs on 
												subs.subscriptionID = grs.subscriptionID
											inner join dbo.sub_types t on 
												t.typeID = subs.typeID
										where 
											grs.status <> 'D'
									</cfquery>
									
									<cfquery name="local.qryPaymentsScheduled" datasource="#application.dsn.membercentral.dsn#">
										SET NOCOUNT ON;

										declare @orgID int = <cfqueryparam value="#local.orgID#" cfsqltype="cf_sql_integer">;

										IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
											DROP TABLE ##mcSubscribersForAcct;
										IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
											DROP TABLE ##mcSubscriberTransactions;

										CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
										CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
											invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
											amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
											assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
											creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_subscriberID_amountToConsider (subscriberID, amountToConsider));

										INSERT INTO ##mcSubscribersForAcct (subscriberID)
										select li.listItem
										from dbo.fn_IntListToTable(<cfqueryparam value="#ValueList(local.memberSubscriptions.subscriberID)#" cfsqltype="cf_sql_varchar">,',') as li;

										EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;

										select IsNull(sum(st.amountToConsider), 0) as amountScheduled
										from dbo.sub_subscribers as s
										inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
										where s.orgID = @orgID;

										IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
											DROP TABLE ##mcSubscribersForAcct;
										IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
											DROP TABLE ##mcSubscriberTransactions;
									</cfquery>
									
									<cfif local.qryPaymentsScheduled.amountScheduled gt 0>
										<cfset local.paymentsScheduled = true>
									<cfelse>
										<cfset local.paymentsScheduled = false>
									</cfif>
									
									<!--- Going to assume that all sections are in the same set for the subscription --->
									<cfquery name="local.qrySetInfo" datasource="#application.dsn.membercentral.dsn#">
										select ao.childSetID, ao.useAcctCodeInSet, ao.PCNum, ao.PCPctOffEach
										from dbo.sub_addons ao
										inner join dbo.sub_subscriptionSets ss on ss.setID = ao.childSetID
										inner join dbo.sub_sets s on s.setID = ss.setID
										inner join dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
											and subs.status <> 'D'
										inner join dbo.sub_types t on t.typeID = subs.typeID 
											and t.uid = <cfqueryparam value="#local.aopTypeUID#" cfsqltype="cf_sql_varchar" />
											and t.siteID = <cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer" />
											and t.status <> 'D'
										inner join dbo.sub_subscriptions topSub on topSub.subscriptionID = ao.subscriptionID 
											and topSub.uid = <cfqueryparam value="#local.aopListingUID#" cfsqltype="cf_sql_varchar" />
										group by ao.childSetID, ao.useAcctCodeInSet, ao.PCNum, ao.PCPctOffEach;
									</cfquery>
									
									<cfset local.currSetID = val(local.qrySetInfo.childSetID)>
								
									<!--- get current number of free subscriptions --->
									<cfquery name="local.qryCurrFree" datasource="#application.dsn.membercentral.dsn#">
										select 
											count(subscriberID) as freeCount
										from 
											dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam value="#local.memberID#" cfsqltype="cf_sql_integer" />, <cfqueryparam value="#local.siteID#" cfsqltype="cf_sql_integer" />, <cfqueryparam value="#local.currAoPSubscriberID#" cfsqltype="cf_sql_integer" />) grs
										inner join dbo.sub_subscriptionSets ss on 
											ss.subscriptionID = grs.subscriptionID 
											and ss.setID = <cfqueryparam value="#local.currSetID#" cfsqltype="cf_sql_integer" />
										where 
											grs.status <> 'D'
											and PCFree = 1
									</cfquery>
									
									<cfset local.currFreeCount = val(local.qryCurrFree.freeCount)>								
									
									<cfif len(local.qryTopAopSubInfo.subStartDate) eq 0>
										<cfset local.startDateToUse = DateFormat(Now(), "yyyy-mm-dd") & " 00:00:00.0">
									<cfelse>
										<cfset local.statusDaysDiff = DateCompare(local.qryTopAopSubInfo.subStartDate, Now(),"d")>
										<cfif (local.statusDaysDiff gt 0)>
											<cfset local.startDateToUse = DateFormat(local.qryTopAopSubInfo.subStartDate, "yyyy-mm-dd") & " " & TimeFormat(local.qryTopAopSubInfo.subStartDate, "HH:mm:ss.l")>
										<cfelse>
											<!--- preserves the hour, thus the TZ --->
											<cfset local.daysDiff = DateDiff("d", local.qryTopAopSubInfo.subStartDate, Now())>
											<cfset local.startDateToUse = DateAdd("d", local.daysDiff, local.qryTopAopSubInfo.subStartDate)>
											<cfset local.startDateToUse = DateFormat(local.startDateToUse, "yyyy-mm-dd") & " " & TimeFormat(local.startDateToUse, "HH:mm:ss.l")>
										</cfif>
									</cfif>								

									<cfset local.endDateToUse = DateFormat(local.qryTopAopSubInfo.subEndDate, "yyyy-mm-dd") & " " & TimeFormat(local.qryTopAopSubInfo.subEndDate, "HH:mm:ss.l")>
									<cfset local.endGraceDateToUse = DateFormat(local.qryTopAopSubInfo.graceEndDate, "yyyy-mm-dd") & " " & TimeFormat(local.qryTopAopSubInfo.graceEndDate, "HH:mm:ss.l")>
									
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddNewAoPSubscription">
										set nocount on
										
										declare @subscriberID int, @rc int, @activationOptionCode char(1), @siteID int
		
										select @siteID = #local.siteid#
										
										select @activationOptionCode=a.subActivationCode
										from dbo.sub_subscriptions subs
										inner join dbo.sub_activationOptions a 
										<cfif local.paymentsScheduled>
											on a.subActivationID = subs.subActivationID
										<cfelse>
											on a.subActivationID = subs.subAlternateActivationID
										</cfif>
										where subs.subscriptionID = #local.qryNewSubscriptions.subscriptionID#
		
										EXEC dbo.sub_addSubscriber @orgID=#local.orgid#,
												@memberID=#local.memberID#, @subscriptionID=#local.qryNewSubscriptions.subscriptionID#,
												@parentSubscriberID=#local.currAoPSubscriberID#, 
												@RFID=#local.qryNewSubscriptions.rfid#, 
												<cfif local.qrySetInfo.useAcctCodeInSet eq 1>
												@GLAccountID=#local.qryNewSubscriptions.GLAccountID#,
												<cfelse>
												@GLAccountID=#local.qryTopAopSubInfo.GLAccountID#,
												</cfif>
												@status='O', 
												@subStartDate='#local.startDateToUse#', 
												@subEndDate='#local.endDateToUse#', 
												<cfif len(local.endGraceDateToUse) gt 0>
												@graceEndDate='#local.endGraceDateToUse#', 
												<cfelse>
												@graceEndDate=NULL,
												</cfif>
												@recogStartDate='#local.startDateToUse#', @recogEndDate='#local.endDateToUse#', 
												<cfif local.qryNewSubscriptions.pcfree eq 1>
												@pcfree=1,
												<cfelse>
												@pcfree=0,
												</cfif>
												@activationOptionCode=@activationOptionCode,
												@recordedByMemberID=#local.memberID#, 
												@bypassQueue=0, 
												@subscriberID=@subscriberID OUTPUT
			
										update dbo.sub_subscribers
										set lastPrice = convert(decimal(18,2), '#local.qryNewSubscriptions.amount#')
										where subscriberID = @subscriberID
			
									<cfif (local.qryTopAopSubInfo.status eq "A")>
										EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID,@newStatusCode="P",@siteID=@siteID,@enteredByMemberID=#local.memberID#,@bypassQueue=0,@result=@rc OUTPUT
										IF @rc = 1
										begin
											EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID,@newStatusCode="A",@siteID=@siteID,@enteredByMemberID=#local.memberID#,@bypassQueue=0,@result=@rc OUTPUT
										end
									<cfelseif (local.qryTopAopSubInfo.status eq "P")>
										EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID,@newStatusCode="P",@siteID=@siteID,@enteredByMemberID=#local.memberID#,@bypassQueue=0,@result=@rc OUTPUT
									</cfif>
		
										select @subscriberID as subscriberID
										
										set nocount off
									</cfquery>
									
									<cfset local.subscriberIDs[local.qryNewSubscriptions.subscriptionID] = local.qryAddNewAoPSubscription.subscriberID>
								</cfif>
							
							</cfloop>						
						
							<cfset local.savedLocal = true>
							<cfcatch type="any">
								<cftransaction action="rollback" />
								<cfset application.objError.SendError(cfcatch=cfcatch)>
								<cfset local.savedLocal = false>
							</cfcatch>
						</cftry>
						</cftransaction>
	

						<!--- accounting for revenue --->
						<cfif local.savedLocal>
							<cftransaction>
								<cftry>
									<cfloop query="local.qryNewSubscriptions">
										<cfif local.qryNewSubscriptions.pcfree eq 1>
											<cfset local.loopPriceToUse = 0>
										<cfelse>
											<cfset local.loopPriceToUse = local.qryNewSubscriptions.amount>
										</cfif>									
			
										<cfset local.descToUse = "Area of Practice Dues: #local.qryNewSubscriptions.SubscriptionName#">				
	
										<cfset local.strACCTemp = { assignedToMemberID	= local.memberID,
																	recordedByMemberID	= local.memberID,
																	detail = local.descToUse,
																	amount = local.loopPriceToUse,																
																	revenueGLAccountID = local.qryNewSubscriptions.GLAccountID}>
										<cfset local.strACCSale = local.objAccounting.recordSale(argumentcollection=local.strACCTemp)>
										<cfif local.strACCSale.rc IS NOT 0 OR local.strACCSale.transactionID IS 0>
											<cfthrow message="Failed to create sale for #local.qryNewSubscriptions.SubscriptionName#">
										<cfelse>
											<cfstoredproc procedure="tr_createApplication" datasource="#application.dsn.membercentral.dsn#">
												<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
												<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
												<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Admin">
												<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.strACCSale.transactionID#">
												<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Dues">
												<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.subscriberIDs[local.qryNewSubscriptions.subscriptionID]#">
												<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
											</cfstoredproc>
										</cfif>
									</cfloop>								
									
									<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>									
										<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=local.memberID, transactionDate=now())>
									</cfif>		
									
									<cfset local.savedAccounting = true>
								<cfcatch type="any">
									<cftransaction action="rollback" />
									<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
									<cfset local.savedAccounting = false>
								</cfcatch> 
								</cftry>			
							</cftransaction>
			
							<!--- construct email --->
							<cfset local.txtstyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:10pt">
							<cfsavecontent variable="local.pdftext">
								<cfoutput>
								
										<div style="#local.txtstyle#">
											<p>TTLA Join an Area of Practice form submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
											<p>
												#local.strACCResponse.accResponseMessage#
												<cfif local.savedLocal>
													This information has already been updated on the website.<br/>
												<cfelse>
													<span style="color:##f00;"><b>This information was not immediately updated on the website.</b></span><br/>
												</cfif>
												<cfif local.savedAccounting>
													This revenue has been recorded in the member's transactions on the website.<br/>
												<cfelse>
													<span style="color:##f00;"><b>This revenue was not recorded in the member's transactions on the website.</b></span><br/>
												</cfif>		
											</p>			
											<table cellpadding="2" cellspacing="0" border="0">
												<tr style="#local.txtstyle#"><td colspan="2" nowrap><b>Member Information</b></td></tr>
												<tr style="#local.txtstyle#"><td valign="top">MemberNumber:</td><td valign="top">#local.memberData.memberNumber#</td></tr>
												<tr style="#local.txtstyle#"><td valign="top">Name:</td><td valign="top">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#</td></tr>
											</table>
											<br/>
											<table cellpadding="2" cellspacing="0" border="0">
												<tr style="#local.txtstyle#"><td><b>New Areas of Practice</b></td></tr>
												<tr style="#local.txtstyle#"><td><cfloop query="local.qryNewSubscriptions">#local.qryNewSubscriptions.subscriptionName#<br /></cfloop></td></tr>
											</table>
											<br/>
											<table cellpadding="2" cellspacing="0" border="0">
												<tr style="#local.txtstyle#"><td colspan="2" nowrap><b>Purchase Information</b></td></tr>
												<tr style="#local.txtstyle#"><td valign="top">Section Total:</td><td valign="top">#dollarFormat(local.stotal)#</td></tr>
											</table>
											<br/>
										</div>
							</cfoutput>
						</cfsavecontent>
						<cfsavecontent variable="local.mailContent">
							<cfoutput>
								#local.pdftext#
							</cfoutput>
						</cfsavecontent>

						<cfscript>
							local.arrEmailTo = [];
							local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}
							if (arrayLen(local.arrEmailTo)) {
								local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="", email=local.ORGEmail.from },
									emailto=local.arrEmailTo,
									emailreplyto=local.ORGEmail.from,
									emailsubject=local.ORGEmail.SUBJECT,
									emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
									emailhtmlcontent=local.mailContent,
									siteID=arguments.event.getValue('mc_siteInfo.siteID'),
									memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
									messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
									sendingSiteResourceID=this.siteResourceID
								);
							}
						</cfscript>
														
						<!--- locate to landing page --->
						<cflocation url="/?pg=formlanding" addtoken="no">
					<cfelse>
						<cfoutput>
						<div id="divProcessEx" class="tsAppBodyText formIntro">
							We've found an issue that is preventing us from allowing you to add Areas of Practice at this time. We apologize, but it is not available online. 
							<br><br>
							We apologize for the inconvenience. 
						</div>
						</cfoutput>
					</cfif>	
					
				</cfif>					
		</cfcase>
		
					<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
		<cfcase value="99">
			<!--- output to screen --->
			<div class="HeaderText">Thank you for submitting your application!</div>
			<br/>
			<cfif isDefined("session.invoice")>
				<div>This page has been emailed to the email address on file. If you would like you could also print the page out as a receipt.</div>
				<br />
				<div class="BodyText">
					#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
				</div>
			</cfif>
			<cfset session.invoice = "" />
		</cfcase>
			
		<!--- SPAM MESSAGE: ================================================================================================================================= --->
		<cfcase value="100">
			<div>
				Error! you Can't Post Here.
			</div>
		</cfcase>
		
	</cfswitch>	
	
<cfelse>
	<cfoutput>
	<div id="divRenewalEx" class="tsAppBodyText formIntro">
		We've found an issue that is preventing us from allowing you to add Areas of Practice at this time. We apologize, but it is not available online. 
		<br><br>
		We apologize for the inconvenience. 
	</div>
	</cfoutput>
</cfif>	