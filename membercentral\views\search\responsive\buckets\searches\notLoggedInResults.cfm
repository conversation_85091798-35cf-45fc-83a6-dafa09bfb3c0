<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div id="MCNotLoggedInContainer"<cfif val(local.strResultsCount.itemCount) GT 0> style="display:none;"</cfif>>
	<cfif local.strResultsCount.itemCount EQ 0>
		<div>#showCommonNotFound(bucketID=arguments.bucketID, searchID=arguments.searchID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#</div>
	<cfelse>
		<cfif local.hasDepositionBucket>
			<div style="padding-bottom:10px;">
				<button class="btn bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
			</div>
		</cfif>

		<cfif val(local.strResultsCount.itemCount) gt 0>
			<div style="padding-bottom:3px;"><strong>#Numberformat(local.strResultsCount.itemCount)#</strong> searches found</div>
		</cfif>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</cfif>
</div>
<cfif val(local.strResultsCount.itemCount) GT 0>
	<div id="MCSearchSummaryContainer">
		<cfif len(trim(local.stsearchVerboseNoName))>
			<div>
				<strong>Search Criteria:</strong> <em>#local.stsearchVerboseNoName#</em>
			</div>
		</cfif>

		<div class="bk-mb-3 bk-mt-4">
			<button type="button" class="btn btn-success btn-large" onclick="b_showLoginContainer();">View Trial Lawyers and Make Connections</button>
		</div>

		<div class="bk-d-flex bk-flex-wrap bk-mt-3">
			<div id="MCSearchSummaryBarChart" class="bk-mb-3">
				<div class="bk-font-weight-bold bk-font-size-md text-center">Recent Searches By Trial Lawyers<br />Similar to Your Own Search (#numberFormat(local.strResultsCount.itemCount,",")# Total)</div>
			</div>
			<div id="MCSearchSummaryLocationChart" class="bk-mb-3">
				<div class="bk-font-weight-bold bk-font-size-md text-center">Location of Searches by Trial Lawyers<br />Similar to Your Search (#numberFormat(local.strResultsCount.itemCount,",")# Total)</div>
			</div>
			<div id="bk_tooltip"></div>
		</div>
		
		<cfif len(local.strSearchSummary.uniqueLawyers)>
			<div class="bk-d-flex bk-mt-4 bk-flex-wrap">
				<div class="bk-d-flex bk-mr-5 bk-similar-search-circle bk-mb-3">
					<div class="bk-summary-circle bk-text-light">#local.strSearchSummary.uniqueLawyers#</div>
					<div class="bk-d-flex bk-align-self-center bk-pl-3 bk-font-weight-bold">Unique Lawyers</div>
				</div>
			</div>
		</cfif>
	</div>
	#showFooter(viewDirectory=arguments.viewDirectory)#
</cfif>
</cfoutput>