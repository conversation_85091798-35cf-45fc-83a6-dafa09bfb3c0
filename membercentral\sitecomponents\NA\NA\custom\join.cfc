<cfcomponent extends="model.customPage.customPage" output="true">
	<cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			variables.redirectlink = "/?pg=join";
			local.formAction = arguments.event.getValue('fa','showLookup');
			variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			variables.currentDate = dateTimeFormat(now());
			local.arrCustomFields = [];

			local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="6A7EF36E-89C6-4D25-AF3D-8F3D0222194D" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a NATLE member. If you have questions about your membership, please call 877.321.3440." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledMessage",type="CONTENTOBJ",desc="Message displayed when account selected billed subscription",value="Our records indicate either your application is currently under review or you are a renewing member. If you are looking to renew, please click [[click here]] to review your renewal statement.  If you have questions about your membership, please call 877.321.3440." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 877.321.3440." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="NATLE Membership Application" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="954ED453-301F-4229-98D7-E88EE483949F" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="ACCGCIM" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application. After your application is reviewed, if it is approved, the payment will be processed on the card you have provided. If your application is not approved, you will hear from NATLE directly." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="NATLE Membership Application Confirmation" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application - [[FirstName]] [[LastName]]" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);       

			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmJoin',
				formNameDisplay=variables.strPageFields.FormTitle,
				orgEmailTo= variables.strPageFields.StaffConfirmationTo,
				memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
			));

			variables.personalInformationFieldSetUID = "F76F91C7-519A-4456-AB3D-F1533D56E2FE";
			variables.contactInformationFieldSetUID = "284004B1-898A-4BB2-AABA-286331F4965D";
			variables.executiveDirectorInformationFieldSetUID = "8C088D56-92A6-4444-A18A-FFE9228668B0";

			variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");
			
			/* Member History Vars */
			variables.useHistoryID = 0;
			variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
			variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
			variables.historyStartedText = "Member started Membership form.";
			variables.historyCompletedText = "Member completed Membership form.";

			variables.origMemberID = variables.useMID;
			if(local.formAction neq "showLookup" and 
				local.formAction neq "processLookup" and 
				structKeyExists(session, "formFields") and 
				structKeyExists(session.formFields, "step0") and 
				structKeyExists(session.formFields.step0, "memberID") and 
				int(val(session.formFields.step0.memberID)) gt 0){       
					variables.useMID = session.formFields.step0.memberID;
					if(structKeyExists(session.formFields.step0, "origMemberID") and int(val(session.formFields.step0.origMemberID)) gt 0){
						variables.origMemberID = session.formFields.step0.origMemberID;
					}              
			}else if(session.cfcuser.memberdata.identifiedAsMemberID){
				variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
				variables.origMemberID = variables.useMID;
			}
			if( local.formAction neq "showLookup" and 
				local.formAction neq "processLookup" and 
				local.formAction neq "showMemberInfo" and 
				local.formAction neq "processMemberInfo" and 
				structKeyExists(session.formFields, "step1") and 
				structKeyExists(session.formFields.step1, "useHistoryID") and 
				int(val(session.formFields.step1.useHistoryID)) gt 0){
					variables.useHistoryID = int(val(session.formFields.step1.useHistoryID));
			}
			local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
			local.subStatus = "";
			if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
				local.subStatus = hasSub(int(variables.useMID));
			}
			
			/* Form Process Flow */
			if(local.isSuperUser){
				local.returnHTML = showError(errorCode='admin');  
			}else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
				local.returnHTML = showError(errorCode=local.subStatus);  
			}else{
				switch (local.formAction) {
					case "processLookup":
						switch (processLookup(rc=arguments.event.getCollection())) {
							case "success":
								local.returnHTML = showMemberInfo();
								break;		
							default:
								local.returnHTML = showError(errorCode='error');
								break;				
						}
						break;
					case "processMemberInfo":
						switch (processMemberInfo(rc=arguments.event.getCollection())) {
							case "success":
								local.returnHTML = showMembershipInfo();
								break;
							case "spam":
							local.returnHTML = showError(errorCode='spam');
								break;
							default:
								local.returnHTML = showError(errorCode='error');
								break;				
						}
						break;
					case "processMembershipInfo":
						switch (processMembershipInfo(rc=arguments.event.getCollection())) {
							case "success":
								local.returnHTML = showPayment();
								break;
							default:
								local.returnHTML = showError(errorCode='error');
								break;				
						}
						break;
					case "processPayment":
						local.processStatus = processPayment(event=arguments.event);
						switch (local.processStatus) {
							case "success":
								local.returnHTML = showConfirmation();
								application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
								structDelete(session, "formFields");
								break;
							default:
								local.returnHTML = showError(errorCode='failpayment');
								break;				
						}
						break;
					case "showMembershipInfo":
						local.returnHTML = showMembershipInfo();
						break;	
					case "showMemberInfo":
						local.returnHTML = showMemberInfo();
						break;
					default:
						if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
						if(structKeyExists(session, "formFields")) structDelete(session, "formFields");
						local.returnHTML = showLookup(memberkey=arguments.event.getTrimValue('mk',''));
						break;
				}
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="memberkey" type="string" required="true">

		<cfset var local = structNew()>
		<cfif arguments.memberkey neq ''>
			<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		</cfif>

		<cfset local.fieldSetUIDlist = '#variables.personalInformationFieldSetUID#,#variables.contactInformationFieldSetUID#,#variables.executiveDirectorInformationFieldSetUID#'>

		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>

		<cfset local.contactTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.strData = {}>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>	
		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

			<cfset local.contactTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldLabel='Membership Category']")/>
			<cfif arrayLen(local.contactTypeData)>				
				<cfset local.contactTypeField.fieldCode = local.contactTypeData[1].XmlAttributes.fieldCode>
				<cfset local.contactTypeField.fieldLabel = local.contactTypeData[1].XmlAttributes.fieldLabel>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.headCode">
			<cfoutput>          
				#variables.pageJS# 
				#variables.pageCSS#
			<style type="text/css">
				@media screen and (max-width: 767px){
					.borderLeft{border: unset!important;}				
				}		
				##divFrmErr{
					font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;
					font-size: 9pt;
					padding:8px;
				}
				.CPSectionTitle{height:unset!important;}
				input[type="checkbox"], input[type="radio"] {
					-webkit-appearance: auto !important;
				}
				.CPSection{border:0.5px solid;}
				.CPSectionContainer{padding: 10px;}
			</style>
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoadOne(obj){
					var _CF_this = document.forms['#variables.formName#'];

					$('html, body').animate({ scrollTop: 500 }, 500);	

					var thisForm = document.forms["#variables.formName#"];
					var er_change = function(r) {
						var results = r;
						if( results.success ){
							if (results.isnewmember && typeof results.licensenumber != "undefined" && results.licensenumber != '') {
								$('.professionalLicensesHolder').show();
								if($('.mpl_pltypeid').length > 0){
									$('.mpl_pltypeid').multiselect("widget").find(":input[value=" + results.licenses[0].pltypeid +"]").each(function() {
										this.click();
									});
									$('##mpl_'+ results.licenses[0].pltypeid +'_licenseNumber').val(results.licenses[0].licensenumber);
									$('##mpl_'+ results.licenses[0].pltypeid +'_activeDate').val(results.licenses[0].activedate);
									$('##mpl_'+ results.licenses[0].pltypeid +'_status option[value="active"]').attr('selected','selected');	
									$('###local.contactTypeField.fieldCode# option:contains(Attorney)').first().attr('selected','selected');

									if(typeof adjustFieldsetDisplay != 'undefined')
										adjustFieldsetDisplay();									
								}
							}
						}
						else{ /*alert('not success');*/ }
					};
					
					var objParams = { memberNumber:obj.memberNumber };
					TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change); 
					
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
					mc_continueForm($('###variables.formName#'),function(){afterFormLoadOne(memObj);});
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				
				function addMember(memObj) {
					$.colorbox.close();
					assignMemberData(memObj);
				}
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585)
						_popupWidth = windowWidth - 30;
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				
				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
						}
					} else
						step = 1;
					prevStep = step;				    
				}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585)
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						else
							$.colorbox.resize({innerWidth:550, innerHeight:330});
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
			<div id="customPage" class="customPage">
				<cfif len(variables.strPageFields.FormTitle)>
					<div class="row-fluid" id="FormTitleId">
						<div class="span12">
							<span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
						</div>
					</div>
				</cfif>			
				<div class="r i frmText">*Denotes required field</div>	
				
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
					<cfinput type="hidden" name="fa" id="fa" value="processLookup">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<div class="CPSection row-fluid">
						<div class="CPSectionTitle">#variables.strPageFields.AccountLocatorTitle#</div>
						<div class="frmRow1" style="padding:10px;">
							<table cellspacing="0" cellpadding="2" border="0" width="100%">
								<tr>
									<td width="50%" class="c">
										<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
											<button name="btnAddAssoc" type="button" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
										</div>
									</td>
									<td>
										<span class="frmText">
											#variables.strPageFields.AccountLocatorInstructions#
										</span>
									</td>
								</tr>
							</table>
						</div>
					</div>				
				</cfform>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step0")>
			<cfset structDelete(session.formFields, "step0")>
		</cfif>	

		<cfset session.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        

		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.fieldSetUIDlist = '#variables.personalInformationFieldSetUID#,#variables.contactInformationFieldSetUID#,#variables.executiveDirectorInformationFieldSetUID#'>

		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>

		<cfset local.contactTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.strData = {}>
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

			<cfset local.contactTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldLabel='Contact Type']")/>
			<cfif arrayLen(local.contactTypeData)>				
				<cfset local.contactTypeField.fieldCode = local.contactTypeData[1].XmlAttributes.fieldCode>
				<cfset local.contactTypeField.fieldLabel = local.contactTypeData[1].XmlAttributes.fieldLabel>
			</cfif>            
			<cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.personalInformationFieldSetUID#,#variables.contactInformationFieldSetUID#',local.fieldSetUid)>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
			</cfif>          
		</cfloop>

		<cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.executiveDirectorInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.executiveDirectorInformationFieldSetUID, mode="collection", strData=local.strData.one)>
	   
		<cfsavecontent variable="local.headCode">
			<cfoutput>			
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 500 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}		
				function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.contactTypeField.fieldCode#']).find('option:selected').text());

					switch(memType) {						
						case 'Executive Director':
							$('.executiveDirectorInformationFieldSetHolder').show();
							break;
						default:
							$('.executiveDirectorInformationFieldSetHolder').hide();
							break;
					}
				}			
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();	
					var memType = $.trim($(_CF_this['#local.contactTypeField.fieldCode#']).find('option:selected').text());	

					
					#local.personalInformationFieldSet.jsValidation#
					#local.contactInformationFieldSet.jsValidation#
					#local.executiveDirectorInformationFieldSet.jsValidation#
					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strData.one#" item="local.thisKey">
						<cfif FindNoCase("m_",local.thisKey)
							or FindNoCase("ma_",local.thisKey) 
							or FindNoCase("mat_",local.thisKey) 
							or FindNoCase("me_",local.thisKey) 
							or FindNoCase("mpl_",local.thisKey)							
							or FindNoCase("mp_",local.thisKey) 
							or FindNoCase("mw_",local.thisKey) 
							or FindNoCase("md_",local.thisKey)
							or FindNoCase("mccf_",local.thisKey)>
							#toScript(local.strData.one[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfif>
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							if($("###variables.formName# [name='"+key+"']").prop("tagName").toLowerCase() == "input" && $("###variables.formName# [name='"+key+"']").attr("type").toLowerCase() == "radio"){

								$("###variables.formName# [name='"+key+"'][value='"+objPrefill[key]+"']").prop("checked", true);

							}else if($("###variables.formName# [name='"+key+"']").prop("tagName").toLowerCase() == "select" && $("###variables.formName# [name='"+key+"']").prop('multiple')){
								var selectString = objPrefill[key];
								$.each(selectString.split(','), function(){
									$("###variables.formName# [name='"+key+"'] option[value='"+this+"']").prop('selected', true);
								});
								$("###variables.formName# [name='"+key+"']").multiselect("refresh");
							}else{
								$("###variables.formName# [name='"+key+"']").val(objPrefill[key]);
							}
						}
					}
				}
				$(document).ready(function() {		
					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>		
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
					
					var contactTypeField = $('##'+"#local.contactTypeField.fieldCode#");	
					$(contactTypeField).change(adjustFieldsetDisplay);				
					$(contactTypeField).trigger('change');
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
			<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<cfif NOT variables.isLoggedIn AND application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID) GT 0>
						<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
					</cfif>
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
							</div>
						</div>
					</cfif>			
					<div class="r i frmText">*Denotes required field</div>
					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>

						<span class="personalInformationFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.personalInformationFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.personalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<span class="contactInformationFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.contactInformationFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.contactInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>

						<span class="executiveDirectorInformationFieldSetHolder">
							<div class="CPSection row-fluid">
								<div class="CPSectionTitle">#local.executiveDirectorInformationFieldSet.fieldSetTitle#</div>								
								<div class="CPSectionContainer">										
									#local.executiveDirectorInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>
						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>	

		<cfset local.isBot = 0>
		<cfif structKeyExists(arguments.rc, 'iAgree')>
			<cfset local.isBot = 1>
		</cfif>
		<cfif (local.isBot) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.response = "failure">

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>			

		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>	
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>

		<cfif variables.isLoggedIn OR session.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
			<cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelseif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=local.loggedMemberID)>
		<cfelse>
			<cfset local.strData.one.memberID = 0>         
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>            
		</cfif>

		<cfset session.formFields.step0.origMemberID = variables.useMID/>
		<cfset session.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>	
			<cfset session.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
												subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
												enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
			<cfset session.captchaEntered = 1>		
			<cfset local.response = "success">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>        
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset local.strData.zero = checkSessionExist("step0")/>
		<cfset variables.useMID = local.strData.zero.memberID/>

		<cfif StructIsEmpty(local.strData.one)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
	   
		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
								subscriptionID = local.subscriptionID,
								memberID = variables.useMID,
								isRenewalRate = false,
								siteID = variables.siteID, 
								strData=local.strData.two
								)>	        
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style>
					input.subRateOverrideBox{margin-bottom:0px;}
				</style>
				<script type="text/javascript">	
					function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}		
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 500 }, 500);
					}	
					$(document).ready(function(){
						$('input[name^=sub#local.subscriptionID#_rateOverride]').attr('disabled',true);
						$('input[name^=sub#local.subscriptionID#_rateOverride]').attr('readonly','readonly');
						
						$("[name='sub#local.subscriptionID#_rate']").each(function(){
							if($(this).prop("checked")){
								$(this).next().children('.subRateOverrideBox').removeAttr('disabled');
								$(this).next().children('.subRateOverrideBox').removeAttr('readonly');
							};
						});
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
							</div>
						</div>
					</cfif>		
					<div class="r i frmText">*Denotes required field</div>
					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>

					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>			
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>        
		<cfset local.strData.two = checkSessionExist("step2")/>

		<cfif StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
										arrPayMethods=local.arrPayMethods, 
										siteID=variables.siteID, 
										memberID=variables.useMID, 
										title="Choose Your Payment Method", 
										formName=variables.formName, 
										backStep="showMembershipInfo"
									)>
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>
				<script type="text/javascript">	
					function validatePaymentForm(isPaymentRequired) {
						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					$('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
					<cfinput type="hidden" name="fa" id="fa" value="processPayment">
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
							</div>
						</div>
					</cfif>		
					<div class="r i frmText">*Denotes required field</div>
					<cfif len(variables.strPageFields.Step3TopContent)>
						<div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
					</cfif>

					<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
					<div class="tsAppSectionContentContainer">						
						#local.strResult.formContent#
					</div>
					<br/>

					<div class="tsAppSectionHeading">Total Price</div>
					<div class="tsAppSectionContentContainer">						
						Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
					</div>

					<br/><br/>

					<cfif local.paymentRequired>
						#local.strReturn.paymentHTML#
					<cfelse>
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					</cfif>

				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step3")>
			<cfset structDelete(session.formFields, "step3")>
		</cfif>			
		<cfset session.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

		<cfset local.strData.three = checkSessionExist("step3")/>
		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>

		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>
		
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.three.memberID = variables.useMID>
		<cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)> 
		<cfif structKeyExists(local.strData.three, 'mccf_payMethID') and structKeyExists(local.strData.three, 'p_#local.strData.three.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.strData.three['p_#local.strData.three.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>
		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->     
		
		<cfif local.strResult.totalFullPrice gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.strData.three.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.strResult.totalFullPrice gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#",local.strData.three.mccf_payMeth)>
			<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
		</cfif>

		<cfset local.response = "success">

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfset local.membershipCatFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.membershipCatFieldCode = "md_" & local.membershipCatFieldInfo.COLUMNID/>
		<cfset local.membershipCatSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData.one["#local.membershipCatFieldCode#"])>

		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.executiveDirectorInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.executiveDirectorInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		
		<cfsavecontent variable="local.invoice">
			<cfoutput>              
			   
				#local.personalInformationFieldSet.fieldSetContent#
				#local.contactInformationFieldSet.fieldSetContent#

				<cfif ListFindNoCase("Executive Director",local.membershipCatSelected)>
					#local.executiveDirectorInformationFieldSet.fieldSetContent#
				</cfif>

				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							#local.strResult.formContent#
							<br/>
						</div>
						<br/>
						<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
						<br/>
						</td>
					</tr>
				</table>

				<cfif local.paymentRequired>
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(local.strData.three,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>


			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<p>#variables.strPageFields.ConfirmationContent#</p>
				</cfif>

				<p>Here are the details of your application:</p>	

				#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfsavecontent variable="local.specialText">
			<cfoutput>

			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<p>#variables.strPageFields.ConfirmationContent#</p>
				</cfif>
				 #local.specialText#
				<p>Here are the details of your application:</p>

				#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>

		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
					<cfif len(variables.strPageFields.ConfirmationContent)>
						<p>#variables.strPageFields.ConfirmationContent#</p>
					</cfif>
					<p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID= variables.siteID)>

		<!--- relocate to message page --->
		<cfset session.invoice = local.invoice />

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
				</cfif>
				<div class="tsAppSectionContentContainer">
					<p>Here are the details of your application:</p>						
					#session.invoice#
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="hasSub" access="private" output="false" returntype="string">
		<cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

		<cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.isExist = false/>
		<cfset local.strData = {}>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, arguments.step)>
			<cfset local.strData = session.formFields[arguments.step]/>
		</cfif>			

		<cfreturn local.strData>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="CPSectionContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						#replaceNoCase(variables.strPageFields.BilledMessage,"[[click here]]","<a href='#local.redirectLink#'>here</a>")#
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelseif arguments.errorCode eq "admin">
						<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>