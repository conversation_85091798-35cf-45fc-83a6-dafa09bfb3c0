<cfsavecontent variable="local.invCSS">
	<cfoutput>
	<script language="javascript">
		function sendReceipt() {
			var emailRegEx = new RegExp("#application.regEx.email#","i");
			var btn = $('##btnResendReceipt');
			var msg = $('##buyNowSendReceiptMsg');
			var em = $('##sendToEmail');
			msg.html('').removeClass('buyNow-text-danger buyNow-font-weight-bold').hide();
			btn.html('Sending...').attr("disabled", true);

			var resendReceiptResult = function(r) {
				btn.html('Send').attr("disabled", false);
				if (r.success && r.success.toLowerCase() == 'true') {
					msg.html('<i class="icon-check"></i> &nbsp; <b>Receipt sent to '+em.val()+'.</b>').show().fadeOut(5000);
					em.val('');
				} else {
					msg.html('There was a problem e-mailing this receipt. Try again.').addClass('buyNow-text-danger buyNow-font-weight-bold').show();
				}
			};
			
			em.val($.trim(em.val()));
			if (em.val().length == 0 || !(emailRegEx.test(em.val()))) {
				btn.html('Send').attr("disabled", false);
				msg.html('Enter a valid e-mail address.').addClass('buyNow-text-danger buyNow-font-weight-bold').show();
				return false;
			}

			var objParams = { receiptUUID:'#local.strBuyStatus.resendKey#', sendToEmail:em.val() };
			TS_AJX('INV','sendPaymentReceipt',objParams,resendReceiptResult,resendReceiptResult,20000,resendReceiptResult);
		}		
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.invCSS)#">

<cfoutput>
<div class="buyNow-mb-3 buyNow-noprint">
	<cfif isDefined("local.strBuyStatus.emailSentTo")>
		We've emailed a copy of this receipt along with the invoice to <b>#local.strBuyStatus.emailSentTo#</b>.<br/>
	</cfif>
	<a href="/?pg=invoices">Click here</a> to pay another invoice.
</div>
<form name="frmResendReceipt" id="frmResendReceipt" onsubmit="sendReceipt();return false;" class="buyNow-mb-4 buyNow-noprint">
	<input type="hidden" name="uuid" id="uuid" value="#local.strBuyStatus.resendKey#">
	<div class="buyNow-d-flex buyNow-flex-sm-column">
		<div class="buyNow-col-auto buyNow-align-self-center buyNow-align-self-sm-start">Email this receipt to: </div>
		<div class="buyNow-col-auto">
			<div class="buyNow-input-append">
				<input type="text" name="sendToEmail" id="sendToEmail" class="buyNow-formcontrol" value="" size="30" value="" placeholder="<EMAIL>" maxlength="200" autocomplete="off">
				<button type="submit" name="btnResendReceipt" id="btnResendReceipt" class="buyNow-add-on buyNow-font-size-md buyNow-appendbtn" style="width:65px!important;" onclick="sendReceipt();">Send</button>
			</div>
			<div id="buyNowSendReceiptMsg" class="buyNow-font-size-sm" style="display:none;"></div>
		</div>
	</div>
</form>
<div class="buyNow-card buyNow-mb-3">
	<div class="buyNow-card-header buyNow-bg-whitesmoke buyNow-pb-1">
		<div class="buyNow-font-size-lg buyNow-font-weight-bold">#local.strBuyStatus.emailTitle#</div>
	</div>
	<div class="buyNow-card-body buyNow-pb-2">
		#local.strBuyStatus.emailconfirmationcontent#
	</div>
</div>
</cfoutput>