<cfsavecontent variable="local.workspaceJS">
	<cfoutput>
	<script type="text/javascript">
		function showAssignedProspects() {
			$('##divAssignedProspects').show(300);
			$('##btnShowAssignedProspects').hide();
			return false;
		}
		function showTaskDetails(tid,pid) { 
			self.location.href = '#attributes.data.mainurl#&wsAction=showTaskDetail&pid='+pid+'&tid='+tid;
		}
		function initTaskProspectsSection(tid) {
			var totalHeight = 0;
			$.each($('##tskProspectDetails').children(), function() { 
				totalHeight += $(this).height(); 
			});
			if ($('##tskAssignedProspects').height() > totalHeight + 200) {
				/* $('##tskAssignedProspects').css({'height':(totalHeight + 175) +'px', 'overflow-y':'scroll' }); */

				if (tid !== undefined) {
					$('##tskProspect'+tid).get(0).scrollIntoView();
					if (!isScrolledIntoView($('##tskProspectDetails')))
						$('##tskProspectDetails').get(0).scrollIntoView();
				}
			}
		}
		function isScrolledIntoView(elem) {
			var docViewTop = $(window).scrollTop();
			var docViewBottom = docViewTop + $(window).height();

			var elemTop = $(elem).offset().top;
			var elemBottom = elemTop + $(elem).height();

			return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
		}
		function showChooseTasks(pid) { 
			self.location.href = '#attributes.data.mainurl#&wsAction=showChooseTasks&pid='+pid;
		}
		function clearTaskForm(pid){
			$('##fFirstName').val('');
			$('##fLastName').val('');
			$('##fCompany').val('');
			$('##fCity').val('');
			$('##fStateID').val(0);
			$('##fMemberZIPRadius').val('');
			$('##fMemberZIP').val('');
			self.location.href = '#attributes.data.mainurl#&wsAction=showChooseTasks&pid='+pid;
		}
		function assignTaskToMember(pid,taskID,siteID,psrID) {
			$('button##btnAssignTask'+taskID).attr('disabled',true);

			var assignTaskResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var newTaskBlockHTML = $('##newTaskBlockHTML_x').html();
					newTaskBlockHTML = newTaskBlockHTML.replace(/{task_id}/g,r.taskinfo.taskid);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{project_id}/g, pid);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{prospect_img}/g, (r.taskinfo.hasmemberphotothumb?'/memberphotosth/'+r.taskinfo.membernumber+'.jpg':'/assets/common/images/socialNetwork/defaultMD.jpg'));
					newTaskBlockHTML = newTaskBlockHTML.replace(/{prospect_name}/g, r.taskinfo.displayname);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{company}/g, r.taskinfo.company);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{status_name}/g, r.taskinfo.statusname);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{last_activity}/g, r.taskinfo.datelastmodified);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{featured_workspacetaskfielddata}/g, r.taskinfo.featuredworkspacetaskfielddata);
					newTaskBlockHTML = newTaskBlockHTML.replace(/{featured_projecttaskfielddata}/g, r.taskinfo.featuredprojecttaskfielddata);
					$('##tskAssignedProspects').append(newTaskBlockHTML);
					if($('.divAssignedTasksNoEntries')) $('.divAssignedTasksNoEntries').remove();
					$('##taskRow'+taskID).remove();
					if($('##taskSearchResultBody').find('tr').length == 0){
						$('##taskSearchResultBody').append('<tr><td colspan="3"><p class="text-error">No more unassigned tasks based on your search criteria.</p></td></tr>');
					}
				} else if (r.success && r.success.toLowerCase() == 'false' && r.msg && r.msg.length){
					alert(r.msg);
				} else {
					alert('We were unable to assign this task to you.');
				}
				$('button##btnAssignTask'+taskID).attr('disabled',false);
			};
			var objParams = { projectID:pid, taskID:taskID, memberID:#attributes.data.orgMemberID#, projectSiteResourceID:psrID };
			TS_AJX('ADMWORKSPACE','assignTaskToMember',objParams,assignTaskResult,assignTaskResult,10000,assignTaskResult);
		}
		function doTaskFieldsValidate(taskfieldsWrapper) {
			var errorMsgArray = [];

			var thisInstance = $(taskfieldsWrapper);

			/*required fields*/
			var taskFieldRequired = thisInstance.find('input:text[data-mctaskfieldisrequired="1"], select[data-mctaskfieldisrequired="1"], textarea[data-mctaskfieldisrequired="1"]').not(':hidden').not(':disabled');

			/*distinct radio, checkbox elements*/
			var radioCheckBoxElements = thisInstance.find('input:radio[data-mctaskfieldisrequired="1"], input:checkbox[data-mctaskfieldisrequired="1"]');

			var elemArr = [];
			$.each( radioCheckBoxElements, function() {
				var elemName = this.name;
				if( $.inArray( elemName, elemArr ) < 0 ){
					elemArr.push(elemName);
					taskFieldRequired.push(this);
				}
			});

			var taskFieldRequiredErrorMsgArray = $.map(taskFieldRequired,validateMCTask_fieldIsRequired);
			Array.prototype.push.apply(errorMsgArray, taskFieldRequiredErrorMsgArray);

			/*text controls offering whole number*/
			var textControlIntegerCustomField = thisInstance.find('input[data-mctaskfielddisplaytypecode="TEXTBOX"][data-mctaskfielddatatypecode="INTEGER"]').not(':hidden').not(':disabled');
			var textControlIntegerCustomFieldErrorMsgArray = $.map(textControlIntegerCustomField,validateMCTask_textControlValidInteger);
			Array.prototype.push.apply(errorMsgArray, textControlIntegerCustomFieldErrorMsgArray);

			/*text controls offering decimal number*/
			var textControlDecimalCustomField = thisInstance.find('input[data-mctaskfielddisplaytypecode="TEXTBOX"][data-mctaskfielddatatypecode="DECIMAL2"]').not(':hidden').not(':disabled');
			var textControlDecimalCustomFieldErrorMsgArray = $.map(textControlDecimalCustomField,validateMCTask_textControlValidDecimal);
			Array.prototype.push.apply(errorMsgArray, textControlDecimalCustomFieldErrorMsgArray);

			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return thisError;
				else return null;
			});
			
			return finalErrors;
		}
		function validateMCTask_fieldIsRequired(thisField) {
			var fld = $(thisField);
			var fldName = $(thisField).attr('name');
			var displayTypeCode = fld.data('mctaskfielddisplaytypecode');
			var returnMsg = '';

			switch(displayTypeCode) {
				case 'TEXTBOX':
				case 'TEXTAREA':
				case 'DATE':
					if (fld.val() == '') {
						returnMsg =  fld.data('mctaskfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
				case 'SELECT':
					if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
						returnMsg =  fld.data('mctaskfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
				case 'RADIO':
				case 'CHECKBOX':
					if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
						returnMsg =  fld.data('mctaskfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
					}
				break;
			}
			return returnMsg;
		}
		function validateMCTask_textControlValidInteger(thisField) {
			var returnMsg = '';
			var fld = $(thisField);
			var fldval = Number(fld.val().trim());

			if (fldval != '' && fldval !== parseInt(fldval)) {
				returnMsg = 'Enter a valid whole number for ' + fld.data('mctaskfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
			return returnMsg;
		}
		function validateMCTask_textControlValidDecimal(thisField) {
			var returnMsg = '';
			var fld = $(thisField);
			var fldval = Number(fld.val().trim());

			if (fldval != '') {
				if (fldval !== parseFloat(fldval)) {
					returnMsg = 'Enter a valid decimal number for ' + fld.data('mctaskfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				}
			}
			return returnMsg;
		}
		function clearSearchAssignedTasks() {
			$('##fFirstName,##fLastName,##fCompany,##fTaskStatus').val('');
			$('##frmSearchAssignedTasks').submit();
		}
		<!--- Assigned to You column filters --->
		function toggleAssignedTaskFilters(label) {
			var isOpen = $('.tskSideBarFilters').is(':visible');
			$('.tskSideBarFilters').toggle(!isOpen);
			toggleFilterButtonLabel(!isOpen, label);
		}
		function toggleFilterButtonLabel(f, label){
			$('##filterProspectsBtn').html(f ? 'Close Filters <i class="icon-remove"></i' : 'Filter '+ label +' <i class="icon-filter"></i>');
		}
		function doFilterAssignedTasks() {
			var fd = {};
				fd.memberID = $('##fMemberID2').val();
				fd.projectID = $('##fProjectID2').val();
				fd.selectedTaskID = $('##fSelectedTaskID2').val();
				fd.firstName = $('##fFirstName2').val();
				fd.lastName = $('##fLastName2').val();
				fd.company = $('##fCompany2').val();
				fd.taskStatus = $('##fTaskStatus2').val();

			$('##btnAssignedTasksFilter,##btnResetAssignedTasksFilter').prop('disabled',true);
			$('.tskSideBar ##tskAssignedProspects')
				.html('<div class="divLoadingAssignedTasks"><i class="icon-spin icon-spinner"></i> <b>Please wait.</b></div>')
				.load('#arguments.event.getValue('resourceurl')#&wsAction=getAssignedTasksByFilters&mode=stream', fd, 
						function() {
							$('.tskSideBar i.icon-filter').show();
							$('##btnAssignedTasksFilter,##btnResetAssignedTasksFilter').prop('disabled',false);
						});
		}
		function clearAssignedTasksFilters() {
			$('##frmFilterAssignedTasks').trigger("reset");
			doFilterAssignedTasks();
		}
		hideAlert = function() { $('##task_frm_err').html('').hide(); };
		showAlert = function(msg) { $('##task_frm_err').html(msg).show(); };

		$(function () {
			$(".MCProjectDesc").each(function () {
				$(this).next().toggle(this.scrollHeight > this.offsetHeight);
			});
		});
	</script>
	<style type="text/css">
		div##MCWorkspaceContainer p { margin:0 0 10px 0 !important; }
		.MCProjectDesc { height:45px; overflow:hidden; }
		.divProspectDetails { height:275px; overflow:hidden; }
		.ellipsis { height:0; position:relative; top:-1.2em; text-align:right; }
		.ellipsis span { padding-left:0.5em; position:relative; top:-0.25em; font-weight:bold; }
		h4 { margin-top:0; }
		.row-fluid.display-flex { display: flex; flex-wrap: wrap; }
		.row-fluid.display-flex .span3 > .tskAssigned { display:flex; flex-direction:column; background-color:##ececec; border:1px solid ##e3e3e3; border-radius:5px; }
		.row-fluid.display-flex .sideBarContainer { margin-top:20px; }
		.tskAssignedLabel { color:##fff; text-align:center; font-size:20px !important; padding:10px; background-color:##a0a0a0; margin-bottom:10px; border-radius:5px; min-height:40px !important; }
		.tskAssignedBox { font-size:11px !important; background-color:##fbfbfb; cursor:pointer; }
		.tskAssignedBox:hover, .tskOnSelected { border:2px solid ##868686; font-size:12px !important; box-shadow: 0 0 10px ##0f0c0c; }
		div##divAssignedProspects li.span4:not(:nth-child(3n + 2)) { margin-left:0 !important; }
		div##divAssignedProspects li.span4:nth-child(3n + 2) { margin:0 10px !important; }
		.tskWidth2 { width:2%; float:left; display:inline-block; }
		.tskWidth20 { width:20%; float:left; display:inline-block; }
		.tskWidth78 { width:78%; float:left; display:inline-block; }
		div.ind { margin-left:10px; }
		div.ind div { margin-bottom:2px; }
		div.ind div.com { margin-bottom:6px; }
		div.bn { padding:5px 0px; } 
		div.bn2 { padding-top:2px; } 
		div.txtCenter {text-align:center;}
		.subContentPadding { margin-left:15px;}
		table.tblTaskAssignedMember tr td { border:0; }
		input.projectTaskdate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; width:90px; }
		.clearProjectTaskDate { vertical-align:text-bottom; }
		##taskSearchFormContainer { margin-top:8px;margin-bottom:10px; }
		##taskSearchResultBody {font-size: 12px;}
		.tskAlignMiddle { vertical-align:middle; }
		.tskAlignTop { vertical-align:top; }
		div.divChooseProspects, div.divFilterProspects { padding-bottom:10px; }
		button.btnChooseProspects, button.btnFilterProspects { height:35px !important; font-size:16px !important; }
		##frmFilterAssignedTasks input, ##frmFilterAssignedTasks select { width:100%; box-sizing:border-box;}
		.divLoadingAssignedTasks, .divAssignedTasksNoEntries { text-align:center;margin:10px 0; }
		.tskSideBar i.icon-filter { vertical-align:baseline; margin-left:3px; }
		.divFilterProspects button i[class^='icon-'] { vertical-align: baseline; margin-left: 5px; }
		##MCWorkspaceContainer .mctask-d-flex {display:flex;}
		##MCWorkspaceContainer .mctask-flex-wrap {flex-wrap:wrap;}
		##MCWorkspaceContainer .mctask-w-45 {width:45%;}
		##MCWorkspaceContainer .mctask-m-2 {margin:0.5em;}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.workspaceJS)#">