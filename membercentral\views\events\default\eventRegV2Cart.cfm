<cfset local.extrapayJS = "">
<cfinclude template="../commonEventRegCartJS.cfm">
<cfinclude template="../commonEventRegStyles.cfm">

<cfoutput>
<div class="evRegV2">
	<cfif local.qryEvRegCart.recordCount>
		<div class="evreg-d-flex evreg-mt-3">
			<div class="evreg-font-size-lg evreg-col evreg-p-0">To complete this registration, enter your payment information below</div>
			<div class="evreg-font-size-sm evreg-col-auto evreg-text-right">
				<a href="#arguments.event.getValue('mainurl')#" style="border-bottom:1px solid;text-decoration:none;">+ Register for Other Events</a>
			</div>
		</div>
		<div class="evreg-d-flex evreg-mt-3">
			<div class="evreg-font-size-xl evreg-font-weight-bold">Registration(s)</div>
			<div class="evreg-ml-auto evreg-pr-2">
				<div id="evRegCartTotalSummary" class="evRegCartTotals">
					<cfif val(local.qryEvRegCartTotals.totalDiscount) gt 0>
						<div class="evreg-card" style="box-shadow:none;border:none;background-color:##f7f7f7;">
							<div class="evreg-card-body">
								<div class="evreg-d-flex evreg-font-weight-bold evreg-font-size-xl">
									<div class="evreg-col">Total:</div>
									<div class="evreg-ml-auto">#DollarFormat(local.qryEvRegCartTotals.actualTotal)##local.displayedCurrencyType#</div>
								</div>

								<div class="evreg-text-right evreg-font-size-sm">
									<div class="evreg-strike">#DollarFormat(local.qryEvRegCartTotals.totalAmt)##local.displayedCurrencyType#</div>
									<div>#DollarFormat(local.qryEvRegCartTotals.totalDiscount)##local.displayedCurrencyType# discount applied</div>
								</div>
								<div class="evreg-font-size-sm evreg-mb-0 evreg-text-center evreg-p-1" style="max-width:200px;color:green;">#local.qryEvRegCartTotals.redeemDetail#</div>
								<div class="evreg-mt-2 evreg-text-right">
									<button type="button" name="btnRemoveCoupon" class="tsAppBodyButton btnRemoveCoupon" onclick="removeAppliedCoupon();">Remove Promo Code</button>
								</div>
							</div>
						</div>
					<cfelse>
						<div class="evreg-d-flex evreg-font-weight-bold evreg-font-size-xl">
							<div class="evreg-col evreg-text-center">Total:</div>
							<div class="evreg-ml-auto">#DollarFormat(local.qryEvRegCartTotals.totalAmt)##local.displayedCurrencyType#</div>
						</div>
					</cfif>
				</div>
				<cfif NOT structIsEmpty(local.strEvRegCartProfiles.strProfileMatchingReg)>
					<cfloop list="#structKeyList(local.strEvRegCartProfiles.strProfileMatchingReg)#" index="local.thisProfileID">
						<cfset local.strThisProfileReg = local.strEvRegCartProfiles.strProfileMatchingReg[local.thisProfileID]>
						<div id="evRegCartTotalSummary#local.thisProfileID#" class="evRegCartTotals" style="display:none;">
							<cfif val(local.strThisProfileReg.qryTotals.totalDiscount) gt 0>
								<div class="evreg-card" style="box-shadow:none;border:none;background-color:##f7f7f7;">
									<div class="evreg-card-body">
										<div class="evreg-d-flex evreg-font-weight-bold evreg-font-size-xl">
											<div class="evreg-col">Total:</div>
											<div class="evreg-ml-auto">#DollarFormat(local.strThisProfileReg.qryTotals.actualTotal)##local.displayedCurrencyType#</div>
										</div>

										<div class="evreg-text-right evreg-font-size-sm">
											<div class="evreg-strike">#DollarFormat(local.strThisProfileReg.qryTotals.totalAmt)##local.displayedCurrencyType#</div>
											<div>#DollarFormat(local.strThisProfileReg.qryTotals.totalDiscount)##local.displayedCurrencyType# discount applied</div>
										</div>
										<div class="evreg-font-size-sm evreg-mb-0 evreg-text-center evreg-p-1" style="max-width:200px;color:green;">#local.strThisProfileReg.qryTotals.redeemDetail#</div>
										<div class="evreg-mt-2 evreg-text-right">
											<button type="button" name="btnRemoveCoupon" class="tsAppBodyButton btnRemoveCoupon" onclick="removeAppliedCoupon();">Remove Promo Code</button>
										</div>
									</div>
								</div>
							<cfelse>
								<div class="evreg-d-flex evreg-font-weight-bold evreg-font-size-xl">
									<div class="evreg-col evreg-text-center">Total:</div>
									<div class="evreg-ml-auto">#DollarFormat(local.strThisProfileReg.qryTotals.totalAmt)##local.displayedCurrencyType#</div>
								</div>
							</cfif>
						</div>
					</cfloop>
				</cfif>

				<cfif local.hasAmountToCharge AND local.offerCoupon>
					<div class="evreg-mt-3 evreg-text-right">
						<div class="evreg-input-append">
							<input type="text" name="couponCode" id="couponCode" class="evreg-formcontrol" value="" size="18" value="" placeholder="Promo Code" maxlength="15" onkeypress="validateCouponCodeOnEnterKey(event);">
							<button type="button" name="btnApplyCouponCode" id="btnApplyCouponCode" class="evreg-add-on evreg-font-size-md" onclick="validateCouponCode();">Apply</button>
						</div>
						<div id="couponCodeResponse" class="alert evreg-p-1 evreg-mt-1 evreg-font-size-sm evreg-mb-0 evreg-text-center" style="display:none;"></div>
					</div>
				</cfif>
			</div>
		</div>

		<cfoutput query="local.qryEvRegCartSorted" group="memberID">
			<div class="evreg-card evreg-mt-3">
				<div class="evreg-card-header evreg-bg-whitesmoke evreg-pb-1">
					<div class="evreg-font-size-lg evreg-font-weight-bold">#local.qryEvRegCartSorted.memberName#</div>
				</div>
				<div class="evreg-card-body">
					<cfset local.thisRowNum = 0>
					<cfoutput>
						<cfset local.thisRowNum++>
						<div id="evRegKey#local.qryEvRegCartSorted.itemKey#" class="evreg-d-flex evreg-mb-3 evregCartItem">
							<div class="evreg-col-auto evreg-font-weight-bold">#local.thisRowNum#.</div>
							<div class="evreg-col">
								<div class="evreg-font-weight-bold">
									#encodeForHTML(local.qryEvRegCartSorted.eventName)# <span class="hidden-phone">(#local.qryEvRegCartSorted.ratename#)</span>
								</div>
								<cfif len(local.qryEvRegCartSorted.eventSubTitle)><div class="evreg-font-size-sm evreg-text-dim">#encodeForHTML(local.qryEvRegCartSorted.eventSubTitle)#</div></cfif>
								<div class="evreg-font-size-sm evreg-mt-1">#local.strEventTimes[local.qryEvRegCartSorted.eventID].eventtime#</div>
								<div class="evreg-font-size-sm evreg-mt-2">
									<a href="##" style="border-bottom:1px solid;text-decoration:none;" onclick="registerOthers(#local.qryEvRegCartSorted.eventID#);">+ Register Someone Else</a>
								</div>
								<div class="evRegCartItemResponse alert evreg-mt-2 evreg-mb-0" style="display:none;"></div>
								<cfif val(local.qryEvRegCartSorted.isRegistered)>
									<div class="alert">#local.qryEvRegCartSorted.memberName# is already registered for this event. Remove this registration to continue.</div>
								<cfelseif val(local.qryEvRegCartSorted.hasOverSoldItems)>
									<div id="overSoldItemAccordion#local.qryEvRegCartSorted.itemKey#" class="alert" onclick="showHideOverSoldItemPanel('#local.qryEvRegCartSorted.itemKey#');">
										<b>Note:</b> We changed this registration due to availability:
										<span class="showOverSoldItems">Click for more info</span><span class="hideOverSoldItems" style="display:none;">Click to hide</span>.
										<div class="issuePanel" style="display:none;">
											<table>
												<tr>
													<th class="tsAppBB20" style="width:70%;">Message</th>
													<th class="tsAppBB20" style="width:2%;">&nbsp;</th>
													<th class="tsAppBB20" style="width:25%;">Amount Not Recorded</th>
												</tr>
												<cfset local.totalAmtNotRecorded = 0>
												<cfloop list="#local.qryEvRegCartSorted.overSoldItems#" index="local.item" delimiters="~~">
													<cfset local.thisItemAmtNotRecorded = val(ListGetAt(local.item,2,"||"))>
													<cfset local.totalAmtNotRecorded = local.totalAmtNotRecorded + local.thisItemAmtNotRecorded>

													<tr>
														<td class="tsAppBB20">#ListGetAt(local.item,1,"||")#</td>
														<td class="tsAppBB20">&nbsp;</td>
														<td class="tsAppBB20">#DollarFormat(local.thisItemAmtNotRecorded)#</td>
													</tr>
												</cfloop>
												<tr>
													<td class="r"><b>Total Amount Not Recorded:</b></td>
													<td>&nbsp;</td>
													<td><b>#DollarFormat(local.totalAmtNotRecorded)#</b></td>
												</tr>
											</table>
										</div>
									</div>
								</cfif>
							</div>
							<div class="evreg-col-auto evreg-ml-auto evreg-w-25">
								<div class="evreg-d-flex">
									<div class="evreg-col evreg-text-center">
										<a href="##" id="edit_evrk_#local.qryEvRegCartSorted.itemKey#" onclick="editReg(#local.qryEvRegCartSorted.eventID#,'#local.qryEvRegCartSorted.itemKey#');return false;" class="evreg-mr-2 evreg-text-decoration-none" title="Edit Registration">
											<i class="icon-pencil"></i>
										</a>
										<a href="##" id="del_evrk_#local.qryEvRegCartSorted.itemKey#" onclick="removeReg('#local.qryEvRegCartSorted.itemKey#');return false;" class="evreg-text-decoration-none" title="Remove Pending Registration">
											<i class="icon-trash"></i>
										</a>
									</div>
									<div class="evreg-col-auto evRegCartItemTotal evreg-mw-25">
										<cfif val(local.qryEvRegCartSorted.discount) gt 0>
											<div class="evreg-font-weight-bold">
												<div class="evreg-font-size-lg">#DollarFormat(local.qryEvRegCartSorted.actualAmount)##local.displayedCurrencyType#</div>
												<div class="evreg-strike evreg-font-size-sm evreg-text-center">#DollarFormat(local.qryEvRegCartSorted.amount)##local.displayedCurrencyType#</div>
											</div>
										<cfelse>
											<div class="evreg-font-weight-bold evreg-font-size-lg evreg-text-right">#DollarFormat(local.qryEvRegCartSorted.amount)##local.displayedCurrencyType#</div>
										</cfif>
									</div>
								</div>
							</div>
						</div>
					</cfoutput>
				</div>
			</div>
		</cfoutput>

		<cfif local.showPaymentArea>
			<div class="evreg-card evreg-mt-5">
				<div class="evreg-card-header evreg-bg-whitesmoke evreg-pb-1">
					<div class="evreg-font-size-lg evreg-font-weight-bold">Payment Information</div>
				</div>
				<div class="evreg-card-body evreg-pb-2<cfif local.strEvRegCartProfiles.paymentGateways.recordcount EQ 1> evreg-pl-0</cfif>">
					<form name="frmPurchaseEvReg" id="frmPurchaseEvReg" method="post" action="#arguments.event.getValue('mainurl')#&evaction=addEventRegV2" onsubmit="return checkPayForm();">
						<input type="hidden" name="confirmPurchase" id="confirmPurchase" value="1">
						<input type="hidden" name="profileid" id="profileid" value="0">
						
						<div id="paymentTabsWrapper">
							<!--- area for payment error --->
							<div id="evRegPmtErr" style="display:none;margin:6px;"></div>
							
							<div id="paymentTabs">
								<cfif local.strEvRegCartProfiles.paymentGateways.recordcount gt 1>
									<div class="evreg-mb-3 evreg-text-dim"><b>Choose from the following payment methods:</b></div>

									<ul id="paymentTypeTabs" class="tsAppNavButtonGroup paymentTypePills" data-mcCollapsibleDivAutoBuildTabs="1" data-mcCollapsibleDivViewClass="paymentTypeViews"></ul>
									<cfinclude template="eventRegV2CartPayment.cfm">
								<cfelse>
									<div id="paymentTypeTabs" class="tsAppViewStackTopBorder">
										<cfinclude template="eventRegV2CartPayment.cfm">
									</div>
								</cfif>
							</div>
						</div>
					</form>
				</div>
			</div>
		<cfelse>
			<div class="evreg-mt-5">
				<cfset local.thisProfileID = local.strEvRegCartProfiles.paymentGateways.profileID[1]>
				<form name="frmPurchaseEvReg" id="frmPurchaseEvReg" method="post" action="#arguments.event.getValue('mainurl')#&evaction=addEventRegV2" onsubmit="return checkPayForm();">
					<input type="hidden" name="confirmPurchase" id="confirmPurchase" value="1">
					<input type="hidden" name="profileid" id="profileid" value="0">
					<input type="hidden" name="p_#local.thisProfileID#_mppid"  id="p_#local.thisProfileID#_mppid" value="0">
					<button type="submit" class="tsAppBodyButton" onclick="selectPayment(#local.thisProfileID#)">Complete Registration</button>
				</form>
			</div>
		</cfif>
	<cfelse>
		No Pending Registrations Found.
	</cfif>
</div>
</cfoutput>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<script language="javascript">
	var checkPayFormInProgress = false;
	function checkPayForm() {

		/* disable payment buttons while validation is running */
		$('button[type="submit"]',$('##frmPurchaseEvReg')).each(function(index,thisButton){
			$(thisButton).attr('disabled','disabled');
		});
	
		var validationPassed = true;

		/* prevent race condition caused by double submitting before validation can be completed */
		if (checkPayFormInProgress) {
			validationPassed = false;
		} else {
			checkPayFormInProgress = true;
			hideAlert();
			var arrReq = new Array();

			<cfif len(local.extrapayJS)>
				var thisForm = document.forms["frmPurchaseEvReg"];
				#local.extrapayJS#
			</cfif>
			
			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				validationPassed = false;
			}

			if (validationPassed) {
				/* change text of payment buttons and leave disabled */
				$('button[type="submit"]',$('##frmPurchaseEvReg')).each(function(index,thisButton){
					$(thisButton).text('Please Wait...');
				});
			} else {
				/* reenable buttons */
				$('button[type="submit"]',$('##frmPurchaseEvReg')).each(function(index,thisButton){
					$(thisButton).removeAttr("disabled");
				});
			}
			checkPayFormInProgress = false;
		}
		
		return validationPassed;
		
	};
	</script>
	<style type="text/css">
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">