<cfset local.strData = attributes.data>
<cfset local.searchTermList = []>
<cfset local.searchTermListStruct = {}>
<cfif StructKeyExists(local.strData,"arrSearchCriteria") AND ArrayLen(local.strData.arrSearchCriteria) >
	<cfloop from="1" to="#arrayLen(local.strData.arrSearchCriteria)#" index="local.thisItem">
		<cfset StructInsert(local.searchTermListStruct, 
			"#lcase(local.strData.arrSearchCriteria[local.thisItem].fieldlabel.replaceall(' '  ,'_'))#", trim(local.strData.arrSearchCriteria[local.thisItem].fieldValue),true
		)>
	</cfloop>
	<cfset arrayAppend(local.searchTermList,local.searchTermListStruct)>
</cfif>
<cfsavecontent variable="local.css">
	<style type="text/css">
	ul.groupImage { width:100%; text-align:left; float:left; margin:0px; padding:0; padding-left:0;list-style-type:none; }
	ul.groupImage li { display:inline; padding:0; padding-left:0; }
	</style>
</cfsavecontent>
<cfsavecontent variable="local.searchResultsJS">
<cfoutput>
	<script language="javascript">
		$(function() {				
			<cfset local.searchTermsJSON = SerializeJSON(local.searchTermList)>
			<cfif IsJSON(local.searchTermsJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/members/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggersearchResultsList('#local.strData.pageName#',#local.searchTermsJSON#);						
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
	</script>
</cfoutput>	
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.searchResultsJS)#">	
<cfhtmlhead text="#local.css#">

<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
	<cfsavecontent variable="local.jsFunc">
		<cfoutput>
		<script language="javascript">		
			<cfif local.strData.numInCompare lte 1>
				runCompareOK = false;
			<cfelse>
				runCompareOK = true;
			</cfif>
			function toggleCompare(btn,mid) {
				btn.html('<i class="icon-spinner icon-spin"></i> Add to Compare').attr('disabled',true);
		        $.ajax({
		            url: '#local.strData.linkForToggleCompare#&dirMemberID='+mid,
		            dataType: 'json',
		            success: function(response){ 
		            	if (response.SUCCESS == true) {
		            		if (response.MEMINCOMP == true) btn.html('<i class="icon-check"></i> Add to Compare').attr('disabled',false); 
	            			else btn.html('<i class="icon-check-empty"></i> Add to Compare').attr('disabled',false); 

		            		$('##btnCompare').html('Compare Listings (' + parseInt(response.NUMINCOMP) + ')');

			            	if (parseInt(response.NUMINCOMP) > 1)
			            		runCompareOK = true;
			            	else
			            		runCompareOK = false;
		            	} else {
		            		btn.attr('disabled',false);
		            	}	
		            },
		            error: function(ErrorMsg){ btn.attr('disabled',false); }
		        })
			}
			function runCompare() {
				if (runCompareOK) {
					var #ToScript(local.strData.linkForCompare,'link')#
					self.location.href = link;
				} else {
					alert('You must select at least 2 listings to compare.');
				}
			}
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.jsFunc#">
</cfif>

<cfoutput>
	<div class="container-fluid">
		<div class="row-fluid">
			<div class="page-header span12"><h2>#attributes.data.memberDirectoryInfo.applicationInstanceName#</h2></div>
		</div>
		<cfif isdefined("local.strData.overlimitmessage")>
			<div class="row-fluid">
				<div class="alert span12" style="padding:6px;">#local.strData.overlimitmessage#</div>
			</div>
		</cfif>
		<div class="row-fluid">
			<ul class="breadcrumb span12">
				<li><a href="/?#local.strData.baseQueryString#&dirAction=search">Search</a> <span class="divider">/</span></li>
				<li class="active"><a href="##">Results</a></li>
			</ul>
		</div>

		<cfif Arraylen(local.strData.arrSearchCriteria)>
			<div class="row-fluid">
				<div class="alert alert-info span12">
					<b>Search Criteria</b>
					<cfif Arraylen(local.strData.arrSearchCriteria) gt 2> 
						<div class="more-less">	
						<div class="more-block">
					<cfelse>
						<div>
					</cfif>
					<cfloop array="#local.strData.arrSearchCriteria#" index="local.x">
						#local.x.fieldLabel##local.x.fieldseparator#<em>#local.x.fieldValue#</em><br/>
					</cfloop>
					<cfif Arraylen(local.strData.arrSearchCriteria) gt 2> 
						</div>
						</div>
					<cfelse>
						</div>
					</cfif>
				</div>
			</div>
		</cfif>

		<cfif not arraylen(local.strData.arrMembers)>
			<div class="row-fluid">
				<div class="alert alert-error span12">
					Sorry, there are no results based on your search criteria.
					<a class="btn btn-large pull-right" href="/?#local.strData.baseQueryString#&dirAction=search">Search Again</a>
				</div>
			</div>
		<cfelse>
			<cfif (local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0) OR ((local.strData.prevPage NEQ 0) OR (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount))>
				<div class="row-fluid">
					<div class="btn-toolbar pull-right">
						<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
							<button type="button" class="btn btn-small btn-inverse" id="btnCompare" onClick="runCompare();">Compare Listings (#local.strData.numInCompare#)</button>
						</cfif>

						<cfif (local.strData.prevPage NEQ 0) OR (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount)>
							<cfif local.strData.prevPage NEQ 0>
								<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.prevPage#" class="btn btn-small btn-primary">&larr; Previous</a>
							</cfif>
							<cfif ((local.strData.nextPage-1)*local.strData.rowsize + 1) lte local.strData.qryMembersCount>
								<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.nextPage#" class="btn btn-small btn-primary">Next &rarr;</a>
							</cfif>
						</cfif>
					</div>
				</div>
			</cfif>

			<cfset local.gridClasses = structNew() />
			<cfset local.photoFound = false>
			<cfloop array="#local.strData.arrMembers#" index="local.thisMember">
				<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowPhoto>
					<cfset local.photoFound = true>
					<cfbreak>
				</cfif>
			</cfloop>
			<cfif local.photoFound>
				<cfset local.gridClasses.photo = "span2">
				<cfset local.gridClasses.fieldset = "span5">
				<cfset local.gridClasses.classifications = "span5">
			<cfelse>
				<cfset local.gridClasses.photo = "span2">
				<cfset local.gridClasses.fieldset = "span5">
				<cfset local.gridClasses.classifications = "span5">
			</cfif>
			
			<cfloop array="#local.strData.arrMembers#" index="local.thisMember">

				<cfset local.thisMemberPerms = local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID]>
				<cfif local.thisMemberPerms.resultsShowDetailsLink and (
						local.strData.resultDetailDisplayDifferences.isAnythingDifferent
						or local.strData.memberDirectoryInfo.includeContactFormOnDetails is 1
						or (local.thisMemberPerms.resultsShowVcard neq local.thisMemberPerms.detailsShowVcard) 
						or (local.thisMemberPerms.resultsShowPhoto neq local.thisMemberPerms.detailsShowPhoto)
						or (local.thisMemberPerms.resultsShowMap neq local.thisMemberPerms.detailsShowMap)
					)>
					<cfset local.thisMemberShowDetailLink = true>
				<cfelse>
					<cfset local.thisMemberShowDetailLink = false>
				</cfif>
				<div class="row-fluid">
				<div class="span12 well">
					<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowPhoto>
						<div class="memberDirectory-photo #local.gridClasses.photo#" style="padding-bottom: 15px;">
							<cfif arrayLen(local.thisMember.arrAboveClassifications)>
								<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-aboveClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								<br clear="all"/>
							</cfif>
							<cfset local.imgToUse = local.thisMember.imgToUse>
							<cfif len(trim(local.imgToUse))>
								<a href="/?#local.strData.baseQueryString#&dirAction=memberDetails&dirMemberid=#local.thisMember.memberID#">#local.imgToUse#</a>
							</cfif>
							<cfif arrayLen(local.thisMember.arrBelowClassifications)>
								<br clear="all"/>
								<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-belowClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								<br clear="all"/>
							</cfif>
						</div>
					<cfelse>
						<div class="#local.gridClasses.photo#" style="margin-bottom:10px;">
							<cfif arrayLen(local.thisMember.arrAboveClassifications)>
								<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
									<div class="thumbnail pull-left"><img class="directory-groupImage directory-groupImage-aboveClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
							</cfif>
							<cfif arrayLen(local.thisMember.arrBelowClassifications)>
								<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
									<div class="thumbnail pull-left"><img class="directory-groupImage directory-groupImage-belowClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								
							</cfif>
						</div>
					</cfif>
					<div class="#local.gridClasses.fieldset#" style="margin-bottom:10px;">
							<!--- name/company --->
							<div class="MCDirectoryNameBlock">
								<cfif len(local.thisMember.stFullName)>
									<span class="MCDirectoryName"><b>#local.thisMember.stFullName#</b></span><br>
								</cfif>
								<span class="MCDirectoryCompany">
									<cfif not len(local.thisMember.stFullName) and len(local.thisMember.stCompany)>
										<b>#local.thisMember.stCompany#</b><br>
									<cfelseif len(local.thisMember.stCompany)>
										#local.thisMember.stCompany#<br>
									</cfif>
								</span>
							</div>
							<!--- addresses --->
							<cfif arrayLen(local.thisMember.mc_combinedAddresses)>
								<cfloop index="local.addri" from="1" to="#arrayLen(local.thisMember.mc_combinedAddresses)#">
									<cfif len(local.thisMember.mc_combinedAddresses[local.addri].addr)>
										<br/>
										<cfif local.strData.multipleAddressTypesDetected>
											<b>#local.thisMember.mc_combinedAddresses[local.addri].label#</b><br/>
										</cfif>
										#local.thisMember.mc_combinedAddresses[local.addri].addr#
									</cfif>	
								</cfloop>
								<br/>
							</cfif>

							<!--- phones --->
							<cfloop array="#local.thisMember.arrPhones#" index="local.thisField">
								<cfif len(local.thisField.phone) and local.strData.makePhoneNumbersClckable>
									<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
										<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue"><a href="tel:#local.thisField.phone#">#local.thisField.phone#</a></span>
									</div></div>
								<cfelseif len(local.thisField.phone)>
									<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
										<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue">#local.thisField.phone#</span>
									</div></div>
								</cfif>
							</cfloop>
							<!--- emails --->
							<cfloop array="#local.thisMember.arrEmails#" index="local.thisField">
								<cfif len(local.thisField.email)>
									<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField" style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
										<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue"><a href="mailto:#local.thisField.email#">#local.thisField.email#</a></span>
									</div></div>
								</cfif>
							</cfloop>
							<!--- websites --->
							<cfloop array="#local.thisMember.arrWebsites#" index="local.thisField">
								<cfif len(local.thisField.website)>
									<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField" style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
										<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue"><a href="#local.thisField.website#" target="_blank">#local.thisField.website#</a></span>
									</div></div>
								</cfif>
							</cfloop>

							<!--- membernumber --->
							<cfif len(local.thisMember.stMemberNumber)><div>#local.thisMember.stMemberNumber#</div></cfif>

							<!--- memberdata / districting --->
							<cfloop array="#local.thisMember.arrMemberDataDistricting#" index="local.thisField">
								<cfif len(local.thisField.value)>
									<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
										<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue">
										<cfif local.thisField.allowMultiple is 1>
											#ReplaceNoCase(local.thisField.value,"|",", ","ALL")#
										<cfelseif local.thisField.dataTypeCode EQ "DOCUMENTOBJ">
											<cfset local.stDocEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.thisField.columnID#|#right(GetTickCount(),5)#|#local.thisMember.memberID#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
											<cfoutput><a href="/?event=cms.showResource&dirAction=memberResultsDocDownload&resID=#attributes.data.instanceSettings.siteResourceID#&mode=stream&doc=#local.stDocEnc#" target="_blank">#local.thisField.value#</a></cfoutput>
										<cfelse>
											#local.thisField.value#<br/>
										</cfif></span>
									</div></div>
								</cfif>
							</cfloop>

							<!--- member prof license data --->
							<cfloop array="#local.thisMember.arrLicenseData#" index="local.thisField">
								<cfif len(local.thisField.value)>
									<div class="MCDirectoryFieldsWrapper"><div class="MCDirectoryField">
										<span class="MCDirectoryFieldLabel">#htmlEditFormat(local.thisField.fieldLabel)#: </span><span class="MCDirectoryFieldValue">#local.thisField.value#</span>
									</div></div>
								</cfif>
							</cfloop>

							<!--- website social icons --->
							<cfif ArrayLen(local.thisMember.arrWebsitesSocialIcons) gt 0 OR
									local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowVcard or 
										(local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr))>
								<div style="padding:2px;">
									<cfloop array="#local.thisMember.arrWebsitesSocialIcons#" index="local.thisField">
										#local.thisField.socialLink#
									</cfloop>

									<!--- VCARD / Google Maps link --->
									<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowVcard>
										<a href="/?event=cms.showResource&dirAction=memberDetailsVCard&resID=#local.strData.instanceSettings.siteResourceID#&mode=stream&dirMemberid=#local.thisMember.memberID#">
											<img src="/assets/common/images/vcard-icon3.png" alt="[vCard]" width="32" height="32" border="0"></a>
									</cfif>
									<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr)>
										<cfset local.thisMap = local.strData.mappingBaseLink & URLEncodedFormat(local.thisMember.mc_combinedAddresses[1].mapaddr)>
										<a href="#local.thisMap#" target="_blank"><img src="/assets/common/images/Maps-icon.png" alt="[View Map]" width="32" height="32" border="0" align="absmiddle"></a>
									</cfif>

								</div>
							</cfif>
							<cfif local.thisMemberShowDetailLink>
								<div class="hidden-phone">
									<br /><br />
									<button type="button" class="btn btn-info" onclick="self.location.href='/?#local.strData.baseQueryString#&dirAction=memberDetails&dirMemberid=#local.thisMember.memberID#';">View Full Profile</button>
								</div>
							</cfif>
							<div class="visible-phone" style="margin-bottom: 15px;">
								<br />
								<cfif local.thisMemberShowDetailLink>
									<button type="button" class="btn btn-info" onclick="document.location.href='/?#local.strData.baseQueryString#&dirAction=memberDetails&dirMemberid=#local.thisMember.memberID#';">View Full Profile</button>
								</cfif>
								<cfif local.thisMemberShowDetailLink or (local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0)>&nbsp;</cfif>
								<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
									<button type="button" class="btn btn-mini" onclick="toggleCompare($(this),#local.thisMember.memberID#);"><i class="<cfif local.thisMember.inCompare>icon-check<cfelse>icon-check-empty</cfif>"></i> Add to Compare</button>
								</cfif>
								<br/>
							</div>
					</div>
					
					<div class="#local.gridClasses.classifications#">
						<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
							<div class="hidden-phone">
								<button type="button" class="btn btn-mini" onclick="toggleCompare($(this),#local.thisMember.memberID#);"><i class="<cfif local.thisMember.inCompare>icon-check<cfelse>icon-check-empty</cfif>"></i> Add to Compare</button>
								<br/><br/>
							</div>
						</cfif>

						<cfif arrayLen(local.thisMember.arrClValues)>
							<cfset local.clTitle = "">
							<cfloop array="#local.thisMember.arrClValues#" index="local.thisClItem">
								<cfif local.clTitle neq local.thisClItem.clName>
									<cfif local.clTitle neq ""><br/></cfif>
									<cfset local.clTitle = local.thisClItem.clName>
									<b>#local.thisClItem.clName#</b><br>
								</cfif>
								#local.thisClItem.groupName#<br/>
							</cfloop>
							<br/>
						</cfif>
						&nbsp;
						<cfif arrayLen(local.thisMember.arrUnderClassifications)>
							<ul class="groupImage">
							<cfloop array="#local.thisMember.arrUnderClassifications#" index="local.thisImg">
								<li><img class="directory-groupImage directory-groupImage-underClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></li>
							</cfloop>
							</ul>
						</cfif>
					</div>
				</div>
				</div>
			</cfloop>

			<cfif (local.strData.prevPage NEQ 0) OR (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount)>
				<div class="btn-toolbar pull-right">
					<cfif local.strData.prevPage NEQ 0>
						<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.prevPage#" class="btn btn-small btn-primary">&larr; Previous</a>
					</cfif>
					<cfif ((local.strData.nextPage-1)*local.strData.rowsize + 1) lte local.strData.qryMembersCount>
						<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.nextPage#" class="btn btn-small btn-primary">Next &rarr;</a>
					</cfif>
				</div>
			</cfif>
		</cfif>
	</div>
</cfoutput>