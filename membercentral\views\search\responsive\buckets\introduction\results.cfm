<cfoutput>
<div name="divExposition" id="divExposition">
	<div class="row-fluid hidden-phone">
		<img src="/assets/common/images/search/searchLeftArrow.png" alt="" /><br />
		<div class="s_rbnm span12" style="margin-left:30px;">
			<div class="resultMsgExposition">#local.scanMessage#</div>
		</div>
	</div>
	<div class="visible-phone row-fluid">
		<div class="span12" style="text-align:center;">
			<i class="icon-folder-close icon-large icon-2x"></i><br />
			<div class="resultMsgExposition">#local.scanMessage#</div>
		</div>
	</div>
</div>
<br /><br />
<div name="divIntroSearch" id="divIntroSearch" class="hide">
	<div class="row-fluid">
		<div class="span12">
			<div class="s_rnfne">You may also search again:</div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span12">
			<form name="searchBoxIntro" id="searchBoxIntro" class="form-search" action="/?pg=search&bid=#arguments.bucketid#" method="POST" style="margin-bottom:0;" onSubmit="if ($('form##searchBoxIntro ##s_key_all').val().trim() == '') {$('form##searchBoxIntro ##s_key_all').focus(); return false;} else {return true;}">
				<input type="hidden" name="s_a" value="doSearch">
				<input type="hidden" name="s_frm" value="1">
				<input type="text" name="s_key_all" id="s_key_all" maxlength="200" value="#local.s_key_all#" class="search-query" style="width:60%;">&nbsp;
				<button type="submit" class="btn">Search</button>
			</form>
		</div>
	</div>
</div>
</cfoutput>