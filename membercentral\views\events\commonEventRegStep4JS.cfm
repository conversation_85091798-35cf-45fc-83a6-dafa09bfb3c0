<cfsavecontent variable="local.evRegStep4JS">
	<cfoutput>
	<script type="text/javascript">
		var ticketPackageTemplateSource, ticketPackageTemplate, ticketPackageNewInstance = {}, guestmidlist = [];

		function initTicketPackageFields() {
			ticketPackageTemplateSource = $('##mc_evTPInstanceTempate').html();
			ticketPackageTemplate = Handlebars.compile(ticketPackageTemplateSource);

			$.ajax({
				url: '?event=proxy.ts_json&c=EVREGV2&m=getTicketFieldsInfo',
				type: 'POST',
				data: {registrationID:#arguments.strEvent.qryEventRegMeta.registrationid#},
				dataType: 'json',
				success: function(response) { 
					var tmpResponse = JSON.stringify(response).replace(/\^~~~\^/g,'');
					var responseResult = JSON.parse(tmpResponse);
					if(responseResult.success == true) {
						responseResult.arrPackages.forEach(function(tp) {
							$('##MCTicketPackageID'+tp.ticketPackageID).append(ticketPackageTemplate(tp))

							$('##instanceCount_'+tp.ticketPackageID).val(tp.instances);
							ticketPackageNewInstance[tp.ticketPackageID] = tp.newInstanceTemplate;
						});

						$('.btnTicketSel').attr('disabled',false);
						initializeTicketPackageControls($('##EvRegStep4'));
						$.map($('.MCTicketPackageWrapper'), function(thisPackageWrapper) {
							updatePackageInstanceTitle($(thisPackageWrapper).data('mcticketpackageid'));
						});
					}
				}, fail: function(response) { 
					alert('Some error occured. Please contact MemberCentral.');
				}
			});
		}
		function hideTicketInstance(wrapperElement) {
			$(wrapperElement).addClass('MCTicketPackageInstanceCollapsed');
		}
		function showTicketInstance(wrapperElement) {
			$(wrapperElement).removeClass('MCTicketPackageInstanceCollapsed');
		}
		function toggleTicketInstanceVisibility(wrapperElement) {
			$(wrapperElement).toggleClass('MCTicketPackageInstanceCollapsed');
		}
		function initializeTicketPackageControls (scope, mode) {
			if(mode === undefined) {
				mode = 'init';
			}
			
			mca_setupMultipleDatePickerFields(scope,'MCAdminDateControl');

			scope.find('.MCAdminDateControlClearLink').click(function(event){
				var linkedDateControlID = $(this).data('linkeddatecontrol');
				$('##' + linkedDateControlID).val('').change();
				event.preventDefault();
			});

			scope.find('.MCTicketNameField').keyup(function(event){
				if(!$(this).val().trim().length) {
					$(this).next().show().next().hide();
				} else {
					$(this).next().hide().next().show();
				}
			});

			var regName = $('##registrantName').val();

			scope.find('.MCTicketNameControlLink').html('Add "' + regName + '"');

			scope.find('.MCTicketNameControlLink').click(function(event){
				$(this).hide().next().show();
				var linkedNameControlID = $(this).data('linkednamecontrol');
				$('##' + linkedNameControlID).val(regName);
				event.preventDefault();
			});

			scope.find('.MCTicketNameClearControlLink').click(function(event){
				$(this).hide().prev().show();
				var linkedNameControlID = $(this).data('linkednamecontrol');
				$('##' + linkedNameControlID).val('');
				event.preventDefault();
			});

			scope.find('.MCInstanceError').click(function(event){
				var linkedInstanceWrapper = $(this).data('linkedinstance');
				var thisInstance = $('##' + linkedInstanceWrapper);

				if (event.target.tagName != 'A') {
					toggleTicketInstanceVisibility(thisInstance);
					event.preventDefault();
				} else {
					showTicketInstance(thisInstance);
				}
			});

			if(mode == 'init') {
				<cfif local.registrantID EQ 0>
					checkNamePrefillForPreexistingTickets();
				</cfif>
			} else if(mode == 'add') {
				/* if the instance we just added is the only instance of any package for this ticketID, then check to see if names need prefilling */
				var thisTicketID = scope.data('mcticketid');
				var numPackageInstancesForTicketID = $('div.MCTicketPackageInstanceWrapper[data-mcticketid=' + thisTicketID + ']').not(':hidden').length;
				if (numPackageInstancesForTicketID == 1) {
					//process name fields for ticket package fields
					assignRegNameToTicketNameFields(scope.find('div.MCTicketPackageInstancePackageFieldsWrapper'));
					//process name fields for first ticket in package
					assignRegNameToTicketNameFields(scope.find('div.MCTicketPackageInstanceTicketWrapper:first'));
				}
			}

			//hide error message div in each instance
			scope.find('.MCInstanceError').hide();
		}

		function incrementPackageCount(thisPackage) {
			var packageID = $(thisPackage).data('packageid');
			var ticketID = $(thisPackage).data('ticketid');
			var availablePriceID = $(thisPackage).data('availablepriceid');
			var maxPackagesPerReg = $(thisPackage).data('maxpackagesperregistrant');

			var totalPackageElem = $('##totalPackage_'+packageID);
			var newPackage = $('##ticketPackage_'+packageID);
			var displayElem = $('##totalPackageCountDisp_'+packageID);
			var ticketInvInput = $('##ticketInventory_'+ticketID);
			var ticketInvCountInput = $('##ticketInventoryCount_'+ticketID);
			var packageInvInput = $('##ticketPackageInventory_'+packageID);
			var packageInvCountInput = $('##ticketPackageInventoryCount_'+packageID);
			var allSelectedPackageCountInput = $('##packageCount_'+packageID);

			var existingPackageCount = Number(totalPackageElem.val());
			var packageCount = Number(newPackage.val());
			var ticketsInPackage = Number($('##ticketsInPackage_'+packageID).val());
			var ticketInventory = Number(ticketInvInput.val());
			var ticketInventoryCount = Number(ticketInvCountInput.val());
			var packageInventory = Number(packageInvInput.val());
			var packageInventoryCount = Number(packageInvCountInput.val());
			var maxPackagesPerRegCount = Number(maxPackagesPerReg);
			var allSelectedPackageCount = Number(allSelectedPackageCountInput.val());

			var newTicketInvCount = 0;
			if(ticketInventory > 0) {
				newTicketInvCount = ticketInventoryCount + ticketsInPackage;
				if(newTicketInvCount > ticketInventory) {
					alert('Exceeded the ticket inventory count.');
					return false;
				}
			}

			var newPackageInvCount = 0;
			if(packageInventory > 0) {
				newPackageInvCount = packageInventoryCount + 1;
				if(newPackageInvCount > packageInventory) {
					alert('Exceeded the ticket package inventory count.');
					return false;
				}
			}

			if(typeof packageCount === 'number' && (packageCount%1)===0) packageCount += 1;
			else packageCount = 1;

			var totalPackageCount = existingPackageCount + 1;
			allSelectedPackageCount = allSelectedPackageCount + 1;

			if(maxPackagesPerRegCount > 0 && allSelectedPackageCount > maxPackagesPerRegCount) {
				alert('Max Packages Allowed per Registrant is '+maxPackagesPerRegCount);
				return false;
			} else {
				/*update inventory count*/
				ticketInvCountInput.val(newTicketInvCount);
				packageInvCountInput.val(newPackageInvCount);

				/*update package count*/
				totalPackageElem.val(totalPackageCount);
				newPackage.val(packageCount);
				displayElem.html('You have ' + totalPackageCount + ' Ticket' + (totalPackageCount != 1 ? 's' : ''));
				allSelectedPackageCountInput.val(allSelectedPackageCount);

				if(typeof addNewPackageInstance == "function") {
					addNewPackageInstance(packageID,availablePriceID);
				}
			}
		}

		function addNewPackageInstance(packageID,availablePriceID) {
			if(!$('##ticketFieldInfoStep').is(':visible')) {
				$('##ticketFieldInfoStep').show();
				$('##MCTicketFieldLoading').show();
			}

			if(typeof ticketPackageNewInstance[packageID] == 'object') {
				var existingInstanceCount = Number($('##instanceCount_'+packageID).val());
				var newInstanceNum = existingInstanceCount + 1;

				ticketPackageNewInstance[packageID].instanceArray[0].instanceNum = newInstanceNum;
				ticketPackageNewInstance[packageID].instanceArray[0].availablePriceID = availablePriceID;
				var newInstanceHTML = ticketPackageTemplate(ticketPackageNewInstance[packageID]);

				$('##MCTicketPackageID'+packageID).append(newInstanceHTML);

				var newInstanceDiv = $('##MCTicketPackage'+packageID+'Instance0_'+newInstanceNum);
				initializeTicketPackageControls(newInstanceDiv,'add');
				newInstanceDiv.show().addClass('MCTicketPackageInstanceNewlyAdded');
				

				updatePackageInstanceTitle(packageID);
				$('##instanceCount_'+packageID).val(newInstanceNum);
				$('##MCTicketFieldLoading').hide();
				showTicketInstance(newInstanceDiv);
				mca_setupCalendarIcons(newInstanceDiv.attr('id'));
			}
		}

		function checkNamePrefillForPreexistingTickets() {
			//get all ticketIDs that this event supports
			var mcticketIDArray = $('div.MCTicketPackageWrapper[data-mcticketid]').map(function(index, thisElement) {
			  return $(thisElement).data('mcticketid');
			})

			//get distinct list of tickeID by filtering list
			var distinctMCTicketIDArray = mcticketIDArray.get().filter(function(el, index, arr) {
				return index === arr.indexOf(el);
			});

			//for each ticket, find the first instance of any package, make sure it's not hidden and then check it to see if any name fields need to be autofilled
			for (var i = 0; i < distinctMCTicketIDArray.length; i++) {
				var thisTicketFirstPackageInstance = $('div.MCTicketPackageInstanceWrapper[data-mcticketid=' + distinctMCTicketIDArray[i] +']').not(':hidden').first();
				if(thisTicketFirstPackageInstance.length) {
					assignRegNameToTicketNameFields(thisTicketFirstPackageInstance);
				}						
			}
		}

		function assignRegNameToTicketNameFields(scope) {
			/* for each empty prefillable input, trigger the click event on the associated prefill link*/
			var emptyPrefillNameFieldsArray = scope.find('input.MCTicketNameField[data-MCEventTicketAutoFillRegistrant="1"][value=""]');
			$(emptyPrefillNameFieldsArray).each(function() {
				//find the prefill link by looking for the link that references the current form field as it's linked name control
				var thisPrefillLink = $('a.MCTicketNameControlLink[data-linkednamecontrol=' + this.id + ']');
				thisPrefillLink.trigger('click');
			});
		}

		function updatePackageInstanceTitle(packageID) {
			var activeInstanceElements = $('##MCTicketPackageID'+packageID+' .MCTicketPackageInstanceWrapper').not(':hidden');
			var packagename = $('##MCTicketPackageID'+packageID).data('mcticketpackagename');
			var ticketname = $('##MCTicketPackageID'+packageID).data('mcticketname');
			var instances = activeInstanceElements.length;
			var newPackageName = "";
			var ticketSeat = "";
			var combinedInstanceTitle = "";

			$.each(activeInstanceElements,function(index){
				newPackageName = ticketname + ' - ' + packagename + ' (' + (index+1) + ' of ' + instances + ')';
				
				$(this).data('mcinstancetitle',newPackageName);
				$(this).find('.MCInstanceTitle').html(newPackageName);
				$(this).find('.MCTicketPackageInstancePackageFieldsWrapper .MCPackageField').data('mceventticketinstancetitle',newPackageName);
				var ticketFieldsWrapper = $(this).find('.MCTicketPackageInstanceTicketFieldsWrapper');
				$.each(ticketFieldsWrapper,function(){
					ticketSeat = $(this).data('mcticketseat');
					combinedInstanceTitle = newPackageName + ' - ' + ticketSeat;
					$(this).find('.MCTicketField').data('mceventticketinstancetitle',combinedInstanceTitle);
				});
			});
		}

		function removeRegPackageInstance(inum,pid,sid,aid) {
			doRemoveRegPackageInstance(inum,pid,sid,aid);
		}
		function doRemoveRegPackageInstance(inum,pid,sid,aid) {
			var instanceWrapper = $('##MCTicketPackage'+pid+'Instance'+sid+'_'+inum);
			var totalPackageElem = $('##totalPackage_'+pid);
			var newPackage = $('##ticketPackage_'+pid);
			var displayElem = $('##totalPackageCountDisp_'+pid);
			var ticketID = $('##ticketID_'+pid).val();
			var allSelectedPackageCountInput = $('##packageCount_'+pid);

			var ticketInvInput = $('##ticketInventory_'+ticketID);
			var ticketInvCountInput = $('##ticketInventoryCount_'+ticketID);
			var packageInvInput = $('##ticketPackageInventory_'+pid);
			var packageInvCountInput = $('##ticketPackageInventoryCount_'+pid);

			var existingPackageCount = Number(totalPackageElem.val());
			var packageCount = Number(newPackage.val());
			var ticketsInPackage = Number($('##ticketsInPackage_'+pid).val());
			var ticketInventory = Number(ticketInvInput.val());
			var ticketInventoryCount = Number(ticketInvCountInput.val());
			var packageInventory = Number(packageInvInput.val());
			var packageInventoryCount = Number(packageInvCountInput.val());
			var allSelectedPackageCount = Number(allSelectedPackageCountInput.val());

			var newTicketInvCount = ticketInventoryCount - ticketsInPackage;
			var newPackageInvCount = packageInventoryCount - 1;
			var totalPackageCount = existingPackageCount - 1;
			allSelectedPackageCount = allSelectedPackageCount - 1;

			if(sid == 0 && packageCount > 0)
				packageCount -= 1;

			/*update inventory count*/
			ticketInvCountInput.val(newTicketInvCount);
			packageInvCountInput.val(newPackageInvCount);

			totalPackageElem.val(totalPackageCount);
			newPackage.val(packageCount);
			displayElem.html('You have ' + totalPackageCount + ' Ticket' + (totalPackageCount != 1 ? 's' : ''));
			allSelectedPackageCountInput.val(allSelectedPackageCount);

			if(sid > 0) {
				$('##removeTicketPackageInstance_'+sid).val(1);
				instanceWrapper.hide();
			} else {
				instanceWrapper.remove();
			}

			updatePackageInstanceTitle(pid);
		}

		function validateMCEventTicket_fieldIsRequired(thisField) {
			var fld = $(thisField);
			var fldName = $(thisField).attr('name');
			var displayTypeCode = fld.data('mceventticketdisplaytypecode');
			var thisElementID = thisField.id;
			var returnMsg = '';

			switch(displayTypeCode) {
				case 'TEXTBOX':
				case 'TEXTAREA':
				case 'DATE':
					if (fld.val() == '') {
						returnMsg =  fld.data('mceventticketrequiredmsg');
					}
				break;
				case 'SELECT':
					if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
						returnMsg =  fld.data('mceventticketrequiredmsg');
					}
				break;
				case 'RADIO':
				case 'CHECKBOX':
					if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
						returnMsg =  fld.data('mceventticketrequiredmsg');
					}
				break;
			}
			if (returnMsg.length){
				returnMsg = "<a href='##" + thisElementID + "'>" + returnMsg + "</a>";
			}
			return returnMsg;
		}
		function validateMCEventTicket_textControlValidInteger(thisField) {
			var returnMsg = '';
			var dataTypeDisplay = 'whole number';
			var fld = $(thisField);
			var fldval = Number(fld.val().trim());
			var supportqty = fld.data('mceventticketofferqty') || 0;
			if(supportqty == 1) {
				dataTypeDisplay = 'quantity';
			}

			if (fldval != '') {
				if (fldval !== parseInt(fldval)) {
					returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mceventticketfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				} else if (supportqty == 1) {
					if (fldval < 0) {
						returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mceventticketfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					} else if (fldval > Number(fld.data('mceventticketmaxqtyallowed'))) {
						returnMsg = 'Enter a quantity between 0 and ' + Number(fld.data('mceventticketmaxqtyallowed')) + ' for ' + fld.data('mceventticketfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					}
				}
			}
			return returnMsg;
		}
		function validateMCEventTicket_textControlValidDecimal(thisField) {
			var returnMsg = '';
			var dataTypeDisplay = 'decimal number';
			var fld = $(thisField);
			var fldval = Number(fld.val().trim());
			var supportamt = fld.data('mceventticketofferamount') || 0;
			if(supportamt == 1) {
				dataTypeDisplay = 'amount';
			}

			if (fldval != '') {
				if (fldval !== parseFloat(fldval)) {
					returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mceventticketfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				} else if (supportamt == 1 && parseFloat(fldval) < 0) {
					returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mceventticketfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				}
			}
			return returnMsg;
		}

		function doTicketFieldsValidate(ticketInstanceWrapper) {
			var errorMsgArray = [];

			var thisInstance = $(ticketInstanceWrapper);
			var thisErrorContainer = thisInstance.find('.MCInstanceError');

			/*required fields*/
			var ticketFieldsRequired = thisInstance.find('input:text[data-MCEventTicketIsRequired="1"], select[data-MCEventTicketIsRequired="1"], textarea[data-MCEventTicketIsRequired="1"]').not(':disabled');

			/*distinct radio, checkbox elements*/
			var radioCheckBoxElements = thisInstance.find('input:radio[data-MCEventTicketIsRequired="1"], input:checkbox[data-MCEventTicketIsRequired="1"]');

			var elemArr = [];
			$.each( radioCheckBoxElements, function() {
				var elemName = this.name;
				if( $.inArray( elemName, elemArr ) < 0 ){
					elemArr.push(elemName);
					ticketFieldsRequired.push(this);
				}
			});

			var ticketFieldsRequiredErrorMsgArray = $.map(ticketFieldsRequired,validateMCEventTicket_fieldIsRequired);
			Array.prototype.push.apply(errorMsgArray, ticketFieldsRequiredErrorMsgArray);

			/*text controls expecting integer values*/
			var ticketTextControlsInteger = thisInstance.find('input[data-MCEventTicketDisplayTypeCode="TEXTBOX"][data-MCEventTicketDataTypeCode="INTEGER"]').not(':disabled').not(':hidden');
			var ticketTextControlsIntegerErrorMsgArray = $.map(ticketTextControlsInteger,validateMCEventTicket_textControlValidInteger);
			Array.prototype.push.apply(errorMsgArray, ticketTextControlsIntegerErrorMsgArray);

			/*text controls expecting decimal values*/
			var ticketTextControlsDecimal = thisInstance.find('input[data-MCEventTicketDisplayTypeCode="TEXTBOX"][data-MCEventTicketDataTypeCode="DECIMAL2"]').not(':disabled').not(':hidden');
			var ticketTextControlsDecimalErrorMsgArray = $.map(ticketTextControlsDecimal,validateMCEventTicket_textControlValidDecimal);
			Array.prototype.push.apply(errorMsgArray, ticketTextControlsDecimalErrorMsgArray);

			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return thisError;
				else return null;
			});

			/* var combinedErrorText = finalErrors.join('<br/>').trim();*/
			var combinedErrorText = finalErrors.map(function(thisMessage){
				return '<li>' + thisMessage + '</li>';
			}).join('\n').trim();
			var errorSummary = "";
			var thisInstanceID = ticketInstanceWrapper.id;
			
			if (combinedErrorText.length) {
				combinedErrorText  = "This ticket has fields that need your attention: <br/><ul>" + combinedErrorText + "</ul>";
				thisErrorContainer.html(combinedErrorText);
				thisErrorContainer.show();
				errorSummary = "Attention needed: " + thisInstance.data('mcinstancetitle') + " <a href='##" + thisInstanceID + "'>(click for details)</a>";
				thisInstance.addClass('MCTicketPackageInstanceWrapperError');
			}
			else {
				thisErrorContainer.html('');
				thisErrorContainer.hide();
				thisInstance.removeClass('MCTicketPackageInstanceWrapperError');
			}
			
			return errorSummary;
		}
		function doS4ValidateAndSave() {
			$('##btnSaveStep4').html('<i class="icon icon-spinner"></i> Please wait..').prop('disabled',true);
			var strErr = '';
			var errorMsgArray = [];
			
			if ($('.MCTicketPackageInstanceWrapper').length) {
				var ticketPackageInstanceErrorArray = [];
				var ticketPackageInstanceArray = $('.MCTicketPackageInstanceWrapper').not(':hidden');
				ticketPackageInstanceErrorArray = $.map(ticketPackageInstanceArray,doTicketFieldsValidate);

				/*drop empty elements*/
				var ticketPackageInstanceErrorArray = $.map(ticketPackageInstanceErrorArray, function(thisError){
					if (thisError.length) return thisError;
					else return null;
				});

				strErr += ticketPackageInstanceErrorArray.join('<br/>');
			}

			return new Promise(function(resolve, reject) {
				if (strErr.length > 0) {
					if ($('##frmEventRegStep4Container').is(':hidden')) editRegStep4();
					$('##step4Err').html('Please complete all the required fields.').show();
					$('##btnSaveStep4').html('Save Changes & Continue').prop('disabled',false);
					reject('Please complete all the required fields in Ticket Selections.');
					return false;
				} else {
					$('##step4Err').html('').hide();
					$('##evRegStep4SaveLoading')
						.html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
						.load('#arguments.event.getValue('evregresourceurl')#&regaction=saves4', $('##frmEventRegStep4').serializeArray(),
							function(resp) {
								let respObj = JSON.parse(resp);

								for (let tpID in respObj.strpackages) {
									if (respObj.strpackages[tpID].totalamt)
										$('##totalPackagePriceDisp_'+tpID).html('Your fee for this package: ' + respObj.strpackages[tpID].totalamtdisplay);
								}

								$('##btnSaveStep4').html('Save Changes').prop('disabled',false);
								$('html, body').animate({
									scrollTop: $('##EvRegStep4').offset().top - 175
								}, 750);
								
								if (respObj.checkout) {
									regCartCheckout();
								} else {
									$('##evRegStep4TotalPriceDisplay').html(respObj.totalamtdisplay);
									$('##frmEventRegStep4Container').hide();
									$('##evRegStep4Summary').show(300);
								}

								resolve();
							}
						);
				}
			});
		}
		function addGuest(tpID,item,tnum) {
			$.colorbox( {innerWidth:750, innerHeight:450, href:'#arguments.event.getValue("mainregurl")#&mode=direct&regAction=guestsearch&tpID=' + tpID + '&tnum=' + tnum + '&item=' + item + '&midlist=0' + guestmidlist.join(','), iframe:true, overlayClose:false} );
		}
		function doAddGuest(fld,mID,mName) {
			var existingMID = $('##ev_tgmid_'+fld).val();
			if (existingMID != '') guestmidlist.splice(guestmidlist.indexOf(parseInt(existingMID)), 1);
			$('##ev_tg_'+fld).val(mName);
			$('##ev_tgmid_'+fld).val(mID);
			$('##addGuest_'+fld).addClass('hide');
			$('##changeGuest_'+fld).removeClass('hide');
			guestmidlist.push(parseInt(mID));
		}
		function removeGuest(tpID,item,tnum) {
			var fld = tpID + '_' + item + '_' + tnum;
			var mID = $('##ev_tgmid_'+fld).val();
			$('##ev_tg_'+fld).val('');
			$('##ev_tgmid_'+fld).val('');
			$('##addGuest_'+fld).removeClass('hide');
			$('##changeGuest_'+fld).addClass('hide');
			guestmidlist.splice(guestmidlist.indexOf(parseInt(mID)), 1);
		}
		function assignToMyself(tpID,item,tnum) {
			var fld = tpID + '_' + item + '_' + tnum;
			var mID = #local.evRegV2.currentReg.s1.memberid#;
			var mName = $('##registrantName').val();
			doAddGuest(fld,mID,mName);
		}
		function editRegStep4() {
			$('##evRegStep4Summary').hide();
			$('##frmEventRegStep4Container').show(300);
		}

		$(function(){
			initTicketPackageFields();
		});
	</script>
	<style type="text/css">
		div.MCTicketPackageInstanceWrapper {border: 2px solid ##d9dcdd; padding:8px; margin-top: 10px; margin-bottom: 15px; border-radius: 4px;}
		div.MCTicketPackageInstanceWrapper span.MCTicketPackageInstanceIncludedFromRate {display:none;}
		div.MCTicketPackageInstanceWrapper span.MCTicketPackageInstanceNewlyAdded {display:none;}
		div.MCTicketPackageInstanceIncludedFromRate span.MCTicketPackageInstanceIncludedFromRate {display:inline;}
		div.MCTicketPackageInstanceNewlyAdded span.MCTicketPackageInstanceNewlyAdded {display:inline;}
		.MCTicketPackageInstanceWrapperError .alert {margin-bottom: 10px;}
		div.MCTicketPackageInstanceWrapper:after {content: "";display: table; clear: both;}
		div.MCTicketPackageInstanceWrapper textarea {width: 75%;}
		.MCTicketPackageWrapperHeaderButtonBar {float:left;margin-left:5px;}
		.MCTicketPackageWrapperHeader:after {content: "";display: table; clear: both;}
		.MCTicketPackageInstanceCollapsed .MCTicketPackageInstancePackageFieldsWrapper {display:none;}
		.MCTicketPackageInstanceCollapsed .MCTicketPackageInstanceTicketWrapper {display:none;}
		.MCInstanceTitle {border-bottom:none; margin-top:0;}
		.MCInstanceTitleWrapper{margin-bottom:10px;}
 	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.evRegStep4JS#">