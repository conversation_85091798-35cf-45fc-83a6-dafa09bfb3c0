<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			variables.memberContactTypeFieldSetUID = "A96C0CA3-26CC-4BAA-BFE2-4007CC1005A4";
			variables.personalInformationFieldSetUID = "8D1067C2-64A8-414A-A1E2-37B6CA05D863";
			variables.officeAddressFieldSetUID = "61B7ECCA-309C-4CA9-844B-43800FAA4856";
			variables.homeAddressFieldSetUID = "9738330E-5520-4869-8FD5-1606A69D9D64";			
			variables.localAssociationMembershipsFieldSetUID = "8D5872E1-68CD-454D-9236-A3C8A7A9EFDF";
            variables.addressPreferenceFieldSetUID = "C651F54A-1E2D-42A3-A671-C94E55FFA22E";

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];
            local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubTypeTest", type="STRING", desc="Check for existing accepted/active/billed subscriptions of this type", value="FCE69DA4-332D-4B91-A4B2-0EAFABF7EC1D"}; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ActiveAcceptedMessage", type="CONTENTOBJ", desc="Message displayed when account selected has active/accepted subscription", value="Our records indicate that you are already a HBA member. If you have questions about your membership, please call (713) 759-1133." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (713) 759-1133." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="HBA Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Step 1 - Please complete the following information." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2TopContent", type="CONTENTOBJ", desc="Content at top of page 2", value="Step 2 - Make your selections below." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent", type="CONTENTOBJ", desc="Content at top of page 3", value="Step 3 - Please review your selections and proceed with payment." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MainSubscription", type="STRING", desc="UID for subscription tree", value="5B9D62D6-3467-4F69-88EF-9A9235A932E2" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCredit", type="STRING", desc="pay profile code for credit card", value="HWJCQNRT" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCheck", type="STRING", desc="pay profile code for check", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeACH", type="STRING", desc="pay profile code for ACH", value="" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you for your application." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="HBA Membership Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
		
            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
            
            /* ***************** */
            /* set form defaults */
            /* ***************** */
            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='join',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));

            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
	
            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            switch (local.formAction) {
                case "processLookup":
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
                case "processMemberInfo":
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
                case "processMembershipInfo":
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
                case "processPayment":
					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
                case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
                default:
                    local.returnHTML = showLookup();
                    break;
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>

    </cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfif structKeyExists(session, "formFields")>
			<cfset structDelete(session, "formFields")>
		</cfif>	
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			
			<style type="text/css">
				
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				
				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}

				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				

				function addMember(memObj) {
					$.colorbox.close();
					assignMemberData(memObj);
				}
								
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {	
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
				<cfif len(variables.strPageFields.FormTitle)>
					<div class="row-fluid" id="FormTitleId">
						<div class="span12">
							<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
						</div>
					</div>
				</cfif>	
				
				<h3>#variables.strPageFields.AccountLocatorTitle#</h3>
				
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="195" style="text-align:center;vertical-align:middle;">					
							<a href="javascript:void(0)" id="btnAddAssoc" class="btn" >#variables.strPageFields.AccountLocatorButton#</a>
						</td>
						<td>#variables.strPageFields.AccountLocatorInstructions#</td>
					</tr>
				</table>
				
			</cfform>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = variables.strPageFields.SubTypeTest>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->

		<cfset local.fieldSetUIDlist = '#variables.memberContactTypeFieldSetUID#,#variables.personalInformationFieldSetUID#,#variables.officeAddressFieldSetUID#,#variables.homeAddressFieldSetUID#'>
		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>
		<cfset local.contactTypeField = {fieldCode="",fieldLabel=""}>
        <cfset local.officeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeAddress2Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.officestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.officepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.officephoneField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddress2Field = {fieldCode="",fieldLabel=""}>
		<cfset local.homeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.homestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.homepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.homephoneField = {fieldCode="",fieldLabel=""}>
		<cfset local.lawStudentValueID = "">
		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
            
			<cfset local.contactTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldLabel='Member Category']")/>
			<cfif arrayLen(local.contactTypeData)>				
				<cfset local.contactTypeField.fieldCode = local.contactTypeData[1].XmlAttributes.fieldCode>
				<cfset local.contactTypeField.fieldLabel = local.contactTypeData[1].XmlAttributes.fieldLabel>

				<cfloop array="#local.contactTypeData[1].XmlChildren#" index="local.optNode">
					<cfif local.optNode.XmlAttributes.columnValueString EQ "Law Student">
						<cfset local.lawStudentValueID = local.optNode.XmlAttributes.valueID>
						<cfbreak>
					</cfif>
				</cfloop>
			</cfif>

            <cfset local.officeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_address1']")/>
            <cfif arrayLen(local.officeAddress1FieldData)>				
                <cfset local.officeAddress1Field.fieldCode = local.officeAddress1FieldData[1].XmlAttributes.fieldCode >
                <cfset local.officeAddress1Field.fieldLabel = local.officeAddress1FieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.officeAddress2FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_address2']")/>
            <cfif arrayLen(local.officeAddress2FieldData)>				
                <cfset local.officeAddress2Field.fieldCode = local.officeAddress2FieldData[1].XmlAttributes.fieldCode >
                <cfset local.officeAddress2Field.fieldLabel = local.officeAddress2FieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_city']")/>
            <cfif arrayLen(local.officeCityFieldData)>				
                <cfset local.officeCityField.fieldCode = local.officeCityFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officeCityField.fieldLabel = local.officeCityFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_stateprov']")/>
            <cfif arrayLen(local.officestateprovFieldData)>				
                <cfset local.officestateprovField.fieldCode = local.officestateprovFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officestateprovField.fieldLabel = local.officestateprovFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_postalcode']")/>
            <cfif arrayLen(local.officepostalcodeFieldData)>				
                <cfset local.officepostalcodeField.fieldCode = local.officepostalcodeFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officepostalcodeField.fieldLabel = local.officepostalcodeFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.officephoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_Direct']")/>
			<cfif arrayLen(local.officephoneFieldData)>				
				<cfset local.officephoneField.fieldCode = local.officephoneFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officephoneField.fieldLabel = local.officephoneFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
            <cfset local.homeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_address1']")/>
            <cfif arrayLen(local.homeAddress1FieldData)>				
                <cfset local.homeAddress1Field.fieldCode = local.homeAddress1FieldData[1].XmlAttributes.fieldCode >
                <cfset local.homeAddress1Field.fieldLabel = local.homeAddress1FieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
			<cfset local.homeAddress2FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_address2']")/>
            <cfif arrayLen(local.homeAddress2FieldData)>				
                <cfset local.homeAddress2Field.fieldCode = local.homeAddress2FieldData[1].XmlAttributes.fieldCode >
                <cfset local.homeAddress2Field.fieldLabel = local.homeAddress2FieldData[1].XmlAttributes.fieldLabel >
            </cfif>
            <cfset local.homeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_city']")/>
            <cfif arrayLen(local.homeCityFieldData)>				
                <cfset local.homeCityField.fieldCode = local.homeCityFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homeCityField.fieldLabel = local.homeCityFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.homestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_stateprov']")/>
            <cfif arrayLen(local.homestateprovFieldData)>				
                <cfset local.homestateprovField.fieldCode = local.homestateprovFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homestateprovField.fieldLabel = local.homestateprovFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.homepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_postalcode']")/>
            <cfif arrayLen(local.homepostalcodeFieldData)>				
                <cfset local.homepostalcodeField.fieldCode = local.homepostalcodeFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homepostalcodeField.fieldLabel = local.homepostalcodeFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.homephoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home_Direct']")/>
			<cfif arrayLen(local.homephoneFieldData)>				
				<cfset local.homephoneField.fieldCode = local.homephoneFieldData[1].XmlAttributes.fieldCode >
				<cfset local.homephoneField.fieldLabel = local.homephoneFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
		</cfloop>
		<cfset local.strPrefillMemberData = local.memberFieldData>		
        	
		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.strData.orgID = variables.orgID>	
		<cfset local.strData.siteID = variables.siteID>

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structKeyExists(session, "useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
			<cfset local.strPrefillMemberData = local.strData>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif variables.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>	

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.memberContactTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.memberContactTypeFieldSetUID, mode="collection", strData=local.strData)>
		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="collection", strData=local.strData)>
		<cfset local.officeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.officeAddressFieldSetUID, mode="collection", strData=local.strData)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="collection", strData=local.strData)>
		<cfset local.localAssociationMembershipsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.localAssociationMembershipsFieldSetUID, mode="collection", strData=local.strData)>
		<cfset local.addressPreferenceFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="collection", strData=local.strData)>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>

		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		
		<cfset local.licenseStatus = {}>
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[local.qryOrgProLicenseStatuses.PLStatusID] = local.qryOrgProLicenseStatuses.statusName>	
		</cfloop> 
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form td.tsAppBodyText select,form td.tsAppBodyText input{
					margin: 0px!important;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}
			
				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
							
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}

					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}
				
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{padding: 10px !important;}				
				
			</style>
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}	

				function adjustFieldsetDisplay() {
					var memType = $(this).find('option:selected').text();
					switch(memType) {
						case 'Law Student':
							showFieldsContactType('');                            
							resetFormFeilds('professionalLicensesHolder');
							resetProfessionalLicenses();
							break;						
						default:
							showFieldsContactType('professionalLicensesHolder');                            
							resetFormFeilds('professionalLicensesHolder');
							resetProfessionalLicenses();
							break;
					}
				}

				function showFieldsContactType(classList)				
				{
					$(".professionalLicensesHolder").hide();
									
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]).show();							
						}			
					});							
				}	

				function resetProfessionalLicenses(){
					
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}

				}		

				function resetFormFeilds(containerClass){
					$("."+containerClass+" input,."+containerClass+" select,."+containerClass+" textarea").each(function(){
						 $(this).val(function() {
							return this.defaultValue;
						});
					});
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.memberContactTypeFieldSet.jsValidation#					
					#local.personalInformationFieldSet.jsValidation#	
                    #local.officeAddressFieldSet.jsValidation#			
					var memType = $.trim($('##'+'#local.contactTypeField.fieldCode#'+' option:selected').text());
					if(memType != 'Law Student' && memType != '')
					{
						var isProfLicenseRequired = true;
						
						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
									if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}	
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}
												
					}                    
					
					#local.homeAddressFieldSet.jsValidation#                    
						
                    var isSetAllOfficeAddress = 0;		
                    <cfif len(trim(local.officeAddress1Field.fieldCode)) OR len(trim(local.officeAddress2Field.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.officeAddress1Field.fieldCode#'], "TEXT", false)){ 
							isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						}else if(_CF_hasValue(_CF_this['#local.officeAddress2Field.fieldCode#'], "TEXT", false)){ 
							isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						}
                    </cfif>
                    <cfif len(trim(local.officeCityField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.officeCityField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
                    </cfif>
                    <cfif len(trim(local.officestateprovField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.officestateprovField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
                    </cfif>
                    <cfif len(trim(local.officepostalcodeField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.officepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
                    </cfif>

                    var isSetAllHomeAddress = 0;		
                    <cfif len(trim(local.homeAddress1Field.fieldCode)) OR len(trim(local.homeAddress2Field.fieldCode))>                        
						if(_CF_hasValue(_CF_this['#local.homeAddress1Field.fieldCode#'], "TEXT", false)){ 
							isSetAllHomeAddress = isSetAllHomeAddress + 1;
						}else if(_CF_hasValue(_CF_this['#local.homeAddress2Field.fieldCode#'], "TEXT", false)){ 
							isSetAllHomeAddress = isSetAllHomeAddress + 1;
						}
                    </cfif>
                    <cfif len(trim(local.homeCityField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.homeCityField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
                    </cfif>
                    <cfif len(trim(local.homestateprovField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.homestateprovField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
                    </cfif>
                    <cfif len(trim(local.homepostalcodeField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.homepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
                    </cfif>
                    if(isSetAllOfficeAddress !=4){
                        if(isSetAllHomeAddress !=4){
                            arrReq[arrReq.length] = "The following fields are required for either Office Address or Home Address:<ul><li>Street Address / Address 2</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
                        }
                    }

					var isSetOfficePhone = 0;
					<cfif len(trim(local.officephoneField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.officephoneField.fieldCode#'], "TEXT", false)) isSetOfficePhone = isSetOfficePhone + 1;
					</cfif>
					var isSetHomePhone = 0;
					<cfif len(trim(local.homephoneField.fieldCode))>
						if(_CF_hasValue(_CF_this['#local.homephoneField.fieldCode#'], "TEXT", false)) isSetHomePhone = isSetHomePhone + 1;
					</cfif>
					if(isSetOfficePhone !=1){
						if(isSetHomePhone !=1){
							arrReq[arrReq.length] = "Either Office Phone or Home Phone is required.";
						}
					}else if(isSetHomePhone !=1){
						if(isSetOfficePhone !=1){
							arrReq[arrReq.length] = "Either Office Phone or Home Phone is required.";
						}
					}
					
					#local.localAssociationMembershipsFieldSet.jsValidation#
					#local.addressPreferenceFieldSet.jsValidation#	                	

					if (arrReq.length > 0) {
						var msg = '';
						
						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						<cfif FindNoCase("m_",local.thisKey)
							or FindNoCase("ma_",local.thisKey) 
							or FindNoCase("mat_",local.thisKey) 
							or FindNoCase("me_",local.thisKey) 
							or FindNoCase("mpl_",local.thisKey)							
							or FindNoCase("mp_",local.thisKey) 
							or FindNoCase("mw_",local.thisKey) 
							or FindNoCase("md_",local.thisKey)
							or FindNoCase("mccf_",local.thisKey)>
							#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfif>
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				function licenseChange(isChecked,val,text)
				{
					$("##state_table").show();
					strOption = '';
					<cfloop collection="#local.licenseStatus#" item="local.i" >
						strOption += '<option value="#local.licenseStatus[local.i]#" <cfif local.licenseStatus[local.i] EQ 'Active'>selected</cfif> >#local.licenseStatus[local.i]#</option>';
					</cfloop>
					if(isChecked){
						$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
								'<div class="span3"><span class="tsAppBodyText">'+text+'</span></div>'+
								'<div class="span3"><input name="mpl_'+val+'_licensenumber" id="mpl_'+val+'_licensenumber" class="tsAppBodyText" type="text" value="" size="13" maxlength="13"></div>'+ 
								'<input name="mpl_'+val+'_licensename" class="mpl_'+val+'_licensename" type="hidden" value="'+text+'" />'+
								'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
								'<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
								'</div>');

						mca_setupDatePickerField('mpl_'+val+'_activeDate');
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
					}									
					else{
						$("##tr_state_"+val).remove();								
					}
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}	
				}
				$(document).ready(function() {		
					$(".step1form .tsAppSectionContentContainer table").attr('cellpadding','6');				
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>						

					$(".mpl_pltypeid option:selected").each(function(){
						licenseChange(true,$(this).attr("value"),$(this).text());	
					});
					prefillData();
					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text);                            										
					   	},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text());	
							});
						}
					});		
					
					var contactTypeField = $('##'+"#local.contactTypeField.fieldCode#");	
					$(contactTypeField).change(adjustFieldsetDisplay);
					$(contactTypeField).trigger('change');
					<cfif len(trim(local.lawStudentValueID))>
						$("###local.contactTypeField.fieldCode# option[value='#local.lawStudentValueID#']").hide();
					</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" onsubmit="return validateMemberInfoForm()">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>		
				
					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif>

						<span class="memberContactTypeHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.memberContactTypeFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">
									#local.memberContactTypeFieldSet.fieldSetContent#
								</div>
							</div>
						</span>	
						<span class="personalInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.personalInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.personalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>                        
						<span class="officeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.officeAddressFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
									#local.officeAddressFieldSet.fieldSetContent#
								</div>
							</div>
						</span>		
                        <span class="professionalLicensesHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">Professional License Information</div>
								<div class="tsAppSectionContentContainer">	
									<table cellpadding="3" border="0" cellspacing="0" >									
										<tr align="top">
											<td class="tsAppBodyText" width="10">*&nbsp;</td>
											<td class="tsAppBodyText" nowrap>Professional License</td>
											<td class="tsAppBodyText">
												<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
													<cfloop query="local.qryOrgPlTypes">	
														<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
														<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="top">
											<td class="tsAppBodyText" width="10"></td>
											<td class="tsAppBodyText"></td>
											<td class="tsAppBodyText"></td>
										</tr>
									</table>
									<br/>
									<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
										<tr>
											<td>
												<div class="row-fluid hide" id="state_table">
													<div class="span3 proLicenseLabel">
														<b>State Name</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</b>
													</div>
													<div class="span3 proLicenseLabel">
														<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
													</div>
												</div>
												<span id="selectedLicense">
												</span>
											</td>
										</tr>					
									</table>
								</div>
							</div>
						</span>
						<span class="homeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
									#local.homeAddressFieldSet.fieldSetContent#
								</div>
							</div>
						</span>
						<span class="localAssociationMembershipsFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.localAssociationMembershipsFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
									#local.localAssociationMembershipsFieldSet.fieldSetContent#
								</div>
							</div>
						</span>
                        <span class="addressPreferenceFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressPreferenceFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
									#local.addressPreferenceFieldSet.fieldSetContent#
								</div>
							</div>
						</span>                             
                        
						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>
				#application.objWebEditor.showEditorHeadScripts()#
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>

				<script language="javascript">
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
							function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
								var _address = _this.val();
								
								if(_address.length >0){
									if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
										$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
									}
								} else {
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
								}
							}
							
							$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
							});
						</cfloop>
					});
				
					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
			
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.contactTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=arguments.rc['md_#local.memberTypeFieldInfo.columnID#'])>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
        <cfset local.thisAddonSubscriptionID = "">
		<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
			<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
					siteID=variables.siteID,
					uid = local.thisDefaultAddon)>
			<cfif local.thisAddonSubscriptionID>
				<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
			</cfif>
		</cfloop>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>			
		</cfif>	

		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">	
					div.alert-danger{padding: 10px !important;}				
				</style>
					
				<script type="text/javascript">
					#local.result.jsAddonValidation#
				    function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#
						
						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}	

						//make sure selected subscriptions all have selected rates.
						$('input.subCheckbox:checkbox:checked').each(function(x,item){
							var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
							var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
							if ( numRates > 0 && numRatesChecked==0) {
								arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
							}
						});		

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}		
						
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}				
					
					$(document).ready(function(){
						$("input.subRateCheckbox[name!='sub#local.subscriptionID#_rate']:radio").change(function(){
							if($(this).prop("checked") && $("##"+$(this).attr('name').split('_')[0]).prop("checked") == false){
								$("##"+$(this).attr('name').split('_')[0]).trigger("click");
							}						
						});
					});	
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">

					<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
					<cfloop collection="#session.formFields.step1#" item="local.thisField">
						<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
							or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
							or left(local.thisField,5) eq "mccf_">
							<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
							<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
						</cfif>
					</cfloop>
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>

					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		<cfset var local = structNew()>	

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>
		<cfif variables.isLoggedIn>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
		<cfelseif session.cfcuser.memberdata.identifiedAsMemberID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=session.cfcuser.memberdata.identifiedAsMemberID)>
		<cfelse>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		</cfif>
		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strResult.memberID = session.formFields.step1.memberID>	
		</cfif>

		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>

		<!--- save member info and record history --->		
		<cfset local.objSaveMember.setRecordType(recordType='Individual')>
		<cfset local.objSaveMember.setMemberType(memberType='User')>	

		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			
		<cfif local.strResult.success>			
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfscript>
			local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc);
		</cfscript>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfscript>
			local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc);
		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>
		</cfif>

		<cfsavecontent variable="local.headcode">
			<cfoutput>
				<cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>
				<style>
					.inner-content{margin-bottom: 20px;}
					div.alert-danger{padding: 10px !important;}	
					form td.tsAppBodyText{
						vertical-align: middle;
					}
					form td.tsAppBodyText select,form td.tsAppBodyText input{
						margin: 0px!important;
					}
					form .tsAppSectionContentContainer tr{
						margin-bottom: 10px!important;
					}
				</style>
				<script type="text/javascript">
					function validatePaymentForm(isPaymentRequired) {
						
						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
                    $('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
												
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script> 

			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">		

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_" or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			<cfif len(variables.strPageFields.FormTitle)>
				<div class="row-fluid" id="FormTitleId">
					<div class="span12">
						<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
					</div>
				</div>
			</cfif>	

			<cfif len(variables.strPageFields.Step3TopContent)>
				<div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
			</cfif>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
			</div>
			<br/>

			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>

			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			//create subscriptions
			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID, siteID=variables.siteID, orgID=variables.orgID)>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#,#variables.strPageFields.ProfileCodeACH#",arguments.rc.mccf_payMeth)>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>


		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

    <cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.contactTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=arguments.rc['md_#local.memberTypeFieldInfo.columnID#'])>

		<cfset local.memberContactTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.memberContactTypeFieldSetUID, mode="confirmation", strData=arguments.rc)>
		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="confirmation", strData=arguments.rc)>
		<cfset local.officeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.officeAddressFieldSetUID, mode="confirmation", strData=arguments.rc)>
		<cfset local.homeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.homeAddressFieldSetUID, mode="confirmation", strData=arguments.rc)>
		<cfset local.localAssociationMembershipsFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.localAssociationMembershipsFieldSetUID, mode="confirmation", strData=arguments.rc)>
		<cfset local.addressPreferenceFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="confirmation", strData=arguments.rc)>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfscript>
			local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc);

		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<p>#variables.strPageFields.ConfirmationContent#</p>
			</cfif>

			<!--@@specialcontent@@-->

			<p>Here are the details of your application:</p>

			#local.memberContactTypeFieldSet.fieldSetContent#
			#local.personalInformationFieldSet.fieldSetContent#	
            #local.officeAddressFieldSet.fieldSetContent#			
			<cfif local.contactTypeSelected NEQ 'Law Student'>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br>
			</cfif>
			#local.homeAddressFieldSet.fieldSetContent#
			#local.localAssociationMembershipsFieldSet.fieldSetContent#            
			#local.addressPreferenceFieldSet.fieldSetContent#        
			
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#local.strResult.formContent#
					<br/>
				</div>
				<br/>
				<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
				<br/>
				</td>
			</tr>
			</table>

			<cfif local.paymentRequired>
				<br/>
				<cfif NOT (structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage") and 
				structKeyExists(arguments.rc.processPaymentResponse,"paymentResponse") and arguments.rc.processPaymentResponse.paymentResponse.responseCode EQ 1)>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
									<table cellpadding="3" border="0" cellspacing="0">
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
										</td>
									</tr>
									</table>
								<cfelse>
									None selected.
								</cfif>
							</td>
						</tr>
					</table>
				</cfif>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(arguments.rc, "m_firstname")>
			<cfset local.Name = arguments.rc['m_firstname']/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(arguments.rc, "m_lastname")>
			<cfset local.Name = local.Name & arguments.rc['m_lastname']/>
		</cfif>	

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
	
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfsavecontent variable="local.specialText">
		<cfoutput>
			<cfif structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage") and 
				structKeyExists(arguments.rc.processPaymentResponse,"paymentResponse") and arguments.rc.processPaymentResponse.paymentResponse.responseCode NEQ 1>
				<div class="alert alert-error" style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
		</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application.",
			emailhtmlcontent = local.confirmationHTMLToMember,
			siteID = variables.siteID,
			memberID = val(variables.useMID),
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#arguments.rc.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
					
				</style>
				</head>
				<body>
					#local.confirmationHTMLToMember#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID)>

		<cfreturn local.confirmationHTMLToMember>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
					<cfelseif arguments.errorCode eq "billedfound">
						You need to renew your membership. You will be re-directed to your renewal shortly.
						<script type="text/javascript">
							setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
							function AJAXRenewSub(member){
								var redirect = function(r) {
									redirectLink = '/renewsub/' + r.data.directlinkcode[0];
									window.location = redirectLink;								
								};		
								
								var params = { memberID:member, status:'O', distinct:false };
								TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
							}						
						</script>
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
  </cfcomponent>