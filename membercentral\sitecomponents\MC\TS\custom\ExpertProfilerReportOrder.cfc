<cfcomponent extends="model.customPage.customPage" output="true">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
	
			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
	
			local.arrCustomFields = [];
			local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="Expert Knowledge Map Report" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Description", type="CONTENTOBJ", desc="Form Description", value="The most comprehensive investigative report for an expert. Lawyers who specialize in expert research will compile a single comprehensive report which will include a full Daubert Challenge Study, plus a full background report on the expert." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step1Title",type="STRING",desc="Step 1 Title",value="Step 1 - Choose Account" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2Title",type="STRING",desc="Step 2 Title",value="Step 2 - Contact Information" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2Content",type="CONTENTOBJ",desc="Content at top of page 2",value="Please enter or verify  contact information for the Attorney requesting the report below.  * Indicates a required field." };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3Title",type="STRING",desc="Step 3 Title",value="Step 3 - Report and Expert Information" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3Content",type="CONTENTOBJ",desc="Content at top of page 3",value="Please select your desired report delivery timeframe, number of experts, and requested expert information." };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step4Title",type="STRING",desc="Step 4 Title",value="Payment" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step5Title",type="STRING",desc="Step 5 Title",value="Confirmation" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="We have received your Expert Knowledge Map Order.Your request has been submitted to our legal research team. If you have questions, need assistance, or would like to check the status of your order, please call 800-443-1757.Thank you!TrialSmith <EMAIL> 800-443-1757" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="Expert Knowledge Map Request Received"};
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="TSStaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Expert Knowledge Map Request" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="TSStaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>; <EMAIL>; <EMAIL>" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RequestorConfirmationFrom",type="STRING",desc="from what address do we send confirmations",value="<EMAIL>" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="option1NumberOfDays",type="STRING",desc="Option 1 Number of Business Days",value="5" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="option1Price",type="STRING",desc="Option 1 Price",value="500" };
				arrayAppend(local.arrCustomFields, local.tmpField);			
			local.tmpField = { name="option2NumberOfDays",type="STRING",desc="Option 2 Number of Business Days",value="2" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="option2Price",type="STRING",desc="Option 2 Price",value="650" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="option3ShowNextBusinessDay",type="STRING",desc="Hide Next Business Day Option, true or false",value="true" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="option3Price",type="STRING",desc="Option 3 Price",value="800" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="option2Show",type="STRING",desc="Show Option 2",value="true" };
				arrayAppend(local.arrCustomFields, local.tmpField);
		
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmExpertProfilerReportOrder',
				formNameDisplay='#variables.strPageFields.FormTitle#',
				orgEmailTo='#variables.strPageFields.TSStaffConfirmationTo#',
				memberEmailFrom='#variables.strPageFields.RequestorConfirmationFrom#'
			));

			variables.accountCode = 5016;

			variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='ExpertProfilerReportForm', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='ExpertProfilerReportForm', subName='Completed');
            variables.historyStartedText = "Member started form.";
            variables.historyCompletedText = "Member completed form.";
			
			local.queryObj = new query();
			local.queryObj.setDatasource("#application.dsn.tlasites_trialsmith.dsn#");
			local.queryObj.setName("qryStates");
			local.result = local.queryObj.execute(sql="SELECT code, name, stateID FROM dbo.states ORDER BY orderpref, name");
			variables.qryStates = local.result.getResult();
			
			/* Form Process Flow */
			switch (local.formAction) {
				case "processInfo":
					switch (processInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = "";
							break;
						case "spam":
							local.returnHTML = showError(errorCode='spam');
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;			
					}
					break;
				case "showConfirmation"	:
						local.strExpert = application.mcCacheManager.sessionGetValue(keyname='strExpert', defaultValue={});
						if(local.strExpert.count()){
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection(), strExpert=local.strExpert);
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);	
							application.mcCacheManager.sessionDeleteValue(keyname='strExpert');
						}
						else {
							local.returnHTML = showError(errorCode='noreceipt');
						}												 
					break;		
				default:
					local.returnHTML = showLookup(memberKey=arguments.event.getTrimValue('mk',''));
					break;
			}
	
			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
	
			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="memberkey" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		
		<cfset local.memberData.memberNumber = StructNew()>
		<cfset local.memberData.memberNumber = ''>
		<cfif len(arguments.memberkey)>
			<cfset local.strMemberKey = deserializeJSON(decrypt(arguments.memberkey,"M@!6T$", "CFMX_COMPAT", "Hex"))>
			<cfset local.membernumber = toString(local.strMemberKey.m)>
			<cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=local.membernumber)>
			<cfset variables.useMID = local.memberData.memberID>
		</cfif>
		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
			<cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=session.cfcUser.memberData.memberNumber)>
		</cfif>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
			##div#variables.formName#wrapper h3 {line-height: 28px;margin: 0;}
			##div#variables.formName#wrapper {margin-bottom: 20px;}
			.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				var compiledNewExpertTemplate = null;
				
				function afterFormLoad(){
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);	
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}
				function reDirect(){
					setTimeout(function() {
						location.href="/?pg=buyNow&item=EXPERT-#session.cfcuser.memberdata.memberid#";
					}, 2000);
				}
				function doS1Validate() {
					var thisForm = document.forms["#variables.formName#"];
					var arrReq = new Array();
					
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND NOT len(arguments.memberkey)>
						if (!_FB_hasValue(thisForm['m_firstName'], 'TEXT')) arrReq[arrReq.length] = 'Enter your First Name';
						if (!_FB_hasValue(thisForm['m_lastName'], 'TEXT')) arrReq[arrReq.length] = 'Enter your Last Name';
					</cfif>
					if(thisForm['m_email']){
						if (!_FB_hasValue(thisForm['m_email'], 'TEXT')) {
							arrReq[arrReq.length] = 'Enter your Email.';
						} else {
							var urlRegEx = new RegExp("#application.regEx.email#", "gi");
							if(!(urlRegEx.test($('##m_email').val()))) 
								arrReq[arrReq.length] = "The provided e-mail address is not valid.";
						}
					}
					if (!_FB_hasValue(thisForm['ma_billingAddress'], 'TEXT')) arrReq[arrReq.length] = 'Enter your Address.';
					if (!_FB_hasValue(thisForm['ma_billingCity'], 'TEXT')) arrReq[arrReq.length] = 'Enter your City.';
					if (!_FB_hasValue(thisForm['ma_billingState'], 'TEXT')) arrReq[arrReq.length] = 'Enter your State.';
					if (!_FB_hasValue(thisForm['ma_billingZip'], 'TEXT')) arrReq[arrReq.length] = 'Enter your Zip.';
					if (!_FB_hasValue(thisForm['mp_phone'], 'TEXT')) arrReq[arrReq.length] = 'Enter your Phone.';
					if (!_FB_hasValue(thisForm['priceNum'], 'RADIO')) arrReq[arrReq.length] = 'Please select How quickly do you need the report(s).';
					if (!_FB_hasValue(thisForm['noofpeople'], 'TEXT')) arrReq[arrReq.length] = 'Please select the number of expert reports.';
					else {
						for(var _i=1;_i<= $('##noofpeople').val();_i++){
							if (!_FB_hasValue(thisForm['m_e_firstName_'+_i], 'TEXT')) arrReq[arrReq.length] 	= 'Enter Expert '+ _i +'\'s First Name.';
							if (!_FB_hasValue(thisForm['m_e_lastName_'+_i], 'TEXT')) arrReq[arrReq.length] 	= 'Enter Expert '+ _i +'\'s Last Name.';
							if (!_FB_hasValue(thisForm['m_city_'+_i], 'TEXT')) arrReq[arrReq.length] 	= 'Enter Expert '+ _i +'\'s City.';
							if (!_FB_hasValue(thisForm['m_state_'+_i], 'TEXT')) arrReq[arrReq.length] 	= 'Enter Expert '+ _i +'\'s State.';
							if(!_FB_hasValue(thisForm['m_area_'+_i], 'TEXT')) arrReq[arrReq.length]  = "Enter Tell us about the Expert's Area of Expertise for Expert " + _i +'.';
						}
					}

					let billingState = $('##ma_billingState').val();
					let billingZip = $('##ma_billingZip').val();
					if (billingState.length && billingZip.length && !mc_isValidBillingZip(billingZip,0,billingState))
						arrReq[arrReq.length] = 'Invalid Zip.';

					showStep1Alert = function(msg) {
						var abox = document.getElementById('s1_err_div');
						abox.innerHTML = msg;
						abox.className = 'alert';
						abox.style.display = '';
						$('html,body').animate({scrollTop: $('##s1_err_div').offset().top},500);
					};
					hideStep1Alert = function() {
						var abox = document.getElementById('s1_err_div');
						abox.innerHTML = '';
						abox.style.display = 'none';
					};
					if (arrReq.length > 0) {
						var strErr = '';
						for (var i=0; i < arrReq.length; i++) strErr += arrReq[i] + '<br>';
						showStep1Alert(strErr);
						return false;						
					} else {
						hideStep1Alert();
						mc_continueForm($('###variables.formName#'),reDirect);
						return false;
					}
				}
				function assignMemberData(memObj){
					/* get member, find mem reg seminars, and display form to fill */
					var assignMemberDataPromise = 
					new Promise(function(resolve, reject) {
						var thisForm = document.forms["#variables.formName#"];
						var getMemResult = function(r) {
							var results = r;
							if( results.success ){
							
								thisForm['memberNumber'].value = results.membernumber;
								thisForm['memberID'].value = results.memberid;
								if (thisForm['m_firstName'])
									thisForm['m_firstName'].value	= results.firstname;
								if (thisForm['m_lastName'])	
									thisForm['m_lastName'].value = results.lastname;
							
								if( #application.objUser.isLoggedIn(cfcuser=session.cfcuser)# || #len(arguments.memberkey)#){
									if (thisForm['m_email'])
										thisForm['m_email'].value = results.email;
									if (thisForm['m_company'])
									thisForm['m_company'].value = results.company;
									thisForm['ma_billingAddress'].value = results.address1;
									thisForm['ma_billingAddress2'].value = results.address2;
									thisForm['ma_billingCity'].value = results.city;
									thisForm['ma_billingZip'].value = results.postalcode;
									thisForm['mp_phone'].value = results.phone;
									$('##ma_billingState').val(results.statecode);	
								} else {
									clearContactInfo(thisForm);
								}
								resolve(results);
							} else {
								reject();
							}
						};
						var objParams = { memberNumber:memObj.memberNumber };
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,getMemResult,getMemResult,1000000,getMemResult);
					}).then(function(response) {
						$("##formToFill").show();
					});
				}
				function showCreateForm(){
					$("##formToFill").show();
				}
				function clearContactInfo(thisForm){
					if (thisForm['m_email']) thisForm['m_email'].value = '';
					if (thisForm['m_company']) thisForm['m_company'].value = '';
					thisForm['ma_billingAddress'].value = '';
					thisForm['ma_billingAddress2'].value = '';
					thisForm['ma_billingCity'].value = '';
					thisForm['ma_billingZip'].value = '';
					thisForm['mp_phone'].value = '';
					$('##ma_billingState').val('');
				}
				function toggleFTM() {
				}
				window.onhashchange = function() {
					if (location.hash.length > 0) {
						step = parseInt(location.hash.replace('##',''),10);
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processInfo");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showPayment");
							mc_loadDataForForm($('###variables.formName#'),'previous');
						}
					} else {
						step = 1;
					}
					prevStep = step;
				}
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser >
					var mo = { memberID:#variables.useMID#, memberNumber:'#local.memberData.memberNumber#' };
					assignMemberData(mo);
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				$('##noofpeople').change(function(){
					var _no = $(this).val();
					if(_no.length){
						_no = parseInt($(this).val());
						var _divLen = $('##newExperts > div').length;
						if(_divLen > _no){
							for(var i = _no+1;i <= _divLen; i ++ ) {
								$('div##Expert-'+i).remove();
							}
						} else if(_divLen < _no) {
							for(var i=_divLen+1;i<=_no;i++){
								var newExpertData = { pCount: i};
								$('##newExperts').append(compiledNewExpertTemplate(newExpertData));
							}
						}
					} else {
						$('##newExperts').html('');
					}
				});
				
				newExpertTemplate = $('##newExpertTemplate').html();
				compiledNewExpertTemplate = Handlebars.compile(newExpertTemplate);
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
			<h3>#variables.strPageFields.FormTitle#</h3>
			<p>#variables.strPageFields.Description#</p>
			<cfform name="#variables.formName#" id="#variables.formName#" action="#variables.baselink#" method="post" class="form-horizontal" onsubmit="return doS1Validate();">
				<cfinput type="hidden" name="fa" id="fa" value="processInfo">
				<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<input type="hidden" name="memberNumber" value="#local.memberData.memberNumber#">
				<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
				<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
				
				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND NOT len(arguments.memberkey)>
					<h4>#variables.strPageFields.Step1Title#</h4>
					<div class="" id="firstPage">
					   <p>Please <a href="?pg=login&returnurl=#URLEncodedFormat("/?pg=ExpertProfilerReportOrder")#">login</a> or <a href="javascript:showCreateForm();">create</a> an account.</p>
					</div>	
				</cfif>
	
				<fieldset id="formToFill" style="padding-top:10px;<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)  AND NOT len(arguments.memberkey)>display:none</cfif>">	
					<div id="contact_form">	
						<div>
							<div>
								<h4>#variables.strPageFields.Step2Title#</h4>
								<p>
									#variables.strPageFields.Step2Content#
								</p>
							</div>
							<div id="bck-rpt-form-step1-err" class="row-fluid"><div id="s1_err_div" style="display:none;"></div></div>
						</div>
						<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) OR len(arguments.memberkey)>
							<div class="span12">		
								<label class="span2 r">First Name:</label>		
								<div class="span10 l marginLeft20">
									#local.memberData.firstname#				
								</div>
							</div>
							
							<div class="span12">		
								<label class="span2 r">Last Name:</label>		
								<div class="span10 l marginLeft20">
									#local.memberData.lastName#				
								</div>
							</div>
							
							<div class="span12">		
								<label class="span2 r">Law Firm Name:</label>
								<div class="controls">
									<cfinput size="25" id="m_company" name="m_company" type="text" value="#local.memberData.company#">
								</div>
							</div>
							
							<div class="span12">	
								<label class="span2 r">* Email:</label>
								<div class="controls">
									<cfinput size="25" id="m_email" name="m_email" type="text" value="#local.memberData.email#">
								</div>
							</div>
							
							<div class="span12">
								<div class="control-group">
									<label class="span2 r" for="ma_billingAddress">*Address:</label>
									<div class="controls">
										<cfinput size="25" id="ma_billingAddress" name="ma_billingAddress" type="text" value="#local.memberData.address1#"><br>
										<cfinput size="25" id="ma_billingAddress2" name="ma_billingAddress2" type="text" value="#local.memberData.address2#" style="margin-top: 4px;">
									</div>
								</div>
							</div>
							
							<div class="span12">
								<div class="control-group">
									<label class="span2 r" for="ma_billingCity">*City:</label>
									<div class="controls">
										<cfinput size="25" id="ma_billingCity" name="ma_billingCity" type="text" value="#local.memberData.city#">
									</div>
								</div>
							</div>
							
							<div class="span12">
								<div class="control-group">
									<label class="span2 r" for="ma_billingState">*State:</label>
									<div class="controls">
										<cfselect name="ma_billingState" id="ma_billingState" query="variables.qryStates" value="code" display="name" selected="#local.memberData.stateCode#" queryposition="below" ><option value=""></option></cfselect>
									</div>
								</div>
							</div>
							
							<div class="span12">
								<div class="control-group">
									<label class="span2 r" for="ma_billingZip">*Zip:</label>
									<div class="controls">
										<cfinput size="25" id="ma_billingZip" name="ma_billingZip" type="text" value="#local.memberData.postalCode#">
									</div>
								</div>
							</div>
							
							<div class="span12">
								<label class="span2 r">Phone:</label>
								<div class="controls">
									<cfinput size="25" id="mp_phone" name="mp_phone" type="text" value="#local.memberData.phone#">
								</div>
							</div>
							<div class="span12">&nbsp;</div>
						<cfelse>
							<div class="control-group">		
								<label class="control-label" for="m_firstName">* First Name</label>		
								<div class="controls">
									<cfinput type="text" id="m_firstName" name="m_firstName" value="" size="25" maxlength="50">				
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="m_lastName">* Last Name</label>		
								<div class="controls">
									<cfinput size="25" id="m_lastName" name="m_lastName" type="text" value="">
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="m_company">Law Firm</label>		
								<div class="controls">
									<cfinput size="25" id="m_company" name="m_company" type="text" value="">
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="m_email">* Email</label>		
								<div class="controls">
									<cfinput size="25" id="m_email" name="m_email" type="text" value="">
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="ma_billingAddress">* Address</label>		
								<div class="controls">
									<cfinput size="25" id="ma_billingAddress" name="ma_billingAddress" type="text" value=""><br>
									<cfinput size="25" id="ma_billingAddress2" name="ma_billingAddress2" type="text" value="" style="margin-top: 4px;">					
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="ma_billingCity">* City</label>		
								<div class="controls">
									<cfinput size="25" id="ma_billingCity" name="ma_billingCity" type="text" value="">
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="ma_billingState">* State</label>		
								<div class="controls">
									<cfselect name="ma_billingState" id="ma_billingState" query="variables.qryStates" value="code" display="name" selected="" queryposition="below"><option value=""></option></cfselect>					
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="ma_billingZip">* Zip</label>		
								<div class="controls">
									<cfinput size="25" id="ma_billingZip" name="ma_billingZip" type="text" value="">
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="mp_phone">* Phone</label>		
								<div class="controls">
									<cfinput size="25" id="mp_phone" name="mp_phone" type="text" value="">
								</div>
							</div>
						</cfif>			
					</div>				
					<br/>
					
					<div id="step3form">
						<h4>#variables.strPageFields.Step3Title#</h4>
						<p>#variables.strPageFields.Step3Content#</p>
						<div>
							<div class="control-group">
								<label class="control-label">* How quickly do you need the report(s)?</label>			
								<div class="controls">
									<label class="radio">
										<input type="radio" name="priceNum" value="1">
										Receive in #variables.strPageFields.option1NumberOfDays# Business Days - $#variables.strPageFields.option1Price# USD
									</label>
									<cfif variables.strPageFields.option2Show eq true>
										<label class="radio">
											<input type="radio" name="priceNum" value="2">
											Receive in #variables.strPageFields.option2NumberOfDays# Business Days - $#variables.strPageFields.option2Price# USD
										</label>
									</cfif>
									<cfif variables.strPageFields.option3ShowNextBusinessDay eq true>
										<label class="radio">
											<input type="radio" name="priceNum" value="3">
											Next Business Day Express Service - $#variables.strPageFields.option3Price# USD
										</label>
									</cfif>
								</div>
							</div>
							<div class="control-group">		
								<label class="control-label" for="noofpeople">* Please select the number of expert reports:</label>		
								<div class="controls">
									<select id="noofpeople" name="noofpeople" >
										<option value="">-Select-</option>
										<option value="1">1</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
										<option value="7">7</option>
										<option value="8">8</option>
										<option value="9">9</option>
										<option value="10">10</option>
									</select>
								</div>
							</div>
							<div id="newExperts"></div>
						</div>
					</div>

					<script id="newExpertTemplate" type="text/x-handlebars-template">
						<div id="Expert-{{{pCount}}}" class="newExpertRow well">
							<legend>Expert {{{pCount}}} - Please Provide as much information as possible.</legend>
							<div class="control-group">
								<label class="control-label" for="m_e_firstName_{{pCount}}">*Expert First Name:</label>
								<div class="controls">
									<input type="text" id="m_e_firstName_{{pCount}}" name="m_e_firstName_{{pCount}}" value="" maxlength="200">	
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="m_e_middleName_{{pCount}}">Expert Middle Name or Initial:</label>
								<div class="controls">
									<input type="text" id="m_e_middleName_{{pCount}}" name="m_e_middleName_{{pCount}}" value="" maxlength="200">	
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="m_e_lastName_{{pCount}}">*Expert Last Name:</label>
								<div class="controls">
									<input type="text" id="m_e_lastName_{{pCount}}" name="m_e_lastName_{{pCount}}" value="" maxlength="200">	
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="m_city_{{pCount}}">*Expert City:</label>
								<div class="controls">
									<input type="text" id="m_city_{{pCount}}" name="m_city_{{pCount}}" value="" maxlength="200">
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="m_state_{{pCount}}">*Expert State:</label>
								<div class="controls">
									<input type="text" id="m_state_{{pCount}}" name="m_state_{{pCount}}" value="" maxlength="200">
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="m_area_{{pCount}}">*Tell us about the Expert Area of Expertise:</label>
								<div class="controls">
									<textarea  id="m_area_{{pCount}}" name="m_area_{{pCount}}" rows="4" maxlength="2000"></textarea>
								</div>
							</div>
						</div>
					</script>
					<div id=""></div>
					<div valign="top">
						<div><cfinput type="submit" name="btnSubmit" value="Continue" class="btn"></div>
					</div>
				</fieldset>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfif structKeyExists(arguments.rc, "iAgree")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		<cfelse>
			<cfset local.strResult.success = true>
		</cfif>

		<cfif local.strResult.success>
			<!--- billing fields --->
			<cfif arguments.rc.keyExists('ma_billingZip') AND arguments.rc.keyExists('ma_billingState')>
				<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=arguments.rc.ma_billingZip, billingState=arguments.rc.ma_billingState)>
				<cfif local.strBillingZip.isValidZip>
					<cfset arguments.rc.ma_billingZip = local.strBillingZip.billingzip>
				<cfelse>
					<cfthrow message="Invalid State/Zip.">
				</cfif>
			</cfif>

			<cfloop collection="#arguments.rc#" index="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "ma_" or left(local.thisField,3) eq "mp_" or local.thisField EQ 'noofpeople' or local.thisField EQ 'priceNum'>
					<cfset arguments.rc["#local.thisField#"] = stripHtml(arguments.rc["#local.thisField#"])>
				</cfif>
			</cfloop>

			<cfif val(variables.useMID)>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID,
					subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText,
					enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>
			</cfif>

			<!--- ----------------------------------------------------------- --->
			<!--- We need to ensure we have a depoMemberDataID at this point. --->
			<!--- ----------------------------------------------------------- --->
			<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
				<cfset arguments.rc["depoMemberDataID"] = session.cfcuser.memberdata.depomemberdataid>
			<cfelseif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and len(arguments.rc.mk)>
				<cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=arguments.rc.memberNumber)>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.memberData.memberID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
					<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
				</cfstoredproc>
				<cfif local.depoMemberDataID gt 0>
					<cfset arguments.rc["depoMemberDataID"] = local.depoMemberDataID>
				<cfelse>
					<cfset local.TrialSmithAllowedRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Site", functionName="TrialSmithAllowed")>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createDepoTLASITESAccount">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberData.memberID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.TrialSmithAllowedRFID#">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.TrialSmithMemberID">
					</cfstoredproc>
					<cfset arguments.rc["depoMemberDataID"] = local.depoMemberDataID>
				</cfif>
			<cfelse>
				<cfquery name="local.qryCreateAccount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY	

						declare @depoMemberDataID int, @firstName varchar(100), @lastName varchar(100), @uniqueNameID int, @sourceID int;
						set @firstName = <cfqueryparam value="#trim(arguments.rc.m_firstName)#" cfsqltype="CF_SQL_VARCHAR">;
						set @lastName = <cfqueryparam value="#trim(arguments.rc.m_lastName)#" cfsqltype="CF_SQL_VARCHAR">;
						select @uniqueNameID=uniqueID from dbo.depomemberdataUniqueNames where firstName=@firstName and lastName=@lastName;

						BEGIN TRAN;
							IF @uniqueNameID IS NULL BEGIN
								INSERT INTO dbo.depomemberdataUniqueNames(firstName, lastName, dateCreated, dateUpdated)
								VALUES(@firstName, @lastName, getdate(), getdate());

								SELECT @uniqueNameID = SCOPE_IDENTITY();
							END

							-- create sourceID that doesnt already exist
							SET @sourceID = 0;
							WHILE @sourceID = 0 BEGIN
								SET @sourceID = abs(cast(newid() as binary(6)) %999999) + 1;
								IF EXISTS (SELECT top 1 SourceID FROM dbo.depomemberdata WHERE SourceID = @sourceID)
									SET @sourceID = 0;
							END

							insert into dbo.depoMemberData (SourceID, memberenrolldate, BillingFirm, BillingADDRESS, 
								BillingADDRESS2, BillingCITY, BillingState, BillingZip, BillingCountry, RenewalDate, LastName, FirstNAME, Phone, FAX, 
								Email, MailCode, TLAMemberState, MemberType, PaymentType, pending, linksource, linkterms, signuporgcode, uniqueNameID, TSAllowed)
							values (
								@sourceID, getdate(),
								<cfqueryparam value="#trim(arguments.rc.m_company)#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#trim(arguments.rc.ma_billingaddress)#" cfsqltype="CF_SQL_VARCHAR">,
								NULL,
								<cfqueryparam value="#trim(arguments.rc.ma_billingcity)#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#trim(arguments.rc.ma_billingstate)#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#trim(arguments.rc.ma_billingzip)#" cfsqltype="CF_SQL_VARCHAR">,
								1,
								dateadd(year,5,getdate()),
								@lastName,
								@firstName,
								<cfqueryparam value="#arguments.rc.mp_phone#" cfsqltype="CF_SQL_VARCHAR">,
								'',
								<cfqueryparam value="#trim(arguments.rc.m_email)#" cfsqltype="CF_SQL_VARCHAR">,
								'A', 'NON', 1, 'C', 1, '', '', 'TS', @uniqueNameID, 0);

							select @depoMemberDataID = SCOPE_IDENTITY();
						COMMIT TRAN;

						select @depoMemberDataID as depoMemberDataID;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset arguments.rc["depoMemberDataID"] = local.qryCreateAccount.depoMemberDataID>
			</cfif>

			<cfset setExpertSession(arguments.rc)>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="stripHtml" access="private" output="false" returntype="string">
		<cfargument name="key" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfscript>
			local.str = arguments.key;
			// remove the whole tag and its content
			for (local.tag in "style,script,noscript"){
				local.str = reReplaceNoCase(local.str, "<s*(#local.tag#)[^>]*?>(.*?)","","all");
			}
			local.str = reReplaceNoCase(local.str, "<.*?>","","all");
			//get partial html in front
			local.str = reReplaceNoCase(local.str, "^.*?>","");
			//get partial html at end
			local.str = reReplaceNoCase(local.str, "<.*$","");

			return trim(local.str);
		</cfscript>
	</cffunction>
	
	<cffunction name="setExpertSession" access="private" output="no" returntype="void">
		<cfargument name="rc" type="struct" required="true">

		<cfset local.priceStruct = getPrice(val(arguments.rc.priceNum))>
		
		<cfset local.formData = {
			"mk":arguments.rc.mk,
			"membernumber":arguments.rc.membernumber,
			"depomemberdataid":arguments.rc.depomemberdataid,
			"email":arguments.rc.m_email,
			"mp_phone":arguments.rc.mp_phone,
			"m_company":arguments.rc.m_company,
			"ma_billingaddress":arguments.rc.ma_billingaddress,
			"ma_billingaddress2":arguments.rc.ma_billingaddress2,
			"ma_billingcity":arguments.rc.ma_billingcity,
			"ma_billingstate":arguments.rc.ma_billingstate,
			"ma_billingZip":arguments.rc.ma_billingZip,
			"reportRate":local.priceStruct.price,
			"accountCode":variables.accountCode,
			"noofpeople":arguments.rc.noofpeople,
			"useHistoryID":variables.useHistoryID,
			"priceStruct":local.priceStruct
		}>

		<cfset local.expertDetails = {}>
		<cfloop from="1" to="#arguments.rc.noofpeople#" index="local.count">
			<cfset local.expertDetails["m_e_firstName_#local.count#"] = arguments.rc['m_e_firstName_#local.count#']>
			<cfset local.expertDetails["m_e_lastName_#local.count#"] = arguments.rc['m_e_lastName_#local.count#']>
			<cfset local.expertDetails["m_e_middleName_#local.count#"] = arguments.rc['m_e_middleName_#local.count#']>
			<cfset local.expertDetails["m_city_#local.count#"] = arguments.rc['m_city_#local.count#']>
			<cfset local.expertDetails["m_state_#local.count#"] = arguments.rc['m_state_#local.count#']>
			<cfset local.expertDetails["m_area_#local.count#"] = arguments.rc['m_area_#local.count#']>
		</cfloop>

		<cfset structAppend(local.formData, local.expertDetails, true)>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND NOT len(arguments.rc.mk)>
			<cfset local.formData.firstName = arguments.rc.m_firstName>
			<cfset local.formData.lastName = arguments.rc.m_lastName>
		</cfif>

		<cfset application.mcCacheManager.sessionSetValue(keyname='strExpert', value=local.formData)>
	</cffunction>

	<!--- ----------------- -------->
	<!--- Buy now functions -------->
	<!--- ----------------- -------->
	<cffunction name="buyNow_parseItem" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
		<cfargument name="strBuyNowReturn" type="struct" required="yes">

		<cfscript>
		var local = structNew();

		// setup item parsing
		arguments.strBuyNowReturn.ItemType = arguments.strBuyNow.itemType; // EXPERT
		arguments.strBuyNowReturn.ItemKey = arguments.strBuyNow.itemKey; // EXPERT-MEMBERID
		arguments.strBuyNowReturn.ItemID = listGetAt(arguments.strBuyNow.itemKey,2,"-"); // MEMBERID
		arguments.strBuyNowReturn.ItemFolder = "EXPERT";
		arguments.strBuyNowReturn.thisCFC = this;

		// custom template overrides
		arguments.strBuyNowReturn.notFoundTemplate = "ItemNotFound_expert";

		local.strExpert = application.mcCacheManager.sessionGetValue(keyname='strExpert', defaultValue={});

		if(local.strExpert.count() gt 0){
			arguments.strBuyNowReturn.strReportCosts = calculateCosts(billingState=local.strExpert.ma_billingstate,
				billingZIP=local.strExpert.ma_billingZip, accountCode=local.strExpert.accountCode, reportRate=local.strExpert.reportRate,
				noofpeople=local.strExpert.noofpeople);
			arguments.strBuyNowReturn.noofpeople = local.strExpert.noofpeople;
			arguments.strBuyNowReturn.priceStruct = local.strExpert.priceStruct;
			arguments.strBuyNowReturn.mk = local.strExpert.mk;
			arguments.strBuyNowReturn.membernumber = local.strExpert.membernumber;
			arguments.strBuyNowReturn.depomemberdataid = local.strExpert.depomemberdataid;
			arguments.strBuyNowReturn.email = local.strExpert.email;
			arguments.strBuyNowReturn.mp_phone = local.strExpert.mp_phone;
			arguments.strBuyNowReturn.m_company = local.strExpert.m_company;
			arguments.strBuyNowReturn.ma_billingaddress = local.strExpert.ma_billingaddress;
			arguments.strBuyNowReturn.ma_billingaddress2 = local.strExpert.ma_billingaddress2;
			arguments.strBuyNowReturn.ma_billingcity = local.strExpert.ma_billingcity;
			arguments.strBuyNowReturn.ma_billingstate = local.strExpert.ma_billingstate;
			arguments.strBuyNowReturn.ma_billingZip = local.strExpert.ma_billingZip;
			arguments.strBuyNowReturn.useHistoryID = local.strExpert.useHistoryID;
			arguments.strBuyNowReturn.accountCode = local.strExpert.accountCode;

			if(NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND NOT len(local.strExpert.mk)){
				arguments.strBuyNowReturn.firstName = local.strExpert.firstName;
				arguments.strBuyNowReturn.lastName = local.strExpert.lastName;
			}

			for (local.i=1; local.i<=local.strExpert.noofpeople; local.i++) {
				arguments.strBuyNowReturn['m_e_firstName_#local.i#'] = local.strExpert['m_e_firstName_#local.i#'];	
				arguments.strBuyNowReturn['m_e_lastName_#local.i#'] = local.strExpert['m_e_lastName_#local.i#'];	
				arguments.strBuyNowReturn['m_e_middleName_#local.i#'] = local.strExpert['m_e_middleName_#local.i#'];	
				arguments.strBuyNowReturn['m_city_#local.i#'] = local.strExpert['m_city_#local.i#'];
				arguments.strBuyNowReturn['m_state_#local.i#'] = local.strExpert['m_state_#local.i#'];
				arguments.strBuyNowReturn['m_area_#local.i#'] = local.strExpert['m_area_#local.i#'];
			}

			arguments.strBuyNowReturn.qryMemberData = application.objMember.getTSMemberData(depoMemberDataID=local.strExpert.depomemberdataid);

			arguments.strBuyNowReturn.itemOK = true;
		}
		else {
			arguments.strBuyNowReturn.itemOK = false;
		}

		// basics
		arguments.strBuyNowReturn.buyNowPageTitle = "Expert Profiler Report Order Form";
		arguments.strBuyNowReturn.receiptTitle = "";
		arguments.strBuyNowReturn.phoneSupport = '************';

		// pricing defaults
		arguments.strBuyNowReturn.showShippingArea = false;
		arguments.strBuyNowReturn.showPaymentArea = true; // payment is required unless overridden below
		arguments.strBuyNowReturn.offerCoupon = false;
		arguments.strBuyNowReturn.noPaymentStatement = "No payment needed.";
		arguments.strBuyNowReturn.onReceiptStatement = "";
		arguments.strBuyNowReturn.purchaserTitle = "Payer";
		</cfscript>

		<cfreturn arguments.strBuyNowReturn>
	</cffunction>

	<cffunction name="buynow_buy" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.strResponse = { success=true, response='' }>
		<cfset local.returnstruct = structNew()>

		<cfset local.priceStruct = arguments.strBuyNow.priceStruct>
		<cfset local.reportRate = local.priceStruct.price>
		<cfset local.strReportCosts =  arguments.strBuyNow.strReportCosts>

		<cfset variables.useMID = session.cfcuser.memberData.memberID>

		<cfset local.cidForGather = "olddid_#arguments.strBuyNow.depomemberdataid#">
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and application.objPlatformStats.isValidStatsSessionID()>
			<cfset local.cidForGather = "oldsid_#session.cfcuser.statsSessionID#">
		</cfif>

		<cfset local.strArgs = { "customerid"=local.cidForGather, "amount"=local.strReportCosts.totalPrice, "chargeDesc"="TrialSmith.com Expert Knowledge Map Report", 
			"merchantProfile"='TS', "TransactionDepoMemberDataID"=arguments.strBuyNow.depomemberdataid }>
		<cfif arguments.event.valueExists('p_TS_tokenData') AND len(arguments.event.getTrimValue('p_TS_tokenData'))>
			<cfset local.strArgs['tokenData'] = deserializeJSON(arguments.event.getTrimValue('p_TS_tokenData'))>
		</cfif>
		<cfset local.strPaymentResponseResult = CreateObject("component","model.buyNow.BuyNow").chargeCC_TS(argumentcollection=local.strArgs)>

		<cfif local.strPaymentResponseResult.ccsuccess>
			<cfquery name="local.qryUpdateMember" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				UPDATE dbo.depomemberdata
				set <cfif structKeyExists(arguments.strBuyNow,"email")>email = <cfqueryparam value="#trim(arguments.strBuyNow.email)#" cfsqltype="CF_SQL_VARCHAR">,</cfif>
					phone = <cfqueryparam value="#trim(arguments.strBuyNow.mp_phone)#" cfsqltype="CF_SQL_VARCHAR">,
					<cfif structKeyExists(arguments.strBuyNow,"m_company")>billingfirm = <cfqueryparam value="#trim(arguments.strBuyNow.m_company)#" cfsqltype="CF_SQL_VARCHAR">,</cfif>
					billingaddress = <cfqueryparam value="#trim(arguments.strBuyNow.ma_billingaddress)#" cfsqltype="CF_SQL_VARCHAR">,
					billingaddress2 = <cfqueryparam value="#trim(arguments.strBuyNow.ma_billingaddress2)#" cfsqltype="CF_SQL_VARCHAR">,
					billingcity = <cfqueryparam value="#trim(arguments.strBuyNow.ma_billingcity)#" cfsqltype="CF_SQL_VARCHAR">,
					billingstate = <cfqueryparam value="#trim(arguments.strBuyNow.ma_billingstate)#" cfsqltype="CF_SQL_VARCHAR">,
					billingzip = <cfqueryparam value="#trim(arguments.strBuyNow.ma_billingZip)#" cfsqltype="CF_SQL_VARCHAR">
				where depomemberdataid = <cfqueryparam value="#arguments.strBuyNow.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;
			</cfquery>

			<cfset local.strExpert = application.mcCacheManager.sessionGetValue(keyname='strExpert', defaultValue={})>
			<cfset structAppend(local.strExpert, {"p_TS_mppid":arguments.event.getValue('p_TS_mppid',0)})>
			<cfif local.strPaymentResponseResult.keyExists("transactionDetail")>
				<cfset structAppend(local.strExpert, {"transactionDetail":local.strPaymentResponseResult.transactionDetail})>
			</cfif>
			<cfset application.mcCacheManager.sessionSetValue(keyname='strExpert', value=local.strExpert)>

			<cfif val(variables.useMID)>
				<cfset variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=arguments.event.getValue('mc_siteinfo.siteid'), parentCode='ExpertProfilerReportForm', subName='Started')>
				<cfset variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=arguments.event.getValue('mc_siteinfo.siteid'), parentCode='ExpertProfilerReportForm', subName='Completed')>
				<cfset variables.historyStartedText = "Member started form.">
				<cfset variables.historyCompletedText = "Member completed form.">
				
				<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=arguments.strBuyNow.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText & ' ' & local.priceStruct.details, 
					newAccountsOnly=false, qty=arguments.strBuyNow.noofpeople, dollarAmt=local.strReportCosts.totalPrice)>
			</cfif>

			<cftry>
				<cfquery name="local.qryCreateTransactions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						declare @depoMemberDataID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.strBuyNow.depomemberdataid#">;
						declare @tlamemberstate varchar(20), @pending char(2), @madeWhilePending char(2);

						select @tlamemberstate = tlamemberstate, @pending = pending 
						from dbo.depomemberdata 
						where depomemberdataid = @depoMemberDataID;

						IF @pending = 0
							SET @madeWhilePending = 1; 
						ELSE 
							SET @madeWhilePending = 0;
						
						BEGIN TRAN;
							<cfloop from="1" to="#val(arguments.strBuyNow.noofpeople)#" index="local.count">
								<cfset local.fullName = arguments.strBuyNow['m_e_firstName_#local.count#'] & ' ' & arguments.strBuyNow['m_e_lastName_#local.count#']>
								<cfset local.transactionDesc = 'Expert Knowledge Map Report - #local.fullName# - #local.priceStruct.details#'>

								insert into dbo.depoTransactions (depoMemberdataid, amountbilled, salestaxamount, accountcode, datepurchased, 
									description, SourceState, orgcode, linksource, linkterms, madeWhilePending, promoCode, stateForTax, zipForTax, statsSessionID)
								values (
									@depoMemberDataID,
									<cfqueryparam value="#NumberFormat(local.reportRate,"0.00")#" cfsqltype="CF_SQL_DOUBLE">,
									<cfqueryparam value="#NumberFormat(local.strReportCosts.taxAmount,"0.00")#" cfsqltype="CF_SQL_DOUBLE">,
									<cfqueryparam value="#arguments.strBuyNow.accountCode#" cfsqltype="CF_SQL_VARCHAR">,
									getdate(),
									<cfqueryparam value="#local.transactionDesc#" cfsqltype="CF_SQL_VARCHAR">,
									@tlamemberstate, 'TS', '', '', @madeWhilePending, null,
									<cfqueryparam value="#trim(arguments.strBuyNow.ma_billingstate)#" cfsqltype="CF_SQL_VARCHAR">,
									<cfqueryparam value="#trim(arguments.strBuyNow.ma_billingZip)#" cfsqltype="CF_SQL_VARCHAR">,
									<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">
								);								
							</cfloop>

							<cfif findNoCase("oldsid_", local.cidForGather) AND val(local.strPaymentResponseResult.payprofileid)>
								update dbo.ccMemberPaymentProfiles
								set customerid = 'olddid_' + cast(@depoMemberDataID as varchar(20)), 
									depomemberdataid = @depoMemberDataID
								where payProfileID = <cfqueryparam value="#local.strPaymentResponseResult.payprofileid#" cfsqltype="CF_SQL_INTEGER">;
							</cfif>

						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfset local.strResponse.success = true>
				<cfcatch type="Any">
					<cfset local.strErrorData = { rc=arguments.strBuyNow, strPaymentResponseResult=local.strPaymentResponseResult }>
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.strErrorData)>
					<cfset local.strResponse.success = false>
					<cfrethrow>
				</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.strResponse.success = false>
			<cfset local.strResponse.response = local.strPaymentResponseResult.ccresponse>
		</cfif>

		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="buyNow_receipt" access="public" output="no" returntype="void">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
		<cfset local.formAction='showConfirmation'>
		<!--- wait one second before showing the confirmtaion--->
		<cfset createObject("java","java.lang.Thread").sleep(1000)>
		<cflocation url="/?pg=ExpertProfilerReportOrder&fa=#local.formAction#" addtoken="false">
	</cffunction>

	<cffunction name="calculateCosts" access="private" output="false" returntype="struct">
		<cfargument name="billingState" type="string" required="true">
		<cfargument name="billingZIP" type="string" required="true">
		<cfargument name="accountCode" type="string" required="true">
		<cfargument name="reportRate" type="numeric" required="true">
		<cfargument name="noofpeople" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strResponse = {}>

		<cfquery name="local.qrySalesTax" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteCode varchar(10) = 'TS',
				@billingState varchar(2) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">,
				@billingZIP varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingZIP#">,
				@acctCode varchar(4) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountCode#">,
				@amountBilled numeric(18,2) = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" value="#arguments.reportRate#" scale="2">;

			SELECT dbo.fn_tax_getTax(@siteCode,@billingState,@billingZIP,@acctCode,@amountBilled) AS salesTax
		</cfquery>

		<cfset local.strResponse.taxAmount = local.qrySalesTax.salesTax>
		<cfset local.strResponse.totalPrice = 0>
		<cfset local.strResponse.totalTax = 0>
		<cfset local.strResponse.showPaymentArea = true>
		<cfloop from="1" to="#arguments.noofpeople#" index="local.count">
			<cfset local.strResponse.totalPrice = local.strResponse.totalPrice + arguments.reportRate + local.strResponse.taxAmount>
			<cfset local.strResponse.totalTax = local.strResponse.totalTax + local.strResponse.taxAmount>
		</cfloop>
		<cfset local.strResponse.totalPriceNoTax = val(arguments.reportRate) * val(arguments.noofpeople)>
		<cfif local.strResponse.totalPrice lte 0>
			<cfset local.strResponse.showPaymentArea = false>
		</cfif>

		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelseif arguments.errorCode eq "noreceipt">
					No receipt found.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="getPrice" access="private" output="false" returntype="struct">
		<cfargument name="priceNum" type="numeric" required="true" default="0">

		<cfset var local = structNew()>
		<cfset local.priceStruct = structNew()>
		
		<cfset local.priceStruct.price = 0>
		<cfset local.priceStruct.details = "">
		<cfif arguments.priceNum EQ 1>
			<cfset local.priceStruct.price = variables.strPageFields.option1Price>
			<cfset local.priceStruct.details = "Receive in #variables.strPageFields.option1NumberOfDays# Business Days">
		<cfelseif arguments.priceNum EQ 2>
			<cfset local.priceStruct.price = variables.strPageFields.option2Price>
			<cfset local.priceStruct.details = "Receive in #variables.strPageFields.option2NumberOfDays# Business Days">
		<cfelseif arguments.priceNum EQ 3>
			<cfset local.priceStruct.price = variables.strPageFields.option3Price>
			<cfset local.priceStruct.details = "Next Business Day Express Service">
		</cfif>

		<cfreturn local.priceStruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="strExpert" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.memberPayProfileDetail = "">
		<cfset local.priceStruct = arguments.strExpert.priceStruct>
		<cfset local.reportRate = local.priceStruct.price>

		<cfset local.strReportCosts = calculateCosts(billingState=arguments.strExpert.ma_billingstate, billingZIP=arguments.strExpert.ma_billingZip,
			accountCode=arguments.strExpert.accountCode, reportRate=arguments.strExpert.reportRate, noofpeople=arguments.strExpert.noofpeople)>

		<cfif local.strReportCosts.showPaymentArea>
			<cfquery datasource="#application.dsn.tlasites_trialsmith.dsn#" name="local.qryProfile">
				select mpp.detail
				from dbo.ccMemberPaymentProfiles as mpp
				where mpp.payProfileID = <cfqueryparam value="#arguments.strExpert.p_TS_mppid#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfset local.memberPayProfileDetail =local.qryProfile.detail>
		</cfif>

		<cfset local.memberData = session.cfcuser.memberData>
		<cfif len(arguments.strExpert.mk)>
			<cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=arguments.strExpert.membernumber)>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<style>
				td.frmText{padding: 6px;}
			</style>
			#variables.pageCSS#
			<h4>#variables.strPageFields.Step5Title#</h4>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>
			
			<br/>
			<div>Here are the details of your request:</div>
			<br/>
			<!--@@specialcontent@@-->
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="1">
			<tr>
				<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Contact Information</td>
			</tr>
			<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND NOT len(arguments.strExpert.mk)>
				<tr><td class="frmText b" width="50%">First Name:</td><td class="frmText">#arguments.strExpert.firstName#&nbsp;</td></tr>						 
				<tr><td class="frmText b">Last Name:</td><td class="frmText">#arguments.strExpert.lastName#&nbsp;</td></tr>
			<cfelse>
				<tr><td class="frmText b" width="50%">First Name:</td><td class="frmText">#local.memberData.firstname#&nbsp;</td></tr>						 
				<tr><td class="frmText b">Last Name:</td><td class="frmText">#local.memberData.lastName#&nbsp;</td></tr>
			</cfif>
			<tr><td class="frmText b">Law Firm:</td><td class="frmText">#arguments.strExpert.m_company#&nbsp;</td></tr>
			<tr><td class="frmText b">Email:</td><td class="frmText">#arguments.strExpert.email#&nbsp;</td></tr>
			<tr><td class="frmText b">Address:</td><td class="frmText">#arguments.strExpert.ma_billingAddress#&nbsp;</td></tr>
			<tr><td class="frmText b">&nbsp;</td><td class="frmText">#arguments.strExpert.ma_billingAddress2#&nbsp;</td></tr>
			<tr><td class="frmText b">City:</td><td class="frmText">#arguments.strExpert.ma_billingCity#&nbsp;</td></tr>
			<tr><td class="frmText b">State:</td><td class="frmText">#arguments.strExpert.ma_billingstate#&nbsp;</td></tr>
			<tr><td class="frmText b">Zip:</td><td class="frmText">#arguments.strExpert.ma_billingZip#&nbsp;</td></tr>
			<tr><td class="frmText b">Phone:</td><td class="frmText">#arguments.strExpert.mp_phone#&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr>
				<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Delivery</td>
			</tr>
			<tr><td class="frmText b" colspan="2">#local.priceStruct.details#&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr>
				<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Experts to be Investigated</td>
			</tr>
			<cfif len(arguments.strExpert.noofpeople)>
				<cfloop from="1" to="#val(arguments.strExpert.noofpeople)#" index="local.count">
					<tr><td colspan="2" class="frmText b">Expert #local.count#</td></tr>
					<tr><td class="frmText b" width="50%">Expert First Name:</td><td class="frmText">#arguments.strExpert['m_e_firstName_#local.count#']#&nbsp;</td></tr>
					<tr><td class="frmText b" width="50%">Expert Middle Name or Initial:</td><td class="frmText">#arguments.strExpert['m_e_middleName_#local.count#']#&nbsp;</td></tr>
					<tr><td class="frmText b" width="50%">Expert Last Name:</td><td class="frmText">#arguments.strExpert['m_e_lastName_#local.count#']#&nbsp;</td></tr>
					<tr><td class="frmText b">Expert City:</td><td class="frmText">#arguments.strExpert['m_city_#local.count#']#&nbsp;</td></tr>
					<tr><td class="frmText b">Expert State:</td><td class="frmText">#arguments.strExpert['m_state_#local.count#']#&nbsp;</td></tr>
					<tr><td class="frmText b">Tell us about the Expert's Area of Expertise:</td><td class="frmText" style="white-space: pre-wrap;">#arguments.strExpert['m_area_#local.count#']#&nbsp;</td></tr>
					<tr><td colspan="2">&nbsp;</td></tr>
				</cfloop>
			</cfif>
			<cfif local.strReportCosts.showPaymentArea>
				<tr>
					<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Information</td>
				</tr>
				<tr><td class="frmText b">Total Amount </td><td class="frmText">#DollarFormat(local.strReportCosts.totalPriceNoTax)#<cfif local.strReportCosts.totalTax GT 0> (+ #DollarFormat(local.strReportCosts.totalTax)# tax)</cfif></td></tr>
				<tr>
					<td class="frmText b">Payment Method: </td>
					<cfif arguments.strExpert.keyExists("transactionDetail") AND len(arguments.strExpert.transactionDetail)>
						<td class="frmText">#arguments.strExpert.transactionDetail#</td>
					<cfelseif len(local.memberPayProfileDetail)>
						<td class="frmText"> Credit Card - #local.memberPayProfileDetail#</td>
					<cfelse>
						<td class="frmText b">None selected.</td>
					</cfif>
				</tr>
			</cfif>
			</table>				
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) OR len(arguments.strExpert.mk)>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.memberEmail.from },
				emailto=[{ name="", email=variables.memberEmail.to }],
				emailreplyto=variables.ORGEmail.to,
				emailsubject=variables.strPageFields.ConfirmationSub,
				emailtitle="#arguments.rc.mc_siteinfo.siteName# - #variables.formNameDisplay#",
				emailhtmlcontent=local.confirmationHTML,
				siteID=variables.siteid,
				memberID=val(variables.useMID),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>
			<cfset local.emailSentToUser = local.responseStruct.success>
		<cfelse>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                emailfrom={ name="", email=variables.memberEmail.from },
                emailto=[{ name="", email=variables.memberEmail.to }],
                emailreplyto=variables.ORGEmail.to,
                emailsubject=variables.memberEmail.subject,
                emailtitle="#arguments.rc.mc_siteinfo.siteName# - #variables.formNameDisplay#",
                emailhtmlcontent=local.confirmationHTML,
                siteID=variables.siteid,
                memberID=val(arguments.rc.mc_siteinfo.sysMemberID),
                messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                sendingSiteResourceID=this.siteResourceID
            )>   
			<cfset local.emailSentToUser = local.responseStruct.success>
		</cfif>

		<cfset local.tsMemberNumber = "<a href='https://admin.trialsmith.com/MemberEdit.cfm?depoMemberDataID=#arguments.strExpert.depoMemberDataID#' target='_blank'>#arguments.strExpert.depoMemberDataID#</a>">

		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<cfif not application.objUser.isLoggedIn(cfcuser=session.cfcuser) and not len(arguments.strExpert.mk)>
				<div><b>New record has been created in TS Admin.</b></div>
			</cfif>
			<div style="padding-bottom:4px;">TS Admin DepoID : <b>#local.tsMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset variables.ORGEmail.SUBJECT = variables.strPageFields.TSStaffConfirmationSub>
		<cfset variables.ORGEmail.FROM = variables.strPageFields.RequestorConfirmationFrom>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.siteCode, strEmail=variables.ORGEmail, emailTitle="#variables.ORGEmail.SUBJECT#", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>