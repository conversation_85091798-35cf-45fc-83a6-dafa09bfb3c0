<cfcomponent extends="model.customPage.customPage" output="false">

<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();		
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		variables.customPage.baseURL = "/?#getBaseQueryString(false)#";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		
		local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="TTLA Board Agreement and Credit Card Authorization Form" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DuesSummary",type="CONTENTOBJ",desc="Dues Summary",value="Each member of the TTLA Board of Directors shall pay or raise as Director Dues the sum of $5000 for 2018. " }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="OptionTitle",type="STRING",desc="Option Title",value="Please choose one option:" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="RecruitText",type="STRING",desc="Recruit Text",value="I intend to RECRUIT new TTLA memberships to raise the sum of $5000. You may process any remaining Board Dues balance on Oct. 1 to the pay method on file for my account after giving me a 30 day reminder." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PayText",type="STRING",desc="Pay Text",value="I will PAY the sum of $5000 in Board Dues in a onetime lump sum payment. Please process payment in full now to the pay method on file for my account." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="InstallmentText",type="STRING",desc="Installment Text",value="I will PAY my Board Dues balance in 10 equal monthly installment payments. Please process payments to the pay method on file for my account." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BoardAgreement",type="CONTENTOBJ",desc="Board Agreement",value="IT IS AGREED THAT:<br>(1) I agree to serve on the TTLA Board of Directors and meet the Board Dues requirement as indicated above.<br>(2) Each debit, upon being charged to one of my account(s) by the respective bank(s) or credit card(s) shall be my receipt for payment of the designated contribution.<br>(3) I reserve the right to revoke this authorization by giving written notice to TTLA." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="TaxText",type="CONTENTOBJ",desc="Tax Text",value="** Dues to professional associations are normally deductible as ordinary and necessary business expenses; however, the portion of dues related to lobbying, an estimated 35% for 2018, is not deductible.  As always, we suggest you consult your tax attorney or tax advisor for further details.  Contributions to TTLA are not deductible as charitable contributions for Federal Income Tax purposes." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCC",type="STRING",desc="pay profile code for CC",value="TTLA-Authorize" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeBank",type="STRING",desc="pay profile for bank draft",value="TTLABD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page",value="Thank you for submitting your payment preference." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="EmailConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation email",value="Thank you for submitting your payment preference." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation	",value="TTLA - Board Membership Authorization received" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="TTLA - Board Membership Authorization Received" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationTitle",type="STRING",desc="Title at top of user confirmation page",value="Thank you for submitting your TTLA Board authorization." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DuplicateErrorMessage",type="CONTENTOBJ",desc="Error Message for Duplicate Entries ",value="Member already exist." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CompletedForm",type="STRING",desc="Member denied access - Completed Form ",value="4c127fdc-dd74-46e6-b0ab-2fa054e66eec" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='BoardAgreement',
			formNameDisplay='Board Agreement',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
				variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
			if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
				variables.useHistoryID = int(val(session.useHistoryID));	
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='BOARDAGREEMENT', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='BOARDAGREEMENT', subName='Completed');
		variables.historyStartedText = "Member started board agreement";
		variables.historyCompletedText = "Member completed board agreement";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		
		switch (local.formAction) {
				case "processLookup":
					if(isMemberInGroup(memberID=session.cfcuser.memberdata.memberID,orgID=variables.orgID,groupUID=variables.strPageFields.CompletedForm)){
						local.returnHTML=showError(errorCode="accessdenied");
						break;
					}
						
					local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
					
					break;
				
				case "processPayment":
					switch (processPayment(rc=arguments.event.getCollection())) {
						case "success": 
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfif not local.isLoggedIn and variables.useMID eq 0>			
			<cflocation url="/?pg=login&logact=boardauthorization">		
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					.subRateLabel {font-weight:normal;}
					.subRatesDisabled {
						opacity: 0.6; /* Real browsers */
						filter: alpha(opacity = 60); /* MSIE */
						
					}
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;
					function addMember(memObj) {
						$.colorbox.close();
						assignMemberData(memObj);
					}
						
					function assignMemberData(memObj) {
						$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
						 mc_continueForm($('###variables.formName#'));
					}
					
					function validatePaymentForm() {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);     
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processMemberInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous');			        	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}
					$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show(); 
					</cfif>
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
					<cfinput type="hidden" name="fa" id="fa" value="processLookup">
					<cfinput type="hidden" name="memberID" id="memberID" value="">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="">
					
					<div class="tsAppSectionHeading">Account Lookup / Create New Account</div>
					<div class="tsAppSectionContentContainer">
						<table cellspacing="0" cellpadding="2" border="0" width="100%">
						<tr>
							<td width="175" style="text-align:center;">
								<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">Account Lookup</button>
							</td>
							<td class="tsAppBodyText">Click the <span class="b">Create Account</span> button to the left to begin the process.</td>
						</tr>
						</table>
					</div>
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		<cfset variables.qryStates = application.objCommon.getStates()>
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
		
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structkeyexists(session,"useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style>
					.FormContentTitle{font-weight:bold;text-align:center;font-size:18px;}
					.DueContent{ margin-left:10px;color:##222;}
					.SummaryContent{font-weight: normal; margin-left:10px; font-size:14px; font-family: "Neutraface 2 Text Book";}					
					input[type=radio] {   float: left;}
					input[type=file] {  font-size: 14px;}
					label {margin-left: 20px;display: block;}
					input##signatureField {height: auto;padding: 15px;}
				</style>
				<script language="javascript">
				$(document).ready(function() {
					prefillData();
					$('##paymentContainer').hide();
				});	
				function prefillData() {
						var objPrefill = new Object();
						<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
							#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfloop>
						for (var key in objPrefill) {
							if (objPrefill.hasOwnProperty(key)) { 
								$('###variables.formName# ##'+key).val(objPrefill[key]);
							}
						}
					}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();					
					
					
					
					var fields = $("input[name='dueOptionbutton']").serializeArray(); 
					if (fields.length === 0) 
					{ 
						arrReq[arrReq.length] = "Please choose one option.";						
					
					}
					 if ($.trim($('##signatureField').val()).length == 0) arrReq[arrReq.length] = "Signature needed to be uploaded";
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					else{
						$('##FirstPageContent').hide();
						hideAlert();
						$('##paymentContainer').show();
						$("html, body").animate({ scrollTop: 0 }, "slow");
					}
				}
				function goBackToPage(){
					$('##FirstPageContent').show();
					$('##paymentContainer').hide();
				}
				function hideAlert() { $('##divFrmErr').html('').hide(); }
				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
			<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" enctype="multipart/form-data" class="form-horizontal"  onsubmit="return validatePaymentForm()">
				<input type="hidden" name="fa" id="fa" value="processPayment">
				<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
				<input type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
				<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
				
				<cfif NOT local.isLoggedIn AND local.identifiedMemberID GT 0>
					<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
				</cfif>

				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
				<div id="FullPage">
					<div id="FirstPageContent">
						
						<cfif len(variables.strPageFields.FormTitle)>
							<div class="FormContentTitle">#variables.strPageFields.FormTitle#<br></div>
						</cfif>
						<div class="DueContent">#variables.strPageFields.DuesSummary#</div>
						<cfset local.mccfRecruitText=0>
						<cfset local.mccfPayText=0>
						<cfset local.mccfInstallmentText=0>
						<cfif structKeyExists(arguments.rc, "mccf_RecruitTextradio")>
							<cfset local.mccfRecruitText = 1>
						</cfif>
						<cfif structKeyExists(arguments.rc, "mccf_PayTextradio")>
							<cfset local.mccfPayText = 1>
						</cfif>
						<cfif structKeyExists(arguments.rc, "mccf_InstallmentTextradio")>
							<cfset local.mccfInstallmentText = 1>
						</cfif>
							<div id="PageOne">
								<div class="fieldsetFormWrapper" id="TextoptionSelect">
									<div class="SummaryContent tsAppSectionHeading">#variables.strPageFields.OptionTitle#</div>
									<div class="tsAppSectionContentContainer" id="optionSelection">
										<input class="subCheckbox" type="radio" name="dueOptionbutton" id="mccf_RecruitTextradio"<cfif local.mccfRecruitText>checked="checked"</cfif> value="Recruit New Members"> <label for="mccf_RecruitTextradio">#variables.strPageFields.RecruitText# </label>
										<input class="subCheckbox" type="radio" name="dueOptionbutton" id="mccf_PayTextradio" <cfif local.mccfPayText>checked="checked"</cfif> value="Pay In Full"> <label for="mccf_PayTextradio">#variables.strPageFields.PayText#</label>
										<input class="subCheckbox" type="radio" name="dueOptionbutton" id="mccf_InstallmentTextradio" <cfif local.mccfInstallmentText>checked="checked"</cfif> value="10-Month Installments"> <label for="mccf_InstallmentTextradio">#variables.strPageFields.InstallmentText#</label>			
									</div>
								</div>	
								<div class="fieldsetFormWrapper tsAppSectionHeading SummaryContent">
									<div class="SummaryContent">#variables.strPageFields.BoardAgreement#</div><br>
									<div class="SummaryContent"><b>Signature / Authorization:</b>
										<input name="signatureField" id="signatureField" type="text" size="30" class="BodyText input-xlarge fieldSize" value="" />	</div>
									<div class="SummaryContent" style="font-style:italic">#variables.strPageFields.TaxText#</div>
								</div><br>
								<button name="btnContinue" type="button" class="tsAppBodyButton" onClick="return validateMemberInfoForm();">Continue</button>
							</div>
						</div>
						<br>
						<!--- showpayment --->
							<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC, variables.strPageFields.ProfileCodeBank ]>
							<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
													arrPayMethods=local.arrPayMethods, 
													siteID=variables.siteID, 
													memberID=variables.useMID, 
													title="Choose Your Payment Method", 
													formName=variables.formName, 
													backStep="showMembershipInfo"
												)
											>
						<cfsavecontent variable="local.headcode">
							<cfoutput>#local.strReturn.headcode#</cfoutput>
						</cfsavecontent>
						<cfhtmlhead text="#local.headcode#">
						<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
						<div class="paymentContainer" id="paymentContainer">
							<script type="text/javascript">
								$(document).ready(function() {
									$('##paymentContainer [name="btnBack"]').attr('onclick','goBackToPage();');
								});
							</script>
							<div class="paymentBlock" id="paymentBlock">
								#local.strReturn.paymentHTML#
							</div>
						</div>
					</div>
				</form>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>
		
		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field="Board Agreement Payment Option", value=arguments.rc.dueOptionbutton)>
		<cfset local.objSaveMember.setCustomField(field="Board Agreement Signature", value=arguments.rc.signatureField)>
		<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>
				<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
			</cfif>
		
			<cfset local.strResult = local.objSaveMember.saveData()>
			<cfif local.strResult.success>
	 			<cfset variables.useMID = local.strResult.memberID>
				<cfset local.response = 'success'>
			</cfif>
			
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>
		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		
		<cfset local.memberPayProfileDetail = "">
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
			
			<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
			<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div>Here are the details of your Agreement:</div><br/>
			
			</table>
			
			
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Member Information</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						First Name	:	#local.strPrefillMemberData.m_firstname#
					</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						Last Name	:	#local.strPrefillMemberData.m_lastname#
					</td>
				</tr>
			</table>
			
			
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Option Selected</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#arguments.rc.dueoptionbutton#
					<br/>
				</div>
				<br/>
				</td>
			</tr>
			</table>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.FROM },
			emailto=[{ name="", email=variables.memberEmail.TO }],
			emailreplyto=variables.ORGEmail.TO,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber:<b> <a href="https://#arguments.rc.mc_siteinfo.mainhostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></div>			
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif  structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.Subject = variables.strPageFields.StaffConfirmationSub >
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="#variables.strPageFields.ConfirmationTitle#", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationTitle#</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="isMemberInGroup" access="public" returntype="boolean">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="groupUID" type="string" required="true">
		
		<cfset var local = structNew() />
		<cfset local.returnValue = false />	
		<cfset local.qryData = application.objMember.getMemberGroups(arguments.memberID,arguments.orgID) />
		
		<cfquery name="local.getGroupID" datasource="#application.dsn.membercentral.dsn#">
			SELECT groupID from ams_groups
			WHERE 	uid=<cfqueryparam cfsqltype="VARCHAR" value= "#arguments.groupUID#">
			AND orgid=<cfqueryparam cfsqltype="INTEGER" value="#arguments.orgID#">
		</cfquery>	
		
		<cfquery dbtype="query" name="local.checkMemberGroup">
			SELECT *
			FROM [local].qryData
			WHERE groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value ="#local.getGroupID.groupID#">
		</cfquery>
		
		<cfif local.checkMemberGroup.recordCount>
			<cfset local.returnValue = true />
		</cfif>
		
		<cfreturn local.returnValue />
		
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "fail">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelseif arguments.errorCode eq "accessdenied">
					<cfoutput>#variables.strPageFields.DuplicateErrorMessage#</cfoutput>
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>	