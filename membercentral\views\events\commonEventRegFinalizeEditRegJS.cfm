<cfsavecontent variable="local.editRegJS">
	<cfoutput>
	<script type="text/javascript">
		function hideAlert() { $('##evRegPmtErr').html('').hide(); };
		function showAlert(msg) { $('##evRegPmtErr').html(msg).attr('class','alert').show(); };

		function selectPayment(pid) {
			hideAlert();
			$('##profileid').val(pid);
		}
		function evRegMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
						var iframeWin = document.querySelector('iframe[name=iframeManageForm'+event.data.profileid+']').contentWindow;
						var message = { success:true, 
										messagetype:"MCFrontEndPaymentEvent", 
										paymentbuttonname:$('##divBtnWrapper'+event.data.profileid+' button[type="submit"]').text(), 
										profileid:event.data.profileid };
						iframeWin.postMessage(message, event.origin);
						break;

					case "mcgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.payprofileid) {
							showEditEvRegPaymentProcessing(event.data.profileid);
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmPurchaseEvReg');
							setEditEvRegPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchaseEvReg').submit();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							showEditEvRegPaymentProcessing(event.data.profileid);
							setEditEvRegPaymentTokenData(event.data.tokendata,event.data.profileid);
							setEditEvRegPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchaseEvReg').submit();
						}
						break;
				};

			} else {
				return false;
			}
		}
		function showEditEvRegPaymentProcessing(pid) {
			$('##divPaymentTable' + pid)
				.html('<i class="icon icon-spinner"></i> Please Wait...')
				.css({'height':'75px', 'padding':'5px'});
		}
		function setEditEvRegPaymentProcessingFeesField(obj) {
			if (typeof obj.enableprocessingfee != "undefined") {
				let processFeeDonationElement = $('##processFeeDonation'+obj.profileid);
				if (processFeeDonationElement.length) {
					processFeeDonationElement.val(obj.enableprocessingfee);
				} else {
					$('<input>').attr({ type: 'hidden', name: 'processFeeDonation'+obj.profileid, id: 'processFeeDonation'+obj.profileid }).val(obj.enableprocessingfee).appendTo('form##frmPurchaseEvReg');
				}
			}
		}
		function setEditEvRegPaymentTokenData(tokendata,profileid) {
			let tokenDataElement = $('##p_'+profileid+'_tokenData');
			if (tokenDataElement.length) {
				tokenDataElement.val(JSON.stringify(tokendata));
			} else {
				$('<input>').attr({ type: 'hidden', name: 'p_'+profileid+'_tokenData', id: 'p_'+profileid+'_tokenData' }).val(JSON.stringify(tokendata)).appendTo('form##frmPurchaseEvReg');
			}
		}
		
		$(function() {
			<cfif arguments.event.valueExists('perr')>
				showAlert('There was a problem processing the payment for this purchase.<br/>#JSStringFormat(HTMLEditFormat(arguments.event.getValue("perr")))#');
			</cfif>

			if (window.addEventListener) {
				window.addEventListener("message", evRegMessageHandler, false);
			} else if (window.attachEvent) {
				window.attachEvent("message", evRegMessageHandler);
			}
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.editRegJS)#">