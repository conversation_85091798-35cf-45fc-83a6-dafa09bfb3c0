<cfcomponent extends="model.customPage.customPage" output="false">
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		variables.fund = arguments.event.getValue('fund','');
		local.formAction = arguments.event.getValue('fa','showLookup');

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CCPayProfileCode", type="STRING", desc="pay profile code for CC", value="CCBA_CC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DonationFormHeaderTitle", type="CONTENTOBJ", desc="Donation form header title", value="Editable Title" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DonationFormHeaderContent", type="CONTENTOBJ", desc="Donation form header content", value="Editable Content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentConfirmation", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you for your donation." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmDonation',
			formNameDisplay='Donation Form',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));
		
		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
 				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
				break;
			case "processDonationInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processDonationInfo(rc=arguments.event.getCollection())) {
					case "showPayment":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					case "skipPayment":
						switch (processPayment(rc=arguments.event.getCollection())) {
							case "success": 
								local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
								local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
								structDelete(session, "formFields");
								break;
							default:
								local.returnHTML = showError(errorCode='failpayment');
								break;				
						}
						break;
					default:
						local.returnHTML = showError(errorCode='fail');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;
				}
				switch (processPayment(rc=arguments.event.getCollection())) {
					case "success": 
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			default:
				arguments.event.collectionAppend(variables);
				local.returnHTML = showLookup(rc=arguments.event.getCollection());
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>	
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##div#variables.formName#loading [class^="icon-"], [class*=" icon-"] {width:auto;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				
				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processDonationInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="">
			<cfinput type="hidden" name="fund" id="fund" value="#arguments.rc.fund#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>

		<cfif not len(trim(variables.fund)) and structKeyExists(arguments.rc, "fund")>
			<cfset variables.fund = arguments.rc.fund>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='4ef298ee-90d8-4f73-a395-15fd7fb0e39c', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='73c86b78-b534-4af0-b80c-1c52d0ff258e', mode="collection", strData=local.strData)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				var IndividualInfoContainer = '<div class="tsAppSectionHeading">#JSStringFormat(local.strFieldSetContent1.fieldSetTitle)#</div><div class="tsAppSectionContentContainer">#JSStringFormat(local.strFieldSetContent1.fieldSetContent)#</div>';
				var orgInfoContainer = '<div class="tsAppSectionHeading">#JSStringFormat(local.strFieldSetContent2.fieldSetTitle)#</div><div class="tsAppSectionContentContainer">#JSStringFormat(local.strFieldSetContent2.fieldSetContent)#</div>';
				
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					var fund_type = $('##fund_type').val();
					var contType = $('input[name="contributionType"]:checked').val();
					if (fund_type == '') arrReq[arrReq.length] = 'Select Fund/Campaign type.';
					if (contType == undefined) arrReq[arrReq.length] = 'Select type of contribution.';
					
					if ($('##paymentPlan').val() == '') arrReq[arrReq.length] = 'Select an Amount.';
					else if ($('##paymentPlan').val() == 'Other' &&  ( ($.trim($('##otherAmt').val()).length == 0) ||( $.trim($('##otherAmt').val()).length > 0 && Number($('##otherAmt').val().replace(/[^0-9\.]+/g,"")) == 0 ) ) )
						arrReq[arrReq.length] = 'Donation - Enter a valid amount. Only positive amounts are allowed.';
					
					if(fund_type != '' && (fund_type != 'Legal Aid of Southeastern Pennsylvania' && fund_type != 'CCBA Lawyers\' Fund') && $('input[name="pmtOption"]:checked').val() == undefined)
						arrReq[arrReq.length] = 'Select Payment Option.';
					
					if (contType == 'Individual') {
						#local.strFieldSetContent1.jsValidation#
					} else if (contType == 'Organization') {
						#local.strFieldSetContent2.jsValidation#
					}
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					
					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				function toggleDonorInfo() {
					var contType = $('input[name="contributionType"]:checked').val();
					$('##donorInformation').html('');
					if (contType == 'Individual') $('##donorInformation').html(IndividualInfoContainer);
					else if (contType == 'Organization') $('##donorInformation').html(orgInfoContainer);
					
					prefillData();
					togglePaymentPlans();
				}
				function togglePaymentPlans() {
					$("##paymentPlan option").remove();
					 $('##otherAmt').hide();
					var fund_type = $('##fund_type').val();
					var contType = $('input[name="contributionType"]:checked').val();
					var paymentOptions = {
						'legalAidPersonal' : { 'value' : ['$50','$200','$500','Other'] },
						'legalAidOrg' : { 'value' : ['$1000','$2000','$3000','Other'] },
						'ccbaLawyer' : { 'value' : ['$50','$100','$250','$500','$1000','$2500','$5000','Other'] },
						'foundationFellow' : { 'value' : ['$1,500 one-time payment','$500 per year for 3 years','$300 per year for 5 years'] },
						'foundationKeyFellow' : { 'value' : ['$1,000 one-time payment','$500 per year for 2 years','$250 per year for 4 years'] },
						'newKeyFellow' : { 'value' : ['$2,500 one-time payment','$500 per year for 5 years'] },
						'corpFellow' : { 'value' : ['$4,000 one-time payment','$2,000 per year for 2 years','$1000 per year for 4 years'] }
					};
					var mappedPmtOption = null;
					if (fund_type == 'Legal Aid of Southeastern Pennsylvania') {
						if (contType == 'Individual') mappedPmtOption = paymentOptions.legalAidPersonal;
						else if (contType == 'Organization') mappedPmtOption = paymentOptions.legalAidOrg;
					}
					else if (fund_type == 'CCBA Lawyers\' Fund') mappedPmtOption = paymentOptions.ccbaLawyer;
					else if (fund_type == 'Foundation Fellow - $1,500') mappedPmtOption = paymentOptions.foundationFellow;
					else if (fund_type == 'Foundation Key Fellow (for existing Foundation Fellows only) - $1,000') mappedPmtOption = paymentOptions.foundationKeyFellow;
					else if (fund_type == 'New Key Fellow (Foundation Fellow + Key Fellow) - $2,500') mappedPmtOption = paymentOptions.newKeyFellow;
					else if (fund_type == 'Corporate Fellow (for organizations only) - $4,000') mappedPmtOption = paymentOptions.corpFellow;
					
					if(fund_type == '' || fund_type == 'Legal Aid of Southeastern Pennsylvania' || fund_type == 'CCBA Lawyers\' Fund') $('##pmtOptionRow').hide();
					else $('##pmtOptionRow').show();
					
					if (mappedPmtOption != null) {
						$('##paymentPlan').append('<option value="">Please select</option>');
						for(var i=0; i<mappedPmtOption.value.length; i++) {
							$('##paymentPlan').append('<option value="' +mappedPmtOption.value[i]+ '">' +mappedPmtOption.value[i]+ '</option>');
						}
					}
				}
				function showHidePaymentAmt(){
					if($('##paymentPlan').val() == 'Other') $('##otherAmt').show().val('');
					else $('##otherAmt').hide();
				}
				
				$(function() {
					<cfif structKeyExists(local.strData, "fund_type") AND local.strData.fund_type neq ''>
						togglePaymentPlans();
						<cfif structKeyExists(local.strData, "paymentPlan") AND local.strData.paymentPlan neq ''>
							$('##paymentPlan').val('#local.strData.paymentPlan#');
							showHidePaymentAmt();
							<cfif structKeyExists(local.strData, "otherAmt")>
								$('##otherAmt').val('#local.strData.otherAmt#');
							</cfif>
						</cfif>
					</cfif>
					<cfif structKeyExists(local.strData, "contributionType")>
						<cfif local.strData.contributionType eq 'Individual'>
							 $('##donorInformation').html(IndividualInfoContainer);
						<cfelseif local.strData.contributionType eq 'Organization'>
							 $('##donorInformation').html(orgInfoContainer);
						</cfif>
					</cfif>
				});
			</script>
			<style type="text/css">
				###variables.formName# input[type="text"] {min-height:30px;}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div style="clear:both;"></div>
				<div style="width:75%;margin:0 auto;">
					<div class="TitleText" style="padding-bottom:15px;">#variables.strPageFields.DonationFormHeaderTitle#</div>
					#variables.strPageFields.DonationFormHeaderContent#
					<div style="clear:both;"></div>
					<br /><br />
					
					<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
						<input type="hidden" name="fa" id="fa" value="processDonationInfo">
						<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
						<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
						<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
						<cfinclude template="/model/cfformprotect/cffp.cfm">
						
						<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
						
						<div class="tsAppSectionContentContainer">
							<table cellspacing="0" cellpadding="3" border="0" style="width:100%;">
								<tbody>
									<tr valign="top">
										<td class="tsAppBodyText" width="150">Fund/Campaign</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<select name="fund_type" id="fund_type" style="width:475px;" onchange="togglePaymentPlans();">
												<option value="">Please Select</option>
												<option value="Legal Aid of Southeastern Pennsylvania" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "Legal Aid of Southeastern Pennsylvania") OR variables.fund eq "legalAid">selected="true"</cfif>>
												Legal Aid of Southeastern Pennsylvania</option>
												<option value="CCBA Lawyers' Fund" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "CCBA Lawyers' Fund") OR variables.fund eq "ccbalawyer">selected="true"</cfif>>
												CCBA Lawyers' Fund</option>
												<option value="Foundation Fellow - $1,500" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "Foundation Fellow - $1,500") OR variables.fund eq "foundationFellow">selected="true"</cfif>>
												Foundation Fellow - $1,500</option>
												<option value="Foundation Key Fellow (for existing Foundation Fellows only) - $1,000" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "Foundation Key Fellow (for existing Foundation Fellows only) - $1,000") OR variables.fund eq "foundationKeyFellow">selected="true"</cfif>>
												Foundation Key Fellow (for existing Foundation Fellows only) - $1,000</option>	
												<option value="New Key Fellow (Foundation Fellow + Key Fellow) - $2,500" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "New Key Fellow (Foundation Fellow + Key Fellow) - $2,500") OR variables.fund eq "newKeyFellow">selected="true"</cfif>>
												New Key Fellow (Foundation Fellow + Key Fellow) - $2,500</option>
												<option value="Corporate Fellow (for organizations only) - $4,000" <cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "Corporate Fellow (for organizations only) - $4,000") OR variables.fund eq "corpFellow">selected="true"</cfif>>
												Corporate Fellow (for organizations only) - $4,000</option>	
											</select>
										</td>
									</tr>
									<tr valign="top">
										<td class="tsAppBodyText">Type</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<label class="radio inline">
												<input type="radio" name="contributionType" id="contributionType1" onclick="toggleDonorInfo();" value="Individual" <cfif structKeyExists(local.strData, "contributionType") and local.strData.contributionType eq 'Individual'>checked="checked"</cfif>> Individual Contribution &nbsp;
											</label>
											<label class="radio inline">
												<input type="radio" name="contributionType" id="contributionType2" onclick="toggleDonorInfo();" value="Organization" <cfif structKeyExists(local.strData, "contributionType") and local.strData.contributionType eq 'Organization'>checked="checked"</cfif>> Organization Contribution
											</label>
										</td>
									</tr>
									<tr><td colspan="3"><hr><br /></td></tr>
									<tr valign="top">
										<td class="tsAppBodyText" nowrap="" width="150">Amount</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<select name="paymentPlan" id="paymentPlan" style="width:400px;" onchange="showHidePaymentAmt();"></select>
											&nbsp;<input type="text" name="otherAmt" id="otherAmt" placeholder="Amount" style="display:none;" onblur="this.value=formatCurrency(this.value);" value="">
										</td>
									</tr>
									<tr id="pmtOptionRow" valign="top" style="display:none;">
										<td class="tsAppBodyText">Payment Options</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<label class="radio inline">
												<input type="radio" name="pmtOption" id="pmtOption1" value="card" <cfif structKeyExists(local.strData, "pmtOption") and local.strData.pmtOption eq 'card'>checked="checked"</cfif>> Auto-pay with card provided &nbsp;
											</label>
											<label class="radio inline">
												<input type="radio" name="pmtOption" id="pmtOption2" value="invoice" <cfif structKeyExists(local.strData, "pmtOption") and local.strData.pmtOption eq 'invoice'>checked="checked"</cfif>> Send me an invoice
											</label>
										</td>
									</tr>
									<tr><td colspan="3">&nbsp;</td></tr>
									<tr valign="top">
										<td class="tsAppBodyText">Donor Comments</td>
										<td class="tsAppBodyText"></td>
										<td class="tsAppBodyText">
											<textarea name="donorComments" id="donorComments" rows="5" style="width:400px;"><cfif structKeyExists(local.strData, "donorComments")>#local.strData.donorComments#</cfif></textarea>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div id="donorInformation"></div>
						<button name="btnContinue" type="submit" class="btn tsAppBodyButton pull-right" onClick="hideAlert();">Next &gt;&gt;</button>
					</form>
				</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processDonationInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.response = ''>
		
		<cfif structKeyExists(arguments.rc,"fund_type") and ListFindNoCase("Foundation Fellow - $1,500|Foundation Key Fellow (for existing Foundation Fellows only) - $1,000|New Key Fellow (Foundation Fellow + Key Fellow) - $2,500|Corporate Fellow (for organizations only) - $4,000",arguments.rc.fund_type,'|')>
			<cfif arguments.rc.pmtOption eq 'card'>
				<cfset local.response = 'showPayment'>
			<cfelseif arguments.rc.pmtOption eq 'invoice'>
				<cfset local.response = 'skipPayment'>
			</cfif>
		<cfelse>
			<cfset local.response = 'showPayment'>
		</cfif>
		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset structDelete(session.formFields, "step1")>
		</cfif>
		<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>		

		<cfif local.contributionType eq 'Organization'>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0)>
			<cfset local.company = ''>
			<cfif structKeyExists(arguments.rc,"m_company")>
				<cfset local.company = arguments.rc.m_company>
			</cfif>
			<cfset local.objSaveMember.setDemo(prefix='', firstName='Organization', middleName='', lastName='Account', suffix='', company=local.company)>
			<cfset local.objSaveMember.setRecordType(recordType='Organization')>
			<cfset local.objSaveMember.setMemberType(memberType='User')>
			<cfset local.objSaveMember.setCustomField(field="Member Type - Other", value="Organization")>
			<!--- address/phone --->
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>
			<cfset local.qryOrgPhoneTypes = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.rc.mc_siteinfo.orgid)>
			<cfloop query="local.qryOrgAddressTypes">
				<cfset local.tmpAddressTypeID = local.qryOrgAddressTypes.addressTypeID>
				<cfset local.tmpAddressType = local.qryOrgAddressTypes.addressType>

				<cfset local.strAddrArgs = {}>
				<cfset local.strAddrArgs.type = local.tmpAddressType>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_attn")>
					<cfset local.strAddrArgs.attn = arguments.rc["ma_#local.tmpAddressTypeID#_attn"]>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_address1")>
					<cfset local.strAddrArgs.address1 = left(arguments.rc["ma_#local.tmpAddressTypeID#_address1"],100)>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_address2")>
					<cfset local.strAddrArgs.address2 = left(arguments.rc["ma_#local.tmpAddressTypeID#_address2"],100)>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_address3")>
					<cfset local.strAddrArgs.address3 = left(arguments.rc["ma_#local.tmpAddressTypeID#_address3"],100)>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_city")>
					<cfset local.strAddrArgs.city = left(arguments.rc["ma_#local.tmpAddressTypeID#_city"],75)>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_stateprov")>
					<cfset local.strAddrArgs.stateID = val(arguments.rc["ma_#local.tmpAddressTypeID#_stateprov"])>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_postalCode")>
					<cfset local.strAddrArgs.postalCode = left(arguments.rc["ma_#local.tmpAddressTypeID#_postalCode"],25)>
				</cfif>
				<cfif isDefined("arguments.rc.ma_#local.tmpAddressTypeID#_county")>
					<cfset local.strAddrArgs.county = left(arguments.rc["ma_#local.tmpAddressTypeID#_county"],50)>
				</cfif>
				<cfif structCount(local.strAddrArgs) gt 1>
					<cfset local.objSaveMember.setAddress(argumentcollection=local.strAddrArgs)>
				</cfif>

				<cfloop query="local.qryOrgPhoneTypes">
					<cfif isDefined("arguments.rc.mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#")>
						<cfset local.tmpVal = left(arguments.rc["mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#"],40)>
						<cfif len(local.tmpVal)>
							<cfset local.objSaveMember.setPhone(addresstype=local.tmpAddressType, type=local.qryOrgPhoneTypes.phoneType, value=local.tmpVal)>
						</cfif>
					</cfif>
				</cfloop>
			</cfloop>			
			<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>

			<cfif local.strResult.success>
	 			<cfset variables.useMID = local.strResult.memberID>
	 		</cfif>
		</cfif>	

		<cfset local.arrPayMethods = [ variables.strPageFields.CCPayProfileCode ]>
		<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																					siteID=variables.siteID, 
																					memberID=variables.useMID, 
																					title="Donation Form", 
																					formName=variables.formName, 
																					backStep="processDonationInfo")>

		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_" or ListFindNoCase("fund_type|contributionType|paymentPlan|pmtOption|otherAmt|donorComments",local.thisField,"|")>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Payment for #variables.formNameDisplay#</div>
			<div class="tsAppSectionContentContainer">
				<div class="tsAppBodyText">
					<b>Fund/Campaign :</b>  #arguments.rc["fund_type"]# <br />
					<cfif arguments.rc["paymentPlan"] eq "Other">
						<b>Amount :</b> #arguments.rc["otherAmt"]#
					<cfelse>
						<b>Amount :</b> #arguments.rc["paymentPlan"]#
					</cfif>
				</div>
			</div>
			
			#local.strReturn.paymentHTML#
			
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>

		<!--- ---------------------- --->
		<!--- 	 Member-History 	 --->
		<!--- ---------------------- --->

		<cfset local.fundType = ''>
		<cfset local.totalAmount = 0>
		<cfset local.paymentPlanInYears = 0>
		<cfset local.donorComments = ''>
		<cfset local.paymentOption = ''>
		<cfset local.pmtOptionCategory = ''>
		<cfset local.paymentPlan = ''>
		<cfif structKeyExists(arguments.rc,"fund_type")>
			<cfset local.fundType = arguments.rc.fund_type>
		</cfif>
		<cfif structKeyExists(arguments.rc,"donorComments")>
			<cfset local.donorComments = arguments.rc.donorComments>
		</cfif>
		<cfif structKeyExists(arguments.rc,"pmtOption")>
			<cfset local.paymentOption = arguments.rc.pmtOption>
			<cfif local.paymentOption eq 'card'>
				<cfset local.pmtOptionCategory = 'Auto-Pay'>
			<cfelseif local.paymentOption eq 'invoice'>
				<cfset local.pmtOptionCategory = 'Send Invoice'>
			</cfif>
		</cfif>
		<cfif structKeyExists(arguments.rc,"paymentPlan")>
			<cfset local.paymentPlan = arguments.rc.paymentPlan>
		</cfif>

		<cfif listFindNoCase(local.fundType,"Legal Aid of Southeastern Pennsylvania")>			
			<cfif local.paymentPlan eq 'Other' and structKeyExists(arguments.rc,"otherAmt")>
				<cfset local.totalAmount = val(ReReplace(arguments.rc.otherAmt, "[^\d.]", "","ALL"))>
			<cfelse>
				<cfset local.totalAmount = val(ReReplace(local.paymentPlan, "[^\d.]", "","ALL"))>
			</cfif>
			<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='OnlineDonation', subName='Legal Aid')>
			<cfif local.qryHistory.recordcount>
				<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																	categoryID=local.qryHistory.categoryID, 
																	subCategoryID=local.qryHistory.subCategoryID, 
																	description=local.donorComments, 
																	enteredByMemberID=variables.useMID, 
																	newAccountsOnly=false,
																	dollarAmt=local.totalAmount)>
			</cfif>			
		<cfelseif listFindNoCase(local.fundType,"CCBA Lawyers' Fund")>			
			<cfif local.paymentPlan eq 'Other' and structKeyExists(arguments.rc,"otherAmt")>
				<cfset local.totalAmount = val(ReReplace(arguments.rc.otherAmt, "[^\d.]", "","ALL"))>
			<cfelse>
				<cfset local.totalAmount = val(ReReplace(local.paymentPlan, "[^\d.]", "","ALL"))>
			</cfif>
			<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='OnlineDonation', subName='Lawyers'' Fund')>
			<cfif local.qryHistory.recordcount>
				<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																	categoryID=local.qryHistory.categoryID, 
																	subCategoryID=local.qryHistory.subCategoryID, 
																	description=local.donorComments, 
																	enteredByMemberID=variables.useMID, 
																	newAccountsOnly=false,
																	dollarAmt=local.totalAmount)>
			</cfif>			
		<cfelseif listFindNoCase(local.fundType,"Foundation Fellow - $1,500","|")>			
			<cfif local.paymentPlan eq '$1,500 one-time payment'>
				<cfset local.totalAmount = 1500>
			<cfelseif local.paymentPlan eq '$500 per year for 3 years'>
				<cfset local.totalAmount = 500>
				<cfset local.paymentPlanInYears = 3>
			<cfelseif local.paymentPlan eq '$300 per year for 5 years'>
				<cfset local.totalAmount = 300>
				<cfset local.paymentPlanInYears = 5>
			</cfif>
			<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='FoundationFellow', subName=local.pmtOptionCategory)>
			<cfif local.qryHistory.recordcount>
				<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																	categoryID=local.qryHistory.categoryID, 
																	subCategoryID=local.qryHistory.subCategoryID, 
																	description=local.donorComments, 
																	enteredByMemberID=variables.useMID, 
																	newAccountsOnly=false,
																	qty=local.paymentPlanInYears,
																	dollarAmt=local.totalAmount)>
			</cfif>			
		<cfelseif listFindNoCase(local.fundType,"Foundation Key Fellow (for existing Foundation Fellows only) - $1,000","|")>			
			<cfif local.paymentPlan eq '$1,000 one-time payment'>
				<cfset local.totalAmount = 1000>
			<cfelseif local.paymentPlan eq '$500 per year for 2 years'>
				<cfset local.totalAmount = 500>
				<cfset local.paymentPlanInYears = 2>
			<cfelseif local.paymentPlan eq '$250 per year for 4 years'>
				<cfset local.totalAmount = 250>
				<cfset local.paymentPlanInYears = 4>
			</cfif>
			<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='FoundationKeyFellow', subName=local.pmtOptionCategory)>
			<cfif local.qryHistory.recordcount>
				<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																	categoryID=local.qryHistory.categoryID, 
																	subCategoryID=local.qryHistory.subCategoryID, 
																	description=local.donorComments, 
																	enteredByMemberID=variables.useMID, 
																	newAccountsOnly=false,
																	qty=local.paymentPlanInYears,
																	dollarAmt=local.totalAmount)>
			</cfif>			
		<cfelseif listFindNoCase(local.fundType,"New Key Fellow (Foundation Fellow + Key Fellow) - $2,500","|")>			
			<cfif local.paymentPlan eq '$2,500 one-time payment'>
				<cfset local.totalAmount = 2500>
			<cfelseif local.paymentPlan eq '$500 per year for 5 years'>
				<cfset local.totalAmount = 500>
				<cfset local.paymentPlanInYears = 5>
			</cfif>
			<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='NewKeyFellow', subName=local.pmtOptionCategory)>
			<cfif local.qryHistory.recordcount>
				<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																	categoryID=local.qryHistory.categoryID, 
																	subCategoryID=local.qryHistory.subCategoryID, 
																	description=local.donorComments, 
																	enteredByMemberID=variables.useMID, 
																	newAccountsOnly=false,
																	qty=local.paymentPlanInYears,
																	dollarAmt=local.totalAmount)>
			</cfif>			
		<cfelseif listFindNoCase(local.fundType,"Corporate Fellow (for organizations only) - $4,000","|")>			
			<cfif local.paymentPlan eq '$4,000 one-time payment'>
				<cfset local.totalAmount = 4000>
			<cfelseif local.paymentPlan eq '$2,000 per year for 2 years'>
				<cfset local.totalAmount = 2000>
				<cfset local.paymentPlanInYears = 2>
			<cfelseif local.paymentPlan eq '$1000 per year for 4 years'>
				<cfset local.totalAmount = 1000>
				<cfset local.paymentPlanInYears = 4>
			</cfif>
			<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='CorporateFellow', subName=local.pmtOptionCategory)>
			<cfif local.qryHistory.recordcount>
				<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																	categoryID=local.qryHistory.categoryID, 
																	subCategoryID=local.qryHistory.subCategoryID, 
																	description=local.donorComments, 
																	enteredByMemberID=variables.useMID,
																	newAccountsOnly=false,
																	qty=local.paymentPlanInYears,
																	dollarAmt=local.totalAmount)>
			</cfif>			
		</cfif>
				
		<cfif listFindNoCase("Legal Aid of Southeastern Pennsylvania|CCBA Lawyers' Fund",local.fundType,"|")>
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=variables.strPageFields.CCPayProfileCode)>
		
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmount, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif listFindNoCase(local.fundType,"Legal Aid of Southeastern Pennsylvania")>
				<cfset local.strAccTemp.payment = { detail="Fellows Online Donation", amount=local.strAccTemp.totalPaymentAmount, profileID=local.payProfileID, profileCode=variables.strPageFields.CCPayProfileCode }>
				<cfset local.strAccTemp.revenue  = [{revenueGLAccountCode=3103, detail="Legal Aid Online Donation", amount=local.strAccTemp.totalPaymentAmount}]>
			<cfelseif listFindNoCase(local.fundType,"CCBA Lawyers' Fund")>
				<cfset local.strAccTemp.payment = { detail="Fellows Online Donation", amount=local.strAccTemp.totalPaymentAmount, profileID=local.payProfileID, profileCode=variables.strPageFields.CCPayProfileCode }>
				<cfset local.strAccTemp.revenue  = [{revenueGLAccountCode=3101, detail="Lawyers Fund Online Donation", amount=local.strAccTemp.totalPaymentAmount}]>
			</cfif>

			<cfif val(local.payProfileID) eq 0>
				<cfset structDelete(local.strAccTemp,'payment')>
			</cfif>

			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
		</cfif>
		
		<cfset local.response = 'success'>
		
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.contributionType = ''>
		<cfif structKeyExists(arguments.rc,"contributionType")>
			<cfset local.contributionType = arguments.rc.contributionType>
		</cfif>
		
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='4ef298ee-90d8-4f73-a395-15fd7fb0e39c', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='73c86b78-b534-4af0-b80c-1c52d0ff258e', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.memberPayProfileDetail = "">
		<cfif structKeyExists(arguments.rc,"mccf_payMethID") and structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
			<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
		<cfelse>
			<cfset local.memberPayProfileSelected = 0>
		</cfif>
		<cfif local.memberPayProfileSelected gt 0>
			<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
		</cfif>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.contentConfirmation)>
					<div>#variables.strPageFields.contentConfirmation#</div>
				</cfif>
				
				<!--@@specialcontent@@-->
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation Information</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;width:150px;">
						<b>Fund/Campaign:</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;"><cfif structKeyExists(arguments.rc,"fund_type")>#arguments.rc["fund_type"]#</cfif></td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Type :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.contributionType#</td>
				</tr>
				<tr>
					<cfif arguments.rc["paymentPlan"] eq "Other">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Amount :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"otherAmt")>#arguments.rc["otherAmt"]#</cfif>
						</td>
					<cfelse>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<b>Amount :</b>
						</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"paymentPlan")>#arguments.rc["paymentPlan"]#</cfif>
						</td>
					</cfif>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<b>Donor Comments :</b>
					</td>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"donorComments")>#arguments.rc["donorComments"]#</cfif>
					</td>
				</tr>
				</table>
				<br/>
				
				<cfif local.contributionType eq 'Individual'>
					#local.strFieldSetContent1.fieldSetContent#
				<cfelseif local.contributionType eq 'Organization'>
					#local.strFieldSetContent2.fieldSetContent#
				</cfif>
				
				<cfif local.memberPayProfileDetail neq "">
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation - Payment</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=variables.memberEmail.from},
						emailto=[{ name="", email=variables.memberEmail.to}],
						emailreplyto= variables.ORGEmail.to,
						emailsubject=variables.memberEmail.SUBJECT,
						emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
						emailhtmlcontent=local.confirmationHTML,
						siteID=variables.siteID,
						memberID=val(variables.useMID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=variables.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your donation", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your donation.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>
