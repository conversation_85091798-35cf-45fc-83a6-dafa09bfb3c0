<cfoutput>
		<style type="text/css">
			.ldbChanges{ color:##f00; font-weight:bold; padding: 0 10px 0 0; }
			.ldbSaved{ color:##090; font-weight:bold; padding: 0 10px 0 0; }
			.ldbSaveBtn{font-weight:bold;}
			.needSave{color:##f00; }
			
			.gridHeaderText{font-family: arial, helvetica, sans-serif;font-size: 14px;font-style: normal;font-weight: bold;color: 336699;}
			.gridHeader{ background:##ccc; padding:3px 5px 3px 5px; }
			.gridHdrBorder{ border-top:1px solid ##666; border-bottom:1px solid ##666; border-left:1px solid ##666;  }
			.gridHdrBorderEnd{ border:1px solid ##666;}
			
			.SectionHeaderText{font-family: arial, helvetica, sans-serif;font-size: 14px;font-style: normal;font-weight: bold;color: fff;}
			.gridHdrSection{ background:##aaa; padding:3px 5px 3px 5px; }
			.gridHdrSectionBorder{ border:1px solid ##666; }
			
			.gridHeaderBySection{ background:##ccc; padding:3px 5px 3px 5px; }
			.gridHdrBorderBySection{ border-bottom:1px solid ##666; border-left:1px solid ##666;  }
			.gridHdrBorderEndBySection{ border-bottom:1px solid ##666; border-left:1px solid ##666; border-right:1px solid ##666; }
			.gridRow{ padding:1px 3px 1px 3px;}
			.gridRowBorder{ border-bottom:1px solid ##999; border-left:1px solid ##999;}
			.gridRowBorderEnd{ border-bottom:1px solid ##999; border-left:1px solid ##999; border-right:1px solid ##999;}
			.small{font-size:10px;}
			
			##groupContainer { height:20px; display:block; padding:0 0 0 0;}
			##group { position:relative; display:block; height:15px; font-size:11px; }
			##group ul { margin:0; padding:0; list-style-type:none; width:auto; }
			##group ul li { display:block; float:right; margin:0 4px 0 0; }
			##group ul li a { color:##777; display:block; float:left; background:##ddd; text-decoration:none; padding:3px 6px 0 6px; height:15px; border:1px solid ##333; }
			##group ul li a:hover { background:##ccc; border:1px solid ##333; }
			##group ul li a.current,
			##group ul li a.current:hover { color:##333; background:##ccc; border:1px solid ##333; }
			
			
			##sortContainer { height:15px; display:block; padding:0 0 0 0;}
			##sort { position:relative; display:block; height:12px; font-size:8px; }
			##sort ul { margin:0; padding:0; list-style-type:none; width:auto; }
			##sort ul li { display:block; float:left; margin:0 4px 0 0; }
			##sort ul li a { color:##777; display:block; float:left; background:##ddd; text-decoration:none; padding:1px 4px 0 4px; height:12px; border:1px solid ##333; }
			##sort ul li a:hover { background:##ccc; border:1px solid ##333; }
			##sort ul li a.current,
			##sort ul li a.current:hover { color:##333; background:##ccc; border:1px solid ##333; }
						
			.row1{background: ##ffffff;}
			.row2{background: ##dddddd;}
			
			##date, ##s_datefrom, ##s_dateto { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
		</style>
</cfoutput>

<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="associationEmail", type="STRING", desc="who do we send staff notifications to", value="<EMAIL>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	event.paramValue('daIDs',0);
	event.paramValue('paIDs',0);
	event.paramValue('pExpertIDS',0);
	event.paramValue('dExpertIDS',0);
	event.paramValue('do','list');
	
	vAction = event.getValue('action','search');
	vDo = event.getValue('do','list');
	
	loal.permissions 		= event.getValue('customPage.myRights');
	local.baseLink 			= '/?pg=verdictDatabase';
	local.viewLink			= local.baseLink & '&action=view';
	local.editLink			= local.baseLink & '&action=edit';
	local.receivedLink	= local.baseLink & '&action=received';
	local.deleteLink		= local.baseLink & '&action=delete';
	local.saveLink			= local.baseLink & '&action=save';
	local.resultsLink		= local.baseLink & '&action=results';
	local.attorneysLink = local.baseLink & '&action=attorneys';
	local.expertsLink 	= local.baseLink & '&action=experts';

	local.userEmail					='#session.cfcUser.memberData.email#';//= session.cfcUser.memberData.email;
	local.associationEmail = local.strPageFields.associationEmail;

	// build javascript listener script
	local.ldbFormJS = buildJSListenerScript();	
</cfscript>

	<cfswitch expression="#variables.vAction#">

		<cfcase value="attorneys">
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<cfswitch expression="#event.getValue('do')#">
					<cfcase value="list">
						<cfset local.qryData = getAttorneys() />
						<cfoutput>
						<cfif local.qryData.recordCount>
						<p><span class="HeaderText">Admin - Attorneys</span></p>			
						<input type="button" onclick="document.location.href='#local.attorneysLink#&do=add';" value="Add Attorney" class="bodyText">
						<input type="button" onclick="document.location.href='#local.baseLink#';" value="Search Verdicts" class="bodyText">
						<table class="tsAppBodyText" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td class="gridHeaderText gridHeader gridHdrBorder">Name</td>
								<td class="gridHeaderText gridHeader gridHdrBorder">Phone</td>
								<td class="gridHeaderText gridHeader gridHdrBorder">Website</td>
								<td class="gridHeaderText gridHeader gridHdrBorderEnd">&nbsp;</td>
							</tr>
							<cfloop query="local.qryData">
								<tr class="<cfif local.qryData.currentRow MOD 2>row1<cfelse>row2</cfif>">
									<td class="tsAppBodyText gridRow gridRowBorder">
										#local.qryData.lastName#,	#local.qryData.firstName# <cfif len(trim(local.qryData.middleName))>#local.qryData.middleName#</cfif>	<cfif len(trim(local.qryData.firm))>#local.qryData.firm#</cfif>
									</td>
									<td class="tsAppBodyText gridRow gridRowBorder">
										#local.qryData.phone#
									</td>
									<td class="tsAppBodyText gridRow gridRowBorder">
										#local.qryData.website#
									</td>
									<td class="tsAppBodyText gridRow gridRowBorderEnd">
										<input type="button" onclick="document.location.href='#local.baseLink#&action=#event.getValue('action')#&do=edit&attorneyID=#local.qryData.attorneyID#';" value="edit" class="tsAppBodyText" />
										<cfif NOT checkAttorneyForVerdicts(local.qryData.attorneyID)><input type="button" onclick="document.location.href='#local.baseLink#&action=#event.getValue('action')#&do=delete&attorneyID=#local.qryData.attorneyID#';" value="delete" class="tsAppBodyText" /></cfif>
									</td>
								</tr>
							</cfloop>
						</table>
						<cfelse>
							There are no attorneys in the database<br />
						</cfif>
						</cfoutput>
					</cfcase>
					<cfcase value="edit,add">
						<cfset event.paramValue('attorneyID',0) />
						<cfset local.data = attorneyConstruct(event.getValue('attorneyID')) />
						<cfsavecontent variable="local.ldbFormAuthorsJS">
							<cfoutput>
							<script>
								function _validateForm(){
									var thisForm = document.forms["ldbForm"];
									var arrReq = new Array();
									var lastMtrxErr = '';
									// add other fields that need to be validated here --------------------------------------------------------------
									if (!_CF_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] 				= 'First Name';
									if (!_CF_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] 				= 'Last Name';
									// --------------------------------------------------------------------------------------------------------------
									if (arrReq.length > 0) {
										var msg = 'The following questions are required:\n\n';
										for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
											alert(msg);
											return false;
									}
									return true;
								}
							</script>
							</cfoutput>
						</cfsavecontent>
						<cfhtmlhead text="#local.ldbFormJS#">
						<cfhtmlhead text="#local.ldbFormAuthorsJS#">
						<cfoutput>
						<p><span class="HeaderText">Admin - Attorneys</span></p>
							<table align="right" border="0" width="100%">
								<tr>
									<td align="right">
										<span id="saveArea" style="float:right;" class="tsAppBodyText">
											<span id="saveMsg" class="ldbSaved">&nbsp;
												<!--- write a switch statement to display message --->
												<cfswitch expression="#event.getValue('msg',0)#">
													<cfcase value="1">Your information has been saved!</cfcase>
													<cfcase value="2">The data has been Deleted!</cfcase>
													<cfcase value="3">The file has been Deleted!</cfcase>
												</cfswitch>
											</span> 
										</span>
									</td>
								</tr>
							</table>
							
						<cfform name="ldbForm"  id="ldbForm" action="#local.baseLink#&action=#event.getValue('action')#&do=save" method="post" onsubmit="return _validateForm();">
							<input type="hidden" name="attorneyID" value="#local.data.attorneyID#">
							<table class="tsAppBodyText">
								<tr>
									<td>First Name</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="firstName" maxlength="50" size="30" value="#local.data.firstName#"></td>
								</tr>
								<tr>
									<td>Middle Name</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="middleName" maxlength="50" size="30" value="#local.data.middleName#"></td>
								</tr>
								<tr>
									<td>Last Name</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="lastName" maxlength="50" size="30" value="#local.data.lastName#"></td>
								</tr>
								<tr>
									<td>Firm</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="Firm" maxlength="50" size="30" value="#local.data.Firm#"></td>
								</tr>
								<tr>
									<td>Phone</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="Phone" maxlength="25" size="15" value="#local.data.Phone#"></td>
								</tr>
								<tr>
									<td>Website</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="Website" maxlength="200" size="50" value="#local.data.Website#"></td>
								</tr>
								<tr>
									<td></td>
									<td></td>
									<td><input type="submit" value="Save" name="saveBtn" id="saveBtn" class="tsAppBodyText" /> 
									&nbsp;<input type="button" onclick="document.location.href='#local.attorneysLink#';" value="Cancel" class="tsAppBodyText"></td>
								</tr>
							</table>
						</cfform>
						<script type="text/javascript">
							setListener("change",formHasChanges);
							document.getElementById('saveArea').style.display = '';
						</script>
						</cfoutput>
					</cfcase>
					<cfcase value="save">
						<cfif val(event.getValue('attorneyID',0))>
							<!--- Update --->
							<cfscript>
								local.attorneyID = event.getValue('attorneyID');
								updateAttorney(
									event.getValue('attorneyID'),
									event.getValue('firstName'),
									event.getValue('middleName'),
									event.getValue('lastName'),
									event.getValue('firm'),
									event.getValue('phone'),								
									event.getValue('website')						
								);
							</cfscript>
						<cfelse>
							<!--- insert --->
							<cfscript>
								local.attorneyID = insertAttorney(
									event.getValue('firstName'),
									event.getValue('middleName'),
									event.getValue('lastName'),
									event.getValue('firm'),
									event.getValue('phone'),								
									event.getValue('website')						
								);
							</cfscript>
						</cfif>
						<cflocation addtoken="false" url="#local.baseLink#&action=#event.getValue('action')#&msg=1">
					</cfcase>
					<cfcase value="delete">
						<cfset deleteAttorney(event.getValue('attorneyID')) />
						<cflocation addtoken="false" url="#local.baseLink#&action=#event.getValue('action')#&msg=2">
					</cfcase>
				</cfswitch>
			</cfif>
		</cfcase>
		
		<cfcase value="experts">
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<cfswitch expression="#event.getValue('do')#">
					<cfcase value="list">
						<cfset local.qryData = getExperts() />
						<cfoutput>
						<cfif local.qryData.recordCount>
						<p><span class="HeaderText">Admin - Experts</span></p>			
						<input type="button" onclick="document.location.href='#local.expertsLink#&do=add';" value="Add Expert" class="bodyText">
						<input type="button" onclick="document.location.href='#local.baseLink#';" value="Search Verdicts" class="bodyText">
						<table class="tsAppBodyText" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td class="gridHeaderText gridHeader gridHdrBorder">Name</td>
								<td class="gridHeaderText gridHeader gridHdrBorderEnd">&nbsp;</td>
							</tr>
							<cfloop query="local.qryData">
								<tr class="<cfif local.qryData.currentRow MOD 2>row1<cfelse>row2</cfif>">
									<td class="tsAppBodyText gridRow gridRowBorder">
										#local.qryData.lastName#,	#local.qryData.firstName# <cfif len(trim(local.qryData.middleInitial))>#local.qryData.middleInitial#</cfif>	<cfif len(trim(local.qryData.suffix))>#local.qryData.suffix#</cfif>
									</td>
									<td class="tsAppBodyText gridRow gridRowBorderEnd">
										<input type="button" onclick="document.location.href='#local.baseLink#&action=#event.getValue('action')#&do=edit&expertID=#local.qryData.expertID#';" value="edit" class="tsAppBodyText" />
										<cfif NOT checkExpertForVerdicts(local.qryData.expertID)><input type="button" onclick="document.location.href='#local.baseLink#&action=#event.getValue('action')#&do=delete&expertID=#local.qryData.expertID#';" value="delete" class="tsAppBodyText" /></cfif>
									</td>
								</tr>
							</cfloop>
						</table>
						<cfelse>
							There are no experts in the database<br />
						</cfif>
						</cfoutput>
					</cfcase>
					<cfcase value="edit,add">
						<cfset event.paramValue('expertID',0) />
						<cfset local.data = expertConstruct(event.getValue('expertID')) />
						<cfsavecontent variable="local.ldbFormAuthorsJS">
							<cfoutput>
							<script>
								function _validateForm(){
									var thisForm = document.forms["ldbForm"];
									var arrReq = new Array();
									var lastMtrxErr = '';
									// add other fields that need to be validated here --------------------------------------------------------------
									if (!_CF_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] 				= 'First Name';
									if (!_CF_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] 				= 'Last Name';
									// --------------------------------------------------------------------------------------------------------------
									if (arrReq.length > 0) {
										var msg = 'The following questions are required:\n\n';
										for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
											alert(msg);
											return false;
									}
									return true;
								}
							</script>
							</cfoutput>
						</cfsavecontent>
						<cfhtmlhead text="#local.ldbFormJS#">
						<cfhtmlhead text="#local.ldbFormAuthorsJS#">
						<cfoutput>
						<p><span class="HeaderText">Admin - Experts</span></p>
							<table align="right" border="0" width="100%">
								<tr>
									<td align="right">
										<span id="saveArea" style="float:right;" class="tsAppBodyText">
											<span id="saveMsg" class="ldbSaved">&nbsp;
												<!--- write a switch statement to display message --->
												<cfswitch expression="#event.getValue('msg',0)#">
													<cfcase value="1">Your information has been saved!</cfcase>
													<cfcase value="2">The data has been Deleted!</cfcase>
													<cfcase value="3">The file has been Deleted!</cfcase>
												</cfswitch>
											</span> 
										</span>
									</td>
								</tr>
							</table>
							
						<cfform name="ldbForm"  id="ldbForm" action="#local.baseLink#&action=#event.getValue('action')#&do=save" method="post" onsubmit="return _validateForm();">
							<input type="hidden" name="expertID" value="#local.data.expertID#">
							<table class="tsAppBodyText">
								<tr>
									<td>First Name</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="firstName" maxlength="50" size="30" value="#local.data.firstName#"></td>
								</tr>
								<tr>
									<td>Middle Initial</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="middleInitial" maxlength="50" size="30" value="#local.data.middleInitial#"></td>
								</tr>
								<tr>
									<td>Last Name</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="lastName" maxlength="50" size="30" value="#local.data.lastName#"></td>
								</tr>
								<tr>
									<td>Suffix</td>
									<td></td>
									<td><input class="tsAppBodyText" type="text" name="Suffix" maxlength="50" size="30" value="#local.data.Suffix#"></td>
								</tr>
								<tr>
									<td></td>
									<td></td>
									<td><input type="submit" value="Save" name="saveBtn" id="saveBtn" class="tsAppBodyText" /> 
									&nbsp;<input type="button" onclick="document.location.href='#local.expertsLink#';" value="Cancel" class="tsAppBodyText"></td>
								</tr>
							</table>
						</cfform>
						<script type="text/javascript">
							setListener("change",formHasChanges);
							document.getElementById('saveArea').style.display = '';
						</script>
						</cfoutput>
					</cfcase>
					<cfcase value="save">
						<cfif val(event.getValue('expertID',0))>
							<!--- Update --->
							<cfscript>
								local.expertID = event.getValue('expertID');
								updateExpert(
									event.getValue('expertID'),
									event.getValue('firstName'),
									event.getValue('middleInitial'),
									event.getValue('lastName'),
									event.getValue('suffix')						
								);
							</cfscript>
						<cfelse>
							<!--- insert --->
							<cfscript>
								local.expertID = insertExpert(
									event.getValue('firstName'),
									event.getValue('middleInitial'),
									event.getValue('lastName'),
									event.getValue('suffix')						
								);
							</cfscript>
						</cfif>
						<cflocation addtoken="false" url="#local.baseLink#&action=#event.getValue('action')#&msg=1">
					</cfcase>
					<cfcase value="delete">
						<cfset deleteExpert(event.getValue('expertID')) />
						<cflocation addtoken="false" url="#local.baseLink#&action=#event.getValue('action')#&msg=2">
					</cfcase>
				</cfswitch>
			</cfif>
		</cfcase>

		<cfcase value="view">
			<cfset verdictData = buildVerdictSheet(int(val(event.getValue('verdictID',0)))) />
			
			<cfif len(verdictData.verdictID) eq 0>
				<cflocation url="#local.baseLink#" addtoken="No">
			</cfif>
			
			<cfoutput>
			<p class="HeaderText">Viewing Information in OH's Verdict and Settlement Exchange Database</p>
			<div style="margin-bottom:15px;">
				<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
					<form action="#local.resultsLink#" method="post">
					<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
						<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
					</CFLOOP>
					<input type="submit" value="Back to Listing" class="bodyText"/>
					</form>
				</cfif>
			</div>
			
			#verdictData.verdictView#
			</cfoutput>
			
				<!--- OUTPUT HERE --->
			
		</cfcase>

		<cfcase value="edit">
			<cfset verdictValues = getVerdict(int(val(event.getValue('verdictID',0))))>
			<cfset allowForm = false>
			
			<cfoutput>
			
				<cfif val(event.getValue('verdictID',0))>
					<!--- EDIT Verdict: --->
					<p class="HeaderText">Edit Information in OH's Verdict and Settlement Exchange Database</p>
					<p class="bodyText">Complete the form below to edit this record.</p>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfset allowForm = true>
					<cfelse>
						<p class="bodyText">You do not have permission to edit this information.</p>
					</cfif>
				<cfelse>
					<!--- ADD Verdict: --->
					<p class="HeaderText">Add to OH's Verdict and Settlement Exchange Database</p>
					<p class="bodyText">Complete the form below to add a verdict or settlement to the database.</p>
					
					<cfif val(event.getValue('customPage.myRights.view',0))>
						<cfset allowForm = true>
					<cfelse>
						<p class="bodyText">You do not have permission to add to this database.</p>
					</cfif>
					
				</cfif>
			</cfoutput>
			
			<cfif allowForm>
				<cfoutput>
				<cfset local.qryActionTypes 			= getActionTypes()>
				<cfset local.qryResolutionTypes 			= getResolutionTypes()>
				<cfset local.qryCountyNames 		= getCountyNames()>
				<cfset local.qryCourtNames 			= getCourtNames()>
				<cfset local.qryJudgeNames 			= getJudgeNames()>
				<cfset local.qryExpertNames 			= getExpertNames()>
				<cfset local.qryInsuranceNames 			= getInsuranceNames()>

				<cfset local.paSelect 		= buildPAForVerdict(verdictValues.paIDs) />
				<cfset local.paNew				= buildPAForVerdict(verdictValues.paIDs,1) />

				<cfset local.daSelect 		= buildDAForVerdict(verdictValues.daIDs) />
				<cfset local.daNew				= buildDAForVerdict(verdictValues.daIDs,1) />

				<cfset local.pExpertSelect 		= buildPExpertForVerdict(verdictValues.pExpertIDs) />
				<cfset local.pExpertNew				= buildPExpertForVerdict(verdictValues.pExpertIDs,1) />

				<cfset local.dExpertSelect 		= buildDExpertForVerdict(verdictValues.dExpertIDs) />
				<cfset local.dExpertNew				= buildDExpertForVerdict(verdictValues.dExpertIDs,1) />
				
				<cfif len(verdictValues.judgementAmount) eq 0>
					<cfset verdictValues.judgementAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.offerAmount) eq 0>
					<cfset verdictValues.offerAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.demandAmount) eq 0>
					<cfset verdictValues.demandAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.settlementAmount) eq 0>
					<cfset verdictValues.settlementAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.medicalPastAmount) eq 0>
					<cfset verdictValues.medicalPastAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.medicalFutureAmount) eq 0>
					<cfset verdictValues.medicalFutureAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.lostWagesAmount) eq 0>
					<cfset verdictValues.lostWagesAmount = "0.00">
				</cfif>
				<cfif len(verdictValues.diminishedEarningAmount) eq 0>
					<cfset verdictValues.diminishedEarningAmount = "0.00">
				</cfif>

				
								
				
				<cfsavecontent variable="JS">
					<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
					<!--
					
					
					function togglePA() {
						var newPA 	= document.getElementById('newPA');
						var paObj 	= document.getElementById('objPA');
						var paLink = document.getElementById('paLink');
						if (newPA.value == 0) {													
							paObj.innerHTML = '#jsStringFormat(local.paNew)#';	
							paLink.innerHTML = 'cancel';	
							newPA.value = 1;
						}
						else {													
							paObj.innerHTML = '#jsStringFormat(local.paSelect)#';
							paLink.innerHTML = 'add new';
							newPA.value = 0;
						}
					}					

					function toggleDA() {
						var newDA 	= document.getElementById('newDA');
						var daObj 	= document.getElementById('objDA');
						var daLink = document.getElementById('daLink');
						if (newDA.value == 0) {													
							daObj.innerHTML = '#jsStringFormat(local.daNew)#';	
							daLink.innerHTML = 'cancel';	
							newDA.value = 1;
						}
						else {													
							daObj.innerHTML = '#jsStringFormat(local.daSelect)#';
							daLink.innerHTML = 'add new';
							newDA.value = 0;
						}
					}					

					function togglePlaintiffExpert() {
						var newPExpert 	= document.getElementById('newPExpert');
						var pExpertObj 	= document.getElementById('objPExpert');
						var pExpertLink = document.getElementById('pExpertLink');
						if (newPExpert.value == 0) {													
							pExpertObj.innerHTML = '#jsStringFormat(local.pExpertNew)#';	
							pExpertLink.innerHTML = 'cancel';	
							newPExpert.value = 1;
						}
						else {													
							pExpertObj.innerHTML = '#jsStringFormat(local.pExpertSelect)#';
							pExpertLink.innerHTML = 'add new';
							newPExpert.value = 0;
						}
					}					
					
					function toggleDefenseExpert() {
						var newDExpert 	= document.getElementById('newDExpert');
						var dExpertObj 	= document.getElementById('objDExpert');
						var dExpertLink = document.getElementById('dExpertLink');
						if (newDExpert.value == 0) {													
							dExpertObj.innerHTML = '#jsStringFormat(local.dExpertNew)#';	
							dExpertLink.innerHTML = 'cancel';	
							newDExpert.value = 1;
						}
						else {													
							dExpertObj.innerHTML = '#jsStringFormat(local.dExpertSelect)#';
							dExpertLink.innerHTML = 'add new';
							newDExpert.value = 0;
						}
					}					
					
					function changeAvailability(formfieldid,disableflag) {
						var ff = document.getElementById(formfieldid);
						ff.disabled = disableflag;
						if (disableflag) ff.value='disabled';
						else ff.value='';
					}
					
					function updateCourtList(defaultSelected)
					{
						var theForm = document.forms["verdictForm"];

						var theCountyID = theForm.countyID.value;

						theForm.courtID.options.length = 0;
						theForm.courtID.options[theForm.courtID.options.length] = new Option('Select from list', '0', true, false);

						switch (theCountyID)
						{
				<cfset currCountyIDLoop = 0>
				<cfloop query="local.qryCourtNames">	
					<cfif currCountyIDLoop neq local.qryCourtNames.countyID>
						<cfif currCountyIDLoop neq 0>
							break;
						</cfif>
							case "#local.qryCourtNames.countyID#":
						<cfset currCountyIDLoop = local.qryCourtNames.countyID>
					</cfif>
								theForm.courtID.options[theForm.courtID.options.length] = new Option("#local.qryCourtNames.courtName#", "#local.qryCourtNames.courtID#", false, false);
				</cfloop>
						}
						if (theForm.courtID.options.length == 1)						
						{
							theForm.courtID.options[theForm.courtID.options.length] = new Option('Select a county to get the list of courts', '0', false, false);
						}
						
						if (defaultSelected != null)
						{
							for(i=0; i < theForm.courtID.options.length; i++)
							{
								if (theForm.courtID.options[i].value == defaultSelected)
								{
									theForm.courtID.selectedIndex = i;
									break;
								}
							}
						}
					}
					
					function chkVerdictForm()
					{
						var theForm = document.forms["verdictForm"];
						var errMsg = '';
						
						var theJudgment = parseFloat(theForm.judgementAmount.value);
						var thOffer = parseFloat(theForm.offerAmount.value);
						var theDemand = parseFloat(theForm.demandAmount.value);
						var theSettlement = parseFloat(theForm.settlementAmount.value);
						
						if (trim(theForm.date.value) == '')
						{
							errMsg += 'Please enter a Date of Resolution.\r\n';
						}
						if (trim(theForm.resolutiontypeID.value) == '')
						{
							errMsg += 'Please select a Resolution.\r\n';
						}
						if ((trim(theForm.actionTypeID.value) == '') && (trim(theForm.actiontypeNew.value) == ''))
						{
							errMsg += 'Please select a Type of Action.\r\n';
						}
						if (trim(theForm.casetitle.value) == '')
						{
							errMsg += 'Please enter a Case Caption.\r\n';
						}
						if (trim(theForm.injuries.value) == '')
						{
							errMsg += 'Please enter details for Injuries.\r\n';
						}
						
						if ((!isNaN(theJudgment) && !isNaN(thOffer) && !isNaN(theDemand) && !isNaN(theSettlement)) &&
							((theJudgment + thOffer + theDemand + theSettlement) <= 0))
						{
							errMsg += 'Judgment, Offer, Demand or Settlement must be above $0.00.\r\n';
						}
						
						
						if (trim(theForm.countyID.value) == '')
						{
							errMsg += 'Please select a County.\r\n';
						}
						/*
						if (trim(theForm.courtID.value) == '0')
						{
							errMsg += 'Please select a Courthouse.\r\n';
						}
						*/
						
						/*
						if (trim(theForm.PlaintiffAttorney.value) == '')
						{
							errMsg += 'Please enter the Plaintiff Attorney Name.\r\n';
						}
						if (trim(theForm.PlaintiffAttorneyFirm.value) == '')
						{
							errMsg += 'Please enter the Plaintiff Attorney Firm.\r\n';
						}
						*/
						
						/*
						if (((trim(theForm.expertFirstNameNew.value) != '') ||
							(trim(theForm.expertMINew.value) != '') ||
							(trim(theForm.expertLastNameNew.value) != '') ||
							(trim(theForm.expertSuffixNew.value) != '')) &&
							((trim(theForm.expertFirstNameNew.value) == '') || (trim(theForm.expertLastNameNew.value) == '')))
						{
							errMsg += 'Please enter at least a First and Last Name for the Expert.\r\n';
						}
						*/
						if (((trim(theForm.judgeFirstNameNew.value) != '') ||
							(trim(theForm.judgeMINew.value) != '') ||
							(trim(theForm.judgeLastNameNew.value) != '') ||
							(trim(theForm.judgeSuffixNew.value) != '')) &&
							((trim(theForm.judgeFirstNameNew.value) == '') || (trim(theForm.judgeLastNameNew.value) == '')))
						{
							errMsg += 'Please enter at least a First and Last Name for the Judge.\r\n';
						}

						if (trim(errMsg).length > 0)	
						{
							alert(errMsg);
							return false;
						}

						return true;
					} 			
	
					$(document).ready(function(){
						mca_setupDatePickerField('date');
					});						
					//-->
					</SCRIPT>
					<script language='javascript' type='text/javascript' src='/assets/common/javascript/common.js'></script>
				</cfsavecontent>
				<cfhtmlhead text="#JS#">
					<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
						<form action="#local.resultsLink#" method="post" name="backForm">
						<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
							<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
						</CFLOOP>
						</form>
					</cfif>
					<cfform name="verdictForm"  id="verdictForm" action="#local.saveLink#" method="post" onsubmit="return chkVerdictForm();">
						<cfinput type="hidden" name="verdictid"  id="verdictid" value="#val(verdictValues.verdictid)#">
						<div>
							<input type="submit" value="Save Verdict" name="btnSave" class="bodyText" /> &nbsp;
							<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND val(verdictValues.verdictID) gt 0>
								<input type="button" name="btnDelete" value="Delete Verdict" class="bodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(verdictValues.verdictID)#';"/> &nbsp;
							</cfif>
							<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
								<input type="button" value="Cancel" class="bodyText" onClick="document.forms['backForm'].submit();" />
							</cfif>
						</div>
						<br/>
						
						
						<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
							<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
								<tr valign="top">
									<td nowrap><strong>Approved Status:</strong></td>
									<td>
										<cfinput required="yes" message="Select an approval status." type="radio" value="1" name="isApproved"  id="isApproved" checked="#val(verdictValues.isApproved) is 1#"> Approved - available for viewing<br/>
										<cfinput type="radio" value="0" name="isApproved"  id="isApproved" checked="#val(verdictValues.isApproved) is 0#"> Not Approved - not available for viewing<br/>
									</td>
								</tr>
							<cfelse>
								<cfinput type="hidden" name="isApproved"  id="isApproved" value="#val(verdictValues.isApproved)#">
							</cfif>
							<tr valign="top">
								<td nowrap><strong>Date of Resolution:</strong></td>
								<td>
									<cfinput type="text" name="date" id="date" value="#dateFormat(verdictValues.date,'mm/dd/yyyy')#">
									<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Case Caption:</strong></td>
								<td><cfinput class="bodyText" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70" value="#verdictValues.casetitle#"></td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Resolution:</strong></td>
								<td>
									<select name="resolutiontypeID" class="bodyText">
									<option value="">Select from list</option>
									<cfloop query="local.qryresolutiontypes">
										<option value="#local.qryresolutiontypes.resolutionID#" <cfif verdictValues.resolutionTypeID eq local.qryresolutiontypes.resolutionID>selected</cfif>>#local.qryresolutiontypes.resolutionType#</option>
									</cfloop>
									</select>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Type of Action:</strong></td>
								<td>
									<select name="actionTypeID" class="bodyText" onchange="changeAvailability('actiontypeNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qryActionTypes">
										<option value="#local.qryActionTypes.actionID#" <cfif verdictValues.actionTypeID eq local.qryActionTypes.actionID>selected</cfif>>#local.qryActionTypes.actiontype#</option>
									</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="actiontypeNew" id="actiontypeNew" maxlength="50" size="30">
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Injuries:</strong></td>
								<td valign="top"><textarea class="bodyText" name="injuries" cols="60" rows="10">#verdictValues.injuries#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Case Summary:</strong></td>
								<td valign="top"><textarea class="bodyText" name="caseSummary" cols="60" rows="10">#verdictValues.caseSummary#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Judgment:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="judgementAmount"  id="judgementAmount" validate="float" message="Please enter a numerical amount in the 'Judgment' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.judgementAmount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Offer:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="offerAmount"  id="offerAmount" validate="float" message="Please enter a numerical amount in the 'Offer' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.offerAmount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Demand:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="demandAmount"  id="demandAmount" validate="float" message="Please enter a numerical amount in the 'Demand' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.demandAmount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Settlement:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="settlementAmount"  id="settlementAmount" validate="float" message="Please enter a numerical amount in the 'Settlement' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.settlementAmount#"></td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>County:</strong></td>
								<td>
									<select name="countyID" class="bodyText" onChange="updateCourtList();">
									<option value="">Select from list</option>
									<cfloop query="local.qrycountynames">
										<option value="#local.qrycountynames.countyID#" <cfif verdictValues.countyID eq local.qrycountynames.countyID>selected</cfif>>#trim(local.qrycountynames.countyName)#</option>
									</cfloop>
									</select>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Courthouse:</strong></td>
								<td>
									<select class="bodyText" name="courtID" >
									</select>
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Plaintiff Attorney: <span class="small">[<a class="r" id="paLink" href="javascript:togglePA();">add new</a>]</span></strong></td>
								<td valign="top">
									<input type="hidden" name="newPA" id="newPA" value="0">
									<span id="objPA">#local.paSelect#</span>
								</td>
							</tr>
							<tr>
								<td valign="top" ><strong>Defense Attorney: <span class="small">[<a class="r" id="daLink" href="javascript:toggleDA();">add new</a>]</span></strong></strong></td>
								<td valign="top">
									<input type="hidden" name="newDA" id="newDA" value="0">
									<span id="objDA">#local.daSelect#</span>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Insurance Company:</strong></td>
								<td>
									<select name="insuranceCoID" class="bodyText" onchange="changeAvailability('insuranceCoNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qryInsuranceNames">
										<option value="#local.qryInsuranceNames.insuranceCoID#" <cfif verdictValues.insuranceCoID eq local.qryInsuranceNames.insuranceCoID>selected</cfif>>#local.qryInsuranceNames.companyName#</option>
									</cfloop>
									</select>
									<cfinput class="bodyText" type="text" name="insuranceCoNew" id="insuranceCoNew" maxlength="50" size="30"><br />
									<cfinput type="radio" value="1" name="isPlaintiffInsurance"  id="isPlaintiffInsurance" checked="#val(verdictValues.isPlaintiffInsurance) is 1#"> Plaintiff's Insurance Company<br/>
									<cfinput type="radio" value="0" name="isPlaintiffInsurance"  id="isPlaintiffInsurance" checked="#val(verdictValues.isPlaintiffInsurance) is 0#"> Defendant's Insurance Company<br/>
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Age of Plaintiff:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="age"  id="age" validate="integer" maxlength="2" size="5" value="#verdictValues.age#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Medical Specials (past):</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="medicalPastAmount"  id="medicalPastAmount" validate="float" message="Please enter a numerical amount in the 'Medical Specials (past)' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.medicalPastAmount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Medical Specials (future):</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="medicalFutureAmount"  id="medicalFutureAmount" validate="float" message="Please enter a numerical amount in the 'Medical Specials (future)' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.medicalFutureAmount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Wage Loss:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="lostWagesAmount"  id="lostWagesAmount" validate="float" message="Please enter a numerical amount in the 'Wage Loss' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.lostWagesAmount#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Diminished Earning Capacity:</strong></td>
								<td valign="top">$<cfinput class="bodyText" type="text" name="diminishedEarningAmount"  id="diminishedEarningAmount" validate="float" message="Please enter a numerical amount in the 'Diminished Earning Capacity' field. Only numerals, commas, and periods allowed." size="10" value="#verdictValues.diminishedEarningAmount#"></td>
							</tr>

							<tr>
								<td valign="top"><strong>Plaintiff Expert: <span class="small">[<a class="r" id="pExpertLink" href="javascript:togglePlaintiffExpert();">add new</a>]</span></strong></td>
								<td valign="top">
									<input type="hidden" name="newPExpert" id="newPExpert" value="0">
									<span id="objPExpert">#local.pExpertSelect#</span>
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Defense Expert: <span class="small">[<a class="r" id="dExpertLink" href="javascript:toggleDefenseExpert();">add new</a>]</span></strong></td>
								<td valign="top">
									<input type="hidden" name="newDExpert" id="newDExpert" value="0">
									<span id="objDExpert">#local.dExpertSelect#</span>
								</td>
							</tr>

							<tr valign="top">
								<td nowrap colspan="2"><strong>Judge:</strong></td>
							</tr>
							<tr>
								<td valign="top" colspan="2">
									<table width="100%">
										<tr>
											<td width="30">&nbsp;</td>
											<td valign="top" colspan="2">
												<select name="judgeID" class="bodyText" onchange="changeAvailability('judgeFirstNameNew',this.value.length);changeAvailability('judgeMINew',this.value.length);changeAvailability('judgeLastNameNew',this.value.length);changeAvailability('judgeSuffixNew',this.value.length);">
												<option value="">Add new entry or select from list</option>
												<cfloop query="local.qryJudgeNames">
													<option value="#local.qryJudgeNames.judgeID#" <cfif verdictValues.judgeID eq local.qryJudgeNames.judgeID>selected</cfif>>#local.qryJudgeNames.judgeName#</option>
												</cfloop>
												</select>
											</td>
										</tr>
										<tr>
											<td>&nbsp;</td>
											<td valign="top"><strong>First Name:</strong></td>
											<td valign="top"><cfinput class="bodyText" type="text" name="judgeFirstNameNew" id="judgeFirstNameNew" maxlength="50" size="30"></td>
										</tr>
										<tr>
											<td>&nbsp;</td>
											<td valign="top"><strong>Middle Initial:</strong></td>
											<td valign="top"><cfinput class="bodyText" type="text" name="judgeMINew" id="judgeMINew" maxlength="50" size="30"></td>
										</tr>
										<tr>
											<td>&nbsp;</td>
											<td valign="top"><strong>Last Name:</strong></td>
											<td valign="top"><cfinput class="bodyText" type="text" name="judgeLastNameNew" id="judgeLastNameNew" maxlength="50" size="30"></td>
										</tr>
										<tr>
											<td>&nbsp;</td>
											<td valign="top"><strong>Suffix:</strong></td>
											<td valign="top"><cfinput class="bodyText" type="text" name="judgeSuffixNew" id="judgeSuffixNew" maxlength="50" size="30"></td>
										</tr>
									</table>
								</td>
							</tr>
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
							<tr>
								<td nowrap><strong>Submitted By:</strong></td>
								<td valign="top"><cfinput class="bodyText" type="text" name="SubmittingAttorney" id="SubmittingAttorney" maxlength="200" size="70" value="#verdictValues.SubmittingAttorney#"></td>
							</tr>
							</cfif>
						</table>
						<br />
						<div>
							<input type="submit" value="Save Verdict" name="btnSave2" class="bodyText" /> &nbsp;
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) AND val(verdictValues.verdictID) gt 0>
								<input type="button" name="btnDelete2" value="Delete Verdict" class="bodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(verdictValues.verdictID)#';"/> &nbsp;
							</cfif>
							<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
								<input type="button" value="Cancel" class="bodyText" onClick="document.forms['backForm'].submit();" />
							</cfif>
						</div>
						
					</cfform>
					<script language="javascript">
						updateCourtList("#verdictValues.courtID#");
						changeAvailability('actiontypeNew',document.forms["verdictForm"].actionTypeID.value.length);
						changeAvailability('insuranceCoNew',document.forms["verdictForm"].insuranceCoID.value.length);
						changeAvailability('judgeFirstNameNew',document.forms["verdictForm"].judgeID.value.length);changeAvailability('judgeMINew',document.forms["verdictForm"].judgeID.value.length);changeAvailability('judgeLastNameNew',document.forms["verdictForm"].judgeID.value.length);changeAvailability('judgeSuffixNew',document.forms["verdictForm"].judgeID.value.length);
					</script>
				</cfoutput>
			</cfif>
		</cfcase>

		<cfcase value="delete">
			<cfset verdictID = int(val(event.getValue('verdictID',0)))>
			<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
				<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
					delete from OH_VS_VerdictsExperts
					where verdictID = <cfqueryparam value="#verdictID#" cfsqltype="CF_SQL_INTEGER">

					delete from OH_VS_VerdictsAttorneys
					where verdictID = <cfqueryparam value="#verdictID#" cfsqltype="CF_SQL_INTEGER">

					delete from OH_VS_VerdictsInsuranceCos
					where verdictID = <cfqueryparam value="#verdictID#" cfsqltype="CF_SQL_INTEGER">

					delete from OH_VS_verdicts
					where verdictID = <cfqueryparam value="#verdictID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
			</cfif>
			<cflocation url="#local.receivedLink#&msg=3">
		</cfcase>

		<cfcase value="save">
			<cfset verdictID = int(val(event.getValue('verdictID',0)))>
			
			<cfif len(event.getValue('actiontypeNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckAction">
					set nocount on
					declare @actionID int
					
					select @actionID = actionID 
					from dbo.OH_VS_ActionTypes
					where actionType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('actiontypeNew'))#">
					
					if @actionID is NULL
					begin
						INSERT INTO dbo.OH_VS_ActionTypes(actionType)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('actiontypeNew'))#">)
						select @actionID = SCOPE_IDENTITY();
					end
				
					select @actionID as actionID
				</cfquery>
				<cfset actionTypeID = qryCheckAction.actionID>
			<cfelse>	
				<cfset actionTypeID = event.getValue('actionTypeID')>
			</cfif>
			
			<cfif len(event.getValue('insuranceCoNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckInsurance">
					set nocount on
					declare @insuranceCoID int
					select @insuranceCoID = insuranceCoID 
					from dbo.OH_VS_InsuranceCos
					where CompanyName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('insuranceCoNew'))#">
					if @insuranceCoID is NULL
					begin
						INSERT INTO dbo.OH_VS_InsuranceCos(companyName)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('insuranceCoNew'))#">)
						select @insuranceCoID = SCOPE_IDENTITY();
					end
					select @insuranceCoID as insuranceCoID
					set nocount off
				</cfquery>
				<cfset insuranceCoID = qryCheckInsurance.insuranceCoID>
			<cfelse>	
				<cfset insuranceCoID = event.getValue('insuranceCoID')>
			</cfif>
			
			<cfif len(event.getValue('judgeFirstNameNew','')) AND len(event.getValue('judgeLastNameNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckJudge">
					set nocount on
					declare @judgeID int
					select @judgeID = judgeID 
					from dbo.OH_VS_Judges
					where FirstName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeFirstNameNew'))#">
					AND LastName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeLastNameNew'))#">
					<cfif len(trim(event.getValue('judgeMINew','')))>
						AND ((MiddleInitial is null) OR (MiddleInitial = ''))
					<cfelse>
						AND MiddleInitial = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeMINew'))#">
					</cfif>
					<cfif len(trim(event.getValue('judgeSuffixNew','')))>
						AND ((Suffix is null) OR (Suffix = ''))
					<cfelse>
						AND Suffix = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeSuffixNew'))#">
					</cfif>
					if @judgeID is NULL
					begin
						INSERT INTO dbo.OH_VS_Judges(FirstName, MiddleInitial, LastName, Suffix)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeFirstNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeMINew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeLastNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeSuffixNew'))#">)
						select @judgeID = SCOPE_IDENTITY();
					end
					select @judgeID as judgeID
					set nocount off
				</cfquery>
				<cfset judgeID = qryCheckJudge.judgeID>
			<cfelse>	
				<cfset judgeID = event.getValue('judgeID')>
			</cfif>

			<cfif len(event.getValue('pExpertFirstNameNew','')) AND len(event.getValue('pExpertLastNameNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckPExpert">
					set nocount on
					declare @expertID int
					select @expertID = expertID 
					from dbo.OH_VS_Experts
					where FirstName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertFirstNameNew'))#">
					AND LastName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertLastNameNew'))#">
					<cfif len(trim(event.getValue('pExpertMINew','')))>
						AND ((MiddleInitial is null) OR (MiddleInitial = ''))
					<cfelse>
						AND MiddleInitial = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertMINew'))#">
					</cfif>
					<cfif len(trim(event.getValue('pExpertSuffixNew','')))>
						AND ((Suffix is null) OR (Suffix = ''))
					<cfelse>
						AND Suffix = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertSuffixNew'))#">
					</cfif>
					if @expertID is NULL
					begin
						INSERT INTO dbo.OH_VS_Experts(FirstName, MiddleInitial, LastName, Suffix)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertFirstNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertMINew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertLastNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('pExpertSuffixNew'))#">)
						select @expertID = SCOPE_IDENTITY();
					end
					select @expertID as expertID
					set nocount off
				</cfquery>
				<cfset pExpertAdded = true>
				<cfset pExpertID = qryCheckPExpert.expertID>
			<cfelse>	
				<cfset pExpertAdded = false>
				<cfset pExpertID = event.getValue('pExpertIDs','')>
			</cfif>
			
			<cfif len(event.getValue('dExpertFirstNameNew','')) AND len(event.getValue('dExpertLastNameNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckDExpert">
					set nocount on
					declare @expertID int
					
					select @expertID = expertID 
					from dbo.OH_VS_Experts
					where FirstName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertFirstNameNew'))#">
					AND LastName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertLastNameNew'))#">
					<cfif len(trim(event.getValue('dExpertMINew','')))>
						AND ((MiddleInitial is null) OR (MiddleInitial = ''))
					<cfelse>
						AND MiddleInitial = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertMINew'))#">
					</cfif>
					<cfif len(trim(event.getValue('dExpertSuffixNew','')))>
						AND ((Suffix is null) OR (Suffix = ''))
					<cfelse>
						AND Suffix = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertSuffixNew'))#">
					</cfif>
					if @expertID is NULL
					begin
						INSERT INTO dbo.OH_VS_Experts(FirstName, MiddleInitial, LastName, Suffix)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertFirstNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertMINew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertLastNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('dExpertSuffixNew'))#">)
						select @expertID = SCOPE_IDENTITY();
					end
					select @expertID as expertID
					set nocount off
				</cfquery>
				<cfset dExpertAdded = true>
				<cfset dExpertID = qryCheckDExpert.expertID>
			<cfelse>	
				<cfset dExpertAdded = false>
				<cfset dExpertID = event.getValue('dExpertIDs','')>
			</cfif>
			<cfif len(event.getValue('paFirstName','')) AND len(event.getValue('paLastName',''))  AND len(event.getValue('paFirm',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckAttorney">
					set nocount on
					declare @attorneyID int
					
					select @attorneyID = attorneyID 
					from dbo.OH_VS_Attorneys
					where FirstName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paFirstName'))#">
					AND LastName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paLastName'))#">
					AND Firm = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paFirm'))#">
					<cfif len(trim(event.getValue('paMiddleName','')))>
						AND ((MiddleName is null) OR (MiddleName = ''))
					<cfelse>
						AND MiddleName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paMiddleName'))#">
					</cfif>
					<cfif len(trim(event.getValue('paPhone','')))>
						AND ((Phone is null) OR (Phone = ''))
					<cfelse>
						AND Phone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paPhone'))#">
					</cfif>
					<cfif len(trim(event.getValue('paWebsite','')))>
						AND ((Website is null) OR (Website = ''))
					<cfelse>
						AND Website = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paWebsite'))#">
					</cfif>
					if @attorneyID is NULL
					begin
						INSERT INTO dbo.OH_VS_Attorneys(FirstName, MiddleName, LastName, Firm, Phone, Website)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paFirstName'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paMiddleName'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paLastName'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paFirm'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paPhone'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('paWebsite'))#">
								)
						select @attorneyID = SCOPE_IDENTITY();
					end
					select @attorneyID as attorneyID
					set nocount off
				</cfquery>
				<cfset pAttorneyAdded = true>
				<cfset pAttorneyIDs = qryCheckAttorney.attorneyID>
			<cfelse>	
				<cfset pAttorneyAdded = false>
				<cfset pAttorneyIDs = event.getValue('paIDs')>
			</cfif>
			<cfif len(event.getValue('daFirstName','')) AND len(event.getValue('daLastName',''))  AND len(event.getValue('daFirm',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckDAttorney">
					set nocount on
					declare @attorneyID int
					select @attorneyID = attorneyID 
					from dbo.OH_VS_Attorneys
					where FirstName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daFirstName'))#">
					AND LastName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daLastName'))#">
					AND Firm = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daFirm'))#">
					<cfif len(trim(event.getValue('daMiddleName','')))>
						AND ((MiddleName is null) OR (MiddleName = ''))
					<cfelse>
						AND MiddleName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daMiddleName'))#">
					</cfif>
					<cfif len(trim(event.getValue('daPhone','')))>
						AND ((Phone is null) OR (Phone = ''))
					<cfelse>
						AND Phone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daPhone'))#">
					</cfif>
					<cfif len(trim(event.getValue('daWebsite','')))>
						AND ((Website is null) OR (Website = ''))
					<cfelse>
						AND Website = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daWebsite'))#">
					</cfif>
					if @attorneyID is NULL
					begin
						INSERT INTO dbo.OH_VS_Attorneys(FirstName, MiddleName, LastName, Firm, Phone, Website)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daFirstName'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daMiddleName'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daLastName'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daFirm'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daPhone'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('daWebsite'))#">
								)
						select @attorneyID = SCOPE_IDENTITY();
					end
					select @attorneyID as attorneyID
					set nocount off
				</cfquery>
				<cfset dattorneyAdded = true>
				<cfset dattorneyIDs = qryCheckDAttorney.attorneyID>
			<cfelse>	
				<cfset dattorneyAdded = false>
				<cfset dattorneyIDs = event.getValue('daIDs')>
			</cfif>
			<cfset event.paramValue('SubmittingAttorney','')>
			<cfif event.getValue('SubmittingAttorney') eq ''>
				<cfset qryMemberName = getMemberNameForID(memberID=session.cfcuser.memberdata.memberID)>
				<cfset event.setValue('SubmittingAttorney', qryMemberName.memberName)>
			</cfif>
			
			<!--- INSERT RECORD ================================================================ --->
			<cfif variables.verdictID is 0>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="insertVerdict">
					set nocount on
					declare @verdictID int
					INSERT INTO OH_VS_Verdicts(resolutionTypeID, actionTypeID, [date], countyID, 
						<!--- OH requested change --->
						<cfif courtID GT 0>
							courtID, 
						</cfif>							
							caseTitle, caseSummary,
												injuries, age, judgementAmount,
												offerAmount, demandAmount, settlementAmount, medicalPastAmount, medicalFutureAmount, lostWagesAmount,
												diminishedEarningAmount, submittingMemberID, submittingAttorney, isApproved)
					VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('resolutiontypeID')#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#actionTypeID#">,
							<cfqueryparam cfsqltype="CF_SQL_DATE" value="#event.getValue('date')#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('countyID')#">,
							<cfif courtID GT 0>
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('courtID')#">,
							</cfif>
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('casetitle')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('caseSummary')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('injuries')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('age')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('judgementAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('offerAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('demandAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('settlementAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('medicalPastAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('medicalFutureAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('lostWagesAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('diminishedEarningAmount')#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('SubmittingAttorney')#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#event.getValue('isApproved')#">)

					select @verdictID = SCOPE_IDENTITY();
					
					<cfif len(judgeID)>
						UPDATE dbo.OH_VS_Verdicts set judgeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#judgeID#"> where verdictID = @verdictID
					</cfif>
					
					<cfif len(insuranceCoID)>
						INSERT INTO dbo.OH_VS_VerdictsInsuranceCos(verdictID, insuranceCoID, isPlaintiffInsurance) VALUES (@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#insuranceCoID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="#event.getValue('isPlaintiffInsurance')#">)
					</cfif>
					<cfif pExpertAdded AND len(pExpertID)>
						INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#pExpertID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
					<cfelse>
						<cfloop list="#pExpertID#" index="eID">
								<cfif eID NEQ "0">							
									INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#eID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
								</cfif>
						</cfloop>					
					</cfif>
					<cfif dExpertAdded AND len(dExpertID)>
						INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#dExpertID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
					<cfelse>
						<cfloop list="#dExpertID#" index="eID">
								<cfif eID NEQ "0">							
									INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#eID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
								</cfif>
						</cfloop>					
					</cfif>
					<cfif pAttorneyAdded AND len(pAttorneyIDs)>
						INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#pAttorneyIDs#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
					<cfelse>
						<cfloop list="#pAttorneyIDs#" index="aID">
								<cfif aID NEQ "0">							
									INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#aID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
								</cfif>
						</cfloop>					
					</cfif>
					<cfif dattorneyAdded AND len(dattorneyIDs)>
						INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#dattorneyIDs#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
					<cfelse>
						<cfloop list="#dattorneyIDs#" index="aID">
								<cfif aID NEQ "0">							
									INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney) VALUES(@verdictID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#aID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
								</cfif>
						</cfloop>					
					</cfif>
					select @verdictID as verdictID
					set nocount off
				</cfquery>
				<cfset thisVerdictID = insertVerdict.verdictid>
				<!--- Email IN about verdict --->
				<cfif NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					
					<cfset verdictData = buildVerdictSheet(thisVerdictID) />

					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>Thank you for submitting your recent case to the Verdict & Settlement Reporter. 
							Once approved your case will appear in the interactive verdict and settlement 
							database online. OAJ truly appreciates your submission and your help in growing 
							our verdict and settlement database. We welcome any and all future verdicts or 
							settlements that you may have. Thank you again.</p>
							
							<div style="border:1px solid black;">
								#verdictData.verdictView#
							</div>
						</cfoutput>
					</cfsavecontent>
					
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email="<EMAIL>" },
						emailto=[{ name="", email=local.userEmail }],
						emailreplyto=local.associationEmail,
						emailsubject="Your Verdict & Settlement Entry",
						emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - Your Verdict & Settlement Entry",
						emailhtmlcontent=local.mailContent,
						siteID=arguments.event.getValue('mc_siteInfo.siteID'),
						memberID=val(session.cfcuser.memberdata.memberID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>
					<!--- email to Association:  --->

					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>An entry has been made in the OH Verdict and Settlement Exchange Database.</p>
							<p>VerdictID: #thisverdictID#</p>
							<p><a href="http://#application.objPlatform.getCurrentHostname()#/#local.editLink#&verdictID=#thisverdictid#">Click here</a> to review the verdict and approve it for display.</p>
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.associationEmail = replace(local.associationEmail,",",";","all");
						local.toEmailArr = listToArray(local.associationEmail,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
						if (arrayLen(local.arrEmailTo)) {
							local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="", email="<EMAIL>" },
								emailto=local.arrEmailTo,
								emailreplyto="<EMAIL>",
								emailsubject="A New Verdict and Settlement Entry",
								emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - A New Verdict and Settlement Entry",
								emailhtmlcontent=local.mailContent,
								siteID=arguments.event.getValue('mc_siteinfo.siteID'),
								memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
								messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
								sendingSiteResourceID=this.siteResourceID
							);
						}
					</cfscript>
					
				</cfif>
				<cflocation url="#local.receivedLink#&msg=1" addtoken="false">
			<cfelse>
				<!--- UPDATE RECORD  ================================================================ --->
				<cfquery datasource="#application.dsn.customApps.dsn#" name="updateVerdict">
					delete from dbo.OH_VS_VerdictsInsuranceCos
					where verdictID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">
					
					delete from dbo.OH_VS_VerdictsExperts
					where verdictID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">

					delete from dbo.OH_VS_VerdictsAttorneys
					where verdictID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">
					

					update dbo.OH_VS_Verdicts
					set resolutionTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('resolutionTypeID')#">,
					actionTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#actionTypeID#">,
					[date] = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#event.getValue('date')#">,
					countyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('countyID')#">,
					<cfif #event.getValue('courtID', '0')# GT 0>
						courtID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('courtID')#">,
					</cfif>
					<cfif len(judgeID)>
					judgeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#judgeID#">,
					</cfif>
					caseTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('casetitle')#">,
					caseSummary = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('caseSummary')#">,
					injuries = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('injuries')#">,
					age = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('age')#">,
			
<!--- 					plaintiffAttorney = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('PlaintiffAttorney')#">,
					plaintiffAttorneyFirm = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('PlaintiffAttorneyFirm')#">,
					plaintiffAttorneyPhone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('PlaintiffAttorneyPhone')#">,
					plaintiffAttorneyWebsite = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('PlaintiffAttorneyWebsite')#">,
					defenseAttorney = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('DefenseAttorney')#">,
					defenseAttorneyFirm = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('DefenseAttorneyFirm')#">,
					defenseAttorneyPhone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('DefenseAttorneyPhone')#">,
					defenseAttorneyWebsite = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('DefenseAttorneyWebsite')#">, --->
					
					
					judgementAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('judgementAmount')#">,
					offerAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('offerAmount')#">,
					demandAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('demandAmount')#">,
					settlementAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('settlementAmount')#">,
					medicalPastAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('medicalPastAmount')#">,
					medicalFutureAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('medicalFutureAmount')#">,
					lostWagesAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('lostWagesAmount')#">,
					diminishedEarningAmount = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('diminishedEarningAmount')#">,
					<cfif event.getValue('submittingAttorney','NotFound') neq 'NotFound'>
					submittingAttorney = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('submittingAttorney')#">,
					</cfif>
					isApproved = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#event.getValue('isApproved')#">
					where verdictID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">

					<cfif len(insuranceCoID)>
						INSERT INTO dbo.OH_VS_VerdictsInsuranceCos(verdictID, insuranceCoID, isPlaintiffInsurance)
						VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#insuranceCoID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="#event.getValue('isPlaintiffInsurance')#">)
					</cfif>
	
					<cfif pExpertAdded AND len(pExpertID)>
						INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert)
						VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#pExpertID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
					<cfelse>
						<cfloop list="#pExpertID#" index="eID">
								<cfif eID NEQ "0">
									INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert)
									VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#eID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
								</cfif>
						</cfloop>					
					</cfif>

					<cfif dExpertAdded AND len(dExpertID)>
						INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert)
						VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#dExpertID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
					<cfelse>
						<cfloop list="#dExpertID#" index="eID">
								<cfif eID NEQ "0">
									INSERT INTO OH_VS_VerdictsExperts(verdictID, ExpertID, isPlaintiffExpert)
									VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#eID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
								</cfif>
						</cfloop>					
					</cfif>

					<cfif pAttorneyAdded AND len(pAttorneyIDs)>
						INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney)
						VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#pAttorneyIDs#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
					<cfelse>
						<cfloop list="#pAttorneyIDs#" index="aID">
								<cfif aID NEQ "0">							
									INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney)
									VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#aID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="TRUE">)
								</cfif>
						</cfloop>					
					</cfif>

					<cfif dattorneyAdded AND len(dattorneyIDs)>
						INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney)
						VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#dattorneyIDs#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
					<cfelse>
						<cfloop list="#dattorneyIDs#" index="aID">
								<cfif aID NEQ "0">							
									INSERT INTO OH_VS_VerdictsAttorneys(verdictID, attorneyID, isPlaintiffAttorney)
									VALUES(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('verdictid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#aID#">, <cfqueryparam cfsqltype="CF_SQL_BIT" value="FALSE">)
								</cfif>
						</cfloop>					
					</cfif>
	
				</cfquery>
				<cfset thisverdictid = event.getValue('verdictid')>
			<!--- locate user to msg=2 update message --->
			<cflocation url="#local.receivedLink#&msg=2">
			</cfif>
			
		</cfcase>
		
		<cfcase value="received">
			
			
			
			<cfoutput>
				<p class="HeaderText">OH's Verdict and Settlement Exchange Database</p>

				<cfswitch expression="event.getValue('msg',0)">
					<cfcase value="1"><p class="HeaderText">Information Added. </p></cfcase>
					<cfcase value="2"><p class="HeaderText">Information Updated. </p></cfcase>
					<cfcase value="3"><p class="HeaderText">Information Deleted. </p></cfcase>
					
				</cfswitch>
				

				<cfif NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<p class="bodyText">Thank you for submitting your recent case to the Verdict & Settlement Reporter. 
				Once approved your case will appear in the interactive verdict and settlement 
				database online. OAJ truly appreciates your submission and your help in growing 
				our verdict and settlement database. We welcome any and all future verdicts or 
				settlements that you may have. Thank you again.</p>
				</cfif>
				<br />
				<table border="0" cellpadding="2">
					<tr>
						<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
							<form action="#local.resultsLink#" method="post">
							<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
								<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
							</CFLOOP>
							<td><input type="submit" value="Return to Results" class="bodyText"/></td>
							</form>
						</cfif>
						<td><input type="button" onclick="parent.location='#local.baseLink#';" value="New Search" class="bodyText" /></td>
					</tr>
				</table>
			</cfoutput>
			
		</cfcase>
		
		<cfcase value="results">
			<cfset pageID = int(val(event.getValue('page',0)))>
			<cfset maxrows = 10>
			<cfquery name="local.qryMatches" datasource="#application.dsn.customApps.dsn#">
				select *
				from OH_VS_Verdicts v
				left outer  join OH_VS_ActionTypes vsat on vsat.actionID = v.actionTypeID
				left outer  join OH_VS_ResolutionTypes vsrt on vsrt.resolutionID = v.resolutionTypeID
				left outer  join OH_VS_Counties cnt on cnt.countyID = v.countyID
				left outer  join OH_VS_Courts c on c.courtID = v.courtID
				<cfif len(event.getValue('insuranceCoID',''))>
					inner join OH_VS_VerdictsInsuranceCos vsvic on vsvic.verdictID = v.verdictID
						and vsvic.InsuranceCoID = <cfqueryparam value="#event.getValue('insuranceCoID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('expertID',''))>
					inner join OH_VS_VerdictsExperts vsve on vsve.verdictID = v.verdictID
						and vsve.expertID = <cfqueryparam value="#event.getValue('expertID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				where 
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and len(event.getValue('isApproved'))>
					isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="CF_SQL_BIT">
				<cfelseif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					1=1
				<cfelse>
					isApproved = 1
				</cfif>
				<cfif len(event.getValue('s_datefrom',''))>
					and v.[date] >= <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('s_datefrom')#">
				</cfif>
				<cfif len(event.getValue('s_dateto',''))>
					and v.[date] <= <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('s_dateto')#">
				</cfif>
				<cfif len(event.getValue('countyID',''))>
					and v.countyID = <cfqueryparam value="#event.getValue('countyID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('courtID',''))>
					and v.courtID = <cfqueryparam value="#event.getValue('courtID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('actionTypeID',''))>
					and v.actionTypeID = <cfqueryparam value="#event.getValue('actionTypeID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('resolutionTypeID',''))>
					and v.resolutionTypeID = <cfqueryparam value="#event.getValue('resolutionTypeID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('judgeID',''))>
					and v.judgeID = <cfqueryparam value="#event.getValue('judgeID')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('keywords',''))>
					and (
						isnull(v.caseTitle,'') + ' ' + isnull(caseSummary,'') + ' ' + isnull(injuries,'') 
						LIKE <cfqueryparam value="%#event.getValue('keywords')#%" cfsqltype="CF_SQL_VARCHAR">
						)
				</cfif>
				order by v.[date] DESC
			</cfquery>
	
			<cfset session.lastVerdictSearch = duplicate(form)>
			<cfif local.qryMatches.recordcount>
				<cfset numpages = ceiling(local.qryMatches.recordcount / variables.maxrows)>
				<cfset startrow = ((variables.pageID-1) * variables.maxrows) + 1>
				<cfset endrow = variables.startrow + variables.maxrows - 1>
				<cfif local.qryMatches.recordcount lt variables.endrow>
					<cfset endrow = local.qryMatches.recordcount>
				</cfif>
			<cfelse>
				<cfset numpages = 0>
				<cfset startrow = 0>
				<cfset endrow = 0>
			</cfif>
			<cfoutput>
				<div class="HeaderText">Verdicts and Settlements Search Results</div>
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					<div class="bodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
				</cfif>
				<script language="JavaScript">
					function prevPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')-1#';
						objForm.submit();
					}
					function nextPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')+1#';
						objForm.submit();
					}
				</script>
				<br/>
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td class="bodyText">Showing #startrow# to #endrow# of #local.qryMatches.recordcount# matches</td>
						<td align="right">
							<cfif form.page gt 1>
								<input type="button" value="&lt;&lt; Previous Page" class="bodyText" onclick="prevPage();">
							</cfif>
							<cfif local.qryMatches.recordcount gt (form.page*maxrows)>
								<input type="button" value="Next Page &gt;&gt;" class="bodyText" onclick="nextPage();">
							</cfif>
							<input type="button" onclick="self.location.href='#local.baseLink#';" value="New Search" class="bodyText">
							<!--- ADD BUTTON: --->
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Verdict" class="bodyText">
							</cfif>
							<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
								<input type="button" onclick="self.location.href='#local.attorneysLink#';" value="Attorneys" class="bodyText">
								<input type="button" onclick="self.location.href='#local.expertsLink#';" value="Experts" class="bodyText">
							</cfif>
							
						</td>
					</tr>
				</table>
				<br/>
			</cfoutput>
			<cfif local.qryMatches.recordcount eq 0>
				<cfoutput><div class="bodyText">No records match your search criteria.</div></cfoutput>
			<cfelse>
				<cfoutput>
				<table border="0" class="bodyText" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
					<tr bgcolor="##999999">
						<td colspan="2"></td>
						
						<th align="left">Case Caption</th>
						<th align="left">Date</th>
						<th align="left">Type of Action</th>
						<th align="left">Resolution</th>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<th align="left">County</th>
					</cfif>
						<th align="left">Courthouse</th>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<th align="left">Submitted By</th>
					</cfif>
					</tr>
				</cfoutput>
				<cfoutput query="local.qryMatches" startrow="#variables.startrow#" maxrows="#variables.maxrows#">
					<tr valign="top" <cfif local.qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#local.qryMatches.currentrow#.</td>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>>
							<a href="#local.viewLink#&verdictID=#local.qryMatches.verdictID#">View</a>
							<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
								<br/><a href="#local.editLink#&verdictID=#local.qryMatches.verdictID#">Edit</a>
							</cfif>
						</td>
						
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.caseTitle#&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;"><cfif isdate(local.qryMatches.date)>#dateformat(local.qryMatches.date,"m/d/yyyy")#</cfif>&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.actionType#&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.resolutionType#&nbsp;</td>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.countyName#&nbsp;</td>
					</cfif>	
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.courtName#&nbsp;</td>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.submittingAttorney#&nbsp;</td>
					</cfif>	
					</tr>
				</cfoutput>
				<cfoutput></table></cfoutput>
			</cfif>
			<cfoutput>	
				<form name="frmHidden" action="#local.resultsLink#" method="post">
				<input type="hidden" name="page" value="">
				<cfloop INDEX="form_element" LIST="#FORM.fieldnames#">
					<cfif form_element neq "page">
						<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
					</cfif>
				</CFLOOP>
				</form>
			</cfoutput>
		</cfcase>

		<cfcase value="search">
			<cfset local.qryCountyNames = getCountyNames(forSearch=true)>
			<cfset local.qryCourtNames = getCourtNames(forSearch=true)>
			<cfset local.qryActionTypes = getActionTypes(forSearch=true)>
			<cfset local.qryResolutionTypes = getResolutionTypes(forSearch=true)>
			<cfset local.qryInsuranceNames = getInsuranceNames(forSearch=true)>
			<cfset local.qryExpertNames = getExpertNames(forSearch=true)>
			<cfset local.qryJudgeNames = getJudgeNames(forSearch=true)>
				<cfsavecontent variable="variables.JS">	
					<cfoutput>
					<script language="JavaScript" type="text/javascript">
					<!--
					$(document).ready(function(){
						mca_setupDatePickerRangeFields('s_datefrom','s_dateto');
					});						
					//-->
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#variables.JS#">
			<cfoutput>
				<p><span class="HeaderText">Search OH Verdict and Settlement Exchange Database</span></p>
				<p><span class="bodyText">OH's Verdict and Settlement Database contains reports 
				submitted by members for publication in Verdict. The database records include 
				information regarding a verdict or a settlement as provided by OH members. 
				The Verdict and Settlement Database is a valuable service to our members and 
				we welcome all reports.</span></p>
			
				<cfform action="#local.resultsLink#" method="post">
					<input type="hidden" name="page" value="1" />
					<table class="bodyText">
					<tr>
						<td>Resolution Date between:</td>
						<td>
							<table cellpadding="0" cellspacing="0">
							<tr>
							<td>
								<cfinput type="text" id="s_datefrom" name="s_datefrom" value="" size="10" maxlength="10" autocomplete="off" validate="date" message="Enter a valid resolution date">
								<a href="javascript:mca_clearDateRangeField('s_datefrom');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 5px;"></i></a>
							</td>
							<td>&nbsp;and&nbsp;</td>
							<td>
								<cfinput type="text" id="s_dateto" name="s_dateto" value="" size="10" maxlength="10" autocomplete="off" validate="date" message="Enter a valid resolution date">
								<a href="javascript:mca_clearDateRangeField('s_dateto');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 5px;"></i></a>
							</td>
							</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>County:</td>
						<td>
							<select name="countyID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryCountyNames">
								<option value="#local.qryCountyNames.countyID#">#local.qryCountyNames.countyName#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Courthouse:</td>
						<td>
							<select name="courtID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryCourtNames">
								<option value="#local.qryCourtNames.courtID#">#local.qryCourtNames.courtName#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Action Type:</td>
						<td>
							<select name="actionTypeID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryActionTypes">
								<option value="#local.qryActionTypes.actionID#">#local.qryActionTypes.actionType#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Resolution Type:</td>
						<td>
							<select name="resolutionTypeID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryResolutionTypes">
								<option value="#local.qryResolutionTypes.resolutionID#">#local.qryResolutionTypes.resolutionType#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Insurance Company:</td>
						<td>
							<select name="insuranceCoID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryInsuranceNames">
								<option value="#local.qryInsuranceNames.insuranceCoID#">#local.qryInsuranceNames.CompanyName#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Expert:</td>
						<td>
							<select name="expertID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryExpertNames">
								<option value="#local.qryExpertNames.expertID#">#local.qryExpertNames.expertName#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Judge:</td>
						<td>
							<select name="judgeID" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryJudgeNames">
								<option value="#local.qryJudgeNames.judgeID#">#local.qryJudgeNames.judgeName#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Keywords (optional):</td>
						<td><input type="text" name="keywords" class="bodyText" maxlength="70" size="70" /></td>
					</tr>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<tr>
							<td>Approval Status:</td>
							<td>
								<select name="isApproved" class="bodyText">
								<option value="">All</option>
								<option value="1">Approved Verdicts Only</option>
								<option value="0">Non-Approved Verdicts Only</option>
								</select>
							</td>
						</tr>
					</cfif>
					</table>
					<br />
					<input type="submit" value="Search Reports" class="bodyText"/>
					<!--- ADD BUTTON: --->
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.editLink#';" value="Add Verdict" class="bodyText" />
					</cfif>	
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.attorneysLink#';" value="Attorneys" class="bodyText">
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.expertsLink#';" value="Experts" class="bodyText">
					</cfif>
					
					
				</cfform>
			</cfoutput>
		</cfcase>
	
		<cfdefaultcase><!--- You do not have rights to view this page ---></cfdefaultcase>
	
	</cfswitch>

<cffunction name="getActionTypes" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>actionID, actionType
		FROM dbo.OH_VS_ActionTypes vsat
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_Verdicts vsv on vsv.actionTypeID = vsat.actionID
		</cfif>
		ORDER BY actionType
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getActionTypeForID" returntype="query" output="No">
	<cfargument name="actionID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT actionID, actionType
		FROM dbo.OH_VS_ActionTypes vsat
		where actionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actionID#">
		ORDER BY actionType
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getResolutionTypes" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>vsrt.resolutionID, vsrt.resolutionType
		FROM dbo.OH_VS_ResolutionTypes vsrt
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_Verdicts vsv on vsv.resolutionTypeID = vsrt.resolutionID
		</cfif>
		ORDER BY resolutionType
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getResolutionTypeForID" returntype="query" output="No">
	<cfargument name="resolutionID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT vsrt.resolutionID, vsrt.resolutionType
		FROM dbo.OH_VS_ResolutionTypes vsrt
		where resolutionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.resolutionID#">
		ORDER BY resolutionType
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCountyNames" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>vsc.countyID, vsc.countyName
		FROM dbo.OH_VS_Counties vsc
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_Verdicts vsv on vsv.countyID = vsc.countyID
		</cfif>
		ORDER BY countyName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCountyNameForID" returntype="query" output="No">
	<cfargument name="countyID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT vsc.countyID, vsc.countyName
		FROM dbo.OH_VS_Counties vsc
		where countyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.countyID#">
		ORDER BY countyName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCourtNames" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select <cfif arguments.forSearch>DISTINCT </cfif>vscc.countyID, vsc.courtID, vsc.courtName
		from dbo.OH_VS_Courts vsc
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_Verdicts v on v.courtID = vsc.courtID
		</cfif>
		left outer join OH_VS_CourtCounties vscc on vscc.courtID = vsc.courtID 
		order by vscc.countyID, vsc.courtName		
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCourtNameForID" returntype="query" output="No">
	<cfargument name="courtID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT vsc.courtID, vsc.courtName
		FROM dbo.OH_VS_Courts vsc
		where courtID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.courtID#">
		ORDER BY courtName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getJudgeNames" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT vsj.LastName, </cfif>vsj.judgeID, RTRIM(RTRIM(ISNULL(vsj.FirstName,'') + ' ' + ISNULL(vsj.MiddleInitial,'')) + ' ' + ISNULL(vsj.LastName,'') + ' ' + ISNULL(vsj.Suffix,'')) as judgeName
		FROM dbo.OH_VS_Judges vsj
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_Verdicts v on v.judgeID = vsj.judgeID
		</cfif>
		ORDER BY vsj.LastName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getJudgeNameForID" returntype="query" output="No">
	<cfargument name="judgeID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT vsj.judgeID, RTRIM(RTRIM(ISNULL(vsj.FirstName,'') + ' ' + ISNULL(vsj.MiddleInitial,'')) + ' ' + ISNULL(vsj.LastName,'') + ' ' + ISNULL(vsj.Suffix,'')) as judgeName
		FROM dbo.OH_VS_Judges vsj
		where judgeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.judgeID#">
		ORDER BY vsj.LastName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getExpertNames" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT vse.expertID, vse.LastName, </cfif>vse.expertID, RTRIM(RTRIM(ISNULL(vse.FirstName,'') + ' ' + ISNULL(vse.MiddleInitial,'')) + ' ' + ISNULL(vse.LastName,'') + ' ' + ISNULL(vse.Suffix,'')) as expertName
		FROM dbo.OH_VS_Experts vse
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_VerdictsExperts ve on ve.expertID = vse.expertID
		</cfif>
		ORDER BY vse.LastName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getExpertNameForID" returntype="query" output="No">
	<cfargument name="expertID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT vse.expertID, RTRIM(RTRIM(ISNULL(vse.FirstName,'') + ' ' + ISNULL(vse.MiddleInitial,'')) + ' ' + ISNULL(vse.LastName,'') + ' ' + ISNULL(vse.Suffix,'')) as expertName
		FROM dbo.OH_VS_Experts vse
		where expertID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.expertID#">
		ORDER BY vse.LastName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getInsuranceNames" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>vsi.insuranceCoID, vsi.CompanyName
		FROM dbo.OH_VS_InsuranceCos vsi
		<cfif arguments.forSearch>
		INNER JOIN dbo.OH_VS_VerdictsInsuranceCos vsvi on vsvi.InsuranceCoID = vsi.insuranceCoID
		</cfif>
		ORDER BY vsi.CompanyName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getInsuranceNameForID" returntype="query" output="No">
	<cfargument name="insuranceCoID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT vsi.insuranceCoID, vsi.CompanyName
		FROM dbo.OH_VS_InsuranceCos vsi
		where insuranceCoID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.insuranceCoID#">
		ORDER BY vsi.CompanyName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getMemberNameForID" returntype="query" output="No">
	<cfargument name="memberID" type="numeric" required="true">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.membercentral.dsn#">
		
		select mActive.memberID, RTRIM(RTRIM(ISNULL(mActive.FirstName,'') + ' ' + ISNULL(mActive.MiddleName,'')) + ' ' + ISNULL(mActive.LastName,'') + ' ' + ISNULL(mActive.Suffix,'')) as memberName
		from dbo.ams_members m
		inner join dbo.ams_members mActive on mActive.memberID = m.activeMemberID
		where m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="buildVerdictSheet" returntype="struct" output="No">
	<cfargument name="verdictID" type="numeric" required="true">
	
	<cfset var local = structNew() />
	<cfscript>
		local.verdictValues = getVerdict(int(val(arguments.verdictID)));
		
		local.verdictData.verdictID = local.verdictValues.verdictID;
		
		if(len(local.verdictValues.judgementAmount) eq 0) local.verdictValues.judgementAmount = "0.00";
		if(len(local.verdictValues.offerAmount) eq 0) local.verdictValues.offerAmount = "0.00";
		if(len(local.verdictValues.demandAmount) eq 0) local.verdictValues.demandAmount = "0.00";
		if(len(local.verdictValues.settlementAmount) eq 0) local.verdictValues.settlementAmount = "0.00";
		if(len(local.verdictValues.medicalPastAmount) eq 0) local.verdictValues.medicalPastAmount = "0.00";
		if(len(local.verdictValues.medicalFutureAmount) eq 0) local.verdictValues.medicalFutureAmount = "0.00";
		if(len(local.verdictValues.lostWagesAmount) eq 0) local.verdictValues.lostWagesAmount = "0.00";
		if(len(local.verdictValues.diminishedEarningAmount) eq 0) local.verdictValues.diminishedEarningAmount = "0.00";
		
	</cfscript>
	<cfsavecontent variable="local.verdictData.verdictView">
		<cfoutput>
			<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<tr>
						<td colspan="2">
							<cfif local.verdictValues.isApproved>
								Approved - available for viewing
							<cfelse>
								Not Approved - not available for viewing
							</cfif>
						</td>
					</tr>
				</cfif>
				<tr valign="top">
					<td nowrap><strong>Date of Resolution:</strong></td>
					<td>#dateformat(local.verdictValues.date,"mm/dd/yyyy")#</td>
				</tr>
				<tr valign="top">
					<td nowrap><strong>Case Caption:</strong></td>
					<td>#local.verdictValues.casetitle#</td>
				</tr>
				<tr valign="top">
					<td nowrap><strong>Resolution:</strong></td>
					<td>
						<cfset local.qryResType = getResolutionTypeForID(local.verdictValues.resolutionTypeID)>
						#local.qryResType.resolutionType#
					</td>
				</tr>
				<tr valign="top">
					<td nowrap><strong>Type of Action:</strong></td>
					<td>
						<cfset local.qryActionType = getActionTypeForID(local.verdictValues.actionTypeID)>
						#local.qryActionType.actionType#
					</td>
				</tr>
				<tr>
					<td valign="top"><strong>Injuries:</strong></td>
					<td valign="top">#local.verdictValues.injuries#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Case Summary:</strong></td>
					<td valign="top">#local.verdictValues.caseSummary#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Judgment:</strong></td>
					<td valign="top">$#local.verdictValues.judgementAmount#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Offer:</strong></td>
					<td valign="top">$#local.verdictValues.offerAmount#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Demand:</strong></td>
					<td valign="top">$#local.verdictValues.demandAmount#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Settlement:</strong></td>
					<td valign="top">$#local.verdictValues.settlementAmount#</td>
				</tr>
				<tr valign="top">
					<td nowrap><strong>County:</strong></td>
					<td>
						<cfset local.qryCountyName = getCountyNameForID(local.verdictValues.countyID)>
						#local.qryCountyName.countyName#
					</td>
				</tr>
				<tr valign="top">
					<td nowrap><strong>Courthouse:</strong></td>
					<td>
						<cfif local.verdictValues.courtID NEQ "">
							<cfset local.qryCourtName = getCourtNameForID(local.verdictValues.courtID)>
						<cfelse>
							<cfset local.qryCourtName = getCourtNameForID(0)>
						</cfif>
						#local.qryCourtName.courtName#
					</td>
				</tr>
				<tr>
					<td valign="top" colspan="2"><strong>Plaintiff Attorney(s):</strong></td>
				</tr>
				<tr>
					<td valign="top" colspan="2">
						<table width="100%" cellspacing="2" cellpadding="2" border="1">
							<tr><th>Name</th><th>Firm</th><td>Phone</td><td>Website</td></tr>
							<cfloop query="local.verdictValues.plaintiffAttys">
								<tr><td width="25%">#local.verdictValues.plaintiffAttys.FirstName# #local.verdictValues.plaintiffAttys.MiddleName# #local.verdictValues.plaintiffAttys.LastName#</td><td width="35%">#local.verdictValues.plaintiffAttys.Firm#</td><td width="15%">#local.verdictValues.plaintiffAttys.Phone#</td><td width="25%">#local.verdictValues.plaintiffAttys.website#</td></tr>
							</cfloop>
						</table>
					</td>
				</tr>
				<tr>
					<td valign="top" colspan="2"><strong>Defense Attorney(s):</strong></td>
				</tr>
				<tr>
					<td valign="top" colspan="2">
						<table width="100%" cellspacing="1" cellpadding="2" border="1">
							<tr><th>Name</th><th>Firm</th><td>Phone</td><td>Website</td></tr>
							<cfloop query="local.verdictValues.defenseAttys">
								<tr><td width="25%">#local.verdictValues.defenseAttys.FirstName# #local.verdictValues.defenseAttys.MiddleName# #local.verdictValues.defenseAttys.LastName#</td><td width="35%">#local.verdictValues.defenseAttys.Firm#</td><td width="15%">#local.verdictValues.defenseAttys.Phone#</td><td width="25%">#local.verdictValues.defenseAttys.website#</td></tr>
							</cfloop>
						</table>
					</td>
				</tr>
				<tr valign="top">
					<td nowrap><strong>Insurance Company:</strong></td>
					<td>
						<cfif len(local.verdictValues.insuranceCoID)>
							<cfset local.qryInsuranceName = getInsuranceNameForID(local.verdictValues.insuranceCoID)>
							#local.qryInsuranceName.companyName# (<cfif local.verdictValues.isPlaintiffInsurance>Plaintiff's Insurance Company<cfelse>Defendant's Insurance Company</cfif>)
						<cfelse>
							(No Insurance Company selected)
						</cfif>
					</td>
				</tr>
				<tr>
					<td valign="top"><strong>Age of Plaintiff:</strong></td>
					<td valign="top">#local.verdictValues.age#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Medical Specials (past):</strong></td>
					<td valign="top">$#local.verdictValues.medicalPastAmount#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Medical Specials (future):</strong></td>
					<td valign="top">$#local.verdictValues.medicalFutureAmount#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Wage Loss:</strong></td>
					<td valign="top">$#local.verdictValues.lostWagesAmount#</td>
				</tr>
				<tr>
					<td valign="top"><strong>Diminished Earning Capacity:</strong></td>
					<td valign="top">$#local.verdictValues.diminishedEarningAmount#</td>
				</tr>
				<tr>
					<td valign="top" colspan="2"><strong>Expert(s):</strong></td>
				</tr>
				<tr>
					<td valign="top" colspan="2">
						<table width="100%" cellspacing="1" cellpadding="2" border="1">
							<tr><th>Expert Name</th><th>Type</th></tr>
							<cfloop query="local.verdictValues.pExperts">
								<tr><td>#local.verdictValues.pExperts.FirstName# #local.verdictValues.pExperts.MiddleInitial# #local.verdictValues.pExperts.LastName# #local.verdictValues.pExperts.suffix#</td><td><cfif local.verdictValues.pExperts.isPlaintiffExpert>Plaintiff<cfelse>Defense</cfif></td></tr>
							</cfloop>
							<cfloop query="local.verdictValues.dExperts">
								<tr><td>#local.verdictValues.dExperts.FirstName# #local.verdictValues.dExperts.MiddleInitial# #local.verdictValues.dExperts.LastName# #local.verdictValues.dExperts.suffix#</td><td><cfif local.verdictValues.dExperts.isPlaintiffExpert>Plaintiff<cfelse>Defense</cfif></td></tr>
							</cfloop>
						</table>
					</td>
				</tr>
	
				<tr valign="top">
					<td nowrap><strong>Judge:</strong></td>
					<td>
						<cfif len(local.verdictValues.judgeID)>
							<cfset local.qryJudgeName = getJudgeNameForID(local.verdictValues.judgeID)>
							#local.qryJudgeName.judgeName#
						<cfelse>
							(No Judge selected)
						</cfif>
					</td>
				</tr>
				<tr>
					<td nowrap><strong>Submitted By:</strong></td>
					<td valign="top">
						<cfif len(local.verdictValues.SubmittingAttorney)>
							#local.verdictValues.SubmittingAttorney#
						<cfelse>
							<cfset local.qryMemberName = getMemberNameForID(local.verdictValues.submittingMemberID)>
							#local.qryMemberName.memberName#
						</cfif>
					</td>
				</tr>
			</table>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.verdictData />
</cffunction>

<cffunction name="buildPAForVerdict" returntype="string" output="No">
	<cfargument name="attorneyIDs" type="string" required="yes">
	<cfset var local 				= structNew()>
	<cfset local.qryAttorneys = getAttorneys() />
	<cfset local.newObj 		= 0 />
	<cfif structCount(arguments) GT 1>
		<cfset local.newObj = arguments[2] />
	</cfif>
	<cfsavecontent variable="local.data">
		<cfoutput>			
			<cfif val(local.newObj)>				
				<input type="hidden" name="paIDs" value="0">
				<table class="tsAppBodyText">
					<tr>
						<td>First Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="paFirstName" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Middle Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="paMiddleName" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Last Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="paLastName" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Firm</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="paFirm" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Phone</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="paPhone" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Website</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="paWebsite" maxlength="50" size="30" value=""></td>
					</tr>
				</table>
			<cfelse>
				<select name="paIDs" multiple="True" size="5">
					<option value="0">--- Select Plaintiff Attorneys </option>
					<cfloop query="local.qryAttorneys">
						<option value="#local.qryAttorneys.attorneyID#" <cfif listFind(arguments.attorneyIDs,local.qryAttorneys.attorneyID)>SELECTED</cfif>>#local.qryAttorneys.lastName#, #local.qryAttorneys.firstName# <cfif len(trim(local.qryAttorneys.MiddleName))>#local.qryAttorneys.MiddleName#</cfif> <cfif len(trim(local.qryAttorneys.Firm))>- #local.qryAttorneys.Firm#</cfif> </option>
					</cfloop>
				</select>
				<input type="hidden" name="paFirstName" value="">
				<input type="hidden" name="paMiddleName" value="">
				<input type="hidden" name="paLastName" value="">
				<input type="hidden" name="paFirm" value="">
				<input type="hidden" name="paPhone" value="">
				<input type="hidden" name="paWebsite" value="">
			</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.data />
</cffunction>
<cffunction name="buildDAForVerdict" returntype="string" output="No">
	<cfargument name="attorneyIDs" type="string" required="yes">
	<cfset var local 				= structNew()>
	<cfset local.qryAttorneys = getAttorneys() />
	<cfset local.newObj 		= 0 />
	<cfif structCount(arguments) GT 1>
		<cfset local.newObj = arguments[2] />
	</cfif>
	<cfsavecontent variable="local.data">
		<cfoutput>			
			<cfif val(local.newObj)>				
				<input type="hidden" name="daIDs" value="0">
				<table class="tsAppBodyText">
					<tr>
						<td>First Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="daFirstName" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Middle Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="daMiddleName" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Last Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="daLastName" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Firm</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="daFirm" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Phone</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="daPhone" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Website</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="daWebsite" maxlength="50" size="30" value=""></td>
					</tr>
				</table>
			<cfelse>
				<select name="daIDs" multiple="True" size="5">
					<option value="0">--- Select Defense Attorneys </option>
					<cfloop query="local.qryAttorneys">
						<option value="#local.qryAttorneys.attorneyID#" <cfif listFind(arguments.attorneyIDs,local.qryAttorneys.attorneyID)>SELECTED</cfif>>#local.qryAttorneys.lastName#, #local.qryAttorneys.firstName# <cfif len(trim(local.qryAttorneys.MiddleName))>#local.qryAttorneys.MiddleName#</cfif> <cfif len(trim(local.qryAttorneys.Firm))>- #local.qryAttorneys.Firm#</cfif> </option>
					</cfloop>
				</select>
				<input type="hidden" name="daFirstName" value="">
				<input type="hidden" name="daMiddleName" value="">
				<input type="hidden" name="daLastName" value="">
				<input type="hidden" name="daFirm" value="">
				<input type="hidden" name="daPhone" value="">
				<input type="hidden" name="daWebsite" value="">
			</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.data />
</cffunction>
<cffunction name="buildPExpertForVerdict" returntype="string" output="No">
	<cfargument name="expertIDs" type="string" required="yes">
	<cfset var local 				= structNew()>
	<cfset local.qryExperts = getExperts() />
	<cfset local.newObj 		= 0 />
	<cfif structCount(arguments) GT 1>
		<cfset local.newObj = arguments[2] />
	</cfif>
	<cfsavecontent variable="local.data">
		<cfoutput>			
			<cfif val(local.newObj)>				
				<input type="hidden" name="pExpertIDs" value="0">
				<table class="tsAppBodyText">
					<tr>
						<td valign="top"><strong>First Name:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="pExpertFirstNameNew" id="pExpertFirstNameNew" maxlength="50" size="30"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Middle Initial:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="pExpertMINew" id="pExpertMINew" maxlength="50" size="30"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Last Name:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="pExpertLastNameNew" id="pExpertLastNameNew" maxlength="50" size="30"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Suffix:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="pExpertSuffixNew" id="pExpertSuffixNew" maxlength="50" size="30"></td>
					</tr>
				</table>
			<cfelse>
				<select name="pExpertIDs" multiple="True" size="5">
					<option value="0">--- Select Experts </option>
					<cfloop query="local.qryExperts">
						<option value="#local.qryExperts.expertID#" <cfif listFind(arguments.expertIDs,local.qryExperts.expertID)>SELECTED</cfif>>#local.qryExperts.lastName#, #local.qryExperts.firstName# <cfif len(trim(local.qryExperts.MiddleInitial))>#local.qryExperts.MiddleInitial#</cfif> <cfif len(trim(local.qryExperts.suffix))> - #local.qryExperts.suffix#</cfif></option>
					</cfloop>
				</select>
				<input type="hidden" name="pExpertFirstNameNew" value="">
				<input type="hidden" name="pExpertMINew" value="">
				<input type="hidden" name="pExpertLastNameNew" value="">
				<input type="hidden" name="pExpertSuffixNew" value="">
			</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.data />
</cffunction>
<cffunction name="buildDExpertForVerdict" returntype="string" output="No">
	<cfargument name="expertIDs" type="string" required="yes">
	<cfset var local 				= structNew()>
	<cfset local.qryExperts = getExperts() />
	<cfset local.newObj 		= 0 />
	<cfif structCount(arguments) GT 1>
		<cfset local.newObj = arguments[2] />
	</cfif>
	<cfsavecontent variable="local.data">
		<cfoutput>			
			<cfif val(local.newObj)>				
				<input type="hidden" name="dExpertIDs" value="0">
				<table class="tsAppBodyText">
					<tr>
						<td valign="top"><strong>First Name:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="dExpertFirstNameNew" id="dExpertFirstNameNew" maxlength="50" size="30"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Middle Initial:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="dExpertMINew" id="dExpertMINew" maxlength="50" size="30"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Last Name:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="dExpertLastNameNew" id="dExpertLastNameNew" maxlength="50" size="30"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Suffix:</strong></td>
						<td>&nbsp;</td>
						<td valign="top"><input class="bodyText" type="text" name="dExpertSuffixNew" id="dExpertSuffixNew" maxlength="50" size="30"></td>
					</tr>
				</table>
			<cfelse>
				<select name="dExpertIDs" multiple="True" size="5">
					<option value="0">--- Select Experts </option>
					<cfloop query="local.qryExperts">
						<option value="#local.qryExperts.expertID#" <cfif listFind(arguments.expertIDs,local.qryExperts.expertID)>SELECTED</cfif>>#local.qryExperts.lastName#, #local.qryExperts.firstName# <cfif len(trim(local.qryExperts.MiddleInitial))>#local.qryExperts.MiddleInitial#</cfif> <cfif len(trim(local.qryExperts.suffix))> - #local.qryExperts.suffix#</cfif></option>
					</cfloop>
				</select>
				<input type="hidden" name="dExpertFirstNameNew" value="">
				<input type="hidden" name="dExpertMINew" value="">
				<input type="hidden" name="dExpertLastNameNew" value="">
				<input type="hidden" name="dExpertSuffixNew" value="">
			</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.data />
</cffunction>
<cffunction name="getExperts" returntype="query" output="No">

	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT *
		FROM dbo.OH_VS_Experts vse
		ORDER BY vse.LastName, vse.FirstName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="checkExpertForVerdicts" returntype="boolean" output="No">
	<cfargument name="expertID" type="numeric" required="yes">
	<cfset var local = structNew() />
	<cfset local.data = FALSE />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">		
		SELECT verdictID
		FROM dbo.OH_VS_VerdictsExperts
		WHERE expertID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.expertID#">
	</cfquery>
	<cfif local.qry.recordCount>
		<cfset local.data = True />
	</cfif>
	<cfreturn local.data />
</cffunction>
<cffunction name="getExpert" returntype="query" output="No">
	<cfargument name="expertID" type="numeric" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
		SELECT *
		FROM dbo.OH_VS_Experts
		WHERE expertID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.expertID#">
	</cfquery>
	<cfreturn local.qry />
</cffunction>
<cffunction name="expertConstruct" returntype="struct" output="No">
	<cfargument name="expertID" type="numeric" required="yes">
	<cfset var local = structNew()>
	<cfscript>
		local.data.expertID		= 0;
		local.data.firstName	= '';
		local.data.middleInitial	= '';
		local.data.lastName	= '';
		local.data.suffix	= '';
		local.qrydata = getExpert(arguments.expertID);
		if( local.qrydata.recordCount ){
			local.data.expertID		= local.qrydata.expertID;
			local.data.firstName	= local.qrydata.firstName;
			local.data.middleInitial	= local.qrydata.middleInitial;
			local.data.lastName	 	= local.qrydata.lastName;
			local.data.suffix	 			= local.qrydata.suffix;
		}
	</cfscript>
	<cfreturn local.data />
</cffunction>
<cffunction name="insertExpert" returntype="numeric" output="No">
	<cfargument name="firstName" type="string" required="yes">
	<cfargument name="middleInitial" type="string" required="yes">
	<cfargument name="lastName" type="string" required="yes">
	<cfargument name="suffix" type="string" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
		set nocount on
		INSERT INTO dbo.OH_VS_Experts(
			middleInitial,
			suffix,
			firstName,
			lastName
		)
		Values(
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.middleInitial#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.suffix#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firstName#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lastName#">
		)
		
		select SCOPE_IDENTITY() as expertID
	</cfquery>
	<cfreturn local.qry.expertID />
</cffunction>
<cffunction name="updateExpert" returntype="void" output="No">
	<cfargument name="expertID" type="numeric" required="yes">
	<cfargument name="firstName" type="string" required="yes">
	<cfargument name="middleInitial" type="string" required="yes">
	<cfargument name="lastName" type="string" required="yes">
	<cfargument name="suffix" type="string" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">		
		UPDATE dbo.OH_VS_Experts
			set 
			middleInitial 		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.middleInitial#">,
			suffix				= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.suffix#">,
			firstName 		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firstName#">,
			lastName 			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lastName#">			
		WHERE expertID 	= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.expertID#">
	</cfquery>
</cffunction>
<cffunction name="deleteExpert" returntype="void" output="No">
	<cfargument name="expertID" type="numeric" required="yes">
	<cfset var local = structNew() />
	<cfif NOT checkExpertForVerdicts(arguments.expertID)>
		<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">		
			DELETE FROM dbo.OH_VS_Experts
			WHERE expertID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.expertID#">
		</cfquery>
	</cfif>
</cffunction>

<!--- ATTORNEYS ************************************************************** --->
<cffunction name="getAttorney" returntype="query" output="No">
	<cfargument name="attorneyID" type="numeric" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
		SELECT *
		FROM dbo.OH_VS_Attorneys
		WHERE attorneyID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.attorneyID#">
	</cfquery>
	<cfreturn local.qry />
</cffunction>
<cffunction name="attorneyConstruct" returntype="struct" output="No">
	<cfargument name="attorneyID" type="numeric" required="yes">
	<cfset var local = structNew()>
	<cfscript>
		local.data.attorneyID		= 0;
		local.data.firstName	= '';
		local.data.middleName	= '';
		local.data.lastName	= '';
		local.data.firm	= '';
		local.data.phone	= '';
		local.data.website	= '';
		local.qrydata = getAttorney(arguments.attorneyID);
		if( local.qrydata.recordCount ){
			local.data.attorneyID		= local.qrydata.attorneyID;
			local.data.firstName	= local.qrydata.firstName;
			local.data.middleName	= local.qrydata.middleName;
			local.data.lastName	 	= local.qrydata.lastName;
			local.data.firm	 			= local.qrydata.firm;
			local.data.phone 			= local.qrydata.phone;
			local.data.website		= local.qrydata.website;
		}
	</cfscript>
	<cfreturn local.data />
</cffunction>
<cffunction name="insertAttorney" returntype="numeric" output="No">
	<cfargument name="firstName" type="string" required="yes">
	<cfargument name="middleName" type="string" required="yes">
	<cfargument name="lastName" type="string" required="yes">
	<cfargument name="firm" type="string" required="yes">
	<cfargument name="phone" type="string" required="yes">
	<cfargument name="website" type="string" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
		set nocount on
		INSERT INTO dbo.OH_VS_Attorneys(
			middleName,
			firm,
			phone,
			website,
			firstName,
			lastName
		)
		Values(
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.middleName#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firm#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.phone#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.website#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firstName#">,
			<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lastName#">
		)
		
		select SCOPE_IDENTITY() as attorneyID
	</cfquery>
	<cfreturn local.qry.attorneyID />
</cffunction>
<cffunction name="updateAttorney" returntype="void" output="No">
	<cfargument name="attorneyID" type="numeric" required="yes">
	<cfargument name="firstName" type="string" required="yes">
	<cfargument name="middleName" type="string" required="yes">
	<cfargument name="lastName" type="string" required="yes">
	<cfargument name="firm" type="string" required="yes">
	<cfargument name="phone" type="string" required="yes">
	<cfargument name="website" type="string" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">		
		UPDATE dbo.OH_VS_Attorneys
			set 
			middleName 		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.middleName#">,
			firm 					= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firm#">,
			phone					= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.phone#">,
			website				= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.website#">,
			firstName 		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firstName#">,
			lastName 			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lastName#">			
		WHERE attorneyID 	= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.attorneyID#">
	</cfquery>
</cffunction>
<cffunction name="deleteAttorney" returntype="void" output="No">
	<cfargument name="attorneyID" type="numeric" required="yes">
	<cfset var local = structNew() />
	<cfif NOT checkAttorneyForVerdicts(arguments.attorneyID)>
		<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">		
			DELETE FROM dbo.OH_VS_Attorneys
			WHERE attorneyID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.attorneyID#">
		</cfquery>
	</cfif>
</cffunction>
<cffunction name="getAttorneys" returntype="query" output="No">
	<cfargument name="attorneyIDs" type="string" required="false" default="0">
	<cfscript>
		var local = structNew();
		
	</cfscript>
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
		SELECT *
		FROM dbo.OH_VS_Attorneys
		<cfif arguments.attorneyIDs NEQ 0>
		WHERE attorneyID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.attorneyIDs#" list="true">)
		</cfif>
		Order by lastName,firstName,MiddleName
	</cfquery>
	<cfreturn local.qry />
</cffunction>
<cffunction name="checkAttorneyForVerdicts" returntype="boolean" output="No">
	<cfargument name="attorneyID" type="numeric" required="yes">
	<cfset var local = structNew() />
	<cfset local.data = FALSE />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">		
		SELECT verdictID
		FROM dbo.OH_VS_VerdictsAttorneys
		WHERE attorneyID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.attorneyID#">
	</cfquery>
	<cfif local.qry.recordCount>
		<cfset local.data = True />
	</cfif>
	<cfreturn local.data />
</cffunction>
<cffunction name="getVerdictAttorneys" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfargument name="isPlaintiff" type="boolean" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
		SELECT a.AttorneyID, atty.firstName,atty.MiddleName,atty.lastName, atty.Firm, atty.Phone, atty.Website
		FROM dbo.OH_VS_VerdictsAttorneys a
		INNER JOIN dbo.OH_VS_Attorneys atty ON a.AttorneyID = atty.AttorneyID
		WHERE a.verdictID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.verdictID#">
		AND isPlaintiffAttorney = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPlaintiff#">
		ORDER BY atty.lastName, atty.firstName
	</cfquery>
	<cfreturn local.qry />
</cffunction>
<cffunction name="getVerdictExperts" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfargument name="isPlaintiff" type="boolean" required="yes">
	<cfset var local = structNew() />
	<cfquery name="local.qry" datasource="#application.dsn.customApps.dsn#">
			select * 
			from  dbo.OH_VS_VerdictsExperts vsve
			inner join dbo.OH_VS_Experts vse on vse.expertID = vsve.expertID
				and vsve.verdictID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.verdictID#">
			where vsve.isPlaintiffExpert = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPlaintiff#">
			order by vse.lastname, vse.firstname, vsve.isPlaintiffExpert
	</cfquery>
	<cfreturn local.qry />
</cffunction>
<cffunction name="getVerdict" returntype="struct" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfset var local = StructNew()>
	
	<cfquery name="local.qryVerdict" datasource="#application.dsn.customApps.dsn#">
		select * 
		from dbo.OH_VS_Verdicts v
		inner join dbo.OH_VS_ActionTypes vsat on vsat.actionID = v.actionTypeID
		inner join dbo.OH_VS_ResolutionTypes vsrt on vsrt.resolutionID = v.resolutionTypeID
		left outer join dbo.OH_VS_Counties ct on ct.countyID = v.countyID		
		left outer join dbo.OH_VS_Courts c on c.courtID = v.courtID		
		left outer join dbo.OH_VS_Judges j on j.judgeID = v.judgeID		
		left outer join dbo.OH_VS_VerdictsInsuranceCos vsvic
			inner join dbo.OH_VS_InsuranceCos vsic on vsic.insuranceCoID = vsvic.InsuranceCoID
			on vsvic.verdictID = v.verdictID
		
		left outer join dbo.OH_VS_VerdictsExperts vsve
			inner join dbo.OH_VS_Experts vse on vse.expertID = vsve.expertID
			on vsve.verdictID = v.verdictID
			
		where v.verdictid = <cfqueryparam value="#arguments.verdictID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfset local.verdictValues = structNew()>
	<cfloop index="local.thisField" list="#local.qryVerdict.columnList#">
		<cfset local.verdictValues[local.thisField] = local.qryVerdict[local.thisField]/>
	</cfloop>
	<cfif local.qryVerdict.recordCount gt 0>
		<cfset local.verdictValues.plaintiffAttys = getVerdictAttorneys(arguments.verdictID, true) />
		<cfset local.verdictValues.paIDs		= valueList(local.verdictValues.plaintiffAttys.attorneyID) />
		<cfset local.verdictValues.defenseAttys = getVerdictAttorneys(arguments.verdictID, false) />
		<cfset local.verdictValues.daIDs		= valueList(local.verdictValues.defenseAttys.attorneyID) />
		<cfset local.verdictValues.pExperts = getVerdictExperts(arguments.verdictID, true) />
		<cfset local.verdictValues.pExpertIDs		= valueList(local.verdictValues.pExperts.expertID) />
		<cfset local.verdictValues.dExperts = getVerdictExperts(arguments.verdictID, false) />
		<cfset local.verdictValues.dExpertIDs		= valueList(local.verdictValues.dExperts.expertID) />

	<cfelse>
		<cfset local.verdictValues.paIDs = 0 />
		<cfset local.verdictValues.daIDs = 0 />
		<cfset local.verdictValues.pExpertIDs = 0 />
		<cfset local.verdictValues.dExpertIDs = 0 />
	</cfif>

	<cfreturn local.verdictValues>
</cffunction>
<cffunction name="buildJSListenerScript" returntype="string" output="No">
	<cfset var local = structNew() />
	<cfsavecontent variable="local.data">
		<cfoutput>
			<script type="text/javascript">
				var changes = false;
				function formHasChanges() {
					if (!changes) {
						changes = true;
						document.getElementById('saveMsg').innerHTML = 'Changes have been made since last saved.';
						document.getElementById('saveMsg').className = 'ldbChanges';
						document.getElementById('saveBtn').className = 'needSave';
					}			
				}
				
				function attachEvent(evt,element,cb) { 
					if (element.addEventListener) element.addEventListener(evt,cb,false); 
					else if (element.attachEvent) element.attachEvent('on' + evt, cb); 
				}
				 
				function setListener(evt,func) { 
					var ele = document.forms['ldbForm'].elements; 
					for (var i=0; i<ele.length;i++) { 
						var element = ele[i]; 
						if (element.type) { 
							if( element.id == "noCheck" ){}
							else{
								switch (element.type) { 
									case 'checkbox': 
									case 'radio': 
									case 'password': 
									case 'text': 
									case 'textarea': 
									case 'select-one': 
									case 'select-multiple': 
									case 'file':
										attachEvent(evt,element,func);
								}
							}
						} 
					} 
				}
			</script>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.data />
</cffunction>

<!--- 


SITE ADMIN ONLY
*** 	attorneys button
*** 	expert button
*** 	Delete button
*** 	Approved Status
*** 	Edit button

*** Add is Members 


*** 1. "OH Members & Staff" may add verdicts & settlements

*** 2. On Screen Confirmation to contributor:
*** "Thank you for submitting your recent case to the Verdict & Settlement Reporter. Once approved your case will appear in the interactive verdict and settlement database online. OAJ truly appreciates your submission and your help in growing our verdict and settlement database. We welcome any and all future verdicts or settlements that you may have. Thank you again."

3. Contributors should receive a copy of their entry by email.  
		Please grab the member's email address (if present) from the database to send the notification.  
		Also, please notate them as the verdict or settlement contributor.

4. Please send a <NAME_EMAIL> each time a new entry is queued up for review.

5. Please ensure that the 'approve' feature is enabled and effective for OAJ's V & S database.


 --->