<cfoutput>
<div id="#local.uniqueID#">
	<div class="bk-d-flex bk-p-2 bk-mb-2 bk-border-gray" style="border:1px solid;">
		<span>
			<cfif local.returnStruct.totalCount GT local.returnStruct.count>
				Showing #local.returnStruct.startPos#-<cfif ((local.returnStruct.startPos + local.returnStruct.count) - 1) lte local.returnStruct.totalCount>#local.returnStruct.startPos + local.returnStruct.count - 1#<cfelse>#local.returnStruct.totalCount#</cfif> of #NumberFormat(local.returnStruct.totalCount,",")# Depositions
			<cfelse>
				Showing #local.returnStruct.totalCount# Deposition<cfif local.returnStruct.totalCount GT 1>s</cfif>
			</cfif>
		</span>
		<span class="bk-ml-auto">
			<table class="tsAppBodyText">
				<tr class="bk-font-size-sm">
				<cfif local.returnStruct.numCurrentPage GT 1>
					<td class="bk-font-size-sm">
						<button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(#local.returnStruct.startPos - local.returnStruct.count#,this);return false;">Previous</button>
					</td>
				</cfif>
				<cfif local.returnStruct.numCurrentPage NEQ local.returnStruct.endPage>
					<td class="bk-font-size-sm">
						<button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(#local.returnStruct.startPos + local.returnStruct.count#,this);return false;">Next</button>
					</td>
				</cfif>
				</tr>
			</table>
		</span>
	</div>

	<cfloop query="local.qryDepositions">
		<cfset local.objDocument.load(local.qryDepositions.documentid)>
		<cfset local.mycost = local.objDocument.getPrice(membertype=variables.cfcuser_membertype, billingstate=variables.cfcuser_billingstate,
				billingzip=variables.cfcuser_billingzip, websiteorgcode=session.mcstruct.sitecode, depomemberdataid=variables.cfcuser_depomemberdataid)>

		<cfset local.inCartStyle = local.qryDepositions.inCart EQ 1 ? "" : "display:none;">
		<cfset local.addToCartStyle = local.qryDepositions.inCart EQ 1 ? "display:none;" : "">

		<div class="tsDocViewerDocument s_row bk-p-2 bk-mb-2 bk-border-gray" style="border:1px solid;" data-tsdocumentid="#local.qryDepositions.documentid#">
			<div class="bk-d-flex">
				<a href="javascript:docViewer(#local.qryDepositions.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchID#);" title="Click to view document"><img src="#application.paths.images.url#images/search/preview_icon.png" width="20" height="20" border="0" align="left" /><b> <cfif len(trim(expertname))>#trim(expertname)#<cfelse>Unknown Expert</cfif></b>  <small>(Click to Preview)</small></a>
				<div class="bk-ml-auto">
					<div class="bk-p-3" style="#local.inCartStyle#" id="s_incart#local.qryDepositions.documentID#">
						<a href="javascript:viewCart();" title="View your cart/checkout"><i class="icon-shopping-cart icon-large"></i> In Cart</a>
					</div>
					<cfif not local.qryDepositions.owned>
						<div class="bk-p-3" id="s_addtocart#local.qryDepositions.documentID#" style="#local.addToCartStyle#">
							<a href="javascript:addToCart(#local.qryDepositions.documentid#,#arguments.searchID#);" title="Buy Document">
								<i class="icon-shopping-cart icon-large hidden-phone"></i> Buy (#replace(dollarformat(local.mycost.price),".00","")#)
							</a>
						</div>
					<cfelse>
						<div class="bk-p-3">
							<a href="javascript:downloadDoc(#local.qryDepositions.documentid#);" title="Download Document"><i class="icon-download icon-large"></i> Download</a>
						</div>
					</cfif>
				</div>
			</div>
			<div class="bk_subContent">
				Document ###local.qryDepositions.documentid# - #DateFormat(local.qryDepositions.DocumentDate, "mmm d, yyyy")# - #local.qryDepositions.jurisdiction# <cfif local.qryDepositions.pages gt 0>- #local.qryDepositions.pages# pages</cfif><br/>
				#local.qryDepositions.style# ... <cfif len(local.qryDepositions.causedesc)>... #local.qryDepositions.causedesc#</cfif>
				<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryDepositions.owned>
					<cfif len(local.qryDepositions.notes)><br/>Notes: #local.qryDepositions.notes#</cfif>
					<br/>Contributed by: <cfif len(trim(local.qryDepositions.email))><a href="mailto:#trim(local.qryDepositions.email)#"></cfif>#local.qryDepositions.FirstName# #local.qryDepositions.LastName#<cfif len(trim(local.qryDepositions.email))></a></cfif>, 
					<cfset local.tmp = "">
					<cfif len(local.qryDepositions.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryDepositions.billingfirm)></cfif>
					<cfif len(local.qryDepositions.phone)><cfset local.tmp = listAppend(local.tmp,"Ph: " & local.qryDepositions.phone)></cfif>
					#replace(local.tmp,",",", ","ALL")#<br/>
				<cfelse>
					<br/>Notes: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
					<br/>Contributed by: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
				</cfif>
			</div>
		</div>
	</cfloop>

	<cfif local.returnStruct.numTotalPages gt 1>
		<table class="tsAppBodyText" style="margin:0 auto;">
			<tr>
			<cfif local.returnStruct.numCurrentPage gt 1>
				<cfif local.returnStruct.numTotalPages GT local.returnStruct.maxPage>
					<td>
						<button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(1,this);return false;" title="First Page">&lt;&lt;</button>
					</td>
				</cfif>
				<td>
					<button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(#local.returnStruct.startPos - local.returnStruct.count#,this);return false;" title="Previous Page">Previous</button>
				</td>
			</cfif>

			<cfloop from="#local.returnStruct.startPage#" to="#local.returnStruct.endPage#" index="local.i">
				<cfset local.thisStartRow = local.i>
				<cfif local.i gt 1>
					<cfset local.thisStartRow = (local.returnStruct.count * (local.i-1)) + 1>
				</cfif>
				
				<cfif local.returnStruct.numCurrentPage eq local.i>
					<td class="active"><button type="button" onclick="return false;">#local.i#</button></td>
				<cfelse>
					<td><button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(#local.thisStartRow#,this);return false;">#local.i#</button></td>
				</cfif>
			</cfloop>
			
			<cfif local.returnStruct.numCurrentPage lt local.returnStruct.numTotalPages>
				<td>
					<button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(#local.returnStruct.startPos + local.returnStruct.count#,this);return false;" title="Next Page">
						Next
					</button>
				</td>
				<cfif local.returnStruct.numTotalPages GT local.returnStruct.maxPage>
					<td>
						<button type="button" class="tsAppBodyButton" onclick="b_getExpertDepositions(#(local.returnStruct.count * (local.returnStruct.numTotalPages-1)) + 1#,this);return false;" title="Last Page">
							&gt;&gt;
						</button>
					</td>
				</cfif>
			</cfif>
			</tr>
		</table>
	</cfif>
</div>
<div class="bk-text-right">
	<button type="button" class="tsAppBodyButton" onclick="bk_slideToggle('#local.uniqueID#','MCExpertDepositionsContainer',this)">
		<span class="mc-arrow-up">Show Less</span><span class="mc-arrow-down" style="display:none;">Expand</span>
	</button>
</div>
</cfoutput>