<cfset local.strData = attributes.data>
<cfset local.extrapayJS = "">

<cfinclude template="../commonSWRegCartJS.cfm">
<cfinclude template="swCatalogcommonCSS.cfm">
<cfinclude template="../commonSWRegStyles.cfm">

<cfoutput>
<div class="swCatalog swCatalogCart">
	<div id="swHeaderDiv">
		<cfinclude template="/views/semwebCatalog/responsive/swPanelHeaderSectionCommon.cfm">
	</div>
	
	<cfif local.strData.swRegCart.recordcount>
		<cfif local.strData.hasSavedProgramsNotInCart>
			<div class="alert alert-info">
				You have saved programs you may wish to add to your cart. <a href="#local.strData.semweb.mainurl#&panel=browse&_swis=1">Click here</a> to view them.
			</div>
		</cfif>
		<div class="swReg">
			<div class="swreg-d-flex">
				<cfif local.strData.showPaymentArea>
					<div class="swreg-font-size-lg swreg-col swreg-p-0">To complete this registration, enter your payment information below.</div>
				<cfelse>
					<div class="swreg-font-size-lg swreg-col swreg-p-0 swreg-text-danger swreg-font-weight-bold">No Payment is due for the registration(s). Click "Complete Registration" below.</div>
				</cfif>
				<div class="swreg-font-size-sm swreg-col-auto swreg-text-right hidden-phone">
					<a href="#local.strData.semweb.mainurl#&panel=browse" style="border-bottom:1px solid;text-decoration:none;">+ Register for Other Programs</a>
				</div>
			</div>
			<div class="swreg-mt-2 swreg-font-size-sm visible-phone">
				<a href="#local.strData.semweb.mainurl#&panel=browse" style="border-bottom:1px solid;text-decoration:none;">+ Register for Other Programs</a>
			</div>
			<div class="swreg-d-flex swreg-mt-4 swreg-pt-3">
				<div class="swreg-font-size-xl swreg-font-weight-bold">Registration(s)</div>
				<div class="swreg-ml-auto swreg-pr-2">
					<div id="swRegCartTotalSummary" class="swRegCartTotals">
						<cfif val(local.strData.swRegCartTotals.totalDiscount) gt 0>
							<div class="swreg-card" style="box-shadow:none;border:none;background-color:##f7f7f7;">
								<div class="swreg-card-body">
									<div class="swreg-d-flex swreg-font-weight-bold swreg-font-size-xl">
										<div class="swreg-col">Total:</div>
										<div class="swreg-ml-auto">#DollarFormat(local.strData.swRegCartTotals.actualTotal)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif></div>
									</div>
	
									<div class="swreg-text-right swreg-font-size-sm">
										<div class="swreg-strike">#DollarFormat(local.strData.swRegCartTotals.totalAmt)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif></div>
										<div>#DollarFormat(local.strData.swRegCartTotals.totalDiscount)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif> discount applied</div>
									</div>
									<div class="alert alert-success swreg-font-size-sm swreg-mb-0 swreg-text-center swreg-p-1" style="max-width:200px;">#local.strData.swRegCartTotals.redeemDetail#</div>
									<div class="swreg-mt-2 swreg-text-right">
										<button type="button" name="btnRemoveCoupon" class="btn btn-small btn-warning btnRemoveCoupon" onclick="removeAppliedCoupon();">Remove Promo Code</button>
									</div>
								</div>
							</div>
						<cfelse>
							<div class="swreg-d-flex swreg-font-weight-bold swreg-font-size-xl">
								<div class="swreg-col swreg-text-center">Total:</div>
								<div class="swreg-ml-auto">#DollarFormat(local.strData.swRegCartTotals.totalAmt)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif></div>
							</div>
						</cfif>
					</div>
					
					<cfif local.strData.hasAmountToCharge AND local.strData.offerCoupon>
						<div class="swreg-mt-3 swreg-text-right">
							<div class="swreg-input-append">
								<input type="text" name="couponCode" id="couponCode" class="swreg-formcontrol" value="" size="18" value="" placeholder="Promo Code" maxlength="15" onkeypress="validateCouponCodeOnEnterKey(event);">
								<button type="button" name="btnApplyCouponCode" id="btnApplyCouponCode" class="swreg-add-on swreg-font-size-md" onclick="validateCouponCode();">Apply</button>
							</div>
							<div id="couponCodeResponse" class="alert alert-error swreg-p-1 swreg-mt-1 swreg-font-size-sm swreg-mb-0 swreg-text-center" style="display:none;"></div>
						</div>
					</cfif>
				</div>
			</div>
	
			<cfoutput query="local.strData.qrySWRegCartSorted" group="memberID">
				<div class="swreg-card swreg-mt-3">
					<div class="swreg-card-header swreg-bg-whitesmoke swreg-pb-1">
						<div class="swreg-font-size-lg swreg-font-weight-bold">#local.strData.qrySWRegCartSorted.memberName#</div>
					</div>
					<div class="swreg-card-body">
						<cfset local.thisRowNum = 0>
						<cfoutput>
							<cfset local.thisRowNum++>
							<div id="swRegKey#local.strData.qrySWRegCartSorted.itemKey#" class="swreg-d-flex swreg-mb-3 swRegCartItem">
								<div class="swreg-col-auto swreg-font-weight-bold">#local.thisRowNum#.</div>
								<div class="swreg-col">
									<div class="swreg-font-weight-bold">
										#encodeForHTML(local.strData.qrySWRegCartSorted.programName)# <span class="hidden-phone">(#local.strData.qrySWRegCartSorted.ratename#)</span>
									</div>
									<cfif len(local.strData.qrySWRegCartSorted.programSubTitle)><div class="swreg-font-size-sm swreg-text-dim">#encodeForHTML(local.strData.qrySWRegCartSorted.programSubTitle)#</div></cfif>
									<cfif GetToken(local.strData.qrySWRegCartSorted.item,1,"-") eq "SWB">
										<cfset local.arrRegCart = local.strData.swReg?.regCart ?: []>
										<cfset thisProgramKey = local.strData.qrySWRegCartSorted.item>
										<cfset local.currentCartItem = arrayFilter(local.arrRegCart, function(cartItem) {
											return cartItem.item eq thisProgramKey;
										})>
										<cfif arrayLen(local.currentCartItem)>
											<cfset local.strChildPrograms = arrayFirst(local.currentCartItem)?.s2?.strChildPrograms ?: {}>
											<cfif structCount(local.strChildPrograms)>
												<div class="swreg-text-dim swreg-mt-2 swreg-mb-1">Programs included in bundle:</div>
												<ol>
													<cfset local.itemsrowNum = 1>
													<cfloop collection="#local.strChildPrograms#" item="local.thisProgramID">
														<li>#local.strChildPrograms[local.thisProgramID]#</li>
														<cfset local.itemsrowNum++>
													</cfloop>
												</ol>
											</cfif>
										</cfif>
									</cfif>
									<cfif local.strData.qrySWP.handlesOwnPayment is 1>
										<div class="swreg-font-size-sm swreg-mt-2">
											<a href="##" style="border-bottom:1px solid;text-decoration:none;" onclick="registerOthers('#local.strData.qrySWRegCartSorted.item#');">+ Register Someone Else</a>
										</div>
									</cfif>
									<div class="swRegCartItemResponse alert alert-info swreg-mt-2 swreg-mb-0 swreg-p-1" style="display:none;"></div>
									<cfif val(local.strData.qrySWRegCartSorted.isRegistered)>
										<div class="alert alert-error">#local.strData.qrySWRegCartSorted.memberName# is already registered for this program. Remove this registration by clicking the trash icon.</div>
									</cfif>
								</div>
								<div class="swreg-col-auto swreg-ml-auto swreg-w-30 swreg-lg-w-25">
									<div class="swreg-d-flex swreg-flex-sm-column">
										<div class="swreg-sm-col-auto swreg-text-right swreg-sm-text-center swreg-w-30 ">
											<a href="##" id="edit_swrk_#local.strData.qrySWRegCartSorted.itemKey#" onclick="editReg('#local.strData.qrySWRegCartSorted.item#','#local.strData.qrySWRegCartSorted.itemKey#');return false;" class="swreg-mr-2 swreg-text-decoration-none" title="Edit Registration">
												<i class="icon-pencil"></i>
											</a>
											<a href="##" id="del_swrk_#local.strData.qrySWRegCartSorted.itemKey#" onclick="removeReg('#local.strData.qrySWRegCartSorted.itemKey#');return false;" class="swreg-text-decoration-none" title="Remove Pending Registration">
												<i class="icon-trash"></i>
											</a>
										</div>
										<div class="swreg-col swRegCartItemTotal swreg-mt-sm-2">
											<cfif val(local.strData.qrySWRegCartSorted.discount) gt 0>
												<div class="swreg-font-weight-bold">
													<div class="swreg-font-size-lg">#DollarFormat(local.strData.qrySWRegCartSorted.actualAmount)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif></div>
													<div class="swreg-strike swreg-font-size-sm swreg-text-center">#DollarFormat(local.strData.qrySWRegCartSorted.amount)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif></div>
												</div>
											<cfelse>
												<div class="swreg-font-weight-bold swreg-font-size-lg swreg-text-right">#DollarFormat(local.strData.qrySWRegCartSorted.amount)#<cfif local.strData.qrySWP.handlesOwnPayment EQ 0> USD<cfelse>#local.strData.displayedCurrencyType#</cfif></div>
											</cfif>
										</div>
									</div>
								</div>
							</div>
						</cfoutput>
					</div>
					<cfif local.strData.qrySWP.handlesOwnPayment EQ 0>
						<cfinclude template="../SWRegECommercePolicy.cfm">
					</cfif>
				</div>
			</cfoutput>
	
			<cfif local.strData.showPaymentArea>
				<div class="swreg-card swreg-mt-3">
					<div class="swreg-card-header swreg-bg-whitesmoke swreg-pb-1">
						<div class="swreg-font-size-lg swreg-font-weight-bold">Payment Information</div>
					</div>
					<div class="swreg-card-body swreg-pb-2<cfif NOT local.strData.qrySWP.handlesOwnPayment OR local.strData.paymentGateways.recordcount EQ 1> swreg-pl-0</cfif>">
						<form name="frmPurchaseSWReg" id="frmPurchaseSWReg" method="post" action="#local.strData.semweb.mainurl#&panel=addSWReg" onsubmit="return checkPayForm();">
							<input type="hidden" name="confirmPurchase" id="confirmPurchase" value="1">
							<input type="hidden" name="profileid" id="profileid" value="0">
							
							<div id="paymentTabsWrapper">
								<!--- area for payment error --->
								<div id="swRegPmtErr" style="display:none;margin:6px;"></div>
								
								<div id="paymentTabs">
									<cfif local.strData.paymentGateways.recordcount gt 1>
										<div class="swreg-mb-3 swreg-text-dim"><b>Choose from the following payment methods:</b></div>
	
										<ul id="swRegPmtTabs" class="nav nav-tabs">
											<cfoutput query="local.strData.paymentGateways">
												<li<cfif local.strData.paymentGateways.currentRow eq 1> class="active"</cfif>><a href="##profile#local.strData.paymentGateways.profileID#" data-toggle="tab">#local.strData.paymentGateways.tabTitle#</a></li>
											</cfoutput>
										</ul>
										<div class="tab-content">
											<cfinclude template="SWRegCartPayment.cfm">
										</div>
									<cfelse>
										<div id="paymentTypeTabs">
											<cfinclude template="SWRegCartPayment.cfm">
										</div>
									</cfif>
								</div>
							</div>
						</form>
					</div>
				</div>
			<cfelse>
				<div class="swreg-mt-5">
					<cfset local.thisProfileID = local.strData.qrySWP.handlesOwnPayment EQ 1 ? local.strData.paymentGateways.profileID[1] : 0>
					<form name="frmPurchaseSWReg" id="frmPurchaseSWReg" method="post" action="#local.strData.semweb.mainurl#&panel=addSWReg" onsubmit="return checkPayForm();">
						<input type="hidden" name="confirmPurchase" id="confirmPurchase" value="1">
						<input type="hidden" name="profileid" id="profileid" value="0">
						<input type="hidden" name="p_#local.thisProfileID#_mppid"  id="p_#local.thisProfileID#_mppid" value="0">
						<button type="submit" class="btn btn-success" onclick="selectPayment(#local.thisProfileID#)">Complete Registration</button>
					</form>
				</div>
			</cfif>
		</div>
	<cfelse>
		No Pending Registrations Found.
	</cfif>
</div>
</cfoutput>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<style>
		/* stacked tabs styling for screen below 576px */
		@media only screen and (max-width:576px) {
			##paymentTabs > ul.nav-tabs {border-bottom: 0;margin-bottom:0;}
			##paymentTabs > ul.nav-tabs > li {float: none;}
			##paymentTabs > ul.nav-tabs > li > a {margin-right:0;border:1px solid ##ddd;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}
			##paymentTabs > ul.nav-tabs > li:last-child > a {margin-bottom:1px}	
			##paymentTabs > ul.nav-tabs  > li.active a {background-color:##eee!important; }
			##paymentTabs .tab-content {border: 1px solid ##ccc;padding:0.5em;}
		}
	</style>
	<script language="javascript">
	var checkPayFormInProgress = false;
	function checkPayForm() {

		/* disable payment buttons while validation is running */
		$('button[type="submit"]',$('##frmPurchaseSWReg')).each(function(index,thisButton){
			$(thisButton).attr('disabled','disabled');
		});
	
		var validationPassed = true;

		/* prevent race condition caused by double submitting before validation can be completed */
		if (checkPayFormInProgress) {
			validationPassed = false;
		} else {
			checkPayFormInProgress = true;
			hideAlert();
			var arrReq = new Array();

			<cfif len(local.extrapayJS)>
				var thisForm = document.forms["frmPurchaseSWReg"];
				#local.extrapayJS#
			</cfif>
			
			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				validationPassed = false;
			}

			if (validationPassed) {
				/* change text of payment buttons and leave disabled */
				$('button[type="submit"]',$('##frmPurchaseSWReg')).each(function(index,thisButton){
					$(thisButton).text('Please Wait...');
				});
			} else {
				/* reenable buttons */
				$('button[type="submit"]',$('##frmPurchaseSWReg')).each(function(index,thisButton){
					$(thisButton).removeAttr("disabled");
				});
			}
			checkPayFormInProgress = false;
		}

		if (validationPassed) $('##frmPurchaseSWReg').data('swregformsubmitted', true);

		return validationPassed;
		
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">