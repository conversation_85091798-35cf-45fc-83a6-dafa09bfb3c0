<cfset maxrows = 25>

<cfset basequerystring = "">
<cfset paramsToRemove = "verdictid,editverdictid,showfullpage,addverdict">
<cfloop collection="#url#" item="thisItem">
	<cfif not listfindnocase(paramsToRemove,thisItem)>
		<cfif len(basequerystring) gt 0>
			<cfset basequerystring = basequerystring & "&#thisItem#=#url[thisitem]#">
		<cfelse>
			<cfset basequerystring = "#thisItem#=#url[thisitem]#">
		</cfif>
	</cfif>
</cfloop>

<cfsavecontent variable="disableJavascript">
	<cfoutput>
	<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
	   <!--
	   function changeAvailability(formfieldid,disableflag){
			document.getElementById(formfieldid).disabled = disableflag;
			if (disableflag) document.getElementById(formfieldid).value='disabled';
			else document.getElementById(formfieldid).value='';
	   }  
	   //-->
   </SCRIPT>
	</cfoutput>
</cfsavecontent>

<cfsavecontent variable="variables.JS">
	<cfoutput>
	<style type="text/css">
		##date { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
	</style>			

	<script language="JavaScript" type="text/javascript">
	$(document).ready(function(){
		mca_setupDatePickerField('date');
	});				
	</script>
	</cfoutput>	
</cfsavecontent>
<cfhtmlhead text="#variables.JS#">

<cfif isdefined("url.addverdict")>
<!--- add verdict --->
	<cfquery name="casetypes" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT casetype
		FROM         NE_verdicts
		WHERE casetype IS NOT NULL AND casetype <> ''
		ORDER BY casetype
	</cfquery>
	<cfquery name="resolutiontypes" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT resolutiontype
		FROM         NE_verdicts
		WHERE resolutiontype IS NOT NULL AND resolutiontype <> ''
		ORDER BY resolutiontype
	</cfquery>
	
	<cfquery name="countynames" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT countyname
		FROM         NE_verdicts
		WHERE countyname IS NOT NULL AND countyname <> ''
		ORDER BY countyname
	</cfquery>
	<cfquery name="courtnames" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT courtname
		FROM         NE_verdicts
		WHERE courtname IS NOT NULL AND courtname <> ''
		ORDER BY courtname
	</cfquery>
	



<cfoutput>	<br />
	<p class="headertext">Add Verdict or Settlement</p>
</cfoutput>
	<cfif not application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfoutput><p>You do not have rights to add verdicts to this database.</p></cfoutput>
	<cfelse>
	
	<cfoutput>
		<cfhtmlhead text="#disableJavascript#">
	
			<cfform name="verdictForm"  id="verdictForm" action="/?#basequerystring#&verdictid=0" method="post">
				<input type="hidden" name="insertVerdict" value="1" />
				<table border="0" cellpadding="2">
					<tr>
						<td><input type="submit" value="Save Verdict" class="bodytext" /></td>
						<td><input type="button" value="Cancel" onclick="history.go(-1);" class="bodytext" /></td>
					</tr>
				</table>
	
				<table border="0" class="bodytext" width="95%" cellpadding="2">
					<tr>
						<td width="140" valign="top"><strong>Verdict Number:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="number"  id="number" maxlength="25" size="10"></td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Date:</strong></td>
						<td valign="top">
							<cfinput type="text" name="date" id="date" value="#dateFormat(now(),'mm/dd/yyyy')#">
							<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
						</td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Resolution:</strong></td>
						<td valign="top">
							<select name="resolutiontype" class="bodytext" onchange="changeAvailability('resolutiontypeNew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="resolutiontypes">
									<option value="#resolutiontypes.resolutiontype#">#resolutiontypes.resolutiontype#</option>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="resolutiontypeNew" id="resolutiontypeNew" maxlength="450" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Category:</strong></td>
						<td valign="top">
							<select name="casetypes" class="bodytext" onchange="changeAvailability('casetypeNew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="casetypes">
									<option value="#casetypes.casetype#">#casetypes.casetype#</option>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="casetypeNew" id="casetypeNew" maxlength="450" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Type of Case:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="casetypedetail"  id="casetypedetail" maxlength="500" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Case:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>County:</strong></td>
						<td valign="top">
							<select name="countyname" class="bodytext" onchange="changeAvailability('countynamenew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="countynames">
									<option value="#countynames.countyname#">#countynames.countyname#</option>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="countynameNew" id="countynamenew" maxlength="500" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td valign="top"><strong>Court:</strong></td>
						<td valign="top">
							<select name="courtname" class="bodytext" onchange="changeAvailability('courtnamenew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="courtnames">
									<option value="#courtnames.courtname#">#courtnames.courtname#</option>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="courtnameNew" id="courtnamenew" maxlength="500" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td valign="top"><strong>Docket Number:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="docketnumber"  id="docketnumber" maxlength="25" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Sex:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="sex"  id="sex" maxlength="15" size="5"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Age:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="age"  id="age" maxlength="15" size="5"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Occupation:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="Occupation"  id="Occupation" maxlength="100" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Amount Awarded:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="amount"  id="amount" required="yes" validate="float" message="Please enter a numerical amount in the ""Amount Awarded"" field. Only numerals, commas, and periods allowed." maxlength="100" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Amount Detail:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="amountdetail"  id="amountdetail" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Settlement Offer:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="settlementoffer"  id="settlementoffer" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Facts:</strong></td>
						<td valign="top"><textarea class="bodytext" name="facts" cols="70" rows="10"></textarea></td>
					</tr>
					<tr>
						<td valign="top"><strong>Injuries:</strong></td>
						<td valign="top"><textarea class="bodytext" name="injuries" cols="70" rows="10"></textarea></td>
					</tr>
					<tr>
						<td valign="top"><strong>Medicals:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="medicals"  id="medicals" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Lost Wages:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="lostwages"  id="lostwages" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Coverage:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="coverage"  id="coverage" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Experts:</strong></td>
						<td valign="top"><textarea class="bodytext" name="experts" cols="70" rows="10"></textarea></td>
					</tr>
					<tr>
						<td valign="top"><strong>Plaintiff Attorney:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="PlaintiffAttorney"  id="PlaintiffAttorney" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Defense Attorney:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="DefenseAttorney"  id="DefenseAttorney" maxlength="450" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Submitting Attorney:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="SubmittingAttorney"  id="SubmittingAttorney" maxlength="450" size="70"></td>
					</tr>
				</table>
				<input type="submit" value="Save Verdict" />
			</cfform>
		</cfoutput>
	</cfif>
	<br /><br />
<cfelseif isdefined("url.editverdictid")>
<!--- edit verdict --->
<cfoutput>	<br />
	<p class="headertext">Edit Verdict or Settlement</p>
</cfoutput>
	<cfif not application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfoutput><p>You do not have rights to edit verdicts in this database</p></cfoutput>
	<cfelse>

		<cfquery name="getverdict" datasource="#application.dsn.customApps.dsn#">
			select * from NE_verdicts
			where verdictid= <cfqueryparam value="#url.editverdictid#" cfsqltype="cf_sql_integer">
		</cfquery>
		
		<cfquery name="casetypes" datasource="#application.dsn.customApps.dsn#">
			SELECT DISTINCT casetype
			FROM         NE_verdicts
			WHERE casetype IS NOT NULL AND casetype <> ''
			ORDER BY casetype
		</cfquery>
		<cfquery name="resolutiontypes" datasource="#application.dsn.customApps.dsn#">
			SELECT DISTINCT resolutiontype
			FROM         NE_verdicts
			WHERE resolutiontype IS NOT NULL AND resolutiontype <> ''
			ORDER BY resolutiontype
		</cfquery>
		<cfquery name="countynames" datasource="#application.dsn.customApps.dsn#">
			SELECT DISTINCT countyname
			FROM         NE_verdicts
			WHERE countyname IS NOT NULL AND countyname <> ''
			ORDER BY countyname
		</cfquery>
		<cfquery name="courtnames" datasource="#application.dsn.customApps.dsn#">
			SELECT DISTINCT courtname
			FROM         NE_verdicts
			WHERE courtname IS NOT NULL AND courtname <> ''
			ORDER BY courtname
		</cfquery>

		
		
		
		<cfoutput>
		<cfhtmlhead text="#disableJavascript#">
			<cfform name="verdictForm"  id="verdictForm" action="/?#basequerystring#&verdictid=#getverdict.verdictid#" method="post">
				<input type="hidden" name="verdictid" value="#getverdict.verdictid#" />
				<table border="0" cellpadding="2">
					<tr>
						<td><input type="submit" value="Save Verdict" class="bodytext" /></td>
						<td><input type="button" value="Cancel" onclick="history.go(-1);" class="bodytext" /></td>
					</tr>
				</table>
	
				<table border="0" class="bodytext" width="95%" cellpadding="2">
					<tr>
						<td width="140" valign="top"><strong>Verdict Number:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="number"  id="number" maxlength="25" size="10" value="#getverdict.number#"></td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Date:</strong></td>
						<td valign="top">
							<cfinput type="text" name="date" id="date" value="#dateFormat(getVerdict.date,'mm/dd/yyyy')#">
							<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
						</td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Resolution:</strong></td>
						<td valign="top">
							<select name="resolutiontype" id="resolutiontype" class="bodytext" onchange="changeAvailability('resolutiontypeNew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="resolutiontypes">
									<cfif getverdict.Resolutiontype eq resolutiontypes.resolutiontype>
										<option selected value="#resolutiontypes.resolutiontype#">#resolutiontypes.resolutiontype#</option>
									<cfelse>
										<option value="#resolutiontypes.resolutiontype#">#resolutiontypes.resolutiontype#</option>
									</cfif>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="resolutiontypeNew" id="resolutiontypeNew" maxlength="450" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Category:</strong></td>
						<td valign="top">
							<select name="casetypes" id="casetypes" class="bodytext" onchange="changeAvailability('casetypeNew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="casetypes">
									<cfif getverdict.casetype eq casetypes.casetype>
										<option selected value="#casetypes.casetype#">#casetypes.casetype#</option>
									<cfelse>
										<option value="#casetypes.casetype#">#casetypes.casetype#</option>
									</cfif>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="casetypeNew" id="casetypeNew" maxlength="450" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td width="140" valign="top"><strong>Type of Case:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="casetypedetail"  id="casetypedetail" maxlength="500" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Case:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Court:</strong></td>
						<td valign="top">
							<select name="courtname" id="courtname" class="bodytext" onchange="changeAvailability('courtnamenew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="courtnames">
									<cfif trim(getverdict.courtname) eq trim(courtnames.courtname)>
										<option selected value="#trim(courtnames.courtname)#">#trim(courtnames.courtname)#</option>
									<cfelse>
										<option value="#trim(courtnames.courtname)#">#trim(courtnames.courtname)#</option>
									</cfif>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="courtnameNew" id="courtnamenew" maxlength="500" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td valign="top"><strong>County:</strong></td>
						<td valign="top">
							<select name="countyname" id="countyname" class="bodytext" onchange="changeAvailability('countynamenew',this.value.length);">
								<option value="">Add new entry below or select from list</option>
								<cfloop query="countynames">
									<cfif trim(getverdict.countyname) eq trim(countynames.countyname)>
										<option selected value="#trim(countynames.countyname)#">#trim(countynames.countyname)#</option>
									<cfelse>
										<option value="#trim(countynames.countyname)#">#trim(countynames.countyname)#</option>
									</cfif>
								</cfloop>
							</select>
							<cfinput class="bodytext" type="text" name="countynameNew" id="countynamenew" maxlength="500" size="70">
							<br />
						</td>
					</tr>
					<tr>
						<td valign="top"><strong>Docket Number:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="docketnumber"  id="docketnumber" maxlength="25" size="70" value="#getverdict.docketnumber#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Sex:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="sex"  id="sex" maxlength="15" size="5" value="#getverdict.sex#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Age:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="age"  id="age" maxlength="15" size="5" value="#getverdict.age#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Occupation:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="Occupation"  id="Occupation" maxlength="100" size="70" value="#getverdict.Occupation#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Amount Awarded:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="amount"  id="amount" validate="float" message="Please enter a numerical amount in the ""Amount Awarded"" field. Only numerals, commas, and periods allowed." maxlength="100" size="70" value="#getverdict.amount#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Amount Detail:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="amountdetail"  id="amountdetail" maxlength="450" size="70" value="#getverdict.amountdetail#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Settlement Offer:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="settlementoffer"  id="settlementoffer" maxlength="450" size="70" value="#getverdict.settlementoffer#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Facts:</strong></td>
						<td valign="top"><textarea class="bodytext" name="facts" cols="70" rows="10">#getverdict.facts#</textarea></td>
					</tr>
					<tr>
						<td valign="top"><strong>Injuries:</strong></td>
						<td valign="top"><textarea class="bodytext" name="injuries" cols="70" rows="10">#getverdict.injuries#</textarea></td>
					</tr>
					<tr>
						<td valign="top"><strong>Medicals:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="medicals"  id="medicals" maxlength="450" size="70" value="#getverdict.medicals#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Lost Wages:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="lostwages"  id="lostwages" maxlength="450" size="70" value="#getverdict.lostwages#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Coverage:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="coverage"  id="coverage" maxlength="450" size="70" value="#getverdict.coverage#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Experts:</strong></td>
						<td valign="top"><textarea class="bodytext" name="experts" cols="70" rows="10">#getverdict.experts#</textarea></td>
					</tr>
					<tr>
						<td valign="top"><strong>Plaintiff Attorney:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="PlaintiffAttorney"  id="PlaintiffAttorney" maxlength="450" size="70" value="#getverdict.PlaintiffAttorney#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Defense Attorney:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="DefenseAttorney"  id="DefenseAttorney" maxlength="450" size="70" value="#getverdict.DefenseAttorney#"></td>
					</tr>
					<tr>
						<td valign="top"><strong>Submitting Attorney:</strong></td>
						<td valign="top"><cfinput class="bodytext" type="text" name="SubmittingAttorney"  id="SubmittingAttorney" maxlength="450" size="70" value="#getverdict.SubmittingAttorney#"></td>
					</tr>
				</table>
				<table border="0" cellpadding="2">
					<tr>
						<td><input type="submit" value="Save Verdict" class="bodytext" /></td>
						<td><input type="button" value="Cancel" onclick="history.go(-1);" class="bodytext" /></td>
					</tr>
				</table>
			</cfform>			
			<script language="javascript">
			<!--
				changeAvailability('courtnameNew',document.getElementById('courtname').value.length);
				changeAvailability('countynameNew',document.getElementById('countyname').value.length);
				changeAvailability('casetypeNew',document.getElementById('casetypes').value.length);
				changeAvailability('resolutiontypeNew',document.getElementById('resolutiontype').value.length);
			// -->
			</script>
		</cfoutput>
	</cfif>
	

<cfelseif isdefined("url.verdictid")>
	<!--- Save Verdict if posted --->
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and isdefined("form.insertVerdict")>
		<cfquery name="insertVerdict" datasource="#application.dsn.customApps.dsn#">
			set nocount on
			insert into NE_verdicts 
				(number,
				resolutiontype,
				date,
				amount,
				amountDetail,
				casetype,
				casetypeDetail,
				casetitle,
				courtname,
				countyname,
				docketnumber,
				facts,
				sex,
				age,
				occupation,
				injuries,
				medicals,
				lostWages,
				Coverage,
				SettlementOffer,
				Experts,
				PlaintiffAttorney,
				DefenseAttorney,
				SubmittingAttorney,
				dateLastModified
				)
			values
				('#trim(form.number)#',
				<cfif (len(trim(form.resolutiontype)) eq 0) and (trim(form.resolutiontypeNew) neq "disabled")>
					'#trim(form.resolutiontypeNew)#',
				<cfelse>
					'#trim(form.resolutiontype)#',
				</cfif>
				'#trim(form.date)#',
				'#trim(rereplacenocase(form.amount,"[^0-9.]","","all"))#',
				'#trim(form.amountDetail)#',
				<cfif (len(trim(form.casetypes)) eq 0) and (trim(form.casetypeNew) neq "disabled")>
					'#trim(form.casetypeNew)#',
				<cfelse>
					'#trim(form.casetypes)#',
				</cfif>
				'#trim(form.casetypeDetail)#',
				'#trim(form.casetitle)#',
				<cfif (len(trim(form.courtname)) eq 0) and (trim(form.courtnameNew) neq "disabled")>
					'#trim(form.courtnameNew)#',
				<cfelse>
					'#trim(form.courtname)#',
				</cfif>
				<cfif (len(trim(form.countyname)) eq 0) and (trim(form.countynameNew) neq "disabled")>
					'#trim(form.countynameNew)#',
				<cfelse>
					'#trim(form.countyname)#',
				</cfif>
				'#trim(form.docketnumber)#',
				'#trim(form.facts)#',
				'#trim(form.sex)#',
				'#trim(form.age)#',
				'#trim(form.occupation)#',
				'#trim(form.injuries)#',
				'#trim(form.medicals)#',
				'#trim(form.lostWages)#',
				'#trim(form.Coverage)#',
				'#trim(form.SettlementOffer)#',
				'#trim(form.Experts)#',
				'#trim(form.PlaintiffAttorney)#',
				'#trim(form.DefenseAttorney)#',
				'#trim(form.SubmittingAttorney)#',
				#now()#
				)

			select @@identity as verdictid
			set nocount off
		</cfquery>
		<cfset thisverdictid = insertVerdict.verdictid>
	<cfelseif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and isdefined("form.verdictid") and isnumeric(form.verdictid)>
	
		<cfquery name="updateVerdict" datasource="#application.dsn.customApps.dsn#">
			update NE_verdicts set
				number='#trim(form.number)#',
				<cfif (len(trim(form.resolutiontype)) eq 0) and (trim(form.resolutiontypeNew) neq "disabled")>
					resolutiontype='#trim(form.resolutiontypeNew)#',
				<cfelse>
					resolutiontype='#trim(form.resolutiontype)#',
				</cfif>
				date='#trim(form.date)#',
				amount='#trim(rereplacenocase(form.amount,"[^0-9.]","","all"))#',
				amountDetail='#trim(form.amountDetail)#',
				<cfif (len(trim(form.casetypes)) eq 0) and (trim(form.casetypeNew) neq "disabled")>
					casetype='#trim(form.casetypeNew)#',
				<cfelse>
					casetype='#trim(form.casetypes)#',
				</cfif>
				casetypeDetail='#trim(form.casetypeDetail)#',
				casetitle='#trim(form.casetitle)#',
				<cfif (len(trim(form.courtname)) eq 0) and (trim(form.courtnameNew) neq "disabled")>
					courtname='#trim(form.courtnameNew)#',
				<cfelse>
					courtname='#trim(form.courtname)#',
				</cfif>
				<cfif (len(trim(form.countyname)) eq 0) and (trim(form.countynameNew) neq "disabled")>
					countyname='#trim(form.countynameNew)#',
				<cfelse>
					countyname='#trim(form.countyname)#',
				</cfif>
				docketnumber='#trim(form.docketnumber)#',
				facts='#trim(form.facts)#',
				sex='#trim(form.sex)#',
				age='#trim(form.age)#',
				occupation='#trim(form.occupation)#',
				injuries='#trim(form.injuries)#',
				medicals='#trim(form.medicals)#',
				lostWages='#trim(form.lostWages)#',
				Coverage='#trim(form.Coverage)#',
				SettlementOffer='#trim(form.SettlementOffer)#',
				Experts='#trim(form.Experts)#',
				PlaintiffAttorney='#trim(form.PlaintiffAttorney)#',
				DefenseAttorney='#trim(form.DefenseAttorney)#',
				SubmittingAttorney='#trim(form.SubmittingAttorney)#',
				dateLastModified = #now()#
			where verdictid = <cfqueryparam value="#trim(form.verdictid)#" cfsqltype="cf_sql_integer">
		</cfquery>
		<cfset thisverdictid = form.verdictid>
	<cfelse>
		<cfset thisverdictid = url.verdictid>
	</cfif>
	<cfquery name="getverdict" datasource="#application.dsn.customApps.dsn#">
		select * from NE_verdicts
		where verdictid= <cfqueryparam value="#thisverdictid#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfoutput>
	<br />
	<p class="headertext">Report Details</p>
	<table border="0" cellpadding="2">
		<tr>
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<td><input type="button" onclick="document.location.href='/?#baseQueryString#&editverdictid=#thisverdictid#';" value="Edit Verdict" class="bodytext" /></td>
			</cfif>
			<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
				<cfform action="/?#baseQueryString#" method="post">
					<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
						<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#session.lastverdictsearch[form_element]#">
					</CFLOOP>
					<td><input type="submit" value="Return to Results" class="bodytext"/></td>
				</cfform>
			</cfif>
			<td><input type="button" onclick="document.location.href='/?#baseQueryString#';" value="New Search" class="bodytext" /></td>
		</tr>
	</table>
	<table border="0" class="bodytext" width="80%" cellpadding="2">
		<tr>
			<td valign="top"><strong>Verdict Number:</strong> #getverdict.number#</td>
			<td valign="top"><strong>Date:</strong> <cfif isdate(getverdict.date)>#dateformat(getverdict.date,"mm/dd/yyyy")#</cfif></td>
			<td valign="top"><strong>Resolution:</strong> #getverdict.resolutiontype#</td>
		</tr>
	</table>
	<br />
	<table border="0" class="bodytext" width="95%" cellpadding="2">
		<tr>
			<td width="140" valign="top"><strong>Category:</strong></td>
			<td valign="top">[#getverdict.casetype#]: #getverdict.casetypedetail#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Case:</strong></td>
			<td valign="top">#getverdict.casetitle#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Court:</strong></td>
			<td valign="top">#getverdict.courtname#</td>
		</tr>
		<tr>
			<td valign="top"><strong>County:</strong></td>
			<td valign="top">#getverdict.countyname#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Docket Number:</strong></td>
			<td valign="top">#getverdict.docketnumber#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Sex:</strong></td>
			<td valign="top">#getverdict.sex#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Age:</strong></td>
			<td valign="top">#getverdict.age#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Occupation:</strong></td>
			<td valign="top">#getverdict.Occupation#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Amount Awarded:</strong></td>
			<td valign="top">#dollarformat(getverdict.amount)# <cfif len(trim(getverdict.amountdetail)) gt 0> - #getverdict.amountdetail#</cfif></td>
		</tr>
		<tr>
			<td valign="top"><strong>Settlement Offer:</strong></td>
			<td valign="top">#getverdict.settlementoffer#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Facts:</strong></td>
			<td valign="top">#getverdict.facts#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Injuries:</strong></td>
			<td valign="top">#getverdict.Injuries#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Medicals:</strong></td>
			<td valign="top">#getverdict.medicals#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Lost Wages:</strong></td>
			<td valign="top">#getverdict.lostwages#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Coverage:</strong></td>
			<td valign="top">#getverdict.coverage#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Experts:</strong></td>
			<td valign="top">#getverdict.experts#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Plaintiff Attorney:</strong></td>
			<td valign="top">#getverdict.plaintiffattorney#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Defense Attorney:</strong></td>
			<td valign="top">#getverdict.defenseattorney#</td>
		</tr>
		<tr>
			<td valign="top"><strong>Submitting Attorney:</strong></td>
			<td valign="top">#getverdict.submittingattorney#</td>
		</tr>
	</table>
	<br />
	<table border="0" cellpadding="2">
		<tr>
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<td><input type="button" onclick="document.location.href='/?#baseQueryString#&editverdictid=#thisverdictid#';" value="Edit Verdict" class="bodytext" /></td>
			</cfif>
			<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
				<cfform action="/?#baseQueryString#" method="post">
					<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
						<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#session.lastverdictsearch[form_element]#">
					</CFLOOP>
					<td><input type="submit" value="Return to Results" class="bodytext"/></td>
				</cfform>
			</cfif>
			<td><input type="button" onclick="document.location.href='/?#baseQueryString#';" value="New Search" class="bodytext" /></td>
		</tr>
	</table>
	<br />
</cfoutput>

<cfelseif isdefined("form.page")>
	<!--- RESULTS --->
	
	<cfquery name="getmatches" datasource="#application.dsn.customApps.dsn#">
		select * from NE_verdicts
		where 1=1
		<cfif isdefined("form.resolutiontypes") and trim(form.resolutiontypes) neq "">
			and resolutiontype = <cfqueryparam value="#form.resolutiontypes#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif isdefined("form.casetypes") and trim(form.casetypes) neq "">
			and casetype = <cfqueryparam value="#form.casetypes#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif isdefined("form.countyname") and trim(form.countyname) neq "">
			and countyname = <cfqueryparam value="#form.countyname#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif isdefined("form.distinctyears") and isnumeric(form.distinctyears)>
			and date between <cfqueryparam value="#createDate(form.distinctyears,1,1)#" cfsqltype="cf_sql_date">
				and <cfqueryparam value="#createDate(form.distinctyears,12,31)#" cfsqltype="cf_sql_date">
		</cfif>
		<cfif isdefined("form.keywords") and len(trim(form.keywords)) gt 0>
			and (
				number like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				resolutionType like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				amountDetail like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				caseType like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				caseTypeDetail like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				caseTitle like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				courtName like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				docketNumber like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				facts like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				sex like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				age like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				occupation like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				injuries like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				medicals like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				lostwages like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				coverage like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				settlementOffer like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				experts like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				plaintiffAttorney like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				defenseAttorney like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				submittingAttorney like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR"> OR
				countyName like <cfqueryparam value="%#form.keywords#%" cfsqltype="CF_SQL_VARCHAR">
				)
		</cfif>
	</cfquery>

	<cfset session.lastVerdictSearch = duplicate(form)>
	
	<cfif getmatches.recordcount gt 0>
		<cfset numpages = ceiling(getmatches.recordcount / maxrows)>
		<cfset startrow = ((form.page-1) * maxrows) + 1>
		<cfset endrow = startrow + maxrows>
		<cfif getmatches.recordcount lt endrow>
			<cfset endrow = getmatches.recordcount>
		</cfif>
	<cfelse>
		<cfset getmatches = querynew('')>
		<cfset numpages = 0>
		<cfset startrow = 0>
		<cfset endrow = 0>
	</cfif>
	
	<cfoutput>
	<br />
	<p class="headertext">Verdicts and Settlements Search Results</p>
	<span class="bodytext">Showing #startrow# to #endrow# of #getmatches.recordcount# matches</span>
	<br /><br />
	
	<table border="0" cellpadding="2">
		<tr>
			<cfif form.page neq 1>
				<cfform action="/?#baseQueryString#" method="post">
					<input type="hidden" name="page" value="#form.page-1#" />
					<CFLOOP INDEX="form_element" LIST="#FORM.fieldnames#">
						<cfif form_element neq "page">
							<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
						</cfif>
					</CFLOOP>
					<td><input type="submit" value="&lt;&lt; Previous Page" class="bodytext"/></td>
				</cfform>
			</cfif>
			<cfif getmatches.recordcount gt (form.page*maxrows)>
				<cfform action="/?#baseQueryString#" method="post">
					<input type="hidden" name="page" value="#form.page+1#" />
					<CFLOOP INDEX="form_element" LIST="#FORM.fieldnames#">
						<cfif form_element neq "page">
							<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
						</cfif>
					</CFLOOP>
					<td><input type="submit" value="Next Page &gt;&gt;" class="bodytext" /></td>
				</cfform>
			</cfif>
			<td><input type="button" onclick="document.location.href='/?#baseQueryString#';" value="New Search" class="bodytext" /></td>
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<td><input type="button" onclick="document.location.href='/?#baseQueryString#&addverdict=1';" value="Add Verdict" class="bodytext" /></td>
			</cfif>
		</tr>
	</table>
	<br clear="all" />
	</cfoutput>
	
	<cfif getmatches.recordcount eq 0>
		<cfoutput><p class="bodytext">No records match your search criteria.</p></cfoutput>
	<cfelse>
		<cfoutput><table border="0" class="bodytext" width="95%" cellpadding="2"></cfoutput>
			<cfoutput query="getmatches" startrow="#startrow#" maxrows="#maxrows#">
				<tr>
					<td valign="top"><strong>Category:</strong> #getmatches.casetype#</td>
					<td valign="top"><strong>Date:</strong> <cfif isdate(getmatches.date)>#dateformat(getmatches.date,"mm/dd/yyyy")#</cfif></td>
					<td valign="top"><strong>Court:</strong>
						<cfif len(getmatches.courtname) gt 30>
							#left(getmatches.courtname,30)# ...
						<cfelse>
							#getmatches.courtname#
						</cfif>
					</td>
					<td valign="top"><strong>Resolution:</strong> #getmatches.resolutiontype#</td>
				</tr>
				<tr>
					<td colspan="4"><strong>Case:</strong> 
						<cfif len(getmatches.casetitle) gt 200>
							#left(getmatches.casetitle,200)# ...
						<cfelse>
							#getmatches.casetitle#
						</cfif>
						<em>(#getmatches.number#)</em>
					</td>
				</tr>
				<tr>
					<td colspan="4"><a href="/?#baseQueryString#&verdictid=#getmatches.verdictid#">View Details</a>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						&nbsp;&nbsp; <a href="/?#baseQueryString#&editverdictid=#getmatches.verdictid#">Edit Verdict</a>
					</cfif>
					</td>
				</tr>
				<tr>
					<td colspan="4">&nbsp;</td>
				</tr>
			</cfoutput>
		<cfoutput></table></cfoutput>
	</cfif>
	<cfoutput>
	<br />
	<table border="0" cellpadding="2">
		<tr>
			<cfif form.page neq 1>
				<cfform action="/?#baseQueryString#" method="post">
					<input type="hidden" name="page" value="#form.page-1#" />
					<CFLOOP INDEX="form_element" LIST="#FORM.fieldnames#">
						<cfif form_element neq "page">
							<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
						</cfif>
					</CFLOOP>
					<td><input type="submit" value="&lt;&lt; Previous Page" class="bodytext" /></td>
				</cfform>
			</cfif>
			<cfif getmatches.recordcount gt (form.page*maxrows)>
				<cfform action="/?#baseQueryString#" method="post">
					<input type="hidden" name="page" value="#form.page+1#" />
					<CFLOOP INDEX="form_element" LIST="#FORM.fieldnames#">
						<cfif form_element neq "page">
							<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
						</cfif>
					</CFLOOP>
					<td><input type="submit" value="Next Page &gt;&gt;" class="bodytext" /></td>
				</cfform>
			</cfif>
			<td><input type="button" onclick="document.location.href='/?#baseQueryString#';" value="New Search" class="bodytext" /></td>
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<td><input type="button" onclick="document.location.href='/?#baseQueryString#&addverdict=1';" value="Add Verdict" class="bodytext" /></td>
			</cfif>
		</tr>
	</table>
	<br />
	</cfoutput>
	
<cfelse>
<!--- SEARCH PAGE --->
	<cfquery name="casetypes" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT casetype
		FROM         NE_verdicts
		WHERE casetype IS NOT NULL AND casetype <> ''
		ORDER BY casetype
	</cfquery>	
	<cfquery name="resolutiontypes" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT resolutiontype
		FROM         NE_verdicts
		WHERE resolutiontype IS NOT NULL AND resolutiontype <> ''
		ORDER BY resolutiontype
	</cfquery>	
	<cfquery name="countynames" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT countyname
		FROM         NE_verdicts
		WHERE countyname IS NOT NULL AND countyname <> ''
		ORDER BY countyname
	</cfquery>	
	<cfquery name="distinctyears" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT YEAR(DATE) as year
		FROM         NE_verdicts
		WHERE DATE IS NOT NULL AND DATE <> ''
		ORDER BY YEAR(DATE)
	</cfquery>
	
	<cfoutput>
	<br />
	<p class="headertext" align="left">Search NATA Verdict and Settlement Exchange Database</p>
	<p class="bodytext" align="left">NATA's Verdict and Settlement Exchange Database contains reports submitted by members for publication in The Prairie Barrister. The database records include all information provided by members and is more detailed than the Barrister's published reports. The Verdict and Settlement Exchange is a valuable service to our members and we welcome all reports. </p>
	<p class="bodytext"><a href="/NE/docDownload/5908" target="_blank">DOWNLOAD A REPORT FORM</a></p>
	<cfform action="/?#baseQueryString#" method="post">
		<input type="hidden" name="page" value="1" />
		<table class="bodytext">
			<tr>
				<td>Resolution Type:</td>
				<td>
					<select name="resolutiontypes" class="bodytext">
						<option value="">All</option>
						<cfloop query="resolutiontypes">
							<option>#resolutiontypes.resolutiontype#</option>
						</cfloop>
					</select>
				</td>
			</tr>
			<tr>
				<td>Case Category:</td>
				<td>
					<select name="casetypes" class="bodytext">
						<option value="">All</option>
						<cfloop query="casetypes">
							<option>#casetypes.casetype#</option>
						</cfloop>
					</select>
				</td>
			</tr>
			<tr>
				<td>Verdict Year:</td>
				<td>
					<select name="distinctyears" class="bodytext">
						<option value="">All</option>
						<cfloop query="distinctyears">
							<option>#distinctyears.year#</option>
						</cfloop>
					</select>
				</td>
			</tr>
			<tr>
				<td>County:</td>
				<td>
					<select name="countyname" class="bodytext">
						<option value="">All</option>
						<cfloop query="countynames">
							<option>#countynames.countyname#</option>
						</cfloop>
					</select>
				</td>
			</tr>
			<tr>
				<td>Keywords (optional):</td>
				<td><input type="text" name="keywords" class="bodytext" maxlength="100" size="70" />
				</td>
			</tr>
		</table>
		<br />
		<input type="submit" value="Search Reports" class="bodytext"/>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			&nbsp;&nbsp;<input type="button" onclick="document.location.href='/?#baseQueryString#&addverdict=1';" value="Add Verdict" class="bodytext" />
		</cfif>
	</cfform>
	</cfoutput>
</cfif>