<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">

		<cfargument name="Event" type="any">
		<!--- variables --->
		<cfset variables.orgID 						= arguments.event.getTrimValue('mc_siteinfo.orgID')>
		<cfset variables.siteID 					= arguments.event.getTrimValue('mc_siteinfo.siteID')>

		<cfset variables.getCategoryStarted 		= application.objCustomPageUtils.mh_getCategory(variables.siteID,'MemAppHistory','Started')>
		<cfset variables.getCategoryCompleted 		= application.objCustomPageUtils.mh_getCategory(variables.siteID,'MemAppHistory','Completed')>
		
		<cfset variables.formNameDisplay 			= "Membership Form">
		<cfset variables.organization 				= arguments.event.getValue('mc_siteInfo.ORGShortName')>

		<cfset variables.profile_1._profileCode 	= "BAMSL_CC">
		<cfset variables.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=variables.profile_1._profileCode)>
		<cfset variables.profile_1._description 	= "#variables.organization# - #variables.formNameDisplay#">
		<cfset variables.qryStates 					= application.objCommon.getStates()>
		<cfset variables.qryCountry 				= application.objCommon.getCountries()>
		
		<cfset variables.renewAuto					= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Automatic Membership Renewal") >
		<cfset variables.memberTypes				= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Member Type") >

		<cfset variables.employmentTypes			= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Employment Type") >

		<cfset variables.qryLawSchools				= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Law School") >
		<cfset variables.gender						= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Gender") >
		<cfset variables.qryPracticeAreas			= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Areas of Practice") >
		<cfset variables.qryProximity				= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Proximity to St Louis") >
		<cfset variables.qryAssociateCreditCard		= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Associate Credit Card") >
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>

		<cfset variables.formName = "frmJoin">

		<!--- Email confirmation settings --->
		<cfset variables.emailSubject = "Thank you for joining Bar Association of Metropolitan St. Louis!">
		<cfset variables.strEMailSettings_staff = { 
				from="<EMAIL>", 
				to="<EMAIL>", 
				subject=variables.emailSubject,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename'),
				maileridCode=arguments.event.getTrimValue('mc_siteinfo.sitecode')
			}>
		<cfset variables.strEMailSettings_member = { 
				from="<EMAIL>", 
				to=arguments.event.getTrimValue('email',''), 
				replyTo="<EMAIL>", 
				subject=variables.emailSubject,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename'),
				maileridCode=arguments.event.getTrimValue('mc_siteinfo.sitecode')
			}>
		
		<cfset variables.todayDate = now()>
		<cfset variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=variables.formName)>
		<!--- for form action --->
		<cfset variables.applicationReservedURLParams = "fa,sid">
		<cfset variables.baselink = "/?#getBaseQueryString(false)#">
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">
		<cfset local.returnScript = "">
		
		<cfscript>
			local.arrCustomFields = [];				
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button", value="Join BAMSL" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instructions", value="<p>Click the <b>Join BAMSL</b>&nbsp;button to the left.<br />Enter the search criteria and click&nbsp;<strong>Continue</strong>.<br />If you see your name, click&nbsp;<strong>Choose</strong>.<br />If you do not see your name, click the&nbsp;<strong>Create an Account</strong>&nbsp;button.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AdvocacyEditableContent", type="CONTENTOBJ", desc="Advocacy editable content", value="<p>Advocacy and status sections and committees are groups of BAMSL members united by common backgrounds or goals. These groups provide the opportunity to seek societal change on behalf of others like you, get tips and ideas for others similarly situated, or just get to know other lawyers with whom you have something in common.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CommunityEditableContent", type="CONTENTOBJ", desc="Community editable content", value="<p>Community service committees provide a way for members to give back to the community alongside other lawyers and judges.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipEditableContent", type="CONTENTOBJ", desc="Membership editable content", value="<p>Member services and association governance committees are where BAMSL members become involved in running the association. These groups provide the opportunity for members to direct the affairs of the local bar. In doing do, members can give back to the bar while influencing the services and programs that BAMSL undertakes, as well as building their networks with others who serve.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="SocialEditableContent", type="CONTENTOBJ", desc="Social editable content", value="<p>Social and event planning committees are where members get involved with planning happy hours, parties, golf outings, conferences, and other events.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubstantiveEditableContent", type="CONTENTOBJ", desc="Substantive editable content", value="<p>Substantive Law and Practice Area Committees and Sections are the place to network with practitioners in your area of law, as well as plan CLEs and write articles related to your area of practice. BAMSL's extensive array of substantive committees and sections provide unparalleled local opportunities for education and career development.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="YoungLawyerEditableContent", type="CONTENTOBJ", desc="Young Lawyer editable content", value="<p>BAMSL's Young Lawyers Division (YLD). All BAMSL members who are 36 years old or younger, or who are within 5 years of their first bar admission automatically become members of YLD. All law student members also automatically become YLD members.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ThankyouMessage", type="STRING", desc="Thank you message", value="Thank you for joining BAMSL!" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step1EditableContent", type="STRING", desc="Step1 Editable Content", value="Please complete the form below to become a BAMSL member." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2EditableContent", type="STRING", desc="Step2 Editable Content", value="Please select a rate option below." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SustainingMembershipEditableContent", type="CONTENTOBJ", desc="Sustaining membership editable content", value="<h2 style='font-size:20px;'><i class='fa fa-arrow-circle-up'>&nbsp;</i>Upgrade your membership by becoming a Sustaining Member</h2><p>BAMSL's Sustaining Members receive invitations to exclusive networking receptions, two free ethics CLE hours, free admission to select signature events, and special recognition among your peers through BAMSL marketing pieces. Upgrade your membership for just $100 more per year by selecting the option below.</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AutoRenewContent", type="CONTENTOBJ", desc="Auto Renew Content", value="<h2 style='font-size: 20px;'><i class='fa fa-refresh'>&nbsp;</i>Set it and forget it with automatic renewal!</h2><p>Automatic renewal includes all membership dues, upgrades (e.g. Sustaining Membership), and section and committee memberships current or added during the membership year.</p><p>On May 1 of each year, the credit card payment will be taken and you will receive an email receipt confirming the payment. You will receive a notification at least one month in advance of dues payment processing.</p><p>A credit card must be on file for automatic renewal. Should the credit card payment be declined, you will be notified by email or phone by BAMSL staff. You must then update your credit card to continue with automatic renewal.</p><p>BAMSL reserves the right to change annual membership dues rates. You will be notified of any such changes prior to the processing of the automatic renewal.</p><p>Visit <a href='/mybamsl' target='_blank'>my.bamsl.org</a> or call ************ to make changes to your account, including cancelling automatic renewal.</p><p><strong>By selecting 'Yes' below you authorize BAMSL to automatically renew your BAMSL membership each year on May 1 and to charge the credit card you have on file.</strong></p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipDesc", type="CONTENTOBJ", desc="Membership description", value="<p>Membership description</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="VoluntaryContributionDesc", type="CONTENTOBJ", desc="Voluntary Contribution description", value="<p>Voluntary Contribution description</p>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			variables.strPageCustomFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		</cfscript>
		
		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>

		<cfset local.allSections = createObject("java", "java.util.LinkedHashMap").init()/>
		<cfset local.allSectionsNames = createObject("java", "java.util.LinkedHashMap").init()/>
		
		<cfset local.qryAvailableSections = getAvailableSections()>

		<cfloop query="local.qryAvailableSections">
			 <cfset local.allSections[local.qryAvailableSections.typeName][local.qryAvailableSections.uid] = local.qryAvailableSections.subscriptionName />
			 <cfset local.allSectionsNames[local.qryAvailableSections.uid] = local.qryAvailableSections.subscriptionName />
			 <cfset local.allTypeName[local.qryAvailableSections.typeName] = local.qryAvailableSections.sub_type_uid />
		</cfloop>
				
		<cfsavecontent variable="local.returnScript">
			<cfinclude template ="commonScript.cfm">
		</cfsavecontent>

		<cfswitch expression="#arguments.event.getValue('fa','showForm')#">
			
			<cfcase value="processStep1">
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = 	arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfif NOT structKeyExists(arguments.event.getCollection(), "newSubs") >
					<cfset local.del1 = structDelete(session.fieldArr,"newSubs") >
				</cfif>
				<cfset local.returnHTML = processStep1(event=arguments.event)>
			</cfcase>

			<cfcase value="processStep2">
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfset processStep2(event=arguments.event,SectionsNames=local.allSectionsNames)>
			</cfcase>

			<cfcase value="complete">
				<cfif NOT isDefined("session.invoice")>
					<cflocation url="#variables.baselink#" addtoken="false">
				</cfif>
				<cfif NOT structKeyExists(session, "historyId") >
					<cfset local.del1 = structDelete(session,"historyId") >
				</cfif>
				<cfif structKeyExists(session, "captchaEntered")>
					<cfset structDelete(session, "captchaEntered")>
				</cfif> 
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<div style="clear:both;"></div>
					<h3>
						#variables.strPageCustomFields.ThankyouMessage#
					</h3>
					#session.invoice#
					</cfoutput>
				</cfsavecontent>

			</cfcase>

			<cfcase value="mediator1To2">
				<cfset local.collection = arguments.event.getCollection()>
				
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfif NOT structKeyExists(local.collection, "professionalLicenseDropdown") >
					<cfset local.del1 = structDelete(session.fieldArr,"professionalLicenseDropdown") >
				</cfif>
				<cfif NOT structKeyExists(local.collection, "empType") >
					<cfset local.del1 = structDelete(session.fieldArr,"empType") >
				</cfif>
				<cfif NOT structKeyExists(local.collection, "practiceAreas") >
					<cfset local.del1 = structDelete(session.fieldArr,"practiceAreas") >
				</cfif>
				<cfif NOT structKeyExists(local.collection, "sustaining_membership_full") >
					<cfset local.del1 = structDelete(session.fieldArr,"sustaining_membership_full") >
				</cfif>

				<cfif NOT structKeyExists(local.collection, "sustaining_membership_monthly") >
					<cfset local.del1 = structDelete(session.fieldArr,"sustaining_membership_monthly") >
				</cfif>
				<cfsavecontent variable="local.returnHTML">
				<cfoutput>1</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="step2">
				<cfif structKeyExists(arguments.event.getCollection(), 'iAgree') OR (NOT structKeyExists(session, "captchaEntered") AND (NOT Len(arguments.event.getValue('captcha',''))) OR application.objCustomPageUtils.validateCaptcha(code=arguments.event.getValue('captcha'),captcha=arguments.event.getValue('captcha_check')).response NEQ "success")>
					<cflocation url="#variables.baselink#&fa=error" addtoken="false">
				</cfif>
				<cfset session.captchaEntered = 1>
				<cfset local.saved = saveMember(event=arguments.event)>
				<cfif not structKeyExists(session, "historyId")>
					<cfset session.historyId  = application.objCustomPageUtils.mh_addHistory(memberID=arguments.event.getValue("memberID"), 
						categoryID=variables.getCategoryStarted.CATEGORYID, subCategoryID=variables.getCategoryStarted.SUBCATEGORYID,
						description='Join Form Started.', linkMemberID=0, enteredByMemberID=arguments.event.getValue("memberID"),
						newAccountsOnly=false)>
				</cfif>
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<h3 class="HeaderText">#variables.strPageCustomFields.Step2EditableContent#</h3>
					<div id="customPage">							
						<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
						
						<div id="formTable">
							<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" 
							onsubmit="return _FB_validateForm(1);" class="form-horizontal">
								<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("fa,amount,btnSubmit",local.key) 
										and left(local.key,7) neq "section"
										and left(local.key,9) neq "formfield"
										and left(local.key,4) neq "fld_">

										<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>

										<cfif NOT listFindNoCase("membership,voluntary_contribution_full,voluntary_contribution_monthly,sustaining_membership_full,sustaining_membership_monthly,auto_renew,otherAmount_full,otherAmount_monthly",local.key) >
											<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
										</cfif>
									</cfif>
								</cfloop>
								<cfif NOT structKeyExists(arguments.event.getCollection(), "newSubs") >
									<cfset local.del1 = structDelete(session.fieldArr,"newSubs") >
								</cfif>
								<cfinput type="hidden" name="fa" id="fa" value="step3">
								<div class="" style="padding-bottom:15px;text-align:right">
									<a style="text-decoration: none;"id="gotoStep2" class="btn btnCustom" href="javascript:;">Back</a>
								</div>
								
								<div class="CPSection step2">
									<div class="NVTitle CPSectionTitle Border">Membership</div>
									<div class="CPSectionContent BorderNoTop">
									#variables.strPageCustomFields.MembershipDesc#
									</div>
									<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
										<div class="CPSectionContent">
											#application.objCMS.renderZone(zone='L',event=event)#
										</div>
									</cfif>
									

									<cfset local.10PlusAttPri 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'10PlusAttorneyIPrivate')>
									<cfset local.2to5AttPriv 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'2to5AttorneyIPrivate')>
									<cfset local.6to9AttPriv 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'6to9AttorneyIPrivate')>
									<cfset local.NewAttPri 				= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'NewBarAttorneyIPrivate')>
									<cfset local.NewLateAttPriv 		= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'NewBarLateAttorneyIPrivate')>
									<cfset local.10PlusAttPub 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'10PlusAttorneyIPublic')>
									<cfset local.2to5AttPub 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'2to5AttorneyIPublic')>
									<cfset local.6to9AttPub 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'6to9AttorneyIPublic')>
									<cfset local.NewBarAttorneyIPub 	= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'NewBarAttorneyIPublic')>
									<cfset local.NewBarLateAttorneyIPub = application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'NewBarLateAttorneyIPublic')>
									<cfset local.Att2 					= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'AttorneyII')>
									<cfset local.Att3 					= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'AttorneyIII')>
									<cfset local.Att4 					= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'AttorneyIV')>
									<cfset local.Att5 					= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'AttorneyV')>
									<cfset local.Judge1 				= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'JudgeI')>
									<cfset local.Judge2 				= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'JudgeII')>
									<cfset local.Judge3 				= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'JudgeIII')>
									<cfset local.LawStudent 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'LawStudent')>
									<cfset local.LegalAdmin 			= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'LegalAdmin')>
									<cfset local.Paralegal 				= application.objCustomPageUtils.mem_getGroups(memberid,variables.orgID,'Paralegal')>

									<cfset local.rateUIDList = arrayNew(1)>
									<cfset local.rateList = '11111111-1111-1111-1111-1117A5DEA9DB'>
									
									
									<cfif local.10PlusAttPri.recordCount neq 0>
									<cfset ArrayAppend(local.rateUIDList, '816C7EB2-0EBC-46C1-9911-AEC7A5DEA9DB,07DBA46F-F667-4208-9179-DAE321DBF5C1,B82DE0C3-69CA-4A7B-B682-CE16890AD669,9C71E3BD-1440-441E-9AEE-F62B6DBFEEB4,070FD70F-6680-48C3-A857-2DD5AB5CE3ED,43884BE8-0A69-4230-B45F-2BF6CA1789C5,FC126556-AF94-49FC-9BD2-A6DF0C75351D')>
										<cfset local.rateList = '816C7EB2-0EBC-46C1-9911-AEC7A5DEA9DB,07DBA46F-F667-4208-9179-DAE321DBF5C1,B82DE0C3-69CA-4A7B-B682-CE16890AD669,9C71E3BD-1440-441E-9AEE-F62B6DBFEEB4,070FD70F-6680-48C3-A857-2DD5AB5CE3ED,43884BE8-0A69-4230-B45F-2BF6CA1789C5,FC126556-AF94-49FC-9BD2-A6DF0C75351D'>
									</cfif>
									<cfif local.2to5AttPriv.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, 'AA93E793-6A83-47FB-B3F6-0490255477B4,32F8A618-1308-45DF-95A4-05BADB908A15,8A529F09-078A-47BC-8EB0-F9DE1CEE4819,386279C5-9A02-474E-95B0-3B330E809CA5,FE2D5A19-7EB7-4986-9494-177C64CEA14E,7002A070-102A-4DEF-A75B-E4665CC53562,F410FFB3-636F-44B7-9FB8-7EBD480A5163')>
										<cfset local.rateList = 'AA93E793-6A83-47FB-B3F6-0490255477B4,32F8A618-1308-45DF-95A4-05BADB908A15,8A529F09-078A-47BC-8EB0-F9DE1CEE4819,386279C5-9A02-474E-95B0-3B330E809CA5,FE2D5A19-7EB7-4986-9494-177C64CEA14E,7002A070-102A-4DEF-A75B-E4665CC53562,F410FFB3-636F-44B7-9FB8-7EBD480A5163'>
									</cfif>
									<cfif local.6to9AttPriv.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, 'C96E51BE-199A-44CB-BDB4-B47C620979B5,A704CBCE-549C-4848-9A53-2AA414B76E84,87B68E29-218F-4DA6-8CB3-E91B8600436E,EE92DD4D-06B5-4387-8694-13A93F695E2F,E491FB92-D0DC-4799-9720-334C32B67A21,3DFA09E3-F7D6-46B0-ADA3-927342B9DFB8,ACA2204E-E5D3-4D35-BDE8-32EFE26C5970')>
										<cfset local.rateList = 'C96E51BE-199A-44CB-BDB4-B47C620979B5,A704CBCE-549C-4848-9A53-2AA414B76E84,87B68E29-218F-4DA6-8CB3-E91B8600436E,EE92DD4D-06B5-4387-8694-13A93F695E2F,E491FB92-D0DC-4799-9720-334C32B67A21,3DFA09E3-F7D6-46B0-ADA3-927342B9DFB8,ACA2204E-E5D3-4D35-BDE8-32EFE26C5970'>
									</cfif>
									<cfif local.NewAttPri.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '92EBD740-F53B-47D4-A774-6312AEA3EBB6')>
										<cfset local.rateList = '92EBD740-F53B-47D4-A774-6312AEA3EBB6'>
									</cfif>
									<cfif local.10PlusAttPub.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '54214FE1-6137-44B4-80BF-65D4CF71F805,2CFC5CEA-36E3-4BA2-91A1-2AC67ABD5BB9,33F719BA-7BAB-45F8-86CB-0A3A7500158F,7CAE186C-FA4E-443C-A990-D2FB3419381C,E7CD1F8E-56C9-40E2-A6FA-D4982D9935D3,C97AB939-573A-4709-842B-86F22C2107F6,CD013E47-2F7D-49F7-BEDD-788DBE6DF2E2')>
										<cfset local.rateList = '54214FE1-6137-44B4-80BF-65D4CF71F805,2CFC5CEA-36E3-4BA2-91A1-2AC67ABD5BB9,33F719BA-7BAB-45F8-86CB-0A3A7500158F,7CAE186C-FA4E-443C-A990-D2FB3419381C,E7CD1F8E-56C9-40E2-A6FA-D4982D9935D3,C97AB939-573A-4709-842B-86F22C2107F6,CD013E47-2F7D-49F7-BEDD-788DBE6DF2E2'>
									</cfif>
									<cfif local.2to5AttPub.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '1B6384F8-464B-4E63-B80B-6A06B277DB73,79919A2F-76E4-4148-A0B4-FB12A6BA3938,8180F6CF-4B6A-4714-B03B-1437AD7B81CF,68D965C4-6FB0-46E5-BA9F-45FC3FD5D04E,A8F6CDDE-F65C-4664-BA77-7D7DAD37DFA5,B3EEFFCC-CC70-4373-A423-371B3CA2D012,6666D596-08B0-456D-BC12-D433883315D3')>
										<cfset local.rateList = '1B6384F8-464B-4E63-B80B-6A06B277DB73,79919A2F-76E4-4148-A0B4-FB12A6BA3938,8180F6CF-4B6A-4714-B03B-1437AD7B81CF,68D965C4-6FB0-46E5-BA9F-45FC3FD5D04E,A8F6CDDE-F65C-4664-BA77-7D7DAD37DFA5,B3EEFFCC-CC70-4373-A423-371B3CA2D012,6666D596-08B0-456D-BC12-D433883315D3'>
									</cfif>
									<cfif local.6to9AttPub.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '8583DB1F-B9CA-4499-8BE1-C3E1C7BDDA8C,2C2C0A31-7F8E-4007-A6A5-8E9249CFF061,4D069505-F55B-4132-80E9-3FB1071A52C8,B62ADE7D-78C4-4197-9A9E-F6F469AC7DC8,F5134D98-C769-45C2-8A92-DA604D78BD2F,08C7AFF6-00AB-46F9-9B09-82D66C1DC402,5E8311E3-AA86-4B52-97F6-013FF4F0E4C6')>
										<cfset local.rateList = '8583DB1F-B9CA-4499-8BE1-C3E1C7BDDA8C,2C2C0A31-7F8E-4007-A6A5-8E9249CFF061,4D069505-F55B-4132-80E9-3FB1071A52C8,B62ADE7D-78C4-4197-9A9E-F6F469AC7DC8,F5134D98-C769-45C2-8A92-DA604D78BD2F,08C7AFF6-00AB-46F9-9B09-82D66C1DC402,5E8311E3-AA86-4B52-97F6-013FF4F0E4C6'>
									</cfif>
									<cfif local.NewBarAttorneyIPub.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '29B227FB-564A-49D1-99C5-1E9AC75F7B37')>
										<cfset local.rateList = '29B227FB-564A-49D1-99C5-1E9AC75F7B37'>
									</cfif>
									<cfif local.Att2.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '0BBA8EE4-BAE7-4FFA-BAD1-7657E4830BBC,0F8979E5-A6C5-41D6-BBD9-42407747D831,8B75ECA4-E47A-43F7-9949-D141112A863A,10C4A8F2-C112-4047-8668-5B39901F668E,FAE49C0F-7E73-4DFF-BA4C-01C2818E93A7,D766D0E9-A563-4C22-BC02-6BFC2972B1C4,B7033FF1-AD62-4B6A-B627-733BD98DB91A')>
										<cfset local.rateList = '0BBA8EE4-BAE7-4FFA-BAD1-7657E4830BBC,0F8979E5-A6C5-41D6-BBD9-42407747D831,8B75ECA4-E47A-43F7-9949-D141112A863A,10C4A8F2-C112-4047-8668-5B39901F668E,FAE49C0F-7E73-4DFF-BA4C-01C2818E93A7,D766D0E9-A563-4C22-BC02-6BFC2972B1C4,B7033FF1-AD62-4B6A-B627-733BD98DB91A'>
									</cfif>
									<cfif local.Att3.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '8D2B1483-36EE-4F9A-9D7A-9ACD5128F848,FE6D6129-55F2-4CAA-B894-BFF7E68F9249,6A037E08-150E-4AB2-8BE8-D291238372E6,F310F5F5-3369-4BCE-ADEE-20366938CF02,08262033-2504-4573-8FBB-F143F5368137,F16C83DE-9216-4017-A650-90A911A91426,B189542E-765C-4EEF-B887-B9AD2D3D112A')>
										<cfset local.rateList = '8D2B1483-36EE-4F9A-9D7A-9ACD5128F848,FE6D6129-55F2-4CAA-B894-BFF7E68F9249,6A037E08-150E-4AB2-8BE8-D291238372E6,F310F5F5-3369-4BCE-ADEE-20366938CF02,08262033-2504-4573-8FBB-F143F5368137,F16C83DE-9216-4017-A650-90A911A91426,B189542E-765C-4EEF-B887-B9AD2D3D112A'>
									</cfif>
									<cfif local.Att4.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, 'A9B04F46-6399-476F-82E6-56C739CEB53C')>
										<cfset local.rateList = 'A9B04F46-6399-476F-82E6-56C739CEB53C'>
									</cfif>
									<cfif local.Att5.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '7AE480D1-1B97-488D-9B08-4DDA9CCAB878')>
										<cfset local.rateList = '7AE480D1-1B97-488D-9B08-4DDA9CCAB878'>
									</cfif>
									<cfif local.Judge1.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '442E8E18-2052-42DC-8039-FD8CF91F5850,8D481F5A-3529-479C-BFBE-B1A2FD522575,4953CCD7-1F9B-4F7A-9DB7-7C492EFF19F9,A70D5A6C-C3CF-496C-8268-11DD258F1C55,A7BD1E60-6069-470F-B0AF-C5E6CED7E451,70BAAAF3-1150-4043-9E65-58630E684F27,AE3551C6-3A33-4B39-8E36-BA8D7058FF24')>
										<cfset local.rateList = '442E8E18-2052-42DC-8039-FD8CF91F5850,8D481F5A-3529-479C-BFBE-B1A2FD522575,4953CCD7-1F9B-4F7A-9DB7-7C492EFF19F9,A70D5A6C-C3CF-496C-8268-11DD258F1C55,A7BD1E60-6069-470F-B0AF-C5E6CED7E451,70BAAAF3-1150-4043-9E65-58630E684F27,AE3551C6-3A33-4B39-8E36-BA8D7058FF24'>
									</cfif>
									<cfif local.Judge2.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, 'BB6FCE00-E0A0-472D-A418-02097F380A47,378D3553-F9D1-4DF4-B3F0-9E1864C822E0,FB04A580-4F70-4A2D-8862-54E6B8BB0896,5FD1B3CD-CE94-40BF-A7EF-B1886908F028,B7ECB0D2-FA4E-477F-9FD6-7AA61A8B8266,08BD3FA6-ECEA-4F84-98A7-54EA51D96846,CD9ED10F-299F-4138-A83E-62D4FC49697B')>
										<cfset local.rateList = 'BB6FCE00-E0A0-472D-A418-02097F380A47,378D3553-F9D1-4DF4-B3F0-9E1864C822E0,FB04A580-4F70-4A2D-8862-54E6B8BB0896,5FD1B3CD-CE94-40BF-A7EF-B1886908F028,B7ECB0D2-FA4E-477F-9FD6-7AA61A8B8266,08BD3FA6-ECEA-4F84-98A7-54EA51D96846,CD9ED10F-299F-4138-A83E-62D4FC49697B'>
									</cfif>
									<cfif local.Judge3.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '374ABFB6-10C0-48F3-9123-C10A352A3A0B,305DB675-AB9A-4C15-A5BC-DA53385A783D,E7006A53-61C4-4845-B500-EDC1B3B644F6,9EC11DEF-3F78-42E2-AE23-776FE1BB8B9C,9489F42B-983D-4FE2-B78E-58087C5CD23B,05A1413E-E262-4771-8B98-7EE3097617CB,747ACB34-4B44-4FE7-92A6-B0B26B3E8E7B')>
										<cfset local.rateList = '374ABFB6-10C0-48F3-9123-C10A352A3A0B,305DB675-AB9A-4C15-A5BC-DA53385A783D,E7006A53-61C4-4845-B500-EDC1B3B644F6,9EC11DEF-3F78-42E2-AE23-776FE1BB8B9C,9489F42B-983D-4FE2-B78E-58087C5CD23B,05A1413E-E262-4771-8B98-7EE3097617CB,747ACB34-4B44-4FE7-92A6-B0B26B3E8E7B'>
									</cfif>
									<cfif local.LawStudent.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '2EB56A6C-D4F4-47AE-AC97-B1FD91F50915')>
										<cfset local.rateList = '2EB56A6C-D4F4-47AE-AC97-B1FD91F50915'>
									</cfif>
									<cfif local.LegalAdmin.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, 'FADABB9F-80C8-4993-8283-DC4BB7C5C3E9,E5A050B6-1BCD-4E8B-AAAA-E8ED47995669,751026FE-4AB4-4226-9144-608F8424B17B,3692D1D7-34E8-44F9-B4C1-BD80FAA536BD,2890CDD5-C46C-43AD-9609-496FC77339F2,46A60787-B29E-4296-903B-892AD6AEB3A3,7D6C3A99-8494-4CB1-BCC4-268B1DB02CDB')>
										<cfset local.rateList = 'FADABB9F-80C8-4993-8283-DC4BB7C5C3E9,E5A050B6-1BCD-4E8B-AAAA-E8ED47995669,751026FE-4AB4-4226-9144-608F8424B17B,3692D1D7-34E8-44F9-B4C1-BD80FAA536BD,2890CDD5-C46C-43AD-9609-496FC77339F2,46A60787-B29E-4296-903B-892AD6AEB3A3,7D6C3A99-8494-4CB1-BCC4-268B1DB02CDB'>
									</cfif>
									<cfif local.Paralegal.recordCount neq 0>
										<cfset ArrayAppend(local.rateUIDList, '6C610F58-2D0D-49B1-B2FC-629AA53F2A15,107CF51A-1A53-48D5-BA91-904B8C9C58C8,B21FD043-AAC5-4BC0-BB73-FD92B8AC9957,05B9C46E-A41F-4A82-8FBB-4E4031288899,CCDE9806-AE6B-492A-AE72-4362601A66DF,A52A0094-B955-4520-86B1-90F2AEC1ABE9,CF3EC74E-FDAF-4905-A285-0368CDB050C1')>
										<cfset local.rateList = '6C610F58-2D0D-49B1-B2FC-629AA53F2A15,107CF51A-1A53-48D5-BA91-904B8C9C58C8,B21FD043-AAC5-4BC0-BB73-FD92B8AC9957,05B9C46E-A41F-4A82-8FBB-4E4031288899,CCDE9806-AE6B-492A-AE72-4362601A66DF,A52A0094-B955-4520-86B1-90F2AEC1ABE9,CF3EC74E-FDAF-4905-A285-0368CDB050C1'>
									</cfif>
									<cfif arrayIsEmpty(local.rateUIDList)>
										<cfset ArrayAppend(local.rateUIDList, '11111111-1111-1111-1111-1117A5DEA9DB')>
									</cfif>

									<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates">
										SET NOCOUNT ON;

										declare @FID int, @scheduleID int, @currentDate datetime, @subscriptionID int;
										set @currentDate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#variables.todayDate#">;
										set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;

										select @scheduleID = scheduleID, @subscriptionID = subscriptionID
										from sub_subscriptions
										where uid = 'f53896ce-7d65-4f5e-8374-afbb26fe97d0';

										select newRate.rateID, newRate.uid,  newRate.rateName, newRate.termAFEndDate as termEndDate,
											rs.scheduleName,rf.rateAmt, f.frequencyShortName,rf.rfid
										from sub_subscriptions subs
										inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID 
											and rs.status = 'A' and subs.subscriptionID = @subscriptionID
										inner join dbo.sub_rates as newRate on newRate.scheduleID = rs.scheduleID 
											and newRate.status = 'A' and newRate.isRenewalRate = 0
											and newRate.uid NOT IN ('7B7B1E2A-A28C-4336-9B4C-5949AD668531','66868B7B-1208-44DB-A945-CE5EE7E9FA8D','115563A8-AFE7-4361-8D0E-DF83E0918CC8','47090F58-66A0-44BB-9D85-792EFDA6F13F','75D1D8C4-53A3-4DF6-84B0-24DB7B401611','5567AF4A-A395-4836-B3AF-32FA3BADB643','D08F7721-8706-4A51-8131-FF2E0BF3B4E3','2A2000A1-AF2B-400F-8B63-077D2224235A','BFF9F11A-675B-42D7-A98D-3498F7B2CBF2','5AE942B0-E077-431A-A095-8792CB2EE486','26C164BB-BDEF-43FF-B376-2009296F55B4','B2BFBFAC-7FD6-4374-AB4D-B6B162A1D293','96B1C40C-ADD9-433A-A099-E1810357FC65','FECE6F36-7748-4EE4-90CD-E458006F4657')
											and @currentDate between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
										inner join dbo.sub_rateFrequencies rf on rf.rateID = newRate.rateID 
											and rf.status = 'A'
										inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
											and f.status = 'A'
											inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = newRate.siteResourceID
											AND srfrp.functionID = @FID
											AND srfrp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#"> 
										inner join dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
											AND gprp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#"> 
										inner join ams_members m
											on m.groupPrintID = gprp.groupPrintID
											and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('memberID')#"> 
										order by newRate.rateORder;
									</cfquery>
									<cfset local.rateCalcDivisor = 12>	
						 			<div class="BorderNoTop frmText membershipRates" style="padding:10px;">
						 				<cfif local.membershipRates.recordCount>
											<cfloop query="local.membershipRates">
												<cfset local.thisAmount = local.membershipRates.rateAmt>
												<cfset local.amountLabel = "1 Payment of " & dollarFormat(local.thisAmount) />
												<!--- Rate divisor should not be greater than 12 --->
												<cfset local.rateCalcDivisor = 12>
												<cfif dateDiff("m", variables.todayDate, local.membershipRates.termEndDate) lt 12>
													<cfset local.rateCalcDivisor =  dateDiff("m", variables.todayDate, local.membershipRates.termEndDate)+1>
												</cfif>
												<cfif local.membershipRates.frequencyShortName eq "M">		
													<cfquery name="local.qryGetFullRate" dbtype="query">
														select * from [local].membershipRates where frequencyShortName = 'F' and rateName = '#local.membershipRates.rateName#'
													</cfquery>																										
													<cfset local.thisAmount = numberFormat(local.qryGetFullRate.rateAmt/local.rateCalcDivisor,"___.__")>
													<cfset local.amountLabel = "Monthly Payments of  " & dollarFormat(local.thisAmount) />
												</cfif>											
												<label class="radio">
													<input  <cfif structKeyExists(session.fieldArr, "membership") and session.fieldArr.membership eq local.membershipRates.rfid>checked </cfif> class="mship_check" id="#local.membershipRates.uid#"  type="radio" name="membership" value="#local.membershipRates.rfid#" frequency="#local.membershipRates.frequencyShortName#" amount ="#local.thisAmount#" subname="#local.membershipRates.rateName#">
													<div class="row-fluid">
														<div class="span3 membershipName">#local.membershipRates.rateName#</div>
														<div class="span3">Start Date : #DateFormat(now(), "mm/dd/yyyy")#</div>
														<div class="span3">End Date : #DateFormat(local.membershipRates.termEndDate, "mm/dd/yyyy")#</div>
														<div class="span3">#local.amountLabel#</div>
													</div>
												</label>
											</cfloop>
										</cfif>	
									</div>
									<cfset local.qrySustainingRates  =  application.objCustomPageUtils.sub_getRateSchedule( siteID = variables.siteID, scheduleUID ='4c5705d6-b72e-47e1-8fb6-98537dbb424b',rateUID ='F0A979E1-11A2-4113-B92B-187220A950FB', activeRatesOnly=true, ignoreRenewalRates=true) />

									<div class="NVTitle  CPSectionTitle BorderNoTop">Sustaining Membership</div>
									<div class="CPSectionContent BorderNoTop">
										#variables.strPageCustomFields.SustainingMembershipEditableContent#
									</div>
									<div class="BorderNoTop frmText" style="padding:10px;">
										<cfloop query="local.qrySustainingRates">
											<cfset local.thisAmount = local.qrySustainingRates.rateAmt>
											<cfif local.qrySustainingRates.frequencyName eq "Monthly">	
												<!--- Rate divisor inherited from parent subscription --->
												<cfset local.thisAmount = numberFormat((local.qrySustainingRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
											</cfif>		
											<cfset local.title = "sustaining_membership_#Lcase(local.qrySustainingRates.frequencyName)#">
													
											<label class="checkbox">
												<input <cfif structKeyExists(session.fieldArr, "sustaining_membership_#Lcase(local.qrySustainingRates.frequencyName)#")  >checked </cfif> type="checkbox" name="sustaining_membership_#Lcase(local.qrySustainingRates.frequencyName)#" value="#local.thisAmount#">
												<cfif local.qrySustainingRates.frequencyName eq "Full">
													1 Payment of 
												<cfelseif local.qrySustainingRates.frequencyName eq "Monthly">
													Monthly Payments of 
												</cfif>
												 #DollarFormat(local.thisAmount)#
											</label>
										</cfloop>
									</div>
									
									<cfset local.qryVoluntaryMembershipRates  =  application.objCustomPageUtils.sub_getRateSchedule( siteID = variables.siteID, scheduleUID ='f0450ac7-2eb9-460b-8694-081f36ed81e0',rateUID='4dff4557-40ea-405f-901c-e01799e93802', activeRatesOnly=true, ignoreRenewalRates=true) />								
									
									<cfif local.qryVoluntaryMembershipRates.recordCount>
										<div class="NVTitle  CPSectionTitle BorderNoTop">Voluntary Contribution</div>
										<div class="CPSectionContent BorderNoTop">
											#variables.strPageCustomFields.voluntaryContributionDesc#
										</div>
										<div class="BorderNoTop frmText" style="padding:10px;">
											
											<cfloop query="local.qryVoluntaryMembershipRates">
												<cfset local.thisAmount = local.qryVoluntaryMembershipRates.rateAmt>
												<cfif local.qryVoluntaryMembershipRates.frequencyName eq "Monthly">	
													<!--- Rate divisor inherited from parent subscription --->
													<cfset local.thisAmount = numberFormat((local.qryVoluntaryMembershipRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
												</cfif>		
												<cfset local.title = "voluntary_contribution_#Lcase(local.qryVoluntaryMembershipRates.frequencyName)#">
												
												<div class="row-fluid margBottom">
													<div class="span3">
														<label class="checkbox inline">
															<input <cfif structKeyExists(session.fieldArr, "voluntary_contribution_#Lcase(local.qryVoluntaryMembershipRates.frequencyName)#")  >checked </cfif>
																			 type="checkbox" name="voluntary_contribution_#Lcase(local.qryVoluntaryMembershipRates.frequencyName)#_checkbox">
															<cfif local.qryVoluntaryMembershipRates.frequencyName eq "Full">
																1 Payment of 
															<cfelseif local.qryVoluntaryMembershipRates.frequencyName eq "Monthly">
																Monthly Payments of 
															</cfif>
														</label>
													</div>
													<div class="span2 left10">
														<label class="radio inline">
															<input type="radio" name="voluntary_contribution_#Lcase(local.qryVoluntaryMembershipRates.frequencyName)#" value="#local.thisAmount#" class="form-control"> #DollarFormat(local.thisAmount)#
														</label>
													</div>
													<div class="span6 left10">
														<div class="span4" style="margin-bottom:0px!important;">
															<label class="radio inline">
															<input type="radio" name="voluntary_contribution_#Lcase(local.qryVoluntaryMembershipRates.frequencyName)#" value="otherAmount" class="form-control">
															Other Amount
															</label>
														</div>
														<div class="span8"><input type="text" name="otherAmount_#Lcase(local.qryVoluntaryMembershipRates.frequencyName)#" value=""></div>
													</div>
												</div>
											</cfloop>
										</div>
									</cfif>
									
									<div class="NVTitle  CPSectionTitle BorderNoTop">Totals</div>
									<div class="BorderNoTop frmText" style="padding:10px;">
																			
										<table id="total_table" width="100%" cellpadding="3" border="0" cellspacing="0">		
											<tr>
												<td id="membership_name" >


												</td>
												<td class="r"  id="membership_amount">

												</td>
											</tr>
											<tr>
												<td id="sustaing_membership_name" >

												</td>
												<td class="r" id="sustaing_membership_amount">
												
												</td>
											</tr>
											<tr>
												<td id="voluntary_contribution_name" >

												</td>
												<td class="r" id="voluntary_contribution_amount">
												
												</td>
											</tr>
											<tr>
												<td>
													Total Amount
												</td>
												<td class="r" id="total_amount">
												
												</td>
											</tr>
																											
										</table>
									</div>
									<div class=" NVTitle  CPSectionTitle BorderNoTop">Auto-Renew Membership</div>
									<div class="CPSectionContent BorderNoTop">
										#variables.strPageCustomFields.AutoRenewContent#
									</div>
									<div class="BorderNoTop frmText" style="padding:10px;">
										<cfloop array="#variables.renewAuto.columnValueArr#" index="variables.renewAuto">
											<label class="radio">
												<input  <cfif structKeyExists(session.fieldArr, "auto_renew") and session.fieldArr.auto_renew eq variables.renewAuto.columnValueString>checked </cfif>type="radio" name="auto_renew" value="#variables.renewAuto.columnValueString#">
												#variables.renewAuto.columnValueString#
											</label>
										</cfloop>
									</div>
									
								</div>
								
								<div class="frmButtons step2">
									<button id="gotoStep2" class="btn btnCustom" type="button" name="btnBack">Back</button>
									<button type="submit" name="btnToStep2" class="btn btnCustom pull-right">Continue</button>
								</div>
							</cfform>
						</div>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="step3">
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
						<div id="customPage">							
							<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
							<div id="formTable">
								<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">

									<cfset local.collection = arguments.event.getCollection()>
									<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("fa,amount,btnSubmit",local.key) 
										and left(local.key,7) neq "section"
										and left(local.key,9) neq "formfield"
										and left(local.key,4) neq "fld_">

										<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>

										<cfif NOT listFindNoCase("newSubs",local.key) >
											<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
										</cfif>
									</cfif>
									</cfloop>
									<cfif NOT structKeyExists(local.collection, "sustaining_membership_full") >
										<cfset local.del1 = structDelete(session.fieldArr,"sustaining_membership_full") >
									</cfif>

									<cfif NOT structKeyExists(local.collection, "sustaining_membership_monthly") >
										<cfset local.del1 = structDelete(session.fieldArr,"sustaining_membership_monthly") >
									</cfif>
									
									<cfif NOT structKeyExists(local.collection, "voluntary_contribution_full") >
										<cfset local.del1 = structDelete(session.fieldArr,"voluntary_contribution_full") >
									</cfif>

									<cfif NOT structKeyExists(local.collection, "voluntary_contribution_monthly") >
										<cfset local.del1 = structDelete(session.fieldArr,"voluntary_contribution_monthly") >
									</cfif>

									<cfinput type="hidden" name="fa" id="fa" value="processStep1">

									<cfif structKeyExists(session.fieldArr, "newSubs")>
										<cfset  local.subscriptions = session.fieldArr.newSubs>
									<cfelse>
										<cfset local.subscriptions = ""> 
									</cfif>

									<div  style="padding-bottom:15px;text-align:right">
										<a style="text-decoration: none;"id="gotoStep2" class="btn btnCustom" onClick="getFormFields();" href="javascript:;">Back</a>
									</div>
									<div class="CPSection step3">
										<div class="NVTitle CPSectionTitle Border">Select Your Committees and Sections:</div>
										<div class="cust_heading">
											<cfif application.objCMS.getZoneItemCount(zone='A',event=event)>
												#application.objCMS.renderZone(zone='A',event=event)#
											</cfif>
										</div>
										<div style="width:100%;padding:10px;" class="parent mrg0 BorderNoTop">
											<cfset local.i = 1>
											<cfloop list="#Listsort(structKeyList(local.allSections),'text')#" index="local.key">
												<cfif local.i eq 1 OR local.i eq 5>
													<div class="BodyText">
												</cfif>	
												<h2 style="margin-top:10px;">#local.key#</h2>
												<cfif local.allTypeName[local.key] eq "89B21701-3915-4586-B662-3EB7154A626D">
													#variables.strPageCustomFields.AdvocacyEditableContent#
												<cfelseif local.allTypeName[local.key] eq "FF418EB4-0461-4A6B-B9C0-A52C23C1AE66">
													#variables.strPageCustomFields.CommunityEditableContent#
												<cfelseif local.allTypeName[local.key] eq "67589875-F89E-455B-A23A-D73A46EF3F78">
													#variables.strPageCustomFields.MembershipEditableContent#
												<cfelseif local.allTypeName[local.key] eq "69FB1B2B-1F47-48E0-9144-1945602E5B0C">
													#variables.strPageCustomFields.SocialEditableContent#
												<cfelseif local.allTypeName[local.key] eq "91306F57-61A5-4AE1-85F6-6D604ABB3509">
													#variables.strPageCustomFields.SubstantiveEditableContent#
												<cfelseif local.allTypeName[local.key] eq "9A361430-A832-4636-86B7-B867DB34F893">
													#variables.strPageCustomFields.YoungLawyerEditableContent#
												</cfif>
												
												<cfloop list="#ArrayToList(StructSort(local.allSections[local.key]))#" index="local.key1">
													<label class="checkbox">
														<input <cfif listFindNoCase(local.subscriptions, "#local.key1#")>checked=checked</cfif> 
																onclick="showReviewSection(this)" type="checkbox"  class="check_price" value="#local.key1#" name="newSubs" id="sectionDivision1">#local.allSections[local.key][local.key1]#
													</label>
												</cfloop>

												<cfset local.i = local.i+1>
												<cfif local.i eq 5 OR local.i eq 7>
													</div>
												</cfif>
											</cfloop>
										</div>
										<div style="clear:both;"></div>
										<div class="NVTitle CPSectionTitle BorderNoTop">Current Selections:</div>
										<div class="cust_heading">
											<cfif application.objCMS.getZoneItemCount(zone='P',event=event)>
												#application.objCMS.renderZone(zone='P',event=event)#
											</cfif>
										</div>
										<div style="width:100%;padding:10px;" class="mrg0 BodyText BorderNoTop" id="review_section">
											
										</div>
										
									</div>
									<div class="frmButtons step1">
										<button id="gotoStep2" class="btn btnCustom" type="button" name="btnBack" onClick="getFormFields();">Back</button>
										<button type="submit" name="btnToStep2" class="btn btnCustom pull-right">Continue</button>
									</div>
								</cfform>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="backto1">
				<cfset local.memberData.phone = application.objMember.getMemberPhones(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID)>
				<cfset local.memberData.officeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Professional Address")>
				<cfsavecontent variable="local.returnHTML">
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfessionalLicenseTypes">
						select mplt.[PLTypeID], mplt.PLName
						from membercentral.dbo.ams_memberProfessionalLicenseTypes mplt
						where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">
						order by ordernum
					</cfquery>
					<cfinclude template="join_backto1.cfm">
				</cfsavecontent>
			</cfcase>
			
			<cfcase value="error">
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<div style="clear:both;"></div>
					<div style="margin:20px 0px"><p>There was an error processing your application.  Please contact your association for assistance.</p></div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfset local.returnHTML = afterAccountSelection(event=arguments.event)>
			</cfdefaultcase>
		</cfswitch>
		<cfset local.returnHTML = local.returnHTML &''& local.returnScript >
		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>	
	
	<cffunction name="afterAccountSelection" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>
		<!--- setup memberdata struct and prefill with logged in member --->
		<cfset local.memberData = {}>
		<cfset session.fieldarr = {}>
		<cfset local.memberData.phone = application.objMember.getMemberPhones(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID)>
		<cfset local.memberData.officeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Professional Address")>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfessionalLicenseTypes">
			select mplt.[PLTypeID], mplt.PLName
			from membercentral.dbo.ams_memberProfessionalLicenseTypes mplt
			where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">
			order by ordernum
		</cfquery>
		<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
		<cfif structKeyExists(session, "captchaEntered")>
			<cfset structDelete(session, "captchaEntered")>
		</cfif> 
		<cfsavecontent variable="local.returnHTML">
			<cfinclude template ="join_default.cfm">
		</cfsavecontent>		
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processStep1" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		

		<cfset local.sustainingFreq = '' >
		<cfset local.memAmt = 0 />
		<cfset local.susAmt = 0 />
		<cfset local.voluntaryAmt = 0>
		<cfset local.voluntaryFreq = ''>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"memberid") >
			<cfset local.memberid =session.fieldArr.memberid >
		<cfelse>
			<cfset local.memberid =arguments.event.getTrimValue('memberid') >
		</cfif>


		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"sustaining_membership_monthly") >
			<cfset local.sustaining =session.fieldArr.sustaining_membership_monthly >
			<cfset local.sustainingFreq = 'Monthly' >
		<cfelseif len(arguments.event.getValue('sustaining_membership_monthly',''))>
			<cfset local.sustaining =arguments.event.getTrimValue('sustaining_membership_monthly') >
			<cfset local.sustainingFreq = 'Monthly' >
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"sustaining_membership_full") >
			<cfset local.sustaining =session.fieldArr.sustaining_membership_full >
			<cfset local.sustainingFreq = 'Full' >
		<cfelseif len(arguments.event.getValue('sustaining_membership_full',''))>
			<cfset local.sustaining =arguments.event.getTrimValue('sustaining_membership_full') >
			<cfset local.sustainingFreq = 'Full' >
		</cfif>
		
		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"voluntary_contribution_monthly") >
			<cfset local.voluntary =session.fieldArr.voluntary_contribution_monthly >
			<cfset local.voluntaryFreq = 'Monthly' >
		<cfelseif len(arguments.event.getValue('voluntary_contribution_monthly',''))>
			<cfset local.voluntary =arguments.event.getTrimValue('voluntary_contribution_monthly') >
			<cfset local.voluntaryFreq = 'Monthly' >
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"voluntary_contribution_full") >
			<cfset local.voluntary =session.fieldArr.voluntary_contribution_full >
			<cfset local.voluntaryFreq = 'Full' >
		<cfelseif len(arguments.event.getValue('voluntary_contribution_full',''))>
			<cfset local.voluntary =arguments.event.getTrimValue('voluntary_contribution_full') >
			<cfset local.voluntaryFreq = 'Full' >
		</cfif>
		
		
		<cfset variables.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																	siteid=variables.siteID,
																	profilecode=variables.profile_1._profileCode,
																	pmid = local.memberid,
																	showCOF = local.memberid EQ session.cfcUser.memberData.memberID,
																	usePopup=false,
																	usePopupDIVName='ccForm',
																	autoShowForm=1)>
		<cfif len(variables.profile_1.strPaymentForm.headCode)>
			<cfhtmlhead text="#application.objCommon.minText(variables.profile_1.strPaymentForm.headCode)#">
		</cfif>
		
		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"membership") >
			<cfset local.membership =session.fieldArr.membership >
		<cfelse>
			<cfset local.membership =event.getValue('membership','') >
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates">
		 	select  r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,r.termAFEndDate as termEndDate,rf.rfid
			from
			dbo.sub_rates as r   
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
				and rs.status = 'A'
				and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
				and  r.isRenewalRate = 0
				and rs.uid = 'F93591DD-391B-43E7-BBA3-165881E786FF'
				and r.status = 'A'
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
				and rf.status = 'A'
				and rf.rfid = '#local.membership#'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.status = 'A'
			 ORDER BY r.rateName
		</cfquery>	

		<cfset local.isMonthlyRate = false>
		<cfset local.thisMembershipAmt = local.membershipRates.rateAmt>
		<cfif local.membershipRates.frequencyShortName eq "M">	

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetFullRate">
			 	select  top 1 r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,r.termAFEndDate as termEndDate,rf.rfid
				from
				dbo.sub_rates as r   
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
					and rs.status = 'A'
					and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
					and  r.isRenewalRate = 0
					and rs.uid = 'F93591DD-391B-43E7-BBA3-165881E786FF'
					and r.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.status = 'A'
					and f.frequencyShortName = 'F'
				where
					r.rateName = '#local.membershipRates.rateName#'
			</cfquery>

			<cfset local.isMonthlyRate = true>
			<!--- Rate divisor should not be greater than 12 --->
			<cfset local.rateCalcDivisor = 12>
			<cfif dateDiff("m", variables.todayDate, local.membershipRates.termEndDate) lt 12>
				<cfset local.rateCalcDivisor =  dateDiff("m", variables.todayDate, local.membershipRates.termEndDate)+1>
			</cfif>			
			<cfset local.thisMembershipAmt = numberFormat(local.qryGetFullRate.rateAmt/local.rateCalcDivisor,"___.__")>
		</cfif>
		
		<cfset local.qrySustainingRates  =  application.objCustomPageUtils.sub_getRateSchedule( siteID = variables.siteID, scheduleUID ='4c5705d6-b72e-47e1-8fb6-98537dbb424b',rateUID ='F0A979E1-11A2-4113-B92B-187220A950FB', activeRatesOnly=true, ignoreRenewalRates=true) /> 
		<cfif  len(local.sustainingFreq)>
			<cfloop query="local.qrySustainingRates">
				<cfif local.qrySustainingRates.frequencyName eq local.membershipRates.frequencyName>
					<cfset local.susAmt = local.qrySustainingRates.rateAmt>
					<cfif local.qrySustainingRates.frequencyName eq "Monthly">	
						<!--- Rate divisor inherited from parent subscription --->
						<cfset local.susAmt = numberFormat((local.qrySustainingRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
					</cfif>					
				</cfif>
			</cfloop>
		</cfif>
		<cfset local.totalAmount = local.susAmt+ local.thisMembershipAmt>
		
		<cfset local.qryVoluntaryMembershipRates  =  application.objCustomPageUtils.sub_getRateSchedule( siteID = variables.siteID, scheduleUID ='f0450ac7-2eb9-460b-8694-081f36ed81e0', rateUID='4dff4557-40ea-405f-901c-e01799e93802',activeRatesOnly=true, ignoreRenewalRates=true) />
		<cfif  len(local.voluntaryFreq)>
			<cfloop query="local.qryVoluntaryMembershipRates">
				<cfif local.qryVoluntaryMembershipRates.frequencyName eq local.membershipRates.frequencyName>
					<cfif arguments.event.getValue('voluntary_contribution_full','') EQ "otherAmount">
						<cfset local.voluntaryAmt = arguments.event.getValue('otherAmount_full')>
					<cfelse>
						<cfset local.voluntaryAmt = local.qryVoluntaryMembershipRates.rateAmt>
					</cfif>
					
					<cfif local.qryVoluntaryMembershipRates.frequencyName eq "Monthly">
						<!--- Rate divisor inherited from parent subscription --->
						<cfif arguments.event.getValue('voluntary_contribution_monthly') EQ "otherAmount">
							<cfset local.voluntaryAmt = numberFormat((arguments.event.getValue('otherAmount_monthly')*12)/local.rateCalcDivisor,"___.__")>
						<cfelse>
							<cfset local.voluntaryAmt = numberFormat((local.qryVoluntaryMembershipRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
						</cfif>
					</cfif>					
				</cfif>
			</cfloop>
		</cfif>
		<cfset local.totalAmount = local.voluntaryAmt+ local.totalAmount>
		
		<cfset local.amountToChargeNow = local.totalAmount>
		<cfset local.formName = "frmPACPay">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<script type="text/javascript"> 
					function _FB_validateForm() {

						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();

						if( MethodOfPaymentValue == 'CC' )	{
							#variables.profile_1.strPaymentForm.jsvalidation#	
						}
						
						// -----------------------------------------------------------------------------------------------------------------
						if (arrReq.length > 0) {
							var msg = 'Please address the following issues with your application:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
					
				function _FB_validateForm() {
					var thisForm = document.forms["#local.formName#"];
					var arrReq = new Array();

					$('.submitFrmBtn').attr("disabled", true);	
					 $(".submitTxt").show();

					if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
					var MethodOfPaymentValue = getMethodOfPayment();
					
					if( MethodOfPaymentValue == 'CC' )	{
						#variables.profile_1.strPaymentForm.jsvalidation#	
					}
					
					if (arrReq.length > 0) {
						var msg = 'Please address the following issues with your application:\n\n';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
						alert(msg);
						return false;
					}
					return true;
				}					
				</script>
			<div>
				<div  style="padding-bottom:15px;text-align:right">
					<a style="text-decoration: none;"id="gotoStep2" class="btn btnCustom" onClick="getFormFields();" href="javascript:;">Back</a>
				</div>
				<cfform name="#local.formName#" id="#local.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();" class="form-horizontal">
					<cfinput type="hidden" name="fa" id="fa" value="processStep2">
					<cfset local.collection = arguments.event.getCollection()>
					<cfloop collection="#arguments.event.getCollection()#" item="local.key">
						<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
							and NOT listFindNoCase("fa,btnSubmit",local.key) 
							and left(local.key,9) neq "formfield"
							and left(local.key,4) neq "fld_">
							<cfset session.fieldArr[local.key] = 	arguments.event.getValue(local.key)>
							<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
						</cfif>
					</cfloop>
								
					<div class="CPSection">
						<div class="CPSectionTitle NVTitle Border">Payment Information</div>
						<div class="BorderNoTop">
							<cfif application.objCMS.getZoneItemCount(zone='O',event=event)>
								#application.objCMS.renderZone(zone='O',event=event)#
							</cfif>
						</div>
						<div class="BorderNoTop" style="padding:10px;">
							<table cellspacing="0" cellpadding="4" border="0" width="100%">
								<tr>
									<td width="95%"><label>#local.membershipRates.rateName#</label> </td>
									<td class="r"><label>#dollarFormat(local.thisMembershipAmt)#&nbsp;</label></td>
								</tr>
								
								<cfif local.susAmt neq 0>
									<tr>
										<td width="95%"><label>Sustaining Membership</label></td>
										<td class="r"><label>#dollarFormat(local.susAmt)#&nbsp;</label></td>
									</tr>
								</cfif>
								
								<cfif local.voluntaryAmt neq 0>
									<tr>
										<td width="95%"><label>Voluntary Contribution</label></td>
										<td class="r"><label>#dollarFormat(local.voluntaryAmt)#&nbsp;</label></td>
									</tr>
								</cfif>

								<tr>
									<td width="95%"><label><b>Total Charge</b> &nbsp;</label></td><td class="r"><label><b>#numberFormat(local.totalAmount,"_$__.__")#</b>&nbsp;</label>
									</td>
								</tr>
							</table>
						</div>
						<div id="paymentTable">
							<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
							<div class="form">
								<cfif local.totalAmount gt 0>
									<div class="NVTitle CPSectionTitle BorderNoTop">Payment Method</div>
									<div class="BorderNoTop nvRow" style="padding:10px;">
										<label>Please select your preferred method of payment from the options.</label>
										
										<div class="row-fluid">
											<label class="radio">
												<input  value="CC" class="tsAppBodyText optionsRadio" name="payMeth" id="payMethCC" type="radio" onClick="checkPaymentMethod();">Credit Card
											</label>
											<label class="radio">
												<input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}">Check
											</label>
										</div>
									</div>
									<div id="CCInfo" style="display:none;">
										<div class="CPSectionTitle NVTitle BorderNoTop">Credit Card Information</div>
										<div id="ccForm" style="padding:10px;" class="BorderNoTop">
											<div class="row-fluid">#variables.profile_1.strPaymentForm.inputForm#</div>
											<div class="row-fluid"><button type="submit" name="btnSubmit" class="btn submitFrmBtn">SUBMIT</button><span class="submitTxt">Submission being processed...</span></div>
										</div>
									</div>
									<div id="CheckInfo" style="display:none;">
										<div class="CPSectionTitle BorderNoTop">Check Information</div>
										<div class="P add_pad BorderNoTop" style="padding:10px;">
											<p style="font-size:15px;font-weight:bold;">
											Please mail payment to Bar Association of Metropolitan St. Louis, 319 N 4th St, Suite 100, St. Louis MO 63102-1909<br/><br/>
											</p>
											<div class="add_pad" style="padding:10px;"><button type="submit" class="btn submitFrmBtn" name="btnSubmit">SUBMIT</button><span class="submitTxt">Submission being processed...</span></div>
										</div>
									</div>
								<cfelse>
									<div class="add_pad" style="padding:10px;"><button type="submit" class="btn submitFrmBtn" name="btnSubmit">SUBMIT</button></div>
								</cfif>
																
								
								<div id="CheckInfo" style="display:none;" class="CPSection BorderNoTop">
									<div class=""><button type="submit" class="btn submitFrmBtn" name="btnSubmit">SUBMIT</button></div>
								</div>
								
							
							</div>
						</div>
					</div>
					<div style="" class="frmButtons step1">
						<a style="text-decoration: none;"id="gotoStep2" class="btn btnCustom" onClick="getFormFields();" href="javascript:;">Back</a>
					</div>
				</cfform>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	
	 <cffunction name="processStep2" access="private" output="false" returntype="Query">
		<cfargument name="Event" type="any">
		<cfargument name="SectionsNames" type="struct">

		<cfset var local = structNew()>
		<cfset local.myArrayList = arrayNew(1)>		
		<!--- UPDATE MEMBER RECORD  --->
		<cftry>
			<cfset local.recordUpdated = false>
			<!--- <cfif isDefined("session.newMemIdArr") AND listFind(ArrayToList(session.newMemIdArr),arguments.event.getTrimValue('memberid'))>	 --->
				<cfscript>
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'));
					
					if (len(event.getValue('auto_renew',''))){
						if(event.getValue('auto_renew','') eq 'Yes'){
							local.objSaveMember.setCustomField (field='Auto-Renew', value='Yes');
						}else{
							local.objSaveMember.setCustomField(field='Auto-Renew', value='No');
						}
					}					
				</cfscript>
				<!--- Setting Captcha submitted flag --->
				<cfset session.captchaEntered = 1>	
				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
				<cfif local.strResult.success>
					<cfset local.recordUpdated = true>	
				<cfelse>					
					<cfset local.recordUpdated = false>	
				</cfif>
			<!--- </cfif> --->
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.recordUpdated = false>
		</cfcatch>
		</cftry>
		
		
		<!--- get card used for this submission --->
		<cfif event.getValue('payMeth','Check') eq "CC">
			<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
																mppid     = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'),
																memberID  = arguments.event.getTrimValue('memberid'),
																profileID = variables.profile_1._profileID)>			
		</cfif>
		
		<!--- get statecode from stateid --->
		<cfif len(event.getValue('business_stateID',''))>
			<cfquery name="local.qryGetBusinessState" dbtype="query">
				select stateName
				from variables.qryStates
				where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('business_stateID',0)#">
			</cfquery>
		</cfif>

		<!--- construct email / confirmation --->
		<cfset local.historyId  = application.objCustomPageUtils.mh_updateHistory(arguments.event.getTrimValue('memberid'),session.historyId,variables.getCategoryCompleted.SUBCATEGORYID,'Join form completed.',false)>
		<cfsavecontent variable="local.invoice">
			<cfoutput>
				<style>.highlightRow{background-color: yellow;}</style>
				<cfsavecontent variable="local.pageCSS">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.msgHeader{ background:##224563; color:##fff;font-weight:bold; padding:5px; }
						.frmText{ font-size:12pt; color:##505050; } 
						.b{ font-weight:bold; }
					</style>
					</cfoutput>
				</cfsavecontent>
				<!-- @accResponseMessage@ -->
				<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
				<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage ">			
				<tr class="msgHeader"><td colspan="2" class="b">MEMBER INFORMATION:</td></tr>
				
				<tr><td class="frmText" width="50%">MemberNumber:</td><td class="frmText">#event.getValue('memberNumber','')#&nbsp;</td></tr>
				<tr><td class="frmText">Member Type:</td><td class="frmText">#event.getValue('memberType','')#&nbsp;</td></tr>
				<tr><td class="frmText">Title:</td><td class="frmText">#event.getValue('title','')#&nbsp;</td></tr>
				<tr><td class="frmText">Professional Title:</td><td class="frmText">#event.getValue('professionalTitle','')#&nbsp;</td></tr>
				<tr><td class="frmText">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>
				<tr><td class="frmText">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>	
				<tr><td class="frmText">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>	
				<tr><td class="frmText">Suffix:</td><td class="frmText">#event.getValue('suffix','')#&nbsp;</td></tr>	
				<tr><td class="frmText">Firm/Company:</td><td class="frmText">#event.getValue('company_name','')#&nbsp;</td></tr>				
				<tr><td class="frmText">Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>

				<cfset local.empTypesAsString = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=event.getValue('mc_siteInfo.orgID'), columnName='Employment Type', valueIDList=arguments.event.getValue('empType','')),'|',', ','ALL')>
				<tr><td class="frmText">Employment Type:</td><td class="frmText">#local.empTypesAsString#&nbsp;</td></tr>
				<tr><td class="frmText">Proximity:</td><td class="frmText">#event.getValue('proximity','')#&nbsp;</td></tr>
				<tr><td class="frmText">Birthdate:</td><td class="frmText">#event.getValue('birthDate_new','')#&nbsp;</td></tr>
				<tr><td class="frmText">Gender:</td><td class="frmText">#event.getValue('gender','')#&nbsp;</td></tr>
				<tr><td class="frmText">Referred By:</td><td class="frmText">#event.getValue('referredby','')#&nbsp;</td></tr>	
				<tr><td class="frmText">Law School:</td><td class="frmText">#event.getValue('lawSchool','')#&nbsp;</td></tr>
				<tr><td class="frmText">Graduation Year:</td><td class="frmText">#event.getValue('grad_year','')#&nbsp;</td></tr>

				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
				<cfset local.qryProfessionalLicenseTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgID)>

				<cfset local.licenseStatus = 	{}>
				<cfset local.licenseDates  = ArrayNew(1)>
				<cfloop query="local.qryOrgProLicenseStatuses">
					<cfset local.licenseStatus[LCase(local.qryOrgProLicenseStatuses.statusName)] = local.qryOrgProLicenseStatuses.PLStatusID>	
				</cfloop>
				<cfset local.licensesNames  = ArrayNew(1)>
				<cfloop query="local.qryProfessionalLicenseTypes">
					<cfset local.licensesNames[local.qryProfessionalLicenseTypes.pltypeid] = local.qryProfessionalLicenseTypes.PLName>	
				</cfloop>
				<cfif event.getValue('professionalLicenseDropdown','') neq "">
					<cfset local.licenses = event.getValue('professionalLicenseDropdown')>
					<cfset  local.licenseArr = ListToArray(local.licenses)>

					<cfloop array="#local.licenseArr#" index="local.key">
						<cfset  local.license_no  = event.getValue('state_#local.key#_licensenumber')>
						<cfset  local.license_date  = event.getValue('state_#local.key#_licenseDate')>
						<cfset  local.licenseDates[local.key] = local.license_date >
						<cfset  local.license_status  = event.getValue('state_#local.key#_status')>
						<cfoutput>
							<tr>
								<td class="frmText">License State: </td>
								<td class="frmText">#local.licensesNames[local.key]# </td>
							</tr>
							<tr>
								<td class="frmText">License Date: </td>
								<td class="frmText">#local.license_date# </td>
							</tr>
							<tr>
								<td class="frmText">License No: </td>
								<td class="frmText">#local.license_no# </td>
							</tr>
							<tr>
								<td class="frmText">License Status: </td>
								<td class="frmText">#upperFirst(local.license_status)# </td>
							</tr>
						</cfoutput>
					</cfloop>
				</cfif>

				<cfset local.areas = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=event.getValue('mc_siteInfo.orgID'), columnName='Areas of Practice', valueIDList=arguments.event.getValue('practiceAreas','')),'|',', ','ALL')>
				<tr ><td class="frmText">Areas of Practice:</td><td class="frmText">#local.areas#&nbsp;</td></tr>
				
				

				<tr class="msgHeader"><td colspan="2" class="b">PROFESSIONAL ADDRESS:</td></tr>
				<tr ><td class="frmText">Address:</td><td class="frmText">#event.getValue('business_address','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">&nbsp;</td><td class="frmText">#event.getValue('business_address_2','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">City:</td><td class="frmText">#event.getValue('business_city','')#&nbsp;</td></tr>	
				
				<tr><td class="frmText">State/Province:</td><td class="frmText">
					#local.qryGetBusinessState.stateName#&nbsp;
				</td></tr>
				<tr ><td class="frmText">Zip:</td><td class="frmText">#event.getValue('business_zip','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Phone (business):</td><td class="frmText">#event.getValue('business_phone','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Phone (home/cell):</td><td class="frmText">#event.getValue('business_cell','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Fax:</td><td class="frmText">#event.getValue('business_fax','')#&nbsp;</td></tr>
				
				<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP TYPES:</td></tr>

				<cfset local.sustainingFreq = '' >
				<cfset local.memAmt = 0 />
				<cfset local.susAmt = 0 />
				<cfset local.voluntaryAmt = 0 />
				<cfset local.voluntaryFreq = ''>
				
				<cfif len(arguments.event.getValue('sustaining_membership_monthly',''))>
					<cfset local.sustaining =arguments.event.getTrimValue('sustaining_membership_monthly') >
					<cfset local.sustainingFreq = 'Monthly' >
				</cfif>

				<cfif len(arguments.event.getValue('sustaining_membership_full',''))>
					<cfset local.sustaining =arguments.event.getTrimValue('sustaining_membership_full') >
					<cfset local.sustainingFreq = 'Full' >
				</cfif>
				
				<cfif len(arguments.event.getValue('voluntary_contribution_monthly',''))>
					<cfset local.voluntary =arguments.event.getTrimValue('voluntary_contribution_monthly') >
					<cfset local.voluntaryFreq = 'Monthly' >
				</cfif>

				<cfif len(arguments.event.getValue('voluntary_contribution_full',''))>
					<cfset local.voluntary =arguments.event.getTrimValue('voluntary_contribution_full') >
					<cfset local.voluntaryFreq = 'Full' >
				</cfif>
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates" result="local.membershipRatesResult">
					select  r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,r.termAFEndDate as termEndDate,rf.rfid, f.uid as freqUID
					from
					dbo.sub_rates as r   
					inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
						and rs.status = 'A'
						and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
						and  r.isRenewalRate = 0
						and r.status = 'A'
						and rs.uid = 'F93591DD-391B-43E7-BBA3-165881E786FF'
					inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
						and rf.status = 'A'
						and rf.rfid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('membership')#">
					inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
						and f.status = 'A'
					 ORDER BY r.rateName
				</cfquery>

				<cfset local.thisMembershipAmt = local.membershipRates.rateAmt>
				<cfset local.subInvoiceMonths = 1>
				<cfif local.membershipRates.frequencyShortName eq "M">	
					<cfset local.subInvoiceMonths = 12 - month(now()) + 1>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetFullRate">
					 	select  top 1 r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,r.termAFEndDate as termEndDate,rf.rfid
						from
						dbo.sub_rates as r   
						inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
							and rs.status = 'A'
							and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
							and  r.isRenewalRate = 0
							and rs.uid = 'F93591DD-391B-43E7-BBA3-165881E786FF'
							and r.status = 'A'
						inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
							and rf.status = 'A'
						inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
							and f.status = 'A'
							and f.frequencyShortName = 'F'
						where
							r.rateName = '#local.membershipRates.rateName#'
					</cfquery>

					<!--- Rate divisor should not be greater than 12 --->
					<cfset local.rateCalcDivisor = 12>
					<cfif dateDiff("m", variables.todayDate, local.membershipRates.termEndDate) lt 12>
						<cfset local.rateCalcDivisor =  dateDiff("m", variables.todayDate, local.membershipRates.termEndDate)+1>
					</cfif>					
					<cfset local.thisMembershipAmt = numberFormat((local.membershipRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
				</cfif>			

				<cfset local.qrySustainingRates  =  application.objCustomPageUtils.sub_getRateSchedule( siteID = variables.siteID, 
																										scheduleUID ='4c5705d6-b72e-47e1-8fb6-98537dbb424b',
																										rateUID ='F0A979E1-11A2-4113-B92B-187220A950FB', 
																										activeRatesOnly=true, 
																										ignoreRenewalRates=true) />

				<cfif  len(local.sustainingFreq) >
					<cfloop query="local.qrySustainingRates">
						<cfif local.qrySustainingRates.frequencyName eq local.membershipRates.frequencyName>
							<cfset local.susAmt = local.qrySustainingRates.rateAmt>
							<cfif local.qrySustainingRates.frequencyName eq "Monthly">	
								<!--- Rate divisor inherited from parent subscription --->
								<cfset local.susAmt = numberFormat((local.qrySustainingRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
							</cfif>								
							<cfbreak>
						</cfif>
					</cfloop>
				</cfif>

				<cfset local.totalAmount = local.susAmt+ local.thisMembershipAmt>	
				
				<cfset local.qryVoluntaryMembershipRates  =  application.objCustomPageUtils.sub_getRateSchedule( siteID = variables.siteID,
																												 scheduleUID='f0450ac7-2eb9-460b-8694-081f36ed81e0',
																												 rateUID='4dff4557-40ea-405f-901c-e01799e93802',activeRatesOnly=true,
																												 ignoreRenewalRates=true) />

				<cfif  len(local.voluntaryFreq) >
					<cfloop query="local.qryVoluntaryMembershipRates">
						<cfif local.qryVoluntaryMembershipRates.frequencyName eq local.membershipRates.frequencyName>
						
							<cfif arguments.event.getValue('voluntary_contribution_full','') EQ "otherAmount">
								<cfset local.voluntaryAmt = arguments.event.getValue('otherAmount_full')>
							<cfelse>
								<cfset local.voluntaryAmt = local.qryVoluntaryMembershipRates.rateAmt>
							</cfif>
							
							<cfif local.qryVoluntaryMembershipRates.frequencyName eq "Monthly">
								<!--- Rate divisor inherited from parent subscription --->
								<cfif arguments.event.getValue('voluntary_contribution_monthly') EQ "otherAmount">
									<cfset local.voluntaryAmt = numberFormat((arguments.event.getValue('otherAmount_monthly')*12)/local.rateCalcDivisor,"___.__")>
								<cfelse>
									<cfset local.voluntaryAmt = numberFormat((local.qryVoluntaryMembershipRates.rateAmt*12)/local.rateCalcDivisor,"___.__")>
								</cfif>
							</cfif>	
														
							<cfbreak>
						</cfif>
					</cfloop>
				</cfif>

				<cfset local.totalAmount = local.voluntaryAmt+ local.totalAmount>	
				
				<cfset local.amountToChargeNow = local.totalAmount>

				<tr ><td class="frmText">#local.membershipRates.rateName#</td><td class="frmText">#dollarFormat(local.thisMembershipAmt)#</td></tr>

				<cfif local.susAmt neq 0>
					<tr ><td class="frmText">Sustaining Membership</td><td class="frmText">#dollarFormat(local.susAmt)#</td></tr>
				</cfif>
				
				<cfif local.voluntaryAmt neq 0>
					<cfset local.voluntarySubscriptionName = getSubscriptionNameFromUID(UID="fe9ef220-d84e-4e03-a1fa-ddca88ac2c1a")>
					
					<tr ><td class="frmText">#local.voluntarySubscriptionName#</td><td class="frmText">#dollarFormat(local.voluntaryAmt)#</td></tr>
				</cfif>

				<cfset local.strNewSubs = arguments.event.getValue('newSubs','')>
				<cfif len(local.strNewSubs)>
					
					<tr class="msgHeader"><td colspan="2" class="b">COMMITTEES AND SECTIONS:</td></tr>
					<cfloop list="#local.strNewSubs#" index="local.newsub">

						<cfoutput>
								<tr>
									<td class="frmText" colspan="2">
										#arguments.sectionsNames[local.newsub]#
									</td>
								</tr>
							
						</cfoutput>
					</cfloop>
				</cfif>
						
				<tr class="msgHeader"><td colspan="2" class="b">AUTORENEW:</td></tr>
				<tr ><td class="frmText">AutoRenew:</td><td class="frmText">
					<cfif event.getValue('auto_renew','') eq 'No'>
						No
					<cfelse>
						Yes
					</cfif>
					&nbsp;
				</td></tr>
				<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP PAYMENT METHOD:</td></tr>
				<tr>
					<td class="frmText">Membership Payment Method:</td><td class="frmText">
						<cfif event.getValue('payMeth','CC') EQ 'CC'>
							Credit Card
							<cfset arguments.event.setValue('p_#variables.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)))) />
							
							<cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid') gt 0>
								<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
										mppid     = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'),
										memberID  = val(event.getValue('memberID',0)),
										profileID = int(variables.profile_1._profileID)) />
								- #local.qrySavedInfoOnFile.detail#
							</cfif>	
						<cfelse>				
							Check
						</cfif>	
					</td>
				</tr>
				</table>
			</cfoutput>
		</cfsavecontent>

		<!--- CREATE SUBSCRIPTION ----------------------------------------------------------------------------- --->		

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
		<cfset local.subStruct = structNew()>
		<cfset local.subStruct.children = arrayNew(1)>

		<cfset local.subStruct.uid = "f53896ce-7d65-4f5e-8374-afbb26fe97d0">
		<cfset local.subStruct.rateUID = local.membershipRates.uid>
		<cfset local.subStruct.freqUID = uCase(local.membershipRates.freqUID)>

		<cfif len(trim(local.strNewSubs))>
			<cfloop list="#local.strNewSubs#" index="local.thisItem">
				<cfset local.childStruct = structNew()>
				<cfset local.childStruct.uid = local.thisItem />
				<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
			</cfloop>
		</cfif>				

		<cfif local.susAmt neq 0>
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = "199f03c5-25e8-47d5-867e-26550f5d1804" />
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>
		
		<cfif local.voluntaryAmt neq 0>
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = "fe9ef220-d84e-4e03-a1fa-ddca88ac2c1a" />
			<cfset local.childStruct.rateOverride = local.voluntaryAmt >
			<cfset local.childStruct.rateUID = '4dff4557-40ea-405f-901c-e01799e93802'>
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>

		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=arguments.event.getTrimValue('memberid'), subStruct=local.subStruct, newAsBilled=false,invoiceMonthd=local.subInvoiceMonths)>

		<cfset local.showErrorMsg = false>
		<cfif not local.subReturn.success>
			<!--- email association ----------------------------------------------------------------------------------------- --->
			<cfsavecontent variable="local.mailContent">
				<cfoutput>
				#local.pageCSS#
					The system was unable to create the subscriptions for this application and #arguments.event.getValue('firstName','')# #arguments.event.getValue('lastname','')# was not sent an email confirmation.<br />
					<hr />
				#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#
				</cfoutput>
			</cfsavecontent>

			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(variables.strEMailSettings_staff.to.replaceAll(',',';'),';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
				if (arrayLen(local.arrEmailTo)) {
					local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=variables.strEMailSettings_staff.from },
						emailto=local.arrEmailTo,
						emailreplyto=variables.strEMailSettings_staff.from,
						emailsubject=variables.strEMailSettings_staff.subject,
						emailtitle="#variables.organization# - #variables.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						emailAttachments=[],
						siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
						memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					);
				}
			</cfscript>

			<cfset local.showErrorMsg = true>
			<cfset local.errCode = 3>
			<!--- We may want an exception to see why the subscription was not created --->
		<cfelse>

			<!--- come back with invoices created, need to pay first one --->
			<!--- find the first invoice for the subscription and pay it --->
			<!--- find all invoices for associating CC 					 --->
			<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
			
			<cfquery dbtype="query" name="local.qryInvoiceDueNow">
				select invoiceID, invoiceProfileID, totalAmount as amount
				from [local].qryInvoice
				where dueNow=1
			</cfquery>

			<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
			<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->			
			<cfset local.useMID  = arguments.event.getTrimValue('memberid')>
			<cfset local.arrInvoicePaths = arrayNew(1)>

			<cfif local.totalAmountDueNow gt 0 and event.getValue('payMeth','CC') eq "CC" >
				
				<cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)>
					<cfset local.memberPayProfileID = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)>
				<cfelse>
					<cfset local.memberPayProfileID = 0 >
				</cfif>	

				<cfset local.strAccTemp = { totalPaymentAmount= local.totalAmountDueNow, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=arguments.event.getCollection() } >
				<cfif local.strAccTemp.totalPaymentAmount gt 0 and event.getValue('payMeth','') eq "CC">
					<cfset local.strAccTemp.payment = { detail=variables.profile_1._description, 
														amount=local.strAccTemp.totalPaymentAmount, 
														profileID=variables.profile_1._profileID, 
														profileCode=variables.profile_1._profileCode }>			

					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

					<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
					<cfif val(local.strACCResponse.paymentResponse.transactionID)>
						<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=local.useMID, subscriberID=0, bypassQueue=0)>
					</cfif>				

					<!--- Associate Card On File to Subscription --->
					<cfif local.totalAmountDueNow gt 0 and local.memberPayProfileID>
						<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=variables.profile_1._profileID, mppid=arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'))>
						<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=variables.profile_1._profileID, mppid=arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'))>				
					</cfif>				

				<cfelse>
					<cfset local.strACCResponse.accResponseMessage = "">
				</cfif>				
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif> <!---  // if local.totalAmountDueNow gt 0 and event.getValue('payMeth','CC') eq "CC"  --->

			<!--- ------------------------------------------ --->
			<!--- Checks And CCs should generate and invoice --->
			<!--- ------------------------------------------ --->
			<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfloop query="local.qryInvoice">
				<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=variables.siteID, 
																			invoiceID=local.qryInvoice.invoiceID, 
																			tmpFolder=local.strFolder.folderPath, 
																			encryptFile=true, 
																			namedForBundle=false)>
				<cfset arrayAppend(local.arrInvoicePaths,local.strInvoice.invoicePath)>
			</cfloop>

			<!--- email submitter (no error shown to user) --->
			<cfset local.emailSentToUser = TRUE>

			<cfsavecontent variable="local.mailContent">
				<cfoutput>
				#local.pageCSS#
				<cfif event.getValue('payMeth','CC') neq 'CC'>
					<p>Please mail payment to Bar Association of Metropolitan St. Louis, 319 N 4th St, Suite 100, St. Louis MO 63102-1909</p>
				</cfif>
				<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
				#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#
				</cfoutput>
			</cfsavecontent>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.strEMailSettings_member.from },
				emailto=[{ name="", email=variables.strEMailSettings_member.to }],
				emailreplyto=variables.strEMailSettings_staff.to,
				emailsubject=variables.strEMailSettings_member.SUBJECT,
				emailtitle="#variables.organization# - #variables.formNameDisplay#",
				emailhtmlcontent=local.mailContent,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=val(local.useMID),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>

			<cfset local.emailSentToUser = local.responseStruct.success>

			<!--- email staff (no error shown to user) --->
			<cftry>
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
						</cfif>
						<cfif NOT session.recordUpdated>
							<p><b>The member's record was NOT updated in Control Panel with any changes made on this application.</b></p>
						</cfif>
						#local.pageCSS#
						
						<cfset local.invoice = replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)>
						#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#
						<br>
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(variables.strEMailSettings_staff.to.replaceAll(',',';'),';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=variables.strEMailSettings_staff.from },
							emailto=local.arrEmailTo,
							emailreplyto=variables.strEMailSettings_staff.from,
							emailsubject=variables.strEMailSettings_staff.subject,
							emailtitle="#variables.organization# - #variables.formNameDisplay#",
							emailhtmlcontent=local.mailContent,
							emailAttachments=[],
							siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
							memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					}
				</cfscript>

				<cfcatch type="Any"></cfcatch>
			</cftry>
			<!--- relocate to message page --->
			<cfset session.invoice = replaceNoCase(replaceNoCase(replaceNoCase(replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all"),"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")>

		</cfif>	<!--- // if not local.subReturn.success --->

		<cfif local.showErrorMsg>
			<cfoutput>
				There was an error processing your application.  Please contact your association for assistance.
			</cfoutput>
		</cfif>
		
		<cflocation url="#variables.baselink#&fa=complete" addtoken="false">
	</cffunction> 

	<cffunction name="saveMember" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<!--- ensure card is selected --->
	

		<cfset local.myArrayList = arrayNew(1)>		
		<!--- UPDATE MEMBER RECORD  --->
		<cftry>
			<cfset local.recordUpdated = false>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'))>
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
			<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.event.getTrimValue('memberid'))>
				<cfscript>
				
				local.objSaveMember.setDemo(prefix=arguments.event.getValue('title',''),suffix=arguments.event.getValue('suffix',''),firstName=arguments.event.getValue('firstName',''),middleName=arguments.event.getValue('middleName',''), lastName=arguments.event.getValue('lastName',''),company=arguments.event.getValue('company_name',''));
				if (len(arguments.event.getValue('email'))) {
					local.objSaveMember.setEmail(type='Primary Email', value=arguments.event.getValue('email'));
				}
				
				local.objSaveMember.setAddress(type='Professional Address', address1=arguments.event.getValue('business_address',''),address2=arguments.event.getValue('business_address_2',''), city=arguments.event.getValue('business_city',''), stateID=arguments.event.getValue('business_stateID',0), postalCode=arguments.event.getValue('business_zip',''));

				local.objSaveMember.setPhone(addressType='Professional Address',type='Phone',value=arguments.event.getValue('business_phone',''));
				local.objSaveMember.setPhone(addressType='Professional Address',type='Fax',value=arguments.event.getValue('business_fax',''));
				local.objSaveMember.setPhone(addressType='Professional Address',type='Mobile',value=arguments.event.getValue('business_cell',''));
				
				local.objSaveMember.setRecordType(recordType='Individual');
				local.objSaveMember.setMemberType(memberType='User');
				
				if (len(arguments.event.getValue('lawSchool',''))){
					local.objSaveMember.setCustomField(field='Law School', value=arguments.event.getValue('lawSchool',''));
				}
				
				if (len(arguments.event.getValue('practiceAreas',''))){
					local.objSaveMember.setCustomField(field='Areas of Practice', valueID=arguments.event.getValue('practiceAreas','') );
				}

				if (len(arguments.event.getValue('gender',''))){
					local.objSaveMember.setCustomField(field='Gender', value=arguments.event.getValue('gender'));
				}
				if (len(arguments.event.getValue('grad_year',''))){
					local.objSaveMember.setCustomField(field='Law School Graduation Year', value=arguments.event.getValue('grad_year'));
				}
				</cfscript>
			</cfif> 
			<cfset local.licenseStatus = 	{}>
			<cfset local.licenseDates  = ArrayNew(1)>
			
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfessionalLicenseTypes">
				select mplt.[PLTypeID], mplt.PLName
				from membercentral.dbo.ams_memberProfessionalLicenseTypes mplt
				where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">
				order by ordernum
			</cfquery>
			<cfset local.licensesNames  = ArrayNew(1)>
			<cfloop query="local.qryProfessionalLicenseTypes">
				<cfset local.licensesNames[local.qryProfessionalLicenseTypes.pltypeid] = local.qryProfessionalLicenseTypes.PLName>	
			</cfloop>
			<cfif arguments.event.getValue('professionalLicenseDropdown','') neq "">
				<cfset local.licenses = arguments.event.getValue('professionalLicenseDropdown')>
				<cfset  local.licenseArr = ListToArray(local.licenses)>

				<cfloop array="#local.licenseArr#" index="local.key">
					<cfset  local.license_no  = arguments.event.getValue('state_#local.key#_licensenumber')>
					<cfset  local.license_date  = arguments.event.getValue('state_#local.key#_licenseDate')>
					<cfset  local.licenseDates[local.key] = local.license_date >
					<cfset  local.license_status  = arguments.event.getValue('state_#local.key#_status')>
					<cfset  memberid =arguments.event.getTrimValue('memberid') >
					
					<cfset  local.objSaveMember.setProLicense(name=local.licensesNames[local.key],status=local.license_status,license=local.license_no, date=local.license_date) >
				</cfloop>

			</cfif>
			<cfif len(arguments.event.getValue('professionalTitle','')) >
				<cfset local.objSaveMember.setCustomField(field='Professional Title', value=arguments.event.getValue('professionalTitle','') )>
			</cfif>
			<cfif len(arguments.event.getValue('memberType','')) >
				<cfset local.objSaveMember.setCustomField(field='Member Type', value=arguments.event.getValue('memberType','') )>
			</cfif>
			<cfif len(arguments.event.getValue('empType','')) >
				<cfset local.objSaveMember.setCustomField(field='Employment Type', valueID=arguments.event.getValue('empType','') )>
			</cfif>
			<cfif len(arguments.event.getValue('proximity',''))>
				<cfset local.objSaveMember.setCustomField(field='Proximity to St Louis', value=arguments.event.getValue('proximity'))>
			</cfif>
			<cfif len(arguments.event.getValue('birthDate_new',''))>
				<cfset local.birthdate = dateFormat(arguments.event.getTrimValue('birthDate_new',''), 'YYYY-MM-DD') />
				<cfset local.objSaveMember.setCustomField(field='Birthday', value=local.birthdate) />			
			</cfif>
			<cfif len(arguments.event.getValue('referredby',''))>
				<cfset local.objSaveMember.setCustomField(field='Member Referred By', value=arguments.event.getValue('referredby'))>
			</cfif>
						
			<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.event.getTrimValue('memberid'))>
				<cfif local.strResult.success>
					<cfset local.recordUpdated = true>	
				<cfelse>					
					<cfset local.recordUpdated = false>	
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.recordUpdated = false>
		</cfcatch>
		</cftry>
		<cfset session.recordUpdated = local.recordUpdated>

		<cfreturn session.recordUpdated>
	</cffunction>

	<cffunction name="getAvailableSections" access="private" output="false" returntype="query">
		
		<cfargument name="newSubs" type="string" required="false" default="">
		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAvailableSections">
				select r.rateID, r.rateName, s.uid, rf.rateAmt, s.subscriptionName, st.typeName,st.uid as sub_type_uid,s.subscriptionID, rf.rfid, 1 as canBeFree, 
					cast(s.subscriptionID as varchar(10)) + '|' + cast(r.rateID as varchar(10)) as subIDrateID
				from  dbo.sub_subscriptions s
					
				inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
					AND sch.status = 'A'
				inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
					and r.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.frequencyShortName = 'F'
					and f.status = 'A'
				inner join dbo.sub_types st on st.typeId = s.typeId
				WHERE sch.siteID = #variables.siteID#
				and s.status = 'A'
				AND sch.uid = '1E551D93-74B0-47C1-9E1F-3544083A4AEF'
		</cfquery>

		<cfreturn local.qryAvailableSections>
	</cffunction>
	<cffunction name="upperFirst" access="public" returntype="string" output="false" hint="I convert the first letter of a string to upper case, while leaving the rest of the string alone.">
			<cfargument name="name" type="string" required="true">
			<cfreturn uCase(left(arguments.name,1)) & right(arguments.name,len(arguments.name)-1)>
	</cffunction>
	
	<cffunction name="getSubscriptionNameFromUID" access="private" output="false" returntype="string">
		<cfargument name="UID" type="string" required="false" default="">
		<cfset var local = structNew()>
		<cfquery name="local.qryGetSubscriptionNameFromUID" datasource="#application.dsn.membercentral.dsn#">
			SELECT subs.subscriptionName
			FROM dbo.sub_types t
			INNER JOIN sub_subscriptions subs
				ON subs.typeID = t.typeID
				AND subs.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.UID#">
				AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
		</cfquery>
			
		<cfreturn local.qryGetSubscriptionNameFromUID.subscriptionName>
	</cffunction>
</cfcomponent>