<cfsavecontent variable="local.js">
	<script type="text/javascript">
		function b_search() {
			$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
			return true;
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.js,'\s{2,}',' ','ALL')#">

<cfoutput>
#showHeader(settings=local.qryBucketInfo, viewDirectory=arguments.viewDirectory)#

<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	<div class="control-group">
		<label class="control-label" for="s_fname">Case Type:</label>
		<div class="controls">
			<select name="s_fname"  id="s_fname">
				<option value="">All</option>
				<cfloop query="local.qryCaseTypes">
					<option value="#local.qryCaseTypes.caseType#" <cfif local.qryCaseTypes.caseType eq local.strSearchForm.s_fname>selected="selected"</cfif>>#local.qryCaseTypes.caseType#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_jurisdiction">County:</label>
		<div class="controls">
			<select name="s_jurisdiction"  id="s_jurisdiction">
				<option value="">All</option>
				<cfloop query="local.qryCounties">
					<option value="#local.qryCounties.county#" <cfif local.qryCounties.county eq local.strSearchForm.s_jurisdiction>selected="selected"</cfif>>#local.qryCounties.county#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_lname">Resolution Year:</label>
		<div class="controls">
			<select name="s_lname"  id="s_lname">
				<option value="">All</option>
				<cfloop query="local.qryYears">
					<option value="#local.qryYears.resolutionYear#" <cfif local.qryYears.resolutionYear eq local.strSearchForm.s_lname>selected="selected"</cfif>>#local.qryYears.resolutionYear#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_cat">Approval Status:</label>
		<div class="controls">
			<select name="s_cat"  id="s_cat">
				<option value="1" <cfif local.strSearchForm.s_cat eq "1">selected="selected"</cfif>>Yes</option>
				<option value="0" <cfif local.strSearchForm.s_cat eq "0">selected="selected"</cfif>>No</option>				
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_category">Full Entry Status:</label>
		<div class="controls">
			<select name="s_category"  id="s_category">
				<option value="1" <cfif local.strSearchForm.s_category eq "1">selected="selected"</cfif>>Yes</option>
				<option value="0" <cfif local.strSearchForm.s_category eq "0">selected="selected"</cfif>>No</option>				
			</select>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>any</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>