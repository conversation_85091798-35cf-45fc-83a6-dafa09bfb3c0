<cfsavecontent variable="javascriptIncludes">
	<cfoutput>
	<script language="javascript" src="/assets/common/javascript/getElementsByClassName.js"></script>
	<script language="JavaScript"> 
		
		function addCategory()
		{
			$.colorbox( {innerWidth:600, innerHeight:275, href:'#dataStruct.baseURL#&fsAction=addCategory&mode=direct&hideCommunityNav=1', iframe:true, overlayClose:false} );			
		}
		
		function closeBox() { $.colorbox.close(); location.reload();}
		
		function resizeBox() { $.colorbox.resize(); }
		
		function fs2JumpPage (pagenumber)
		{
			baselink = "#dataStruct.browseLink#&catID=#dataStruct.parentCategoryID#&byT=#dataStruct.byTreeID#&currSort=#dataStruct.currSort#&currOrder=#dataStruct.currOrder#&filter="
			maxPerPage = #dataStruct.MaxPerPage#;
			startRow = ((pagenumber-1) * maxPerPage) + 1;
			$('##fs2pagination-bottom').text('Loading ....');
			window.location.href = baselink + "&startrow=" + startRow;
		}


		<cfif dataStruct.NumTotalPages gt 1>
			$(document).ready(function () {
				$('##fs2pagination-top').twbsPagination({
					totalPages: #dataStruct.NumTotalPages#,
					startPage: #dataStruct.NumCurrentPage#,
					visiblePages: 5,
					first:"<<",
					last:">>",
					next:">",
					prev:"<",
					onPageClick: function (event, page) {
						$('##fs2pagination-top').text('Loading ....');
						fs2JumpPage(page);
					}
				});

				$('##fs2pagination-bottom').twbsPagination({
					totalPages: #dataStruct.NumTotalPages#,
					startPage: #dataStruct.NumCurrentPage#,
					visiblePages: 7,
					first:"<<",
					last:">>",
					next:">",
					prev:"<",
					onPageClick: function (event, page) {
						$('##fs2pagination-bottom').text('Loading ....');
						fs2JumpPage(page);
					}
				});

			});
		</cfif>

	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#javascriptIncludes#">

<cfset local.parentCatID = getParentCategoryID(dataStruct.parentCategoryID) />
<cfif dataStruct.qryGetSubCategories.RecordCount EQ 0 AND dataStruct.qryGetCategoryDocuments.recordcount EQ 0>
	<cfif local.parentCatID EQ "">
		<div class="row-fluid">
			<div class="span12">Warning: there are no files available here.</div>
		</div>
	<cfelse>
		<cflocation url="#dataStruct.browseLink#&catID=#local.parentCatID#&byT=#dataStruct.byTreeID#" addtoken="false">
	</cfif>
</cfif>


<cfoutput>
	<cfsavecontent variable="fileShareHeader">
		<div class="alert alert-info clearfix">
			<strong>#fileShareSettings.applicationInstanceName#</strong>
			<div class="pull-right">
				<cfif StructKeyExists(appRightsStruct, 'fsAddDocuments') AND appRightsStruct.fsAddDocuments eq 1>
					<button class="btn btn-info btn-mini" type="button" onclick="document.location.href='#dataStruct.uploadsURL#&from=browse<cfif dataStruct.parentCategoryID NEQ "">&catID=#dataStruct.parentCategoryID#</cfif>&byT=#dataStruct.byTreeID#';"><i class="icon-upload"></i>Upload file</button>
				</cfif>	
				<div class="btn-group">
					<a class="btn btn-info btn-mini dropdown-toggle" style="display:block;" data-toggle="dropdown" href="##">Browsing by #dataStruct.qryCurrentCategoryTree.categoryTreeName# <span class="caret"></span></a>
					<ul class="dropdown-menu">
						<cfloop query="dataStruct.qryGetCategoryTrees">
							<cfif dataStruct.qryGetCategoryTrees.categoryTreeID NEQ dataStruct.byTreeID>
								<li class=""><a href="#dataStruct.browseLink#&byT=#dataStruct.qryGetCategoryTrees.categoryTreeID#">Browse by #dataStruct.qryGetCategoryTrees.categoryTreeName#</a></li>
							</cfif>
						</cfloop>
					</ul>
				</div>				
			</div>
		</div>
	</cfsavecontent>
	<cfsavecontent variable="filesharebreadcrumbs">
		<cfif dataStruct.qryGetCategoryPathUp.recordcount>
			<ul class="breadcrumb">
				<li><a href="#dataStruct.browseLink#&byT=#dataStruct.byTreeID#">All #dataStruct.qryCurrentCategoryTree.categoryTreeName#</a></li>
				<cfloop query="dataStruct.qryGetCategoryPathUp" endrow="#dataStruct.qryGetCategoryPathUp.recordcount - 1#">
					<li><span class="divider">/</span><a href="#dataStruct.browseLink#&catID=#dataStruct.qryGetCategoryPathUp.categoryID#&byT=#dataStruct.byTreeID#">#dataStruct.qryGetCategoryPathUp.CategoryName#</a></li>
				</cfloop>
				<cfif dataStruct.parentCategoryID gt 0>
					<li><span class="divider">/</span><strong>#dataStruct.qryGetCategoryPathUp.CategoryName[dataStruct.qryGetCategoryPathUp.recordcount]#</strong></li>
				</cfif>
			</ul>
		</cfif>
	</cfsavecontent>
</cfoutput>


<cfif dataStruct.qryGetSubCategories.recordCount and dataStruct.qryGetCategoryDocuments.recordcount>
	<cfset filesharesidebarcolumns = 4 />
	<cfset fileshareMainAreaColumns = 8 />
	<cfset fileshareShowBreadcrumbsInPrimaryColumn = false/>
	<cfset fileshareShowToolbarInPrimaryColumn = true/>
<cfelseif dataStruct.qryGetSubCategories.recordCount and not dataStruct.qryGetCategoryDocuments.recordcount>
	<cfset filesharesidebarcolumns = 12 />
	<cfset fileshareMainAreaColumns = 0 />
	<cfset fileshareShowBreadcrumbsInPrimaryColumn = false/>
	<cfset fileshareShowToolbarInPrimaryColumn = false/>
<cfelse>
	<cfset filesharesidebarcolumns = 0 />
	<cfset fileshareMainAreaColumns = 12 />
	<cfset fileshareShowBreadcrumbsInPrimaryColumn = true/>
	<cfset fileshareShowToolbarInPrimaryColumn = true/>
</cfif>
<cfif fileshareMainAreaColumns>
	<cfset topHeaderExtraClasses = "visible-phone">
<cfelse>
	<cfset topHeaderExtraClasses = "">
</cfif>

<cfoutput>
	<div class="row-fluid #topHeaderExtraClasses#">
		<div class="span-12">
			#fileShareHeader#
		</div>
	</div>

	<div class="row-fluid">
		<cfif dataStruct.qryGetSubCategories.recordCount gt 0>
			<div class="span#filesharesidebarcolumns#">
			<!--- Category table --->
				#filesharebreadcrumbs#
				<table class="table table-hover table-striped">
					<tr>
						<td >
							<strong>#NumberFormat(dataStruct.numDocsInSubCats)#</strong> file<cfif dataStruct.numDocsInSubCats is not 1>s</cfif> in <strong>#dataStruct.qryGetSubCategories.RecordCount#</strong> <cfif dataStruct.qryGetCategoryPathUp.recordcount>sub</cfif>categor<cfif dataStruct.qryGetSubCategories.RecordCount is 1>y<cfelse>ies</cfif>
							<cfif StructKeyExists(appRightsStruct, 'fsAddSubFolder') AND appRightsStruct.fsAddSubFolder eq 1>
								<button class="btn btn-info btn-mini pull-right" type="button" onclick="addCategory();">Add category</button>
							</cfif>
						</td>
					</tr>
					<tbody id="subCategoriesListContainerFirstFew">
						<cfloop query="dataStruct.qryGetSubCategories" startrow="1" endrow="4">
							<cfif dataStruct.qryGetSubCategories.categoryCount gt 0 or dataStruct.qryGetSubCategories.documentCount gt 0>
								<cfset local.categoryOnclick = "document.location.href='#dataStruct.browseLink#&catID=#dataStruct.qryGetSubCategories.categoryID#&byT=#dataStruct.byTreeID#';"/>
							<cfelse>
								<cfset local.categoryOnclick = ""/>
							</cfif>
							<tr>
								<td class="span12" id="category-#dataStruct.qryGetSubCategories.categoryID#" onclick="#local.categoryOnclick#">
									<i class="pull-left icon-folder-close"></i>
									<i class="pull-right icon-chevron-right"></i>
									<span><strong>#ucase(dataStruct.qryGetSubCategories.categoryName)# <cfif dataStruct.qryGetSubCategories.categoryCount gt 0 or dataStruct.qryGetSubCategories.documentCount gt 0>(#Numberformat(dataStruct.qryGetSubCategories.documentCount)#)</strong></cfif></span><br />
								</td>
							</tr>
						</cfloop>
					</tbody>
					<cfif (dataStruct.qryGetSubCategories.recordcount gt 4) and structKeyExists(dataStruct,"qryGetCategoryDocuments") and dataStruct.qryGetCategoryDocuments.recordcount gt 0>
						<cfset local.subCategoriesListContainerTheRestClass = "hidden-phone"/>
						<tbody id="subCategoriesListContainerShowmore" class="visible-phone">
							<tr class="info" id="showMoreLink" onclick="$('##subCategoriesListContainerShowmore').removeClass('visible-phone');$('##subCategoriesListContainerShowmore').hide();$('##subCategoriesListContainerTheRest').removeClass('#local.subCategoriesListContainerTheRestClass#');">
								<td class="span12">
									<div class="text-center">
										<i class="pull-left icon-chevron-down"></i>
										<i class="pull-right icon-chevron-down"></i>
										<strong>SHOW ALL #dataStruct.qryGetSubCategories.recordcount# subcategories</strong>
									</div>
								</td>
							</tr>
						</tbody>
					<cfelse>
						<cfset local.subCategoriesListContainerTheRestClass = ""/>
					</cfif> 
					<tbody id="subCategoriesListContainerTheRest" class="#local.subCategoriesListContainerTheRestClass#">
						<cfloop query="dataStruct.qryGetSubCategories" startrow="5">
							<cfif dataStruct.qryGetSubCategories.categoryCount gt 0 or dataStruct.qryGetSubCategories.documentCount gt 0>
								<cfset local.categoryOnclick = "document.location.href='#dataStruct.browseLink#&catID=#dataStruct.qryGetSubCategories.categoryID#&byT=#dataStruct.byTreeID#';"/>
							<cfelse>
								<cfset local.categoryOnclick = ""/>
							</cfif>
							<tr>
								<td class="span12" id="category-#dataStruct.qryGetSubCategories.categoryID#" onclick="#local.categoryOnclick#">
									<i class="pull-left icon-folder-close"></i>
									<i class="pull-right icon-chevron-right"></i>
									<span><strong>#ucase(dataStruct.qryGetSubCategories.categoryName)# <cfif dataStruct.qryGetSubCategories.categoryCount gt 0 or dataStruct.qryGetSubCategories.documentCount gt 0>(#Numberformat(dataStruct.qryGetSubCategories.documentCount)#)</strong></cfif></span><br />
								</td>
							</tr>
						</cfloop>
					</tbody>
				</table>
				<br />
			</div>
		</cfif>
		<cfif fileshareMainAreaColumns>
			<div class="span#fileshareMainAreaColumns#">
				<cfif fileshareShowToolbarInPrimaryColumn>
					<div class="hidden-phone">#fileShareHeader#</div>
				</cfif>
				<cfif fileshareShowBreadcrumbsInPrimaryColumn>
					#filesharebreadcrumbs#
				</cfif>
				<div class="clearfix tsAppBB" style="margin-bottom:20px;">
					<b>#dataStruct.documentCount#</b> file(s) found
					<cfif ArrayLen(local.tmpSort)>
					<br/>Sort by:
					<cfloop from="1" to="#arraylen(local.tmpSort)#" index="local.arrEl">
						<cfset local.icon = "">
						<cfif listLen(local.tmpSort[local.arrEl],"|") gt 2 and len(trim(dataStruct.currSort)) and dataStruct.currSort eq getToken(local.tmpSort[local.arrEl],1,"|")> 
							<cfif getToken(local.tmpSort[local.arrEl],3,"|") eq "ASC">
								<cfset local.icon = "<i class='icon-arrow-down' style='margin-bottom:-3px;' title='Click to sort in descending order'></i>">
							<cfelse>
								<cfset local.icon = "<i class='icon-arrow-up' style='margin-bottom:-3px;' title='Click to sort in ascending order'></i>">
							</cfif>
						</cfif>					
						<a href="#dataStruct.browseLink#&catID=#dataStruct.parentCategoryID#&byT=#dataStruct.byTreeID#&startRow=1&sortType=#GetToken(local.tmpSort[local.arrEl],1,'|')#&sortOrder=#GetToken(local.tmpSort[local.arrEl],3,'|')#&filter=" <cfif GetToken(local.tmpSort[local.arrEl],1,"|") eq dataStruct.currSort>class="currSort "</cfif>>#GetToken(local.tmpSort[local.arrEl],2,"|")# #local.icon#</a><cfif local.arrEl lt arraylen(local.tmpSort)> &bull; </cfif>
					</cfloop>
				</cfif>	
					<cfif dataStruct.numTotalPages GT 1>
						<div class="pull-right pagination pagination-small" style="margin:0px 0px;">
							<ul id="fs2pagination-top" style="margin:0px 0px;"></ul>
						</div>
					</cfif>
				</div>


				<!--- Form count and display --->
				<cfif dataStruct.qryGetCategoryDocuments.recordcount>


				<!--- results table --->
				<cfset local.objDocVersions = CreateObject("component","model.admin.common.modules.documentDetails.documentDetails") />
				<cfoutput>
					<cfloop query="dataStruct.qryGetCategoryDocuments">
							<div class="tsAppBodyText tsDocViewerDocument s_row <cfif dataStruct.qryGetCategoryDocuments.currentrow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#dataStruct.qryGetCategoryDocuments.documentID#">
								<cfset icon = dataStruct.getIcon(dataStruct.qryGetCategoryDocuments.fileExt)>
								<cfset description = dataStruct.getFileDescription(dataStruct.qryGetCategoryDocuments.fileExt)>
								<a href="/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#" target="_blank" title="Click to view document"><img src="#application.paths.images.url#images/fileExts/#icon#" width="16" height="16" border="0" align="left" /></a>&nbsp;&nbsp;<a href="/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#" target="_blank" title="Click to view document"><b>#dataStruct.qryGetCategoryDocuments.docTitle#</b></a> <em>(#description#)</em>
								<br/>
								<cfif fileShareSettings.showTags EQ "1">
									<strong>Tags:</strong> #dataStruct.getTags(dataStruct.qryGetCategoryDocuments.docSiteResourceID, fileShareSettings.showTagsAsLinks,'<span class="label" style="font-weight:normal;">','</span>&nbsp;' )#<br/>
								</cfif>

								<cfloop query="fileShareSettings.customFields">
									<cfset local.columnData = dataStruct.methods.getExtraFSColumnDisplay(dataStruct.qryGetCategoryDocuments.docSiteResourceID, fileShareSettings.customFields.columnID) />
									<cfif listFind(fileShareSettings.showCustomFields,fileShareSettings.customFields.columnID) AND len(trim(local.columnData))>
										<cfif fileShareSettings.customFields.columnDesc EQ "Hot Document">
											<strong>#fileShareSettings.customFields.columnDesc#</strong><br />
										<cfelse>
											<strong>#fileShareSettings.customFields.columnDesc#:</strong> #local.columnData#<br/>
										</cfif>
									</cfif>
								</cfloop>					
								
								<cfif len(trim(dataStruct.qryGetCategoryDocuments.sectionPath))>
									<strong>Folder:</strong> #dataStruct.qryGetCategoryDocuments.sectionPath#<br />
								</cfif>
							
								<cfif val(fileShareSettings.showPublicationDate) AND len(trim(dataStruct.qryGetCategoryDocuments.publicationDate))>
									<strong>Publication Date:</strong> #DateFormat(dataStruct.qryGetCategoryDocuments.publicationDate, "MM/DD/YYYY")#<br />
								</cfif>
								
								<cfif val(fileShareSettings.showAuthor) AND len(trim(dataStruct.qryGetCategoryDocuments.author))>
									<strong>#fileShareSettings.authorLabel#:</strong> #dataStruct.qryGetCategoryDocuments.author#<br />
								</cfif>
								
								<cfif val(fileShareSettings.showContributedBy) AND len(trim(dataStruct.qryGetCategoryDocuments.firstName))>
									<strong>Contributor:</strong> #dataStruct.qryGetCategoryDocuments.firstName# #dataStruct.qryGetCategoryDocuments.lastName#<br />
								</cfif>
								
								<cfif val(fileShareSettings.showFirm) AND len(trim(dataStruct.qryGetCategoryDocuments.company))>
									<strong>Firm:</strong> #dataStruct.qryGetCategoryDocuments.company#<br />
								</cfif>
								
								<cfif val(fileShareSettings.showAddress)>
									<cfset local.address = getAddress(dataStruct.qryGetCategoryDocuments.contributorMemberID)>
									<cfif len(trim(local.address.address1))>
										<strong>Address:</strong> #local.address.address1# #local.address.address2# #local.address.city# , #local.address.code# #local.address.postalCode#<br />
									</cfif>
									<cfif len(trim(local.address.email))>
										<strong>Email:</strong> <A HREF="mailto:#local.address.email#">#local.address.email#</a><br />
									</cfif>
								</cfif>
								
								<cfif len(trim(dataStruct.qryGetCategoryDocuments.docDesc))>
									#dataStruct.qryGetCategoryDocuments.docDesc#<br />
								</cfif>
								
								<cfif fileShareSettings.showVersioning EQ "1">
									<strong>Version(s):</strong> #local.objDocVersions.getDocumentVersions(dataStruct.qryGetCategoryDocuments.documentLanguageID).recordCount#  
									<strong>Created:</strong> #DateFormat(dataStruct.qryGetCategoryDocuments.dateCreated, "MM/DD/YYYY")# #TimeFormat(dataStruct.qryGetCategoryDocuments.dateCreated, "hh:mm tt")#  
									<strong>Revised:</strong> #DateFormat(dataStruct.qryGetCategoryDocuments.dateModified, "MM/DD/YYYY")# #TimeFormat(dataStruct.qryGetCategoryDocuments.dateModified, "hh:mm tt")# 
								</cfif>
								
								<div id="#dataStruct.qryGetCategoryDocuments.docSiteResourceID#" style="display:none;">
									<a href="###dataStruct.qryGetCategoryDocuments.docSiteResourceID#"></a>
									<cfset local.document = local.objDocVersions.getDocumentVersions(dataStruct.qryGetCategoryDocuments.documentLanguageID) />
									<cfif local.document.recordcount gt 1>
										<table class="table table-condensed" style="margin-top:10px;" border="0">
											<tr><th>Source</th><th>File Name</th><th>Created</th><th>Revised</th></tr>
										<cfloop query="local.document">						
											<cfif local.document.isActive EQ 0>
											<tr>
												<td>#local.document.author#</td>
												<td><a href="/docDownload/#local.document.documentID#&VID=#local.document.documentVersionID#&lang=#local.document.languageCode#" target="_blank" title="Click to view document">#local.document.filename#</a></td>
												<td>#DateFormat(local.document.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.document.dateCreated, "hh:mm tt")#  </td>
												<td>#DateFormat(local.document.dateModified, "MM/DD/YYYY")# #TimeFormat(local.document.dateModified, "hh:mm tt")# </td>
											</tr>
											</cfif>
										</cfloop>
										</table>
									</cfif>
								</div>
								
								<br/><br/>
								<div class="btn-toolbar visible-phone">
									<button class="btn btn-large" onclick="document.location.href='/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#';" title="Download Document"><i class="fa fa-save"></i> &nbsp;Download</button>
									<cfif dataStruct.qryGetCategoryDocuments.fileExt eq "pdf">
										<button class="btn btn-large" onclick="javascript:docViewer2(#dataStruct.qryGetCategoryDocuments.documentID#,0);" title="Download Document"><i class="fa-solid fa-eye"></i> &nbsp;View</button>
									</cfif>
									<button class="btn btn-large" onclick="document.location.href='mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#';" title="Share Document by Email"><i class="fa-solid fa-share"></i> &nbsp;Email</button>
								</div>
								<div class="btn-toolbar hidden-phone">
									<div class="btn-group">
										<button class="btn btn-small" onclick="document.location.href='/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#';"><i class="fa fa-save" title="Download Document"></i> Download</button>
										<cfif dataStruct.qryGetCategoryDocuments.fileExt eq "pdf">
											<button class="btn btn-small" onclick="javascript:docViewer2(#dataStruct.qryGetCategoryDocuments.documentID#,0);"><i class="fa-solid fa-eye" title="Download Document"></i> View</button>
										</cfif>
										<cfif val(fileShareSettings.showShareButton)>
											<button class="btn btn-small" onclick="document.location.href='mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#';" title="Share Document by Email"><i class="fa fa-envelope"></i> Email</button>
										</cfif>	
										<cfif canEditAny or (canEditOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetCategoryDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
											<button class="btn btn-small" onclick="document.location.href='#dataStruct.editURL#&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#';"><i class="fa-solid fa-edit" title="Edit Document"></i> Edit</button>
										</cfif>
									</div>
									<div class="btn-group pull-right">
										<cfif local.document.recordCount gt 1>
											<button class="btn btn-small" onClick="javascript:togglePriorVersions(#dataStruct.qryGetCategoryDocuments.docSiteResourceID#);" title="View Prior Versions"><i class="icon-time"></i></button>
										</cfif>						
										<cfif canDeleteAny or (canDeleteOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetCategoryDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
											<button class="btn btn-small" onClick="javascript:copyDocument('/?event=cms.showResource&resID=#fileShareSettings.siteResourceID#&#baseQueryString#&fsAction=copyDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#');" title="Copy Document"><i class="icon-copy"></i></button>
											<button class="btn btn-small" onClick="javascript:moveDocument('/?event=cms.showResource&resID=#fileShareSettings.siteResourceID#&#baseQueryString#&fsAction=moveDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#');" title="Move Document"><i class="icon-move"></i></button>
										</cfif>
										<cfif canDeleteAny or (canDeleteOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetCategoryDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
											<button class="btn btn-small" onClick="javascript:confirmDelete('/?#baseQueryString#&fsAction=deleteDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#');" title="Delete Document"><i class="icon-remove"></i></button>
										</cfif>
										<cfif canDeleteAny>
											<cfif #dataStruct.qryGetCategoryDocuments.siteResourceStatusDesc# EQ "Active">
												<button class="btn btn-small" onClick="javascript:confirmHide('/?#baseQueryString#&fsAction=hideDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#');" title="Hide Document"><i class="icon-eye-close"></i></button>
											<cfelse>
												<button class="btn btn-small" onClick="javascript:confirmUnhide('/?#baseQueryString#&fsAction=unhideDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#');" title="Unhide Document"><i class="icon-eye-open"></i></button>
											</cfif>
										</cfif>
										<button class="btn btn-small" onclick="document.location.href='#dataStruct.baseURL#&from=browse&panel=sendNote<cfif dataStruct.parentCategoryID NEQ "">&catID=#dataStruct.parentCategoryID#&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#</cfif>&byT=#dataStruct.byTreeID#';" title="Notify Admin"><i class="icon-warning-sign"></i></button>
									</div>
								</div>
								<br/>
							</div>
					</cfloop>
				</cfoutput>
				<cfif dataStruct.numTotalPages GT 1>
					<div class="clearfix tsAppBT text-center" style="margin-top:20px;">
						<div class="pagination pagination-small">
							<ul id="fs2pagination-bottom"></ul>
						</div>
					</div>
				</cfif>
				
				</cfif>
			</div>
		</cfif>
	</div>
	
</cfoutput>

<cffunction name="getParentCategoryID" access="public" output="yes" returntype="string">
	<cfargument name="categoryID" required="yes" type="string">
	
	<cfset local.tag = "">
	
	<cfquery name="local.qryParent" datasource="#application.dsn.membercentral.dsn#">
		SELECT c.parentCategoryID
		FROM dbo.cms_categories AS c 
		WHERE c.categoryID = <cfqueryparam value="#arguments.categoryID#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	
	<cfreturn local.qryParent.parentCategoryID>
</cffunction>

<cffunction name="getAddress" access="public" output="yes" retuntype="string">
	<cfargument name="memberid" required="yes" type="string">
	
	<cfquery name="local.qyrAdd" datasource="#application.dsn.membercentral.dsn#">
		select email, address1, address2, city, code, postalcode
		from dbo.ams_members m
		inner join dbo.ams_memberEmailTypes t on t.orgID = m.orgID
		inner join dbo.ams_memberEmails e on e.memberID = m.memberID
		inner join dbo.ams_memberAddresses a on a.memberid = m.memberid
		inner join dbo.ams_states s on s.stateID = a.stateID
		where e.memberid =  <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER"> 

	</cfquery>
	
	<cfreturn local.qyrAdd>
</cffunction>