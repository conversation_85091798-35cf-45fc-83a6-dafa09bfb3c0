<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
			return true;
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	
	<div class="control-group">
		<label class="control-label" for="s_ownership">Document Bank:</label>
		<div class="controls">
			<select name="s_ownership"  id="s_ownership" onChange="b_refreshCourtDocs();">
				<cfif variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"search") and local.strSettings.search.allownationwide>
					<option value="0">Search Nationwide</option>
				<cfelseif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"search") and local.strSettings.search.allownationwide and StructKeyExists(local.strSettings,"searchoverrideguest") and local.strSettings.searchoverrideguest.allownationwide>
					<option value="0">Search Nationwide</option>
				</cfif>
				<cfloop query="local.qryBanks">
					<option value="#local.qryBanks.groupid#" <cfif local.strSearchForm.s_ownership is local.qryBanks.groupid>selected</cfif>>#local.qryBanks.Description#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_cause"><span id="bk_cd_ca_nm">Cause of Action</span>:</label>
		<div class="controls">
			<select name="s_cause"  id="s_cause" onChange="b_refreshCourtDocs();">
				<option value="0">All Categories</option>
				<cfoutput query="local.strCaseTypesAndDocTypes.qryCaseTypes">
					<option value="#local.strCaseTypesAndDocTypes.qryCaseTypes.caseTypeid#" <cfif local.strSearchForm.s_cause is local.strCaseTypesAndDocTypes.qryCaseTypes.caseTypeID>selected</cfif>>#local.strCaseTypesAndDocTypes.qryCaseTypes.Description#<cfif local.strCaseTypesAndDocTypes.showcategorycounts> (#local.strCaseTypesAndDocTypes.qryCaseTypes.DocCount#)</cfif></option>
				</cfoutput>
			</select>
			<span id="bk_cd_ca"></span>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_type"><span id="bk_cd_td_nm">Type of Document</span>:</label>
		<div class="controls">
			<select name="s_type"  id="s_type" onChange="b_refreshCourtDocs();">
				<option value="0">All Document Types</option>
				<cfoutput query="local.strCaseTypesAndDocTypes.qryDocumentTypes">
					<option value="#local.strCaseTypesAndDocTypes.qryDocumentTypes.documentTypeid#" <cfif local.strSearchForm.s_type is local.strCaseTypesAndDocTypes.qryDocumentTypes.documentTypeid>selected</cfif>>#local.strCaseTypesAndDocTypes.qryDocumentTypes.Description#<cfif local.strCaseTypesAndDocTypes.showcategorycounts> (#local.strCaseTypesAndDocTypes.qryDocumentTypes.DocCount#)</cfif></option>
				</cfoutput>
			</select>
			<span id="bk_cd_td"></span>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one"><b>Without</b> the words:</label>
		<div class="controls">
			<input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<cfif local.qryDocumentFlags.recordcount gt 0>
		<div class="control-group">
			<label class="control-label">Document Flags:</label>
			<div class="controls">
				<cfoutput query="local.qryDocumentFlags">
					<label class="checkbox inline">
						<input type="checkbox" name="s_docflags" class="mc_inlinecheckbox" value="#local.qryDocumentFlags.docflagid#">#local.qryDocumentFlags.name#
					</label><br/>
				</cfoutput>
			</div>
		</div>
	</cfif>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>