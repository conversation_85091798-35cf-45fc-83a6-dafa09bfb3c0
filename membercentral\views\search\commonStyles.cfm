<cfsavecontent variable="local.searchAppCSS">
	<cfoutput>
	<style type="text/css">
		##tsSearchAppWrapper .bk-text-light {color:##fff !important;}
		##tsSearchAppWrapper .bk-text-danger {color:##f83245!important}
		##tsSearchAppWrapper .bk-text-dim {color:##808080!important}
		##tsSearchAppWrapper .bk-text-center {text-align:center !important;}
		##tsSearchAppWrapper .bk-text-right {text-align:right !important;}
		##tsSearchAppWrapper .bk-align-baseline {vertical-align:baseline !important;}
		##tsSearchAppWrapper .bk-w-100 {width:100% !important;}
		##tsSearchAppWrapper .bk-h-100 {height:100% !important;}
		##tsSearchAppWrapper .bk-d-block {display:block !important;}
		##tsSearchAppWrapper .bk-d-flex {display:flex !important;}
		##tsSearchAppWrapper .bk-d-none {display:none !important;}
		##tsSearchAppWrapper .bk-flex-wrap {flex-wrap:wrap !important;}
		##tsSearchAppWrapper .bk-flex-column {flex-direction:column !important;}
		##tsSearchAppWrapper .bk-flex-grow {flex-grow:1 !important;}
		##tsSearchAppWrapper .bk-flex-align-center {align-items:center !important;}
		##tsSearchAppWrapper .bk-col {flex-basis:0;flex-grow:1;max-width:100%;padding-right:5px;padding-left:5px;}
		##tsSearchAppWrapper .bk-col-auto {flex:0 0 auto;width:auto;max-width:100%;padding-right:5px;padding-left:5px;}
		##tsSearchAppWrapper .bk-align-self-center{align-self:center !important;}
		##tsSearchAppWrapper .bk-justify-content-center{justify-content:center!important;}
		##tsSearchAppWrapper .bk-pt-0 {padding-top:0!important;}
		##tsSearchAppWrapper .bk-pt-1 {padding-top:0.5rem!important;}
		##tsSearchAppWrapper .bk-pb-2 {padding-bottom:.5em!important;}
		##tsSearchAppWrapper .bk-pr-0 {padding-right:0!important;}
		##tsSearchAppWrapper .bk-p-0 {padding:0!important;}
		##tsSearchAppWrapper .bk-p-1 {padding:.25em!important;}
		##tsSearchAppWrapper .bk-p-2 {padding:.5em!important;}
		##tsSearchAppWrapper .bk-p-2 {padding:.5em!important;}
		##tsSearchAppWrapper .bk-p-3 {padding:1em!important;}
		##tsSearchAppWrapper .bk-pr-3 {padding-right:1em!important;}
		##tsSearchAppWrapper .bk-pr-4 {padding-right:1.5em!important;}
		##tsSearchAppWrapper .bk-pl-3 {padding-left:1em!important;}
		##tsSearchAppWrapper .bk-pl-5 {padding-left:2em!important;}
		##tsSearchAppWrapper .bk-pb-0 {padding-bottom:0!important;}
		##tsSearchAppWrapper .bk-m-0 {margin:0!important;}
		##tsSearchAppWrapper .bk-m-1 {margin:.25em!important;}
		##tsSearchAppWrapper .bk-m-2 {margin:.5em!important;}
		##tsSearchAppWrapper .bk-mt-auto {margin-top:auto!important;}
		##tsSearchAppWrapper .bk-mt-0 {margin-top:0!important;}
		##tsSearchAppWrapper .bk-mt-1 {margin-top:.25em!important;}
		##tsSearchAppWrapper .bk-mt-2 {margin-top:.5em!important;}
		##tsSearchAppWrapper .bk-mt-3 {margin-top:1em!important;}
		##tsSearchAppWrapper .bk-mt-4 {margin-top:1.5em!important;}
		##tsSearchAppWrapper .bk-mt-5 {margin-top:2em!important;}
		##tsSearchAppWrapper .bk-mb-0 {margin-bottom:0!important;}
		##tsSearchAppWrapper .bk-mb-1 {margin-bottom:.25em!important;}
		##tsSearchAppWrapper .bk-mb-2 {margin-bottom:.5em!important;}
		##tsSearchAppWrapper .bk-mb-3 {margin-bottom:1em!important;}
		##tsSearchAppWrapper .bk-mb-4 {margin-bottom:1.5em!important;}
		##tsSearchAppWrapper .bk-mb-5 {margin-bottom:2em!important;}
		##tsSearchAppWrapper .bk-ml-2 {margin-left:.5em!important;}
		##tsSearchAppWrapper .bk-mr-1 {margin-right:.25em!important;}
		##tsSearchAppWrapper .bk-mr-2 {margin-right:.5em!important;}
		##tsSearchAppWrapper .bk-mr-3 {margin-right:1em!important;}
		##tsSearchAppWrapper .bk-mr-4 {margin-right:1.5em!important;}
		##tsSearchAppWrapper .bk-mr-5 {margin-right:2em!important;}
		##tsSearchAppWrapper .bk-font-size-xs { font-size:.79em; }
		##tsSearchAppWrapper .bk-font-size-sm { font-size:.85em; }
		##tsSearchAppWrapper .bk-font-size-md {font-size:.95em}
		##tsSearchAppWrapper .bk-font-weight-bold {font-weight:bold;}
		##tsSearchAppWrapper .bk-ml-auto {margin-left:auto !important;}
		##tsSearchAppWrapper .bk-mr-auto {margin-right:auto !important;}
		##tsSearchAppWrapper .bk-mx-auto {margin-left:auto !important;margin-right:auto !important;}
		##tsSearchAppWrapper .bk-border-gray {border-color:gray;}
		##tsSearchAppWrapper .bk-align-top {vertical-align:top;}
		##tsSearchAppWrapper .bk-text-nowrap {white-space:nowrap !important;}
		##tsSearchAppWrapper .bk-minh-104 {min-height:104px !important;}
		##tsSearchAppWrapper .bk-border-1 {border-width:1px!important;border-style:solid!important;}
		##tsSearchAppWrapper .bk-border-2 {border-width:2px !important;border-style:solid!important;}
		##tsSearchAppWrapper .bk-border-bottom {border-bottom: 1px solid ##eeeff8 !important;}
		##tsSearchAppWrapper .bk-price-card-selected { border-color: rgb(60, 115, 205) !important;border-width: 2px !important; }
		##tsSearchAppWrapper .bk-formcontrol {height:35px!important;box-sizing:border-box!important;margin:0!important;}

		##tsSearchAppWrapper .bk-card-box { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal;
			-ms-flex-direction: column;flex-direction: column; min-width: 0; word-wrap: break-word; background-color: ##fff; background-clip: border-box; box-sizing:border-box!important;
			border: 0 solid rgba(122, 123, 151, 0.3); border-radius: 0.65em; box-shadow: 0 .46875em 2.1875em rgba(0,0,0,.03),0 .9375em 1.40625em rgba(0,0,0,.03),0 .25em .53125em rgba(0,0,0,.05),0 .125em .1875em rgba(0,0,0,.03); }
		##tsSearchAppWrapper .bk-card-header { padding:.75em;margin-bottom:0; }
		##tsSearchAppWrapper .bk-card-body {padding:1.25em;}
		##tsSearchAppWrapper .bk-card-selected { border-color:rgb(214, 214, 214) !important;border-width:3px !important;}
		##tsSearchAppWrapper .bk-bg-secondary {background-color: ##f8f9ff !important;}
		##tsSearchAppWrapper .bk-shadow-none {-webkit-box-shadow: none !important;box-shadow: none !important;}
		##tsSearchAppWrapper .bk-price-badge {position:absolute;top:-12px;right:10px;background-color:##0073f7;border: 1px solid ##e0e0e0;border-radius: 999px;padding:6px 14px;font-size:10px !important;
			font-weight:500;color:white;box-shadow:0 2px 6px rgba(0, 0, 0, 0.1);font-size:10px;}
		##tsSearchAppWrapper .bk-dashed-container {background-color:##f0f8ff;border-style:dashed;border-width:1px;border-color:##7aa9d6;}
		##tsSearchAppWrapper .mc_add_link {display:inline-block !important;color:##007bff !important;text-decoration:underline !important;font-weight:500 !important;}
		##tsSearchAppWrapper .mc_add_link:hover {text-decoration:none !important;color: ##0056b3 !important;}

		##bk_content .bk-summary-circle { background-color:##4472c4; height:90px; width:90px; border-radius:50%; display:flex; align-items:center; justify-content:center; font-size:20px; text-align:center; line-height:24px; text-transform:uppercase; }
		##bk_content .bk-bg-chartblue { fill:##4472c4; }
		##bk_content .state{ fill: none; stroke: ##a9a9a9; stroke-width: 1;	}
		##bk_content .state:hover{ fill-opacity:0.5; }
		##bk_content ##MCSearchSummaryContainer {position:relative!important;}
		##bk_content ##bk_tooltip { position: absolute; text-align: center; padding: 20px; margin: 10px; font: 12px sans-serif; background-color: ##ffffff; border-radius: 2px; pointer-events: none;
			border: 1px solid ##ccc; border: 1px solid rgba(0, 0, 0, 0.2); border-radius:6px; width:auto; padding:4px; opacity:0; }
		##bk_content .bk-similar-search-circle {width:280px;}
		##bk_content .bk-list-search-circle {width:340px;}
		##bk_content .cursor-auto{ cursor:auto!important; }
		##bk_content .text-decoration-none{ text-decoration:none!important; }
		##tsSearchAppWrapper .mc_flexbox { display:-webkit-flex; display:-ms-flexbox; display:flex; flex-direction:row; }
		##tsSearchAppWrapper .mc_flexbox .mc_flexbox_col { flex:1; padding:20px; background:##eee }
		##tsSearchAppWrapper .bk-mc-tag { display:inline-flex; margin:0 8px 8px 0; align-items:center; border-radius:0.5em; border:1px solid ##dadce0; 
			background-color:##fff; height:2em; transition:all .2s cubic-bezier(0.4,0,0.2,1) 0s; padding:0.45em; font-size:15px; }
		##tsSearchAppWrapper .bk-mc-tag:hover { box-shadow:0 1px 5px 0 rgba(0,0,0,.1); color:##3c4043; border-color:##b2b3b5; text-decoration:none; }
		##tsSearchAppWrapper .bk-overlay {position: fixed;top: 0%;left: 0%;width: 100%;height: 100%;background-color:black;z-index:1001;opacity:.2;}
		##tsSearchAppWrapper .bk-active-card {z-index:1002;border:4px solid ##c1c1c1;}

		<cfif isDefined("local.rc") AND structKeyExists(local.rc,"viewDirectory") AND local.rc.viewDirectory EQ 'responsive'>
			##searchSponsorRightSideContainer ~ div.s_row {width:83%;}
			@media screen and (min-width:1401px){
				##searchSponsorRightSideContainer {width:25% !important;}
				##searchSponsorRightSideContainer ~ div.s_row {width:72% !important;}
			}
			@media screen and (max-width:1400px){
				##searchSponsorRightSideContainer {width:25% !important;}
				##searchSponsorRightSideContainer ~ div.s_row {width:72% !important;}
			}
			@media screen and (max-width:1199px){
				##searchSponsorRightSideContainer {width:21% !important;}
				##searchSponsorRightSideContainer ~ div.s_row {width:75% !important;}
			}
			@media screen and (max-width:1024px){
				##searchSponsorRightSideContainer ~ div.s_row {width:100% !important;}
			}
			@media screen and (min-width:978px) and (max-width:1024px){
				##searchSponsorRightSideContainer{display:none !important;}
			}
			@media screen and (max-width:979px){
				div##searchSponsorRightSideContainer {float:none;width:100%}
				##searchSponsorRightSideContainer ~ div.s_row {width:100%;}
			}
			@media (max-width: 992px) {
				##tsSearchAppWrapper .bk-flex-lg-row {flex-direction: row !important;}
				##tsSearchAppWrapper .bk-flex-lg-column {flex-direction: column !important;}
				##tsSearchAppWrapper .bk-mt-lg-2 {margin-top: 0.5em !important;}
			}
			@media (max-width: 576px) {
				##tsSearchAppWrapper .bk-flex-sm-column {flex-direction: column !important;}
				##tsSearchAppWrapper .bk-w-sm-100 {width:100% !important;}
			}

			/*default bootstrap style (to override tsApps style for now)*/
			##bk_content .form-horizontal input:not(.mc_inlinecheckbox):not(.datecontrol) {width: 206px;}
			##bk_content .form-horizontal select {width: 220px;}

			/*default bootstrap style (to override custom styles)*/
			##bk_content li {line-height: 20px;} 
			##bk_content ##seachResultsPaginationBar .pagination ul {margin:0px;}
			##bk_content ul.breadcrumb {margin: 0 0 20px;}

			div##tsSearchAppWrapper a.btn i[class^="icon-"] {vertical-align:middle;}
			div##s_listing .nav-list>.active>a {color: ##fff!important;}
			div##bk_content {padding:12px 8px 8px 10px;}
			div##bk_content form input.datecontrol {background-color:##fff;cursor:pointer; width:100px;}
			div##mobileViewOptionsHolder {padding-left:5px;}
			span##docCartCountDisplay {padding:1px 7px;}
			div##bk_content .bk_appIcons i[class^="icon-"] {margin-right:7px;}
			div##bk_content div.bk_subContent {margin-left: 20px;margin-top: 4px;}
			div##bk_content div.bk_smallfont {font-size:11pt;}

			##bk_content ##seachResultsPaginationBar .pager {margin: 2px;}
			##bk_content ##seachResultsPaginationBar .pager li>a, .pager li>span {padding: 2px 10px; font-size:13px;}
			##bk_content ##seachResultsPaginationBar .mobileViewPager .pager li {display: block;}
			##bk_content .bk-controls-large select {width: 300px;}
			##bk_content .bk-input-prepend .bk-add-on {height:100%!important;padding:8px 6px!important;}
			##bk_content .dataTables_wrapper input {box-sizing: unset!important;}
			##bk_content .dataTables_wrapper select, ##bk_content .dataTables_wrapper input {width:auto!important;}
			##tsSearchAppWrapper .m-10 { margin:10px; }
			##tsSearchAppWrapper .mb-10 { margin-bottom:10px; }
			##tsSearchAppWrapper .ml-10 { margin-left:10px; }
			##tsSearchAppWrapper .p-10 { padding:10px; }
			##tsSearchAppWrapper .py-10 { padding-bottom:10px; padding-top:10px; }
			##tsSearchAppWrapper .p-3 { padding:3px; }
			##tsSearchAppWrapper .p-0 { padding:0px; }
			
			@media (min-width: 480px){
				.form-medium .control-label {width:220px;}
				.form-medium .controls {margin-left:240px;}
			}
			@media (max-width: 600px) {
				##seachResultsPaginationBar .pagination ul>li>a, ##seachResultsPaginationBar .pagination ul>li>span {padding: 2px 7px; font-size: 15px;}
				##tsSearchAppWrapper .mc_flexbox { flex-direction:column; }
			}
			@media (max-width: 767px) {
				div##bk_content div.bk_subContent {margin-left: 5px;}
				##bk_content .bk-controls-large select {width: 255px;}
			}
			@media (min-width: 768px) and (max-width: 979px) {
				##bk_content .form-horizontal input:not(.mc_inlinecheckbox):not(.datecontrol) {width: 125px!important;}
				##bk_content .form-horizontal select {width: 139px!important;}
			}
			@media (min-width: 768px) and (max-width: 991px){
				div##s_listing div##bk_list{width: 44%;}
				div##s_listing div##bk_content{width: 53%;}
				div##s_listing div##bk_list .nav li a{font-size:small;}
			}
			@media (min-width: 768px) and (max-width: 1199px) {
				##bk_content .row-fluid .tablet-fullwidth [class*="span"] {float: none;width: 100%;}
				##bk_content .bk-controls-large select {width: 175px!important;}
			}
			@media (min-width: 992px){
				.form-medium-lg .control-label {width:220px;}
				.form-medium-lg .controls {margin-left:240px;}
				##bk_content .text-lg-center{text-align:center;}
			}
		<cfelse>
			##bk_content .bk-alert-error { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			##bk_content .bk-alert-success { background:##f1ffed url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border:2px solid ##00bf0f; }
		</cfif>
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.searchAppCSS,'\s{2,}',' ','ALL')#">