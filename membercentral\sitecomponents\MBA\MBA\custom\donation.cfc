<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="MCBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentCLAccountCode", type="STRING", desc="GL Account to be used.", value="30300" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you. Your donation has been submitted. You will receive an email from Maricopa County Bar Association with the information you provided." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMembershipInfo", type="CONTENTOBJ", desc="Content above Membership Information", value="Fields marked with a * are required." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="subscriptionTypeUID",type="STRING",desc="UID of the Subscription Type",value="fd89b396-46ab-4b4d-b090-79186c3021ff" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="subscriptionUID",type="STRING",desc="UID of the Root Subscription that this form offers",value="489ef6d0-2dd3-48fc-9139-************" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="FieldSetDonorInfo",type="STRING",desc="UID of the Donor Information fieldset",value="e89e749b-876d-405e-b95b-479f9821af5c" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);				
		local.tmpField = { name="memberAdditionalInformationIntroText", type="CONTENTOBJ", desc="Additional Information text for content block", value="Some optional info goes in here... " }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="memberInformationIntroText", type="CONTENTOBJ", desc="Information text for content block for memberships", value="Some optional info goes in here... " }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="MemberHistoryTypeCode", type="STRING", desc="Code of the Member History Type", value="FoundationDonation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="paymentDetailText", type="STRING", desc="Detail text for payment.", value="MCBF General Fund Online Donation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="revenueDetailText", type="STRING", desc="Detail text for revenue", value="MCBF General Fund Online Donation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);								
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmDonation',
			formNameDisplay='Donation Form',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode=variables.strPageFields.MemberHistoryTypeCode, subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode=variables.strPageFields.MemberHistoryTypeCode, subName='Completed');
		variables.historyStartedText = "Member started Donation form.";
		variables.historyCompletedText = "Member completed Donation form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				local.subStatus = processLookup();
				arguments.event.setValue('subStatus',local.subStatus);
				if(local.subStatus == 'billedjoinfound'){
					local.returnHTML = showError(errorCode='billedjoinfound');
				}else{
					local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
				}				
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}	
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());	
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				} 
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
/* 			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break; */				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
						
			<cfif len(variables.strPageFields.AccountLocatorIntroText)>
				<div id="AccountLocatorIntroText">#variables.strPageFields.AccountLocatorIntroText#</div><br/>
			</cfif>											
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.subscriptionTypeUID = variables.strPageFields.subscriptionTypeUID>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.subscriptionTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.subscriptionTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.subscriptionTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "nonefound">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(arguments.rc, "subStatus")>
			<cfset variables.subStatus = arguments.rc.subStatus>
		</cfif>			

		<!--- subStatus --->

		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.fieldSetDonorInfo, mode="collection", strData=local.strData)>			

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;

					if($("##fund_type").val() == "")
						arrReq[arrReq.length] = 'Donation - Select Donation amount.';

					$('##otherAmt').val( $('##otherAmt').val().replace(/\$|,/g,''));
					if( $("##fund_type").val() == "Other" &&  $("##otherAmt").is(':visible') && (($.trim($('##otherAmt').val()).length == 0) || ($.trim($('##otherAmt').val()).length > 0 && !amountRegex.test($.trim($('##otherAmt').val()))) || ($.trim($('##otherAmt').val()).length > 0 && $.trim($('##otherAmt').val()) == 0)  ) )
						arrReq[arrReq.length] = 'Donation - Enter a valid amount. Only positive amounts are allowed.';		

					if( !$("input[name='contributionType']:checked").val() )
						arrReq[arrReq.length] = 'Donation - Select the Frequency.';

					#local.strFieldSetContent1.jsValidation#			

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>

					$("##otherAmt").hide();
					<cfif (structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "Other")>
						$("##otherAmt").show();
					</cfif>

					$("##fund_type").change(function(){
						$("##otherAmt").val('');
						$("##otherAmt").hide();
						if($(this).val() == "Other")
							$("##otherAmt").show();
					});
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="subStatus" id="subStatus" value="#variables.subStatus#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<cfif len(variables.strPageFields.contentMembershipInfo)>
				<div>#variables.strPageFields.contentMembershipInfo#</div>
				<br/>
			</cfif>

			<div id="donation-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">Donation Information</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="3" border="0">
						<tbody>
							<tr valign="top">
								<td class="tsAppBodyText" width="10">* </td>
								<td class="tsAppBodyText" nowrap>Donation Amount</td>
								<td class="tsAppBodyText">&nbsp;</td>								
								<td class="tsAppBodyText">
									<select name="fund_type" id="fund_type">
										<option value="">Please Select</option>
										<option value="25" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "25">selected="true"</cfif>>$25</option>
										<option value="50" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "50">selected="true"</cfif>>$50</option>
										<option value="100" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "100">selected="true"</cfif>>$100</option>
										<option value="250" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "250">selected="true"</cfif>>$250</option>
										<option value="500" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "500">selected="true"</cfif>>$500</option>
										<option value="1000" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "1000">selected="true"</cfif>>$1,000</option>
										<option value="Other" <cfif structKeyExists(local.strData, "fund_type") and local.strData.fund_type eq "Other">selected="true"</cfif>>Other</option>
									</select>
									&nbsp;
									<input type="text" name="otherAmt" id="otherAmt" placeholder="Amount" style="display:none;" onblur="this.value=formatCurrency(this.value);" value="<cfif structKeyExists(local.strData, 'otherAmt')>#local.strData.otherAmt#</cfif>">
								</td>
							</tr>
							<tr valign="top">
								<td class="tsAppBodyText">* </td>
								<td class="tsAppBodyText">Frequency</td>
								<td class="tsAppBodyText">&nbsp;</td>
								<td class="tsAppBodyText">
									<label class="radio inline">
										<input type="radio" name="contributionType" id="contributionTypeOnteTime" value="OneTime" <cfif structKeyExists(local.strData, "contributionType") and local.strData.contributionType eq 'OneTime'>checked="checked"</cfif>> One-Time Donation &nbsp;
									</label>
									<cfif not listFindNoCase("activefound,acceptedfound,billedfound", variables.subStatus)>
										<label class="radio inline">
											<input type="radio" name="contributionType" id="contributionTypeRecurring" value="Recurring" <cfif structKeyExists(local.strData, "contributionType") and local.strData.contributionType eq 'Recurring'>checked="checked"</cfif>> Recurring Monthly Donation (until cancelled)
										</label>										
									</cfif>
								</td>
							</tr>
							<tr><td colspan="3">&nbsp;</td></tr>
							<tr valign="top">
								<td class="tsAppBodyText">&nbsp;</td>
								<td class="tsAppBodyText">Donor Comments</td>
								<td class="tsAppBodyText">&nbsp;</td>
								<td class="tsAppBodyText">
									<textarea name="donorComments" id="donorComments" rows="5" style="width:400px;"><cfif structKeyExists(local.strData, "donorComments")>#local.strData.donorComments#</cfif></textarea>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>				

			<div id="member-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent1.fieldSetContent#				
				</div>
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<cfset local.totalAmount = 0>
			<cfif arguments.rc.fund_type neq "Other">
				<cfset local.totalAmount = arguments.rc.fund_type>
			<cfelse>
				<cfif structKeyExists(arguments.rc, "otherAmt")>
					<cfset local.totalAmount = arguments.rc.otherAmt>
				</cfif>
			</cfif>	

			<cfset local.qty = 1>
			<cfif structKeyExists(arguments.rc, "contributionType") and arguments.rc.contributionType eq "Recurring">	
				<cfset local.qty = 12>
			</cfif>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(
													memberID=variables.useMID, 
													categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, 
													description=arguments.rc.donorComments, 
													enteredByMemberID=variables.useMID, 
													newAccountsOnly=false,
													qty=local.qty,
													dollarAmt=local.totalAmount)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			<cfelse>
				<cfset application.objCustomPageUtils.mh_updateHistory(
													memberID=variables.useMID, 
													historyID=variables.useHistoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, 
													description=arguments.rc.donorComments, 
													newAccountsOnly=false,
													qty=local.qty,
													dollarAmt=local.totalAmount)>				
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.totalAmount = 0>
		<cfif session.formFields.step1.fund_type neq "Other">
			<cfset local.totalAmount = session.formFields.step1.fund_type>
		<cfelse>
			<cfif structKeyExists(session.formFields.step1, "otherAmt")>
				<cfset local.totalAmount = session.formFields.step1.otherAmt>
			</cfif>
		</cfif>

		<cfset local.paymentRequired = (local.totalAmount gt 0)>

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC ]>
			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMemberInfo"
			)>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Donation Total</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.totalAmount)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>

		<cfset local.totalAmount = 0>
		<cfif session.formFields.step1.fund_type neq "Other">
			<cfset local.totalAmount = session.formFields.step1.fund_type>
		<cfelse>
			<cfif structKeyExists(session.formFields.step1, "otherAmt")>
				<cfset local.totalAmount = session.formFields.step1.otherAmt>
			</cfif>
		</cfif>

		<cfset local.qty = 1>
		<cfif session.formFields.step1.contributionType eq "Recurring">	
			<cfset local.qty = 12>
		</cfif>

		<cfset application.objCustomPageUtils.mh_updateHistory(
											memberID=variables.useMID, 
											historyID=variables.useHistoryID, 
											subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
											description=session.formFields.step1.donorComments, 
											newAccountsOnly=false,
											qty=local.qty,
											dollarAmt=local.totalAmount)>

		<cfif session.formFields.step1.contributionType eq "Recurring">			

			<!--- create subscriptions --->
			<cfset local.totalAmountDueNow = local.totalAmount>
			<cfset local.totalAmount = local.totalAmount * 12>

				<cfset local.subStruct = structNew()>
				<cfset local.subStruct.uid = variables.strPageFields.subscriptionUID>
				<cfset local.qryRate = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(
						memberID=variables.useMID, 
						subscriptionUID=local.subStruct.uid, 
						isRenewalRate=0,
						frequencyShortName="M")>
 				<cfif local.qryRate.recordcount>
					<cfset local.subStruct.rateUID = uCase(local.qryRate.rateUID)>
					<cfset local.subStruct.freqUID = uCase(local.qryRate.freqUID)>
				<cfelse>
					<cfset local.subStruct.rateUID = "">
				</cfif>
				<cfset local.subStruct.rateOverride = local.totalAmount>

			<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
			<cfset local.subReturn = local.objSubReg.autoSubscribe(
					event=arguments.event, 
					memberID=variables.useMID, 
					subStruct=local.subStruct, 
					newAsBilled=false)> 

			<!--- find the invoice for the subscription and pay it --->
			<!--- find all invoices for associating CC 				 --->
			<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,orgID=variables.orgID, siteID=variables.siteID)>
			<cfquery dbtype="query" name="local.qryInvoiceDueNow">
				select invoiceID, invoiceProfileID, totalAmount as amount
				from [local].qryInvoice
				where dueNow=1
			</cfquery>

			<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
				<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
			<cfelse>
				<cfset local.memberPayProfileID = 0 >
			</cfif>

			<!--- -------------------------------------------------- --->
			<!--- Save card on file to subscription and all invoices --->
			<!--- -------------------------------------------------- --->
			<cfif local.totalAmount gt 0 and local.memberPayProfileID>
				<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
				<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			</cfif>

			<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.PaymentProfileCodeCC>
				<!--- ---------------------- --->
				<!--- Payment and accounting --->
				<!--- ---------------------- --->
				<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
				<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.PaymentProfileCodeCC>
					<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

					<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
					<cfif val(local.strACCResponse.paymentResponse.transactionID)>
						<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
					</cfif>
				<cfelse>
					<cfset local.strACCResponse.accResponseMessage = "">
				</cfif>
				<cfset local.returnstruct.strACCResponse = local.strACCResponse>
			</cfif>			

		<cfelse>

			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->			
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmount, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.event.getCollection() } >
			<cfset local.strAccTemp.payment = { detail=variables.strPageFields.paymentDetailText, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=variables.strPageFields.PaymentProfileCodeCC }>
			<cfset local.strAccTemp.revenue  = [{revenueGLAccountCode=variables.strPageFields.PaymentCLAccountCode, detail=variables.strPageFields.revenueDetailText, amount=local.strAccTemp.totalPaymentAmount}]>
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>		

		</cfif>	<!--- // if session.formFields.step1.contributionType eq "Recurring" --->

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>	

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.fieldSetDonorInfo, mode="confirmation", strData=arguments.rc)>

		<cfset local.totalAmount = 0>
		<cfif session.formFields.step1.fund_type neq "Other">
			<cfset local.totalAmount = session.formFields.step1.fund_type>
		<cfelse>
			<cfif structKeyExists(session.formFields.step1, "otherAmt")>
				<cfset local.totalAmount = session.formFields.step1.otherAmt>
			</cfif>
		</cfif>

		<cfset local.paymentRequired = true >
		<cfif not val(local.totalAmount)>
			<cfset local.paymentRequired = false >
		</cfif>
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationMessage)>
				<div>#variables.strPageFields.ConfirmationMessage#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div>Here are the details of your donation:</div><br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Donation Information</td>
			</tr>				
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<table cellpadding="3" border="0" cellspacing="0">
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Donation Amount: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">#dollarFormat(local.totalAmount)#</td>
					</tr>
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Frequency: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">#session.formFields.step1.contributionType#</td>
					</tr>		
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Donor Comments: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">#session.formFields.step1.donorComments#</td>
					</tr>									
					</table>
				</td>
			</tr>
			</table>	
			<br>
			#local.strFieldSetContent1.fieldSetContent#			

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>					
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteid,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to Knoxville Bar Association", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your donation.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.errorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script language="javascript">
						window.location = "/?pg=renew";
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>