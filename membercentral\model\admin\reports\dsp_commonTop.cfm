<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.dataHead">
	<style type="text/css">
	div#csvsettings div.frmcsvsettingsheader { background-color:#E0E0E0; padding:6px 0 6px 5px; font-weight:bold; }
	div#csvsettings table tr.csv_in_row { cursor:pointer; }
	div#csvsettings table tr.csv_in_row:hover { background:rgba(0,159,254,0.45); }
	div#csvsettings table tr.sorting-row { cursor:move; background:rgba(0,159,254,0.45); }
	div#reportDefs input.reportdate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
	</style>
	<style type="text/css" media="print">
		div#reportDefs, div##reportActionBar { display:none; }
	</style>
	<cfoutput><script src="/assets/admin/javascript/reports.js#local.assetCachingKey#"></script></cfoutput>
</cfsavecontent>		

<cfif arguments.event.valueExists('rptID')>
	<cfif arguments.event.getValue('qryReportInfo').recordcount is 0>
		<cflocation url="#this.link.showReport#" addtoken="no">
	</cfif>

	<cfsavecontent variable="local.dataHead">
		<cfoutput>
		#local.dataHead#
		<script src="/assets/common/javascript/jQueryAddons/rowSorter/jquery.rowsorter.1.0.0.MC.js"></script>
		<script language="JavaScript">
			mcrpt_link_showreport = '#this.link.showReport#&rptID=#arguments.event.getValue("qryReportInfo").reportID#';
			mcrpt_link_copyreport = '#this.link.copyReport#';
			mcrpt_link_csvsavesettings = '#this.link.csvSaveSettings#&rptID=#arguments.event.getValue("qryReportInfo").reportID#';
			mcrpt_link_runreportcustomcsv = '#this.link.runReport#&rptID=#arguments.event.getValue("qryReportInfo").reportID#&reportAction=customcsv';
			mcrpt_link_savereportextra = '#this.link.saveReportExtra#&rptId=#arguments.event.getValue("qryReportInfo").reportID#';
			mcrpt_link_listreportruleversions = '#this.link.listReportRuleVersions#&ruleID=#arguments.event.getValue("qryReportInfo").ruleID#';
			#toScript(arguments.event.getValue("qryReportInfo").reportID,"mcrpt_rptid")#
			#toScript(arguments.event.getValue('mca_tt',''),"mcrpt_ttid")#
			#toScript(this.siteResourceID,"mcrpt_srid")#
			#toScript(variables.AllowScheduling,"mcrpt_allowsch")#
			<cfif local.canEditReport>
				let #toScript("#local.editScheduledReportLink#&rptId=#arguments.event.getValue("qryReportInfo").reportID#&rptRunFormats=#arrayToList(variables.runformats)#","mcrpt_link_schedrpt")#
				let #toScript(arguments.event.getValue("qryReportInfo").ruleID,"mcrpt_ruleid")#
			</cfif>

			$(function() { 
				writeReportRunStatement(#val(arguments.event.getValue('qryReportInfo').runCount)#,'#dateFormat(arguments.event.getValue('qryReportInfo').lastRunDate,'m/d/yyyy')#','#EncodeForJavaScript(arguments.event.getValue('qryReportInfo').lastRunBy)#',mcrpt_allowsch);
			});
		</script>
		</cfoutput>
	</cfsavecontent>		
	<cfhtmlhead text="#local.dataHead#">

	<cfsavecontent variable="local.data">
		<cfoutput>
		<div class="d-flex">
			<h4 id="editRptNameBtn">
				<span class="text-uppercase rptName">#EncodeForHTML(arguments.event.getValue('qryReportInfo').reportname)#</span>
				<cfif local.canEditReport AND val(arguments.event.getValue('qryReportInfo').isReadOnly) eq 0>
					<button class="btn btn-link" type="button" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="bottom" title="Change the name of this report" onClick="editReportName();">
						<span class="btn-wrapper--icon"><i class="fa-light fa-pen-to-square"></i></span>
					</button>
				</cfif>
			</h4>
			<cfif local.canEditReport AND val(arguments.event.getValue('qryReportInfo').isReadOnly) eq 0>
				<div id="editRptNameContainer" class="w-75 d-none">
					<form name="frmReportRename" id="frmReportRename">
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group">
										<div class="input-group">
											<input type="text" name="rptName" id="rptName" value="#encodeForHTMLAttribute(arguments.event.getValue('qryReportInfo').reportname)#" class="form-control">
											<div class="input-group-append">
												<button type="button" name="btnRenameReport" id="btnRenameReport" class="btn input-group-text" onclick="saveReportName(#arguments.event.getValue("qryReportInfo").reportID#);">
													Save
												</button>
											</div>
											<label for="rptName">Report Name</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="ml-auto">
					<div class="btn-group btn-group-sm">
						<button class="btn btn-secondary" type="button" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="bottom" title="Copy this report and all its settings" onClick="$(this).tooltip('hide'); $(this).prop('disabled', true); copyReport(#arguments.event.getValue("qryReportInfo").reportID#,'#arguments.event.getValue("qryReportInfo").toolType#');">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-copy"></i></span>
							<span class="btn-wrapper--label">Copy</span>
						</button>
						<button class="btn btn-secondary" type="button" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="bottom" title="Create a new version of this report" onClick="$(this).tooltip('hide'); $(this).prop('disabled', true); self.location.href='#this.link.showReport#';">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
							<span class="btn-wrapper--label">New</span>
						</button>
					</div>
				</div>
			</cfif>
		</div>

		<div id="reportActionBar" class="row mt-2 mb-3">
			<div class="col-md-12">
				<div class="card card-box mb-1 <cfif arguments.event.getValue('qryReportInfo').status EQ 'I'>bg-warning</cfif>">
					<div class="card-header py-1 <cfif arguments.event.getValue('qryReportInfo').status EQ 'I'>alert-warning</cfif>">
						<div class="card-header--title font-size-lg">
							About This Report <cfif arguments.event.getValue('qryReportInfo').status EQ 'I'><span class="badge badge-warning float-right mt-1">Inactive</span></cfif>
						</div>
					</div>
					<div class="card-body pb-3 <cfif arguments.event.getValue('qryReportInfo').status EQ 'I'>alert-warning</cfif>">
						<div class="font-size-lg">#arguments.event.getValue('mc_adminNav.currentNavigationItem.navName')#</div>
						<cfif len(arguments.event.getValue('mc_adminNav.currentNavigationItem.navDesc'))>
							<div>#arguments.event.getValue('mc_adminNav.currentNavigationItem.navDesc')#</div>
						</cfif>
						<cfif NOT (local.canEditReport AND val(arguments.event.getValue('qryReportInfo').isReadOnly) eq 0)>
							<div class="alert alert-warning mt-1 mb-2">This report is read only; the report settings cannot be changed.</div>
						</cfif>
					</div>
					<div class="card-footer <cfif arguments.event.getValue('qryReportInfo').status EQ 'I'>alert-warning</cfif>" id="divRptActionBarRunStatement"></div>
				</div>
				<cfif variables.AllowScheduling>
					<cfif val(arguments.event.getValue('qryReportInfo').runCount) eq 0>
						<div id="schedRptsContainer" class="d-none"></div>
					<cfelse>
						<div id="schedRptsContainer"></div>
					</cfif>
					<script id="mc_schedRptList" type="text/x-handlebars-template">
					
						<div class="card card-box mt-3">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title">
									<div class="font-size-lg">Scheduled Report Settings</div>
									<div class="small text-dim">Save time by having this report emailed on a set schedule to one or more recipients.</div>
								</div>
							</div>
							<div class="card-body pb-3">
								<cfif arguments.event.getValue('qryReportInfo').status EQ 'I'>
									<div class="alert alert-warning" role="alert">This report is inactive and will skip any schedules defined below.</div>
								</cfif>
								{{##if arrschedulereports}}
									<table class="table table-sm table-hover table-borderless m-2">
										<tbody>
											{{##each arrschedulereports}}
												<tr>
													<td>
														<i class="fa-light fa-calendar-days mr-3"></i>Report will email <u data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="{{toemail}}">{{toemaillength}} recipient{{##compare toemaillength '>' 1}}s{{/compare}}</u> every {{##if interval}}{{interval}} {{/if}}{{intervaltypename}}
														{{##compare endrundate.length '>' 0}}{{##compare (moment endrundate) '>' (moment)}}	until {{moment endrundate format='M/D/YYYY'}}. Next run is {{nextrundate}} CT.{{else}} until {{moment endrundate format='M/D/YYYY'}}.{{/compare}}{{else}}. Next run is {{nextrundate}} CT.{{/compare}}
													</td>
													<cfif local.canEditReport>
														<td class="text-right">
															<a href="##" onclick="editScheduledReport({{itemid}});return false;" class="ml-2"><i class="fa-light fa-calendar-days mr-2"></i>Modify</a>
															<a href="##" id="delSchedRpt_{{itemid}}" onclick="deleteScheduledReport({{itemid}},{{reportid}});return false;" class="ml-3" data-usagemode="savedreports" data-confirm="0"><i class="fa-light fa-calendar-days mr-2"></i>Delete</a>
														</td>
													</cfif>
												</tr>
											{{/each}}
										</tbody>
									</table>
								{{else}}
									<p class="mb-0">This report does not have any scheduled deliveries</p>
								{{/if}}
								<cfif val(arguments.event.getValue('qryReportInfo').isReadOnly) eq 1>
									<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning mb-0" role="alert">
										<span class="font-size-lg d-block d-40 mr-2 text-center">
											<i class="fa-solid fa-triangle-exclamation"></i>
										</span>
										<span>
											Changes to this report may affect the delivery and the content and format expected by its recipients.
											<cfif local.canEditReport>
												<div class="mt-2">
													<button type="button" id="markReportEditableBtn" class="btn btn-sm btn-secondary" onclick="markReportAsEditable(#arguments.event.getValue("qryReportInfo").reportID#);">
														<span class="btn-wrapper--icon"><i class="fa-regular fa-pen-to-square"></i></span>
														<span class="btn-wrapper--label">Edit Anyway</span>
													</button>
													<button type="button" class="btn btn-sm btn-secondary" onclick="$(this).prop('disabled', true); copyReport(#arguments.event.getValue("qryReportInfo").reportID#,'#arguments.event.getValue("qryReportInfo").toolType#');">
														<span class="btn-wrapper--icon"><i class="fa-regular fa-copy"></i></span>
														<span class="btn-wrapper--label">Copy this Report</span>
													</button>
												</div>
											</cfif>
										</span>
									</div>
								</cfif>
							</div>
							<cfif local.canEditReport>
								<div class="card-footer">
									<span class="ml-2">
										<a class="btn btn-sm btn-info" href="javascript:editScheduledReport(0);"><i class="fa-light fa-calendar-check mr-2"></i>Add a New Schedule</a>
									</span>
								</div>
							</cfif>
						</div>

					</script>
					<script type="text/javascript">
						let #toScript(local.strScheduledReports,"mcrpt_strSchedRpts")#
						populateScheduledReports(mcrpt_strSchedRpts);
					</script>
				</cfif>
			</div>
		</div>
		<div id="err_report" class="alert alert-danger my-2 d-none"></div>
		<div id="rptHeadingBar" class="row mt-3 mb-5">
			<div class="col-md-12">
				<div class="card card-box mb-1">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-size-lg">
							Report Settings
						</div>
					</div>
					<div class="card-body pb-3">
		</cfoutput>
	</cfsavecontent>
<cfelse>
	<cfsavecontent variable="local.dataHead">
		<cfoutput>
		#local.dataHead#
		<script language="JavaScript">
			let savedReportsTable;
			mcrpt_link_copyreport = '#this.link.copyReport#';
			<cfif local.qryCountSavedReports.theCount gt 0>
				$(function() { 
					savedReportsTable = $('##savedReportsTable').DataTable({
					"processing": true,
					"serverSide": true,
					"language": {
						/* remove the words from around the page size control*/
						"lengthMenu": "_MENU_"
					},
					"ajax": { 
						"url": "#local.resultsList#",
						"type": "post"
					},
					"autoWidth": false,
					"columns": [
						{
						"data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? data.reportName : data;
						},
						"width": "40%",
						"className": "align-top"
						},
						{
							"data": null,
							"render": function ( data, type, row, meta ) {
								return type === 'display' ? '<div>'+ data.lastrundatetime +'</div><div class="mt-1 text-dim">'+data.lastrunname+'</div>' : data;
							},
							"width": "19%",
							"className": "align-top"
						},
						{
							"data": null,
							"render": function ( data, type, row, meta ) {
								return type === 'display' ? '<div>'+ data.createddatetime +'</div><div class="mt-1 text-dim">'+data.creatorname+'</div>' : data;
							},
							"width": "19%",
							"className": "align-top"
						},
						{
							"data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									if(row.loadReport == 1) {
										renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="loadReport('+row.reportID+');return false;" title="View Report"><i class="fa-solid fa-eye"></i></a>';
									} else {
										renderData += '<a class="btn btn-sm px-2 m-1"><i class="fa-solid fa-eye"></i></a>';
									}
									if(row.copyReport == 1) {
										renderData += '<a href="##" class="btn btn-sm btn-outline-info px-2 m-1" onclick="copyReport('+row.reportID+',\''+row.toolType+'\');return false;" title="Copy Report"><i class="fa-solid fa-copy"></i></a>';
										renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="statusChange('+row.reportID+',\''+(row.status === 'I' ? 'A' : 'I')+'\',\''+row.reportName.replace(/'/g, "\\'").replace(/"/g, '\\"')+'\');return false;" title="'+row.switchtext+'"><i class="fa-solid fa-shuffle"></i></a>';
									} else {
										renderData += '<a class="btn btn-sm px-2 m-1"><i class="fa-solid fa-copy"></i></a>';
									}
									if (data.deleteReport == 1) {
										renderData += '<a href="##" id="btnDelRpt'+row.reportID+'" class="btn btn-sm btn-outline-danger px-2 m-1" onclick="deleteReport('+row.reportID+');return false;" title="Delete Report" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"width": "22%",
							"className": "text-center"
						}

						],
						"order": [[1, 'desc']],
						"createdRow": function( row, data, index ) {debugger;
							if (data.status != "A") {
								$('td', row).eq(0).append('<span class="badge badge-warning float-right ml-2">Inactive</span>');
							}
						}
					});
				});
			</cfif>
			<cfif arguments.event.getValue('err',0) is 1>
				$(function() { rptShowAlert('There was an error saving this report.'); });
			</cfif>

			// Status change function for Load Existing Report DataTable
			function statusChange(thisReportID, statusChangeTo, reportName) {debugger;
				// If activating, proceed directly
				if (statusChangeTo === 'A') {
					executeStatusChange(thisReportID, statusChangeTo);
					return;
				}

				// If inactivating, check for schedules first
				if (statusChangeTo === 'I') {
					checkReportSchedulesAndConfirm(thisReportID, reportName);
				}
			}
			function deleteReport(rid) {
				var deleteResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						savedReportsTable.ajax.reload();
					} else {
						delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						alert('We were unable to delete this Report. Try again.');
					}
				};

				let delBtn = $('##btnDelRpt'+rid);
				mca_initConfirmButton(delBtn, function(){
					let objParams = { rptID:rid };
					TS_AJX('SAVEDREPORT', 'deleteReport', objParams, deleteResult, deleteResult, 10000, deleteResult);
				});
			}
		</script>
		<style type="text/css">
		.report-row { padding: 5px 0; }
		</style>
		</cfoutput>
	</cfsavecontent>		
	<cfhtmlhead text="#local.dataHead#">

	<cfsavecontent variable="local.data">
		<cfoutput>	
			<h4>#arguments.event.getValue('mc_adminNav.currentNavigationItem.navName')#</h4>
			<cfif len(arguments.event.getValue('mc_adminNav.currentNavigationItem.navDesc'))>
				<div>#arguments.event.getValue('mc_adminNav.currentNavigationItem.navDesc')#</div>
			</cfif>
			<div id="err_report" class="alert alert-danger my-2 d-none"></div>
			<cfif local.canEditReport>
				<div class="row mt-4 mb-3">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-lg">
									Create Report
								</div>
							</div>
							<div class="card-body pb-3">
								<cfform name="frmReportName" id="frmReportName" action="#this.link.saveNewReport#" method="post" onSubmit="return valStep1();">
									<cfinput type="text" name="frmRN" id="frmRN" size="72" autocomplete="off" maxlength="200" class="form-control form-control-sm" placeholder="Give this report a name, like #session.cfcuser.memberdata.firstname#'s #arguments.event.getValue('mc_adminNav.currentNavigationItem.navName')#">
									<button type="submit" class="btn btn-sm btn-primary mt-2">Start a New Report</button>
								</cfform>
							</div>
						</div>
					</div>
				</div>
			</cfif>
			<cfif local.qryCountSavedReports.theCount gt 0>
				<div class="row mt-4 mb-3">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-lg">
									Load Existing Report
								</div>
							</div>
							<div class="card-body pb-3">
								<table id="savedReportsTable" class="table table-sm table-striped table-bordered" style="width:100%">
									<thead>
										<tr>
											<th>Report</th>
											<th>Last Run</th>
											<th>Created</th>
											<th>Actions</th>
										</tr>
									</thead>
								</table>
							</div>
						</div>
					</div>
				</div>
			<cfelseif NOT local.canEditReport>
				<div class="row mt-4 mb-3">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-lg">
									Load Existing Report
								</div>
							</div>
							<div class="card-body pb-3">
								There are no saved reports to run.
							</div>
						</div>
					</div>
				</div>
			</cfif>
		</cfoutput>
	</cfsavecontent>
</cfif>