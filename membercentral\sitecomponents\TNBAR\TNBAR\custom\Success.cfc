<cfcomponent extends="model.customPage.customPage" output="false">
	<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			variables.baseURL = "/?pg=success";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.crlf = chr(13) & chr(10);
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

			
			variables.link.updatePhoto = variables.baseURL &"&fa=updatePhoto" ;
			variables.link.uploadAndCropPhoto = variables.baseURL &"&mode=direct&fa=uploadAndCropPhoto" ; 
			variables.link.savePhoto = variables.baseURL &"&mode=direct&fa=savePhoto" ;  

			variables.useMID = arguments.event.getValue('memberID',0);
			// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button", value="Account Lookup" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see the name for whom you are submitting, please press the Choose button next to the name. If you do not see the name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Success Submissions" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ContactInformation", type="CONTENTOBJ", desc="Contact Information Content", value="Please add or update the contact information below. If you are submitting a Career Move, please be sure to update the Firm field." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SuccessSubmission", type="CONTENTOBJ", desc="Success Submission Content", value="Select Type of NewsPlease select the type of news you wish to report from the options below and fill out the requested fields." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="OptionalSubmissionInfo", type="CONTENTOBJ", desc="Optional Submission Info Content", value="Please upload a photo and/or press release PDF/DOC/DOCX for your submission." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmittedBy", type="CONTENTOBJ", desc="Submitted By content", value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmissionCompleteContent", type="CONTENTOBJ", desc="Content at top of Submission Complete page.", value="Thanks for your Success submission! You will receive an email shortly confirming your submission. If you would like to enter another, click ""Make Another Submission"" below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmitterConfirmationContent", type="CONTENTOBJ", desc="Content at top of Submitter email", value="Thank you for your Success Submission. TBA staff will review and process the submission." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmitterConfirmationSub", type="STRING", desc="Subject line of emailed Submitter confirmation", value="TBA Success Submission Confirmation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmitteeConfirmationContent", type="CONTENTOBJ", desc="Content at top of emailed confirmation to the Submittee", value="A TBA Success Submission has been made on your behalf. TBA will review and process this submission shortly. If you feel this submission was made in error, please call (615) 383-7421." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmitteeConfirmationSub", type="STRING", desc="Subject line of emailed Submittee confirmation", value="New TBA Success Submission" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationContent", type="CONTENTOBJ", desc="Content at top of emailed staff confirmation", value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Success Submission" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="To whom do we send Tn Bar staff confirmations", value="<EMAIL>" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubmitterSubmitteeConfirmationFrom", type="STRING", desc="From whom do we send confirmations to the Submitter and Submittee", value="<EMAIL>" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
				formName='frmSuccess',
				formNameDisplay='#variables.strPageFields.FormTitle#',
				orgEmailTo='#variables.strPageFields.StaffConfirmationTo#',
				orgEmailFrom='<EMAIL>',
				memberEmailFrom='#variables.strPageFields.SubmitterSubmitteeConfirmationFrom#'
			));
			variables.useMID = arguments.event.getValue('memberID',0);

			switch (local.formAction) {
				case "processLookup":
					
					if (NOT variables.objCffp.testSubmission(form) && NOT arguments.event.getValue('submitForMyself',0)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					if(arguments.event.getValue('btnContinue','') EQ 'submit'){
						local.response = processMemberInfo(rc=arguments.event.getCollection());
						switch (local.response.status) {
							case "success":
								local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection(),submissionId=local.response.submissionId);
								application.objCommon.redirect(variables.baseUrl& "&fa=confirm");
								break;
							default:
								local.returnHTML = showError(errorCode='failsavemember');
								break;				
						}
					} else {
						application.objCommon.redirect(variables.baseURL);
					}
					break;
				case "confirm":
					local.returnHTML = showConfirmation();
					application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
					break;
				case "updatePhoto" :
					local.returnHTML = updatePhoto();
					break;
				case "uploadAndCropPhoto" :
					local.returnHTML = uploadAndCropPhoto(arguments.event);
					break;
				case "savePhoto" :
					local.saveResponse = savePhoto(arguments.event);
					switch (local.saveResponse.status) {
						case "success":
							local.returnHTML = "<script>window.parent.$('##submissionPhotoFullPath').val('#local.saveResponse.filepath#');window.parent.$('##submissionPhoto').val('#local.saveResponse.filename#');window.parent.$('##submissionPhotoName').text('#local.saveResponse.filename#');top.$.colorbox.close();</script>";
							break;
						default:
							local.returnHTML = showError(errorCode='failsavephoto');
							break;	
					}
					break;
				default:
					local.returnHTML = showLookup();
					break;
			}
			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
                .inner-content{    margin-bottom: 20px;}
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				.subRateLabel {font-weight:normal;}
				.c{text-align:center;}
				.margin-20{margin-top:20px;margin-bottom:20px;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				var memberTypeField;
                
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				function chooseOption(_option){
					if(_option == 1){
						$('##submitForMyself').val(1);
						var mo = { memberID:#session.cfcUser.memberData.memberID#,memberNumber:'#session.cfcuser.memberdata.memberNumber#' };
						assignMemberData(mo);
					} else {
						$('##chooseOptionLoggedIn').hide();
						$('##acntLocator').show();
						$('##submitForMyself').val(0);
					}
				}		
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>
					<cfif NOT val(local.isLoggedIn)> 			
						var mo = { memberID:#variables.useMID#};
						assignMemberData(mo);
					</cfif>
									
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>

			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
		
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baseLink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinput type="hidden" name="submitForMyself" id="submitForMyself" value="0">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			<cfif len(variables.strPageFields.FormTitle)>
				<div class="row-fluid TitleText" id="FormTitleId"><h2>#variables.strPageFields.FormTitle#</h2></div>
			</cfif>

			<cfif val(local.isLoggedIn)>
				<div id="chooseOptionLoggedIn">
					<div class="tsAppSectionHeading">Choose your option</div>
					<div class="tsAppSectionContentContainer">
						<div class="span12">
							<div class="span6 c margin-20">
								<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="chooseOption(1)">Submit for Yourself</button>
							</div>
							<div class="span6 c margin-20">
								<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="chooseOption(2)">Submit for Someone Else</button>
							</div>
						</div>
					</div>
				</div>
			</cfif>

			<div id="acntLocator" <cfif val(local.isLoggedIn)>class="hide"</cfif>>
				<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
					</tr>
					</table>			
				</div>
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.stReturn = "success">
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
	
		<cfset local.objAdminSuccess	= CreateObject("component","model.admin.custom.tnbar.tnbar.success")>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>

		<cfset local.fieldSetUIDlist = '77D16097-3911-4ECC-AEED-7E1282F6DE25'>
		<cfset local.memberFieldDetails = structNew()>
		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
			<cfset local.NewAcctFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
			<cfset StructAppend(local.memberFieldDetails,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.NewAcctFormXMLFields))>
		</cfloop>
		<cfset local.strPrefillMemberData = local.memberFieldDetails>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	
		
		<cfset variables.origMemberID = variables.useMID>

		<cfset local.contactInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='77D16097-3911-4ECC-AEED-7E1282F6DE25', mode="collection", strData=local.strData)>

		<cfset local.qrySubmissionTypes = local.objAdminSuccess.getSubmissionTypes(displayInFrontEnd=true)>
		
		<cfset local.submittedByName = "">
		<cfset local.submittedByTitle = "">
		<cfset local.submittedByEmail = "">
		<cfset local.submittedByPhone = "">
		<cfif structKeyExists(session,"submittedByName")>
			<cfset local.submittedByName = session.submittedByName>
			<cfset local.submittedByTitle = session.submittedByTitle>
			<cfset local.submittedByEmail = session.submittedByEmail>
			<cfset local.submittedByPhone = session.submittedByPhone>
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
                .inner-content{    margin-bottom: 20px;}
				##dateOfMove,##appointedDate,##achievementDate,##awardDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }	
				.content input[type="text"] {width:206px!important;}
				.content select{width:220px!important;}
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {white-space: initial!important;}
				@media screen and (max-width: 767px){
					##content-wrapper table td {display: block;margin-bottom:0px;}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{padding: 10px !important;}
				.wrapLeft{display:none;}
						
			</style>
			
			<script language="javascript">
				function afterFormLoad(){					
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					#local.contactInfoFieldSet.jsValidation#

					if($('##submissionType').val().length == 0){
						arrReq[arrReq.length] = 'Submission Type is required.'; 
					} else {
						_subType = $('##submissionType').val();
						if(_subType == 'Achievement'){

							if(CKEDITOR.instances['achievementDescription'] != null)
								achievementDescription = CKEDITOR.instances['achievementDescription'].getData().trim();

							if (achievementDescription.length == 0) {
								arrReq[arrReq.length] = 'Achievement Description is required.'; 
							}
							if($('##achievementDate').val().length == 0){
								arrReq[arrReq.length] = 'Achievement Date is required.'; 
							}
						} else if(_subType == 'Appointment'){
							if($('##appointedBy').val().length == 0){
								arrReq[arrReq.length] = 'Appointed By is required.'; 
							}
							if($('##role').val().length == 0){
								arrReq[arrReq.length] = 'Role  is required.'; 
							}
							if($('##termsOfService').val().length == 0){
								arrReq[arrReq.length] = 'Term of Service is required.'; 
							}
							if($('##appointedDate').val().length == 0){
								arrReq[arrReq.length] = 'Date Appointed is required.'; 
							}
						} else if(_subType == 'Award/Recognition'){
							if($('##awardName').val().length == 0){
								arrReq[arrReq.length] = 'Award Name is required.'; 
							}
							if($('##awardOrganization').val().length == 0){
								arrReq[arrReq.length] = 'Awarding Organization is required.'; 
							}
							if($('##awardDate').val().length == 0){
								arrReq[arrReq.length] = 'Date Awarded is required.'; 
							}
						} else if(_subType == 'Career Move'){
							if($('##previousPosition').val().length == 0){
								arrReq[arrReq.length] = 'Previous Position is required.'; 
							}
							if($('##previousFirm').val().length == 0){
								arrReq[arrReq.length] = 'Previous Firm is required.'; 
							}
							if($('##newPosition').val().length == 0){
								arrReq[arrReq.length] = 'New Position is required.'; 
							}
							if($('##newFirm').val().length == 0){
								arrReq[arrReq.length] = 'New Firm is required.'; 
							}
							if($('##dateOfMove').val().length == 0){
								arrReq[arrReq.length] = 'Date of Move is required.'; 
							}
						}
					}
					if($('##submittedByName').val().length == 0){
						arrReq[arrReq.length] = 'Submitted By Name is required.'; 
					}
					if($('##submittedByEmail').val().length == 0){
						arrReq[arrReq.length] = 'Submitted By Email is required.'; 
					} else{
						if(!validateEmail($('##submittedByEmail').val())){
							arrReq[arrReq.length] = 'Enter valida Submitted By Email.'; 
						}
					}
					if($('##submittedByPhone').val().length == 0){
						arrReq[arrReq.length] = 'Submitted By Phone is required.'; 
					}
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}
					return true;
				}
				function validateEmail(e){
					var t = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z]{2,4}$/;
					return t.test(e)
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) {
							if(typeof $('###variables.formName# ##'+key).attr('multiple') != "undefined"){
								_arrValue = objPrefill[key].split(',');
								for(var _i=0;_i<_arrValue.length;_i++){
									$('###variables.formName# ##'+key+' option[value="'+ _arrValue[_i]+'"]').attr('selected','selected');
									$('###variables.formName# ##'+key).multiselect('refresh')
								}
							} else
								$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				$(document).ready(function() {
					prefillData();

					mca_setupDatePickerField('dateOfMove');
					mca_setupDatePickerField('appointedDate');
					mca_setupDatePickerField('achievementDate');
					mca_setupDatePickerField('awardDate');				
					
				});

				function hideAllSubFields(){
					$('.achievement').hide();
					$('.appointment').hide();
					$('.awardRecognition').hide();
					$('.careerMove').hide();
				}
				function fnUpdateMemberPhoto() {
					$.colorbox( {innerWidth:800, innerHeight:500, href:'#variables.link.updatePhoto#&mode=direct', iframe:true, overlayClose:false} );
				}
				$(document).on('change','##submissionType',function(){
					var _this = $(this);
					var _subType = _this.children("option:selected").attr('data-type');
					
					hideAllSubFields();
					if(_subType == 'Achievement'){
						$('.achievement').show();
					} else if(_subType == 'Appointment'){
						$('.appointment').show();
					} else if(_subType == 'Award/Recognition'){
						$('.awardRecognition').show();
					} else if(_subType == 'Career Move'){
						$('.careerMove').show();
					}
				});
				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.FormTitle)>
					<div class="row-fluid TitleText" id="FormTitleId"><h2>#variables.strPageFields.FormTitle#</h2></div>
				</cfif>	
					
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baseURL#" onsubmit="return validateMemberInfoForm()" enctype="multipart/form-data">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<cfif NOT local.isLoggedIn AND local.identifiedMemberID GT 0>
						<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
					</cfif>	
					<cfif structKeyExists(arguments.rc, "submitForMyself") and len(trim(arguments.rc.submitForMyself))>
						<input type="hidden" name="submitForMyself" id="submitForMyself" value="#arguments.rc.submitForMyself#">
					</cfif>		
					<cfinclude template="/model/cfformprotect/cffp.cfm">
					
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					
					<div id="content-wrapper" class="row-fluid">

						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.contactInfoFieldSet.fieldSetTitle#</div>
							<div class="tsAppBodyText">#variables.strPageFields.ContactInformation#</div>
							<div class="tsAppSectionContentContainer" id="contactInfoFieldSet">
								#local.contactInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">Success Submission</div>
							<div class="tsAppSectionContentContainer">
								<cfif len(variables.strPageFields.SuccessSubmission)>
									<div id="SuccessSubmissionContent"  class="tsAppBodyText">#variables.strPageFields.SuccessSubmission#</div><br/>
								</cfif>
								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submissionType">* Submission Type:</label>
									<div class="controls">
										<select name="submissionType" id="submissionType" class="tsAppBodyText">
											<option></option>
											<cfloop query="local.qrySubmissionTypes">
												<option value="#local.qrySubmissionTypes.submissionType#" data-type="#local.qrySubmissionTypes.submissionType#">#local.qrySubmissionTypes.submissionType#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="control-group achievement hide" >
									<label class="control-label tsAppBodyText" for="achievementDescription">* Achievement Description:</label>
									<div class="controls">
										<textarea class="tsAppBodyText span6" id="achievementDescription" name="achievementDescription" rows="10" cols="80" ></textarea>
									</div>
								</div>
								<script>
								
								</script>
								<div class="control-group achievement hide">
									<label class="control-label tsAppBodyText" for="achievementDate">* Date:</label>
									<div class="controls">
										<input type="text" name="achievementDate"  id="achievementDate" value="" class="tsAppBodyText">
									</div>
								</div>

								<div class="control-group appointment hide">
									<label class="control-label tsAppBodyText" for="appointedBy">* Appointed By:</label>
									<div class="controls">
										<input type="text" name="appointedBy"  id="appointedBy" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group appointment hide">
									<label class="control-label tsAppBodyText" for="role">* Role:</label>
									<div class="controls">
										<input type="text" name="role"  id="role" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group appointment hide">
									<label class="control-label tsAppBodyText" for="termsOfService">* Term of Service:</label>
									<div class="controls">
										<input type="text" name="termsOfService"  id="termsOfService" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group appointment hide">
									<label class="control-label tsAppBodyText" for="appointedDate">* Date Appointed:</label>
									<div class="controls">
										<input type="text" name="appointedDate"  id="appointedDate" value="" class="tsAppBodyText">
									</div>
								</div>
								<div class="control-group appointment hide">
									<label class="control-label tsAppBodyText" for="appointmentType"> Type of Appointment:</label>
									<div class="controls">
										<input type="radio" name="appointmentType" value="N" style="margin-top:0px"><span class="help-inline tsAppBodyText">New Appointment</span><br/>
										<input type="radio" name="appointmentType" value="R" style="margin-top:0px"><span class="help-inline tsAppBodyText">Re-Appointment</span>
									</div>
								</div>

								<div class="control-group awardRecognition hide">
									<label class="control-label tsAppBodyText" for="awardName">* Name of Award:</label>
									<div class="controls">
										<input type="text" name="awardName"  id="awardName" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group awardRecognition hide">
									<label class="control-label tsAppBodyText" for="awardOrganization">* Awarding Organization:</label>
									<div class="controls">
										<input type="text" name="awardOrganization"  id="awardOrganization" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group awardRecognition hide">
									<label class="control-label tsAppBodyText" for="organizationInfo"> Organization URL or other contact information:</label>
									<div class="controls">
										<input type="text" name="organizationInfo"  id="organizationInfo" value="" class="tsAppBodyText" maxlength="200">
									</div>
								</div>
								<div class="control-group awardRecognition hide">
									<label class="control-label tsAppBodyText" for="awardDate">* Date Awarded:</label>
									<div class="controls">
										<input type="text" name="awardDate"  id="awardDate" value=""><br>
										<input type="checkbox" name="othersHonored"  id="othersHonored" value="1" style="margin-top:0px">
										<span class="help-inline tsAppBodyText">
											Others were Honored
										</span>
									</div>
								</div>

								<div class="control-group careerMove hide">
									<label class="control-label tsAppBodyText" for="previousPosition">* Previous Position:</label>
									<div class="controls">
										<input type="text" name="previousPosition"  id="previousPosition" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group careerMove hide">
									<label class="control-label tsAppBodyText" for="previousFirm">* Previous Firm:</label>
									<div class="controls">
										<input type="text" name="previousFirm"  id="previousFirm" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								
								<div class="control-group careerMove hide">
									<label class="control-label tsAppBodyText" for="newPosition">* New Position:</label>
									<div class="controls">
										<input type="text" name="newPosition"  id="newPosition" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group careerMove hide">
									<label class="control-label tsAppBodyText" for="newFirm">* New Firm:</label>
									<div class="controls">
										<input type="text" name="newFirm"  id="newFirm" value="" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group careerMove hide">
									<label class="control-label tsAppBodyText" for="dateOfMove">* Date of Move:</label>
									<div class="controls">
										<input type="text" name="dateOfMove"  id="dateOfMove" value="" class="tsAppBodyText">
									</div>
								</div>
							</div>
						</div>

						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">Optional Submission Info</div>
							<div class="tsAppSectionContentContainer">
								<cfif len(variables.strPageFields.OptionalSubmissionInfo)>
									<div id="OptionalSubmissionInfoContent"  class="tsAppBodyText">#variables.strPageFields.OptionalSubmissionInfo#</div><br/>
								</cfif>

								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submissionPhoto">Upload a Photo:</label>
									<div class="controls">
                                        <span id="submissionPhotoName"></span>
										<input type="hidden" name="submissionPhoto" id="submissionPhoto" value="">
										<input type="hidden" name="submissionPhotoFullPath" id="submissionPhotoFullPath" value="">
										<input type="button" name="submissionPhotoBtn" id="submissionPhotoBtn" onclick="fnUpdateMemberPhoto();" value="Upload a photo"><br>
										<input type="checkbox" name="usePhotoForProfile" id="usePhotoForProfile" value="1" style="margin-top:0px"> <span class="help-inline tsAppBodyText">Use this photo as my TBA member photo</span>
									</div>
								</div>

								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submissionPressRelease">Upload a Press Release:</label>
									<div class="controls">
										<input type="file" name="submissionPressRelease" id="submissionPressRelease" value="" class="tsAppBodyText"><br>
										<span class="help-inline tsAppBodyText">Press Release must be in PDF, DOC, or DOCX format</span>
									</div>
								</div>
							</div>
						</div>

						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">Submitted By</div>
							<div class="tsAppSectionContentContainer">
								<cfif len(variables.strPageFields.SubmittedBy)>
									<div id="SubmittedByContent">#variables.strPageFields.SubmittedBy#</div><br/>
								</cfif>
								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submittedByName">* Name:</label>
									<div class="controls">
										<input type="text" name="submittedByName"  id="submittedByName" value="#local.submittedByName#" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submittedByTitle">Title:</label>
									<div class="controls">
										<input type="text" name="submittedByTitle"  id="submittedByTitle" value="#local.submittedByTitle#" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submittedByEmail">* Email:</label>
									<div class="controls">
										<input type="text" name="submittedByEmail"  id="submittedByEmail" value="#local.submittedByEmail#" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
								<div class="control-group">
									<label class="control-label tsAppBodyText" for="submittedByPhone">* Phone:</label>
									<div class="controls">
										<input type="text" name="submittedByPhone"  id="submittedByPhone" value="#local.submittedByPhone#" class="tsAppBodyText" maxlength="100">
									</div>
								</div>
							</div>
						</div>
						
						<button name="btnContinue" type="submit" class="btn" value="submit" onClick="hideAlert();">Submit</button>
					</div>
					#application.objWebEditor.showEditorHeadScripts()#
					<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = arguments.rc.mc_siteinfo.orgid, includeTags=0)>
					<script language="javascript">					
						function editContentBlock(cid,srid,tname) {
							var editMember = function(r) {
								if (r.success && r.success.toLowerCase() == 'true') {
									$('##frmmd_'+cid).html(r.html);
									var x = div.getElementsByTagName("script");
									for(var i=0;i<x.length;i++) eval(x[i].text); 
								}
							};
							var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
							TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
						}
					</script>					
				</form>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		<cfset var local = structNew()>
		<cfset local.response = StructNew()>
		<cfset local.objAdminSuccess	= CreateObject("component","model.admin.custom.tnbar.tnbar.success")>

		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.creatorMemberID = 0>
		<cfif structKeyExists(arguments.rc, "isMemberKey") OR (val(local.isLoggedIn) AND structKeyExists(arguments.rc, "submitForMyself") AND arguments.rc.submitForMyself EQ 1 )>
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		<cfif val(local.isLoggedIn)>
			<cfset local.creatorMemberID = session.cfcUser.memberData.memberID>
		</cfif>
		
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>
		
		<cfif structKeyExists(arguments.rc,"submittedByName") AND len(arguments.rc.submittedByName)>
			<cfset session.submittedByName = arguments.rc.submittedByName>
			<cfset session.submittedByTitle = arguments.rc.submittedByTitle>
			<cfset session.submittedByEmail = arguments.rc.submittedByEmail>
			<cfset session.submittedByPhone = arguments.rc.submittedByPhone>
		</cfif>

		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		<cfset local.objSaveMember.setMemberType(memberType='User')>

		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		<cfif local.strResult.success>
			<cfset local.response.status = "success">
			<cfset variables.useMID = local.strResult.memberID>
			
			<cfif local.creatorMemberID EQ 0>
				<cfset local.creatorMemberID = variables.useMID>
			</cfif>
			<cfset local.submissionStruct = StructNew()>
			<cfset local.submissionStruct = prepareSubmissionParameters(rc=arguments.rc,memberid= variables.useMID,creatorID=local.creatorMemberID)>
			<cfset local.submissionId = saveSubmission(local.submissionStruct)>
			<cfif local.submissionId GT 0 AND len(arguments.rc.submissionPressRelease)>
				<cfset local.responseStruct = uploadPressReleaseDocument(local.submissionId,arguments.rc)>
			</cfif>
			<cfset local.response.submissionId = local.submissionId>
		<cfelse>
			<cfset local.response.status = "failure">
		</cfif>
		
		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="submissionId" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.contactInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='77D16097-3911-4ECC-AEED-7E1282F6DE25', mode="confirmation", strData=arguments.rc)>

		<cfsavecontent variable="local.confirmationContent">
			<cfoutput>
				<cfif len(variables.strPageFields.SubmissionCompleteContent)>
					<div>
						<div>#variables.strPageFields.SubmissionCompleteContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div>
					<div>#variables.strPageFields.StaffConfirmationContent#</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.confirmationContentForSubmittee">
			<cfoutput>
				<div>
					<div>#variables.strPageFields.SubmitteeConfirmationContent#</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.confirmationContentForSubmitter">
			<cfoutput>
				<div>
					<div>#variables.strPageFields.SubmitterConfirmationContent#</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<cfset local.pressReleaseFileName = "">
		<cfset local.pressReleaseFileDocumentId = "">
		<cfquery name="local.qryGetPressReleaseFileName" datasource="#application.dsn.customapps.dsn#">
			SELECT pressReleaseFileName, pressReleaseFileDocumentId FROM TNBAR_submissions WHERE submissionID = #arguments.submissionId#
		</cfquery>
		<cfif local.qryGetPressReleaseFileName.recordCount AND len(local.qryGetPressReleaseFileName.pressReleaseFileName)>
			<cfset local.pressReleaseFileName = local.qryGetPressReleaseFileName.pressReleaseFileName>
			<cfset local.pressReleaseFileDocumentId = local.qryGetPressReleaseFileName.pressReleaseFileDocumentId>
		</cfif>
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfset local.rowDataStyle = 'font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;'>
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<div class="row-fluid">Here are the details of the submission:</div><br/>
				
                <!--@@SpecialContentforEmail@@-->
				#local.contactInfoFieldSet.fieldSetContent#
				<br>
				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr><td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;border-top:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Submission Details</td></tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">
								<tr><td style="#local.rowDataStyle#">Submission Type:</td><td style="#local.rowDataStyle#">#arguments.rc['submissionType']#</td></tr>
								<cfif arguments.rc['submissionType'] eq "Achievement">
									<tr><td style="#local.rowDataStyle#">Achievement Description</td><td style="#local.rowDataStyle#">#arguments.rc['achievementDescription']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Date</td><td style="#local.rowDataStyle#">#arguments.rc['achievementDate']#</td></tr>
								<cfelseif arguments.rc['submissionType'] eq "Appointment">
									<tr><td style="#local.rowDataStyle#">Appointed By:</td><td style="#local.rowDataStyle#">#arguments.rc['appointedBy']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Role:</td><td style="#local.rowDataStyle#">#arguments.rc['role']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Term of Service:</td><td style="#local.rowDataStyle#">#arguments.rc['termsOfService']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Appointed Date:</td><td style="#local.rowDataStyle#">#arguments.rc['appointedDate']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Type of Appointment:</td><td style="#local.rowDataStyle#"><cfif structKeyExists(arguments.rc,"appointmentType") ><cfif arguments.rc['appointmentType'] EQ 'N'>New Appointment<cfelseif arguments.rc['appointmentType'] EQ 'R'>Re-Appointment</cfif></cfif></td></tr>
								<cfelseif arguments.rc['submissionType'] eq "Award/Recognition">
									<tr><td style="#local.rowDataStyle#">Name of Award:</td><td style="#local.rowDataStyle#">#arguments.rc['awardName']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Awarding Organization:</td><td style="#local.rowDataStyle#">#arguments.rc['awardOrganization']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Organization URL or other contact information:</td><td style="#local.rowDataStyle#">#arguments.rc['organizationInfo']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Date Awarded:</td><td style="#local.rowDataStyle#">#arguments.rc['awardDate']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Others were Honored:</td><td style="#local.rowDataStyle#"><cfif structKeyExists(arguments.rc,"othersHonored")>Yes<cfelse>No</cfif></td></tr>
								<cfelseif arguments.rc['submissionType'] eq "Career Move">
									<tr><td style="#local.rowDataStyle#">Previous Position:</td><td style="#local.rowDataStyle#">#arguments.rc['previousPosition']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Previous Firm:</td><td style="#local.rowDataStyle#">#arguments.rc['previousFirm']#</td></tr>
									<tr><td style="#local.rowDataStyle#">New Position:</td><td style="#local.rowDataStyle#">#arguments.rc['newPosition']#</td></tr>
									<tr><td style="#local.rowDataStyle#">New Firm:</td><td style="#local.rowDataStyle#">#arguments.rc['newFirm']#</td></tr>
									<tr><td style="#local.rowDataStyle#">Date of Move:</td><td style="#local.rowDataStyle#">#arguments.rc['dateOfMove']#</td></tr>
								</cfif>
								<tr><td style="#local.rowDataStyle#">Photo Uploaded:</td><td style="#local.rowDataStyle#">#arguments.rc['submissionPhoto']#</td></tr>
                                <tr><td style="#local.rowDataStyle#">Use this photo as my TBA member photo:</td><td style="#local.rowDataStyle#"><cfif structKeyExists(arguments.rc,"usePhotoForProfile")> Yes<cfelse>No</cfif></td></tr>
								<tr><td style="#local.rowDataStyle#">Press Release Uploaded:</td><td style="#local.rowDataStyle#"><a href="#local.thisScheme#://#local.thisHostname#/docDownload/#local.pressReleaseFileDocumentId#" target="_blank">#local.pressReleaseFileName#</a></td></tr>
							</table>
						</td>
					</tr>
				</table>
				<br>
				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr><td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;border-top:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Submitted By</td></tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">
								<tr><td style="#local.rowDataStyle#">Name:</td><td style="#local.rowDataStyle#">#arguments.rc['submittedByName']#</td></tr>
								<tr><td style="#local.rowDataStyle#">Title:</td><td style="#local.rowDataStyle#">#arguments.rc['submittedByTitle']#</td></tr>
								<tr><td style="#local.rowDataStyle#">Email:</td><td style="#local.rowDataStyle#">#arguments.rc['submittedByEmail']#</td></tr>
								<tr><td style="#local.rowDataStyle#">Phone:</td><td style="#local.rowDataStyle#">#arguments.rc['submittedByPhone']#</td></tr>
							</table>
						</td>
					</tr>					
				</table>
				<br/>				
			</div>	
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(arguments.rc, "m_firstname")>
			<cfset local.Name = arguments.rc['m_firstname']/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(arguments.rc, "m_lastname")>
			<cfset local.Name = local.Name & arguments.rc['m_lastname']/>
		</cfif>	

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfif StructKeyExists(arguments.rc,'origMemberID')>
				<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=arguments.rc.origMemberID).email>
			</cfif>
		</cfif>

		<cfset local.confirmationContentHTML =  local.confirmationContent>
        <!---Email to Submitee--->
        <cfset variables.memberEmail.subject = variables.strPageFields.SubmitteeConfirmationSub>
		<cfset local.confirmationToSubmittee = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForSubmittee)>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.strPageFields.SubmitteeConfirmationSub,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationToSubmittee,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToSubmittee = local.responseStruct.success>
		<!---Email to Submitter--->
		<cfset variables.memberEmail.subject = variables.strPageFields.SubmitterConfirmationSub>	
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfif StructKeyExists(arguments.rc,'submittedByEmail')>
				<cfset variables.memberEmail.TO = arguments.rc['submittedByEmail']>
			</cfif>
		</cfif>
		<cfset local.confirmationToSubmitter = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForSubmitter)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.memberEmail, emailTitle="#variables.strPageFields.SubmitterConfirmationSub#", emailContent=local.confirmationToSubmitter)>
		<cfset local.emailSentToSubmitter = local.responseStruct.success>
		<!---Email to Staff--->
		<cfset variables.orgEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.confirmationHTMLToTNStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
        <cfsavecontent variable="local.memberNumberContent">
            <cfoutput>
                <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#arguments.rc.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
                <div style="padding-bottom:4px;">Member Number tied to Submission Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

                <cfif NOT local.emailSentToSubmitter>
                    <div style="padding-bottom:4px;"><b>We were not able to send the submitter an e-mail confirmation.</b></div>
                </cfif>
                <cfif NOT local.emailSentToSubmittee>
                    <div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
                </cfif>
            </cfoutput>
        </cfsavecontent>
        <cfset local.confirmationHTMLToTNStaff = replaceNoCase(local.confirmationHTMLToTNStaff,"<!--@@SpecialContentforEmail@@-->",local.memberNumberContent)>
        <cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.orgEmail, emailTitle="#variables.strPageFields.StaffConfirmationSub#", emailContent=local.confirmationHTMLToTNStaff)>
		
		<cfreturn local.confirmationContentHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
			<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif len(variables.strPageFields.SubmissionCompleteContent)>
					<div>
						<div>#variables.strPageFields.SubmissionCompleteContent#</br></br></div>
					</div>	
				</cfif>
				<a href="#variables.baseUrl#"><button type="button" class="btn">Make Another Submission</button></a>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="prepareSubmissionParameters"  access="public" output="false" returntype="struct">
		<cfargument name="memberID" required="true" type="numeric">
		<cfargument name="creatorID" required="true" type="numeric">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.memberId = arguments.memberId>
		<cfset local.returnStruct.originalMemberId = arguments.rc.origMemberID>
		<cfset local.returnStruct.submissionType = arguments.rc.submissionType>
		
		<cfset local.returnStruct.submissionPhoto = arguments.rc.submissionPhoto>
		<cfset local.returnStruct.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.memberId, orgID=variables.orgID)>

		<cfif structKeyExists(arguments.rc, "usePhotoForProfile")>
			<cfset local.returnStruct.usePhotoForProfile = arguments.rc.usePhotoForProfile>
		<cfelse>
			<cfset local.returnStruct.usePhotoForProfile = 0>
		</cfif>
		<cfset local.returnStruct.submittedByName = stripHtml(arguments.rc.submittedByName)>
		<cfset local.returnStruct.submittedByTitle = stripHtml(arguments.rc.submittedByTitle)>
		<cfset local.returnStruct.submittedByEmail = stripHtml(arguments.rc.submittedByEmail)>
		<cfset local.returnStruct.submittedByPhone = stripHtml(arguments.rc.submittedByPhone)>
		<cfset local.returnStruct.achievementDescription = arguments.rc.achievementDescription>
		<cfset local.returnStruct.achievementDate = ''>
		<cfset local.returnStruct.appointedBy = ''>
		<cfset local.returnStruct.role = ''>
		<cfset local.returnStruct.termsOfService = ''>
		<cfset local.returnStruct.appointedDate = ''>
		<cfset local.returnStruct.appointmentType = ''>
		<cfset local.returnStruct.awardName = ''>
		<cfset local.returnStruct.awardOrganization = ''>
		<cfset local.returnStruct.awardOrgDetails = ''>
		<cfset local.returnStruct.awardedDate = ''>
		<cfset local.returnStruct.othersHonored = 0>
		<cfset local.returnStruct.creatorID = arguments.creatorID>
		<cfset local.returnStruct.previousPosition = ''>
		<cfset local.returnStruct.previousFirm = ''>
		<cfset local.returnStruct.newPosition = ''>
		<cfset local.returnStruct.newFirm = ''>
		<cfset local.returnStruct.movedDate = ''>
		<cfswitch expression="#arguments.rc.submissionType#">
			<cfcase value="Achievement">
				<cfset local.returnStruct.achievementDescription = trim(arguments.rc.achievementDescription)>
				<cfset local.returnStruct.achievementDate = arguments.rc.achievementDate>
			</cfcase>
			<cfcase value="Appointment">
				<cfset local.returnStruct.appointedBy = trim(stripHtml(arguments.rc.appointedBy))>
				<cfset local.returnStruct.role = trim(stripHtml(arguments.rc.role))>
				<cfset local.returnStruct.termsOfService = trim(stripHtml(arguments.rc.termsOfService))>
				<cfset local.returnStruct.appointedDate = arguments.rc.appointedDate>
				<cfif structKeyExists(arguments.rc, "appointmentType")>
					<cfset local.returnStruct.appointmentType = arguments.rc.appointmentType>
				</cfif>
			</cfcase>
			<cfcase value="Award/Recognition">
				<cfset local.returnStruct.awardName = trim(stripHtml(arguments.rc.awardName))>
				<cfset local.returnStruct.awardOrganization = trim(stripHtml(arguments.rc.awardOrganization))>
				<cfset local.returnStruct.awardOrgDetails = trim(stripHtml(arguments.rc.organizationInfo))>
				<cfset local.returnStruct.awardedDate = arguments.rc.awardDate>
				<cfif structKeyExists(arguments.rc, "othersHonored")>
					<cfset local.returnStruct.othersHonored = 1>
				<cfelse>
					<cfset local.returnStruct.othersHonored = 0>
				</cfif>
			</cfcase>
			<cfcase value="Career Move">
				<cfset local.returnStruct.previousPosition = trim(stripHtml(arguments.rc.previousPosition))>
				<cfset local.returnStruct.previousFirm = trim(stripHtml(arguments.rc.previousFirm))>
				<cfset local.returnStruct.newPosition = trim(stripHtml(arguments.rc.newPosition))>
				<cfset local.returnStruct.newFirm = trim(stripHtml(arguments.rc.newFirm))>
				<cfset local.returnStruct.movedDate = arguments.rc.dateOfMove>
			</cfcase>
		</cfswitch>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSubmission" access="public" output="false" returntype="any">
		<cfargument name="submissionStruct" type="struct" required="true">
		<cfset var local = structNew() />
		
		<cfset local.photoFolder = "#application.paths.RAIDUserAssetRoot.path##LCASE('#variables.orgCode#/#variables.siteCode#')#/successphotos/">
		
		<cfquery name="local.qrySaveSubmission" datasource="#application.dsn.customapps.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @submissionStatusId int, @submissionTypeId int, @submissionId int;
				SELECT @submissionStatusId = submissionStatusId FROM TNBAR_submissionStatuses WHERE submissionStatus = 'Pending';
				SELECT @submissionTypeId = submissionTypeId FROM TNBAR_submissionTypes WHERE submissionType = '#arguments.submissionStruct.submissionType#';
				BEGIN TRAN;
					INSERT INTO TNBAR_submissions (
						submissionStatusId
						,submissionTypeId
						,memberId
						,originalMemberId
						,achievementDescription
						,achievementDate
						,appointedBy
						,role
						,termsOfService
						,appointedDate
						,appointmentType
						,awardName
						,awardOrganization
						,awardOrgDetails
						,awardedDate
						,othersHonoured
						,previousPosition
						,previousFirm
						,newPosition
						,newFirm
						,movedDate
						,submissionPhotoName
						,usePhotoForProfile
						,submittedByName
						,submittedByEmail
						,submittedByTitle
						,submittedByPhone
						,createdDate
						,lastModifiedBy
						,modifiedDate
					) VALUES (
						@submissionStatusId
						,@submissionTypeId
						,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionStruct.memberId#">
						,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionStruct.originalMemberId#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.achievementDescription#">
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.submissionStruct.achievementDate#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.appointedBy#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.role#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.termsOfService#">
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.submissionStruct.appointedDate#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.appointmentType#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.awardName#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.awardOrganization#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.awardOrgDetails#">
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.submissionStruct.awardedDate#">
						,<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.submissionStruct.othersHonored#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.previousPosition#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.previousFirm#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.newPosition#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.newFirm#">
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.submissionStruct.movedDate#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.submissionPhoto#">
						,<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.submissionStruct.usePhotoForProfile#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.submittedByName#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.submittedByEmail#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.submittedByTitle#">
						,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.submissionStruct.submittedByPhone#">
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
						,NULL
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
					)
					SELECT @submissionId = SCOPE_IDENTITY();
					SELECT @submissionId as submissionId;
					INSERT INTO TNBAR_submissions_submissionStatuses
						(submissionID
						,submissionStatusId
						,statusNotes
						,dateStatusEffective
						,enteredBy
						,dateCreated)
					VALUES (
						@submissionId
						,@submissionStatusId
						,'Submitted via Success Submission Form'
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
						,NULL
						,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
					)
				COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

            <cfset local.submissionPhotoName= "">
            <cfif len(arguments.submissionStruct.submissionPhoto) AND len(local.qrySaveSubmission.submissionId)>
                <cfset local.fileExt = ListLast(arguments.submissionStruct.submissionPhoto,".")>
                <cfset local.submissionPhotoName = "#local.qrySaveSubmission.submissionId#.#local.fileExt#">
                <cftry>
                    <cffile action="move" destination="#local.photoFolder#/#LCASE('#local.qrySaveSubmission.submissionId#.#local.fileExt#')#" source="#local.photoFolder#/#LCASE('#arguments.submissionStruct.submissionPhoto#')#">
					<cffile action="move" destination="#local.photoFolder#/thumbnails/#LCASE('#local.qrySaveSubmission.submissionId#.#local.fileExt#')#" source="#local.photoFolder#/thumbnails/#LCASE('#arguments.submissionStruct.submissionPhoto#')#">
                    <cfset local.renameStatus = "success">
                    <cfcatch type="any">
                        <cfset local.renameStatus = "fail">
                    </cfcatch>
                </cftry>
            <cfelse>
                <cfset local.renameStatus = "success">
            </cfif>
            <cfif local.renameStatus EQ "success" AND len(local.submissionPhotoName)>
                <cfquery name="qryUpdateSubmissionPhotoName" datasource="#application.dsn.customapps.dsn#">
                    UPDATE TNBAR_submissions SET submissionPhotoName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.submissionPhotoName#"> 
                    WHERE submissionId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySaveSubmission.submissionId#">
                </cfquery>
            </cfif>

            <cfreturn local.qrySaveSubmission.submissionId>
	</cffunction>

	<cffunction name="updatePhoto" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfscript> 
			
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(variables.siteCode);			
			local.memberPhotoImgHTML = "";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_uploadProfilePicture.cfm">
		</cfsavecontent>

		<!--- return the app struct --->
		<cfreturn local.data>		
	</cffunction>

	<cffunction name="uploadAndCropPhoto" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfscript> 
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(variables.siteCode);
		</cfscript>
		
		<!--- upload the photo --->
		<cfif len(arguments.event.getTrimValue('new_sn_prof_pic'))>
			
			<!--- create temp folder --->
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=variables.siteCode)>
			<cfset local.imgDirEnc = local.strFolder.folderPathEnc>

			<!--- upload photo --->
			<cffile action="UPLOAD" filefield="new_sn_prof_pic" destination="#local.strFolder.folderPath#" nameconflict="MAKEUNIQUE" result="local.uploadedPhoto">

			<!--- if uploaded --->
			<cfif local.uploadedPhoto.fileWasSaved>

				<cftry>
					<cfset local.serverDirectoryAndFileName = "#local.uploadedPhoto.ServerDirectory#/#local.uploadedPhoto.serverFile#">
					<cfset local.serverDirectoryAndFileName_LG = "#local.uploadedPhoto.ServerDirectory#/LG_#local.uploadedPhoto.serverFile#">
					
					<cfset local.processPhoto = application.objCommon.thumborImageTranform(command="fit-in/500x500",filePath=local.serverDirectoryAndFileName, outputfilePath=local.serverDirectoryAndFileName_LG)>
					<cfset local.resultPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.serverDirectoryAndFileName_LG)>

					<cfset local.success = local.processPhoto.success>

					<cfif local.success EQ 'No'>
						<cfthrow message="Thumbor Image Tranform Error" 
							type="thumborImageTranformError" 
							detail="#local.resultPhotoInfo#">						
					<cfelse>
						<cfset local.imageHeight = local.resultPhotoInfo.imageInfo.source.height>
						<cfset local.imageWidth = local.resultPhotoInfo.imageInfo.source.width>

						<cfset local.memberPhotoWidth = 250/>
						<cfset local.memberPhotoHeight = 400/>
						<cfset local.memberPhotoRatioWidth = local.memberPhotoWidth/10 />
						<cfset local.memberPhotoRatioHeight = local.memberPhotoHeight/10 />
					
						<!--- copy to /temp for preview purposes --->
						<cfset local.imgInTemp = "#createUUID()##createUUID()#.jpg">
						<cffile action="copy" destination="#application.paths.RAIDTemp.path##local.imgInTemp#" source="#local.serverDirectoryAndFileName_LG#">
					</cfif>

					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.uploadedPhoto)>
						<cflocation url="#variables.link.updatePhoto#&mode=direct&msg=1" addtoken="false">
					</cfcatch>
				</cftry>
			</cfif>	
		<cfelse>
			<cflocation url="#variables.link.updatePhoto#" addtoken="false">
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_profilePicture.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="savePhoto" access="public" output="false" returntype="struct"> 
		<cfargument name="Event" type="any">
		
		<cfscript>
		local.filename = arguments.event.getValue('filename');
		local.objWebsite = CreateObject('component','model.admin.website.website');
		local.qrySiteSettings = local.objWebsite.getSettings(arguments.event.getValue('mc_siteinfo.siteCode'));
		local.responseStruct = StructNew();
		</cfscript>
			
		<!--- decrypt photodir --->
		<cftry>
			<cfset local.strFolder = application.objDocDownload.decryptDownloadURL(d=arguments.event.getValue('photodir',''))>

			<cfset local.memberFullImagePath_LG = "#local.strFolder.sourceFilePath#/LG_#local.filename#">
			<cfset local.memberFullImagePath_final_LG = "#local.strFolder.sourceFilePath#/final_LG_#local.filename#">

			<cfset local.photoFolder = "#application.paths.RAIDUserAssetRoot.path##LCASE('#variables.orgCode#/#variables.siteCode#')#/successphotos/">
			<cfset local.photoFolderThumb = "#application.paths.RAIDUserAssetRoot.path##LCASE('#variables.orgCode#/#variables.siteCode#')#/successphotos/thumbnails/">
			<cfset local.photoDestination = local.photoFolderThumb & "#LCASE(local.filename)#">
			<cfset local.responseStruct.status = "fail">
			<!--- crop photo --->
			
			<cfif fileExists(local.memberFullImagePath_LG) and arguments.event.getValue('w',0) gt 0 and arguments.event.getValue('h',0) gt 0>
				<cfif arguments.event.getValue('x',0) LT 0>
					<cfset local.x = 0>
				<cfelse>
					<cfset local.x = Int(arguments.event.getValue('x',0))>
				</cfif>
				<cfif arguments.event.getValue('y',0) LT 0>
					<cfset local.y = 0>
				<cfelse>
					<cfset local.y = Int(arguments.event.getValue('y',0))>
				</cfif>
				
				<cfif NOT DirectoryExists(local.photoFolder)>
					<cfdirectory action="create" directory="#local.photoFolder#">
				</cfif>

				<cffile action="copy" destination="#local.photoFolder#/#LCASE(local.filename)#" source="#local.memberFullImagePath_LG#">

				<cfif NOT DirectoryExists(local.photoFolderThumb)>
					<cfdirectory action="create" directory="#local.photoFolderThumb#">
				</cfif>

				<cfset local.resultPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.memberFullImagePath_LG)>

				<cfset local.imageHeight = local.resultPhotoInfo.imageInfo.source.height>
				<cfset local.imageWidth = local.resultPhotoInfo.imageInfo.source.width>

				<cfset local.defaultCropPoints = "#250#x#400#">

				<cfset local.cropPoints = application.objCommon.getManualCropPointsForThumbor(x=local.x,y=local.y,width=Int(arguments.event.getValue('w',0)),height=Int(arguments.event.getValue('h',0)))>
				<cfset local.command = "#local.cropPoints#/#local.defaultCropPoints#">
				<cfset local.cropRequest = application.objCommon.thumborImageTranform(command=local.command,filePath=local.memberFullImagePath_LG, outputfilePath=local.photoDestination)>
					
				<cfif local.cropRequest.success >
					<cfset local.responseStruct.status = "success">
					<cfset local.responseStruct.filepath = local.photoDestination>
					<cfset local.responseStruct.filename = local.filename>
				<cfelse>
					<cfset local.responseStruct.cropRequest = local.cropRequest>
					<cfthrow>
				</cfif>
			</cfif>
			
			<cfcatch type="Any">
				<cfset local.responseStruct.status = "fail">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.responseStruct)>
			</cfcatch>
		</cftry>
		<cfreturn local.responseStruct>
	</cffunction>

	<cffunction name="uploadPressReleaseDocument" access="public" returntype="struct" output="false">
		<cfargument name="submissionID" required="true" type="numeric">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = StructNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>

		<cffile action="upload" filefield="submissionPressRelease" destination="#local.strFolder.folderPath#" result="local.uploadResult" nameconflict="MAKEUNIQUE" accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document">

		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = local.UploadResult.ServerFile>
		<cfset local.strPDF['serverFileExt'] = local.UploadResult.ServerFileExt>

		<cfset local.newFileStruct = storePressReleaseDocument(memberID=variables.useMID, strPDF=local.strPDF,submissionId=arguments.submissionID)>
		<cfquery name="updateSubmissionWithPressRelease" datasource="#application.dsn.customapps.dsn#">
			UPDATE TNBAR_submissions SET pressReleaseFileName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newFileStruct.fileName#">
			,pressReleaseFileDocumentId= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.newFileStruct.documentID#">
			WHERE submissionID = #arguments.submissionID#
		</cfquery>
		<cfset local.responseStruct = StructNew()>
		<cfset local.responseStruct.status = "success">
		<cfset local.responseStruct.pressReleaseFileName = local.newFileStruct.fileName>
		<cfreturn local.responseStruct>
	</cffunction>
	
	<cffunction name="storePressReleaseDocument" access="private" returntype="struct" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">
        <cfargument name="submissionID" required="true" type="numeric">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.memberId = application.objMember.getMemberIDByMemberNumber( "SuccessDocs", variables.orgID)>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt=arguments.strPDF.serverFileExt } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
					parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle=arguments.strPDF.serverFile, 
					docDesc='Press Release Document - Submission ID: #arguments.submissionID#', author='', fileData=local.newFile, isActive=1, isVisible=true, 
					contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt=arguments.strPDF.serverFileExt)>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberId, documentID=local.insertResults.documentID)>
		 <cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.pressReleaseData = StructNew()>
		<cfset local.pressReleaseData.fileName = arguments.strPDF.serverFile>
		<cfset local.pressReleaseData.documentId = local.insertResults.documentID>
        <cfreturn local.pressReleaseData>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>			
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavephoto">
						We were unable to save the photo uploaded. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="stripHtml" access="private" output="false" returntype="string">
        <cfargument name="key" type="string" required="true">
        <cfscript>
            var local.str = arguments.key;
            // remove the whole tag and its content
            var local.list = "style,script,noscript";
            for (var local.tag in local.list){
            local.str = reReplaceNoCase(local.str, "<s*(#local.tag#)[^>]*?>(.*?)","","all");
            }
            local.str = reReplaceNoCase(local.str, "<.*?>","","all");
            //get partial html in front
            local.str = reReplaceNoCase(local.str, "^.*?>","");
            //get partial html at end
            local.str = reReplaceNoCase(local.str, "<.*$","");

            return trim(local.str);
        </cfscript>
    </cffunction>
</cfcomponent>