﻿<cfset local.myPageData = attributes.data>
<cfset local.settingsObj = local.myPageData.OBJSETTINGS>
<cfset local.objGrpMsgCnt = local.myPageData.objGrpMsgCnt>
<cfset local.objBoxTabCnt = local.myPageData.objBoxTabCnt>
<cfsavecontent variable="local.myPageFrmJS">
	<cfoutput>
		<style type="text/css">
			##myPage .myInfo .HeaderText{color: ##444444;font-size:25px;}
			##myPage .myInfo li{padding-top:10px;cursor:pointer;}
			##myPage .myInfo li a{color:##444444;text-decoration:none;}
			##myPage .myInfo li a {font-size:15px;}
			##myPage .myInfo li:hover a {color: ##618597;text-decoration:underline;}
			##myPage .pageTitleWrap{
				padding-top: 15px;
				padding-bottom: 15px;
			}
			##myPage .welcomeMessageWrap{
				padding-bottom: 15px;
			}
			##myPage .profileWrap{
				margin-bottom:20px;
                display:flex;
			}
            ##myPage .myInfo {
                margin-left: 25px;
            }
            ##myPage .myInfo ul{
                padding-left: 20px;
                margin-top: 2px;
            }
			.renewTextWrap {
				width: 100%;
				border: 1.4px solid ##2a4161;
				padding: 10px;
				margin-bottom: 16px;
				text-align: center;
			}
			##myPage  .nav-tabs{
				list-style: none;
			}
			##myPage  .nav-tabs > .active > a, ##myPage  .nav-tabs>.active>a:hover {
				font-weight: normal;
				text-decoration: none;
				cursor:default;
				font-size: 16px;
			}
			##myPage  .nav-tabs li.active a {
				text-decoration: none!important;
			}
			##myPage  .nav-tabs>li>a, ##myPage  .nav-pills>li>a {
				padding: 7px 7px 7px 7px!important;
			}
			##myPage  .nav-tabs > li > a {
				border: 1px solid transparent;
				border-radius: 4px 4px 0 0;
				line-height: 1.42857;
				margin-right: 2px;
			}
			##myPage  .tab-content {
				border: 2px solid ##ddd;
				padding: 10px;
				margin-bottom: 20px;
				background: ##fff;
				min-height: 250px;
				max-height: 250px;
				overflow: auto;
			}
			##myPage  .tab-content .tab-pane {
				overflow-y: auto;
				min-height: 210px;
				max-height: 100%;
			}
			##myPage  .nav {
				margin-bottom: 0px;
			}
			##myPage  .nav-tabs li {
				margin-bottom: 0!important;
			}
			##myPage  .nav-tabs>li:last-child>a {
				margin-right: auto!important;;
			}
			##myPage  .nav-tabs a, ##myPage  .nav-tabs a:hover {
				font-weight: normal;
				text-decoration: none;
				font-size: 16px;
			}
			##myPage  .nav-tabs{
				border-bottom: 0px;
			}
			##myPage  .carousel,.carousel p{
				margin-bottom: 0!important;
			}
			##myPage  .tabLi{
				display:flex!important;
			}
			##myPage  .carousel {
				display: block;
				vertical-align: middle;
			}  
			##myPage .rowFluid{
				display:flex;
				width:100%;
			}
			##myPage .rowFluid .rowCol{
				width:31.6%;
				margin-left:20px;
			}
			##myPage .rowFluid .rowCol > ul[id^=box]{
				padding-left:0;
				display:flex;
			}
			##myPage .rowFluid .rowCol > ul[id^=box] > li.boxTab{
				list-style: none;
			}
			##myPage .rowFluid .rowCol{				
				width:31.6%;
				margin-left:20px;
			}
			.rowFluid .rowCol:first-child{
				margin-left: 0 !important;
			}
			.rowFluid .rowCol .tab-pane.active{
				display:block !important;
			}
			.rowFluid .rowCol .tab-pane{
				display:none;
			}
			##myPage .owl-item,##myPage .owl-carousel,##myPage .owl-stage-outer,##myPage .owl-item img{
				max-width:250px !important;
			}
			##myPage .owl-item img{
				max-height:175px !important;
				margin-top: 25px !important;
			}
			##myPage .owl-carousel{
				margin: 0 auto !important;
			}
			##myPage .owl-carousel .owl-stage { display: flex; align-items: center; }
			##myPage .tab-pane .icon-spinner{width:1.3em;height:1.3em;line-height:1.3em}
			@media screen and (min-width: 768px)  and (max-width: 1700px) {
				##myPage .span1.myPhoto{
					width: 10.3646408839779% !important;
				}
			}
		</style>
		<script>
			var arrLoadedTabs = [];
			function renderMyPageTabContent(b,t) {
				if (!arrLoadedTabs.includes(t))
					$('##box'+b+'_'+t).load('#local.myPageData.tabContentURL#&tabid='+t, function() { 
						let injectedMergeTemplate = $('.mcMergeTemplate', this);
						mcExecuteSingleMergeTemplate(injectedMergeTemplate);
						arrLoadedTabs.push(t);
					}).show();
			}
			$(document).ready(function() {
				if(typeof($('.carousel').carousel) == "undefined"){
					var head = document.getElementsByTagName('head')[0];

					var script = document.createElement('script');
					var script1 = document.createElement('link');
					script.src = '/assets/common/javascript/owlCarousel/221/owl.carousel.min.js';
					script.type = 'text/javascript';

					script1.href = '/assets/common/javascript/owlCarousel/221/owl.carousel.min.css';
					script1.type = 'text/css';
					script1.rel = 'stylesheet';

					head.append(script);
					head.append(script1);

					$("head").append('<style type="text/css"></style>');
					var newStyleElement = $("head").children(':last');
					newStyleElement.html('.tabLi.active a{background:#local.settingsObj.activeTabBgColor# !important;color:#local.settingsObj.activeTabTextColor#  !important;} .tabLi:active a,.tabLi:focus a, .tabLi a{background:#local.settingsObj.inactiveTabBgColor# !important;color:#local.settingsObj.inactiveTabTextColor# !important;}');
				}
			});
			$(document).on('click','.tabLi',function(){
				$(this).closest('ul').find('li').removeClass('active');
				$(this).closest('.rowCol').find('.tabCntWrap .tab-pane').removeClass('active');
				tabId = $(this).data('tab');
				$('##'+tabId).addClass('active');
				$(this).addClass('active');
				var boxid = $(this).data("boxid");
				var tabid = $(this).data("tabid");
				renderMyPageTabContent(boxid,tabid);
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.myPageFrmJS#" />
<cfoutput>
	<div id="myPage" class="defaultMypage">	
		<cfloop query="local.objGrpMsgCnt">
			<div class="row-fluid renewTextWrap groupMessageWrap">#local.objGrpMsgCnt.rawContent#</div>
		</cfloop>
		<div class="welcomeMessageWrap">	
			#local.settingsObj.welcomeMessage#
		</div>
		<div class="profileWrap">			
			<div class="myPhoto">					
				<cfif attributes.data.hasMemberPhotoThumb is 1>
					<img src="/memberphotosth/#LCASE(attributes.data.memberNumber)#.jpg?cb=#getTickCount()#" width="100px" >
				<cfelse>
					<img src="/assets/common/images/directory/default.jpg" width="100px" >
				</cfif>
			</div>
			<div class="myInfo" style="margin-top: 5px;">
				<span class="BodyText profileLinksWrap">
					#local.settingsObj.profileLinks#
				</span>
			</div>
		</div>
		<cfset local.boxIdx = 0>
		<cfset local.currentBoxId = 0>
		<cfset local.newRow = 1>
		<cfset local.arrBoxTabId = ArrayNew(1)>
		<cfset local.arrBoxTabName = ArrayNew(1)>
		<cfset local.arrBoxTabContent = ArrayNew(1)>
		<cfset local.arrBoxTabIsCarousel = ArrayNew(1)>
		<cfset local.arrBoxTabCarouselInterval = ArrayNew(1)>
		
		<cfloop query="#local.objBoxTabCnt#">

			<cfif local.objBoxTabCnt.myPageBoxId neq local.currentBoxId>
				<cfif local.boxIdx % 3 eq 0>
					<cfif local.boxIdx eq 0>						<div class="rowFluid">
					</cfif>						
				</cfif>
				<cfif local.boxIdx neq 0>					
					<div class="rowCol">
						<ul class="nav nav-tabs" id="box#local.currentBoxId#">
							<cfloop array="#local.arrBoxTabName#" index="local.idx"  item="local.tabName" >
								<li class="tabLi <cfif local.idx eq 1 >active</cfif> boxTab" data-boxid="#local.currentBoxId#" data-tabid="#local.arrBoxTabId[local.idx]#" data-tab="box#local.currentBoxId#_#local.idx#">
									<a href="javascript:void(0);" >#local.tabName#</a>
								</li>
							</cfloop>							
						</ul>
						<div class="tab-content tabCntWrap">
							<cfloop array="#local.arrBoxTabContent#" index="local.idx"  item="local.tabCnt" >
								<div class="tab-pane <cfif local.idx eq 1 >active</cfif>" id="box#local.currentBoxId#_#local.idx#">
									<cfif local.arrBoxTabIsCarousel[local.idx]>
										<div id="carousel_#local.currentBoxId#_#local.idx#" class="owl-carousel box5aContentCarousel owl-theme"
											<cfif len(trim(local.tabCnt))>
												<cfloop list="#local.tabCnt#" index="local.thisImage" delimiters="|">
													#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#
												</cfloop>
											</cfif>
										</div>
										<cfif len(trim(local.tabCnt))>
											<script type='text/javascript'>
												$(document).ready(function() {
													setTimeout(function(){
														$('##carousel_#local.currentBoxId#_#local.idx#').owlCarousel({
															autoplayTimeout: #local.arrBoxTabCarouselInterval[local.idx]#,
															autoplay: true,
															items:1,
															margin:20,
															nav:false,
															dots:false,
															loop:true
														});
													}, 3500);
												});     
											</script>
										</cfif>
									<cfelse>
										<div id="box#local.currentBoxId#_#local.arrBoxTabId[local.idx]#" class="hide"><i class="icon-spinner icon-spin icon-large"></i> Loading...</div>
										<cfif local.idx eq 1>
										<script>
										$(document).ready(function(){ renderMyPageTabContent(#local.currentBoxId#,#local.arrBoxTabId[local.idx]#); });
										</script>
										</cfif>
									</cfif>
								</div>
							</cfloop>	
						</div>
					</div>
					<cfif local.boxIdx % 3 eq 0>
						<cfif local.boxIdx neq 0>
							</div>
							<div class="rowFluid">	
						</cfif>
					</cfif>
					<cfset ArrayClear(local.arrBoxTabId)>
					<cfset ArrayClear(local.arrBoxTabName)>
					<cfset ArrayClear(local.arrBoxTabContent)>
					<cfset ArrayClear(local.arrBoxTabIsCarousel)>
					<cfset ArrayClear(local.arrBoxTabCarouselInterval)>
				</cfif>

				<cfset local.boxIdx =  local.boxIdx + 1>
			</cfif>

			<cfset ArrayAppend(local.arrBoxTabId, local.objBoxTabCnt.myPageBoxTabId)>
			<cfset ArrayAppend(local.arrBoxTabName, local.objBoxTabCnt.tabTitle)>
			<cfset ArrayAppend(local.arrBoxTabContent, local.objBoxTabCnt.rawContent)>
			<cfset ArrayAppend(local.arrBoxTabIsCarousel, local.objBoxTabCnt.isCarousel)>
			<cfset ArrayAppend(local.arrBoxTabCarouselInterval, local.objBoxTabCnt.carouselInterval)>
			<cfset local.currentBoxId = local.objBoxTabCnt.myPageBoxId>
		</cfloop>

		<cfif local.boxIdx neq 0>
				<div class="rowCol">
					<ul class="nav nav-tabs" id="box#local.currentBoxId#">
						<cfloop array="#local.arrBoxTabName#" index="local.idx"  item="local.tabName" >
							<li class="tabLi <cfif local.idx eq 1 >active</cfif> boxTab" data-boxid="#local.currentBoxId#" data-tabid="#local.arrBoxTabId[local.idx]#" data-tab="box#local.currentBoxId#_#local.idx#">
								<a href="javascript:void(0);" >#local.tabName#</a>
							</li>
						</cfloop>							
					</ul>
					<div class="tab-content tabCntWrap">
						<cfloop array="#local.arrBoxTabContent#" index="local.idx"  item="local.tabCnt" >
							<div class="tab-pane <cfif local.idx eq 1 >active</cfif>" id="box#local.currentBoxId#_#local.idx#">
								<cfif local.arrBoxTabIsCarousel[local.idx]>
									<div id="carousel_#local.currentBoxId#_#local.idx#" class="owl-carousel box5aContentCarousel owl-theme">
										<cfif len(trim(local.tabCnt))>
											<cfloop list="#local.tabCnt#" index="local.thisImage" delimiters="|">
												#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#
											</cfloop>
										</cfif>
									</div>
									<cfif len(trim(local.tabCnt))>
										<script type='text/javascript'>
											$(document).ready(function() {
												setTimeout(function(){
													$('##carousel_#local.currentBoxId#_#local.idx#').owlCarousel({
														autoplayTimeout: #local.arrBoxTabCarouselInterval[local.idx]#,
														autoplay: true,
														items:1,
														margin:20,
														nav:false,
														dots:false,
														loop:true
													});
												}, 3500);
											});    
										</script>
									</cfif>
								<cfelse>
									<div id="box#local.currentBoxId#_#local.arrBoxTabId[local.idx]#" class="hide"><i class="icon-spinner icon-spin icon-large"></i> Loading...</div>
									<cfif local.idx eq 1>
									<script>
									$(document).ready(function(){ renderMyPageTabContent(#local.currentBoxId#,#local.arrBoxTabId[local.idx]#); });
									</script>
									</cfif>
								</cfif>
							</div>
						</cfloop>	
					</div>
				</div>
			</div>
		</cfif>
	</div>	
</cfoutput>