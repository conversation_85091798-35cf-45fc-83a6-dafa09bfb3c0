<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmJoin',
		formNameDisplay='Circle of Advocates Donation Form',
		orgEmailTo='<EMAIL>',
		memberEmailFrom='<EMAIL>'
	));

	local.profile_1 = application.objCustomPageUtils.acct_getProfile(siteid=local.siteID, profileCode='TNAJAuthorizeCIM');
	
	local.USStates = application.objCustomPageUtils.mem_getStatesByCountry('United States');
</cfscript>

<cfoutput>
	
	#local.pageJS#
	#local.pageCSS#
	
	<div id="customPage">
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			
			<cfcase value="0">
				
				<script type="text/javascript">
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						
							if (!_FB_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] 				= 'First Name';
							if (!_FB_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] 					= 'Last Name';
						
							if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] 					= 'Address';
							if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] 							= 'City';
							if (!_FB_hasValue(thisForm['stateCode'], 'SELECT')) arrReq[arrReq.length] 			= 'State';
							if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] 							= 'Zip Code';
							if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] 						= 'Phone Number';
							if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] 						= 'Email Address';
							
							if (!_FB_hasValue(thisForm['recognition'], 'RADIO')) arrReq[arrReq.length] 			= 'How would you like to be recognized?';
							if ( $('##recognitionIndividual').prop('checked') == true ){
								if (!_FB_hasValue(thisForm['recognitionOther'], 'TEXT')) arrReq[arrReq.length] = 'Name to recognize';
							}
					
							if (!_FB_hasValue(thisForm['donationAmount'], 'RADIO')) arrReq[arrReq.length] 	= 'Donation Amount';
							if ( $('##donationOtherRadio').prop('checked') == true ){
									if (!_FB_hasValue(thisForm['donationOther'], 'TEXT')) arrReq[arrReq.length] = 'Other Donation Amount';
							}
							
							if (!_FB_hasValue(thisForm['paymentOption'], 'RADIO')) arrReq[arrReq.length] 		= 'Payment Frequency';
					
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
				
					function assignMemberData(memObj){
						var thisForm = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								thisForm['memberNumber'].value 		= results.membernumber;
								thisForm['memberID'].value 			= results.memberid;

								thisForm['firstName'].value 		= results.firstname;
								thisForm['middleName'].value 		= results.middlename;
								thisForm['lastName'].value 			= results.lastname;
								thisForm['suffix'].value 			= results.suffix;
								thisForm['address'].value 			= results.address1;
								thisForm['address2'].value 			= results.address2;
								thisForm['city'].value 				= results.city;
								thisForm['zip'].value 				= results.postalcode;
								thisForm['phone'].value 			= results.phone;
								thisForm['email'].value 			= results.email;
								
								var stateCode = thisForm['stateCode'];
								for (var i=0; i <= stateCode.length-1; i++) {
									if (stateCode[i].value == results.statecode) stateCode[i].selected = true;
								}								
								
								// un hide form   
								document.getElementById('formToFill').style.display 			= '';
							}
							else{ /*alert('not success');*/ }
						};

						var objParams = { memberNumber:memObj.memberNumber };
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
					}					
					
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					
				function checkOther(x){
					if(x.checked && x.value == 'Other'){
						$('##donationOtherTR').show();	
					}
					else{
						$('##donationOtherTR').hide();
						$('input[name="donationOther"]').val();
					}
				}
				
				function checkRecognitionOther(x){
					if(x.checked && x.value == 'Individual Name'){
						$("##recognitionOtherTR").show();	
					}
					else{
						$("##recognitionOtherTR").hide();
						$('input[name="recognitionOther"]').val();
					}
				}
					
				</script>
				<br />
				<div class="r i frmText"><span class="required">* </span>Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="c">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
												<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
												<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
												<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>					
					
					<div id="formToFill" style="display:none;">
					
						<div class="CPSection">
							<div class="CPSectionTitle BB">Contact Information</div>
							<div class=" frmRow1 frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0" align="center">
									<tr class="frmRow1">
										<td class="r P" width="200">Prefix:</td>
										<td>
											<input name="prefix" type="radio" value="Mr." class="tsAppBodyText" />Mr.&nbsp;&nbsp;
											<input name="prefix" type="radio" value="Ms." class="tsAppBodyText" />Ms.&nbsp;&nbsp;
											<input name="prefix" type="radio" value="Mrs." class="tsAppBodyText" />Mrs.&nbsp;&nbsp;
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r P"><span class="required">* </span>First Name:</td>
										<td><input size="40" name="firstName" type="text" value="#session.cfcUser.memberData.firstname#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r P">Middle Name:</td>
										<td><input size="40" name="middleName" type="text" value="#session.cfcUser.memberData.middlename#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r P"><span class="required">* </span>Last Name:</td>
										<td><input size="40" name="lastName" type="text" value="#session.cfcUser.memberData.lastname#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r P">Suffix:</td>
										<td><input size="40" name="suffix" type="text" value="#session.cfcUser.memberData.suffix#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r P"><span class="required">* </span>Address:</td>
										<td><input size="40" name="address" type="text" value="#local.data.address.address1#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r P">&nbsp;</td>
										<td><input size="40" name="address2" type="text" value="#local.data.address.address2#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r P"><span class="required">* </span>City:</td>
										<td><input size="40" name="city" type="text" value="#local.data.address.city#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r P"><span class="required">* </span>State:</td>
										<td>
											<select name="stateCode" class="tsAppBodyText">
												<option value = ''>--Select a State</option>
												<cfloop query="local.USStates">
													<option value="#local.USStates.code#"<cfif local.USStates.code EQ local.data.address.stateCode> SELECTED</cfif>>#local.USStates.Name#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r P"><span class="required">* </span>Zip:</td>
										<td><input size="10" maxlength="15" name="zip" type="text" value="#local.data.address.postalCode#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r P"><span class="required">* </span>Phone:</td>
										<td class=" frmText">
											<input size="13" maxlength="13" name="phone" id="phone" type="text" value="" class="tsAppBodyText" onchange="checkPhFormat(this);" />
										</td>
									</tr>
									<tr class="frmRow1" id="phone_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter phone number in the format: ************</div></td></tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r P"><span class="required">* </span>Email Address:</td>
										<td><input size="40" name="email" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r P"><span class="required">* </span>How would you like the gift to be recognized?</td>
										<td>
											<input name="recognition" type="radio" value="Anonymous" class="tsAppBodyText" onChange="checkRecognitionOther(this);" />&nbsp;Anonymous
											<input name="recognition" type="radio" value="Firm Name" class="tsAppBodyText" onChange="checkRecognitionOther(this);" />&nbsp;Firm Name
											<input name="recognition" type="radio" value="Individual Name" id="recognitionIndividual" class="tsAppBodyText" onChange="checkRecognitionOther(this);" />&nbsp;Individual Name
										</td>
									</tr>
									<tr class="frmRow1" id="recognitionOtherTR" style="display:none;">
										<td class="r P"><span class="required">* </span>Name to recognize:</td>
										<td><input size="40" name="recognitionOther" type="text" value="#session.cfcUser.memberData.firstName#" class="tsAppBodyText" /></td>
									</tr>
									<tr class="frmRow1"><td colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB"><span class="required">* </span>Donation Level</div>
							<div class="frmRow1 frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="r PT" width="50%"><input name="donationAmount" type="radio" value="30000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$30,000
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT"><input name="donationAmount" type="radio" value="25000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$25,000
										</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r PT" width="50%"><input name="donationAmount" type="radio" value="20000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$20,000
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT" width="50%"><input name="donationAmount" type="radio" value="15000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$15,000
										</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r PT" width="50%"><input name="donationAmount" type="radio" value="10000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$10,000
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT"><input name="donationAmount" type="radio" value="7500" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$7,500
										</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r PT"><input name="donationAmount" type="radio" value="5000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$5,000
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT" width="50%"><input name="donationAmount" type="radio" value="3000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$3,000
										</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r PT"><input name="donationAmount" type="radio" value="2500" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$2,500
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT"><input name="donationAmount" type="radio" value="2000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$2,000
										</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r PT"><input name="donationAmount" type="radio" value="1500" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$1,500
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT"><input name="donationAmount" type="radio" value="1000" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$1,000
										</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="r PT"><input name="donationAmount" type="radio" value="500" class="tsAppBodyText" onChange="checkOther(this);"  /></td>
										<td class="l PT">
											$500
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="r PT"><input name="donationAmount" id="donationOtherRadio" type="radio" value="Other" class="tsAppBodyText" onChange="checkOther(this);" /></td>
										<td class="l PT">
											Other
										</td>
									</tr>
									<tr class="frmRow2" id="donationOtherTR" style="display:none;">
										<td colspan="2" class="c P">
											<span class="required">* </span>I would like to donate:&nbsp;<input name="donationOther" id="donationOther" type="text" value="" class="tsAppBodyText" onChange="checkNaN(this);">
										</td>
									</tr>
									<tr class="frmRow2" id="donationOther_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter a valid donation amount.</div></td></tr>
									<tr class="frmRow2"><td colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
						
							<div class="CPSection">
								<div class="CPSectionTitle BB"><span class="required">* </span>Payment Options</div>
								<div class="frmRow1 frmText">
									<table width="100%" cellspacing="0" cellpadding="0" border="0">
										<tr class="frmRow1">
											<td colspan="2">
												<table width="100%" cellpadding="0" cellspacing="0" border="0">
													<tr>
														<td width="50%" class="P c">
															<input type="radio" name="paymentOption" value="Single Payment">&nbsp;Single Payment
														</td>
														<td class="P c">
															<input type="radio" name="paymentOption" value="Quarterly">&nbsp;Four(4) Payments
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>
											
						<div id="formButtons">
							<div style="padding:10px;">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
								</div>
							</div>
						</div>
										
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
				
			</cfcase>
			
			<cfcase value="1">
				<cfscript>					
					
					local.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopup=false,
																			usePopupDIVName='ccForm',
																			autoShowForm=1
																		);
				</cfscript>
				<script type="text/javascript">
					function checkPaymentMethod() {
						var rdo = document.forms["#local.formName#"].payMeth;
						if (rdo[0].checked) {
							$('##CCInfo').show();
							$('##CheckInfo').hide();
						} else if (rdo[1].checked) {
							$('##CCInfo').hide();
							$('##CheckInfo').show();
						} 
					}

					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}

					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatementCC'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}						
						
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
				
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				
				<div class="BodyText">
					<div id="paymentTable">
						<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
						<div class="form">
							<cfform name="#local.formName#" id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _validate();">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
							
							<div class="CPSection">
								<div class="CPSectionTitle">*Method of Payment</div>
								<div class="P">
									<table cellpadding="2" cellspacing="0" width="100%" border="0">
										<tr valign="top">
											<td colspan="2">Please select your preferred method of payment from the options below.</td>
										</tr>
										<tr>
											<td>
												<table cellpadding="2" cellspacing="0" width="100%" border="0">
													<tr>
														<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Credit Card</td>
													</tr>
													<tr>
														<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Check</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>								
								
							<div id="CCInfo" class="CPSection" style="display:none;">
								<div class="CPSectionTitle">Credit Card Information</div>
								<div id="ccForm" style="padding:10px;">
									<div>#local.profile_1.strPaymentForm.inputForm#</div>
									<div><button type="submit" class="tsAppBodyButton" name="btnSubmit">SUBMIT</button></div>
								</div>
							</div>
							
							<div id="CheckInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Check Information</div>
									<div class="P">
										
												Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
												<strong>Tennesee Association for Justice</strong><br />
												1903 Division Street<br />
												Nashville, TN 37203
												
												
									</div>
									<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button></div>
								</div>								
								
							<cfinclude template="/model/cfformprotect/cffp.cfm" />
							</cfform>
						</div>
					</div>
				</div>
			</cfcase>
			
			<cfcase value="2">
				
				<!--- CHECK FOR SPAM SUBMISSION: -------------------------------------------------------------------------------- --->
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>
				
				<cfset local.totalAmount = event.getValue('donationAmount',0) />
				
				<cfif local.totalAmount eq 'Other'>
					<cfset local.totalAmount = event.getValue('donationOther',0)>
				</cfif>
				
				<cfif event.getValue('paymentOption','Single Payment') eq 'Quarterly'>
					<cfset local.perMonthTotal = local.totalAmount / 4>
				</cfif>

				<cfset local.timeStamp 			= now() />
				<cfset local.savedAccounting 	= false />

				<cfsavecontent variable="local.name">
					#event.getTrimValue('firstName','')# <cfif len(trim(event.getTrimValue('middleName','')))>#event.getTrimValue('middleName','')# </cfif>#event.getTrimValue('lastName','')#<cfif len(trim(event.getTrimValue('suffix','')))> #event.getTrimValue('suffix','')#</cfif>
				</cfsavecontent>
				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & trim(local.name) />
 
				<cfsavecontent variable="local.invoice">
					
					#local.pageCSS#
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
			
					<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">MemberNumber:</td><td class="frmText">#event.getValue('memberNumber','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Prefix:</td><td class="frmText">#event.getValue('prefix','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Suffix:</td><td class="frmText">#event.getValue('suffix','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Address:</td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Address Line 2:</td><td class="frmText">#event.getValue('address2','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#event.getValue('stateCode','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">How would you like to be recognized?</td><td class="frmText">#event.getValue('recognition','')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">CONTRIBUTION LEVEL</td></tr>
						<tr class="frmRow1">
							<td class="frmText b">#event.getValue('donationLevel','Donation Amount')#:</td>
							<td class="frmText">
								#dollarFormat(local.totalAmount)#&nbsp;
							</td>
						</tr>
						<cfif event.getValue('paymentOption','Single Payment') eq 'Quarterly'>
							<tr class="frmRow2"><td class="frmText b">Payment Schedule:</td><td class="frmText">#dollarFormat(local.perMonthTotal)# (first payment of four)&nbsp;</td></tr>
						</cfif>
						<tr><td class="frmText b frmRow1">Payment Type: </td><td class="frmText">
						<cfif event.getValue('payMeth','CC') EQ 'CC'>
							Credit Card
							<cfset arguments.event.setValue('p_#local.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#local.profile_1._profileID#_mppid',0)))) />
							<cfif arguments.event.getValue('p_#local.profile_1._profileID#_mppid') gt 0>
								<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
										mppid     = arguments.event.getValue('p_#local.profile_1._profileID#_mppid'),
										memberID  = val(local.useMID),
										profileID = local.profile_1._profileID) />
								- #local.qrySavedInfoOnFile.detail#
							</cfif>
						<cfelse>
							Check
						</cfif>
						
					</table>
				</cfsavecontent>
			
				<cftry>
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.useMID)>
					
					<cfset local.objSaveMember.setRecordType(recordType='Individual')>
					<cfset local.objSaveMember.setMemberType(memberType='User')>
					<cfset local.objSaveMember.setMemberStatus(memberStatus='Active')>
					
					<cfset local.strResult = local.objSaveMember.saveData()>
					<cfif not local.strResult.success>
						<cfthrow message="Unable to save member info.">
					</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
				</cftry>

				<!--- email member ---------------------------------------------------------------------------------------------- --->
				<cfset local.emailSentToUser = TRUE />
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for your donation!  The Circle of Advocates provides the resources TAJ needs to implement the only legislative program in Tennessee that ultimately protects the rights of clients we serve.<br /> 
						<br />
						Please print this page - it is your receipt.</p>	
						<hr />
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>
				
				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.memberEmail.from },
					emailto=[{ name="", email=local.memberEmail.to }],
					emailreplyto=local.ORGEmail.to,
					emailsubject=local.memberEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val(local.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				  )>
				  
				<cfset local.emailSentToUser = local.responseStruct.success>
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							#local.name# was not sent email confirmation due to bad Data.<br />
							Please contact, and let them know.
							<hr />
						</cfif>
						#local.invoice#
					</cfoutput>
				</cfsavecontent>
				<cfscript>
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
							emailhtmlcontent=local.mailContent,
							siteID=arguments.event.getValue('mc_siteInfo.siteID'),
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					}
				</cfscript>
				
				<!--- relocate to message page --->
				<cfset session.invoice = local.invoice />

				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			
			<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
			<cfcase value="99">
				<!--- output to screen --->
				<div class="HeaderText">Thank you for your donation!  The Circle of Advocates provides the resources TAJ needs to implement the only legislative program in Tennessee that ultimately protects the rights of clients we serve. </div>
				<br/>
				<div>Please print this page for your records. You will also receive a confirmation email.</div>
				<br />
				<cfif isDefined("session.invoice")>
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
					<cfset session.invoice = "" />
				</cfif>
			</cfcase>
			
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>