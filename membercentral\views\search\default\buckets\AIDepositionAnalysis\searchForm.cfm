<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
	</script>
	<style>
		.bk-ai-analysis-box {background-color: #fff;border: 1px solid #ddd;border-radius: 6px;padding: 10px 30px;box-shadow: 0 0 10px rgba(0,0,0,0.1);}
		div#bk_content {padding:0 8px 8px 4px;}
	</style>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
<div class="bk-ai-analysis-box">
	#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#

	<div id="searchBoxError" class="alert alert-error hide"></div>
	<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();">
		<table border="0" cellspacing="0" cellpadding="2">
		<tr>
			<td class="tsAppBodyText">Expert First Name:</td>
			<td><input type="text" name="s_fname"  id="s_fname" value="#local.strSearchForm.s_fname#" size="14" maxlength="50" class="tsAppBodyText"></td>
		</tr>
		<tr>
			<td class="tsAppBodyText">Expert Last Name:</td>
			<td><input type="text" name="s_lname"  id="s_lname" value="#local.strSearchForm.s_lname#" size="14" maxlength="50" class="tsAppBodyText"></td>
		</tr>
		<tr>
			<td></td>
			<td><input type="hidden" name="s_frm" id="s_frm" value="1">
				<button type="submit" name="s_btn" id="s_btn" class="tsAppBodyButton">Search</button></td>
		</tr>
		</table>
	</form>
</div>
</cfoutput>