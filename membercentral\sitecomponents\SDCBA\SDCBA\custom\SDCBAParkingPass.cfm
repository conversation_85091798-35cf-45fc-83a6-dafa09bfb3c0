<cfscript>
	variables.applicationReservedURLParams 	= "";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	
	arguments.event.paramValue('membernumber','');
	arguments.event.paramValue('downloadTxt',0);
	local.membernumber = arguments.event.getValue('membernumber');
	local.downloadTxt = arguments.event.getValue('downloadTxt');
	
	if(local.membernumber eq ''){
		local.membernumber = session.cfcUser.memberData.memberNumber;
	}
	local.arrCustomFields = [];
	local.tmpField = { name="ParkingHours", type="CONTENTOBJ", desc="Parking lot availability", value="Nights after 5pm and all weekend (Sat-Sun)" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="Address", type="CONTENTOBJ", desc="Parking lot Address", value="1220 Union Street Location Only (Ace lot 282)" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="Expiration", type="CONTENTOBJ", desc="Expiration Date", value="Expires 12/31/2019" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);
</cfscript>

<cfif local.downloadTxt eq 1>
	<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="SDCBA")>

	<cfdocument format="PDF" filename="#local.strFolder.folderPath#/parkingpass.pdf" overwrite="Yes">
		<cfoutput>
			<html>
				<head>
					<style type="text/css">						
						table.passTable {
							margin: 15px 0;
							border: 1px solid black;
							table-layout: fixed;
							width: 75%; 
							border-collapse: separate;
							border-spacing: 0;							
						}
						table.passTable td{
							display: table-cell !important;
						}
						body {
							max-width: 400px;
							margin: 0 auto;
							background: white;
							padding: 10px;
						}

						.passTable td:nth-child(1){
							width: 15%;	
						}
						.passTable td:nth-child(3) {
							width: 30%;							
						}
						.passTable td:nth-child(3) {
							background-color:##cfe7f5 !important;
							background:##cfe7f5 !important;
							border:none;
						}
						.passTable tr.leftContent td:nth-child(1){
							background-color:##52aedf !important;
							background:##52aedf important;
							border:none;
							text-align:center
						}
						.passTable tr.leftContent td:nth-child(2) {
							width: 55%;
							background-color:##52aedf !important;
							background:##52aedf important;
							border:none;
						}
						.sdcbaTicketLogo{
							padding-top:5px;
						}
						.sdcbaContentLogo{
							margin-top:-5px;
						}
						.cntTitle{
							font-size:70px;
							color:##fff;
							padding:45px 5px 5px 20px;
							font-style: italic!important;
							font-weight: bold!important;
						}
						.cntData{
							font-size:40px;
							color:##fff;
							padding:10px 5px 25px 20px;
						}
						.licensePlate{
							color: ##003760;
							padding: 20px 40px 40px 40px;
							font-size: 40px;
							vertical-align: top;
						}
						.licensePlate hr{
							border-bottom:none;
							border-top:3px solid ##003760;
							margin-left: 0px!important;
							margin-bottom: 13px;
						}
						.licensePlate span.msg{
							padding-left: 26px!important;
							color: ##003760;
							line-height:0px;
							font-size: 20px;
						}
						.memberNumberWrap{
							color: ##003760;
							font-size: 35px;
							margin-top:-5px !important;
							word-spacing: -4px !important;
						}
						div.expiresWrap{
							position:absolute;
							bottom:120px;
							color: ##003760;
							padding-left: 35px !important;
							font-size: 27px;
						}
						hr.bottomHr{
							border-bottom:none;
							border-top:2px dashed ##003760;
							margin-bottom: 11px;
							width: 300%;
							margin-left: -50%;
						}
						.msgFold{
							padding-left: 23px!important;
							color: ##003760;
							line-height:0px;
							font-size: 30px;
							border:0px solid ##fff;
						}
					</style>
				</head>
				<body>
					<table class="passTable" align="center">
						<tr>
							<td colspan="3" style="background: ##123164;"><img src="#arguments.event.getValue('mc_siteInfo.internalAssetsURL')#images/passHeading.PNG"></td>
						</tr>
						<tr>
							<td colspan="3">&nbsp;</td>
						</tr>
						<tr class="leftContent">
							<td class="sdcbaTicketLogo"><img src="#arguments.event.getValue('mc_siteInfo.internalAssetsURL')#images/sdcbaLogoTicket.PNG"></td>
							<td>
								<div class="cntTitle"><br/>SDCBA MEMBER<br/>PARKING PASS</div>
								<div class="cntData">#local.strPageFields.ParkingHours#</div>
							</td>
							<td>
								<div class="licensePlate">License Plate ##: <br/> <br/> 
									<hr>  <span class="msg"> Please write in</span>
									<br/><br/>
									<div class="memberNumberWrap" style="display:flex;word-spacing:-4px !important;letter-spacing: -4px;">
										<cfif Len(local.membernumber) gt 10>
											<cfloop index="local.intChar" from="1" to="#Len(local.membernumber)#" step="1">
												<cfset local.strChar = Mid(local.membernumber, local.intChar, 1 ) />
												<cfif trim(local.strChar) neq ''>
													#local.strChar#
												</cfif>
											</cfloop>
											<cfelse>
											#local.membernumber#
										</cfif>											
									</div>									
								</div>															
							</td>
						</tr>
						<tr class="leftContent">
							<td class="sdcbaContentLogo"><img src="#arguments.event.getValue('mc_siteInfo.internalAssetsURL')#images/aceLogoNew.PNG"></td>
							<td>
								<div class="cntData"><br/>#local.strPageFields.Address#</div>
							</td>
							<td>
								<div class="expiresWrap">#local.strPageFields.Expiration#</div>
							</td>
						</tr>
					</table>
					<table>
						<tr>
							<td><br/><br/></td>	
						</tr>
						<tr>
							<td><hr class="bottomHr"> <span class="msgFold">Fold Here</span></td>	
						</tr>
					</table>				
				</body>
			</html>
		</cfoutput>
	</cfdocument>

	<cfheader name="Content-Disposition" value="attachment;filename=parkingPass.pdf">
	<cfcontent type="application/octet-stream" file="#local.strFolder.folderPath#/parkingpass.pdf" deletefile="Yes">

	<cflocation url="#local.customPage.baseURL#" addtoken="false">

<cfelse>
	<cfsavecontent variable="local.CLEHead">
		<cfoutput>
		<link href="/css/printPass.css" rel="stylesheet" media="print" type="text/css">
		<style type="text/css">
			.BodyTextTitle{text-transform:uppercase;font-size:16px;}
			.alertMsg {
				text-align: left;
				padding: 5px 20px 5px 45px;
				border: 2px solid ##fc6;
			}
			.HeaderText{padding-top:10px;}
			.btnSubmit{
				line-height:20px !important;
				vertical-align:top !important;
			}
			.sdcbaTicketLogo{
				padding: 43px 0px 27px 0px;
			}
			.LeftContentLogo{
				padding: 5px 0px 60px 0px;
			}
			.sdcbaTicketLogo img{
				height : 140px !important;
			}
			.LeftContentLogo img{
				height : 100px !important;
				
			}
			.passWrap table{
				border: 1.5px solid ##123164;
				width:100%;
			}
			.parkingPassHead{
				color: ##fff;
				font-size: 30px;
				font-style: italic;
				font-weight: bold;
				line-height: 40px;
				margin-top: -35px;
			}
			.passWrap{
				margin:10px 10px 30px 10px !important;
			}
			.leftBg{
				background-color:##52aedf !important;
			}
			.rightBg{
				background-color:##cfe7f5 !important;
				vertical-align:top;
			}
			.leftContent{
				color: ##fff;
				font-size: 26px;
				line-height: 38px;
				margin-top: -30px;
			}
			.licensePlate{
				color: ##003760;
				padding: 25px;
				font-size: 18px;
				vertical-align: top;
			}
			.licensePlate hr{
				border-bottom:none;
				border-top:2px solid ##003760;
				margin-bottom: 11px;
			}
			hr.bottomHr{
				border-bottom:none;
				border-top:2px dashed ##003760;
				margin-bottom: 11px;
				width: 200%;
				margin-left: -50%;
			}
			.licensePlate span.msg{
				color: ##003760;
				line-height:0px;
				font-size:13px !important;
			}
			.memberNumberWrap{
				color: ##003760;
				font-size: 18px;
				word-break: break-all;
				word-spacing: -4px;
			}
			td.expiresWrap{
				vertical-align: bottom;
			}
			.expiresWrap div{
				
				padding:10px;
				padding-left: 25px;
				color: ##003760;
				font-size: 14px;
			}
			.formSection{
				padding:20px;
			}
			.bgBlue{
				background: ##52aedf !important;
			}
			.downloadBtn{
				margin-left:10px;
			}
			.td1{
				width:25%;
			}
			.td2{
				width:45%;
			}
			.td3{
				width:30%;
			}
		</style>
		<script language="JavaScript">
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function hideAlert() { $('##issuemsg').html('').hide(); };
			function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alertMsg').show(); };
			function _FB_validateForm(){
				var theForm = document.forms["frmSub"];
				var arrReq = new Array();
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or  application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					if (typeof $('##membernumber') != "undefined" && !_FB_hasValue(theForm['membernumber'], 'TEXT')) arrReq[arrReq.length] ='Must enter MemberNumber before you can generate a pass.';
				</cfif>
				if (arrReq.length > 0) {
					var msg = 'The following fields are required:\n\n';
					for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
					showAlert(msg);
					return false;
				}
			}
			function printpage() {
                $(".printLogo").show();                 
                window.print();
                $(".printLogo").hide();
            }
			function downloadPass() {
                $("##downloadTxt").val(1);                 
                $("##frmSub").submit();
				$("##downloadTxt").val(0); 
            }				
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.CLEHead#">

	<cfoutput>	
	<div class="row-fluid noprint">
		<div class="span12" id="issuemsg" style="display:none;margin:6px 0 10px 0;color:red"></div>
		<div class="span12"><h1 class="TitleText">Parking Pass</h1></div>
	</div>
	<br/>
	<cfform method="POST" action="#local.customPage.baseURL#" name="frmSub" id="frmSub" onsubmit="return _FB_validateForm();">
		<div class="row-fluid noprint">
			<div class="span12 formSection">
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<b>MemberNumber :</b> &nbsp;
					<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber."> &nbsp;
					<button type="submit" class="btn btn-default btnSubmit" name="btnSubmit">Generate Pass</button>
				<cfelse>
					<cfinput type="hidden" name="membernumber" id="membernumber" value="#local.membernumber#">
				</cfif>
				<cfinput type="hidden" name="downloadTxt" id="downloadTxt" value="0">
				<button class="btn pull-right downloadBtn" type="button" onClick="downloadPass();"><i class="icon-download"></i> Download</button> &nbsp;
				<button class="btn pull-right" type="button" onClick="printpage();"><i class="icon-print"></i> Print</button>
			</div>
		</div>
	</cfform>
	
	<div id="content" class="row-fluid passWrap">
		<table border="0" >
			<tr>
				<td colspan="3" style="background: ##123164;"><img src="./images/passHeading.PNG" ></td>
			</tr>
			<tr>
				<td colspan="3">&nbsp;</td>
			</tr>
			<tr class="bgBlue">
				<td style="text-align:center" class="sdcbaTicketLogo leftBg td1"><img src="./images/sdcbaLogoTicket.PNG" ></td>
				<td  class="leftBg td2">
					<div class="parkingPassHead"><br/> SDCBA MEMBER <br/> PARKING PASS</div>
					<br/>
					<br/>
					<br/>
					<div class="leftContent">#local.strPageFields.ParkingHours#</div>
				</td>
				<td class="rightBg td3">
					<div class="licensePlate">License Plate ##: <br/> <br/> 
						<hr>  <span class="msg"> Please write in</span>
						<br/><br/><br/>
						<div class="memberNumberWrap">
							<cfloop index="local.intChar" from="1" to="#Len(local.membernumber)#" step="1">
								<cfset local.strChar = Mid(local.membernumber, local.intChar, 1 ) />
								<cfif trim(local.strChar) neq ''>
									#local.strChar#
								</cfif>
							</cfloop>
						</div>
					</div>
				</td>
			</tr>
			<tr class="bgBlue">
				<td style="text-align:center" class="LeftContentLogo leftBg td1"><img src="./images/aceLogoNew.PNG" ></td>
				<td class="leftBg td2">
					
					<div class="leftContent"><br/>#local.strPageFields.Address#</div>
				</td>
				<td class="rightBg expiresWrap td3">
					<div >#  local.strPageFields.Expiration#</div>
				</td>
			</tr>
		</table>	
	</div>
	<hr class="bottomHr noscreen"> <span class="msg noscreen">Fold Here</span>
	</cfoutput>

</cfif>