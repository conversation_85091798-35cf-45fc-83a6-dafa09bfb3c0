<cfsavecontent variable="local.subPageHead">
	<cfoutput>
	<script type="text/javascript">
		function chainedSelect(elemIdName,elemIdDefault,elemValueDefault,selected,format){
			var strURL = "/?event=cms.showResource&resID=#local.strData.siteResourceID#&mode=stream&ra=subPanelData&panelid="+ selected + "&isActive=1";
			$.ajax({
				url: strURL,
				dataType: 'json',
				success: function(response){				
					$('##' + elemIdName).empty();
					for (var i = 0; i < response.DATA.length; i++) {
						if(response.DATA[i][1]) {//show panel in front end
							var o = new Option(response.DATA[i][1], response.DATA[i][0]);
							/* jquerify the DOM object 'o' so we can use the html method */
							$(o).html(response.DATA[i][1]);
							$('##' + elemIdName).append(o);
							$('##' + elemIdName).multiselect('refresh');
						}
					}
				},
				error: function(ErrorMsg){
				}
			})
		}	

		function callChainedSelect(panel,subpanel){
			var strSelected = $("##" + panel).val();
			chainedSelect(
				subpanel,		/* select box id  */
				0,				  /* select box default value */
				'Select Options',   /* select box default text */
				strSelected,		/* value of the select */
				'json'			  /* return format */
			);
		}
		
		function chkSubPanel1Select(subPanel,subID){
			
			if (!subCheckHasRun1){
				var dd = document.getElementById('subPanelID1');
				<cfloop list="#local.strData.subPanelID1#" index="local.thisItem">			
				for (var i=0; i < dd.length; i++){
					if (dd.options[i].value == #local.thisItem#) {
						dd.options[i].selected = true;
						break;
					}
				}
				</cfloop>
				subCheckHasRun1 = true;
				$("##subPanelID1").multiselect('refresh');
			}					
		}

		function refreshDropDown(panelField){
			setTimeout(function() {
				$("##" + panelField).multiselect('refresh');
			}, 1500);
		}

		$(function(){
			$("##subPanelID1").multiselect({
				multiple: true,
				header: false
			});
			$('body').on('change', '##panelID1', function(e) {
				var panelsArr = []; 
				<cfloop query="local.strData.qryGetPanelsFilter">
				panelsArr["#local.strData.qryGetPanelsFilter.panelID#"] = "#jsstringformat(rereplace(rereplace(local.strData.qryGetPanelsFilter.longDesc,'#chr(10)#','','ALL'),'#chr(13)#','<br>','ALL'))#";
				</cfloop>
				var divPanelDesc = "";
				callChainedSelect("panelID1","subPanelID1");
			});
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.subPageHead#">

<cfoutput>
<div class="<cfif local.strData.showContactFormFirst> hide</cfif>" id="panelSubpanel">
	<cfif len(trim(local.strData.feFormInstructionsStep1Content))>
		<div class="tsAppBodyText stepInstructions" style="margin-top:30px;">				
			#local.strData.feFormInstructionsStep1Content#							
		</div>	
	</cfif>
	<div class="questionLRISWrapper">
		<fieldset>
			<legend>Panel Selection <i id="editForm_panelSubpanel" class="icon icon-pencil hide editForm_panelSubpanel" data-id="panelSubpanel"></i></legend>
			<div class="innerLRISContentWrapper">
				<div id="panelSubpanelView" class="hide"></div>
				<div id="panelSubpanelForm">
					<div id="panelSubpanelIssueErr" class="alert alert-error hide"><span></span><button type="button" class="close" data-dismiss="alert">&times;</button></div>
					<div class="tsAppBodyText" data-label="Primary Panel">
						<label for="panelID1" class="control-label ">Primary Panel:*</label>
						<div class="controls">
							<cfselect name="panelID1" id="panelID1" class="tsAppBodyText">
								<option value="0">Select a Panel</option>
								<cfloop query="local.strData.qryGetPanelsFilter">
									<cfif local.strData.qryGetPanelsFilter.feDspClientReferral>
										<option value="#local.strData.qryGetPanelsFilter.panelID#" <cfif local.strData.panelid1 is local.strData.qryGetPanelsFilter.panelID>selected</cfif>>#local.strData.qryGetPanelsFilter.name#</option>
									</cfif>
								</cfloop>
							</cfselect>
						</div>
					</div>

					<div class="tsAppBodyText" data-label="Sub-Panel">
						<label for="subPanelID1" class="control-label ">Sub-Panel</label>
						<div class="controls">
							<cfselect name="subPanelID1" id="subPanelID1" multiple="true" >
								<cfif val(local.strData.panelid1)>
									<cfloop query="local.strData.qryGetSubPanelsFilter1">
										<cfif val(local.strData.qryGetSubPanelsFilter1.feDspClientReferral)>
											<option value="#local.strData.qryGetSubPanelsFilter1.panelID#" <cfif listFind(local.strData.subPanelID1,local.strData.qryGetSubPanelsFilter1.panelID)>selected</cfif>>#local.strData.qryGetSubPanelsFilter1.name#</option>
										</cfif>
									</cfloop>
								</cfif>				
							</cfselect>
						</div>
					</div>
				</div>

				<div class="tsAppBodyText">
					<button class="tsAppBodyButton btnContinuePanelSubpanel" type="button">Continue </button>
				</div>
			</div>
		</fieldset>
	</div>
</div>
</cfoutput>