<cfinclude template="../commonEditEntry.cfm">

<cfsavecontent variable="local.formJS">
<cfoutput>
	<script type="text/javascript">

		function showNewAuthor(r) {
			var rowNum = Number($('##tbodyAuthor tr.authorRow').length) + 1;
			$('##tbodyAuthor').append('<tr class="authorRow" id="authorRow' + r.memberid + '"><td>' + rowNum + '.</td><td>' + r.firstname + ' ' + r.lastname + '</td><td><a href="##" onclick="deleteAuthor(' + r.memberid + ');return false;">Remove</a></td></tr>');
		}
		function showNewBlogInstances(arrNewBID) {
			var rowNum = 0;
			var blogName = '';

			for (var k=0; k<arrNewBID.length;k++) { 
				rowNum = Number($('##tbodyBlogInstances tr.blInstanceRow').length) + 1;
				blogName = $('##newBlogInstance option[value="'+arrNewBID[k]+'"]').text();
				$('##tbodyBlogInstances').append('<tr class="blInstanceRow" id="blInstanceRow' + arrNewBID[k] + '"><td>' + rowNum + '.</td><td>' + blogName + '</td><td><a href="##" onclick="removeBlogInstance(' + arrNewBID[k] + ');return false;">Remove</a></td></tr>');
			}
			
			$('##btnAddBlogInstance').prop('disabled',false);
			$('##divNewBlogInstanceAddForm').hide();
		}
		function onChangePostType() {
			var ptid = $('##blPostTypeID').val() || '';

			$('.be-settings-well').toggleClass('mc_entry_hide', !(ptid && ptid != ''));
			$('##tBodyPostTypeFields, .div_posttype_cf').addClass('mc_entry_hide');
			$('.trCatAssignmentsContainer, tr.catTreeRow').hide();

			if($('##div_posttype_' + ptid + '_cf').length){
				$('##div_posttype_' + ptid + '_cf').removeClass('mc_entry_hide');
				$('##tBodyPostTypeFields').removeClass('mc_entry_hide');
			}

			var catTreeIDList = '';
			<cfif local.dataStruct.qryAllowedPostTypesForBlog.recordCount eq 1>
				catTreeIDList = $('##blPostTypeID').data('cattreeidlist') || '';
			<cfelse>
				catTreeIDList = $('##blPostTypeID').find(':selected').data('cattreeidlist') || '';

				var description = $('##blPostTypeID').find(':selected').data('desc') || '';
				$("##postTypeDesc").remove();
				if(description.length) $('##blPostTypeID').parent().append('<small id="postTypeDesc"><i>'+description+'</i></small>');
			</cfif>

			if (catTreeIDList && catTreeIDList.toString().length) {
				var arrCatTreeIDs = catTreeIDList.toString().split('|');
				arrCatTreeIDs = arrCatTreeIDs.map(function(e) { return '##catTree' + e; });
				$(arrCatTreeIDs.join(', ')).show();
				$('.trCatAssignmentsContainer').show();
				$('tr.catTreeRow:hidden').find('select.afterArchiveCat,select.beforeArchiveCat').val('').multiselect('refresh');
			}
		}
		function onChangeArchiveDate() {
			if($('##expirationDate').length){
				var expirationDate = $('##expirationDate').val().trim();
			}else{
				var expirationDate = "#dateFormat(local.dataStruct.getDocData.expirationDate,'m/d/yyyy')#";
			}
			
			if (expirationDate == '') {
				$('.mcblog_archivedatedepend').hide();
				$('select.afterArchiveCat').val('').multiselect('refresh');
			} else {
				$('.mcblog_archivedatedepend').show();
			}
		}
		function initBlogEntryFields() {
			var fd = new Object();
				fd.siteid = #attributes.event.getValue('mc_siteInfo.siteid')#;
				fd.blogid = #local.instanceSettings.blogID#;
				fd.blogentryid = #val(local.dataStruct.getDocData.blogEntryID)#;
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					fd.hideadminonly = 0;
				<cfelse>
					fd.hideadminonly = 1;
				</cfif>
			var entryFieldTemplateSource = $('##mc_blogEntryFieldTemplate').html();
			var entryFieldTemplate = Handlebars.compile(entryFieldTemplateSource);

			$.ajax({
				url: '?event=proxy.ts_json&c=ADMBLOG&m=getPostTypeFieldsInfo',
				type: 'POST',
				data: {fd:JSON.stringify(fd)},
				dataType: 'json',
				success: function(response) { 
					var tmpResponse = JSON.stringify(response).replace(/\^~~~\^/g,'');
					var responseResult = JSON.parse(tmpResponse);
					
					if(responseResult.arrPostTypes && responseResult.arrPostTypes.length) {
						responseResult.arrPostTypes.forEach(function(ptype) {
							if(ptype.hasPostTypeFields) {
								var postTypeFields = new Object();
								postTypeFields.arrPostTypeFields = ptype.arrPostTypeFields;

								var postTypeFieldHTML = '<div id="div_posttype_'+ptype.postTypeID+'_cf" class="div_posttype_cf mc_entry_hide">' + entryFieldTemplate(postTypeFields) + '</div>';

								$('##divMCPostTypeFieldContainer').append(postTypeFieldHTML);

								if($('##blPostTypeID').val() == ptype.postTypeID){
									$('##div_posttype_'+ptype.postTypeID+'_cf').removeClass('mc_entry_hide');
									$('##tBodyPostTypeFields').removeClass('mc_entry_hide');
								}
							}
						});

						if ($('div.div_posttype_cf').length)  {
							$('##divMCPostTypeFieldLoading').hide();
							initializeBlogEntryFieldControls($('##divMCPostTypeFieldContainer'));
						} else {
							$('##tBodyPostTypeFields').addClass('mc_entry_hide');
						}
					} else {
						$('##tBodyPostTypeFields').addClass('mc_entry_hide');
					}

					$('##btnSaveBlogEntry').attr('disabled',false);
					
				}, fail: function(response) { 
					alert('Some error occured while loading post type fields. Please contact MemberCentral.');
				}
			});
		}

	<cfif local.dataStruct.getDocData.blogEntryID gt 0>
		<cfset local.linkDomain = "#application.objPlatform.isRequestSecure() ? 'https' : 'http'#://#attributes.event.getValue('mc_siteinfo.mainhostname')#">
		function getBlogEntryDocs() {
			var getDocsResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.arrdocuments.length) {
						var html = '';
						for (var i=0; i<r.arrdocuments.length; i++) {
							html += '<tr id="entryDocRow'+r.arrdocuments[i].documentid+'" class="entryDocRow"><td><a href="/docDownload/'+r.arrdocuments[i].documentid+'" target="_blank">'+r.arrdocuments[i].filename+'</a></td><td><a href="#local.linkDomain#/docDownload/'+r.arrdocuments[i].documentid+'" class="copylinkBtn" id="'+r.arrdocuments[i].documentid+'" title="Copy Link"><i class="icon-copy"></i> Copy</a></td><td><a href="##" onclick="confirmDeleteBEDoc('+r.arrdocuments[i].documentid+');return false;"><i class="icon-trash"></i> Remove</a></a></td></tr>';
						}
						$('##tbodyDocs').append(html);
						initCopyEntryDocLinks();
						$('##divEntryDocsHolder').show();
						$('##BlogDocumentsFlexWrapper').addClass('has-existing')
					} else {
						$('##divEntryDocsHolder').hide();
					}
				} else {
					alert('We were unable to list blog entry documents.');
				}
			};

			$('##tbodyDocs tr.entryDocRow').remove();

			var objParams = { siteID:#arguments.event.getValue('mc_siteinfo.siteID')#, blogEntryID:mcblog_entryid };
			TS_AJX('ADMBLOG','getBlogEntryDocuments',objParams,getDocsResult,getDocsResult,10000,getDocsResult);
		}
	</cfif>
	</script>
	<style type="text/css">
		##articleDate, ##postDate, ##expirationDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:99%; background-repeat:no-repeat; }
		.mc_entry_hide { display:none !important; }
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
		.warning { background:##fff6bf url(/assets/common/images/warning.gif) 16px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border:2px solid ##fc6; }
		.fimg_info {line-height: 1.2em;}
		.hide{display:none !important;}
		.tableWell{
			border:1px solid;
			margin:15px 10px;
			width:100%;
			border-collapse: collapse !important;
		}
		.tableWell > tbody{
			padding: 10px 10px;
    		display: block;
		}
		.w100,##tbodyAuthor,##tbodyBlogInstances{
			display:block;
			width:100%;
		}
		##tbodyAuthor > tr{
			display:block;
			width:100%;
		}
		##tbodyBlogInstances > tr{
			display:block;
			width:100%;
		}

		div.tableWell{
			padding: 10px;
		}
		div.ui-multiselect-menu.catmultisel {width:220px!important;}
		table##tblCatAssignments tr td { border:0!important; }
	</style>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.formJS#">

<cfoutput>
	<cfinclude template="navigation.cfm">
	<cfform name="frmBlog" id="frmBlog">
		<cfinput type="hidden" name="blBlogSaved" id="blBlogSaved" value="1">
		<cfinput type="hidden" name="blBlogEntryID" id="blBlogEntryID" value="#local.dataStruct.getDocData.blogEntryID#">
		<cfinput type="hidden" name="blBlogContentID" id="blBlogContentID" value="#local.dataStruct.getDocData.blogContentID#">
		<cfinput type="hidden" name="blAuthorMIDList" id="blAuthorMIDList" value="#local.dataStruct.getDocData.authorMemberIDList#">
		<cfinput type="hidden" name="blogIDList" id="blogIDList" value="#local.dataStruct.getDocData.blogIDList#">
		<cfinput type="hidden" name="redirectID" id="redirectID" value="#local.dataStruct.getDocData.redirectID#">
		<cfinput type="hidden" name="currentRedirectName" id="currentRedirectName" value="#local.dataStruct.getDocData.redirectName#">
		<cfinput type="hidden" name="ownBlogID" id="ownBlogID" value="#local.dataStruct.getDocData.ownBlogID#">
		<cfif not local.instanceSettings.URL>
			<cfinput type="hidden" name="blURL" id="blURL" value="">
		</cfif>
		<cfif not local.instanceSettings.Title>
			<cfinput type="hidden" name="blTitle" id="blTitle" value="">
		</cfif>

		<div id="divBEFormSubmitArea" class="mc_entry_hide"></div>
		<div id="divBESaveLoading" class="mc_entry_hide">
			<h3><cfif local.editMode>Edit #local.instanceSettings.Singular#<cfelse>Add #local.instanceSettings.Singular#</cfif></h3>
			<div style="text-align:center;margin:30px 0;">
				<i class="icon-spin icon-spinner icon-3x"></i> <b><span class="loadingMsg">Please wait while we validate and save the details.</span></b>
			</div>
		</div>

		<div class="blogEntryHideWhileSaving">
			<div class="tsAppHeading"><cfif local.editMode>Edit #local.instanceSettings.Singular#<cfelse>Add #local.instanceSettings.Singular#</cfif></div>
			<br>
			<cfif len(trim(local.instanceSettings.addEntryFormBeforeContent))>
				#local.instanceSettings.addEntryFormBeforeContent#
			</cfif>
		</div>

		<!--- error placeholder --->
		<a name="entryFrmTop"></a>
		<div id="entry_frm_err" class="alert" style="display:none;margin:12px 0 0 0;"></div>

		<cfif local.dataStruct.getDocData.blogEntryID gt 0>
			<div id="divEntryConnectedWarning" class="warning blogEntryHideWhileSaving" style="display:none;margin:12px 0 10px 0;">
				Editing this #local.instanceSettings.Singular# will affect associated blogs.
			</div>
		</cfif>

		<table width="100%" cellpadding="2" cellspacing="0" class="blogEntryHideWhileSaving">
			<tr>
				<cfif isdefined('local.dataStruct.msg')>
					<cfswitch expression="#local.dataStruct.msg#">
						<cfcase value="1"><cfset local.msgText = "#local.instanceSettings.Singular# has been Added"></cfcase>
						<cfcase value="2"><cfset local.msgText = "#local.instanceSettings.Singular# has been Saved"></cfcase>
						<cfdefaultcase><cfset local.msgText = ""></cfdefaultcase>
					</cfswitch>
					<td class="tsAppBodyText tsAppBodyTextImportant">#local.msgText#</td>
				</cfif>
				<td align="right">
					<cfif local.editMode and local.canDelete>
						<button class="tsAppBodyButton" id="deleteButton" type="button" onClick="javascript:confirmDelete('/?#local.baseQueryString#&blAction=deleteEntry&blBlogEntryID=#local.dataStruct.getDocData.blogEntryID#')">Delete</button>
					</cfif>
				</td>
			</tr>
		</table>

		<cfif local.dataStruct.qryAllowedPostTypesForBlog.recordCount eq 1>
			<input type="hidden" name="blPostTypeID" id="blPostTypeID" value="#local.dataStruct.qryAllowedPostTypesForBlog.postTypeID#" data-cattreeidlist="#local.dataStruct.qryAllowedPostTypesForBlog.categoryTreeIDList#">
		<cfelse>
			<table class="tsAppBodyText postTypeWell tableWell blogEntryHideWhileSaving">
				<tbody>
					<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
						<cfif (not local.editMode) or local.canEditMetaData>
							<tr>
								<td class="">Choose #ucFirst(local.instanceSettings.Singular)# Type:</td>
								<td></td>
								<td>
									<select name="blPostTypeID" id="blPostTypeID" onchange="onChangePostType();">
										<option value=""></option>
										<cfloop query="local.dataStruct.qryAllowedPostTypesForBlog">
											<option value="#local.dataStruct.qryAllowedPostTypesForBlog.postTypeID#" data-cattreeidlist="#local.dataStruct.qryAllowedPostTypesForBlog.categoryTreeIDList#"<cfif local.dataStruct.qryAllowedPostTypesForBlog.postTypeID eq local.dataStruct.getDocData.postTypeID> selected="selected"</cfif> data-desc="#local.dataStruct.qryAllowedPostTypesForBlog.description#">#local.dataStruct.qryAllowedPostTypesForBlog.typeName#</option>
										</cfloop>
									</select>
								</td>
							</tr>
						</cfif>
					</cfif>
				</tbody>
			</table>
		</cfif>

		<table class="be-settings-well mc_entry_hide tsAppBodyText tableWell blogEntryHideWhileSaving" style="margin-bottom:20px;">
			<tbody id="tBodyBasicInfo">
			<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
				<cfif (not local.editMode) or local.canEditMetaData>
					<cfif local.instanceSettings.Title>
						<tr>
							<td class="">Title:</td>
							<td>*</td>
							<td><cfinput class="tsAppBodyText" type="text" name="blTitle"  id="blTitle" value="#local.dataStruct.getDocData.blogTitle#" size="50"></td>
						</tr>
					</cfif>
					
					<cfset local.statusID = local.dataStruct.getDocData.statusName EQ 'Approved' ? local.dataStruct.getDocData.statusID : 0>
						
					<cfloop query="local.dataStruct.qryBlogStatuses">
						<cfif local.dataStruct.getDocData.statusID eq local.dataStruct.qryBlogStatuses.statusID> 
							<cfset local.statusID = local.dataStruct.qryBlogStatuses.statusID>
						<cfelseif local.dataStruct.getDocData.blogEntryID is 0 and local.dataStruct.qryBlogStatuses.statusName eq 'Draft'> 
							<cfset local.statusID = local.dataStruct.qryBlogStatuses.statusID>
						</cfif>
					</cfloop>
					
					<input name="blStatusID" id="blStatusID" type="hidden" value="#local.statusID#">
					
					<cfif local.instanceSettings.URL>
						<tr>
							<td class="">URL:*</td>
							<td></td>
							<td><cfinput class="tsAppBodyText" type="text" required="true" message="Enter a URL for your #local.instanceSettings.Singular#" name="blURL"  id="blURL" value="#local.dataStruct.getDocData.blogURL#" size="50"></td>
						</tr>
					</cfif>					
					
					<cfif local.dataStruct.isOwnBlogEntry and local.instanceSettings.appRightsStruct.manageQuickLinks is 1>
						<tr>
							<td class="">Quick Link:</td>
							<td></td>
							<td>
								<span class="mr-2">#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/</span>
								<input type="text" id="newRedirectName" name="newRedirectName" value="#local.dataStruct.getDocData.redirectName#" onblur="onBlurRedirectName(this.value);" />
								<div id="redirectBox" class="py-2 d-none">
									<i class="fas" id="redirectImg"></i> <span id="redirectText"></span>
								</div>
							</td>
						</tr>
					</cfif>
					<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates eq 1>
						<tr>
							<td></td>
							<td></td>
							<td>
								<label><input type="checkbox" class="tsAppBodyText" name="isStickyBlog" id="isStickyBlog" value="1"<cfif local.dataStruct.getDocData.isSticky is 1> checked="checked"</cfif>> Keep Entry at top of list</label>
							</td>
						</tr>
					</cfif>
					<tr class="trCatAssignmentsContainer">
						<td>Category Assignments:</td>
						<td></td>
						<td></td>
					</tr>
					<tr class="trCatAssignmentsContainer">
						<td colspan="3">
							<table>
								<tr>
									<td>
										<p class="mcblog_archivedatedepend">#local.instanceSettings.plural# can be automatically reassigned to different categories on the Archive Date you select.</p>
										<table id="tblCatAssignments" class="table tsAppBodyText" style="max-width:650px;">
											<thead>
												<tr valign="top" class="mcblog_archivedatedepend">
													<th>&nbsp;</th>
													<th>Before Archive Date</th>
													<th>After Archive Date</th>
												</tr>
											</thead>
											<cfloop query="local.dataStruct.qryGetCategoryTrees">
												<cfset local.treeCategories = local.objCategory.getIndentedCategoriesForTree(categoryTreeID=local.dataStruct.qryGetCategoryTrees.categoryTreeID,ordertype="categoryPath")>
												<tr id="catTree#local.dataStruct.qryGetCategoryTrees.categoryTreeID#" class="catTreeRow" valign="top">
													<td class="">#local.dataStruct.qryGetCategoryTrees.categoryTreeName#:</td>
													<td>
														<cfif local.treeCategories.qryCategories.recordCount gte 10>
															<cfset local.listBoxSize = 10>
														<cfelseif local.treeCategories.qryCategories.recordCount eq 0>
															<cfset local.listBoxSize = 1>
														<cfelse>
															<cfset local.listBoxSize = local.treeCategories.qryCategories.recordCount>
														</cfif>
														<cfset local.categoryName = "s_#local.dataStruct.qryGetCategoryTrees.categoryTreeID#ID">
														<select name="#local.categoryName#" id="#local.categoryName#" class="tsAppBodyText beforeArchiveCat" multiple="multiple" size="#local.listBoxSize#">
															<cfloop query="local.treeCategories.qryCategories">
																<option value="#local.treeCategories.qryCategories.categoryID#" <cfif StructKeyExists(local.dataStruct.getExtraDocData, "#local.categoryName#") AND  find("#local.treeCategories.qryCategories.categoryID#", local.dataStruct.getExtraDocData["#local.categoryName#"]) >selected</cfif>>
																	<cfloop from="1" to="#local.treeCategories.qryCategories.catlevel#" index="local.currCatCount">
																		&nbsp;&nbsp;&nbsp;&nbsp;
																	</cfloop>#local.treeCategories.qryCategories.categoryName#
																</option>					
															</cfloop>
														</select>
														<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates is 0>
															<cfloop query="local.treeCategories.qryCategories">
																<cfif StructKeyExists(local.dataStruct.getExtraDocData, "#local.categoryName#") AND  find("#local.treeCategories.qryCategories.categoryID#", local.dataStruct.getExtraDocData["#local.categoryName#"]) >
																	<input type="hidden" name="#local.categoryName#" value="#local.treeCategories.qryCategories.categoryID#">
																</cfif>
															</cfloop>
														</cfif>
													</td>
													<td class="mcblog_archivedatedepend">
														<cfif local.treeCategories.qryCategories.recordCount gte 10>
															<cfset local.listBoxSize = 10>
														<cfelseif local.treeCategories.qryCategories.recordCount eq 0>
															<cfset local.listBoxSize = 1>
														<cfelse>
															<cfset local.listBoxSize = local.treeCategories.qryCategories.recordCount>
														</cfif>
														<cfset local.categoryName = "s_#local.dataStruct.qryGetCategoryTrees.categoryTreeID#ID">
														<select name="#local.categoryName#_Archived" id="#local.categoryName#_Archived" class="tsAppBodyText afterArchiveCat" multiple="multiple" size="#local.listBoxSize#" <cfif local.instanceSettings.appRightsStruct.canEditPublishingDates is 0> disabled</cfif>>
															<cfloop query="local.treeCategories.qryCategories">
																<option value="#local.treeCategories.qryCategories.categoryID#" <cfif structKeyExists(local.dataStruct.getDocData,"archiveCategories") and StructKeyExists(local.dataStruct.getDocData.archiveCategories, local.categoryName) AND  listfind(local.dataStruct.getDocData.archiveCategories[local.categoryName], local.treeCategories.qryCategories.categoryID) >selected</cfif>>
																	<cfloop from="1" to="#local.treeCategories.qryCategories.catlevel#" index="local.currCatCount">
																		&nbsp;&nbsp;&nbsp;&nbsp;
																	</cfloop>#local.treeCategories.qryCategories.categoryName#
																</option>					
															</cfloop>
														</select>
														
														<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates is 0>
															<cfloop query="local.treeCategories.qryCategories">
																<cfif structKeyExists(local.dataStruct.getDocData,"archiveCategories") and StructKeyExists(local.dataStruct.getDocData.archiveCategories, local.categoryName) AND  listfind(local.dataStruct.getDocData.archiveCategories[local.categoryName], local.treeCategories.qryCategories.categoryID)>
																	<input type="hidden" name="#local.categoryName#_Archived" value="#local.treeCategories.qryCategories.categoryID#">
																</cfif>
															</cfloop>
														</cfif>
													</td>
												</tr>
											</cfloop>
										</table>
									</td>
								<tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="3">
							<table class="tsAppBodyText">
								<tbody id="tBodyPostTypeFields" class="mc_entry_hide">
									<tr>
										<td colspan="3">
											
										</td>
									</tr>
									<tr><td colspan="3"></td></tr>
									<tr>
										<td colspan="3">
											<div id="divMCPostTypeFieldLoading" class="c"><i class="icon-spin icon-spinner"></i><br/><b>Please Wait...</b></div>
											<div id="divMCPostTypeFieldContainer" class="MCPostTypeFieldContainer"></div>
										</td>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
				</cfif>
			</cfif>
			</tbody>
		</table>
		
		<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
			<cfif (not local.editMode) or local.canEditMetaData>
				<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates eq 1>
					<table class="be-settings-well mc_entry_hide tsAppBodyText tableWell blogEntryHideWhileSaving" style="margin-bottom:20px;">
						<tbody >
								<tr>
									<td class="">Article Date:</td>
									<td></td>
									<td>
										<input class="tsAppBodyText" name="articleDate" id="articleDate" type="text" value="#dateFormat(local.dataStruct.getDocData.articleDate,'m/d/yyyy')# - #timeFormat(local.dataStruct.getDocData.articleDate,'h:mm tt')#" size="26" />
										<span>Central</span>
										<a href="javascript:mca_clearDateRangeField('articleDate');">Clear Date</a>
									</td>
								</tr>
								<tr>
									<cfif len(trim(local.instanceSettings.publishDateLabel)) GT 0 >
										<td class="">#local.instanceSettings.publishDateLabel#:</td>
									<cfelse>
										<td class="">Publish Date:</td>
									</cfif>
									<td></td>
									<td>
										<input class="tsAppBodyText" name="postDate" id="postDate" type="text" value="#dateFormat(local.dataStruct.getDocData.postDate,'m/d/yyyy')# - #timeFormat(local.dataStruct.getDocData.postDate,'h:mm tt')#" size="26" />
										<span>Central</span>
										<a href="javascript:mca_clearDateRangeField('postDate');">Clear Date</a><br/>
										<small><i>Schedule an article to be released at a later date by entering a Publish Date in the future</i></small>
									</td>
								</tr>
								<tr>
									<td class="">Archive Date:</td>
									<td></td>
									<td>
										<input class="tsAppBodyText" name="expirationDate" id="expirationDate" type="text" value="#dateFormat(local.dataStruct.getDocData.expirationDate,'m/d/yyyy')#" size="26" onchange="onChangeArchiveDate();">
										<span>Central</span>
										<a href="##" onclick="mca_clearDateRangeField('expirationDate');onChangeArchiveDate();return false;">clear</a>
									</td>
								</tr>					
						</tbody>
					</table>
				</cfif>
			</cfif>
		</cfif>

		<cfif local.canManageAuthors or local.canCrossPost>
			<table class="be-settings-well mc_entry_hide tsAppBodyText tableWell blogEntryHideWhileSaving">
				<tbody>
				<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
					<cfif (not local.editMode) or local.canEditMetaData>
						<cfif local.canManageAuthors>
							<tr class="w100">
								<td class="w100">
									<table class="table w100" >
										<tbody id="tbodyAuthor">
											<tr>
												<td colspan="3" style="border-top:0;">
													<button type="button" name="btnAddAuthor" id="btnAddAuthor" class="tsAppBodyButton" style="float:right;" onclick="selectBlogAuthor();">Add New Author</button>
												</td>
											</tr>
											<tr>
												<th style="width:10%">##</th>
												<th>Author</th>
												<th style="width:10%"></th>
											</tr>
						
											<cfset local.thisNum = 1>
											<cfloop list="#local.dataStruct.getDocData.authors#" delimiters="^~~~^" index="local.thisAuthor">
												<tr class="authorRow" id="authorRow#ListFirst(local.thisAuthor,'|')#">
													<td>#local.thisNum#.</td>
													<td>#ListGetAt(local.thisAuthor,2,'|')# #ListGetAt(local.thisAuthor,3,'|')#</td>
													<td>
														<cfif local.instanceSettings.appRightsStruct.editAny is 1>
															<a href="##" onclick="deleteAuthor(#ListFirst(local.thisAuthor,'|')#);return false;">Remove</a>
														</cfif>
													</td>
												</tr>
												<cfset local.thisNum = local.thisNum + 1>
											</cfloop>
										</tbody>
									</table>
								</td>
							</tr>
						</cfif>
						<cfif local.canCrossPost>
							<tr class="w100">
								<td class="w100">
									<table class="table w100">
										<tbody id="tbodyBlogInstances">
											<cfif local.dataStruct.allowedCrossPostingTargets.len()>
												<tr>
													<td colspan="3" style="border-top:0;">
														<button type="button" name="btnSelectBlogInstance" id="btnSelectBlogInstance" class="tsAppBodyButton" style="float:right;" onclick="selectBlogInstance();">Connect to New Blog</button>
													</td>
												</tr>
												<tr>
													<td colspan="3" style="border-top:0;">
														<div id="divBlogInstancesLoading" style="padding-top:10px;padding-left:5px;display:none;"><i class="icon-spin icon-spinner"></i> <b>Loading...</b></div>
														<div id="divNewBlogInstanceAddForm" style="margin:10px 0;display:none;">
															<select id="newBlogInstance" name="newBlogInstance" multiple="multiple"></select>
															<button type="button" name="btnAddBlogInstance" id="btnAddBlogInstance" class="tsAppBodyButton" onclick="doAddBlogInstance();">Add</button>
														</div>
													</td>
												</tr>
											</cfif>
											<tr>
												<th style="width:10%">##</th>
												<th>Title</th>
												<th style="width:10%"></th>
											</tr>
											<cfset var thisSharedBlogID = 0>
											<cfloop query="local.dataStruct.qrySharedBlogInstances">
												<cfset local.thisSharedBlogPerms = "">
												<cfset thisSharedBlogID = local.dataStruct.qrySharedBlogInstances.blogID>
												<cfset local.thisSharedBlog = local.dataStruct.allowedCrossPostingTargets.filter((item) => item.blogID eq thisSharedBlogID)>
												<cfif local.thisSharedBlog.len()>
													<cfset local.thisSharedBlogPerms = local.thisSharedBlog.first().functions.replace('|',',',"ALL")>
												<cfelseif local.dataStruct.qrySharedBlogInstances.blogID eq local.instanceSettings.blogID>
													<cfif local.instanceSettings.appRightsStruct.AddBlog>
														<cfset local.thisSharedBlogPerms = local.thisSharedBlogPerms.listAppend("AddBlog")>
													</cfif>
													<cfif local.instanceSettings.appRightsStruct.editOwn>
														<cfset local.thisSharedBlogPerms = local.thisSharedBlogPerms.listAppend("editOwn")>
													</cfif>
													<cfif local.instanceSettings.appRightsStruct.editAny>
														<cfset local.thisSharedBlogPerms = local.thisSharedBlogPerms.listAppend("editAny")>
													</cfif>
												</cfif>
												<tr class="blInstanceRow" id="blInstanceRow#local.dataStruct.qrySharedBlogInstances.blogID#">
													<td class="tsAppBodyText">#local.dataStruct.qrySharedBlogInstances.currentRow#.</td>
													<td>#local.dataStruct.qrySharedBlogInstances.applicationInstanceName#</td>
													<td class="tsAppBodyText">
														<cfif local.dataStruct.getDocData.blogID eq local.dataStruct.qrySharedBlogInstances.blogID>
															<small>Primary</small>
														<cfelseif (listFindNoCase(local.thisSharedBlogPerms,"editAny") or (listFindNoCase(local.thisSharedBlogPerms,"editOwn") and listFind(local.dataStruct.getDocData.authorMemberIDList,session.cfcuser.memberdata.memberID)))>
															<a href="##" onclick="removeBlogInstance(#local.dataStruct.qrySharedBlogInstances.blogID#);return false;">Remove</a>
														</cfif>
													</td>
												</tr>
											</cfloop>
										</tbody>
									</table>
								</td>
							</tr>
						</cfif>
					</cfif>
				</cfif>
				</tbody>
			</table>
		</cfif>

		<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>			
			<cfif (not local.editMode) or local.canEditMetaData>	
				<div class="be-settings-well mc_entry_hide tableWell blogEntryHideWhileSaving">
					<div class="tsAppHeading" style="margin-bottom:10px;">Summary:*</div>
					<cfif len(local.instanceSettings.shortDescForContentSummary)>
						<div style="margin-bottom:10px;">#paragraphFormat(local.instanceSettings.shortDescForContentSummary)#</div>
					</cfif>
					<cfif local.instanceSettings.WYSIWYG>
						#local.dataStruct.getDocData.blogSummary.html#
					<cfelse>
						<textarea class="tsAppBodyText" id="rawContentSummary" name="rawContent" rows="5" cols="50">#local.dataStruct.getDocData.blogSummary.text#</textarea>
					</cfif>
				</div>
				
				<div class="be-settings-well mc_entry_hide tableWell blogEntryHideWhileSaving">
					<div class="tsAppHeading" style="margin-bottom:10px;">#local.instanceSettings.NameDesc#:*</div>
					<cfif len(local.instanceSettings.shortDescForContentBody)>
						<div style="margin-bottom:10px;">#paragraphFormat(local.instanceSettings.shortDescForContentBody)#</div>
					</cfif>
					<cfif local.instanceSettings.WYSIWYG>
						#local.dataStruct.getDocData.blogBody.html#
					<cfelse>
						<cfif local.instanceSettings.NameDesc eq "Wall">
							<cfif not len(local.dataStruct.getDocData.blogBody.text)>
								<cfset local.dataStruct.getDocData.blogBody.text = "What's on your mind?">
							</cfif>
							<cfinput type="text" id="rawContent" name="rawContent" value="#local.dataStruct.getDocData.blogBody.text#" class="tsAppBodyText" style="width:280px;" onfocus="this.value='';" required="false"  message="Enter a #local.instanceSettings.NameDesc#.">
							<button class="tsAppBodyButton" name="btnSaveBlogEntry" id="btnSaveBlogEntry" type="button" onclick="validateAndSaveEntryDetails();">Save & Review</button> <button class="tsAppBodyButton" id="cancelButton" type="button" onClick="<cfif attributes.data.blAction EQ "addEntry">self.location.href='/?#local.baseQueryString#<cfelse>self.location.href='/?#local.baseQueryString#&blAction=showEntry&blogEntry=#local.dataStruct.getDocData.blogEntryID#</cfif>';">Cancel</button>
						<cfelse>
							<textarea class="tsAppBodyText" id="rawContent" name="rawContent" rows="5" cols="50">#local.dataStruct.getDocData.blogBody.text#</textarea>
						</cfif>
					</cfif>
				</div>
			</cfif>
		</cfif>		

		<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
			<cfif (not local.editMode) or local.canEditMetaData>
				<cfif local.dataStruct.showFeatureImageControls>
					<div style="width:100%;">
						<cfset local.doesImageExist = val(local.dataStruct.getDocData.featureImageID) gt 0 and fileExists("#local.dataStruct.featuredImageFullRootPath##local.dataStruct.getDocData.featureImageID#.#local.dataStruct.getDocData.featureImageFileExt#")>
						<table id="featuredImageWell" class="tsAppBodyText be-settings-well mc_entry_hide tableWell blogEntryHideWhileSaving" style="width:100%;margin-bottom:20px;">
							<tbody style="display:table-row-group;">
								<tr><td style="padding:5px 10px;"><b><cfif NOT local.doesImageExist>Add </cfif>Featured Image:</b></td></tr>
								<tr>
									<td style="padding:5px 10px;">
										<div id="featuredImageFlexWrapper" <cfif val(local.dataStruct.getDocData.featureImageID) gt 0>class="has-existing"</cfif>>
											<div id="featuredImageUppyWrapper" class"ml-0">
												<div id="mcBEFeaturedImageUploader" class="blogEntryHideWhileSaving"></div>
											</div>
										<cfif val(local.dataStruct.getDocData.featureImageID) gt 0>
											<div id="featuredImageExistingImageWrapper">
												<div id="imgDiv"  class="blogEntryHideWhileSaving">
													<cfif local.doesImageExist>
														<div style="margin-bottom:10px;">
															<div><label>Existing Image: <button type="button" name="btnDeletePostImage" id="btnDeletePostImage" class="mcblogentry_ftdimage" onclick="removeFeaturedImage(#local.dataStruct.getDocData.blogEntryID#,'blogEntry');">Remove</button></label></div>
														</div>

														<div id="divBlogEntryFeaturedImage" class="mcblogentry_ftdimage">
															<a href="#local.dataStruct.featuredOriginalImageRootPath##local.dataStruct.getDocData.featureImageID#.#local.dataStruct.getDocData.featureImageFileExt#?#local.dataStruct.imageUUID#" target="_blank">
																<img id="blogEntryFtdImg" src="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-preview-large.#local.dataStruct.getDocData.featureImageFileExt#?#local.dataStruct.imageUUID#" class="mcblog_img">
															</a>
														</div>
														<div class="mcblogentry_ftdimage" style="margin:5px;">
															<cfif local.dataStruct.qryFeaturedImageConfigSizes.recordCount>
																<div>Thumbnail Previews (click to see in new window)</div>
															</cfif>
															<cfloop query="local.dataStruct.qryFeaturedImageConfigSizes">
																<div style="float:left;margin:2px;">
																	<div style="text-align:center;height:100px;display:table-cell;vertical-align:bottom;">
																		<a href="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.dataStruct.qryFeaturedImageConfigSizes.featureImageSizeID#.#local.dataStruct.qryFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" target="_blank">
																			<img src="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.dataStruct.qryFeaturedImageConfigSizes.featureImageSizeID#-preview-small.#local.dataStruct.qryFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" class="mcblog_img">
																		</a>
																		<div class="fimg_info"><i><small>#local.dataStruct.qryFeaturedImageConfigSizes.featuredImageSizeCode#</small></i></div>
																		<div class="fimg_info"><i><small>#local.dataStruct.qryFeaturedImageConfigSizes.width#x#local.dataStruct.qryFeaturedImageConfigSizes.height#</small></i></div>
																	</div>
																</div>
															</cfloop>
															<div style="clear:both;"></div>
														</div>
														<cfif listLen(local.dataStruct.relatedFeatureImageConfigIDList) gt 0>
															<div style="margin-top:20px;" class="mcblogentry_ftdimage">
																<b>Image Configs Used By Connected Blogs:</b>														
																<cfloop list="#local.dataStruct.relatedFeatureImageConfigIDList#" index="local.relatedFeatureImageConfigID">
																	<cfquery name="local.qryRelatedFeaturedImageConfigSizes" dbtype="query">
																		select featureImageSizeID, featuredImageSizeName, featuredImageSizeDesc, featuredImageSizeCode, width, height, fileExtension
																		from local.dataStruct.qryAllRelatedFeaturedImageConfigSizes
																		where featureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.relatedFeatureImageConfigID#">
																		order by featureImageSizeID;
																	</cfquery>
																	<cfquery name="local.qryConnectedBlogsAndImageConfigInfo" dbtype="query">
																		select blogName
																		from local.dataStruct.qryAllConnectedBlogsAndImageConfigInfo
																		where featureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.relatedFeatureImageConfigID#">
																		order by blogName;
																	</cfquery>															
																	<div style="margin-top:10px;">
																		<label>#valueList(local.qryConnectedBlogsAndImageConfigInfo.blogName," / ")#</label>
																		<div class="mcblogentry_ftdimage" style="margin:5px;">
																			<cfloop query="local.qryRelatedFeaturedImageConfigSizes">
																				<div style="float:left;margin:2px;">
																					<div style="text-align:center;height:100px;display:table-cell;vertical-align:bottom;">
																						<a href="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.qryRelatedFeaturedImageConfigSizes.featureImageSizeID#.#local.qryRelatedFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" target="_blank">
																							<img src="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.qryRelatedFeaturedImageConfigSizes.featureImageSizeID#-preview-small.#local.qryRelatedFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" class="mcblog_img">
																						</a>
																						<div class="fimg_info"><i><small>#local.qryRelatedFeaturedImageConfigSizes.featuredImageSizeCode#</small></i></div>
																						<div class="fimg_info"><i><small>#local.qryRelatedFeaturedImageConfigSizes.width#x#local.qryRelatedFeaturedImageConfigSizes.height#</small></i></div>
																					</div>
																				</div>
																			</cfloop>
																			<div style="clear:both;"></div>
																		</div>
																	</div>
																</cfloop>
															</div>
														</cfif>
													<cfelse>
														<label><div style="float:left; display:inline; margin-right:25px;">No image exists.</div></label>								
													</cfif>
												</div>
											</div>
										</cfif>

									</td>
								</tr>
							</tbody>
						</table>
						<table id="documentsWell" class="tsAppBodyText be-settings-well mc_entry_hide tableWell blogEntryHideWhileSaving" style="width:100%;margin-bottom:20px;">
							<tbody style="display:table-row-group;">
								<tr>
									<td style="padding:5px 10px;"><b>Add Documents:</b></td>
								</tr>
								<tr valign="top" class="">
									<td class="tsAppBodyText" width="85%" style="padding:5px 10px;">
										Files uploaded here will be available for you to link in your #local.instanceSettings.Singular#. Use the <i class="icon-copy"></i> Copy icon to copy the link to your clipboard.
										You can then paste the link as the URL hyperlink for text in your #local.instanceSettings.Singular# or paste it directly into your #local.instanceSettings.Singular#.<br/>

										<div id="BlogDocumentsFlexWrapper" style="margin-top:10px;">
											<div id="BlogDocumentsUppyWrapper">
												<div id="mcBEDocUploader"></div>
											</div>
											<div id="divEntryDocsHolder" class="blogEntryHideWhileSaving" style="display:none;">
												<table class="table" style="width:100%;">
													<tbody id="tbodyDocs">
														<tr>
															<th>Document</th>
															<th></th>
															<th></th>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</cfif>
			</cfif>
		</cfif>
		<cfif len(trim(local.instanceSettings.addEntryFormAfterContent))>
			<div class="blogEntryHideWhileSaving" style="margin-top:10px;">
				#local.instanceSettings.addEntryFormAfterContent#
			</div>
		</cfif>
		<cfif local.instanceSettings.NameDesc neq "Wall">
			<div class="blogEntryHideWhileSaving">
				<button class="tsAppBodyButton" name="btnSaveBlogEntry" id="btnSaveBlogEntry" type="button" onclick="validateAndSaveEntryDetails();">Save & Review</button> 
				<button class="tsAppBodyButton" id="cancelButton" type="button" onClick="<cfif attributes.data.blAction EQ "addEntry">self.location.href='/?#local.baseQueryString#<cfelse>self.location.href='/?#local.baseQueryString#&blAction=showEntry&blogEntry=#local.dataStruct.getDocData.blogEntryID#</cfif>';">Cancel</button>
			</div>
		</cfif>
	</cfform>

	<script id="mc_blogEntryFieldTemplate" type="text/x-handlebars-template">
		<div class="MCBlogEntryFieldsWrapper">
			{{##each arrPostTypeFields}}
				{{##compare fieldGroupingID '>' 0}}
					<div>
						<fieldset style="padding:10px;border:1px solid ##ccc;margin:10px 0;">
						<legend style="width:auto;font-size:1.1875em;font-weight:400;margin:0;padding:0 8px;border:0;line-height:20px;">{{fieldGrouping}}</legend>
						{{##compare fieldGroupingDesc.length '>' 0}}
							<div style="margin-bottom:15px;">{{fieldGroupingDesc}}</div>
						{{/compare}}
				{{/compare}}

				{{##each arrFields}}
					{{##unless allOptionEmptyOrDisabled}}
						{{##compare itemID '>' 0}}
							<input type="hidden" name="old_cf_{{fieldID}}" value="{{value}}">
						{{/compare}}

						<div style="padding:4px 0;">
							{{##compare displayTypeCode '==' "LABEL"}}
								{{{attributes.fieldText}}}
							{{/compare}}
							{{##compare displayTypeCode '!=' "LABEL"}}
								{{##if isRequired}}* {{/if}}
								{{attributes.fieldText}}
							{{/compare}}

							<!--- textbox --->
							{{##compare displayTypeCode '==' "TEXTBOX"}}

								<div style="padding:6px 0 6px 20px;">

									{{##if supportQty}}
										
										{{##unless maxQtyAllowed}}
											<input type="hidden" name="cf_{{fieldID}}" value="{{value}}">
										{{/unless}}

										Quantity: <input type="text" size="6" autocomplete="off" maxlength="4" id="{{##unless maxQtyAllowed}}disabled_{{/unless}}cf_{{fieldID}}" name="{{##unless maxQtyAllowed}}disabled_{{/unless}}cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldOfferQTY="{{supportQty}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldMaxQtyAllowed="{{maxQtyAllowed}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" value="{{value}}" {{##unless maxQtyAllowed}}disabled{{/unless}} class="MCBlogEntryField">
										{{##unless maxQtyAllowed}}
											&nbsp; <span class="tsAppBodyTextImportant">[SOLD OUT]</span>
										{{/unless}}

									{{else}}
										<input type="text" size="60" autocomplete="off" id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" value="{{value}}" style="width:600px;" class="MCBlogEntryField">
									{{/if}}

								</div>
							{{/compare}}

							<!--- Drop-Down List --->
							{{##compare displayTypeCode '==' "SELECT"}}
								<div style="padding:6px 0 6px 20px;">
									<select id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" class="MCBlogEntryField">
										<option value=""></option>
										{{##each children}}

											<option value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}selected{{/compare}} {{##if unavailable}}disabled{{/if}}>
											{{attributes.fieldValue}}
											{{##if unavailable}}
												&nbsp; [SOLD OUT]
											{{/if}}
											</option>

										{{/each}}
									</select>
								</div>
							{{/compare}}

							<!--- Radio Controls --->
							{{##compare displayTypeCode '==' "RADIO"}}
								<div style="padding:6px 0 6px 20px;">
									{{##each children}}
										<input type="radio" id="cf_{{../fieldID}}" name="cf_{{../fieldID}}" value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}checked{{/compare}} {{##if unavailable}}disabled{{/if}} data-MCBlogEntryFieldDisplayTypeCode="{{../displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{../dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{../attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{../attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{../attributes.fieldText}}" class="MCBlogEntryField"> {{attributes.fieldValue}}
										{{##if unavailable}}
											&nbsp; [SOLD OUT]
										{{/if}}
										<br/>
									{{/each}}
								</div>
							{{/compare}}

							<!--- Checkboxes --->
							{{##compare displayTypeCode '==' "CHECKBOX"}}
								<div style="padding:6px 0 6px 20px;">
									{{##each children}}
										<input type="checkbox" id="cf_{{../fieldID}}" name="cf_{{../fieldID}}" value="{{attributes.valueID}}" {{##if unavailable}}disabled{{/if}} data-MCBlogEntryFieldDisplayTypeCode="{{../displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{../dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{../attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{../attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{../attributes.fieldText}}" {{##each ../value}}{{##compare this '==' ../attributes.valueID}}checked{{/compare}}{{/each}} class="MCBlogEntryField"> {{attributes.fieldValue}}
										{{##if unavailable}}
											&nbsp; [SOLD OUT]
										{{/if}}
										<br/>
									{{/each}}
								</div>
							{{/compare}}

							<!--- Date --->
							{{##compare displayTypeCode '==' "DATE"}}
								<div style="padding:6px 0 6px 20px;">
									<input type="text" size="16" autocomplete="off" id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="tsAppBodyText MCBlogEntryField MCAdminDateControl"> <a href="##" class="MCAdminDateControlClearLink" data-linkedDateControl="cf_{{fieldID}}">clear</a>
								</div>
							{{/compare}}

							<!--- Textarea --->
							{{##compare displayTypeCode '==' "TEXTAREA"}}
								<div style="padding:6px 0 6px 20px;">
									<cfoutput><textarea cols="62" rows="5" id="cf_{{fieldID}}" name="cf_{{fieldID}}" class="tsAppBodyText MCBlogEntryField" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}">{{value}}</textarea></cfoutput>
								</div>
							{{/compare}}
						</div>
					{{/unless}}
				{{/each}}

				{{##compare fieldGroupingID '>' 0}}
						</fieldset>
					</div>
				{{/compare}}
			{{/each}}
		</div>
	</script>
</cfoutput>