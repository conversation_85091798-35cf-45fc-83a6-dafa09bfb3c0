<cfscript>
	variables.applicationReservedURLParams 	= "issubmitted";
	local.customPage.baseURL				= "/?#getBaseQueryString(false)#";
	
	local.arrCustomFields = [];
	
	local.tmpField = { name="PayProfileCode", type="STRING", desc="Profile Code", value="ctlaAuthCIM" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="formNameDisplay", type="STRING", desc="formNameDisplay", value="CTLA Membership Application" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Staff Confirmation Email", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="memberEmailFrom", type="STRING", desc="Member Email From", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AutoRenewTitle", type="STRING", desc="Auto Renew Title", value="Auto-Renew Membership" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AutoRenewFieldText", type="STRING", desc="Display text beside selection box", value="I wish to Opt-Out of Auto-Renewals" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AutoRenewDescription", type="CONTENTOBJ", desc="Auto Renew Description", value="I give consent to CTLA to automatically charge my membership dues to the pay method on file on an annual basis.I understand that CTLA will inform and invoice me via email, 60 days prior to charging my payment method on file. I understand that if I choose to withdraw my auto renewal status, I will inform CTLA, in writing, 30 days prior to my annual renewal date." };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="ListAgreementTitle", type="STRING", desc="List Agreement Title", value="List Server Agreement" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="ListAgreementDescription", type="CONTENTOBJ", desc="List Agreement Description", value="Enter Sample Text Here" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="ListAgreementFieldText", type="CONTENTOBJ", desc="Display text beside selection box", value="I agree to the terms assigned to the list agreement." }; 
	arrayAppend(local.arrCustomFields, local.tmpField);	
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	
	//SET PAGE DEFAULTS:
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
		formName='frmJoin',
		formNameDisplay = local.strPageFields.formNameDisplay,
		orgEmailTo = local.strPageFields.StaffConfirmationEmail,
		memberEmailFrom = local.strPageFields.memberEmailFrom
	));
	
	//GATEWAY INFORMATION: 
	local.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=local.siteID, profileCode=local.strPageFields.PayProfileCode);
	local.profile_1._description 	= '#local.organization# - #local.formNameDisplay#';
	
	local.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=local.formName);
	
	//Custom data:
	local.USStates 					= application.objCustomPageUtils.mem_getStatesByCountry('United States');
	local.nameOfHiddenField 		= "memid" & hash(dateformat(now(),'yyyymmdd'));
</cfscript>

<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID="A4D4401F-A454-4734-B469-425B69B1EC99")>
<cfset local.hasSub = false>
<cfif event.getValue('msg','') neq 2 and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
	<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
</cfif>

<cfscript>
	/*List of full ratescheduleUID*/
	local.sectionSubscriptionUID = '586cc33c-42e3-42c0-9943-18eb23c0c95c';
	local.attorneyFeesRateScheduleID = '1f4de6c5-b8b8-4ad1-9aa1-15a9ddc5fc7b';
	local.legalStaffFeesRateScheduleID = '4ab95f84-7875-4b0f-b37c-6997dc6ca341';
	local.lawStudentFeesRateScheduleID = '3d4dc62f-f00d-48f7-8261-f96ab40d6f33';
	local.areaOfPracticeFeesRateScheduleID = '1affcceb-9b3e-448f-93ca-e13dc632737e';
	local.rateScheduleIDList = local.attorneyFeesRateScheduleID & ',' & local.legalStaffFeesRateScheduleID & ',' & local.lawStudentFeesRateScheduleID & ',' & local.areaOfPracticeFeesRateScheduleID;	
	local.02yRateUID = 'c9f994a3-47b0-4481-9c07-8f6b7125e909';
	local.35yRateUID = '5265235d-9eb8-4e31-b0fa-1b6b68879380';
	local.610yRateUID = 'e186a761-4f87-4bb6-8100-5dd3d5747967';
	local.1115yRateUID = '624e340e-93c9-4855-98a4-7d0b67dfaac4';
	local.1620yRateUID = '587e19de-ef5e-45b1-b2ab-5902806c2498';
	local.21PyRateUID = 'c6e9d76e-44f2-4fed-ae8a-16bbf72281e0';
	local.legalStaffRateUID = '79916253-ecdd-4de2-8ea7-2ee9bd150908';
	local.studentFeeRateUID = 'c9e148ad-2d6d-485f-b4c4-6aceb38b49b4';
	local.areaOfPracticeFeeRateUID = '222f1f33-1590-4a57-9107-3595a5f1def0';
	local.02yRate = 0;
	local.35yRate = 0;
	local.610yRate = 0;
	local.1115yRate = 0;
	local.1620yRate = 0;
	local.21PyRate = 0;
	local.legalStaffRate = 0;
	local.studentFeeRate = 0;
	local.areaOfPracticeFeeRate = 0;
	local.rateUIDList = local.02yRateUID & ',' & local.35yRateUID & ',' & local.610yRateUID & ',' & local.1115yRateUID & ',' & local.1620yRateUID & ',' & local.21PyRateUID & ',' & local.legalStaffRateUID & ',' & local.studentFeeRateUID & ',' & local.areaOfPracticeFeeRateUID;
</cfscript>

<cfset local.qryRateData = application.objCustomPageUtils.sub_getRateSchedule(siteID=local.siteID, scheduleUID='#local.rateScheduleIDList#', rateUID ='#local.rateUIDList#', activeRatesOnly=true, ignoreRenewalRates=true, isAllowFrontEndOnly=true, isFrequencyFull=true) />

<cfloop query="local.qryRateData">
	<cfif local.qryRateData.rateUID EQ local.02yRateUID>
		<cfset local.02yRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.35yRateUID>
		<cfset local.35yRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.610yRateUID>
		<cfset local.610yRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.1115yRateUID>
		<cfset local.1115yRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.1620yRateUID>
		<cfset local.1620yRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.21PyRateUID>
		<cfset local.21PyRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.legalStaffRateUID>
		<cfset local.legalStaffRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.studentFeeRateUID>
		<cfset local.studentFeeRate = local.qryRateData.RATEAMT/>
	<cfelseif local.qryRateData.rateUID EQ local.areaOfPracticeFeeRateUID>
		<cfset local.areaOfPracticeFeeRate = local.qryRateData.RATEAMT/>
	</cfif>
</cfloop>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##03608B; border-bottom:1px solid ##03608B; }
			.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
			.CPSection{ border:1px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:18pt; font-weight:bold; color:##fff; padding:10px; background:##244966; }
			.CPSectionContent { padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ccc; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			.tsAppBodyText { color:##000;}
			select.tsAppBodyText{color:##666;}
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			##date_graduation,##date_of_birth { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
			.membershipAgreementWrap{display:none;}
			##captchaWrap input{
				height: 30px !important;
			}
		</style>
	</cfsavecontent>

	#local.pageCSS#
	#local.pageJS#
	
	<div id="customPage">
		<div class="TitleText PB">#local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: --->
			<cfcase value="0">
				
				<cfif event.getValue('msg',0) EQ "2">		
					<!--- Renewal form is not Open --->
					<div class="bodyText" >
						It looks like you might already be a member! Thank you and please contact CTLA at 303.831.1192 or <a href="mailto:<EMAIL>"><EMAIL></a> for information about your membership or renewing.
					</div>
				<cfelse>
				
				<cfif local.hasSub eq true>
					<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no" />
				</cfif>
				
				<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

				<cfif local.qryOrgMemberFields.usePrefixList is 1>
					<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
				</cfif>
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAoP">
					select sub.subscriptionname, sub.uid
						from membercentral.dbo.sites s 
						inner join membercentral.dbo.sub_types t on t.siteID = s.siteID and s.siteID = #local.siteID#
						inner join membercentral.dbo.sub_subscriptions sub on sub.typeID = t.typeID and t.typeName in ('Areas of Practice')	and 
						lower(sub.subscriptionname) not in ('areas of practice')						
					order by subscriptionname
				</cfquery>
				<cfset local.qryPartyAffil = application.objCustomPageUtils.getCustomFieldData(orgID=local.orgID,columnName='Party Affiliation')>
				<cfset local.qryGender = application.objCustomPageUtils.getCustomFieldData(orgID=local.orgID,columnName='Gender')>
				<cfset local.qryEthnicity = application.objCustomPageUtils.getCustomFieldData(orgID=local.orgID,columnName='Ethnicity')>
				
				<cfset local.qryStates = application.objCommon.getStates()>
			
				<cfsavecontent variable="local.js">
					<script type="text/javascript" src="/javascript/global.js"></script>
					
					<script type="text/javascript">
						function _FB_hasValue(obj, obj_type) {
							if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
							else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
							else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
							else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
							else{ return true; }
						}
						
						function _FB_validateForm() {
							var theForm = document.forms["#local.formName#"];
							var proType = "";
							var proRate = "";
							var optDCExclude = "";
							
							for (var i=0; i < theForm.proRate.length; i++) {
								if (theForm.proRate[i].checked) {
									proRate = theForm.proRate[i].value;
								}
							}
							
							var arrReq = new Array();
							
								if (!_FB_hasValue(theForm['firstname'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your First Name.';
								if (!_FB_hasValue(theForm['lastname'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Last Name.';
								if (!_FB_hasValue(theForm['maddress'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Office Address.';
								if (!_FB_hasValue(theForm['mcity'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your City.';
								if (!_FB_hasValue(theForm['mstateID'], 'SELECT')) arrReq[arrReq.length] 	= 'Please provide your State.';
								if (!_FB_hasValue(theForm['mzip'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Zip Code.';
								if (!_FB_hasValue(theForm['mphone'], 'TEXT')) arrReq[arrReq.length] 			= 'Please provide your Office Phone number.';
								if (!_FB_hasValue(theForm['hcell'], 'TEXT')) arrReq[arrReq.length] 			= 'Please provide your Mobile Phone number.';
								if (!_FB_hasValue(theForm['email'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Email Address.';
								
								if (!_FB_hasValue(theForm['proRate'], 'RADIO')) arrReq[arrReq.length] 		= 'Please select a Level of Membership.';

								if (proRate == "0-2Yrs" || proRate == "3-5Yrs" || proRate == "6-10Yrs" || proRate == "11-15Yrs" || proRate == "16-20Yrs" || proRate == "21+Yrs" ) {
									if (!_FB_hasValue(theForm['PracticeMonth'], 'SELECT')) arrReq[arrReq.length]		= 'Please provide Practice Month.';
									if (!_FB_hasValue(theForm['PracticeYear'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Practice Year.';
									if (!_FB_hasValue(theForm['affidavitName'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Full Name.';
									if (!_FB_hasValue(theForm['affidavitDate'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Date.';
									if (!_FB_hasValue(theForm['lawSchool'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Law School.';
								}
								if (proRate == "LS") {
									if (!_FB_hasValue(theForm['affidavitNameLS'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Full Name.';
									if (!_FB_hasValue(theForm['affidavitDateLS'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Date.';
								}
								if (proRate == "STU") {
									if (!_FB_hasValue(theForm['date_graduation'], 'TEXT')) arrReq[arrReq.length] 	= 'Please enter a valid Expected Graduation Date.';
									if (!_FB_hasValue(theForm['affidavitNameStudent'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Full Name.';
									if (!_FB_hasValue(theForm['affidavitDateStudent'], 'TEXT')) arrReq[arrReq.length] 				= 'Please provide your Date.';
									if (!_FB_hasValue(theForm['lawSchool'], 'TEXT')) arrReq[arrReq.length] 		= 'Please provide your Law School.';
								}

								var showEagle = "";
								
								for (var i=0; i < theForm.becomeEagle.length; i++) {
									if (theForm.becomeEagle[i].checked) {
										showEagle = theForm.becomeEagle[i].value;
									}
								}
								
								if (showEagle == 'Yes') {
									if (!_FB_hasValue(theForm['eagleRate'], 'RADIO')) {
										arrReq[arrReq.length] = 'Please select a EAGLE Donor Giving Level.';
									} else {
										if (!_FB_hasValue(theForm['pledgeAmount'], 'TEXT')) { 
											arrReq[arrReq.length] = 'Please provide your Pledge Amount.'; 
										} else {
											/* check ranges */
											var pledgeLevel = "";
											for (var i=0; i < theForm.eagleRate.length; i++) {
												if (theForm.eagleRate[i].checked) {
													pledgeLevel = theForm.eagleRate[i].value;
												}
											}
											var pledgeAmount = theForm['pledgeAmount'].value;
											var number = Number(pledgeAmount.replace(/[^0-9\.]+/g,""));
											
											if (pledgeLevel == "Legacy" && number < 50000) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Founder" && !(number >= 25000  && number <= 49999)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Champion" && !(number >= 15000 && number <= 24999)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Benefactor" && !(number >= 10000 && number <= 14999)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Defender" && !(number >= 7500 && number <= 9999)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Patron" && !(number >= 5000 && number <= 7499)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Advocate" && !(number >= 3750 && number <= 4999)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Sponsor" && !(number >= 2500 && number <= 3749)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Associate" && !(number >= 1500 && number <= 2499)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "NewAdvocate" && !(number >= 500 && number <= 1499)) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
											if (pledgeLevel == "Contributor" && number > 2499 ) {
												arrReq[arrReq.length] = 'Please provide your Pledge Amount in the level you have selected.'; 
											}
										}
									}																
								}								
								
								if (arrReq.length > 0) {
										var msg = 'The following fields are required:\n\n';
										for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
										alert(msg);
										return false;
									}
							
							return true;
							
						}
						
						function getSelectedRadio(buttonGroup) {
							if (buttonGroup[0]) {
								for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
							} else { if (buttonGroup.checked) return 0; }
							return -1;
						}
						
						function showAffidavit() {
							var theForm = document.forms["#local.formName#"];
							var aDiv = document.getElementById('divAttorneyAffidavit');
							var lDiv = document.getElementById('divLegalStaffAffidavit');
							var sDiv = document.getElementById('divStudentAffidavit');
							var aqDiv = document.getElementById('divAttorneyQuestions');
							var lqDiv = document.getElementById('divLegalStaffQuestions');
							var lADiv = document.getElementById('ListAgreementId');
							$('.membershipAgreementWrap').show();
							
							var proRate = "";
							
							for (var i=0; i < theForm.proRate.length; i++) {
								if (theForm.proRate[i].checked) {
									proRate = theForm.proRate[i].value;
								}
							}
							
							if (proRate == 'LS') {
								aDiv.style.display = 'none';
								aqDiv.style.display = 'none';
								lDiv.style.display = 'block';
								lqDiv.style.display = 'block';
								sDiv.style.display = 'none';
								lADiv.style.display = 'none';
							} else if (proRate == 'STU') {
								aDiv.style.display = 'none';
								aqDiv.style.display = 'none';
								lDiv.style.display = 'none';
								lqDiv.style.display = 'none';
								lADiv.style.display = 'none';
								sDiv.style.display = 'block';
							} else {
								aDiv.style.display = 'block';
								aqDiv.style.display = 'block';
								lDiv.style.display = 'none';
								lqDiv.style.display = 'none';
								sDiv.style.display = 'none';
								lADiv.style.display = 'block';
							}
						}
						
						function showEagle() {
							var theForm = document.forms["#local.formName#"];
							var aDiv = document.getElementById('divEagle');

							var showEagle = "";
							
							for (var i=0; i < theForm.becomeEagle.length; i++) {
								if (theForm.becomeEagle[i].checked) {
									showEagle = theForm.becomeEagle[i].value;
								}
							}
							
							if (showEagle == 'No') {
								aDiv.style.display = 'none';
							} else {
								aDiv.style.display = 'block';
							}
						}
						
						function areasOfPracticeTotal(x) {
							var areasOfPractice = $('##practiceAreas');
							var areasTotalSpan	= $('##areasOfPracticeTotal');
							var areasTotal 			= 0;
							
							if ( areasOfPractice.val().length >= 1 ) {
								var areasTotal = ( areasOfPractice.val().length - 1 ) * #numberFormat(local.areaOfPracticeFeeRate)#;
								var areasTotalText = '$' + areasTotal + '.00';
								areasTotalSpan.text(areasTotalText);
							}
							
						}
											
						function checkCaptchaAndValidate(){
							var thisForm = document.forms["#local.formName#"];
							var status = false;
							var captcha_callback = function(captcha_response){
								if (captcha_response.response && captcha_response.response != 'success') {
									status = false;
								} else {
									status = true;
								}
							}
							if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							} else {
								#local.captchaDetails.jsvalidationcode#
							}
							if(status){
								return _FB_validateForm();
							} else {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							}
						}
						$(document).ready(function(){
							mca_setupDatePickerField('date_graduation');
							mca_setupDatePickerField('date_of_birth');
							if($('input[name="becomeEagle"]:checked').length == 0){
								$("##becomeEagleYes").prop("checked", true);
								showEagle();
							}
							showCaptcha();
							
						});
			
					</script>
				</cfsavecontent>
				<cfhtmlhead text="#local.js#">
					<div class="form">
			
						<div class="r i frmText">*Denotes required field</div>
						<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return checkCaptchaAndValidate();">
							<input type="hidden" name="isSubmitted" value="1" />
							<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="" />
							<input type="checkbox" name="iAgree" id="iAgree" value="1" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
							<div class="page">					
								<div class="CPSection">
									<div class="CPSectionTitle BB">Applicant Information</div>
									<div class="tsAppBodyText subCPSectionArea1"></div>
									<div class="tsAppBodyText frmRow1 frmText">	
									<table cellspacing="0" cellpadding="2" border="0">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<cfif local.qryOrgMemberFields.hasPrefix is 1>
											<td class="tsAppBodyText questionText" width="65">Prefix:</td>
											</cfif>
											<td class="tsAppBodyText questionText" width="150">* First Name:</td>
											<cfif local.qryOrgMemberFields.hasMiddleName is 1>
											<td class="tsAppBodyText questionText" width="150">Middle Name:</td>
											</cfif>
											<td class="tsAppBodyText questionText" width="150">* Last Name:</td>
											<cfif local.qryOrgMemberFields.hasSuffix is 1>
											<td class="tsAppBodyText questionText" >Suffix:</td>
											</cfif>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline">
											<cfif local.qryOrgMemberFields.hasPrefix is 1>
												<cfif local.qryOrgMemberFields.usePrefixList is 1>
													<cfselect class="tsAppBodyText" id="prefix" name="prefix" query="local.qryOrgPrefixes" value="prefix" display="prefix" selected="" queryPosition="below">
													<option value=""></option>
													</cfselect>
												<cfelse>
													<cfinput value="" class="tsAppBodyText largeBox" name="prefix"  id="prefix" type="text" size="5" />
												</cfif>
											</cfif>
											</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="firstname"  id="firstname" type="text" /></td>
											<cfif local.qryOrgMemberFields.hasMiddleName is 1>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="middlename"  id="middlename" type="text" /></td>
											</cfif>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="lastname"  id="lastname" type="text" /></td>
											<cfif local.qryOrgMemberFields.hasSuffix is 1>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="suffix"  id="suffix" type="text" size="5" /></td>
											</cfif>
										</tr>
										<tr><td coslpan="5">&nbsp;</tr>
									</table>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" colspan="4">Firm/Business Name:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="firm"  id="firm" type="text" size="100" /></td>
										</tr>
										<tr><td coslpan="5">&nbsp;</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" colspan="4">* Mailing Address:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="maddress"  id="maddress" type="text" size="100" message="" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="maddress2"  id="maddress2" type="text" size="100" message="" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" >* City:</td>
											<td class="tsAppBodyText questionText" >* State:</td>
											<td class="tsAppBodyText questionText" >* Zip:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="mcity"  id="mcity" type="text" /></td>
											<td class="tsAppBodyText optionsInline">
												<cfselect name="mstateID"  id="mstateID" class="tsAppBodyText">
												<option value=""></option>
													<cfset local.currentCountryID = 0>
													<cfloop query="local.qryStates">
														<cfif local.qryStates.countryID neq local.currentCountryID>
															<cfset local.currentCountryID = local.qryStates.countryID>
															<optgroup label="#local.qryStates.country#">
														</cfif>
														<cfoutput>
															<option value="#local.qryStates.stateID#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
														</cfoutput>
														<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
															</optgroup>
														</cfif>
													</cfloop>
												</cfselect>
											</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="mzip"  id="mzip" type="text" /></td>
										</tr>
									</table>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" >* Office Phone:</td>
											<td class="tsAppBodyText questionText" >* Mobile Phone:</td>
											<td class="tsAppBodyText questionText" >Office Fax:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" validate="telephone" message = "Enter telephone number, formatted xxx-xxx-xxxx (e.g. ************)" class="tsAppBodyText largeBox" name="mphone"  id="mphone" type="text" /></td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="hcell"  id="hcell" type="text" /></td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" validate="telephone" message = "Enter telephone number, formatted xxx-xxx-xxxx (e.g. ************)" class="tsAppBodyText largeBox" name="mfax"  id="mfax" type="text" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" colspan="3">* Email:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value=""  size="50" class="tsAppBodyText largeBox" name="email"  id="email" type="text" validate="regular_expression" pattern="#application.regEx.email#" message="Please provide a valid Email address." /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" colspan="3" >Website:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="3"><cfinput value="" class="tsAppBodyText largeBox" name="website"  id="website" type="text" size="50" validate="regular_expression" pattern="#application.regEx.url#" message="Please enter a valid website address." /></td>
										</tr>
										<tr><td coslpan="5">&nbsp;</tr>
									</table>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr><td coslpan="5">&nbsp;</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" colspan="4">Home Address:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="haddress"  id="haddress" type="text" size="100" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline" colspan="4"><cfinput value="" class="tsAppBodyText largeBox" name="haddress2"  id="haddress2" type="text" size="100" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" >City:</td>
											<td class="tsAppBodyText questionText" >State:</td>
											<td class="tsAppBodyText questionText" >Zip:</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="hcity"  id="hcity" type="text" /></td>
											<td class="tsAppBodyText optionsInline">
												<cfselect name="hstateID"  id="hstateID" class="tsAppBodyText">
												<option value=""></option>
													<cfset local.currentCountryID = 0>
													<cfloop query="local.qryStates">
														<cfif local.qryStates.countryID neq local.currentCountryID>
															<cfset local.currentCountryID = local.qryStates.countryID>
															<optgroup label="#local.qryStates.country#">
														</cfif>
														<cfoutput>
															<option value="#local.qryStates.stateID#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
														</cfoutput>
														<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
															</optgroup>
														</cfif>
													</cfloop>
												</cfselect>
											</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="hzip"  id="hzip" type="text" /></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" >Home Phone:</td>
											<td class="tsAppBodyText questionText" ></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="hphone"  id="hphone" type="text" /></td>
											<td class="tsAppBodyText optionsInline"></td>
										</tr>
										<tr><td coslpan="5">&nbsp;</tr>
									</table>
									</div>
								</div>
								<br />
								<div class="CPSection">
									<div class="CPSectionTitle BB">Additional Information</div>
									<div class="tsAppBodyText subCPSectionArea1"></div>
									<div class="tsAppBodyText frmRow1 frmText">
									<table cellspacing="0" cellpadding="2" border="0" style="margin-top:7px;"> 
										<tr valign="top">
											<td class="tsAppBodyText questionText">Who/What encouraged you to join CTLA?</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="whyJoined"  id="whyJoined" type="text"></td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText">* Law School (Not required for Legal Staff)</td>
											<td class="tsAppBodyText optionsInline"><cfinput value="" class="tsAppBodyText largeBox" name="lawSchool"  id="lawSchool" type="text"></td>
										</tr>										
										<tr valign="top">
											<td class="tsAppBodyText questionText">Expected Graduation Date (Law Students Only):</td>
											<td class="tsAppBodyText">
												<cfinput value="" class="tsAppBodyText largeBox" name="date_graduation" id="date_graduation" type="text" validate="date" message="Please enter a valid Expected Graduation Date." />	
												<a href="javascript:mca_clearDateRangeField('date_graduation');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin-bottom:-1px;"></i></a>
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText">Date of Birth:</td>
											<td class="tsAppBodyText">
												<cfinput value="" class="tsAppBodyText largeBox" name="date_of_birth" id="date_of_birth" type="text" validate="date" message="Please enter a valid Expected Graduation Date." />	
												<a href="javascript:mca_clearDateRangeField('date_of_birth');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin-bottom:-1px;"></i></a>
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText">Party Affiliation</td>
											<td class="tsAppBodyText optionsInline">
												<select name="politAffil">
													<option value="0"> - Please Select - </option>
													<cfloop query="local.qryPartyAffil">
														<option value="#local.qryPartyAffil.valueID#">#local.qryPartyAffil.columnValueString#</option>
													</cfloop>
												</select>							
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText">Gender</td>
											<td class="tsAppBodyText optionsInline">
												<cfselect name="gender"  id="gender" class="tsAppBodyText">
													<option value="0"> - Please Select - </option>
													<cfloop query="local.qryGender">
														<option value="#local.qryGender.valueID#">#local.qryGender.columnValueString#</option>
													</cfloop>
												</cfselect>							
											</td>
										</tr>
										<tr valign="top">
											<td class="tsAppBodyText questionText">Ethnicity</td>
											<td class="tsAppBodyText optionsInline">
												<cfselect name="ethnicity"  id="ethnicity" class="tsAppBodyText">
													<option value="0"> - Please Select - </option>
													<cfloop query="local.qryEthnicity">
														<option value="#local.qryEthnicity.valueID#">#local.qryEthnicity.columnValueString#</option>
													</cfloop>
												</cfselect>						
											</td>
										</tr>
									</table>
									</div>
								</div>
								<br/>
								<br/>
								<div class="CPSection">
									<div class="CPSectionTitle BB">Areas of Practice</div>
									<div class="tsAppBodyText frmRow1 frmText">								
									<div class="tsAppBodyText" style="font-size:10pt;font-weight:bold;padding:10px;">You may select multiple areas of practice by pressing the Ctrl key.  Your 1st area of practice is free and each additional is $#numberFormat(local.areaOfPracticeFeeRate)# per year.</div>
										<br>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfselect name="practiceAreas" id="practiceAreas" class="tsAppBodyText" multiple="true" size="10" style="width:500px;" onChange="areasOfPracticeTotal(this);">
														<cfloop query="local.qryAoP">
															<cfoutput>
																<option value="#local.qryAoP.subscriptionname#">#local.qryAoP.subscriptionname#</option>
															</cfoutput>
														</cfloop>
													</cfselect>
												</td>
											</tr>
										</table>
										<div class="tsAppBodyText" style="font-size:10pt;padding:10px;"><strong>Total Due for Areas of Practice:&nbsp;&nbsp;</strong><span id="areasOfPracticeTotal">$0.00</span></div>
										<br/>
									</div>
								</div>
								<br/>
								<br/>
								<div id="divRates" class="CPSection">
									<div class="CPSectionTitle BB">Membership Information</div>
									<div class="tsAppBodyText subCPSectionArea1">Annual membership pricing is as follows. Attorney dues are based on the number of years in practice: </div>
									<div id="divRegRates" >
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline" colspan="4">
													<b>Regular Member Fees *</b>
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="0-2Yrs" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.02yRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													0-2 Years (from Bar Date)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="3-5Yrs" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.35yRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													3-5 Years (from Bar Date)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="6-10Yrs" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.610yRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													6-10 Years (from Bar Date)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="11-15Yrs" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.1115yRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													11-15 Years (from Bar Date)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="16-20Yrs" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.1620yRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													16-20 Years (from Bar Date)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="21+Yrs" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.21PyRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													21+ Years (from Bar Date)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="LS" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.legalStaffRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Legal Staff
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="proRate" id="proRate" value="STU" onclick="showAffidavit()" /> 
												</td>
												<td class="tsAppBodyText optionsInline" align="right">
													$#numberFormat(local.studentFeeRate)#
												</td>
												<td>&nbsp;</td>
												<td class="tsAppBodyText optionsInline">
													Student Fee
												</td>
											</tr>
										</table>
										<div id="divAttorneyQuestions" style="padding-left:15px; padding-right:15px;display:none;">
											<br/>
											<table cellspacing="0" cellpadding="2" border="0">
												<tr valign="middle">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText optionsInline" colspan="4">
														<b>Additional questions</b>
													</td>
												</tr>
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Month/Year Admitted to Practice :</td>
													<td class="tsAppBodyText optionsInline" colspan="2">
														<cfselect required="true" name="PracticeMonth" id="PracticeMonth" >
															<option value="">-- Select Month</option>
															<option value="1">January</option>
															<option value="2">February</option>
															<option value="3">March</option>
															<option value="4">April</option>
															<option value="5">May</option>
															<option value="6">June</option>
															<option value="7">July</option>
															<option value="8">August</option>
															<option value="9">September</option>
															<option value="10">October</option>
															<option value="11">November</option>
															<option value="12">December</option>
														</cfselect>/<cfinput value="" class="tsAppBodyText largeBox" name="PracticeYear"  id="PracticeYear" type="text" size=4 maxlength="4">
													</td>
												</tr>
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" colspan="2"><label for="memberNonProfit"><cfinput value="1" class="tsAppBodyText largeBox" name="memberNonProfit"  id="memberNonProfit" type="radio"> &nbsp; Click here if you are a member of a non-profit</label></td>													
												</tr>
											</table>
										</div>
										<div id="divLegalStaffQuestions" style="display:none;">
											<br/>
											<table cellspacing="0" cellpadding="2" border="0">
												<tr valign="middle">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText optionsInline" colspan="3">
														<b>Additional questions</b>
													</td>
												</tr>
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">Name of CTLA Attorney/Member Sponsor:</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="sponsor"  id="sponsor" type="text" size=40></td>
												</tr>
											</table>
										</div>
										
										<br/>
										<div class="tsAppBodyText" style="padding-left:15px; padding-right:15px;">
											<b>Please note:</b> This payment is not tax-deductible as a charitable contribution; payment may be deductible as a business expense.
										</div>
										<br/>													
									</div>
								</div>
					
								<br/>
								<br/>
								<div  class="CPSection membershipAgreementWrap">
									<div class="CPSectionTitle BB">Membership Agreement</div>
									<div id="divRegRates" >
													
										<div id="divAttorneyAffidavit" style="padding-left:15px; padding-right:15px;display:none;">
											<br/>

											<b>ARTICLE III - MEMBERSHIP</b><br/><br/>
											Section 1. Classes. There shall be the following classes of membership: <br/>
											<br/>
											(a) Regular Voting Members. Any person who is licensed to practice law before the Supreme Court of the State of Colorado or who is licensed to practice law before the highest court of any state of the United States, and who, through his/her professional conduct, adheres to the purpose, mission, goals, and bylaws of the association and who meets any continuing legal education requirements which may, from time to time, be established by the Board of Directors, shall be eligible for regular membership in this association provided, however, that no person shall be eligible for, or continue membership in this association should more than one third of his or her individual practice concern the following areas, taken in combination: the representation of either insurance companies and/or mutual exchanges in any kind of litigation, including subrogation matters; the representation of insured defendants when compensated by insurance companies in personal injury matters; and/or the defense of institutions or corporations in personal injury litigation. His or her individual practice shall be measured by any one of the following parameters: percentage of time spent, percentage of gross receipts or percentage of clients represented during a calendar year. Furthermore, no person shall be eligible for, or continue membership in this association if he or she is eligible for, or is a member of, the Colorado Defense Lawyers Association.  As a condition of membership, new members, renewing members and former members who are rejoining shall sign a certificate confirming their meeting of the qualification for regular voting members. Invoices for dues renewals shall state the qualifications as a reminder to renewing members and include such a certificate. <br/><br/>
											I certify that I meet the membership qualifications of the Colorado Trial Lawyers Association. Furthermore, I agree to be bound by any disciplinary procedures then in effect should I violate this or any provision of the CTLA Bylaws or resolution the CTLA Board of Directors. By signing and dating below, I also agree to receive communication from CTLA by mail, fax or electronic/digital means.<br/>
											<br/>

											<table cellspacing="0" cellpadding="2" border="0">
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Full Name:</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="affidavitName"  id="affidavitName" type="text"></td>
												</tr>
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Date (xx/xx/xxxx):</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="affidavitDate"  id="affidavitDate" type="text"></td>
												</tr>
											</table>
											<br/>
										</div>
													
										<div id="divStudentAffidavit" style="padding-left:15px; padding-right:15px;display:none;">
											<br/>

											<b>ARTICLE III - MEMBERSHIP</b><br/><br/>
											Section 1. Classes. There shall be the following classes of membership:  <br/>
											<br/>
											1)  Student Member.  Any person who is currently enrolled as a student in an accredited law school and who adheres to the purpose, mission, goals and bylaws of the association may be admitted to this association as a student member.  A student member shall remain a member in good standing by continuing the study of law at an accredited law school, paying required dues, and continuing to adhere to the objectives of the association and to the qualifications for student membership. Student members shall enjoy the rights and privileges accorded them by the Board of Directors, but shall not have the right to vote or hold office. <br/><br/>
											I certify that I meet the membership qualifications of the Colorado Trial Lawyers Association. Furthermore, I agree to be bound by any disciplinary procedures then in effect should I violate this or any provision of the CTLA Bylaws or resolution the CTLA Board of Directors. By signing and dating below, I also agree to receive communication from CTLA by mail, fax or electronic/digital means. <br/>
											<br/>							

											<table cellspacing="0" cellpadding="2" border="0">
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Full Name:</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="affidavitNameStudent"  id="affidavitNameStudent" type="text"></td>
												</tr>
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Date (xx/xx/xxxx):</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="affidavitDateStudent"  id="affidavitDateStudent" type="text"></td>
												</tr>
											</table>
											<br/>
										</div>
															
										<div id="divLegalStaffAffidavit" style="padding-left:15px; padding-right:15px;display:none;">
											<br/>

											<b>ARTICLE III - MEMBERSHIP</b><br/><br/>
											Section 1. Classes. There shall be the following classes of membership:  <br/>
											<br/>
											(2)  LEGAL STAFF MEMBER.  Any person who is currently employed as legal staff by a member attorney(s) and who adheres to the purpose, mission, goals and bylaws of the association may be admitted to this association as a legal staff member upon sponsorship by the member attorney(s) who employs him or her. Such staff member shall not be eligible if he or she is also employed in another office by an attorney who is not eligible for CTLA membership.  A legal staff member shall remain a member in good standing by continuing in the employ of a member lawyer(s), paying required dues and continuing to adhere to the objectives of the association and to the qualifications for legal staff membership.  Legal staff members  shall enjoy the rights and privileges accorded them by the board of directors, but shall not have the right to vote or hold office. <br/><br/>
											I certify that I meet the membership qualifications of the Colorado Trial Lawyers Association. Furthermore, I agree to be bound by any disciplinary procedures then in effect should I violate this or any provision of the CTLA Bylaws or resolution the CTLA Board of Directors. By signing and dating below, I also agree to receive communication from CTLA by mail, fax or electronic/digital means. <br/>
											<br/>

											<table cellspacing="0" cellpadding="2" border="0">
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Full Name:</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="affidavitNameLS"  id="affidavitNameLS" type="text"></td>
												</tr>
												<tr valign="top">
													<td class="tsAppBodyText questionNumber"></td>
													<td class="tsAppBodyText questionText" align="right">* Date (xx/xx/xxxx):</td>
													<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="" class="tsAppBodyText largeBox" name="affidavitDateLS"  id="affidavitDateLS" type="text"></td>
												</tr>
											</table>
											<br/>
										</div>

									</div>
								</div>
								<div class="CPSection">
									<div class="CPSectionTitle BB">Optional Eagle Contribution</div>
									<div class="tsAppBodyText subCPSectionArea1">Making an EAGLE contribution is one of the most important things you can do for your practice and your clients right now.  The EAGLE program plays a vital role in the financial welfare of the Colorado Trial Lawyers Association and our efforts each year to protect and advance your interests. To learn more, <a href="/?pg=eagle" target="_blank">click here</a>.</div>
									<div class="tsAppBodyText frmRow1 frmText">								

									<br>
									<table cellspacing="0" cellpadding="2" border="0">
										<tr valign="top">
											<td class="tsAppBodyText questionNumber"></td>
											<td class="tsAppBodyText questionText" align="right">Would you like to become an Eagle Donor?</td>
											<td class="tsAppBodyText optionsInline" colspan="2" valign="top"><cfinput value="Yes" class="tsAppBodyText largeBox" name="becomeEagle"  id="becomeEagleYes" type="radio" onclick="showEagle()"> Yes  <cfinput value="No" class="tsAppBodyText largeBox" name="becomeEagle"  id="becomeEagleNo" type="radio"  onclick="showEagle()"> No</td>
										</tr>
									</table>
									<br/>
									
									<div id="divEagle" style="padding-left:5px; display:none;">
										The following are EAGLE Donor Giving Levels (please choose one):	<br/>

										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Legacy" value="Legacy" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Legacy: $50,000 and Above
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Founder" value="Founder" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Founder: $25,000 - $49,999
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Champion" value="Champion" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Champion: $15,000 - $24,999
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Benefactor" value="Benefactor" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Benefactor: $10,000 - $14,999
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Defender" value="Defender" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Defender: $7,500 - $9,999
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Patron" value="Patron" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Patron: $5,000 - $7,499
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Advocate" value="Advocate" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Advocate: $3,750 - $4,999
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Sponsor" value="Sponsor" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Sponsor: $2,500 - $3,749
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Associate" value="Associate" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Associate: $1,500 - $2,499 (in practice 10 years or less)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="NewAdvocate" value="NewAdvocate" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													New Advocate: $500 - $1,499 (in practice 5 years or less)
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="eagleRate" id="Contributor" value="Contributor" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Contributor: Up to $2,499
												</td>
											</tr>
										</table>
										<br/>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" align="right">* Pledge Amount:</td>
												<td class="tsAppBodyText optionsInline" colspan="2">
														<cfinput value="" class="tsAppBodyText largeBox" name="pledgeAmount"  id="pledgeAmount" type="text" size="10" maxlength="10">
												</td>
											</tr>
										</table>										
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" align="right">Would you like to make this an anonymous contribution? </td>
												<td class="tsAppBodyText optionsInline" colspan="2"><cfinput value="Yes" class="tsAppBodyText largeBox" name="isAnonymous"  id="isAnonymous" type="radio"> Yes  <cfinput value="No" class="tsAppBodyText largeBox" name="isAnonymous"  id="isAnonymous" type="radio"> No</td>
											</tr>
										</table>										
										<br/>
										<div class="tsAppBodyText subCPSectionArea1"><strong>Eagle Payment Options</strong></div>
										<br/>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" align="right">Payment Schedule:</td>
												<td class="tsAppBodyText optionsInline" colspan="2">
														<cfselect class="tsAppBodyText largeBox" name="paymentSchedule" id="paymentSchedule" >
															<option value="Monthly">Monthly</option>
															<option value="Quarterly">Quarterly</option>
															<option value="Semi-Annual">Semi-Annual</option>
															<option value="Annual"  selected="true">Annual</option>
														</cfselect>
												</td>
											</tr>
											<tr valign="top">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText questionText" align="right">Payment Method: </td>
												<td class="tsAppBodyText optionsInline" colspan="2">
														<cfinput value="Pay by Check" class="tsAppBodyText largeBox" name="paymentMethod"  id="paymentMethod" type="radio"> Pay by Check  <cfinput value="Pay by Credit Card" class="tsAppBodyText largeBox" name="paymentMethod"  id="paymentMethod" type="radio"> Pay by Credit Card 
												</td>
											</tr>
										</table>										
										<br/>
										Please indicate the type of funds you will be using to pay for this contribution:<br/>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="contributionType" id="contributionType" value="Individual/Personal" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Individual/Personal
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="contributionType" id="contributionType" value="Individual/Sole Proprietor" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Individual/Sole Proprietor
												</td>
											</tr>
											<tr valign="middle">
												<td class="tsAppBodyText questionNumber"></td>
												<td class="tsAppBodyText optionsInline">
													<cfinput type="radio" name="contributionType" id="contributionType" value="Firm" /> 
												</td>
												<td class="tsAppBodyText optionsInline">
													Firm (PC or LCC or Other)
												</td>
											</tr>
										</table>
										<br/>
										Special Instructions: <br/>
										<textarea name="specialInstructions" rows="4" style="width:95%"></textarea>
										<br/>
										<br/>
										<div class="tsAppBodyText" style="padding-left:15px; padding-right:15px;">
											<strong>Please note</strong>: Contributions or gifts to CTLA are not tax deductible as charitable contributions for income tax purposes. However, a portion may be tax deductible as ordinary and necessary business expenses subject to restrictions imposed as a result of association lobbying activities. You will be notified of the applicable percentages.
										</div>
										<br/>
																
									</div>									
									
									</div>
								</div>
								<br/>
			
								<br />
								<div class="CPSection" id="renewagreeId">
										<div class="CPSectionTitle BB row-fluid">		#local.strPageFields.AutoRenewTitle#
										</div>
										<div class="tsAppBodyText subCPSectionArea1">
											#local.strPageFields.AutoRenewDescription#
										</div>
										<div class="frmText row-fluid" style="padding:8px;">
											<div class="control-group" style="position:relative">
												<div class="controls">
													<label for="autoRenew" class="labelCheckbox">
														<input value="1" class="tsAppBodyText optionsRadio" name="autoRenew" id="autoRenew" type="checkbox" >&nbsp;&nbsp;#local.strPageFields.AutoRenewFieldText#
													</label>
												</div>
											</div>
										</div>
									</div>	
								<br/>			
								<br />
								<div class="CPSection" id="ListAgreementId" style="display:none;">
									<div class="CPSectionTitle BB row-fluid">
										#local.strPageFields.ListAgreementTitle#
									</div>
									<div class="tsAppBodyText subCPSectionArea1">
										#local.strPageFields.ListAgreementDescription#
									</div>
									<div class="frmText row-fluid" style="padding:8px;">
										<div class="control-group" style="position:relative">
											<div class="controls">
												<label for="agreeListAgreement" class="labelCheckbox">
													<input value="1" class="tsAppBodyText optionsRadio" name="agreeListAgreement" id="agreeListAgreement" type="checkbox" >&nbsp;&nbsp;#local.strPageFields.ListAgreementFieldText#
												</label>
											</div>
										</div>
									</div>
								</div>
								<div class="CPSection" id="captchaWrap" >
									
									<div class="row-fluid" style="padding:8px;">
										#local.captchaDetails.htmlContent#
									</div>
								</div>								
								<br/>			
								<br />
								<!--- BUTTONS:  --->					
								<div id="formButtons">
									<div style="padding:10px;">
										<div align="center" class="frmButtons">
											<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp;
										</div>
									</div>
								</div>
							</div>
						</cfform>
					</div>
				</cfif>
			</cfcase>
			
			<!--- PAYMENT INFO: --->
			<cfcase value="1">
				
				<cfif structKeyExists(event.getCollection(), 'iAgree') OR (NOT structKeyExists(session, "captchaEntered") AND (NOT Len(event.getValue('captcha',''))) OR application.objCustomPageUtils.validateCaptcha(code=event.getValue('captcha'),captcha=event.getValue('captcha_check')).response NEQ "success")>
					<cflocation url="#local.customPage.baseURL#&issubmitted=100" addtoken="no">
				</cfif>
				<cfif application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
					<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no">
				</cfif>

				<cfset local.contactType = ''>
				<cfswitch expression="#event.getValue('proRate')#">
					<cfcase value="0-2Yrs">
						<cfset local.contactType = 'Attorney'>						
					</cfcase>
					<cfcase value="3-5Yrs">
						<cfset local.contactType = 'Attorney'>						
					</cfcase>
					<cfcase value="6-10Yrs">
						<cfset local.contactType = 'Attorney'>						
					</cfcase>
					<cfcase value="11-15Yrs">
						<cfset local.contactType = 'Attorney'>						
					</cfcase>
					<cfcase value="16-20Yrs">
						<cfset local.contactType = 'Attorney'>						
					</cfcase>
					<cfcase value="21+Yrs">
						<cfset local.contactType = 'Attorney'>						
					</cfcase>
					<cfcase value="LS">
						<cfset local.contactType = 'Legal Staff'>						
					</cfcase>
					<cfcase value="STU">
						<cfset local.contactType = 'Student'>						
					</cfcase>
				</cfswitch>

				<cfif len(local.contactType) and arguments.event.getTrimValue('memberNonProfit','0') EQ 1>
					<cfset local.contactType = local.contactType & ',Non-Profit'>
				</cfif>
				
				<!--- CREATE MEMBER RECORD --->
				<cfscript>
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0);
					local.objSaveMember.setDemo(prefix=arguments.event.getTrimValue('prefix',''), firstName=arguments.event.getTrimValue('firstName',''), 
						middleName=arguments.event.getTrimValue('middleName',''), lastName=arguments.event.getTrimValue('lastName',''), 
						suffix=arguments.event.getTrimValue('suffix',''), company=arguments.event.getTrimValue('firm',''));
					local.objSaveMember.setMemberType(memberType='User');
					local.objSaveMember.setMemberStatus(memberStatus='Active');

					local.objSaveMember.setAddress(type='Office Address', address1=arguments.event.getTrimValue('maddress',''), 
						address2=arguments.event.getTrimValue('maddress2',''), city=arguments.event.getTrimValue('mcity',''), 
						stateID=arguments.event.getTrimValue('mstateID',0), postalCode=arguments.event.getTrimValue('mzip',''));
					local.objSaveMember.setPhone(addresstype='Office Address', type='Phone', value=arguments.event.getTrimValue('mphone',''));
					local.objSaveMember.setPhone(addresstype='Office Address', type='Fax', value=arguments.event.getTrimValue('mfax',''));
					local.objSaveMember.setPhone(addressType='Office Address', type='Mobile', value=arguments.event.getTrimValue('hcell',''));

					local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getTrimValue('haddress',''), 
						address2=arguments.event.getTrimValue('haddress2',''), city=arguments.event.getTrimValue('hcity',''), 
						stateID=arguments.event.getTrimValue('hstateID',0), postalCode=arguments.event.getTrimValue('hzip',''));

					local.objSaveMember.setEmail(type='Email', value=arguments.event.getTrimValue('email'));
					local.objSaveMember.setWebsite(type='Website', value=arguments.event.getTrimValue('website'));

					local.objSaveMember.setCustomField(field='Contact Type', value=local.contactType);
					local.objSaveMember.setCustomField(field='Date of Birth', value=arguments.event.getTrimValue('date_of_birth',''));
					
					if(val(arguments.event.getTrimValue('politAffil',0)) neq 0 ){
						local.objSaveMember.setCustomField(field='Party Affiliation', valueID=val(arguments.event.getTrimValue('politAffil',0)));
					}
					
					if(val(arguments.event.getTrimValue('ethnicity',0)) neq 0){
						local.objSaveMember.setCustomField(field='Ethnicity', valueID=val(arguments.event.getTrimValue('ethnicity',0)));
					}
					
					if(val(arguments.event.getTrimValue('gender',0)) neq 0 ){
						local.objSaveMember.setCustomField(field='Gender', valueID=val(arguments.event.getTrimValue('gender',0)));
					}
					
					local.objSaveMember.setCustomField(field='Law School', value=arguments.event.getTrimValue('lawSchool',''));
					
					if(len(arguments.event.getTrimValue('autoRenew',''))){
						local.objSaveMember.setCustomField(field='Auto-Renewal Opt-Out', value='Yes');
					}else{
						local.objSaveMember.setCustomField(field='Auto-Renewal Opt-Out', value='No');
					}
					
					if(len(arguments.event.getTrimValue('agreeListAgreement',''))){
						local.objSaveMember.setCustomField(field='List Serve Agreement', value='Yes');
					}else{
						local.objSaveMember.setCustomField(field='List Serve Agreement', value='No');
					}					

					local.licenseDate = '#arguments.event.getTrimValue('PracticeMonth','')#/1/#arguments.event.getTrimValue('PracticeYear','')#';
					if (isValid('date',local.licenseDate))
						local.objSaveMember.setProLicense(name='Colorado State Bar', status='Active', license='', date=local.licenseDate);

					local.strResult = local.objSaveMember.saveData();
					local.memberNumber = local.strResult.membernumber;
				</cfscript>
				
                <cfif NOT local.strResult.success>
                    <cfoutput>
                        <div class="tsAppBodyText">
                            <b>An error occurred while setting up your membership.  We are unable to process your membership online at this time.</b>
                            <br>
                            Please contact customer support for assistance.
                            <br><br>
                            We apologize for the inconvenience. 
                        </div>
                    </cfoutput>
                <cfelse>
                	<cfset local.useMID = local.strResult.memberID>
					
					<cfset local.politAffilValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=local.orgID,columnname='Party Affiliation',valueIDList="#event.getValue('politAffil','0')#")>
					<cfset local.genderValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=local.orgID,columnname='Gender',valueIDList="#event.getValue('gender','0')#")>
					<cfset local.ethnicityValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=local.orgID,columnname='Ethnicity',valueIDList="#event.getValue('ethnicity','0')#")>

                    <cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberLookups">
						set nocount on;

						declare @mStateName varchar(50), @hStateName varchar(50);

						select @mStateName=Name
						from dbo.ams_states 
						where stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(event.getValue('mstateID'))#">;

						select @hStateName=Name
						from dbo.ams_states 
						where stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(event.getValue('hstateID'))#">;
						
						select @mStateName as mStateName, @hStateName as hStateName;
                    </cfquery>

                    <cfsavecontent variable="local.invoice">
                        #local.pageCSS#
                        <!-- @msg@ -->
                        <!-- @profile_1.ccResponse@ -->
                        <p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>

                        <table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
                            <tr class="msgHeader"><td colspan="2" class="b">Member Information</td></tr>
                            <tr><td valign="top" class="docText">Name:</td><td valign="top" class="docText">#arguments.event.getTrimValue('prefix','')# #arguments.event.getTrimValue('firstname','')# #arguments.event.getTrimValue('middlename','')# #arguments.event.getTrimValue('lastname','')# #arguments.event.getTrimValue('suffix','')#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Firm / Business Name:</td><td valign="top" class="docText">#trim(event.getValue('firm',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Mailing Address:</td><td valign="top" class="docText">#trim(event.getValue('maddress',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Mailing Address2:</td><td valign="top" class="docText">#trim(event.getValue('maddress2',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">City:</td><td valign="top" class="docText">#trim(event.getValue('mcity',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">State:</td><td valign="top" class="docText">#local.qryMemberLookups.mStateName#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Zip:</td><td valign="top" class="docText">#trim(event.getValue('mzip',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Phone:</td><td valign="top" class="docText">#trim(event.getValue('mphone',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Fax:</td><td valign="top" class="docText">#trim(event.getValue('mfax',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Email:</td><td valign="top" class="docText">#trim(event.getValue('email',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Website:</td><td valign="top" class="docText">#trim(event.getValue('website',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Home Address:</td><td valign="top" class="docText">#trim(event.getValue('haddress',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Home Address2:</td><td valign="top" class="docText">#trim(event.getValue('haddress2',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">City:</td><td valign="top" class="docText">#trim(event.getValue('hcity',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">State:</td><td valign="top" class="docText">#local.qryMemberLookups.hStateName#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Zip:</td><td valign="top" class="docText">#trim(event.getValue('hzip',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Phone:</td><td valign="top" class="docText">#trim(event.getValue('hphone',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Mobile:</td><td valign="top" class="docText">#trim(event.getValue('hcell',''))#&nbsp;</td></tr>
                            <tr><td colspan="2" height="25"></td></tr>
                            <tr class="msgHeader"><td colspan="2" class="docText"><strong>Additional Information</strong></td></tr>
                            <tr><td valign="top" class="docText">Who/What encouraged you to join CTLA:</td><td valign="top" class="docText">#trim(event.getValue('whyJoined',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Law School:</td><td valign="top" class="docText">#trim(event.getValue('lawSchool',''))#&nbsp;</td></tr>
                            <tr><td valign="top" class="docText">Expected Graduation Date:</td><td valign="top" class="docText">#event.getValue('date_graduation','')#&nbsp;</td></tr> 							
							<tr><td valign="top" class="docText">Date of Birth:</td><td valign="top" class="docText">#event.getValue('date_of_birth','')#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Party Affiliation:</td><td valign="top" class="docText">#local.politAffilValue#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Gender:</td><td valign="top" class="docText">#local.genderValue#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Ethnicity:</td><td valign="top" class="docText">#local.ethnicityValue#&nbsp;</td></tr>
                            
                            <tr><td colspan="2" height="25"></td></tr>
                            <tr class="msgHeader"><td colspan="2" class="docText"><strong>Areas of Practice</strong></td></tr>
                            <tr>
                                <td valign="top" class="docText">Areas of Practice:</td>
                                <td valign="top" class="docText">
                                    <cfloop list="#event.getValue('practiceAreas','')#" index="local.thisAOP">
                                        #local.thisAOP#<br>
                                    </cfloop>&nbsp;
                                </td>
                            </tr>
                            <tr><td colspan="2" height="25"></td></tr>
                            <tr class="msgHeader"><td colspan="2" class="docText"><strong>Membership Information</strong></td></tr>
                            <tr>
                                <td valign="top" class="docText">Rate Selected:</td>
                                <td valign="top" class="docText">
                                    <cfswitch expression="#event.getValue('proRate')#">
                                        <cfcase value="0-2Yrs">
                                            $#numberFormat(local.02yRate)# 	  	0-2 Years (from Bar Date) 
                                        </cfcase>
                                        <cfcase value="3-5Yrs">
                                            $#numberFormat(local.35yRate)# 	  	3-5 Years (from Bar Date) 
                                        </cfcase>
										<cfcase value="6-10Yrs">
                                            $#numberFormat(local.610yRate)# 	6-10 Years (from Bar Date)
                                        </cfcase>
                                        <cfcase value="11-15Yrs">
                                            $#numberFormat(local.1115yRate)# 	11-15 Years (from Bar Date)
                                        </cfcase>
                                        <cfcase value="16-20Yrs">
                                            $#numberFormat(local.1620yRate)# 	16-20 Years (from Bar Date)
                                        </cfcase>
                                        <cfcase value="21+Yrs">
                                            $#numberFormat(local.21PyRate)# 	21+ Years (from Bar Date)
                                        </cfcase>
                                        <cfcase value="LS">
                                            $#numberFormat(local.legalStaffRate)# 	  	Legal Staff 
                                        </cfcase>
                                        <cfcase value="STU">
                                        	$#numberFormat(local.studentFeeRate)# 	  	Student Fee 
                                        </cfcase>
                                    </cfswitch>&nbsp;
                                </td>
                            </tr>
                    <cfswitch expression="#event.getValue('proRate')#">
                        <cfcase value="0-2Yrs;3-5Yrs;6-10Yrs;11-15Yrs;16-20Yrs;21+Yrs" delimiters=";">
                            <tr>
                                <td valign="top" class="docText">Month/Year Admitted to Practice:</td>
                                <td valign="top" class="docText">#event.getValue('PracticeMonth')#/#event.getValue('PracticeYear')#&nbsp;	</td>
                            </tr>
                        </cfcase>
                        <cfcase value="LS">
                            <tr>
                                <td valign="top" class="docText">Name of CTLA Attorney/Member Sponsor:</td>
                                <td valign="top" class="docText">#event.getValue('sponsor', 'No')#&nbsp;	</td>
                            </tr>
                        </cfcase>
                    </cfswitch>&nbsp;

                            <tr><td colspan="2" height="25">
                                    Please note: This payment is not tax-deductible as a charitable contribution; payment may be deductible as a business expense. 
                            </td></tr>
							<tr><td colspan="2" height="25"></td></tr>
                            <tr class="msgHeader"><td colspan="2" class="docText"><strong>Membership Agreement.</strong></td></tr>
                            
                    <cfswitch expression="#event.getValue('proRate')#">
                        <cfcase value="0-2Yrs;3-5Yrs;6-10Yrs;11-15Yrs;16-20Yrs;21+Yrs" delimiters=";">
                           
                            <tr>
                                <td valign="top" class="docText">Full Name:</td>
                                <td valign="top" class="docText">#event.getValue('affidavitName','')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Date:</td>
                                <td valign="top" class="docText">#event.getValue('affidavitDate','')#&nbsp;	</td>
                            </tr>
                        </cfcase>
                        <cfcase value="LS">
                            <tr>
                                <td valign="top" class="docText">Full Name:</td>
                                <td valign="top" class="docText">#event.getValue('affidavitNameLS','')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Date:</td>
                                <td valign="top" class="docText">#event.getValue('affidavitDateLS','')#&nbsp;	</td>
                            </tr>
                        </cfcase>
                        <cfcase value="STU">
                            <tr>
                                <td valign="top" class="docText">Full Name:</td>
                                <td valign="top" class="docText">#event.getValue('affidavitNameStudent','')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Date:</td>
                                <td valign="top" class="docText">#event.getValue('affidavitDateStudent','')#&nbsp;	</td>
                            </tr>
                        </cfcase>
                    </cfswitch>&nbsp;
					 <cfif event.getValue('becomeEagle','No') EQ "Yes">
                            <tr><td colspan="2" height="25"></td></tr>
                            <tr class="msgHeader"><td colspan="2" class="docText"><strong>Optional Eagle Contribution</strong></td></tr>
                            <tr>
                                <td valign="top" class="docText">Donation Level Selected:</td>
                                <td valign="top" class="docText">
                                    <cfswitch expression="#event.getValue('eagleRate')#">
                                        <cfcase value="Legacy">
                                            Legacy: $50,000 and Above
                                        </cfcase> 
										<cfcase value="Founder">
                                            Founder: $25,000 - $49,999
                                        </cfcase>
                                        <cfcase value="Champion">
                                            Champion: $15,000 - $24,999
                                        </cfcase>
                                        <cfcase value="Benefactor">
                                            Benefactor: $10,000 - $14,999
                                        </cfcase>
                                        <cfcase value="Defender">
                                            Defender: $7,500 - $9,999
                                        </cfcase>
                                        <cfcase value="Patron">
                                            Patron: $5,000 - $7,499
                                        </cfcase>
                                        <cfcase value="Advocate">
                                            Advocate: $3,750 - $4,999
                                        </cfcase>
                                        <cfcase value="Sponsor">
											Sponsor: $2,500 - $3,749
                                        </cfcase>
                                        <cfcase value="Associate">
											Associate: $1,500 - $2,499 (in practice 10 years or less)
                                        </cfcase>
                                        <cfcase value="NewAdvocate">
                                            New Advocate: $500 - $1,499 (in practice 5 years or less)
                                        </cfcase>
                                        <cfcase value="Contributor">
                                            Contributor: Up to $2,499
                                        </cfcase>
                                    </cfswitch>&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Pledge Amount:</td>
                                <td valign="top" class="docText">#event.getValue('pledgeAmount','')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Would you like to make this an anonymous contribution?:</td>
                                <td valign="top" class="docText">#event.getValue('isAnonymous', 'No')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Payment Schedule:</td>
                                <td valign="top" class="docText">#event.getValue('paymentSchedule','')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Payment Method:</td>
                                <td valign="top" class="docText">#event.getValue('paymentMethod','')#&nbsp;	</td>
                            </tr>
                            <tr>
                                <td valign="top" class="docText">Special Instructions:</td>
                                <td valign="top" class="docText">#event.getValue('specialInstructions','')#&nbsp;	</td>
                            </tr>

                            <tr><td colspan="2" height="25">
                                     Please note: Contributions or gifts to CTLA are not tax deductible as charitable contributions for income tax purposes. However, a portion may be tax deductible as ordinary and necessary business expenses subject to restrictions imposed as a result of association lobbying activities. You will be notified of the applicable percentages.
                            </td></tr>
                        </cfif>
						
							<tr><td colspan="2" height="25"></td></tr>
                            <tr class="msgHeader"><td colspan="2" class="docText"><strong>#local.strPageFields.AutoRenewTitle#</strong></td></tr>
                            <tr>
                                <td valign="top" class="docText">#local.strPageFields.AutoRenewFieldText#</td>
                                <td valign="top" class="docText">
									<cfscript>
										if(len(arguments.event.getTrimValue('autoRenew',''))){
											local.autoRenewLabel = 'Yes';
										}else{
											local.autoRenewLabel = 'No';
										}
									</cfscript>
									#local.autoRenewLabel#
                                </td>
                            </tr>
							<cfif event.getValue('proRate') neq 'STU' && event.getValue('proRate') neq 'LS'>
								<tr><td colspan="2" height="25"></td></tr>
								<tr class="msgHeader"><td colspan="2" class="docText"><strong>#local.strPageFields.ListAgreementTitle#</strong></td></tr>
								<tr>
									<td valign="top" class="docText">#local.strPageFields.ListAgreementFieldText#</td>
									<td valign="top" class="docText">
										<cfscript>
											if(len(arguments.event.getTrimValue('agreeListAgreement',''))){
												local.agreeListLabel = 'Yes';
											}else{
												local.agreeListLabel = 'No';
											}
										</cfscript>
										#local.agreeListLabel#
									</td>
								</tr>
							</cfif>
                        </table>
                    </cfsavecontent>

					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.siteCode)>
					<cfset local.uid = createuuid()>
					<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
						<cfoutput>
							<html><body>#local.invoice#</body></html>
						</cfoutput>
					</cfdocument>			
					
					<cfset local.strPDF = structNew()>
					<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
					<cfset local.strPDF['serverFile'] = "Membership_#local.memberNumber#_#DateFormat(now(),'m-d-yyyy')#.pdf">
					<cfset local.emailAttachFile = "Membership_#DateFormat(now(),'m-d-yyyy')#.pdf">
					<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
					<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
                    <!--- email member --->
					<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=local.useMID, strPDF=local.strPDF, siteID=local.orgDefaultSiteID, docTitle="Membership_#local.memberNumber#_#DateFormat(now(),'m-d-yyyy')#", docDesc="Membership_#local.memberNumber#_#DateFormat(now(),'m-d-yyyy')#" )>

					<cfsavecontent variable="local.mailContent">
						<cfoutput>
								<p>Your membership is pending approval by the board.  The following information was sent.</p><br><br>
							<hr />
							#local.invoice#	
						</cfoutput>
					</cfsavecontent>
					
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from},
						emailto=[{ name="", email=local.memberEmail.to}],
						emailreplyto= local.ORGEmail.to,
						emailsubject=local.memberEmail.SUBJECT,
						emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=val(local.useMID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>

					<cfset local.emailSentToUser = local.responseStruct.success>
					<cfif NOT local.emailSentToUser>
						<cfset session.invoice = local.invoice>
					</cfif>
					
                    <!--- email association --->
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<cfif NOT local.emailSentToUser>
                                Member was not sent email confirmation due to bad Data.<br />
                                Please contact them and let them know.
                                <hr />
                            </cfif>
                            #local.invoice#
						</cfoutput>
					</cfsavecontent>
					
					<cfscript>
						local.arrEmailTo = [];
						local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
						local.toEmailArr = listToArray(local.ORGEmail.to,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.ORGEmail.from},
						emailto=local.arrEmailTo,
						emailreplyto=local.ORGEmail.from,
						emailsubject=local.ORGEmail.SUBJECT,
						emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=arguments.event.getValue('mc_siteinfo.siteID'),
						memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>

					<!--- GATEWAY INFORMATION: --->
                    <cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=local.siteID, profilecode=local.strPageFields.PayProfileCode, pmid=local.useMID, usePopupDIVName='paymentTable')>

                    <cfoutput>
                    <script type="text/javascript">
                        function getMethodOfPayment() {
                            var btnGrp = document.forms['#local.formName#'].payMeth;
                            var i = getSelectedRadio(btnGrp);
                            if (i == -1) return "";
                            else {
                                if (btnGrp[i]) return btnGrp[i].value;
                                else return btnGrp.value;
                            }
                        }
                        function _validate() {
                            var thisForm = document.forms["#local.formName#"];
                            var arrReq = new Array();
                            thisForm.btnSubmit.disabled=true;
                            #local.strPaymentForm.jsvalidation#
                            if (arrReq.length > 0) {
                                var msg = 'The following fields are required:\n\n';
                                for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
                                alert(msg);
                                thisForm.btnSubmit.disabled=false;
                                return false;
                            }
                            return true;
                        }
                        function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
                    </script>
                    <cfif len(local.strPaymentForm.headCode)>
                        <cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headCode)#">
                    </cfif>

                    <div id="paymentTable">
                        <div id="payerrDIV" style="display:none;margin:6px 0;"></div>
                        <div class="form">
                            <cfform name="#local.formName#" id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
                                <cfinput type="hidden" name="isSubmitted" id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
                                <cfinput type="hidden" name="#local.nameOfHiddenField#" id="#local.nameOfHiddenField#" value="" />
                                <div>
                                   <div id="CCInfo" class="">
                                        <div class="tsAppLegendTitle sectionTitle">Credit Card Information</div>
                                        <div class="tsAppBodyText optionsInline">
                                            <cfif len(local.strPaymentForm.inputForm)>
                                                <div>#local.strPaymentForm.inputForm#</div>
                                            </cfif>
                                            <br />
                                        </div>
                                    </div>

                                    <div class="PB"><button type="submit" class="tsAppBodyText formButton" name="btnSubmit">Continue</button></div>

                                </div>							
                            </cfform>
                        </div>
                    </div>
                    </cfoutput>
                </cfif>
			</cfcase>
		
			<!--- PROCESS: --->
			<cfcase value="2">
				<cfoutput>
					Thank you for submitting your request for membership.<br /><br />
					<cfif IsDefined("session.invoice") and session.invoice NEQ "">
						Please print the following for your records:
						#session.invoice#	
					<cfelse>
						You have been sent an email for your records.
					</cfif>
					<br /><br />
					Please note that the credit card provided will not be charged until your application is processed and approved by CTLA. If you chose to pay your Eagle contribution by check, an invoice will be sent to you by mail and/or Email.										
				</cfoutput>				
				<cfset session.invoice = "">
			</cfcase>			
			<cfcase value="100"><!--- SPAM MESSAGE: If captcha is rejected or isAgree field has value [Bot manipulation] --->
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
		</cfswitch>
	</div>
</cfoutput>