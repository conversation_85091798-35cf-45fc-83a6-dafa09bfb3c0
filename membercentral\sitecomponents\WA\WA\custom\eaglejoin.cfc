<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Click Here to Begin" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you. Your EAGLE application has been submitted for review. You will receive an email from Washington State Association for Justice once your application has been approved and processed." };  
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="EaglePledgeApplication", type="CONTENTOBJ", desc="Eagle Pledge Application", value="EAGLE members are regular WSAJ members whose higher level of dues support WSAJ's legislative & public affairs programs. A portion of EAGLE dues are allocated to the member's local independently governed trial attorney political action committee. Your EAGLE membership includes your dues and many other benefits." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="Washington State Association for Justice records indicate you are currently an EAGLE member. Please click here to log in." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Error message when billed subscription found", value="You need to renew your EAGLE membership. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="MembershipType", type="CONTENTOBJ", desc="Membership Type Intro", value="Per the information provided on the previous page, below is your Eagle Program selection and amount." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="ECC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField); 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="PaymentProfileCodeBD", type="STRING", desc="pay profile code for Bank Draft", value="EAGLEBD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCheck", type="STRING", desc="pay profile code for Check", value="PaybyCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfessionalLicense", type="CONTENTOBJ", desc="Professional License", value="Please add all State Licensures. For License Number, please be sure to use any leading zeroes on the number.  You must provide your admittance date for each license type (even if it is an estimate)." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="RequiredStatement", type="CONTENTOBJ", desc="Required Reminder Statement", value="* Required Data" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ImportantTaxInformation", type="CONTENTOBJ", desc="Important Tax Information Statement", value="Important Tax Information:NO PART OF THIS AMOUNT IS DEDUCTIBLE AS A CHARITABLE CONTRIBUTION FOR FEDERAL INCOME TAX PURPOSES. CONSULT YOUR TAX COUNSEL ABOUT A DEDUCTION AS A BUSINESS EXPENSE. WSAJ WILL NOTIFY YOU WHAT PERCENT OF YOUR ANNUAL DUES IS REGARDED AS DIRECTLY OR INDIRECTLY PERTAINING TO WSAJ LOBBYING ACTIVITY AND IS THEREFORE NOT DEDUCTIBLE. " }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PersonalInjuryCheckBox", type="CONTENTOBJ", desc="Personal Injury Statement Check Box", value="I certify that of the personal injury cases handled, 50% or more are for the plaintiff; criminal practice involves representation of defendants; represents claimants in workers' compensation, social security and longshoreworkers' actions." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="TrueCertification", type="CONTENTOBJ", desc="True Certification Statement", value="I certify that the statements I made herein are true. By providing my mailing address, email address, telephone and fax number, I agree to receive communications sent by or on behalf of the Washington State Association for Justice via mail, email, telephone or fax." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CertificationCheckBox", type="CONTENTOBJ", desc="Certification Statement Check Box", value="I certify that the above information is true and correct." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="EditableField", type="CONTENTOBJ", desc="Editable content", value="per the information provided in the previous page,below is your Eagle Program selection and amount"}; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Eagle  Membership Application',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='EagleMembershipApplicationHistory', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='EagleMembershipApplicationHistory', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;
				}
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				@media only screen and (min-width: 768px) and (max-width: 979px){
					input:not(.mc_inlinecheckbox) {
						width: auto!important;
					}
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
				}
				function validateMembershipInfoForm(){
					var arrReq = new Array();
			        if (!$('###variables.formName# ##PersonalInjuryCheckBox').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";
					
			        else if (!$('###variables.formName# ##CertificationCheckBox').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";
					
					var fields = $("input[name='mccf_EagleMemberRate']").serializeArray(); 
					if (fields.length === 0) 
					{ 
						arrReq[arrReq.length] = "Select any rate";						
					
					}
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
						
			<div class="row-fluid tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="row-fluid tsAppBodyText">#variables.strPageFields.AccountLocatorIntroText#</div>
			<div class="row-fluid tsAppSectionContentContainer">
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.annualMembershipDuesUID = "5538907b-ec63-4b93-b8f3-7da04c96e260">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.annualMembershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.annualMembershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.annualMembershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Join WSAJ - Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='0eb23607-d58a-4054-9882-644e88bae734', mode="collection", strData=local.strData)>
		<!--- Join WSAJ - Mailing Address Information --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='fe98e972-c59b-4c7d-9775-edcda37810ef', mode="collection", strData=local.strData)>
		
		<!--- get Kansas Bar Num, Admission Date --->
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Law School">
				<cfset local.lawSchool = local.thisField>
			<cfelseif local.strFieldSetContent1.strFields[local.thisField] eq "Graduation Date (or expected date)">
				<cfset local.gradDate = local.thisField>
			</cfif>
		</cfloop>

		<!--- get Mailing Address --->
		<cfloop collection="#local.strFieldSetContent3.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent3.strFields[local.thisField] eq "Address Line 1">
				<cfset local.memberMailAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	
		
		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#
					#local.strFieldSetContent3.jsValidation#				
					
					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					if(prof_license != "" && prof_license != null){
						isProfLicenseSelected = true;
						$.each(prof_license,function(i,val){
							var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
							if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
							if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
						});
					}			
					
					<cfif isDefined("local.memberMailAddress1")>						
						if ($.trim($('###variables.formName# ###local.memberMailAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Mailing Address is required.";
					</cfif>
					
					if(!isProfLicenseSelected){
								arrReq[arrReq.length] = "Professional License is required.";
						} 

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>

					$("##mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							if(ui.checked){
								$("##state_table").show();
								$('##state_table tr:last').after('<tr id="tr_state_'+ui.value+'">'
																+'<td align="right" class="tsAppBodyText">'+ui.text+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" />'
																+'	<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
																+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="tsAppBodyText" />'
																+'</td>'
																+'<td align="center" class="tsAppBodyText" style="display:none">'
																+'	<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="">Please Select</option><option value="active" selected="selected">Active</option><option value="inactive">Inactive</option><option value="disbarred">Disbarred</option><option value="suspended">Suspended</option></select>'
																+'</td>'
														+'</tr>');
								if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
									mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
								}
								$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
								}else{
								$("##tr_state_"+ui.value).remove();
							}
					   },
					});
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<cfif len(variables.strPageFields.RequiredStatement)>
				<div style="color:##FF0000;">#variables.strPageFields.RequiredStatement#</div>
				<br/>
			</cfif>
			<div class="row-fluid">
				<div class=" span12 tsAppSectionHeading">Eagle Pledge Application</div>
				<div class="span12 tsAppSectionContentContainer">
					#variables.strPageFields.EaglePledgeApplication#
				</div>
			</div></br>	
			
			<div class="row-fluid">
				<div class=" span12 tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
				<div class="span6 tsAppSectionContentContainer">
					#local.strFieldSetContent1.fieldSetContent#
				</div>
			</div></br>	
						
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">Professional License Information</div>
				<div class="row-fluid tsAppSectionContentContainer">
					<cfif len(variables.strPageFields.ProfessionalLicense)>
						<div id="ProfessionalLicense">#variables.strPageFields.ProfessionalLicense#</div><br/>
					</cfif>
					<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
					<table cellpadding="3" border="0" cellspacing="0">		
						<tr class="top">
							<th class="tsAppBodyText" colspan="3" align="left">
								<b>Please add all State Licensures. For License number, please be sure to use any leading zeroes on the number.</b>
							</th>
						</tr>							
						<tr align="top">
							<td class="tsAppBodyText" width="10">&nbsp;</td>
							<td class="tsAppBodyText" width="365">Professional License:</td>
							<td class="tsAppBodyText">
								<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
									<cfloop query="local.qryOrgPlTypes">
										<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
										<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr class="top">
							<td class="tsAppBodyText" width="10"></td>
							<td class="tsAppBodyText"></td>
							<td class="tsAppBodyText"></td>
						</tr>
					</table>
					<table cellpadding="3" border="0" cellspacing="0">
						<tr>
							<td class="tsAppBodyText" width="375">&nbsp;</td>
							<td>
								<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
									<thead>
										<tr valign="top">
											<th align="center" class="tsAppBodyText">State Name</th>
											<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
											<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
										</tr>
									</thead>
									<tbody>
									<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
										<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
											<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
											<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
											<cfset  local.license_status  = 'Active'>
											<tr id="tr_state_#local.thisItem#">
												<td align="right" class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</td>
												<td align="center" class="tsAppBodyText">
													<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
													<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
												</td>
												<td align="center" class="tsAppBodyText">
													<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" />
													<cfsavecontent variable="local.datejs">
														<cfoutput>
														<script language="javascript">
															$(document).ready(function() {
																mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
															});
														</script>
														<style type="text/css">
														##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
														</style>
														</cfoutput>
													</cfsavecontent>
													<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
												</td>
												<td align="center" class="tsAppBodyText">
													<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText">
														<option value="">Please Select</option>
														<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>
														<option <cfif local.license_status eq "inactive">selected="selected"</cfif>value="inactive">Inactive</option>
														<option <cfif local.license_status eq "disbarred">selected="selected"</cfif>value="disbarred">Disbarred</option>
														<option <cfif local.license_status eq "suspended">selected="selected"</cfif>value="suspended">Suspended</option>
													</select>
												</td>
											</tr>
										</cfloop>
									</cfif>									
									</tbody>
								</table>
							</td>
						</tr>					
					</table>
				</div>
			</div>
			
			<div class="row-fluid">
				<div class="row-fluid tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
				<div class="row-fluid tsAppSectionContentContainer">	
					#local.strFieldSetContent3.fieldSetContent#
				</div>
			</div>
						
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->	
		<cfset local.membershipValue='Regular Attorney'>
		<cfset local.contactTypeValue = "Attorney">
		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field='Membership Category', value=local.membershipValue)>
		<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = '7b131f1f-f46d-463e-ad99-c0a2ec549962')>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
		<cfset local.eagleMemberAdvocateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Advocate'
				).recordcount gt 0)>

		<cfset local.eagleMemberAssociateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Associate'
				).recordcount gt 0)>

		<cfset local.eagleMemberSponsorRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Sponsor'
				).recordcount gt 0)>				

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">					
						<cfset local.mccf_EagleMemberRate = 0>						
						<cfif structKeyExists(arguments.rc, "mccf_EagleMemberRate")>
							<cfset local.mccf_EagleMemberRate = arguments.rc.mccf_EagleMemberRate>
						</cfif>
										
						<div style="margin-left:15px;" class="subAddonWrapper subAvailableRates subRatesDisabled">
						<div class="well subAddonWrapper" id="EagleMemberRate">
							<legend>EAGLE Program</legend>
							#variables.strPageFields.EditableField#	<br/><br/>
							<cfif local.eagleMemberAdvocateRate>
								<label class="radio subRateLabel" for="mccf_newAdvocate_660">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 660>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_newAdvocate_660" value="660">
									<span class="labelText">EAGLE Member: New Advocate ( less than 5 years ) - 1 payment of $660</span>
								</label>
								<label class="radio subRateLabel" for="mccf_newAdvocate_55">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 55>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_newAdvocate_55" value="55">
									<span class="labelText">EAGLE Member: New Advocate ( less than 5 years ) - 12 Payments of $55 ( $660 Total )</span>
								</label>
							</cfif>	
							
							<cfif local.eagleMemberAssociateRate>
								<label class="radio subRateLabel" for="mccf_associate_1320">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 1320>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_associate_1320" value="1320">
									<span class="labelText">EAGLE Member: Associate - 1 Payment of $1320</span>
								</label>
								<label class="radio subRateLabel" for="mccf_associate_110">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 110>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_associate_110" value="110">
									<span class="labelText">EAGLE Member: Associate - 12 Payments of $110 ( $1320 Total )</span>
								</label>	
							</cfif>
							<cfif local.eagleMemberSponsorRate>
								<label class="radio subRateLabel" for="mccf_sponsor_3300">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 3300>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_sponsor_3300" value="3300">
									<span class="labelText">EAGLE Member: Sponsor - 1 Payment of $3300</span>
								</label>
								<label class="radio subRateLabel" for="mccf_sponsor_275">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 275>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_sponsor_3300" value="275">
									<span class="labelText">EAGLE Member: Sponsor - 12 Payments of $275 ( $3300 Total )</span>
								</label>
							</cfif>
							<cfif (local.eagleMemberSponsorRate) >
								<label class="radio subRateLabel" for="mccf_goldeagle_3960">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 3960>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_goldeagle_3960" value="3960">
									<span class="labelText">EAGLE Member: Gold EAGLE - 1 Payment of $3960</span>
								</label>
								<label class="radio subRateLabel" for="mccf_sponsor_330">
									<input type="radio" class="subRateCheckbox" <cfif local.mccf_EagleMemberRate eq 330>checked="checked"</cfif> name="mccf_EagleMemberRate" id="mccf_sponsor_3300" value="330">
									<span class="labelText">EAGLE Member: Gold EAGLE - 12 Payments of $330 ( $3960 Total )</span>
								</label>
							</cfif>	
						</div>
						<div class="well subAddonWrapper" id="subAddonContent">
							<cfif len(variables.strPageFields.ImportantTaxInformation)>
								<div id="ImportantTaxInformation">#variables.strPageFields.ImportantTaxInformation#</div><br/>
							</cfif> 
							<cfif len(variables.strPageFields.PersonalInjuryCheckBox)>
								<div id="PersonalInjuryCheckBox">
									<label class="checkbox subLabel" for="PersonalInjuryCheckBox">
									<input class="subCheckbox" type="checkbox" name="PersonalInjuryCheckBox" id="PersonalInjuryCheckBox" value="1">
									#variables.strPageFields.PersonalInjuryCheckBox#
								</div><br/>
							</cfif>
							<cfif len(variables.strPageFields.TrueCertification)>
								<div id="TrueCertification">#variables.strPageFields.TrueCertification#</div><br/>
							</cfif>
							<cfif len(variables.strPageFields.CertificationCheckBox)>
								<div id="CertificationCheckBox">
									<label class="checkbox subLabel" for="CertificationCheckBox">
									<input class="subCheckbox" type="checkbox" name="CertificationCheckBox" id="CertificationCheckBox" value="1">
									#variables.strPageFields.CertificationCheckBox#
								</div><br/>
							</cfif>

						</div>
					</div>
				</div>
			</div>
			
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>			
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
			<cfset structDelete(session.formFields, "substruct")>
		</cfif>			

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
				
		<cfset local.eagleMemberAdvocateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Advocate'
				).recordcount eq 0)>
		<cfset local.eagleMemberAssociateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Associate'
				).recordcount eq 0)>
		<cfset local.eagleMemberSponsorRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Sponsor'
				).recordcount eq 0)>
	
		<!--- <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)> --->
		
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC, variables.strPageFields.PaymentProfileCodeBD ]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">
				<strong>EAGLE Member</strong><br/>
			
				<cfif structKeyExists(arguments.rc, 'mccf_EagleMembeRRate') >
					<cfswitch expression="#arguments.rc.mccf_EagleMembeRRate#">
						<cfcase value="660">
							EAGLE Member: New Advocate ( less than 5 years ) - 1 Payment of $660
						</cfcase>
						<cfcase value="55">
							EAGLE Member: New Advocate ( less than 5 years ) - 12 Payments of $55( $660 Total)
						</cfcase>
						<cfcase value="1320">
							EAGLE Member: Associate - 1 Payment of $1320
						</cfcase>
						<cfcase value="110">
							EAGLE Member: Associate - 12 Payments of $110 ( $1320 Total)
						</cfcase>
						<cfcase value="3300">
							 EAGLE Member: Sponsor - 1 Payment of $3300
						</cfcase>
						<cfcase value="275">
							 EAGLE Member: Sponsor - 12 Payments of $275  ( $3300 Total)
						</cfcase>
						<cfcase value="3960">
							EAGLE Member: Gold EAGLE - 1 Payment of $3960
						</cfcase>
						<cfcase value="330">
							EAGLE Member: Gold EAGLE - 12 Payments of $330  ($3960 Total)
						</cfcase>
					</cfswitch>		
				 
				</cfif>	
				<br/>				
			</div>			
			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(arguments.rc.mccf_EagleMembeRRate)#
			</div>		
			<br/><br/>			
				#local.strReturn.paymentHTML#
	
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
				
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>	
		
		<cfset local.eagleMemberAdvocateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Advocate'
				).recordcount gt 0 )>
		<cfset local.eagleMemberAssociateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Associate'
				).recordcount gt 0)>
		<cfset local.eagleMemberSponsorRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Sponsor'
				).recordcount gt 0)>
			
	
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Join WSAJ - Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='0eb23607-d58a-4054-9882-644e88bae734', mode="confirmation", strData=arguments.rc)>
		<!--- Join WSAJ - Mailing Address Information --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='fe98e972-c59b-4c7d-9775-edcda37810ef', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.eagleMemberAdvocateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Advocate'
				).recordcount gt 0)>
		<cfset local.eagleMemberAssociateRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Associate'
				).recordcount gt 0)>
		<cfset local.eagleMemberSponsorRate = ( 
				application.objCustomPageUtils.mem_getGroups(
					memberID=variables.useMID,orgID=variables.orgID,groupCode='Sponsor'
				).recordcount gt 0)>		
		
		<cfset local.memberPayProfileDetail = "">
		<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
			<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
		<cfelse>
			<cfset local.memberPayProfileSelected = 0>
		</cfif>
		<cfif local.memberPayProfileSelected gt 0>
			<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<fieldset>
						<legend>Form submission</legend>
						<div class="row-fluid tsAppSectionContentContainer">#variables.strPageFields.ConfirmationMessage#</br></br></div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<div class="row-fluid tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.ConfirmationMessage#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div class="row-fluid tsAppSectionContentContainer">
					<div class="span12">You have received an application for membership.</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.strFieldSetContent1.fieldSetContent#
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join WSAJ - Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0">
												<thead>
													<tr valign="top">
														<th align="center" class="tsAppBodyText">State Name</th>
														<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" class="tsAppBodyText">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" class="tsAppBodyText">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" class="tsAppBodyText">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" class="tsAppBodyText">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br/>
				
				
				#local.strFieldSetContent3.fieldSetContent#	

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join WSAJ - Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>
								<strong>EAGLE Member</strong><br/>
							<cfif structKeyExists(arguments.rc, 'mccf_EagleMembeRRate') >
								<cfswitch expression="#arguments.rc.mccf_EagleMembeRRate#">
									<cfcase value="660">
										EAGLE Member: New Advocate ( less than 5 years ) - 1 Payment of $660
									</cfcase>
									<cfcase value="55">
										EAGLE Member: New Advocate ( less than 5 years ) - 12 Payments of $55( $660 Total)
									</cfcase>
									<cfcase value="1320">
										EAGLE Member: Associate - 1 Payment of $1320
									</cfcase>
									<cfcase value="110">
										EAGLE Member: Associate - 12 Payments of $110( $1320 Total)
									</cfcase>
									<cfcase value="3300">
										EAGLE Member: Sponsor - 1 Payment of $3300
									</cfcase>
									<cfcase value="275">
										EAGLE Member: Sponsor - 12 Payments of $275  ( $3300 Total)
									</cfcase>
									<cfcase value="3960">
										EAGLE Member: Gold EAGLE - 1 Payment of $3960
									</cfcase>
									<cfcase value="330">
										EAGLE Member: Gold EAGLE - 12 Payments of $330 ( $3960 Total)
									</cfcase>
								</cfswitch>		
							</cfif>							
							
							</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(arguments.rc.mccf_EagleMembeRRate)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<!--- <cfif local.paymentRequired> --->
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				<!--- </cfif>	 --->
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>		

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.memberEmail.from },
                            emailto=[{ name="", email=variables.memberEmail.to }],
                            emailreplyto=variables.ORGEmail.to,
                            emailsubject=variables.memberEmail.SUBJECT,
                            emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
                            emailhtmlcontent=local.confirmationHTMLToMember,
                            siteID=variables.siteID,
                            memberID=val(variables.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

		<cfset local.emailSentToUser = local.responseStruct.success>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your EAGLE application to WSAJ", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>

		<cfreturn local.confirmationPageHTMLContent>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>