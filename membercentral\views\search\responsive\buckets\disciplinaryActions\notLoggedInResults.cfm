<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div id="MCNotLoggedInContainer"<cfif val(local.strResultsCount.itemCount) GT 0> style="display:none;"</cfif>>
	<cfif local.strResultsCount.itemCount EQ 0>
		<div>#showCommonNotFound(bucketID=arguments.bucketID, searchID=arguments.searchID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#</div>
	<cfelse>
		<cfif local.hasDepositionBucket>
			<div style="padding-bottom:10px;">
				<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
			</div>
		</cfif>
		<cfif val(local.strResultsCount.itemCount) gt 0>
			<div class="p-3"><strong>#Numberformat(local.strResultsCount.itemCount)#</strong> professional<cfif local.strResultsCount.itemCount is not 1>s</cfif> found</div>
		</cfif>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</cfif>
</div>
<cfif val(local.strResultsCount.itemCount) GT 0>
	<div id="MCSearchSummaryContainer">
		<cfif len(trim(local.stsearchVerboseNoName))>
			<div>
				<strong>Search Criteria:</strong> <em>#local.stsearchVerboseNoName#</em>
			</div>
		</cfif>

		<div class="bk-mb-3 bk-mt-4">
			<button type="button" class="btn btn-success btn-large" onclick="b_showLoginContainer();">View Disciplinary Actions</button>
		</div>

		<div class="bk-d-flex bk-mt-4 bk-flex-wrap">
			<div class="bk-d-flex bk-mr-5 bk-mb-3">
				<div class="bk-summary-circle bk-text-light">#Numberformat(local.strResultsCount.itemCount)#</div>
				<div class="bk-d-flex bk-align-self-center bk-pl-3 bk-font-weight-bold">Potential Disciplinary Action(s)</div>
			</div>
		</div>
	</div>
	#showFooter(viewDirectory=arguments.viewDirectory)#
</cfif>
</cfoutput>