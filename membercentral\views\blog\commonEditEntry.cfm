<cfscript>
	local.dataStruct 		= attributes.data.actionStruct;
	local.instanceSettings 	= attributes.data.instanceSettings;
	local.rc				= attributes.event.getCollection();
	local.baseQueryString 	= attributes.data.baseQueryString;
	local.objCategory = CreateObject('component', 'model.system.platform.category');	
	
	if( (attributes.data.blAction EQ "editEntry") or (attributes.data.blAction EQ "addEntry" and isdefined("local.rc.blBlogSaved")) ){
		local.editMode = true;
		local.canDelete = (local.instanceSettings.appRightsStruct.deleteOwn AND listFind(local.dataStruct.getDocData.authorMemberIDList,application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=attributes.event.getvalue("mc_pageDefinition.orgID")))) OR local.instanceSettings.appRightsStruct.deleteAny;
		local.canEditMetaData = true;
		local.canReupload = true;
	} else{
		local.editMode = false;
		local.canDelete = false;
		local.canEditMetaData = true;
		local.canReupload = true;
	}

	local.canCrossPost = (local.dataStruct.allowedCrossPostingTargets.len() OR local.dataStruct.qrySharedBlogInstances.recordcount gt 1);
	local.canManageAuthors = (local.instanceSettings.blogAuthorSupport neq "D" and local.instanceSettings.appRightsStruct.manageAllEntryAuthors is 1);

</cfscript>

<!--- force IE 8+ into Standards Mode, which is ideal for CKEditor, as opposed to Quirks Mode --->
<cfheader name="X-UA-Compatible" value="IE=Edge">

<cfsavecontent variable="local.commonJS">
	<cfoutput>
		#application.objWebEditor.showEditorHeadScripts()#
		<script type="text/javascript">
			var #toScript(local.dataStruct.getDocData.blogID,"mcblog_blogid")#
			var #toScript(local.dataStruct.getDocData.blogEntryID,"mcblog_entryid")#
			var #toScript(local.dataStruct.uploadEntryDocsLink, "mcblog_entryUploadDocsLink")#
			var #toScript(local.dataStruct.uploadEntryFeaturedImageLink, "mcblog_entryUploadFeaturedImageLink")#

			function validateAndSaveEntryDetails() {
				var arrReq = new Array();

				if (($('##blPostTypeID').val() || '') == ''){
					showMCBEAlert('Choose #ucFirst(local.instanceSettings.Singular)# Type.');
					return false;
				}

				$("button##btnSaveBlogEntry").prop('disabled',true);

				<cfif local.instanceSettings.Title>
					if ($('##blTitle').val() == '') arrReq.push('Enter a Title for your #local.instanceSettings.Singular#.');
				</cfif>

				var summaryContent = "";
				var blogBodyContent = "";

				if(typeof(CKEDITOR) !== "undefined" && CKEDITOR.instances['rawContentSummary'] != null)
					summaryContent = CKEDITOR.instances['rawContentSummary'].getData().trim();
				else 
					summaryContent = $('textarea[name="rawContentSummary"]').val().trim();
				
				<cfif local.instanceSettings.NameDesc eq "Wall">
					blogBodyContent = $('##rawContent').val();
					if (blogBodyContent == null || blogBodyContent == "" || blogBodyContent == "What's on your mind?") 
						arrReq.push('Enter #JSStringFormat(local.instanceSettings.NameDesc)# text.');
				<cfelse>
					for (instance in CKEDITOR.instances) {
						CKEDITOR.instances[instance].updateElement();
					}

					if(typeof(CKEDITOR) !== "undefined" && CKEDITOR.instances['rawContent'] != null)
						blogBodyContent = CKEDITOR.instances['rawContent'].getData().trim();
					else 
						blogBodyContent = $('textarea[name="rawContent"]').val().trim();
					
					if (blogBodyContent == '')
						arrReq.push('Enter a #local.instanceSettings.NameDesc#.');
				</cfif>
				<cfif local.instanceSettings.blogAuthorSupport eq "R">
					if ($('##blAuthorMIDList').val() == '') arrReq.push('Enter a #local.instanceSettings.Singular# author.');
				</cfif>

				if ($('.MCBlogEntryField').length) {
					var fieldsErrorArray = [];
					var fieldContainer = $('.MCPostTypeFieldContainer');
					fieldsErrorArray = $.map(fieldContainer,doBlogPostTypeFieldsValidate);

					/*drop empty elements*/
					var fieldsErrorArray = $.map(fieldsErrorArray, function(thisError){
						if (thisError.length) return thisError;
						else return null;
					});

					arrReq = arrReq.concat(fieldsErrorArray);
				}
				
				<cfif local.dataStruct.isOwnBlogEntry and local.instanceSettings.appRightsStruct.manageQuickLinks is 1>
					var redirectName = $.trim($('##newRedirectName').val());
					if(validateRedirectName(redirectName).length) arrReq.push(validateRedirectName(redirectName));
				</cfif>
				
				if (arrReq.length > 0) {
					showMCBEAlert(arrReq.join('<br/>'));
					location.href="##entryFrmTop";
					$("button##btnSaveBlogEntry").prop('disabled',false);
					return false;
				} else {
					hideMCBEAlert();
				}

				var beForm = $('##frmBlog');
				var arrFrmData = beForm.serializeArray();
				var fd = new Object();

				var arrCheckboxMultiSelectFields = $.unique(beForm.find('input[type="checkbox"],select[multiple="true"]').map( function() { return $(this).attr('name'); }).get());

				$.each(arrFrmData, function() {
					if (fd[this.name] !== undefined && typeof this.value !== undefined) {
						fd[this.name] = fd[this.name] + ',' + this.value || '';
					} else {
						fd[this.name] = this.value || '';
					}
				});

				fd['rawContentSummary'] = summaryContent;
				fd['rawContent'] = blogBodyContent;

				/*empty checkbox and multiselect handling*/
				$.each(arrCheckboxMultiSelectFields, function() {
					if (fd[this] === undefined) fd[this] = '';
				});

				<cfif local.dataStruct.isOwnBlogEntry and local.instanceSettings.appRightsStruct.manageQuickLinks is 1>
					var checkQuickLinkResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							if(r.isduplicate == true) {
								setQuickLinkTestMessage(false);
								showMCBEAlert('Entered Quick Link is already in use.');
								$("button##btnSaveBlogEntry").prop('disabled',false);
							} else {
								doSaveEntryDetails(fd);
							}
						} else {
							showMCBEAlert('We were unable to check whether the quick link exists.');
							$("button##btnSaveBlogEntry").prop('disabled',false);
						}
					};
					
					var currentRedirectName = $('##currentRedirectName').val();
					var newRedirectName = $.trim($('##newRedirectName').val());
					if(newRedirectName && newRedirectName.length && currentRedirectName.toLowerCase() != newRedirectName.toLowerCase()) {
						checkUniqueQuickLink(newRedirectName,checkQuickLinkResult);
						return false;
					}
				</cfif>

				doSaveEntryDetails(fd);
			}
			function doSaveEntryDetails(fd) {
				$('##frmBlog .blogEntryHideWhileSaving').addClass('mc_entry_hide');
				$('##divBESaveLoading').removeClass('mc_entry_hide');
				$("##divBEFormSubmitArea").load('#local.dataStruct.formPostURL#', fd, function(response) {
					var r = JSON.parse(response);
					if(r.success) onBlogSaveComplete(r);
					else {
						showMCBEAlert('Some error occured. We were unable to save the details.');
						$('##frmBlog').removeClass('mc_entry_hide');
						$("##divBESaveLoading").addClass('mc_entry_hide');
						$("button.btnSaveBlogEntry").prop('disabled',false);
					}
				});
			}
			async function onBlogSaveComplete(r) {
				let uploadDocsSuccess = false, uploadImgSuccess = false;
				let arrError = [];

				if(r.process == 'insert'){
					mcblog_entryid = r.blogentryid;
					uppyDocuments.getPlugin('XHRUpload').setOptions({ endpoint: mcblog_entryUploadDocsLink + '&blBlogEntryID=' + mcblog_entryid });
					if(uppyFeaturedImage) uppyFeaturedImage.getPlugin('XHRUpload').setOptions({ endpoint: mcblog_entryUploadFeaturedImageLink + '&blBlogEntryID=' + mcblog_entryid });
				}

				if (uppyDocuments && uppyDocuments.getFiles().length){
					$('##divBESaveLoading .loadingMsg').html('Please wait while we upload the files.');
					$('##BlogDocumentsFlexWrapper').removeClass('has-existing')
					$('##divEntryDocsHolder').hide();
					$('##mcBEDocUploader, ##documentsWell').removeClass('mc_entry_hide');
					
					uploadDocsSuccess = await uppyEntryFileUploadPromise(uppyDocuments);
					if(!uploadDocsSuccess){
						uppyDocuments.getPlugin('Dashboard').setOptions({ disabled:true });
						arrError[arrError.length] = "An error occured while uploading the documents.";
						uploadDocsSuccess = true;
					}
					else {
						$('##mcBEDocUploader, ##documentsWell').addClass('mc_entry_hide');
					}
				}
				else uploadDocsSuccess = true;

				if (uploadDocsSuccess) {
					if (uppyFeaturedImage && uppyFeaturedImage.getFiles().length){
						$('##divBESaveLoading .loadingMsg').html('Please wait while we upload the featured image.');
						$('##featuredImageFlexWrapper').removeClass('has-existing')
						$('##mcBEFeaturedImageUploader, ##featuredImageWell').removeClass('mc_entry_hide');
						
						uploadImgSuccess = await uppyEntryFileUploadPromise(uppyFeaturedImage);
						if(!uploadImgSuccess) {
							uppyFeaturedImage.getPlugin('Dashboard').setOptions({ disabled:true });
							arrError[arrError.length] = "An error occured while uploading the featured image.";
							uploadImgSuccess = true;
						}
						else {
							$('##mcBEFeaturedImageUploader, ##featuredImageWell').addClass('mc_entry_hide');
						}
					}
					else uploadImgSuccess = true;
				}

				if (uploadImgSuccess) {
					if(arrError.length){
						$('##divBESaveLoading').addClass('mc_entry_hide');
						showMCBEAlert('<div class="mb-2">'+ arrError.join('<br/>') +'</div><button class="btn btn-small" style="margin-top:5px;" onclick="onSaveFinalFn(\''+r.relocateurl+'\');return false;">Review Entry</button>');
					}
					else {
						onSaveFinalFn(r.relocateurl);
					}
				}
			}
			function onSaveFinalFn(relocateURL){
				window.location.href = relocateURL;
			}
			function uppyEntryFileUploadPromise(uppyInstance) {
				return uppyInstance.retryAll()
					.then((result) => uppyInstance.upload())
					.then((result) => {
						if (result.failed.length > 0) {
							console.error('Failed ['+uppyInstance.getID()+']:');
							result.failed.forEach((file) => {
								console.error(file.error);
							});
							return false;
						} else {
							return true;
						}
					}).catch(error => {
						console.error('Error ['+uppyInstance.getID()+']:');
						console.log(error);
						return false;
					});
			}

			function hideMCBEAlert() { $('##entry_frm_err').html('').hide(); }
			function showMCBEAlert(msg) { $('##entry_frm_err').html(msg).show(); }

			<cfif local.dataStruct.isOwnBlogEntry>
				function onBlurRedirectName(redirectName) {
					hideMCBEAlert();
					
					if(validateRedirectName(redirectName).length) return;
					
					var checkQuickLinkResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							setQuickLinkTestMessage(r.isduplicate == false);
						} else {
							$('##redirectBox').addClass('d-none');
							showMCBEAlert('We were unable to check whether this quick link exists.');
						}
					};
					
					if(redirectName.length) checkUniqueQuickLink(redirectName, checkQuickLinkResult);
					else $('##redirectBox').addClass('d-none');
				}
				function checkUniqueQuickLink(redirectName,callback) {
					var objParams = { redirectID:#val(local.dataStruct.getDocData.redirectID)#, redirectName:redirectName };
					TS_AJX('ALIAS','checkUniqueQuickLink', objParams, callback,callback,10000,callback);
				}
				function setQuickLinkTestMessage(success){
					$('##redirectImg').toggleClass('icon-ok-sign', success).toggleClass('icon-exclamation-sign', !success);
					$('##redirectText').html(success ? 'Passed!' : 'Quick Link already used!');
					$('##redirectBox').toggleClass('text-success', success).toggleClass('text-error', !success).removeClass('d-none');
				}
				function validateRedirectName(redirectName) {
					var invalidRedirectNameMsg = "";
					if (redirectName.length && redirectName.startsWith('?')) {
						invalidRedirectNameMsg = "Enter a valid quick link. It cannot begin with a question mark.";
						var success = false;
						$('##redirectImg').toggleClass('icon-ok-sign', success).toggleClass('icon-exclamation-sign', !success);
						$('##redirectText').html(invalidRedirectNameMsg);
						$('##redirectBox').toggleClass('text-success', success).toggleClass('text-error', !success).removeClass('d-none');			
					}
					return invalidRedirectNameMsg;
				}
			</cfif>

			<cfif local.editMode and local.canDelete>
				function confirmDelete(delUrl) {
					if (confirm("Are you sure you want to delete this blog entry?")) {
						self.location.href = delUrl;
					}
				}
			</cfif>
			
			function resizeBox(newW,newH) { 
				var windowWidth = $(window).width();
				var _popupWidth = 550;
				if(windowWidth < 585) {
					_popupWidth = windowWidth - 30;
				}
				$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
			}
			
			<cfif local.canManageAuthors>
				function selectBlogAuthor() {
					var windowWidth = $(window).width();
					var _popupWidth = 550;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=doAddAuthor', iframe:true, overlayClose:false} );
				}
				function doAddAuthor(memObj) {
					$.colorbox.close();

					var nonExistingMID = true;
					var arrMID = $('##blAuthorMIDList').val().split(',');
					for (var j=0; j<arrMID.length;j++) { 
						if(arrMID[j]==memObj.memberID) { nonExistingMID = false; break; }
					}

					if (nonExistingMID) {		
						var arrMID = $('##blAuthorMIDList').val().split(',');
							arrMID.push(memObj.memberID);
							arrMID = arrMID.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
						$('##blAuthorMIDList').val(arrMID);

						assignMemberData(memObj);
					} else {
						alert('The selected member is already an author.');
					}
					return false;
				}
				function assignMemberData(memObj) {
					var result = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							if (Number(mcblog_entryid) > 0) saveBlogAuthor(r);
							else showNewAuthor(r);
						}
					};
					var objParams = { memberNumber:memObj.memberNumber };
					TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,result,result,10000,result);
				}
				function saveBlogAuthor(obj) {
					var saveResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							showNewAuthor(obj);
						} else {
							alert(r.msg ? r.msg : 'We were unable to save this new #local.instanceSettings.Singular# author. Try again.');
						}
					};
					var objParams = { blogEntryID:mcblog_entryid, blogID:mcblog_blogid, memberID:obj.memberid };
					TS_AJX('ADMBLOG','addBlogAuthor',objParams,saveResult,saveResult,10000,saveResult);
				}

				function deleteAuthor(mid) {
					if (confirm('Are you sure you want to delete this author?')) {
						var idxID = -1;

						var cv = $('##blAuthorMIDList').val().split(',');
						for (var j=0; j<cv.length; j++) { 
							if(cv[j]==mid) { idxID = j; break; }
						}
						
						if (idxID != -1) {
							var cv = $('##blAuthorMIDList').val().split(',');
							cv.splice(idxID, 1);
							cv = cv.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
							$('##blAuthorMIDList').val(cv);

							if (Number(mcblog_entryid) > 0) doRemoveAuthor(mid);
							else removeAuthorRow(mid);
						}
					}
					return false;
				}
				function doRemoveAuthor(mid) {
					var deleteResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							removeAuthorRow(mid);
						} else {
							alert('We were unable to delete this #local.instanceSettings.Singular# author. Try again.');
						}
					};
					var objParams = { blogEntryID:mcblog_entryid, blogID:mcblog_blogid, memberID:mid };
					TS_AJX('ADMBLOG','removeBlogAuthor',objParams,deleteResult,deleteResult,10000,deleteResult);
				}
				function removeAuthorRow(mid) {
					$('tr##authorRow'+mid).remove();
					$.each($('tr.authorRow'), function(index,obj) {
						$(this).find('td:first').html(index + 1);
					});
				}
			</cfif>

			function selectBlogInstance() {
				var saveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						if (r.arrblogcrosspostingtargets.length) {
							var arrBID = $('##blogIDList').val().split(',').map(function (x) {
											return parseInt(x); 
										});

							$.each(r.arrblogcrosspostingtargets, function (i, item) {
								if ($.inArray(Number(item.blogid),arrBID) == -1 && item.functions.split('|').includes('AddBlog'))
									$('##newBlogInstance').append( $('<option>', { value:item.blogid, text:item.applicationinstancename }) );
							});
						}
						$('##newBlogInstance').multiselect('refresh');
						$('##divBlogInstancesLoading').hide();
						$('##btnAddBlogInstance').prop('disabled',false);
						$('##divNewBlogInstanceAddForm').show();
					} else {
						alert('We were unable to load the configured target blogs. Try again.');
					}
				};

				$('##newBlogInstance').find('option').remove();
				$('##divNewBlogInstanceAddForm').hide();
				$('##divBlogInstancesLoading').show();

				var objParams = { siteID:#arguments.event.getValue('mc_siteinfo.siteID')#, blogID: mcblog_blogid};
				TS_AJX('ADMBLOG','getAllowedCrossPostingTargets',objParams,saveResult,saveResult,10000,saveResult);
			}
			function doAddBlogInstance() {
				$('##btnAddBlogInstance').prop('disabled',true);

				var nonExistingBID = true;
				var arrBID = $('##blogIDList').val().split(',');
				var arrNewBID = $('##newBlogInstance').val() || '';

				if (arrNewBID == null || arrNewBID == '') {
					$('##btnAddBlogInstance').prop('disabled',false);
					alert('Select a blog instance');
					return false;
				}

				for (var j=0; j<arrBID.length;j++) { 
					for (var k=0; k<arrNewBID.length;k++) { 
						if(arrBID[j]==arrNewBID[k]) { nonExistingBID = false; break; }
					}
					if (!nonExistingBID) break;
				}
				if (nonExistingBID) {
					saveBlogInstance(arrNewBID);
				} else {
					$('##btnAddBlogInstance').prop('disabled',false);
					alert('Blog instances selected are already connected.');
				}
				return false;
			}
			function saveBlogInstance(arrNewBID) {
				if (Number(mcblog_entryid) > 0) doAddNewBlogInstances(arrNewBID);
				else {
					showNewBlogInstances(arrNewBID);
					appendToBlogIDList(arrNewBID);
				}
			}

			function appendToBlogIDList(arrNewBID){
				var arrBID = $('##blogIDList').val().split(',');
				for (var k=0; k<arrNewBID.length;k++) { 
					arrBID.push(arrNewBID[k]);
				}
				var bidlist = arrBID.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
				$('##blogIDList').val(bidlist);
			}
			function doAddNewBlogInstances(arrNewBID) {
				var saveResult = function(r) {
					appendToBlogIDList(arrNewBID);
					$('##btnAddBlogInstance').prop('disabled',false);
					if (r.success && r.success.toLowerCase() == 'true') {
						$('##divNewBlogInstanceAddForm').hide();
						showNewBlogInstances(arrNewBID);
					} else {
						alert('We were unable to save connected #local.instanceSettings.Singular# blog instances. Try again.')
					}
				};
				var objParams = { blogEntryID:mcblog_entryid, blogIDList:arrNewBID.join(',') };
				TS_AJX('ADMBLOG','addConnectedBlogInstances',objParams,saveResult,saveResult,10000,saveResult);
			}
			function removeBlogInstance(bid) {
				var removeResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						removeBlogInstanceRow(bid);
						removeFromBlogIDList(bid);
					} else {
						alert('We were unable to delete connected #local.instanceSettings.Singular# blog instance. Try again.')
					}
				};

				if (confirm('Are you sure you want to remove this connected blog?')) {
					var cv = $('##blogIDList').val().split(',');
					if (cv.indexOf(bid.toString()) != -1) {
						if (Number(mcblog_entryid) > 0) {
							var objParams = { blogEntryID:mcblog_entryid, blogID:bid };
							TS_AJX('ADMBLOG','removeConnectedBlogInstance',objParams,removeResult,removeResult,10000,removeResult);
						} else {
							removeBlogInstanceRow(bid);
							removeFromBlogIDList(bid);
						}
					}
				}
			}
			function removeFromBlogIDList(bid){
				var cv = $('##blogIDList').val().split(',') || [];
				var arrIndex = cv.indexOf(bid.toString());
				if(arrIndex != -1){
					cv.splice(arrIndex, 1);
					cv = cv.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
					$('##blogIDList').val(cv);
				}
			}
			function removeBlogInstanceRow(bid) {
				$('##divNewBlogInstanceAddForm').hide();
				$('tr##blInstanceRow'+bid).remove();
				$.each($('tr.blInstanceRow'), function(index,obj) {
						$(this).find('td:first').html(index + 1);
				});
			}
			
			function checkEntryAssociatedWithBlogs() {
				var checkResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						if (r.hasblogs && r.hasblogs == true)
							$('##divEntryConnectedWarning').show();
						else 
							$('##divEntryConnectedWarning').hide();
						
					} else {
						alert('We were unable to check connected #local.instanceSettings.Singular# blog instances. Try again.')
					}
				};
				var objParams = { blogEntryID:mcblog_entryid };
				TS_AJX('ADMBLOG','isBlogEntryConnectedWithOtherBlogs',objParams,checkResult,checkResult,10000,checkResult);
			}
			function initializeBlogEntryFieldControls (scope, mode) {
				if(mode === undefined) {
					mode = 'init';
				}
				
				mca_setupMultipleDatePickerFields(scope,'MCAdminDateControl');

				scope.find('.MCAdminDateControlClearLink').click(function(event){
					var linkedDateControlID = $(this).data('linkeddatecontrol');
					$('##' + linkedDateControlID).val('').change();
					event.preventDefault();
				});
			}
			function doBlogPostTypeFieldsValidate(blogFieldsWrapper) {
				var errorMsgArray = [];

				var thisInstance = $(blogFieldsWrapper);

				/*required fields*/
				var blogEntryFieldRequired = thisInstance.find('input:text[data-mcblogentryfieldisrequired="1"], select[data-mcblogentryfieldisrequired="1"], textarea[data-mcblogentryfieldisrequired="1"]').not(':hidden').not(':disabled');

				/*distinct radio, checkbox elements*/
				var radioCheckBoxElements = thisInstance.find('input:radio[data-mcblogentryfieldisrequired="1"], input:checkbox[data-mcblogentryfieldisrequired="1"]');

				var elemArr = [];
				$.each( radioCheckBoxElements, function() {
					var elemName = this.name;
					if( $.inArray( elemName, elemArr ) < 0 ){
						elemArr.push(elemName);
						blogEntryFieldRequired.push(this);
					}
				});

				var blogEntryFieldRequiredErrorMsgArray = $.map(blogEntryFieldRequired,validateMCBlogEntry_fieldIsRequired);
				Array.prototype.push.apply(errorMsgArray, blogEntryFieldRequiredErrorMsgArray);

				/*text controls offering quantity*/
				var textControlsOfferingQuantity = thisInstance.find('input[data-mcblogentryfielddisplaytypecode="TEXTBOX"][data-mcblogentryfieldofferqty="1"]').not(':hidden').not(':disabled');
				var textControlsOfferingQuantityErrorMsgArray = $.map(textControlsOfferingQuantity,validateMCBlogEntry_textControlValidQTY);
				Array.prototype.push.apply(errorMsgArray, textControlsOfferingQuantityErrorMsgArray);

				/*text controls offering decimal number*/
				var textControlDecimalCustomField = thisInstance.find('input[data-mcblogentryfielddisplaytypecode="TEXTBOX"][data-mcblogentryfielddatatypecode="DECIMAL2"]').not(':hidden').not(':disabled');
				var textControlDecimalCustomFieldErrorMsgArray = $.map(textControlDecimalCustomField,validateMCBlogEntry_textControlValidDecimal);
				Array.prototype.push.apply(errorMsgArray, textControlDecimalCustomFieldErrorMsgArray);

				/*drop empty elements*/
				var finalErrors = $.map(errorMsgArray, function(thisError){
					if (thisError.length) return thisError;
					else return null;
				});
				
				return finalErrors;
			}
			function validateMCBlogEntry_fieldIsRequired(thisField) {
				var fld = $(thisField);
				var fldName = $(thisField).attr('name');
				var displayTypeCode = fld.data('mcblogentryfielddisplaytypecode');
				var returnMsg = '';

				switch(displayTypeCode) {
					case 'TEXTBOX':
					case 'TEXTAREA':
					case 'DATE':
						if (fld.val() == '') {
							returnMsg =  fld.data('mcblogentryfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
						}
					break;
					case 'SELECT':
						if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
							returnMsg =  fld.data('mcblogentryfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
						}
					break;
					case 'RADIO':
					case 'CHECKBOX':
						if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
							returnMsg =  fld.data('mcblogentryfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
						}
					break;
				}
				return returnMsg;
			}
			function validateMCBlogEntry_textControlValidQTY(thisField) {
				var isQtyInt = function(value) {
						var n = ~~Number(value);
					return String(n) === value && n >= 0;
				};

				var fld = $(thisField);
				var qtyval = fld.val();
				if (qtyval != '') {
					var maxQtyAllowed = fld.data('mcblogentryfieldmaxqtyallowed');
					var returnMsg = '';
					if (!isQtyInt(qtyval)) {
						returnMsg = 'Enter a valid quantity for the ' +  fld.data('mcblogentryfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					} else if (parseInt(qtyval) > maxQtyAllowed) {
						returnMsg = 'Enter a quantity between 0 and ' + maxQtyAllowed + ' for the ' +  fld.data('mcblogentryfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					}
				}
				return returnMsg;
			}
			function validateMCBlogEntry_textControlValidDecimal(thisField) {
				var returnMsg = '';
				var fld = $(thisField);
				var fldval = Number(fld.val().trim());

				if (fldval != '') {
					if (fldval !== parseFloat(fldval)) {
						returnMsg = 'Enter a valid decimal number for ' + fld.data('mcblogentryfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
					}
				}
				return returnMsg;
			}
   
	<cfif local.dataStruct.getDocData.blogEntryID gt 0>
		function initCopyEntryDocLinks() {
			$('.copylinkBtn').unbind().on('click',function(event){
				event.preventDefault();
				copyToClipboard(this);
			});
		}
		function copyToClipboard(element) {
			var $temp = $("<input>");
			$("body").append($temp);
			var value = $(element).attr('href');
			$temp.val(value).select();
			document.execCommand("copy");
			$temp.remove();
			var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg\"></i><div class=\"mc-noty\">We\'ve copied the following link to your clipboard : '+ value +'</div></div>';
			
			Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
				new Noty({ 
					type:'success',
					layout:'bottomLeft', 
					theme:'bootstrap-v4', 
					text:mc_noty_msg,
					closeWith:['button','click'],
					timeout:3000 
				}).show();
			});
		}
		function confirmDeleteBEDoc(id) {
			var deleteBEDocResult = function(s) {
				if (s.success && s.success.toLowerCase() == 'true') removeEntryDocRow(id); 
				else alert('We were unable to delete this document.');
			};
			var objParams = { siteID:#attributes.event.getValue('mc_siteinfo.siteID')#, blogEntryID:mcblog_entryid, documentID:id };
			TS_AJX('ADMBLOG','deleteEntryDocument',objParams,deleteBEDocResult,deleteBEDocResult,15000,deleteBEDocResult);
		}
		function removeEntryDocRow(id) {
			$('tr##entryDocRow'+id).remove();
			if ($('##tbodyDocs tr.entryDocRow').length == 0 ) {
				$('##BlogDocumentsFlexWrapper').removeClass('has-existing')
				$('##divEntryDocsHolder').hide();
			}
		}
	</cfif>

	<cfif local.instanceSettings.enableFeaturedImage is 1 and val(local.instanceSettings.featureImageConfigID) gt 0>
		function removeFeaturedImage(rid,rtype) {
			$("button##btnDeletePostImage").prop('disabled',true);
			var deleteImageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					clearCurrentBlogImage();
				} else {
					$("button##btnDeletePostImage").prop('disabled',false);
					alert('We were unable to remove this image.');
				}
			};
			if (confirm('Are you sure you want to remove this image?')) {
				var objParams = { referenceID:rid, referenceType:rtype };
				TS_AJX('FTDIMAGES','deleteFeaturedImageUsage',objParams,deleteImageResult,deleteImageResult,20000,deleteImageResult);
			}
		}
		function clearCurrentBlogImage() {
			$('.mcblogentry_ftdimage').remove();
			$('##featuredImageFlexWrapper').removeClass('has-existing');
					
		}
	</cfif>

	var uppyDocuments = null;
	var uppyFeaturedImage = null;
	
	$(function() {
		const allowedFileTypes = ['.jpg','.gif','.png','.jpeg','.zip','.doc','.docx','.ppt','.pptx','.xls','.xlsx','.csv','.pdf','.txt'];
		let hideUppyManualButtons = #local.dataStruct.getDocData.blogEntryID gt 0 ? false : true #;

		dynamicallyLoadUppy().then((result) => {
			uppyDocuments = new Uppy({ id: 'uppyDocuments', debug: true, autoProceed: false, restrictions: {allowedFileTypes: allowedFileTypes},locale:{
				strings: {
					// removing the filename from this default message so that this message get deduped on display
					exceedsSize: 'Exceeds maximum allowed size of %{size}',
					dropPasteFiles: 'Drag and drop documents here or %{browseFiles}',
					browseFiles: 'click to browse files'
				},
			}})
			.use(XHRUpload, {
				<cfif local.dataStruct.getDocData.blogEntryID gt 0>
					endpoint: mcblog_entryUploadDocsLink + '&blBlogEntryID=#local.dataStruct.getDocData.blogEntryID#'
				<cfelse>
					endpoint: ""
				</cfif>
			})
			.on('complete', (result) => {
				<cfif local.dataStruct.getDocData.blogEntryID gt 0>
					// reload grid only upon ManualUploadButton action
					if($("##divBESaveLoading").hasClass('mc_entry_hide')){
						getBlogEntryDocs();
					}
				</cfif>
				if (!result.failed.length) uppyDocuments.cancelAll();
				else {
					result.successful.forEach((file) => {uppyDocuments.removeFile(file.id)});
				}
			})
			.use(Dashboard, {
				target: '##mcBEDocUploader',
				inline: true, isWide: false, width:'100%', height:'300px', disableThumbnailGenerator: true,
				hideUploadButton:hideUppyManualButtons,
				hideRetryButton: hideUppyManualButtons,
				proudlyDisplayPoweredByUppy:false
			});

			<cfif local.dataStruct.showFeatureImageControls>
				uppyFeaturedImage = new Uppy({
					id: 'uppyFeaturedImage',
					debug: true,
					autoProceed: false,
					allowMultipleUploadBatches: false,
					restrictions: { maxNumberOfFiles: 1, allowedFileTypes: ['image/*'] },
					locale:{
						strings: {
							// removing the filename from this default message so that this message get deduped on display
							exceedsSize: 'Exceeds maximum allowed size of %{size}',
							dropPasteFiles: 'Drag and drop image here or %{browseFiles}',
							browseFiles: 'click to browse files'
						},
				}})
				.use(XHRUpload, {
					fieldName: 'featuredImageFile',
					<cfif local.dataStruct.getDocData.blogEntryID gt 0>
						endpoint: mcblog_entryUploadFeaturedImageLink + '&blBlogEntryID=#local.dataStruct.getDocData.blogEntryID#'
					<cfelse>
						endpoint: ""
					</cfif>
				})
				.on('complete', (result) => {
					if (!result.failed.length) uppyFeaturedImage.cancelAll();
					else {
						result.successful.forEach((file) => {uppyFeaturedImage.removeFile(file.id)});
					}
				})
				.use(Dashboard, {
					target: '##mcBEFeaturedImageUploader',
					inline: true, singleFileFullScreen:true, isWide: false, width:'100%', height:'300px',
					disableThumbnailGenerator: false,
					hideUploadButton:true,
					hideRetryButton: true,
					proudlyDisplayPoweredByUppy:false,
					note: 'Supports a single image (JPG, PNG, or GIF)'
				});
			</cfif>
		});

		<cfif local.dataStruct.getDocData.blogEntryID gt 0>
			getBlogEntryDocs();
		</cfif>

		<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates eq 1>
			mca_setupDateTimePickerField('articleDate');
			mca_setupDateTimePickerField('postDate');
			mca_setupDatePickerField('expirationDate');
		</cfif>

		<cfloop query="local.dataStruct.qryGetCategoryTrees">
			<cfset local.categoryName = "s_#local.dataStruct.qryGetCategoryTrees.categoryTreeID#ID">
			$("###local.categoryName#").multiselect({ selectedList:5, classes:'catmultisel' }); 
			$("###local.categoryName#_Archived").multiselect({ selectedList:5, classes:'catmultisel' });
		</cfloop>
		$("##newBlogInstance").multiselect({ multiple:true, noneSelectedText:'Select Blogs', minWidth:200, header:false, selectedList:5 }); 

		initBlogEntryFields();
		
		<cfif local.dataStruct.getDocData.blogEntryID gt 0>
			checkEntryAssociatedWithBlogs();
		</cfif>
		onChangeArchiveDate();
		setTimeout(function(){
			$('##wellLoading').addClass('hide');
			onChangePostType();
			$('.postTypeWell,.formActionBtns').removeClass('mc_entry_hide');
		},300);
	});


	</script>
	<script type="text/javascript" src="/assets/common/javascript/clipboard.js/1.7.1/clipboard.min.js"></script>
	<style type="text/css">
		##BlogDocumentsUppyWrapper {
				display: flex;
				flex-direction: row;
			}
			##mcBEDocUploader {
				flex-grow: 1;
			}

			
			##featuredImageFlexWrapper, ##BlogDocumentsFlexWrapper {
				display: flex;
				flex-flow: row wrap;
				gap: 10px;
			}

			##featuredImageUppyWrapper, ##BlogDocumentsUppyWrapper {
				margin-top: 10px;
				flex: 1 1 100%; /* Default to 100% width */
			}

			##featuredImageExistingImageWrapper, ##divEntryDocsHolder {
				margin-top: 10px;
				flex: 1 1 500px; /* Minimum width of 500px, but can grow */
			}

			/* If ##existingImage is present, adjust ##uppyBox width */
			##featuredImageFlexWrapper.has-existing ##featuredImageUppyWrapper, ##BlogDocumentsFlexWrapper.has-existing ##BlogDocumentsUppyWrapper {
				flex: 0 0 300px; /* Fixed width of 300px */
			}

			@media (max-width: 885px) {
				##featuredImageUppyWrapper, ##BlogDocumentsUppyWrapper,##featuredImageFlexWrapper.has-existing ##featuredImageUppyWrapper, ##BlogDocumentsFlexWrapper.has-existing ##BlogDocumentsUppyWrapper {
					flex: 1 1 100%; /* Grow to 100% width on small screens when on its own row */
				}
				##featuredImageExistingImageWrapper, ##divEntryDocsHolder {
					flex: 1 1 100%; /* Grow to 100% width on small screens when on its own row */
				}

			}
		</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.commonJS#">