<cfset local.strSeminar = attributes.data.strSeminar>
<cfset local.strCredit = attributes.data.strCredit>
<cfset local.JSStructCreditInfo = attributes.data.JSStructCreditInfo>
<cfset local.qryBundles = attributes.data.qryBundles>
<cfset local.seminarSuggestions = attributes.data.seminarSuggestions>
<cfset local.speakerBio = attributes.data.speakerBio>
<cfset local.qrySWP = attributes.data.qrySWP>
<cfset local.semWeb = attributes.data.semWeb>
<cfset local.seminarID = attributes.data.seminarID>
<cfset local.isRegOpen = attributes.data.isRegOpen>

<cfset local.featuredThumbImageFullRootPath = attributes.data.featuredThumbImageFullRootPath>
<cfset local.featuredThumbImageRootPath = attributes.data.featuredThumbImageRootPath>
<cfset local.programFeaturedImagePath = "">
<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.strSeminar.qrySeminar.featureImageID) AND fileExists("#local.featuredThumbImageFullRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#")>
	<cfset local.programFeaturedImagePath = "#local.featuredThumbImageRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#">
<cfelseif len(attributes.data.defaultFeaturedImagePathsStr.defaultSWLFeaturedImagePath)>
	<cfset local.programFeaturedImagePath = attributes.data.defaultFeaturedImagePathsStr.defaultSWLFeaturedImagePath>
</cfif>
<cfif isDefined("session.mcstruct.deviceProfile.is_bot") AND session.mcstruct.deviceProfile.is_bot is 1>
	<cfset local.isBot = 1>
<cfelse>
	<cfset local.isBot = 0>
</cfif>

<cfsavecontent variable="local.liveContentJS">
	<cfoutput>
	<script language="javascript">
		<cfif local.isRegOpen and NOT local.isBot>
			function enrollNow() { self.location.href='#attributes.event.getValue('mainurl')#&panel=reg&item=SWL-#local.strSeminar.qrySeminar.seminarID#'; }
		</cfif>
		<cfloop query="local.strCredit.qryCredit">
			var #ToScript(local.JSStructCreditInfo[local.strCredit.qryCredit.authorityID],"credit_a#local.strCredit.qryCredit.authorityID#")#
		</cfloop>
		function setCreditDetails(aID) { $('##creditdetailbox ##tab'+ aID +'.tab-pane').html(eval('credit_a' + aID)); return false; }
		$(function() {
			<cfloop query="local.strCredit.qryCredit">
				setCreditDetails(#local.strCredit.qryCredit.authorityID#);
			</cfloop>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.liveContentJS#">

<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#: #encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarSubTitle)#"'>
<cfelse>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#"'>
</cfif>
<cfif len(local.programFeaturedImagePath)>
	<cfset local.shareThisTitleAndImage = '#local.shareThisTitleAndImage# st_image="#local.semweb.baseurl##local.programFeaturedImagePath#"'>
</cfif>

<cfoutput>
<div class="swProgramDetailsHeader">
	<div class="row-fluid swCatalogSeparator">
		<cfsavecontent variable="local.shareBoxContent">
			<cfoutput>
			<div class="flex swCatalogShareBox <cfif NOT len(local.programFeaturedImagePath)>pull-right</cfif>">
				<p class="mb-0"><span class="muted">Share this program:</span></p>
				<ul class="flex">
					<li><span class='st_facebook_large' #local.shareThisTitleAndImage#></span></span></li>
					<li><span class='st_linkedin_large' #local.shareThisTitleAndImage#></span></li>
					<li><span class='st_email_large' #local.shareThisTitleAndImage# st_summary="Check out this program offered by #local.semWeb.qrySWP.description#"></span></li>
					<li><span class='st_twitter_large' #local.shareThisTitleAndImage#></span></li>
				</ul>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfif local.strSeminar.qryLearningObjectives.recordCount>
			<cfsavecontent variable="local.learningObjectivesContent">
				<cfoutput>
				<p><span class="lead"><strong>What You Will Learn</strong></span></p>
				<ul class="lead">
					<cfloop query="local.strSeminar.qryLearningObjectives">
						<li>#local.strSeminar.qryLearningObjectives.objective#</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfif len(local.programFeaturedImagePath)>
			<cfsavecontent variable="local.imageAndShareProgramContent">
			<cfoutput>
				<div class="swCatalogImgBox">
					<img src="#local.programFeaturedImagePath#" alt="">
					<cfif NOT local.isBot>
						<div class="swCatalogIconBox">
							<a href="javascript:void(0);" class="swHeartIcon">
								<i class="sw_saveforlater bi <cfif attributes.data.isSavedProgram>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.seminarID#" data-swprogramtype="swl" data-swsaveforlatermode="detail"></i>
							</a>
						</div>
					</cfif>
				</div>
				#local.shareBoxContent#
			</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfif len(local.programFeaturedImagePath)>
			<div class="span3 swProgramDetailsHeader-img-section hidden-phone">
				#local.imageAndShareProgramContent#
			</div>
		</cfif>
		<div class="<cfif len(local.programFeaturedImagePath)>span9<cfelse>span12</cfif>">
			<div class="swProgramDetailsHeader-right">
				<div class="swWebinar swWebinarBorder sw-brand-label"><i class="bi bi-laptop" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWLTab#</div>
				<h1 class="swPrimary">
					#encodeForHTML(local.strSeminar.qrySeminar.seminarName)#
					<cfif NOT len(local.programFeaturedImagePath)>
						<cfif NOT local.isBot>
							<a href="javascript:void(0);" class="muted swHeartIcon">
								<i class="sw_saveforlater bi <cfif attributes.data.isSavedProgram>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.seminarID#" data-swprogramtype="swl" data-swsaveforlatermode="detail"></i>
							</a>
						</cfif>
					</cfif>
				</h1>
				<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
					<h1><small class="swPrimary">#encodeForHTML(local.strSeminar.qrySeminar.seminarSubTitle)#</small></h1>
				</cfif>
				<cfif local.strSeminar.qryLearningObjectives.recordCount>
					<div class="hidden-phone">
						#local.learningObjectivesContent#
					</div>
				</cfif>
				<cfif NOT len(local.programFeaturedImagePath)>
					#local.shareBoxContent#
				</cfif>
			</div>
		</div>
		<!--- featured image mobile view --->
		<cfif len(local.programFeaturedImagePath)>
			<div class="span12 swProgramDetailsHeader-img-section visible-phone">
				#local.imageAndShareProgramContent#
			</div>
		</cfif>
	</div>
	<cfif local.strSeminar.qryLearningObjectives.recordCount>
		<div class="visible-phone swProgramDetailsHeader-right">
			#local.learningObjectivesContent#
		</div>
	</cfif>
</div>

<cfsavecontent variable="local.jumpToOptionsContent">
	<cfoutput>
	<cfif local.strSeminar.qrySponsors.recordCount>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="sponsorsSection" data-label="Sponsors">Sponsors</a></li>
	</cfif>
	<cfif len(local.speakerBio)>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="presentersSection" data-label="Presenters">Presenters</a></li>
	</cfif>
	<cfif len(trim(local.strSeminar.qrySeminar.agenda))>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="agendaSection" data-label="Agenda">Agenda</a></li>
	</cfif>
	<cfif local.strSeminar.qrySeminar.offerCredit>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="creditSection" data-label="Credit">Credit</a></li>
	</cfif>
	<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="howToAttendSection" data-label="How to Attend">How to Attend</a></li>
	<cfif local.qrySWP.handlesOwnPayment is 0>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="moreSection" data-label="More">More</a></li>
	</cfif>
	</cfoutput>
</cfsavecontent>

<cfsavecontent variable="local.jumpToOptionsContentMobile">
	<cfoutput>
	<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="summarySection" data-label="Summary">Summary</a></li>
	#local.jumpToOptionsContent#
	</cfoutput>
</cfsavecontent>

<div class="content">
	<div class="row-fluid">
		<div class="span4 pull-right swCatalogContLightSection">
			<cfif attributes.data.hasPendingRegistrations and NOT local.isBot>
				<div class="alert alert-block text-center">
					You have <a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="swPrimary">pending registrations</a>.
				</div>
			</cfif>

			<cfset local.displayMessageOnly = false>

			<cfif NOT local.isBot>
				<cfsavecontent variable="local.registerSectionContent">
					<cfoutput>
					<cfif local.strSeminar.qrySeminar.isRegistered>
						<p class="text-center alert alert-info">You are registered for this program. Refer to confirmation e-mail for connection instructions.</p>
					</cfif>
					<cfif now() gte local.strSeminar.qrySeminar.dateStart AND local.strSeminar.qrySeminar.linkedSWODSeminarID gt 0 AND NOT local.strSeminar.qrySeminar.isRegisteredForLinkedSWOD>
						<cfset local.displayMessageOnly = true>
						<p class="text-center"><strong>Missed the program?</strong></p>
						<p class="text-center"><a href="/?pg=semwebCatalog&panel=showSWOD&seminarid=#local.strSeminar.qrySeminar.linkedSWODSeminarID#" class="swPrimary" target="_blank">Register</a> for the on-demand version.</p>
					</cfif>
					<cfif local.isRegOpen and local.strSeminar.qrySeminar.isRegistered>
						<p class="text-center"><a href="javascript:enrollNow();" class="swPrimary">Register a colleague</a> for this program.</p>
					<cfelseif local.strSeminar.qrySeminarPrices.recordcount is 0 or (local.strSeminar.qrySeminarPrices.recordcount is 1 and local.strSeminar.qrySeminarPrices.price lt 0)>
						<cfset local.displayMessageOnly = true>
						<p class="text-center">This program is not available for purchase at this time. If you feel this is an error, contact customer service at <nobr>#local.qrySWP.supportPhone#</nobr>.</p>
					<cfelseif local.isRegOpen>
						<a name="btnRegNow" href="javascript:enrollNow();" class="swPrimaryBkgd swWhite swPrimaryBorder cart-btn btn btn-primary">Register Now <i class="bi bi-chevron-right"></i></a>
					<cfelse>
						<cfset local.displayMessageOnly = true>
						<p class="text-center"><strong>Registration Closed</strong></p>
						<div class="text-center">
							<p class="text-warning">This program is not accepting registrations at this time.</P>
						</div>
					</cfif>
					<cfif NOT local.strSeminar.qrySeminar.isRegistered AND attributes.data.memberID is 0>
						<p class="text-center">
							<small><a href="javascript:animateToSection('programDetailsFAQ');" class="swPrimary">Need login details for your webinar?</a></small>
						</p>
					</cfif>
					</cfoutput>
				</cfsavecontent>

				<cfif local.displayMessageOnly>
					<div class="well">#local.registerSectionContent#</div>
				<cfelse>
					#local.registerSectionContent#
				</cfif>
			</cfif>

			<div class="visible-phone">
				<p><small><strong>JUMP TO:</strong></small></p>
				<ul class="swCatalogTabList jumpToList">
					#local.jumpToOptionsContentMobile#
				</ul>
			</div>
			<div class="swCatalogRightBlocks copyToCalendar">
				<h5 class="swPrimary"><i class="bi bi-calendar-fill" aria-hidden="true"></i>#DateFormat(local.strSeminar.qrySeminar.dspStartDate,'dddd, mmmm d, yyyy')#</h5>
				<p>
					#replace(TimeFormat(local.strSeminar.qrySeminar.dspStartDate,'h:mm TT'),":00 ","")# #UCASE(local.strSeminar.qrySeminar.dspTZ)#<br>
					#local.strSeminar.qrySeminar.dspTZStr#
				</p>
				<div class="flex swCatalogShareBox">
					<p class="mb-0"><span class="muted">Copy to Calendar:</span></p>
					<ul class="flex">
						<li><a href="/?pg=semwebCatalog&panel=downloadICal&seminarid=#local.strSeminar.qrySeminar.seminarID#&frmView=1&mode=stream"><i class="icon-windows muted tooltip-icon" title="Outlook Calendar"></i></a></li>
						<li><a href="/?pg=semwebCatalog&panel=downloadICal&seminarid=#local.strSeminar.qrySeminar.seminarID#&frmView=1&mode=stream"><i class="icon-apple muted tooltip-icon" title="iCal Calendar"></i></a></li>
						<li><a href="/?pg=semwebCatalog&panel=downloadGCal&seminarid=#local.strSeminar.qrySeminar.seminarID#&frmView=1&mode=stream" target="_blank"><i class="icon-google-plus-sign muted tooltip-icon" title="Google Calendar"></i></a></li>
					</ul>
				</div>
				<div class="muted line-height-sm"><small>Reminder: Adding to your calendar does not register you for the webinar.</small></div>
			</div>
			<cfif local.strSeminar.qrySeminarPrices.recordcount>
				<div class="swCatalogRightBlocks">
					<h5 class="swPrimary"><i class="bi bi-tag-fill" aria-hidden="true"></i>Price</h5>
					<p>
						<cfloop query="local.strSeminar.qrySeminarPrices">
							<cfif local.strSeminar.qrySeminarPrices.price gte 0>
								<cfif local.strSeminar.qrySeminarPrices.price is 0>
									#replace(local.strSeminar.qrySeminar.freeRateDisplay,".00","")#
								<cfelse>
									#replace(dollarformat(local.strSeminar.qrySeminarPrices.price),".00","")#<cfif local.strSeminar.qrySeminar.showUSD is 1> USD</cfif>
								</cfif>
								<cfif len(local.strSeminar.qrySeminarPrices.description)> <cfif local.strSeminar.qrySeminarPrices.price gt 0 or len(local.strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.strSeminar.qrySeminarPrices.description#</cfif>
								<br/>
							</cfif>
						</cfloop>
					</p>
				</div>
			</cfif>
			<div class="swCatalogRightBlocks">
				<h5 class="swPrimary"><i class="bi bi-hourglass-split" aria-hidden="true"></i>#dateDiff("n",local.strSeminar.qrySeminar.dspStartDate,local.strSeminar.qrySeminar.dspendDate)# minutes</h5>
			</div>
			<cfif local.qryBundles.recordcount>
				<div class="swCatalogRightBlocks relatedBundles">
					<h5 class="swPrimary"><i class="bi bi-basket-fill" aria-hidden="true"></i>Bundles</h5>
					<div class="muted line-height-sm"><small>Program is also part of <cfif local.qryBundles.recordcount is 1>this bundle<cfelse>these bundles</cfif>:</small></div>
					<p>
						<cfloop query="local.qryBundles">
							<a href="/?pg=semwebCatalog&panel=showBundle&bundleID=#local.qryBundles.bundleID#" class="swPrimary">#local.qryBundles.bundleName#</a><br/>
						</cfloop>
					</p>
				</div>
			</cfif>
			<div class="swCatalogRightBlocks">
				<h5 class="swPrimary"><i class="bi bi-briefcase-fill" aria-hidden="true"></i>Publisher</h5>
				<p>#local.strSeminar.qrySeminar.description#</p>
			</div>
			<cfif local.strSeminar.qryLinkedCategories.recordCount>
				<div class="swCatalogRightBlocks">
					<h5 class="swPrimary"><i class="bi bi-pencil-fill" aria-hidden="true"></i>Subjects</h5>
					<p>
						<cfloop query="local.strSeminar.qryLinkedCategories">
							#local.strSeminar.qryLinkedCategories.categoryName#<cfif local.strSeminar.qryLinkedCategories.currentRow neq local.strSeminar.qryLinkedCategories.recordCount>, </cfif>
						</cfloop>
					</p>
				</div>
			</cfif>
			<div class="swCatalogRightBlocks" id="programDetailsFAQ">
				<h5 class="swPrimary"><i class="bi bi-question-circle-fill" aria-hidden="true"></i>Questions</h5>
				<p>
					<cfif NOT local.strSeminar.qrySeminar.isRegistered AND attributes.data.memberID is 0>
						<p><strong>Already Registered?</strong> Refer to confirmation e-mail for connection instructions.</p>
					</cfif>
					<p>
						<cfif attributes.data.memberID is 0>
							<strong>Something else?</strong> 
						</cfif>
						Please consult our <a href="/?pg=semwebCatalog&panel=showFAQ" class="swPrimary"> FAQ page</a>. 
						If you're unable to find the answer you need, please call #local.qrySWP.supportPhone# (#local.qrySWP.supportHours#) or <a href="mailto:#local.qrySWP.supportEmail#" class="swPrimary">e-mail customer service</a>.
					</p>
				</p>
			</div>
		</div>
		<div class="span8 content-left-section">
			<div class="hidden-phone">
				<p><small><strong>JUMP TO:</strong></small></p>
				<ul class="swCatalogTabList jumpToList">
					#local.jumpToOptionsContent#
				</ul>
			</div>
			<div class="swCatalogSubBlocks swDetailSummary">
				<h3 class="swPrimary">Summary</h3>
				<div class="expand expandSection" id="summarySection">
					<p>
						#local.strSeminar.qrySeminar.SeminarDesc#
						<div class="swProgramCode muted"><strong>#local.strSeminar.qrySeminar.programCode#</strong></div>
					</p>
					<a href="javascript:void(0);" class="swPrimary swAExpandSummary">Expand summary</a>
				</div>
			</div>
			<cfif local.strSeminar.qrySponsors.recordCount>
				<cfset local.qrySponsors = local.strSeminar.qrySponsors>
				<div class="swCatalogSubBlocks swDetailSponsors" id="sponsorsSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Sponsors</h3>
					<cfinclude template="/views/semwebCatalog/responsive/swProgramDetailsSpeakersCommon.cfm">
				</div>
			</cfif>
			<cfif len(local.speakerBio)>
				<div class="swCatalogSubBlocks swDetailSpeakers" id="presentersSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Presenters</h3>
					#local.speakerBio#
				</div>
			</cfif>
			<cfif len(trim(local.strSeminar.qrySeminar.agenda))>
				<div class="swCatalogSubBlocks swDetailAgenda" id="agendaSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Agenda</h3>
					<p>#replace(local.strSeminar.qrySeminar.agenda,"#chr(13)##chr(10)#","<br/>","ALL")#</p>
				</div>
			</cfif>
			<cfif local.strSeminar.qrySeminar.offerCredit>
				<div class="swCatalogSubBlocks swDetailCredit" id="creditSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Credit</h3>
					<cfif local.strCredit.qryCreditDistinct.recordcount>
						<p>
							If applicable, you may obtain credit in multiple jurisdictions simultaneously for this program (see pending/approved list below).
							<cfif local.strSeminar.qrySeminar.offerCertificate>
								Registrants in jurisdictions not listed below will receive a Certificate of Attendance/Completion that may or may 
								not meet credit requirements in other jurisdictions. 
							</cfif>
							Where applicable, credit will be only awarded to a paid registrant attending the live program at their own computer and phone.
						</p>
						<cfif local.strCredit.qryCreditDistinct.recordCount gt 1>
							<p>Click on jurisdiction for specific details:</p>
						</cfif>
						<ul id="tab" data-toggle="buttons-radio" class="swCatalogTabList">
							<cfloop query="local.strCredit.qryCreditDistinct">
								<li <cfif local.strCredit.qryCreditDistinct.currentRow eq 1>class="active"</cfif>><a href="##tab#local.strCredit.qryCreditDistinct.authorityID#" class="swPrimary" data-toggle="tab">#local.strCredit.qryCreditDistinct.authorityCode#</a></li>
							</cfloop>
						</ul>
						<div class="tab-content" id="creditdetailbox">
							<cfloop query="local.strCredit.qryCreditDistinct">
								<div class="tab-pane <cfif local.strCredit.qryCreditDistinct.currentRow eq 1>active</cfif>" id="tab#local.strCredit.qryCreditDistinct.authorityID#"></div>
							</cfloop>
						</div>
					<cfelse>
						<p>
							This program <i>has not been submitted</i> for credit in any jurisdiction. 
							<cfif local.strSeminar.qrySeminar.offerCertificate>
								Registrants will receive a Certificate of Attendance/Completion that may or may not meet credit 
								requirements in various jurisdictions. 
							</cfif>
							Where applicable, credit will be only awarded to a paid registrant attending the live program at their 
							own computer and phone.
						</p>
					</cfif>
				</div>
			</cfif>
			<div class="swCatalogSubBlocks swDetailAttend" id="howToAttendSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
				<h3 class="swPrimary">How to Attend</h3>
				<p>
					Join the live program from your office, home, or hotel room using a computer with high speed internet. 
					You may ask questions, participate in evaluations, and post comments from your computer during the program. 
					Please note that credit (if available) is only provided to registered attendees participating at their 
					own computer and phone. Simple instructions with a link to the program will be sent when you register 
					and again the day before the webinar.
				</p>
			</div>
			<cfif local.qrySWP.handlesOwnPayment is 0>
				<div class="swCatalogSubBlocks swDetailMore" id="moreSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">More</h3>
					<p>
						<strong><span class="muted">Refund Policy</span></strong><br>
						SeminarWeb and #local.semWeb.orgname# programs are non-refundable.
					</p>
					<p>
						<strong><span class="muted">Privacy Statement</span></strong><br>
						We respect and are committed to protecting your privacy. <a href="javascript:sw_showPrivacyStatement();" class="swPrimary">Read Statement</a>.
					</p>
				</div>
			</cfif>
		</div>
	</div>
	<cfif local.seminarSuggestions.recordcount>
		<div class="swOtherPrgms">
			<h4 class="muted">YOU MAY ALSO BE INTERESTED IN...</h4>
			<div class="row-fluid">
				<cfloop query="local.seminarSuggestions">
					<cfset local.otherPgmsFeaturedThumbImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
					<cfset local.otherPgmsFeaturedThumbImageRootPath = "/userassets/#LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
					<cfset local.thisProgramFeaturedImagePath = "">
					<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.seminarSuggestions.featureImageID) AND fileExists("#local.otherPgmsFeaturedThumbImageFullRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#")>
						<cfset local.thisProgramFeaturedImagePath = "#local.otherPgmsFeaturedThumbImageRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#">
					<cfelseif local.seminarSuggestions.seminarType eq "On-Demand" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath)>
						<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath>
					<cfelseif local.seminarSuggestions.seminarType eq "Webinar" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath)>
						<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath>
					</cfif>

					<div class="span4">
						<div class="swCatalogInnerPrgms <cfif NOT len(local.thisProgramFeaturedImagePath)>no-image</cfif>">
							<div class="row-fluid">
								<cfif len(local.thisProgramFeaturedImagePath)>
									<div class="span3">
										<img src="#local.thisProgramFeaturedImagePath#" alt="">
									</div>
								</cfif>
								<div class="<cfif len(local.thisProgramFeaturedImagePath)>span9<cfelse>span12</cfif>">
									<h6>
										<cfif local.seminarSuggestions.seminarType eq "On-Demand">
											<cfset local.programLink = "?pg=semwebCatalog&panel=showSWOD&seminarid=#local.seminarSuggestions.seminarID#">
										<cfelse>
											<cfset local.programLink = "?pg=semwebCatalog&panel=showLive&seminarid=#local.seminarSuggestions.seminarID#">
										</cfif>
										<a href="#local.programLink#" class="swPrimary" target="_blank">#encodeForHTML(local.seminarSuggestions.seminarName)#</a>
									</h6>
									<div class="flex">
										<cfif local.seminarSuggestions.seminarType eq "On-Demand">
											<p class="mb-0 swOnDemand small"><i class="bi bi-play-circle" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWODTab#</p>
										<cfelse>
											<p class="mb-0 swWebinar small"><i class="bi bi-laptop" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWLTab#</p>
										</cfif>
										<p class="mb-0 muted small tooltip-icon" title="<cfif local.seminarSuggestions.seminarType eq "On-Demand">Published on<cfelse>Being Held On </cfif> #dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#">
											<i class="bi bi-calendar-fill" aria-hidden="true"></i><span class="text-nowrap">#dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#</span>
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					<cfif local.seminarSuggestions.currentRow mod 3 is 0 and local.seminarSuggestions.currentRow neq local.seminarSuggestions.recordCount>
						</div><div class="row-fluid">
					</cfif>
				</cfloop>
			</div>
		</div>
	</cfif>
</div>
</cfoutput>