<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">
        <cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?#getBaseQueryString(false)#";
            variables.redirectlink = "/?pg=SpeakerProposalForm";
            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

            variables.submissionTypeFieldSetUID = "E71FFE7A-BBDA-4ED1-8E45-DCC7BE3240F3";
            variables.personalInfoFieldSetUID = "C6E5A1C8-89B3-440D-A1EB-5813FED7D2A8";
            variables.proposalInfoFieldSetUID = "B980B2C6-207C-4879-9F60-D79137979D84";
            variables.demographicFieldSetUID = "56C102AC-BAE6-4F29-8195-A761D9AE17F8";

            local.arrCustomFields = [];
            local.tmpField = { name="IntroText", type="CONTENTOBJ", desc="Form Intro Text", value="CAALA is always looking for new speakers for the many educational programs it holds throughout the year. For consideration, please submit written proposals to speak at CAALA webinars/programs or CAALA Vegas at any time. Submissions will be distributed to the various Education Planning Committees for review." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="StaffConfirmationEmail", type="STRING", desc="Organization recipient email address", value="<EMAIL>" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="formNameDisplay", type="STRING", desc="Form name on top of the page", value="Speaker & Program Proposal Form" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);
            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmSpeakerProposal',
                formNameDisplay= variables.strPageFields.formNameDisplay,
                orgEmailTo= variables.strPageFields.StaffConfirmationEmail,
                memberEmailFrom= variables.strPageFields.StaffConfirmationEmail
            ));

            variables.origMemberID = variables.useMID;
            variables.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=variables.orgid, includeTags=0);
            variables.qryOrgWebsiteTypes = application.objOrgInfo.getOrgWebsiteTypes(orgID=variables.orgid);

            if(local.formAction neq "showLookup"){
                if(structKeyExists(session, "formFields")  AND structKeyExists(session.formFields,"memberid")){
                    variables.useMID = session.formFields.memberid;
                } else {
                    variables.useMID = arguments.event.getTrimValue('memberid',0);
                }
                if(structKeyExists(session, "formFields") AND structKeyExists(session.formFields, "origMemberID") and int(val(session.formFields.origMemberID)) gt 0){
                    variables.origMemberID = session.formFields.origMemberID;
                }              
            }else if(session.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);

            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin');  
            }else{
                switch (local.formAction) {
                    case "processProposalInfo":
                        switch (processProposalInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                location("#variables.baselink#&fa=confirm", "false");
                                break;
                            case "spam":
                                local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "confirm":
                        if(structKeyExists(session,"proposal")){
                            local.returnHTML = showConfirmation();
                            structDelete(session,"proposal");
                        } else {
                            location("#variables.baselink#", "false");
                        }
                        break;
                    default:
                        if(structKeyExists(session, "formFields")) structDelete(session, "formFields");
                        local.returnHTML = showLookupOrMemberInfo();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");        
        </cfscript>
    </cffunction>

    <cffunction name="showLookupOrMemberInfo" access="private" output="false" returntype="string">
        <cfset var local = structNew()>
        
        <cfset local.useMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData.memberID = session.formFields.memberID>
			<cfset variables.origMemberID = session.formFields.origMemberID>
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
        <cfset local.submissionTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.submissionTypeFieldSetUID, mode="collection", strData=local.strData)>
        <cfset local.personalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInfoFieldSetUID, mode="collection", strData=local.strData)>
        <cfset local.proposalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.proposalInfoFieldSetUID, mode="collection", strData=local.strData)>
        <cfset local.demographicFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demographicFieldSetUID, mode="collection", strData=local.strData)>
        <cfset local.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=variables.formName)>
        <cfsavecontent variable="local.headCode">
			<cfoutput>          
            <style type="text/css">
                .customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
                .frmContent{ padding:10px; background:##ddd }
                .frmRow1{ background:##fff; }
                .frmRow2{ background:##dbdedf; }
                .frmRow3{ background:##999999;}
                .frmText{ font-size:8pt; color:##000; }
                .frmButtons{ padding:5px 0; border-top:3px solid ##03608B; border-bottom:3px solid ##03608B; }
                .TitleText { font-family:Tahoma; font-size:16pt; color:##000; font-weight:bold; padding-bottom: 15px; padding-top: 15px;}
                .CPSection{ border:0.5px solid ##56718c; margin-bottom:15px; }
                .CPSectionTitle { font-size:12pt; height:auto; font-weight:bold; color:##000; padding:10px 5px; background:##bbb; }
                .CPSectionContent{ padding:10px 5px; }
                .subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ececec; }
                .subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##ececec; }
                .subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##ececec;}
                .subCPSectionTitle { font-size:9pt; font-weight:bold; }
                .info{ font-style:italic; font-size:7pt; color:##777; }
                .small{ font-size:7pt;}
                .r { text-align:right; }
                .l { text-align:left; }
                .c { text-align:center; }
                .i { font-style:italic; }
                .b { font-weight:bold; }
                .P{padding:10px;}
                .PL{padding-left:10px;}
                .PR{padding-right:10px;}
                .PB{padding-bottom:10px;}
                .PT{padding-top:10px;}
                .BB { border-bottom:0.5px solid ##666; }
                .BL { border-left:1px solid ##666; }
                .BT { border-top:1px solid ##666; }
                .block { display:block; }
                .black{ color:##000000; }
                .red{ color:##ff0000; }
                .msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
                .msgSubHeader{background:##dddddd;}
                .tsAppBodyText { color:##000000;}
                select.tsAppBodyText{color:##666;}
                .alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
                .paymentGateway{ background-color:##ededed; padding:10px; }
                ##memberNumber{ display:inline-block; width:140px; }
                .form-horizontal .control-group { margin-bottom: 5px; }
                .form-horizontal .control-label { width: 320px; }
                .form-horizontal .controls {  margin-left: 340px; }
                textarea { line-height: 25px; }
                input.tsAppBodyText { font-family: Verdana, Arial, Helvetica, sans-serif; color: ##666; }
                .frmText { font-size:11pt; }
                textarea { width: 216px; }
                .table th, .table td { border-top: 1px solid ##060606; word-break: break-word; }
                form .tsAppSectionContentContainer tr { line-height: 30px; }
                form .tsAppSectionContentContainer tr td { padding:5px 0px; }
                .proposalInfoFieldSetHolder .tsAppSectionContentContainer tr td:nth-child(2) { width:13%; }
                .demographicFieldSetHolder .tsAppSectionContentContainer tr td:nth-child(2) { width:25%; }
                .acntLookUpBtn,.acntLookUpMessage{ display:inline!important; float: right; width: 48%!important; }
                .acntLookUpBtn{	margin-right: 5px; } .acntLookUpMessage .span12{ margin-left:0px !important; }
                .acntLookUpBtn { position: absolute; width: 50%; height: 100%; min-height: 100%; }
                .centerit { position: absolute; top: 50%; width: 100%; text-align: center; }
                .centerit button { position: relative; top: -35px; }
                .center-holder{ position: relative; }
                .acntLookUpMessage p{font-size:15px;}
                .acntLookUpMessage { margin-left: 48%!important; }
                @media screen and (max-width: 979px){
                    .input-xxlarge { width:100% !important; }
                } 
                @media screen and (max-width: 767px){ 
                .form-horizontal .control-label{width: 100%; text-align: left;}
                .form-horizontal .controls{margin-left:0px}
                .tsAppSectionContentContainer table td { display: block; margin-bottom: 0px; }
                .tsAppSectionContentContainer table td:nth-child(1), .tsAppSectionContentContainer table td:nth-child(2), .tsAppSectionContentContainer table td:nth-child(3) { display: inline; margin: 0; padding: 0; }
                .tsAppSectionContentContainer table td:nth-child(4) { display: block; margin: 0; padding: 0; }
                .tsAppSectionContentContainer div.ui-multiselect-menu{width:auto!important;}
                .tsAppSectionContentContainer input[type="text"] { width: 95%!important; }
                .tsAppSectionContentContainer button.ui-multiselect,.tsAppSectionContentContainer select { width: 220px!important; }
                }
                .centerit { position: absolute; top: 50%; width: 100%; } .centerit button { position: relative; top: -15px; height: 30px; }
                .center-holder{ position: relative; } .accntPopup > div:last-child > div.span12{margin-left: 2.564102564102564%;}
                .accntPopup > div:first-child > div { left:40%; position:absolute; }
                @media screen and (min-width: 0px){
                .acntLookUpBtn { margin-top: 0px!important; }
                }
                @media screen and (min-width: 359px) and (max-width: 368px){
                .acntLookUpBtn button{ font-size:13px; }
                }
                @media screen and (max-width: 359px){
                .acntLookUpBtn button{ font-size:11px; }
                }
                .submissionTypeFieldSetHolder table > tbody > tr:nth-child(1){ display:block; }
                .submissionTypeFieldSetHolder table > tbody > tr:first-child > td:nth-child(4){ float: left; line-height: 22px; margin-left: 20px; }
                .submissionTypeFieldSetHolder table > tbody > tr:first-child > td:nth-child(4) input[type=checkbox]{ margin-top: -1px; margin-right: 5px;}
            </style>
            #variables.pageJS# 
			<script type="text/javascript">
                $(document).ready(function(){
                    $('form .tsAppSectionContentContainer tr td').removeAttr('nowrap');
					
					var ss  = $('.submissionTypeFieldSetHolder  table > tbody > tr:first-child > td:nth-child(4) input[type=checkbox]');
					
					arrSubmissionDataSortOrder = ["I am interested in being a SPEAKER","I am interested in being a MODERATOR","I am recommending a speaker","I am recommending a program topic"];
					
					var arrSubmissionData = [];
					ss.each(function(){
						radioId = $(this).attr('id');
						radioText = document.getElementById(radioId).nextSibling.textContent;
						
						objSubmissionData = {"radioId":radioId,"radioObj":$(this),"radioText":radioText};
						arrSubmissionData.push(objSubmissionData);
					});
					$('.submissionTypeFieldSetHolder  table > tbody > tr:first-child > td:nth-child(4)').html('');					
					
					$(arrSubmissionDataSortOrder).each(function(key,val){
						
						dataIdx = Object.keys(arrSubmissionData).filter(function(key1) {
							return arrSubmissionData[key1].radioText == val;
						})
						if(dataIdx.length >= 1){
							dataIdx = dataIdx[0];
							textValue = arrSubmissionData[dataIdx].radioText;
							switch(arrSubmissionData[dataIdx].radioText){
								case 'I am interested in being a MODERATOR':
									textValue = 'I am interested in being a <b>MODERATOR</b> <a  target="blank" href="/docDownload/1691202">(click here for moderator guidelines)</a>';
								break;
								case 'I am interested in being a SPEAKER':
									textValue = 'I am interested in being a <b>SPEAKER</b>';
								break;
							}
							$('.submissionTypeFieldSetHolder  table > tbody > tr:first-child > td:nth-child(4)').append(arrSubmissionData[dataIdx].radioObj[0].outerHTML+" "+textValue+"<br/>");
							arrSubmissionData.splice(dataIdx,1);
						}						
						
					});
					
					$(arrSubmissionData).each(function(key,val){
						$('.submissionTypeFieldSetHolder  table > tbody > tr:first-child > td:nth-child(4)').append(val.radioObj[0].outerHTML+" "+val.radioText+"<br/>");
					});
                });

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
                function assignMemberData(memObj){
                    var thisForm = document.forms["#variables.formName#"];
                    var er_change = function(r) {
                        var results = r;
                        if( results.success ){

                            $('##memberID').val(results.memberid);
                            $('##origMemberID').val(results.memberid);
                            $('##memberNumber').val(results.membernumber);
                            $.each(results, function(key,value) {
                                if(typeof $('##m_'+key) != 'undefined' && $('##m_'+key).length){
                                    $('##m_'+key).val(value);
                                }
                            }); 

                            <cfloop query="variables.qryOrgWebsiteTypes">
                                if(results.websitetypeid == #variables.qryOrgWebsiteTypes.websiteTypeID# && typeof  $('input[id=mw_#variables.qryOrgWebsiteTypes.websiteTypeID#_website') != 'undefined' &&  $('input[id=mw_#variables.qryOrgWebsiteTypes.websiteTypeID#_website').length){
                                    $('input[id=mw_#variables.qryOrgWebsiteTypes.websiteTypeID#_website').val(results.website);
                                }
                            </cfloop>

                            <cfloop query="variables.qryOrgAddressTypes">
                                for (var i = 0; i<results.phoneobj.length; i++) {
                                    if($('##mp_#variables.qryOrgAddressTypes.addressTypeID#_'+results.phoneobj[i].phonetypeid.toString()) != undefined){
                                        $('##mp_#variables.qryOrgAddressTypes.addressTypeID#_'+results.phoneobj[i].phonetypeid.toString()).val(results.phoneobj[i].phone);
                                    }
                                }
                            </cfloop>

                            if(results.addresstypeid.toString().length){
                                $('##ma_'+results.addresstypeid+'_address1').val(results.address1);
                                $('##ma_'+results.addresstypeid+'_city').val(results.city);
                                $('##ma_'+results.addresstypeid+'_stateprov').val(results.stateid);
                                $('##ma_'+results.addresstypeid+'_postalcode').val(results.postalcode);
                                $('##me_'+results.emailtypeid+'_email').val(results.email);
                            }

                            var isNewRecord = $('##isNewRecord').val();
                            if (isNewRecord == '0') {
                                $('##memberNumber_old').val(results.membernumber);
                            }
                            document.getElementById('formToFill').style.display = '';
                        }
                    };
                    $('##isNewRecord').val(memObj.isNewRecord);
                    var objParams = { memberNumber:memObj.memberNumber};
                    TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);

                    showCaptcha();
                }
                function selectMember() {
                    hideAlert();
                    var windowWidth = $(window).width();
                    var _popupWidth = 550;
                    if(windowWidth < 585) {
                        _popupWidth = windowWidth - 30;
                    } 
                    $.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
                }			
                function resizeBox(newW,newH) { 
                    var windowWidth = $(window).width();
                    var _popupWidth = newW;
                    if(windowWidth < 585) {
                        _popupWidth = windowWidth - 30;
                    }
                    $.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
                }
                function addMember(memObj) {
                    $.colorbox.close();
                    assignMemberData(memObj);
                }
                function loadMember(memNumber){
                    var objParams = { memberNumber:memNumber };
                    assignMemberData(objParams);
                }
                function checkCaptchaAndValidate(stepNum){
                    var thisForm = document.forms["#variables.formName#"];
                    var status = false;
                    var captcha_callback = function(captcha_response){
                        if (captcha_response.response && captcha_response.response != 'success') {
                            status = false;
                        } else {
                            status = true;
                        }
                    }
                    if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
                        alert('Please enter the correct code shown in the graphic.');
                        return false;
                    } else {
                        #local.captchaDetails.jsvalidationcode#
                    }
                    if(status){
                        return _FB_validateForm(stepNum);
                    } else {
                        alert('Please enter the correct code shown in the graphic.');
                        return false;
                    }
                }
                function _FB_validateForm(stepNum) {
                    var _CF_this = document.forms['#variables.formName#'];
                    hideAlert();
                    
                    var arrReq = new Array();

                    #local.submissionTypeFieldSet.jsvalidation#
                    #local.personalInfoFieldSet.jsvalidation#
                    #local.proposalInfoFieldSet.jsvalidation#
                    #local.demographicFieldSet.jsvalidation#
                    
                    if (arrReq.length > 0) {
                        var msg = 'Please address the following issues with your application:<br/>';
                        for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                        showAlert(msg);
                        $('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
                        return false;
                    }
                    return true;
                }
                function hideAlert() { $('##issuemsg').html('').hide(); };
                function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert').show(); };
                function showAlertDone(msg) { $('##issuemsg').html(msg).attr('class','success').show(); };

				$(document).ready(function() {	
                    <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                    loadMember('#local.useMemberNumber#');
                    </cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
        <cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
            <div id="customPage" class="row-fluid">
                <div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="form-horizontal" onsubmit="return checkCaptchaAndValidate(1);">
                    <cfinput type="hidden" name="fa" id="fa" value="processProposalInfo">
                    <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                    <cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                    <input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
                    <div class="row-fluid" id="FormTitleId">
                        <div class="span12 c">
                            <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                        </div>
                    </div>

                    <cfif len(variables.strPageFields.IntroText)>
                        <div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.IntroText#</div></div>
                    </cfif>

                    <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                        <div class="CPSection row-fluid">
                            <div class="CPSectionTitle BB row-fluid">Account Lookup / Create New Account</div>
                            <div class="frmRow1 row-fluid" style="padding:10px;">
                                <div class="row-fluid center-holder">

                                    <div class="span4 acntLookUpBtn c">
                                        <div class="centerit" id="associatedMemberIDSelect">
                                            <button name="btnAddAssoc" type="button" id="btnAddAssoc" onClick="selectMember()">Account Lookup</button>
                                        </div>
                                    </div>
                                    <div class="span8 acntLookUpMessage frmText pull-right">
                                        <div class="span12">Click the <span class="b">Account Lookup</span> button to the left.</div>
                                        <div class="span12">Enter the search criteria and click <span class="b">Continue</span>.</div>
                                        <div class="span12">If you see your name, please press the <span class="b">Choose</span> button next to your name.</div>
                                        <div class="span12">If you do not see your name, click the <span class="b">Create an Account</span> link.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </cfif>

                    <div id="formToFill" style="display:none;">
						<span class="submissionTypeFieldSetHolder">
                            <div class="CPSection row-fluid">
                                <div class="CPSectionTitle BB row-fluid">#local.submissionTypeFieldSet.fieldSetTitle#</div>								
                                <div class="tsAppSectionContentContainer submissionTypeFieldSet">								
                                    #local.submissionTypeFieldSet.fieldSetContent#									
                                </div>
                            </div>
                        </span>
                        <span class="personalInfoFieldSetHolder">
                            <div class="CPSection row-fluid">
                                <div class="CPSectionTitle BB row-fluid">#local.personalInfoFieldSet.fieldSetTitle#</div>								
                                <div class="tsAppSectionContentContainer">										
                                    #local.personalInfoFieldSet.fieldSetContent#									
                                </div>
                            </div>
                        </span>
                        <span class="proposalInfoFieldSetHolder">
                            <div class="CPSection row-fluid">
                                <div class="CPSectionTitle BB row-fluid">#local.proposalInfoFieldSet.fieldSetTitle#</div>								
                                <div class="tsAppSectionContentContainer">										
                                    #local.proposalInfoFieldSet.fieldSetContent#									
                                </div>
                            </div>
                        </span>
                        <span class="demographicFieldSetHolder">
                            <div class="CPSection row-fluid">
                                <div class="CPSectionTitle BB row-fluid">#local.demographicFieldSet.fieldSetTitle#</div>								
                                <div class="tsAppSectionContentContainer">										
                                    #local.demographicFieldSet.fieldSetContent#									
                                </div>
                            </div>
                        </span>
                        <div class="row-fluid">
							<div class="tsAppSectionContentContainer tabContentWrap" >
								#local.captchaDetails.htmlContent#
							</div>
						</div>
                        <div class="row-fluid">
                            <div class="span12">
                                <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                            </div>
                        </div>
                    </div>
                </cfform>
            </div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processProposalInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>	

        <cfset local.response = "failure">
        <cfset local.strData = arguments.rc>

        <cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.strData.mc_siteinfo = arguments.rc.mc_siteinfo>
        
        <cfset local.strData.memberID = arguments.rc.memberID>
        <cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData)>
        <cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
	
		<cfset local.objSaveMember.setRecordType(recordType='Speaker & Program Proposal')>

		<cfset local.strResultData = local.objSaveMember.saveData(runImmediately=1)>

		<cfset session.formFields.origMemberID = variables.useMID>
        <cfset session.formFields.memberID = local.strResult.memberID>

        <cfif local.strResult.success>
            <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
            <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=local.strResult.memberID, orgID=variables.orgID)>

            <cfset local.response = "success">
            
            <cfset local.strData.memberID = variables.useMID>
			
			<cfset local.submissionTypeFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.submissionTypeFieldSetUID, mode="confirmation", strData=local.strData)>
            <cfset local.personalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInfoFieldSetUID, mode="confirmation", strData=local.strData)>
            <cfset local.proposalInfoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.proposalInfoFieldSetUID, mode="confirmation", strData=local.strData)>
            <cfset local.demographicFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.demographicFieldSetUID, mode="confirmation", strData=local.strData)>
            
            <cfsavecontent variable="local.proposal">
                <cfoutput>              
                    #local.submissionTypeFieldSet.fieldSetContent#
					
                    #local.personalInfoFieldSet.fieldSetContent#
                    
                    #local.proposalInfoFieldSet.fieldSetContent#
                
                    #local.demographicFieldSet.fieldSetContent#

                </cfoutput>
            </cfsavecontent>

            <cfsavecontent variable="local.specialText">
                <cfoutput>
                <div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
                <div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
                <br/>
                </cfoutput>
            </cfsavecontent>

            <cfsavecontent variable="local.mailContent">
                <cfoutput>
                    <p>Here are the details of your proposal:</p>	
                    #local.specialText#
                    #local.proposal#	
                </cfoutput>
            </cfsavecontent>

            <!--- email to member --->
            <cfif application.MCEnvironment eq "production">
                <cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
            </cfif>

            <cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                emailfrom={ name="", email=variables.memberEmail.from },
                emailto=[{ name="", email=variables.memberEmail.to }],
                emailreplyto=variables.ORGEmail.to,
                emailsubject=variables.memberEmail.subject,
                emailtitle="#local.strData.mc_siteinfo.sitename# - #variables.formNameDisplay#",
                emailhtmlcontent=local.mailContent,
                siteID=variables.siteID,
                memberID=val(variables.useMID),
                messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                sendingSiteResourceID=this.siteResourceID
            )>
            <cfset local.emailSentToUser = local.responseStruct.success>

            <cfif application.MCEnvironment eq "production">
                <cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
            <cfelse>
                <cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
            </cfif>

            <!--- email to association --->
            <cfsavecontent variable="local.specialText">
                <cfoutput>
                <div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
                <div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
                <cfif NOT local.emailSentToUser>
                    <div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
                </cfif>
                <br/>
                </cfoutput>
            </cfsavecontent>

            <cfsavecontent variable="local.mailContent">
                <cfoutput>
                    #local.specialText#
                    <p>Here are the details of the application:</p>

                    #local.proposal#	
                </cfoutput>
            </cfsavecontent>

            <cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your proposal.", emailContent=local.mailContent)>

            <!--- relocate to message page --->
            <cfset session.proposal = local.proposal />
		</cfif>	
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

        <cfsavecontent variable="local.headCode">
			<cfoutput>          
           
            <style type="text/css">
                .customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
               
                .frmContent{ padding:10px; background:##ddd }
                .frmRow1{ background:##fff; }
                .frmRow2{ background:##dbdedf; }
                .frmRow3{ background:##999999;}
                .frmText{ font-size:8pt; color:##000; }
                .frmButtons{ padding:5px 0; border-top:3px solid ##03608B; border-bottom:3px solid ##03608B; }
               
                .TitleText { font-family:Tahoma; font-size:16pt; color:##000; font-weight:bold; padding-bottom: 15px; padding-top: 15px;}
                .CPSection{ border:0.5px solid ##56718c; margin-bottom:15px; }
                .CPSectionTitle { font-size:12pt; height:auto; font-weight:bold; color:##000; padding:10px 5px; background:##bbb; }
                .CPSectionContent{ padding:10px 5px; }
                .subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ececec; }
                .subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##ececec; }
                .subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##ececec;}
                .subCPSectionTitle { font-size:9pt; font-weight:bold; }
               
                .info{ font-style:italic; font-size:7pt; color:##777; }
                .small{ font-size:7pt;}
               
                .r { text-align:right; }
                .l { text-align:left; }
                .c { text-align:center; }
                .i { font-style:italic; }
                .b { font-weight:bold; }
               
                .P{padding:10px;}
                .PL{padding-left:10px;}
                .PR{padding-right:10px;}
                .PB{padding-bottom:10px;}
                .PT{padding-top:10px;}
               
                .BB { border-bottom:0.5px solid ##666; }
                .BL { border-left:1px solid ##666; }
                .BT { border-top:1px solid ##666; }
                .block { display:block; }
                .black{ color:##000000; }
                .red{ color:##ff0000; }
                <!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
                .msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
                .msgSubHeader{background:##dddddd;}
                .tsAppBodyText { color:##000000;}
              
                input.tsAppBodyText {
                    font-family: Verdana, Arial, Helvetica, sans-serif;
                    color: ##666;
                }
                .frmText { font-size:11pt; }
                .table th, .table td { border-top: 1px solid ##060606; word-break: break-word; }

                @media screen and (max-width: 979px){
                    .input-xxlarge { width:100% !important; }
                }
                @media screen and (max-width: 767px){
                    .form-horizontal .control-label{width: 100%; text-align: left;}
                    .form-horizontal .controls{margin-left:0px}
                } 
            </style>
            <script>
                $(document).ready(function(){
                $('.tsAppSectionContentContainer tr td').removeAttr('nowrap');
                });
            </script>
			</cfoutput>
		</cfsavecontent>
        <cfhtmlhead text="#local.headCode#">
            
        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <div class="row-fluid" id="FormTitleId">
                    <div class="span12 c">
                        <span class="TitleText" style="margin-bottom: 40px;">#variables.formNameDisplay#</span>
                    </div>
                </div>
                
                <div class="tsAppSectionContentContainer">
                    <b>Thank you for submitting proposal</b>
                    <p>Here are the details of your proposal:</p>						
                    #session.proposal#
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
                     <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent> 