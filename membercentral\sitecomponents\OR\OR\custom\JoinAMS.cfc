<cfcomponent extends="model.customPage.customPage" output="true">
	<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
	
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
    	<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			variables.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
			local.arrCustomFields = [];
			
			local.tmpField = { name="SubTypeTest", type="STRING", desc="Check for existing accepted/active/billed subscriptions of this type", value="********-F04A-4D57-AA31-CD8D61EA74AC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
						
			local.tmpField = { name="JoinFormTitle", type="STRING", desc="Join Form Title", value="Membership Application Form" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
			
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
						
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>,<EMAIL>,<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="MembershipCategoriesDesc", type="CONTENTOBJ", desc="Membership Category Description", value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for credit card", value="ORCCCIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="ProfileCodeCheck", type="STRING", desc="pay profile code for check", value="OTLA Bank Draft" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="ConfirmationMessage", type="STRING", desc="Message to display on top of confirmation page", value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	

			local.tmpField = { name="RegularMemberNewAdmitteeRate", type="STRING", desc="Regular Member: New Admittee Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RegularMemberLegalAidRate", type="STRING", desc="Regular Member: Legal Aid Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RegularMember1_3YearsRate", type="STRING", desc="Regular Member: 1-3 Years Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RegularMember4_6YearsRate", type="STRING", desc="Regular Member: 4-6 Years Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RegularMember7_9YearsRate", type="STRING", desc="Regular Member: 7-9 Years Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RegularMember10_14YearsRate", type="STRING", desc="Regular Member: 10-14 Years Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RegularMember15PlusYearsRate", type="STRING", desc="Regular Member: 15+ Years Rate", value="30" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PublicAgencyMemberRate", type="STRING", desc="Public Agency Member Rate", value="47.50" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="NeutralMemberRate", type="STRING", desc="Neutral Member Rate", value="125.00" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="LawProfessorMemberRate", type="STRING", desc="Law Professor Member Rate", value="0" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="LawStudentMemberRate", type="STRING", desc="Law Student Member Rate", value="0" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="EmeritusMemberRate", type="STRING", desc="Emeritus Member Rate", value="0" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="LegalStaffMemberRate", type="STRING", desc="Legal Staff Member Rate", value="47.50" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AssociateMemberRate", type="STRING", desc="Associate Member Rate", value="125" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);
			
			StructAppend(
				variables, application.objCustomPageUtils.setFormDefaults( event=arguments.event, 
				formName='frmJoin',
				formNameDisplay='#variables.strPageFields.JoinFormTitle#',
				orgEmailTo='#variables.strPageFields.StaffConfirmationTo#',
				memberEmailFrom='<EMAIL>,<EMAIL>'
			));	

			variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
			local.profile_1 = application.objCustomPageUtils.acct_getProfile(siteid=variables.siteID, profileCode='ORCCCIM');
			variables.profile_1 = local.profile_1;
			
			switch (local.formAction) {
				case "processLookup":
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					local.rc=arguments.event.getCollection();
					switch (processMemberInfo(rc=local.rc)) {
						case "success":
							if(local.rc.membershipPrice eq 0){ 
								local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
								local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
								application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
								structDelete(session, "formFields");
							}else{
								local.returnHTML = showPayment(rc=arguments.event.getCollection());
							}
							break;
						case "spam":
							local.returnHTML = showError(errorCode='spam');
							break;							
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					
					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
					
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>
	</cffunction>
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
			
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##cboxOverlay{
					z-index: 99998 !important;
				}
				##colorbox{
					z-index: 99999 !important;
				}
				.tabHeader{
					background: ##3D5A80 !important;
					color: ##fff !important;
					font-family: 'Open Sans', sans-serif !important;
					font-size: 14pt  !important;
					font-weight: bold  !important;
					color: ##ffffff  !important;
					padding: 10px  !important;
					border:1px solid ##3D5A80 !important;
				}
				.tabContentWrap{
					border:1px solid ##3D5A80 !important;
					padding-top: 10px;
				}
				.pageTitle h2{
					font-family: Tahoma;
					font-size: 16pt;
					color: ##03608b;
					font-weight: bold;
				}
				##frmJoin  ##content-wrapper .tsAppBodyText{
					color: ##03608b;
					font-family: Verdana, Arial, Helvetica, sans-serif;
					font-size: 9pt;
				}
				.memberContentWrap .tabContentWrap td,.memberContentWrap .tabContentWrap div,.memberContentWrap .tabContentWrap span,.memberContentWrap .tabContentWrap p{

					color: ##03608b;
					font-family: Verdana, Arial, Helvetica, sans-serif;
					font-size: 9pt;
				}  
				.tabContentWrap table{
					border-spacing: 0px 10px;
					border-collapse: separate;
					width:100%;
				} 
				##selectedLicense input,
				##selectedLicense select{
					width:85%
				}
				##state_table{
					max-height: 50px;
				}
				##state_table .proLicenseLabel{
					font-weight:bold;
				}
				##state_table .proLicenseLabel:first-child{
					text-align:center;
					font-weight:bold;
				}
				##selectedLicense .span3:first-child{
					text-align:center;
				}
				.licenseLabelSm{
					display:none;
				}
				.tabContentWrap nobr{
					white-space: normal !important;
				}
				.tabContentWrap .selectWidth{
					width: auto !important;
				}
				.tabContentWrap .selectWidth button.ui-multiselect{
					width:50% !important;
				}
				.tabContentWrap .selectWidth.ui-multiselect-menu{
					width:50% !important;
				}
				.demographicWrap table td:nth-child(2){
					width:50%;
				}
				.demographicWrap table td input[type="radio"]{
					vertical-align: top !important;
					margin-right: 5px;
					width: 20px;
					height: 16px;
				}
				.demographicWrap table td input[type="checkbox"]{
					vertical-align: bottom;
					margin-right: 5px;
					width: 20px;
					height: 16px;
				}
				.fPaymentWrap table td, .fPaymentWrap .tabContentWrap{
					color: ##03608b !important;
					font-family: Verdana, Arial, Helvetica, sans-serif !important;
					font-size: 9pt !important;
				}
				.fPaymentWrap .tabContentWrap{
					padding:10px;
					line-height:20px;
				}
				.memCatLeft{
					max-width:50%;
					font-weight:bold;				
				}
				.memCatLeft input[type="radio"]{
					vertical-align: text-bottom !important;	
					width: 20px;
					height: 16px;
				}
				.profHead{
					width:58%;
				}				
				
				@media screen and (min-width: 768) and (max-width: 1100px) {
					##state_table .proLicenseLabel{
						width:20%;
					}
					##selectedLicense .span3{
						width:20%;
					}
				}
				
				@media screen and (min-width: 550px) and (max-width: 767px) {
					.state_tableSm{
						display: inline-flex ;
					}
					##state_table .proLicenseLabel{
						width: 25% !important;
					}
					##selectedLicense .span3{
						width:25%;
					}
					##selectedLicense{
						width: 100% !important;
					}
					##selectedLicense .row-fluid{
						display: inline-flex !important;
					}
					.tabContentWrap button.ui-multiselect{
						width:100% !important;
					}
					.memCatRight{
						margin-bottom:-30px;
						padding:10px;					
					}
					.memCatLeft{
						max-width:100%;					
					}
					.selectWidth.ui-multiselect-menu{
						width:40% !important;
					}
				}
				
				@media screen and (max-width: 549px) {
					.licenseLabelSm{
						display:block;
						font-weight:bold !important;
					}
					##selectedLicense .span3:first-child{
						text-align:left;
						margin-top: -40px;
					}
					.proLicenseLabel{
						display:none !important;
					}
					.prlWrap{
						padding-left:10px;
					}
					.prlWrap .span3{
						padding: 8px 0px 8px 0px;
					}
					.demographicWrap table td:nth-child(2){
						width:100px;
					}
					.tabContentWrap button.ui-multiselect{
						width:100% !important;
					}
					.memCatRight{
						margin-bottom:-30px;
						padding:10px;					
					}
					.memCatLeft{
						max-width:100%;					
					}
					.selectWidth.ui-multiselect-menu{
						width:60% !important;
					}
				}
				@media screen and (max-width: 380px) {
					.memberContentWrap .tabContentWrap tr{
						display:block;					
					}
					.memberContentWrap .demographicWrap tr{
						height:0px;	
						display: table !important;
					}
					.memberContentWrap .demographicWrap tr td:blank{
						display:none !important;					
					}
					.memberContentWrap .tabContentWrap tr td:nth-child(4),
					.memberContentWrap .demographicWrap tr td:nth-child(2),
					.memberContentWrap .profHead{
						float: left;
						line-height: 22px;	
						width:100% !important;						
					}
					.tabContentWrap button.ui-multiselect{
						width:90% !important;
					}
					.memberContentWrap .tabContentWrap input[type="text"],
					.memberContentWrap .tabContentWrap select{
						width:90% !important;
					}
					.memberContentWrap .tabContentWrap .profSelect{
						display:block !important;
					}
					.eachProfWrap{
						display:block !important;
					}
					.memCatRight{
						margin-bottom:-30px;
						padding:10px;					
					}
					.memCatLeft{
						max-width:100%;					
					}
					.selectWidth.ui-multiselect-menu{
						width:75% !important;
					}
					
					.emptyTd{
						 display:none;
					}
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				var memberTypeField;				

				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}	
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				$(document).ready(function() {

					<cfif variables.useMID and NOT variables.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif variables.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>					
				
				});			
				
			</script>		
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				
				<cfif len(variables.strPageFields.JoinFormTitle)>
					<div class="row-fluid TitleText pageTitle"><h2>#variables.strPageFields.JoinFormTitle#</h2><br/></div>
				</cfif>
				<div class="row-fluid tabHeader">#variables.strPageFields.AccountLocatorTitle#</div>
				<div class="row-fluid tabContentWrap">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
					</tr>
					</table>
				</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfscript>
				local.qryBilledOffer = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberid=variables.useMID, status='O', distinct=true,includeRate=true);
				
				local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=variables.siteID, typeUID=variables.strPageFields.SubTypeTest);
				local.hasSub = false;
				if ( application.objUser.isLoggedIn(cfcuser=session.cfcuser) ){
					local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=variables.orgID, mcproxy_siteID=variables.siteID, memberID=variables.useMID, typeID=local.membershipDuesTypeID);
				}
			</cfscript>
			
			<cfif Len(local.qryBilledOffer.directLinkCode) gt 0>
				<cfif local.qryBilledOffer.isRenewalRate>
					<cfset local.stReturn = "billedfound">
				<cfelse>
					<cfset local.stReturn = "billedjoinfound">
				</cfif>			
			<cfelseif local.hasSub >
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.stReturn = "success">
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif variables.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfset local.graduationDateFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Law School Graduation Date')>
		
		<cfset local.graduationDateId = 0>
		<cfif structKeyExists(local.graduationDateFieldInfo, "COLUMNID")>
			<cfset local.graduationDateId = local.graduationDateFieldInfo.COLUMNID>
		</cfif>
	
		<cfscript>
			local.scheduleNameList = 'Regular Member,Public Agency Member,Neutral Member,Law Professor Member,Law Student Member,Emeritus Member,Legal Staff Member,Associate Member';
			local.scheduleUIDList = 'F0B436D6-065C-40B5-BEF1-80FE9D477764,108CC875-B191-4A5D-BF65-F4FE7A755AD7,5BC884FC-BD04-4EB0-877E-F94C9A4483CC,E94EBC25-5C2A-4AC7-AA3E-23E36DD7235B,FAB64676-6C80-4D7D-B6EE-7D92A8F9E7A5,8071A115-787A-49F6-864F-4D31889C5B21,50C59030-D4EF-4F8D-B399-4ECA46DD9768,BA265D94-A5D8-4E14-B78C-E80657A22BB4';
			local.regularRateNameList = 'New Admittee,Legal Aid,1-3 Years,4-6 Years,7-9 Years,10-14 Years,15+ Years';
			local.qryRates = getCustomRates();
			local.xmlAdditionalData = application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(orgID=variables.orgID);

			local.now = CreateDate(year(now()),month(now()),day(now()));
			local.thisYear = year(now());
			local.lastYear = year(now()) - 1;
			local.nextYear = year(now()) + 1;
			local.currShortYear = Right(year(now()),2);
			local.strDues = arrayNew(1);
			LinkedHashMap = createObject("java", "java.util.LinkedHashMap");
			
			for ( x=1; x LTE listLen(local.scheduleNameList); x=x+1 ){
				local.strDues[x] 	= LinkedHashMap.init();
				local.strDues[x]["type"] = listGetAt(local.scheduleNameList,x);
				
				for ( y=1; y LTE listLen(local.regularRateNameList); y=y+1 ){
					if ( local.strDues[x]["type"] eq listGetAt(local.scheduleNameList,1)  ){
						local.strDues[x][y] 	= structNew();
						local.strDues[x][y]["txt"] = listGetAt(local.regularRateNameList,y);
						local.strDues[x][y]["amt"] = 0;
					}
					else{
						local.strDues[x]["amt"] = 0;
					}
				}
			}
			
			for (x=1; x LTE local.qryRates.recordCount; x=x+1){
						
				if ( local.qryRates["scheduleName"][x] eq listGetAt(local.scheduleNameList,1) ){
					for ( z=1; z LTE listLen(local.regularRateNameList); z=z+1 ){
						if (  Right(trim(local.qryRates["rateName"][x]),len(listGetAt(local.regularRateNameList,z))) eq trim(local.strDues[1][z]["txt"]) ){
							local.strDues[1][z]["amt"] = local.qryRates["rateAmt"][x];
						}
					}
				}
				for ( z=2; z LTE listLen(local.scheduleNameList); z=z+1 ){
					if (  Right(trim(local.qryRates["rateName"][x]),len(listGetAt(local.scheduleNameList,z))) eq trim(local.strDues[z]["type"]) ){
						local.strDues[z]["amt"] = local.qryRates["rateAmt"][x];
					}
				}
			}		
			
		</cfscript>

		<cfset local.objCustomPageUtils = application.objCustomPageUtils>					
		<cfset local.contactInfoFieldSet = local.objCustomPageUtils.renderFieldSet(uid='E258C68F-952D-486F-AAA2-A2C2746E636B', mode="collection", strData=local.strData)>
		<cfset local.personalInfoFieldSet = local.objCustomPageUtils.renderFieldSet(uid='2BFAB657-F56E-4D4E-B029-2208D7B72555', mode="collection", strData=local.strData)>
		<cfset local.demographicInfoFieldSet = local.objCustomPageUtils.renderFieldSet(uid='********-F01D-4EB5-9CF8-34DDBED39539', mode="collection", strData=local.strData)>
		<cfset local.areaOfPracticeFieldSet = local.objCustomPageUtils.renderFieldSet(uid='A26E2631-B982-45BE-91B5-DC69656D005A', mode="collection", strData=local.strData)>
		<cfset local.membershipFieldSet = local.objCustomPageUtils.renderFieldSet(uid='6E6BE36B-64D6-45C8-9E15-2E8A2B854D50', mode="collection", strData=local.strData)>
		<cfset local.agreementFieldSet = local.objCustomPageUtils.renderFieldSet(uid='3001FBEE-A1C0-4A8F-A93B-43EFC956143A', mode="collection", strData=local.strData)>
		<cfset local.profLicenseIDList = "">
		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset index=1 >
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[index] = local.qryOrgProLicenseStatuses.statusName>	
			<cfset index=index + 1>
		</cfloop> 
		<cfset local.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=variables.formName)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<script type="text/javascript">
					#toScript(local.graduationDateId,"graduationDateId")#
					function prefillData() {
						var objPrefill = new Object();
						<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
							#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfloop>
						for (var key in objPrefill) {
							if (objPrefill.hasOwnProperty(key)) { 
								$('###variables.formName# ##'+key).val(objPrefill[key]);
							}
						}
						showCaptcha();
					}
					function clearDateMask(){
						$('form[name=#variables.formName#] input[type=text]').each(function(){
							if($(this).val() == '__/__/____'){
								$(this).val('');
							}
						});
					}
					function validateMemberInfoForm(){
						var _CF_this = document.forms['#variables.formName#'];
						var arrReq = new Array();

						if (!_FB_hasValue(_CF_this['membership'], 'RADIO')) arrReq[arrReq.length] = 'Please select a membership category.';
						else {
							if ($('input[value="Regular Member"]').is(':checked') && (!_FB_hasValue(_CF_this['membershipDues1'], 'SELECT'))) {
								arrReq[arrReq.length]  = 'Please select a regular membership dues';
							}else{
								memName = $('##membership:checked').val();
								switch(memName){
									case 'Emeritus Member':
									case 'Law Professor Member':
										if($('##mpl_pltypeid').val() == undefined || $('##mpl_pltypeid').val() == null || $('##mpl_pltypeid').val().length == 0){
										
											arrReq[arrReq.length] = "Professional License is required.";
										}									
									break;
									case 'Law Student Member':
										if($('##md_'+graduationDateId).val() == ''){
											arrReq[arrReq.length] = "Law School Graduation Date is required.";
										}									
									break;								
								}							
							}						
						}
						
						if($('##mpl_pltypeid').val() != undefined && $('##mpl_pltypeid').val() != null && $('##mpl_pltypeid').val().length != 0){
							var prof_license = $('##mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
									if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}	
						}

						#local.contactInfoFieldSet.jsvalidation#
						#local.personalInfoFieldSet.jsvalidation#
						#local.demographicInfoFieldSet.jsvalidation#
						#local.areaOfPracticeFieldSet.jsvalidation#
						#local.membershipFieldSet.jsvalidation#
						#local.agreementFieldSet.jsvalidation#

						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}						

						$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function calcMemCat() {
						
						var memText = '';
						var pricepaid = 0;
						var donate = 0;
						
						var membership = determineMembership();
						if (membership == 'Regular Member') 
						{

							// get the dues from the dropdown
							var regType = document.getElementById('membershipDues1').value;
							switch(regType){
								case 'New Admittee':  	pricepaid = #local.strDues[1][1]["amt"]#; break;					
								case 'Legal Aid': 		pricepaid = #local.strDues[1][2]["amt"]#; break;								
								case '1-3 Years':  		pricepaid = #local.strDues[1][3]["amt"]#; break;
								case '4-6 Years':  		pricepaid = #local.strDues[1][4]["amt"]#; break;
								case '7-9 Years':  		pricepaid = #local.strDues[1][5]["amt"]#; break;
								case '10-14 Years':  	pricepaid = #local.strDues[1][6]["amt"]#; break;
								case '15+ Years':  		pricepaid = #local.strDues[1][7]["amt"]#; break;
								
							}						
						} 
						<cfloop index="thisMembership" from="2" to="#ArrayLen(local.strDues)#">
							else if (membership == '#local.strDues[thisMembership]["type"]#') 
							{
								pricepaid = #local.strDues[thisMembership]["amt"]#;
							}
						</cfloop>
						else 
						{
							pricepaid = 0;
							memText = '';
						}						
						
						switch(membership){
							case 'Public Agency Member':
								memText = "I am an attorney working in a public or nonprofit agency in Oregon.  I am a member, in good standing, of the Oregon State Bar and employed by a public agency.  I am of good moral character and committed to the principles of fundamental fairness, trial by jury and the civil justice system.  I do not, for the most part, represent the defense in personal injury, civil rights, employment, workers\' compensation or business tort litigation.  I attest that all information on this application is accurate.";
							break;
							case 'Neutral Member':
								memText = "I am a full-time arbitrator, mediator or reference judge in the state of Oregon; of good moral character; and committed to the principles of fundamental fairness, trial by jury and the civil justice system.  I attest that all information on this application is accurate.";
							break;
							case 'Law Professor Member':
								memText = "I am a law professor at an accredited law school.  I am of good moral character and committed to the principles of fundamental fairness, trial by jury and the civil justice system. I do not work or volunteer for a law firm who, for the most part, represents the defense in personal injury, civil rights, employment, workers\' compensation or business tort litigation. I attest to the fact that the information on this application is accurate";
							break;
							case 'Law Student Member':
								memText = "I am a law student at an accredited law school.  I am of good moral character and committed to the principles of fundamental fairness, trial by jury and the civil justice system.  I do not work or volunteer for a law firm who, for the most part, represents the defense in personal injury, civil rights, employment, workers\' compensation or business tort litigation. I attest to the fact that the information on this application is accurate";
							break;
							case 'Emeritus Member':
								memText = "I am a former OTLA member, retired from the practice of law; of good moral character; and committed to the principles of fundamental fairness, trial by jury and the civil justice system.  I attest that all information on this application is accurate.";
							break;
							case 'Legal Staff Member':
								memText = "I am a legal assistant/paralegal/legal secretary/legal researcher employed by an OTLA member.  I am of good moral character and committed to the principles of fundamental fairness, trial by jury, and the civil justice system. I attest that all information on this application is accurate.";
							break;
							case 'Associate Member':
								memText = "I am a non-Oregon attorney in good standing with my state bar.  I do not spend the majority of time practicing law in the state of Oregon.  I am of good moral character and committed to the principles of fundamental fairness, trial by jury and the civil justice system.  I do not, for the most part, represent the defense in personal injury, civil rights, employment, workers\' compensation or business tort litigation. I attest that all information on this application is accurate.";
							break;
							case 'Regular Member':
								var memOption = $('##membershipDues1 option:selected')[0].value;
								if ( $('##membershipDues1 option:selected')[0].value == 'Legal Aid' ){
									memText = "I am a legal assistant/paralegal/legal secretary/legal researcher employed by an OTLA member.  I am of good moral character and committed to the principles of fundamental fairness, trial by jury, and the civil justice system. I attest that all information on this application is accurate.";
								}
								else{
									memText = "I am a member in good standing with the Oregon State Bar. I am a plaintiff\'s attorney. I do not, for the most part, represent the defense in personal injury, civil rights, employment, workers\' compensation or business tort litigation. I attest that all information on this application is accurate.  (Legal Aid/Public Interest applicants must meet above criteria and be employed by a legal aid program or a nonprofit, non-governmental public interest legal program.)";
								}
							break;
						}
						
						$('##membershipPrice').val(pricepaid);
						
						donate = parseFloat(document.getElementById('scholarshipAmount').value).toFixed(2);
						if (donate == 'NaN'){
							donate = 0.0;
						}
						
						var total = (parseFloat(pricepaid) + parseFloat(donate));
						
						var totalPriceInput = $('##totalPrice')[0];
						totalPriceInput.value = total;
	
						var dynhtml = '<table align="center">';
						dynhtml = dynhtml + '<tr><td class="bodytext">Membership: </td><td class="bodyText" align="right">' + formatCurrency(pricepaid) + '</td></tr>';
						if (donate > 0.0)	{
							dynhtml += '<tr><td class="bodytext">Donation: </td><td class="bodyText" align="right">' + formatCurrency(donate) + '</td></tr>';
						} 
						dynhtml += '<tr><td class="bodytext"><b>Total: </b></td><td class="bodyText" align="right"><b>' + formatCurrency(total) + '</b></td></tr>';
						dynhtml += '</table>';
						
						if (memText.length)
						{
							document.getElementById('dyninfo').innerHTML = dynhtml;
							
							$('##totalCost').show();
							$('##meminfo').show();
						}
						
						checkPracticeAreas(membership);
					}
					function checkPracticeAreas(thisCategory){
						if ( thisCategory == "Regular Member" || thisCategory == "Public Agency Member" ||  thisCategory == "Associate Member" ){
							$('##practiceAreaSection').show();
						}
						else{
							$('##practiceAreaSection').hide();
						}
					}
					
					function formatCurrency(num) {
						num = num.toString().replace(/\$|\,/g,'');
						if(isNaN(num)) num = "0";
						num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
						sign = (num == (num = Math.abs(num)));
						num = Math.floor(num*100+0.50000000001);
						cents = num%100;
						num = Math.floor(num/100).toString();
						if(cents<10) cents = "0" + cents;
						for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
						num.substring(num.length-(4*i+3));
						return (((sign)?'':'-') + '$' + num + '.' + cents);
					}
					function determineMembership() {
						var btnGrp = document.forms['frmJoin'].membership;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					function chkOSB()
					{
						var regExChk = /#local.currShortYear#/;
						var duesLevel = document.getElementById('membershipDues1');

						duesLevel[1].selected  = false;
						duesLevel[3].selected  = false;
						duesLevel[4].selected  = false;
						duesLevel[5].selected  = false;
						duesLevel[6].selected  = false;
						duesLevel[7].selected  = false;

						duesLevel[1].disabled = true;
						duesLevel[3].disabled = true;
						duesLevel[4].disabled = true;
						duesLevel[5].disabled = true;
						duesLevel[6].disabled = true;
						duesLevel[7].disabled = true;	
						var start = moment("#dateFormat(now(),"mm/dd/yyyy")#");
						var end = moment("#dateFormat(now(),"mm/dd/yyyy")#");
						var finalEnd = moment("#dateFormat(now(),"mm/dd/yyyy")#");
						
						
						if($('##mpl_pltypeid').val() != undefined && $('##mpl_pltypeid').val() != null && $('##mpl_pltypeid').val().length != 0){
							var prof_license = $('##mpl_pltypeid').val();
							if(prof_license != "" && prof_license != null){
								$.each(prof_license,function(i,val){
									proDate = $("##mpl_"+val+"_activeDate").val();								
									if(end.isBefore(moment(proDate)) == false){
										finalEnd = moment(proDate);
									}
									end = moment(proDate);
								});
							}	
						}
						
						var monthDiff = start.diff(finalEnd, "month");

						if ( !isNaN(monthDiff)){
							
							if ( monthDiff < 12 ){
								duesLevel[1].disabled = false;
								duesLevel[1].selected  = true;
							}
							if ( monthDiff >= 12 && monthDiff <= 36 ){
								duesLevel[3].disabled = false;
								duesLevel[3].selected  = true;
							}
							if ( monthDiff > 36 && monthDiff <= 72 ){
								duesLevel[4].disabled = false;
								duesLevel[4].selected  = true;
							}
							if ( monthDiff > 72 && monthDiff <= 108 ){
								duesLevel[5].disabled = false;
								duesLevel[5].selected  = true;
							}
							if ( monthDiff > 108 && monthDiff <= 128 ){
								duesLevel[6].disabled = false;
								duesLevel[6].selected  = true;
							}
							
							if ( monthDiff > 128 ){
								duesLevel[7].disabled = false;
								duesLevel[7].selected  = true;
							}
						}	
						calcMemCat();				
					}
					function licenseChange(isChecked,val,text)
					{
						$("##state_table").show();
						
						strOption = '';
						<cfloop collection="#local.licenseStatus#" item="local.i" >
							strOption += '<option value="#local.licenseStatus[local.i]#" <cfif local.licenseStatus[local.i] EQ 'Active'>selected</cfif> >#local.licenseStatus[local.i]#</option>';
						</cfloop>
						if(isChecked){
							$("##state_table").addClass('state_tableSm');
							strLicenseText = '';
							strLicenseText += '<div class="row-fluid prlWrap" id="tr_state_'+val+'">';
							strLicenseText += '<div class="span3">';
							strLicenseText += '<span class="licenseLabelSm">State Name</span>';
							strLicenseText += '<span class="tsAppBodyText">'+text+'</span>';
							strLicenseText += '</div>';
							strLicenseText += '<div class="span3">';
							strLicenseText += '<span class="licenseLabelSm">#variables.strProfLicenseLabels.profLicenseNumberLabel#</span>';
							strLicenseText += '<input name="mpl_'+val+'_licensenumber" onBlur="javascript:chkOSB()" id="mpl_'+val+'_licensenumber" class="tsAppBodyText" type="text" value="" size="13" maxlength="13">';
							strLicenseText += '<input name="mpl_'+val+'_licensename" class="mpl_'+val+'_licensename" type="hidden" value="'+text+'" />';
							strLicenseText += '</div>';
							strLicenseText += '<div class="span3">';
							strLicenseText += '<span class="licenseLabelSm">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</span>';
							strLicenseText += '<input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="" class="tsAppBodyText" size="13" maxlength="10" readonly="" onchange="javascript:chkOSB()">';
							strLicenseText += '</div>';
							strLicenseText += '<div class="span3">';
							strLicenseText += '<span class="licenseLabelSm">#variables.strProfLicenseLabels.profLicenseStatusLabel#</span>';
							strLicenseText += '<select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select>';
							strLicenseText += '</div>';
							strLicenseText += '</div>';							
							
							$('##selectedLicense').append(strLicenseText);

							mca_setupDatePickerField('mpl_'+val+'_activeDate');
							$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
						}									
						else{
							$("##state_table").removeClass('state_tableSm');
							$("##tr_state_"+val).remove();								
						}
						if($('##selectedLicense .row-fluid').length == 0){
							$("##state_table").hide();
						}	
					}
					function checkCaptchaAndValidate(){
						var thisForm = document.forms["#variables.formName#"];
						var status = false;
						var captcha_callback = function(captcha_response){
							if (captcha_response.response && captcha_response.response != 'success') {
								status = false;
							} else {
								status = true;
							}
						}
						if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
							alert('Please enter the correct code shown in the graphic.');
							return false;
						} else {
							#local.captchaDetails.jsvalidationcode#
						}
						if(status){
							return validateMemberInfoForm();
						} else {
							alert('Please enter the correct code shown in the graphic.');
							return false;
						}
					}	
					$(document).ready(function() {
						prefillData();

						$('##scholarshipAmount').keyup(function(e){
							val = $(this).val();
							val = val.replace(/[^0-9.]/g, "");
							fIndex = val.search(/\./) + 1;
							val = val.substr(0, fIndex) + val.slice(fIndex).replace(/\./g, '');
							if(isNaN(val)){
								$(this).val('00.00');
							}else{
								$(this).val(val);
							}						
						});
						$('##scholarshipAmount').blur(function(e){
							val = $(this).val();
							
							if(val != ''){
								$(this).val(parseFloat(val).toFixed(2));									
							}
							calcMemCat();								
						});
						$('.tabContentWrap table td').each(function(){
							var attr = $(this).attr('nowrap');
							if (typeof attr !== typeof undefined && attr !== false) {
								$(this).removeAttr('nowrap');
							}
						});
						$(document).on('change','##md_'+graduationDateId,function(){
							chkOSB();
							
						});
						$('.memberContentWrap .demographicWrap tr td').each(function(){

							if($(this).html().trim().length == 0 || $(this).html().trim() == '&nbsp;'){
								$(this).addClass('emptyTd');
							}

						});
						chkOSB();
						$('.ui-multiselect-menu').addClass('selectWidth')
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<cfif len(variables.strPageFields.JoinFormTitle)>
					<div class="row-fluid TitleText pageTitle" ><h2>#variables.strPageFields.JoinFormTitle#</h2><br/></div>
				</cfif>
				
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return checkCaptchaAndValidate();">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">

					<div id="content-wrapper" class="row-fluid memberContentWrap">
						
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">#local.contactInfoFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer tabContentWrap contactWrap" >
								#local.contactInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">#local.personalInfoFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								#local.personalInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">#local.demographicInfoFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer tabContentWrap demographicWrap" >
								#local.demographicInfoFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">*Membership Categories</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								<table>
									<tr><td>#variables.strPageFields.MembershipCategoriesDesc#</td></tr>
									<tr><td>
										<div class="span12">
											<cfloop array="#local.strDues#" index="thisMembership">
												<div class="row-fluid">
													<div class="span8 memCatLeft">
														<input type="radio" value="#thisMembership["type"]#" name="membership" id="membership" onClick="calcMemCat();" />
														<cfif #thisMembership["type"]# eq 'Public Agency Member'>
															#thisMembership["type"]# - Oregon Bar member employed by public or nonprofit agency
														<cfelseif #thisMembership["type"]# eq 'Neutral Member'>
															#thisMembership["type"]# - Arbitrator, mediator, or reference judge
														<cfelse>
															#thisMembership["type"]#
														</cfif>
													</div>
													<div class="span4 memCatRight">
														<cfif ListLen(StructKeyList(thisMembership)) gt 2>
															<select id="membershipDues1" name="membershipDues1" class="tsAppBodyText" onChange="calcMemCat();">
																<option value="">Please Select</option>
																<cfloop collection="#thisMembership#" item="thisRegularMembershipType">
																	<cfif isStruct(thisMembership[thisRegularMembershipType]) eq TRUE>
																		<option value="#thisMembership[thisRegularMembershipType]["txt"]#">#thisMembership[thisRegularMembershipType]["txt"]# - #DollarFormat(thisMembership[thisRegularMembershipType]["amt"])#</option>
																	</cfif>
																</cfloop>
															</select>
														<cfelse>
															#dollarFormat(thisMembership["amt"])#
														</cfif>
													</div>
												</div>
											</cfloop>								
										</div>
									
									</td></tr>
								</table>								
							</div>
						</div>
						<div class="row-fluid hide" id="practiceAreaSection">
							<div class="span12 tsAppSectionHeading tabHeader">#local.areaOfPracticeFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								#local.areaOfPracticeFieldSet.fieldSetContent#
							</div>
						</div>					
						<div class="row-fluid hide" id="membershipSection">
							<div class="span12 tsAppSectionHeading tabHeader">#local.membershipFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								#local.membershipFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">Help a Fellow Member Fund</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td>I would like to make a contribution to the <strong>Help a Fellow Member</strong> fund in the amount of $<input type="text" size="10" id="scholarshipAmount" name="scholarshipAmount" ></td>
									</tr>
								</table>
							</div>
						</div>
						<div class="row-fluid hide" id="meminfo">
							<div class="span12 tsAppSectionHeading tabHeader">#local.agreementFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								#local.agreementFieldSet.fieldSetContent#
							</div>
						</div>
						
						<div class="row-fluid hide" id="totalCost">
							<div class="span12 tsAppSectionHeading tabHeader">Total</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								<input type="hidden" id="membershipPrice" name="membershipPrice" value="" />
								<input type="hidden" id="totalPrice" name="totalPrice" value="" />
								<div id="dyninfo" class="BodyText"></div>
							</div>
						</div>
						<div class="row-fluid">
							<div class="tsAppSectionContentContainer tabContentWrap" >
								#local.captchaDetails.htmlContent#
							</div>
						</div>	
						
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					</div>
					<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
					<table class="hide PLInfo">
						<tr align="top">
							<td class="tsAppBodyText emptyTd" width="10">&nbsp;</td>
							<td class="tsAppBodyText profHead">Professional License:</td>
							<td class="tsAppBodyText profSelect">
								<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
									<cfloop query="local.qryOrgPlTypes">
										<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
										<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr class="top">
							<td colspan="3" class="eachProfWrap">
								<div class="row-fluid" id="state_table" style="display:none;" >
									<div class="span3 proLicenseLabel" >
										State Name
									</div>
									<div class="span3 proLicenseLabel"  >
										#variables.strProfLicenseLabels.profLicenseNumberLabel#
									</div>
									<div class="span3 proLicenseLabel"  >
										#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)
									</div>
									<div class="span3 proLicenseLabel" >
										#variables.strProfLicenseLabels.profLicenseStatusLabel#
									</div>
								</div>							
								<span id="selectedLicense">
								</span>										
							</td>
						</tr>
					</table>
					<script>
						$(document).ready(function(){						
							if($('.PLInfo tbody').html().length > 0){
								$('.contactWrap').prepend('<table cellpadding="3" border="0" cellspacing="0">'+$('.PLInfo tbody').html()+'</table>');
							}else if($('.PLInfo').html().length > 0){
								$('.contactWrap').prepend('<table class="fPrfWrap" cellpadding="3" border="0" cellspacing="0">'+$('.PLInfo').html()+'</table>');
							}
							$('.PLInfo').remove();
							setTimeout(function(){
								$("##mpl_pltypeid").multiselect({
									header: true,
									noneSelectedText: ' - Please Select - ',
									selectedList: 1,
									click: function(event, ui){
										licenseChange(ui.checked,ui.value,ui.text);						
									},
									uncheckAll: function(){
										$("##mpl_pltypeid option").each(function(){
											$('##tr_state_'+$(this).attr("value")).remove();
										});
										$("##state_table").hide();
										
									},
									checkAll: function( e ){
										$("##mpl_pltypeid option").each(function(){
											licenseChange(true,$(this).attr("value"),$(this).text());	
										});
									}
								});	
							},500);
							
						});
					</script>
				</form>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree") and len(arguments.rc.iAgree)) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		<cfset local.objSaveMember.setMemberType(memberType='User')>
		
		<cfset local.contactTypeName = "">
		<cfswitch expression="#arguments.rc.membership#">
			<cfcase value="Regular Member">
				<cfif arguments.rc.membershipDues1 eq 'Legal Aid'>
					<cfset local.contactTypeName = 'Legal Aid' />
				<cfelse>
					<cfset local.contactTypeName = 'Attorney' />
				</cfif>
			</cfcase>
			<cfcase value="Public Agency Member">
				<cfset local.contactTypeName = 'Public Agency Staff' />
			</cfcase>
			<cfcase value="Neutral Member">
				<cfset local.contactTypeName = 'Arbitrator, Mediator, or Reference Judge' />
			</cfcase>
			<cfcase value="Law Professor Member">
				<cfset local.contactTypeName = 'Law Professor ' />
			</cfcase>
			<cfcase value="Law Student Member">
				<cfset local.contactTypeName = 'Law Student ' />
			</cfcase>
			<cfcase value="Emeritus Member">
				<cfset local.contactTypeName = 'Retired Attorney' />
			</cfcase>
			<cfcase value="Legal Staff Member">
				<cfset local.contactTypeName = 'Legal Staff/Paralegal' />
			</cfcase>
			<cfcase value="Associate Member">
				<cfset local.contactTypeName = 'Out of State Attorney' />
			</cfcase>
		</cfswitch>	
		<cfif (len(local.contactTypeName))>
			<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeName)>	
		</cfif>
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
												subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
												enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
			<cfset session.useHistoryID = variables.useHistoryID>		
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
	
		<cfset var local = structNew()>	

		<cfscript>
			local.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=variables.siteId,
												profilecode= variables.strPageFields.ProfileCodePayCC,
												pmid = variables.useMID,
												showCOF = variables.useMID EQ session.cfcUser.memberData.memberID,
												usePopupDIVName='paymentTable'
											);
		</cfscript>	
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
					.noBorder{
						border:0 !important;
					}
				</style>
				<script type="text/javascript">
				
					function checkPaymentMethod() {
						var rdo = document.forms["#variables.formName#"].payMeth;
						if (rdo[0].checked) {//credit card
							$('##CCInfo').show();
							$('##CheckInfo').hide();
						}  
						else if (rdo[1].checked) {//check
							$('##CCInfo').hide();
							$('##CheckInfo').show();
						}  
						
					}
					function getMethodOfPayment() {
						var btnGrp = document.forms['#variables.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					function validatePaymentForm() {
						var thisForm = document.forms["#variables.formName#"];
						var arrReq = new Array();
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatement'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}

						$("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;						
					}
					$('##mccfdiv_#variables.strPageFields.ProfileCodePayCC# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
												
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				<div id="paymentTable">
					<cfform name="#variables.formName#"  id="#variables.formName#" method="POST" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
						<cfinput type="hidden" name="fa" id="fa" value="processPayment">
						<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
						<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
						<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
						<cfinput type="hidden" name="membership" id="membership" value="#arguments.rc.membership#">
						<cfinput type="hidden" name="membershipPrice" id="membershipPrice" value="#arguments.rc.membershipPrice#">
						<cfinput type="hidden" name="scholarshipAmount" id="scholarshipAmount" value="#arguments.rc.scholarshipAmount#">
						<cfloop collection="#arguments.rc#" item="local.thisField">
							<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
								or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
								or left(local.thisField,5) eq "mccf_" or left(local.thisField,3) eq "sub">
								<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
							</cfif>
						</cfloop>
						<div id="content-wrapper" class="row-fluid fPaymentWrap">
							<cfif len(variables.strPageFields.JoinFormTitle)>
								<div class="row-fluid TitleText pageTitle" ><h2>#variables.strPageFields.JoinFormTitle#</h2><br/></div>
							</cfif>
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading tabHeader">*Method of Payment</div>
								<div class="tsAppSectionContentContainer tabContentWrap" >
									<table class="table" cellpadding="2" cellspacing="0" width="100%" border="0">
										<tr valign="top">
											<td colspan="2" >Please select your preferred method of payment from the options below.</td>
										</tr>
										<tr>
											<td class="noBorder">
												<table cellpadding="2" cellspacing="0" width="100%" border="0">
													<tr>
														<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
														<td>Credit Card</td>
													</tr>
													<tr>
														<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
														<td>Check</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>
							<div class="row-fluid hide" id="CCInfo">
								<div class="span12 tsAppSectionHeading tabHeader">Credit Card Information</div>
								<div class="row-fluid tabContentWrap">
									<div class="tsAppSectionContentContainer paymentGateway" >
										<cfif len(local.profile_1.strPaymentForm.inputForm)>
											<div>#local.profile_1.strPaymentForm.inputForm#</div>
										</cfif>
									</div>
									<div class="tsAppSectionContentContainer" >
										<div class="PB">* Please confirm the statement below:</div>
										<table width="100%">
											<tr>
												<td width="25"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
												<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
											</tr>
										</table>
									</div>
									<div class="P"><button type="submit" class="tsAppBodyButton" id="checkAuthorizeButton" name="btnSubmit">AUTHORIZE</button></div>
								</div>
							</div>
						</div>
						<div class="row-fluid hide fPaymentWrap" id="CheckInfo">
							<div class="span12 tsAppSectionHeading tabHeader">Check Information</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
									Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
									<strong>Oregon Trial Layers Association</strong><br />
									812 SW Washington Ste. 900<br />
									Portland OR 97205<br/>
									<div><button type="submit" class="tsAppBodyButton btn" id="checkContinueButton" name="btnSubmit">CONTINUE</button></div>
							</div>						
						</div>
					</cfform>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.objCustomPageUtils = application.objCustomPageUtils>					
		<cfset local.contactInfoFieldSet = local.objCustomPageUtils.renderFieldSet(uid='E258C68F-952D-486F-AAA2-A2C2746E636B', mode="confirmation", strData=arguments.rc)>
		<cfset local.personalInfoFieldSet = local.objCustomPageUtils.renderFieldSet(uid='2BFAB657-F56E-4D4E-B029-2208D7B72555', mode="confirmation", strData=arguments.rc)>
		<cfset local.demographicInfoFieldSet = local.objCustomPageUtils.renderFieldSet(uid='********-F01D-4EB5-9CF8-34DDBED39539', mode="confirmation", strData=arguments.rc)>
		<cfset local.areaOfPracticeFieldSet = local.objCustomPageUtils.renderFieldSet(uid='A26E2631-B982-45BE-91B5-DC69656D005A', mode="confirmation", strData=arguments.rc)>
		<cfset local.membershipFieldSet = local.objCustomPageUtils.renderFieldSet(uid='6E6BE36B-64D6-45C8-9E15-2E8A2B854D50', mode="confirmation", strData=arguments.rc)>
		<cfset local.agreementFieldSet = local.objCustomPageUtils.renderFieldSet(uid='3001FBEE-A1C0-4A8F-A93B-43EFC956143A', mode="confirmation", strData=arguments.rc)>
		
		<cfset session.orgMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset session.newMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif len(arguments.rc.Membership)>
			<cfset local.membershipName = arguments.rc.Membership/>
		<cfelse>
			<cfset local.membershipName = 'Regular Member' />
		</cfif>
		<cfif len(arguments.rc.membershipPrice)>
			<cfset local.membershipDues = arguments.rc.membershipPrice/>
		<cfelse>
			<cfset local.membershipDues = 0 />
		</cfif>
			<cfset local.totalAmount = local.membershipDues />

		<cfif (arguments.rc.scholarshipAmount neq '')>
			<cfset local.totalAmount = local.totalAmount + arguments.rc.scholarshipAmount>
		</cfif>
		<cfsavecontent variable="local.invoice">
			<cfoutput>
				<style type="text/css">
					.confirmationContent .fieldSetWrap th{
						background: ##3D5A80 !important;
						color: ##fff !important;
						font-family: 'Open Sans', sans-serif !important;
						font-size: 14pt !important;
						font-weight: bold !important;
						color: ##ffffff !important;
						padding: 10px !important;
						border: 1px solid ##3D5A80 !important;
						text-align: left;
						text-transform: uppercase;
					}
					.confirmationContent .tabContentWrap td,.confirmationContent .fieldSetWrap td,.confirmationContent .tabContentWrap div,.confirmationContent .fieldSetWrap div,.confirmationContent .tabContentWrap span,.confirmationContent .fieldSetWrap span,.confirmationContent .tabContentWrap p,.confirmationContent .fieldSetWrap p{

						color: ##03608b !important;
						font-family: Verdana, Arial, Helvetica, sans-serif !important;
						font-size: 9pt !important;
					} 
					.tabHeader{
						background: ##3D5A80 !important;
						color: ##fff !important;
						font-family: 'Open Sans', sans-serif !important;
						font-size: 14pt  !important;
						font-weight: bold  !important;
						color: ##ffffff  !important;
						padding: 10px  !important;
						border:1px solid ##3D5A80 !important;
					}
					.tabContentWrap{
						border:1px solid ##3D5A80 !important;
					}
					.pageTitle h2{

						font-family: Tahoma;
						font-size: 16pt;
						color: ##03608b;
						font-weight: bold;
					}
					##frmJoin  ##content-wrapper .tsAppBodyText{
						color: ##03608b;
						font-family: Verdana, Arial, Helvetica, sans-serif;
						font-size: 9pt;
					}
					.confirmationContent hr{
						padding:0 !important;
					}
					.noLeftWrap{
						margin-left:0 !important;
					}
					.noLeftWrap,.confirmationMessage,.confirmationMessage p{
						color: ##03608b !important;
						font-family: Verdana, Arial, Helvetica, sans-serif !important;
						font-size: 9pt !important;
					}
					.confirmationContent nobr{
						white-space: normal !important;
					}
					.topText{
						margin-bottom:10px;
					}
				</style>
				<script>
					$(document).ready(function(){
						$('.confirmationContent  > table').each(function(){
								$($(this).find('td')[0]).addClass('tabHeader');
								$($(this).find('td')[1]).addClass('tabContentWrap');
						});						
						$('.confirmationContent table td').each(function(){
							var attr = $(this).attr('nowrap');
							if (typeof attr !== typeof undefined && attr !== false) {
								$(this).removeAttr('nowrap');
							}
						});
						$('.fieldSetWrap > table').each(function(){
							$($(this).find('td').eq(0)).addClass('tabHeader');
							$($(this).find('td').eq(0)).replaceWith('<th>' + $($(this).find('td').eq(0)).html() +'</th>');

						});
					});
				</script>
				<div class="row-fluid topText">
					<!-- @accResponseMessage@ -->			
					<div class="span12 noLeftWrap confirmationMessage"> 
						#variables.strPageFields.ConfirmationMessage#
					</div>		
				</div>
				
				<div id="content-wrapper" class="row-fluid confirmationContent">
					<div class="row-fluid" id="membershipSection">
						<div class="span12 tsAppSectionHeading tabHeader">JOIN</div>
						<div class="tsAppSectionContentContainer tabContentWrap" >
							<table width="100%" cellspacing="0" cellpadding="3"  >
								<tr>
									<td>Member Number:</td>
									<td>#session.newMemberNumber#&nbsp;</td>
								</tr>
								<tr>
									<td>Membership Category:</td>
									<td>#local.membershipName# #dollarFormat(local.memberShipDues)#&nbsp;</td>
								</tr>
								<cfif (arguments.rc.scholarshipAmount neq '')>
									<tr>
										<td>Contributions:</td>
										<td>#arguments.rc.scholarshipAmount#</td>
									</tr>
								</cfif>
							</table>
						</div>
					</div>
					<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">Professional License Information</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td>										
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_status']#</td>
													</tr>
												</cfloop>
												</tbody>
											</table>										
										</td>
									</tr>						
								</table>
							</div>
						</div>
					</cfif>
					<div class="row-fluid fieldSetWrap">
						#local.contactInfoFieldSet.fieldSetContent#
					</div>
					<div class="row-fluid fieldSetWrap">
						#local.personalInfoFieldSet.fieldSetContent#
					</div>
					<div class="row-fluid fieldSetWrap">
						#local.demographicInfoFieldSet.fieldSetContent#
					</div>
					<div class="row-fluid fieldSetWrap">
						#local.areaOfPracticeFieldSet.fieldSetContent#
					</div>
					<div class="row-fluid fieldSetWrap">
						#local.membershipFieldSet.fieldSetContent#
					</div>
					<div class="row-fluid fieldSetWrap">
						#local.agreementFieldSet.fieldSetContent#
					</div>
					
					<cfif local.totalAmount gt 0>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading tabHeader">PAYMENT METHOD</div>
							<div class="tsAppSectionContentContainer tabContentWrap" >
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td>
											Payment Type:
										</td>
										<td>
											<cfif arguments.rc.payMeth EQ 'CC'>
												Credit Card
												
												<cfset local.d = "p_#variables.profile_1._profileID#_mppid">
												<cfset local.paymentCr =  arguments.rc[local.d]/>
												
												 <cfif local.paymentCr gt 0>
													<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
															mppid     = local.paymentCr ,
															memberID  = val(variables.useMID),
															profileID = variables.profile_1._profileID) />
													- #local.qrySavedInfoOnFile.detail#
												</cfif>
											<cfelse>
												Check
											</cfif>
										</td>
									</tr>
								</table>
							</div>
						</div>	
					</cfif>			
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=variables.siteCode)>
		<cfset local.uid = createuuid()>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
					<body>
						<cfif len(variables.strPageFields.JoinFormTitle)>
							<div class="row-fluid TitleText pageTitle" ><h2>#variables.strPageFields.JoinFormTitle#</h2><br/></div>
						</cfif>
						#local.invoice#
					</body>
				</html>
			</cfoutput>
		</cfdocument>

		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "Membership_#local.stFinalMemberNumber#_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>

		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
		<cfset local.emailSentToUser = TRUE>
		<cfset local.responseStruct = structNew()>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif len(variables.strPageFields.JoinFormTitle)>
					<div class="row-fluid TitleText pageTitle" ><h2>#variables.strPageFields.JoinFormTitle#</h2></div>
				</cfif>
				#local.invoice#				
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle=variables.strPageFields.JoinFormTitle,
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteId,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>
		
		<cfset local.emailSentToUser = local.responseStruct.success>

		<cfsavecontent variable="local.mailContentAdmin">
			<cfoutput>
				<cfif NOT local.emailSentToUser>
					<p style="color:red;">We were not able to send an e-mail confirmation.</p>
				</cfif>
				<p><b>The member's record was updated in Control Panel with any changes made on this application.</b></p>
				<div>
					<p>MemberNumber Found/Created in Account Lookup: <b>#session.orgMemberNumber#</b></p>
					<p>MemberNumber of Final Member Record: <b>#session.newMemberNumber#</b></p>
				</div>
				<!--- #replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)# --->
				#local.invoice#
			</cfoutput>
		</cfsavecontent>

		<cfscript>
			local.arrEmailTo = [];
			variables.ORGEmail.to = replace(variables.ORGEmail.to,",",";","all");
			local.toEmailArr = listToArray(variables.ORGEmail.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
			if (arrayLen(local.arrEmailTo)) {
				local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.ORGEmail.from },
					emailto=local.arrEmailTo,
					emailreplyto=variables.ORGEmail.from,
					emailsubject=variables.ORGEmail.SUBJECT,
					emailtitle=variables.strPageFields.JoinFormTitle,
					emailhtmlcontent=local.mailContentAdmin,
					siteID=variables.siteID,
					memberID=arguments.rc.mc_siteinfo.sysMemberID,
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				);
			}
		</cfscript>
		
		<cfreturn local.mailContent>
	</cffunction>
	
	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
					
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	<cffunction name="storeJoinApplicationDoc" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memNumber" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >

		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=arguments.siteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(arguments.siteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.siteID, resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.parentSiteResourceID,
			sectionID=local.docSectionID, docTitle="Membership_#arguments.memNumber#_ #DateFormat(now(),'m-d-yyyy')#", docDesc="Membership_#arguments.memNumber#_ #DateFormat(now(),'m-d-yyyy')#",
			author='', fileName=arguments.strPDF.serverFile, fileExt='pdf', isActive=1, isVisible=true, 
			contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, fileData=local.newFile)>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>			
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						We've found an issue with your application. We apologize, but it is not available online. Please contact the OTLA office by calling (503) 223-5587 or via email at <a href="mailto:<EMAIL>"><EMAIL></a>.
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						<cflocation url="#local.redirectLink#" addtoken="false">
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="getCustomRates" access="private" output="false" returntype="query">

		<cfset var local = structNew()>

		<cfset local.arrRates= [
			#variables.strPageFields.RegularMemberNewAdmitteeRate#,
			#variables.strPageFields.RegularMemberLegalAidRate#,
			#variables.strPageFields.RegularMember1_3YearsRate#,
			#variables.strPageFields.RegularMember4_6YearsRate#,
			#variables.strPageFields.RegularMember7_9YearsRate#,
			#variables.strPageFields.RegularMember10_14YearsRate#,
			#variables.strPageFields.RegularMember15PlusYearsRate#,
			#variables.strPageFields.PublicAgencyMemberRate#,
			#variables.strPageFields.NeutralMemberRate#,
			#variables.strPageFields.LawProfessorMemberRate#,
			#variables.strPageFields.LawStudentMemberRate#,
			#variables.strPageFields.EmeritusMemberRate#,
			#variables.strPageFields.LegalStaffMemberRate#,
			#variables.strPageFields.AssociateMemberRate#
			
		]>
		
		<cfset local.scheduleNameList = ['Regular Member','Public Agency Member','Neutral Member','Law Professor Member','Law Student Member','Emeritus Member','Legal Staff Member','Associate Member']>
		<cfset local.regularRateNameList = ['New Admittee','Legal Aid','1-3 Years','4-6 Years','7-9 Years','10-14 Years','15+ Years']>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRates">
			IF OBJECT_ID('tempdb..##tblRatesTemp') IS NOT NULL
				DROP TABLE ##tblRatesTemp;

				CREATE TABLE ##tblRatesTemp (
					tempID int PRIMARY KEY, 
					scheduleName varchar(max), 
					rateName varchar(max), 
					rateAmt decimal(18,2)
				);

				INSERT INTO ##tblRatesTemp VALUES
				<cfset local.idx = 1 >
				<cfset local.idxSub = 1 >
				<cfloop collection="#local.arrRates#" item="local.thisKey">
					<cfset local.strRate = trim(local.arrRates[local.thisKey])>

					<cfif IsNumeric(local.strRate)>
						<cfset local.strRate = numberFormat(local.strRate, "9.99")>
					<cfelse>
						<cfset local.strRate = numberFormat('0', "9.99")>
					</cfif>
					
					<cfif local.thisKey lte 7>
						<cfset local.idx = 1 >
						<cfset local.rateSubName = local.regularRateNameList[local.idxSub] >
						<cfset local.idxSub = local.idxSub + 1 >
					<cfelse>
						<cfset local.idx = local.idx + 1 >
						<cfset local.rateSubName = local.scheduleNameList[local.idx]>
					</cfif>

					<cfif local.thisKey eq 1>
						(#local.thisKey#,'#local.scheduleNameList[local.idx]#','#local.rateSubName#',#local.strRate#)
					<cfelse>
						,(#local.thisKey#,'#local.scheduleNameList[local.idx]#','#local.rateSubName#',#local.strRate#)
					</cfif>			
				</cfloop>

				SELECT * FROM ##tblRatesTemp;

			IF OBJECT_ID('tempdb..##tblRatesTemp') IS NOT NULL
				DROP TABLE ##tblRatesTemp;
		</cfquery>

		<cfreturn local.qryRates>
	</cffunction>
</cfcomponent>