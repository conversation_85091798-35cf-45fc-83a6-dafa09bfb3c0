<cfinclude template="../commonManageSubsStyles.cfm">
<cfinclude template="../commonSubsPayReceiptJS.cfm">

<cfoutput>
<div class="manageSubsApp">
	<cfif len(local.topSubscriptionContent)>
		<div class="mcsubs-card mcsubs-p-3 mcsubs-mb-2">#local.topSubscriptionContent#</div>
	</cfif>

	<div class="mcsubs-card mcsubs-mt-3">
		<div class="mcsubs-card-body mcsubs-p-4 mcsubs-px-sm-2">
			<div class="mcsubs-d-flex mcsubs-mb-3">
				<div class="col">
					<div class="mcsubs-font-size-xl mcsubs-font-weight-bold">#local.qryMember.firstName# #local.qryMember.lastName#</div>
					<cfif len(local.qryMember.company)><div class="mcsubs-text-dim"><small>#local.qryMember.company#</small></div></cfif>
				</div>
				<a href="javascript:downloadPaymentReceipt();" id="mcsubs_download_receiptlink" class="mcsubs-ml-auto"><i class="icon-print icon-large mcsubs-noprint"></i></a>
				<span id="mcsubs_download_receiptLoading" class="mcsubs-ml-auto mcsubs-d-none"></span>
			</div>

			<div class="mcsubs-p-2">
				<cfquery name="local.qryThisSubAmountToday" dbtype="query">
					SELECT SUM(amount) AS totalAmountToday
					FROM [local].qrySubscriberTransactions
					WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">
					AND dateDue = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.topDueDate#">
				</cfquery>
				<cfset local.parentFreqNameDisplay = "">
				<cfif local.qryRootSubscriber.frequencyShortName NEQ 'F' AND val(local.qryRootSubscriber.forceUpfront) EQ 0 AND local.qryOtherInvoices.recordcount>
					<cfset local.parentFreqNameDisplay = local.qryRootSubscriber.frequencyName>
				</cfif>
				<cfset local.subRootRateNameMatchesSubName = local.qryRootSubscriber.rateName EQ local.qryRootSubscriber.subscriptionName>
				
				<cfif local.subRootRateNameMatchesSubName>
					<div class="mcsubs-d-flex mcsubs-mb-3">
						<div class="mcsubs-col mcsubs-font-weight-bold">#local.qryRootSubscriber.subscriptionName#</div>
						<div class="mcsubs-col-auto mcsubs-pr-lg-5">
							#renderConfirmationPrice(amount=val(local.qryThisSubAmountToday.totalAmountToday), displayedCurrencyType=local.displayedCurrencyType, freqName=local.parentFreqNameDisplay)#
						</div>
					</div>
				<cfelse>
					<div class="mcsubs-font-weight-bold">#local.qryRootSubscriber.subscriptionName#</div>
					<div class="mcsubs-d-flex mcsubs-mt-2 mcsubs-mb-3 mcsubs-pl-3">
						<div class="mcsubs-col">#local.qryRootSubscriber.rateName#</div>
						<div class="mcsubs-col-auto mcsubs-pr-lg-5">
							#renderConfirmationPrice(amount=val(local.qryThisSubAmountToday.totalAmountToday), displayedCurrencyType=local.displayedCurrencyType, freqName=local.parentFreqNameDisplay)#
						</div>
					</div>
				</cfif>

				<cfif local.qrySelectedAddOnSubs.recordCount>
					<div class="mcsubs-pl-3">
						<cfset local.setIDsList = "">
						<cfset local.strSubPath = {}>
						<cfloop query="local.qrySelectedAddOnSubs">
							<cfquery name="local.qryThisSubscriber" dbtype="query">
								SELECT subscriberID, frequencyName, frequencyShortName, forceUpfront
								FROM [local].qryAddOnSubs
								WHERE subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySelectedAddOnSubs.subscriptionID#">
							</cfquery>

							<cfquery name="local.qryThisSubAmountToday" dbtype="query">
								SELECT SUM(amount) AS totalAmountToday
								FROM [local].qrySubscriberTransactions
								WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryThisSubscriber.subscriberID#">
								AND dateDue = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.topDueDate#">
							</cfquery>

							<cfquery name="local.qryThisSubNumPaymentsLeft" dbtype="query">
								SELECT COUNT(amount) AS numPaymentsLeft
								FROM [local].qrySubscriberTransactions
								WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryThisSubscriber.subscriberID#">
								AND dateDue <> <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.topDueDate#">
							</cfquery>

							<cfquery name="local.qryThisSubPath" dbtype="query">
								SELECT thePath
								FROM [local].qryMemberSubscriptions
								WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryThisSubscriber.subscriberID#">
							</cfquery>

							<cfquery name="local.qryThisSubDeepestNestedPath" dbtype="query">
								SELECT MAX(thePath) AS maxPath
								FROM [local].qryMemberSubscriptions
								WHERE thePath LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryThisSubPath.thePath#.%">
							</cfquery>

							<cfif local.qryThisSubDeepestNestedPath.recordCount>
								<cfset local.thisSubDeepestPath = local.qryThisSubDeepestNestedPath.maxPath>
							<cfelse>
								<cfset local.thisSubDeepestPath = local.qryThisSubPath.thePath>
							</cfif>

							<cfif NOT structKeyExists(local.strSubPath,local.thisSubDeepestPath)>
								<cfset local.strSubPath[local.thisSubDeepestPath] = 0>
							</cfif>
							<cfset local.strSubPath[local.thisSubDeepestPath]++>

							<cfset local.freqNameDisplay = "">
							<cfif local.qryThisSubNumPaymentsLeft.recordCount>
								<cfset local.freqNameDisplay = local.parentFreqNameDisplay>
							</cfif>
							
							<cfif NOT listFind(local.setIDsList,local.qrySelectedAddOnSubs.setID)>
								<cfset local.setIDsList = listAppend(local.setIDsList,local.qrySelectedAddOnSubs.setID)>
								<cfset local.setNameMatchesSubName = local.qrySelectedAddOnSubs.setName EQ local.qrySelectedAddOnSubs.subscriptionName>

								<cfif local.setNameMatchesSubName>
									<div class="mcsubs-d-flex mcsubs-mb-3">
										<div class="mcsubs-col mcsubs-font-weight-bold">#local.qrySelectedAddOnSubs.subscriptionName#</div>
										<div class="mcsubs-col-auto mcsubs-pr-lg-5">
											#renderConfirmationPrice(amount=val(local.qryThisSubAmountToday.totalAmountToday), displayedCurrencyType=local.displayedCurrencyType, freqName=local.freqNameDisplay)#
										</div>
									</div>
								<cfelse>
									<div class="mcsubs-font-weight-bold mcsubs-pl-1">#local.qrySelectedAddOnSubs.setName#</div>
								</cfif>
							<cfelse>
								<cfset local.setNameMatchesSubName = false>
							</cfif>

							<div class="mcsubs-pl-3">
								<cfif NOT local.setNameMatchesSubName>
									<div class="mcsubs-d-flex mcsubs-mt-2 mcsubs-mb-3">
										<div class="mcsubs-col">#local.qrySelectedAddOnSubs.subscriptionName#</div>
										<div class="mcsubs-col-auto mcsubs-pr-lg-5">
											#renderConfirmationPrice(amount=val(local.qryThisSubAmountToday.totalAmountToday), displayedCurrencyType=local.displayedCurrencyType, freqName=local.freqNameDisplay)#
										</div>
									</div>

									<cfif local.qryThisSubDeepestNestedPath.recordCount>
										<cfset local.strSubPath[local.thisSubDeepestPath]++>
										<div class="mcsubs-pl-3">
									</cfif>
								</cfif>

							<cfif local.strSubPath.keyExists(local.qryThisSubPath.thePath)>
								#RepeatString("</div>",local.strSubPath[local.qryThisSubPath.thePath])#
							</cfif>
						</cfloop>
					</div>
				</cfif>

				<div class="mcsubs-d-flex mcsubs-mt-3 mcsubs-mb-3">
					<div class="mcsubs-col mcsubs-font-weight-bold">Total Due:</div>
					<div class="mcsubs-col-auto mcsubs-font-weight-bold mcsubs-pr-lg-5">
						#renderConfirmationPrice(amount=local.totalAmount, displayedCurrencyType=local.displayedCurrencyType, freqName="")#
					</div>
				</div>

				<cfset local.amountDueToday = val(local.qryTodaySum.totalAmt)>
				<cfif local.strSubs.keyExists("qryAdditionalFees")>
					<cfset local.amountDueToday += local.strSubs.qryAdditionalFees.additionalFees>
					<div class="mcsubs-d-flex mcsubs-mt-3 mcsubs-mb-3">
						<div class="mcsubs-col mcsubs-font-weight-bold">#local.strSubs.qryAdditionalFees.additionalFeesLabel#:</div>
						<div class="mcsubs-col-auto mcsubs-font-weight-bold mcsubs-pr-lg-5">
							#renderConfirmationPrice(amount=local.strSubs.qryAdditionalFees.additionalFees, displayedCurrencyType=local.displayedCurrencyType, freqName="")#
						</div>
					</div>
				</cfif>
				<cfset local.isAmountDueForTodayPaid = local.totalAmount GT 0 AND local.strSubs.qryPaymentTransaction.recordcount AND val(local.strSubs.qryPaymentTransaction.amount) EQ local.amountDueToday>
				<div class="mcsubs-d-flex mcsubs-mt-3 mcsubs-mb-3">
					<div class="mcsubs-col mcsubs-font-weight-bold">Amount Due Today:</div>
					<div class="mcsubs-col-auto mcsubs-font-weight-bold<cfif NOT local.isAmountDueForTodayPaid> mcsubs-pr-lg-5</cfif>">
						#renderConfirmationPrice(amount=local.amountDueToday, displayedCurrencyType=local.displayedCurrencyType, freqName="")#<cfif local.isAmountDueForTodayPaid> (Paid)</cfif>
					</div>
				</div>

				<cfif local.qryOtherInvoices.recordcount gt 0>
					<div class="mcsubs-mt-5 mcsubs-mb-3">
						<div class="mcsubs-font-weight-bold mcsubs-mb-2">Schedule of remaining invoices:</div>
						<cfloop query="local.qryOtherInvoices">
							<div class="mcsubs-mb-2 mcsubs-pl-1">
								#renderConfirmationPrice(amount=val(local.qryOtherInvoices.sumTotal), displayedCurrencyType=local.displayedCurrencyType, freqName="")#
								-  Due: #dateFormat(local.qryOtherInvoices.datedue,'m/d/yyyy')#
							</div>
						</cfloop>
					</div>
				</cfif>
			</div>
		</div>
	</div>

	<div class="mcsubs-card mcsubs-mt-3">
		<div class="mcsubs-card-header mcsubs-bg-whitesmoke mcsubs-pb-1">
			<div class="mcsubs-font-size-lg mcsubs-font-weight-bold">Payment Information</div>
		</div>
		<div class="mcsubs-card-body mcsubs-pb-2">
			<div class="mcsubs-d-flex mcsubs-flex-wrap mcsubs-flex-sm-column-reverse">
				<div class="mcsubs-col">
					<cfif local.totalAmount gt 0 and local.strSubs.qryPaymentTransaction.recordcount gt 0>
						#dollarFormat(local.strSubs.qryPaymentTransaction.amount)# #local.strSubs.qryPaymentTransaction.detail#<br/>
						Payment Date: #dateformat(local.strSubs.qryPaymentTransaction.transactionDate,"m/d/yyyy")# #timeformat(local.strSubs.qryPaymentTransaction.transactionDate,"h:mm tt")#
					<cfelseif local.strSubs.qryPaymentTransaction.recordcount is 0 and local.strSubs.qryPaymentGateway.gatewayID is 11>
						No payment was made.
						<cfif local.totalAmount gt 0>
							<br/><br/>
							<b>Payment instructions:</b><br/>
							<cfif len(local.strSubs.qryPaymentGateway.paymentInstructions)>
								#local.strSubs.qryPaymentGateway.paymentInstructions#
							<cfelse>
								No instructions have been provided. Contact the association for payment instructions.
							</cfif>
						</cfif>
					<cfelseif local.totalAmount gt 0 and local.strSubs.qryPaymentTransaction.recordcount is 0>
						No payment was made.
					<cfelse>
						No payment was due.
					</cfif>
					<br/><br/>
					#local.PurchaserName#<br/>
					<cfif len(local.PurchaserCompany)>#local.PurchaserCompany#<br/></cfif>
					#local.PurchaserAddress#
				</div>
				<div class="mcsubs-ml-auto mcsubs-w-25 mcsubs-w-sm-100">
					<cfloop query="local.qryTodayInvoices">
						<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryTodayInvoices.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryTodayInvoices.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
						<div class="mcsubs-mb-3"><a href="javascript:viewInvoice('#local.stInvEnc#')" class="mcsubs-noprint"><i class="icon-file-text"></i> View Invoice #local.qryTodayInvoices.invoiceNumber#</a></div>
					</cfloop>
					<cfif local.strSubs.keyExists("qryAdditionalPaymentFeeInvoice")>
						<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.strSubs.qryAdditionalPaymentFeeInvoice.invoiceNumber#|#right(GetTickCount(),5)#|#local.strSubs.qryAdditionalPaymentFeeInvoice.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
						<div class="mcsubs-mb-3"><a href="javascript:viewInvoice('#local.stInvEnc#')" class="mcsubs-noprint"><i class="icon-file-text"></i> View Invoice #local.strSubs.qryAdditionalPaymentFeeInvoice.invoiceNumber#</a></div>
					</cfif>
				</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>