<cfset dataStruct = attributes.data.actionStruct>
<cfset baseQueryString = attributes.data.baseQueryString>
<cfset jsonURL = attributes.data.jsonURL>
<cfset ajaxBaseQueryString = #Replace(baseQueryString,"&","^", "ALL")# >
<cfset appRightsStruct = attributes.data.appRightsStruct>
<cfset rc = attributes.event.getCollection()>
<cfset variables.messageid = 0 />
<cfif isDefined("rc.messageid") and val(rc.messageid)>
	<cfset variables.messageid = rc.messageid />
</cfif>
<cfset variables.list = "" />
<cfif isDefined("rc.list") and len(rc.list)>
	<cfset variables.list = rc.list />
</cfif>
<cfset assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfquery name="local.dedupped" dbtype="query">
	SELECT distinct list_, descshort_
	FROM dataStruct.listBrowse.qryLists
</cfquery>
 
<cfoutput>
	<div id="listViewerLoading" style="display:none;"></div>
	<div id="listViewerSponsorContainer" style="margin-left:auto;margin-right:auto;max-width:728px;margin-bottom:15px;"></div>
	<div class="container-fluid">
		<div class="row-fluid">
			<ul class="nav nav-tabs" id="changeListViewMenu">
				<li id="browseModeLink-normal" class="active"><a href="javascript:changeBrowseMode('normal');" data-browsemode="normal">Messages</a></li>
				<li id="browseModeLink-search" class="hidden-phone"><a href="/?pg=search&searchtype=listservview">Search Lists</a></li>
				<li id="browseModeLink-threaded"><a href="javascript:changeBrowseMode('threaded');" data-browsemode="threaded">Threads</a></li>
				<cfif dataStruct.listBrowse.showDailySummariesTab>
					<li id="browseModeLink-dailySummary"><a href="javascript:changeBrowseMode('dailysummary');" data-browsemode="dailySummary">Daily Summaries</a></li>
				</cfif>
				<li id="browseModeLink-newMessage"><a href="javascript:showMessageForm();">New<span class="hidden-phone"> Post</span></a></li>
				<li id="browseModeLink-certify"><a href="#attributes.data.baseURL#&lsAction=listCertify">Email Certify</a></li>				
				<li id="browseModeLink-settings"><a href="#attributes.data.baseURL#&lsAction=listSettings"><span class="hidden-phone">Settings</span><span class="visible-phone"><i class="icon-gear"></i></span></a></li>
			</ul>
		</div>
		<cfif dataStruct.listBrowse.qryLists.recordcount eq 0>
			<div class="span12">
				<p>Your certified email addresses are not members of any list servers.<br /><br /><span>To certify your email address please select 'Email Certify' from the selection above and follow the instructions to gain access to your listservers through the #attributes.data.siteName# website.</span></p>
			</div>
		<cfelse>
			<div class="row-fluid">
				<div class="span4" id="leftPaneHeader">
					<div id="chooseListInstructions" class="well well-small" style="margin-bottom: 10px;">
						<strong>Choose a List From Below</strong>
					</div>
					<div id="chooseListLink" onclick="changeViewState('listChooser',false);" class="well well-small" style="margin-bottom: 10px;">
						<strong><i class="icon-chevron-left"></i><span class="listName"></span> (Change List)</strong>
					</div>
					<div id="backToConversationLink" onclick="changeViewState('threadlist',false);" class="well well-small" style="margin-bottom: 10px;">
						<strong><i class="icon-chevron-left"></i><span id="backToConversationLinkText"></span></strong>
					</div>
					<div id="chooseDailySummaryListLink" onclick="changeViewState('dailySummaryListChooser',false);" class="well well-small" style="margin-bottom: 10px;">
						<strong><i class="icon-chevron-left"></i><span class="dailySummaryListName"></span> (Change List)</strong>
					</div>
					<div id="closeMessageButton" onclick="closeMessage();" class="well well-small" style="margin-bottom: 10px;">
						<strong><i class="icon-chevron-left"></i><span id="closeMessageText"></span></strong>
					</div>
				</div>
				<div id="listViewerRightPaneControls" class="span8 well well-small clearfix" style="margin-bottom: 10px;">
					<div class="pull-right" id="messageBrowserButtonsLeft">
						<button id="previousMessageButton" class="btn btn-small btn-primary disabled" onclick="gotoPreviousMessage(this);"><i class="icon-chevron-left icon-white"></i> Previous</button>
						<button id="nextMessageButton" class="btn btn-small btn-primary disabled" onclick="gotoNextMessage(this);">Next <i class="icon-chevron-right icon-white"></i></button>
					</div>
				</div>
			</div>
			<div id="listBrowserMainUI" class="row-fluid">
				<div id="messageScrollWrapper" class="span4">
					<div id="messageFilterContainer" class="well well-small" style="padding:0px;margin-bottom:0px;"> 
						<form class="form" style="margin:2px;" onsubmit="return doSearch();">
							<div class="controls controls-row">
								 <input type="text" id="searchTermsField" onblur="doSearch();" class="input-block-level" style="box-sizing: border-box;" placeholder="Enter search terms">
							</div>
							<div id="messageFilterDateContainer">
								<label class="control-label">Active Between: (<span onclick="clearsearchdates();doSearch();">clear dates</span>)</label>
								<div class="controls controls-row">
									<input type="text" id="searchDateStart" onchange="doSearch();" class="span6 input-block-level" placeholder="StartDate" value="">
									<input type="text" id="searchDateEnd" onchange="doSearch();" class="span6 input-block-level" placeholder="EndDate" value="">
								</div>
							</div>
						</form>
					</div>
					<div id="messageScroll" class="scrollable">
						<table class="table table-condensed table-hover table-striped">
							<tbody id="chooseListContainer">
								<cfloop query="local.dedupped">
									<cfif local.dedupped.list_ eq variables.list>
										<cfset local.liClass = "active">
									<cfelse>
										<cfset local.liClass = "">
									</cfif>
									<tr id="list-#local.dedupped.list_#" data-listname="#local.dedupped.list_#" onclick="chooseList(this);">
										<td class="span12">
											<span class=""><strong>#ucase(local.dedupped.list_)#</strong></span><br />
											<span class="">#local.dedupped.descshort_#</span>
										</td>
									</tr>
								</cfloop>
							</tbody>
							<cfif dataStruct.listBrowse.showDailySummariesTab>
								<tbody id="chooseDailySummaryListContainer">
								<cfoutput query="dataStruct.listBrowse.qryLaunchedMCThreadIndexOrDigestLists" group="listID">
									<tr id="dailySummaryList_#dataStruct.listBrowse.qryLaunchedMCThreadIndexOrDigestLists.listID#" data-listname="#dataStruct.listBrowse.qryLaunchedMCThreadIndexOrDigestLists.list_#" data-listid="#dataStruct.listBrowse.qryLaunchedMCThreadIndexOrDigestLists.listID#" onclick="chooseList(this);">
										<td class="span12">
											<span class=""><strong>#ucase(dataStruct.listBrowse.qryLaunchedMCThreadIndexOrDigestLists.list_)#</strong></span><br />
											<span class="">#dataStruct.listBrowse.qryLaunchedMCThreadIndexOrDigestLists.descshort_#</span>
										</td>
									</tr>
								</cfoutput>
								</tbody>
							</cfif>
							<tbody id="messageListContainer"></tbody>
							<tbody id="messageThreadContainer"></tbody>
							<tbody id="messageDailySummaryContainer"></tbody>
						</table>
						<div id="endOfMessageList" class="">
						<div id="loadingIndicator" class="progress progress-striped active">
						  <div class="bar" style="width: 100%;"><strong>Loading Messages</strong></div>
						</div>
						</div>
						<br />
					</div>
				</div>
				<div id="listViewerRightPane" class="span8">
					<div id="messageDetails" class="scrollable"></div>					
				</div>				
			</div>
			<iframe id="MCcontenttoprint" class="row-fluid"></iframe>
			<div class="postMessageContent container-fluid">
				
			</div>
		</cfif>
	</div>



	<cfset anHourFromNow = dateadd("h",1,now())>
	<cfsavecontent variable="local.gridJS">

		<script type="text/javascript" src="/assets/common/javascript/cfform.js"></script>
		<script type="text/javascript" src="/CFIDE/scripts/masks.js"></script>
		<script type="module">
			import { Uppy,DragDrop, StatusBar, XHRUpload } from "//releases.transloadit.com/uppy/v3.6.1/uppy.min.mjs";
			window.Uppy = Uppy;
			window.DragDrop = DragDrop;
			window.StatusBar = StatusBar;
			window.XHRUpload = XHRUpload;
		</script>
		<script type='text/javascript'>
			var screenLayout = 'desktop';
			var jsonURL = '#jsonURL#';
			var baseURL = '#attributes.data.baseURL#';
			var orig_isBefore = '#dateFormat(anHourFromNow,"mm/dd/yyyy")# #timeFormat(anHourFromNow,"HH:mm")#';
			var isBefore = orig_isBefore;
			var isAfter = '1/1/1997';

			var thread_isBefore = '#dateFormat(anHourFromNow,"mm/dd/yyyy")# #timeFormat(anHourFromNow,"HH:mm")#';
			var orig_isAfter = '1/1/1997';
			var thread_threadID = 0;
			var currentMessageID = 0;
			var currentList = '';
			var searchterms = '';
			var searchdatestart = '';
			var searchdateend = '';
			var searchmessageid = 0;

			var currentDailySummaryMessageID = 0;
			var currentDailySummaryList = '';
			var currentDailySummaryListID = 0;
			var dailySummaryList_posStart = 0;
			var scrollPosition_dailySummaryMsgList=0;
			var endOfList_dailySummaryMsgList=false;
			var currentlyLoadingDailySummaryMessages = false;
			var isInitialDailySummaryLoadDone = false;

			var previousMessageButton;
			var nextMessageButton;
			var closeMessageButton;
			var messageDetails;
			var messageScroll;
			var scrollPosition_messagelist=0;
			var scrollPosition_threadlist=0;
			var endOfList_messagelist=false;
			var endOfList_threadlist=false;
			var isInitialLoadDone = false;
			var isInitialThreadLoadDone = false;
			var currentlyLoadingMessages = false;
			var currentlyLoadingThreads = false;
			var checkForListPostOnload = false;
			var autoLoadFirstMessage = true;
			var viewState = '';
			var previousViewState = '';
			var browseMode = '';
			var attachmentUploader;
			var list;
			var email;
			var subject;
			var messagebox; 
			

			var initialScripts = [];
			initialScripts.push(MCLoader.loadJS("/assets/common/javascript/enquire.js/2.0.2/enquire.min.js#assetCachingKey#"));
			initialScripts.push(MCLoader.loadJS("/assets/common/javascript/jQueryAddons/inview/jquery.inview.min.js#assetCachingKey#"));
			initialScripts.push(MCLoader.loadJS("/assets/common/javascript/jQueryAddons/history.js/1.8b2/scripts/bundled/html4+html5/jquery.history.js#assetCachingKey#"));
			Promise.all(initialScripts)
				.then(function(){
					return MCLoader.loadJS("/assets/common/javascript/mcapps/listviewer/listviewer-common.js#assetCachingKey#")
				})
				.then(function(){
					return MCLoader.loadJS("/assets/common/javascript/mcapps/listviewer/responsive-listviewer.js#assetCachingKey#")
				})
				.then(initApp);


			function recordState(title){
				var url = '';
				url = baseURL + 
					'&currentMessageID=' + currentMessageID +
					'&thread_threadID=' + thread_threadID +
					'&currentList=' + currentList +
					'&viewState=' + viewState +
					'&browseMode=' + browseMode +
					'&isBefore=' + isBefore +
					'&thread_isBefore=' + thread_isBefore +
					'&orig_isBefore=' + orig_isBefore +
					'&isAfter=' + isAfter +
					'&searchterms=' + searchterms +
					'&searchdatestart=' + searchdatestart +
					'&searchdateend=' + searchdateend;

			}

			function initApp(){
				previousMessageButton = $('##previousMessageButton');
				nextMessageButton = $('##nextMessageButton');
				messageDetails = $('##messageDetails');
				messageScroll = $('##messageScroll');
				closeMessageButton = $('##closeMessageButton');			
				

				$('##messageListContainer').on('click','tr', function() {
					$link = $(this).find('td a');
					if($link.length > 0)
						$link[0].click();
				});

				$('##messageListContainer').on('click','tr td a', function(event) {
					event.stopPropagation();
					var messageRow = $(this).closest('.messageRowClass');
					getMessage(messageRow, messageRow.data("messageid"));						
				});

				$('##messageThreadContainer').on('click','tr', function() {
					getMessageThread(this,$(this).data("threadid"));
				});

				$('##messageDailySummaryContainer').on('click','tr', function() {
					event.stopPropagation();
					var messageRow = $(this).closest('.messageRowClass');
					getDailySummaryMessage(this,$(this).data("messageid"));
				});

				updateListname(currentList);

				$('##endOfMessageList').bind('inview', function(event, isInView, visiblePartX, visiblePartY) {
				switch (browseMode) {
					case "threaded":
						if (isInView && isInitialThreadLoadDone && (isViewState('threadlist') )) {
							// element is now visible in the viewport
							loadThreads();
						} else {
							//element has gone out of viewport
						}
						break;
					case "normal":
						if (isInView && isInitialLoadDone && (isViewState('messagelist') || isViewState('viewMessage'))) {
							// element is now visible in the viewport
							loadMessages();
						} else {
							//element has gone out of viewport
						}
						break;
					case "dailysummary":
						if (isInView && isInitialDailySummaryLoadDone && (isViewState('dailySummaryMessageList') || isViewState('viewDailySummaryMessage'))) {
							// element is now visible in the viewport
							loadDailySummaryMessages();
						} else {
							//element has gone out of viewport
						}
						break;
				}
				});

				moment.lang('en', {
						calendar : {
								lastDay : '[Yesterday at] LT',
								sameDay : '[Today at] LT',
								nextDay : '[Tomorrow at] LT',
								lastWeek : '[Last] ddd [at] LT',
								nextWeek : 'dddd [at] LT',
								sameElse : 'M/D/YY h:mm A'
						}
				});
				changeBrowseMode('normal');
				<cfif attributes.event.getValue('nAction','') eq 'listThread'>
					changeBrowseMode('threaded');
				</cfif>

				<cfswitch expression="#attributes.event.getValue('lsAction','')#">
					<cfcase value="listPost">
						changeViewState ('listChooser', true);
						autoLoadFirstMessage = false;
						showMessageForm();
					</cfcase>
					<cfcase value="listThread">
						changeViewState ('listChooser', true);
						changeBrowseMode('threaded');

						<cfif len(attributes.event.getTrimValue('searchdatestart','')) or len(attributes.event.getTrimValue('searchdateend',''))>
							$('##searchDateStart').val('#DateFormat(attributes.event.getTrimValue('searchdatestart',''),"m/d/yyyy")#');
							$('##searchDateEnd').val('#DateFormat(attributes.event.getTrimValue('searchdateend',''),"m/d/yyyy")#');
							searchdatestart = $('##searchDateStart').val();
							searchdateend = $('##searchDateEnd').val();
						</cfif>
						<cfif len(attributes.event.getTrimValue('lslist',''))>
							if ($('tr##list-#attributes.event.getTrimValue('lslist')#')[0]) {
								$('tr##list-#attributes.event.getTrimValue('lslist')#')[0].click();
								<cfif val(attributes.event.getTrimValue('lsMessageID',0)) gt 0>
									searchmessageid = #int(val(attributes.event.getTrimValue('lsMessageID')))#;
									$('##listViewerLoading').show();
									retryLoadThreads();
								</cfif>
							}
						</cfif>
					</cfcase>
					<cfcase value="dailySummary">
						changeBrowseMode('dailysummary');
					</cfcase>
				</cfswitch>
			}

			function listBrowseDelay(t) {
				return new Promise(function(resolve, reject) {
					setTimeout(resolve, t);
				});
			}

			function retryLoadThreads() {
				return new Promise(function(resolve, reject) {
					loadThreads().then(function(response) {
						resolve();
					}).catch(function() {
						listBrowseDelay(500).then(retryLoadThreads);
					});
				});
			}
			
			function doSearch(){
				searchterms = $('##searchTermsField')[0].value;
				searchdatestart = $('##searchDateStart')[0].value;
				searchdateend = $('##searchDateEnd')[0].value;
			
				switch (browseMode) {
					case "threaded":
					case "normal":
						reloadList(currentList);
					break;
					case "dailysummary":
						reloadList(currentDailySummaryList);
					break;
				};

				document.activeElement.blur();
				return false;
			}

			function clearsearchdates(){
				searchdatestart = '';
				searchdateend = '';
				if ($('##searchDateStart')[0])
					$('##searchDateStart')[0].value = '';
				if ($('##searchDateEnd')[0])
					$('##searchDateEnd')[0].value = '';
			}


			function clearSearch(){
				searchterms = '';
				if ($('##searchTermsField')[0])
					$('##searchTermsField')[0].value = '';
				clearsearchdates();
				return false;
			}

			function showMessageForm() {
				$('##browseModeLink-normal').removeClass('active');
				$('##browseModeLink-search').removeClass('active');
				$('##browseModeLink-threaded').removeClass('active');
				$('##browseModeLink-newMessage').addClass('active');
				
				mcg_replyToList(#variables.messageid#,#attributes.data.instanceSettings.applicationInstanceID#);
				checkForListPostOnload = false;
			}		
			
			function loadMessages() {
				if (!currentlyLoadingMessages && !endOfList_messagelist) {

					currentlyLoadingMessages = true;

					$('##loadingIndicator').show();
					$.getJSON( jsonURL + '&m=getMessages',{aID:#attributes.data.instanceSettings.applicationInstanceID#, list:currentList,isBefore:moment(isBefore).format('MM/DD/YYYY HH:mm:ss'),messageCount:"50", threadID:thread_threadID,isAfter:moment(isAfter).format('MM/DD/YYYY HH:mm:ss'), searchterms:searchterms, searchdatestart:searchdatestart, searchdateend:searchdateend})
					.done(function( data ) {
						var messageDisplay = [];
						
						if(!data.STATUS || $.inArray(data.STATUS, ['notloggedin','invalid','success']) == -1){
							alert('We were unable to load the messages.');
							$('##loadingIndicator').hide();
							reject();
						}

						if(data.STATUS && data.STATUS.length > 0 && data.STATUS == "notloggedin"){
							location.href= "/?pg=login&returnurl=#urlEncodedFormat('/?pg=listViewer')#";
						} else {
							if (data.MESSAGES.length == 0) {
								endOfList_messagelist = true;
							} else {
								if (data.THREADID == 0 && data.EARLIESTMESSAGEINFO.DATECREATED.length > 0 && moment(data.EARLIESTMESSAGEINFO.DATECREATED).isValid())
									isBefore = moment(data.EARLIESTMESSAGEINFO.DATECREATED, "MMM, D YYYY HH:mm:ss").toDate();
								else if (data.THREADID > 0 && data.LATESTMESSAGEINFO.DATECREATED.length > 0 && moment(data.EARLIESTMESSAGEINFO.DATECREATED).isValid())
									isAfter = moment(data.LATESTMESSAGEINFO.DATECREATED, "MMM, D YYYY HH:mm:ss").toDate();

								$.each(data.MESSAGES, function(itemID, item) {
									messageDisplay.push(
									'<tr id="messageID' + item.MESSAGEID + '" data-messageid="' + item.MESSAGEID + '" class="messageRowClass">'+
										'<td class="span12">' +
											'<a href="##msg_'+item.MESSAGEID+'" style="text-decoration: none; cursor:default; color: inherit; font-weight:inherit">'+
												'<span class="pull-right">' + moment(item.DATECREATED).calendar() + '</span>' +
												'<span class=""><strong>' + cleanFromHeader(item.HEADERFROM,item.FROMEMAIL) + '</strong></span><br />' +
												'<span class="">' + (item.HASATTACHMENT == 1 ? '<span class="label"><i class="icon-file icon-white"></i> Attachment</span> ' : '') + item.SUBJECT + '</span>' +
											'</a>'+
										'</td>'+
									'</tr>');
								});
							}
							
							$('##messageListContainer').append(messageDisplay.join(''));
							updateMessageBrowserButtons(false);
							isInitialLoadDone = true;
							$('##loadingIndicator').hide();
			
							
							currentlyLoadingMessages = false;
							if (!$('##messageListContainer tr.success + tr').length > 0 && screenLayout != 'mobile' && autoLoadFirstMessage){
								gotoFirstMessage();
							}
						}

					})
					.fail(function( jqxhr, textStatus, error ) {
					  var err = textStatus + ', ' + error;
					  //console.log( "Request Failed: " + err);
					  $('##loadingIndicator').hide();
					});
				}
			}
			
			function loadThreads() {
				return new Promise(function(resolve, reject) {
					if (!currentlyLoadingMessages && !endOfList_threadlist) {

						currentlyLoadingMessages = true;
						$('##loadingIndicator').show();
						$.getJSON( jsonURL+ '&m=getThreads',{aID:#attributes.data.instanceSettings.applicationInstanceID#,list:currentList,isBefore:moment(thread_isBefore).format('MM/DD/YYYY HH:mm:ss'),messageCount:"50",searchterms:searchterms, searchdatestart:searchdatestart, searchdateend:searchdateend})
						.done(function( data ) {
							var messageDisplay = [];

							if(!data.STATUS || $.inArray(data.STATUS, ['notloggedin','invalid','success']) == -1){
								alert('We were unable to load the threads.');
								$('##loadingIndicator').hide();
								reject();
							}

							if(data.STATUS && data.STATUS.length > 0 && data.STATUS == "notloggedin"){
								location.href= "/?pg=login&returnurl=#urlEncodedFormat('/?pg=listViewer')#";
							} else {
								if (data.MESSAGES.length == 0) {
									endOfList_threadlist = true;
								} else {
									if (data.EARLIESTMESSAGEINFO.DATECREATED.length > 0 && moment(data.EARLIESTMESSAGEINFO.DATECREATED).isValid())
										thread_isBefore = moment(data.EARLIESTMESSAGEINFO.DATECREATED, "MMM, D YYYY HH:mm:ss").toDate();
									$.each(data.MESSAGES, function(itemID, item) {
										messageDisplay.push(
										'<tr id="threadID' + item.THREADID + '" data-threadid="' + item.THREADID + '"><td class="span12">' +
											'<span class="pull-right">' + moment(item.DATECREATED).calendar() + '</span>' +
											'<span class=""><strong>' + cleanFromHeader(item.HEADERFROM,item.FROMEMAIL) + ' (' + item.MESSAGECOUNT + ') <i class="icon-chevron-right"></i></strong></span><br />' +
											'<span class="">' + (item.HASATTACHMENT == 1 ? '<span class="label"><i class="icon-file icon-white"></i> Attachment</span> ' : '') + item.SUBJECT + '</span>' +
										'</td></tr>');
									});
								}
								$('##messageThreadContainer').append(messageDisplay.join(''));
								updateMessageBrowserButtons(false);
								
								isInitialThreadLoadDone = true;
								$('##loadingIndicator').hide();
								currentlyLoadingMessages = false;

								if (!endOfList_threadlist && searchmessageid > 0) {
									if ($('tr##threadID'+searchmessageid)[0]) {
										var threadRow = $('tr##threadID'+searchmessageid);
										threadRow.trigger('click');
										$('##listViewerLoading').hide();
										searchmessageid = 0;
										resolve();
									} else {
										reject();
									}
								} else {
									$('##listViewerLoading').hide();
									resolve();
								} 
								
							}
						})
						.fail(function( jqxhr, textStatus, error ) {
							var err = textStatus + ', ' + error;
							$('##loadingIndicator').hide();
							reject();
						});
					} else {
						reject();
					}
				});
			}
			function updateMessageBrowserButtons(checkScrollPosition){
				if ($('##messageListContainer tr').length){
					if ($('##messageListContainer tr.success + tr').length)
						$('##nextMessageButton').removeClass("disabled");
					if ($('##messageListContainer tr.success').prev('tr').length)
						$('##previousMessageButton').removeClass("disabled");
					
					if (checkScrollPosition){
						switch (screenLayout){
							case "desktop":
								if (!(isScrolledIntoView($('##messageListContainer tr.success')[0],document.getElementById('messageScroll')))) {
									var topPos = $('##messageListContainer tr.success')[0].offsetTop;
									document.getElementById('messageScroll').scrollTop = topPos;
								}
								break;
							
						}
					}
				}
				if (($('##messageListContainer tr.success ~ tr').length < 15) && isInitialLoadDone) {
					loadMessages();
				}
			}
			function loadDailySummaryMessages() {
				if (!currentlyLoadingDailySummaryMessages && !endOfList_dailySummaryMsgList) {
					currentlyLoadingDailySummaryMessages = true;
					$('##loadingIndicator').show();

					var objParams = { appInstanceID:#attributes.data.instanceSettings.applicationInstanceID#,
						listID:currentDailySummaryListID, posStart:dailySummaryList_posStart, messageCount:50, searchterms:searchterms,
						searchdatestart:searchdatestart, searchdateend:searchdateend
					};
					
					$.getJSON( jsonURL + '&m=getDailySummaryMessages', objParams)
						.done(function( data ) {
						
							if (data.success) {
								var messageDisplay = [];

								if (data.arrmessages.length == 0) {
									endOfList_dailySummaryMsgList = true;
								} else {
									dailySummaryList_posStart += data.arrmessages.length;

									$.each(data.arrmessages, function(itemID, item) {
										messageDisplay.push(
										'<tr id="messageID' + item.messageid + '" data-messageid="' + item.messageid + '" class="messageRowClass">'+
											'<td class="span12">' +
												'<a href="##msg_'+item.messageid+'" style="text-decoration: none; cursor:default; color: inherit; font-weight:inherit">'+
													'<span class="pull-right">' + moment(item.sendondate).calendar() + '</span>' +
													'<span class=""><strong>' + cleanFromHeader(item.fromname,item.fromemail) + '</strong></span><br />' +
													'<span class="">' + item.subject + '</span>' +
												'</a>'+
											'</td>'+
										'</tr>');
									});
									$('##messageDailySummaryContainer').append(messageDisplay.join(''));
								}
								
								updateDailySummaryMessageBrowserButtons(false);
								isInitialDailySummaryLoadDone = true;
								$('##loadingIndicator').hide();

								currentlyLoadingDailySummaryMessages = false;
								if (!$('##messageDailySummaryContainer tr.success + tr').length > 0 && screenLayout != 'mobile' && autoLoadFirstMessage){
									gotoFirstMessage();
								}
							} else if (data.status && data.status.length > 0 && data.status == "notloggedin") {
								location.href= "/?pg=login&returnurl=#urlEncodedFormat('/?pg=listViewer')#";
							} else {
								alert('We were unable to load daily summary messages.');
								$('##loadingIndicator').hide();
							}
						})
						.fail(function( jqxhr, textStatus, error ) {
							$('##loadingIndicator').hide();
						});
				}
			}
			function updateDailySummaryMessageBrowserButtons(checkScrollPosition){
				if ($('##messageDailySummaryContainer tr').length){
					if ($('##messageDailySummaryContainer tr.success + tr').length)
						$('##nextMessageButton').removeClass("disabled");
					if ($('##messageDailySummaryContainer tr.success').prev('tr').length)
						$('##previousMessageButton').removeClass("disabled");
					
					if (checkScrollPosition){
						switch (screenLayout){
							case "desktop":
								if (!(isScrolledIntoView($('##messageDailySummaryContainer tr.success')[0],document.getElementById('messageScroll')))) {
									var topPos = $('##messageDailySummaryContainer tr.success')[0].offsetTop;
									document.getElementById('messageScroll').scrollTop = topPos;
								}
								break;
							
						}
					}
				}
				if (($('##messageDailySummaryContainer tr.success ~ tr').length < 15) && isInitialDailySummaryLoadDone) {
					loadDailySummaryMessages();
				}
			}
		</script>
		<script type="text/javascript">
			if (window.ColdFusion) ColdFusion.required['subject']=true;
		</script>
		<script type="text/javascript">
			
			function getMessage(messagerow, mID) {
				if (!currentlyLoadingMessages) {
					$('##messageListContainer tr.success').removeClass('success');
					$(messagerow).addClass('success');
					$('##previousMessageButton').addClass('disabled');
					$('##nextMessageButton').addClass('disabled');
	
					currentMessageID = mID;	
					
					var getMessageResult = function(data) {
						if (data.success && data.success == true)
						{
							document.getElementById('messageDetails').innerHTML = data.resulthtml;
						}
						else
						{
						 	document.getElementById('messageDetails').innerHTML = 'An error occurred fetching message';
						}
						updateMessageBrowserButtons(true);					
					};
					document.getElementById('messageDetails').innerHTML = '<div align="center"><img src="/assets/common/images/indicator.gif" alt="Loading..." width="100" height="100"></div>';
					changeViewState ('viewMessage',false);
					recordState('View Message');
	
					var objParams = { messageID:mID, appInstanceID:#attributes.data.instanceSettings.applicationInstanceID#, orgCode:"#attributes.data.instanceSettings.orgcode#", baseURL:"#ajaxBaseQueryString#", isResponsive:true };

					var default_jsonurl = '/?event=proxy.ts_json&c=LISTVIEWER&m=getMessage';
					$.getJSON( default_jsonurl, objParams)
						.done(getMessageResult)
						.fail(getMessageResult);	
				}
			}

			function getMessageThread(messagerow, tID) {
				if (!currentlyLoadingMessages) {
					$('##messageThreadContainer tr.success').removeClass('success');
					$(messagerow).addClass('success');
					 
					clearMessageList();
					endOfList_messagelist = false;
					thread_threadID = tID;
					isBefore = orig_isBefore;
					isAfter = orig_isAfter;
					scrollPosition_messagelist=0;
					loadMessages();
					changeViewState('threadmessages', true);
					recordState('View Thread');
				}
			}

			function scollIframeToAnchor(e, iframeContents) {
				if($(e.currentTarget).attr('href').startsWith('##')) {
					var hash = $(e.currentTarget).attr('href').substr(1);
					var hashanchorselector = ("a[name='" + hash + "']")

					var targetElement = $(iframeContents).find(hashanchorselector);
					$(iframeContents).find('html,body').stop().animate({ scrollTop: $(targetElement).offset().top }, 300, 'swing');
					e.preventDefault();
				}
			}

			function getDailySummaryMessage(messagerow, mID) {
				if (!currentlyLoadingDailySummaryMessages) {
					$('##messageDailySummaryContainer tr.success').removeClass('success');
					$(messagerow).addClass('success');
					$('##previousMessageButton').addClass('disabled');
					$('##nextMessageButton').addClass('disabled');
					
					var getMessageResult = function(data) {
						if (data.success && data.success == true){
							$('##messageDetails').addClass('dailySummaryMessageDetails');
							$('##messageDetails').html('<iframe id="MCDailySummaryMessage" width="100%" height="100%" frameborder="0" onload="resizeDailySummaryMessage();"></iframe>');
							var iframe = document.getElementById('MCDailySummaryMessage');
							iframe = iframe.contentWindow || ( iframe.contentDocument.document || iframe.contentDocument);
							iframe.document.open();
							iframe.document.write(data.resulthtml);
							iframe.document.close();

							//all anchor links in iframe should scroll iframe to target 
							$('##MCDailySummaryMessage').contents().find('body').on('click', 'a', function(e) {scollIframeToAnchor(e,$('##MCDailySummaryMessage').contents())});
							//all links (except anchor links and anchor names) should open in new window
							$('##MCDailySummaryMessage').contents().find('a').not("a[href^='##']").not("a[name]").attr('target','_blank');
						} else {
						 	$('##messageDetails').html('An error occurred fetching message');
						}
						updateDailySummaryMessageBrowserButtons(true);					
					};
					
					$('##messageDetails').html('<div align="center"><img src="/assets/common/images/indicator.gif" alt="Loading..." width="100" height="100"></div>');
					changeViewState('viewDailySummaryMessage',false);
						
					var objParams = { messageID:mID };

					var jsonurl = '/?event=proxy.ts_json&c=LISTVIEWER&m=getDailySummaryMessage';
					$.getJSON(jsonurl, objParams).done(getMessageResult).fail(getMessageResult);
				}
			}
			function resizeDailySummaryMessage() {
				switch (screenLayout){
					case "mobile":
						var iFrameID = document.getElementById('MCDailySummaryMessage');
						if(iFrameID) {
							iFrameID.height = "";
							iFrameID.height = iFrameID.contentWindow.document.body.scrollHeight + "px";
						}   
					break;
					case "desktop":
					break;
				};
			}


			function mcg_replyToList(mID, aID) {
				var getReplyToListResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {					
					 	document.getElementById('messageDetails').innerHTML = r.resulthtml;
						 if (r.hasOwnProperty('maxattachmentbytes'))
							uppyMaxTotalFileSize = r.maxattachmentbytes;
						preparePostMessageScreen();
					}
					else {
					 	document.getElementById('messageDetails').innerHTML = 'An error occurred creating the reply message';
					}
					window.location.hash = 'PostMessage';
				};
				
				document.getElementById('messageDetails').innerHTML = '<div align="center"><img src="/assets/common/images/indicator.gif" alt="Loading..." width="100" height="100"></div>';
				changeViewState ('postMessage',false);
				recordState('Post Message');
				var objParams = { messageID:mID, appInstanceID:aID, baseURL:"#ajaxBaseQueryString#", isResponsive:true };
				TS_AJX('LISTVIEWER','getListPost',objParams,getReplyToListResult,getReplyToListResult,10000,getReplyToListResult);
			}

			function preparePostMessageScreen(){
				if ($("##listSelector").length) {
					var selectedList = $("##listSelector")[0].value;
					if (!selectedList.length) {
						$('.hideUntilListSelected').hide();
					} else {
						$('.hideUntilListSelected').show();
						listPostloadUppy();
					}
				}
			}

			function onChangeListSelectionForPost() {
				mcg_submit(document.lyrisSend.list.value,'', document.lyrisSend.subject.value,'','', document.lyrisSend.messagebox.value);
			}
			
			function mcg_submit(list,email,subject,inreplyto,references,messagebox) {
				var submitForm = function(r) {
					if (r.success && r.success.toLowerCase() == 'true')
					{
					 	document.getElementById('messageDetails').innerHTML = r.resulthtml;
						 if (r.hasOwnProperty('maxattachmentbytes'))
							uppyMaxTotalFileSize = r.maxattachmentbytes;
						preparePostMessageScreen(); 
					}
					else
					{
					 	document.getElementById('messageDetails').innerHTML = 'An error occurred sending your message';
					}
				};
	
				document.getElementById('messageDetails').innerHTML = '<div align="center"><img src="/assets/common/images/indicator.gif" alt="Loading..." width="100" height="100"></div>';
				var m = replaceUnicode(messagebox);
				var subj = replaceUnicode(subject);
				var destination =uppyDestination;
				var objParams = { messageID:0, appInstanceID:#attributes.data.instanceSettings.applicationInstanceID#, baseURL:"#ajaxBaseQueryString#",list:list,email:email,subject:subj,inreplyto:inreplyto,references:references,messagebox:m,isresponsive:true,destination:destination};
				TS_AJX('LISTVIEWER','getListPost',objParams,submitForm,submitForm,10000,submitForm);
			}

		<!--- ------
			DPC, 3/25/10: removed the CFFORM because it was messing up the AJAX call for the "Reply To List" function.
			This the validation code generated by CF.	
		----- --->

	    _CF_checklyrisSend = function()
	    {		

    		var validationPassed = true;

    		$("##lyrisSendBtn").attr('disabled','disabled');    	

	        //form element subject required check
	        if(document.lyrisSend.list.value == '' )
	        {
	           alert('Please choose a list');
	           document.lyrisSend.list.focus();
	            validationPassed = false;
	        } else if(document.lyrisSend.email.value == '' )
	        {
	           alert('Please choose an email address');
	           document.lyrisSend.email.focus();
	            validationPassed = false;
	        } else if(document.lyrisSend.subject.value == '' )
	        {
	           alert('Please enter a subject');
	           document.lyrisSend.subject.focus();
	            validationPassed = false;
	        } else {
				if ((document.lyrisSend.list.value != '') && (document.lyrisSend.email.value != '') && (document.lyrisSend.list.subject == '')) {
					alert('Please select a sending address');
					validationPassed = false;
				} else if (uppy && uppy.getFiles()) {
					ajaxPromise({
						url: '#dataStruct.prepareAttachmentFolderProxyLink#',
						type: 'get',
						contentType: 'application/json; charset=utf-8'})
						.then((result) => {
							listPostSetUppyPluginOptions('XHRUpload',{ endpoint: result.proxyLink});
							uppyDestination = result.encryptString;
							return uppy.retryAll();
						}).then((result) => uppy.upload())
						.then((result) => {
							console.info('Successful uploads:', result.successful);
							if (result.failed.length > 0) {
								console.error('Errors:');
								result.failed.forEach((file) => {
									console.error(file.error);
								});
								validationPassed = false;
								$("##lyrisSendBtn").removeAttr("disabled")
							} else {
								validationPassed = true;
								mcg_submit(document.lyrisSend.list.value, document.lyrisSend.email.value, document.lyrisSend.subject.value, document.lyrisSend.inreplyto.value,document.lyrisSend.references.value, document.lyrisSend.messagebox.value);
							}
							return true;
						}).catch(error => {
							console.log(error);
							validationPassed = true;
						});
				} else {
					validationPassed = true;
					mcg_submit(document.lyrisSend.list.value, document.lyrisSend.email.value, document.lyrisSend.subject.value,document.lyrisSend.inreplyto.value,document.lyrisSend.references.value, document.lyrisSend.messagebox.value);
				}
			}

			if (!validationPassed) {
				$("##lyrisSendBtn").removeAttr("disabled");						
			}
			return validationPassed;	        
	    }
	    
	    function loadGrid() {
			<cfif attributes.event.getValue('lsAction','') eq 'listPost'>
				if (checkForListPostOnload = true) {
					mcg_replyToList(#variables.messageid#,#attributes.data.instanceSettings.applicationInstanceID#);
					preparePostMessageScreen();
					checkForListPostOnload = false;
				}
			</cfif>
		}
		
		var checkForListPostOnload = true;

		$(function() {
			mca_setupDatePickerRangeFields('searchDateStart','searchDateEnd');
		});
		</script>
		<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/datetimepicker/jquery.datetimepicker.min.css"/>
		<script src="/assets/common/javascript/jQueryAddons/datetimepicker/jquery.datetimepicker.full.min.js"></script>
		<link rel="stylesheet" type="text/css" href="https://releases.transloadit.com/uppy/v3.6.1/uppy.min.css"/>
		<style type="text/css">

			##listBrowserMainUI {
				/* display: flex; */
			}

			##listPostAttachmentsDragDrop .uppy-DragDrop-inner {
				padding: 0;
			}
			##listPostAttachmentsDragDrop .uppy-DragDrop-inner svg {
				display: none;
			}
			##listPostAttachmentsFileList > .label {
				margin: 0.25rem 0.25rem;
			}

</style>
	</cfsavecontent>
	<cfhtmlhead text="#local.gridJS#">
	
	<cfsavecontent variable="jsoncode">

		<style type="text/css">

			##MCcontenttoprint {height: 0px; width: 0px; position: absolute;border-top-width: 0px;border-right-width: 0px;border-bottom-width: 0px;border-left-width: 0px;}
			##messageScroll {height:510px;overflow-y:auto;}
			##messageDetails {height:510px;margin-top:6px;margin-bottom:20px;overflow:auto;padding-right: 20px;}
			##closeMessageButton {display:none;}
			##loadingIndicator {display:none;}
			##chooseListLink, ##chooseDailySummaryListLink {display:none;}
			##chooseListInstructions {display:none;}
				
			##backToConversationLink {display:none;}
			##chooseListContainer, ##chooseDailySummaryListContainer {display:none;}
			##listViewerRightPaneControls {display: none;}
			##messageFilterContainer {display: none;
				-moz-box-sizing: border-box;
				-ms-box-sizing: border-box;
				-o-box-sizing: border-box;
				-webkit-box-sizing: border-box;
				box-sizing: border-box;
			}
			##messageFilterContainer  input{
				-moz-box-sizing: border-box!important;
				-ms-box-sizing: border-box!important;
				-o-box-sizing: border-box!important;
				-webkit-box-sizing: border-box!important;
				box-sizing: border-box!important;
			}
			##changeListViewMenu {margin-bottom: 10px;display:none;}
			
			.scrollable {
				overflow-y: scroll;
	        	-webkit-overflow-scrolling: touch;
			}
			
			##listViewerControls {
				position:relative;
				width:100%;
			}
			##listViewerControls.sticky  {
				position:fixed;
				top:50;
				box-shadow:0 2px 4px rgba(0, 0, 0, .3);
			}
			
			##messageListContainer tr a:visited { 
				color: ##999 !important;
			}

			##searchDateStart, ##searchDateEnd { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }

			##listViewerLoading {
				position:fixed;
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				width: 100%;
				height: 100%;
				background: ##fff url('/assets/common/images/indicator.gif') no-repeat center center;
				z-index: 9999;
				opacity:0.4;
				cursor:progress;
			}

			.dailySummaryMessageDetails {height:620px !important;overflow:hidden !important;padding-right:0 !important;margin-top:0 !important;}

			/* Landscape phones and down */
			@media (max-width: 480px) {
			 	.hidden-smallphone {display:none;}
			 }
			/* Landscape phone to portrait tablet */
			@media (max-width: 767px) {
				
				##messageScroll {height:auto;overflow-y:visible;}
				##messageDetails {height:auto;overflow:visible;display:none;padding-right: 0px;}
				.dailySummaryMessageDetails {height:auto !important;overflow:visible !important;}
				##previousMessageButton, ##previousMessageButton, ##nextMessageButton  {display:none;}
				##messageFilterContainer .controls-row [class*="span"] + [class*="span"] {
					margin-left: 0!important;
				}
			
			}
			 
			/* Portrait tablet to landscape and desktop */
			@media (min-width: 768px) and (max-width: 940px) {}
			 
			/* Default: 940px and up  */
			 
			/* Large desktop */
			@media (min-width: 1210px) {}
		
		</style>
	</cfsavecontent>
	<cfhtmlhead text="#jsoncode#">
	<cfsavecontent variable="listviewerAdJS">
		<cfoutput>
			<script type="text/javascript">
				$(document).ready(function() {
					MCPromises.BackendPlatformServices.then(function() {
						var MCadlimit = 1;
						var MCadContainer = $("##listViewerSponsorContainer");
						var MCadSitecode = '#attributes.data.instanceSettings.sitecode#';
						var MCadZoneType = 'listviewer';

						MCBackendPlatformServices.SponsorAdsService.MCIncludeAds(MCadContainer, MCadZoneType,MCadSitecode,MCadlimit,'_blank',null);
					}).catch(error => {
						let msg = 'Failed to get ads for listViewerSponsorContainer';
						if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
							MCJSErrorReporting.promiseRejectionHandler(msg)
						else 
							console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`);
					});
				});
			</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#listviewerAdJS#">	
	
</cfoutput>