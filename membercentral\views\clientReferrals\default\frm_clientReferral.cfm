<cfparam name="local.msgText" default="" />
<cfsavecontent variable="local.clientFormJS">
	<cfoutput>
        <style type="text/css">
            ##clientFormStyle {padding:5px 0 15px 20px; width:80%; }
            ##clientFormStyle input, ##clientFormStyle select, ##clientFormStyle textarea { 
                font-family:"Trebuchet MS", Arial, Helvetica, Geneva, sans-serif; 
                font-size:12px; 
                line-height:1.2em; 
                color:##333; }	
            ##clientFormTbl th, ##clientFormTbl td {  height:30px; }
            ##clientFormTbl th.longDesc, ##clientFormTbl td.longDesc { height:78px; }
            ##feesDueTbl { border: 1px solid ##cccccc; margin-left:10px; border-collapse:collapse;}
            ##feesDueTbl tr.ev_modern { background-color:##dddee3; }
            ##feesDueTbl tr.odd_modern { background-color:##fff; }
            ##feesDueTbl th { background-color:##cccccc; }
            ##feesDueTbl th { height:30px; }
            ##feesDueTbl td { height:20px; }
            ##feesDueTbl th.longDesc, ##feesDueTbl td.longDesc { height:78px; }
            ##feesDueTbl td.totals { padding-left:10px; font-weight:bold; border-top:1px solid ##CCCCCC; }
            .saveButton { padding: 0 35px 0 0; }
            .fr { float:right; }
            .dspTabTrue { visibility:visible; }
            .dspTabFalse { visibility:hidden; }
            ##tsAppVisible {.tsApp;}
            ##requiredLabel { padding-left:10px; }
            .reqPhoneText {padding-left:10px; font-style:italic;}
			##clientFormTbl .iti  input,
			##repFormTbl .iti  input{
				width: 62% !important;
				padding-top: 14px !important;
				padding-bottom: 14px !important;
			}
			##repFormTbl .iti,
			##clientFormTbl .iti{
				display:block !important;
				margin-bottom:10px;
			}
			##clientFormStyle .reqPhoneText{
				margin-top:-11px !important;
			}
			.smsReferralRep .ui-icon-triangle-2-n-s ,
			.smsReferralClient .ui-icon-triangle-2-n-s {
				background-position: -129px -12px;
			}
			.smsReferralRep,.smsReferralClient{
				padding-top: 10px;
			}
			.emptyPadding{
				padding:5px;
			}
        </style>
		<script type="text/javascript" src="/assets/common/javascript/resourceFields.js"></script>
        <script language="JavaScript">
            $(document).ready(function(){
                var subCheckHasRun1 = false;
				hasRep = 1;
				if(localStorage.resultViewed != undefined && localStorage.resultViewed == 1){
					$("##frmClient").hide();
					window.location.href = '#attributes.data.mainurl#';
				}
				localStorage.resultViewed = 0;
                formOriginalData = $("##frmClient").serialize();		

                $('##isRep').change(function(){
                    $("##repFormTbl").toggle();
                });

                $("##subpanelid1").multiselect({
                    multiple: true,
                    header: false
                });

                function chainedSelect(elemIdName,elemIdDefault,elemValueDefault,selected,format){
                    var strURL = "/?event=cms.showResource&resID=#attributes.data.siteResourceID#&mode=stream&ra=subPanelData&panelid="+ selected + "&isActive=1";
                    $.ajax({
                        url: strURL,
                        dataType: 'json',
                        success: function(response){				
                            $('##' + elemIdName).empty();
                            for (var i = 0; i < response.DATA.length; i++) {
                                if(response.DATA[i][1]) {//show panel in front end
                                    var o = new Option(response.DATA[i][1], response.DATA[i][0]);
                                    /* jquerify the DOM object 'o' so we can use the html method */
                                    $(o).html(response.DATA[i][1]);
                                    $('##' + elemIdName).append(o);
                                    $('##' + elemIdName).multiselect('refresh');
                                }
                            }
                        },
                        error: function(ErrorMsg){
                            /*console.log(ErrorMsg);*/
                        }
                    })
                }	
                
                function callChainedSelect(panel,subpanel){
                    var strSelected = $("##" + panel).val();
                    chainedSelect(
                        subpanel,        /* select box id  */
                        0,                  /* select box default value */
                        'Select Options',   /* select box default text */
                        strSelected,        /* value of the select */
                        'json'              /* return format */
                    );
                }

                $('body').on('change', '##panelid1', function(e) {
                    var panelsArr = []; 
                    <cfloop query="attributes.data.qryGetPanelsFilter">
                    panelsArr["#attributes.data.qryGetPanelsFilter.panelID#"] = "#jsstringformat(rereplace(rereplace(attributes.data.qryGetPanelsFilter.longDesc,'#chr(10)#','','ALL'),'#chr(13)#','<br>','ALL'))#";
                    </cfloop>
                    var divPanelDesc = "";				
                    if($('##panelid1').val() > 0) {
                        divPanelDesc = panelsArr[$('##panelid1').val()];
                        if ($.trim(divPanelDesc).length > 0) {
                            $(".trPanelDesc").show();
                            $(".divPanelDesc").html(divPanelDesc);	
                        }		
                        else{
                            $(".trPanelDesc").show();
                            $(".divPanelDesc").html("");					
                        }			
                    }
                    else{
                        $(".trPanelDesc").show();
                        $(".divPanelDesc").html("");		
                    }
                    callChainedSelect("panelid1","subpanelid1");
                });

                <cfif not attributes.data.isRep>
                    $("##repFormTbl").hide();
                </cfif>	

                $(".trPanelDesc").hide();
            });
			
			function doFormProcess(){
				var errorMsg = "";
				
				$('.saveClientBtn').attr('disabled','disabled');
				$("##frmClient").attr('action', '#attributes.data.mainurl#&ra=search&saveClientBtn=1');
				var changeInd = checkFormChanged();

				errorMsg += validateClientReferral();

				if (errorMsg.length > 0) {
					if (errorMsg != 'cancel') {
						alert("There were errors with your submission.\n\n" + errorMsg);
					}
				} else {
					$('##frmClient').hide();
					$('##formSubmitting').show();
					$('html,body').animate({scrollTop: $('##formSubmitting').offset().top},100);
					return true;
				}
				$('.saveClientBtn').attr('disabled',false);
				return false;
			}
			
            function chkSubPanel1Select(subPanel,subID){
                
                if (!subCheckHasRun1){
                    var dd = document.getElementById('subpanelid1');
                    <cfloop list="#attributes.data.subpanelid1#" index="local.thisItem">			
                    for (var i=0; i < dd.length; i++){
                        if (dd.options[i].value == #local.thisItem#) {
                            dd.options[i].selected = true;
                            break;
                        }
                    }
                    </cfloop>
                    subCheckHasRun1 = true;
                    $("##subpanelid1").multiselect('refresh');
                }					
            }

            function refreshDropDown(panelField){
                setTimeout(function() {
                    $("##" + panelField).multiselect('refresh');
                }, 1500);
            }

            function checkFormChanged(){
            var changeInd = false;
                if (($('##referralCanEditClient').val() == 1) && ($("##frmClient").serialize() != formOriginalData)) {
                    $("##formChanged").val('1');
                    changeInd = true;
                }
                return changeInd;
            }

            function validateClientFrm(){
                var nameRegex = /^([a-zA-Z]{1,})+([a-zA-Z0-9'\.\-\s]{0,1})+([a-zA-Z]{1,})$/;
				var initialRegex = /^([a-zA-Z]{1,})+([a-zA-Z0-9'\.\-\s]{0,1})+([a-zA-Z]{0,1})$/;
                var emailRegex = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/;
                var phoneRegex = /^(1\s*[-\/\.]?)?(\((\d{3})\)|(\d{3}))\s*([\s-./\\])?([0-9]*)([\s-./\\])?([0-9]*)$/;
                var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
                var errorMsg = "";
                var typeLabel = $('##typeID option:selected').text();
                var sourceOption = $.trim($('##sourceID option:selected').text());

				<cfif attributes.data.isFrontEndDisplay['First Name']>
                if( <cfif attributes.data.isRequired['First Name']>($.trim($('##firstName').val()).length == 0) ||</cfif> (($.trim($('##firstName').val()).length > 0) && (!nameRegex.test($.trim($('##firstName').val())))) ) {
                    errorMsg += '- Enter a valid client first name. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Middle Name']>
                if( <cfif attributes.data.isRequired['Middle Name']>($.trim($('##middleName').val()).length == 0) || </cfif>(($.trim($('##middleName').val()).length > 0) && (!initialRegex.test($.trim($('##middleName').val()))))) {
                    errorMsg += '- Enter a valid client middle name. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Last Name']>
                if( <cfif attributes.data.isRequired['Last Name']>($.trim($('##lastName').val()).length == 0) || </cfif>(($.trim($('##lastName').val()).length > 0) && (!nameRegex.test($.trim($('##lastName').val()))))) {
                    errorMsg += '- Enter a valid client last name. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Business']>
                if( <cfif attributes.data.isRequired['Business']>($.trim($('##businessName').val()).length == 0) || </cfif>(($.trim($('##businessName').val()).length > 0) && (!nameRegex.test($.trim($('##businessName').val()))))) {
                    errorMsg += '- Enter a valid business. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Address 1'] AND  attributes.data.isRequired['Address 1']>
                if($.trim($('##address1').val()).length == 0) {
                    errorMsg += '- Enter a valid address 1.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Address 2'] AND  attributes.data.isRequired['Address 2']>
                if($.trim($('##address2').val()).length == 0) {
                    errorMsg += '- Enter a valid address 1.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['City'] AND attributes.data.isRequired['City']>
                if( $.trim($('##city').val()).length == 0) {
                    errorMsg += '- Enter a valid city.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['State'] AND attributes.data.isRequired['State']>
                if($.trim($('##state').val()).length == 0) {
                    errorMsg += '- Select a state.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Zip Code'] AND attributes.data.isRequired['Zip Code']>
                if( $.trim($('##postalCode').val()).length == 0) {
                    errorMsg += '- Enter a valid Zip Code.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Email']>
				if( <cfif attributes.data.isRequired['Email']>( $.trim($('##email').val()).length == 0) ||</cfif> ($.trim($('##email').val()).length > 0 && (!emailRegex.test($.trim($('##email').val())))) ) {
                    errorMsg += '- Enter a valid client e-mail. \n';
                }
                if( <cfif attributes.data.isRequired['Email']>( $.trim($('##verifyEmail').val()).length == 0) ||</cfif> ( $.trim($('##email').val()).length > 0 && $.trim($('##email').val()) != $.trim($('##verifyEmail').val()))) {
                    errorMsg += '- Please verify your e-mail. \n';
                }	
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Home Phone ##']>
				indexHome = arrPhoneNumbersIds.indexOf("homePhone");
                if (<cfif attributes.data.isRequired['Home Phone ##']>( $.trim($('##homePhone').val()).length == 0) || </cfif>($.trim($('##homePhone').val()).length > 0) && indexHome >= 0) {
                    if(!MFAPhNoInput[indexHome].isValidNumber()) {
						errorMsg += '- Enter the client home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
                	}else{
						setNumberFormats(indexHome,'validation');
					}
				}
				</cfif>
				indexCellPhone = arrPhoneNumbersIds.indexOf("cellPhone");
				<cfif attributes.data.isFrontEndDisplay['Cell Phone ##']>
				if( <cfif attributes.data.isRequired['Cell Phone ##']>( $.trim($('##cellPhone').val()).length == 0) ||</cfif>($.trim($('##cellPhone').val()).length > 0) && indexCellPhone >= 0) {
					if(!MFAPhNoInput[indexCellPhone].isValidNumber()) {
						errorMsg += '- Enter the client cell number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
					}else{
						setNumberFormats(indexCellPhone,'validation');
					}
				}
				</cfif>

				indexAlternatePhone = arrPhoneNumbersIds.indexOf("alternatePhone");
				<cfif attributes.data.isFrontEndDisplay['Alternate Phone ##']>
                if( <cfif attributes.data.isRequired['Alternate Phone ##']>( $.trim($('##alternatePhone').val()).length == 0) ||</cfif>($.trim($('##alternatePhone').val()).length > 0) && indexAlternatePhone >= 0) {
                    if(!MFAPhNoInput[indexAlternatePhone].isValidNumber()) {
						errorMsg += '- Enter the client alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
					}else{
						setNumberFormats(indexAlternatePhone,'validation');
					}
				}	
                </cfif>

                if ($("##isRep").is(':checked')){		
                    if( ($.trim($('##repFirstName').val()).length == 0) || (($.trim($('##repFirstName').val()).length > 0) && (!nameRegex.test($.trim($('##repFirstName').val())))) ) {
                        errorMsg += '- Enter a valid representative first name. \n';
                    }
                    if( ($.trim($('##repLastName').val()).length == 0) || (($.trim($('##repLastName').val()).length > 0) && (!nameRegex.test($.trim($('##repLastName').val()))))) {
                        errorMsg += '- Enter a valid representative last name. \n';
                    }
					
					indexRepHomePhone = arrPhoneNumbersIds.indexOf("repHomePhone");
					if($.trim($('##repHomePhone').val()).length > 0 && indexRepHomePhone >= 0) {
						if(!MFAPhNoInput[indexRepHomePhone].isValidNumber()){
							errorMsg += '- Enter the representative home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
						}else{
							setNumberFormats(indexRepHomePhone,'validation');
						}		
					}

					indexRepCellPhone = arrPhoneNumbersIds.indexOf("repCellPhone");
					if($.trim($('##repCellPhone').val()).length > 0 && indexRepCellPhone >= 0) {
						if(!MFAPhNoInput[indexRepCellPhone].isValidNumber()){
							errorMsg += '- Enter the representative cell telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
						}else{
							setNumberFormats(indexRepCellPhone,'validation');
						}		
					}

					indexRepAlternatePhone = arrPhoneNumbersIds.indexOf("repAlternatePhone");
					if($.trim($('##repAlternatePhone').val()).length > 0 && indexRepAlternatePhone >= 0) {
						if(!MFAPhNoInput[indexRepAlternatePhone].isValidNumber()){
							errorMsg += '- Enter the representative alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
						}else{
							setNumberFormats(indexRepAlternatePhone,'validation');
						}		
					}

                    if( (($.trim($('##repEmail').val()).length > 0) && (!emailRegex.test($.trim($('##repEmail').val())))) ) {
                        errorMsg += '- Enter a valid representative e-mail. \n';
                    }			
                    if( ($.trim($('##repEmail').val()).length > 0) && ($.trim($('##repEmail').val()) != $.trim($('##repVerifyEmail').val()))) {
                        errorMsg += '- Please verify representative e-mail. \n';
                    }
                }

				<cfif attributes.data.dspLegalDescription>
					if( ($.trim($('##issueDesc').val()).length == 0)) {
						errorMsg += '- Enter the legal issue description. \n';
					}

					<cfif val(attributes.data.feLegalDescLimitWords)>
						updateLegalDescWordCount();
						var legalDescWordLimit = #int(val( attributes.data.feLegalDescLimitWordCount))#;
						/* Check if word count exceeds limit */
						if (parseInt($('##legalDescWordcount').text()) > legalDescWordLimit) errorMsg += '- ' + $('##legalDescWordLimitExceedAlert').text() + ' \n';
					</cfif>
				</cfif>
				
                var panelLabel = $('##panelid1 option:selected').text();
                    
                if($('##panelid1').val() == 0) {
                    errorMsg += '- Select a primary panel.\n';
                }

				var errorMsgArray = [];
				<cfif attributes.data.extraInformation.hasFields>
					#attributes.data.extraInformation.JSVALIDATION#
				</cfif>		

				/*drop empty elements*/
				var finalErrors = $.map(errorMsgArray, function(thisError){
					if (thisError.length) return "- "+thisError;
					else return null;
				});
				errorMsg += finalErrors.join('\n');	
                                    
                return errorMsg;
            }

            function validateClientReferral(){		
                var errorMsg = "";	

                errorMsg = validateClientFrm();

                return errorMsg;
            }
            
            function closeBox() { $.colorbox.close(); }
            
            function selectCaseStatus(){
                $.colorbox( {innerWidth:650,innerHeight:350, href:'#attributes.data.mainurl#&ra=selectCase&clientReferralID=#attributes.data.clientReferralID#&mode=direct', iframe:true, overlayClose:false} );
            }

            function formatCurrency(num) {
                num = num.toString().replace(/\$|\,/g,'');
                if(isNaN(num)) num = "0";
                sign = (num == (num = Math.abs(num)));
                num = Math.floor(num*100+0.50000000001);
                cents = num%100;
                num = Math.floor(num/100).toString();
                if(cents<10) cents = "0" + cents;
                for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
                return (((sign)?'':'-') + num + '.' + cents);
            }
			<cfif attributes.data.dspLegalDescription AND val(attributes.data.feLegalDescLimitWords)>
				function updateLegalDescWordCount() {
					const legalDesc = $('##issueDesc').val().trim();
					const legalDescWordLimit = #int(val(attributes.data.feLegalDescLimitWordCount))#;
					const legalDescWords = legalDesc.split(/\s+/).filter(Boolean); /* Filter out empty strings*/
					const legalDescWordCount = legalDescWords.length;
					$('##legalDescWordcount').text(legalDescWordCount);

					/* Check if word count exceeds limit */
					if (legalDescWordCount > legalDescWordLimit) {
						$('##legalDescWordLimitExceedAlert').show();
						$('.saveClientBtn').prop('disabled',true);
						return false;
					} else {
						$('##legalDescWordLimitExceedAlert').hide();
						$('.saveClientBtn').prop('disabled',false);
					}
				}

				$(function () {
					$('##issueDesc').on("input", updateLegalDescWordCount);
					updateLegalDescWordCount();
				});
			</cfif>
        </script>
		<cfif attributes.data.extraInformation.hasFields>
			#attributes.data.extraInformation.head#
		</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.clientFormJS#" />
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

<cfform name="frmClient" id="frmClient" method="POST" onsubmit="return doFormProcess();">
	<cfinput type="hidden" name="repParentID" id="repParentID" value="#attributes.data.repParentID#" />
	<cfoutput>
	<div id="clientFormStyle">	
		<h4 class="tsAppHeading">#attributes.data.referralPageTitle#</h4>
		<cfif structKeyExists(attributes.data,"saleError") AND  val(attributes.data.saleError)>
			<div class="row-fluid" id="saleError">
				<div class="alert alert-danger" role="alert"><span>Oops! Something went wrong. Please try after sometime.</span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close" >&times;</button></div>
			</div>
		</cfif>
		<cfif len(trim(attributes.data.feFormInstructionsContent))>
			<div>				
				#attributes.data.feFormInstructionsContent#							
			</div>
		</br>
		</cfif>	
		<fieldset class="tsApp tsAppBodyText">
			<legend class="tsAppLegendTitle"> &nbsp; Contact Information &nbsp; </legend>
			<br/>	
			<table id="clientFormTbl" width="100%" cellspacing="3" border="0">
				<cfif attributes.data.isFrontEndDisplay['First Name']>
					<tr> 
						<td class="r" width="#attributes.data.labelTDwidth#"><label for="firstName">First Name:</label></td> 
						<td width="3%"><cfif attributes.data.isRequired['First Name']>*</cfif></td> 
						<td><cfinput name="firstName"  id="firstName" type="text" value="#attributes.data.firstName#" maxlength="75" style="#attributes.data.responsiveFieldwidth#" /></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Middle Name']>
					<tr> 
						<td class="r"><label for="middleName">Middle Name:</label></td> 
						<td><cfif attributes.data.isRequired['Middle Name']>*</cfif></td> 
						<td><cfinput name="middleName"  id="middleName" type="text"  maxlength="25" value="#attributes.data.middleName#" style="#attributes.data.responsiveFieldwidth#" /></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Last Name']>
					<tr> 
						<td class="r"><label for="lastName">Last Name:</label></td> 
						<td><cfif attributes.data.isRequired['Last Name']>*</cfif></td> 
						<td><cfinput name="lastName"  id="lastName" type="text" maxlength="75" value="#attributes.data.lastName#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Business']>
					<tr> 
						<td class="r"><label for="businessName">Business:</label></td> 
						<td><cfif attributes.data.isRequired['Business']>*</cfif></td> 
						<td><cfinput name="businessName"  id="businessName" type="text" maxlength="100" value="#attributes.data.businessName#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Address 1']>
					<tr> 
						<td class="r"><label for="address1">Address 1:</label></td> 
						<td><cfif attributes.data.isRequired['Address 1']>*</cfif></td> 
						<td><cfinput name="address1"  id="address1" type="text" maxlength="100" value="#attributes.data.address1#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Address 2']>
					<tr> 
						<td class="r"><label for="address2">Address 2:</label></td> 
						<td><cfif attributes.data.isRequired['Address 2']>*</cfif></td> 
						<td><cfinput name="address2"  id="address2" type="text" maxlength="100" value="#attributes.data.address2#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['City']>
					<tr> 
						<td class="r"><label for="city">City:</label></td> 
						<td><cfif attributes.data.isRequired['City']>*</cfif></td> 
						<td><cfinput name="city"  id="city" type="text" maxlength="100" value="#attributes.data.city#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
			
				<cfif attributes.data.isFrontEndDisplay['State']>
					<tr> 
						<td class="r"><label for="state">State:</label></td> 
						<td><cfif attributes.data.isRequired['State']>*</cfif></td> 
						<td>
							<select name="state" id="state" style="#attributes.data.responsiveFieldwidth#">
								<option value=""></option>
								<cfoutput query="attributes.data.qryStates" group="countryID">
								<optgroup label="#attributes.data.qryStates.country#">
								<cfoutput>
									<option value="#attributes.data.qryStates.stateid#" <cfif val(attributes.data.qryStates.stateid) eq val(attributes.data.state)>selected</cfif>>#attributes.data.qryStates.stateName# &nbsp;</option>
								</cfoutput>								
								</optgroup>
								</cfoutput>
							</select>
						</td>
					</tr>
				</cfif>
				
				<cfif attributes.data.isFrontEndDisplay['Zip Code']>
					<tr> 
						<td class="r"><label for="postalCode">Zip Code:</label></td> 
						<td><cfif attributes.data.isRequired['Zip Code']>*</cfif></td> 
						<td><cfinput name="postalCode"  id="postalCode" type="text" maxlength="25" value="#attributes.data.postalCode#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Email']>
					<tr> 
						<td class="r"><label for="email">E-mail:</label></td> 
						<td><cfif attributes.data.isRequired['Email']>*</cfif></td> 
						<td><cfinput name="email"  id="email" type="text" maxlength="255" value="#attributes.data.email#" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
					<tr> 
						<td class="r"><label for="verifyEmail">Verify Your Email:</label></td> 
						<td><cfif attributes.data.isRequired['Email']>*</cfif></td> 
						<td><cfinput name="verifyEmail" id="verifyEmail" type="text" maxlength="255" value="" style="#attributes.data.responsiveFieldwidth#"/></td> 
					</tr>
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Home Phone ##']>
					<tr> 
						<td class="r"><label for="homePhone">Home Phone ##:</label></td> 
						<td><cfif attributes.data.isRequired['Home Phone ##']>*</cfif></td> 
						<td>
							<cfinput name="homePhone"  id="homePhone" type="text" maxlength="40" value="#attributes.data.homePhone#" style="#attributes.data.responsiveFieldwidth#" />
							<input type="hidden" name="homePhoneE164" id="homePhoneE164" value="#attributes.data.homePhoneE164#">
							<input type="hidden" name="homePhoneNational" id="homePhoneNational" value="#attributes.data.homePhone#">
						</td> 
					</tr>
				</cfif>	
				<cfif attributes.data.isFrontEndDisplay['Cell Phone ##']>
					<tr> 
						<td class="r"><label for="cellPhone">Cell Phone ##:</label></td> 
						<td><cfif attributes.data.isRequired['Cell Phone ##']>*</cfif></td> 
						<td>
							<cfinput name="cellPhone"  id="cellPhone" type="text" maxlength="40" value="#attributes.data.cellPhone#" style="#attributes.data.responsiveFieldwidth#" />
							<input type="hidden" name="cellPhoneE164" id="cellPhoneE164" value="#attributes.data.cellPhoneE164#">
							<input type="hidden" name="cellPhoneNational" id="cellPhoneNational" value="#attributes.data.cellPhone#">
						</td> 
					</tr>
				</cfif>	
				<cfif attributes.data.isFrontEndDisplay['Alternate Phone ##']>
					<tr> 
						<td class="r"><label for="alternatePhone">Alternate Phone ##:</label></td> 
						<td><cfif attributes.data.isRequired['Alternate Phone ##']>*</cfif></td> 
						<td>
							<cfinput name="alternatePhone"  id="alternatePhone" type="text" maxlength="40" value="#attributes.data.alternatePhone#" style="#attributes.data.responsiveFieldwidth#" />
							<input type="hidden" name="alternatePhoneE164" id="alternatePhoneE164" value="#attributes.data.alternatePhoneE164#">
							<input type="hidden" name="alternatePhoneNational" id="alternatePhoneNational" value="#attributes.data.alternatePhone#">
						</td> 
					</tr>
				</cfif>	
				<tr> 
					<td class="r"></td> 
					<td></td>  
					<td >
						<div class="reqPhoneText">At least one phone number or e-mail is required.</div>
					</td>  
				</tr>
				<tr> 
					<td class="r"></td> 
					<td></td>  
					<td >
						<div class="emptyPadding">&nbsp;</div>
					</td>  
				</tr>
				<cfif attributes.data.qryActiveSMSTemplate.recordcount and attributes.data.qryActiveSMSTemplate.status eq 'A'>
					<tr> 
						<td class="r"><label for="smsReferralClient">Text Me My Referrals</label></td> 
						<td></td>  
						<td>
							<i>
							In addition to email, would you also like to receive your referral information via text? 
							Selecting phone numbers below indicates that you agree to receive your referral text messages from [[organizationName]]. You may reply STOP at any time to cancel.
							</i>
						</td>  
					</tr>
					<tr> 
						<td class="r"></td> 
						<td></td>  
						<td class="smsReferralClient"">
							<select class="input-large" name="smsClientNumbers" id="smsClientNumbers" multiple="multiple" >
								
							</select>
						</td>  
					</tr>
				</cfif>	
			</table>
			<br />
		</fieldset>
		<br />
		<fieldset class="tsApp tsAppBodyText repInfo"<cfif attributes.data.hideRepFieldsFE> style="display:none;"</cfif>>
			<legend class="tsAppLegendTitle"> &nbsp; Representative &nbsp; </legend>
			<br/>
			<table id="needRepFormTbl" width="100%" cellspacing="3" border="0">
			<tr> 
				<td style="padding-left:15px;">
					Are you filling out this form on behalf of another party? <label for="isRep"><input type="checkbox" name="isRep" id="isRep" value="1" <cfif attributes.data.isRep AND NOT attributes.data.hideRepFieldsFE>checked</cfif>> Yes</label>
				</td> 
			</tr>				
			</table>
			<table id="repFormTbl" width="100%" cellspacing="3" border="0">
			<tr> 
				<td colspan="3">&nbsp;</td> 
			</tr>					
			<tr> 
				<td class="r" width="#attributes.data.labelTDwidth#"><label for="repFirstName">First Name:</label></td> 
				<td width="3%">*</td> 
				<td><cfinput name="repFirstName"  id="repFirstName" type="text" maxlength="75" value="#attributes.data.repFirstName#" style="#attributes.data.responsiveFieldwidth#" /></td> 
			</tr> 
			<tr> 
				<td class="r"><label for="repLastName">Last Name:</label></td> 
				<td>*</td>
				<td><cfinput name="repLastName"  id="repLastName" type="text" maxlength="75" value="#attributes.data.repLastName#" style="#attributes.data.responsiveFieldwidth#"/></td> 
			</tr> 
			<tr> 
				<td class="r"><label for="relationToClient">Relationship to Contact:</label></td> 
				<td>*</td>
				<td><cfinput name="relationToClient"  id="relationToClient" type="text" maxlength="100" value="#attributes.data.relationToClient#" style="#attributes.data.responsiveFieldwidth#"/></td> 
			</tr> 	
			<tr> 
				<td class="r"><label for="repAddress1">Address 1:</label></td> 
				<td></td> 
				<td><cfinput  name="repAddress1"  id="repAddress1" type="text" maxlength="100" value="#attributes.data.repAddress1#" style="#attributes.data.responsiveFieldwidth#" /></td> 
			</tr> 
			<tr> 
				<td class="r"><label for="repAddress2">Address 2:</label></td> 
				<td></td> 
				<td><cfinput name="repAddress2"  id="repAddress2" type="text" maxlength="100" value="#attributes.data.repAddress2#" style="#attributes.data.responsiveFieldwidth#"/></td> 
			</tr> 
			<tr> 
				<td class="r"><label for="repCity">City:</label></td> 
				<td></td> 
				<td><cfinput name="repCity"  id="repCity" type="text" maxlength="100" value="#attributes.data.repCity#" style="#attributes.data.responsiveFieldwidth#" /></td> 
			</tr> 	
			<tr> 
				<td class="r"><label for="repState">State:</label></td> 
				<td></td> 
				<td>
					<select name="repState" id="repState" class="tsAppBodyText" style="#attributes.data.responsiveFieldwidth#">
						<option value=""></option>
						<cfoutput query="attributes.data.qryStates" group="countryID">
						<optgroup label="#attributes.data.qryStates.country#">
						<cfoutput>
							<option value="#attributes.data.qryStates.stateid#" <cfif attributes.data.qryStates.stateid eq attributes.data.state>selected</cfif>>#attributes.data.qryStates.stateName# &nbsp;</option>
						</cfoutput>
						</optgroup>
						</cfoutput>
					</select>
				</td>
			</tr>	
			<tr> 
				<td class="r"><label for="repPostalCode">Zip Code:</label></td> 
				<td></td> 
				<td><cfinput name="repPostalCode"  id="repPostalCode" type="text" maxlength="25" value="#attributes.data.repPostalCode#" style="#attributes.data.responsiveFieldwidth#"/></td> 
			</tr> 
			<tr> 
				<td class="r"><label for="repEmail">E-mail:</label></td> 
				<td></td> 
				<td><cfinput name="repEmail"  id="repEmail" type="text" maxlength="255" value="#attributes.data.repEmail#" style="#attributes.data.responsiveFieldwidth#"/></td> 
			</tr> 	
			<tr>
				<td class="r"><label for="repVerifyEmail">Verify E-mail:</label></td> 
				<td></td> 
				<td><cfinput name="repVerifyEmail"  id="repVerifyEmail" type="text" maxlength="255" value="" style="#attributes.data.responsiveFieldwidth#"/></td> 
			</tr>
			<tr> 
				<td class="r"><label for="repHomePhone">Home Phone ##:</label></td> 
				<td>*</td> 
				<td>
					<cfinput name="repHomePhone"  id="repHomePhone" type="text" maxlength="40" value="#attributes.data.repHomePhone#" style="#attributes.data.responsiveFieldwidth#" />
					<input type="hidden" name="repHomePhoneE164" id="repHomePhoneE164" value="#attributes.data.repHomePhoneE164#">
					<input type="hidden" name="repHomePhoneNational" id="repHomePhoneNational" value="#attributes.data.repHomePhone#">
				</td> 
			</tr> 	
			<tr> 
				<td class="r"><label for="repCellPhone">Cell Phone ##:</label></td> 
				<td></td> 
				<td>
					<cfinput name="repCellPhone"  id="repCellPhone" type="text" maxlength="40" value="#attributes.data.repCellPhone#" style="#attributes.data.responsiveFieldwidth#" />
					<input type="hidden" name="repCellPhoneE164" id="repCellPhoneE164" value="#attributes.data.repCellPhoneE164#">
					<input type="hidden" name="repCellPhoneNational" id="repCellPhoneNational" value="#attributes.data.repCellPhone#">
				</td> 
			</tr>	
			<tr> 
				<td class="r"><label for="repAlternatePhone">Alternate Phone ##:</label></td> 
				<td></td> 
				<td>
					<cfinput name="repAlternatePhone"  id="repAlternatePhone" type="text" maxlength="40" value="#attributes.data.repAlternatePhone#" style="#attributes.data.responsiveFieldwidth#" />
					<input type="hidden" name="repAlternatePhoneE164" id="repAlternatePhoneE164" value="#attributes.data.repAlternatePhoneE164#">
					<input type="hidden" name="repAlternatePhoneNational" id="repAlternatePhoneNational" value="#attributes.data.repAlternatePhone#">
				</td> 
			</tr>
			<tr> 
				<td class="r"></td> 
				<td></td>  
				<td>
					<div class="reqPhoneText">At least one phone number or a valid email is required if Representative information is provided.</div>
				</td>  
			</tr>
			<tr> 
				<td class="r"></td> 
				<td></td>  
				<td>
					<div class="emptyPadding">&nbsp;</div>
				</td>  
			</tr>
			<cfif attributes.data.qryActiveSMSTemplate.recordcount and attributes.data.qryActiveSMSTemplate.status eq 'A'>
				<tr> 
					<td class="r"><label for="smsReferralRep">Text Me My Referrals</label></td> 
					<td></td>  
					<td>
						<i>
						In addition to email, would you also like to receive your referral information via text? 
						Selecting phone numbers below indicates that you agree to receive your referral text messages from [[organizationName]]. You may reply STOP at any time to cancel.
						</i>
					</td>  
				</tr>
				<tr> 
					<td class="r"></td> 
					<td></td>  
					<td class="smsReferralRep"">
						<select class="input-large" name="smsRepNumbers" id="smsRepNumbers" multiple="multiple" >
							
						</select>
					</td>  
				</tr>
			</cfif>																																
			</table>		
		</fieldset>
		<br />
		<cfif attributes.data.qryGetLanguages.recordCount GT 1>
		<fieldset class="tsApp tsAppBodyText">
			<legend class="tsAppLegendTitle"> &nbsp; Language &nbsp; </legend>
			<br/>
			<table id="languageFormTbl" width="100%" cellspacing="3" border="0">	
			<tr> 
				<td class="r" width="#attributes.data.labelTDwidth#"><label for="communicateLanguageID">What is your preferred language?:</label></td> 
				<td width="3%"></td> 
				<td>
					<select name="communicateLanguageID" id="communicateLanguageID">
						<option value="">Select Language</option>
					<cfloop query="attributes.data.qryGetLanguages">
						<option value="#attributes.data.qryGetLanguages.languageID#" <cfif (attributes.data.qryGetLanguages.languageID eq attributes.data.communicateLanguageID) OR (not val(attributes.data.communicateLanguageID) and  val(attributes.data.qryGetLanguages.isDefault))>selected</cfif>>#attributes.data.qryGetLanguages.languageName#</option>
					</cfloop>
					</select>
				</td>
			</tr> 			
			</table>			
			<br />
		</fieldset>	
		<br />
		<cfelse>
			<input type="hidden" name="communicateLanguageID" id="communicateLanguageID" value="#attributes.data.qryGetLanguages.languageID#">
		</cfif>
		<div <cfif NOT attributes.data.dspLegalDescription>style="display:none" </cfif>>
			<fieldset class="tsApp tsAppBodyText">
				<legend class="tsAppLegendTitle"> &nbsp; Legal Issue &nbsp; </legend>
				<br/>
				<cfif len(trim(attributes.data.feLegalDescInstructContent))>
					<div id="legalDescInfo">
						#attributes.data.feLegalDescInstructContent#
					</div>
					<br/>
				</cfif>
				<table id="legalIssueFormTbl" width="100%" cellspacing="3" border="0">
				<tr> 
					<td class="r" width="#attributes.data.labelTDwidth#" valign="top"><label for="issueDesc">Description:</label></td> 
					<td width="3%" valign="top">*</td> 
					<td>
						<textarea name="issueDesc" id="issueDesc" style="width:90%;" rows="5" class="tsAppBodyText">#ReReplace(attributes.data.issueDesc, "<[^<|>]+?>", "","ALL")#</textarea>
						<cfif val(attributes.data.feLegalDescLimitWords)>
							<div style="width:90%;">
								<div style="font-size:small;text-align:right;">Word Count:<span id="legalDescWordcount">0</span></div>
								<div id="legalDescWordLimitExceedAlert" class="alert alert-danger" style="display:none">#htmlEditFormat(attributes.data.feLegalDescLimitExceedMsg)#</div>
							</div>
						</cfif>
					</td> 
				</tr>				
				</table>
				<br />
			</fieldset>
			<br />
		</div>
		<cfif attributes.data.feDspSurveyOption>
		<fieldset class="tsApp tsAppBodyText">
			<legend class="tsAppLegendTitle"> &nbsp; Survey &nbsp; </legend>
			<br/>
			<table id="surveyFormTbl" width="100%" cellspacing="3" border="0">
			<tr> 
				<td style="padding-left:15px;">
					Would you like to receive surveys regarding the case? <label for="sendSurvey"><input type="checkbox" name="sendSurvey" id="sendSurvey" value="1" <cfif attributes.data.sendSurvey OR attributes.data.feSurveyOptionDefaultYes>checked</cfif>> Yes</label>
				</td> 
			</tr>				
			</table>
			<br />
		</fieldset>			
		<br/>
		</cfif>
		<cfif attributes.data.feDspBlogOption>
		<fieldset class="tsApp tsAppBodyText">
			<legend class="tsAppLegendTitle"> &nbsp; Newsletters / Blogs &nbsp; </legend>
			<br/>
			<table id="surveyFormTbl" width="100%" cellspacing="3" border="0">
			<tr> 
				<td style="padding-left:15px;">
					Would you like to receive e-mails regarding Newsletters and/or Blog updates? <label for="sendNewsBlog"><input type="checkbox" name="sendNewsBlog" id="sendNewsBlog" value="1" <cfif attributes.data.sendNewsBlog>checked</cfif>> Yes</label>
				</td> 
			</tr>				
			</table>
			<br />
		</fieldset>			
		<br/>
		</cfif>		
		<fieldset id="filterFS" class="tsApp tsAppBodyText">
			<legend class="tsAppLegendTitle"> &nbsp; Filters &nbsp; </legend>
			<br/>
			<cfif len(trim(attributes.data.fePanelInfoContent))>
				<div id="panelInfo">
					#attributes.data.fePanelInfoContent#
				</div>
				<br/>
			</cfif>
			<table id="clientFormTbl" width="100%" cellspacing="3" border="0">
			<tr> 
				<td class="r" width="#attributes.data.labelTDwidth#" valign="top"><label for="panelid1">Primary Panel:</label></td> 
				<td width="3%" valign="top">*</td> 
				<td valign="top">
					<cfselect name="panelid1" id="panelid1" onclick="refreshDropDown('subpanelid1');" style="min-width: 15em;">
						<option value="0">Select a Panel</option>
						<cfloop query="attributes.data.qryGetPanelsFilter">
						<cfif val(attributes.data.qryGetPanelsFilter.feDspClientReferral)>
							<option value="#attributes.data.qryGetPanelsFilter.panelID#" <cfif attributes.data.panelid1 is attributes.data.qryGetPanelsFilter.panelID>selected</cfif>>#attributes.data.qryGetPanelsFilter.name#</option>
						</cfif>
						</cfloop>
					</cfselect>
				</td>
			</tr> 
			<tr>
				<td class="r"><label for="subpanelid1">Sub-Panel:</label></td> 
				<td width="3%" valign="top"></td> 
				<td id="td_subpanelid1">
					<cfselect name="subpanelid1" id="subpanelid1" multiple="true" style="min-width: 15em;">
						<cfif val(attributes.data.panelid1)>
							<cfloop query="attributes.data.qryGetSubPanelsFilter1">
    							<cfif val(attributes.data.qryGetSubPanelsFilter1.feDspClientReferral)>
                                    <option value="#attributes.data.qryGetSubPanelsFilter1.panelID#" <cfif listFind(attributes.data.subpanelid1,attributes.data.qryGetSubPanelsFilter1.panelID)>selected</cfif>>#attributes.data.qryGetSubPanelsFilter1.name#</option>
    							</cfif>
							</cfloop>
						</cfif>				
					</cfselect>
				</td>
			</tr> 			
			<tr class="trPanelDesc">
				<td colspan="3">
					<br/>
					<div class="divPanelDesc"></div>
				</td>
			</tr>
			</table>
			
			<div class="tsAppBodyText"><div id="M_err_div" style="display:none;"></div></div>
			<table id="srchformTbl" class="tsAppBodyText" width="100%" cellspacing="3" border="0">
			<cfset local.jsValidation = ''>
			<cfif ArrayLen(attributes.data.xmlFields.xmlRoot.xmlChildren)>
				<cfloop array="#attributes.data.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
					<cfset local.thisFieldValue = evaluate("attributes.data.#local.thisfield.xmlattributes.fieldCode#") />
					<tr> 
						<td colspan="5"><hr /></td>
					</tr> 
					<tr valign="top">
						<td class="r" width="#attributes.data.labelTDwidth#"><cfoutput>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:</cfoutput></td>
						<td width="3%"><cfif local.thisfield.xmlattributes.isRequired is 1>*</cfif>&nbsp;</td>
						<td align="left"> 
							<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
							<cfcase value="TEXTBOX">
								<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
									<cfset local.thisRadiusValue = evaluate("attributes.data.#local.thisfield.xmlattributes.fieldCode#_radius") />	
									Within 
									<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" style="#attributes.data.responsiveFieldwidth#">
										<cfloop list="5,10,25,50,100" index="local.thisrad">
											<cfoutput><option value="#local.thisrad#" <cfif listFindNoCase(local.thisRadiusValue,local.thisrad)>selected="selected"</cfif>>#local.thisrad#</option></cfoutput>
										</cfloop>
									</cfselect>
									<cfoutput>miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</cfoutput>
									<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off" class="tsAppBodyText" style="#attributes.data.responsiveFieldwidth#">
								<cfelse>
									<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off" class="tsAppBodyText" style="#attributes.data.responsiveFieldwidth#">
								</cfif>
							</cfcase>
							<cfcase value="RADIO,SELECT,CHECKBOX">
								<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
									<cfset local.qryStates = application.objMember.getStates(attributes.data.orgID)>
									<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" style="min-width: 15em;">
										<option value=""></option>
										<cfoutput query="local.qryStates" group="countryID">
											<optgroup label="#local.qryStates.country#">
											<cfoutput>
												<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
											</cfoutput>
											</optgroup>
										</cfoutput>
									</cfselect>
								<cfelseif listFindNoCase("m_recordtypeid,m_membertypeid,m_status", local.thisfield.xmlattributes.fieldCode)>
									<cfselect name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" style="#attributes.data.responsiveFieldwidth#">
										<option value=""></option>
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
											<cfoutput><option value="#local.thisOpt.xmlAttributes.valueID#" <cfif listFindNoCase(local.thisFieldValue,local.thisOpt.xmlAttributes.valueID)>selected="selected"</cfif>>#local.thisOpt.xmlAttributes.columnValueString#</option></cfoutput>
										</cfloop>
									</cfselect>
								<cfelse>
									<cfset local.multiSelect = local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' AND local.thisfield.xmlAttributes.dataTypeCode NEQ 'BIT'>
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#"<cfif local.multiSelect>multiple="multiple"</cfif> style="min-width: 15em;">
										<cfif not local.multiSelect><option value=""></option></cfif>
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
											<cfswitch expression="#local.thisfield.xmlattributes.dataTypeCode#">
											<cfcase value="STRING">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
												<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
											</cfcase>
											<cfcase value="DECIMAL2">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
												<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
											</cfcase>
											<cfcase value="INTEGER">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
												<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
											</cfcase>
											<cfcase value="DATE">
												<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
												<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
											</cfcase>
											<cfcase value="BIT">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
												<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
											</cfcase>
											<cfdefaultcase>
												<cfset local.thisOptColValue = "">
												<cfset local.thisOptColDisplay = "">
											</cfdefaultcase>
											</cfswitch>
											<cfset local.thisOptVal = local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' ? local.thisOpt.xmlAttributes.valueID : local.thisOptColValue>
											<cfoutput><option value="#local.thisOptVal#" <cfif listFindNoCase(local.thisFieldValue,local.thisOptVal)>selected="selected"</cfif>>#local.thisOptColDisplay#</option></cfoutput>
										</cfloop>
									</select>
									<cfif local.multiSelect>
										<cfsavecontent variable="local.jQueryMultiselect">
											<cfoutput>
											<script type="text/javascript">
											$(function(){
												$("###local.thisfield.xmlattributes.fieldCode#").multiselect({ header: "Choose options below", selectedList: 10, minWidth: 200 });
											});
											</script>	
											</cfoutput>
										</cfsavecontent>
										<cfhtmlhead text="#application.objCommon.minText(local.jQueryMultiselect)#">
									</cfif>
								</cfif>
							</cfcase>
							<cfcase value="DATE">
								<nobr>
								<cfinput class="tsAppBodyText" type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="" style="#attributes.data.responsiveFieldwidth#">
								<cfinput type="button" name="btnClear#local.thisfield.xmlattributes.fieldCode#"  id="btnClear#local.thisfield.xmlattributes.fieldCode#" value="clear">
								</nobr>
								<cfsavecontent variable="local.datejs">
									<cfoutput>
									<script language="javascript">
										$(function() { 
											mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#'); 
											$("##btnClear#local.thisfield.xmlattributes.fieldCode#").click( function(e) { mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');return false; } );
										});
									</script>
									<style type="text/css">
									###local.thisfield.xmlattributes.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
									</style>
									</cfoutput>
								</cfsavecontent>
								<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
							</cfcase>
							</cfswitch>
						</td>
						<td class="tsAppBodyText">
							<cfif attributes.data.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
								<cfoutput><img src="/assets/common/images/help-icon.jpg" onMouseOver="Tip('#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#');" onMouseOut="UnTip();" /></cfoutput>
							<cfelse>
							&nbsp;
							</cfif>
						</td>
						<td width="3%"></td> 
					</tr>
					<cfif local.thisfield.xmlattributes.isRequired is 1>
						<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
						<cfcase value="TEXTBOX,DATE">
							<cfsavecontent variable="local.jsValidation">
								<cfoutput>
								#local.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="RADIO,SELECT,CHECKBOX">
							<cfsavecontent variable="local.jsValidation">
								<cfoutput>
								#local.jsValidation#
								if (_CF_this['#local.thisfield.xmlattributes.fieldCode#'].options[_CF_this['#local.thisfield.xmlattributes.fieldCode#'].selectedIndex].value.length == 0) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						</cfswitch>
						<cfset attributes.data.showReqFlag = true>
					</cfif>
				</cfloop>
				<cfif attributes.data.showReqFlag>
					<tr><td colspan="5" class="tsAppBodyText"><i>* required field</i></td></tr>
				</cfif>		
			</cfif>
			<cfoutput>
			<cfloop query="attributes.data.qryGetClassifications">
				<cfif val(attributes.data.qryGetClassifications.allowSearch)>
					<tr> 
						<td colspan="5"><hr /></td>
					</tr> 			
					<tr> 
						<td class="r" width="25%" valign="top">
							<label for="mg_gid_#attributes.data.qryGetClassifications.groupSetID#"><cfif len(trim(attributes.data.qryGetClassifications.name))>#attributes.data.qryGetClassifications.name#<cfelse>#attributes.data.qryGetClassifications.groupSetName#</cfif>:</label>
						</td> 
						<td width="3%" valign="top"></td> 
						<td valign="top" colspan="3">
							<cfset local.qryGetGroupSetGroup = attributes.data.objAdminReferrals.getGroupSetGroup(attributes.data.qryGetClassifications.groupSetID) />
							<cfset local.thisGroupIDValue = evaluate("attributes.data.mg_gid_#attributes.data.qryGetClassifications.groupSetID#") />
							<select class="tsAppBodyText" name="mg_gid_#attributes.data.qryGetClassifications.groupSetID#" id="mg_gid_#attributes.data.qryGetClassifications.groupSetID#" multiple="multiple" style="min-width: 15em;">
								<cfloop query="local.qryGetGroupSetGroup">
								    <option value="#local.qryGetGroupSetGroup.groupsetGroupID#" <cfif listFind(local.thisGroupIDValue,local.qryGetGroupSetGroup.groupsetGroupID)>selected="selected"</cfif>>#local.qryGetGroupSetGroup.labelOverride#</option>
								</cfloop>
							</select>
						</td>
					</tr>
					<cfsavecontent variable="local.jQueryMultiselectGroup">
						<cfoutput>
						<script type="text/javascript">
						$(function(){
							$("##mg_gid_#attributes.data.qryGetClassifications.groupSetID#").multiselect({
								header: "Choose options below",
								selectedList: 10,
								minWidth: 200
							});
						});
						</script>	
						</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#application.objCommon.minText(local.jQueryMultiselectGroup)#">			
				</cfif>
			</cfloop>
			</cfoutput>
			</table>	
		</fieldset>
		<br />			
		<cfif attributes.data.extraInformation.hasFields>
			<input type="hidden" name="hasClientFields" id="hasClientFields" value="1">
			<fieldset class="tsApp tsAppBodyText">
				<legend class="tsAppLegendTitle"> &nbsp; Extra Information &nbsp; </legend>
				<br/>
				<table id="surveyFormTbl" width="100%" cellspacing="3" border="0">
				<tr> 
					<td style="padding-left:15px;">
						#attributes.data.extraInformation.HTML#	
					</td> 
				</tr>				
				</table>
				<br />
			</fieldset>			
			<br/>	
		</cfif>																									
		<div id="requiredLabel" class="fl tsAppBodyText">* indicates a required field</div>
		<br /><br /><br />
		<div align="right">
			<button type="submit" id="saveClientBtn2" class="saveClientBtn tsAppBodyButton">Submit Form</button>
			<button type="button" id="cancelBtn2" class="tsAppBodyButton" onClick="parent.location.href='#attributes.data.mainurl#';">Cancel</button>
		</div>
		<br />	
	</div>
	</cfoutput>
</cfform>
<div id="formSubmitting" style="display:none;text-align:center;margin:50px;">
	<i class="icon-spin icon-spinner icon-3x"></i> <b>Please wait while we process your application.</b>
</div>
<cfinclude template="/views/clientReferrals/commonTelJS.cfm">