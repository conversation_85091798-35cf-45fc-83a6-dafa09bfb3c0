<cfsavecontent variable="local.QAHead">
	<cfoutput>
	<script type="text/javascript">
		function getReferralQuestionAnswers() {
			var getRefQAResult = function(r) {
				$('input.mcref-qa-answer').not(':checked').prop('disabled',false);
				if (r.success && r.success.toLowerCase() == 'true') loadReferralQuestionAnswers(r);
				else alert('We were unable to load the next question. Try again!');
			};

			$('input.mcref-qa-answer').not(':checked').prop('disabled',true);
			$(this).attr('aria-checked', 'true');
			var strAnswer = { questionID:Number($(this).data('questionid')),
				answerID:Number($(this).val()), currentQAMode:$(this).data('qamode') };
			TS_AJX('CLIENTREFERRALS','getReferralQuestionAnswers',strAnswer,getRefQAResult,getRefQAResult,10000,getRefQAResult);
		}
		function loadReferralQuestionAnswers(r) {
			if (!(r && r.qamode && r.qamode.length)) return false;

			switch (r.qamode) {
				case 'getrootquestions':
				case 'getrootquestionanswers':
				case 'getnextquestionandanswers':
				case 'getpanelfields':
					if (r.arranswers && r.arranswers.length) {
						$('##mcref_qa_childof_'+r.parentquestionid).remove();
						var refQATemplateSource = $('##mcref_qa_template').html();
						var refQATemplate = Handlebars.compile(refQATemplateSource);
						$('##divMCClientRefQASection').append(refQATemplate(r));
						initClientRefQA($('##mcref_qa_questionid_'+r.questionid),r);
						scrollDown('mcref_qa_questionid_'+r.questionid);
						if (r.arranswers.length == 1) $('##answer_'+r.arranswers[0].valueid).prop('checked',true).trigger('click');
					}
					break;
				case 'getpanels':
					if (r.arrpanels && r.arrpanels.length) {
						if (r.arrpanels.length > 1) {
							var arrchildpanels = r.arrpanels.filter(function(p) { return p.panelparentid >  0; });
							var indexToSplit = Math.ceil(arrchildpanels.length/2);
							var arrleftpanels = arrchildpanels.slice(0, indexToSplit);
							var arrrightpanels = arrchildpanels.slice(indexToSplit);
							var strpanels = { questionid:r.parentquestionid, answerid:r.selectedanswerid, arrleftpanels:arrleftpanels, arrrightpanels:arrrightpanels,
												questionnumber:$('##divMCClientRefQASection .questionLRISWrapper').length, panelparentid:r.arrpanels[0].panelid };
							
							var refQASubPanelTemplateSource = $('##mcref_qa_subpaneltemplate').html();
							var refQASubPanelTemplate = Handlebars.compile(refQASubPanelTemplateSource);
							$('##mcref_qa_answercontainer_'+r.parentquestionid).after(refQASubPanelTemplate(strpanels));
							initClientRefQASubPanels($('##mcref_qa_subpanels_'+r.selectedanswerid),r);
						} else {
							if (['getpanelfields','getrootquestionanswers','getnextquestionandanswers'].includes(r.currentqamode)) {
								if (r.arrpanels[0].panelparentid > 0) {
									$('##panelID1').val(r.arrpanels[0].panelparentid);
									$('##subPanelID1').val(r.arrpanels[0].panelid);
								} else {
									$('##panelID1').val(r.arrpanels[0].panelid);
									$('##subPanelID1').val('');
								}
							} else {
								$('##panelID1').val(r.arrpanels[0].panelid);
								$('##subPanelID1').val('');
							}
							showRefQASummary(r.parentquestionid,r.selectedanswerid);
							loadLegalIssueDescContainer(r.selectedanswerid);
						}
					}
					break;

			};

			populateQuestionAnswerPath();
		}
		function initClientRefQA(scope,strQA) {
			scope.find('input.mcref-qa-answer, i.mcref_qa_edit').off('click');
			scope.find('input.mcref-qa-answer').on('click',getReferralQuestionAnswers);
			scope.find('i.mcref_qa_edit').on('click',editReferralQuestion);
			onClickPrevAndGotoTopBtn(scope);
			showRefQASummary(strQA.parentquestionid,strQA.selectedanswerid);
		}
		function initClientRefQASubPanels(scope,strQA) {
			scope.find('.mcref_qa_subpanels, .btnSkipSubPanel, .btnPanelContinue').off('click');
			showRefQASummary(strQA.parentquestionid,strQA.selectedanswerid);

			scope.find('.mcref_qa_subpanels').on('click',function(){
				if (scope.find('input.mcref_qa_subpanels:checked').length) $('##btnPanelContinue_'+strQA.selectedanswerid).prop('disabled', false);
				else $('##btnPanelContinue_'+strQA.selectedanswerid).prop('disabled', true);
			});
			scope.find('.btnSkipSubPanel').on('click',function() {
				if ($('##refQAPanelID').val() > 0) {
					scope.find('input.mcref_qa_subpanels:checked').prop('checked',false);
					$('##panelID1').val($('##refQAPanelID').val());
					$('##subPanelID1').val('');
					loadLegalIssueDescContainer(strQA.selectedanswerid);
				}
			});
			scope.find('.btnPanelContinue').on('click',function() {
				if ($('##refQAPanelID').val() > 0) {
					$('##panelID1').val($('##refQAPanelID').val());
					$('##subPanelID1').val('');
					if (scope.find('input.mcref_qa_subpanels:checked').length) {
						var arrsubpanels = scope.find('input.mcref_qa_subpanels:checked').map(
												function() { 
													return  '<span class="specialization">'+ $(this).next().text().trim() + '</span>'; 
												}).get();
						if (arrsubpanels.length) {
							var newQALabel = $('##questionlabel_'+strQA.parentquestionid).text().trim() + '<br />Specialization(s) selected: ' + arrsubpanels.join('');
							$('##questionlabel_'+strQA.parentquestionid).html(newQALabel);
						}
						var subPanelIDList = scope.find('input.mcref_qa_subpanels:checked').map(function() { return $(this).val() }).get().join(',');
						$('##subPanelID1').val(subPanelIDList);
					}

					loadLegalIssueDescContainer(strQA.selectedanswerid);
				}
			});

			onClickPrevAndGotoTopBtn(scope);
		}
		function onClickPrevAndGotoTopBtn(scope) {
			scope.find('.btnPrevQuest, .btnGoToTop').off('click');

			scope.find('.btnPrevQuest').on('click',function(){
				$('##divMCClientRefQASection .questionLRISWrapper .icon-pencil:visible').last().trigger('click');
			});
			scope.find('.btnGoToTop').on('click',function(){
				$('##divMCClientRefQASection .questionLRISWrapper .icon-pencil:visible').first().trigger('click');
			});
		}
		function showRefQASummary(parentquestionid,selectedanswerid) {
			if ((parentquestionid + selectedanswerid) == 0) return false;
			$('##questionlabel_'+parentquestionid).html($('##mcref_qa_answerlabel_'+selectedanswerid).html());
			$('##mcref_qa_answercontainer_'+parentquestionid+', ##mcref_qa_btncontainer_'+parentquestionid).hide();
			$('.mcref_qa_controls_'+parentquestionid).show();
		}
		function editReferralQuestion() {
			var questionID = $(this).attr('data-fieldid');
			var questionTreeOrder = $(this).attr('data-treeorder');

			$('.mcref_qa_controls_'+questionID).hide();
			/*$('##mcref_qa_answercontainer_'+questionID+' input.mcref-qa-answer:checked').prop('checked',false);*/
			$('##mcref_qa_btncontainer_'+questionID).show();
			$('##mcref_qa_answercontainer_'+questionID).slideDown();
			$('.mcref_qa_dependent_'+questionID).remove();
			$(this).parents('.questionLRISWrapper').nextAll().remove();
			scrollDown('mcref_qa_questionid_'+questionID);

			if ($('##legalIssue').is(':visible')) {
				goToViewMode('legalIssue');
				hideSection('legalIssue');
			}
			
			if ($('##additionalFilters').is(':visible')) {
				goToViewMode('additionalFilters');
				hideSection('additionalFilters');
			}
			$('.step2btnWrapper').hide();
		}
		function loadLegalIssueDescContainer(selectedanswerid) {
			$('##mcref_qa_subpanels_'+selectedanswerid).hide();
			$('.legalIssueDesc').slideDown();
			goToEditMode('legalIssue');
			scrollDown('legalIssue');
			onClickPrevAndGotoTopBtn($('##legalIssue'));
		}
		function populateQuestionAnswerPath() {
			var QAPath = '';
			$('##divMCClientRefQASection fieldset.questionsFieldSet').each(function() {
				var thisQuestion = $(this).find('legend').text().trim();
				var thisAnswer = $(this).find('input.mcref-qa-answer:checked').parent().find('span').text().trim();
				if(thisQuestion.trim().length > 0 && thisQuestion.trim() != 'Choose your question' && thisQuestion.trim() != 'Please choose one from below')
					QAPath = QAPath + thisQuestion + ' / ' ;
				if(thisAnswer.trim().length > 0 && thisAnswer.trim() != 'Choose your question' && thisAnswer.trim() != 'Please choose one from below')
					QAPath = QAPath + thisAnswer + ' / ';
			});
			$('##questionAnswerPath').val(QAPath);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.QAHead)#">

<cfoutput>
<div<cfif local.strData.showContactFormFirst> class="hide" id="questionsSection" style="padding-top:20px;"</cfif>>
	<cfif len(trim(local.strData.feFormInstructionsStep1Content))>
		<div class="row-fluid control-group stepInstructions">#local.strData.feFormInstructionsStep1Content#</div>
	</cfif>
	<div id="divMCClientRefQASection"></div>
</div>

<!--- QA Template --->
<script id="mcref_qa_template" type="text/x-handlebars-template">
	<div id="mcref_qa_questionid_{{questionid}}" class="questionLRISWrapper {{##compare questionid '!=' 'x'}}mcref_qa_childof_{{parentquestionid}}{{/compare}}">
		<fieldset class="questionsFieldSet">
			<legend>{{arranswers.0.fieldtext}} <i class="icon icon-pencil hide mcref_qa_controls_{{questionid}} mcref_qa_edit" data-fieldid="{{questionid}}" data-treeorder="" tabindex="0" role="button" aria-label="Edit {{arranswers.0.fieldtext}}"></i></legend>
			<div class="form-group innerLRISContentWrapper">
				<div class="hide mcref_qa_controls_{{questionid}} clearfix">
					<label id="questionlabel_{{questionid}}"></label>
				</div>
				<div id="mcref_qa_answercontainer_{{questionid}}">
					{{##each arranswers}}
						<label for="answer_{{valueid}}" class="mcref-qa-radio-container radio" tabindex="0" aria-label="{{valuestring}}">
							<input type="radio" name="answer_{{fieldid}}" id="answer_{{valueid}}" value="{{valueid}}" data-questionid="{{fieldid}}" data-qamode="{{../qamode}}" class="mcref-qa-answer mcref-qa-radio" role="radio" aria-checked="false">
							<svg class="mcref-qa-radio-svg" width="15px" height="15px" viewBox="0 0 20 20" >
								<circle cx="10" cy="10" r="9" ></circle>
								<path d="M10,7 C8.34314575,7 7,8.34314575 7,10 C7,11.6568542 8.34314575,13 10,13 C11.6568542,13 13,11.6568542 13,10 C13,8.34314575 11.6568542,7 10,7 Z" class="mcref-qa-radio-inner"></path>
								<path d="M10,1 L10,1 L10,1 C14.9705627,1 19,5.02943725 19,10 L19,10 L19,10 C19,14.9705627 14.9705627,19 10,19 L10,19 L10,19 C5.02943725,19 1,14.9705627 1,10 L1,10 L1,10 C1,5.02943725 5.02943725,1 10,1 L10,1 Z" class="mcref-qa-radio-outer"></path>
							</svg>
							<span id="mcref_qa_answerlabel_{{valueid}}">{{valuestring}}</span>
						</label>
					{{/each}}
				</div>
			</div>
			{{##compare qamode '!=' 'getrootquestions'}}
				<div id="mcref_qa_btncontainer_{{questionid}}" class="text-right refqa-mt-3 refqa-mb-3 refqa-pr-4">
					<button class="btn btn-default btnPrevQuest" type="button" role="button">Go to Previous Question</button>
					<button class="btn btn-default btnGoToTop" type="button" role="button">Go to Top</button>
				</div>
			{{/compare}}
		</fieldset>
	</div>
</script>

<!--- load root question and answers --->
<script type="text/javascript">
	const #toScript(local.strData.strRefQuestionAnswers, "mcref_qa_rootqa")#
	loadReferralQuestionAnswers(mcref_qa_rootqa);
</script>

<!--- QA Sub-Panels Template --->
<script id="mcref_qa_subpaneltemplate" type="text/x-handlebars-template">
	<div id="mcref_qa_subpanels_{{answerid}}" class="refqa-pl-3 refqa-mt-3 mcref_qa_dependent_{{questionid}}">
		<input type="hidden" name="refQAPanelID" id="refQAPanelID" value="{{panelparentid}}">
		<label><b>Please select a specialization from below:</b></label>
		<div class="row-fluid">
			<div class="span6">
				{{##each arrleftpanels}}
					<label class="checkbox">
						<input type="checkbox" name="subpanelID_{{panelid}}" id="subpanelID_{{panelid}}" value="{{panelid}}" class="mcref_qa_subpanels" role="checkbox"> <span>{{name}}</span>
					</label>
				{{/each}}
			</div>
			<div class="span6">
				{{##each arrrightpanels}}
					<label class="checkbox">
						<input type="checkbox" name="subpanelID_{{panelid}}" id="subpanelID_{{panelid}}" value="{{panelid}}" class="mcref_qa_subpanels" role="checkbox"> <span>{{name}}</span>
					</label>
				{{/each}}
			</div>
		</div>
		<div id="mcref_qa_subpanelbtncontainer_{{questionid}}" class="text-right refqa-mt-3 refqa-mb-3 refqa-pr-4">
			<button type="button"class="btn btn-primary btnPanelContinue" id="btnPanelContinue_{{answerid}}" disabled="disabled" role="button">Continue</button> 
			<button type="button"class="btn btn-default btnSkipSubPanel" id="btnSkipSubPanel_{{answerid}}" role="button">Skip</button> 
			{{##compare questionnumber '>' 1}}
				<button class="btn btn-default btnPrevQuest" type="button" role="button">Go to Previous Question</button>
				<button class="btn btn-default btnGoToTop" type="button" role="button">Go to Top</button>
			{{/compare}}
		</div>
	</div>
</script>
</cfoutput>