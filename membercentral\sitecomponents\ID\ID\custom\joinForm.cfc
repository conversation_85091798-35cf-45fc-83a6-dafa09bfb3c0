<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];
            local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Dues are for a 1-year membership to ITLA. Renewal is sent according to anniversary month." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfessionalLicContent", type="CONTENTOBJ", desc="Content at top of Professional Licenses", value="Please add all State Licensures. For each license type added, you must provide your admittance date (even if it is an estimate) and current status." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent", type="CONTENTOBJ", desc="Content at top of page 2", value="Add step 2 content." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for CC", value="ALCCCIM" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodePayLater", type="STRING", desc="pay profile code for Cash/Check", value="PayLater" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);		
			local.tmpField = { name="ProfileCodePayBankDraft", type="STRING", desc="pay profile code for bank draft", value="ITLABD" };
				arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="SubscriptionUID", type="STRING", desc="UID of the Root Subscription that this form offers", value="6f00a0c7-5467-42a8-a52a-62b6fca01efd" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you. Your membership application has been submitted for review. You will receive an email from Idaho Trial Lawyers Association once your application has been processed." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="ITLA Membership Submission Confirmation" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application Submitted" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);			
            local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="membershipDuesUID", type="STRING", desc="Membership due UID", value="6530742c-457c-485f-a96c-af1de3808504" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
                
            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
            
            /* ***************** */
            /* set form defaults */
            /* ***************** */
            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='joinform',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
            
            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            switch (local.formAction) {
				case "processLookup":			
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and not len(arguments.event.getTrimValue('mk',''))) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
				case "processMembershipInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}

					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>

    </cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
        <cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='contact type')>
	
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##cboxOverlay{
					z-index: 99998 !important;
				}
				##colorbox{
					z-index: 99999 !important;
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				function addMember(memObj) {
					$.colorbox.close();

					assignMemberData(memObj);
				}

				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;vertical-align:middle;">					
						<a href="javascript:void(0)" id="btnAddAssoc" class="btn-blue" style="min-width: 170px;letter-spacing: 1px;padding: 10px 10px;font-size: 15px;">#variables.strPageFields.AccountLocatorButton#</a>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

  	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			
			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.membershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.membershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.membershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>	
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Member Information --->
		<cfset local.strMemberInformationContent = application.objCustomPageUtils.renderFieldSet(uid='137c2ced-408a-4cf6-a20c-401e4a8c67f2', mode="collection", strData=local.strData)>
		
		<!--- Address Information  --->
		<cfset local.addressInformation = application.objCustomPageUtils.renderFieldSet(uid='0296d202-1cdb-4329-a445-f711bb817247', mode="collection", strData=local.strData)>		
		
		<!--- Home Address Information--->
		<cfset local.homeAddressInformation = application.objCustomPageUtils.renderFieldSet(uid='980530e2-9455-478f-8dc1-b662f39b4b34', mode="collection", strData=local.strData)>
		
		<!--- Address Preference --->
		<cfset local.addressPreference = application.objCustomPageUtils.renderFieldSet(uid='6aa2c698-658e-40f8-9cca-83ccb29a169c', mode="collection", strData=local.strData)>
		
		<!--- get  Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.addressPreference.strFields#" item="local.thisField">
			<cfif local.addressPreference.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.addressPreference.strFields[local.thisField] eq "Use this address for Mailing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>	
		<cfset local.contacttype = "">
		<cfloop collection="#local.strMemberInformationContent.strFields#" item="local.thisField">
			<cfif local.strMemberInformationContent.strFields[local.thisField] eq "Contact Type">
				<cfset local.contacttype = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>		
		<!--- get Home Address --->
		<cfloop collection="#local.addressInformation.strFields#" item="local.thisField">
			<cfif local.addressInformation.strFields[local.thisField] eq "Address1">
				<cfset local.memberAltAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				
				.inner-middle-section input[type="text"] {
					width:206px!important;
				}
				.inner-middle-section select{
					width:220px!important;
				}
				##ProfessionalLicenseFields input[type="text"] {
					width:auto!important;
				}
				##ProfessionalLicenseFields select{
					width:auto!important;
				}
				##ProfessionalLicenseFields{
					width:100%!important;
				}
				
				div.tsAppSectionHeading{margin-bottom:20px}
				##Step1TopContent{margin-left:14px;margin-bottom: 15px;}
				##Autorenew-Position table tr > td:nth-child(2){width:30%!important;}
				##addressPreference table tr > td:nth-child(2){width:30%!important;}
				
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
							
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##ProfessionalLicenseFields input[type="text"] {
						width:206px!important;
					}
					##ProfessionalLicenseFields select{
						width:220px!important;
					}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{
						padding: 10px !important;
					}

				@media (min-width: 1200px){
					.eachRow{
						margin-bottom: 5px;
					}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.areaStatus{
						text-align:left;
						margin-left: 0px!important;
					}
					.areaState{
						text-align:left;
					}
					##state_table  .proLicenseLabel{
						display:block;
					}
					.wrapLeft{
						display: table-cell!important;
					}
					.span3 input {
						width: 90%!important;
					}
				}
				.wrapLeft{
						display:none;
				}
				.jsLabel{
					display:none !important;
					font-weight:700;
					font-size: 9pt;
				}
				.areaStatus{
					margin-left:0 !important;
				}
				.span3{
					margin-left:0 !important;
					margin-right:10px !important;
				}
				@media  (min-width: 767px) and  (max-width: 1200px){
					
						.span3 input{
							width: 90%!important;
						}
						.proLicenseLabel{
							font-weight:700;
							text-align:left;
							font-size: 9pt;
						}
						.eachRow{
						    margin-bottom: 5px;
						}
						.areaState{
							text-align:left;
						}

				}
				@media (max-width: 979px) and (min-width: 768px){
						.span3{
							margin-left:0 !important;
							margin-right:10px !important;
						}
						.span3 input{
							width: 90%!important;
						}
						.proLicenseLabel{
							font-weight:700;
							text-align:left;
							font-size: 9pt;
						}
						.eachRow{
						    margin-bottom: 5px;
						}
						.areaState{
							text-align:left;
						}

				}
				@media (max-width: 767px){
					.eachRow{
						    margin-bottom: 5px;
					}
					##state_table  .proLicenseLabel{
						display:none !important;
					}
					.jsLabel{
						display:block !important;
						margin-top: -5px;
					}
					
				}
				
			</style>
			<script language="javascript">
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strMemberInformationContent.jsValidation#

					if($('##memberInfoFieldFields select option:contains("Law Student"):selected').length == 0 && $.trim($('##memberInfoFieldFields .tsAppBodyText:contains("Firm/Company")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Firm/Company is required.';
					}

					if($('##memberInfoFieldFields select option:contains("Law Student"):selected').length && $.trim($('##memberInfoFieldFields .tsAppBodyText:contains("ITLA Student Chapter Member")').siblings().find("select").val()).length == 0)
					{
						arrReq[arrReq.length] = 'ITLA Student Chapter Member is required.';
					}

                    if($('##memberInfoFieldFields select option:contains("Legal Staff"):selected').length && $.trim($('##memberInfoFieldFields .tsAppBodyText:contains("Sponsoring ITLA Attorney Member")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Sponsoring ITLA Attorney Member is required.';
					}
					
					#local.addressInformation.jsValidation#		
                    #local.homeAddressInformation.jsValidation#		
					#local.addressPreference.jsValidation#	

					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					if(prof_license != "" && prof_license != null){
						isProfLicenseSelected = true;
						$.each(prof_license,function(i,val){
							var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
							if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
							if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
						});
					}

					var memberPrefPrimAddress = "";
					<cfif len(trim(local.memberPrefPrimAddress))>
						memberPrefPrimAddress = $('###variables.formName# ###local.memberPrefPrimAddress# option:selected').text();
					</cfif>

					var memberPrefBillAddress = "";
					<cfif len(trim(local.memberPrefBillAddress))>
						memberPrefBillAddress = $('###variables.formName# ###local.memberPrefBillAddress# option:selected').text();
					</cfif>	
					
					<cfif isDefined("local.memberAltAddress1")>
						if ( (memberPrefPrimAddress == "Address" || memberPrefBillAddress == "Address") && $.trim($('###variables.formName# ###local.memberMailAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Mailing Address is required.";
					</cfif>			

					 <cfif len(trim(local.contacttype))>
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						if (mcSel == 'Attorney' && !isProfLicenseSelected)
								arrReq[arrReq.length] = "Professional License is required.";	
					</cfif>

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
				
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>

					$("##mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							if(ui.checked){
								$("##state_table").show();
								$('##state_table').after('<div class="row-fluid eachRow" id="tr_state_'+ui.value+'" >'
													+'<div style="margin-bottom: -1px;margin-top: 15px;" class="row-fluid eachRow jsLabel">State Name</div>'
													+'<div class="span3 areaState" >'
													+'<span  class="tsAppBodyText">'+ui.text+'</span>'
													+'</div>'
													+'<div class="row-fluid eachRow jsLabel" >#variables.strProfLicenseLabels.profLicenseNumberLabel#</div>'
													+'<div class="span3" >'
													+'<input class="licensenumber" size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" />'
													+'<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
													+'</div>'
													+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</div>'
													+'<div class="span3" >'
													+'<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="tsAppBodyText" />'
													+'</div>'
													+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseStatusLabel#</div>'
													+'<div class="span3 areaStatus" >'
													+'<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="">Please Select</option><option value="active" selected="selected">Active</option><option value="inactive">Inactive</option><option value="disbarred">Disbarred</option><option value="suspended">Suspended</option></select>'
													+'</div>'
													+'</div>');

								if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
									mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
								}
								$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
								}else{
								$("##tr_state_"+ui.value).remove();
							}
					   },
					});			
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
				<input type="hidden" name="fa" id="fa" value="processMemberInfo">
				<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
				<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
				<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">				
				<cfinclude template="/model/cfformprotect/cffp.cfm">			
				<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
				<cfif len(variables.strPageFields.FormTitle)>
					<div class="row-fluid" id="FormTitleId"><p style="font-size:24px;">#variables.strPageFields.FormTitle#</p></div>
				</cfif>		
				
				<div id="content-wrapper" class="row-fluid">
					<cfif len(variables.strPageFields.Step1TopContent)>
						<div class="row-fluid" id="Step1TopContent">#variables.strPageFields.Step1TopContent#</div>
					</cfif>				
					<div class="row-fluid">
						<div class=" span12 tsAppSectionHeading">#local.strMemberInformationContent.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer" id="memberInfoFieldFields">
							#local.strMemberInformationContent.fieldSetContent#
						</div>
					</div></br>	
					<div class="row-fluid">
						<div class="tsAppSectionHeading">Professional License Information</div>
						<div class="tsAppSectionContentContainer professionalLisenceWrap">
							<cfif len(variables.strPageFields.ProfessionalLicContent)>
								<div id="ProfessionalLicense">#variables.strPageFields.ProfessionalLicContent#</div><br/>
							</cfif>
							<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
							<table cellpadding="3" border="0" cellspacing="0">		
								<tr class="top">
									<th class="tsAppBodyText" colspan="3" align="left">
										&nbsp;
									</th>
								</tr>
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText">Professional License:</td>
									<td class="tsAppBodyText">
										<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
											<cfloop query="local.qryOrgPlTypes">
												<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
												<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr class="top">
									<td class="tsAppBodyText" width="10"></td>
									<td class="tsAppBodyText"></td>
									<td class="tsAppBodyText"></td>
								</tr>
							</table>
							<table cellpadding="3" border="0" cellspacing="0" id="ProfessionalLicenseFields">
								<tr>
									<td class="tsAppBodyText wrapLeft" width="">&nbsp;</td>
									<td >
										
										<div class="row-fluid" id="state_table" style="display:none;" >
											<div class="span3 proLicenseLabel" >
												State Name
											</div>
											<div class="span3 proLicenseLabel"  >
												#variables.strProfLicenseLabels.profLicenseStatusLabel#
											</div>
											<div class="span3 proLicenseLabel"  >
												#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)
											</div>
											<div class="span3 proLicenseLabel" >
												#variables.strProfLicenseLabels.profLicenseStatusLabel#
											</div>


										</div>
									
											
											<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
												<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
													<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
													<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
													<cfset  local.license_status  = 'Active'>
													<div class="row-fluid"  >
														<div class="span3" id="tr_state_#local.thisItem#">
															<span  class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</span>
															
														</div>
														<div class="span3" >
															<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
															<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
															
														</div>
														<div class="span3" >
															<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" />
																<cfsavecontent variable="local.datejs">
																	<cfoutput>
																		<script language="javascript">
																			$(document).ready(function() { 
																				mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
																			});
																		</script>
																		<style type="text/css">
																		##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
																		</style>
																	</cfoutput>
																</cfsavecontent>
																<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
															
														</div>
														<div class="span3" >
															<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText">
																	<option value="">Please Select</option>
																	<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>																	
																</select>	
															
														</div>
													</div>
													
												</cfloop>
												</cfif>	
										
										
									</td>
								</tr>					
							</table>
						</div>
					</div>
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.addressInformation.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
						#local.addressInformation.fieldSetContent#
						</div>
					</div>
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.homeAddressInformation.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">	
							#local.homeAddressInformation.fieldSetContent#
						</div>
					</div>
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.addressPreference.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer" id="addressPreference">	
							#local.addressPreference.fieldSetContent#
						</div>
					</div>

					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
				</div>
				#application.objWebEditor.showEditorHeadScripts()#
			
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

				<script language="javascript">	
				
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
							function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
								var _address = _this.val();
								
								if(_address.length >0){
									if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
										$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
									}
								} else {
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
								}
							}
							
							$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
							});
						</cfloop>
					});
					
					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
				</form>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		<cfset var local = structNew()>
	
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID) >
		<!--- save member info and record history --->		
		<cfset local.objSaveMember.setRecordType(recordType='Individual')>
		<cfset local.objSaveMember.setMemberType(memberType='User')>
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		
		<cfset local.strResult1 = local.objSaveMember.saveData()>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
  
  	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>	
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
		
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	
		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
				 	function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();	

						<cfif val(local.subscriptionID)>
							if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
								arrReq[arrReq.length] = " Select Membership.";
							}
						</cfif>				      

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}

						#local.result.jsValidation#

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
				</script>
 			</cfoutput>
 		</cfsavecontent>
 		<cfhtmlhead text="#local.resultHtmlHeadText#">
				
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
			
			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>
			
			<cfform name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="btn-default tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.subscriptionUID)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
	
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.subscriptionUID)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		<cfset local.arrPayMethods = []>
		<cfif len(variables.strPageFields.ProfileCodePayCC) gt 0 AND variables.strPageFields.ProfileCodePayCC neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodePayCC)>		
		</cfif>
		<cfif len(variables.strPageFields.ProfileCodePayLater) gt 0 AND variables.strPageFields.ProfileCodePayLater neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodePayLater)>		
		</cfif>
		<cfif len(variables.strPageFields.ProfileCodePayBankDraft) gt 0 AND variables.strPageFields.ProfileCodePayBankDraft neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodePayBankDraft)>		
		</cfif>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>
				
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
				<script type="text/javascript">
					$('##mccfdiv_#variables.strPageFields.ProfileCodePayCC# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
												
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">
	
			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
				<div class="tsAppSectionContentContainer">						
					#local.strResult.formContent#
					<br/><br/>
				</div>
			</div>	
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Total Price</div><br/>
				<div class="tsAppSectionContentContainer">						
					#dollarFormat(local.strResult.totalFullPrice)#
					<br/><br/>					
				</div>
			</div>
			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton btn-default" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
		
		<!--- create subscriptions--->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.subscriptionUID)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = arguments.rc)>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)>

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID,orgID=variables.orgID)>
		
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
	
    <cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <!--- Member Information --->
		<cfset local.strMemberInformationContent = application.objCustomPageUtils.renderFieldSet(uid='137c2ced-408a-4cf6-a20c-401e4a8c67f2', mode="confirmation", strData=arguments.rc)>
		
		<!--- Address Information  --->
		<cfset local.addressInformation = application.objCustomPageUtils.renderFieldSet(uid='0296d202-1cdb-4329-a445-f711bb817247', mode="confirmation", strData=arguments.rc)>		
		
		<!--- Home Address Information--->
		<cfset local.homeAddressInformation = application.objCustomPageUtils.renderFieldSet(uid='980530e2-9455-478f-8dc1-b662f39b4b34', mode="confirmation", strData=arguments.rc)>
		
		<!--- Address Preference --->
		<cfset local.addressPreference = application.objCustomPageUtils.renderFieldSet(uid='6aa2c698-658e-40f8-9cca-83ccb29a169c', mode="confirmation", strData=arguments.rc)>
		
		

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationContent)>
					
					<div class="tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.ConfirmationContent#</br></br></div>
					</div>
					
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.ConfirmationContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
					<div class="tsAppSectionContentContainer">
						<div class="span12">You have received an application for membership through the online Idaho Trial Lawyers Association membership application form.</br></br></div>
					</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.strMemberInformationContent.fieldSetContent#
				#local.addressInformation.fieldSetContent#
				#local.homeAddressInformation.fieldSetContent#
				#local.addressPreference.fieldSetContent#		

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfset variables.memberEmail.TO = '' >
		<cfif StructKeyExists(arguments.rc,'email')>
			<cfset variables.memberEmail.TO = arguments.rc.email>
		</cfif>	
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>
		
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.memberEmail, emailTitle="Thank you for your application to ITLA", emailContent=local.confirmationHTMLToMember)>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to IAJ",
			emailhtmlcontent = local.confirmationHTMLToMember,
			siteID = variables.siteID,
			memberID = variables.useMID,
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub>
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to ITLA", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationPageHTMLContent>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					Idaho Trial Lawyers Association records indicate you are currently an ITLA member. Please <a href='/?pg=login'> click here </a> to log in.
				<cfelseif arguments.errorCode eq "acceptedfound">
					Idaho Trial Lawyers Association records indicate you are currently an ITLA member. Please <a href='/?pg=login'> click here</a> to log in.
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					You need to renew your ITLA membership. You will be re-directed to your renewal shortly.
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

  
  </cfcomponent>