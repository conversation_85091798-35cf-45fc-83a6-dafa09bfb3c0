<cfsavecontent variable="local.pghead">
	<cfoutput>
	<style type="text/css">
		.myMAJ .nav-tabs > .active > a,.myMAJ  .nav-tabs>.active>a:hover { color:##000000!important; background:##a19466!important;font-family: Helvetica; font-size:16px;font-weight:bold;}
		.myMAJ .nav-tabs a, .myMAJ .nav-tabs a:hover {  color:##a19466!important; background:##000000!important; font-family: Helvetica;font-size:16px;font-weight:bold; }
		.myMAJ .nav-tabs>li>a { margin-right:23px;   }
		.myMAJ .nav-tabs>li:last-child>a { margin-right:auto; }
		.myMAJ .nav { margin-bottom:0px; margin-left: 2px;}
		.myMAJ .sponsors { margin-bottom:0px; margin-top:0px;background:##a19466 none repeat scroll 0 0!important;color: ##000!important;padding: 1px;border-radius: 5px 4px 0 0;font-size: 16px;text-align: center;font-weight: bold!important; }
		.infoCont{padding-top:20px !important;margin-bottom:10px !important;}
		.MAJRow{margin-left:0px !important;margin-bottom:0px !important;}
		.tab-content { border:2px solid ##ddd; min-height:220px; padding:10px; margin-bottom:20px; background:##fff;}
		.showBullets{list-style: inside !important; text-decoration: underline;}
		.showBullets:hover {text-decoration: none;}
		.myMAJ .nav-tabs > li > a { border: 1px solid transparent; border-radius: 4px 4px 0 0; line-height: 1.42857; margin-right: 2px;font-size:16px;font-weight:bold;}
		.HeaderText { font-size: 20px; }
		.BodyText{font-family: Helvetica!important;font-size: 16px !important;}
		.tab-pane active BodyText{font-size: 14px!important;}
		.myMAJ a {font-family: Helvetica!important; color: ##a19466;font-size:16px!important;}
		.myMAJ a:hover{font-family: Helvetica!important; color: ##a19466; }
		.span1.myPhoto{width:auto
		li a:active {
			text-shadow: none;
		}
		.tab-pane p {
			padding-bottom: 0px;
			}
		
		h3{text-transform:none}
		
		.carousel-inner p { padding: 15px !important; margin: 15px 0 20px !important; }
		.nav.nav-tabs > li:first-child {
			border-left: none;
		} 
		.nav.nav-tabs > li {
			border-right: none;
			float:left;			
		}
		 ##scroll .tab-content .tab-pane {
			overflow-y: auto;
			min-height: 10px;
			max-height: 195px;
		} 
		ul.nav-tabs li a { 
			padding: 5px;
			font-size: 14px;
			font-family: 'Open Sans', sans-serif;
		}
		##myWY .tab-content .tab-pane {
			overflow-y: auto;			
			max-height: 200px;
		}	
		@media screen and (max-width:784px){
			ul.nav.nav-tabs{
				display:block!important;
			}
			
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pghead#">

<cfscript>
	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="UpcomingEventsTitle", type="STRING", desc="Title for Editableboxone section", value="Upcoming Events & CLE" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="UpcomingEventsContent", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MyEventsTitle", type="STRING", desc="Title for MyEvents section", value="My Events" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MyEventsContent", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="DueInvoicesTitle", type="STRING", desc="Title for Past Due Invoices section", value="Past Due Invoices" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="DueInvoicesContent", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="RecentpaymentsTitle", type="STRING", desc="Title for Recentpayments section", value="Recent Payments" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="RecentpaymentsContent", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AnnouncementsETitle", type="STRING", desc="Title for Annual Partner section", value="eNews" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="AnnouncementsEContent", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AnnouncementsTitle", type="STRING", desc="Title for Announcements section", value="Announcements" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="AnnouncementsContent", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="QuickLinksTitle", type="STRING", desc="Title for Quick Links section", value="Quick Links" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="QuickLinksDesc", type="CONTENTOBJ", desc="Quick Links Description", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="PreferredSupportersTitle", type="STRING", desc="Title for Featured Resources section", value="Preferred Supporters" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="PreferredSupportersDesc", type="CONTENTOBJ", desc="Featured Resources", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="FileshareDocsTitle", type="STRING", desc="Title for Fileshare Docs section", value="New Fileshare Docs" };
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="FileshareDocsDesc", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FiledownloadDescTitle", type="STRING", desc="Title for Fileshare Docs section", value="My File Downloads" };
		arrayAppend(local.arrCustomFields, local.tmpField);		
	local.tmpField = { name="FiledownloadDesc", type="CONTENTOBJ", desc="Content", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
</cfscript>

<cfoutput>
	<div class="container" id="myWY">
		<div class="row-fluid">			
			<div class="span12 row-fluid myMAJ infoCont">
				<div class="span1 myPhoto">
					<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
						<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
					<cfelse>
						<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
					</cfif>
				</div>
				<div class="span10 myInfo">
					<span class="HeaderText">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName# #session.cfcUser.memberData.suffix#</span><br />
					<span class="BodyText">
						<ul style="margin:0px" class="showBullets">
							<li><a href="/?pg=updateMember">Update My Profile</a></li>
							<li><a href="/?pg=updateMember##memberUpdateSectionLoginInfo">Change My Login</a></li>
							<li><a href="/?pg=updatemember&memaction=updatePhoto">Update My Photo</a></li>
							<li><a href="/?pg=listviewer">Browse My Listservers</a></li>
						</ul>
					</span>
				</div>
			</div>

			<div class="span12 row-fluid myMAJ MAJRow">
				<div class="span4">
					<div class="row-fluid" id="scroll">
						<ul class="nav nav-tabs" id="myEvents">
							<li class="active"><a href="##upcomingEvents" data-toggle="tab" class="MainNavText">#local.strPageFields.UpcomingEventsTitle#</a></li>
							<li><a href="##registeredEvents" data-toggle="tab" class="MainNavText">#local.strPageFields.MyEventsTitle#</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active BodyText" id="upcomingEvents">								
								#local.strPageFields.UpcomingEventsContent#								
								<div>
									<a href="/?pg=events&evAction=viewMonth"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
								</div>
							</div>
							<div class="tab-pane BodyText" id="registeredEvents">								
								#local.strPageFields.MyEventsContent#
								<br/>
								<div>
									<a href="/?pg=events&evAction=viewMonth"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
								</div>
							</div>				
						</div>
					</div>
				</div>

				<div class="span4">
					<div class="row-fluid">
						<ul class="nav nav-tabs" id="myInvoice">
							<li class="active"><a href="##invoices" data-toggle="tab" class="MainNavText">#local.strPageFields.DueInvoicesTitle#</a></li>
							<li><a href="##myPayments" data-toggle="tab" class="MainNavText">#local.strPageFields.RecentpaymentsTitle#</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active BodyText" id="invoices">
								#local.strPageFields.DueInvoicesContent#
								<br/>
								<div>
									<a href="/?pg=invoices"><i class="icon-calendar icon2x">&nbsp;</i><strong>View All Past Due Invoices</strong></a>
								</div>
							</div>
							<div class="tab-pane BodyText" id="myPayments">
								#local.strPageFields.RecentpaymentsContent#
							</div>
						</div>
					</div>
				</div>
				
				 <div class="span4">
					<div class="row-fluid">
						<h3 class="sponsors">#local.strPageFields.QuickLinksTitle#</h3>							
						<div class="tab-content">
							<div class="tab-pane active BodyText" id="quicklink">
								#local.strPageFields.QuickLinksDesc#
							</div>
						</div>
					</div>
				</div>			
			</div>
			
			<div class="span12 row-fluid myMAJ MAJRow">
				<div class="span4">
					<div class="row-fluid">
						<ul class="nav nav-tabs" >
							<li class="active"><a href="##Announcements" data-toggle="tab" class="MainNavText">#local.strPageFields.AnnouncementsTitle#</a></li>
							<li><a href="##AnnouncementsEContent" data-toggle="tab" class="MainNavText">#local.strPageFields.AnnouncementsETitle#</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active BodyText" id="Announcements">								
								#local.strPageFields.AnnouncementsContent#
							</div>
							<div class="tab-pane BodyText" id="AnnouncementsEContent">								
								#local.strPageFields.AnnouncementsEContent#
								<br/>
								<div>
									<a href="/?pg=events&evAction=viewMonth"><i class="icon-calendar icon2x">&nbsp;</i><strong>View the Full Calendar</strong></a>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="span4">
					<div class="row-fluid">
						<ul class="nav nav-tabs" id="myFile">
							<li class="active"><a href="##newfilesharedocs" data-toggle="tab" class="MainNavText">#local.strPageFields.FileshareDocsTitle#</a></li>
							<li><a href="##myfiledownloads" data-toggle="tab" class="MainNavText">#local.strPageFields.FiledownloadDescTitle#</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active BodyText" id="newfilesharedocs">
								#local.strPageFields.FileshareDocsDesc#
							</div>
							<div class="tab-pane BodyText" id="myfiledownloads">
								#local.strPageFields.filedownloadDesc#
							</div>
						</div>
					</div>
				</div>
				
				<div class="span4">
					<div class="row-fluid">					
						<h3 class="sponsors">#local.strPageFields.PreferredSupportersTitle#</h3>							
						<div class="tab-content">
							<div class="tab-pane active" id="sponsers">
								<div id="myCarousel" class="carousel slide text-center">
									<!-- Carousel items -->
									<div class="carousel-inner text-center">
										<cfif len(trim(local.strPageFields.PreferredSupportersDesc))>
											<cfloop list="#local.strPageFields.PreferredSupportersDesc#" index="local.thisImage" delimiters="||">
												<div class="<cfif ListFirst(local.strPageFields.PreferredSupportersDesc,'||') eq local.thisImage>active </cfif>item">
													<p><a href="/" target="_blank">#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#</a></p>
												</div>
											</cfloop>
										</cfif>
									</div>
								</div>
								<script type='text/javascript'>
									$(document).ready(function() {
										 $('.carousel').carousel({
											 interval: 3000
										 })
									});    
								</script>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfoutput>