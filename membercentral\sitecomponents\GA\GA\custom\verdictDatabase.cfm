<cfscript>
	local.vAction = event.getValue('action','search');
	
	local.baseLink 		= '/?pg=verdictDatabase';
	local.viewLink		= local.baseLink & '&action=view';
	local.editLink		= local.baseLink & '&action=edit';
	local.deleteLink	= local.baseLink & '&action=delete';
	local.saveLink		= local.baseLink & '&action=save';
	local.resultsLink	= local.baseLink & '&action=results';

	local.permissions 		= event.getValue('customPage.myRights');
	
</cfscript>

<cfif local.vAction eq "view">
	<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
	<cfif local.qryVerdict.recordcount is 0>
		<cflocation url="#local.baseLink#" addtoken="No">
	</cfif>
	<cfoutput>
	<p class="HeaderText">Viewing Information in GTLA's Verdict and Settlement Exchange Database</p>
	<div><input type="button" value="Back to Listing" onclick="history.go(-1);" class="BodyText" /></div>
	
	<br/>
	<table border="0" class="BodyText" width="100%" cellpadding="2" cellspacing="0">
	<cfif len(local.qryVerdict.date) and isdate(local.qryVerdict.date)>
		<tr valign="top">
			<td><strong>Date:</strong></td>
			<td>#dateformat(local.qryVerdict.date,"mm/dd/yyyy")#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.resolutiontype)>
		<tr valign="top">
			<td><strong>Resolution:</strong></td>
			<td>#local.qryVerdict.resolutiontype#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.casetype) or len(local.qryVerdict.casetypedetail)>
		<tr valign="top">
			<td><strong>Category:</strong></td>
			<td>[#local.qryVerdict.casetype#]: #local.qryVerdict.casetypedetail#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.casetitle)>
		<tr valign="top">
			<td><strong>Case:</strong></td>
			<td>#local.qryVerdict.casetitle#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.courtname)>
		<tr valign="top">
			<td><strong>Court:</strong></td>
			<td>#local.qryVerdict.courtname#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.countyname)>
		<tr valign="top">
			<td><strong>County:</strong></td>
			<td>#local.qryVerdict.countyname#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.docketnumber)>
		<tr valign="top">
			<td nowrap><strong>Docket Number:</strong></td>
			<td>#local.qryVerdict.docketnumber#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.sex)>
		<tr valign="top">
			<td><strong>Sex:</strong></td>
			<td>#local.qryVerdict.sex#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.age)>
		<tr valign="top">
			<td><strong>Age:</strong></td>
			<td>#local.qryVerdict.age#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.Occupation)>
		<tr valign="top">
			<td><strong>Occupation:</strong></td>
			<td>#local.qryVerdict.Occupation#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.amount) or len(local.qryVerdict.amountdetail)>
		<tr valign="top">
			<td nowrap><strong>Amount Awarded:</strong></td>
			<td>#dollarformat(local.qryVerdict.amount)# <cfif len(trim(local.qryVerdict.amountdetail)) gt 0> - #local.qryVerdict.amountdetail#</cfif></td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.settlementoffer)>
		<tr valign="top">
			<td nowrap><strong>Settlement Offer:</strong></td>
			<td>#local.qryVerdict.settlementoffer#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.facts)>
		<tr valign="top">
			<td><strong>Facts:</strong></td>
			<td>#local.qryVerdict.facts#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.Injuries)>
		<tr valign="top">
			<td><strong>Injuries:</strong></td>
			<td>#local.qryVerdict.Injuries#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.medicals)>
		<tr valign="top">
			<td><strong>Medicals:</strong></td>
			<td>#local.qryVerdict.medicals#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.lostwages)>
		<tr valign="top">
			<td nowrap><strong>Lost Wages:</strong></td>
			<td>#local.qryVerdict.lostwages#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.coverage)>
		<tr valign="top">
			<td><strong>Coverage:</strong></td>
			<td>#local.qryVerdict.coverage#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.experts)>
		<tr valign="top">
			<td><strong>Experts:</strong></td>
			<td>#local.qryVerdict.experts#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.plaintiffattorney)>
		<tr valign="top">
			<td nowrap><strong>Plaintiff Attorney:</strong></td>
			<td>#local.qryVerdict.plaintiffattorney#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.defenseattorney)>
		<tr valign="top">
			<td nowrap><strong>Defense Attorney:</strong></td>
			<td>#local.qryVerdict.defenseattorney#</td>
		</tr>
	</cfif>
	<cfif len(local.qryVerdict.submittingattorney)>
		<tr valign="top">
			<td nowrap><strong>Submitting Attorney:</strong></td>
			<td>#local.qryVerdict.submittingattorney#</td>
		</tr>
	</cfif>
	</table>
	</cfoutput>
	
<cfelseif local.vAction eq "edit">
	<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>

	<cfset local.allowForm = false>
	<cfif local.qryVerdict.recordcount is 0>
		<p class="HeaderText">Add to GTLA's Verdict and Settlement Exchange Database</p>
		<p class="BodyText">Complete the form below to add a verdict or settlement to the database.</p>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) or local.permissions.customAddDatabase eq "1">
			<cfset local.allowForm = true>
		<cfelse>
			<p class="BodyText">You do not have permission to add to this database.</p>
		</cfif>
	<cfelse>
		<p class="HeaderText">Edit Information in GTLA's Verdict and Settlement Exchange Database</p>
		<p class="BodyText">Complete the form below to edit this record.</p>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfset local.allowForm = true>
		<cfelse>
			<p class="BodyText">You do not have permission to edit this information.</p>
		</cfif>
	</cfif>

	<cfif local.allowForm>
		<cfset local.qryCaseTypes = getCaseTypes()>
		<cfset local.qryResolutionTypes = getResolutionTypes()>
		<cfset local.qryCountyNames = getCountyNames()>
		<cfset local.qryCourtNames = getCourtNames()>
		<cfsavecontent variable="local.JS">
			<cfoutput>
			<style type="text/css">
				##date { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
			</style>			
		
			<script language="JavaScript" type="text/javascript">	
			$(document).ready(function(){
				mca_setupDatePickerField('date');
			});				
			</script>
			</cfoutput>	
		</cfsavecontent>
		<cfhtmlhead text="#local.JS#">
		<cfoutput>
		<cfform name="verdictForm"  id="verdictForm" action="#local.saveLink#" method="post">
		<cfinput type="hidden" name="verdictid"  id="verdictid" value="#val(local.qryVerdict.verdictid)#">

		<div>
			<input type="submit" value="Save Verdict" name="btnSave" class="BodyText" /> &nbsp;
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and val(local.qryVerdict.verdictID) gt 0>
				<input type="button" name="btnDelete" value="Delete Verdict" class="BodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(local.qryVerdict.verdictID)#';"/> &nbsp;
			</cfif>
			<input type="button" value="Cancel" onclick="history.go(-1);" class="BodyText" />
		</div>
		<br/>
		<table border="0" class="BodyText" width="100%" cellpadding="2" cellspacing="0">

		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and val(local.qryVerdict.verdictID) gt 0>
			<tr valign="top">
				<td nowrap><strong>Approved Status:</strong></td>
				<td>
					<cfinput required="yes" message="Select an approval status." type="radio" value="1" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 1#"> Approved - available for viewing<br/>
					<cfinput type="radio" value="0" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 0#"> Not Approved - not available for viewing<br/>
				</td>
			</tr>
		<cfelse>
			<cfinput type="hidden" name="isApproved"  id="isApproved" value="#val(local.qryVerdict.isApproved)#">
		</cfif>
			
		<tr valign="top">
			<td nowrap><strong>Verdict Date:</strong></td>
			<td>
				<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 4px 7px;"></i></a>
			</td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>Resolution:</strong></td>
			<td>
				<select name="resolutiontype" class="BodyText" onchange="changeAvailability('resolutiontypeNew',this.value.length);">
				<option value="">Add new entry or select from list</option>
				<cfloop query="local.qryresolutiontypes">
					<option value="#local.qryresolutiontypes.resolutiontype#" <cfif local.qryVerdict.Resolutiontype eq local.qryresolutiontypes.resolutiontype>selected</cfif>>#local.qryresolutiontypes.resolutiontype#</option>
				</cfloop>
				</select>
				<cfinput class="BodyText" type="text" name="resolutiontypeNew" id="resolutiontypeNew" maxlength="15" size="20">
			</td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>Category:</strong></td>
			<td>
				<select name="casetype" class="BodyText" onchange="changeAvailability('casetypeNew',this.value.length);">
				<option value="">Add new entry or select from list</option>
				<cfloop query="local.qrycasetypes">
					<option value="#local.qrycasetypes.casetype#" <cfif local.qryVerdict.casetype eq local.qrycasetypes.casetype>selected</cfif>>#local.qrycasetypes.casetype#</option>
				</cfloop>
				</select>
				<cfinput class="BodyText" type="text" name="casetypeNew" id="casetypeNew" maxlength="50" size="30">
			</td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>Type of Case:</strong></td>
			<td><cfinput class="BodyText" type="text" name="casetypedetail"  id="casetypedetail" maxlength="250" size="70" value="#local.qryVerdict.casetypedetail#"></td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>Case:</strong></td>
			<td><cfinput class="BodyText" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70" value="#local.qryVerdict.casetitle#"></td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>Court:</strong></td>
			<td>
				<select name="courtname" class="BodyText" onchange="changeAvailability('courtnameNew',this.value.length);">
				<option value="">Add new entry or select from list</option>
				<cfloop query="local.qrycourtnames">
					<option value="#trim(local.qrycourtnames.courtname)#" <cfif trim(local.qryVerdict.courtname) eq trim(local.qrycourtnames.courtname)>selected</cfif>>#left(trim(local.qrycourtnames.courtname),80)#<cfif len(local.qrycourtnames.courtname) gt 80>...</cfif></option>
				</cfloop>
				</select>
				<cfinput class="BodyText" type="text" name="courtnameNew" id="courtnameNew" maxlength="75" size="30">
			</td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>County:</strong></td>
			<td>
				<select name="countyname" class="BodyText" onchange="changeAvailability('countynameNew',this.value.length);">
				<option value="">Add new entry or select from list</option>
				<cfloop query="local.qrycountynames">
					<option value="#trim(local.qrycountynames.countyname)#" <cfif trim(local.qryVerdict.countyname) eq trim(local.qrycountynames.countyname)>selected</cfif>>#trim(local.qrycountynames.countyname)#</option>
				</cfloop>
				</select>
				<cfinput class="BodyText" type="text" name="countynameNew" id="countynameNew" maxlength="20" size="20">
			</td>
		</tr>
		<tr>
			<td valign="top"><strong>Docket Number:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="docketnumber"  id="docketnumber" maxlength="30" size="30" value="#local.qryVerdict.docketnumber#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Sex:</strong></td>
			<td valign="top"><select name="sex" class="BodyText"><option value=""></option><option value="M" <cfif local.qryVerdict.sex eq "M">selected</cfif>>Male</option><option value="F" <cfif local.qryVerdict.sex eq "F">selected</cfif>>Female</option></select></td>
		</tr>
		<tr>
			<td valign="top"><strong>Age:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="age"  id="age" validate="integer" maxlength="2" size="5" value="#local.qryVerdict.age#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Occupation:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="Occupation"  id="Occupation" maxlength="75" size="30" value="#local.qryVerdict.Occupation#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Amount Awarded:</strong></td>
			<td valign="top">$<cfinput class="BodyText" type="text" name="amount"  id="amount" validate="float" message="Please enter a numerical amount in the 'Amount Awarded' field. Only numerals, commas, and periods allowed." size="10" value="#local.qryVerdict.amount#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Amount Detail:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="amountdetail"  id="amountdetail" maxlength="500" size="70" value="#local.qryVerdict.amountdetail#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Settlement Offer:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="settlementoffer"  id="settlementoffer" maxlength="250" size="70" value="#local.qryVerdict.settlementoffer#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Facts:</strong></td>
			<td valign="top"><textarea class="BodyText" name="facts" cols="70" rows="10">#local.qryVerdict.facts#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><strong>Injuries:</strong></td>
			<td valign="top"><textarea class="BodyText" name="injuries" cols="70" rows="10">#local.qryVerdict.injuries#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><strong>Medicals:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="medicals"  id="medicals" maxlength="400" size="70" value="#local.qryVerdict.medicals#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Lost Wages:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="lostwages"  id="lostwages" maxlength="30" size="30" value="#local.qryVerdict.lostwages#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Coverage:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="coverage"  id="coverage" maxlength="100" size="70" value="#local.qryVerdict.coverage#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Experts:</strong></td>
			<td valign="top"><textarea class="BodyText" name="experts" cols="70" rows="10">#local.qryVerdict.experts#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><strong>Plaintiff Attorney:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="PlaintiffAttorney"  id="PlaintiffAttorney" maxlength="200" size="70" value="#local.qryVerdict.PlaintiffAttorney#"></td>
		</tr>
		<tr>
			<td valign="top"><strong>Defense Attorney:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="DefenseAttorney"  id="DefenseAttorney" maxlength="300" size="70" value="#local.qryVerdict.DefenseAttorney#"></td>
		</tr>
		<tr>
			<td nowrap><strong>Submitting Attorney:</strong></td>
			<td valign="top"><cfinput class="BodyText" type="text" name="SubmittingAttorney"  id="SubmittingAttorney" maxlength="200" size="70" value="#local.qryVerdict.SubmittingAttorney#"></td>
		</tr>
		</table>
		</cfform>
		</cfoutput>
	</cfif>
	
<cfelseif local.vAction eq "delete">
	<cfset local.verdictID = int(val(event.getValue('verdictID',0)))>

	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
			delete from dbo.GA_VS_Verdicts
			where verdictID = <cfqueryparam value="#local.verdictID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	</cfif>
	<cfoutput>
		<p class="HeaderText">Information Updated</p>
		<br />
		<table border="0" cellpadding="2">
			<tr>
			<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
				<cfoutput>
				<form action="#local.baseLink#" method="post">
				<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
					<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
				</CFLOOP>
				<td><input type="submit" value="Return to Results" class="BodyText"/></td>
				</form>
				</cfoutput>
			</cfif>
			<td><cfoutput><input type="button" onclick="document.location.href='#local.baseLink#';" value="New Search" class="BodyText" /></cfoutput></td>
			</tr>
		</table>
	</cfoutput>	
	
<cfelseif local.vAction eq "save">
	<cfset local.verdictID = int(val(event.getValue('verdictID',0)))>

	<cfif local.verdictID is 0>
		<cfquery name="insertVerdict" datasource="#application.dsn.customApps.dsn#">
			set nocount on
			insert into dbo.GA_VS_Verdicts (resolutiontype, date, amount, amountDetail, casetype, casetypeDetail, casetitle,
				courtname, countyname, docketnumber, facts, sex, age, occupation, injuries, medicals, lostWages,
				Coverage, SettlementOffer, Experts, PlaintiffAttorney, DefenseAttorney, SubmittingAttorney, DepoMemberDataID, 
				dateLastModified, isApproved)
			VALUES (
				<cfif len(trim(event.getValue('resolutiontypeNew',''))) and trim(event.getValue('resolutiontypeNew','')) neq "disabled">
					<cfqueryparam value="#trim(replace(event.getValue('resolutiontypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					<cfqueryparam value="#trim(replace(event.getValue('resolutiontype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfif isDate(event.getValue('date'))>
					<cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
				<cfelse>
					<cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
				</cfif>
				<cfif len(event.getValue('amount'))>
					<cfqueryparam value="#RereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
				<cfelse>
					<cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('amountDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfif len(trim(event.getValue('casetypeNew',''))) and trim(event.getValue('casetypeNew','')) neq "disabled">
					<cfqueryparam value="#trim(replace(event.getValue('casetypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					<cfqueryparam value="#trim(replace(event.getValue('casetype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('casetypeDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfif len(trim(event.getValue('courtnameNew',''))) and trim(event.getValue('courtnameNew','')) neq "disabled">
					<cfqueryparam value="#trim(replace(event.getValue('courtnameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					<cfqueryparam value="#trim(replace(event.getValue('courtname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfif len(trim(event.getValue('countynameNew',''))) and trim(event.getValue('countynameNew','')) neq "disabled">
					<cfqueryparam value="#trim(replace(event.getValue('countynameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					<cfqueryparam value="#trim(replace(event.getValue('countyname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('sex'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('age'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('occupation'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('injuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('medicals'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('lostWages'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('Coverage'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('SettlementOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('Experts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('PlaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#session.cfcuser.memberdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				getdate(),
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
				<cfelse>
					<cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
				</cfif>
			)
			select SCOPE_IDENTITY() as verdictid
			set nocount off
		</cfquery>
		<cfset local.thisVerdictID = insertVerdict.verdictid>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<p>A verdict has been added to the Verdict Database.</p>
				<p>VerdictID: #local.thisverdictID#</p>
				<p><a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editLink#&verdictID=#local.thisverdictid#">Click here</a> to review the verdict and approve it for display.</p>
			</cfoutput>
		</cfsavecontent>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email="<EMAIL>" },
			emailto=[{ name="", email="<EMAIL>" }],
			emailreplyto="<EMAIL>",
			emailsubject="GTLA Verdict and Settlement Database Updated",
			emailtitle=event.getTrimValue('mc_siteinfo.sitename'),
			emailhtmlcontent=local.mailContent,
			siteID=event.getValue('mc_siteinfo.siteID'),
			memberID=event.getValue('mc_siteinfo.sysMemberID'),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>
		
	<cfelse>
		<cfquery name="updateVerdict" datasource="#application.dsn.customApps.dsn#">
			update dbo.GA_VS_Verdicts
			set <cfif len(trim(event.getValue('resolutiontypeNew',''))) and trim(event.getValue('resolutiontypeNew','')) neq "disabled">
					resolutiontype = <cfqueryparam value="#trim(replace(event.getValue('resolutiontypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					resolutiontype = <cfqueryparam value="#trim(replace(event.getValue('resolutiontype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfif isDate(event.getValue('date'))>
					date = <cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
				<cfelse>
					date = <cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
				</cfif>
				<cfif len(event.getValue('amount'))>
					amount = <cfqueryparam value="#rereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
				<cfelse>
					amount = <cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
				</cfif>
				amountDetail = <cfqueryparam value="#trim(event.getValue('amountDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfif len(trim(event.getValue('casetypeNew',''))) and trim(event.getValue('casetypeNew','')) neq "disabled">
					casetype = <cfqueryparam value="#trim(replace(event.getValue('casetypeNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					casetype = <cfqueryparam value="#trim(replace(event.getValue('casetype',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				casetypeDetail = <cfqueryparam value="#trim(event.getValue('casetypeDetail'))#" cfsqltype="CF_SQL_VARCHAR">,
				casetitle = <cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfif len(trim(event.getValue('courtnameNew',''))) and trim(event.getValue('courtnameNew','')) neq "disabled">
					courtname = <cfqueryparam value="#trim(replace(event.getValue('courtnameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					courtname = <cfqueryparam value="#trim(replace(event.getValue('courtname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfif len(trim(event.getValue('countynameNew',''))) and trim(event.getValue('countynameNew','')) neq "disabled">
					countyname = <cfqueryparam value="#trim(replace(event.getValue('countynameNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					countyname = <cfqueryparam value="#trim(replace(event.getValue('countyname',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				docketnumber = <cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
				facts = <cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				sex = <cfqueryparam value="#trim(event.getValue('sex'))#" cfsqltype="CF_SQL_VARCHAR">,
				age = <cfqueryparam value="#trim(event.getValue('age'))#" cfsqltype="CF_SQL_VARCHAR">,
				occupation = <cfqueryparam value="#trim(event.getValue('occupation'))#" cfsqltype="CF_SQL_VARCHAR">,
				injuries = <cfqueryparam value="#trim(event.getValue('injuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				medicals = <cfqueryparam value="#trim(event.getValue('medicals'))#" cfsqltype="CF_SQL_VARCHAR">,
				lostWages = <cfqueryparam value="#trim(event.getValue('lostWages'))#" cfsqltype="CF_SQL_VARCHAR">,
				Coverage = <cfqueryparam value="#trim(event.getValue('Coverage'))#" cfsqltype="CF_SQL_VARCHAR">,
				SettlementOffer = <cfqueryparam value="#trim(event.getValue('SettlementOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
				Experts = <cfqueryparam value="#trim(event.getValue('Experts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				PlaintiffAttorney = <cfqueryparam value="#trim(event.getValue('PlaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
				DefenseAttorney = <cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
				SubmittingAttorney = <cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
				dateLastModified = getdate(),
				isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="cf_sql_BIT">
			where verdictid = <cfqueryparam value="#event.getValue('verdictid')#" cfsqltype="cf_sql_integer">
		</cfquery>
	</cfif>

	<cfoutput>
		<p class="HeaderText">Information Saved. 
		<cfif NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>Once approved, the information you entered will be searchable in the database.</cfif>
		</p>
		<br />
		<table border="0" cellpadding="2">
		<tr>
			<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
				<cfoutput>
				<form action="#local.baseLink#" method="post">
				<CFLOOP INDEX="local.form_element" LIST="#session.lastverdictsearch.fieldnames#">
					<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#session.lastverdictsearch[local.form_element]#">
				</CFLOOP>
				<td><input type="submit" value="Return to Results" class="BodyText"/></td>
				</form>
				</cfoutput>
			</cfif>
			<td><cfoutput><input type="button" onclick="document.location.href='#local.baseLink#';" value="New Search" class="BodyText" /></cfoutput></td>
		</tr>
		</table>
	</cfoutput>

<cfelseif int(val(event.getValue('page',0))) gt 0>
	<cfset local.pageID = int(val(event.getValue('page',0)))>
	<cfset local.maxrows = 10>

	<cfquery name="local.qryMatches" datasource="#application.dsn.customApps.dsn#">
		select * 
		from dbo.GA_VS_Verdicts
		where 
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and len(event.getValue('isApproved'))>
			isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="CF_SQL_BIT">
		<cfelseif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			1=1
		<cfelse>
			isApproved = 1
		</cfif>
		<cfif len(event.getValue('resolutiontypes',''))>
			and resolutiontype = <cfqueryparam value="#event.getValue('resolutiontypes')#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif len(event.getValue('casetypes',''))>
			and casetype = <cfqueryparam value="#event.getValue('casetypes')#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif len(event.getValue('countyname',''))>
			and countyname = <cfqueryparam value="#event.getValue('countyname')#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif len(event.getValue('distinctyears',''))>
			and year(date) = <cfqueryparam value="#event.getValue('distinctyears')#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		<cfif len(event.getValue('keywords',''))>
			and (
				isnull(resolutiontype,'') + ' ' + isnull(cast(amount as varchar(30)),'') + ' ' +
				isnull(amountdetail,'') + ' ' + isnull(casetype,'') + ' ' + isnull(casetypedetail,'') + ' ' + 
				isnull(casetitle,'') + ' ' + isnull(courtname,'') + ' ' + isnull(docketnumber,'') + ' ' + 
				isnull(facts,'') + ' ' + isnull(sex,'') + ' ' + isnull(age,'') + ' ' + 
				isnull(occupation,'') + ' ' + isnull(injuries,'') + ' ' + isnull(medicals,'') + ' ' +
				isnull(lostwages,'') + ' ' + isnull(coverage,'') + ' ' + isnull(settlementoffer,'') + ' ' + 
				isnull(experts,'') + ' ' + isnull(plaintiffAttorney,'') + ' ' + isnull(defenseAttorney,'') + ' ' +
				isnull(submittingattorney,'') + ' ' + isnull(countyname,'')
				LIKE <cfqueryparam value="%#event.getValue('keywords')#%" cfsqltype="CF_SQL_VARCHAR">
				)
		</cfif>
	</cfquery>

	<cfset session.lastVerdictSearch = duplicate(form)>
	
	<cfif local.qryMatches.recordcount>
		<cfset local.startrow = ((local.pageID-1) * local.maxrows) + 1>
		<cfset local.endrow = local.startrow + local.maxrows - 1>
		<cfif local.qryMatches.recordcount lt local.endrow>
			<cfset local.endrow = local.qryMatches.recordcount>
		</cfif>
	<cfelse>
		<cfset local.startrow = 0>
		<cfset local.endrow = 0>
	</cfif>
	
	<cfoutput>
	<div class="HeaderText">Verdicts and Settlements Search Results</div>
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<div class="BodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
	</cfif>
	
	<script language="JavaScript">
		function prevPage() {
			var objForm = document.forms['frmHidden'];
			objForm.page.value = '#event.getValue('page')-1#';
			objForm.submit();
		}
		function nextPage() {
			var objForm = document.forms['frmHidden'];
			objForm.page.value = '#event.getValue('page')+1#';
			objForm.submit();
		}
	</script>
		
	<br/>
	<table width="100%" cellpadding="0" cellspacing="0">
	<tr><td class="BodyText">Showing #local.startrow# to #local.endrow# of #local.qryMatches.recordcount# matches</td>
		<td align="right">
			<cfif form.page gt 1>
				<input type="button" value="&lt;&lt; Previous Page" class="BodyText" onclick="prevPage();">
			</cfif>
			<cfif local.qryMatches.recordcount gt (form.page*local.maxrows)>
				<input type="button" value="Next Page &gt;&gt;" class="BodyText" onclick="nextPage();">
			</cfif>
			<input type="button" onclick="self.location.href='#local.baseLink#';" value="New Search" class="BodyText">
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) or local.permissions.customAddDatabase eq "1">
				<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Verdict" class="BodyText">
			</cfif>
		</td>
	</tr>
	</table>

	<br/>
	</cfoutput>
	<cfif local.qryMatches.recordcount eq 0>
		<cfoutput>
			<div class="BodyText">No records match your search criteria.</div>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<table border="0" class="BodyText" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
		<tr bgcolor="##999999">
			<td colspan="2"></td><th align="left">Category</th><th align="left">Date</th><th align="left">Court / Case</th><th align="left">Resolution</th>
		</tr>
		</cfoutput>
		<cfoutput query="local.qryMatches" startrow="#local.startrow#" maxrows="#local.maxrows#">
			<tr valign="top" <cfif local.qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
				<td <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#local.qryMatches.currentrow#.</td>
				<td <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>><a href="#local.viewLink#&verdictID=#local.qryMatches.verdictID#">View</a>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<br/><a href="/?pg=verdictDatabase&action=edit&verdictID=#local.qryMatches.verdictID#">Edit</a>
					</cfif>
				</td>
				<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.casetype#&nbsp;</td>
				<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;"><cfif isdate(local.qryMatches.date)>#dateformat(local.qryMatches.date,"m/d/yyyy")#</cfif>&nbsp;</td>
				<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">
					#local.qryMatches.courtname#
					<cfif len(local.qryMatches.courtname) and len(local.qryMatches.casetitle)><br/></cfif>
					#left(local.qryMatches.casetitle,100)#<cfif len(local.qryMatches.casetitle) gt 100>...</cfif>
					&nbsp;
				</td>
				<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-right:2px solid ##FF0000;<cfelse>border-right:1px solid ##666;</cfif>">#local.qryMatches.resolutiontype#&nbsp;</td>
			</tr>
		</cfoutput>
		<cfoutput>
		</table>
		</cfoutput>
	</cfif>

	<cfoutput>
	<form name="frmHidden" action="#local.baseLink#" method="post">
	<input type="hidden" name="page" value="">
	<cfloop INDEX="local.form_element" LIST="#FORM.fieldnames#">
		<cfif local.form_element neq "page">
			<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#form[local.form_element]#">
		</cfif>
	</CFLOOP>
	</form>
	</cfoutput>

<cfelse>
	<cfset local.qryCaseTypes = getCaseTypes()>
	<cfset local.qryResolutionTypes = getResolutionTypes()>
	<cfset local.qryCountyNames = getCountyNames()>
	<cfset local.qryYears = getYears()>

	<cfoutput>
	<p class="HeaderText">Search GTLA's Verdict and Settlement Exchange Database</p>

	<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)><p class="BodyText"><strong><em>Please <a href="#local.baseLink#">login</a> to submit a verdict or settlement.</em></strong></p></cfif>
	<p class="BodyText">GTLA's Verdict and Settlement Exchange Database contains reports submitted by members for publication. The database records include all information provided by members. The Verdict and Settlement Exchange is a valuable service to our members, and we welcome all reports. </p>
	<cfform action="#local.baseLink#" method="post">
	<input type="hidden" name="page" value="1" />
	<table class="BodyText">
	<tr>
		<td>Resolution Type:</td>
		<td>
			<select name="resolutiontypes" id="resolutiontypes" class="BodyText">
			<option value="">All</option>
			<cfloop query="local.qryResolutionTypes">
				<option value="#local.qryResolutionTypes.resolutiontype#">#local.qryResolutionTypes.resolutiontype#</option>
			</cfloop>
			</select>
		</td>
	</tr>
	<tr>
		<td>Case Category:</td>
		<td>
			<select name="casetypes" id="casetypes" class="BodyText">
			<option value="">All</option>
			<cfloop query="local.qryCaseTypes">
				<option value="#local.qryCaseTypes.casetype#">#local.qryCaseTypes.casetype#</option>
			</cfloop>
			</select>
		</td>
	</tr>
	<tr>
		<td>Verdict Year:</td>
		<td>
			<select name="distinctyears" id="distinctyears" class="BodyText">
			<option value="">All</option>
			<cfloop query="local.qryYears">
				<option value="#local.qryYears.year#">#local.qryYears.year#</option>
			</cfloop>
			</select>
		</td>
	</tr>
	<tr>
		<td>County:</td>
		<td>
			<select name="countyname" id="countyname" class="BodyText">
			<option value="">All</option>
			<cfloop query="local.qryCountyNames">
				<option value="#local.qryCountyNames.countyname#">#local.qryCountyNames.countyname#</option>
			</cfloop>
			</select>
		</td>
	</tr>
	<tr>
		<td>Keywords (optional):</td>
		<td><input type="text" name="keywords" id="keywords" class="BodyText" maxlength="70" size="70" /></td>
	</tr>
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<tr>
			<td>Approval Status:</td>
			<td>
				<select name="isApproved" id="isApproved" class="BodyText">
				<option value="">All</option>
				<option value="1">Approved Verdicts Only</option>
				<option value="0">Non-Approved Verdicts Only</option>
				</select>
			</td>
		</tr>
	</cfif>
	</table>
	<br />
	<input type="submit" value="Search Reports" class="BodyText"/>
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) or local.permissions.customAddDatabase eq "1">
		&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.editLink#';" value="Add Verdict" class="BodyText" />
	</cfif>

	</cfform>
	</cfoutput>
</cfif>

<cffunction name="getCaseTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT casetype
		FROM dbo.GA_VS_Verdicts
		WHERE casetype IS NOT NULL AND casetype <> ''
		ORDER BY casetype
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getResolutionTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT resolutiontype
		FROM dbo.GA_VS_Verdicts
		WHERE resolutiontype IS NOT NULL AND resolutiontype <> ''
		ORDER BY resolutiontype
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getCountyNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT countyname
		FROM dbo.GA_VS_Verdicts
		WHERE countyname IS NOT NULL AND countyname <> ''
		ORDER BY countyname
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getCourtNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT courtname
		FROM dbo.GA_VS_Verdicts
		WHERE courtname IS NOT NULL AND courtname <> ''
		ORDER BY courtname
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getYears" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT YEAR(DATE) as year
		FROM dbo.GA_VS_Verdicts
		WHERE DATE IS NOT NULL AND DATE <> ''
		ORDER BY YEAR(DATE) DESC
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getVerdict" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select * 
		from dbo.GA_VS_Verdicts
		where verdictid = <cfqueryparam value="#arguments.verdictID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfreturn qry>
</cffunction>
