<cfset local.qryTaskStatuses = createObject('component','model.admin.tasks.task').getTaskStatuses()>
<cfoutput>
<div class="divFilterProspects">
	<button id="filterProspectsBtn" class="btn btn-secondary btnFilterProspects btn-block" onclick="toggleAssignedTaskFilters('#local.strProjectData.taskFieldLabelPlural#');">Filter #local.strProjectData.taskFieldLabelPlural# <i class="icon-filter"></i></button>
</div>
<div class="tskSideBarFilters" style="display:none">
	<div class="row-fluid">
		<div class="span12">
			<form name="frmFilterAssignedTasks" id="frmFilterAssignedTasks" autocomplete="off">
				<input type="hidden" name="fMemberID2" id="fMemberID2" value="#local.strProjectData.orgMemberID#">
				<input type="hidden" name="fProjectID2" id="fProjectID2" value="#local.strProjectData.projectID#">
				<input type="hidden" name="fSelectedTaskID2" id="fSelectedTaskID2" value="#local.strProjectData.selectedTaskID#">
				<input type="text" name="fFirstName2" id="fFirstName2" value="" placeholder="First Name"><br/>
				<input type="text" name="fLastName2" id="fLastName2" value="" placeholder="Last Name"><br/>
				<input type="text" name="fCompany2" id="fCompany2" value="" placeholder="Company"><br/>
				<select name="fTaskStatus2" id="fTaskStatus2">
					<option value="">Select a Status</option>
					<cfloop query="local.qryTaskStatuses">
						<option value="#local.qryTaskStatuses.statusName#" <cfif local.qryTaskStatuses.statusName eq "Open">selected</cfif>>#local.qryTaskStatuses.statusName#</option>
					</cfloop>
				</select><br/>
				<button type="button" name="btnAssignedTasksFilter" id="btnAssignedTasksFilter" class="btn btn-primary" onclick="doFilterAssignedTasks();">Filter</button>
				<button type="button" name="btnResetAssignedTasksFilter" id="btnResetAssignedTasksFilter" class="btn btn-default" onclick="clearAssignedTasksFilters();">Clear</button>
			</form>
		</div>
	</div>
</div>
<div class="tskAssigned tskSideBar">
	<div class="row-fluid">
		<div class="span12 tskAssignedLabel">Assigned To You <i class="icon-filter" style="display:none"></i></div>
	</div>
	<div id="tskAssignedProspects">
		<cfinclude template="dsp_projectAssignedTasksSidebar_entries.cfm">
	</div>
</div>
</cfoutput>