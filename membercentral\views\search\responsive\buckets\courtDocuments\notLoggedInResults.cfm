<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	<div style="padding-bottom:10px;">
		<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
		<button class="btn" onclick="document.location.href='/?pg=uploadDocuments'"><i class="icon-upload"></i> Upload a document</button>
	</div>
</cfoutput>
<cfif (not variables.cfcuser_isSiteAdmin and val(local.qryBucketInfo.restrictToGroupID) gt 0 and local.qryBucketInfo.isMemberInRestrictedGroup is not 1)>
	<cfoutput>
	<cfif not variables.cfcuser_isLoggedIn>
		<div style="padding-bottom:3px;">You are not authorized to see these results.</div>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	<cfelse>
		<div style="padding-bottom:3px;">You are not authorized to see these results.</div>
	</cfif>
	</cfoutput>
<cfelseif local.strResultsCount.itemCount EQ 0>
	<cfoutput>#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#</cfoutput>
<cfelse>
	<cfoutput>
	<cfif val(local.strResultsCount.itemCount) gt 0>
		<div style="padding-bottom:3px;"><strong>#Numberformat(local.strResultsCount.itemCount)#</strong> documents found</div>
	</cfif>
	<cfif not variables.cfcuser_isLoggedIn>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</cfif>
	</cfoutput>
</cfif>