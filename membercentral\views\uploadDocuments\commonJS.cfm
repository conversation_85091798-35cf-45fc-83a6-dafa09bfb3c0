<cfif arguments.event.getValue('step') EQ 2>
	<cfsavecontent variable="local.pageJS">
		<cfoutput>
		<link href="/assets/common/javascript/jQueryAddons/plupload/3.1.2/jquery.plupload.queue/css/jquery.plupload.queue.css" rel="stylesheet" type="text/css" />
	 	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/plupload.full.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/jquery.plupload.queue/jquery.plupload.queue.min.js"></script>
		<script type="text/javascript">
			let depoDocUploader, depoDocsUploadedCount = 0;

			function onChangeDepoDocStates() {
				validateUploadDocsForm('prevalidate');
			}
			function onClickCreditPreference(credType) {
				if (credType.toLowerCase() == 'amazon') $('##depoDocAmazonBucksInfo').show();
				else  $('##depoDocAmazonBucksInfo').hide();
				validateUploadDocsForm('prevalidate');
			}
			function showUploadSection() {
				if (validateUploadDocsForm('validateInitSteps')) {
					$('##awardCredType').html($('input[name="credType"]:checked').val() == 'amazon' ? 'Amazon Credits' : 'Deposition Purchase Credits');
					$('##depoDocOrgDisp').html($('##docState option:selected').text());
					$('.docUploadSteps').hide();
					$('.docUpdTool').show();
					if (depoDocUploader.pluploadQueue().files.length)
						enableStartUploadOfFilesBtn();
				}
			}
			function showLearnMoreSection(){
				$("##learnMoreSection").show();
			}
			function startUploadDocs() {
				$('##btnUploadDocs').prop('disabled',true).html('Uploading Documents...');
				if (validateUploadDocsForm('final')) {
					$('.removeDepoDocFile').remove();
					depoDocUploader.pluploadQueue().start();
				}
			}
			function validateUploadDocsForm(validateMode) {
				let emailRegEx = new RegExp("#application.regEx.email#","i");
				let arrReq = [];

				let enableButtonWhenError = false, disableButtonWhenError = false, enableButtonWhenNoError = false, 
					disableButtonWhenNoError = false, showErrorMsg = true;
				
				if (validateMode == 'final') {
					enableButtonWhenError = true;
				} else if (validateMode == 'prevalidate') {
					disableButtonWhenError = true;
					enableButtonWhenNoError = true;
					showErrorMsg = false;						
				} else if (validateMode == 'validateInitSteps') {
					disableButtonWhenNoError = true;
				}

				if ($('##docState').val() == '') arrReq.push('Which trial lawyer group gets Credit for your depositions?');
				if (!$('input[name="credType"]').is(':checked')) arrReq.push('What type of Credit do you want for your depositions?');
				else if ($('input[name="credType"]:checked').val() == 'amazon') {
					if (!$('##depoDocAmazonBucksFullName').val().trim().length)
						arrReq.push('Enter the Full Name for Amazon Gift Card Recipient.');
					if (!$('##depoDocAmazonBucksEmail').val().trim().length || !(emailRegEx.test($('##depoDocAmazonBucksEmail').val())))
						arrReq.push('Enter a valid Email Address for Amazon Gift Card Recipient.');
				}
				if ($('##depoDocUploader').is(':visible') && !depoDocUploader.pluploadQueue().files.length)
					arrReq.push('Drag and drop files to the area below, or click "Add files" to browse your computer.');

				if (arrReq.length) {
					if (enableButtonWhenError) {
						enableStartUploadOfFilesBtn();
					} else if (disableButtonWhenError) {
						disableStartUploadOfFilesBtn();
					}
					if (showErrorMsg) uploadDocsShowErr(arrReq.join('<br/>'));
					else uploadDocsHideErr();
					return false;
				} else {
					if (enableButtonWhenNoError) {
						enableStartUploadOfFilesBtn();
					} else if (disableButtonWhenNoError) {
						disableStartUploadOfFilesBtn();
					}
					uploadDocsHideErr();
					return true;
				}
			}
			function uploadDocsShowErr(errmsg) {
				$('##depoDocUploaderError').html(errmsg).show();
			}
			function uploadDocsHideErr() {
				$('##depoDocUploaderError').html('').hide();
			}
			function enableStartUploadOfFilesBtn() {
				$('##btnUploadDocs').addClass('mc_blinkerbtn').prop('disabled',false).html('Start Upload of Files');
			}
			function disableStartUploadOfFilesBtn() {
				$('##btnUploadDocs').removeClass('mc_blinkerbtn').prop('disabled',true);
			}
			function showUploadSteps() {
				$('##docUploadDocConf').html('').hide();
				$('.docUpdTool').hide();
				$('.docUploadSteps').show(300);
			}
			function onDepoDocFileAddedOrRemoved(up, files) {
				$.each(depoDocUploader.pluploadQueue().files,function(i, file) {
					$('##'+file.id).find('.plupload_file_name').html('<a href="##" onclick="removeDepoDocFileFromQueue(\''+file.id+'\');return false;" class="removeDepoDocFile" title="Remove File"><img src="/assets/common/images/cancel.png" alt="Remove File"></a> ' + file.name);
				});
			}
			function removeDepoDocFileFromQueue(id) {
				depoDocUploader.pluploadQueue().removeFile(id);
			}
			function uploadDepositionsAgain() {
				$('##docUploadDocConf').html('').hide();
				$('##depoDocUploader,##depoDocUploaderButtonContainer').show(300);
				disableStartUploadOfFilesBtn();
			}
			function clearDepoDocUploadQueue() {
				$.each(depoDocUploader.pluploadQueue().files,function(i, file) {
					depoDocUploader.pluploadQueue().removeFile(file.id);
				});
				depoDocsUploadedCount = 0;
			}

			$(function() {
				depoDocUploader = $("##depoDocUploader").pluploadQueue({
					runtimes : 'html5',
					url : '#local.frmUploadLink#',
					multipart : true,
					file_data_name : 'depoDoc', 
					multiple_queues : true,
					init : {
						PostInit: function() {
							$('##depoDocUploader .plupload_header_text').html('Drag and drop files to the area below, or click "Add files" to browse your computer. Click the "Start upload of files" button after selecting your files.');
							$('##depoDocUploader_container').attr('title','Add files here');
						},
						FilesAdded: onDepoDocFileAddedOrRemoved,
						FilesRemoved: onDepoDocFileAddedOrRemoved,
						QueueChanged: function(up, files) {
							validateUploadDocsForm('prevalidate');
						},
						BeforeUpload: function(up, file) {
							up.settings.multipart_params = {
								'docState': $('##docState').val(),
								'creditType': $('input[name="credType"]:checked').val(),
								'depoDocAmazonBucksFullName': $('input[name="credType"]:checked').val() == 'amazon' 
																? $('##depoDocAmazonBucksFullName').val().trim()
																: '',
								'depoDocAmazonBucksEmail': $('input[name="credType"]:checked').val() == 'amazon' 
																? $('##depoDocAmazonBucksEmail').val().trim()
																: ''
							};

							/*Next line needed to force the params to be passed along in the POST*/
							up.setOption('params', up.settings.multipart_params);
						},
						FileUploaded: function(up, file, ret) {
							let retRegex = /[\r\n]/ig;
							let retObj = JSON.parse(ret.response.replaceAll(retRegex,''));
							if (retObj.success) {
								depoDocsUploadedCount += retObj.uploadeddocscount;
							} else {
								if (retObj.errmsg) uploadDocsShowErr(retObj.errmsg);
								else uploadDocsShowErr('We were unable to upload the files.');
							}
						},
						UploadComplete: function(up, files) {
							if (depoDocsUploadedCount > 0) {
								$('##docUploadDocConf')
									.load('#local.showDocUploadConfirmLink#&docsCount='+depoDocsUploadedCount, function() {
										$('html, body').animate({
											scrollTop: $('##docUploadDocConf').offset().top - 175
										}, 750);
										clearDepoDocUploadQueue();
									})
									.html('<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>')
									.show();
								$('##depoDocUploader,##depoDocUploaderButtonContainer').hide();
							}
						},
						Error: function(up, error) {
							uploadDocsShowErr('We ran into an issue. ' + error.message);
						}
					}
				});
			});
		</script>
		<style type="text/css">
			.depoDocLabel { font-size:18px;line-height:30px;font-weight:600; }
			##depoDocUploader .plupload_header {background:##dfdfdf;}
			##depoDocUploader .plupload_header_content {color:##003366 !important;background:none;padding-left:10px !important;}
			##depoDocUploader .plupload_header_title {font-weight:600;}
			##depoDocUploader .plupload_header_text {font-weight:400;font-size: 13px;padding-bottom:5px;}
			##depoDocUploader .plupload_button.plupload_start {display:none;}
			##depoDocUploader .plupload_droptext {font-size:18px;color:##777;}
			.depoDocNavTab li a {background-color:##f4f4f4 !important;border-bottom:1px solid ##ddd;color:##036 !important;font-weight:bold;}
			.mc_blinkerbtn { animation: mc_blinker 4000ms linear infinite; }
			@keyframes mc_blinker { 50% {opacity: 0.5;} 75% {opacity: 0.7;} }
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">
</cfif>