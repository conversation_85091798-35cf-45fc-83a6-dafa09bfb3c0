<cfscript>
	vAction = event.getValue('action','search');

	local.baseLink 		= '/?pg=verdictDatabase';
	local.viewLink		= local.baseLink & '&action=view';
	local.editLink		= local.baseLink & '&action=edit';
	local.deleteLink	= local.baseLink & '&action=delete';
	local.saveLink		= local.baseLink & '&action=save';
	local.resultsLink	= local.baseLink & '&action=results';
	
</cfscript>

	
	<cfswitch expression="#variables.vAction#">
	
		<!--- view record --->
		<cfcase value="view">
			<cfoutput>
				<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
				<cfif local.qryVerdict.recordcount is 0>
					<cflocation url="#local.baseLink#" addtoken="No">
				</cfif>
				<p class="TitleText">Viewing Information in KJA's Verdict and Settlement Database</p>
<!--- 
				<div><input type="button" value="Back to Listing" onclick="history.go(-1);" class="BodyText" /></div>
 --->
						<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
							<form action="#local.resultsLink#" method="post">
							<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
								<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
							</CFLOOP>
							<td><input type="submit" value="Back to Listing" class="BodyText"/></td>
							</form>
						</cfif>
				
				<br/>
				<table border="0" class="BodyText" width="100%" cellpadding="2" cellspacing="0">
					<cfif len(local.qryVerdict.date) and isdate(local.qryVerdict.date)>
						<tr valign="top">
							<td><strong>Date:</strong></td>
							<td>#dateformat(local.qryVerdict.date,"mm/dd/yyyy")#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.plaintiffattorney)>
					<tr valign="top">
						<td nowrap><strong>Plaintiff Attorney:</strong></td>
						<td>#local.qryVerdict.plaintiffattorney#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.plaintiffFirm)>
					<tr valign="top">
						<td nowrap><strong>Firm:</strong></td>
						<td>#local.qryVerdict.plaintiffFirm#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.plaintiffCity)>
					<tr valign="top">
						<td nowrap><strong>City:</strong></td>
						<td>#local.qryVerdict.plaintiffCity#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.plaintiffState)>
					<tr valign="top">
						<td nowrap><strong>State:</strong></td>
						<td>#local.qryVerdict.plaintiffState#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.plaintiffZip)>
					<tr valign="top">
						<td nowrap><strong>Zip:</strong></td>
						<td>#local.qryVerdict.plaintiffZip#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.plaintiffPhone)>
					<tr valign="top">
						<td nowrap><strong>Phone:</strong></td>
						<td>#local.qryVerdict.plaintiffPhone#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.plaintiffFax)>
					<tr valign="top">
						<td nowrap><strong>Fax:</strong></td>
						<td>#local.qryVerdict.plaintiffFax#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.plaintiffEmail)>
					<tr valign="top">
						<td nowrap><strong>Email:</strong></td>
						<td>#local.qryVerdict.plaintiffEmail#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.resolutiontype)>
						<tr valign="top">
							<td><strong>Resolution:</strong></td>
							<td>#local.qryVerdict.resolutiontype#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.categoryDescription)>
						<tr valign="top">
							<td><strong>Category:</strong></td>
							<td>#local.qryVerdict.categoryDescription#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.casetitle)>
						<tr valign="top">
							<td><strong>Case:</strong></td>
							<td>#local.qryVerdict.casetitle#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.countyName)>
						<tr valign="top">
							<td><strong>County:</strong></td>
							<td>#local.qryVerdict.countyName#</td>
						</tr>
					</cfif>
					<!---
					<cfif len(local.qryVerdict.courtName)>
						<tr valign="top">
							<td><strong>Court:</strong></td>
							<td>#local.qryVerdict.courtName#</td>
						</tr>
					</cfif>
					--->
					<cfif len(local.qryVerdict.lastname)>
						<tr valign="top">
							<td><strong>Judge:</strong></td>
							<td>#local.qryVerdict.lastname#, #local.qryVerdict.firstname#<cfif local.qryVerdict.MiddleInitial NEQ ""> #local.qryVerdict.middleinitial#</cfif></td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.docketnumber)>
						<tr valign="top">
							<td nowrap><strong>Docket Number:</strong></td>
							<td>#local.qryVerdict.docketnumber#</td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.amount)>
						<tr valign="top">
							<td nowrap><strong>Amount Awarded:</strong></td>
							<td>#local.qryVerdict.amount# </td>
						</tr>
					</cfif>
					<cfif len(local.qryVerdict.facts)>
					<tr valign="top">
						<td><strong>Facts:</strong></td>
						<td>#local.qryVerdict.facts#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.Injuries)>
					<tr valign="top">
						<td><strong>Injuries:</strong></td>
						<td>#local.qryVerdict.Injuries#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.disabilityDueToInjuries)>
					<tr valign="top">
						<td><strong>Disability Due To Injuries:</strong></td>
						<td>#local.qryVerdict.disabilityDueToInjuries#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.medicalExpense)>
					<tr valign="top">
						<td><strong>Medicals:</strong></td>
						<td>#local.qryVerdict.medicalExpense#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.makingDifference)>
					<tr valign="top">
						<td><strong>Making A Difference:</strong></td>
						<td>#local.qryVerdict.makingDifference#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.comments)>
					<tr valign="top">
						<td><strong>Comments:</strong></td>
						<td>#local.qryVerdict.comments#</td>
					</tr>
					</cfif>					
					<cfif len(local.qryVerdict.lostwages)>
					<tr valign="top">
						<td nowrap><strong>Lost Wages:</strong></td>
						<td>#local.qryVerdict.lostwages#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.finalDemand)>
					<tr valign="top">
						<td><strong>Final Demand:</strong></td>
						<td>#local.qryVerdict.finalDemand#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.finalOffer)>
					<tr valign="top">
						<td nowrap><strong>Final Offer:</strong></td>
						<td>#local.qryVerdict.finalOffer#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.plaintifExpertWitness)>
					<tr valign="top">
						<td><strong>Plaintif Expert Witness:</strong></td>
						<td>#local.qryVerdict.plaintifExpertWitness#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.defenseExpertWitness)>
					<tr valign="top">
						<td><strong>Defense Expert Witness:</strong></td>
						<td>#local.qryVerdict.defenseExpertWitness#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.defenseattorney)>
					<tr valign="top">
						<td nowrap><strong>Defense Attorney:</strong></td>
						<td>#local.qryVerdict.defenseattorney#</td>
					</tr>
					</cfif>
					<cfif len(local.qryVerdict.submittingattorney)>
					<tr valign="top">
						<td nowrap><strong>Submitting Attorney:</strong></td>
						<td>#local.qryVerdict.submittingattorney#</td>
					</tr>
					</cfif>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="edit">
			<cfset local.qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
			<cfset allowForm = false>
			
			<cfoutput>
				
				<!--- 
					if verdictID is 0
						ADD
					else
						EDIT
				 --->
				
				<cfif val(event.getValue('verdictID',0))>
					<!--- EDIT Verdict: --->
					<p class="TitleText">Edit Information in KJA's Verdict and Settlement  Database</p>
					<p class="BodyText">Complete the form below to edit this record.</p>
					
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<cfset allowForm = true>
					<cfelseif val(event.getValue('customPage.myRights.customAddDatabase',0)) AND local.qryVerdict.depoMemberDataID EQ #session.cfcuser.memberdata.depoMemberDataID#>
						<cfset allowForm = true>
					<cfelse>
						<p class="BodyText">You do not have permission to edit this information.</p>
						<p class="BodyText"><a href="/?pg=verdictDatabase">Return to search.</a></p>
					</cfif>
					
					
				<cfelse>
					<!--- ADD Verdict: --->
					<p class="TitleText">Add to KJA's Verdict and Settlement  Database</p>
					<p class="BodyText">Complete the form below to add a verdict or settlement to the database.</p>
					
					<cfif val(event.getValue('customPage.myRights.view',0))>
						<cfset allowForm = true>
					<cfelse>
						<p class="BodyText">You do not have permission to add to this database.</p>
						<p class="BodyText"><a href="/?pg=verdictDatabase">Return to search.</a></p>
					</cfif>
					
				</cfif>
			</cfoutput>
			
			<cfif allowForm>
				<cfoutput>
				<cfset local.qryCategories 			= getCategories()>
				<cfset local.qryResolutionTypes = getResolutionTypes()>
				<cfset local.qryCountyNames 		= getCountyNames()>
				
				<cfset local.judgeSelect 		= buildJudgeForVerdict(local.qryVerdict.judgeID) />
				<cfset local.judgeNew				= buildJudgeForVerdict(local.qryVerdict.judgeID,1) />
				
				<cfsavecontent variable="JS">
					<style type="text/css">
						##date { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
					</style>						
					<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
					<!--
					function changeAvailability(formfieldid,disableflag) {
						var ff = document.getElementById(formfieldid);
						ff.disabled = disableflag;
						if (disableflag) ff.value='disabled';
						else ff.value='';
					}  

					function toggleJudge() {
						var newJudge 	= document.getElementById('newJudge');
						var judgeObj 	= document.getElementById('objJudge');
						var judgeLink = document.getElementById('judgeLink');
						if (newJudge.value == 0) {													
							judgeObj.innerHTML = '#jsStringFormat(local.judgeNew)#';	
							judgeLink.innerHTML = 'cancel';	
							newJudge.value = 1;
						}
						else {													
							judgeObj.innerHTML = '#jsStringFormat(local.judgeSelect)#';
							judgeLink.innerHTML = 'add new';
							newJudge.value = 0;
						}
					}		
										
					function chkVerdictForm()
					{
						var theForm = document.forms["verdictForm"];
						var errMsg = '';
						
/*
						var theJudgment = parseFloat(theForm.judgementAmount.value);
						var thOffer = parseFloat(theForm.offerAmount.value);
						var theDemand = parseFloat(theForm.demandAmount.value);
						var theSettlement = parseFloat(theForm.settlementAmount.value);
						
						if (trim(theForm.date.value) == '')
						{
							errMsg += 'Please enter a Date of Resolution.\r\n';
						}
						if (trim(theForm.resolutiontypeID.value) == '')
						{
							errMsg += 'Please select a Resolution.\r\n';
						}
*/						
						if ((trim(theForm.categoryID.value) == '') && (trim(theForm.categoryNew.value) == ''))
						{
							errMsg += 'Please enter the new category or select from the list.\r\n';
						}
						if ((trim(theForm.resolutiontypeid.value) == '') && (trim(theForm.resolutiontypeNew.value) == ''))
						{
							errMsg += 'Please enter the resolution type or select from the list.\r\n';
						}
/*
						if (trim(theForm.casetitle.value) == '')
						{
							errMsg += 'Please enter a Case Caption.\r\n';
						}
						if (trim(theForm.injuries.value) == '')
						{
							errMsg += 'Please enter details for Injuries.\r\n';
						}
						
						if ((!isNaN(theJudgment) && !isNaN(thOffer) && !isNaN(theDemand) && !isNaN(theSettlement)) &&
							((theJudgment + thOffer + theDemand + theSettlement) <= 0))
						{
							errMsg += 'Judgment, Offer, Demand or Settlement must be above $0.00.\r\n';
						}
						
						
						if (trim(theForm.countyID.value) == '')
						{
							errMsg += 'Please select a County.\r\n';
						}
						if (trim(theForm.courtID.value) == '0')
						{
							errMsg += 'Please select a Courthouse.\r\n';
						}
						if (trim(theForm.PlaintiffAttorney.value) == '')
						{
							errMsg += 'Please enter the Plaintiff Attorney Name.\r\n';
						}
						if (trim(theForm.PlaintiffAttorneyFirm.value) == '')
						{
							errMsg += 'Please enter the Plaintiff Attorney Firm.\r\n';
						}
*/
						
						/*
						if (((trim(theForm.expertFirstNameNew.value) != '') ||
							(trim(theForm.expertMINew.value) != '') ||
							(trim(theForm.expertLastNameNew.value) != '') ||
							(trim(theForm.expertSuffixNew.value) != '')) &&
							((trim(theForm.expertFirstNameNew.value) == '') || (trim(theForm.expertLastNameNew.value) == '')))
						{
							errMsg += 'Please enter at least a First and Last Name for the Expert.\r\n';
						}
						*/

						if (((trim(theForm.judgeFirstNameNew.value) != '') ||
							(trim(theForm.judgeMINew.value) != '') ||
							(trim(theForm.judgeLastNameNew.value) != '')) &&
							((trim(theForm.judgeFirstNameNew.value) == '') || (trim(theForm.judgeLastNameNew.value) == '')))
						{
							errMsg += 'Please enter at least a First and Last Name for the Judge.\r\n';
						}

						if (trim(errMsg).length > 0)	
						{
							alert(errMsg);
							return false;
						}

						return true;
					}
					$(document).ready(function(){
						mca_setupDatePickerField('date');
					});						
					//-->
					</SCRIPT>
					<script language='javascript' type='text/javascript' src='/assets/common/javascript/common.js'></script>					
				</cfsavecontent>
				<cfhtmlhead text="#JS#">
					<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
						<form action="#local.resultsLink#" method="post" name="backForm">
						<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
							<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
						</CFLOOP>
						</form>
					</cfif>				
					<cfform name="verdictForm"  id="verdictForm" action="#local.saveLink#" method="post" onsubmit="return chkVerdictForm();">
						<cfinput type="hidden" name="verdictid"  id="verdictid" value="#val(local.qryVerdict.verdictid)#">
						<div>
							<input type="submit" value="Save Verdict" name="btnSave" class="BodyText" /> &nbsp;
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) AND val(local.qryVerdict.verdictID) gt 0>
								<input type="button" name="btnDelete" value="Delete Verdict" class="BodyText" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&verdictID=#val(local.qryVerdict.verdictID)#';"/> &nbsp;
							</cfif>
							<input type="button" value="Cancel" onclick="self.location.href='#local.baseLink#';" class="BodyText" />
						</div>
						<br/>
						<table border="0" class="BodyText"  cellpadding="2" cellspacing="0">
							<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
								<tr valign="top">
									<td nowrap><strong>Approved Status:</strong></td>
									<td>
										<cfinput required="yes" message="Select an approval status." type="radio" value="1" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 1#"> Approved - available for viewing<br/>
										<cfinput type="radio" value="0" name="isApproved"  id="isApproved" checked="#val(local.qryVerdict.isApproved) is 0#"> Not Approved - not available for viewing<br/>
									</td>
								</tr>
							<cfelse>
								<cfinput type="hidden" name="isApproved"  id="isApproved" value="#val(local.qryVerdict.isApproved)#">
							</cfif>
							<tr>
								<td valign="top"><strong>Attorney:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="PlaintiffAttorney"  id="PlaintiffAttorney" maxlength="200" size="70" value="#local.qryVerdict.PlaintiffAttorney#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Firm:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffFirm"  id="plaintiffFirm" maxlength="200" size="70" value="#local.qryVerdict.plaintiffFirm#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Address:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffAddress"  id="plaintiffAddress" maxlength="200" size="70" value="#local.qryVerdict.plaintiffAddress#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>City:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffCity"  id="plaintiffCity" maxlength="200" size="70" value="#local.qryVerdict.plaintiffCity#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>State:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffState"  id="plaintiffState" maxlength="200" size="70" value="#local.qryVerdict.plaintiffState#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Zip:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffZip"  id="plaintiffZip" maxlength="200" size="70" value="#local.qryVerdict.plaintiffZip#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Phone:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffPhone"  id="plaintiffPhone" maxlength="200" size="70" value="#local.qryVerdict.plaintiffPhone#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Fax:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffFax"  id="plaintiffFax" maxlength="200" size="70" value="#local.qryVerdict.plaintiffFax#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Email:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="plaintiffEmail"  id="plaintiffEmail" maxlength="200" size="70" value="#local.qryVerdict.plaintiffEmail#"></td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Category:</strong></td>
								<td>
									<select name="categoryID" class="BodyText" onchange="changeAvailability('categoryNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qryCategories">
										<option value="#local.qryCategories.categoryID#" <cfif local.qryVerdict.categoryID eq local.qryCategories.categoryID>selected</cfif>>#local.qryCategories.categoryDescription#</option>
									</cfloop>
									</select>
									<cfinput class="BodyText" type="text" name="categoryNew" id="categoryNew" maxlength="50" size="30">
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Docket Number:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="docketnumber"  id="docketnumber" maxlength="30" size="30" value="#local.qryVerdict.docketnumber#"></td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>County:</strong></td>
								<td>
									<select name="countyID" class="BodyText">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qrycountynames">
										<option value="#local.qrycountynames.countyID#" <cfif trim(local.qryVerdict.countyID) eq local.qrycountynames.countyID>selected</cfif>>#trim(local.qrycountynames.countyname)#</option>
									</cfloop>
									</select>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Resolution:</strong></td>
								<td>
									<select name="resolutiontypeid" class="BodyText" onchange="changeAvailability('resolutiontypeNew',this.value.length);">
									<option value="">Add new entry or select from list</option>
									<cfloop query="local.qryresolutiontypes">
										<option value="#local.qryresolutiontypes.resolutionTypeID#" <cfif local.qryVerdict.resolutionTypeID eq local.qryresolutiontypes.resolutionTypeID>selected</cfif>>#local.qryresolutiontypes.resolutionType#</option>
									</cfloop>
									</select>
									<cfinput class="BodyText" type="text" name="resolutiontypeNew" id="resolutiontypeNew" maxlength="15" size="20">
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Resolution Date:</strong></td>
								<td>
									<cfinput type="text" name="date" id="date" value="#dateFormat(local.qryVerdict.date,'mm/dd/yyyy')#">
									<a href="javascript:mca_clearDateRangeField('date');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
								</td>
							</tr>
							<tr>
								<td valign="top"><strong>Judge: <span class="small">[<a class="r" id="judgeLink" href="javascript:toggleJudge();">add new</a>]</span></strong></td>
								<td valign="top">
									<input type="hidden" name="newJudge" id="newJudge" value="0">
									<span id="objJudge">#local.judgeSelect#</span>
								</td>
							</tr>
								
							<tr valign="top">
								<td nowrap><strong>Name of Case:</strong></td>
								<td><cfinput class="BodyText" type="text" name="casetitle"  id="casetitle" maxlength="500" size="70" value="#local.qryVerdict.casetitle#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Facts of Case:</strong></td>
								<td valign="top"><textarea class="BodyText" name="facts" cols="70" rows="5">#local.qryVerdict.facts#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Injuries(Diagnosis, Prognosis):</strong></td>
								<td valign="top"><textarea class="BodyText" name="injuries" cols="70" rows="6">#local.qryVerdict.injuries#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Disabilities Resulting From Injuries:</strong></td>
								<td valign="top"><textarea class="BodyText" name="disabilityDueToInjuries" cols="70" rows="6">#local.qryVerdict.disabilityDueToInjuries#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Medical Expense:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="medicalExpense"  id="medicalExpense" maxlength="250" size="70" value="#local.qryVerdict.medicalExpense#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Lost Wages:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="lostwages"  id="lostwages" maxlength="250" size="70" value="#local.qryVerdict.lostWages#"></td>
							</tr>
							
							<tr>
								<td valign="top"><strong>Final Demand:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="finalDemand"  id="finalDemand" maxlength="250" size="70" value="#local.qryVerdict.finalDemand#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Final Offer:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="finalOffer"  id="finalOffer" maxlength="250" size="70" value="#local.qryVerdict.finalOffer#"></td>
							</tr>
							<tr>
								<td valign="top"><strong>Amount Awarded:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="amount"  id="amount" maxlength="250" size="70" value="#local.qryVerdict.amount#"></td>
							</tr>
							
							<tr>
								<td valign="top"><strong>Making a Difference:<br/>(How has this changed client's life?)</strong></td>
								<td valign="top"><textarea class="BodyText" name="makingDifference" cols="70" rows="3">#local.qryVerdict.makingDifference#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Special Comments:</strong></td>
								<td valign="top"><textarea class="BodyText" name="comments" cols="70" rows="3">#local.qryVerdict.comments#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Expert Witness for Plaintiff:</strong></td>
								<td valign="top"><textarea class="BodyText" name="plaintifExpertWitness" cols="70" rows="3">#local.qryVerdict.plaintifExpertWitness#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Expert Witness for Defense:</strong></td>
								<td valign="top"><textarea class="BodyText" name="defenseExpertWitness" cols="70" rows="3">#local.qryVerdict.defenseExpertWitness#</textarea></td>
							</tr>
							<tr>
								<td valign="top"><strong>Defense Attorney:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="DefenseAttorney"  id="DefenseAttorney" maxlength="300" size="70" value="#local.qryVerdict.DefenseAttorney#"></td>
							</tr>
							<tr>
								<td nowrap><strong>Submitting Attorney:</strong></td>
								<td valign="top"><cfinput class="BodyText" type="text" name="SubmittingAttorney"  id="SubmittingAttorney" maxlength="200" size="70" value="#local.qryVerdict.SubmittingAttorney#"></td>
							</tr>
						</table>
					</cfform>
					<script language="javascript">
						changeAvailability('categoryNew',document.forms["verdictForm"].categoryID.value.length);
						changeAvailability('resolutiontypeNew',document.forms["verdictForm"].resolutiontypeid.value.length);
					</script>
					
				</cfoutput>
			</cfif>
		</cfcase>
	
		<cfcase value="delete">
			<cfset verdictID = int(val(event.getValue('verdictID',0)))>
			<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
				<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
					delete from KY_VS_Verdicts
					where verdictID = <cfqueryparam value="#verdictID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
			</cfif>
			<cfoutput>
				<p class="TitleText">Information Updated</p>
				<br />
				<table border="0" cellpadding="2">
					<tr>
					<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
						<form action="#local.resultsLink#" method="post">
						<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
							<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
						</CFLOOP>
						<td><input type="submit" value="Return to Results" class="BodyText"/></td>
						</form>
					</cfif>
					<td><input type="button" onclick="document.location.href='#local.baseLink#';" value="New Search" class="BodyText" /></td>
					</tr>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="save">
			<cfset verdictID = int(val(event.getValue('verdictID',0)))>
			
			
			<cfif len(event.getValue('categoryNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckAction">
					set nocount on
					declare @categoryID int
					
					select @categoryID = categoryID 
					from dbo.KY_VS_Categories
					where categoryDescription = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('categoryNew'))#">
					
					if @categoryID is NULL
					begin
						INSERT INTO dbo.KY_VS_Categories(categoryDescription)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('categoryNew'))#">)
						select @categoryID = SCOPE_IDENTITY()
					end
				
					select @categoryID as categoryID
					set nocount off
				</cfquery>
				<cfset categoryID = qryCheckAction.categoryID>
			<cfelse>	
				<cfset categoryID = event.getValue('categoryID')>
			</cfif>
			
			<cfif len(event.getValue('resolutiontypeNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckAction">
					set nocount on
					declare @resolutionTypeID int
					
					select @resolutionTypeID = resolutionTypeID 
					from dbo.KY_VS_ResolutionTypes
					where resolutionType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('resolutiontypeNew'))#">
					
					if @resolutionTypeID is NULL
					begin
						INSERT INTO dbo.KY_VS_ResolutionTypes(resolutionType)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('resolutiontypeNew'))#">)
						select @resolutionTypeID = SCOPE_IDENTITY()
					end
				
					select @resolutionTypeID as resolutionTypeID
					set nocount off
				</cfquery>
				<cfset resolutionTypeID = qryCheckAction.resolutionTypeID>
			<cfelse>	
				<cfset resolutionTypeID = event.getValue('resolutionTypeID')>
			</cfif>
			
			<cfif len(event.getValue('judgeFirstNameNew','')) AND len(event.getValue('judgeLastNameNew',''))>
				<cfquery datasource="#application.dsn.customApps.dsn#" name="qryCheckJudge">
					set nocount on
					declare @judgeID int
					
					select @judgeID = judgeID 
					from dbo.KY_VS_Judges
					where FirstName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeFirstNameNew'))#">
					AND LastName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeLastNameNew'))#">
					<cfif len(trim(event.getValue('judgeMINew','')))>
						AND ((MiddleInitial is null) OR (MiddleInitial = ''))
					<cfelse>
						AND MiddleInitial = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeMINew'))#">
					</cfif>
					
					if @judgeID is NULL
					begin
						INSERT INTO dbo.KY_VS_Judges(FirstName, MiddleInitial, LastName)
						values(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeFirstNameNew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeMINew'))#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(event.getValue('judgeLastNameNew'))#">)
						select @judgeID = SCOPE_IDENTITY()
					end
				
					select @judgeID as judgeID
					set nocount off
				</cfquery>
				<cfset judgeID = qryCheckJudge.judgeID>
			<cfelse>	
				<cfset judgeID = event.getValue('judgeID')>
			</cfif>
									
			<!--- INSERT RECORD --->
			<cfif variables.verdictID is 0>
				<cfquery name="insertVerdict" datasource="#application.dsn.customApps.dsn#">
					set nocount on
					insert into dbo.KY_VS_Verdicts (resolutiontypeid, date, amount, categoryID, casetitle,
						countyID, judgeID, docketnumber, facts, injuries, disabilityDueToInjuries, makingDifference, comments,  medicalExpense, lostWages,
						finalDemand, finalOffer, defenseExpertWitness, plaintifExpertWitness, DefenseAttorney, SubmittingAttorney, 
						plaintiffAttorney, plaintiffFirm, plaintiffAddress, plaintiffCity, plaintiffState, plaintiffZip, plaintiffPhone, plaintiffFax, plaintiffEmail,
						DepoMemberDataID, dateLastModified, isApproved)
					VALUES (
						<cfqueryparam value="#trim(event.getValue('resolutiontypeid'))#" cfsqltype="CF_SQL_INTEGER">,
						<cfif isDate(event.getValue('date'))>
							<cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
						</cfif>
						<cfqueryparam value="#trim(event.getValue('amount'))#" cfsqltype="CF_SQL_VARCHAR">,
<!--- 
						
						<cfif len(event.getValue('amount'))>
							<cfqueryparam value="#RereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
						<cfelse>
							<cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
						</cfif>
 --->
						<cfqueryparam value="#categoryID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif len(event.getValue('countyID'))>
							<cfqueryparam value="#trim(event.getValue('countyID'))#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							<cfqueryparam value="" cfsqltype="CF_SQL_INTEGER" null="Yes">,
						</cfif>
						<cfqueryparam value="#judgeID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('injuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('disabilityDueToInjuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('makingDifference'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('comments'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('medicalExpense'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('lostWages'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('finalDemand'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('finalOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('defenseExpertWitness'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintifExpertWitness'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffFirm'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffAddress'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffCity'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffState'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffZip'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffPhone'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffFax'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plaintiffEmail'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#session.cfcuser.memberdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
						getdate(),
						<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
							<cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
						<cfelse>
							<cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
						</cfif>
					)
					select SCOPE_IDENTITY() as verdictid
					set nocount off
				</cfquery>
				<cfset thisVerdictID = insertVerdict.verdictid>

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>A verdict has been added to the Verdict Database.</p>
						<p>VerdictID: #thisverdictID#</p>
						<p><a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editLink#&verdictID=#thisverdictid#">Click here</a> to review the verdict and approve it for display.</p>
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email="<EMAIL>" },
					emailto=[{ name="", email="<EMAIL>" }],
					emailreplyto="<EMAIL>",
					emailsubject="KJA Verdict and Settlement Database Updated",
					emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - Verdict and Settlement Database",
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
					memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)/>

			<cfelse>
				<!--- UPDATE RECORD --->
				<cfquery name="updateVerdict" datasource="#application.dsn.customApps.dsn#">
					update dbo.KY_VS_Verdicts
					set 
						resolutiontypeid = <cfqueryparam value="#resolutionTypeID#" cfsqltype="CF_SQL_INTEGER">,
						<cfif isDate(event.getValue('date'))>
							date = <cfqueryparam value="#event.getValue('date')#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							date = <cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
						</cfif>
						amount = <cfqueryparam value="#trim(event.getValue('amount'))#" cfsqltype="CF_SQL_VARCHAR">,
<!--- 
						<cfif len(event.getValue('amount'))>
							amount = <cfqueryparam value="#rereplaceNoCase(event.getValue('amount'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DOUBLE">,
						<cfelse>
							amount = <cfqueryparam value="" cfsqltype="CF_SQL_DOUBLE" null="Yes">,
						</cfif>
 --->
						categoryID = <cfqueryparam value="#categoryID#" cfsqltype="CF_SQL_INTEGER">,
						casetitle = <cfqueryparam value="#trim(event.getValue('casetitle'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif len(event.getValue('countyID'))>
							countyID = <cfqueryparam value="#trim(event.getValue('countyID'))#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							countyID = <cfqueryparam value="" cfsqltype="CF_SQL_INTEGER" null="Yes">,
						</cfif>
						judgeID = <cfqueryparam value="#judgeID#" cfsqltype="CF_SQL_INTEGER">,
						docketnumber = <cfqueryparam value="#trim(event.getValue('docketnumber'))#" cfsqltype="CF_SQL_VARCHAR">,
						facts = <cfqueryparam value="#trim(event.getValue('facts'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						injuries = <cfqueryparam value="#trim(event.getValue('injuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						disabilityDueToInjuries = <cfqueryparam value="#trim(event.getValue('disabilityDueToInjuries'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						makingDifference = <cfqueryparam value="#trim(event.getValue('makingDifference'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						comments = <cfqueryparam value="#trim(event.getValue('comments'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						medicalExpense = <cfqueryparam value="#trim(event.getValue('medicalExpense'))#" cfsqltype="CF_SQL_VARCHAR">,
						lostWages = <cfqueryparam value="#trim(event.getValue('lostWages'))#" cfsqltype="CF_SQL_VARCHAR">,
						finalDemand = <cfqueryparam value="#trim(event.getValue('finalDemand'))#" cfsqltype="CF_SQL_VARCHAR">,
						finalOffer = <cfqueryparam value="#trim(event.getValue('finalOffer'))#" cfsqltype="CF_SQL_VARCHAR">,
						defenseExpertWitness = <cfqueryparam value="#trim(event.getValue('defenseExpertWitness'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						plaintifExpertWitness = <cfqueryparam value="#trim(event.getValue('plaintifExpertWitness'))#" cfsqltype="CF_SQL_LONGVARCHAR">,
						DefenseAttorney = <cfqueryparam value="#trim(event.getValue('DefenseAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						SubmittingAttorney = <cfqueryparam value="#trim(event.getValue('SubmittingAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffAttorney = <cfqueryparam value="#trim(event.getValue('plaintiffAttorney'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffFirm = <cfqueryparam value="#trim(event.getValue('plaintiffFirm'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffAddress = <cfqueryparam value="#trim(event.getValue('plaintiffAddress'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffCity = <cfqueryparam value="#trim(event.getValue('plaintiffCity'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffState = <cfqueryparam value="#trim(event.getValue('plaintiffState'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffZip = <cfqueryparam value="#trim(event.getValue('plaintiffZip'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffPhone = <cfqueryparam value="#trim(event.getValue('plaintiffPhone'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffFax = <cfqueryparam value="#trim(event.getValue('plaintiffFax'))#" cfsqltype="CF_SQL_VARCHAR">,
						plaintiffEmail = <cfqueryparam value="#trim(event.getValue('plaintiffEmail'))#" cfsqltype="CF_SQL_VARCHAR">,
						dateLastModified = getdate(),
						isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="cf_sql_BIT">
					where 
						verdictid = <cfqueryparam value="#event.getValue('verdictid')#" cfsqltype="cf_sql_integer">
				</cfquery>
				<cfset thisverdictid = event.getValue('verdictid')>
			</cfif>
			<cfoutput>
				<p class="TitleText">Information Saved. 
				<cfif NOT val(event.getValue('customPage.myRights.customAddDatabase',0))>Once approved, the information you entered will be searchable in the database.</cfif>
				</p>
				<br />
				<table border="0" cellpadding="2">
					<tr>
						<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
							<form action="#local.resultsLink#" method="post">
							<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
								<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
							</CFLOOP>
							<td><input type="submit" value="Return to Results" class="BodyText"/></td>
							</form>
						</cfif>
						<td><input type="button" onclick="parent.location='#local.baseLink#';" value="New Search" class="BodyText" /></td>
					</tr>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="results">
			<cfset pageID = int(val(event.getValue('page',0)))>
			<cfset maxrows = 10>
			<cfquery name="local.qryMatches" datasource="#application.dsn.customApps.dsn#">
				select *
				from KY_VS_Verdicts v
				left outer join KY_VS_Judges j on j.judgeID = v.judgeID
				left outer join KY_VS_Categories cat on cat.categoryID = v.categoryID		
				left outer join KY_VS_ResolutionTypes r on r.resolutionTypeID = v.resolutionTypeID		
				left outer join KY_VS_Counties c on c.countyID = v.countyID
				where 
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and len(event.getValue('isApproved'))>
					isApproved = <cfqueryparam value="#event.getValue('isApproved')#" cfsqltype="CF_SQL_BIT">
				<cfelseif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					1=1
				<cfelse>
					isApproved = 1
				</cfif>
				<cfif len(event.getValue('resolutiontypes',''))>
					and resolutiontype = <cfqueryparam value="#event.getValue('resolutiontypes')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('categoryID',''))>
					and v.categoryID = <cfqueryparam value="#event.getValue('categoryID')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif len(event.getValue('countyname',''))>
					and c.countyname = <cfqueryparam value="#event.getValue('countyname')#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('distinctyears',''))>
					and year(date) = <cfqueryparam value="#event.getValue('distinctyears')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif len(event.getValue('keywords',''))>
				  <cfset local.keywords  = Replace(trim(event.getValue('keywords')), " "," and ","ALL") />
					and 
						contains (v.*, <cfqueryparam value="#local.keywords#" cfsqltype="CF_SQL_VARCHAR">)
				</cfif>
				order by verdictID
			</cfquery>
	
			<cfset session.lastVerdictSearch = duplicate(form)>
			<cfif local.qryMatches.recordcount>
				<cfset numpages = ceiling(local.qryMatches.recordcount / variables.maxrows)>
				<cfset startrow = ((variables.pageID-1) * variables.maxrows) + 1>
				<cfset endrow = variables.startrow + variables.maxrows - 1>
				<cfif local.qryMatches.recordcount lt variables.endrow>
					<cfset endrow = local.qryMatches.recordcount>
				</cfif>
			<cfelse>
				<cfset numpages = 0>
				<cfset startrow = 0>
				<cfset endrow = 0>
			</cfif>
			<cfoutput>
				<div class="TitleText">Verdicts and Settlements Search Results</div>
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					<div class="BodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
				</cfif>
				<script language="JavaScript">
					function prevPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')-1#';
						objForm.submit();
					}
					function nextPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')+1#';
						objForm.submit();
					}
				</script>
				<br/>
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td class="BodyText">Showing #startrow# to #endrow# of #local.qryMatches.recordcount# matches</td>
						<td align="right">
							<cfif form.page gt 1>
								<input type="button" value="&lt;&lt; Previous Page" class="BodyText" onclick="prevPage();">
							</cfif>
							<cfif local.qryMatches.recordcount gt (form.page*maxrows)>
								<input type="button" value="Next Page &gt;&gt;" class="BodyText" onclick="nextPage();">
							</cfif>
							<input type="button" onclick="self.location.href='#local.baseLink#';" value="New Search" class="BodyText">
							<!--- ADD BUTTON: --->
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Verdict" class="BodyText">
							</cfif>
							
						</td>
					</tr>
				</table>
				<br/>
			</cfoutput>
			<cfif local.qryMatches.recordcount eq 0>
				<cfoutput><div class="BodyText">No records match your search criteria.</div></cfoutput>
			<cfelse>
				<cfoutput>
				<table border="0" class="BodyText" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
					<tr bgcolor="##999999">
						<td colspan="2"></td>
						<th align="left">Category</th>
						<th align="left">Date</th>
						<th align="left">Judge / Case</th>
						<th align="left">Resolution</th>
					</tr>
				</cfoutput>
				
				<cfoutput query="local.qryMatches" startrow="#variables.startrow#" maxrows="#variables.maxrows#">
					<tr valign="top" <cfif local.qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#local.qryMatches.currentrow#.</td>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>>
							<a href="#local.viewLink#&verdictID=#local.qryMatches.verdictID#">View</a>
							
							<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) OR (val(event.getValue('customPage.myRights.customAddDatabase',0)) AND local.qryMatches.depoMemberDataID EQ session.cfcuser.memberdata.depoMemberDataID)>
								<br/><a href="#local.editLink#&verdictID=#local.qryMatches.verdictID#">Edit</a>
							</cfif>
						</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.categoryDescription#&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;"><cfif isdate(local.qryMatches.date)>#dateformat(local.qryMatches.date,"m/d/yyyy")#</cfif>&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">
							<cfif len(local.qryMatches.lastname)>#local.qryMatches.lastname#, #local.qryMatches.firstname#<cfif local.qryMatches.middleinitial NEQ ""> #local.qryMatches.middleinitial#</cfif></cfif>
							<cfif len(local.qryMatches.lastname) and len(local.qryMatches.casetitle)><br/></cfif>
							#left(local.qryMatches.casetitle,100)#<cfif len(local.qryMatches.casetitle) gt 100>...</cfif>
							&nbsp;
						</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and NOT local.qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-right:2px solid ##FF0000;<cfelse>border-right:1px solid ##666;</cfif>">#local.qryMatches.resolutiontype#&nbsp;</td>
					</tr>
				</cfoutput>
				<cfoutput></table></cfoutput>
			</cfif>
			<cfoutput>	
				<form name="frmHidden" action="#local.resultsLink#" method="post">
				<input type="hidden" name="page" value="">
				<cfloop INDEX="form_element" LIST="#FORM.fieldnames#">
					<cfif form_element neq "page">
						<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
					</cfif>
				</CFLOOP>
				</form>
			</cfoutput>
		</cfcase>
	
		<cfcase value="search">
			<cfset local.qryCategories = getCategories(forSearch=true)>
			<cfset local.qryResolutionTypes = getResolutionTypes(forSearch=true)>
			<cfset local.qryCountyNames = getCountyNames(forSearch=true)>
			<cfset local.qryYears = getYears()>
			<cfoutput>
				<p><span class="TitleText">Search KJA Verdict and Settlement  Database</span></p>
				<p><span class="BodyText">KJA's Verdict and Settlement Database contains reports 
				submitted by members for publication in Verdict. The database records include 
				information regarding a verdict or a settlement as provided by KJA members. 
				The Verdict and Settlement Database is a valuable service to our members and 
				we welcome all reports.</span></p>
			
				<cfform action="#local.resultsLink#" method="post">
					<input type="hidden" name="page" value="1" />
					<table class="BodyText">
					<tr>
						<td>Resolution Type:</td>
						<td>
							<select name="resolutiontypes" class="BodyText">
							<option value="">All</option>
							<cfloop query="local.qryResolutionTypes">
								<option value="#local.qryResolutionTypes.resolutiontype#">#local.qryResolutionTypes.resolutiontype#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Case Category:</td>
						<td>
							<select name="categoryID" class="BodyText">
							<option value="">All</option>
							<cfloop query="local.qryCategories">
								<option value="#local.qryCategories.categoryID#">#local.qryCategories.categoryDescription#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Verdict Year:</td>
						<td>
							<select name="distinctyears" class="BodyText">
							<option value="">All</option>
							<cfloop query="local.qryYears">
								<option value="#local.qryYears.year#">#local.qryYears.year#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>County:</td>
						<td>
							<select name="countyname" class="BodyText">
							<option value="">All</option>
							<cfloop query="local.qryCountyNames">
								<option value="#local.qryCountyNames.countyname#">#local.qryCountyNames.countyname#</option>
							</cfloop>
							</select>
						</td>
					</tr>
					<tr>
						<td>Keywords (optional):</td>
						<td><input type="text" name="keywords" class="BodyText" maxlength="70" size="70" /></td>
					</tr>
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						<tr>
							<td>Approval Status:</td>
							<td>
								<select name="isApproved" class="BodyText">
								<option value="">All</option>
								<option value="1">Approved Verdicts Only</option>
								<option value="0">Non-Approved Verdicts Only</option>
								</select>
							</td>
						</tr>
					</cfif>
					</table>
					<br />
					<input type="submit" value="Search Reports" class="BodyText"/>
					<!--- ADD BUTTON: --->
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.editLink#';" value="Add Verdict" class="BodyText" />
					</cfif>
					
				</cfform>
			</cfoutput>
		</cfcase>
	
		<!--- search --->
		<cfdefaultcase></cfdefaultcase>
	
	</cfswitch>



<cffunction name="getCategories" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">
	
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>c.*
		FROM KY_VS_Categories c
		<cfif arguments.forSearch>
			INNER JOIN dbo.KY_VS_Verdicts vsv on vsv.categoryID = c.categoryID
		</cfif>
		ORDER BY categoryDescription
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getResolutionTypes" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">
	
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>vsrt.resolutionTypeID, vsrt.resolutionType
		FROM dbo.KY_VS_ResolutionTypes vsrt
		<cfif arguments.forSearch>
			INNER JOIN dbo.KY_VS_Verdicts vsv on vsv.resolutionTypeID = vsrt.resolutionTypeID
		</cfif>
		ORDER BY resolutionType		
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getCountyNames" returntype="query" output="No">
	<cfargument name="forSearch" type="boolean" required="false" default="false">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT <cfif arguments.forSearch>DISTINCT </cfif>c.*
		FROM KY_VS_Counties c
		<cfif arguments.forSearch>
			INNER JOIN dbo.KY_VS_Verdicts vsv on vsv.countyID = c.countyID
		</cfif>
		ORDER BY countyname
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getCourtNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT courtID, courtname
		FROM KY_VS_Courts
		ORDER BY courtname
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getJudgeNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT judgeID, lastname, firstname, middleinitial
		FROM KY_VS_Judges
		ORDER BY lastname
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getYears" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT YEAR(DATE) as year
		FROM KY_VS_Verdicts
		WHERE DATE IS NOT NULL AND DATE <> ''
		ORDER BY YEAR(DATE) DESC
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getVerdict" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select * 
		from KY_VS_Verdicts v
		left outer join KY_VS_Counties cty on cty.countyID = v.countyID		
		left outer join KY_VS_ResolutionTypes r on r.resolutionTypeID = v.resolutionTypeID		
		left outer join KY_VS_Categories cat on cat.categoryID = v.categoryID		
		left outer join KY_VS_Judges j on j.judgeID = v.judgeID
		where v.verdictid = <cfqueryparam value="#arguments.verdictID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfreturn qry>
</cffunction>


<cffunction name="buildJudgeForVerdict" returntype="string" output="No">
	<cfargument name="judgeID" type="string" required="yes">
	<cfset var local 				= structNew()>
	<cfset local.qryJudgeNames = getJudgeNames() />
	<cfset local.newObj 		= 0 />
	<cfif structCount(arguments) GT 1>
		<cfset local.newObj = arguments[2] />
	</cfif>
	<cfsavecontent variable="local.data">
		<cfoutput>			
			<cfif val(local.newObj)>				
				<input type="hidden" name="judgeID" value="0">
				<table class="tsAppBodyText">
					<tr>
						<td>First Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="judgeFirstNameNew" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Middle Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="judgeMINew" maxlength="50" size="30" value=""></td>
					</tr>
					<tr>
						<td>Last Name</td>
						<td></td>
						<td><input class="tsAppBodyText" type="text" name="judgeLastNameNew" maxlength="50" size="30" value=""></td>
					</tr>
				</table>
			<cfelse>
				<select name="judgeID" class="BodyText">
				<option value="0">--- Select Judge </option>					
				<cfloop query="local.qryJudgeNames">
					<option value="#local.qryJudgeNames.judgeID#" <cfif arguments.judgeID eq local.qryJudgeNames.judgeID>selected</cfif>>#local.qryJudgeNames.lastname#, #local.qryJudgeNames.firstname#<cfif local.qryJudgeNames.middleinitial NEQ ""> #local.qryJudgeNames.middleinitial#</cfif></option>
				</cfloop>
				</select>
				<input type="hidden" name="judgeFirstNameNew" value="">
				<input type="hidden" name="judgeMINew" value="">
				<input type="hidden" name="judgeLastNameNew" value="">
			</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfreturn local.data />
</cffunction>
