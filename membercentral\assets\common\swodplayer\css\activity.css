*:not(.selectable) {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
.selectable, .jqte_editor.selectable div,.jqte_editor.selectable ul,.jqte_editor.selectable li,.jqte_editor.selectable ol,
.jqte_editor.selectable b,.jqte_editor.selectable i,.jqte_editor.selectable span,.jqte_editor.selectable br,.jqte_editor.selectable u{
    -webkit-user-select: auto !important;
    -moz-user-select: auto !important;
    -ms-user-select: auto !important;
    -o-user-select: auto !important;
    user-select: auto !important;
}
* {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
html, body {	
	-webkit-transform: translate3d(0, 0, 0) !important;
	-webkit-backface-visibility: hidden !important;
	-webkit-perspective: none !important;
	height: 100%;
}
body {
	margin: 0;
	-webkit-transform: translate3d(0, 0, 0) !important;
	-webkit-backface-visibility: hidden !important;
	-webkit-perspective: none !important;
}
@font-face {
    font-family: bold;
    src: url(../assets/fonts/OpenSans-Bold_4.ttf), url(../assets/fonts/OpenSans-Bold_4.otf), url(../assets/fonts/OpenSans-Bold_4.eot);
}
@font-face {
    font-family: bold-italic;
    src: url(../assets/fonts/OpenSans-BoldItalic_3.ttf), url(../assets/fonts/OpenSans-BoldItalic_3.otf), url(../assets/fonts/OpenSans-BoldItalic_3.eot);
}
@font-face {
    font-family: bold-extra;
    src: url(../assets/fonts/OpenSans-ExtraBoldItalic_2.ttf), url(../assets/fonts/OpenSans-ExtraBoldItalic_2.otf), url(../assets/fonts/OpenSans-ExtraBoldItalic_2.eot);
}
@font-face {
    font-family: italic;
    src: url(../assets/fonts/OpenSans-Italic_3.ttf), url(../assets/fonts/OpenSans-Italic_3.otf), url(../assets/fonts/OpenSans-Italic_3.otf);
}
@font-face {
    font-family: light;
    src: url(../assets/fonts/OpenSans-Light_3.ttf), url(../assets/fonts/OpenSans-Light_3.otf), url(../assets/fonts/OpenSans-Light_3.eot);
}
@font-face {
    font-family: light-italic;
    src: url(../assets/fonts/OpenSans-LightItalic_2.ttf), url(../assets/fonts/OpenSans-LightItalic_2.otf), url(../assets/fonts/OpenSans-LightItalic_2.eot);
}
@font-face {
    font-family: regular;
    src: url(../assets/fonts/OpenSans-Regular_3.ttf), url(../assets/fonts/OpenSans-Regular_3.otf), url(../assets/fonts/OpenSans-Regular_3.eot);
}
@font-face {
    font-family: bold-semi;
    src: url(../assets/fonts/OpenSans-Semibold_2.ttf), url(../assets/fonts/OpenSans-Semibold_2.otf), url(../assets/fonts/OpenSans-Semibold_2.eot);
}
@font-face {
    font-family: bold-semi-italic;
    src: url(../assets/fonts/OpenSans-SemiboldItalic_1.ttf), url(../assets/fonts/OpenSans-SemiboldItalic_1.otf), url(../assets/fonts/OpenSans-SemiboldItalic_1.eot);
}
.regular:not(.items) {
    font-family: regular;
}
.bold:not(.items) {
    font-family: bold;
}
.italic:not(.items) {
    font-family: italic;
}
.boldSemi:not(.items) {
    font-family: bold-semi;
}
.boldItalic:not(.items) {
    font-family: bold-italic;
}
.displayHide {
    display: none !important
}
.visibilityHide {
    visibility: hidden !important
}
.opacityShow {
    opacity: 1 !important
}
.opacityHide {
    opacity: 0 !important
}
textarea{border-radius:12px;}
#preload {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 99999;
}
#preload #spinner {
    position: relative;
    margin: 0 auto;
    top: 50%;
    display: table;
    width: 175px !important;
    height: 175px !important;
    background-image: url(../assets/images/preload_spinner.gif);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-top: -88px;
}
#percent {
    position: relative;
    width: 100%;
    text-align: center;
    vertical-align: middle;
    display: table-cell;
}
#wrapper_parent {
    position: relative;
    width: 100%;
    height: 100%;
}
#wrapper {
    position: relative;
    max-width: 1366px;
    height: 100%;
    margin: 0 auto;
    max-height:1024px;
}
#activity {
    position: relative;
    width: 100%;
    height: 100%;
}
.userSelectNone,
.lable {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
.tableCell {
    position: relative;
    display: table-cell;
    vertical-align: middle;
}
.height_zero {
    padding: 0 !important;
    overflow: hidden !important;
    height: 0 !important;
}
.arrowDown {
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid;
}
.arrowRight {
    border-top: 6px solid transparent !important;
    border-bottom: 6px solid transparent !important;
    border-left: 8px solid !important;
    border-right: 0px solid transparent;
    margin-top: 4px !important;
}
.scale200 {
    -webkit-transform-origin: 0px 0px;
    -moz-transform-origin: 0px 0px;
    -ms-transform-origin: 0px 0px;
    -o-transform-origin: 0px 0px;
    transform-origin: 0px 0px;
}
.scaleFitHeight {
    -webkit-transform-origin: 0px 0px;
    -moz-transform-origin: 0px 0px;
    -ms-transform-origin: 0px 0px;
    -o-transform-origin: 0px 0px;
    transform-origin: 0px 0px;
}
.scaleFitWidth {
    -webkit-transform-origin: 0px 0px;
    -moz-transform-origin: 0px 0px;
    -ms-transform-origin: 0px 0px;
    -o-transform-origin: 0px 0px;
    transform-origin: 0px 0px;
} 
.boxInset {
    box-shadow: inset 0px 0px 0px 1px grey;
}
.border {
    border: 1px solid lightgrey;
}
.border2 {
    border: 2px solid lightgrey;
}
.white {
    color: #FFFFFF;
}
.buttonCss {
    min-width: 60px;
    min-height: 28px;
    background-color: rgb(28, 141, 221);
    color: #FFFFFF;
    padding: 0px 1%;
    font-size: 14px;
}
.buttonCss .title {
    height: 100%;
}
.videoCenter {
    height: 282% !important;
    z-index: 999 !important;
    width: 368% !important;
    z-index: 992;
}
.fontItalic {
    font-family: italic !important;
}
.button {
    position: relative;
    height: 28px;
    text-align: center;
    display: table;
    border-radius: 7px;
}
.button.disable {
    background-color: #46607B !important;
}
.button:not(.select) {
    cursor: pointer;
}
.button:not(.select):hover {
}
.button:not(.select):active {
    cursor: pointer !important;
}
.button.select {
    background-color: #253545;
    cursor: default;
}
.buttonColors.select{background-color: #253545!important;}
.button .title {
    height: 100%;
    font-size: 14px;
}
.button .lable {
    font-size: 14px;
}
.button.disabled {
    background-color: #a3b0bd; !important;
	cursor: default;
}
.button.disabled:hover {
    background-color: #a3b0bd; !important;
	cursor: default;
}
/*
	################
	### Override ###
	################
*/
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    -o-appearance: none;
    appearance: none;
    margin: 0;
}
::-webkit-input-placeholder {
    text-align: center;
}
:-moz-placeholder {
    text-align: center;
}
:-ms-input-placeholder {
    text-align: center;
}
input[type="number"] {
    -moz-appearance: textfield !important;
    -moz-appearance: none;
}
.visible[ng\:cloak],
.visible[ng-cloak],
.visible[data-ng-cloak],
.visible[x-ng-cloak],
.visible.ng-cloak,
.visible.x-ng-cloak,
.visible.ng-hide:not(.ng-hide-animate) {
    visibility: hidden !important;
    display: block !important;
}
#right .screen.learn .ppts.displayHide {
    visibility: hidden !important;
    display: block !important;
}
#right #learn-controller #beginScreen .start.button.bold{
	margin-top: 0% !important;
}

#right .tabs,
#right .screen.learn *,
#right .screen.orsc *,
.seminarCompleteProcess *,#leftBox .screen.learn *,#leftBox .screen.orsc, #leftBox .screen .learn.cmplt  {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
.overflowHidde {
    overflow: hidden !important;
}
.tab.disabled{pointer-events: none;}
.tab.disabled .section{color:lightgray;}
/*
	###########
	### Top ###
	###########
*/
#activity > .patch {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(75, 75, 75, 0.92);
    z-index: 10000;
    font-family: regular;
    font-size: 16px;
    top:0px;
}
#activity > .patch > .content {
    width: 100%;
    height: 100%;
}
#overlay-box {
	/*background-color:transparent !important;*/
}
#background-color-patch{
    width: 100%;
    display: block;
    height: 100%;
    background-color: rgba(75, 75, 75, 0.92);
    position: absolute;
}
#activity .content-popup {
    position: absolute;
    left: 50%;
    top: 35%;
    background: #fff !important ;
    box-shadow: 0px 0px 0px 1px lightgrey;
    padding-top: 0px !important;
    border-radius: 12px;
}
#activity > .patch > .content .saveExit {
    position: absolute;
    left: 50%;
    top: 35%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px lightgrey;
    padding-top: 0px !important;
}
#activity > .patch > .content .saveExit.onlyShowClose{    
    width: 30% !important;
    transform: translateX(-50%);
    margin-left: unset !important;
}
#activity > .patch > .content .emailInput {
    position: absolute;
    left: 50%;
    top: 35%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px lightgrey;
    padding-top: 0px !important;
    text-align: center;
    padding: 10px;
    width: 430px !important;
    margin-left: -215px !important;
    border-radius: 12px;
    margin-top: -62px !important;
}
#activity > .patch > .content .emailOK, #activity > .patch > .content .emailCancel {
    float: left;
    position: relative;
    margin-left: 6px;
    width: 99px;
}
#activity > .patch > .content .disable {
    opacity: 0.5;
    cursor: default;
}
#activity > .patch > .content .emailSentView {
    position: absolute;
    left: 50%;
    top: 35%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px lightgrey;
    padding-top: 0px !important;
}
#activity > .patch > .content input {
    margin-left: 2px;
}
#activity > .patch > .content .errorText {
    margin-top: -10px;
}
#activity > .patch > .content .emailSentView .content .heading {
    text-align: center;
}
#activity > .patch > .content .emailSentView .content .button {
    width: 60px;
    margin: 0px auto;
}
#activity #alert-box #alert-button{
    width: 80px;
    height:25px;
    margin:0 auto;
}
#activity > .patch > .content #alert-box-container {
    position: absolute;
    left: 50%;
    top: 50%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px lightgrey;
    padding-top: 0px !important;
    padding: 10px;
    width: 430px;
    margin-left: -225px;
    margin-top: -62px;
    text-align: center;
}
#activity > .patch > .content .focusOutView {
    position: absolute;
    left: 50%;
    top: 35%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px lightgrey;
    padding-top: 0px !important;
    text-align: center;
    border-radius:12px;
}
#activity #overlay-box .focusOutView .heading,#activity #overlay-box .focusOutView .title{
    text-align: center;
}
#activity > .patch > .content .saveExit .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#activity > .patch > .content .saveExit .content > .heading {
    text-align: center;
}
#activity > .patch > .content .saveExit .content > .title {
    padding-top: 0px !important;
	width: 80%;
	position: relative;
    margin: 0px auto;
}
#activity > .patch > .content .saveExit .buttons {
    position: relative;
    display: table;
    margin: 0 auto;
}
#activity > .patch > .content .emailInput .buttons {
    position: relative;
    display: table;
    margin: 0 auto;
}
#activity > .patch > .content .saveExit .button {
    position: relative;
    float: left;
}
#activity > .patch > .content .saveExit .cancel {
    margin-left: 10px;
}
#activity > .patch > .content .guidedTour > .top {
    position: relative;
    width: 100%;
    height: 38px;
    background-color: white;
    border-bottom: 1px solid #888;
}
#activity > .patch > .content .guidedTour > .top .title {
    position: relative;
    display: table;
    height: 100%;
}
#activity > .patch > .content .guidedTour > .top .lable {
    padding-left: 10px;
}
#activity > .patch > .content .guidedTour > .top .close {
    position: absolute;
    height: 100%;
    top: 0px;
    right: 5px;
    width: 20px;
}
#activity > .patch > .content .guidedTour > .top .close .img {
    position: absolute;
    width: 18px;
    height: 20px;
    margin-top: 10px;
    background-image: url(../assets/images/close.png);
    background-repeat: no-repeat;
    cursor: pointer;
}
#activity > .patch > .content .guidedTour > .bottom {
    position: relative;
    background-color: white;
    padding:20px;
}
.saveExit,.emailSentView,.seminarCompleted,.creditExpired,.seminarPending,.seminarInstance,.examInProgress{border-radius:7px;}
/*
	###########
	### Top ###
	###########
*/
#top {
    position: relative;
    width: 100%;
}
#top .title {
    display: table;
    height: 100%;
	padding-top: 1px;
    font-size: 1.5em;
    text-align:left;
    margin-bottom:10px;
}
#top .saveExit {
    position: relative;
    color: #FFFFFF;
    text-align: center;
    cursor: pointer;
    min-height: 26px;
    font-size: 12px;
    background-color: rgb(28, 141, 221);
    padding: 4px;
    margin-right: 5px;
    white-space: nowrap;
}
/*
	##############
	### Bottom ###
	##############
*/
#bottom {
    position: relative;
    width: 100%;
    border: 1px solid grey;
    border-top: 0px solid transparent;
    box-sizing: border-box;
}
#bottom:not(.audioOnly) .panel {
    height: 100%;
}
/*
@media screen and (max-width: 767px){
    #bottom .panel{
        height:auto;
    }
}*/
#left {
    left: 0;
    display: flex;
    flex-flow: column;
}
#left .base {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#left .box {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
#left .box > .content {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#left .box > .patch {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 101%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 99;
    box-sizing: border-box;
}
#left .logo .img {
    position: absolute;
    width: 180px;
    height: 62px;
    left: 50%;
    top: 50%;
    margin-left: -90px;
    margin-top: -31px;
    background-image: url(../assets/images/left_logo.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
#left .logo .title {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    font-family: arial;
}
#left .logo .tsae {
    font-size: 20px;
}
/*
		#############
		### Video ###
		#############
*/
.video > .content {
    margin: 0 auto;
}
#left #video-controller{margin-bottom:10px;z-index: 9;}
#video-controller[type=audio] > div{padding-left:0;padding-right:0;}
@media screen and (max-width:820px){
    #video-controller[type=audio] > div{padding-right:10px;}
}
#video {
    width: 100%;
    background-color: black;
    box-sizing: border-box;
    display: table-row;

    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    top: 0;
}
#video .buffer {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 9;
}
#video .buffer.displayHide {
    visibility: hidden !important;
    display: block !important;
}
#video .buffer .img{
    position: absolute;
    width: 50px;
    height: 50px;
    left: 50%;
    top: 50%;
    margin-left: -25px;
    margin-top: -25px;
    background-image: url(../assets/images/loading.gif);
    background-size: 100% 100%;
}
*::-webkit-media-controls-start-playback-button {
    display: none!important;
    -webkit-appearance: none;
}
.video .playpause {
    background-image: url(../assets/images/playPause.png);    
    background-repeat:no-repeat;
    width:50%;
    height:50%;
    position:absolute;
    left:0%;
    right:0%;
    top:0%;
    bottom:0%;
    margin:auto;
    background-size:contain;
    background-position: center;
    z-index: 10;
}
.video .playMedia {
	padding-top:15px;
	font-size:15px;
	font-weight:bold;
	color:#ffffff;
    background-color:rgba(90,90,90,0.9);
    width:100%;
    height:15%;
    position:absolute;
    top:0 !important;
    margin:auto;
    z-index: 12;
	left: 0%;
    right: 0%;
}
.video .clkToLearnMsg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    background-color: #000;
    opacity: 1;
	font-size:15px;
    z-index: 999;
}
.video .clkToLearnMsg span {
    padding: 5px;
    border-radius: 5px;
    color: #FFFFFF;
    position:relative;
    top:48%;
}
video {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #000;
}
#afterVideo {
    display: none;
}
.video .controls {
    position: absolute;
    width: 100%;
    bottom: 0;
    z-index: 99;
}
.sizeLarge.ipad .video .controls {
    box-sizing: content-box;
    padding-top: 3px;
    padding-bottom: 4px;    
}
#activity:not(.ipad) .video[type=video] .controls {
    /*opacity: 0;*/
}
#activity:not(.ipad) .video[type=video] .controls:hover {
    opacity: 1;
}
.video[type=audio] .controls {
    top: 0 !important
}
.video .controls .top {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
.video .top .sliderUi {
    position: relative;
    top: 50%;
    margin: 0 auto;
    box-sizing: border-box;
    left: 14px;
    float: left;
}
.flex-video{padding-bottom:65.5%!important;}
@media screen and (min-width:48em){
    #video-controller .columns{padding-left: 0em; padding-right: 0em;}
}
@media screen and (max-width:767px){
    .video[type=audio] .top .sliderUi{
        margin-top: 7px!important;
    }
    .video[type=audio] .top .sliderUi.volume{
        margin-top: 20px!important;
    }
}
.video .top .slider {
    position: relative;
    height: 100%;
    background-image: none;
    border: 0;
    box-sizing: border-box;
}
.video .top .slider * {
    box-sizing: border-box;
}
.video .top .slider .fill {
    position: absolute;
    width: 100%;
    height: 100%;
}
.video .top .slider .ui-slider-handle {
    position: absolute;
    width: 10px !important;
    height: 10px !important;
    top: 50%;
    margin-top: -5px;
    border: 0;
    border-radius: 50%;
    background-image: none;
    background-color: #8B8B8B;
    margin-left: -4px;
    /*cursor: pointer;*/
}
.video .time {
    position: relative;
    height: 100%;
    color: #FFFFFF;
    font-size: 10px;
    display: table;
    margin-top: -4px;
    float: right;
    margin-right: 10px;
}
.video .controls .bottom {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
.video .playPause {
    position: relative;
    height: 100%;
    display: table;
    float: left;
    margin-left: 10px;
}
.video .playPrevNext {
    position: relative;
    width: 14px;
    height: 22px;
    float: left;
    background-repeat: no-repeat;
    background-position: 0px 50%;
    z-index: 9;
    cursor: pointer;
}
.video .playPrev {
    left: 0;
    background-image: url(../assets/images/play_prev.png);
}
.video .play {
    position: relative;
    width: 27px;
    height: 100%;
    float: left;
    z-index: 9;
    margin: 0px 5px;
    cursor: pointer;
}
.video .play .bg {
    position: absolute;
    width: 20px;
    height: 20px;
    left: 50%;
    top: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border-radius: 50%;
    background-color: rgb(5, 85, 174);
}
.video .play .bg:hover {
    background-color: #053468 !important;    
}
.video .play .image {
    position: absolute;
    width: 8px;
    height: 10px;
    left: 50%;
    top: 50%;
    background-image: url(../assets/images/play.png);
    background-position: -10px -8px;
    background-repeat: no-repeat;
    margin-left: -3px;
    margin-top: -5px;
}
.video .play.pause .image {
    background-image: url(../assets/images/pause.png);
    background-position: -11px -8px;
}
.video .play .image:hover {
    background-image: url(../assets/images/play_hover.png);
}
.video .play.pause .image:hover {
    background-image: url(../assets/images/pause_hover.png);
}
.video .playNext {
    right: 0;
    background-image: url(../assets/images/play_next.png);
}
.video .volumeZoom .videoZoomInOutParent {
    position: relative;
    float: right;
    height: 100%;
}
.video .volumeZoom {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.video .volumeZoom .volumeLable {
    position: absolute;
    font-size: 9px;
    color: white;
    /*bottom: 1px;*/
    margin-left: 12px;
    text-align: center;
    display: none;
}
.video .volumeZoom .sliderUiParent {
    position: relative;
    float: right;
    width: 70%;
    height: 100%;
    margin-right: 10px;
}
.video .bottom .sliderUi {
    position: relative;
    top: 50%;
    box-sizing: border-box;
    float: right;
}
.video .bottom .slider {
    position: relative;
    height: 100%;
    background-image: none;
    border: 0;
    box-sizing: border-box;
}
.video .bottom .slider *:not(.ui-slider-handle) {
    box-sizing: border-box;
}
.video .bottom .slider .fill {
    position: absolute;
    width: 100%;
    height: 100%;
}
.video .bottom .slider .ui-slider-handle {
    position: absolute;
    width: 10px !important;
    height: 10px !important;
    top: 50%;
    margin-top: -9px;
    border: 0;
    background-image: none;
    background-color: transparent;
    margin-left: -6px;
    border: 4px solid transparent;
    box-sizing: content-box;
    outline: none;
    cursor: pointer;
}
.video .mute {
    position: relative;
    float: right;
    top: -1px;
    background-repeat: no-repeat;
    background-position: 0px 50%;
    z-index: 9;
    cursor: pointer;
    width: 17px;
    height: 21px;
    background-image: url(../assets/images/mute.png);
}
.video .videoZoom {
    position: relative;
    float: right;
    width: 21px;
    height: 20px;
    background-repeat: no-repeat;
    background-position: 0px 50%;
    z-index: 9;
    cursor: pointer;
}
.video .videoZoomIn {
    margin-right: 8px;
}
.video .videoZoomIn {
    opacity: 0.5;
    cursor: default;
}
.video .videoZoomIn .bg, .video .videoZoomOut .bg {
    position: absolute;
    width: 18px;
    height: 18px;
    left: 50%;
    top: 50%;
    margin-left: -9px;
    margin-top: -9px;
    background-color: rgb(5, 85, 174);
}
.video .videoZoomIn .image {
    position: absolute;
    width: 10px;
    height: 10px;
    left: 50%;
    top: 50%;
    background-image: url(../assets/images/video_zoom_in.png);
    background-position: -4px -4px;
    background-repeat: no-repeat;
    margin-left: -5px;
    margin-top: -5px;
}
.video .videoZoomOut {
    opacity: 1;
    cursor: pointer;
}
.video .videoZoomOut .image {
    position: absolute;
    width: 12px;
    height: 4px;
    left: 50%;
    top: 50%;
    background-image: url(../assets/images/video_zoom_out.png);
    background-position: -4px -8px;
    background-repeat: no-repeat;
    margin-left: -6px;
    margin-top: -2px;
}
.video .mute.muted {
    background-image: url(../assets/images/muted.png);
}
.video .mute:hover {
    background-image: url(../assets/images/muted_hover.png);
}
.video .mute:hover {
    background-image: url(../assets/images/mute_hover.png);
}
.video .mute.muted:hover {
    background-image: url(../assets/images/muted_hover.png);
}
/*
		###########
		### Nav ###
		###########
*/
.nav > .content {
    border: 1px solid;
}
.nav .top {
    position: relative;
    width: 100%;
}
.nav .top .title {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
}
.nav .top .title .lm-popup-icon{
    position: absolute;
    top:5px;
    right:5px;
    width: 17px;
    height: 16px;
    background-color: rgb(70, 96, 123);
    background-image: url(../assets/images/pop_icon.png);
    cursor: pointer;
}
.nav .top .title .lm-popup-close{
    position: absolute;
    top:2px;
    right:2px;
    width: 21px;
    height: 20px;
    background-image: url(../assets/images/close.png);
    cursor: pointer;
}
.nav .bottom {
    position: relative;
    width: 100%;
}
.nav .bottom .chapters {
    position: relative;
    display: inline-block;
    width: 100%;
    cursor: pointer;
    box-sizing: border-box;
}
.nav .bottom .chapters .content {
    position: relative;
}
.nav .bottom .chapters .img.status {
    position: relative;
    width: 13px;
    height: 13px;
    display: inline-block;
    float: left;
    margin-top: 6px;
}
#lm-popup-panel .top{
    border-top-right-radius: 7px;
    border-top-left-radius: 7px;
}#lm-popup-panel .bottom{
    border-bottom-right-radius: 7px;
    border-bottom-left-radius: 7px;
}
@media screen and (max-width:767px){
    .bottom .chapters .content .lable div{
        font-size:13px;
    }
}
@media screen and (max-width:600px){
    .video .clkToLearnMsg span{top:35%;}
    #right .video .clkToLearnMsg span{top:48%;}
    #activity > .patch > .content .saveExit.onlyShowClose{    
        width: 40% !important;
        transform: translateX(-50%);
        margin-left: unset !important;
    }
}
@media screen and (max-width:500px){
    #activity > .patch > .content .saveExit.onlyShowClose{    
        width: 60% !important;
    }
}
/*#right{display:flex;}*/
#right #singleImgPreloader {
	width: 100px;
    height: 100px;
    margin: 0px auto;
    background-size: 100% 100%;
    background-image: url(../assets/images/loading.gif);
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.loadingScreen {
	width: 100%;
    height: 200px;
    background-size: 100px 100px;
    background-image: url(../assets/images/loading.gif);
	background-repeat: no-repeat;
    background-position: center;
}
#right #singleImgPreloaderParent {
    height: 100%;
    width: 100%;
}
.nav .bottom .chapters .img.icon {
    position: relative;
    width: 20px;
    height: 19px;
    background-image: url(../assets/images/folder.png);
    background-repeat: no-repeat;
    float: left;
    margin-left: 2px;
    margin-top: 2px;
	background-size: 100% 100%;
}
.nav .bottom .chapters .title {
    position: relative;
}
.nav .bottom .chapters .lable {
    position: relative;
}
.nav .bottom .topics {
    position: relative;
    height: 100px;
}
#lm-popup-panel .bottom .topics {
    min-height: 380px;
    overflow-y:auto; 
}
#lm-popup-panel .bottom .topics.height_zero {
    visibility: hidden;
}
.nav .bottom .topic {
    position: relative;
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
}
.nav .bottom .topic .content {
    position: relative;
    display: table;
}
.nav .bottom .videoPpt {
    position: relative;
    width: 100%;
    display: table;
    cursor: pointer;
}
/*
.nav .bottom .videoPpt.select {
    background-color: #DDE7F0;
}*/
.nav .bottom .videoPpt .img.icon {
    position: relative;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    float: left;
}
.nav .bottom .videoTopic .img.icon {
    background-image: url(../assets/images/video.png);
}
.nav .bottom .pptTopic .img.icon {
    background-image: url(../assets/images/ppt.png);
}
.nav .bottom .audioTopic .img.icon {
    background-image: url(../assets/images/mp3.png);
}
.nav .bottom .videoPpt .lable {
    position: relative;
}
/*
		############
		### Note ###
		############
*/
#note-panel.notesHide,.notesHide{display:none!important;}
#note-panel.note > .content {
    border: 1px solid;
}
#note-panel.note .rows {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
#note-panel.note .top {
    position: relative;
    width: 100%;
}
#note-panel.note .top .title {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
}
#note-panel.note .middle .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#note-panel.note .items {
    position: relative;
    height: 100%;
    display: table;
    text-align: center;
    cursor: pointer;
    border-radius:7px;
}
#note-panel.note .items.save {
    position: absolute;
    left: 0;
    bottom: 3px;
    top:auto;
    max-height: 0px;    
    width: 100%;
}
#note-panel.note .items.save > div.tableCell {
    padding-top:10px;
    padding-bottom:10px;
    width:33%;
}
#note-panel.note .items.save .lable {
    font-size: 12px;
    padding: 3px;
}
#note-panel.note .items.email {
    position: absolute;
    top: 3px;
    right: 0;
    margin-right: 5px;
    max-height: 0px;
    text-decoration: none;
    padding:2px;
}
#note-panel.note .items.email .lable {
    font-size: 12px;
    padding: 1px;
}
#note-panel.note .center {
    position: relative;
    height: 100%;
    display: table;
    margin: 0 auto;
    left: -4px;
}
#note-panel.note .center .items {
    float: left;
    cursor: pointer;
}
#note-panel.note .center .items .img {
    position: relative;
    width: 26px;
    height: 24px;
    background-repeat: no-repeat;
}
#note-panel.note .leftSide {
    float: left;
}
#note-panel.note .rightSide {
    float: right;
}
#note-panel.note .divider {
    height: 100%;
    width: 1px;
    background-color: #4F4F4F;
}
#note-panel.note .lable {
    position: relative;
}
.jqte_tool_icon {
    width: 27px;
    height: 27px;
    background-image:none;
}
#note-panel.note .jqte_tool_4 > a{background-image:none;}
#note-panel.note .jqte_tool_4 > a::after {
    content: "\f032";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 6px;
}
#note-panel.note .jqte_tool_5 > a{background-image:none;}
#note-panel.note .jqte_tool_5 > a::after {
    content: "\f033";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 6px;
}
#note-panel.note .jqte_tool_6 > a{background-image:none;}
#note-panel.note .jqte_tool_6 > a::after {
    content: "\f0cd";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 6px;
}
#note-panel.note .jqte_tool_7 > a{background-image:none;}
#note-panel.note .jqte_tool_7 > a::after {
    content: "\f0cb";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 5px;
}
#note-panel.note .jqte_tool_8 > a{background-image:none;}
#note-panel.note .jqte_tool_8 > a::after {
    content: "\f03a";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 5px;
}
#note-panel.note .jqte_tool_13 > a{background-image:none;}
#note-panel.note .jqte_tool_13 > a::after {
    content: "\f036";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 6px;
}
#note-panel.note .jqte_tool_14 > a{background-image:none;}
#note-panel.note .jqte_tool_14 > a::after {
    content: "\f037";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 6px;
}
#note-panel.note .jqte_tool_15 > a{background-image:none;}
#note-panel.note .jqte_tool_15 > a::after {
    content: "\f038";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 6px;
}
#note-panel.note .jqte_tool_2 > a{background-image:none;}
#note-panel.note .jqte_tool_2 > a::after {
    content: "\f894";
    font: var(--fa-font-solid);
    font-size: 16px;
    padding: 3px 3px;
}
#note-panel.note .bottom .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#note-panel.note .bottom textarea {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 0;
    resize: none;
    box-sizing: border-box;
}
/*
	##################
	### TextEditor ###
	##################
*/
.jqte {
    position: relative;
    width: 100%;
    margin: 0;
    margin: 0;
    box-sizing: border-box;
    border: 0;
    box-shadow: 0px 0px 0px 0px transparent;
    height: 100%;
    border-radius: 12px!important;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    box-sizing: border-box;
}
/*
.jqte * {
    font-family: regular;
}*/
.jqte_source {
    padding: 0;
}
.jqte_tool_1,
.jqte_tool_3,
.jqte_tool_9,
.jqte_tool_10,
.jqte_tool_11,
.jqte_tool_12,
.jqte_tool_16,
.jqte_tool_17,
.jqte_tool_18,
.jqte_tool_19,
.jqte_tool_20,
.jqte_tool_21 {
    display: none;
}
.jqte_focused {
    border-color: transparent;
    box-shadow: 0 0 1px #A5C7FE;
    -webkit-box-shadow: 0 0 0px #A5C7FE;
    -moz-box-shadow: 0 0 1px #A5C7FE;
}
.jqte_focused .jqte_editor{outline:lightgray!important;border: 1px solid lightgray!important;}
/*bold italic underline icons bar*/
.jqte_toolbar {
    padding: 0;
    overflow: visible;
}

/*textEditor*/
.jqte_editor {
    height: calc(100% - 70px);
    padding: 0;
    resize: none;
    padding: 4px;
    box-sizing: border-box;
}
@media screen and (max-width:1024px){
    /*.jqte_editor {
        max-height: 165px;
        margin-bottom: 30px;
        height: 165px;
    }*/
}
.jqte_title {
    display: none !important;
}
.jqte_fontsizes {
    width: 22px !important;
    height: auto !important;
    overflow-y: hidden !important;
    overflow: hidden !important;
    z-index: 999;
}
.jqte_fontsize {
    padding: 0px 0px !important;
    font-size: 12px !important;
    text-align: center;
}
#note-panel.note .bottom .placeholder {
    position: absolute;
    width: 100%;
    top: 50%;
    margin: 0;
    cursor: text;
}
#note-panel.note .bottom .placeholder .title {
    position: relative;
    width: 100%;
    height: 100%;
    display: table;
    margin: 0;
}
#note-panel.note .bottom .placeholder .title .lable ,.noPptMsg{
    text-align: center;
    color: #999999;
}
/*
	#############
	### Right ###
	#############
*/
#right {
    right: 0;
}
#right .base {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#right .box {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    height: 50px;
}
#right .box > .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#right .logo .img {
    position: absolute;
    width: 208px;
    height: 54px;
    background-image: url(../assets/images/right_logo.png);
    background-repeat: no-repeat;
    right: 1px;
    top: 50%;
    margin-top: -27px;
    -webkit-transform-origin: top right;
    -moz-transform-origin: top right;
    -ms-transform-origin: top right;
    -o-transform-origin: top right;
    transform-origin: top right;
}
#right .tabs {
    border-left: 1px solid;
}
#right .tab {
    position: relative;
    height: 100%;
    float: left;
    border-top: 1px solid black;
    cursor: pointer;
    box-sizing: border-box;
    width: 20%;
    color: rgb(255, 255, 255);
    background-color: rgb(31, 107, 191);
}
#right .tab.disable {
    cursor: default;
}
#right .tab:last-child {
    border-right: 1px solid black;
}
#right .tab:not(.select):hover {
    background-color: #4081C9 !important;
}
#right .tab.select {
    background-color: #053468 !important;
	cursor:default;
}
#right .tab.disable:not(.select) {
    opacity: 0.5;
}
#right .tab.disable:not(.select) {
    background-color: rgb(31, 107, 191) !important;
}
#right .tab .divider {
    position: absolute;
    width: 1px;
    right: 0;
}
#right .tab:last-child .divider {
    display: none;
}
#right .tab .center {
    position: relative;
    height: 100%;
    display: table;
    margin: 0 auto;
}
#right .tab .img {
    position: relative;
    width: 45px;
    height: 100%;
    float: left;
    background-position: 0px 50%;
    background-repeat: no-repeat;
	height: 50px;
}
#right .tab .title {
    max-width: 80px;
}
#right .tab .title .lable {
    position: relative;
    text-align: center;
	line-height: 18px;
}
#right .tab.learn .img {
    background-image: url(../assets/images/icon_1.png)
}
#right .tab.orsc .img {
    background-image: url(../assets/images/icon_2.png)
}
#right .tab.qa .img {
    width: 58px !important;
    margin-right: 5px;
    background-image: url(../assets/images/icon_3.png)
}
#right .tab.cmplt .img {
    background-image: url(../assets/images/icon_4.png)
}
#right .tab.help .img {
    background-image: url(../assets/images/icon_6.png)
}
#right .screen.ng-hide, #leftBox .screen.ng-hide{height:0px!important;}
#right .screen {
	/*position: absolute;*/
	width: 100%;
	height: 100%;
	overflow: hidden;
    overflow-y:auto;
	border-left: 1px solid;
	border-bottom: 1px solid;
	border-right: 1px solid;
	background-color: rgb(5, 52, 104);
}
@media screen and (max-width:820px){
    #right .screen.qa, #leftBox .screen.qa{height:100%!important;}
}
#right .screen > [ng-controller], #right .screen > [ng-if]{
    position: relative;
    width: 100%;
    height: 100%;
}
#right .screen.learn #beginScreen,
#leftBox .screen.learn #beginScreen{
    position: absolute;
    width: 100%;
    border: 8px solid lightgray;
    display: inline-block;
    box-sizing: border-box;
}
/*
	###############
	### preTest ###
	###############
*/
.seminarPretestCompleteProcess  #preTest {
    width: 100%;
}
.seminarPretestCompleteProcess  #preTest .content {
    position: relative;
    width: 100%;
    height: 100%;
}
.seminarPretestCompleteProcess  #preTest .pages {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest .text {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest .congrats_1 .feedback {
    color: #053468;
}
.seminarPretestCompleteProcess  #preTest .mcqs {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest  .mcq {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest  .mcq .qn {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest  .mcq .options {
    position: relative;
    margin-top: 0px;
    margin-left: 20px;
    text-align: left;
}
.seminarPretestCompleteProcess  #preTest  .mcq .feedback.displayHide {
    visibility: hidden !important;
    display: block !important;
}
.seminarPretestCompleteProcess  #preTest  .option {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest  .option .tick {
    position: absolute;
    width: 20px;
    height: 16px;
    background-image: url(../assets/images/tick.png);
    background-repeat: no-repeat;
    margin-left: -30px;
    margin-top: 5px;
    background-size: 100% 100%;
}
.seminarPretestCompleteProcess  #preTest .checkBox {
    position: absolute;
    width: 16px;
    height: 16px;
    display: inline-block;
    border: 2px solid;
    cursor: pointer;
    margin-top: 4px;
    box-shadow: inset 0px 0px 0px 1px white;
}
.seminarPretestCompleteProcess  #preTest .checkBox:not(.select):hover {
    border-color: #67798C !important;
}
.seminarPretestCompleteProcess  #preTest .checkBox:not(.select) {
    background-color: white !important;
}
.seminarPretestCompleteProcess  #preTest .option .title {
    position: relative;
    margin-left: 28px;
    display: inline-block;
}
.seminarPretestCompleteProcess  #preTest .option .lable {
    position: relative;
}
.seminarPretestCompleteProcess  #preTest .prevNextPage {
    float: left;
}
.seminarPretestCompleteProcess  #preTest .disable {
    opacity: 0.5;
    cursor: default;
}
/*
	###############
	### Seminar ###
	###############
*/
#right .screen.learn #seminar,
#leftBox .screen.learn #seminar {
    position: absolute;
    width: 100%;
    border: 8px solid;
    display: inline-block;
    box-sizing: border-box;
}
#right .screen.learn #seminar .content,#leftBox .screen.learn #seminar .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#right .screen.learn #seminar .table ,#leftBox .screen.learn #seminar .table {
    margin-top: 1%;
}
#right .screen.learn #seminar .playNow,#leftBox .screen.learn #seminar .playNow {
    padding-left: 10px;
    padding-right: 10px;
}
/*
	##############
	### pptTop ###
	##############
*/
#right .screen.learn .ppts {
    position: absolute;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#right .screen.learn .ppts > .top {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    color: white;
    display: table;
}
#right .screen.learn .ppts > .top .content {
    position: relative;
    width: 100%;
    height: 100%;
    display: table;
    padding: 4px;
    box-sizing: border-box;
    font-size: 14px;
}
#right .screen.learn .ppts > .top .heading {
    position: relative;
    float: left;
    display: table;
    width: 100%;
    margin-bottom: 4px;
    padding-bottom: 4px;
    border-bottom: 1px solid #888888;
}
#right .screen.learn .ppts > .top .heading .title {
    float: left;
}
#right .screen.learn .ppts > .top .heading .lable {
    position: relative;
    color:#000;
}
#right .screen.learn .ppts > .top .lable {
    color:#000;
}
#right .screen.learn .ppts > .top .download {
    position: absolute;
    left:5px;
    display: table;
    text-decoration: none;
    cursor: pointer;
    padding: 0 5px;
    box-sizing: border-box;
    border-radius:7px;
    bottom:0;

}#right .screen.learn .ppts > .top .download{
    transition: all 0.3s ease-out;
}
#right .screen.learn .ppts > .top .download:hover {
    background-color: rgb(28 141 221)!important
}
#right .screen.learn .ppts > .top .fa-down-to-bracket{padding: 8px 5px;}

#right .screen.learn .ppts > .top .download .title {
    position: relative;
    height: 100%;
}
#right .screen.learn .ppts > .top .download .lable {
    position: relative;
    padding-left: 3px;
    display: table-cell;
    vertical-align: middle;
}
#right .screen.learn .ppts > .top .scale {
    position: relative;
    height: 100%;
    width: 100px !important;
    float: right;
    cursor: pointer;
}
#right .screen.learn .ppts .scale .dropDown {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid;
}
#right .screen.learn .ppts .scale .selected {
    position: relative;
    height: 100%;
    float: left;
    text-align: center;
}
#right .screen.learn .ppts .scale .selected .title {
    position: relative;
    width: 100%;
    height: 100%;
    top: 1px;
    display: table;
}
#right .screen.learn .ppts .scale .down {
    position: relative;
    height: 100%;
    float: left;
}
#right .screen.learn .ppts .scale .down .arrow {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -3px;
    margin-left: -6px;
    display: inline-block;
}
#right .screen.learn .ppts .scale .down .divider {
    position: absolute;
    width: 1px;
    height: 80%;
    left: 0;
    top: 10%;
}
#right .screen.learn .ppts .scale .menu {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    z-index: 9;
}
#right .screen.learn .ppts .scale .menu .item {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    border-left: 1px solid;
    border-right: 1px solid;
    border-bottom: 1px solid;
    box-sizing: border-box;
    cursor: pointer;
    z-index: 9;
}
#right .screen.learn .ppts .scale .menu .item:hover {
    background-color: #DCE7EF !important;
}
#right .screen.learn .ppts .scale .menu .title {
    position: relative;
    width: 100%;
    height: 100%;
    top: 1px;
    display: table;
}
#right .screen.learn .ppts .nav {
    text-align:center;
    width:100%;
    box-sizing: border-box;
    bottom: 0;
    display: block;
    margin: 0 auto;
}
#right .screen.learn .ppts .nav .backPatch {
    position: relative;
    color: #999999;
}
#right .screen.learn .ppts .nav .backPatch:hover,
#right .screen.learn .ppts .nav .nextPatch:hover {
    color: #FFFFFF !important;
}
.hoverArrow {
    color: #FFFFFF !important;
}
#right .screen.learn .ppts .nav .nextPatch {
    position: relative;
    color: #999999;
    float: right;
    top: 3px;
    margin-left: 7px;
}
.cursorPointer{	
    cursor: pointer;
}
#right .screen.learn .ppts .nav{
    width:190px!important;
}
#right .screen.learn .ppts .nav .back {
    position: relative;
    left: 0px;
    padding-left: 4px;
    float:left;
    color: #464545;
    font-size: 30px;
}
#right .screen.learn .ppts .nav .next {
    position: relative;
    padding-right: 4px;
    margin-left: 7px;
    float:right;
    color: #464545;
    font-size: 30px;
}
#right .screen.learn .ppts .nav .pageNum {
    position: relative;
    height: 100%;
    float: left;
    text-align: center;
    display: table;
    padding: 0;
}
#right .screen.learn .ppts .nav .current {
    cursor: text;
}
#right .screen.learn .ppts .nav .current input {
    position: relative;
    height: 100%;
    text-align: center;
    border: 0;
    cursor: text;
    box-sizing: border-box;
    width: 100%;
    padding: 0;
    margin: 12px 0px;
}
.alignCenter{float: left;display:flex; justify-content: center;width:115px;}
@media screen and (max-width:820px){
    .alignCenter{width:83px;}  
    #right .screen.learn .ppts .nav{width:155px!important;} 
    #right .screen.learn .ppts .nav .back{padding-left:0px;}
    #right .screen.learn .ppts .nav .next{padding-right:1px;}
    .navControls{left:0px;}
    #right .screen.learn .ppts > .top .download{padding:0 2px;font-size: 13px;}
    #note-panel{position:absolute;width:100%;}
}
#right .screen.learn .ppts .nav .pageNum .lable{margin: 12px 0px;}
#right .screen.learn .ppts .nav .of .lable {
    cursor: default;
}
#right .screen.learn .ppts .nav .tot .lable {
    cursor: default;
}
/*
	#################
	### pptBottom ###
	#################
*/
#right .screen.learn .ppts .bottom {
    position: relative;
    width: 100%;
    overflow: auto;
}
#right .screen.learn .ppts .bottom .content {
    position: absolute;
    width: 100%;
    height: 100%;
}
#right .screen.learn .ppts .bottom .img {
    position: relative;
    width: 1650px;
    height:100%;
    left: 0;
    margin: 0 auto;
    top: 0;
    background-size: contain;
    background-position:center;
    background-repeat: no-repeat;
}
/*
	#######################
	### CompletedScreen ###
	#######################
*/
#right .screen.learn #completed ,#leftBox .screen.learn #completed {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    border-top: 1px solid;
    z-index: 99;
    box-sizing: border-box;
}
#right .screen.learn .ppts .bottom #referenceImg{
	display:none;
}
#right .screen.learn #completed > .content {
    position: absolute;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#right .screen.learn #completed .alert {
    position: relative;
    height: 20%;
    left: 50%;
    top: 50%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px black;
}
#right .screen.learn #completed .alert .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#right .screen.learn #completed .alert .ok {
    position: relative;
    text-align: center;
    display: table;
    margin: 0 auto;
    cursor: pointer;
}
#right .screen.learn #completed .alert .ok:hover {
    background-color: #67798C !important;
}
#right .screen.learn #completed .alert .ok:active {
    background-color: #263D56 !important;
}
#right .screen.learn #completed .alert .ok .title {
    height: 100%;
}
/*
	#######################
	### Other Resources ###
	#######################
*/
#right .screen.orsc .materials,
#leftBox .screen.orsc .materials {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#right .screen.orsc .materials .content,#leftBox .screen.orsc .materials .content {
    position: relative;
    width: 100%;
    height: 100%;
}
#right .screen.orsc .materials .top,
#leftBox .screen.orsc .materials .top, #complete-controller .top,.cmpltScreen .heading {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    border-radius: 12px;
}.cmpltScreen .heading{padding: 8px;}
#right .screen.orsc .materials .top .title,#leftBox .screen.orsc .materials .top .title {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: left;
    display: table;
}
#right .screen.orsc .materials .bottom ,#leftBox .screen.orsc .materials .bottom {
    position: relative;
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
}
#right .screen.orsc .materials .bottom .heading,#leftBox .screen.orsc .materials .bottom .heading {
    position: relative;
    float: left;
    font-weight: 500;
}
#right .screen.orsc .materials .bottom .link,#leftBox .screen.orsc .materials .bottom .link {
    position: relative;
    clear: both;
    display: flex;
    align-items: center;
}
#right .screen.orsc .materials .bottom .download,#leftBox .screen.orsc .materials .bottom .download {
    text-decoration: none;
}
#right .screen.orsc .materials .bottom .download .img,#leftBox .screen.orsc .materials .bottom .download .img {
    position: relative;
    width: 28px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 0px 50%;
    float: right;
}
#right .screen.orsc .materials .bottom .download .pdf,#leftBox .screen.orsc .materials .bottom .download .pdf {
    background-image: url(../assets/images/pdf.png);
}
#right .screen.orsc .materials .bottom .download .mp3,#leftBox .screen.orsc .materials .bottom .download .mp3 {
    background-image: url(../assets/images/mp3.png);
}
#right .screen.orsc .materials .bottom .download .doc,#right .screen.orsc .materials .bottom .download .docx ,#leftBox .screen.orsc .materials .bottom .download .doc,#leftBox .screen.orsc .materials .bottom .download .docx {
    background-image: url(../assets/images/word_icon.png);
}

#right .screen.orsc .materials .bottom .download .ppt,#right .screen.orsc .materials .bottom .download .pptx ,#leftBox .screen.orsc .materials .bottom .download .ppt,#leftBox .screen.orsc .materials .bottom .download .pptx {
    background-image: url(../assets/images/ppt1.png);
}
#right .screen.orsc .materials .bottom .download .xlsx,#right .screen.orsc .materials .bottom .download .xls,#leftBox .screen.orsc .materials .bottom .download .xlsx,#leftBox .screen.orsc .materials .bottom .download .xls {
    background-image: url(../assets/images/xls.png);background-size: 24px;
}
/*
	################
	### Complete ###
	################
*/
.seminarCompleteProcess .elements,#leftBox .screen.cmplt .elements {
    position: ralative;
    width: 100%;
    display: inline-block;
    padding:8px;
    box-sizing: border-box;
}
.seminarCompleteProcess .elements .content,#leftBox .screen.cmplt .elements .content {
    position: relative;
    width: 100%;
    height: 100%;
}
.seminarCompleteProcess .successScreen.displayHide,#leftBox .screen.cmplt .successScreen.displayHide {
    display: block;
    visibility: hidden !important;
}

.seminarCompleteProcess .successScreen .certificate,#leftBox .screen.cmplt .successScreen .certificate {
    position: relative;
    width: 820px;
    height: 808px;
    left: 50%;
    top: 50%;
    margin-left: -410px;
    margin-top: -404px;
    background-image: url(../assets/images/certificate.png);
    background-repeat: no-repeat;
}
.seminarCompleteProcess .successScreen .options ,#leftBox .screen.cmplt .successScreen .options {
    position: absolute;
    top: 0;
    right: 0;
}
.seminarCompleteProcess .successScreen .options .icon ,#leftBox .screen.cmplt .successScreen .options .icon {
    position: relative;
    width: 48px;
    height: 42px;
    float: right;
    cursor: pointer;
}
.seminarCompleteProcess .successScreen .options .save,#leftBox .screen.cmplt .successScreen .options .save {
    background-image: url(../assets/images/save.png);
}
.seminarCompleteProcess .successScreen .options .print,#leftBox .screen.cmplt .successScreen .options .print {
    background-image: url(../assets/images/print.png);
}
.seminarCompleteProcess .successScreen .options .email,#leftBox .screen.cmplt .successScreen .options .email {
    background-image: url(../assets/images/email.png);
}
.seminarCompleteProcess .startScreen .feedback {
    margin-top: 15px;
}
.continueMsg{padding:1%;}
/*
	################
	### postTest ###
	################
*/
.seminarCompleteProcess #postTest {
    position: relative;
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
}
.seminarCompleteProcess #postTest .content {
    position: relative;
    width: 100%;
    height: 100%;
}
.seminarCompleteProcess #postTest .pages {
    position: relative;
}
.seminarCompleteProcess #postTest .text {
    position: relative;
}
.seminarCompleteProcess #postTest .mcqs {
    position: relative;
}
.seminarCompleteProcess #postTest .mcq {
    position: relative;
}
.seminarCompleteProcess #postTest .mcq .qn {
    position: relative;
}
.seminarCompleteProcess #postTest .mcq .options {
    position: relative;
    margin-top: -8px;
    margin-left: 20px;
}
.seminarCompleteProcess #postTest .option {
    position: relative;
}
.seminarCompleteProcess #postTest .checkBox {
    position: absolute;
    width: 16px;
    height: 16px;
    display: inline-block;
    border: 2px solid;
    top: 50%;
    margin-top: -8px;
    cursor: pointer;
    box-shadow: inset 0px 0px 0px 1px white;
}
.seminarCompleteProcess #postTest .checkBox:not(.select):hover {
    border-color: #67798C !important;
}
.seminarCompleteProcess #postTest .checkBox:not(.select) {
    background-color: white !important;
}
.seminarCompleteProcess #postTest .option .title {
    position: relative;
    left: 25px;
    display: inline-block;
}
.seminarCompleteProcess #seminarRateView,#leftBox #seminarRateView{
	margin-top: 2px;
}
.seminarCompleteProcess #postTest .option .lable {
    position: relative;
}
.seminarCompleteProcess #postTest .prevNextPage {
    position: relative;
}
.seminarCompleteProcess #postTest .disable {
    opacity: 0.5;
    cursor: default;
}
/*
	####################
	### returnScreen ###
	####################
*/
.seminarCompleteProcess .returnScreen th ,#leftBox .screen.cmplt .returnScreen th {
    text-align: left;
}
.seminarCompleteProcess .returnScreen tr td:last-child,#leftBox .screen.cmplt .returnScreen tr td:last-child {
    text-align: center;
}
.seminarCompleteProcess .returnScreen .img,#leftBox .screen.cmplt .returnScreen .img {
    position: absolute;
    top: 50%;
    right: 0;
    width: 194px;
    height: 188px;
    background-image: url(../assets/images/wrong.png);
    background-repeat: no-repeat;
    margin-top: -84px !important;
    opacity: 0.3;
}
.seminarCompleteProcess .pages .page,
.seminarCompleteProcess .totalUnansweredQtns,
.seminarCompleteProcess .attemptedQuestionCount{
    padding-top: 10px;
}
#activity .content-popup.seminarCompleteProcess  {
    top: 25% !important;
}
.seminarCompleteProcess .resultHead{
    margin-bottom: 10px;
}
/*
	################
	### qaScreen ###
	################
*/
#right .screen.qa .qaScreen,#leftBox .screen.qa .qaScreen {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#right .screen.qa .qaScreen .content ,#leftBox .screen.qa .qaScreen .content {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
#right .screen.qa .qaScreen .elements ,#leftBox .screen.qa .qaScreen .elements {
    position: relative;
    width: 100%;
}
#right .screen.qa .qaScreen .elements > .content,#leftBox .screen.qa .qaScreen .elements > .content {
    box-shadow: 0px 0px 0px 1px lightgrey;
    border-radius: 12px;
}
#right .screen.qa .qaScreen .askQa .top,#leftBox .screen.qa .qaScreen .askQa .top {
    position: relative;
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    border-top-right-radius: 11px;
    border-top-left-radius: 11px;
}
#right .screen.qa .qaScreen .askQa .top .title,#leftBox .screen.qa .qaScreen .askQa .top .title {
    display: table;
    height: 100%;
}
#right .screen.qa .qaScreen .askQa .middle,#leftBox .screen.qa .qaScreen .askQa .middle {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
#right .screen.qa .qaScreen .askQa .middle .title ,#leftBox .screen.qa .qaScreen .askQa .middle .title {
    display: table;
    height: 100%;
}
#right .screen.qa .qaScreen .askQa .bottom,#leftBox .screen.qa .qaScreen .askQa .bottom {
    position: relative;
    padding-top: 0 !important;
    box-sizing: border-box;
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
}
#right .screen.qa .qaScreen .askQa .bottom .textArea,#leftBox .screen.qa .qaScreen .askQa .bottom .textArea {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
#seminarSuggetionView textarea {
    -webkit-user-select: initial!important;
}
#right .screen.qa .qaScreen .askQa .bottom textArea ,#leftBox .screen.qa .qaScreen .askQa .bottom textArea {
    position: relative;
    width: 100%;
    height: 100%;
    border: 0;
    resize: none;
    box-sizing: border-box;
    -webkit-user-select: initial;
}
#right .screen.qa .qaScreen .prevQa .top,#leftBox .screen.qa .qaScreen .prevQa .top {
    position: relative;
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}
#right .screen.qa .qaScreen .prevQa .top .title,#leftBox .screen.qa .qaScreen .prevQa .top .title {
    display: table;
    height: 100%;
}
#right .screen.qa .qaScreen .prevQa .middle ,#leftBox .screen.qa .qaScreen .prevQa .middle {
    position: relative;
    width: 100%;
    box-sizing: border-box;
}
#right .screen.qa .qaScreen .prevQa .middle .title,#leftBox .screen.qa .qaScreen .prevQa .middle .title {
    display: table;
    height: 100%;
}
#right .screen.qa .qaScreen .prevQa .bottom ,#leftBox .screen.qa .qaScreen .prevQa .bottom {
    position: relative;
    padding-top: 0 !important;
    box-sizing: border-box;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}
#right .screen.qa .qaScreen .prevQa .bottom .qaBox,#leftBox .screen.qa .qaScreen .prevQa .bottom .qaBox {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
}
#right .screen.qa .qaScreen .prevQa .bottom .qa a ,#leftBox .screen.qa .qaScreen .prevQa .bottom .qa a {
    cursor: pointer;
}
#right .screen.qa .qaScreen .prevQa .bottom .qa .lable,#leftBox .screen.qa .qaScreen .prevQa .bottom .qa .lable {
    border-bottom: 1px solid;
}
#right .screen.qa .qaScreen .prevQa .bottom .qa:last-child:last-child ,#leftBox .screen.qa .qaScreen .prevQa .bottom .qa:last-child:last-child {
    padding-bottom: 3%;
}
#right .screen.qa .qaScreen .prevQa .bottom .qa .answer ,#leftBox .screen.qa .qaScreen .prevQa .bottom .qa .answer {
    margin-left: 18px;
    padding-top: 5px;
}
#right .screen.qa .qaScreen .prevQa .img.status,#leftBox .screen.qa .qaScreen .prevQa .img.status {
    position: relative;
    width: 0;
    height: 0;
    display: inline-block;
    float: left;	
	margin-top: 7px !important;
    margin-right: 6px;
}
#right .screen.qa .qaScreen .prevQa .img.arrowRight.arrowDown,#leftBox .screen.qa .qaScreen .prevQa .img.arrowRight.arrowDown {
    border-left: 6px solid transparent !important;
    border-right: 6px solid transparent !important;
    border-top: 8px solid !important;
    margin-top: 6px !important;
}
#right .screen.qa .qaScreen #qa_feedback,#leftBox .screen.qa .qaScreen #qa_feedback {
    margin-top: 15px;
    font-size: 14px;
}
/*
	###########
	### Help ###
	###########
*/

#right .screen.help .accordion,#leftBox .screen.help .accordion {
    position: relative;
    width: 100%;
    display: inline-block;
    padding:8px;
    box-sizing: border-box;
}
#leftBox .screen.help .accordion{padding:0;}
#right .screen.help .accordion .top,#leftBox .screen.help .accordion .top {
    position: relative;
    width: 100%;
    cursor: pointer;
    box-sizing: border-box;
    border-radius: 12px;
}
#right .screen.help .accordion .top:hoverm,#leftBox .screen.help .accordion .top:hover {
    /*background-color: rgba(183, 196, 212, 0.7) !important;*/
    filter: brightness(120%);
}
#right .screen.help .accordion .top:first-child,#leftBox .screen.help .accordion .top:first-child {
    margin-top: 0 !important;
}
#right .screen.help .accordion .top .img.icon.status.arrowRight ,#leftBox .screen.help .accordion .top .img.icon.status.arrowRight {
    position: absolute;
    top: 50%;
    margin-top: -5px !important;
    margin-left: 1% !important;
}
#right .screen.help .accordion .top .img.arrowRight.arrowDown,#leftBox .screen.help .accordion .top .img.arrowRight.arrowDown {
    border-left: 6px solid transparent !important;
    border-right: 6px solid transparent !important;
    border-top: 8px solid !important;
    margin-left: -4px;
    margin-top: 6px !important;
}
#right .screen.help .accordion .top .img.icon.status.arrowRight.arrowDown,#leftBox .screen.help .accordion .top .img.icon.status.arrowRight.arrowDown {
    margin-top: -3px !important;
}
#right .screen.help .accordion .top .title,#leftBox .screen.help .accordion .top .title  {
    position: relative;
    height: 100%;
    left: 8px;
    display: table;
}
#right .screen.help .accordion .bottom ,#leftBox .screen.help .accordion .bottom{
    position: relative;
    width: 100%;
    padding-bottom: 0 !important;
    box-sizing: border-box;
}
#right .screen.help .accordion .bottom .paragraph ,#leftBox .screen.help .accordion .bottom .paragraph {
    margin: 5px 0px;
}
#right .screen.help .accordion .bottom [ng-controller], #right .screen.help .accordion .bottom [ng-if],#leftBox .screen.help .accordion .bottom [ng-controller], #leftBox .screen.help .accordion .bottom [ng-if] {
    position: relative;
    width: 100%;
    display: inline-block;
    padding:10px;
}
#right .screen.help .accordion .bottom .left,#leftBox .screen.help .accordion .bottom .left {
    position: relative;
    float: left;
}
#right .screen.help .accordion .bottom .right ,#leftBox .screen.help .accordion .bottom .right {
    position: relative;
    float: right;
}
#right .screen > [ng-controller*=learn] .video .controls {
    top: auto !important;
}
#right .screen > [ng-controller*=learn] .video .flex-video {
    padding-top: 0 !important;
    margin-bottom: 0%;
}
.flex-video{border-radius:12px;}
#video-controller.orientation {   
    display: table !important;
}
#right .screen > [ng-controller*=learn] .video {
    height: 100% !important;
}
#right .screen > [ng-controller*=learn] .video > div:first-child {
    display: table-cell;
    vertical-align: middle;
}
@media screen and (max-width:767px){
    #left #video-controller{margin-bottom:10px!important;}
    #left{margin-bottom:10px!important;}
}
#right .screen > [ng-controller*=learn] .video .small-12 {
    width: 100% !important;
}
.large_video {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    background-color: transparent !important;
}
.large_video video {
    position: relative !important;
    background-color: black !important;
    padding: 0 !important;
}
.videoRemove {
    overflow: hidden;
}
.large_video + .controls .videoZoom{
    display: none !important;
}

.sizeLarge #right {
	position: absolute !important;
}

.boxShadowRed {
	box-shadow: inset 0px 0px 0px 1px red;
}
.sizeLarge #left, .sizeLarge #right {
	height: 100% !important;
}
.sizeLarge #left .nav .top {
	position: absolute;
	top: 0;
	height: 26px !important;
	z-index: 9;
}
.sizeLarge #left .nav .bottom {
	top: 0;
	box-sizing: border-box;
	z-index: 8;
}
.sizeLarge #note-panel.note .top {
	position: absolute;
	top: 0;
	height: 26px !important;
	z-index: 9;
}
.sizeLarge #note-panel.note .bottom {
	position: absolute;
	top: 0;
	height: 100% !important;
	box-sizing: border-box;
	z-index: 8;
}
#noteHolder{margin-top:35px;    padding-left: 0.5em;    padding-right: 0.5em;}
.sizeLarge .video .controls .bottom {
	position: absolute !important;
}
.hidingControllsForIpad,.hidingControlls {
    visibility: hidden;
}
.guidedTour  .guidedTour .video .videoZoomInOutParent {
    visibility: hidden;
}
.ui-slider-handle .content {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #8B8B8B;
    border-radius: 50%;    
}
/*slider status background color*/
.ui-slider-range {
    background-color: rgb(5, 85, 174);
}
#activity.touch .video .playNext {
	left: 0 !important;
	height: 33px /*!important*/;
}
#activity.touch .video .playPrev {
	height: 33px /*!important*/;
}
#activity.touch  .video .play {
	margin: 0px 20px !important;
}
#activity.touch  .video .controls .top {
	margin-top: 0 !important;
}
#activity.touch  .video .controls .bottom {
	margin-top: 0 !important;
}
/* all touch device specific css*/
#activity.touch .video .volumeZoom .sliderUiParent{
    display: none;
}

.ipad.landscape.zoomMode-2 #right .tab .title,
.ipad.landscape.zoomMode-3 #right .tab .title{
    display: none;
}
.navControls{
    position: absolute;
    width: 100%;
    height: 45px;
    bottom: 8px;
    display: block;
    z-index: 999;
}
#left .video[type*=audio] .controls .videoZoom{
    display: none !important;
}
.ipad #video {
    background-color: black !important;
}
.ipad .video[type*=large_video] video {
    display: table;
    margin: 0 auto;
}
.video[type*=large_video] .playPause  {
    display: table;
    margin: 0 auto;
    float: none;
}
.video[type*=large_video] .playMedia  {
    display: table;
    margin: 0 auto;
    float: none;
}
#left .leftTabSelected{
	background-color: #053468;
	color: white;
}
#left .leftTabContainer{
	color: white;
	background-color: #4081C9;
}
#left .leftTabContent{
	height:60%;
}
#left .leftTabContent.fullheight{height: 100%!important;}
#left .left-tab{
	text-align: center;
	line-height: 30px;
	height: 100%;	
	padding: 0px !important;
	cursor: pointer;
}
#left .leftTabContainer  .divider{
    position: absolute;
    background: white;
    top: 5%;
    height: 90%;
    right: 0;
    width: 1px;
}
#left .leftTabContainer .patch{
	height: 30px;
	width: 100%;
	position: absolute;
	background-color: rgba(255, 255, 255, 0.9);
	display:none;
	z-index:99;
}
.showPatch{
	display: block !important;
}
#right #seminarRateView table thead tr th{
	text-align:center;
}

.flex-video.flexAudio{padding-bottom:10%!important;}
.flex-video.flexAudio .controls{padding-top:6%;}
.videoSmall{
	height: 58px !important;
	display:none;
}
/*
.videoAlignCenter{
	position:absolute !important;
	left:0;
	right:0;
	top:0;
	bottom:0;
	margin:auto;
}*/
.playPrevLarge{
	top:4px;
	left:-3px;
}
.playNextLarge{
	top:4px;
	left:10px;
}
#userInactiveView{
    width: 100%;
    height: 100%;
    background-color: red;
    position: absolute;
    z-index: 1000;
    display: none;
}
#right .userTimeReportScreen .userInputContainer{
    width: 100%;
    padding: 1%;
}
#right .userTimeReportScreen .textNdInput{
    width: 100%;
    height: 50px;
    padding: 5px;
}
#right .userTimeReportScreen .textNdInput .text{
    display: inline-block;
    width: 170px;
}
#right .userTimeReportScreen .textNdInput .inputContainer{
    display: inline-block;
    margin-left: 10px;
    width: 100px;
}
#right .userTimeReportScreen .textNdInput .inputContainer .input-box{
    display: inline-block;
}
#right .userTimeReportScreen .textNdInput .inputContainer #steper-nav-container{
    display: inline-block;
    margin-left: 3px;
    cursor: pointer;
}
#right .userTimeReportScreen .checkBoxContainer .text{
    display: inline-block;
}
#right .userTimeReportScreen .checkBoxContainer{
    margin-left: 15px;
}
#right .userTimeReportScreen .disable{
    opacity: 0.5;
    cursor: default;
}
/* ALERT POPUP STYLE */
/* ALERT POPUP STYLE */
.alert{
    position: absolute;
    width: 100%;
    padding: 20px;
    z-index: 100;
    background: #fff;
    border: 1px solid lightgray;
    height: 100%;
    display: table;
    font-family: regular;
    z-index: 99999;
}
.alert .alert-container{
    position: absolute;
    left: 50%;
    top: 50%;
    background-color: white;
    box-shadow: 0px 0px 0px 1px lightgray;
    padding-top: 0px !important;
    padding: 10px;
    width: 430px;
    margin-left: -225px;
    margin-top: -62px;
    text-align: center;
    border-radius:12px;
}
.alert-container-iphone{
    top: 20% !important;
    width: 300px !important;
    margin-left: -150px !important;
}
.alert-topic{
    width: 100%;
    height: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
    font-family: bold-semi;
}
.alert-description{
    position: relative;
    width: 100%;
    margin-bottom: 10px;
}
.alert .alert-container .button{
    margin: 0 auto;
    width: 70px;
    border-radius: 6px;
    padding: 6px;
}
#right .screen #complete-controller .requirementsScreen{
    border: 8px solid rgba(0,0,0,0);
    background-color: none;
}
#right .screen #complete-controller .requirementsScreen 
.requirementsScreenContent{
    margin-bottom: 10px;
    border-color: rgb(5, 52, 104);
    background-color: rgb(255, 255, 255);
}

#right .screen #complete-controller .requirementsScreen 
.requirementsScreenContent .authority{
    display: inline-block;
    width: 40%;
}
#right .screen #complete-controller .requirementsScreen 
.requirementsScreenContent .deadline{
    display: inline-block;
    width: 55%;
}
#right .screen #complete-controller .requirementsScreen 
.requirementsScreenContent .button{
    width: 194px;
}
.seminarCompleteProcess #postTest .tick {
    position: absolute;
    width: 24px;
    height: 20px;
    background-image: url(../assets/images/tick.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
	top: 0px;
    bottom: 0px;
    margin:auto;
    margin-left: -30px;
} 
.seminarCompleteProcess #postTest .feedback{
    color: rgb(195, 31, 80);
}
.seminarCompleteProcess #postTest .message{
	color: #1db94b;
}
.seminarCompleteProcess .startScreen .accept{
    margin-left: 5px;
}
.seminarCompleteProcess .elements .button.acceptAndStart.disable{
    opacity: 0.5;
    cursor: default;
}
#lmPanelContent{
    overflow-y: auto;
}
.submitEvalBtn{
	
	padding-left: 10px;
    padding-right: 10px;
}
.audioVideoSync{
	padding-left: 0!important;
    padding-right: 0!important;	
	top: 34% !important;
}
.audioVideoSync .heading{
	color: #fff!important;
    background: #323232 !important;	
	padding-left: 10px !important;
	text-align: left !important;
}
.addWrap{
	float: left!important;
    margin-left: 7px!important;
}
.saveWrap{
	float: right!important;
    margin-right: 7px!important;	
}
.tblSync{
	width:100%;
}
.tblSync thead tr th{
	
	text-align: center;
    background: #1f6bbf;
    color: #fff;
}
.tblSync tbody tr td{	
	text-align: center;
}
.tblSync tbody tr{	
	border-bottom: 1px solid #9E9E9E
}
.tblSync.fixed_header{
	width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
}

.tblSync.fixed_header tbody{
    display:block;
    width: 100%;
    overflow-y: scroll;
    height: 420px;
}

.tblSync.fixed_header thead tr {
    display: block;
}
.tblSync.fixed_header th, .tblSync.fixed_header td {
	padding: 5px;
	text-align: center;
	width: 200px;
}
.pointerIcons{
	cursor:pointer;
}
.closeIconSync{
	border: 1px solid #fff;
    background: #fff;
    position: absolute;
    right: 15px;
}
.heading3{
	border-bottom: 1px solid #323232;
}
.Synccontent,.syncTableWrap1,.tblSync{
	border-radius:12px;
}
.Synccontent{padding: 0px 0 10px 0;}
.Synccontent thead{
	background: #1f6bbf !important;
}
.Synccontent .heading{
	color: rgb(255, 255, 255);
    background-color: rgb(183, 196, 212);
    color: rgb(36, 55, 75);
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    border-top-left-radius:7px;
    border-top-right-radius:7px;
    position:relative;
}
.audioSyncClose{
    right: 3%;
    position: absolute;
    top: 22%;
    font-size: 1.5em;
    cursor: pointer;
}
.noteShow{display:block;}
#noteHolder{display:block;}
@media screen and (max-width: 1800px) {
    #note-panel.note .items.email,#note-panel.note .items.save{bottom:3px;top:auto;}
}
@media screen and (max-width: 1024px) {
	.mediaSyncWrap {
		position:absolute !important;
		top:100% !important;
	}
    #bottom{display:flex;flex-direction:column;}
    /*#right{flex-grow:1;}*/
    #noteHolder{flex-grow:1;}
    /*#right > div{height:100%;}*/
    #right > div{height:auto;}
    #right.nopadding{padding-left:0;padding-right:0;}
    #noteHolder{display:none;}
    .noteShow{display:block!important;}
    .background-color-patch{position:fixed;}
}@media screen and (max-width: 1023px) {
    #note-panel.note .items.email,#note-panel.note .items.save{bottom:3px;}
    #right > div{margin-top:10px;}
}
@media screen and (max-width: 860px) {
	#right .screen.learn .ppts .bottom .img{
        height:100%;
        max-height:500px;
    }
}
@media screen and (max-width: 400px) {
    #right .screen.learn .ppts .bottom .img{
        height:100%;
        width: 100%;
    }
    #note-panel.note .items.email,#note-panel.note .items.save{bottom:3px;top:auto;}
}
.sliderWidth{
	width:60% !important;
}
.progressSlider{
	width:70%;
}
.qaRefresh{
	width: 14px;
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
}
.qaRefreshLoading{
	
	display:none;
}
.pptImageSlide{
	width:98% !important;
}
.playpauseImg{
	background-image: url(../assets/images/pauseVideo.png);
    background-repeat: no-repeat;
    width: 9%;
    height: 9%;
    position: absolute;
    left: 0%;
    right: 0%;
    top: 0%;
    bottom: 0%;
    margin: auto;
    background-size: contain;
    background-position: center;
    z-index: 10;
}
.playpauseImgHide{
	display:none;
}
.show-for-small-only .title-area{
	padding-left:0;
}
.show-for-small-only .title-area li.name > h1 > a{
	padding-left:5px;
}
.show-for-small-only .title-area .menu-icon a div{
	width: 27px;
    height: 2.2px;
    background-color: #fff;
    margin: 6px 0;
}
.show-for-small-only .title-area .menu-icon a{	
	padding:0 ;
	padding-right: 10px !important;
}
.gotoCompletedButton{
	padding: 3px 5px;
	margin: auto;
    display: inline-block;
}
.revisitVideoButton{
	padding: 3px 5px;
	margin: auto;
    display: inline-block;
}
.seminarCompleted .seminarCompletedBtns{
	text-align : center;
}
.creditExpiredBtns div.button{
	padding: 3px 10px;
    margin: 0 auto;
}
.currentVideoFileTitle {
    text-align: center;
    z-index: 998;
    background: transparent;
    position: absolute;
    color: white;
    font-size: 0.8em;
    top: 0;
    padding: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width:50%
}
.evaluationScreen #formtitle,#certificateTitle{border-radius: 12px;padding: 8px 8px 8px 2%;margin-bottom: 10px;}
.evaluationScreen #matrixView th{text-align:center;}
.playerWell,div.screen{max-height:1024px;}
#notePanelContent .bottom{height:100%;}
.noPptMsg{position: absolute;width: 100%;top: 50%;margin: 0;cursor: text;font-size: 14px;}
/*mobile UX change for hiding learn section when other tabs are active*/
@media screen and (max-width:700px){
    .mobileHidden{visibility: hidden;display: none!important;}
    #right.expandFull,.expandFull div.screen,.expandFull div#rightBase{height:100%!important;}
}
@media screen and (max-height:800px){
    .mobileHidden{visibility: hidden;display: none!important;}
    #right.expandFull,.expandFull div.screen,.expandFull div#rightBase{height:100%!important;}
}

@media screen and (max-width:1024px){
    #activity.touch .video .flex-video.flexAudio .playNext{
        left:40px!important;
    }
    #activity.touch .video .flex-video.flexAudio .play .bg{
        left: 0px;
        top: 16px;
    }
}
@media screen and (max-width:820px){
    #activity .content-popup,#activity > .patch > .content .saveExit,#activity > .patch > .content .focusOutView,#activity > .patch > .content .emailSentView,#activity > .patch > .content .emailInput  {
        top: 10%;
    }

    div#left ~ #right .screen.orsc[style*='visibility: visible'], div#leftBox ~  .screen.orsc[style*='visibility: visible'] {
        visibility: hidden;
    }
    #left .leftTabContent #note-panel{
        padding-left:0.5em;
        padding-right:0.5em;
    }
}
.video-js{
    min-height: 180px;
    width: 100%;
    height:100%;
}
#right.wideScreen #video .video-js{min-height:318px;}
#video-controller .medium-centered{
    height: 180px;
}
/*
#right .video-js{
    height: 100% !important;
}*/
.audioWrap#video{
    height:0% !important;
}
.audioWrap .video-js {
    min-height: 54px !important;
}
.audioWrapLg{
    height: 80px !important;
}
.completeProgramTitle div{
    font-size: 14px !important;
    font-weight: bold !important;
}
.completeProgramTitle i{
    font-size: 18px !important;
    font-weight: bold !important;
}
.completedStatus{
    position: absolute;
    right: 20px;
}
.learningRequirementsIncomplete{
    color:grey !important;
}
.noteBtnClr{
    color: var(--button-txtcolor)!important;
}
.noteBtnClr  i{
    font-size: 16px;
}
.viewCertBtn,#emailmat a{
    color: var(--button-color)!important;
}
.viewCertBtn i{
    color: var(--button-color)!important;
}
.CertificateLink{
    padding-top: 10px;
}
.cmpltPrcent.danger{
    color:rgb(245, 11, 11) !important;
}
.completeProgramTitle{
    display:block !important;
    width:100% !important;
}
.startCompleteProcess{
    position: absolute;
    top: 0px;
    right: 25px;
    padding: 3px 5px 5px 5px;
    font-weight: normal !important;
}
.learningRequirementsIncomplete .startCompleteProcess,
.learningRequirementsIncomplete .startCompleteProcess:hover{
    cursor: not-allowed !important;
    background: gray !important;
}
.pull-right{
    float:right;
}
.closeCompletePopup{
    cursor: pointer;
    width: 30px;
}
#preTest .pretestTitle{
    padding-bottom: 15px !important;
}
#preTest .button{
    margin-top: 20px;
}
#sectionAfterEndPreTest .content{
    text-align: left;
}
#preTest .mcqs{
    padding-bottom: 30px;
}
.removeSpace{
    padding-bottom: 0px !important;
}
.textLeft{
    text-align: left !important;
}
.optionMessage{
    margin-top: 7px !important;
}

#examWrapper {
    position: absolute;
    right: 0px;
    top: 0px;
    display: none;
    z-index: 99999;
    width: 100%;
}
#examWrapper .content-popup:not(.mobileView){
    position: unset !important;
    width: 75% !important;
    float: right;
    height: 100vh;
    border-radius: 0px;
    overflow-y: scroll;
}

.closeExamPopup{
    background: #a6a1a1;
    color: #fff;
    min-width: 60px;
    min-height: 28px;
    padding: 3px 1%;
    margin-top: 10px;
}
.actionBtns{
    width: 100% !important;
}
.actionBtns .button{
    display: inline-block;
}
.closeExamPopup{
    padding-left: 10px;
    padding-right: 10px;
}
.failedExamHead{
    color: #ff0000 !important;
    font-weight: bold;
}
.seminarCompleteProcess .popupLogo,
.seminarPretestCompleteProcess .popupLogo{
    display: none;
}
.start.button{
    text-align: center !important;
    text-align: -webkit-center !important;
}

/*Mobile View Exam Sextion*/
.seminarCompleteProcess.mobileView .popupLogo,
.seminarCompleteProcess.mobileView .popupHead,
.seminarPretestCompleteProcess.mobileView .popupLogo{
    display: block !important;
    padding: 10px 10px !important;
}
.seminarCompleteProcess.mobileView,
.seminarPretestCompleteProcess.mobileView{
    width: 100%!important;
    position: unset !important;
    float: right;
    border-radius: 0px !important;
    padding-left: 20px !important;
    overflow-y: hidden;
}
.popupLogo{
    width: 100%!important;
    max-width: 100%;
    border-bottom: 2px solid #d3d3d3 !important;
}
.popupHead{
    width: 100%!important;
    max-width: 100%;
    border-bottom: 2px solid #d3d3d3 !important;
    text-align: left !important;
}
.popupLogo img{
    height: 70px !important;
}
#examWrapper .mobileView.content-popup{
    overflow-y: scroll;
}
.mobileBody,.lgBody{
    overflow: hidden;
    height: 100%;
}
#examWrapper .content-popup:not(.mobileView) .popupHead{
    padding-left: 8px !important;
}

.playerWell #leftBox.leftBoxWrap{
    visibility: hidden;
}
#leftLoading{
    text-align: center;
}
#leftLoading img{
    width:70px;
}
.viewModeBtnHide{
    display:none !important;
}
.downloadNoteCellWait img,
.emailMaterialsCellWait img{
    width:20px;
}

.emailMaterialsCellWait{
    text-align: center;
    font-size: 1.1em;
}
.emailDisabled{
    cursor: not-allowed !important;
    color: gray !important;
}
#emailMaterialResult{
    color: green;
    padding-bottom: 5px;
}
.hideInDisplay{
    visibility:hidden !important;
}
.videoLoader {
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 32px;
}
.leftBoxTopPadding{
    padding-top: 10px !important;
}
.downloadMaterialWrap > div.lable{
    display: inline-block;
    width: 105px;
}
.downloadMaterialWrap > div.lable > i{
    float: right;
    margin-right: 7px;
    margin-top: 6px;
}
.faqHead{
    font-weight: 700 !important;
}
.faqParagraph{
    margin-bottom:22px !important;
}
.windowsAlert .alert{
    z-index:999999 !important;
}
#activity #swlCompleteWrap .content-popup{
    margin-left: 0 !important;
    top: 0% !important;
    left: 0% !important;
    width: 100% !important;    
    overflow-y:scroll !important;
    box-shadow: unset !important;
}
#activity #swlCompleteWrap .closeAll{
   display:none;
}
#swlCompleteWrap .popupLogo{
    display:none !important;
}
.vidErr{
    font-size: 16px;
    text-align: center;
    margin-top: 10%;
}
.windowReload{
    color: #22f8e4;
    font-size: 16px;
    padding: 11px 5px;
    display: block;
    width: 129px;
    margin: 0 auto;
}
.exclamationIcon{
    position: absolute;
    top: 1px;
    left: 155px;
    color: var(--button-color)!important;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 134px;
    background-color: #0000008a;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px 2px;
    position: absolute;
    z-index: 1;
    font-size: 11px;
    margin-left: 13px;
}
.tooltip .tooltiptextEmail {
    visibility: hidden;
    width: 183px;
    background-color: #0000008a;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 12px 16px;
    position: absolute;
    z-index: 1;
    font-size: 13px;
    margin-left: 10px;
}
.tooltip{
    cursor: pointer !important;
}
.tooltip:hover .tooltiptext {
    visibility: visible;
}
.tooltip:hover .tooltiptextEmail {
    visibility: visible;
}
.learningRequirementsIncomplete .subExamList:hover,
.learningRequirementsIncomplete .completeProgramTitle:hover{
    cursor:not-allowed;
}
.pageAlert{
    z-index: 999999 !important;
}
.seminarCmpltMsgIcon{
    color: var(--button-color)!important;
}
.seminarCmpltMsgFullText{
    font-size: 13px;
    color: rgb(174 174 174);
}
#invalidSeminarMsg i{
    color:#f8a732;
    font-size: 20px;
    font-weight: bold;
}
#invalidSeminarMsg{
    text-align: center;
    position: absolute;
    top: 45%;
    left: 0;
    right: 0;
    margin: auto;
    transform: translateY(-50%);
}
.notFoundTitle{
    font-size: 20px;
    font-weight: bold;
}
.notFoundDesc{
    font-size: 16px;
}
#endOfSeminarText{
    color: #0c6b7e;
    background-color: #cff3f8;
    border-color: #bceff5;
    -ms-flex-align: center!important;
    align-items: center!important;
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border-radius: 0.65rem;
    text-align: left;
}
#endOfSeminarText i{
    margin-right: 8px;
}
.saveExitBtns{
    padding-left: 10px;
    padding-right: 10px;
}
#other-resources-controller .divider.div-transparent{
    margin-top: 10px !important;
}
.materialHeading{
    margin-bottom: 0 !important;
    margin-top: 0 !important;
}
.materialRow{
    padding-bottom: 1% !important;
}

.content.subChapters:hover .zeroPercent,
.content.subChapters:hover .zeroPercent .percentcompletedSpan,
.nav .bottom .videoPpt.select .subChapters .zeroPercent,
.nav .bottom .videoPpt.select .subChapters .zeroPercent .percentcompletedSpan{
    color: #fff !important;
}
.zeroPercent ,
.zeroPercent .percentcompletedSpan{
    color: #d3d3d3;
}
.normalPercent ,
.normalPercent .percentcompletedSpan{
    color: var(--primary-color);
}
.seminarCmpltMsgFullText .windowReload{
    font-size: 15px;
    color: rgb(174 174 174);
    width: 100%;
    padding: 0;
    text-decoration: underline;
}
.seminarCmpltMsgFullText.reloadWrap{
    padding-top: 5px;
}