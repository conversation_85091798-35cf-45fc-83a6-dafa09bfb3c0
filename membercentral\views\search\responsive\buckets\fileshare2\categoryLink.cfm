<cfoutput>
    <cfset local.linkLabel = "#arguments.categoryName# (<strong>#arguments.docCount#</strong>)">
    <cfif arguments.filter_category2 gt 0>
        <cfset local.linkLabel = arguments.categoryName>
        <cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#arguments.filter_category1#|#arguments.filter_category2#|0|#arguments.appInstanceID#');">        
    <cfelseif arguments.filter_category1 gt 0>
        <cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#arguments.filter_category1#|#categoryID#|0|#arguments.appInstanceID#');">        
    <cfelse>
        <cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#arguments.categoryID#|0|0|#arguments.appInstanceID#');">
    </cfif>
    <a href="javascript:#local.filterLink#"><i class="icon-folder-close icon-large" title="Category"></i> #local.linkLabel#</a>
</cfoutput>