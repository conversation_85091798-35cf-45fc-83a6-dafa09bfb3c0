<cfinclude template="../commonJoinTrialSmithStep2JS.cfm">
<cfhtmlhead text="<style>.tsAppBodyButton {padding: 3px;margin-bottom: 10px;}.rowHead td{padding: 5px;}.tsSubscriptionWrap .leftColumn{    padding-right:30px;}.tsSubscriptionWrap  li{  list-style-type: disc;    margin-left: 30px;}</style>">
<cfoutput>
<cfif len(local.qryContentJoinTSPromotionalMessageContent.rawContent)>
	<div class="joinTrialSmith-card joinTrialSmith-mt-3 joinTrialSmith-p-3">	
		<table border="0" cellspacing="0" cellpadding="4" width="100%">
			<tbody>
				<tr valign="">
					<td class="tsAppBodyText">
						#local.qryContentJoinTSPromotionalMessageContent.rawContent#	
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</cfif>	
<div class="joinTrialSmith-card joinTrialSmith-mt-3 joinTrialSmith-p-3">
	<div id="joinTrialSmithPlanContainer" <cfif len(local.selectedPlanString)> style="display: none;"</cfif>>
		<cfif len(local.codeErr)>
			<div class="alert alert-danger planError">
				#local.codeErr#
			</div>         
		</cfif> 
		<div class="tsAppLegendTitle joinTrialSmith-mt-0">
			Choose a TrialSmith Subscription Plan
		</div>
		<div class="joinTrialSmith-mb-3">
			<form name="frmJoin" action="#arguments.event.getValue('jointrialsmithurl')#" method="POST" class="form-inline">
				<table border="0" cellspacing="0" cellpadding="4" width="100%">
					<tbody>
						<tr valign="">
							<td class="tsAppBodyText">
								<cfif len(session.cfcuser.subscriptionData['4'].strJoinTS.promoCode) is 0 >
									<p class="tsAppBodyText">If you have a Promotional Code, enter it here to view updated pricing.</p>
								<cfelseif len(session.cfcuser.subscriptionData['4'].strJoinTS.promoCode)> 
									<cfif len(local.qryPlans.codeDescription) and local.strValidate.isvalid> 
										<p class="tsAppBodyText">
											<b>#session.cfcuser.subscriptionData['4'].strJoinTS.promoCode#</b> - #local.qryPlans.codeDescription#
										</p>
									</cfif>
								</cfif>
							</td>
							<td align="right">
								<cfif len(session.cfcuser.subscriptionData['4'].strJoinTS.promoCode) and local.strValidate.isvalid>
									<button type="button" name="btnPromo" id="btnPromo" class="tsAppBodyButton" onClick="promoSubmit(0)" value="#session.cfcuser.subscriptionData['4'].strJoinTS.promoCode#">Remove Promotional Code</button>
								<cfelse>                     
									<input type="text" name="promoCode" id="promoCode" size="12" value="">          
									<button type="button" name="btnPromo" class="tsAppBodyButton" onClick="promoSubmit(1)">Check Code</button>                            
								</cfif>
							</td>
						</tr>
					</tbody>
				</table>
				<table cellspacing="0" cellpadding="1" class="tsSubscriptionWrap">
					<cfloop query="local.qryPlans">
						<cfset local.theMemberTypeID = local.qryPlans.membertypeid>
						<!--- get the best price (dont show tax in this table, so dont pass in billingstate) --->
						<cfset local.currmemtype = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='MemberType')>
						<cfset local.strPlanDetails = local.objPlans.getPlanDetails(orgcode=session.cfcuser.subscriptionData['4'].strJoinTS.tlastate,
							billingstate='', billingzip='', depomemberdataID=session.cfcuser.memberdata.depoMemberDataID, membertypeid=local.theMemberTypeID,
							action='join', CurrentMembertypeID=IIF(local.currmemtype EQ "","0",local.currmemtype), promoCode=session.cfcuser.subscriptionData['4'].strJoinTS.promoCode)>

						<tr class="rowHead">
							<td width="20" rowspan="2" valign="top">
								<cfif local.qryPlans.memberTypeStatus is 1>
									<input type="Radio" name="membertype" id="membertype" value="#local.theMemberTypeID#" <cfif session.cfcuser.subscriptionData['4'].strJoinTS.membertype is local.qryPlans.membertypeid> checked</cfif>>
								</cfif>
							</td>
							<td class="tsAppBodyText"><b>#local.qryPlans.membertype#</b> - #local.strPlanDetails.sel_PriceShown#</td>
							<td class="tsAppBodyText"><cfif val(local.strPlanDetails.sel_YouSave) gt 0><b><span class="red">(you save #DollarFormat(local.strPlanDetails.sel_YouSave)#)</span></b></cfif></td>
						</tr>
						<tr valign="top">
							<td class="tsAppBodyText leftColumn">#local.qryPlans.Description#</td>
							<td class="tsAppBodyText rightColumn">#local.qryPlans.Description2#</td>
						</tr>                            
					</cfloop>
				</table>
			</form>
		</div>
	</div>
	
	<div id="joinTrialSmithSelectedPlanContainer" class="joinTrialSmithSummary joinTrialSmith-cursor-pointer" data-jointrialsmithsummarystep="3" <cfif len(local.selectedPlanString) EQ 0> style="display:none;" </cfif>>
		<div class="joinTrialSmith-d-flex">
			<a href="##" class="joinTrialSmith-align-self-center joinTrialSmith-mr-2 joinTrialSmith-font-size-lg joinTrialSmith-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="joinTrialSmith-col">
				<div id="joinTrialSmithSelectedPlan" class="joinTrialSmith-font-size-lg joinTrialSmith-font-weight-bold">
					#local.selectedPlanString#
				</div>
			</div>
		</div>
	</div>
	<div id="joinTrialSmithStep2SaveLoading" style="display:none;"></div>
</div>
</cfoutput>