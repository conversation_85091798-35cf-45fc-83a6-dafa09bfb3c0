<cfcomponent extends="model.customPage.customPage" output="true">
	<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
	
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
    	<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);


			local.tmpField = { name="SubTypeTest", type="STRING", desc="Check for existing accepted/active/billed subscriptions of this type", value="02FA49B8-B9BF-4822-9DB8-59796C69A0E3" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="ActiveAcceptedMessage", type="STRING", desc="Message displayed when account selected has active/accepted subscription", value="Our records indicate you are already a DBA member. If you have questions about your membership, please call 214.220.7414 <NAME_EMAIL>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="BilledMessage", type="STRING", desc="Message displayed when account selected billed subscription", value="Our records indicate either your application is currently under review or you are a renewing member. If you are looking to renew, please [[click here]] to review your renewal statement. If you have questions about your membership, please call 214.220.7414 <NAME_EMAIL>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 214.220.7414 <NAME_EMAIL>." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			
			local.tmpField = { name="DonationNote", type="CONTENTOBJ", desc="Donation Note", value="DBA dues categories, except Law Student and Emeritus Member levels, include a charitable contribution of $20.00 to the Dallas Bar Foundation (DBF) and $10.00 to the Dallas Volunteer Attorney Program (DVAP). If you prefer not to contribute to one or both organizations, please send an email <NAME_EMAIL>. The amount you request will be removed from the total dues to be processed once you submit your application." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="MainSubscription", type="STRING", desc="UID for subscription tree", value="16EBB5C2-A205-486C-9300-DBF7A7B9F116" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	

			local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="DBA Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Step 1 - Please complete the following information." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2TopContent", type="CONTENTOBJ", desc="Content at top of page 2", value="Step 2 - Make your selections below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent", type="CONTENTOBJ", desc="Content at top of page 3", value="Step 3 - Please review your selections and proceed with payment." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for credit card", value="DBA_Affinipay" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCheck", type="STRING", desc="pay profile code for check", value="DBA_Pay_Later" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you for your application." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="DBA Membership Application Receipt" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
            

            StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='join',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
          
            variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
	

            switch (local.formAction) {
				case "processLookup":
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and not len(arguments.event.getTrimValue('mk',''))) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
				case "processMembershipInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}					
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}

					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
					
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>

    </cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='contact type')>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				.subRateLabel {font-weight:normal;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				var memberTypeField;

				function adjustFieldsetDisplay(contactTypeField) {
					var memType = contactTypeField.find('option:selected').text();
					switch(memType) {
					case 'Law Student':
						case 'Graduated, Awaiting Bar Results':
						case '':
							showFieldsContactType('personalInfoFieldSetHolder,communicationAddressFieldSetHolder,officeAddressFieldSetHolder,homeAddressFieldSetHolder');
							break;
						default:
showFieldsContactType('personalInfoFieldSetHolder,officeAddressFieldSetHolder,professionalInfoFieldSetHolder,homeAddressFieldSetHolder,communicationAddressFieldSetHolder,volunteeringFieldSetHolder');
							break;
					}
				}

				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}

				function validateMembershipInfoForm(){
					var arrReq = new Array();

					//make sure selected subscriptions all have selected rates.
					$('input.subCheckbox:checkbox:checked').each(function(x,item){
						var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
						var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
						if ( numRates > 0 && numRatesChecked==0) {
							arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
						}
					});

					//make sure any chosen editable rates have amounts greater than zero.

					$('input.subRateCheckbox:radio:checked').each(function(index,item){
						var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
						if ($(rateOverrideField).length) {
							var overridePrice = parseFloat($(rateOverrideField)[0].value);
							if (!overridePrice) {
								var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
								subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
								arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
							}
						}
					});


					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm(ispaymentrequired) {
					if(ispaymentrequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
					$('button[name^=btnClearmd]').prev().each(function(){
						mca_setupDatePickerField($(this).attr('id'));
					});
				});
			</script>

			<script type="text/javascript">
				function subscriptionRateOverrideBoxHandler(event) {
					var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());

					//check subscription if not already checked
					if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
						$(subRateRadioButton)[0].click();
						$(this).focus();
					} else if (subRateRadioButton) {
						$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
					}
				}

				function subscriptionCheckboxHandler() {
					if ($(this)[0].checked) {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
					} else {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
					}
				}

				function subscriptionRateRadioButtonHandler() {

					if ($(this)[0].checked) {
						var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
						var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
						var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();

						if (rateOverrideBox.length) {
							//rateoverride box is present
							rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
						}

						//put label of selected rate radio button next to subscription
						rateDescription = ' - ' + rateDescription;
						$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);

						//check subscription if not already checked
						if (!$(subCheckbox)[0].checked)
							$(subCheckbox)[0].click();
					}
				}

				function initializeAddons() {
					$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
				}

				function selectAllSubscriptionsIfRequired() {
					var addonData = $(this).data();
					// select all addons if minimum required by set is gte available count
					// hide checkboxes so they can not be unselected
					if (addonData.minallowed >= $('.subCheckbox',this).length) {
						$('.subCheckbox:not(:checked)',this).click().hide();
						$('.subCheckbox',this).hide();
					}
				}
			</script>


			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="btn btn-default" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
		
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>

					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>	
			</cfif>
		</cfif>
		
		<cfreturn local.stReturn>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>			
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						#replaceNoCase(variables.strPageFields.BilledMessage,"[[click here]]","<a href='#local.redirectLink#'>click here</a>")#
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		
		<cfset local.appCredentialingFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='62B97ED7-5F74-4B99-9F88-D04F4F805FF7', mode="collection", strData=local.strData)>
		<cfset local.personalInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='78CED6D2-E40C-40D6-BD22-621182DE7E75', mode="collection", strData=local.strData)>
		<cfset local.officeAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='1E2AB303-1D32-44AA-89AD-90D483DDADC0', mode="collection", strData=local.strData)>
		<cfset local.professionalInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='7683493D-E30A-45E9-94EF-4B88F01FB75D', mode="collection", strData=local.strData)>
		<cfset local.homeAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='833159C7-FDED-4D5D-8598-39BDD407A398', mode="collection", strData=local.strData)>
		<cfset local.communicationAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='2E996043-5F2D-4976-9EC1-B5FC386FE69E', mode="collection", strData=local.strData)>
		<cfset local.volunteeringFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='537BB2E1-FB67-4AA7-B684-6ECE64DD2D80', mode="collection", strData=local.strData)>
		
		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		
		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>
		
		<cfset local.memberPrefBillingAddress = "">
		<cfset local.memberPrefMailingAddress = "">		
		<cfloop collection="#local.communicationAddressFieldSet.strFields#" item="local.thisField">
			<cfif local.communicationAddressFieldSet.strFields[local.thisField] eq "Address for Billing">
				<cfset local.memberPrefBillingAddress = local.thisField>
			</cfif>
			<cfif local.communicationAddressFieldSet.strFields[local.thisField] eq "Address for Mailing">
				<cfset local.memberPrefMailingAddress = local.thisField>
			</cfif>			
		</cfloop>
		
		<cfset local.homeAddress = "">
		<cfloop collection="#local.homeAddressFieldSet.strFields#" item="local.thisField">
			<cfif local.homeAddressFieldSet.strFields[local.thisField] eq "Street Address">
				<cfset local.homeAddress = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.graduationDate = "">
		<cfloop collection="#local.personalInfoFieldSet.strFields#" item="local.thisField">
			<cfif local.personalInfoFieldSet.strFields[local.thisField] eq "Law School Graduation Date">
				<cfset graduationDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.lawschool2 = "">
		<cfloop collection="#local.personalInfoFieldSet.strFields#" item="local.thisField">
			<cfif local.personalInfoFieldSet.strFields[local.thisField] eq "Law School 2">
				<cfset local.lawschool2 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.contacttype = "">
		<cfloop collection="#local.appCredentialingFieldSet.strFields#" item="local.thisField">
			<cfif local.appCredentialingFieldSet.strFields[local.thisField] eq "Contact Type">
				<cfset local.contacttype = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.isPracticeLawActive = "">
		<cfloop collection="#local.appCredentialingFieldSet.strFields#" item="local.thisField">
			<cfif local.appCredentialingFieldSet.strFields[local.thisField] eq "Are You Actively Practicing Law?">
				<cfset local.isPracticeLawActive = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.isResidentOrPracticing = "">
		<cfloop collection="#local.appCredentialingFieldSet.strFields#" item="local.thisField">
			<cfif local.appCredentialingFieldSet.strFields[local.thisField] eq "Are You A Resident Or Practicing in the Dallas Area?">
				<cfset local.isResidentOrPracticing = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.licenseInAnotherState = "">
		<cfset local.firmSize = "">
		<cfset local.areaOfPractice = "">
		<cfset local.occupation = "">
		<cfloop collection="#local.professionalInfoFieldSet.strFields#" item="local.thisField">
			<cfif trim(REReplace(local.professionalInfoFieldSet.strFields[local.thisField],"[^0-9A-Za-z ]","","all")) eq "Are You Licensed in more than one state">
				<cfset local.licenseInAnotherState = local.thisField>
			<cfelseif trim(REReplace(local.professionalInfoFieldSet.strFields[local.thisField],"[^0-9A-Za-z ]","","all")) eq "Firm Size">
				<cfset local.firmSize = local.thisField>
			<cfelseif trim(REReplace(local.professionalInfoFieldSet.strFields[local.thisField],"[^0-9A-Za-z ]","","all")) eq "Area of Practice">
				<cfset local.areaOfPractice = local.thisField>
			<cfelseif trim(REReplace(local.professionalInfoFieldSet.strFields[local.thisField],"[^0-9A-Za-z ]","","all")) eq "Occupation">
				<cfset local.occupation = local.thisField>
			</cfif>
		</cfloop>
		<cfset local.officeName = "">
		<cfset local.officeAddress = "">
		<cfset local.officeCity = "">
		<cfset local.officeState = "">
		<cfset local.officeZip = "">
		<cfloop collection="#local.officeAddressFieldSet.strFields#" item="local.thisField">
			<cfif local.officeAddressFieldSet.strFields[local.thisField] eq "Office Address">
				<cfset local.officeAddress = local.thisField>
			<cfelseif local.officeAddressFieldSet.strFields[local.thisField] eq "Company or Firm Name">
				<cfset local.officeName = local.thisField>
			<cfelseif local.officeAddressFieldSet.strFields[local.thisField] eq "City">
				<cfset local.officeCity = local.thisField>
			<cfelseif local.officeAddressFieldSet.strFields[local.thisField] eq "State">
				<cfset local.officeState = local.thisField>
			<cfelseif local.officeAddressFieldSet.strFields[local.thisField] eq "Zip Code" >
				<cfset local.officeZip = local.thisField>
			</cfif>
		</cfloop>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			
			<style type="text/css">	
				.content input[type="text"] {width:206px!important;}
				.content select{width:220px!important;}
				##ProfessionalLicenseFields input[type="text"] {width:auto!important;}
				##ProfessionalLicenseFields select{width:auto!important;}
				##ProfessionalLicenseFields{width:100%!important;}
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {white-space: initial!important;}
				@media screen and (max-width: 767px){
					##content-wrapper table td {display: block;margin-bottom:0px;}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##ProfessionalLicenseFields input[type="text"] {width:206px!important;}
					##ProfessionalLicenseFields select{width:220px!important;}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{padding: 10px !important;}
				@media (min-width: 1200px){
					.eachRow{margin-bottom: 5px;}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.areaStatus{
						text-align:left;
						margin-left: 0px!important;
					}
					.areaState{text-align:left;}
					##state_table  .proLicenseLabel{display:block;}
					.wrapLeft{display: table-cell!important;}
					.span3 input {width: 90%!important;}
				}
				.wrapLeft{display:none;}
				.jsLabel{
					display:none !important;
					font-weight:700;
					font-size: 9pt;
				}
				.areaStatus{margin-left:0 !important;}
				.span3{
					margin-left:0 !important;
					margin-right:10px !important;
				}
				@media  (min-width: 767px) and  (max-width: 1200px){
					.span3 input{width: 90%!important;}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.eachRow{margin-bottom: 5px;}
					.areaState{text-align:left;}
				}
				@media (max-width: 979px) and (min-width: 768px){
					.span3{
						margin-left:0 !important;
						margin-right:10px !important;
					}
					.span3 input{width: 90%!important;}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.eachRow{margin-bottom: 5px;}
					.areaState{text-align:left;}
				}
				@media (max-width: 767px){
					.eachRow{margin-bottom: 5px;}
					##state_table  .proLicenseLabel{display:none !important;}
					.jsLabel{display:block !important;margin-top: -5px;}
				}				
			</style>
				
			<script language="javascript">
				function afterFormLoad(){					
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				function clearDateMask(){
					$('form[name=#variables.formName#] input[type=text]').each(function(){
						if($(this).val() == '__/__/____'){
							$(this).val('');
						}
					});
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					#local.appCredentialingFieldSet.jsValidation#

					<cfif len(trim(local.contacttype))>
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						if(mcSel == '') {
							arrReq[arrReq.length] = "Contact Type is required.";	
						}
						if(mcSel == 'Attorney') {
							<cfif len(trim(local.isPracticeLawActive))>
								var isPracticeLawActive = $('###variables.formName# ###local.isPracticeLawActive#').val();
								if (isPracticeLawActive.length == 0)
										arrReq[arrReq.length] = "Are You Actively Practicing Law? is required.";	
							</cfif>
							<cfif len(trim(local.isResidentOrPracticing))>
								var isResidentOrPracticing = $('###variables.formName# ###local.isResidentOrPracticing#').val();
								if (isResidentOrPracticing.length == 0)
										arrReq[arrReq.length] = "Are You A Resident Or Practicing in the Dallas Area? is required.";	
							</cfif>
						}
					</cfif>				
					

					#local.personalInfoFieldSet.jsValidation#
					
					if(mcSel == 'Law Student' || mcSel == 'Graduated, Awaiting Bar Results') {
						<cfif len(trim(local.lawSchool2))>
							var lawSchool2 = $('###variables.formName# ###local.lawSchool2#').val();
							if (lawSchool2.length == 0)
									arrReq[arrReq.length] = "Law School 2 is required.";	
						</cfif>
						<cfif len(trim(local.graduationDate))>
							var graduationDate = $('###variables.formName# ###local.graduationDate#').val();
							if (graduationDate.length == 0)
									arrReq[arrReq.length] = "Law School Graduation Date is required.";	
						</cfif>
					}					
					
					if($.trim($("###local.officeAddress#").val()).length){
						if(!_CF_hasValue(_CF_this['#local.officeCity#'], "TEXT", false)) arrReq[arrReq.length] = "Office - City is required.";
						if (_CF_this['#local.officeState#'].options[_CF_this['#local.officeState#'].selectedIndex].value.length == 0) arrReq[arrReq.length] = "Office - State is required.";
						if(!_CF_hasValue(_CF_this['#local.officeZip#'], "TEXT", false)) arrReq[arrReq.length] = "Office - Zip Code is required.";
					}
					
					if(mcSel != 'Law Student' && mcSel != 'Graduated, Awaiting Bar Results') {
						<cfif len(trim(local.firmSize))>
							var officeSize = $('###variables.formName# ###local.firmSize#').val();
							if (officeSize.length == 0)
									arrReq[arrReq.length] = "Firm Size is required.";	
						</cfif>
						<cfif len(trim(local.areaOfPractice))>
							var officeAOP = $('###variables.formName# ###local.areaOfPractice# option:selected').text();
							if (officeAOP.length == 0)
									arrReq[arrReq.length] = "Area of Practice is required.";	
						</cfif>
						<cfif len(trim(local.occupation))>
							var officeOccup = $('###variables.formName# ###local.occupation#').val();
							if (officeOccup.length == 0)
									arrReq[arrReq.length] = "Occupation is required.";	
						</cfif>
					}
					
                    #local.professionalInfoFieldSet.jsValidation#
					<cfif len(trim(local.licenseInAnotherState))>
						if(mcSel != 'Law Student' && mcSel != 'Graduated, Awaiting Bar Results') {
							var licenseInAnotherState = $('###variables.formName# ###local.licenseInAnotherState#');
							if (licenseInAnotherState.val().length == 0){
								arrReq[arrReq.length] = "Are You Licensed in more than one state? is required.";	
							}
						}
					</cfif>
					if(mcSel != 'Law Student' && mcSel != 'Graduated, Awaiting Bar Results') {
						var prof_license = $('##mpl_pltypeid').val();
						if(prof_license == "" || prof_license == null || prof_license.length < 1){
							arrReq[arrReq.length] = "Professional License is required.";
						}
					}
					
					#local.homeAddressFieldSet.jsValidation#	
					#local.communicationAddressFieldSet.jsValidation#	
					#local.volunteeringFieldSet.jsValidation#	

					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					if(prof_license != "" && prof_license != null){
						isProfLicenseSelected = true;
						$.each(prof_license,function(i,val){
							var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
							if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
							if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
						});
					}
					
					var memberPrefBillingAddress = "";
					<cfif len(trim(local.memberPrefBillingAddress))>
						memberPrefBillingAddress = $('###variables.formName# ###local.memberPrefBillingAddress# option:selected').text();
					</cfif>

					var memberPrefMailingAddress = "";
					<cfif len(trim(local.memberPrefMailingAddress))>
						memberPrefMailingAddress = $('###variables.formName# ###local.memberPrefMailingAddress# option:selected').text();
					</cfif>	
					
					if (memberPrefBillingAddress == ""){
						arrReq[arrReq.length] = "Address for Billing is required.";
					}
					if (memberPrefMailingAddress == ""){
						arrReq[arrReq.length] = "Address for Mailing is required.";
					}
					<cfif isDefined("local.homeAddress")>
						if ( (memberPrefBillingAddress == "Home" || memberPrefMailingAddress == "Home") && $.trim($('###variables.formName# ###local.homeAddress#').val()) == '')			
							arrReq[arrReq.length] = "Home Address is required.";
					</cfif>			
					
					<cfif len(trim(local.contacttype))>
						if (mcSel == 'Attorney' && !isProfLicenseSelected)
								arrReq[arrReq.length] = "Professional License is required.";	
					</cfif>

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function adjustFieldsetDisplay(contactTypeField) {
					var memType = contactTypeField.find('option:selected').text();
					switch(memType) {
					case 'Law Student': 
						case 'Graduated, Awaiting Bar Results':
						case '':
							showFieldsContactType('personalInfoFieldSetHolder,communicationAddressFieldSetHolder,officeAddressFieldSetHolder,homeAddressFieldSetHolder');
							break;
						default:
showFieldsContactType('personalInfoFieldSetHolder,officeAddressFieldSetHolder,professionalInfoFieldSetHolder,homeAddressFieldSetHolder,communicationAddressFieldSetHolder,volunteeringFieldSetHolder');
							break;
					}
				}
				function showFieldsContactType(classList) {
					$(".personalInfoFieldSetHolder").hide();
					$(".officeAddressFieldSetHolder").hide();
					$(".professionalInfoFieldSetHolder").hide();
					$(".homeAddressFieldSetHolder").hide();
					$(".communicationAddressFieldSetHolder").hide();
					$(".volunteeringFieldSetHolder").hide();
									
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]).show();							
						}			
					});							
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
				<cfset local.licenseStatus = {}>
				<cfset index=1 >
				<cfloop query="local.qryOrgProLicenseStatuses">
					<cfset local.licenseStatus[index] = local.qryOrgProLicenseStatuses.statusName>	
					<cfset index=index + 1>
				</cfloop> 
				$(document).ready(function() {
					prefillData();
					memberTypeField = $('##md_#local.memberTypeFieldInfo.columnID#');
					$(memberTypeField).change(function(){adjustFieldsetDisplay($(this));});
					adjustFieldsetDisplay(memberTypeField);
					
					$("##mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text);						
					   },
						uncheckAll: function(){
							$("##mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##state_table').siblings('div').has('input').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$("##mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text());	
							});
						}
					});	
					<cfif len(trim(local.lawSchool2))>
						$('###variables.formName# ###local.lawSchool2#').attr('size','7');
					</cfif>
					$('button[name^=btnClearmd]').prev().each(function(){
						mca_setupDatePickerField($(this).attr('id'));
					});
				});	

				function licenseChange(isChecked,val,text){
					if(isChecked){
						$("##state_table").show();
						strOption = '';
						<cfloop collection="#local.licenseStatus#" item="i" >
								strOption += '<option value="#LCase(local.licenseStatus[i])#" >#local.licenseStatus[i]#</option>';
						</cfloop>
						$("##tr_state_"+val).remove();
						$('##state_table').after('<div class="row-fluid eachRow" id="tr_state_'+val+'" >'
											+'<div style="margin-bottom: -1px;margin-top: 15px;" class="row-fluid eachRow jsLabel">State Name</div>'
											+'<div class="span3 areaState" >'
											+'<span  class="tsAppBodyText">'+text+'</span>'
											+'</div>'
											+'<div class="row-fluid eachRow jsLabel" >#variables.strProfLicenseLabels.profLicenseNumberLabel#</div>'
											+'<div class="span3" >'
											+'<input class="licensenumber" size="13" maxlength="13" name="mpl_'+val+'_licensenumber"id="mpl_'+val+'_licensenumber" type="text" value="" />'
											+'<input name="mpl_'+val+'_licensename" id="mpl_'+val+'_licensename" type="hidden" value="'+text+'" />'
											+'</div>'
											+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</div>'
											+'<div class="span3" >'
											+'<input size="13" maxlength="10" name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="" class="tsAppBodyText" autocomplete="off"/>'
											+'</div>'
											+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseStatusLabel#</div>'
											+'<div class="span3 areaStatus" >'
											+'<select name="mpl_'+val+'_status" id="mpl_'+val+'_status"><option value="">Please Select</option>'+strOption+'</select>'
											+'</div>'
											+'</div>');

						if ($("##tr_state_"+val).is(':visible') &&  $('##mpl_'+val+'_activeDate').is(':visible')) {
							mca_setupDatePickerField('mpl_'+val+'_activeDate');
						}
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
					}else{
						$("##tr_state_"+val).remove();
						if($('##state_table').siblings('div').has('input').length == 0){
							$("##state_table").hide();
						}
					}
					
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="clearDateMask();return validateMemberInfoForm()">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<cfinclude template="/model/cfformprotect/cffp.cfm">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid TitleText" id="ApplicationTitleId"><h2>#variables.strPageFields.FormTitle#</h2><br/></div>
					</cfif>
					<div id="content-wrapper" class="row-fluid">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="ApplicationIntroText">#variables.strPageFields.Step1TopContent#</div>
						</cfif>				
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.appCredentialingFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer" id="appCredentialingFieldSet">
								#local.appCredentialingFieldSet.fieldSetContent#
							</div>
						</div>
						<span class="personalInfoFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.personalInfoFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.personalInfoFieldSet.fieldSetContent#
								</div>
							</div>	
						</span>
						<span class="officeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.officeAddressFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.officeAddressFieldSet.fieldSetContent#															
								</div>
							</div>
						</span>
						<span class="professionalInfoFieldSetHolder">	
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.professionalInfoFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.professionalInfoFieldSet.fieldSetContent#																
								</div>
							</div>
							
							<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
							<table cellpadding="3" border="0" cellspacing="0">		
								<tr class="top">
									<th class="tsAppBodyText" colspan="3" align="left">
										&nbsp;
									</th>
								</tr>
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText">Professional License:</td>
									<td class="tsAppBodyText">
										<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
											<cfloop query="local.qryOrgPlTypes">
												<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
												<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr class="top">
									<td class="tsAppBodyText" width="10"></td>
									<td class="tsAppBodyText"></td>
									<td class="tsAppBodyText"></td>
								</tr>
							</table>
							<table cellpadding="3" border="0" cellspacing="0" id="ProfessionalLicenseFields">
								<tr>
									<td class="tsAppBodyText wrapLeft" width="">&nbsp;</td>
									<td>
										<div class="row-fluid" id="state_table" style="display:none;" >
											<div class="span3 proLicenseLabel" >
												State Name
											</div>
											<div class="span3 proLicenseLabel"  >
												#variables.strProfLicenseLabels.profLicenseNumberLabel#
											</div>
											<div class="span3 proLicenseLabel"  >
												#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)
											</div>
											<div class="span3 proLicenseLabel" >
												#variables.strProfLicenseLabels.profLicenseStatusLabel#
											</div>
										</div>
									
											
										<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
											<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
												<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
												<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
												<cfset  local.license_status  = 'Active'>
												<div class="row-fluid"  >
													<div class="span3" id="tr_state_#local.thisItem#">
														<span  class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</span>
														
													</div>
													<div class="span3" >
														<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
														<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
														
													</div>
													<div class="span3" >
														<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" autocomplete="off"/>
															<cfsavecontent variable="local.datejs">
																<cfoutput>
																	<script language="javascript">
																		$(document).ready(function() {
																			mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
																		});
																	</script>
																	<style type="text/css">
																	##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
																	</style>
																</cfoutput>
															</cfsavecontent>
															<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
														
													</div>
													<div class="span3" >
														<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText">
																<option value="">Please Select</option>
																<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>																	
															</select>	
														
													</div>
												</div>
												
											</cfloop>
										</cfif>	
									</td>
								</tr>					
							</table>
						</span>	
						<span class="homeAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.homeAddressFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.homeAddressFieldSet.fieldSetContent#																
								</div>
							</div>	
						</span>
						<span class="communicationAddressFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.communicationAddressFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.communicationAddressFieldSet.fieldSetContent#																
								</div>
							</div>	
						</span>
						<span class="volunteeringFieldSetHolder">
							<div class="row-fluid">
								<div class="span12 tsAppSectionHeading">#local.volunteeringFieldSet.fieldSetTitle#</div>
								<div class="tsAppSectionContentContainer">	
								#local.volunteeringFieldSet.fieldSetContent#																
								</div>
							</div>	
						</span>
						
						
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					</div>
					#application.objWebEditor.showEditorHeadScripts()#
					<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = arguments.rc.mc_siteinfo.orgid, includeTags=0)>
					<script language="javascript">
						$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
							function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
								var _address = _this.val();
								
								if(_address.length >0){
									if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
										$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
									}
								} else {
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
								}
							}
							
							$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
							});
						</cfloop>
					});
					
						function editContentBlock(cid,srid,tname) {
							var editMember = function(r) {
								if (r.success && r.success.toLowerCase() == 'true') {
									$('##frmmd_'+cid).html(r.html);
									var x = div.getElementsByTagName("script");
									for(var i=0;i<x.length;i++) eval(x[i].text); 
								}
							};
							var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
							TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
						}
					</script>
					
					
				</form>
			
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
   <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		<cfset local.objSaveMember.setMemberType(memberType='User')>
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
	
  	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>	
		
		<cfset local.12combinedMemberAndDallasAsianAmericanBarAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "8E385EFE-BD65-4943-A845-5F0578DC4595",
						frequencyShortName="F")/>
		<cfset local.12combinedMemberAndDallasHispanicBarAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "64E2B25E-F6B2-4D09-AC44-9CBAABF46882",
						frequencyShortName="F")/>
		<cfset local.12combinedMemberAndJLTurnerLegalAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "8B9EEC0A-A2F3-4CCD-9716-7B53E32CC609",
						frequencyShortName="F")/>
		<cfset local.25combinedMemberAndDallasAsianAmericanBarAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "EC432EEE-BFEF-46F5-B1F2-61D8631757C8",
						frequencyShortName="F")/>						
		<cfset local.25combinedMemberAndDallasHispanicBarAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "67AF1BDD-3265-450B-A29A-5ECDFA17299D",
						frequencyShortName="F")/>
		<cfset local.25combinedMemberAndJLTurnerLegalAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "B7B3E63F-06FA-4E4A-8867-50A743DFC175",
						frequencyShortName="F")/>
		<cfset local.5combinedMemberAndDallasAsianAmericanBarAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "5F61D2A6-07FA-4C58-8651-A4EAD86B3CBB",
						frequencyShortName="F")/>						
		<cfset local.5combinedMemberAndDallasHispanicBarAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "79B49606-DC45-408A-A80C-FFB23EFD47C7",
						frequencyShortName="F")/>
		<cfset local.5combinedMemberAndJLTurnerLegalAssociation = application.objCustomPageUtils.sub_getSRateInfo(
						siteID=variables.siteID,
						rateUID = "9D79E608-171F-4315-A6E5-D66B062F26A7",
						frequencyShortName="F")/>
		<cfset local.DallasAsianAmericanBarAssociation = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = "A07222A4-**************-FCA28A8FB1C2")/>
		<cfset local.DallasHispanicBarAssociation = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = "CBD0BB1D-1A29-455D-A55A-BB3B6D577AD4")/>
		<cfset local.JLTurnerLegalAssociation = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = "733CD755-9545-4678-9A53-46CA866B551C")/>
		<cfset local.sistersBar = application.objCustomPageUtils.sub_getSetSubscriptions(
						siteID=variables.siteID,
						setUID = "91D11FC8-EE62-434C-90EE-F666B136E06F")/>

		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
		
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	


		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
				 	function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
						$('button[name^=btnClearmd]').prev().each(function(){
							mca_setupDatePickerField($(this).attr('id'));
						});
					}
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();	

						<cfif val(local.subscriptionID)>
							if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
								arrReq[arrReq.length] = " Select Membership.";
							}
						</cfif>		
						
						$('div [data-setname="Committees"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
							var minlp = $(this).data("minallowed");
							if($('input:checkbox:checked',this).length == 0){
								arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $('legend',this).eq(0).text()+".";
							}
						});

						$('div [data-setname="Committees"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
							var maxlp = $(this).data("maxallowed");
							if($('input:checkbox:checked',this).length > maxlp){
								arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $('legend',this).eq(0).text()+".";
							}
						});

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}

						#local.result.jsValidation#

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function enableSubscription(){
						$.each('#ListRemoveDuplicates(valueList(local.sistersBar.subscriptionID))#'.split(","), function( index, value ) {
							$('input[type=checkbox][name=sub'+value+']').removeAttr('disabled').prop("checked", false).parent('label').show();
						});
					}
					$(document).ready(function() {

						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.12combinedMemberAndDallasAsianAmericanBarAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.DallasAsianAmericanBarAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();							
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.12combinedMemberAndDallasHispanicBarAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.DallasHispanicBarAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.12combinedMemberAndJLTurnerLegalAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.JLTurnerLegalAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.25combinedMemberAndDallasAsianAmericanBarAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.DallasAsianAmericanBarAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();							
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.25combinedMemberAndDallasHispanicBarAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.DallasHispanicBarAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.25combinedMemberAndJLTurnerLegalAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.JLTurnerLegalAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.5combinedMemberAndDallasAsianAmericanBarAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.DallasAsianAmericanBarAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();							
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.5combinedMemberAndDallasHispanicBarAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.DallasHispanicBarAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();
						});
						$('input[type=radio][id=sub#local.subscriptionID#_rate#local.5combinedMemberAndJLTurnerLegalAssociation.rateID#_F]').click(function(){
							enableSubscription();
							$('input[type=checkbox][name=sub#local.JLTurnerLegalAssociation#]').removeAttr('disabled').attr('disabled','disabled').parent('label').hide();
						});
						if($("input[name='sub#local.subscriptionID#_rate'][checked='checked']").length){
							$("input[name='sub#local.subscriptionID#_rate'][checked='checked']").trigger("click");
						}						
					});
				</script>
 			</cfoutput>
 		</cfsavecontent>
 		<cfhtmlhead text="#local.resultHtmlHeadText#">
				
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
			
			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>
			
			<cfform name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#
					</div>
				</div>
			</div>
			<div class="container-fluid">
				<div class="row-fluid">
					<div class="well">
						#variables.strPageFields.DonationNote#
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="btn-default tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.MainSubscription)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
	
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.MainSubscription)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		<cfset local.arrPayMethods = []>
		<cfif len(variables.strPageFields.ProfileCodePayCC) gt 0 AND variables.strPageFields.ProfileCodePayCC neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodePayCC)>		
		</cfif>
		<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
			<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
		</cfif>

		<cfset local.strReturn = 
			application.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
			
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
		
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
				<script type="text/javascript">
					$('##mccfdiv_#variables.strPageFields.ProfileCodePayCC# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
												
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">
	
			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
				<div class="tsAppSectionContentContainer">						
					#local.strResult.formContent#
					<br/><br/>
				</div>
			</div>	
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Total Price</div><br/>
				<div class="tsAppSectionContentContainer">						
					#dollarFormat(local.strResult.totalFullPrice)#
					<br/><br/>					
				</div>
			</div>
			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton btn-default" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
		
		<!--- create subscriptions--->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.MainSubscription)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = arguments.rc)>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>	

		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=true)>
	
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
	
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
		
    <cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberTypeFieldInfo = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='contact type')>
		<cfset local.contactTypeFieldName = "md_"&local.memberTypeFieldInfo.columnID>

		<cfset local.appCredentialingFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='62B97ED7-5F74-4B99-9F88-D04F4F805FF7', mode="confirmation", strData=arguments.rc)>
		<cfset local.personalInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='78CED6D2-E40C-40D6-BD22-621182DE7E75', mode="confirmation", strData=arguments.rc)>
		<cfset local.officeAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='1E2AB303-1D32-44AA-89AD-90D483DDADC0', mode="confirmation", strData=arguments.rc)>
		<cfset local.professionalInfoFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='7683493D-E30A-45E9-94EF-4B88F01FB75D', mode="confirmation", strData=arguments.rc)>
		<cfset local.homeAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='833159C7-FDED-4D5D-8598-39BDD407A398', mode="confirmation", strData=arguments.rc)>
		<cfset local.communicationAddressFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='2E996043-5F2D-4976-9EC1-B5FC386FE69E', mode="confirmation", strData=arguments.rc)>
		<cfset local.volunteeringFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='537BB2E1-FB67-4AA7-B684-6ECE64DD2D80', mode="confirmation", strData=arguments.rc)>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfset local.contactType = "">
		<cfloop array="#local.memberTypeFieldInfo.columnValueArr#" index="local.thisOption">
			<cfif local.thisOption.VALUEID EQ arguments.rc["#local.contactTypeFieldName#"]>
				<cfset local.contactType = local.thisOption.COLUMNVALUESTRING>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div>
						<div>#variables.strPageFields.ConfirmationContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
					<div>
						<div>You have received an application for membership through the online membership application form.</br></br></div>
					</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.appCredentialingFieldSet.fieldSetContent#
				#local.personalInfoFieldSet.fieldSetContent#
				#local.officeAddressFieldSet.fieldSetContent#

				<cfif local.contactType NEQ "Law Student" AND local.contactType NEQ "Graduated, Awaiting Bar Results">
					#local.professionalInfoFieldSet.fieldSetContent#

					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
						</tr>				
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
												<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
													<thead>
														<tr valign="top">
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
														</tr>
													</thead>
													<tbody>
													<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
														<tr id="tr_state_#local.key#">
															<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#DateFormat(arguments.rc['mpl_#local.key#_activeDate'],'mm/dd/yyyy')#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
														</tr>
													</cfloop>
													</tbody>
												</table>
											</cfif>	
										</td>
									</tr>						
								</table>
							</td>
						</tr>
					</table>
					<br>
				</cfif>
				#local.homeAddressFieldSet.fieldSetContent#		
				#local.communicationAddressFieldSet.fieldSetContent#
				<cfif local.contactType NEQ "Law Student" AND local.contactType NEQ "Graduated, Awaiting Bar Results">
					#local.volunteeringFieldSet.fieldSetContent#
				</cfif>

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-top:1px solid ##999;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>	
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to DBA",
			emailhtmlcontent = local.confirmationHTMLToMember,
			siteID = variables.siteID,
			memberID = val(variables.useMID),
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub>
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to DBA", emailContent=local.confirmationHTMLToStaff)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
					
				</style>
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
		
		<cfreturn local.confirmationHTMLToMember>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>