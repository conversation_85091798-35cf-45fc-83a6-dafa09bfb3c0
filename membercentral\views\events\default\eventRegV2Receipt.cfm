<cfinclude template="../commonEventRegReceiptJS.cfm">
<cfinclude template="../commonEventRegStyles.cfm">

<cfoutput>
<div class="evRegV2" class="tsAppBodyText">
	<div class="evreg-d-flex evreg-mb-2">
		<div class="evreg-font-size-xl evreg-font-weight-bold">Registration Complete</div>
		<a href="javascript:window.print();" class="evreg-ml-auto"><i class="icon-print icon-large"></i></a>
	</div>
	<div class="evreg-mb-5">
		You have completed the registration(s) listed in the payment receipt below. A detailed confirmation of each registration will be sent to the email address(es) on file for the registrant(s).
	</div>

	<div class="evreg-d-flex evreg-mb-5">
		<div class="evreg-col">
			<form name="frmResendReceipt" id="frmResendReceipt" onsubmit="sendReceipt();return false;">
				<div class="evreg-d-flex">
					<div class="evreg-col-auto evreg-align-self-center">Email this receipt to: </div>
					<div class="evreg-col-auto">
						<div class="evreg-input-append">
							<input type="text" name="sendToEmail" id="sendToEmail" class="evreg-formcontrol" value="" size="30" value="" placeholder="Email Address" maxlength="200" autocomplete="off">
							<button type="submit" name="btnResendReceipt" id="btnResendReceipt" class="evreg-add-on evreg-font-size-md evreg-appendbtn" style="width:65px!important;">Send</button>
						</div>
						<div id="evRegSendPmtReceiptMsg" class="evreg-font-size-sm" style="display:none;"></div>
					</div>
				</div>
			</form>
		</div>
		<a href="#arguments.event.getValue('mainurl')#" class="evreg-ml-auto evreg-text-decoration-none evreg-font-size-sm">
			<i class="icon-arrow-left"></i> Back to Events
		</a>
	</div>

	<cfif local.keyExists("evRegV2") AND local.evRegV2.keyExists("regCart") AND arrayLen(local.evRegV2.regCart)>
		<div class="alert evreg-mb-3">
			<a href="/?pg=events&regcartv2">Please return to the cart to complete your other registration(s).</a>
		</div>
	</cfif>

	<div class="evreg-d-flex evreg-mb-3">
		<div class="evreg-font-size-xl evreg-font-weight-bold">Registration(s)</div>
		<cfif local.strReceipt.keyExists("qryAdditionalFees")>
			<div class="evreg-card evreg-ml-auto" style="box-shadow:none;border:none;background-color:##f7f7f7;">
				<div class="evreg-card-body" style="width:220px;">
					<div class="evreg-d-flex evreg-font-weight-bold evreg-mb-2">
						<div class="evreg-col">Subtotal:</div>
						<div class="evreg-ml-auto">#DollarFormat(local.strReceipt.totalAmountOnReceipt)##local.displayedCurrencyType#</div>
					</div>
					<div class="evreg-d-flex evreg-font-weight-bold evreg-mb-2">
						<div class="evreg-col">#local.strReceipt.qryAdditionalFees.additionalFeesLabel#:</div>
						<div class="evreg-ml-auto">#DollarFormat(local.strReceipt.qryAdditionalFees.additionalFees)##local.displayedCurrencyType#</div>
					</div>
					<div class="evreg-d-flex evreg-font-weight-bold">
						<div class="evreg-col">Total:</div>
						<div class="evreg-ml-auto">#DollarFormat(local.strReceipt.qryPaymentTransaction.amount)##local.displayedCurrencyType#</div>
					</div>
				</div>
			</div>
		<cfelse>
			<div class="evreg-ml-auto evreg-font-weight-bold evreg-font-size-xl">Total: #DollarFormat(local.strReceipt.totalAmountOnReceipt)##local.displayedCurrencyType#</div>
		</cfif>
	</div>

	<cfoutput query="local.strReceipt.qryItemsForReceiptSorted" group="memberID">
		<div class="evreg-card evreg-mt-3">
			<div class="evreg-card-header evreg-bg-whitesmoke evreg-pb-1">
				<div class="evreg-font-size-lg evreg-font-weight-bold">#local.strReceipt.qryItemsForReceiptSorted.memberName#</div>
			</div>
			<div class="evreg-card-body">
				<cfset local.showConfirmation = true>

				<cfif local.strReceipt.keyExists("qryRegistrationCap")>
					<cfquery name="local.qryMemEventsCount" dbtype="query">
						SELECT COUNT(eventID) AS eventsCount
						FROM [local].strReceipt.qryItemsForReceiptSorted
						WHERE memberID = #val(local.strReceipt.qryItemsForReceiptSorted.memberID)#
					</cfquery>

					<cfquery name="local.qryRegCapReachedEventsCount" dbtype="query">
						SELECT COUNT(eventID) AS eventsCount
						FROM [local].strReceipt.qryRegistrationCap
						WHERE memberID = #val(local.strReceipt.qryItemsForReceiptSorted.memberID)#
					</cfquery>
					<cfset local.showConfirmation = val(local.qryRegCapReachedEventsCount.eventsCount) NEQ local.qryMemEventsCount.recordCount>
				</cfif>

				<cfif local.showConfirmation>
					<div id="evRegConfirmSummary#local.strReceipt.qryItemsForReceiptSorted.memberID#" class="evreg-mb-3">
						Confirmation emailed to 
						<a href="##" class="evreg-text-decoration-none evreg-ml-2" onclick="editRegConfirmEmail(#local.strReceipt.qryItemsForReceiptSorted.memberID#);return false;" style="border-bottom:1px solid;">
							<i class="icon-pencil evreg-font-size-sm"></i> #local.strReceipt.qryItemsForReceiptSorted.email#
						</a>
					</div>
					<div id="evRegConfirmFrmContainer#local.strReceipt.qryItemsForReceiptSorted.memberID#" class="evreg-mb-3" style="display:none;">
						<form name="frmResendConfirm#local.strReceipt.qryItemsForReceiptSorted.memberID#" id="frmResendConfirm#local.strReceipt.qryItemsForReceiptSorted.memberID#" onsubmit="sendRegConfirmMail(#local.strReceipt.qryItemsForReceiptSorted.memberID#);return false;">
							<div class="evreg-d-flex">
								<div class="evreg-col-auto evreg-align-self-center">Send confirmation to: </div>
								<div class="evreg-col-auto">
									<div class="evreg-input-append">
										<input type="text" name="regConfirmEmail#local.strReceipt.qryItemsForReceiptSorted.memberID#" id="regConfirmEmail#local.strReceipt.qryItemsForReceiptSorted.memberID#" class="evreg-formcontrol" value="" size="30" value="" placeholder="Email Address" maxlength="200" autocomplete="off">
										<button type="submit" name="btnResendConfirm#local.strReceipt.qryItemsForReceiptSorted.memberID#" id="btnResendConfirm#local.strReceipt.qryItemsForReceiptSorted.memberID#" class="evreg-add-on evreg-font-size-md evreg-appendbtn" style="width:65px!important;">Send</button>
									</div>
									<div id="evRegSendConfirmErr#local.strReceipt.qryItemsForReceiptSorted.memberID#" class="evreg-text-danger evreg-font-size-sm" style="display:none;"></div>
								</div>
							</div>
						</form>
					</div>
				</cfif>
				
				<cfset local.thisRowNum = 0>
				<cfoutput>
					<cfset local.thisRowNum++>
					<cfset local.showRegRate = true>

					<div id="evRegKey#local.strReceipt.qryItemsForReceiptSorted.itemKey#" class="evreg-d-flex evreg-mb-3 evRegReceiptItem">
						<div class="evreg-col-auto evreg-font-weight-bold">#local.thisRowNum#.</div>
						<div class="evreg-col">
							<div class="evreg-font-weight-bold">
								#encodeForHTML(local.strReceipt.qryItemsForReceiptSorted.eventName)#
							</div>
							<cfif len(local.strReceipt.qryItemsForReceiptSorted.eventSubTitle)><div class="evreg-font-size-sm evreg-text-dim">#encodeForHTML(local.strReceipt.qryItemsForReceiptSorted.eventSubTitle)#</div></cfif>
							<div class="evreg-font-size-sm evreg-mt-1">
								#local.strReceipt.strRegEvent[local.strReceipt.qryItemsForReceiptSorted.eventID].eventtime#
							</div>
							<div class="evreg-mt-1 evreg-font-weight-bold">
								<i>#local.strReceipt.qryItemsForReceiptSorted.ratename#</i>
							</div>
							<cfif structKeyExists(local.strReceipt.strRegEvent[local.strReceipt.qryItemsForReceiptSorted.eventID],"evRegURL")>
								<div class="evreg-font-size-sm evreg-mt-2">
									<a href="#local.strReceipt.strRegEvent[local.strReceipt.qryItemsForReceiptSorted.eventID].evRegURL#" style="border-bottom:1px solid;text-decoration:none;" target="_blank">+ Register Someone Else</a>
								</div>
							</cfif>
							<cfif local.strReceipt.keyExists("qryOversold")>
								<cfquery name="local.qryThisRegOverSoldItems" dbtype="query">
									SELECT message
									FROM [local].strReceipt.qryOversold
									WHERE eventID = #val(local.strReceipt.qryItemsForReceiptSorted.eventid)#
									AND memberID = #val(local.strReceipt.qryItemsForReceiptSorted.memberID)#
									ORDER BY rowID
								</cfquery>
								<cfif local.qryThisRegOverSoldItems.recordcount>
									<table width="100%" style="margin-top:10px; padding-top:4px;">
									<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;border:1px solid;margin:4px 0px;padding:15px 10px 15px 10px;color:##9F6000;background-color:##FEEFB3;">
										<b>Note:</b> We changed this registration due to availability:
										<ul>
										<cfloop query="local.qryThisRegOverSoldItems">
											<li>#local.qryThisRegOverSoldItems.message#</li>
										</cfloop>
										</ul>
									</td>
									</tr>
									</table>
								</cfif>
							</cfif>
							<cfif local.strReceipt.keyExists("qryRegistrationCap")>
								<cfquery name="local.qryThisRegInRegCaps" dbtype="query">
									SELECT memberID
									FROM [local].strReceipt.qryRegistrationCap
									WHERE eventID = #val(local.strReceipt.qryItemsForReceiptSorted.eventid)#
									AND memberID = #val(local.strReceipt.qryItemsForReceiptSorted.memberID)#
								</cfquery>
								<cfif local.qryThisRegInRegCaps.recordcount>
									<table width="100%" style="padding-top:4px;">
									<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;border:1px solid;margin:4px 0px;padding:15px 10px 15px 10px;color:##9F6000;background-color:##FEEFB3;">
										<b>Note:</b> This event sold out before your checkout was completed.
									</td>
									</tr>
									</table>
									<cfset local.showRegRate = false>
								</cfif>
							</cfif>
						</div>
						<div class="evreg-col-auto evreg-w-25 evreg-text-right">
							<cfif local.showRegRate>
								<div class="evreg-d-flex evreg-mb-2">
									<span class="evreg-col evreg-text-dim evreg-font-size-sm">Add to Calendar:</span>
									<div class="evreg-col-auto evreg-ml-auto">
										<a href="#local.strReceipt.strRegEvent[local.strReceipt.qryItemsForReceiptSorted.eventID].icalURL#" class="evreg-text-decoration-none evreg-ml-1 evreg-mr-1" target="_blank" onclick="return handleGtagCalendarAction(#local.strReceipt.qryItemsForReceiptSorted.eventID#, `#encodeForHTML(local.strReceipt.qryItemsForReceiptSorted.eventName)#`, 'Outlook, Yahoo, iCAL, or Other Calendar');">
											<i class="icon-windows icon-large evreg-text-dim tooltip-icon" title="Outlook Calendar"></i>
										</a>
										<a href="#local.strReceipt.strRegEvent[local.strReceipt.qryItemsForReceiptSorted.eventID].gcalURL#" class="evreg-text-decoration-none evreg-ml-1 evreg-mr-1" target="_blank" onclick="return handleGtagCalendarAction(#local.strReceipt.qryItemsForReceiptSorted.eventID#, `#encodeForHTML(local.strReceipt.qryItemsForReceiptSorted.eventName)#`, 'Google Calendar');">
											<i class="icon-google-plus-sign icon-large evreg-text-dim tooltip-icon" title="Google Calendar"></i>
										</a>
										<a href="#local.strReceipt.strRegEvent[local.strReceipt.qryItemsForReceiptSorted.eventID].icalURL#" class="evreg-text-decoration-none evreg-ml-1 evreg-mr-1" target="_blank" onclick="return handleGtagCalendarAction(#local.strReceipt.qryItemsForReceiptSorted.eventID#, `#encodeForHTML(local.strReceipt.qryItemsForReceiptSorted.eventName)#`, 'Apple Calendar');">
											<i class="icon-apple icon-large evreg-text-dim tooltip-icon" title="iCal Calendar"></i>
										</a>
									</div>
								</div>
								<span class="evreg-font-weight-bold evreg-font-size-lg">#DollarFormat(local.strReceipt.qryItemsForReceiptSorted.discountAppliedTotal)##local.displayedCurrencyType#</span>
							<cfelse>
								N/A
							</cfif>
						</div>
					</div>
				</cfoutput>
			</div>
		</div>
	</cfoutput>

	<div class="evreg-card evreg-mt-5">
		<div class="evreg-card-header evreg-bg-whitesmoke evreg-pb-1">
			<div class="evreg-font-size-lg evreg-font-weight-bold">Payment Information</div>
		</div>
		<div class="evreg-card-body evreg-pb-2">
			<cfif local.strReceipt.totalAmountOnReceipt gt 0 and local.strReceipt.qryPaymentTransaction.recordcount gt 0>
				#dollarFormat(local.strReceipt.qryPaymentTransaction.amount)# #local.strReceipt.qryPaymentTransaction.detail#<br/>
				Payment Date: #dateformat(local.strReceipt.qryPaymentTransaction.transactionDate,"m/d/yyyy")# #timeformat(local.strReceipt.qryPaymentTransaction.transactionDate,"h:mm tt")#
			<cfelseif local.strReceipt.qryPaymentTransaction.recordcount is 0 and local.strReceipt.qryPaymentGateway.gatewayID is 11>
				No payment was made.
				<cfif local.strReceipt.totalAmountOnReceipt gt 0>
					<br/><br/>
					<b>Payment instructions:</b><br/>
					<cfif len(local.strReceipt.qryPaymentGateway.paymentInstructions)>
						#local.strReceipt.qryPaymentGateway.paymentInstructions#
					<cfelse>
						No instructions have been provided. Contact the association for payment instructions.
					</cfif>
				</cfif>
			<cfelseif local.strReceipt.totalAmountOnReceipt gt 0 and local.strReceipt.qryPaymentTransaction.recordcount is 0>
				No payment was made.
			<cfelse>
				No payment was due.
			</cfif>
			<br/><br/>
			#local.strReceipt.PurchaserName#<br/>
			<cfif len(local.strReceipt.PurchaserCompany)>#local.strReceipt.PurchaserCompany#<br/></cfif>
			#local.strReceipt.PurchaserAddress#
		</div>
	</div>
</div>
</cfoutput>