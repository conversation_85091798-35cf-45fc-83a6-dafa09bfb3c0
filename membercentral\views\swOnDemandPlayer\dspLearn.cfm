<cfoutput>
<div class="row-fluid">
	<div class="span12">
		<div class="navbar">
		  <div class="navbar-inner">
		    <a class="brand" href="##">Learn</a>
		  </div>
		</div>
	</div>
</div>

<div class="row-fluid pageContent">
	<div class="span7">
		<div class="videoPlayer" id="videoPlayer">
			<div id="jp_container_1" class="jp-video jp-video-270p">
			<div class="jp-type-single">
				<div id="jquery_jplayer_1" class="jp-jplayer"></div>
				<div class="jp-gui">
					<div class="jp-video-play">
						<a href="javascript:;" class="jp-video-play-icon" tabindex="1">play</a>
					</div>
					<div class="jp-interface">
						<div class="jp-progress">
							<div class="jp-seek-bar">
								<div class="jp-play-bar"></div>
							</div>
						</div>
						<div class="jp-current-time"></div>
						<div class="jp-duration"></div>
						<div class="jp-controls-holder">
							<ul class="jp-controls">
								<li><a href="javascript:;" class="jp-play" tabindex="1">play</a></li>
								<li><a href="javascript:;" class="jp-pause" tabindex="1">pause</a></li>
								<li><a href="javascript:;" class="jp-stop" tabindex="1">stop</a></li>
								<li><a href="javascript:;" class="jp-mute" tabindex="1" title="mute">mute</a></li>
								<li><a href="javascript:;" class="jp-unmute" tabindex="1" title="unmute">unmute</a></li>
								<li><a href="javascript:;" class="jp-volume-max" tabindex="1" title="max volume">max volume</a></li>
							</ul>
							<div class="jp-volume-bar">
								<div class="jp-volume-bar-value"></div>
							</div>
							<ul class="jp-toggles">
								<li><a href="javascript:;" class="jp-full-screen" tabindex="1" title="full screen">full screen</a></li>
								<li><a href="javascript:;" class="jp-restore-screen" tabindex="1" title="restore screen">restore screen</a></li>
								<li><a href="javascript:;" class="jp-repeat" tabindex="1" title="repeat">repeat</a></li>
								<li><a href="javascript:;" class="jp-repeat-off" tabindex="1" title="repeat off">repeat off</a></li>
							</ul>
						</div>
						<div class="jp-title">
							<ul>
								<li id="videoTitle">Big Buck Bunny Trailer</li>
							</ul>
						</div>
					</div>
				</div>
				<div class="jp-no-solution">
					<span>Update Required</span>
					To play the media you will need to either update your browser to a recent version or update your <a href="http://get.adobe.com/flashplayer/" target="_blank">Flash plugin</a>.
				</div>
			</div>
		</div>	
	</div>
	
	<hr />
	
	<div class="">
		<cfloop array="#attributes.data.seminardata.titlearray#" index="title">
			<ul class="nav nav-list well span12 ">
				<li class="nav-header">#title.titleName#</li>
				<cfloop from="1" to="#ArrayLen(title.filearray)#" index="i">
					<cfif title.filearray[i].type eq 'video'>
						<li><a href="##" class="videoTitle" videoID="#title.filearray[i].fileid#"><i class="icon-film"></i> - #title.filearray[i].filetitle# <cfif structKeyExists(title.filearray[i], 'syncarray') && arrayLen(title.filearray[i].syncarray)> and Slides</cfif></a></li>
					</cfif>
				</cfloop>				
			</ul>
		</cfloop>
	</div>
</div>
	<div class="span5" >
		<div class="well" id="slideController">
			<div class="span5">
				<a href="##" role="button" class="btn" id="btnPreviousSlide"><i class="icon-step-backward"></i></a>		
				<a href="##" role="button" class="btn" id="btnToggleSync"><i class="icon-pause"></i></a>	
				<a href="##" role="button" class="btn" id="btnNextSlide"><i class="icon-step-forward"></i></a>				
			</div>
			<div class="span5">
				<div class="progress progress-warning">
					<div id="slideProgressBar" class="bar" style="width: 60%">Slide 6/10</div>
				</div>
			</div>
		</div>
		
		<div class="well" id="slidePlayer" style="height: 300px;" >
			**VIDEO SLIDES PLACEHOLDER**
		</div>
	</div>
</div>
<div class="row-fluid pageContent">
	<div class="well span12">
		Banner bottom
	</div>
</div>
</cfoutput>

<script>
	//Gather sync and slide data
	//TODO: do this dynamically when changing video
	//var syncPoints = JSON.parse('<cfoutput>#serializejson(attributes.data.seminardata.titlearray[1].fileArray[1].syncArray)#</cfoutput>');
	//var slideData = '<cfoutput>#REReplace(attributes.data.seminardata.titlearray[1].fileArray[2].fileurl,"#chr(13)#|#chr(9)#|\n|\r","","ALL")#</cfoutput>';
	var videoSlideData = {};
	var videoSlideDataContent;
	<cfloop array="#attributes.data.seminardata.titlearray#" index="title">
		<cfloop from="1" to="#ArrayLen(title.fileArray)#" index="i">
			<cfif title.filearray[i].type eq 'video'>
				videoSlideDataContent = {};
				videoSlideDataContent.filetitle = "<cfoutput>#title.filearray[i].filetitle#</cfoutput>";
				videoSlideDataContent.fileurl = "<cfoutput>#title.filearray[i].fileurl#</cfoutput>";
				<cfif structKeyExists(title.filearray[i], 'syncarray') && arrayLen(title.filearray[i].syncarray)>
					videoSlideDataContent.hasSlides = true;
					videoSlideDataContent.syncPoints = JSON.parse('<cfoutput>#serializejson(title.filearray[i].syncArray)#</cfoutput>');
					videoSlideDataContent.slideData = '<cfoutput>#REReplace(title.filearray[i+1].fileurl,"#chr(13)#|#chr(9)#|\n|\r","","ALL")#</cfoutput>';
				<cfelse>
					videoSlideDataContent.hasSlides = false;
				</cfif>
				videoSlideData['<cfoutput>#title.filearray[i].fileid#</cfoutput>'] = videoSlideDataContent;
			</cfif>	
		</cfloop>
	</cfloop>
	var syncSlides = true;
	var videoPlayer;
	var slideController = new slideController($("#slidePlayer"), $("#slideProgressBar"));
	var videoWatcher = new videoWatcher(slideController);

	$(document).ready(function(){
		videoPlayer = $("#jquery_jplayer_1").jPlayer({
			ready: function () {
				$(this).jPlayer("setMedia", {
					m4v: "http://www.jplayer.org/video/m4v/Big_Buck_Bunny_Trailer.m4v",
					ogv: "http://www.jplayer.org/video/ogv/Big_Buck_Bunny_Trailer.ogv",
					webmv: "http://www.jplayer.org/video/webm/Big_Buck_Bunny_Trailer.webm",
					poster: "http://www.jplayer.org/video/poster/Big_Buck_Bunny_Trailer_480x270.png"
				});
			},
			play: function() { // To avoid both jPlayers playing together.
				videoWatcher.start();
			},
			stop: function() {
				videoWatcher.stop();
			},
			pause: function() {
				videoWatcher.stop();
			},
			repeat: function(event) { // Override the default jPlayer repeat event handler
				if(event.jPlayer.options.loop) {
					$(this).unbind(".jPlayerRepeat").unbind(".jPlayerNext");
					$(this).bind($.jPlayer.event.ended + ".jPlayer.jPlayerRepeat", function() {
						$(this).jPlayer("play");
					});
				} else {
					$(this).unbind(".jPlayerRepeat").unbind(".jPlayerNext");
					$(this).bind($.jPlayer.event.ended + ".jPlayer.jPlayerNext", function() {
						$("#jquery_jplayer_2").jPlayer("play", 0);
					});
				}
			},
			swfPath: "js",
			supplied: "webmv, ogv, m4v"
		});
		
		videoWatcher.setVideoPlayer(videoPlayer);	

		$("#btnPreviousSlide").click(function(){
			slideController.previousSlide();
		});
		
		$("#btnNextSlide").click(function(){
			slideController.nextSlide();
		});
		
		$("#btnToggleSync").click(function(){
			(syncSlides) ? syncSlides = false : syncSlides = true;
			videoWatcher.changeSyncSlides(syncSlides);
		});
		
		$(".videoTitle").click(function(){
			videoWatcher.reinit();
			
			if(videoSlideData[$(this).attr('videoID')].hasSlides){
				
				//Video has slides - reinit the slidecontroller with syncpoints and slidedata for the selected video
				slideController.reinit(videoSlideData[$(this).attr('videoID')].syncPoints, videoSlideData[$(this).attr('videoID')].slideData);
				videoWatcher.setVideoIncludesSlides(true);
				//reset slide sync
				syncSlides = true;
				videoWatcher.changeSyncSlides(syncSlides);
				
				//update video player URL
				//TODO when full URL is known, setting video to start for now
				$("#videoTitle").html(videoSlideData[$(this).attr('videoID')].filetitle);
				$("#jquery_jplayer_1").jPlayer("play", 0);
				videoWatcher.start();
				
			}else{
				//Video does not have slides; update the UI (hide the slide div)
				videoWatcher.setVideoIncludesSlides(false);
			}
		});
	});
</script>


