<cfcomponent extends="model.customPage.customPage" output="true">

	<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

			local.arrCustomFields = [];
            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="SABA Membership Application" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			
			local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2- Make your selections below." };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3-  Please review your selections and proceed with payment." };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="0EB039A1-6A25-4A57-B331-90C8EB6CF53B" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns",type="STRING",desc="UIDs for Add-Ons that qualified users will have to opt out of",value="A680A70B-CF08-4278-9AF7-9DD6A4D40661" }; 	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="069698ca-c1b9-4c4c-90ec-0d576e8595a2" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a SABA member. If you have questions about your membership, please call 901.527.3573." }; 			
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberBilledMessage",type="CONTENTOBJ",desc="Message displayed when account selected has billed subscription",value="Our records indicate you have a billed subscription. Will redirect to renewal shortly." }; 			
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 901.527.3573." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="SABACIM" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="SABAPayLater" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="SABA Draft" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				
			local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." };
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="SABA Membership Application Receipt" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);			
				
			local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo2",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="LRSConfirmationSub",type="STRING",desc="Subject line for the LRS confirmation email",value="LRS Terms, Condition, and Next Steps" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="LRSEmailContent",type="CONTENTOBJ",desc="Content for the LRS Confirmation Email sent to the member",value="" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
		
            variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
            
            StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='join',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
			
			variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");
			
            variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
            
            switch (local.formAction) {
				case "processLookup":
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
				case "processMembershipInfo":				
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
			
			return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>
    </cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">
		
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
		<cfif structKeyExists(session, "captchaEntered")>
			<cfset structDelete(session, "captchaEntered")>
		</cfif>
		
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				div.alert-danger,.alert-error{color:##950300!important;padding: 10px !important;}
				##cboxOverlay{z-index: 99998 !important;}
				##colorbox{z-index: 99999 !important;}
				.bottomMargin20{margin-bottom:20px;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
					$(".ContactTypeHolder tr:first-child td").eq(0).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(0).width());
					$(".ContactTypeHolder tr:first-child td").eq(1).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(1).width());				
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false, onComplete:fnOnCompleteLocatorLoad} );
				}
				function fnOnCompleteLocatorLoad(){
					$('iframe.cboxIframe').attr('aria-label', 'Account Locator');
					$('button##cboxClose').attr('aria-label', 'Close');
				}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				function addMember(memObj) {
					$.colorbox.close();
					assignMemberData(memObj);
				}
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
				function subscriptionRateOverrideBoxHandler(event) {
					var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());
					//check subscription if not already checked
					if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
						$(subRateRadioButton)[0].click();
						$(this).focus();
					} else if (subRateRadioButton) {
						$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
					}
				}
				function subscriptionCheckboxHandler() {
					if ($(this)[0].checked) {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
					} else {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
					}
				}
				function subscriptionRateRadioButtonHandler() {

					if ($(this)[0].checked) {
						var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
						var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
						var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();

						if (rateOverrideBox.length) {
							//rateoverride box is present
							rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
						}

						//put label of selected rate radio button next to subscription
						rateDescription = ' - ' + rateDescription;
						$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);

						//check subscription if not already checked
						if (!$(subCheckbox)[0].checked)
							$(subCheckbox)[0].click();
					}
				}
				function initializeAddons() {
					$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
				}
				function selectAllSubscriptionsIfRequired() {
					var addonData = $(this).data();
					// select all addons if minimum required by set is gte available count
					// hide checkboxes so they can not be unselected
					if (addonData.minallowed >= $('.subCheckbox',this).length) {
						$('.subCheckbox:not(:checked)',this).click().hide();
						$('.subCheckbox',this).hide();
					}
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">		
				<cfif len(variables.strPageFields.FormTitle)>
					<div class="row-fluid TitleText bottomMargin20">#variables.strPageFields.FormTitle#</div>
				</cfif>
				<div class="row-fluid"><h3>#variables.strPageFields.AccountLocatorTitle#</h3></div>
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="195" style="text-align:center;vertical-align:middle;">					
							<a href="javascript:void(0)" id="btnAddAssoc" class="btn btnBlue" >#variables.strPageFields.AccountLocatorButton#</a>
						</td>
						<td>#variables.strPageFields.AccountLocatorInstructions#</td>
					</tr>
				</table>
				
			</cfform>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID),variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>	
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		
		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>	

		<cfset local.contactType = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname="Contact Type")/>
		<cfset local.practiceAreas = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname="Law School Practice Area")/>
		<cfset local.graduationYear = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname="Law School Graduation Year")/>
		<cfset local.LRSInterest = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='1027A3B3-C2FE-4BED-B825-680C3F93C58D')/>
		<cfset local.areasOfPractice = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='A3566693-285D-4CCE-9E93-74E492941451')/>
		<cfset local.Optintoemails = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='D06DF030-2CB9-4BF0-8026-F6AA637BCD78')/>
		<cfset local.OptintoDirectory = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='BD05BD37-FC0E-48EC-B61B-206C4BA932DC')/>
		<cfset local.MemAgreementRemitField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='0003337D-86DC-4178-A49D-E10924B892F4')/>
		<cfset local.MemAgreementRemitFieldText = 'I agree to remit 10% of all earned attorney fees to the Lawyer Referral Service for each referred case'/>
		<cfset local.DisMattersSanctionsField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='DEF7F8FB-C95B-4B9F-A7C6-863ABFA5EDA6')/>
		<cfset local.DisMattersSanctionsFieldText = 'I have been subject to any disciplinary sanctions by the State Bar of Texas, by a district court in Texas, or by an entity in another state which has authority over attorney discipline' />
		<cfset local.DisMattersConvictedField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='464D81CA-D72C-407B-8DF4-186DF068B23E')/>
		<cfset local.DisMattersConvictedFieldText = 'I have been convicted, given probation (whether deferred or not), or fined for a felony or misdemeanor involving moral turpitude or other serious crime' />
		<cfset local.DisMattersMalpracticeField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='3E04591C-CA0A-4F4B-A888-655C63B9544F')/>
		<cfset local.DisMattersMalpracticeFieldText = 'A suit(s) for legal malpractice or other private civil action alleging attorney misconduct has been concluded (by settlement or judgement) against me' />
		<cfset local.GrantAACOGSeniorProgramField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='93E09AE8-FF52-4672-B7E5-61D5C250FF9D')/>
		<cfset local.GrantAACOGSeniorProgramFieldText = 'I have reviewed the AACOG Senior Program - Rules and Guidelines and I agree to accept referrals for qualified senior citizens at a reduced rate of $50 per hour' />
		<cfset local.HousingFirstProgramRulesField = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgID,UID='AFEA7697-484F-4FA5-A3B8-CBFF030A18DF')/>
		<cfset local.HousingFirstProgramRulesFieldText = 'I have reviewed the Housing First Program - Rules and Guidelines and I agree to accept referrals for qualified students at a reduced rate of $200 per hour' />
		<cfset local.optInDirFieldInfo = application.objCustomPageUtils.mem_getCustomFieldDataByUID(orgID=variables.orgid,UID='21497371-86C7-4C07-BE67-4FED0C4BE939')>
		
		<cfquery dbtype="query" name="local.qryOptintoemails">
			select columnID, valueID
			from [local].Optintoemails
			where columnValueString='Yes'
		</cfquery>
		<cfquery dbtype="query" name="local.qryOptintoDirectory">
			select columnID, valueID
			from [local].OptintoDirectory
			where columnValueString='Yes'
		</cfquery>
		<cfquery dbtype="query" name="local.qrYOptInDirFieldInfo">
			select columnID, valueID
			from [local].optInDirFieldInfo
			where columnValueBit=1
		</cfquery>
		
		<!--- Category --->
		<cfset local.category = variables.objCustomPageUtils.renderFieldSet(uid='EEDD8A40-5E6F-418E-ACD5-A5839F22B789', mode="collection", strData=local.strData)>

		<!--- Disciplinary Matters --->
		<cfset local.disciplinaryMatters = variables.objCustomPageUtils.renderFieldSet(uid='FF1E43E8-0C74-4795-AE42-436137EC7942', mode="collection", strData=local.strData)>
		<cfset local.DisMattersFieldTextsToReplace = 'LRS Disciplinary Matters - Sanctions|LRS Disciplinary Matters - Convicted|LRS Disciplinary Matters - Malpractice'>
		<cfset local.disMattersNewjsvalidation =  replaceList(local.disciplinaryMatters.jsvalidation, local.DisMattersFieldTextsToReplace, '#local.DisMattersSanctionsFieldText#|#local.DisMattersConvictedFieldText#|#local.DisMattersMalpracticeFieldText#', '|')>

		<!--- LRS Terms of Membership --->
		<cfset local.LRSTermsOfMembership = variables.objCustomPageUtils.renderFieldSet(uid='A0387254-0C95-4CF6-AD75-BC1F6BEA26D6', mode="collection", strData=local.strData)>
		<cfset local.TermsOfMembershipNewjsvalidation =  replace(local.LRSTermsOfMembership.jsvalidation, 'LRS Membership Agreement - Remit', '#local.MemAgreementRemitFieldText#')>
		
		<!--- Personal Information --->
		<cfset local.personalInformation = variables.objCustomPageUtils.renderFieldSet(uid='0ECCD265-E836-4195-83F4-219AD688B000', mode="collection", strData=local.strData)>

		<!--- Education Information --->
		<cfset local.educationInformation = variables.objCustomPageUtils.renderFieldSet(uid='4619D61F-E940-46FD-9D4D-EE2DFC98A68D', mode="collection", strData=local.strData)>

		<!--- Organizational Address --->
		<cfset local.organizationalAddress = variables.objCustomPageUtils.renderFieldSet(uid='B6173296-1451-42F7-B188-EBA9670CC4C7', mode="collection", strData=local.strData)>
		
		<!--- Professional Information --->
		<cfset local.professionalInformation = variables.objCustomPageUtils.renderFieldSet(uid='0572A56D-226F-4CEF-B899-2841546C640A', mode="collection", strData=local.strData)>
		
		<!--- Secondary Address --->
		<cfset local.secondaryAddress = variables.objCustomPageUtils.renderFieldSet(uid='AA14C5A9-6DA5-4E6F-9978-62AAA8CF9DDA', mode="collection", strData=local.strData)>
		
		<!--- Address Preference --->
		<cfset local.addressPreference = variables.objCustomPageUtils.renderFieldSet(uid='3BD17D1F-F531-4A7E-9635-0D94A032C11A', mode="collection", strData=local.strData)>
		
		<!--- Communication Preferences --->
		<cfset local.communicationPreference = variables.objCustomPageUtils.renderFieldSet(uid='1C33CCDB-B723-4DAD-BA10-A67BE2796CB2', mode="collection", strData=local.strData)>	
		
		<!--- Publication  Preferences --->
		<cfset local.publicationPreference = variables.objCustomPageUtils.renderFieldSet(uid='B74D708B-4EC0-4073-BD5A-34E1C651B62F', mode="collection", strData=local.strData)>
		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.LRSInterestFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='EC14B179-1241-4B36-A1BE-89F39364AC30', mode="collection", strData=local.strData)>

		<cfset local.housingFirstProgram = variables.objCustomPageUtils.renderFieldSet(uid='AE776B78-2225-4D11-849F-5E169BDECED2', mode="collection", strData=local.strData)>
		<cfset local.housingFirstNewjsvalidation =  replace(local.housingFirstProgram.jsvalidation, 'LRS Housing First Program - Rules', '#local.HousingFirstProgramRulesFieldText#')>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				
				.content input[type="text"] {
					width:206px!important;
				}
				.content select{
					width:220px!important;
				}
				##ProfessionalLicenseFields input[type="text"] {
					width:auto!important;
				}
				##ProfessionalLicenseFields select{
					width:auto!important;
				}
				##ProfessionalLicenseFields{
					width:100%!important;
				}
			
				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}							
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##ProfessionalLicenseFields input[type="text"] {
						width:206px!important;
					}
					##ProfessionalLicenseFields select{
						width:220px!important;
					}					
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{color:##950300!important;padding: 10px !important;}	

				.hide{
					display:none;
				}	
				@media (min-width: 1200px){
					.eachRow{
						margin-bottom: 5px;
					}
					.proLicenseLabel{
						font-weight:700;
						text-align:left;
						font-size: 9pt;
					}
					.areaStatus{
						text-align:left;
						margin-left: 0px!important;
					}
					.areaState{
						text-align:left;
					}
					##state_table  .proLicenseLabel{
						display:block;
					}
					.wrapLeft{
						display: table-cell!important;
					}
					.span3 input {
						width: 90%!important;
					}
					.tsAppSectionHeading{
						min-height: 20px !important;
						vertical-align: middle;
					}
				}
				.wrapLeft{
						display:none;
				}
				.jsLabel{
					display:none !important;
					font-weight:700;
					font-size: 9pt;
				}
				.areaStatus{
					margin-left:0 !important;
				}
				.span3{
					margin-left:0 !important;
					margin-right:10px !important;
				}
				@media  (min-width: 767px) and  (max-width: 1200px){
					
						.span3 input{
							width: 90%!important;
						}
						.proLicenseLabel{
							font-weight:700;
							text-align:left;
							font-size: 9pt;
						}
						.eachRow{
						    margin-bottom: 5px;
						}
						.areaState{
							text-align:left;
						}

				}
				@media (max-width: 979px) and (min-width: 768px){
						.span3{
							margin-left:0 !important;
							margin-right:10px !important;
						}
						.span3 input{
							width: 90%!important;
						}
						.proLicenseLabel{
							font-weight:700;
							text-align:left;
							font-size: 9pt;
						}
						.eachRow{
						    margin-bottom: 5px;
						}
						.areaState{
							text-align:left;
						}

				}
				@media (max-width: 767px){
					.eachRow{
						    margin-bottom: 5px;
					}
					##state_table  .proLicenseLabel{
						display:none !important;
					}
					.jsLabel{
						display:block !important;
						margin-top: -5px;
					}
					.span12, .row-fluid .span12 {
						width: 94%!important;
					}
				}
				.tsAppSectionHeading{
					min-height: 20px;
					vertical-align: middle;
				}
				.bottomMargin20{margin-bottom:20px;}
				div.tsAppSectionContentContainer tr > td.BodyText > input[type="radio"] { margin-top:0px; }
			</style>
			<script language="javascript">
				var #toScript(local.personalInformation.strfields, "strFieldSetPrsnlEductnObj")#;
				var #toScript(local.educationInformation.strfields, "strFieldSetEductnObj")#;
				var #toScript(local.professionalInformation.strfields, "strFieldSetProfnObj")#;
				function afterFormLoad(){
					var _CF_this = document.forms['#variables.formName#'];					
					$('html, body').animate({ scrollTop: 0 }, 500);	
					adjustFieldsetDisplay();
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}

				function adjustFieldsetDisplay() {
					var memType = $('##md_#local.contactType.COLUMNID#').find(":selected").text();
					$('##memberTypeField').val(memType);
					$('div.LRSInterestFieldSetFields').addClass('hide');
					if(memType != 'Law Student'){
						$('select[id=md_#local.practiceAreas.COLUMNID#]').parents('tr').hide();
						$('select[id=md_#local.practiceAreas.COLUMNID#]').val('');
						$('select[id=md_#local.practiceAreas.COLUMNID#]').multiselect("refresh");
						$('input[id="md_#local.graduationYear.COLUMNID#"]').parents('tr').find('td').eq(1).html('Law School Graduation Year');
					}
					switch(memType) {
						case 'Attorney':
							$('div.professionalInformationWrap').removeClass('hide');
							$('div.professionalLicenceWrap').removeClass('hide');
							$('div.LRSInterestFieldSetFields').removeClass('hide');
							$('div.educationInfoFieldFields').removeClass('hide');	
						break;
						case 'Judge':
							$('div.professionalInformationWrap').removeClass('hide');
							$('div.professionalLicenceWrap').removeClass('hide');
							$('div.educationInfoFieldFields').removeClass('hide');	
						break;
						case 'Law Student':
							$('div.professionalInformationWrap').addClass('hide');
							$('div.educationInfoFieldFields').removeClass('hide');	
							$('select[id=md_#local.practiceAreas.COLUMNID#]').parents('tr').show();
							$('input[id="md_#local.graduationYear.COLUMNID#"]').parents('tr').find('td').eq(1).html('Anticipated Graduation Year');
						break;
						case 'Paralegal/Legal Assistant':
							$('div.professionalInformationWrap').removeClass('hide');
							$('div.professionalLicenceWrap').addClass('hide');
							$('div.educationInfoFieldFields').addClass('hide');	
							$('.licenseWrap').remove();
							$('##mpl_pltypeid option:selected').each(function() {
								$(this).prop('selected', false);
							})
							try{    
								$('##mpl_pltypeid').multiselect('refresh');
							}catch(e){}
						break;
						case 'Retired Attorney':
							$('div.professionalInformationWrap').addClass('hide');
							$('div.professionalLicenceWrap').addClass('hide');
							$('div.educationInfoFieldFields').removeClass('hide');					
							$('.licenseWrap').remove();
							$('##mpl_pltypeid option:selected').each(function() {
								$(this).prop('selected', false);
							})
							try{    
								$('##mpl_pltypeid').multiselect('refresh');
							}catch(e){}
						break;
						default:							
							$('div.professionalInformationWrap').addClass('hide');
							$('div.professionalLicenceWrap').addClass('hide');
							$('div.educationInfoFieldFields').addClass('hide');						
							$('.licenseWrap').remove();
							$('##mpl_pltypeid option:selected').each(function() {
								$(this).prop('selected', false);
							})
							try{    
								if($('##mpl_pltypeid'))$('##mpl_pltypeid').multiselect('refresh');
							}catch(e){}
						break;
					}
				}
				
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}


				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					memberType = $('##md_#local.contactType.COLUMNID#').find(":selected").text();

					#local.category.jsvalidation#
					#local.personalInformation.jsvalidation#
					if(memberType == 'Attorney' || memberType == 'Judge' || memberType == 'Retired Attorney' || memberType == 'Law Student' ) {	
						#local.educationInformation.jsvalidation#
					}					
					if($('.categoryInfoFields select option:contains("Law Student"):selected').length == 0 && $.trim($('.contactInfoFields .BodyText:contains("Firm/Company")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Firm/Company is required.';
					}
					if($('.categoryInfoFields select option:contains("Law Student"):selected').length == 0 && $.trim($('.contactInfoFields .BodyText:contains("Street Address")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Office Street Address is required.';
					}
					if($('.categoryInfoFields select option:contains("Law Student"):selected').length == 0 && $.trim($('.contactInfoFields .BodyText:contains("City")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Office City is required.';
					}
					if($('.categoryInfoFields select option:contains("Law Student"):selected').length == 0 && $.trim($('.contactInfoFields .BodyText:contains("State")').siblings().find("select").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Office State is required.';
					}
					if($('.categoryInfoFields select option:contains("Law Student"):selected').length == 0 && $.trim($('.contactInfoFields .BodyText:contains("Zip Code")').siblings().find("input[type='text']").val()).length == 0)
					{
						arrReq[arrReq.length] = 'Office Zip Code is required.';
					}
					#local.organizationalAddress.jsvalidation#

					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					if(memberType == 'Attorney' || memberType == 'Judge') {						
						if(prof_license != "" && prof_license != null){
							isProfLicenseSelected = true;
							$.each(prof_license,function(i,val){
								var text = $("##mpl_"+val+"_licensenumber").closest('.licenseWrap').find('.areaState span').text();
								if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' #variables.strProfLicenseLabels.profLicenseNumberLabel#.'; }
								if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License #variables.strProfLicenseLabels.profLicenseDateLabel#.'; }
							});
						}else{
							arrReq[arrReq.length] = 'Select a Professional Licence.';
						}
					}
					if(memberType == 'Attorney' || memberType == 'Judge' || memberType == 'Retired Attorney') {	
						lawSchoolObj = '###variables.formName# ##'+lawSchoolSelector;
						yearGraduatedObj = '###variables.formName# ##'+yearGraduatedSelector;
						firmSizeObj = '###variables.formName# ##'+firmSizeSelector;
							if($(lawSchoolObj).val() == ''){
								arrReq[arrReq.length] = "Law School is required.";
							}
							if($(yearGraduatedObj).val() == ''){
								arrReq[arrReq.length] = "Law School Graduation Year is required.";
							}
							if(memberType == 'Attorney' || memberType == 'Judge' )
							{
								if($(firmSizeObj).val() == ''){
									arrReq[arrReq.length] = "Firm Size is required.";
								}
							}
							
					}
					
					if( memberType == 'Law Student') {	
						lawSchoolObj = '###variables.formName# ##'+lawSchoolSelector;
						yearGraduatedObj = '###variables.formName# ##'+yearGraduatedSelector;
						
							if($(lawSchoolObj).val() == ''){
								arrReq[arrReq.length] = "Law School is required.";
							}
							if($(yearGraduatedObj).val() == ''){
								arrReq[arrReq.length] = "Anticipated Graduation Year is required.";
							}						
					}
					
					if(memberType == 'Attorney' || memberType == 'Paralegal/Legal Assistant'){
						if($('##md_#local.areasOfPractice.columnID#').val() == null || $('##md_#local.areasOfPractice.columnID#').val() == ''){
							arrReq[arrReq.length] = 'Area of Practice is required.';
						}
					}
					if(memberType == 'Attorney' || memberType == 'Judge' || memberType == 'Paralegal/Legal Assistant'){
						#local.professionalInformation.jsvalidation#
					}
					if(memberType == 'Attorney'){
						#local.LRSInterestFieldSet.jsvalidation#
					}

					valNo = $($('input[type=radio][name=md_#local.LRSInterest.columnID#]')[0]).val();
					valYes = $($('input[type=radio][name=md_#local.LRSInterest.columnID#]')[1]).val();
					if($('input[type=radio][name=md_#local.LRSInterest.columnID#]:checked').val() == valYes){
						#local.TermsOfMembershipNewjsvalidation#
						#local.disMattersNewjsvalidation#
						#local.housingFirstNewjsvalidation#
					}
					if($('input[type=radio][name=md_#local.LRSInterest.columnID#]:checked').val() == valNo){
						disciplinarErrIndex = $.inArray("I have NOT received any of the above disciplinary matters. is required.", arrReq);						
						if(disciplinarErrIndex >= 0){
							arrReq.splice(disciplinarErrIndex,1);
						}
						LRSMembershipErrIndex = $.inArray("I agree to the above terms of LRS Membership is required.", arrReq);
						if(LRSMembershipErrIndex >= 0){
							arrReq.splice(LRSMembershipErrIndex,1);
						}
					} 
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				
				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]+" input,."+containerClassArray[i]+" select,."+containerClassArray[i]+" textarea").each(function(){
								$(this).val('');
							});	

							$("."+containerClassArray[i]+" [type=radio]").each(function(){
								$(this).prop('checked', false);
							});
						});
					}
				}				

				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>

					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
					adjustFieldsetDisplay();
					afterFormLoad();
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
				}

				function LRSInterest(value,load){

					if (value == $($('input[type=radio][name=md_#local.LRSInterest.columnID#]')[0]).val()) {
						if(load == false){
							resetFormFieldsByContainerClass('disciplinaryMattersFields');
							resetFormFieldsByContainerClass('LRSTermsOfMembershipFields');	
							resetFormFieldsByContainerClass('housingFirstProgramFields');	
						}
						$(".disciplinaryMattersFields").hide();
						$(".LRSTermsOfMembershipFields").hide();
						$(".housingFirstProgramFields").hide();
						$("##LRSInterest").val('No');
						
					}
					else if (value == $($('input[type=radio][name=md_#local.LRSInterest.columnID#]')[1]).val()) {
						$(".disciplinaryMattersFields").show();
						$(".LRSTermsOfMembershipFields").show();
						$(".housingFirstProgramFields").show();
						$("##LRSInterest").val('Yes');
					}

				}

				$(document).ready(function() {						
					prefillData();
					$.each(strFieldSetEductnObj, function (key, val) {
						if(val == 'Law School'){
							lawSchoolSelector = key;
						}else if(val == 'Law School Graduation Year'){
							yearGraduatedSelector = key;
						}
					}); 
					$.each(strFieldSetProfnObj, function (key, val) {
						if(val == 'Firm Size'){
							firmSizeSelector = key;
						}
					});
					$("[name='md_#local.qryOptintoemails.columnID#']").find("[value='#local.qryOptintoemails.valueID#']").prop("selected",true);
					$("[name='md_#local.qryOptintoDirectory.columnID#']").find("[value='#local.qryOptintoDirectory.valueID#']").prop("selected",true);
					$("[name='md_#local.qrYOptInDirFieldInfo.columnID#']").find("[value='#local.qrYOptInDirFieldInfo.valueID#']").prop("selected",true);
					memberTypeField = $('##md_#local.contactType.COLUMNID#');
					$(memberTypeField).change(adjustFieldsetDisplay);
					
					$("##mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							if(ui.checked){
								$("##state_table").show();
								$('##state_table').after('<div class="row-fluid eachRow licenseWrap" id="tr_state_'+ui.value+'" >'
													+'<div style="margin-bottom: -1px;margin-top: 15px;" class="row-fluid eachRow jsLabel">State Name</div>'
													+'<div class="span3 areaState" >'
													+'<span  class="BodyText">'+ui.text+'</span>'
													+'</div>'
													+'<div class="row-fluid eachRow jsLabel" >#variables.strProfLicenseLabels.profLicenseNumberLabel#</div>'
													+'<div class="span3" >'
													+'<input class="licensenumber" size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" />'
													+'<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
													+'</div>'
													+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</div>'
													+'<div class="span3" >'
													+'<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="BodyText" />'
													+'</div>'
													+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseStatusLabel#</div>'
													+'<div class="span3 areaStatus" >'
													+'<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="">Please Select</option><option value="active" selected="selected">Active</option><option value="inactive">Inactive</option><option value="disbarred">Disbarred</option><option value="suspended">Suspended</option></select>'
													+'</div>'
													+'</div>');

								if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
									mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
								}
								$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
							}else{
								$("##tr_state_"+ui.value).remove();
							}
					    },
					});	
							
					adjustFieldsetDisplay();
					
					$('input[type=radio][name=md_#local.LRSInterest.columnID#]').change(function() {
						LRSInterest(this.value,false);
					});
					
					$('select[name=md_#local.contactType.columnID#]').change(function() {
						strContactType = $(this).find(":selected").text();
						if(strContactType == 'Law Student'){
							$($('.contactInfoFields .BodyText:contains("Firm/Company Name")').closest('tr').find('td:first-child')).html('')
						}else{
							$($('.contactInfoFields .BodyText:contains("Firm/Company Name")').closest('tr').find('td:first-child')).html('*')
						}
					});

					if($('input[type=radio][name=md_#local.LRSInterest.columnID#]:checked').length){
						LRSInterest($('input[type=radio][name=md_#local.LRSInterest.columnID#]:checked').val(),true);
					}

					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>
					$('input[name="md_#local.MemAgreementRemitField.columnID#"]').parents('tr').find('td').eq(1).html('#local.MemAgreementRemitFieldText#');
					$('input[name="md_#local.DisMattersSanctionsField.columnID#"]').parents('tr').find('td').eq(1).html('#local.DisMattersSanctionsFieldText#');
					$('input[name="md_#local.DisMattersConvictedField.columnID#"]').parents('tr').find('td').eq(1).html('#local.DisMattersConvictedFieldText#');
					$('input[name="md_#local.DisMattersMalpracticeField.columnID#"]').parents('tr').find('td').eq(1).html('#local.DisMattersMalpracticeFieldText#');
					$('input[name="md_#local.GrantAACOGSeniorProgramField.columnID#"]').parents('tr').find('td').eq(1).html('#local.GrantAACOGSeniorProgramFieldText#');
					$('input[name="md_#local.HousingFirstProgramRulesField.columnID#"]').parents('tr').find('td').eq(1).html('#local.HousingFirstProgramRulesFieldText#');
					$('div.tsAppSectionContentContainer tr > td.BodyText > input:radio:first-child + br').remove();
					$("##join td.tsAppBodyText").addClass('BodyText');
					$("##join td.tsAppBodyText.BodyText").removeClass('tsAppBodyText');
					
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#"  <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">					
                    <input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<input type="hidden" name="memberTypeField" id="memberTypeField" value="">				
					<input type="hidden" name="LRSInterest" id="LRSInterest" value="No">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid  TitleText bottomMargin20">#variables.strPageFields.FormTitle#</div>
					</cfif>
					<div id="content-wrapper" class="row-fluid">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid">#variables.strPageFields.Step1TopContent#</div>
						</cfif>			
						<div class="row-fluid categoryInfoFields">
							<div class="span12 tsAppSectionHeading">#local.category.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.category.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid personalInfoFieldFields">
							<div class="span12 tsAppSectionHeading">#local.personalInformation.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.personalInformation.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid educationInfoFieldFields">
							<div class="span12 tsAppSectionHeading">#local.educationInformation.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.educationInformation.fieldSetContent#
							</div>
						</div>
						
						<div class="row-fluid contactInfoFields">
							<div class="span12 tsAppSectionHeading">#local.organizationalAddress.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">#local.organizationalAddress.fieldSetContent#</div>
						</div>
						<div class="row-fluid professionalLicenceWrap hide">
							<div class="tsAppSectionHeading">State Bar License Information</div>
							<div class="tsAppSectionContentContainer professionalLisenceWrap">
								<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
								<table cellpadding="3" border="0" cellspacing="0">		
									<tr class="top">
										<th class="BodyText" colspan="3" align="left">
											&nbsp;
										</th>
									</tr>
									<tr align="top">
										<td class="BodyText" width="10">&nbsp;</td>
										<td class="BodyText">State Bar License:</td>
										<td class="BodyText">
											<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
												<cfloop query="local.qryOrgPlTypes">
													<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
													<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr class="top">
										<td class="BodyText" width="10"></td>
										<td class="BodyText"></td>
										<td class="BodyText"></td>
									</tr>
								</table>
								<table cellpadding="3" border="0" cellspacing="0" id="ProfessionalLicenseFields">
									<tr>
										<td class="BodyText wrapLeft" width="">&nbsp;</td>
										<td >
											<div class="row-fluid" id="state_table" style="display:none;" >
												<div class="span3 proLicenseLabel" >
													State Name
												</div>
												<div class="span3 proLicenseLabel"  >
													#variables.strProfLicenseLabels.profLicenseNumberLabel#
												</div>
												<div class="span3 proLicenseLabel"  >
													#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)
												</div>
												<div class="span3 proLicenseLabel" >
													#variables.strProfLicenseLabels.profLicenseStatusLabel#
												</div>
											</div>
											<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
												<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
													<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
													<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
													<cfset  local.license_status  = 'Active'>
													<div class="row-fluid licenseWrap"  id="tr_state_#local.thisItem#">
														<div class="span3" >
															<span  class="BodyText">#local.licenseTextArr[local.thisItem]#</span>															
														</div>
														<div class="span3" >
															<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="BodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
															<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
														</div>
														<div class="span3" >
															<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="BodyText" size="13" maxlength="10" />
															<cfsavecontent variable="local.datejs">
																<cfoutput>
																	<script language="javascript">
																		$(document).ready(function() { 
																			mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
																		});
																	</script>
																	<style type="text/css">
																	##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
																	</style>
																</cfoutput>
															</cfsavecontent>
															<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
														</div>
														<div class="span3" >
															<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="BodyText">
																	<option value="">Please Select</option>
																	<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>															
															</select>	
														</div>
													</div>
												</cfloop>
											</cfif>	
										</td>
									</tr>					
								</table>
							</div>
						</div>
						<div class="row-fluid professionalInformationWrap hide">
							<div class="span12 tsAppSectionHeading">#local.professionalInformation.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">#local.professionalInformation.fieldSetContent#</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.secondaryAddress.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">#local.secondaryAddress.fieldSetContent#</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.addressPreference.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">#local.addressPreference.fieldSetContent#</div>
						</div>
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.communicationPreference.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">#local.communicationPreference.fieldSetContent#</div>
						</div>	
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.publicationPreference.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">#local.publicationPreference.fieldSetContent#</div>
						</div>
						<div class="row-fluid LRSInterestFieldSetFields hide"  >
							<div class="span12 tsAppSectionHeading">#local.LRSInterestFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.LRSInterestFieldSet.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid LRSTermsOfMembershipFields hide">
							<div class="span12 tsAppSectionHeading">#local.LRSTermsOfMembership.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.LRSTermsOfMembership.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid disciplinaryMattersFields hide">
							<div class="span12 tsAppSectionHeading">#local.disciplinaryMatters.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.disciplinaryMatters.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid housingFirstProgramFields hide">
							<div class="span12 tsAppSectionHeading">#local.housingFirstProgram.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.housingFirstProgram.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>  

						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
						<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>
						<script>
							$(document).ready(function(){
								<cfloop query="local.qryOrgAddressTypes">										   addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
										function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
											var _address = _this.val();
											
											if(_address.length >0){
												if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
													$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
												}
											} else {
												$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
											}
										}
										
										$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
											addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
										});
										
										<cfif listLen(local.profLicenseIDList)>
											$("##state_table").show();
											$('div.professionalLicenceWrap').removeClass('hide');
										</cfif>

								</cfloop>
								
								<cfif NOT structKeyExists(session, "formFields") >								
									<cfloop collection="#local.communicationPreference.strfields#" item="key">
										<cfset local.filedSetObj = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname=local.communicationPreference.strfields[key])>
										
										<cfloop collection="#local.filedSetObj.columnvaluearr#" item="idx">
											<cfif isdefined("local.filedSetObj.columnvaluearr[idx].isdefault")>
												$('##md_#local.filedSetObj.columnid#').val(#local.filedSetObj.columnvaluearr[idx].valueid#);				
												<cfbreak>					
											</cfif>						
										</cfloop>
									</cfloop>
									<cfloop collection="#local.publicationPreference.strfields#" item="key">
										<cfset local.filedSetObj = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname=local.publicationPreference.strfields[key])>
										
										<cfloop collection="#local.filedSetObj.columnvaluearr#" item="idx">
											<cfif isdefined("local.filedSetObj.columnvaluearr[idx].isdefault")>
												$('##md_#local.filedSetObj.columnid#').val(#local.filedSetObj.columnvaluearr[idx].valueid#);				
												<cfbreak>					
											</cfif>						
										</cfloop>
									</cfloop>
								</cfif>
							});
						</script>
					</div>					
				</form>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- save member info and record history --->		
		
		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID) >
		
		<cfif local.strResult.success>
			<!--- Setting Captcha submitted flag --->
			<cfset session.captchaEntered = 1>
			<cfset variables.useMID = local.strResult.memberID>

			<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>	

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>									
			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subxml">
			set nocount on;

			declare @subID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.subscriptionID#">;
				
			select [dbo].[fn_sub_getSubscriptionStructureXML] (@subID,1) as subxml;

			set nocount off;
		</cfquery>
		
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>			
		
		<cfset	local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>		

		<cfsavecontent variable="local.headtext">
			<cfoutput>
				<style>
					.bottomMargin20{margin-bottom:20px;}
					div.alert-danger,.alert-error{color:##950300!important;padding: 10px !important;}
				</style>
				<script type="text/javascript">
					$(document).ready(function(){
						$('input.subCheckbox:checkbox').on('change',subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').on('change',subscriptionRateRadioButtonHandler);
						$('input.subRateOverrideBox').on('change',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('focus',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('blur',subscriptionRateOverrideBoxHandler);

						initializeAddons();

						$('input.subCheckbox:checkbox').each(subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').each(subscriptionRateRadioButtonHandler);	

						$(document).on('change',"*[id^='sub#local.subscriptionID#_rate']",function(){
							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').not('[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').closest('label').hide();
							
							$('div [data-setname="LRS Membership"] input:radio[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').closest('label').show();
							
							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').not('[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').prop('checked',false);

							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').prop('disabled',false);
							
							$('div [data-setname="LRS Membership"] input:radio').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:radio').not('[data-frequencyFname="'+$(this).data('frequencyfname')+'"]').prop('disabled',true);							
						});						
						
					});
					function afterFormLoad(){
						var _CF_this = document.forms['#variables.formName#'];
						$('html, body').animate({ scrollTop: 0 }, 500);
						$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
						setTimeout(function() {
							$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
						}, 5200);
						$("##join td.BodyText").addClass('BodyText');
						$("##join td.BodyText.BodyText").removeClass('BodyText');
					}

					function validateMembershipInfoForm(){
						var arrReq = new Array();
						
						<cfif val(local.subscriptionID)>
							if($(".well").eq(1).find('input[type="radio"],input[type="checkbox"],input[name="sub#local.subscriptionID#"]').length == 0){
								arrReq[arrReq.length] = " Select Membership.";
							}
							else if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
								arrReq[arrReq.length] = " Select Membership.";
							}
						</cfif>	
	
						//make sure selected subscriptions all have selected rates.
						$('input.subCheckbox:checkbox:checked').each(function(x,item){
							var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
							var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
							if ( numRates > 0 && numRatesChecked==0) {
								arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
							}
						});
	
						//make sure any chosen editable rates have amounts greater than zero.		
						$('input.subRateCheckbox:radio:checked').each(function(index,item){
							var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
							if ($(rateOverrideField).length) {
								var overridePrice = parseFloat($(rateOverrideField)[0].value);
								if (!overridePrice) {
									var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
									subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
									arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
								}
							}
						});
						
						var min = $('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper').data('minallowed');
						var max = $('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper').data('maxallowed');
						
						if($('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
							
							if(min != 0 && $('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper input.subCheckbox:checked').length < min ){
								arrReq[arrReq.length] = "Please select at least <b>" + numberToWords(min) + '</b> LRS Panels subscription';
							}	

							if(max != 0 && $('div [data-setname="LRS Membership"] .subAddonsArrayWrapper .subAddonWrapper input.subCheckbox:checked').length > max ){
								arrReq[arrReq.length] = "Please select no more than <b>" + numberToWords(max) + '</b> LRS Panels subscription';
							}
						}														
						
						#Replace(local.result.jsValidation, "$('.subRateCheckbox:checked, .subRateCheckbox:hidden')", "$('.subRateCheckbox:checked')", "ALL")#
						#local.result.jsAddonValidation#				
													
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}		
						
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					function numberToWords(number) {
						var digit = ['zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];  
						var elevenSeries = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];  
						var countingByTens = ['twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];  
						var shortScale = ['', 'thousand', 'million', 'billion', 'trillion'];  
				
						number = number.toString(); number = number.replace(/[\, ]/g, ''); if (number != parseFloat(number)) return 'not a number'; var x = number.indexOf('.'); if (x == -1) x = number.length; if (x > 15) return 'too big'; var n = number.split(''); var str = ''; var sk = 0; for (var i = 0; i < x; i++) { if ((x - i) % 3 == 2) { if (n[i] == '1') { str += elevenSeries[Number(n[i + 1])] + ' '; i++; sk = 1; } else if (n[i] != 0) { str += countingByTens[n[i] - 2] + ' '; sk = 1; } } else if (n[i] != 0) { str += digit[n[i]] + ' '; if ((x - i) % 3 == 0) str += 'hundred '; sk = 1; } if ((x - i) % 3 == 1) { if (sk) str += shortScale[(x - i - 1) / 3] + ' '; sk = 0; } } if (x != number.length) { var y = number.length; str += 'point '; for (var i = x + 1; i < y; i++) str += digit[n[i]] + ' '; } str = str.replace(/\number+/g, ' '); return str.trim();  
				
					}
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headtext#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.FormTitle)>
				<div class="row-fluid  TitleText bottomMargin20">#variables.strPageFields.FormTitle#</div>
			</cfif>
			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="hasAddon" class="hasAddon" value="0">
			<cfinput type="hidden" name="memberTypeField" class="memberTypeField" value="#arguments.rc.memberTypeField#">
			<cfinput type="hidden" name="LRSInterest" id="LRSInterest" value="#arguments.rc.LRSInterest#">

			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<cfoutput>#local.result.formcontent#</cfoutput>
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton btn btn-default" onClick="hideAlert();">Continue</button>
			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				<script>				
					$(document).ready(function(){
						if($('##LRSInterest').val() == "Yes"){
							$('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').attr('checked',true);
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').show();
						}
						$(document).on('change','div [data-setname="LRS Membership"] input[type="checkbox"]',function(){
							if($('div [data-setname="LRS Membership"] input[type="checkbox"]:checked').length > 0){
								$('.hasAddon').val(1);
							}else{
								$('.hasAddon').val(0);
							}
						});
						if($('div [data-setname="LRS Membership"] input[type="checkbox"]:checked').length > 0){
							$('.hasAddon').val(1);
						}else{
							$('.hasAddon').val(0);
						}	

						if($('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').show();						
						}else{
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').hide();
							$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
						}

						$(document).on('change',$('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox'),function(){
							if($('div [data-setname="LRS Membership"] input:checkbox').not('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input:checkbox').prop('checked')){
								$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').show();						
							}else{
								$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper').hide();
								$('div [data-setname="LRS Membership"] .subAddonsArrayWrapper input[type="checkbox"]').prop('checked',false);
							}
						});
					});
					
				</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset	local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc)>		

		<cfif local.strResult.success>
			
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>		
			
				
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
				
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
	<cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>

		<cfreturn getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)>
	</cffunction>
	<cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', totalFullPrice = 0 }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">			
		</cfif>
		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								- 
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
										<!--- #local.thisRate.rateName#  --->
										
										<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
											#local.thisRate.rateName#  (<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> Full)
											<cfset local.strReturn.totalFullPrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
										<cfelse>
										   <cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												
												<cfset local.endData = ListToArray(local.thisRate.termAFEndDate,"T")>
												<cfset local.dateDiff = dateDiff("m", Now(), local.endData[1])>
												<cfset local.quarterLeft = '4'>
												<cfif local.thisRateFrequency.frequencyName eq "Full">
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
												<cfelseif local.thisRateFrequency.frequencyName eq "Quarterly">
													<cfif local.dateDiff gte 0 && local.dateDiff lte 3>
														<cfset local.quarterLeft = 1>
													<cfelseif local.dateDiff gte 4 AND local.dateDiff lte 6 >
														<cfset local.quarterLeft = 2>
													<cfelseif local.dateDiff gte 7 AND local.dateDiff lte 9 >
														<cfset local.quarterLeft = 3>
													<cfelse>
														<cfset local.quarterLeft = 4>
													</cfif>
												</cfif>
												
												<cfif arguments.recursionLevel eq 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
													<cfif local.thisRateFrequency.frequencyName eq "Quarterly">
														<cfset local.strReturn.totalFullPrice = (local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft />
														<cfset local.rateVal = dollarFormat((local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft)>
													<cfelse>
														<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
														<cfset local.rateVal = dollarFormat(local.thisRateFrequency.rateAmt)>
													</cfif>
													
													#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#local.rateVal#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
												<cfelseif arguments.recursionLevel gt 1>
													<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
														<cfif local.thisRateFrequency.frequencyName eq "Quarterly">
															<cfset local.strReturn.totalFullPrice = (local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft />
															<cfset local.rateVal = dollarFormat((local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments)/ local.quarterLeft)>
														<cfelse>
															<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
															<cfset local.rateVal = dollarFormat(local.thisRateFrequency.rateAmt)>
														</cfif>
													</cfif>
													
													
													<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid )>
														   #local.thisRate.rateName#  
														   <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#local.rateVal#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
													</cfif> 
													
												</cfif>							
											</cfloop>
										</cfif>
									</cfif>
								</cfloop>
							</span>
						</div>
					</div>			
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false />							
									<cfset local.pcNumCounter = 1 />									
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false />
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true />
										</cfif>										
										<cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree) />
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
										<cfif len(trim(local.thisAddonSubForm.formContent))>	
																			
											<cfset local.selectionFound = true />
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
											</cfif>
											<cfset local.pcNumCounter = local.pcNumCounter + 1 />											
											#local.thisAddonSubForm.formContent#
										</cfif>									
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>						
						</div>
					</cfif>						
				</cfif>
			</cfoutput>				
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfscript>
			local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc);
		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >	
		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<style>
				.bottomMargin20{margin-bottom:20px;}
				div.alert-danger,.alert-error{color:##950300!important;padding: 10px !important;}
			</style>
			<script type="text/javascript">
				$("body").on('DOMSubtreeModified', "##mccfdiv_#variables.strPageFields.ProfileCodeCredit#", function() {
					if($('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# input[type=radio]').length > 0) {
						$('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# button[type="submit"]:contains("Please Wait...")').html("Continue").removeAttr('disabled');
					} else {
						$('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# button[type="submit"]:contains("Continue")').html("Please Wait...").attr('disabled','disabled');
					}
				});
			</script>
			<cfif len(variables.strPageFields.FormTitle)>
				<div class="row-fluid  TitleText bottomMargin20">#variables.strPageFields.FormTitle#</div>
			</cfif>
			<cfif len(variables.strPageFields.Step3TopContent)>
				<div id="Step3TopContent">#variables.strPageFields.Step3TopContent#</div><br/>
			</cfif>
			
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="hasAddon" id="hasAddon" value="#arguments.rc.hasAddon#">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfinput type="hidden" name="memberTypeField" class="memberTypeField" value="#arguments.rc.memberTypeField#">
			<cfinput type="hidden" name="LRSInterest" id="LRSInterest" value="#arguments.rc.LRSInterest#">

			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub"
					>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
			</div>
			<br/>

			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				<cfif local.strResult.totalFullPrice gt 0>
					<div id="paymentMethodContainer">#local.strReturn.paymentHTML#</div>
				</cfif>
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton btn btn-default" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>

			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			//create subscriptions
			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice">
			set nocount on;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

			select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, dueNow= case when nv.dateDue < getdate() then 1 else 0 end
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs
				on subs.subscriptionID = s.subscriptionID
				and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
			inner join dbo.sub_types t
				on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
				and ta.applicationTypeID = 17
				and ta.itemType = 'Dues'
			inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
			inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
			group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
			order by nv.dateDue
		</cfquery>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodeCredit>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodeCredit>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<!--- Category --->
		<cfset local.category = variables.objCustomPageUtils.renderFieldSet(uid='EEDD8A40-5E6F-418E-ACD5-A5839F22B789', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.LRSInterestFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='EC14B179-1241-4B36-A1BE-89F39364AC30', mode="confirmation", strData=arguments.rc)>

		<cfset local.memType = "#arguments.rc.memberTypeField#">
		<cfset local.LRSInterest = "#arguments.rc.LRSInterest#">

		<cfif local.LRSInterest EQ "Yes">

			<!--- Disciplinary Matters --->
			<cfset local.disciplinaryMatters = variables.objCustomPageUtils.renderFieldSet(uid='FF1E43E8-0C74-4795-AE42-436137EC7942', mode="confirmation", strData=arguments.rc)>

			<cfset local.DisMattersFieldTextsToReplace = 'LRS Disciplinary Matters - Sanctions|LRS Disciplinary Matters - Convicted|LRS Disciplinary Matters - Malpractice'>
			<cfset local.DisMattersSanctionsFieldText = 'I have been subject to any disciplinary sanctions by the State Bar of Texas, by a district court in Texas, or by an entity in another state which has authority over attorney discipline.' />
			<cfset local.DisMattersConvictedFieldText = 'I have been convicted, given probation (whether deferred or not), or fined for a felony or misdemeanor involving moral turpitude or other serious crime.' />
			<cfset local.DisMattersMalpracticeFieldText = 'A suit(s) for legal malpractice or other private civil action alleging attorney misconduct has been concluded (by settlement or judgement) against me.' />
			<cfset local.disMattersReplacedContent =  replaceList(local.disciplinaryMatters.fieldSetContent, local.DisMattersFieldTextsToReplace, '#local.DisMattersSanctionsFieldText#|#local.DisMattersConvictedFieldText#|#local.DisMattersMalpracticeFieldText#', '|')>

			<!--- LRS Terms of Membership --->
			<cfset local.LRSTermsOfMembership = variables.objCustomPageUtils.renderFieldSet(uid='A0387254-0C95-4CF6-AD75-BC1F6BEA26D6', mode="confirmation", strData=arguments.rc)>

			<!--- Housing first program --->
			<cfset local.housingFirstProgram = variables.objCustomPageUtils.renderFieldSet(uid='AE776B78-2225-4D11-849F-5E169BDECED2', mode="confirmation", strData=arguments.rc)>
		
		</cfif>
		
		<!--- Personal Information --->
		<cfset local.personalInformation = variables.objCustomPageUtils.renderFieldSet(uid='0ECCD265-E836-4195-83F4-219AD688B000', mode="confirmation", strData=arguments.rc)>

		<!--- EducationInformation Information --->
		<cfset local.educationInformation = variables.objCustomPageUtils.renderFieldSet(uid='4619D61F-E940-46FD-9D4D-EE2DFC98A68D', mode="confirmation", strData=arguments.rc)>

		<!--- Organizational Address --->
		<cfset local.organizationalAddress = variables.objCustomPageUtils.renderFieldSet(uid='B6173296-1451-42F7-B188-EBA9670CC4C7', mode="confirmation", strData=arguments.rc)>
		
		<!--- Professional Information --->
		<cfset local.professionalInformation = variables.objCustomPageUtils.renderFieldSet(uid='0572A56D-226F-4CEF-B899-2841546C640A', mode="confirmation", strData=arguments.rc)>
		
		<!--- Secondary Address --->
		<cfset local.secondaryAddress = variables.objCustomPageUtils.renderFieldSet(uid='AA14C5A9-6DA5-4E6F-9978-62AAA8CF9DDA', mode="confirmation", strData=arguments.rc)>
		
		<!--- Address Preference --->
		<cfset local.addressPreference = variables.objCustomPageUtils.renderFieldSet(uid='3BD17D1F-F531-4A7E-9635-0D94A032C11A', mode="confirmation", strData=arguments.rc)>
		
		<!--- Communication Preferences --->
		<cfset local.communicationPreference = variables.objCustomPageUtils.renderFieldSet(uid='1C33CCDB-B723-4DAD-BA10-A67BE2796CB2', mode="confirmation", strData=arguments.rc)>		
		
		<!--- publication Preferences --->
		<cfset local.publicationPreference = variables.objCustomPageUtils.renderFieldSet(uid='B74D708B-4EC0-4073-BD5A-34E1C651B62F', mode="confirmation", strData=arguments.rc)>		
		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Category')>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset	local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<p>Here are the details of your application:</p>

			#local.category.fieldSetContent#
			<cfif local.memType eq 'Law Student'>
				#ReplaceNoCase(local.personalandEducationalInformation.fieldSetContent,'Law School Graduation Year', 'Anticipated Graduation Year')#
			<cfelse>	
				#local.personalInformation.fieldSetContent#
			</cfif>
			<cfif local.memType eq 'Attorney' OR local.memType eq 'Judge' OR local.memType eq 'Retired Attorney' OR local.memType eq 'Law Student'>
				#local.educationInformation.fieldSetContent#
			</cfif>
			#local.organizationalAddress.fieldSetContent#
			
			<cfif local.memType eq 'Attorney' OR local.memType eq 'Judge'>
				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">State Bar License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br/>
			</cfif>
			<cfif local.memType eq 'Attorney' OR local.memType eq 'Judge' || local.memType eq 'Paralegal/Legal Assistant'>
				#local.professionalInformation.fieldSetContent#
			</cfif>
			#local.secondaryAddress.fieldSetContent#
			#local.addressPreference.fieldSetContent#
			#local.communicationPreference.fieldSetContent#			
			#local.publicationPreference.fieldSetContent#	
			<cfif local.memType eq 'Attorney'>		
				#local.LRSInterestFieldSet.fieldSetContent#
			</cfif>
			<cfif local.LRSInterest EQ "Yes">
				#ReplaceNoCase(local.LRSTermsOfMembership.fieldSetContent,'LRS Membership Agreement - Remit', 'I agree to remit 10% of all earned attorney fees to the Lawyer Referral Service for each referred case.')#
				#local.disMattersReplacedContent#
				#ReplaceNoCase(local.housingFirstProgram.fieldSetContent,'LRS Housing First Program - Rules', 'I have reviewed the Housing First Program - Rules and Guidelines and I agree to accept referrals for qualified students at a reduced rate of $200 per hour.')#
			</cfif>	
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#local.strResult.formContent#
					<br/>
					<br/>
					<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
					<br/>
				</td>
			</tr>
			</table>

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				#local.confirmationHTML#
			</cfoutput>
		</cfsavecontent>
		
		<cfif application.MCEnvironment eq "production">
            <cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
        </cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.strPageFields.MemberConfirmationFrom },
                            emailto=[{ name="", email=variables.memberEmail.TO }],
                            emailreplyto=variables.strPageFields.StaffConfirmationTo,
                            emailsubject=variables.strPageFields.ConfirmationSub,
                            emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
                            emailhtmlcontent=local.mailContent,
                            siteID=variables.siteID,
                            memberID=val(variables.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF)>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		
		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				#local.confirmationHTMLToStaff#
			</cfoutput>
		</cfsavecontent>

		<cfscript>
			local.arrEmailTo = [];
			variables.strPageFields.StaffConfirmationTo = replace(variables.strPageFields.StaffConfirmationTo,",",";","all");
			local.toEmailArr = listToArray(variables.strPageFields.StaffConfirmationTo,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		</cfscript>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.strPageFields.MemberConfirmationFrom },
			emailto=local.arrEmailTo,
			emailreplyto=variables.strPageFields.MemberConfirmationFrom,
			emailsubject=variables.strPageFields.StaffConfirmationSub,
			emailtitle="#arguments.rc.mc_siteInfo.ORGNAME# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(arguments.rc.mc_siteInfo.sysMemberID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>
		
		<cfscript>
			if(arguments.rc.hasAddon eq 1 or local.LRSInterest EQ "Yes"){
				local.arrEmailTo = [];
				variables.strPageFields.StaffConfirmationTo2 = replace(variables.strPageFields.StaffConfirmationTo2,",",";","all");
				local.toEmailArr = listToArray(variables.strPageFields.StaffConfirmationTo2,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			}
		</cfscript>
		
		<cfif arguments.rc.hasAddon eq 1>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.strPageFields.MemberConfirmationFrom },
				emailto=local.arrEmailTo,
				emailreplyto=variables.strPageFields.MemberConfirmationFrom,
				emailsubject=variables.strPageFields.StaffConfirmationSub,
				emailtitle="#arguments.rc.mc_siteInfo.ORGNAME# - #variables.formNameDisplay#",
				emailhtmlcontent=local.mailContent,
				siteID=variables.siteID,
				memberID=val(arguments.rc.mc_siteInfo.sysMemberID),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>
		</cfif>

		<cfif local.LRSInterest EQ "Yes">
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=listFirst(variables.strPageFields.StaffConfirmationTo2,";") },
				emailto=[{ name="", email=variables.memberEmail.TO }],
				emailreplyto=listFirst(variables.strPageFields.StaffConfirmationTo2,";"),
				emailsubject=variables.strPageFields.LRSConfirmationSub,
				emailtitle="#arguments.rc.mc_siteInfo.ORGNAME# - LRS Confirmation Email",
				emailhtmlcontent=variables.strPageFields.LRSEmailContent,
				siteID=variables.siteID,
				memberID=val(variables.useMID),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
				)>
		</cfif>
		
		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.FormTitle)>
				<div class="row-fluid TitleText bottomMargin20">#variables.strPageFields.FormTitle#</div>
			</cfif>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	<cffunction name="storeMembershipApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
					parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', 
					docDesc='Membership Application Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
					contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.ActiveAcceptedMessage#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ActiveAcceptedMessage#	
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.MemberBilledMessage#
						<script type="text/javascript">
							setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
							function AJAXRenewSub(member){
								var redirect = function(r) {
									redirectLink = '/renewsub/' + r.data.directlinkcode[0];
									window.location = redirectLink;								
								};		
								
								var params = { memberID:member, status:'O', distinct:false };
								TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
							}						
						</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
  </cfcomponent>