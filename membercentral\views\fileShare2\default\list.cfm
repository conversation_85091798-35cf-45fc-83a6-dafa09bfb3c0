<cfset dataStruct = attributes.data.actionStruct />
<cfset fileShareSettings = attributes.data.fileShareSettings />
<cfset appRightsStruct = attributes.data.appRightsStruct />
<cfset baseQueryString = attributes.data.baseQueryString />


<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/memberCentral_grid.js"></script>
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/dhtmlxgrid/dhtmlxgrid.css" />
	<script>dhtmlxError.catchError("LoadXML", mcg_ErrorHandler);</script>
	<script>
		var sectionID = 0;
		<!--- TREE/Folders --->
		var mcg_itemsingle = 'folder'; 
		var mcg_itemplural = 'folders';
		var mcg_gridBaseQString = '#dataStruct.foldersURL#';
		var mcg_gridQString = mcg_gridBaseQString + #dataStruct.sectionToShow#;
		<!--- Grid/Files --->
		var mcg2_itemsingle = 'document'; 
		var mcg2_itemplural = 'documents';
		var mcg2_gridBaseQString = '#dataStruct.filesURL#';
		var mcg2_gridQString = mcg2_gridBaseQString + 0;
		
		<!--- Grid Control Functions --->
		function doOnRowSelect(id) {
			mcg2_gridQString = mcg2_gridBaseQString + id;
			mcg2_reloadGrid();
		} 
		function doLoadPages(){ 
			if( sectionID == 0 ){ mcg_g.selectRow(0,true,false,true); }
			else{ if(mcg_g.doesRowExist(sectionID)){ mcg_g.selectRowById(sectionID,false,true,true); } else{ mcg_g.selectRow(0,true,false,true); } }
		}
		function loadPages(id){ pagesGrid_gridQString = mcg2_gridBaseQString + id; pagesGrid_reload(); }
		
		<!--- Navigation Functions --->
		function mcg_viewDocument(id,lang) { self.location.href = "/?#baseQueryString#&fsAction=viewDocument&fsDocumentID=" + id <cfif not listcontains(baseQueryString,'lang','&')>+ "&lang=" + lang</cfif>; }
		function goToAddDocument() { self.location.href = "/?#baseQueryString#&fsAction=addDocument"; }
		
	</script>	
	</cfoutput>

</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">
<cfoutput>
<span class="tsAppHeading">#fileShareSettings.applicationInstanceName#</span>
<br/>
<table>
	<tr>
		<td width="220" valign="top">
			<div id="mcg_rnum" class="tsAppBodyText" style=" height:13px;"></div>
			<div id="mcg_navigation" style="height:28px; background-color:##dddddd; margin-top:3px; margin-bottom:3px; border:1px solid ##707070;">
				Click the Sections header below to sort the folders in reverse
				<div id="mcg_buttons" style="padding:2px;" align="right">
					<img src="/assets/common/images/spacer.gif" width="1" height="20">
				</div>
			</div>
			<div id="mcg_gridbox" style="width:220px;height:250px;"></div>
		</td>
		<td width="525" valign="top">
			<div id="mcg2_rnum" class="tsAppBodyText" style=" height:13px;"></div>
			<div id="mcg2_navigation" style="height:28px; background-color:##dddddd; margin-top:3px; margin-bottom:3px; border:1px solid ##707070;">
				<div id="mcg2_buttons" style="padding:2px;" align="right">
					<button name="btnAddDocument" onClick="javascript:{goToAddDocument();}" type="submit" title="Add Document"><i class="icon-file-text-alt"></i></button>
				</div>
			</div>
			<div id="mcg2_gridbox" style="width:525px;height:250px;"></div>
		</td>
	</tr>
	<tr>
		<td><img src="/assets/common/images/spacer.gif" height="1" width="1"></td>
	</tr>
</table>
<script>
	mcg_init();
	mcg2_init();
</script>
</cfoutput>	
	
	


