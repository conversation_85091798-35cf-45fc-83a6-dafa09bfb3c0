<cfoutput>#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#</cfoutput>
<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#
	</cfoutput>
<cfelse>
	<cfset local.tmpSort = ArrayNew(1)>
	<cfset ArrayAppend(local.tmpSort,"rank|Relevance")>
	<cfset ArrayAppend(local.tmpSort,"date|Date")>
	<cfset ArrayAppend(local.tmpSort,"case|Case")>
	<cfset ArrayAppend(local.tmpSort,"witness|Witness")>

	<cfoutput>
	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startRow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='document', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=arguments.sortType, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
	<div style="clear:both;"></div>
	<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>
	<!--- results table --->
	<cfoutput query="local.qryResults">
		<div class="tsDocViewerDocument s_row <cfif local.qryResults.currentrow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.qryResults.documentid#">
			<div class="pull-right">
				<div class="p-3"><a href="javascript:downloadDoc(#local.qryResults.documentid#)"><i class="icon-download icon-large"></i> Download</a></div>
			</div>
			<div><a href="javascript:docViewer(#local.qryResults.DocumentID#,#variables.thisBucketCartItemTypeID#,#arguments.searchid#)"><img src="#application.paths.images.url#images/search/page_acrobat.png" width="16" height="16" border="0" align="left" alt="[Adobe Acrobat Document]" class="hidden-phone"><span class="hidden-phone">&nbsp;</span><b><cfif len(trim(expertname))>#trim(expertname)#<cfelse>Unknown Expert</cfif></b></a><br/></div>
			<div class="bk_subContent">
				Document ###local.qryResults.documentid# - #DateFormat(local.qryResults.DocumentDate, "mmm d, yyyy")# - #local.qryResults.state#<br/>
				#local.qryResults.style# ... <cfif len(local.qryResults.causedesc)>... #local.qryResults.causedesc#</cfif>
				<cfif len(local.qryResults.notes)><br/>Notes: #local.qryResults.notes#</cfif>
			</div>
		</div>
	</cfoutput>
	<cfoutput>
	<br clear="all"/>
	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startRow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='document', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=arguments.sortType, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
</cfif>