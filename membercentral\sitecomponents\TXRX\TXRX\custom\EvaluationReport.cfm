<cfset local.thisPageName = "EvaluationReport">
<cfset local.eventID = arguments.event.getValue('f_eventID','')>
<cfset local.doPDF = arguments.event.getValue('pdf',0)>
<cfif local.eventID NEQ "">
	<cfstoredproc datasource="#application.dsn.customApps.dsn#" procedure="txrx_conferenceEvaluations" result="local.spResult">
		<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.eventID#">
		<cfprocresult resultset="1" name="local.qryReport">
		<cfprocresult resultset="2" name="local.qrySection1">
		<cfprocresult resultset="3" name="local.qrySection2">
		<cfprocresult resultset="4" name="local.qryDidPromote">
		<cfprocresult resultset="5" name="local.qryNotMetLearningObjectives">
		<cfprocresult resultset="6" name="local.qryThingsDifferently">
		<cfprocresult resultset="7" name="local.qryActivityEnjoyedMost">
		<cfprocresult resultset="8" name="local.qryActivityEnjoyedLeast">
		<cfprocresult resultset="9" name="local.qryAdditionalComments">
		<cfprocresult resultset="10" name="local.qryHowCommitted">
		<cfprocresult resultset="11" name="local.qryActivitySpeaker">
	</cfstoredproc>	
</cfif>		
<cfoutput>
<div class="tsAppHeading">Summary of Evaluation Forms</div><br/>
<div class="bodyText">
</cfoutput>
	<cfif local.doPDF neq 1>
	<cfform name="frmReport"  id="frmReport" method="post" action="/?pg=#local.thisPageName#">
	<div align="center">
		<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
		<tr valign="top">
			<td align="right">Event ID:&nbsp;</td>
			<td align="left">
			<cfinput type="text" name="f_eventID"  id="f_eventID" value="#local.eventID#" size="16">
		</tr>
		<tr valign="top">
			<td align="right">
				<button type="button" class="tsAppBodyButton" style="width:100px;" onClick="printIt()"><i class="icon-print"></i> Print</button>
			</td>			
			<td align="right">
			<button type="submit" class="tsAppBodyButton" onClick="return chkReport();">show Report</button>
			</td>
		</tr>
		</table>
	</div>
	</cfform>
	</cfif>
	
	
	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<cfif local.doPDF neq 1>
		<script language="JavaScript">
			function printIt() {
				self.location.href = '/?pg=#local.thisPageName#&f_eventid=#local.eventID#&mode=direct&pdf=1';
			}
		</script>
		</cfif>
		<style type="text/css">
		##clehistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">	
	
	<cfif local.eventID NEQ "">
		<cfsavecontent variable="local.showPage">
		<cfoutput>
			<div class="tsAppHeading" align="center">Summary of Evaluation Forms</div>
			<br/>
			<div class="tsAppHeading">#local.qryReport.eventTitle#</div>
			<div class="tsAppBodyText">Date Run: #local.qryReport.dateRun#</div>
			<div class="tsAppBodyText">ACPE## #local.qryReport.ACPENumber#</div>
			<div class="tsAppBodyText">#local.qryReport.location#</div>
			<br/>
			<div class="tsAppBodyText"><strong>Total number of attendees: #local.qryReport.numAttendees#</strong></div>
			<div class="tsAppBodyText"><strong>Total number of evaluation forms: #local.qryReport.numResponses#</strong></div>
	
			<br>
			<table class="bodyText" cellpadding="4" cellspacing="0" width="100%">
			<tr>
				<td colspan="2"><strong>I. ACTIVITY EVALUATION</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td>Poor=1 Excellent=5</td>
						<th>&nbsp;</td>
						<th align="center" class="bodyText" width="35">1</th>
						<th align="center" class="bodyText" width="35">2</th>
						<th align="center" class="bodyText" width="35">3</th>
						<th align="center" class="bodyText" width="35">4</th>
						<th align="center" class="bodyText" width="35">5</th>
						<th>&nbsp;</th>
						<th align="center" class="bodyText" width="65">Average</th>
					</tr>
					<cfloop query="local.qrySection1">
					<tr>
						<td>#local.qrySection1.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection1.1#</td>
						<td align="center" class="bodyText">#local.qrySection1.2#</td>
						<td align="center" class="bodyText">#local.qrySection1.3#</td>
						<td align="center" class="bodyText">#local.qrySection1.4#</td>
						<td align="center" class="bodyText">#local.qrySection1.5#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection1.average#</td>
					</tr>
					</cfloop>
				</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><strong>II. IMPACT OF THE ACTIVITY</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td width="35%">A. Information presented:</td>
						<td width="1%">&nbsp;</td>
						<td align="center" class="bodyText">##(yes)</td>
						<td align="center" class="bodyText">%</td>
						<td align="center" class="bodyText"></td>
						<td align="center" class="bodyText"></td>
						<td></td>
					</tr>
					<cfloop query="local.qrySection2" startrow="1" endrow="4">
					<tr>
						<td>&nbsp;&nbsp;&nbsp;&nbsp;#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText"></td>
						<td align="center" class="bodyText"></td>
						<td></td>
					</tr>
					</cfloop>

					<tr><td colspan="7">&nbsp;</td></tr>

					<tr>
						<td></td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText" width="35">Agree</td>
						<td align="center" class="bodyText" width="35">%</td>
						<td align="center" class="bodyText" width="35">Disagree</td>
						<td align="center" class="bodyText" width="35">%</td>
						<td></td>
					</tr>
					<cfloop query="local.qrySection2" startrow="5" endrow="7">
					<tr>
						<td>#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.numNo#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctNo#</td>
						<td></td>
					</tr>
					</cfloop>
					<cfif local.qryDidPromote.recordcount>
					<tr>
						<td>If you disagreed with question D, please indicate which particular product or company was promoted.</td>
						<td>&nbsp;</td>
						<td colspan='5' class="bodyText">
							<table>
								<cfloop query="local.qryDidPromote">
									<tr><td>#local.qryDidPromote.didPromote#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>

					<tr>
						<td></td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">Agree</td>
						<td align="center" class="bodyText">%</td>
						<td align="center" class="bodyText">Disagree</td>
						<td align="center" class="bodyText">%</td>
						<td></td>
					</tr>
					</cfif>

					<cfloop query="local.qrySection2" startrow="8" endrow="8">
					<tr>
						<td>#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.numNo#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctNo#</td>
						<td></td>
					</tr>
					</cfloop>

					<cfif local.qryNotMetLearningObjectives.recordcount>
					<tr>
						<td>If you disagreed with question E, please indicate which learning objectives were not met</td>
						<td>&nbsp;</td>
						<td colspan='5' class="bodyText">
							<table>
								<cfloop query="local.qryNotMetLearningObjectives">
									<tr><td>#local.qryNotMetLearningObjectives.NotMetLearningObjectives#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>

					<tr>
						<td></td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">Agree</td>
						<td align="center" class="bodyText">%</td>
						<td align="center" class="bodyText">Disagree</td>
						<td align="center" class="bodyText">%</td>
						<td></td>
					</tr>
					</cfif>

					<cfloop query="local.qrySection2" startrow="9" endrow="9">
					<tr>
						<td>#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.numNo#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctNo#</td>
						<td></td>
					</tr>
					</cfloop>
					
					<cfif local.qryThingsDifferently.recordcount>
					<tr><td colspan="7">&nbsp;</td></tr>
					<tr>
						<td valign="top">List two things you will do differently</td>
						<td>&nbsp;</td>
						<td colspan="5">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 0>											
								<cfloop query="local.qryThingsDifferently">
									<cfset local.oddeven = local.oddeven + 1>				
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryThingsDifferently.ThingsDifferently#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>
					</cfif>
				</table>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td>#local.qryHowCommitted.question#</td>
						<th>&nbsp;</td>
						<th align="center" class="bodyText" width="35">1</th>
						<th align="center" class="bodyText" width="35">2</th>
						<th align="center" class="bodyText" width="35">3</th>
						<th align="center" class="bodyText" width="35">4</th>
						<th align="center" class="bodyText" width="35">5</th>
						<th>&nbsp;</th>
						<th align="center" class="bodyText" width="65">Average</th>
					</tr>
					<cfloop query="local.qryHowCommitted">
					<tr>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.1#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.2#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.3#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.4#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.5#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.average#</td>
					</tr>
					</cfloop>
				</table>
				</td>
			</tr>			
			<tr>
				<td colspan="2"><strong>III. ACTIVITY COMMENTS</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td valign="top">1. What aspects of this live training activity did you enjoy most?</td>
						<td>&nbsp;</td>
						<td class="bodyText">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 1>								
								<cfloop query="local.qryActivityEnjoyedMost">
									<cfset local.oddeven = local.oddeven + 1>
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryActivityEnjoyedMost.ActivityEnjoyedMost#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>
					<tr><td colspan="3"></td></tr>	
					<tr>
						<td valign="top">2. What aspects of this live training activity did you enjoy least?</td>
						<td>&nbsp;</td>
						<td class="bodyText">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 1>								
								<cfloop query="local.qryActivityEnjoyedLeast">
									<cfset local.oddeven = local.oddeven + 1>
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryActivityEnjoyedLeast.ActivityEnjoyedLeast#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>						
					<tr><td colspan="3">&nbsp;</td></tr>	
					<tr>
						<td valign="top">3. Please provide any additional comments about the seminar</td>
						<td>&nbsp;</td>
						<td class="bodyText">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 1>								
								<cfloop query="local.qryAdditionalComments">
									<cfset local.oddeven = local.oddeven + 1>									
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryAdditionalComments.AdditionalComments#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>						
					</table>
				</td>
			</tr>			
			<tr>
				<td colspan="2"><strong>IV. ACTIVITY SPEAKER</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td colspan="9"><strong>Speaker Name: #local.qryReport.speakerName#</strong></td>
					</tr>
					<tr>
						<td>Poor=1 Excellent=5</td>
						<th>&nbsp;</td>
						<th align="center" class="bodyText" width="35">1</th>
						<th align="center" class="bodyText" width="35">2</th>
						<th align="center" class="bodyText" width="35">3</th>
						<th align="center" class="bodyText" width="35">4</th>
						<th align="center" class="bodyText" width="35">5</th>
						<th>&nbsp;</th>
						<th align="center" class="bodyText" width="65">Average</th>
					</tr>
					<cfloop query="local.qryActivitySpeaker">
					<tr>
						<td>#local.qryActivitySpeaker.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.1#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.2#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.3#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.4#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.5#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.average#</td>
					</tr>
					</cfloop>
				</table>
				</td>
			</tr>			
			</table>
		</cfoutput>
		</cfsavecontent>
	
		<cfif local.doPDF eq 1>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='txrx')>
			<cfset local.reportFileName = "SummaryEvaluationReport.pdf">

			<cfdocument format="PDF" filename="#local.strFolder.folderPath#/#local.reportFileName#" margintop="1" marginbottom="1" marginright="1" marginleft="1" backgroundvisible="Yes" scale="100">
				<cfoutput>#local.showPage#</cfoutput>
			</cfdocument>

			<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfelse>
			<cfoutput><hr>#local.showPage#</cfoutput>
		</cfif>
	</cfif>
<cfoutput>
</div>
</cfoutput>