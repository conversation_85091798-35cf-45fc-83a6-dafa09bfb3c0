<cfinclude template="../commonUpgradeTrialSmithStep2JS.cfm">

<cfoutput>
<cfif DateDiff('m', variables.currentRenewalDate, DateFormat(now(),'mm/dd/yyyy')) GT 24  AND len(local.qryUpgradeTSPromotionalMessageForOldExpiredAccounts.rawContent)>
	<div class="upgradeTrialSmith-card upgradeTrialSmith-mt-3 upgradeTrialSmith-p-3">	
		<table border="0" cellspacing="0" cellpadding="4" width="100%">
			<tbody>
				<tr valign="">
					<td class="tsAppBodyText">
						#local.qryUpgradeTSPromotionalMessageForOldExpiredAccounts.rawContent#	
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</cfif>
<div class="upgradeTrialSmith-card upgradeTrialSmith-mt-3 upgradeTrialSmith-p-3">
	<div id="upgradeTrialSmithPlanContainer" <cfif len(local.selectedPlanString)> style="display: none;"</cfif>>
		<cfif len(local.codeErr)>
			<div class="alert alert-danger planError">
				#local.codeErr#
			</div>         
		</cfif> 
		<div class="tsAppLegendTitle upgradeTrialSmith-mt-0">#local.subscriptionOptionsTitle#</div>
		<div class="upgradeTrialSmith-mb-3">            
			<cfset local.showContinueButton = 0>
			<form name="frmJoin" action="#arguments.event.getValue('upgradetrialsmithurl')#" method="POST" class="form-inline">
				<cfif not application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='TrialSmithExpired') and (local.qryPlans.recordcount is 1) and (local.qryPlans.membertypeid is application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='MemberType'))>
					<table border="0" cellspacing="0" cellpadding="4" width="100%">
					<tbody>
						<tr valign="">
							<td class="tsAppBodyText">                                
								<b>Your account is active and expires on #dateformat(application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='renewaldate'),"mm/dd/yyyy")#</b>
							</td>
						</tr>
					</tbody>
					</table>
				<cfelse>
					 <table border="0" cellspacing="0" cellpadding="4" width="100%">
						<tbody>
							<tr valign="">
								<td class="tsAppBodyText">
									<cfif len(variables.cfcuser.subscriptionData['4'].strJoinTS.promoCode) is 0 >
										<p class="tsAppBodyText">If you have a Promotional Code, enter it here to view updated pricing.</p>
									<cfelseif len(variables.cfcuser.subscriptionData['4'].strJoinTS.promoCode)> 
										<cfif len(local.qryPlans.codeDescription) and local.strValidate.isvalid> 
											<p class="tsAppBodyText">
												<b>#variables.cfcuser.subscriptionData['4'].strJoinTS.promoCode#</b> - #local.qryPlans.codeDescription#
											</p>
										</cfif>
									</cfif> 
								</td>
								<td align="right">
									<cfif len(variables.cfcuser.subscriptionData['4'].strJoinTS.promoCode) and local.strValidate.isvalid>
										<button type="button" name="btnPromo" id="btnPromo" class="tsAppBodyButton" onClick="promoSubmit(0)" value="#variables.cfcuser.subscriptionData['4'].strJoinTS.promoCode#">Remove Promotional Code</button>
									<cfelse>                     
										<input type="text" name="promoCode" id="promoCode" size="12" value="">          
										<button type="button" name="btnPromo" class="tsAppBodyButton" onClick="promoSubmit(1)">Check Code</button>                            
									</cfif>
								</td>
							</tr>
						</tbody>
					</table>
				</cfif>
				<table cellspacing="0" cellpadding="1">
					<cfset local.showContinueButton = 0>
					<cfloop query="local.qryPlans">
						<cfset local.theMemberTypeID = local.qryPlans.membertypeid>
						<!--- get the best price (dont show tax in this table, so dont pass in billingstate) --->
						<cfset local.strPlanDetails = local.objPlans.getPlanDetails(orgcode=application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='TLAMemberState'),
							billingstate='', billingzip='', depoMemberDataID=variables.cfcuser.memberdata.depoMemberDataID, membertypeid=local.theMemberTypeID, 
							action=variables.cfcuser.subscriptionData['4'].strJoinTS.userAction, CurrentMembertypeID=application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='MemberType'),
							promoCode=variables.cfcuser.subscriptionData['4'].strJoinTS.promoCode)>
						<tr>
							<td width="20" rowspan="2" valign="top">
								<!--- plan must be 1 to show button --->
								<!--- expired should show all buttons --->
								<!--- not expired should show all buttons except current plan --->
								<!--- should allow user to renew current plan from 2 months before expiry date--->
								<cfif local.qryPlans.memberTypeStatus is 1 AND (application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='TrialSmithExpired') OR variables.isRenewDateWithin60days)>
									<cfset local.showContinueButton = 1>
									<input type="Radio" name="membertype" id="membertype" class="tsAppBodyText" value="#local.theMemberTypeID#" <cfif variables.cfcuser.subscriptionData['4'].strJoinTS.membertype is local.qryPlans.membertypeid> checked</cfif>>
								<cfelseif local.qryPlans.memberTypeStatus is 1 AND application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='MemberType') is not local.theMemberTypeID>
									<cfset local.showContinueButton = 1>
									<input type="Radio" name="membertype" id="membertype" class="tsAppBodyText" value="#local.theMemberTypeID#" <cfif variables.cfcuser.subscriptionData['4'].strJoinTS.membertype is local.qryPlans.membertypeid> checked</cfif>>
								</cfif>
							</td>
							<td class="tsAppBodyText" colspan="2">
								<cfif application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='MemberType') is not local.theMemberTypeID>
									<b>Upgrade to</b>
								</cfif>
								<b>#local.qryPlans.membertype#</b> 
								<cfif application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='MemberType') is local.theMemberTypeID>
									<span class="red">(YOUR CURRENT PLAN)</span>
									<cfif application.objUser.getSubscriptionData(cfcuser=variables.cfcuser, orgID='4', key='TrialSmithExpired') OR variables.isRenewDateWithin60days>
										- <b>#local.strPlanDetails.sel_PriceShown#</b> &nbsp;&nbsp; <cfif val(local.strPlanDetails.sel_YouSave) gt 0>(you save #DollarFormat(local.strPlanDetails.sel_YouSave)#)
										</cfif>
									</cfif>
								<cfelse>
									- <b>#local.strPlanDetails.sel_PriceShown#</b> &nbsp;&nbsp; <cfif val(local.strPlanDetails.sel_YouSave) gt 0>(you save #DollarFormat(local.strPlanDetails.sel_YouSave)#)</cfif>
								</cfif>
							</td>
						</tr>
						<tr valign="top">
							<td class="tsAppBodyText">#local.qryPlans.Description#</td>
							<td class="tsAppBodyText">#local.qryPlans.Description2#</td>
						</tr>
					</cfloop>
				</table>
			</form>
		</div>
	</div>

	<div id="upgradeTrialSmithSelectedPlanContainer" class="upgradeTrialSmithSummary upgradeTrialSmith-cursor-pointer" data-upgradetrialsmithsummarystep="3" <cfif len(local.selectedPlanString) EQ 0> style="display:none;" </cfif>>
		<div class="upgradeTrialSmith-d-flex">
			<a href="##" class="upgradeTrialSmith-align-self-center upgradeTrialSmith-mr-2 upgradeTrialSmith-font-size-lg upgradeTrialSmith-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="upgradeTrialSmith-col">
				<div id="upgradeTrialSmithSelectedPlan" class="upgradeTrialSmith-font-size-lg upgradeTrialSmith-font-weight-bold">
					#local.selectedPlanString#
				</div>
			</div>
		</div>
	</div>
	<div id="upgradeTrialSmithStep2SaveLoading" style="display:none;"></div>
</div>
</cfoutput>