<cfsavecontent variable="local.headCode">
	<cfoutput>
	<script language="javascript">
		const #toScript(local.strRootSub.qryRootSubscriber.subscriptionID,"mcsub_rootsubid")#
		const #toScript(local.rootSubscriberID,"mcsub_rootsubscriberid")#
		const #toScript(local.memberID,"mcsub_mid")#
		const #toScript(local.freeRateDisplay,"mcsub_freeratedisplay")#
		const #toScript(local.strRootSub.isRenewalRate ? 1 : 0,"mcsub_userenewalrate")#
		let #toScript(local.strRootSub.qryRootSubscriber.frequencyID,"mcsub_rootsubratefreqid")#
		let subAddOnsCache;

		/* common */
		function delaySubFunc(ms) {
			return new Promise(resolve => setTimeout(resolve, ms));
		}
		function formatCurrency(num) {
			num = num.toString().replace(/\$|\,/g,'');
			if(isNaN(num)) num = "0";
			num = Math.abs(num);
			sign = (num == (num = Math.abs(num)));
			num = Math.floor(num*100+0.50000000001);
			cents = num%100;
			num = Math.floor(num/100).toString();
			if(cents<10) cents = "0" + cents;
			for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
			return (((sign)?'':'-') + num + '.' + cents);
		}
		function hasErrorsInSub() {
			let hasErr = false;
			$('.mainSubCard').each(function() {
				hasErr = $(this).attr('data-errfields').length > 0;
				if (hasErr) return false;
			});
			return hasErr;
		}
		function enableSubCheckoutBtn() {
			if (hasErrorsInSub()) {
				disableSubCheckoutBtn();
			} else {
				doEnableSubCheckoutBtn();
			}
		}
		function doEnableSubCheckoutBtn() {
			$('##manageSubsContainer .btnCheckOut').prop('disabled',false);
			hideSubsFooterAlert();
		}
		function disableSubCheckoutBtn() {
			$('##manageSubsContainer .btnCheckOut').prop('disabled',true);
		}
		function defineEditHandlerForSubCards(mode) {
			let subCardScope = mode == 'init' 
								? $('.subCardSummary') 
								: $('##sub'+mcsub_rootsubid+'_addons').find('.subCardSummary');
			
			subCardScope.find('.subCardEditBtn').off('click').on('click',function() {
				let editBtn = $(this);
				if (editBtn.length) {
					let editMode = editBtn.data('editmode');
					if (editMode == 'editrootsub') {
						editRootSub();
					} else {
						let addonid = editBtn.data('addonid');
						let subscriptionid = editBtn.data('subscriptionid');
						subAddOnsCache = $('##sub'+mcsub_rootsubid+'_addons').clone();
						let subAddOnCard = editSubAddOn(addonid,subscriptionid);
						
						/* auto select an add-on sub */
						if ($('input[name="subAddOn'+addonid+'"]').length == 1 && $('input[name="subAddOn'+addonid+'"]').not(':checked').length == 1) {
							$('input[name="subAddOn'+addonid+'"]').trigger('click');
							delaySubFunc(50).then(function(){
								activateSubCard(subAddOnCard);
							});
						} else {
							activateSubCard(subAddOnCard);
						}
					}
				}
			});
		}
		function jumpToSubCard(scopeID) {
			var jumpTo = $('##' + scopeID);
			if(jumpTo.length && jumpTo.is(':visible')) {
				$('html, body').animate({
					scrollTop: jumpTo.offset().top - 175
				}, 750);
			}
		}

		/* init */
		function initManageSubs() {
			let fd = { freeratedisplay:mcsub_freeratedisplay, userenewalrate:mcsub_userenewalrate, loadMode:'init' };

			loadSubAddOns(fd)
				.then(function() {
					defineEditHandlerForSubCards('init');
					validateRootSubRate();
					validateOnLoadSubAddOns();
					prepSubCheckout(true);
				}).catch(function(e) {
					console.log(e);
				});
			
			/* prevent enter key submit */
			$('##frmSubs').on('keydown', function(e) {
				if (e.keyCode == 13) e.preventDefault();
			});
		}

		/* root sub */
		function editRootSub() {
			$('##rootSubSummary').addClass('mcsubs-d-none');
			$('##rootSubForm').removeClass('mcsubs-d-none');
			activateSubCard($('##rootSubWrapper'));
		}
		function confirmRootSubChanges() {
			disableSubCheckoutBtn();
			let rootSubForm = $('##rootSubForm');
			
			/* reverting to orig rate amts for unchecked rate fields having alerts */
			removeRateAlerts(rootSubForm);

			/* if editable price */
			if (rootSubForm.find('.editableRateFields').length) {
				let rootSubRFID = $('input[name="sub'+mcsub_rootsubid+'_rfid"]:checked').val();
				let editableRate = $('##newRateTotal_'+mcsub_rootsubid+'_'+rootSubRFID);
				if (editableRate.length) {
					doValidateRateAmt(editableRate[0],false);
				}
			}
			
			/* any alerts */
			let alertContainer = rootSubForm.find('.alert:first');
			if (alertContainer.length) {
				$('html, body').animate({
					scrollTop: alertContainer.offset().top - 175
				}, 750);

				return false;
			}

			inactivateSubCard($('##rootSubWrapper'));
			$('##rootSubSummary').removeClass('mcsubs-d-none');
			rootSubForm.addClass('mcsubs-d-none');
			enableSubCheckoutBtn();
			return true;
		}
		function validateRootSubRate() {
			let rootSubForm = $('##rootSubForm');
			let rootSubRFID = $('input[name="sub'+mcsub_rootsubid+'_rfid"]:checked').val();
			if (!rootSubForm.find('.editableRateFields').length) return;

			let editableRate = $('##newRateTotal_'+mcsub_rootsubid+'_'+rootSubRFID);
			if (editableRate.length) {
				editableRate.keyup();
			}
		}

		/* add-ons */
		function loadSubAddOns(fd) {
			return new Promise(function(resolve,reject) {
				$('##sub'+mcsub_rootsubid+'_addons')
					.html($('##subLoadingCard').html())
					.load('#local.loadSubAddOnsLink#', fd, resolve);
			});
		}
		function editSubAddOn(aoID,sid) {
			let subAddOnForm = $('##sub'+sid+'_addonID'+aoID+'_formWrapper');
			let subAddOnSummary = $('##sub'+sid+'_addonID'+aoID+'_summary');
			let subAddOnCard = subAddOnSummary.closest('.addOnCards');
			
			subAddOnSummary.addClass('mcsubs-d-none');
			subAddOnForm.removeClass('mcsubs-d-none');
			
			subAddOnForm.find('input.subSelector:checked').each(function(){
				let subID = $(this).val();
				let innersubAddOns = $('##sub'+subID+'_addons');
				innersubAddOns.removeClass('mcsubs-d-none');
			});

			if (typeof subAddOnsCache == "object") {
				subAddOnCard.find('.subCardCancelBtn').removeClass('mcsubs-d-none');
			} else {
				subAddOnCard.find('.subCardCancelBtn').addClass('mcsubs-d-none');
			}

			return subAddOnCard;
		}
		function confirmSubAddOn(aoID,sid,enableScroll){
			disableSubCheckoutBtn();
			
			let subAddOnForm = $('##sub'+sid+'_addonID'+aoID+'_formWrapper');
			let subAddOnSummary = $('##sub'+sid+'_addonID'+aoID+'_summary');
			let subAddOnCard = subAddOnSummary.closest('.addOnCards');

			/* revert to orig rate amts for unchecked sub rate fields having alerts */
			removeRateAlerts(subAddOnForm);

			subAddOnForm.find('input.subSelector:checked').each(function() {
				let thisSubID = $(this).val();
				
				/* sub rate selected */
				if ($("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").length) {
					$("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").prop('disabled',false);
					let rfid = $("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").val();
					hideSubMainCardAlert(subAddOnForm.closest('.mainSubCard'),$(this).attr('id'));

					/* validate editable rate amts */
					let editableRateField = $('##newRateTotal_'+thisSubID+'_'+rfid);
					if (editableRateField.length) {
						doValidateRateAmt(editableRateField[0],false);
					}
				} else {
					showSubMainCardAlert(subAddOnForm.closest('.mainSubCard'),$(this).attr('id'),'Select a Rate.');
					return false;
				}
			});

			/* any alerts */
			let alertContainer = subAddOnForm.find('.alert:first');
			if (alertContainer.length) {
				if (enableScroll) {
					$('html, body').animate({
						scrollTop: alertContainer.offset().top - 175
					}, 750);
				}

				return false;
			}

			/* min/max check */
			validateAddOnSubMinMaxSelections(subAddOnCard.find('.subAddOnFormContainer'));
			
			let addOnFooterContainer = subAddOnCard.find('.addOnFooterContainer');
			if (subAddOnCard.find('.alert').length) {
				addOnFooterContainer.find('.addOnBottomAlert').html('Please complete all required fields.').addClass('alert alert-error');
				return false;
			} else {
				addOnFooterContainer.find('.addOnBottomAlert').html('').removeClass('alert alert-error');
			}

			inactivateSubCard(subAddOnCard);
			subAddOnSummary.removeClass('mcsubs-d-none');
			subAddOnForm.addClass('mcsubs-d-none');

			if (enableScroll) {
				$('html, body').animate({
					scrollTop: subAddOnCard.offset().top - 175
				}, 750);
			}

			enableSubCheckoutBtn();
			return true;
		}
		function validateOnLoadSubAddOns() {
			if (!$('.subAddOnFormContainer').length) return false;
			
			$('.subAddOnFormContainer').each(function() {
				let addOnID = $(this).data('addonid');
				let maxAllowed = $(this).data('maxallowed');
				let inputName = 'subAddOn'+addOnID;

				/* disable unchecked checkboxes */
				if (maxAllowed > 1 && $('input.subSelector[name="'+inputName+'"]:checked').length >= maxAllowed) {
					$('input.subSelector[name="'+inputName+'"]').not(':checked').prop('disabled',true);
					$('input.subSelector[name="'+inputName+'"]:disabled').parent().find('label').addClass('mcsubs-text-dim');
				}
			});

			/* min/max checks */
			$('.addOnCards').each(function() {
				validateAddOnSubMinMaxSelections($(this).find('.subAddOnFormContainer'));
				let addOnFooterContainer = $(this).find('.addOnFooterContainer');
				if ($(this).find('.alert').length) {
					if ($(this).find('.addOnCardBody').attr('data-errcode') == 'setmax') {
						$(this).find('.inlineAddOn').addClass('mcsubs-opacity-4');
						$(this).find('.inlineAddOn .subSelector').prop('disabled',true);
					}
				}
			});

			/* expand add-ons having errors */
			$('.addOnCards').each(function() {
				if ($(this).attr('data-errfields').length) {
					let editBtn = $(this).find('.subCardEditBtn');
					let addonid = editBtn.data('addonid');
					let subscriptionid = editBtn.data('subscriptionid');
					subAddOnsCache = '';
					editSubAddOn(addonid,subscriptionid);
				}
			});
		}

		/* add-ons subs */
		function chooseSub(thisObj){
			let subID = $(thisObj).val();
			let addOnID = $(thisObj).data("addonid");
			let parentSubID = $(thisObj).data('parentsubscriptionid');
			let numRates = $("input.subRateRadio[rate-subscriptionid="+subID+"]").length;

			if(thisObj.checked) {
				/* checking a sub to automatically select the first sub rate */		
				if($("input.subRateRadio[rate-subscriptionid="+subID+"]:checked").length == 0) {
					$($("input.subRateRadio[rate-subscriptionid="+subID+"]")[0]).trigger("click");
				}

				validateAddOnSubSelections(parentSubID,addOnID,subID,true);

				/* show sub addons if any */
				$('##sub'+subID+'_addons').removeClass('mcsubs-d-none');

				/* show rate amt for a single rate sub and not a free sub */
				if ($(thisObj).attr('data-isfreeaddonsub') != 1) {
					$('.addOnSub'+subID+'SingleRateLabel').removeClass('mcsubs-d-none');
				}

				/* show edit price input box */
				if (numRates == 1) {
					$('.sub'+subID+'_rateWrapper').find('.editRatePrices').removeClass('mcsubs-d-none');
				}

			} else {
				validateAddOnSubSelections(parentSubID,addOnID,subID,false);

				/* uncheck selected rate */
				$("input.subRateRadio[rate-subscriptionid="+subID+"]:checked").prop('checked', false);
				
				/* uncheck sub-addon subs */
				$('##sub'+subID+'_addons').find('input.subSelector:checked').each(function() {
					let thisSubID = $(this).val();
					let thisAddOnID = $(this).data("addonid");
					uncheckSub(thisSubID,thisAddOnID);
				});

				uncheckSub(subID,addOnID);
			}

			prepSubCheckout(false);
		}
		function uncheckSub(subID,addOnID) {
			let numRates = $("input.subRateRadio[rate-subscriptionid="+subID+"]").length;

			$('.sub'+subID+'_addons input.subSelector:checked').prop('checked', false);
			$('.sub'+subID+'_addons input.subRateRadio:checked').prop('checked', false);
			$('.sub'+subID+'_addons input.subSelector').prop('disabled', false);

			$('##sub'+subID+'_addons, .addOnSub'+subID+'SingleRateLabel').addClass('mcsubs-d-none');
			$('##sub'+subID+'_addons').find('.addOnSubSingleRateLabel').addClass('mcsubs-d-none');
			$('.sub'+subID+'RateAmtDisp').removeClass('mcsubs-d-none');
			$('##sub'+subID+'_addons').find('.subRateAmtDisp').removeClass('mcsubs-d-none');

			/* hide edit price input box */
			let subRateContainer = $('.sub'+subID+'_rateWrapper');
			if (numRates == 1 && subRateContainer.length && subRateContainer.find('.editRatePrices').length) {
				let subMainCard = subRateContainer.closest('.mainSubCard');
				subRateContainer.removeClass('alert alert-error');
				subRateContainer.attr('data-errcode','');
				subRateContainer.find('.editRatePriceRange').removeClass('mcsubs-d-none');
				subRateContainer.find('.editRatePriceRangeErr').html('').addClass('mcsubs-d-none');
				subRateContainer.find('.editableRateFields').val(subRateContainer.find('.editableRateFields').data('origrateamt'));
				subRateContainer.find('.editRatePrices').addClass('mcsubs-d-none');
				hideSubMainCardAlert(subMainCard,subRateContainer.find('.editableRateFields').attr('id'));
			}
		}
		function validateAddOnSubSelections(parentSubID,addOnID,subID,isChecked) {
			let subAddOnForm =  $('##sub'+parentSubID+'_addonID'+addOnID+'_formWrapper');
			let addOnTitleContainer = $('.sub'+parentSubID+'_addonID'+addOnID+'_titleContainer');
			let subAddOnCard = subAddOnForm.closest('.addOnCards');
			let minAllowed = subAddOnForm.data('minallowed');
			let maxAllowed = subAddOnForm.data('maxallowed');
			let freeSubsCount = subAddOnForm.data('itemsfree');
			let inputName = 'subAddOn'+addOnID;
			let numCheckedSubs = $('input.subSelector[name="'+inputName+'"]:checked').length;
			
			if (maxAllowed == 1) {
				let checkedSubID = $('input.subSelector[name="'+inputName+'"]:checked').val();

				$('input.subSelector[name="'+inputName+'"][value!="'+checkedSubID+'"]').each(function() {
					let thisSubID = $(this).val();
					$("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").prop('checked', false);
					$('.sub'+thisSubID+'_addons input.subSelector:checked, .sub'+thisSubID+'_addons input.subRateRadio:checked').prop('checked', false);
					$('##sub'+thisSubID+'_addons').addClass('mcsubs-d-none');
				});
			} else if (maxAllowed > 1) {
				if ($('input.subSelector[name="'+inputName+'"]:checked').length >= maxAllowed) {
					$('input.subSelector[name="'+inputName+'"]').not(':checked').prop('disabled',true);
					$('input.subSelector[name="'+inputName+'"]:disabled').parent().find('label').addClass('mcsubs-text-dim');
				} else {
					$('input.subSelector[name="'+inputName+'"]').not(':checked').prop('disabled',false);
					$('input.subSelector[name="'+inputName+'"]:not(:disabled)').parent().find('label').removeClass('mcsubs-text-dim');
				}
			}

			/* validation errors */
			let hasErr = false;
			if (isChecked && numCheckedSubs > 0) {
				if (minAllowed > 0 && numCheckedSubs < minAllowed) {
					hasErr = true;
				} else if (maxAllowed > 0 && numCheckedSubs > maxAllowed) {
					hasErr = true;
				}
			}

			if (!hasErr) {
				let prevErrCode = subAddOnForm.attr('data-errcode');
				if (prevErrCode == 'setmax') {
					if (subAddOnForm.find('.inlineAddOn').length) {
						subAddOnForm.find('.inlineAddOn').removeClass('mcsubs-opacity-4');
						subAddOnForm.find('.inlineAddOn .subSelector').prop('disabled',false);

						subAddOnForm.find('.inlineAddOn').each(function() {
							let thisSubAddOnForm =  $(this).find('.subAddOnFormContainer');
							if (thisSubAddOnForm.attr('data-errcode') == 'setmax') {
								let thisSubAddOnID = thisSubAddOnForm.data('addonid');
								let thisSubAddOnSubInputName = 'subAddOn'+thisSubAddOnID;
								$('input.subSelector[name="'+thisSubAddOnSubInputName+'"]').not(':checked').prop('disabled',true);
								$('input.subSelector[name="'+thisSubAddOnSubInputName+'"]:disabled').parent().find('label').addClass('mcsubs-text-dim');
							}
						});
					}
				}

				/* remove alerts */
				subAddOnForm.attr('data-errcode','');
				hideSubMainCardAlert(subAddOnCard,subAddOnForm.attr('id'));

				addOnTitleContainer.removeClass('mcsubs-title-alert alert alert-error');
				addOnTitleContainer.find('.addOnTitleAlert').html('');
				subAddOnForm.find('.addOnSelMsg').removeClass('mcsubs-d-none');

				$('##sub'+subID+'_addons').find('.subAddOnFormContainer').each(function() {
					$(this).attr('data-errcode','');
					hideSubMainCardAlert(subAddOnCard,$(this).attr('id'));
				});

				let innerAddOnTitleContainer = $('##sub'+subID+'_addons').find('.innerAddOnTitleContainer');
				innerAddOnTitleContainer.removeClass('mcsubs-title-alert alert alert-error');
				innerAddOnTitleContainer.find('.addOnTitleAlert').html('');
				
				if (!subAddOnCard.find('.mcsubs-title-alert').length) {
					let addOnFooterContainer = subAddOnCard.find('.addOnFooterContainer');
					addOnFooterContainer.find('.addOnBottomAlert').html('').removeClass('alert alert-error');
				}

				enableSubCheckoutBtn();
			}

			if (freeSubsCount) {
				let arrSavedFreeSubs = $('##addOn'+addOnID+'_freeSubs').val().length ? $('##addOn'+addOnID+'_freeSubs').val().split(',').map(Number) : [];
				let arrFreeSubs = arrSavedFreeSubs.filter(function(freeSubID) { 
										return $('##subAddOn'+addOnID+'_'+freeSubID).is(':checked');
									});

				$('input.subSelector[name="'+inputName+'"]').not(':checked').each(function() {
					$(this).attr('data-isfreeaddonsub',0);
					$('.sub'+$(this).val()+'RateAmtDisp').removeClass('mcsubs-d-none');
					let index = arrFreeSubs.indexOf($(this).val());
					if (index > -1) {
						arrFreeSubs.splice(index, 1);
					}
				});

				let freeSubsCounter = arrFreeSubs.length;
				$('input.subSelector[name="'+inputName+'"]:checked').each(function() {
					let thisSubID = $(this).val();
					let rfid = $("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").val();

					/* if editable price */
					if ($('##newRateTotal_'+thisSubID+'_'+rfid).length) return;

					if (freeSubsCounter < freeSubsCount && arrFreeSubs.indexOf(Number(thisSubID)) == -1) {
						$(this).attr('data-isfreeaddonsub',1);
						arrFreeSubs.push(Number(thisSubID));
						freeSubsCounter++;
					}

					if ($(this).attr('data-isfreeaddonsub') == 1) {
						$('##sub'+thisSubID+'_rfid_'+rfid+'_rateAmtDisp').addClass('mcsubs-d-none');
					} else {
						$('##sub'+thisSubID+'_rfid_'+rfid+'_rateAmtDisp').removeClass('mcsubs-d-none');
					}
				});

				let freeSubIDList = arrFreeSubs.length ? arrFreeSubs.join(',') : '';
				$('##addOn'+addOnID+'_freeSubs').val(freeSubIDList);
			}
		}
		function validateAddOnSubMinMaxSelections(subAddOnFormContainers) {
			subAddOnFormContainers.each(function() {
				let subID = $(this).data('subscriptionid');
				if (!$('input.subSelector[value="'+subID+'"]').is(':checked')) return;
				
				let errMsg = '';
				let errCode = '';
				let minAllowed = $(this).data('minallowed');
				let maxAllowed = $(this).data('maxallowed');
				let addOnID = $(this).data('addonid');
				let inputName = 'subAddOn'+addOnID;
				let numCheckedSubs = $('input.subSelector[name="'+inputName+'"]:checked').length;
				let addOnTitleContainer = $('.subAddOn'+addOnID+'TitleContainer');
				let isInlineAddOn = $(this).parent().hasClass('inlineAddOn');
				let subMainCard = $(this).closest('.mainSubCard');
			
				if (minAllowed > 0 && numCheckedSubs < minAllowed) {
					errMsg = $(this).find('.addOnSelMsg').html();
					errCode = 'setmin';
				} else if (maxAllowed > 0 && numCheckedSubs > maxAllowed) {
					errMsg = $(this).find('.addOnSelMsg').html() + ' Current selections exceed the maximum of ' + maxAllowed + '.';
					errCode = 'setmax';
				}
				if (errMsg.length) {
					addOnTitleContainer.addClass('mcsubs-title-alert alert alert-error');
					addOnTitleContainer.find('.addOnTitleAlert').html(errMsg);
					$(this).find('.addOnSelMsg').addClass('mcsubs-d-none');
					$(this).attr('data-errcode',errCode);

					showSubMainCardAlert(subMainCard,$(this).attr('id'),(isInlineAddOn ? '' : errMsg));

				} else {
					addOnTitleContainer.removeClass('mcsubs-title-alert alert alert-error');
					addOnTitleContainer.find('.addOnTitleAlert').html('');
					$(this).find('.addOnSelMsg').removeClass('mcsubs-d-none');
					$(this).attr('data-errcode','');
					hideSubMainCardAlert(subMainCard,$(this).attr('id'));
				}
			});
		}
		
		/* sub rates */
		function chooseRate(thisObj) {
			if(thisObj.checked) {
				let freqID = $(thisObj).data("freqid");
				let rfid = $(thisObj).val();
				
				/* assoc sub */
				let subInput = $('##'+$(thisObj).data("linkedsubinputid"));
				let subID = subInput.val();
				
				if (!subInput.is(':checked')) {
					subInput.trigger("click");
				}

				hideSubMainCardAlert(subInput.closest('.mainSubCard'),subInput.attr('id'));

				/* hide all edit rate price fields */
				$('##sub'+subID+'_rates').find('.editRatePrices').addClass('mcsubs-d-none');
				/* show all rate prices */
				$('##sub'+subID+'_rates').find('.dspRatePrices').removeClass('mcsubs-d-none');

				/* can edit rate price */
				if ($('.editRatePrice'+rfid).length) {
					$('.editRatePrice'+rfid).removeClass('mcsubs-d-none');
					$('.dspRatePrice'+rfid).addClass('mcsubs-d-none');
				}

				let editableRateField = $('##newRateTotal_'+subID+'_'+rfid);
				if (editableRateField.length) {
					doValidateRateAmt(editableRateField[0],false);
				}

				/* root sub rate change */
				if (subID == mcsub_rootsubid && mcsub_rootsubratefreqid != freqID) {
					mcsub_rootsubratefreqid = freqID;
					
					let arrSelectedAddOns = $('##sub'+mcsub_rootsubid+'_addons').find('input.subSelector:checked').map(function() { return $(this).data('addonid'); }).get();
						arrSelectedAddOns = arrSelectedAddOns.filter((v, i, a) => a.indexOf(v) === i);
					let arrSelectedAddOnSubs = $('##sub'+mcsub_rootsubid+'_addons').find('input.subSelector:checked').map(function() { return $(this).val(); }).get();

					let fd = { parentRFID:rfid, freeratedisplay:mcsub_freeratedisplay, userenewalrate:mcsub_userenewalrate, 
								selectedAddOns:arrSelectedAddOns.join(','), selectedAddOnSubs:arrSelectedAddOnSubs.join(','), 
								loadMode:'reload' };

					arrSelectedAddOns.forEach(function(addOnID) {
						if ($('##addOn'+addOnID+'_freeSubs').length) {
							fd['addOn'+addOnID+'_freeSubs'] = $('##addOn'+addOnID+'_freeSubs').val();
						}
					});

					$('##sub'+mcsub_rootsubid+'_addons').find('input.subSelector:checked').each(function() {
						let thisAddOnID = $(this).data('addonid');
						let thisSubID = $(this).val();
						let selectedRate = $('input[name="sub'+thisSubID+'_rfid"]:checked');

						if (!selectedRate) return;

						fd['sub'+thisSubID+'_currRFID'] = selectedRate.val();
						fd['sub'+thisSubID+'_currFreqID'] = selectedRate.data('freqid');
						fd['sub'+thisSubID+'_currRateID'] = selectedRate.data('rateid');

						let editableRootSubRateField = $('##newRateTotal_'+thisSubID+'_'+selectedRate.val());
						if (editableRootSubRateField.length) {
							fd['sub'+thisSubID+'_newRateAmt'] = Number(editableRootSubRateField.val().replace(',',''));
						}
					});

					disableSubCheckoutBtn();

					/* reload addons */
					loadSubAddOns(fd).then(function() {
						defineEditHandlerForSubCards('reloadAddOn');
						validateOnLoadSubAddOns();
						prepSubCheckout(true);
					});
				}
			}

			removeRateAlerts($(thisObj).closest('.subsContainer'));
			prepSubCheckout(false);
		}
		function calculateSubRateTotals() {
			let grandTotal = 0, totalDiscount = 0;
			
			$('##rootSubWrapper,.addOnCardBody').each(function() {
				let subCardTotal = 0, subCardTotalDiscount = 0;
				let addOnID = $(this).data('addonid') ? $(this).data('addonid') : 0;
				let subID = $(this).data('subscriptionid');
				let subCard = addOnID > 0 ? $('##sub'+subID+'_addonID'+addOnID+'_card') : $('##rootSubWrapper');
				let selectedCount = $(this).find('input.subSelector:checked').length;
				let strSubCardTotals = {};

				$(this).find('input.subRateRadio:checked').each(function() {
					/* assoc sub not checked */
					let subInput = $('##'+$(this).data("linkedsubinputid"));
					if (!subInput.is(':checked')) return;

					/* is free sub */
					if (subInput.attr('data-isfreeaddonsub') == 1) return;

					let rfid = $(this).val();
					let freqCode = $(this).data('freq');
					let assocSubID = subInput.val();
					let rateID = $(this).data('rateid');

					if (!(freqCode in strSubCardTotals))  {
						strSubCardTotals[freqCode] = { freqName:$(this).data('freqname'), totalAmt:0 };
					}

					if ($('##newRateTotal_'+assocSubID+'_'+rfid).length) {
						let thisEditPriceField = $('##newRateTotal_'+assocSubID+'_'+rfid);
						let thisSubID = thisEditPriceField.data('subscriptionid');
						let thisRateWrapper = thisEditPriceField.closest('.sub'+thisSubID+'_rateWrapper');
						if (!thisRateWrapper.hasClass('alert')) {
							let thisRateAmt = Number(thisEditPriceField.val().replace(',',''));
							let thisRateTotalAmt = thisRateAmt * Number(thisEditPriceField.data('rateinstallments'));
							subCardTotal += Number(thisRateTotalAmt.toFixed(2));
							
							strSubCardTotals[freqCode].totalAmt += freqCode == 'F' ? thisRateTotalAmt : thisRateAmt;
						}

					} else {
						let thisRateAmt = Number(parseFloat($(this).attr("data-termprice")).toFixed(2));
						subCardTotal += thisRateAmt;
						strSubCardTotals[freqCode].totalAmt += freqCode == 'F' ? thisRateAmt : Number(parseFloat($(this).attr("data-price")).toFixed(2));
					}

					if ($('##subRateDiscount_'+assocSubID+'_'+rateID).length) {
						subCardTotalDiscount = Number(parseFloat(subCardTotalDiscount + Number($('##subRateDiscount_'+assocSubID+'_'+rateID).val())).toFixed(2));
					}
				});

				grandTotal += subCardTotal;
				totalDiscount += subCardTotalDiscount;

				let priceDisplay = '';
				if (selectedCount > 0 && Object.keys(strSubCardTotals).length) {
					let subCardFreqCode = Object.keys(strSubCardTotals)[0];
					
					if (strSubCardTotals[subCardFreqCode].totalAmt > 0) {
						priceDisplay = ' - $' + formatCurrency(strSubCardTotals[subCardFreqCode].totalAmt.toFixed(2));
						if (subCardFreqCode != 'F') priceDisplay += ' ' + strSubCardTotals[subCardFreqCode].freqName;
					} else if (mcsub_freeratedisplay.length) {
						priceDisplay = ' - ' + mcsub_freeratedisplay;
					}
				}

				subCard.find('.'+$(this).data('displaypriceelement')).html(priceDisplay);
			});

			$("##manageSubsContainer .grandTotal").html('$' + formatCurrency(grandTotal.toFixed(2)));

			if (totalDiscount) {
				let discountAppliedGrandTotal = Number(parseFloat(grandTotal - totalDiscount)).toFixed(2);
				$('.grandTotal').html('$' + formatCurrency(discountAppliedGrandTotal));
				$('.actualSubTotal').html('$' + formatCurrency(grandTotal.toFixed(2)));
				$('.totalSubDiscount').html('$' + formatCurrency(totalDiscount.toFixed(2)));
				$('##subRegCartTotalSummary').removeClass('mcsubs-d-none');
				$('##subRegCartTotal').addClass('mcsubs-d-none');
			} else {
				$('##subRegCartTotalSummary').addClass('mcsubs-d-none');
				$('##subRegCartTotal').removeClass('mcsubs-d-none');
			}
		}
		function onFocusEditRateField(thisObj) {
			let subID = $(thisObj).data('subscriptionid');
			let rfid = $(thisObj).data('rfid');
			let rateRadio = $('##sub'+subID+'_rfid_'+rfid);
			if (!rateRadio.is(':checked')) {
				rateRadio.prop('checked',true).trigger('change');
			}
		}
		function validateRateAmt(thisObj,byPassDelay) {
			let isFinalizedChange = (event && event.key == 'Enter') || !$(thisObj).is(':visible');
			
			if (isFinalizedChange || byPassDelay) {
				doValidateRateAmt(thisObj,isFinalizedChange);
			} else {
				delaySubFunc(1200).then(function() {
					doValidateRateAmt(thisObj,isFinalizedChange);
				});
			}
		}
		function doValidateRateAmt(thisObj,isFinalizedChange) {
			let thisRatePrice = formatCurrency($(thisObj).val()).replace('.00','');
			if (thisRatePrice == 0) $(thisObj).val(0);

			let minPrice = Number($(thisObj).data('ratemin'));
			let maxPrice = Number($(thisObj).data('ratemax'));
			let numInstallments = Number($(thisObj).data('rateinstallments'));
			let subID = $(thisObj).data('subscriptionid');
			let rfid = $(thisObj).data('rfid');
			let rateAmt = Number(thisRatePrice.replace(',',''));
			let totalAmt = rateAmt * numInstallments;

			if ($('##totalPrice'+rfid).length) {
				$('##totalPrice'+rfid).html('$'+formatCurrency(totalAmt));
			}

			if (minPrice > 0 || maxPrice > 0) {
				let errMsg = '';
				let errCode = '';
				let subRateContainer = $(thisObj).closest('.sub'+subID+'_rateWrapper');
				let subMainCard = $(thisObj).closest('.mainSubCard');
				
				if (minPrice > 0 && totalAmt < minPrice) {
					errMsg = 'Amount is below $'+formatCurrency(minPrice)+' Minimum';
					errCode = 'minprice';
				} else if (maxPrice > 0 && totalAmt > maxPrice) {
					errMsg = 'Amount exceeds $'+formatCurrency(maxPrice)+' Maximum';
					errCode = 'maxprice';
				}

				if (errMsg.length) {
					subRateContainer.find('.editRatePriceRange').addClass('mcsubs-d-none');
					subRateContainer.addClass('alert alert-error');
					subRateContainer.attr('data-errcode',errCode);
					subRateContainer.find('.editRatePriceRangeErr').html(errMsg).removeClass('mcsubs-d-none');

					let titleErrMsg = '';
					if (errCode == 'minprice') {
						titleErrMsg = 'Current selections do not meet the required minimum amount of $' + formatCurrency(minPrice) + '.';
					} else if (errCode == 'maxprice') {
						titleErrMsg = 'Current selections exceed the required maximum amount of $' + formatCurrency(maxPrice) + '.';
					}

					showSubMainCardAlert(subMainCard,$(thisObj).attr('id'),titleErrMsg);

				} else {
					subRateContainer.find('.editRatePriceRange').removeClass('mcsubs-d-none');
					subRateContainer.removeClass('alert alert-error');
					subRateContainer.attr('data-errcode','');
					subRateContainer.find('.editRatePriceRangeErr').html('').addClass('mcsubs-d-none');

					hideSubMainCardAlert(subMainCard,$(thisObj).attr('id'));
				}
			}

			removeRateAlerts($(thisObj).closest('.subsContainer'));
			prepSubCheckout(false);

			/* move focus away */
			if (isFinalizedChange) {
				$(thisObj).blur();
				return false;
			}
		}
		function onBlurRateAmt(thisObj) {
			let thisRatePrice = formatCurrency($(thisObj).val()).replace('.00','');
			$(thisObj).val(thisRatePrice);
		}
		function removeRateAlerts(formContainer) {
			let subMainCard = formContainer.closest('.mainSubCard');

			formContainer.find('.alert').each(function() {
				let thisRatePriceField = $(this).find('.editableRateFields');
				if (!thisRatePriceField.length) return;

				let assocRate = $('##sub'+thisRatePriceField.data('subscriptionid')+'_rfid_'+thisRatePriceField.data('rfid'));
				if (!assocRate.is(':checked')) {
					thisRatePriceField.val(thisRatePriceField.data('origrateamt'));
					$(this).removeClass('alert alert-error');
					$(this).find('.editRatePriceRange').removeClass('mcsubs-d-none');
					$(this).find('.editRatePriceRangeErr').addClass('mcsubs-d-none');
					hideSubMainCardAlert(subMainCard,thisRatePriceField.attr('id'));
				}
			});
		}
		function getCouponQualifiedSubs() {
			let arrQualifiedSubs = [];

			$('##rootSubWrapper,.addOnCardBody').each(function() {
				let addOnID = $(this).data('addonid') ? $(this).data('addonid') : 0;
				let subID = $(this).data('subscriptionid');
				let subCard = addOnID > 0 ? $('##sub'+subID+'_addonID'+addOnID+'_card') : $('##rootSubWrapper');
				let selectedCount = $(this).find('input.subSelector:checked').length;
				
				$(this).find('input.subRateRadio:checked').each(function() {
					/* assoc sub not checked */
					let subInput = $('##'+$(this).data("linkedsubinputid"));
					if (!subInput.is(':checked')) return;

					/* is free sub */
					if (subInput.attr('data-isfreeaddonsub') == 1) return;

					let rfid = $(this).val();
					let freqCode = $(this).data('freq');
					let assocSubID = subInput.val();
					let rateID = $(this).data('rateid');
					let thisSubPrice = 0;

					if ($('##newRateTotal_'+assocSubID+'_'+rfid).length) {
						let thisEditPriceField = $('##newRateTotal_'+assocSubID+'_'+rfid);
						let thisSubID = thisEditPriceField.data('subscriptionid');
						let thisRateWrapper = thisEditPriceField.closest('.sub'+thisSubID+'_rateWrapper');
						if (!thisRateWrapper.hasClass('alert')) {
							let thisRateAmt = Number(thisEditPriceField.val().replace(',',''));
							let thisRateTotalAmt = thisRateAmt * Number(thisEditPriceField.data('rateinstallments'));
							thisSubPrice = Number(thisRateTotalAmt);
						}

					} else {
						let thisRateAmt = Number(parseFloat($(this).attr("data-termprice")).toFixed(2));
						thisSubPrice = Number(thisRateAmt);
					}

					arrQualifiedSubs.push({ subscriptionid:assocSubID, rateid:rateID, parentsubscriptionid:subInput.data('parentsubscriptionid'), amt:thisSubPrice, numinstallments:Number($(this).data('rateinstallments')) });
				});
			});

			return arrQualifiedSubs;
		}
		function validateCouponCode() {
			var validateResult = function(r) {
				$('##btnApplyCouponCode').prop('disabled',false).text('Apply');
				$('##couponCode').val('');

				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.isvalidcoupon) {
						$('##subCouponContainer').addClass('mcsubs-d-none');
						prepSubCheckout(true);
					} else {
						$('##couponCodeResponse').html(r.couponresponse).show();
					}
				} else {
					if (r.couponresponse) {
						$('##couponCodeResponse').html(r.couponresponse).show();
					} else {
						$('##couponCodeResponse').html('Unable to apply coupon code. Try again.').show();
					}
				}
			};

			let couponCode = $('##couponCode').val().trim();

			if (couponCode.length) {
				$('##btnApplyCouponCode').prop('disabled',true).text('Applying...');
				$('##couponCodeResponse').html('').hide();

				let arrQualifiedSubs = getCouponQualifiedSubs();
				let objParams = { couponCode:couponCode, memberID:mcsub_mid, rootSubscriberID:mcsub_rootsubscriberid, subs:JSON.stringify(arrQualifiedSubs) };
				TS_AJX('SUBREG','validateCouponCode',objParams,validateResult,validateResult,10000,validateResult);
			} else {
				validateResult({ success:'false', couponresponse:'Invalid Promo Code' });
			}
		}
		function applyCouponToSubs(obj) {
			for (let subscriptionID in obj.strsubprice) {
				let discount = Number(obj.strsubprice[subscriptionID].discount);
				if (discount) {
					let rateID = obj.strsubprice[subscriptionID].rateid;
					$('##frmSubs').append('<input type="hidden" name="subRateDiscount_'+subscriptionID+'_'+rateID+'" id="subRateDiscount_'+subscriptionID+'_'+rateID+'" class="subCouponRateFields" value="'+discount+'">');
				}
			}
			$('.subCouponRedeemDetail').html(obj.redeemdetail);
		}
		function removeAppliedCoupon() {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					resetCouponSubs();
					prepSubCheckout(true);
				} else {
					$('.btnRemoveCoupon').prop('disabled',false).text('Remove Promo Code');
					alert('Unable to remove applied coupon. Try again.');
				}
			};

			$('.btnRemoveCoupon').prop('disabled',true).text('Removing...');
			let objParams = { rootSubscriberID:mcsub_rootsubscriberid, mid:mcsub_mid, topSubID:mcsub_rootsubid };
			TS_AJX('SUBREG','removeAppliedCoupon',objParams,removeResult,removeResult,10000,removeResult);
		}
		function resetCouponSubs() {
			$('##subCouponContainer').addClass('mcsubs-d-none');
			$('##btnApplyCouponCode').prop('disabled',false).text('Apply');
			$('##couponCode').val('');
			$('input.subCouponRateFields').remove();
			$('##couponCodeResponse').html('').hide();
			$('.discountAppliedSubTotal,.actualSubTotal,.totalSubDiscount').html('');
			$('.btnRemoveCoupon').prop('disabled',false).text('Remove Promo Code');
			$('##subRegCartTotalSummary').addClass('mcsubs-d-none');
			$('##subRegCartTotal').removeClass('mcsubs-d-none');
		}
		function subCouponHandler() {
			return new Promise(function(resolve,reject) {
				let result = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						resetCouponSubs();

						if (r.offercoupon) {
							$('##subCouponContainer').removeClass('mcsubs-d-none');
						} else if (r.couponapplied) {
							applyCouponToSubs(r);
						}

					} else {
						if (r.success && r.success.toLowerCase() == 'false' && r.couponresponse) {
							$('##couponCodeResponse').html(r.couponresponse).show();
						} else {
							$('##couponCodeResponse').html('Unable to apply coupon code. Try again.').show();
						}
					}

					resolve();
				};

				let arrQualifiedSubs = getCouponQualifiedSubs();
				let objParams = { memberID:mcsub_mid, rootSubscriberID:mcsub_rootsubscriberid, subs:JSON.stringify(arrQualifiedSubs) };
				TS_AJX('SUBREG','subCouponHandler',objParams,result,result,10000,result);
			}).catch(function(e) {
				console.log(e);
			});
		}
		function prepSubCheckout(enchkbtn) {
			subCouponHandler().then(function() {
				calculateSubRateTotals();
				updateSubCardSummary();

				if (enchkbtn) {
					enableSubCheckoutBtn();
				}
			}).catch(function(e) {
				console.log(e);
			});
		}

		/* summary */
		function updateSubCardSummary() {
			let rootSubRate = $('##rootSubForm').find('input.subRateRadio:checked');
			let rootSubRateRFID = rootSubRate.val();
			let editableRootSubRateField = $('##newRateTotal_'+mcsub_rootsubid+'_'+rootSubRateRFID);
			let editableRootSubRate = editableRootSubRateField.length == 1;

			let strRootSub = {
				subid:mcsub_rootsubid,
				subname:$('##sub'+mcsub_rootsubid).data("subscriptionname"),
				rateName:rootSubRate.data('ratename'),
				rfid:rootSubRateRFID,
				setid:0,
				setname:'',
				ordernum:1,
				editableRate:editableRootSubRate
			};

			if (editableRootSubRate) {
				let rootSubRateAmt = editableRootSubRateField.val().replace(',','');
				let totalRootSubRateAmt = Number(rootSubRateAmt) * Number(editableRootSubRateField.data('rateinstallments'));
				
				strRootSub.rateAmt = Number(rootSubRateAmt);
				strRootSub.totalRateAmt = Number(totalRootSubRateAmt.toFixed(2));
				strRootSub.freqCode = rootSubRate.data('freq');
				strRootSub.freqName = rootSubRate.data('freqname');
				strRootSub.rateMin = editableRootSubRateField.data('ratemin');
				strRootSub.rateMax = editableRootSubRateField.data('ratemax');
			}

			$('##rootSubRateInfo').html(getSubVerbose(strRootSub,0,0));
			if ($('##rootSubForm').find('input.subRateRadio').length > 1 || ($('##rootSubForm').find('input.subRateRadio').length && editableRootSubRate)) {
				$('##rootSubEditBtn').html('<span><i class="icon-edit"></i> Edit</span>').removeClass('mcsubs-d-none');
			} else {
				$('##rootSubEditBtn').remove();
			}
			
			$('.addOnCardBody').each(function() {
				let arrSubs = [], strSets = {}, childSetID = $(this).data('childsetid');

				$(this).find('input.subSelector:checked').each(function() {
					let setID = $(this).data('setid');
					let selectedSubID = $(this).val();
					let selectedSubRate = $('input.subRateRadio[name="sub'+selectedSubID+'_rfid"]:checked');
					let rateName = selectedSubRate.data('ratename');
					let rfid = selectedSubRate.val();
					let editableRateField = $('##newRateTotal_'+selectedSubID+'_'+rfid);
					let editableRate = editableRateField.length == 1;

					if (!(setID in strSets)) strSets[setID] = 1;
					else strSets[setID]++;

					let strSub = {
						subid:selectedSubID,
						subname:$(this).attr("data-subscriptionname"),
						rateName:rateName,
						rfid:rfid,
						setid:setID,
						setname:$('##subSet'+setID).data('setname'),
						ordernum:strSets[setID],
						inlineaddon:$('##subSet'+setID).data('inlineaddon'),
						editableRate:editableRate
					};

					if (editableRate) {
						let rateAmt = editableRateField.val().replace(',','');
						let totalRateAmt = Number(rateAmt) * Number(editableRateField.data('rateinstallments'));
						
						strSub.rateAmt = Number(rateAmt);
						strSub.totalRateAmt = Number(totalRateAmt.toFixed(2));
						strSub.freqCode = selectedSubRate.data('freq');
						strSub.freqName = selectedSubRate.data('freqname');
						strSub.rateMin = editableRateField.data('ratemin');
						strSub.rateMax = editableRateField.data('ratemax');
					}
					
					arrSubs.push(strSub);
				});

				let addOnID = $(this).data('addonid');
				let subID = $(this).data('subscriptionid');
				let subAddOnCard = $('##sub'+subID+'_addonID'+addOnID+'_card');
				let subAddOnSummary = $('##sub'+subID+'_addonID'+addOnID+'_summary').find('.addOnSummary');
				subAddOnSummary.html('');

				arrSubs.forEach(function(strSub) {
					let paddingLeft = strSub.inlineaddon && strSub.inlineaddon == 1 ? 15 : 0;
					let paddingTop = 0;
					
					if (strSub.setid != childSetID) {
						if (strSub.ordernum == 1) {
							subAddOnSummary.append('<div class="mcsubs-font-weight-bold mcsubs-mb-1 mcsubs-font-size-md" style="padding-left:'+paddingLeft+'px;">'+strSub.setname+'</div>');
						}
						paddingLeft += 10;
					}
					subAddOnSummary.append(getSubVerbose(strSub,paddingLeft,paddingTop));
				});

				subAddOnCard.find('.subCardEditBtn').html(arrSubs.length ? '<span><i class="icon-edit"></i> Edit</span>' : '<span><i class="icon-plus-sign"></i> Add</span>').removeClass('mcsubs-d-none');
			});

			/* selected add-on subs count */
			$('.subAddOnFormContainer').each(function() {
				let addOnID = $(this).data('addonid');
				let inputName = 'subAddOn'+addOnID;
				let numSubsSelected = $('input.subSelector[name="'+inputName+'"]:checked').length;
				$('.selectedSubsCount'+addOnID).html('(' + numSubsSelected + ' selected)');
			});
		}
		function getSubVerbose(strSub,paddingLeft,paddingTop) {
			let subHTML = '';

			if (strSub.editableRate) {
				subHTML += '<div class="mcsubs-d-flex mcsubs-flex-wrap" style="padding-left:'+paddingLeft+'px;padding-top:'+paddingTop+'px;">';
				if (strSub.subid == mcsub_rootsubid) {
					subHTML += '<div class="mcsubs-col-auto mcsubs-pr-5">' + strSub.rateName + '</div>';
				} else {
					subHTML += '<div class="mcsubs-col-auto mcsubs-pr-5">' + strSub.subname + '</div>';
				}
				if (strSub.rateMin > 0 || strSub.rateMax > 0) {
					subHTML += '<div class="mcsubs-col-auto mcsubs-text-dim">';
					if (strSub.rateMin > 0 && strSub.rateMax > 0) {
						subHTML += '$' + formatCurrency(strSub.rateMin) + ' Min - $' + formatCurrency(strSub.rateMax) + ' Max';
					} else if (strSub.rateMin > 0) {
						subHTML += '$' + formatCurrency(strSub.rateMin) + ' Minimum';
					} else {
						subHTML += '$' + formatCurrency(strSub.rateMax) + ' Maximum';
					}
					subHTML += '</div>';
				}
				subHTML += '</div>';
				subHTML += '<div class="mcsubs-d-flex mcsubs-mb-2 mcsubs-align-items-center" style="padding-left:'+paddingLeft+'px;padding-top:'+paddingTop+'px;">';
				subHTML += '<div class="mcsubs-col-auto '+(strSub.freqCode != 'F' ? ' mcsubs-pt-3' : '')+'">';
				subHTML += '<div class="mcsubs-font-size-xs mcsubs-text-dim mcsubs-text-center">Enter Amount</div><div class="mcsubs-input-prepend"><span class="mcsubs-add-on">$</span><input type="text" class="mcsubs-formcontrol" value="'+formatCurrency(strSub.rateAmt)+'" style="background-color:##f2f2f2 !important;color:##000 !important;" size="10" disabled></div>';

				if (strSub.freqCode != 'F') {
					subHTML += '<div class="mcsubs-font-size-sm mcsubs-text-dim mcsubs-text-center">Total: $'+formatCurrency(strSub.totalRateAmt)+'</div>';
				}
				subHTML += '</div>';

				if (strSub.freqCode != 'F') {
					subHTML += '<div class="mcsubs-col-auto">'+strSub.freqName+'</div>';
				}
				subHTML += '</div>';

			} else {
				subHTML += '<div class="mcsubs-d-flex mcsubs-align-items-center mcsubs-mb-2 mcsubs-flex-wrap" style="padding-left:'+paddingLeft+'px;padding-top:'+paddingTop+'px;">';
				if (strSub.subid == mcsub_rootsubid) {
					subHTML += '<div class="mcsubs-col-auto">' + strSub.rateName + '</div>';
				} else if (strSub.setname != strSub.subname) {
					subHTML += '<div class="mcsubs-col-auto">' + strSub.subname + '</div>';
				}
				subHTML += '</div>';
			}

			return subHTML;
		}
		
		/* form validation/cancel changes */
		function validateSubs() {
			disableSubCheckoutBtn();

			/* validate root sub card */
			if (!confirmRootSubChanges()) {
				$('##rootSubSummary').addClass('mcsubs-d-none');
				$('##rootSubForm').removeClass('mcsubs-d-none');
				showSubsFooterAlert('Please complete all required fields.');
				return false;
			}

			/* validate sub-addons */
			let arrErrSubAddOnCards = [];

			$('.addOnCardBody').each(function() {
				let subID = $(this).data('subscriptionid');
				let addOnID = $(this).data('addonid');
				let subAddOnForm = $('##sub'+subID+'_addonID'+addOnID+'_formWrapper');
				let subAddOnSummary = $('##sub'+subID+'_addonID'+addOnID+'_summary');
				let subAddOnCard = subAddOnSummary.closest('.addOnCards');
				confirmSubAddOn(addOnID,subID,false);

				if (subAddOnCard.find('.alert').length) {
					subAddOnCard.find('.subCardEditBtn').trigger('click');
					arrErrSubAddOnCards.push(subAddOnCard);
				}
			});

			let hasBillInfoErr = false;
			$('##stateIDforTax_err,##zipForTax_err').html('').hide();

			if (!$('##stateIDforTax').val().length) {
				hasBillInfoErr = true;
				$('##stateIDforTax_err').html('State/Province is required.').show();
			}
			if (!$('##zipForTax').val().length) {
				hasBillInfoErr = true;
				$('##zipForTax_err').html('Postal Code is required.').show();
			}
			if (!hasBillInfoErr && !mc_isValidBillingZip($('##zipForTax').val(),$('##stateIDforTax').val(),'')) {
				hasBillInfoErr = true;
				$('##zipForTax_err').html('Invalid Postal Code.').show();
			}

			if (arrErrSubAddOnCards.length || hasBillInfoErr) {
				showSubsFooterAlert('Please complete all required fields.');

				let errContainer;
				if (arrErrSubAddOnCards.length) {
					errContainer = arrErrSubAddOnCards[0];
				} else {
					errContainer = $('##billingInfoForm');
				}

				$('html, body').animate({
					scrollTop: errContainer.offset().top - 175
				}, 750);

				return false;
			}

			hideSubsFooterAlert();
			onSuccessValidateSubs();

			return true;
		}
		function onSuccessValidateSubs() {
			$('.btnCheckOut').parent().html('<i class="icon-spin icon-spinner"></i> Please wait...');
			$('.subCardEditBtn,.subCardConfirmBtn').remove();
		}
		function cancelSubAddOnChanges(addOnID,subID) {
			$('##sub'+mcsub_rootsubid+'_addons').replaceWith(subAddOnsCache.clone());
			defineEditHandlerForSubCards('reloadAddOn');
			validateOnLoadSubAddOns();
			prepSubCheckout(true);

			let subAddOnSummary = $('##sub'+subID+'_addonID'+addOnID+'_summary');
			let subAddOnCard = subAddOnSummary.closest('.addOnCards');
			inactivateSubCard(subAddOnCard);
		}

		/* sub cards */
		function activateSubCard(subCard) {
			subCard.addClass('mcsubs-active-card');
			subCard.find('.subsContainer:first').addClass('mcsubs-active-card-body');

			let activeSubCard = subCard[0];
			let clientHeight = document.documentElement.clientHeight;
			let activeCardTop = activeSubCard.getBoundingClientRect().y;
			let activeCardBottom = activeSubCard.getBoundingClientRect().bottom;
			let addOnSubFrontEndContentHeight = subCard.find('.addOnSubFrontEndContentHeight:first').height();
			let hdrFtrSubFEContentTotalHeight = 250 + addOnSubFrontEndContentHeight;
			
			if (activeCardBottom > clientHeight) {
				let calcHeight = parseInt(clientHeight - (activeCardTop + hdrFtrSubFEContentTotalHeight));

				const activateSubCardPromise = new Promise(function(resolve,reject) {
					if (calcHeight < hdrFtrSubFEContentTotalHeight && activeCardTop + calcHeight < activeCardBottom) {						
						let delayInMS = 750;

						$('html, body').animate({
							scrollTop: subCard.offset().top - 175
						}, delayInMS);

						delaySubFunc(delayInMS + 100)
						.then(function() {
							activeCardTop = activeSubCard.getBoundingClientRect().y;
							calcHeight = parseInt(clientHeight - (activeCardTop + hdrFtrSubFEContentTotalHeight));
							resolve();
						});
						
					} else {
						resolve();
					}
				}).then(function() {
					calcHeight = calcHeight < 300 ? 300 : calcHeight;
					subCard.find('.subsContainer:first').css('max-height',calcHeight+'px');
				});
			} else {
				calcHeight = parseInt(clientHeight/2) < 300 ? 300 : parseInt(clientHeight/2);
				subCard.find('.subsContainer:first').css('max-height',parseInt(clientHeight/2)+'px');
			}

			$('.mcsubs-overlay').removeClass('mcsubs-d-none');
		}
		function inactivateSubCard(subCard) {
			subCard.removeClass('mcsubs-active-card');
			subCard.find('.subsContainer:first').removeClass('mcsubs-active-card-body');
			subCard.find('.subsContainer:first').css('max-height','none');
			$('.mcsubs-overlay').addClass('mcsubs-d-none');
			subAddOnsCache = '';
		}

		/* alerts */
		function manageSubsAlert() {
			let arrErrAddOns = [];
			$('##sub'+mcsub_rootsubid+'_addons').find('.mainSubCard').each(function() {
				if ($(this).attr('data-errfields').length) {
					arrErrAddOns.push({ name:$(this).data('setname'), jumpto:$(this).attr('id') });
				}
			});

			if (arrErrAddOns.length) {
				let errMsg = '<div class="mcsubs-mb-2">The following Subscription Add-Ons need your attention. <span class="mcsubs-font-size-xs">(Click the Add-On for more details)</span></div><ul>';
				arrErrAddOns.forEach(function(strAddOn) {
					errMsg += '<li><a href="##" onclick="jumpToSubCard(\''+strAddOn.jumpto+'\');return false;">'+strAddOn.name+'</a></li>';
				});
				errMsg += '</ul>';
				showSubsAlert(errMsg);
			} else {
				hideSubsAlert();
			}
		}
		function showSubsAlert(msg) {
			$('##manageSubsHeaderAlert').html(msg).removeClass('mcsubs-d-none');
		}
		function hideSubsAlert() {
			$('##manageSubsHeaderAlert').html('').addClass('mcsubs-d-none');
		}
		function showSubMainCardAlert(subMainCard,errFld,errMsg) {
			let subMainCardTitleContainer = subMainCard.find('.'+subMainCard.data('linkedtitlecontainer'));
			let subMainCardTitleErrContainer = subMainCard.find('.'+subMainCard.data('linkedtitleerrorcontainer'));
			let subCardSummary = $('##'+subMainCard.data('linkedsummarycontainer'));
			let subCardForm = $('##'+subMainCard.data('linkedformcontainer'));

			addSubMainCardAlertElement(subMainCard,errFld);

			if (subMainCard.attr('data-errfields').length) {
				if (errMsg.length) {
					subMainCardTitleErrContainer.html(errMsg);
					subMainCardTitleContainer.addClass('mcsubs-title-alert alert alert-error');
				}
				subMainCard.addClass('mcsubs-border-danger');
				
				if (subCardForm.not(':visible')) {
					subCardSummary.addClass('mcsubs-d-none');
					subCardForm.removeClass('mcsubs-d-none');
				}
			}

			manageSubsAlert();
		}
		function hideSubMainCardAlert(subMainCard,errFld) {
			let subMainCardTitleContainer = subMainCard.find('.'+subMainCard.data('linkedtitlecontainer'));
			let subMainCardTitleErrContainer = subMainCard.find('.'+subMainCard.data('linkedtitleerrorcontainer'));

			removeSubMainCardAlertElement(subMainCard,errFld);
			
			if (!subMainCard.attr('data-errfields').length) {
				subMainCardTitleErrContainer.html('');
				subMainCardTitleContainer.removeClass('mcsubs-title-alert alert alert-error');
				subMainCard.removeClass('mcsubs-border-danger');
			}

			manageSubsAlert();
		}
		function addSubMainCardAlertElement(subMainCard,errFld) {
			let subMainCardErrFlds = subMainCard.attr('data-errfields') || '';
			let arrErrFlds = subMainCardErrFlds.length ? subMainCardErrFlds.split(',') : [];
			
			if (errFld.length && arrErrFlds.indexOf(errFld) == -1) {
				arrErrFlds.push(errFld);
			}
			subMainCard.attr('data-errfields',arrErrFlds.join(','));
		}
		function removeSubMainCardAlertElement(subMainCard,errFld) {
			let subMainCardErrFlds = subMainCard.attr('data-errfields') || '';
			let arrErrFlds = subMainCardErrFlds.length ? subMainCardErrFlds.split(',') : [];
			let index = arrErrFlds.indexOf(errFld);
			if (index > -1) {
				arrErrFlds.splice(index, 1);
			}
			subMainCard.attr('data-errfields',arrErrFlds.join(','));
		}
		function showSubsFooterAlert(msg) {
			$('##manageSubsFooterAlert').html(msg).removeClass('mcsubs-d-none');
		}
		function hideSubsFooterAlert() {
			$('##manageSubsFooterAlert').html('').addClass('mcsubs-d-none');
		}

		$(function() {
			initManageSubs();
		});
	</script>	
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.headCode#">