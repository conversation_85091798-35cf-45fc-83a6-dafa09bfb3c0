<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div style="padding-bottom:10px;">
	<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
</div>
</cfoutput>

<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		#showCommonNotFound(bucketID=arguments.bucketID, searchID=arguments.searchID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
<cfelse>
	<cfoutput>
	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='professional', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
	</cfoutput>
	<div class="clearfix"></div>
	<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>
	<!--- results table --->
	<cfoutput query="local.qryResults">
		<cfquery name="local.qryGetDAForName" dbtype="query" maxrows="1">
			select DiscActionCount, jurisdictions
			from [local].qryDiscActions
			where uniqueNameID = #local.qryResults.uniqueNameID#
		</cfquery>

		<cfif local.qryResults.inCart>
			<cfset local.inCartClass = "">
			<cfset local.addToCartClass = "hide">
		<cfelse>
			<cfset local.inCartClass = "hide">
			<cfset local.addToCartClass = "">
		</cfif>
		<div class="s_row<cfif local.qryResults.currentrow mod 2 is 0> s_row_alt</cfif>">
			<div class="pull-right">
				<div class="p-3 #local.inCartClass#" id="s_incart#local.qryResults.uniqueNameID#">
					<a href="javascript:viewCart();" title="View your cart/checkout"><i class="icon-shopping-cart icon-large"></i> In Cart</a>
				</div>
				<cfif not local.qryResults.owned and variables.cfcuser_DisplayVersion gt 1 >
					<div class="p-3 #local.addToCartClass#" id="s_addtocart#local.qryResults.uniqueNameID#">
						<a href="javascript:oneClickPurchaseDAReport(#local.qryResults.uniqueNameID#,#arguments.searchID#);" title="Buy Actions">
							<i class="icon-download icon-large"></i> View Report
						</a>
					</div>
				<cfelseif local.qryResults.owned>
					<div class="p-3">
						<a href="javascript:viewDAReport(#local.qryResults.owned#);" title="View Report">
							<i class="icon-download icon-large"></i> View Report
						</a>
					</div>
				</cfif>
			</div>
			<div>
				<img src="#application.paths.images.url#images/search/page.png" class="hidden-phone" width="16" height="16" border="0" align="left" alt="[Document]">
				<span class="hidden-phone">&nbsp;</span>
				<cfif len(local.qryResults.companyName) and len(local.qryResults.lastname) eq 0 and len(local.qryResults.firstname) eq 0>
					<b>#local.qryResults.companyName#</b>  (#local.qryGetDAForName.DiscActionCount# action<cfif local.qryGetDAForName.discActionCount is not 1>s</cfif>)
				<cfelse>
					<b>#local.qryResults.lastname#, #local.qryResults.firstname#<cfif len(local.qryResults.middlename)> #local.qryResults.middlename#</cfif></b> (#local.qryGetDAForName.DiscActionCount# action<cfif local.qryGetDAForName.discActionCount is not 1>s</cfif>)
				</cfif>
			</div>
			<div class="bk_subContent">
				<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned>
					<cfif len(local.qryResults.companyName)>
						Company Name: #local.qryResults.companyName#
						<br/>
					</cfif>
					<cfif len(local.qryResults.lastname) GT 0 OR len(local.qryResults.firstname) GT 0>								
						Full Name: #local.qryResults.salutation# #local.qryResults.firstname# #local.qryResults.middlename# #local.qryResults.lastname# #local.qryResults.suffix# 
							<cfif len(local.qryResults.professionalSuffix)>, #local.qryResults.professionalSuffix#</cfif>
							<br/>
					</cfif>	
					<cfif len(local.qryGetDAForName.jurisdictions)>
						Jurisdiction<cfif listLen(local.qryGetDAForName.jurisdictions) gt 1>s</cfif>: #local.qryGetDAForName.jurisdictions#<br/>
					</cfif>
				<cfelse>
					<a href="/?pg=upgradeTrialSmith">Hidden - Click here to upgrade your TrialSmith Plan and view ALL Disciplinary Action reports</a>
				</cfif>
			</div>
		</div>
	</cfoutput>
	<cfoutput>
	<br/>

	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startRow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='action', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
	</cfoutput>
</cfif>