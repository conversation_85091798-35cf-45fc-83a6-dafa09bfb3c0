<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>
<cfset local.thisPageName = "ceCreditReport">

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=MyCLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div class="tsAppHeading">Continuing Education Credit Report</div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact NYSTLA for assistance.
	</div>
	</cfoutput>

<cfelse>

	<cfset local.doPDF = arguments.event.getValue('pdf',0)>
	<cfset local.startDate = arguments.event.getValue('f_startdate','')>
	<cfset local.endDate = arguments.event.getValue('f_enddate','')>
	<cfif Len(local.startDate) eq 0>
		<cfset local.startDate = DateFormat(DateAdd("m", -11, CreateDate(year(now()), month(now()), 1)), "m/d/yyyy")>
	</cfif>
	<cfif Len(local.endDate) eq 0>
		<cfset local.endDate = DateFormat(CreateDate(year(now()), month(now()), daysinmonth(now())), "m/d/yyyy")>
	</cfif>
	<cfset local.crAuthority = arguments.event.getValue('f_selAgency',0)>

	<cfset local.memberInfo = application.objMember.getMemberInfo(session.cfcUser.memberData.memberID, arguments.event.getValue('mc_siteInfo.orgID'))>
	<cfset local.memberAddress = application.objMember.getMemberAddressByFirstAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=session.cfcUser.memberData.memberID)>
	<cfset local.memberEmail = application.objMember.getMainEmail(session.cfcUser.memberData.memberID)>

	<!--- Credit Authorities --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAuthorities">
		select ca.authorityID, ca.authorityName
		from dbo.ams_members m
		inner join dbo.ams_members mAll on mAll.activeMemberID = m.activeMemberID
			and m.orgID = #arguments.event.getValue('mc_siteInfo.orgID')#
			and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
		inner join dbo.ev_registrants r on r.memberID = mAll.memberID
		inner join dbo.crd_requests rc on rc.registrantID = r.registrantID
		inner join dbo.crd_offeringTypes ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_offerings ec on ec.offeringID = ect.offeringID
		inner join dbo.crd_authoritySponsors cas on cas.ASID = ec.ASID
		inner join dbo.crd_authorities ca on ca.authorityID = cas.authorityID
		<cfif local.doPDF eq 1>
			and ca.authorityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.crAuthority#">
		</cfif>
		group by ca.authorityID, ca.authorityName
		order by ca.authorityName
	</cfquery>

	<cfquery name="local.qrySiteCode" datasource="#application.dsn.membercentral.dsn#">
		select sitecode
		from dbo.sites
		where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteID')#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>

	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		select e.eventid, r.registrantID, r.dateRegistered, c.contentTitle, rc.creditValueAwarded, 
			isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,r.dateRegistered) as CLEYear,
			e.isAllDayEvent, e.lockTimeZoneID
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID AND evr.siteID = r.recordedOnSiteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid
		inner join dbo.ev_times et 
			on et.eventID = e.eventID  AND e.siteID = evr.siteID
			and et.timezoneID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(local.qrySiteCode.sitecode).defaultTimeZoneID#">
			and et.endTime >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.startDate#">
			and et.endTime <= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.endDate# 23:59:59.997">
		inner join dbo.cms_contentLanguages as c on c.contentID = e.eventContentID and c.languageID = 1
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		<cfif local.crAuthority neq 0>
			inner join dbo.crd_offerings ec on ec.offeringID = ect.offeringID
			inner join dbo.crd_authoritySponsors cas on cas.ASID = ec.ASID
			inner join dbo.crd_authorities ca on ca.authorityID = cas.authorityID
				and ca.authorityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.crAuthority#">
		</cfif>
		inner join dbo.ams_members as m on m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID 
			and r.memberid = mMerged.memberID
			and m.orgID = #arguments.event.getValue('mc_siteInfo.orgID')#
		order by r.dateRegistered desc, e.eventid
	</cfquery>
	<cfquery name="local.qryCLETotals" dbtype="query">
		select creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by creditType
		order by creditType
	</cfquery>
	
	<cfquery name="local.qryCLETotalsTotal" dbtype="query">
		select sum(totalCLE) as totalTotalCLE
		from [local].qryCLETotals
	</cfquery>
	
	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<cfif local.doPDF neq 1>
		<script language="JavaScript">
			function printIt() {
				self.location.href = '/?pg=#local.thisPageName#&f_startdate=#local.startDate#&f_enddate=#local.endDate#&f_selAgency=#local.crAuthority#&mode=direct&pdf=1';
			}
			$(function() {
				mca_setupDatePickerRangeFields('f_startdate','f_enddate');
			});
		</script>
		</cfif>
		<style type="text/css">
		##f_startdate, ##f_enddate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
		##clehistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">
	
	<cfsavecontent variable="local.showPage">
	<cfoutput>
	<div class="tsAppHeading" align="center">Continuing Education Credit Report</div>
	<br/>
	<cfif local.doPDF neq 1>
	<cfform name="frmReport"  id="frmReport" method="post" action="/?pg=#local.thisPageName#">
	<div align="center">
		<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
		<tr valign="top">
			<td align="right">Period:&nbsp;</td>
			<td align="left">
			<cfinput type="text" name="f_startdate"  id="f_startdate" value="#local.startDate#" size="16"> to 
			<cfinput type="text" name="f_enddate"  id="f_enddate" value="#local.endDate#" size="16"></td>
		</tr>
		<tr valign="top">
			<td align="right">Accrediting Agency:&nbsp;</td>
			<td align="left">
			<cfselect name="f_selAgency" id="f_selAgency" >
				<cfloop query="local.qryAuthorities">
					<option value="#local.qryAuthorities.authorityID#" <cfif local.crAuthority eq local.qryAuthorities.authorityID>selected</cfif>>#local.qryAuthorities.authorityName#</option>
				</cfloop>
			</cfselect> 
			</td>
		</tr>
		<tr valign="top">
			<td align="right">
			<cfif local.qryCLE.recordcount>
				<button type="button" class="tsAppBodyButton" style="width:100px;" onClick="printIt()"><i class="icon-print"></i> Print</button>
			<cfelse>
				&nbsp;
			</cfif>
			</td>			
			<td align="right">
			<button type="submit" class="tsAppBodyButton" onClick="return chkReport();">Filter Report</button>
			</td>
		</tr>
		</table>
	</div>
	</cfform>
	<cfelse>
	<div align="center">
		<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
		<tr valign="top">
			<td align="right">Period:&nbsp;</td>
			<td align="left">#local.startDate# to #local.endDate#</td>
		</tr>
		<tr valign="top">
			<td align="right">Accrediting Agency:&nbsp;</td>
			<td align="left">#local.qryAuthorities.authorityName#</td> 
		</tr>
		</table>
	</div>
	</cfif>
	<br>
		<cfif local.doPDF neq 1>
		</cfif>
		<table cellpadding="2" cellspacing="0" class="tsAppBodyText" width="100%">
			<tr>
				<td><b>Provider:</b></td>
				<td><b>Participant:</b></td>
			</tr>
			<tr>
				<td width="50%" valign="top">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
						<tr>
							<td valign="top">
								New York State Trial Lawyers Association <br>
								132 Nassau Street, 2nd Floor <br>
								New York NY 10038 <br>
								(************* <br>
								<b>Fax:</b>&nbsp; (************* <br>
								<EMAIL>
							</td>
						</tr>
					</table>
				</td>
				<td width="50%" valign="top">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
						<tr>
							<td valign="top">
								#Trim(Trim(local.memberInfo.firstName & ' ' & local.memberInfo.middleName) & ' ' & local.memberInfo.lastName & ' ' & local.memberInfo.suffix)# <br>
								<cfif local.memberAddress.hasAttn and len(local.memberAddress.attn)>#local.memberAddress.attn#<br/></cfif>
								<cfif len(local.memberAddress.address1)>#local.memberAddress.address1#</cfif><br/>
								<cfif local.memberAddress.hasAddress2 and len(local.memberAddress.address2)>#local.memberAddress.address2#<br/></cfif>
								<cfif local.memberAddress.hasAddress3 and len(local.memberAddress.address3)>#local.memberAddress.address3#<br/></cfif>
								<cfif len(local.memberAddress.city)>#local.memberAddress.city#,</cfif> 
								<cfif len(local.memberAddress.stateName)>#local.memberAddress.stateName#</cfif> 
								<cfif len(local.memberAddress.postalcode)>#local.memberAddress.postalcode#</cfif><br/>
								<cfif len(local.memberAddress.phone)>#local.memberAddress.phone#</cfif><br/>
								<cfif len(local.memberEmail.email)>#local.memberEmail.email#</cfif>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>	
		<br>
		<div class="tsAppHeading" align="center">CREDIT DETAILED REPORT</div>
		<br/>
	</cfoutput>
	
	<cfif local.qryCLE.recordcount>
		<cfoutput>

		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory2">
		<cfset local.oddeven = 0>
		</cfoutput>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			<cfset local.creditSum = 0>
			<cfsavecontent variable="local.creditSubjects">
			<cfoutput>
				<cfset local.creditSum = local.creditSum + local.qryCLE.creditValueAwarded>
				&nbsp;&nbsp;&nbsp;&nbsp;#local.qryCLE.creditType#<br/>
			</cfoutput>
			</cfsavecontent>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCalendar">
				SET NOCOUNT ON;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(local.qrySiteCode.sitecode).siteID#">;

				select ai.applicationInstanceName 
				from (
					select top 1 sourceCalendarID as calendarID
					from dbo.ev_calendarEvents
					where sourceEventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCLE.eventid#">
					group by sourceCalendarID
				) x
				inner join dbo.ev_calendars ec on ec.siteID = @siteID and ec.calendarID = x.calendarID
				inner join dbo.cms_applicationInstances ai on ai.siteID = @siteID and ai.applicationInstanceID = ec.applicationInstanceID;
			</cfquery>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEventTimes">
				EXEC dbo.ev_getTimesByEventID @eventID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCLE.eventid#">
			</cfquery>			
			<cfif local.qryCLE.lockTimeZoneID gt 0>
				<cfquery name="local.qryEventTimes_selected" dbtype="query">
					select *
					from [local].qryEventTimes
					where timezoneID = #local.qryCLE.lockTimeZoneID#
				</cfquery>
			<cfelse>
				<cfquery name="local.qryEventTimes_selected" dbtype="query">
					select *
					from [local].qryEventTimes
					where timezoneID = #application.objSiteInfo.getSiteInfo(local.qrySiteCode.sitecode).defaultTimeZoneID#
				</cfquery>
			</cfif>
			<cfset local.eventtime = "">
			<cfif local.qryCLE.isAllDayEvent>
				<cfif Month(local.qryEventTimes_selected.endTime) is not Month(local.qryEventTimes_selected.startTime)
					or Year(local.qryEventTimes_selected.endTime) is not Year(local.qryEventTimes_selected.startTime)>
					<cfset local.eventtime = local.eventtime & '#DateFormat(local.qryEventTimes_selected.startTime, "mmmm d, yyyy")#'>
					<cfset local.eventtime = local.eventtime & ' - #DateFormat(local.qryEventTimes_selected.endTime, "mmmm d, yyyy")#'>
				<cfelse>
					<cfset local.eventtime = local.eventtime & '#DateFormat(local.qryEventTimes_selected.startTime, "mmmm d")#'>

					<cfif DateCompare(local.qryEventTimes_selected.endTime,local.qryEventTimes_selected.startTime,"d")>
						<cfset local.eventtime = local.eventtime & '-#DateFormat(local.qryEventTimes_selected.endTime, "d")#'>
					</cfif>
					<cfset local.eventtime = local.eventtime & ' #DateFormat(local.qryEventTimes_selected.startTime, ", yyyy")#'>
				</cfif>
			<cfelse>
				<cfset local.eventtime = local.eventtime & '#DateFormat(local.qryEventTimes_selected.startTime, "mmmm d, yyyy")# '>
				<cfif DateCompare(local.qryEventTimes_selected.endTime,local.qryEventTimes_selected.startTime,"d") is 0 and
					DateDiff("n",local.qryEventTimes_selected.endTime,local.qryEventTimes_selected.startTime) is 0>
					<cfset local.eventtime = local.eventtime & ' #TimeFormat(local.qryEventTimes_selected.startTime, "h:mm TT")# #local.qryEventTimes_selected.timeZoneAbbr#'>
				<cfelseif DateCompare(local.qryEventTimes_selected.endTime,local.qryEventTimes_selected.startTime,"d") is 0>
					<cfset local.eventtime = local.eventtime & ' #TimeFormat(local.qryEventTimes_selected.startTime, "h:mm TT")# '>
					<cfset local.eventtime = local.eventtime & '- #timeformat(local.qryEventTimes_selected.endTime,"h:mm TT")# #local.qryEventTimes_selected.timeZoneAbbr#'>
				<cfelse>
					<cfset local.eventtime = local.eventtime & ' #TimeFormat(local.qryEventTimes_selected.startTime, "h:mm TT")# '>
					<cfset local.eventtime = local.eventtime & '- #DateFormat(local.qryEventTimes_selected.endTime, "mmmm d, yyyy")#'>
					<cfset local.eventtime = local.eventtime & '#TimeFormat(local.qryEventTimes_selected.endTime, "h:mm TT")# #local.qryEventTimes_selected.timeZoneAbbr#'>
				</cfif>
			</cfif>
			
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" width="30" nowrap><b>Date </b></td>
				<td class="tsAppBodyText" width="2"><b>:</b>&nbsp;</td>
				<td class="tsAppBodyText" colspan="6">#local.eventtime# </td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" align="left" nowrap><b>Event</b></td>
				<td class="tsAppBodyText"><b>:</b>&nbsp;</td>
				<td class="tsAppBodyText" align="left" colspan="6">#local.qryCLE.contentTitle# <br><br></td>
			</tr>	
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="right" nowrap><b>Participation Method :</b>&nbsp; </td>
				<td class="tsAppBodyText" align="left">Attended</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" colspan="4" nowrap><b>Credits :</b>&nbsp;#local.creditSum# units</td>
				<td class="tsAppBodyText" align="right" nowrap><b>Accreditation Number :</b>&nbsp; </td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<cfif local.doPDF neq 1>
				<td class="tsAppBodyText" align="right">&nbsp;</td>
				<cfelse>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				</cfif>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" align="left" nowrap colspan="8"><b>Credits in Subjects :</b>&nbsp;</td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" class="tsAppBodyText" colspan="8" nowrap>#local.creditSubjects#<br></td>
			</tr>
			<tr>
				<td class="tsAppBodyText" style="border-bottom: 1px dashed ##ccc;" colspan="8" nowrap>&nbsp;</td>
			</tr>
		</cfoutput>
		<cfoutput>
		</table>		
		<br>
		<table cellpadding="2" cellspacing="0" border="0">
		<tr><td colspan="2" class="tsAppBodyText"><b>Credit Totals</b></td></tr>
		<cfset local.totalYear = "">
		<cfloop query="local.qryCLETotals">
			<tr valign="top">
				<td class="tsAppBodyText">&nbsp;&nbsp;&nbsp;&nbsp;#creditType#</td>
				<td class="tsAppBodyText">&nbsp;</td>
				<td class="tsAppBodyText" align="right"><b>#local.qryCLETotals.totalCLE#</b> credits</td>
			</tr>
		</cfloop>
			<tr valign="top">
				<td class="tsAppBodyText" align="right"><br><b>Total</b></td>
				<td class="tsAppBodyText">&nbsp;</td>
				<td class="tsAppBodyText" align="right"><br><b>#local.qryCLETotalsTotal.totalTotalCLE#</b> credits</td>
			</tr>
		</table>
		<br/>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText" align="center">
			<br>
			<b>There are no credits to report for this period.</b>
		</div>
		</cfoutput>
	</cfif>
	</cfsaveContent>
		
	<cfif local.doPDF eq 1>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='NY')>
		<cfset local.reportFileName = "EventCredits.pdf">

		<cfdocument format="PDF" filename="#local.strFolder.folderPath#/#local.reportFileName#" margintop="1" marginbottom="1" marginright="1" marginleft="1" backgroundvisible="Yes" scale="100">
			<cfoutput>#local.showPage#</cfoutput>
		</cfdocument>

		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
	<cfelse>
		<cfoutput>#local.showPage#</cfoutput>
	</cfif>
</cfif>