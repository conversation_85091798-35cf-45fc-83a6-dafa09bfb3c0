<cfset dataStruct = attributes.data.actionStruct>
<cfset instanceSettings = attributes.data.instanceSettings>

<!--- Get sidebar position setting --->
<cfset local.sidebarPosition = "right">
<cfif isXML(instanceSettings.settingsXML)>
	<cfset local.sidebarPositionSetting = xmlSearch(instanceSettings.settingsXML, 'string(/settings/setting[@name="sidebarPosition"]/@value)')>
	<cfif Len(local.sidebarPositionSetting) and listFindNoCase("right,left",local.sidebarPositionSetting)>
		<cfset local.sidebarPosition = local.sidebarPositionSetting>
	</cfif>
</cfif>

<cfset local.isBot = (dataStruct.isbot ?: 0)>

<cfsavecontent variable="local.evhead">
	<cfoutput>
	<style type="text/css">
		span.ev_heading { font-weight:bold; background-color:##DEDEDE; border:1px solid ##ccc; padding:2px; }
		div.ev_infoarea { margin-bottom:14px; border-left:1px solid ##DEDEDE; }
		div##evTitle { margin-top:6px; }
		div.sidebox { border:1px solid ##DEDEDE; margin-bottom:20px; }
		div.sidebox div.sideboxtitle { padding:4px; background-color:##DEDEDE; font-weight:bold; }
		div.sidebox div.sideboxbody { padding:4px; overflow-wrap: anywhere; }
		div##loadingIndicator { display:none; }
		.event-layout-flex {
			display: flex;
			flex-wrap: wrap;
		}
		.event-layout-flex .event-main-content {
			flex: 0 0 74.46808511%; 
			max-width: 74.46808511%;
			min-height: 1px;
		}
		.event-layout-flex .event-sidebar {
			flex: 0 0 23.40425532%; 
			max-width: 23.40425532%;
			min-height: 1px;
		}
		.event-layout-flex .event-main-content {
			order: 1;
			margin-left: 0; 
		}
		.event-layout-flex .event-sidebar {
			order: 2;
			margin-left: 2.127659574%; 
		}
		.event-layout-flex.sidebar-left .event-sidebar {
			order: 1;
			margin-left: 0; 
		}
		.event-layout-flex.sidebar-left .event-main-content {
			order: 2;
			margin-left: 2.127659574%; 
		}

		@media (min-width: 768px) and (max-width: 979px) {
			.event-layout-flex .event-main-content,
			.event-layout-flex.sidebar-left .event-main-content {
				margin-left: 0;
			}
			.event-layout-flex.sidebar-left .event-sidebar {
		        margin-right: 2.127659574%;
		    }
		}
		@media (max-width: 767px) {
			.event-layout-flex {
				display: block; 
			}
			.event-layout-flex .event-main-content,
			.event-layout-flex .event-sidebar {
				flex: none;
				max-width: none;
				width: 100%;
				margin-left: 0;
				padding-left: 0;
				padding-right: 0;
			}
			.event-layout-flex .event-main-content,
			.event-layout-flex .event-sidebar,
			.event-layout-flex.sidebar-left .event-main-content,
			.event-layout-flex.sidebar-left .event-sidebar {
				order: initial;
			}
		}
		.rsvp-alert-success { background:##f1ffed url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 40px; color: ##116d2f;
			background-color: ##d1f4d9;
			border-color: ##bff0ca;
			border-radius: .65rem;
		}
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
	</style>
	
	<cfif NOT local.isBot>
		<script type="text/javascript">var switchTo5x=true;</script>	
		<script type="text/javascript" src="https://ws.sharethis.com/button/buttons.js"></script>
		<script type="text/javascript">stLight.options({ publisher:'b8181e8a-537f-4fcd-9253-53d15b7080c3', tracking:'google', doneScreen:'false' });</script>
	</cfif>

	<script language="javascript">
		<cfif NOT local.isBot>
			function registerEv() {
				<cfoutput>var #ToScript(dataStruct.regLink,"regpage")#</cfoutput>
				self.location.href=regpage;
			}
		</cfif>

		<cfif NOT local.isBot and dataStruct.registrationType eq "RSVP">
			function checkRSVP() {
				var theForm = document.forms['frmRSVP'];
				var okToSubmit = false;
				for (var i=1; i<=5; i++) {
					if ((document.getElementById('firstname' + i).value.length > 0 || document.getElementById('lastname' + i).value.length > 0 || document.getElementById('email' + i).value.length > 0 || document.getElementById('phone' + i).value.length > 0) && (document.getElementById('firstname' + i).value.length == 0 || document.getElementById('lastname' + i).value.length == 0)) {
						showAlert('<b>First Name</b> and <b>Last Name</b> are required for all RSVPs.\n\nRSVP ##' + i + ' is missing this information.');
						return false;
					}
					if (document.getElementById('firstname' + i).value.length > 0 && document.getElementById('lastname' + i).value.length > 0 && (document.getElementById('email' + i).value.length > 0 || document.getElementById('phone' + i).value.length > 0))
						okToSubmit = true;
				}
				if (!okToSubmit) {
					showAlert('<b>There are no RSVPs to send.</b> Enter at least one RSVP for this event.<br/>Each RSVP must have a first and last name as well as contact information.');
					return false;
				}
				return true;
			}
			function attachEvent(evt,element,cb) { 
				if (element.addEventListener) element.addEventListener(evt,cb,false); 
				else if (element.attachEvent) element.attachEvent('on' + evt, cb); 
			} 
			function setListener(evt,func) { 
				var ele = document.forms['frmRSVP'].elements; 
				for (var i=0; i<ele.length;i++) { 
					var element = ele[i]; 
					if (element.type) { 
						switch (element.type) { 
							case 'checkbox': case 'radio': case 'password': case 'text': case 'textarea': case 'select-one': case 'select-multiple': attachEvent(evt,element,func); 
						} 
					} 
				} 
			}
			function hideAlert() {
				document.getElementById('rsvperr').style.display = 'none';
				document.getElementById('rsvpmsg').style.display = '';
			}
			function showAlert(msg) {
				document.getElementById('rsvpmsg').style.display = 'none';
				var abox = document.getElementById('rsvperr');
					abox.innerHTML = msg;
					abox.className = 'alert';
					abox.style.display = '';
			}		
		</cfif>
		
		<cfif (dataStruct.canDelEv OR dataStruct.canEditEv) AND dataStruct.enteredByMemberID eq dataStruct.orgMemberID>
			<cfoutput>
			function gotoEditDetail(edID) {
				var strLoc = '#dataStruct.mainurl#&evAction=editEv&eID=' + edID;
				<cfif len(dataStruct.subAction)>
					strLoc += '&evSubAction=#dataStruct.subAction#';
				</cfif>
				self.location.href=strLoc;
			}
			function confirmDelete(delID) {
				if (confirm("Are you sure you want to delete this Event?")) {
					var strLoc = '#dataStruct.mainurl#&evAction=delEv&eID=' + delID;
					<cfif len(dataStruct.subAction)>
						strLoc += '&evSubAction=#dataStruct.subAction#';
					</cfif>
					self.location.href=strLoc;
				}
			}
			</cfoutput>
		</cfif>		

		<cfif dataStruct.qryRegistrant.recordCount gt 0>
			<cfoutput>
			function viewRegister() {
				$.colorbox( {innerWidth:800, innerHeight:500, href:'#dataStruct.mainurl#&evAction=printReg&eID=#dataStruct.eventID#&mid=#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=instanceSettings.orgID)#&registrantID=#dataStruct.qryRegistrant.registrantID#&mode=direct', iframe:true, overlayClose:false} );
				return false;
			}
			</cfoutput>
		</cfif>
		
		<cfif dataStruct.regEditAllowed is 1 and dataStruct.showEditRegLink is 1>
			function editEVRegistration() {
				self.location.href='#dataStruct.editRegLink#';
			}
		</cfif>
		
		<cfif dataStruct.eventStruct.hasDoc EQ 1 AND (dataStruct.eventStruct.qryEventDocs.recordCount OR dataStruct.eventStruct.qrySubEventDocs.recordCount)>
		function eventDocDownload(eid, documentID, eventTitle, docTitle, fileExt) {
			var downloadResult = function(r) {
				if(r.success == "true"){
					triggerEventFileDownload(eid, eventTitle, docTitle, fileExt);
					self.location.href = r.stdownloadurl;
				}else{
					console.log(r.errmsg);
				}
			};
			var objParams = { eid:eid, documentID:documentID };
			TS_AJX('EV','getEventDocumentDownloadLink',objParams,downloadResult,downloadResult,120000,downloadResult);
		}
		</cfif>

		<cfif NOT local.isBot and dataStruct.showAddToCalendar>
		function handleGtagCalendarAction(calendarType){
			triggerAddToMyCalendar(#dataStruct.eventID#, "#encodeForJavascript(dataStruct.eventContentTitle)#", calendarType);
		}
		</cfif>

		$(function() {
			<cfset local.categories = []>
			<cfloop query="dataStruct.qryEventCategories">
				<cfset arrayAppend(local.categories, {
					"categoryID": dataStruct.qryEventCategories.categoryID,
					"categoryName": dataStruct.qryEventCategories.category
				})>
			</cfloop>

			<cfset local.arrEventList = [{
				"eventID": dataStruct.eventID,
				"title": dataStruct.eventContentTitle,
				"categories": local.categories
			}]>

			MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
			.then( () => {
				try {
					triggerEventViewItem(#SerializeJSON(local.arrEventList)#);
				} catch (error) {
					console.error("Error parsing JSON:", error.message);
				}
			});
		});
	</script>
	<cfif NOT local.isBot and dataStruct.enableRealTimeRoster AND dataStruct.viewRealTimeRoster>
		<script type="text/javascript" src="/assets/common/javascript/polyfills/media-match/2.0.2/media.match.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/enquire.js/2.0.2/enquire.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/inview/jquery.inview.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/mcapps/listviewer/listviewer-common.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/mcapps/listviewer/responsive-listviewer.js"></script>
		<script language="javascript">
			var jsonURL = '#dataStruct.jsonURL#';
			var endOfListRegistrantslist=false;
			var currentlyLoadingRegistrants = false;
			var row = 0;
			var posStart = 0;
			
			$(function() {
				$('##loadingIndicator').show();
				$('##endOfRegistrantsList').bind('inview', function(event, isInView, visiblePartX, visiblePartY) {
					if (isInView) {
					  /*element is now visible in the viewport*/
					  loadMembers();
					} else {
					  /*element has gone out of viewport*/
					}
				});
			});
			
			function loadMembers() {
				if (!currentlyLoadingRegistrants && !endOfListRegistrantslist) {
					currentlyLoadingRegistrants = true;
					$.ajax({
						url: jsonURL,
						data: {eventID:#dataStruct.eventID#, posStart:posStart},
						dataType: 'json',
						success: function(response) {
							if(response.SUCCESS == true) {
								if(response.TOTALCOUNT == 0) {
									endOfListRegistrantslist = true;
									$('##loadingIndicator').hide();
									if($('##registrantListContainer tr').length == 0){
										$('##registrantListDiv table').removeClass('table').removeClass('table-hover').removeClass('table-striped');
										$('##registrantListContainer').append('<tr><td>Be the first to register for this event!</td></tr>');
									}
								} else {
									var msg = [];
									for(var i=0;i<response.DATA.length;i++) {
										msg.push('<tr><td>'+ response.DATA[i] + '</td></tr>');
									}
									$('##registrantListContainer').append(msg.join(''));
									posStart += response.DATA.length;
								}
								var isEndOfRegistrantListInViewport = mcIsElementInViewport($('##endOfRegistrantsList'));
								currentlyLoadingRegistrants = false;
								/* call load function again if end of registrant list is still visible on screen */
								if (isEndOfRegistrantListInViewport && !endOfListRegistrantslist) loadMembers();
							}
						},
						error: function(ErrorMsg) {
							$('##loadingIndicator').hide();
							alert(ErrorMsg);
						}
					});
					currentlyLoadingRegistrants = false;
				}
			}
		</script>
	</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.evhead#">

<cfsavecontent variable="registrationBox">
	<div class="sidebox">
		<div class="sideboxtitle" role="heading" aria-level="2">Registration</div>
		<div class="sideboxbody">
			<b>Event:</b><br/>
			<cfoutput>#dataStruct.eventtime#</cfoutput>
			
			<cfif dataStruct.qryRegistrant.recordCount gt 0>
				<cfoutput>
				<br/><br/>
				<div align="center"><b>#dataStruct.regAlready#</b></div>
				<br/>
				<div align="center">
					<cfif dataStruct.isOnlineMeeting>
						<cfif dataStruct.allowEnterOnlineMeeting>
							<button type="button" name="btnEnterProgram" class="btn btn-primary" onClick="self.location.href='#datastruct.onlineMeetingLink#';">Enter Program</button>
						<cfelse>
							<button type="button" name="btnEnterProgram" class="btn btn-primary" disabled>Enter Program</button>
							<div>#dataStruct.disallowEnterOnlineMeetingMessage#</div>
						</cfif>
						<br/><br/>
					</cfif>
					<a href="##" onClick="return viewRegister();"><b>View Your Registration</b></a>
					<cfif dataStruct.regEditAllowed is 1 and dataStruct.showEditRegLink is 1>
						<br/><a href="##" onclick="return editEVRegistration();"><b>Edit Your Registration</b></a>
					<cfelseif dataStruct.regEditAllowed is 1 and dataStruct.showEditRegLink is 0 and len(dataStruct.regEditDeadlineContent)>
						<div>#dataStruct.regEditDeadlineContent#</div>
					</cfif>
					<cfif len(dataStruct.evalURL)>
						<br/><a href="#datastruct.evalURL#"><b>Submit an Evaluation</b></a>
					</cfif>
				</div>
				</cfoutput>
			</cfif>
			
			<cfif dataStruct.allowRegister and (dataStruct.showRates or len(dataStruct.altRegistrationURL))>
				<br/><br/>
				<cfif NOT local.isBot>
					<cfoutput><div align="center"><button type="button" name="btnRegNow" class="btn btn-primary dd" onClick="registerEv();">#dataStruct.regBtn#</button></div></cfoutput>
				<cfelse>
					<cfoutput><div align="center"><button type="button" name="btnRegNow" class="btn btn-primary" disabled>#dataStruct.regBtn#</button></div></cfoutput>
				</cfif>
			<cfelseif len(dataStruct.regMessage)>
				<br/><br/>
				<cfoutput><div>#dataStruct.regMessage#</div></cfoutput>
				<cfif len(dataStruct.remarketingURL) AND NOT local.isBot>
					<br/><cfoutput><div>#dataStruct.remarketingText#</div></cfoutput>
					<cfoutput><div align="center" style="margin-top:8px;"><button type="button" name="btnRemarketingURL" class="btn btn-primary" onClick="self.location.href='#datastruct.remarketingURL#';">#dataStruct.remarketingBtnText#</button></div></cfoutput>
				</cfif>
			</cfif>
			<cfif dataStruct.showLoginOnlineMeeting>
				<br/><br/>
				<b>Already Registered?</b><br/>
				<div align="center"><button type="button" name="btnLogin" class="btn btn-primary" onClick="self.location.href='/?pg=login';">Login to Meeting</button></div>
			</cfif>
		</div>
	</div>
</cfsavecontent>

<!--- title --->
<div class="container-fluid">
	<cfoutput>
		<div class="row-fluid">
			<div id="page-header" class="span12">
				<h1><small>#encodeForHTML(dataStruct.eventContentTitle)#</small></h1>
				<cfif len(dataStruct.eventSubTitle)><h4>#encodeForHTML(dataStruct.eventSubTitle)#</h4></cfif>
				<cfif dataStruct.canDelEv AND dataStruct.enteredByMemberID eq dataStruct.orgMemberID>
					<div align="right" style="float:right; display: inline; margin-right: 2px;">
						<input type="button" name="btnDelEv" value="Delete" onClick="confirmDelete(#dataStruct.eventID#);">
					</div>
				</cfif>
				<cfif dataStruct.canEditEv AND dataStruct.enteredByMemberID eq dataStruct.orgMemberID>
					<div align="right" style="float:right; display: inline; margin-right: 5px;">
						<input type="button" name="btnEditEv" value="Edit" onClick="gotoEditDetail(#dataStruct.eventID#);">
					</div>
				</cfif>
				<div style="clear: both;"></div>
				<hr />
			</div>
			<br/>	
		</div>
	</cfoutput>
	<div class="row-fluid">
		<div class="span12">
			<div class="row-fluid event-layout-flex<cfif local.sidebarPosition eq 'left'> sidebar-left</cfif>">
				<div class="span9 event-main-content">
					<div class="visible-phone">
						<cfoutput>#registrationBox#</cfoutput>
					</div>
					<cfif len(dataStruct.eventContent)>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Details</div>
							<div class="sideboxbody">
								<br />
								<cfoutput>#dataStruct.eventContent#</cfoutput>
							</div>
						</div>
					</cfif>
			
					<cfif dataStruct.showCredit>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Credit</div>
							<div class="sideboxbody">
								<cfoutput>This program has been approved for credit in the following jurisdiction(s):<br/><br/></cfoutput>
								<cfoutput>#datastruct.strEventRegCredits.detail#</cfoutput>
							</div>
						</div>	
					</cfif>

					<cfif len(dataStruct.locationContentTitle) or len(dataStruct.locationContent)>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Location</div>
							<div class="sideboxbody">
								<cfif len(dataStruct.locationContentTitle)>
									<cfoutput><b>#dataStruct.locationContentTitle#</b><br/></cfoutput>
								</cfif>
								<cfif len(dataStruct.locationContent)>
									<cfoutput>#dataStruct.locationContent#<br/></cfoutput>
								</cfif>
							</div>
						</div>	
					</cfif>
					<cfif dataStruct.eventStruct.hasDoc EQ 1>
						<div class="sidebox" id="evMaterial">
							<div class="sideboxtitle" role="heading" aria-level="2">Materials</div>
							<div class="sideboxbody">
								Materials available with registration:<br/>
								<div style="margin-left:20px;">
								<cfif dataStruct.eventStruct.qryEventDocs.recordCount>
									<cfoutput query="dataStruct.eventStruct.qryEventDocs" group="eventDocumentGroupingOrder">
										<cfif val(dataStruct.eventStruct.qryEventDocs.eventDocumentGroupingID)>
											<div style="margin:4px 0;"><b>#dataStruct.eventStruct.qryEventDocs.eventDocumentGrouping#</b></div>
										</cfif>
										<div style="margin-top:4px;">
											<cfoutput group="eventDocumentID">
												<cfif dataStruct.eventStruct.qryEventDocs.isRegistrant IS 1>
													<a href="javascript:eventDocDownload(#dataStruct.eventStruct.qryEventDocs.eventID#, #dataStruct.eventStruct.qryEventDocs.documentID#, '#encodeForJavaScript(dataStruct.eventStruct.qryEventDocs.contenttitle)#', '#encodeForJavaScript(dataStruct.eventStruct.qryEventDocs.docTitle)#', '#encodeForJavaScript(dataStruct.eventStruct.qryEventDocs.fileExt)#');" class="btn btn-info" style="margin:0 5px 8px 0;">#EncodeForHTML(dataStruct.eventStruct.qryEventDocs.docTitle)##len(dataStruct.eventStruct.qryEventDocs.author) GT 0 ? " - #dataStruct.eventStruct.qryEventDocs.author#" : ""#</a>
												<cfelse>
													<div class="btn btn-info disabled" style="margin:0 5px 5px 0;">#EncodeForHTML(dataStruct.eventStruct.qryEventDocs.docTitle)##len(dataStruct.eventStruct.qryEventDocs.author) GT 0 ? " - #dataStruct.eventStruct.qryEventDocs.author#" : ""#</div>
												</cfif>
											</cfoutput>
										</div>
									</cfoutput>
								</cfif>
								<cfif dataStruct.eventStruct.qrySubEventDocs.recordCount>
									<cfoutput query="dataStruct.eventStruct.qrySubEventDocs" group="eventID">
										<br/>
										<div style="margin-bottom:4px;"><b>#dataStruct.eventStruct.qrySubEventDocs.contenttitle#:</b></div>
										<cfoutput group="eventDocumentGroupingOrder">
											<cfif val(dataStruct.eventStruct.qrySubEventDocs.eventDocumentGroupingID)>
												<div style="margin:4px 0;"><b>#dataStruct.eventStruct.qrySubEventDocs.eventDocumentGrouping#</b></div>
											</cfif>
											<cfoutput group="eventDocumentID">
												<cfif dataStruct.eventStruct.qrySubEventDocs.isRegistrant EQ 1>
													<a href="javascript:eventDocDownload(#dataStruct.eventStruct.qrySubEventDocs.eventID#, #dataStruct.eventStruct.qrySubEventDocs.documentID#, '#encodeForJavaScript(dataStruct.eventStruct.qrySubEventDocs.contenttitle)#', '#encodeForJavaScript(dataStruct.eventStruct.qrySubEventDocs.docTitle)#', '#encodeForJavaScript(dataStruct.eventStruct.qrySubEventDocs.fileExt)#');" class="btn btn-info" style="margin:0 5px 5px 0;">#EncodeForHTML(dataStruct.eventStruct.qrySubEventDocs.docTitle)##len(dataStruct.eventStruct.qrySubEventDocs.author) GT 0 ? " - #dataStruct.eventStruct.qrySubEventDocs.author#" : ""#</a>
												<cfelse>
													<div class="btn btn-info disabled" style="margin:0 5px 8px 0;">#EncodeForHTML(dataStruct.eventStruct.qrySubEventDocs.docTitle)##len(dataStruct.eventStruct.qrySubEventDocs.author) GT 0 ? " - #dataStruct.eventStruct.qrySubEventDocs.author#" : ""#</div>
												</cfif>
											</cfoutput>
											<br/>
										</cfoutput>
									</cfoutput>
								</cfif>
								</div>
								<br/>
							</div>
						</div>	
					</cfif>
					
					<cfif len(dataStruct.travelContentTitle) or len(dataStruct.travelContent)>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Travel/Accommodations</div>
							<div class="sideboxbody">
								<cfif len(dataStruct.travelContentTitle)>
									<cfoutput><b>#dataStruct.travelContentTitle#</b><br/></cfoutput>
								</cfif>
								<cfif len(dataStruct.travelContent)>
									<cfoutput>#dataStruct.travelContent#<br/></cfoutput>
								</cfif>
							</div>
						</div>	
					</cfif>
			
					<cfif dataStruct.qryEventSponsors.recordCount>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Sponsors</div>
							<div class="sideboxbody">
								<div style="margin:0 20px 6px 20px;">
									<cfoutput query="dataStruct.qryEventSponsors" group="sponsorGroupingID">
										<div<cfif dataStruct.qryEventSponsors.sponsorGroupingID gt 0> style="margin-bottom:15px;"</cfif>>
											<cfif dataStruct.qryEventSponsors.sponsorGroupingID gt 0>
												<div class="sponsor-group-header">
													<h3 class="sponsor-group-name">#dataStruct.qryEventSponsors.sponsorGrouping#</h3>
												</div>
											</cfif>
											<cfoutput>
												<div class="media"<cfif dataStruct.qryEventSponsors.sponsorGroupingID gt 0> style="margin-left:20px;"</cfif>>
													<cfif val(dataStruct.qryEventSponsors.featureImageID) gt 0>
														<a class="pull-left"<cfif len(dataStruct.qryEventSponsors.sponsorURL)> href="#dataStruct.qryEventSponsors.sponsorURL#" target="_blank"</cfif>>
															<img class="media-object" alt="Sponsor Image" src="#dataStruct.featuredThumbImageRootPath##dataStruct.qryEventSponsors.featureImageID#-#dataStruct.qryEventSponsors.featureImageSizeID#.#dataStruct.qryEventSponsors.fileExtension#?#dataStruct.imageUUID#" />
														</a>
													</cfif>
													<div class="media-body">
														<div class="media-heading"><b>#dataStruct.qryEventSponsors.sponsorName#</b></div>
														#dataStruct.qryEventSponsors.sponsorContent#
														<div class="clearfix"></div>
														<cfif len(dataStruct.qryEventSponsors.sponsorURL)>
															Website: <a href="#dataStruct.qryEventSponsors.sponsorURL#" target="_blank">#dataStruct.qryEventSponsors.sponsorURL#</a><br />
														</cfif>
													</div>
												</div>
											</cfoutput>
										</div>
									</cfoutput>
								</div>
							</div>
						</div>
					</cfif>
			
					<cfif len(dataStruct.cancelContentTitle) or len(dataStruct.cancelContent)>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Cancellation Policy</div>
							<div class="sideboxbody">
								<cfif len(dataStruct.cancelContentTitle)>
									<cfoutput><b>#dataStruct.cancelContentTitle#</b><br/></cfoutput>
								</cfif>
								<cfif len(dataStruct.cancelContent)>
									<cfoutput>#dataStruct.cancelContent#<br/></cfoutput>
								</cfif>
							</div>
						</div>	
					</cfif>
					<cfif NOT local.isBot and dataStruct.allowRegister and dataStruct.registrationType eq "RSVP">
						<a name="RSVP"></a>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">RSVP Today!</div>
							<div class="sideboxbody">
								<div id="rsvperr"></div>
								<div id="rsvpmsg">
								
									<cfif dataStruct.rsvpCountExists>
										<cfoutput>
											<div class="rsvp-alert-success" style="margin-top:10px;">
											You have successfully submitted #dataStruct.rsvpCount# RSVP request(s) for this event. You can submit additional RSVPs for this event below.
											</div>
										</cfoutput>
									<cfelse>
										<div>Complete the information below to RSVP for this event. You may RSVP for up to five (5) people at a time.</div>
									</cfif>
								</div>
								<br/>
								<cfoutput>
								<div>
									<form name="frmRSVP" method="post" action="#dataStruct.mainurl#&eid=#dataStruct.eid#" onsubmit="return checkRSVP();">
									<input type="hidden" name="evAction" value="saveRSVP">
									<table cellpadding="2" cellspacing="0" style="width:100%;">
									<tr>
										<td width="5">&nbsp;</td>
										<cfif attributes.data.orgSettings.hasPrefix>
											<td><b>Prefix</b></td>
										</cfif>
										<td><b>First Name</b></td>
										<td><b>Last Name</b></td>
										<td><b>Company Name</b></td>
										<td><b>E-mail</b></td>
										<td><b>Phone</b></td>
									</tr>
									<cfloop from="1" to="5" index="local.x">
										<tr>
											<td>#local.x#.</td>
											<cfif attributes.data.orgSettings.hasPrefix>
												<td>
													<cfif attributes.data.orgSettings.usePrefixList>
														<select name="salutation#local.x#" style="width:75px;">
															<option value=""></option>
															<cfloop query="attributes.data.orgPrefixTypes">
																<option value="#attributes.data.orgPrefixTypes.prefix#">#attributes.data.orgPrefixTypes.prefix#</option>
															</cfloop>
														</select>
													<cfelse>
														<input type="text" maxlength="10" name="salutation#local.x#"  value="" >
													</cfif>
												</td>
											<cfelse>
												<input type="hidden" name="salutation#local.x#" value="">
											</cfif>
											<td style="width:17%;"><input type="text" maxlength="70" id="firstname#local.x#" name="firstname#local.x#" value="" style="width:90%;"></td>
											<td style="width:17%;"><input type="text" maxlength="70" id="lastname#local.x#" name="lastname#local.x#" value="" style="width:90%;"></td>
											<td style="width:17%;"><input type="text" maxlength="200" id="company#local.x#" name="company#local.x#" value="" style="width:90%;"></td>
											<td style="width:17%;"><input type="text" maxlength="200" id="email#local.x#" name="email#local.x#" value="" style="width:90%;"></td>
											<td style="width:17%;"><input type="text" maxlength="30" id="phone#local.x#" name="phone#local.x#" value="" style="width:90%;"></td>
										</tr>
									</cfloop>
									</table>
									<br/>
									<button type="submit" name="btnRSVP">Send RSVP(s)</button>
									</form>
								</div>
								</cfoutput>
							</div>
						</div>	
						<script type="text/javascript">
							setListener("change",hideAlert);
						</script>
					</cfif>
					<cfif NOT local.isBot and dataStruct.enableRealTimeRoster AND dataStruct.viewRealTimeRoster>
						<div class="sidebox">
							<div class="sideboxtitle" role="heading" aria-level="2">Who's Attending?</div>
							<div class="sideboxbody">
								<div id="registrantListDiv">
									<table class="table table-condensed table-hover table-striped">
										<tbody id="registrantListContainer"></tbody>
									</table>
									<div id="endOfRegistrantsList" class="">
										<div id="loadingIndicator" class="progress progress-striped active">
										  <div class="bar" style="width: 100%;"><strong>Loading Registrants</strong></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>
				</div>
				<div class="span3 event-sidebar">
					<div>
						<cfif dataStruct.hasPendingRegistrations>
							<div style="padding:3px;border:1px solid ##f00;">
								<div>Reminder:</div>
								<cfoutput>You have <a href="#dataStruct.mainurl#&regcartv2">pending registrations</a>.</cfoutput>
							</div>
						</cfif>
						<div class="hidden-phone">
							<cfoutput>#registrationBox#</cfoutput>
						</div>
						<cfif dataStruct.showRates>
							<div class="sidebox">
								<div class="sideboxtitle" role="heading" aria-level="2">Event Rates</div>
								<div class="sideboxbody eventRateSideBox">
									<table>
									<cfoutput query="dataStruct.qryEventRegRates" group="rateGroupingOrder">
										<tr><td colspan="3"><b>#dataStruct.qryEventRegRates.rateGrouping#</b></td></tr>
										<cfoutput group="rateID">
											<tr valign="top">
											<td nowrap>
												<cfif dataStruct.qryEventRegRates.rate is 0>#dataStruct.qryEventRegRates.freeRateDisplay#<cfelse>#dollarformat(dataStruct.qryEventRegRates.rate)#<cfif len(dataStruct.displayedCurrencyType)> #dataStruct.displayedCurrencyType#</cfif></cfif>
											</td>
											<td width="5"></td>
											<td>#htmleditformat(dataStruct.qryEventRegRates.rateName)#</td>
											</tr>
											<cfoutput>
												<cfif val(dataStruct.qryEventRegRates.bulkRateID) gt 0>
													<tr>
													<td colspan="3">
														<div style="padding-left:25px;">
														<i><cfif dataStruct.qryEventRegRates.rate is 0>#dataStruct.qryEventRegRates.freeRateDisplay#<cfelse>#dollarformat(dataStruct.qryEventRegRates.bulkRate)#<cfif len(dataStruct.displayedCurrencyType)> #dataStruct.displayedCurrencyType#</cfif></cfif>
														if registering #dataStruct.qryEventRegRates.bulkQty# or more</i>
														</div>
													</td>
													</tr>
												</cfif>
											</cfoutput>
											<tr><td colspan="3" style="line-height:2px;">&nbsp;</td></tr>
										</cfoutput>
									</cfoutput>
									</table>
								</div>
							</div>
						</cfif>
						<cfif NOT local.isBot>
							<div class="sidebox">
								<div class="sideboxtitle" role="heading" aria-level="2">Tell a Colleague!</div>
								<div class="sideboxbody">
									<cfoutput>
									<span class='st_facebook_large' st_title='#encodeForURL(dataStruct.eventContentTitle)#'></span>
									<span class='st_twitter_large' st_title='#encodeForURL(dataStruct.eventContentTitle)#'></span>
									<span class='st_linkedin_large' st_title='#encodeForURL(dataStruct.eventContentTitle)#'></span>
									<span class='st_email_large' st_title='#encodeForURL(dataStruct.eventContentTitle)#'></span>
									</cfoutput>
								</div>
							</div>
						</cfif>
						<cfif NOT local.isBot and dataStruct.showAddToCalendar>
							<div class="sidebox">
								<div class="sideboxtitle" role="heading" aria-level="2">Add to My Calendar</div>
								<div class="sideboxbody">
								 	<cfoutput>
										<div>
											<a title="Download event to your calendar" 
												style="text-decoration:none;" 
												href="/?event=cms.ShowResource&resID=#dataStruct.siteResourceID#&evAction=downloadICal&eid=#dataStruct.eid#&mode=stream"
												onclick="return handleGtagCalendarAction('Outlook, Yahoo, iCAL, or Other Calendar');">
												<i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp; Outlook Calendar</a>
										</div>
										<div>
											<a title="Add event to your Google calendar" 
												target="_blank" 
												style="text-decoration:none;" 
												href="/?event=cms.ShowResource&resID=#dataStruct.siteResourceID#&evAction=downloadGCal&eid=#dataStruct.eid#&mode=stream"
												onclick="return handleGtagCalendarAction('Google Calendar');">
													<img style="vertical-align: middle;" src="/assets/common/images/icon_google_calendar.gif" border="0">&nbsp; Google Calendar</a>
										</div>
										<div>
											<a title="Download event to your calendar" 
												style="text-decoration:none;" 
												href="/?event=cms.ShowResource&resID=#dataStruct.siteResourceID#&evAction=downloadICal&eid=#dataStruct.eid#&mode=stream"
												onclick="return handleGtagCalendarAction('Apple Calendar');">
													<i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp; iCal Calendar</a>
										</div>
									</cfoutput>
									<br/>
									<div><em>Reminder: Adding to your calendar does not register you for the event.</em></div>
								</div>
							</div>
						</cfif>

						<cfif len(dataStruct.contactContentTitle) or len(dataStruct.contactContent)>
							<div class="sidebox">
								<div class="sideboxtitle" role="heading" aria-level="2">Questions?</div>
								<div class="sideboxbody">
									<cfif len(dataStruct.contactContentTitle)>
										<cfoutput><b>#dataStruct.contactContentTitle#</b><br/></cfoutput>
									</cfif>
									<cfif len(dataStruct.contactContent)>
										<cfoutput>#dataStruct.contactContent#<br/></cfoutput>			
									</cfif>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</div>
	</div>
</div> <!-- End container -->