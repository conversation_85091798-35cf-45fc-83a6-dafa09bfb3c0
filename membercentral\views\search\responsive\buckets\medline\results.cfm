<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	<cfif val(local.strCount.itemcount) EQ 0>
		#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#
	<cfelse>
		#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.itemCount, itemWord='article', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
		<div style="clear:both;"></div>
		<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>

		<cfset local.itemCount = 0>
		<cfloop list="#local.itemidlist#" index="local.itemID">
			<cfif structKeyExists(local.resultStr,local.itemID)>
				<cfset local.itemCount = local.itemCount + 1>

				<div class="medlineArticles <cfif arrayLen(local.resultStr[local.itemID]['attributes']) gt 0>tsDocViewerDocument</cfif> s_row <cfif local.itemCount mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.itemID#" data-sn="#getStandardNumber(local.resultStr[local.itemID])#" data-year="#left(local.resultStr[local.itemID]['sortpubdate'],4)#">
					<div class="pull-right">
						<div class="p-3 <cfif NOT local.resultStr[local.itemID]['inCart']>hide</cfif>" id="s_incart#local.itemID#">
							<a href="javascript:viewCart();" title="View your cart/checkout"><i class="icon-shopping-cart icon-large"></i> In Cart</a>
						</div>										
						<cfif not local.resultStr[local.itemID]['owned']>
							<div class="p-3 <cfif local.resultStr[local.itemID]['inCart']>hide</cfif>" id="s_addtocart#local.itemID#">
								<a href="javascript:addToCart(#local.itemID#,#arguments.searchid#);" title="Buy Article"><i class="icon-shopping-cart icon-large"></i> Buy</a> <span id="price#local.itemID#" style="display: none;"></span>
							</div>
						<cfelse>
							<div class="p-3"><a href="/?pg=myDocuments&tab=PMD" title="Download Document"><i class="icon-download icon-large"></i> Purchased</a></div>
						</cfif>
					</div>
					<div>
						<cfif arrayLen(local.resultStr[local.itemID]['attributes']) gt 0>
							<a href="javascript:loadMedlineAbstractText(#local.itemID#,#arguments.searchid#);" title="Click to view abstract"><i class="icon-file icon-large hidden-phone"></i> <b>#local.resultStr[local.itemID]['title']#</b></a>
							<a href="javascript:loadMedlineAbstractText(#local.itemID#,#arguments.searchid#);" title="Click to view abstract" class="hidden-phone" style="white-space:nowrap;">(click to view)</a><br/>
						<cfelse>
							<i class="icon-file icon-large"></i> <b>#local.resultStr[local.itemID]['title']#</b>
						</cfif>
					</div>
					<div class="bk_subContent">
						<cfloop from="1" to="#arrayLen(local.resultStr[local.itemID]['authors'])#" index="local.index">
							#local.resultStr[local.itemID]['authors'][local.index]['name']#<cfif not listFind("1,#arrayLen(local.resultStr[local.itemID]['authors'])#",local.index)>, </cfif>
						</cfloop>
						<br/>
						#local.resultStr[local.itemID]['source']#.#local.resultStr[local.itemID]['pubdate']#;#local.resultStr[local.itemID]['volume']#(#local.resultStr[local.itemID]['issue']#):#local.resultStr[local.itemID]['pages']#. 
						<cfif len(local.resultStr[local.itemID]['elocationid'])>#local.resultStr[local.itemID]['elocationid']#.</cfif> 
						#replace(arrayToList(local.resultStr[local.itemID]['pubtype']),",",", ","all")#. 
						<cfif arrayLen(local.resultStr[local.itemID]['attributes']) is 0>No abstract available.</cfif><br/>
						PMID: #local.itemID#
					</div>
				</div>
			</cfif>
		</cfloop>
		
		<br clear="all"/>
		#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.itemCount, itemWord='article', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
	</cfif>
</cfoutput>