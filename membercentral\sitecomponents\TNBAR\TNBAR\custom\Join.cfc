<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();		
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		local.crlf = chr(13) & chr(10);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];

		local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="5E124168-9AA2-46F3-B7F2-81CA9C54FA50" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a TBA Member. If you have questions about your membership, please call (615) 383-7421." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledRedirectMessage",type="CONTENTOBJ",desc="The message that displays to members who have a billed membership before the system redirects them.",value="Our records indicate your membership is available for renewal. Please wait while we redirect you to the renewal process." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (615) 383-7421." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="ACA9CC54-EDA8-4AD4-9A0B-10D6B36A10CA" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PreCheckedAddOns",type="STRING",desc="UIDs for Add-Ons that qualified users will have to opt out of",value="13068AF4-5B75-448F-A575-52EFAD854FC5" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfessionalLicContent",type="STRING",desc="Content at top of Professional Licenses",value="Please list all State Licensures. For out of state licenses, please append the state abbreviation to the license number (ex. AL-12345). For each license type added, please provide your admittance date (even if it is an estimate) and current status." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for CC",value="TBA Credit Cards" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="TBA Pay by Check" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for check",value="TBA Bank Draft" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="TBA Membership Application Receipt" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Join TBA',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));	
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
		variables.historyStartedText = "Member started join form.";
		variables.historyCompletedText = "Member completed join form.";
		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="frmJoin");

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
								
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					case "spam":
						local.returnHTML = showError(errorCode='spam');
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
		
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>	
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>	
		
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>

		<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
		<cfif structKeyExists(session, "captchaEntered")>
			<cfset structDelete(session, "captchaEntered")>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				
				<style type="text/css">
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					.subRateLabel {font-weight:normal;}
					.subRatesDisabled {
						opacity: 0.6; /* Real browsers */
						filter: alpha(opacity = 60); /* MSIE */
					}
					.inner-content{    margin-bottom: 20px;}
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;
					function afterFormLoadStep1(obj){
						$('html, body').animate({ scrollTop: 0 }, 500);
                        var thisForm = document.forms["#variables.formName#"];
                        var er_change = function(r) {
                            var results = r;
                            if( results.success ){
                                if (results.isnewmember && typeof results.licensenumber != "undefined" && results.licensenumber != '') {
                                    if($('.mpl_pltypeid').length > 0){
										$('.mpl_pltypeid').multiselect("widget").find(":input[value=" + results.licenses[0].pltypeid +"]").each(function() {
											this.click();
										});
										$('##mpl_'+ results.licenses[0].pltypeid +'_licenseNumber').val(results.licenses[0].licensenumber);
										$('##mpl_'+ results.licenses[0].pltypeid +'_activeDate').val(results.licenses[0].activedate);
										$('##mpl_'+ results.licenses[0].pltypeid +'_status option[value="active"]').attr('selected','selected');										
                                    }
                                }
                            }
                            else{ /*alert('not success');*/ }
                        };
                        
                        var objParams = { memberNumber:obj.memberNumber };
                        TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);    
					}
                    
                    function assignMemberData(memObj) {
                        $('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
                        mc_continueForm($('###variables.formName#'),function(){afterFormLoadStep1(memObj);});
                    }
	
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}

					function validatePaymentForm(ispaymentrequired) {
						if(ispaymentrequired){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);     
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous');			        	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}				
					function resizeBox(newW,newH) { 
						var windowWidth = $(window).width();
						var _popupWidth = newW;
						if(windowWidth < 585) {
							_popupWidth = windowWidth - 30;
						}
						$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
					}
					$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>

						$(window).on("resize load", function() {
							var windowWidth = $(window).width();
							if(windowWidth < 585) {
								$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
							} else{
								$.colorbox.resize({innerWidth:550, innerHeight:330});		
							}				
						});
					});
				</script>
	
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				
				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>
	
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = variables.strPageFields.SubTypeTest>
			<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
			<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfif local.qryBilledSubs.isRenewalRate>
					<cfset local.stReturn = "billedfound">
				<cfelse>
					<cfset local.stReturn = "billedjoinfound">
				</cfif>
			<cfelse>
				<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "activefound">
				<cfelse>
					<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
					<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfset local.stReturn = "acceptedfound">
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>

		<cfset local.contactTypeFieldSetUID = '868BBE8A-C164-4482-A831-4E9BF703A6C0'>
		<cfset local.personalInfoFieldSetUID = 'EEAF6A6F-E057-4B2F-BF9F-97B8BA7FD19A'>
		<cfset local.officeAddressFieldSetUID = '9B5A06F8-92B8-42F8-AC49-CA9C32E1575D'>
		<cfset local.studentInfoFieldSetUID = '5443E7BD-9131-41C5-84B7-9FAE2A1D00A0'>
		<cfset local.professionalInfoFieldSetUID = '3D8B5953-5FF1-426E-A97F-8A02C4B7DFFE'>
		<cfset local.homeAddressFieldSetUID = 'BC33D417-3E18-4925-8240-D66A820EC0FF'>
		<cfset local.addressPrefFieldSetUID = '0B847969-8E00-441B-9CA4-E2395D44AD22'>
		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
		<cfquery name="local.qryGetHomeAddress" dbtype="query">
			SELECT addressTypeId FROM local.qryOrgAddressTypes WHERE addressType = 'Home Address'
		</cfquery>
		<cfquery name="local.qryOtherAddress" dbtype="query">
			SELECT addressTypeId FROM local.qryOrgAddressTypes WHERE addressType <> 'Home Address'
		</cfquery>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
		<cfif variables.isLoggedIn OR (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),variables.useMID))>
			<cfset local.fieldSetUIDlist = '#local.contactTypeFieldSetUID#,#local.personalInfoFieldSetUID#,#local.officeAddressFieldSetUID#,#local.studentInfoFieldSetUID#,#local.professionalInfoFieldSetUID#,#local.homeAddressFieldSetUID#,#local.addressPrefFieldSetUID#'>
			<cfset local.memberFieldDetails = structNew()>
			<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
				<cfset local.NewAcctFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
				<cfset StructAppend(local.memberFieldDetails,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.NewAcctFormXMLFields))>
			</cfloop>
			<cfset local.strPrefillMemberData = local.memberFieldDetails>
		<cfelse>
			<cfset local.memberFieldData = structNew()>
			<cfset local.fieldSetUIDlist = '#local.contactTypeFieldSetUID#,#local.personalInfoFieldSetUID#,#local.officeAddressFieldSetUID#,#local.studentInfoFieldSetUID#,#local.professionalInfoFieldSetUID#,#local.homeAddressFieldSetUID#,#local.addressPrefFieldSetUID#'>
			<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            	<cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
			</cfloop>
			<cfset local.strPrefillMemberData = local.memberFieldData>
		</cfif>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structkeyexists(session,"useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>			
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif variables.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>	

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfset local.contactTypeFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='868BBE8A-C164-4482-A831-4E9BF703A6C0', mode="collection", strData=local.strData)>
		<cfset local.personalInfoFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='EEAF6A6F-E057-4B2F-BF9F-97B8BA7FD19A', mode="collection", strData=local.strData)>
		<cfset local.officeAddressFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='9B5A06F8-92B8-42F8-AC49-CA9C32E1575D', mode="collection", strData=local.strData)>
		<cfset local.studentInfoFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='5443E7BD-9131-41C5-84B7-9FAE2A1D00A0', mode="collection", strData=local.strData)>
		<cfset local.professionalInfoFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='3D8B5953-5FF1-426E-A97F-8A02C4B7DFFE', mode="collection", strData=local.strData)>
		<cfset local.homeAddressFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='BC33D417-3E18-4925-8240-D66A820EC0FF', mode="collection", strData=local.strData)>
		<cfset local.addressPrefFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='0B847969-8E00-441B-9CA4-E2395D44AD22', mode="collection", strData=local.strData)>


		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>

		<cfset local.tennesseePLType = 0/>
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>	
		<cfquery name="local.qryTennesseePlTypes" dbtype="query">
			SELECT PLTypeID FROM [local.qryOrgPlTypes] WHERE PLName = 'Tennessee';
		</cfquery>
		<cfif local.qryTennesseePlTypes.recordCount GT 0>
			<cfset local.tennesseePLType = local.qryTennesseePlTypes.PLTypeID/>
		</cfif>
		
		<cfif variables.isLoggedIn>			
			<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>
		<cfelseif session.cfcuser.memberdata.identifiedAsMemberID>			
			<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=session.cfcuser.memberdata.identifiedAsMemberID, orgID=variables.orgID)/>
		<cfelse>
			<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=0, orgID=variables.orgID)/>
		</cfif>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		<cfelseif local.qryProLicenses.recordCount GT 0>
			<cfset local.profLicenseIDList = valueList(local.qryProLicenses.PLTypeID)>
		</cfif>
		

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				var memberTypeField;
				
				function toggleFTM() {
				}
				
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);
				}

				function adjustFieldsetDisplay(memberTypeField) {
					var memType = $("option:selected",memberTypeField).text();

					$('select[name*="addresstype"] option').removeAttr('selected')

					if(memType.indexOf('Law Student') != -1 || memType == ""){
						$(".officeAddressFieldSetInfo-wrapper").hide();
						$(".professionalInfoFieldSetInfo-wrapper").hide();
						$('.professionalLicWrapper').hide();
						$('.homeAddressFieldSetInfo-wrapper > div > table > tbody > tr >td:first-child').html('*&nbsp;');
						$('select[name*="addresstype"] option[value=#local.qryGetHomeAddress.addressTypeId#]').attr('selected',true);
						if(memType =='Law Student') {
						<cfloop query="local.qryOtherAddress">
							$('input[id^=ma_#local.qryOtherAddress.addressTypeId#_]').val('');
							$('select[id^=ma_#local.qryOtherAddress.addressTypeId#_] option').removeAttr('selected');
						</cfloop>
						}
						$('.addressPrefFieldSetInfo-wrapper').hide();
					} else {
						$('.professionalLicWrapper').show();
						<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
						<cfif NOT( variables.isLoggedIn OR (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),variables.useMID)))>
							resetProfessionalLicenses();
						</cfif>
						$(".professionalInfoFieldSetInfo-wrapper").show();
						$(".officeAddressFieldSetInfo-wrapper").show();
						showFieldsContactType('officeAddressFieldSetInfo-wrapper');
						$('.addressPrefFieldSetInfo-wrapper').show();
						$('.homeAddressFieldSetInfo-wrapper > div > table > tbody > tr >td:first-child').html('&nbsp;');
					}
				}		

				function resetProfessionalLicenses(){
					
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}

				}

				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					var memType = $('###variables.formName# ##md_#local.memberTypeFieldInfo.columnID# option:selected').text();
					
					if ($(".contactTypeFieldSetInfo-wrapper").is(':visible')) {
						#local.contactTypeFieldSetInfo.jsValidation#
					}
					if ($(".personalInfoFieldSetInfo-wrapper").is(':visible')) {
						#local.personalInfoFieldSetInfo.jsValidation#
					}
					if ($(".officeAddressFieldSetInfo-wrapper").is(':visible')) {
						#local.officeAddressFieldSetInfo.jsValidation#
					}
					if ($(".studentInfoFieldSetInfo-wrapper").is(':visible')) {
						#local.studentInfoFieldSetInfo.jsValidation#
					}
					if ($(".professionalInfoFieldSetInfo-wrapper").is(':visible')) {
						#local.professionalInfoFieldSetInfo.jsValidation#
					}
					if ($(".homeAddressFieldSetInfo-wrapper").is(':visible')) {
						if(memType != 'Law Student') {
							#local.homeAddressFieldSetInfo.jsValidation#
						} else {
							<cfloop collection="#local.homeAddressFieldSetInfo.strFields#" item="local.thisField">
								if($('###local.thisField#').val() == ''){
									arrReq[arrReq.length] = '#local.homeAddressFieldSetInfo.strFields['#local.thisField#']# is required'
								}
							</cfloop>
						}
					}
					if ($(".addressPrefFieldSetInfo-wrapper").is(':visible')) {
						#local.addressPrefFieldSetInfo.jsValidation#
					}
					
					if (memType != 'Law Student'){

						var isProfLicenseRequired = true;
						
						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseName").val();
									if($("##mpl_"+val+"_licenseNumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; } else if((isNaN($("##mpl_"+val+"_licenseNumber").val()) || $("##mpl_"+val+"_licenseNumber").val().length != 6) && val == '#local.tennesseePLType#'){ arrReq[arrReq.length] = 'Enter valid 6 digit '+text +' License  Number.'; }
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}	
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}
					}	

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
				}
				function showFieldsContactType(classList) {
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]).show();							
						}			
					});							
				}
				function moveContentToPlace(tempLocation,actualLocation){
					var _html = $('##'+tempLocation).html();
					$('.'+actualLocation+' .fieldSetContainer').html(_html);
				}
				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
				<cfset local.licenseStatus = {}>
				<cfset local.index = 1>
				<cfloop query="local.qryOrgProLicenseStatuses">
					<cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
					<cfset local.index = local.index + 1>
				</cfloop> 
				<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
				$(document).ready(function() {
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>						
					prefillData();
					memberTypeField = $('##md_#local.memberTypeFieldInfo.columnID#');
				
					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
					   	},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
					
					$(memberTypeField).change(function(){adjustFieldsetDisplay($(this))});
					adjustFieldsetDisplay(memberTypeField);
					
					<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');
						</cfloop>
					<cfelseif local.qryProLicenses.recordCount GT 0>
						<cfloop query="local.qryProLicenses">
							licenseChange(true,'#local.qryProLicenses.PLTypeID#','#local.qryProLicenses.PLName#','#local.qryProLicenses.LicenseNumber#','#local.qryProLicenses.ActiveDate#','#local.qryProLicenses.StatusName#');	
						</cfloop>
					</cfif>
					
				});
					
				function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status)
				{
					$("##state_table").show();
					if(status == ''){
						status = 'Active';
					}
					strOption = '';
					<cfloop collection="#local.licenseStatus#" item="local.i" >
						strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
					</cfloop>
					if(isChecked){
						$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
								'<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
								'<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
								'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
								'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
								'<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
								'</div>');
						$('##mpl_'+val+'_status').val(status);
						mca_setupDatePickerField('mpl_'+val+'_activeDate');
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
					}									
					else{
						$("##tr_state_"+val).remove();								
					}
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}	
				}	
			</script>
			
			<style type="text/css">
				.inner-content{    margin-bottom: 20px;}
				.content input[type="text"] {width:206px!important;}
				.content select{width:220px!important;}
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {white-space: initial!important;}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {display: block;margin-bottom:0px;}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{padding: 10px !important;}
					
			</style>
			
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.Step1TopContent)>
				<div id="Step1TopContent">#variables.strPageFields.Step1TopContent#</div>
			</cfif>

			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			<div id="content-wrapper" >
				<div  class="contactTypeFieldSetInfo-wrapper fieldsetFormWrapper">
					<div class="tsAppSectionHeading">#local.contactTypeFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer">
						#local.contactTypeFieldSetInfo.fieldSetContent#
					</div>
				</div>
				<div  class="personalInfoFieldSetInfo-wrapper fieldsetFormWrapper">
					<div class="tsAppSectionHeading">#local.personalInfoFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer">
						#local.personalInfoFieldSetInfo.fieldSetContent#
					</div>
				</div>	
				<div  class="officeAddressFieldSetInfo-wrapper fieldsetFormWrapper hide">
					<div class="tsAppSectionHeading">#local.officeAddressFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer fieldSetContainer">
						#local.officeAddressFieldSetInfo.fieldSetContent#	
					</div>
				</div>
				<div  class="studentInfoFieldSetInfo-wrapper fieldsetFormWrapper">
					<div class="tsAppSectionHeading">#local.studentInfoFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer fieldSetContainer">
						#local.studentInfoFieldSetInfo.fieldSetContent#
					</div>
				</div>
				<div  class="professionalInfoFieldSetInfo-wrapper fieldsetFormWrapper">
					<div class="tsAppSectionHeading">#local.professionalInfoFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer fieldSetContainer">
						#local.professionalInfoFieldSetInfo.fieldSetContent#
					</div>
				</div>

				<div class="row-fluid professionalLicWrapper" id="professionalLicWrapper">
					<div class="tsAppSectionHeading">Professional License Information</div>
					<div class="tsAppSectionContentContainer fieldSetContainer">
						<cfif len(variables.strPageFields.ProfessionalLicContent)>
							<div id="ProfessionalLicense">#variables.strPageFields.ProfessionalLicContent#</div><br/>
						</cfif>
						
						<table cellpadding="3" border="0" cellspacing="0" >									
							<tr align="top">
								<td class="tsAppBodyText" width="10">*&nbsp;</td>
								<td class="tsAppBodyText" nowrap>Professional License</td>
								<td class="tsAppBodyText">
									<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
										<cfloop query="local.qryOrgPlTypes">	
											<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
										</cfloop>
									</select>
								</td>
							</tr>
							<tr class="top">
								<td class="tsAppBodyText" width="10"></td>
								<td class="tsAppBodyText"></td>
								<td class="tsAppBodyText"></td>
							</tr>
						</table>
						<br/>
						<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
							<tr>
								<td>
									<div class="row-fluid hide" id="state_table">
										<div class="span3 proLicenseLabel">
											<b>State Name</b>
										</div>
										<div class="span3 proLicenseLabel">
											<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
										</div>
										<div class="span3 proLicenseLabel">
											<b>#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</b>
										</div>
										<div class="span3 proLicenseLabel">
											<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
										</div>
									</div>
									<span id="selectedLicense">
									</span>
								</td>
							</tr>					
						</table>
					</div>
				</div>

				<div  class="homeAddressFieldSetInfo-wrapper fieldsetFormWrapper">
					<div class="tsAppSectionHeading">#local.homeAddressFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer">
						#local.homeAddressFieldSetInfo.fieldSetContent#
					</div>
				</div>
				<div  class="addressPrefFieldSetInfo-wrapper fieldsetFormWrapper">
					<div class="tsAppSectionHeading">#local.addressPrefFieldSetInfo.fieldSetTitle#</div>
					<div class="tsAppSectionContentContainer">
						#local.addressPrefFieldSetInfo.fieldSetContent#
					</div>
				</div>

				<div class="row-fluid">
					<div class="span12">							
						#variables.captchaDetails.htmlContent#
					</div>
				</div>

				<button name="btnContinue" type="submit" class="tsAppBodyButton btn" onClick="hideAlert();">Continue</button>
			</div>
				#application.objWebEditor.showEditorHeadScripts()#
				<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = arguments.rc.mc_siteinfo.orgid, includeTags=0)>
				<script language="javascript">
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
							
							function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
								if(_this.length > 0){
									var _address = _this.val();
									
									if(_address.length >0 ){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
									}


								} else {
									$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
								}

								var memType = $("option:selected",memberTypeField).text();

								$('select[name*="addresstype"] option').removeAttr('selected');

								if(memType.indexOf('Law Student') != -1){
									$('select[name*="addresstype"] option[value=#local.qryOrgAddressTypes.addressTypeId#]').attr('selected',true);
								}

							}
							
							$(document).on('change','###variables.formName# ##ma_#local.qryOrgAddressTypes.addresstypeid#_address1',function(){
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
							});
						</cfloop>
					});

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>

			</form>
			

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	
		
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<cfif variables.isLoggedIn>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
		<cfelseif session.cfcuser.memberdata.identifiedAsMemberID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=session.cfcuser.memberdata.identifiedAsMemberID)>
		<cfelse>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		</cfif>
					
		<cfif local.strResult.success>			
			<!--- Setting Captcha submitted flag --->
			<cfset session.captchaEntered = 1>
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>			
		<cfscript>
			local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				);
		</cfscript>

		<cfsavecontent variable="local.headtext">
			<cfoutput>
				<style>
					.inner-content{    margin-bottom: 20px;}
				</style>

				<script type="text/javascript">
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					#local.result.jsAddonValidation#
					function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						<cfif val(local.subscriptionID)>
							if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
								arrReq[arrReq.length] = " Select Membership.";
							}	
						</cfif>	

						//make sure selected subscriptions all have selected rates.
						$('input.subCheckbox:checkbox:checked').each(function(x,item){
							var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
							var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
							if ( numRates > 0 && numRatesChecked==0) {
								arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
							}
						});
	
						//make sure any chosen editable rates have amounts greater than zero.
	
						$('input.subRateCheckbox:radio:checked').each(function(index,item){
							var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
							if ($(rateOverrideField).length) {
								var overridePrice = parseFloat($(rateOverrideField)[0].value);
								if (!overridePrice) {
									var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
									subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
									arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
								}
							}
						});
	
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
	
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					function subscriptionRateOverrideBoxHandler(event) {
	
						var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());
	
						//check subscription if not already checked
						if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
							$(subRateRadioButton)[0].click();
							$(this).focus();
						} else if (subRateRadioButton) {
							$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
						}
					}
	
					function subscriptionCheckboxHandler() {
						if ($(this)[0].checked) {
							$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
							$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
						} else {
							$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
							$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
						}
					}
	
					function subscriptionRateRadioButtonHandler() {
	
						if ($(this)[0].checked) {
							var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
							var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
							var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();
	
							if (rateOverrideBox.length) {
								//rateoverride box is present
								rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
							}
	
							//put label of selected rate radio button next to subscription
							rateDescription = ' - ' + rateDescription;
							$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);
	
							//check subscription if not already checked
							if (!$(subCheckbox)[0].checked)
								$(subCheckbox)[0].click();
						}
					}
					
					function initializeAddons() {
						$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
					}
	
					function selectAllSubscriptionsIfRequired() {
						var addonData = $(this).data();
						// select all addons if minimum required by set is gte available count
						// hide checkboxes so they can not be unselected
						if (addonData.minallowed >= $('.subCheckbox',this).length) {
							$('.subCheckbox:not(:checked)',this).click().hide();
							$('.subCheckbox',this).hide();
						}
					}
					$(function() {

						
						$('input.subCheckbox:checkbox').on('change',subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').on('change',subscriptionRateRadioButtonHandler);
						$('input.subRateOverrideBox').on('change',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('focus',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('blur',subscriptionRateOverrideBoxHandler);

						initializeAddons();

						$('input.subCheckbox:checkbox').each(subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').each(subscriptionRateRadioButtonHandler);

						$("input.subRateCheckbox[name!='sub#local.subscriptionID#_rate']:radio").change(function(){
							if($(this).prop("checked") && $("##"+$(this).attr('name').split('_')[0]).prop("checked") == false){
								$("##"+$(this).attr('name').split('_')[0]).trigger("click");
							}						
						});

					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headtext#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">

			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<cfoutput>#local.result.formcontent#</cfoutput>
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton btn" onClick="hideAlert();">Continue</button>
			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfscript>
			local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);
		</cfscript>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfscript>
			local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc);
		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>

			<cfsavecontent variable="local.headcode">
				<cfoutput>
					#local.strReturn.headcode#
					<style>
						.inner-content{    margin-bottom: 20px;}
					</style>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			
			<cfif len(variables.strPageFields.Step3TopContent)>
				<div id="Step3TopContent">#variables.strPageFields.Step3TopContent#</div><br/>
			</cfif>
			
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub"
					>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
			</div>
			<br/>

			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton btn" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>

			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			//create subscriptions
			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice">
			set nocount on;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

			select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, dueNow= case when nv.dateDue < getdate() then 1 else 0 end
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs
				on subs.subscriptionID = s.subscriptionID
				and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
			inner join dbo.sub_types t
				on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
				and ta.applicationTypeID = 17
				and ta.itemType = 'Dues'
			inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
			inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
			group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
			order by nv.dateDue
		</cfquery>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#,#variables.strPageFields.ProfileCodeACH#",arguments.rc.mccf_payMeth)>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		<cfelse>

		</cfif>
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>

		<cfset local.contactTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=arguments.rc['md_#local.memberTypeFieldInfo.columnID#'])>
		
		<cfset local.contactTypeFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='868BBE8A-C164-4482-A831-4E9BF703A6C0', mode="confirmation", strData=arguments.rc)>
		<cfset local.personalInfoFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='EEAF6A6F-E057-4B2F-BF9F-97B8BA7FD19A', mode="confirmation", strData=arguments.rc)>
		<cfset local.officeAddressFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='9B5A06F8-92B8-42F8-AC49-CA9C32E1575D', mode="confirmation", strData=arguments.rc)>
		<cfset local.studentInfoFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='5443E7BD-9131-41C5-84B7-9FAE2A1D00A0', mode="confirmation", strData=arguments.rc)>
		<cfset local.professionalInfoFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='3D8B5953-5FF1-426E-A97F-8A02C4B7DFFE', mode="confirmation", strData=arguments.rc)>
		<cfset local.homeAddressFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='BC33D417-3E18-4925-8240-D66A820EC0FF', mode="confirmation", strData=arguments.rc)>
		<cfset local.addressPrefFieldSetInfo = application.objCustomPageUtils.renderFieldSet(uid='0B847969-8E00-441B-9CA4-E2395D44AD22', mode="confirmation", strData=arguments.rc)>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfscript>
			local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc);

		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<p>Here are the details of your application:</p>

			#local.contactTypeFieldSetInfo.fieldSetContent#
			#local.personalInfoFieldSetInfo.fieldSetContent#
			<cfif local.contactTypeSelected NEQ "Law Student">
			#local.officeAddressFieldSetInfo.fieldSetContent#
			</cfif>
			#local.studentInfoFieldSetInfo.fieldSetContent#
			<cfif local.contactTypeSelected NEQ "Law Student">
			#local.professionalInfoFieldSetInfo.fieldSetContent#
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licenseName']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licenseNumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br>
			</cfif>
			#local.homeAddressFieldSetInfo.fieldSetContent#
			#local.addressPrefFieldSetInfo.fieldSetContent#
			
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#local.strResult.formContent#
					<br/>
				</div>
				<br/>
				<strong>Total Price:</strong> #dollarFormat(local.strResult.totalPrice)#
				<br/>
				</td>
			</tr>
			</table>

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset local.Name = ""/>
		<cfif structKeyExists(arguments.rc, "m_firstname")>
			<cfset local.Name = arguments.rc['m_firstname']/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(arguments.rc, "m_lastname")>
			<cfset local.Name = local.Name & arguments.rc['m_lastname']/>
		</cfif>	

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#arguments.rc.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.confirmationHTMLToStaff)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
					
				</style>
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				
				<cfif arguments.errorCode eq "billedfound">
					#variables.strPageFields.BilledRedirectMessage#	
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "activefound">
					#variables.strPageFields.ActiveAcceptedMessage#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ActiveAcceptedMessage#	
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="storeMembershipApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
					parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', 
					docDesc='Membership Application Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
					 contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

    <cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>

		<cfset local.strReturn = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)/>
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
		<cfset local.maxFrequencyInstallments = local.objSubReg.getMaxFrequencyInstallments(siteID=arguments.siteID)>

		<cfset local.arrPaySchedule = arrayNew(1)>
		<cfset local.strPS = { date='', amount='' }>
		<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
			<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
		</cfloop>
				
		<cfset local.numPaymentsToUse = val(local.strReturn.subPaymentDates.NUMPAYMENTS)>
        <cfset local.NonUpFrontTotalAmt = 0>
        <cfif local.strReturn.totalMonthlyPrice GT 0>
		    <cfset local.NonUpFrontTotalAmt = local.strReturn.totalMonthlyPrice * local.numPaymentsToUse>
        <cfelseif local.strReturn.totalQuarterlyPrice GT 0>
            <cfset local.NonUpFrontTotalAmt = local.strReturn.totalQuarterlyPrice * local.numPaymentsToUse >
		<cfelseif local.strReturn.totalSplitPaymentsPrice GT 0>
            <cfset local.NonUpFrontTotalAmt = local.strReturn.totalSplitPaymentsPrice * local.numPaymentsToUse >
        </cfif>
		<cfset local.UpFrontTotalAmt = local.strReturn.totalFullPrice >
		
		<cfif local.UpFrontTotalAmt gt 0>
			<cfset local.arrPaySchedule[1] = { date=now(), amount=numberformat(local.UpFrontTotalAmt, "_.__") }>
			<cfset local.firstPaymentMinimum = numberformat(local.UpFrontTotalAmt, "_.__")>
		</cfif>

		<cfset local.distribAmt = local.NonUpFrontTotalAmt / local.numPaymentsToUse>
		<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.NonUpFrontTotalAmt - (numberformat((local.NonUpFrontTotalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

		<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
			<cfset local.loopAmt = 0>
			<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
			<cfset local.arrPaySchedule[local.loopCnt] = { date=now(), amount=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
		</cfloop>

		<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >
	
		<cfset local.strReturn.totalPrice = numberformat(local.arrPaySchedule[1].amount + local.strReturn.totalFullPriceRateOverridden + local.strReturn.totalMonthlyPriceRateOverridden + local.strReturn.totalQuarterlyPriceRateOverridden + local.strReturn.totalSplitPaymentsPriceRateOverridden, "_.__") />

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', subTermDates = {}, subPaymentDates = {}, totalFullPriceRateOverridden = 0, totalMonthlyPriceRateOverridden = 0, totalQuarterlyPriceRateOverridden = 0,  totalSplitPaymentsPriceRateOverridden = 0, totalFullPrice = 0, totalMonthlyPrice = 0, totalQuarterlyPrice = 0, totalSplitPaymentsPrice = 0, totalPrice = 0, subtotal = 0, subtotalRateOverridden = 0 }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">			
		</cfif>
		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<cfset local.rootSubTermFlag = arguments.subDefinitionStruct.rateTermDateFlag>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							<cfif arguments.recursionLevel neq 1>
								#arguments.subDefinitionStruct.subscriptionName# -
							</cfif>
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
										<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
                                            <cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">											
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
                                                   	#local.thisRate.rateName# (<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)													
													<cfif local.thisRateFrequency.frequencyName eq "Full">
														<cfset local.strReturn.totalFullPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
                                                        <cfset local.strReturn.totalMonthlyPriceRateOverridden = 0/>
                                                        <cfset local.strReturn.totalQuarterlyPriceRateOverridden = 0/>
														<cfset local.strReturn.totalSplitPaymentsPriceRateOverridden = 0/>
                                                    <cfelseif local.thisRateFrequency.frequencyName eq "Monthly">
														<cfset local.strReturn.totalMonthlyPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
                                                        <cfset local.strReturn.totalFullPriceRateOverridden = 0/>
                                                        <cfset local.strReturn.totalQuarterlyPriceRateOverridden = 0/>
														<cfset local.strReturn.totalSplitPaymentsPriceRateOverridden = 0/>
                                                    <cfelseif local.thisRateFrequency.frequencyName eq "Quarterly">
													    <cfset local.strReturn.totalQuarterlyPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
														<cfset local.strReturn.totalFullPriceRateOverridden = 0/>
                                                        <cfset local.strReturn.totalMonthlyPriceRateOverridden = 0/>
														<cfset local.strReturn.totalSplitPaymentsPriceRateOverridden = 0/>
													<cfelseif local.thisRateFrequency.frequencyName eq "Split Payments">
													    <cfset local.strReturn.totalSplitPaymentsPriceRateOverridden = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
														<cfset local.strReturn.totalFullPriceRateOverridden = 0/>
                                                        <cfset local.strReturn.totalMonthlyPriceRateOverridden = 0/>
														<cfset local.strReturn.totalQuarterlyPriceRateOverridden = 0/>														
                                                    </cfif>
													<cfset local.strReturn.subtotal = local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments/>
                                                    <cfif arguments.recursionLevel eq 1>														
														<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
														<cfset local.rootTermDateRFID = local.thisRateFrequency.rfid>														
														<cfset local.rootNumPayments = local.thisRateFrequency.numInstallments>
														<cfset local.rootRateInterval = local.thisRateFrequency.monthlyInterval>
														<cfset local.strReturn.subTermDates = local.objSubReg.getSubTermDates(termDateRFID=local.rootTermDateRFID, subTermFlag=local.rootSubTermFlag)>
														<cfset local.strReturn.subPaymentDates = local.objSubReg.getSubPaymentDates(local.strReturn.subTermDates.subTermStartDate,local.strReturn.subTermDates.subTermEndDate,local.rootNumPayments,local.rootRateInterval)>
													</cfif>
                                                </cfif>																																		
											</cfloop>
                                        <cfelse>										
											<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
													<cfset local.maxFrequencyInstallments = local.thisRateFrequency.numInstallments />
													<cfif local.thisRateFrequency.frequencyName eq "Full">														
														<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
														<cfset local.strReturn.totalMonthlyPrice = 0/>
                                                        <cfset local.strReturn.totalQuarterlyPrice = 0/>
														<cfset local.strReturn.totalSplitPaymentsPrice = 0/>
													<cfelseif local.thisRateFrequency.frequencyName eq "Monthly">
														<cfset local.strReturn.totalMonthlyPrice = local.thisRateFrequency.rateAmt />
														<cfset local.strReturn.totalFullPrice = 0/>
                                                        <cfset local.strReturn.totalQuarterlyPrice = 0/>
														<cfset local.strReturn.totalSplitPaymentsPrice = 0/>
													<cfelseif local.thisRateFrequency.frequencyName eq "Quarterly">
														<cfset local.strReturn.totalQuarterlyPrice = local.thisRateFrequency.rateAmt />
														<cfset local.strReturn.totalFullPrice = 0/>
                                                        <cfset local.strReturn.totalMonthlyPrice = 0/>
														<cfset local.strReturn.totalSplitPaymentsPrice = 0/>
													<cfelseif local.thisRateFrequency.frequencyName eq "Split Payments">
														<cfset local.strReturn.totalSplitPaymentsPrice = local.thisRateFrequency.rateAmt />
														<cfset local.strReturn.totalFullPrice = 0/>
                                                        <cfset local.strReturn.totalMonthlyPrice = 0/>
														<cfset local.strReturn.totalQuarterlyPrice = 0/>											
													</cfif>	
													<cfset local.strReturn.subtotal = local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments/>
													
													<cfif arguments.recursionLevel eq 1>
														<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>
														<cfset local.rootTermDateRFID = local.thisRateFrequency.rfid>														
														<cfset local.rootNumPayments = local.thisRateFrequency.numInstallments>
														<cfset local.rootRateInterval = local.thisRateFrequency.monthlyInterval>
														<cfset local.strReturn.subTermDates = local.objSubReg.getSubTermDates(termDateRFID=local.rootTermDateRFID, subTermFlag=local.rootSubTermFlag)>
														<cfset local.strReturn.subPaymentDates = local.objSubReg.getSubPaymentDates(local.strReturn.subTermDates.subTermStartDate,local.strReturn.subTermDates.subTermEndDate,local.rootNumPayments,local.rootRateInterval)>														
														#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat((local.thisRateFrequency.rateAmt * local.maxFrequencyInstallments) / local.strReturn.subPaymentDates.NUMPAYMENTS)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>												
													<cfelse>
														#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
													</cfif>
												</cfif>																																		
											</cfloop>
										</cfif>
									</cfif>
								</cfloop>
							</span>
						</div>
					</div>		
				
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false />							
									<cfset local.pcNumCounter = 1 />									
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false />
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true />
										</cfif>										
										<cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree) />
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
										<cfif len(trim(local.thisAddonSubForm.formContent))>										
											<cfset local.selectionFound = true />
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>											
												<cfset local.strReturn.totalFullPriceRateOverridden = local.strReturn.totalFullPriceRateOverridden + local.thisAddonSubForm.totalFullPriceRateOverridden />
												<cfset local.strReturn.totalMonthlyPriceRateOverridden = local.strReturn.totalMonthlyPriceRateOverridden + local.thisAddonSubForm.totalMonthlyPriceRateOverridden />
												<cfset local.strReturn.totalQuarterlyPriceRateOverridden = local.strReturn.totalQuarterlyPriceRateOverridden + local.thisAddonSubForm.totalQuarterlyPriceRateOverridden />
												<cfset local.strReturn.totalSplitPaymentsPriceRateOverridden = local.strReturn.totalSplitPaymentsPriceRateOverridden + local.thisAddonSubForm.totalSplitPaymentsPriceRateOverridden />
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
												<cfset local.strReturn.totalMonthlyPrice = local.strReturn.totalMonthlyPrice + local.thisAddonSubForm.totalMonthlyPrice />
												<cfset local.strReturn.totalQuarterlyPrice = local.strReturn.totalQuarterlyPrice + local.thisAddonSubForm.totalQuarterlyPrice />
												<cfset local.strReturn.totalSplitPaymentsPrice = local.strReturn.totalSplitPaymentsPrice + local.thisAddonSubForm.totalSplitPaymentsPrice />
											</cfif>
											<cfset local.pcNumCounter = local.pcNumCounter + 1 />											
											#local.thisAddonSubForm.formContent#
										</cfif>									
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>						
						</div>
					</cfif>						
				</cfif>
			</cfoutput>				
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>
	
</cfcomponent>