<cfset dataStruct = attributes.data.actionStruct>
<cfset baseQueryString = attributes.data.baseQueryString>
<cfset ajaxBaseQueryString = #Replace(baseQueryString,"&","^", "ALL")# >
<cfset baseURL = attributes.data.baseURL>
<cfset appRightsStruct = attributes.data.appRightsStruct>

<cfoutput>
	<cfif isDefined("dataStruct.displayMessage") AND dataStruct.displayMessage neq "">
		<p>#dataStruct.displayMessage#</p>
	<cfelse>
		<form method="post" class="form" name="lyrisSend" action="#baseURL#&lsAction=listPost" onSubmit="return _CF_checklyrisSend(this);">
			<input type="hidden" id="inreplytoMessageID" value="#(dataStruct?.inreplytoMessageID ?: 0)#" name="inreplytoMessageID">
			<input type="hidden" id="inreplyto" value="#dataStruct.headersForReplyMessageStruct['In-Reply-To']#" name="inreplyto">
			<input type="hidden" id="references" value="#dataStruct.headersForReplyMessageStruct['references']#" name="references">
			<div class="control-group">
				<div class="controls">
					<cfif isDefined("dataStruct.origMessage")>
						<input type="hidden" id="listSelector" value="#dataStruct.list#" name="list">
						List: #dataStruct.list#
					<cfelse>
						<select id="listSelector" name="list" class="span6" onChange="onChangeListSelectionForPost()">
							<option value="">Select a List</option>
							<cfloop item="local.currentItem" array="#dataStruct.distinctLists#"> 
								<option value="#local.currentItem.list_#" <cfif (dataStruct.distinctLists.len() eq 1) or (dataStruct.list eq local.currentItem.list_)>selected</cfif>>List: #local.currentItem.list_#</option>
							</cfloop>
						</select>
					</cfif>
				</div>
			</div>
			<div class="control-group hideUntilListSelected" id="listEmailControlGroup">
				<div class="controls">
					<select id="emailSelector" name="email" class="span6">
						<option value="">Select your Email Address</option>
						<cfif isdefined("dataStruct.emailsOnList") and isquery(dataStruct.emailsOnList)>
							<cfloop query="dataStruct.emailsOnList">
								<option value="#dataStruct.emailsOnList.emailaddr_#" <cfif (dataStruct.emailsOnList.recordcount eq 1)>selected</cfif>>From: #dataStruct.emailsOnList.emailaddr_#</option>
							</cfloop>
						</cfif>
					</select>
				</div>
			</div>
			<div class="control-group">
				<div class="controls">
					<cfif isDefined("dataStruct.origMessage")>
						<input type="hidden" id="subject" value="#dataStruct.subject#" name="subject">
						Subject: #dataStruct.subject#
					<cfelse>
						<input type="text" name="subject" class="span12 hideUntilListSelected" value="#dataStruct.subject#" placeholder="Enter Your Subject Here">
					</cfif>
				</div>
			</div>
			<button class="btn btn-mini btn-primary hideUntilListSelected" type="button" onclick="$(this).hide();$('##listPostAttachmentsDragDrop').show();">Attach Files</button>
			<div id="listPostAttachmentsDragDrop" style="display:none;"></div>
			<div id="listPostAttachmentsStatusMessages"></div>
			<div id="listPostAttachmentsFileList"></div>
			<div id="listPostAttachmentsStatusBar"></div>
			<div class="control-group hideUntilListSelected" style="margin-top:15px;">
				<label class="control-label" for="messagebox">Message:</label>
				<div class="controls">
					<textarea name="messagebox" rows="8" wrap="soft" class="span12" ><cfoutput><cfif isDefined("dataStruct.origMessage")>#htmlEditFormat(dataStruct.origMessage)#<cfelse>#dataStruct.postMessage#</cfif></cfoutput></textarea>
				</div>
			</div>
			<input type="button" name="Submit" id="lyrisSendBtn" class="btn btn-primary hideUntilListSelected" value="Send Email" onClick="javaScript:return _CF_checklyrisSend(this);" <cfoutput>#iif(dataStruct.list eq "", de("disabled"),de(""))#</cfoutput>></td>
		</form>
		
		<template id="selectedFileTemplate">
			<span class="label label-info selectedFile">	
				<i class="icon-paperclip icon-white">&nbsp;</i></i>
			  <span class="filename"></span>
			  <a
				data-uppyfileid="" style="margin-left:10px;color:##ffffff;"
				href="javascript:void(0)"
				onclick="uppy.removeFile(this.dataset.uppyfileid)"
				><i class="icon-trash icon-white">&nbsp;</i></a
			  >
			</span>
		  </template>
		  <template id="uppyMessageTemplate">
			<div class="alert uppyMessage"></div>
		  </template>
	</cfif>
</cfoutput>