<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function hideAlert() { $('##verr').html('').hide(); }
		function showAlert(msg) { $('##verr').html(msg).show(); }
		function validateForm() {
			hideAlert();
			var arrReq = [];

			$('##vc').val($('##vc').val().toUpperCase());
			if ($('##vc').val().trim().length == 0) arrReq[arrReq.length] = 'Subscription Code is required.';

			if (arrReq.length > 0) {
				showAlert(arrReq.join('<br/>'));
				return false;
			}
			$('##frmSub button:submit').html('<i class="icon-spin icon-spinner mcsubs-font-size-sm mcsubs-mr-1"></i> Please wait...').prop('disabled',true);
			return true;
		}

		$(function() {
			<cfif len(attributes.data.actionStruct.errorMessage)>
				showAlert('#encodeForJavaScript(attributes.data.actionStruct.errorMessage)#');
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">