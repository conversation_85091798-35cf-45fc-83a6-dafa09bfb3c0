<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	
	local.arrCustomFields = [];
	
	local.tmpField = { name="InvoiceProfile",type="STRING",desc="Name of Invoice Profile with past due invoices",value="MCB Member Dues" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Subscription",type="STRING",desc="UID for Membership Subscription",value="e4f7202f-6db8-47b3-b2a8-16e86f6a5632" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="PageContent",type="CONTENTOBJ",desc="Page title and content at top",value="Editable content." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="PaymentProfile",type="STRING",desc="Code for payment profile to use for payments",value="MCB_CC" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="PaymentCodeACH",type="STRING",desc="Code for payment profile Bank Draft to use for payments",value="MCB_ACH" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ButtonText",type="STRING",desc="Text for payment button",value="PAY NOW" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="NoBilledSubMsg",type="STRING",desc="Message if no billed sub",value="Your membership renewal is not available online at this time." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="paidmessage",type="STRING",desc="Message user has a subscription and no unpaid invoice",value="you have no unpaid invoices." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	
	variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
</cfscript>

<cfset variables.orgID = arguments.event.getValue('mc_siteInfo.orgID', '0')>
<cfset variables.siteID = arguments.event.getValue('mc_siteInfo.siteID', '0')>
<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

<cfif val(variables.useMID) eq 0>
	<cflocation url="/renewsub" addtoken="false">
</cfif>

<cfset local.qrySubscription = application.objCustomPageUtils.sub_isbilledSubscription(mcproxy_siteID=variables.siteID, typeName='Membership', memberID=variables.useMID)/>

<cfif local.qrySubscription.success eq true>
	<cfset local.qryInvoicesWithAmountDue = application.objCustomPageUtils.getUnpaidInvoices(variables.useMID,variables.strPageFields.InvoiceProfile)>
		
	<cfif local.qryInvoicesWithAmountDue.recordcount neq 0>	
		<cfsavecontent variable="local.pageHead">
			<cfoutput>
				<style type="text/css">
					.table {
						width: 80%;
						margin: 0 auto;
						border: 1px solid ##333;
						color: ##000;
					}
					th {
						background-color: ##870231;
						color: ##fff;
					}
					.table-striped tbody>tr:nth-child(odd)>td, .table-striped tbody>tr:nth-child(odd)>th {
						background-color: ##EBF2F2;
					}
					.table th, .table td {
						border-right: 1px solid ##333;
						border-left: 1px solid ##333;
						padding: 4px;
						font-size: 14px;
						font-family: "Lato",Helvetica,Arial,sans-serif;
						border-top: 1px solid ##EBF2F2;
					}
					.bottomDiv {
						font-weight: 500;
						font-size: 22px;
					}
					.table a {
						color: ##000;
					}
				</style>
				<script language="JavaScript">
					function hideAlert() { $('##issuemsg').html('').hide(); };
					function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alertMsg').show(); };
					function _FB_validateForm(){
						var theForm = document.forms["frmPaymentDue"];
						var arrReq = new Array();
						if (arrReq.length > 0) {
							return false;
						}
					}
					function validatePaymentForm() {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
		
						mc_continueForm($('##frmPayment'));
						return false;
					}
					function viewInvoice(i) {
						self.location.href = '/?pg=invoices&va=show&item=' + escape(i) + '&mode=stream';
					}
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.pageHead#">

	<cfoutput>
		
		<cfform name="frmPaymentDue" id="frmPaymentDue" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">

			#variables.strPageFields.PageContent#
			<br/><br/>
			<cfset variables.totalDueAmt = 0>
			<table class="table table-striped">
				<thead>
					<th>Invoice</th>
					<th>Amount Due</th>
					<th>View Invoice</th>
				</thead>
				<tbody>
				<cfloop query="local.qryInvoicesWithAmountDue">
					<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryInvoicesWithAmountDue.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryInvoicesWithAmountDue.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
					<tr>
						<td>
							#local.qryInvoicesWithAmountDue.invoiceNumber# <br>#local.qryInvoicesWithAmountDue.invoiceProfile#
						</td>
						<td>
							$#numberFormat(local.qryInvoicesWithAmountDue.InvCanPay,"0.00")#
						</td>
						<td>
							<a href="javascript:viewInvoice('#local.stInvEnc#')" class="inv_vi"><i class="fa fa-file-pdf-o" aria-hidden="true"></i> View Invoice</a>
						</td>
					</tr>
					<cfset variables.totalDueAmt = variables.totalDueAmt + local.qryInvoicesWithAmountDue.InvCanPay>
				</cfloop>
				</tbody>
			</table>
			<br/><br/>
			<div class="bottomDiv">
				<center>
					Total : $#numberFormat(variables.totalDueAmt,"0.00")# &nbsp;&nbsp;&nbsp;&nbsp;
					<button type="submit" name="btnSubmit">#variables.strPageFields.ButtonText#</button>
				</center>
			</div>
			
		</cfform>
		
		<br /><br />
	</cfoutput>
		
	<cfelse>
		<cflocation url="/renewsub/#local.qrySubscription.directLinkCode#" addtoken="false">
	</cfif>

<cfelse>
	
	<cfset local.qryInvoicesWithAmountDue = application.objCustomPageUtils.getUnpaidInvoices(variables.useMID,variables.strPageFields.InvoiceProfile)>
	
	<cfif local.qryInvoicesWithAmountDue.recordcount neq 0>
		<cflocation url="/?pg=invoices" addtoken="false">
	<cfelse>	
		
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionActive">
		select s.subscriberID, s.directLinkCode, ss.statusCode as status, count(s2.subscriberID) as currAccepted
		from dbo.sub_subscribers s
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
			and subs.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.Subscription#">
		inner join dbo.sub_types t on t.typeID = subs.typeID 
			and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
		inner join dbo.sub_statuses ss on ss.statusID = s.statusID
		left outer join dbo.sub_subscribers s2 
			inner join dbo.sub_subscriptions subs2 on subs2.subscriptionID = s2.subscriptionID
				and subs2.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.Subscription#">
			inner join dbo.sub_types t2 on t2.typeID = subs2.typeID 
				and t2.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			inner join dbo.sub_statuses ss2 on ss2.statusID = s2.statusID on s2.memberID = s.memberID
				and ss2.statusCode = 'P'
				and s2.parentSubscriberID is null
		where s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.useMID#">
		and ss.statusCode = 'A'
		and s.parentSubscriberID is null
		group by s.subscriberID, s.directLinkCode, ss.statusCode
	</cfquery>
		<!--- content to be displayed is the member does not have a renewal pending --->
		<cfoutput>
			<div class="tsAppBodyText">
				<cfif (local.qrySubscriptionActive.recordcount eq 1)>
					#variables.strPageFields.paidmessage# <br/>
				<cfelse>
					#variables.strPageFields.NoBilledSubMsg#
				</cfif>
				
			</div>
		</cfoutput>
	</cfif>
</cfif>

<cfif IsDefined("Form.btnSubmit")>
	<cfset variables.arrPayMethods = []>
	<cfif len(variables.strPageFields.PaymentProfile)>
		<cfset ArrayAppend(variables.arrPayMethods, variables.strPageFields.PaymentProfile)>
	</cfif>
	<cfif len(variables.strPageFields.PaymentCodeACH)>
		<cfset ArrayAppend(variables.arrPayMethods, variables.strPageFields.PaymentCodeACH)>
	</cfif>
	
	<cfset variables.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=variables.arrPayMethods, siteID=variables.siteID, memberID=variables.useMID, title="Choose Your Payment Method", formName='frmPayment', backStep="")>
	
	<cfsavecontent variable="variables.headcode">
		<cfoutput>#variables.strReturn.headcode#</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#variables.headcode#">
	
	<cfsavecontent variable="local.PaymentHead">
	<cfoutput>
		<style type="text/css">
			.mccfpayinfodiv [name=btnBack] { display: none; }
		</style>
	</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.PaymentHead#">
	
	<cfform name="frmPayment" id="frmPayment" method="post" action="#local.customPage.baseURL#" onsubmit="return validatePaymentForm()">	<div class="tsAppSectionHeading">Select Payment Method</div>
		<cfinput type="hidden" name="fa" id="fa" value="processPayment">
		<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
		<div>
			<cfoutput>#variables.strReturn.paymentHTML#</cfoutput>
		</div>
	</cfform>
</cfif>

<cfif IsDefined("Form.fa") AND form.fa EQ "processPayment">
		
	<cfset rc = arguments.event.getCollection()>
	
	<cfquery dbtype="query" name="local.qryInvoiceDueNow">
		select invoiceID, invoiceProfileID, InvCanPay as amount
		from [local].qryInvoicesWithAmountDue
	</cfquery>
	
	<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID, profileCode=arguments.event.getValue('mccf_payMeth','#variables.strPageFields.PaymentProfile#'))>

	<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
	<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow>

	<cfset local.strAccTemp = { totalPaymentAmount=variables.totalDueAmt, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=rc }>
	<cfset local.strAccTemp.payment = { detail="Payment", amount=local.strAccTemp.totalPaymentAmount, profileID=local.payProfileID, profileCode=arguments.event.getValue('mccf_payMeth','#variables.strPageFields.PaymentProfile#') }>
	<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
	<cfset application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0)>
		
	<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>
		<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
		<cflocation url="/renewsub/#local.qrySubscription.directLinkCode#" addtoken="false">
	</cfif>

</cfif>
