<cfoutput>
<div class="form-group row-fluid">
	<div class="span12" style="text-align:left;">
		<h4><b>My Referrals</b></h4>
	</div>
</div>
<div class="form-group row-fluid">
	<div class="span12">
		<form class="form-inline refFilterForm" action="/" method="get">
			<input type="hidden" name="pg" value="referrals">
			<input type="hidden" name="panel" value="browse">
			<input type="hidden" name="tab" value="#variables.structData.tabToShow#">
			<input type="hidden" name="sort" value="#variables.structData.sort#">
			<input type="hidden" name="count" value="#variables.structData.count#">
			<input type="hidden" name="orderBy" value="#variables.structData.orderBy#">
			<input type="hidden" name="start" value="1">
			<label class="refDateRange"><br/>
				<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
					<option <cfif variables.structData.refDateRanges eq 0 > selected </cfif> value="0">View All My Referrals</option>
					<option <cfif variables.structData.refDateRanges eq 30 > selected </cfif>  value="30">Last 30 Days</option>
					<option <cfif variables.structData.refDateRanges eq 60 > selected </cfif> value="60">Last 60 Days</option>
					<option <cfif variables.structData.refDateRanges eq 90 > selected </cfif> value="90">Last 90 Days</option>
				</select>
			</label>
			<label class="refferalID"><b>Referral ID</b><br/>
				<input type="text" name="filterReferralID" class="input-small refferalID" <cfif variables.structData.filterReferralID GT 0> value="#variables.structData.filterReferralID#" </cfif>>
			</label>
			<label class="clientLastName"><b>Client Last Name</b><br/>
				<input type="text" name="filterClientLastName" class="input-medium clientLastName" value="#variables.structData.filterClientLastName#">
			</label>
			<label class="clientFirstName"><b>Client First Name</b><br/>
				<input type="text" name="filterClientFirstName" class="input-medium clientFirstName" value="#variables.structData.filterClientFirstName#">
			</label>
			<label class="Status"><b>Status</b><br/>
				<select class="Status input-medium" name="filterStatus">
					<option value="0"></option>
					<cfloop query="variables.structData.qryGetReferralStatus">
						<option <cfif variables.structData.filterStatus eq variables.structData.qryGetReferralStatus.clientReferralStatusID > selected </cfif> value="#variables.structData.qryGetReferralStatus.clientReferralStatusID#">#variables.structData.qryGetReferralStatus.statusName#</option>
					</cfloop>
				</select>
			</label>	
			<button type="submit" class="btn filterRefferals">Search</button>
			<button type="reset" class="btn filterClear">Clear Filters</button>									
		</form>
	</div>
</div>
<div class="form-group row-fluid <cfif arrayLen(variables.structData.referralData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;								
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>	
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>			
</div>

<cfif arrayLen(variables.structData.referralData) gt 0>							
	<div class="dataResp">
		<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.referralData)#">
			<div class="row-fluid" bgcolor="##DEDEDE">
				<div class="span12 clearfix well well-small">
					<div class="row-fluid">
						<div class="span8 eventLeft list-text">			
							<p>														
								<a href="javascript:editReferral(#variables.structData.referralData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>Referral ##  #variables.structData.referralData[local.Counter].CLIENTREFERRALID#</b></a>
							</p>
							<p><b>Client Name :</b>  #variables.structData.referralData[local.Counter].CLIENTNAME#</p>	
							<cfset local.issueDesc = "">
							<cfif structKeyExists(variables.structData.referralData[local.Counter], "ISSUEDESC")>
								<cfset local.issueDesc = variables.structData.referralData[local.Counter].ISSUEDESC>
							</cfif>													
							<div class="mb-10"><b>Description : </b><span class="show-read-more">#htmleditformat(local.issueDesc)#</span></div>
							<p><b>Status :</b> #variables.structData.referralData[local.Counter].STATUSNAME#</p>	
							
						</div>											
						<div class="span4 eventRight">
							<p class="ReferralDate"><b>Referral Date :</b> #variables.structData.referralData[local.Counter].clientReferralDate#</p>
						</div>
					</div>	
					<div class="row-fluid">
						<cfif not variables.structData.referralData[local.Counter].isClosed>
							<div class="MCReferralBtnBox MCReferralBtnBoxMob">
								<ul>
									<li><a href="javascript:void(0);" class="retainBtn btn"  data-clientrefid="#variables.structData.referralData[local.Counter].CLIENTREFERRALID#">Retain Referral</a></li>
									<li><a href="javascript:void(0);" class="closeReferralBtn btn" data-clientrefid="#variables.structData.referralData[local.Counter].CLIENTREFERRALID#">Close Referral</a></li>
								</ul>
							</div>
						</cfif>	
					</div>											
				</div>
			</div>
		</cfloop>
	</div>
	
	<div class="row-fluid  dataTable hide">
		<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
			<tr class="gridHead">
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referralid">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="referralid" data-order="#local.order#"><b>Referral ID##</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "clientname" >
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="clientname" data-order="#local.order#"><b>Client Name</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "description">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="description" data-order="#local.order#"><b>Description</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "status" >
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="status" data-order="#local.order#"><b>Status</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referralDate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="referralDate" data-order="#local.order#"><b>Referral Date</b>&nbsp;<i class="fa fa-sort"></i></td>
			</tr>			
			<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.referralData)#">
				<tr class="gridData" style="position:relative">
					<td width="140" style="text-align:left;">
						<a href="javascript:editReferral(#variables.structData.referralData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>#variables.structData.referralData[local.Counter].CLIENTREFERRALID#</b></a>
					</td>
					<td width="175" style="text-align:left;">
						#variables.structData.referralData[local.Counter].CLIENTNAME#
					</td>
					<td width="175" style="text-align:left;" class="MCIssueDesc">
						<cfset local.issueDesc = "">
						<cfset local.fullIssueDesc = "">
						<cfif structKeyExists(variables.structData.referralData[local.Counter], "ISSUEDESC")>
							<cfset local.fullIssueDesc = variables.structData.referralData[local.Counter].ISSUEDESC>
							<cfset local.issueDesc = local.fullIssueDesc >
							<cfif len(trim(local.fullIssueDesc)) gt 100>
								<cfset local.issueDesc = left(variables.structData.referralData[local.Counter].ISSUEDESC, "97") & "...">
							</cfif>
						</cfif>	
						#htmleditformat(local.issueDesc)#
						<cfif len(trim(local.fullIssueDesc)) gt 100>
							<div class="MCIssueDescBox hide">
								#htmleditformat(local.fullIssueDesc)#
							</div>
						</cfif>
					</td>
					<td width="250" style="text-align:left;" class="MCStatusWrapper">
						#variables.structData.referralData[local.Counter].STATUSNAME#

						<cfif not variables.structData.referralData[local.Counter].isClosed>
							<div class="MCReferralBtnBox">
								<ul>
									<li><a href="javascript:void(0);" class="retainBtn btn"  data-clientrefid="#variables.structData.referralData[local.Counter].CLIENTREFERRALID#">Retain Referral</a></li>
									<li><a href="javascript:void(0);" class="closeReferralBtn btn" data-clientrefid="#variables.structData.referralData[local.Counter].CLIENTREFERRALID#">Close Referral</a></li>
								</ul>
							</div>
						</cfif>

					</td>
					<td width="175" style="text-align:left;">
						#variables.structData.referralData[local.Counter].clientReferralDate#
					</td>												
				</tr>																					
			</cfloop>
		</table>
	</div>							
</cfif>	

<div class="form-group row-fluid <cfif arrayLen(variables.structData.referralData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>	
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>			
</div>

<div class="row-fluid <cfif arrayLen(variables.structData.referralData) gt 0> hide</cfif> noReferrals" bgcolor="##DEDEDE">
	<div class="span12 clearfix well well-small">
		<div class="row-fluid">
			<p>No Referrals match this criteria.</p>
		</div>
	</div>
</div>
</cfoutput>