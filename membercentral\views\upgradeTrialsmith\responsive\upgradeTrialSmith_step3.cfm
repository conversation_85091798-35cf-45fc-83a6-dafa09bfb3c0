<cfinclude template="../commonUpgradeTrialSmithStep3JS.cfm">

<cfoutput>
<div class="upgradeTrialSmith-card upgradeTrialSmith-mt-3 upgradeTrialSmith-p-3">
    <div id="upgradeTrialSmithTermContainer" <cfif variables.cfcuser.subscriptionData['4'].strJoinTS.iAgree EQ 1> style="display: none;"</cfif>>
        <div class="upgradeTrialSmith-mb-3">
            <form name="frmJoin" action="#arguments.event.getValue('upgradeTrialSmithurl')#" method="POST">
                <section>
                    <div class="well">
                        <cfinclude template="/sitecomponents/COMMON/content/viewTSlicenseagreement.cfm">
                    </div>
                    <div class="row-fluid">
                        <div class="span12"> 
                            <button type="button" name="btnAgree" class="btn" onclick="agreeSubmit(1);">I Agree</button>
                            <button type="button" name="btnAgree" class="btn" onclick="agreeSubmit(0);">I Don't Agree</button>
                        </div>
                    </div>
                </section>
            </form>
        </div>
    </div>
    <div id="upgradeTrialSmithSelectedTermContainer" class="upgradeTrialSmithSummary upgradeTrialSmith-cursor-pointer" data-upgradetrialsmithsummarystep="4" <cfif variables.cfcuser.subscriptionData['4'].strJoinTS.iAgree NEQ 1> style="display: none;"</cfif>>
        <div class="upgradeTrialSmith-d-flex">
            <a href="##" class="upgradeTrialSmith-align-self-center upgradeTrialSmith-mr-2 upgradeTrialSmith-font-size-lg upgradeTrialSmith-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
            <div class="upgradeTrialSmith-col">
                <div id="upgradeTrialSmithSelectedTerm" class="upgradeTrialSmith-font-size-lg upgradeTrialSmith-font-weight-bold"><b>TRIALSMITH LICENSE AGREEMENT</b></div>
            </div>
        </div>
    </div>
    <div id="upgradeTrialSmithStep3SaveLoading" style="display:none;"></div>
</div>
</cfoutput>