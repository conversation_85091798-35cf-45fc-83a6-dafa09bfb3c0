<cfoutput>
<div class="<cfif arguments.topOrBottom eq "bottom">s_rhrd s_pgbtm tsAppBT<cfelse>s_rhrd s_pgtop tsAppBB</cfif>" data-totalPages="#arguments.numTotalPages#" data-currentPage="#arguments.NumCurrentPage#">
	<cfif arguments.numTotalPages gt 1>
		<cfif arguments.topOrBottom eq "bottom">
			<div style="margin-top:15px; max-width:650px; text-align:center; margin-left:auto; margin-right:auto;">
				<cfif arguments.NumCurrentPage gt 1>
					<span style="">
						<button class="tsAppBodyButton bucketListButton bucketPagingButtonFirstPage" style="width:inherit;" onclick="b_doSearchPN(#arguments.searchid#,1,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">&lt;&lt;</button>
						<button class="tsAppBodyButton bucketListButton bucketPagingButtonPreviousPage" style="width:inherit;" onclick="b_doSearchPN(#arguments.searchid#,#arguments.startrow - variables.thisBucketMaxPerPage#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">&lt;</button>
					</span>
				</cfif>
				
				<span style=" margin-left:20px;">
					<cfloop index="local.i" from="#local.startPage#" to="#local.endPage#">
						<cfset local.thisStartRow = local.i>
						<cfif local.i gt 1>
							<cfset local.thisStartRow = (variables.thisBucketMaxPerPage * (local.i-1)) + 1>
						</cfif>
						<cfif arguments.NumCurrentPage eq local.i><span class="tsAppBodyTextImportant">#local.i#</span> <cfelse> <a href="javascript:b_doSearchPN(#arguments.searchid#,#local.thisStartRow#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">#local.i#</a> </cfif> <cfif local.i lt local.endPage> &nbsp; </cfif>
					</cfloop>
				</span>

				<cfif arguments.NumCurrentPage lt arguments.NumTotalPages> 
					<span style="margin-left:20px;">
						<button class="tsAppBodyButton bucketListButton bucketPagingButtonNextPage" style="width:inherit;" onclick="b_doSearchPN(#arguments.searchid#,#arguments.startrow + variables.thisBucketMaxPerPage#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">&gt;</button>
						<button class="tsAppBodyButton bucketListButton bucketPagingButtonLastPage" style="width:inherit;" onclick="b_doSearchPN(#arguments.searchid#, #(variables.thisBucketMaxPerPage * (arguments.NumTotalPages-1)) + 1#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">&gt;&gt;</button>
					</span>	
				</cfif>
				<div style="max-width:650px; text-align:center; margin-left:auto; margin-right:auto;">
					Page #arguments.NumCurrentPage# of #numberformat(arguments.NumTotalPages)# 
				</div>
			</div>
		<cfelse>
			<span id="tsSearchTopPagingControls" <cfif arguments.topOrBottom eq "top">style="float:right;"</cfif>>
				<cfif arguments.NumCurrentPage gt 1>
					<button class="tsAppBodyButton bucketListButton bucketPagingButtonPreviousPage" style="width:inherit;" onclick="b_doSearchPN(#arguments.searchid#,#arguments.startrow - variables.thisBucketMaxPerPage#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">&lt;</button>
				</cfif>
				Page #arguments.NumCurrentPage# of #numberformat(arguments.NumTotalPages)# 
				<cfif arguments.NumCurrentPage lt arguments.NumTotalPages> 
					<button class="tsAppBodyButton bucketListButton bucketPagingButtonNextPage" style="width:inherit;" onclick="b_doSearchPN(#arguments.searchid#,#arguments.startrow + variables.thisBucketMaxPerPage#,'#arguments.currentSort##local.sortOrder#','#arguments.filter#','#arguments.postfilter#');">&gt;</button>
				</cfif>
			</span>
		</cfif>	
	</cfif>
	<cfif arguments.topOrBottom eq "top"><b>#numberformat(arguments.itemCount)#</b> #arguments.itemword#<cfif arguments.itemCount is not 1>#arguments.itemwordSuffix#</cfif> found</cfif>
	<cfif ArrayLen(arguments.arrSort) and arguments.topOrBottom neq "bottom">
		<br/>Sort by: 
		<cfloop from="1" to="#arraylen(arguments.arrSort)#" index="local.arrEl">
			<cfset local.icon = "">
			<cfif listLen(arguments.arrSort[local.arrEl],"|") gt 2 and len(trim(arguments.currentSort)) and arguments.currentSort eq getToken(arguments.arrSort[local.arrEl],1,"|")> 
				<cfif getToken(arguments.arrSort[local.arrEl],3,"|") eq "ASC">
					<cfset local.icon = "<i class='icon-arrow-down' title='Click to sort in descending order' style='margin-bottom:-3px;'></i>">
				<cfelse>
					<cfset local.icon = "<i class='icon-arrow-up' alt='Click to sort in ascending order' style='margin-bottom:-3px;'></i>">
				</cfif>
			</cfif>
			<a style="" href="javascript:b_doSearchSort(#arguments.searchid#,'#GetToken(arguments.arrSort[local.arrEl],1,"|")#<cfif listLen(arguments.arrSort[local.arrEl],"|") gt 2>|#GetToken(arguments.arrSort[local.arrEl],3,"|")#</cfif>'<cfif len(#arguments.filter#)>,'#arguments.filter#'</cfif><cfif len(#arguments.postfilter#)>,'#arguments.postfilter#'</cfif>);" <cfif GetToken(arguments.arrSort[local.arrEl],1,"|") eq arguments.currentSort>class="currSort "</cfif>>#GetToken(arguments.arrSort[local.arrEl],2,"|")# #local.icon#</a><cfif local.arrEl lt arraylen(arguments.arrSort)> &bull; </cfif>
		</cfloop>
	</cfif>
</div>
</cfoutput>