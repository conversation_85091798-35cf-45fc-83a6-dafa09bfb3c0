<cfsavecontent variable="local.css">
	<cfoutput>
	<style type="text/css">
	ul.groupImage { width:100%; text-align:left; float:left; padding:0; padding-left:0; }
	ul.groupImage li.groupImage { float:left; padding:0; padding-left:0; list-style-type:none; list-style-position:inside; }
	<cfif attributes.data.viewDirectory EQ 'default'>
		div##divclientReferralAmount { margin-left:40px; font-size: 110%; }
		##CCInfoError { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
	</cfif>
	</style>
	<script language="JavaScript">
		function clientRefMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
						var iframeWin = document.querySelector('iframe[name=iframeManageForm'+event.data.profileid+']').contentWindow;
						var message = { success:true, 
										messagetype:"MCFrontEndPaymentEvent", 
										paymentbuttonname:$('##clientSaleBtn_'+event.data.profileid).text(), 
										profileid:event.data.profileid };
						iframeWin.postMessage(message, event.origin);
						break;

					case "mcgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.payprofileid) {
							showClientRefPaymentProcessing(event.data.profileid);
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmClient');
							$('<input>').attr({ type: 'hidden', name: 'p1_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmClient');
							$('##profileid').val(event.data.profileid);
							payProfileCode = $('##clientSaleBtn_'+event.data.profileid).data('payprofilecode');
							faAccountCode = $('##clientSaleBtn_'+event.data.profileid).data('faaccountcode');
							gatewayType = $('##clientSaleBtn_'+event.data.profileid).data('gateway');
							$('##payProfileCode').val(payProfileCode);
							$('##faAccountCode').val(faAccountCode);
							$('##gatewayType').val(gatewayType);
							
							setClientRefPaymentProcessingFeesField(event.data);
							clientRefCheckout();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							payProfileCode = $('##clientSaleBtn_'+event.data.profileid).data('payprofilecode');
							faAccountCode = $('##clientSaleBtn_'+event.data.profileid).data('faaccountcode');
							gatewayType = $('##clientSaleBtn_'+event.data.profileid).data('gateway');

							$('##payProfileCode').val(payProfileCode);
							$('##faAccountCode').val(faAccountCode);
							$('##gatewayType').val(gatewayType);
							
							
							showClientRefPaymentProcessing(event.data.profileid);
							setClientRefPaymentTokenData(event.data.tokendata,event.data.profileid);
							setClientRefPaymentProcessingFeesField(event.data);
							clientRefCheckout();
						}
						break;
				};

			} else {
				return false;
			}
		}
		function showClientRefPaymentProcessing(pid) {
			$('##ccForm')
				.html('<i class="icon icon-spinner"></i> Please Wait...')
				.css({'height':'75px', 'padding':'5px'});
		}
		function setClientRefPaymentProcessingFeesField(obj) {
			if (typeof obj.enableprocessingfee != "undefined") {
				let processFeeDonationElement = $('##processFeeDonation'+obj.profileid);
				if (processFeeDonationElement.length) {
					processFeeDonationElement.val(obj.enableprocessingfee);
				} else {
					$('<input>').attr({ type: 'hidden', name: 'processFeeDonation'+obj.profileid, id: 'processFeeDonation'+obj.profileid }).val(obj.enableprocessingfee).appendTo('form##frmClient');
				}
			}
		}
		function setClientRefPaymentTokenData(tokendata,profileid) {
			let tokenDataElement = $('##p_'+profileid+'_tokenData');
			if (tokenDataElement.length) {
				tokenDataElement.val(JSON.stringify(tokendata));
			} else {
				$('<input>').attr({ type: 'hidden', name: 'p_'+profileid+'_tokenData', id: 'p_'+profileid+'_tokenData' }).val(JSON.stringify(tokendata)).appendTo('form##frmClient');
			}
		}
		function clientRefCheckout() {
			$("##frmClient").attr('action', '#attributes.data.mainurl#&ra=checkout');
			$('##frmClient').submit();
		}

		$(function() {
			if(localStorage.resultViewed != undefined && localStorage.resultViewed == 1){
				$("##frmClient").hide();
				window.location.href = '#attributes.data.mainurl#';
			}
			$('.goBack').click(function(){
				var errorMsg = "";
				$("##frmClient").attr('action', '#attributes.data.mainurl#');
				$('##frmClient').submit();
			});	

			$('button.clientSaleBtn').click(function(){
				var arrReq = new Array();
				$(this).attr('disabled','disabled');
				profileid = $(this).data('profileid');

				<cfloop array="#attributes.data.arrPaymentForm#" index="local.arrData">
					#ToScript(local.arrData.profileID,"profileidEach")#

					if(profileid == profileidEach){
						#local.arrData.STRPAYMENTFORM.jsvalidation#
					}
				</cfloop>

				if (arrReq.length) {
					$(this).removeAttr('disabled');
					var msg = 'Please address the following issues with your application:\n\n';
					for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
					alert(msg);
					return false;
				} else {
					$('##profileid').val(profileid);
					payProfileCode = $('##clientSaleBtn_'+profileid).data('payprofilecode');
					faAccountCode = $('##clientSaleBtn_'+profileid).data('faaccountcode');
					gatewayType = $('##clientSaleBtn_'+profileid).data('gateway');

					$('##payProfileCode').val(payProfileCode);
					$('##faAccountCode').val(faAccountCode);
					$('##gatewayType').val(gatewayType);
					clientRefCheckout();
				}		

			});

			window.addEventListener("message", clientRefMessageHandler, false);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.css#">