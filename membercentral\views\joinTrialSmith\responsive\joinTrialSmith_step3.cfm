<cfinclude template="../commonJoinTrialSmithStep3JS.cfm">

<cfoutput>
<div class="joinTrialSmith-card joinTrialSmith-mt-3 joinTrialSmith-p-3">
    <div id="joinTrialSmithTermContainer" <cfif session.cfcuser.subscriptionData['4'].strJoinTS.iAgree EQ 1> style="display: none;"</cfif>>
        <div class="joinTrialSmith-mb-3">
            <form name="frmJoin" action="#arguments.event.getValue('jointrialsmithurl')#" method="POST">
                <section>
                    <div class="well">
                        <cfinclude template="/sitecomponents/COMMON/content/viewTSlicenseagreement.cfm">
                    </div>
                    <div class="row-fluid">
                        <div class="span12"> 
                            <button type="button" name="btnAgree" class="btn" onclick="agreeSubmit(1);">I Agree</button>
                            <button type="button" name="btnAgree" class="btn" onclick="agreeSubmit(0);">I Don't Agree</button>
                        </div>
                    </div>
                </section>
            </form>
        </div>
    </div>
    <div id="joinTrialSmithSelectedTermContainer" class="joinTrialSmithSummary joinTrialSmith-cursor-pointer" data-jointrialsmithsummarystep="4" <cfif session.cfcuser.subscriptionData['4'].strJoinTS.iAgree NEQ 1> style="display: none;"</cfif>>
        <div class="joinTrialSmith-d-flex">
            <a href="##" class="joinTrialSmith-align-self-center joinTrialSmith-mr-2 joinTrialSmith-font-size-lg joinTrialSmith-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
            <div class="joinTrialSmith-col">
                <div id="joinTrialSmithSelectedTerm" class="joinTrialSmith-font-size-lg joinTrialSmith-font-weight-bold"><b>TRIALSMITH LICENSE AGREEMENT</b></div>
            </div>
        </div>
    </div>
    <div id="joinTrialSmithStep3SaveLoading" style="display:none;"></div>
</div>
</cfoutput>