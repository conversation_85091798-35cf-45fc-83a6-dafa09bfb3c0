<cfsavecontent variable="local.searchAppJS">
	<cfoutput>
	<script language="javascript">
		function toggleBKList(isPostSearchOp){
			var isExpand = $('##toggleBKListIcon').hasClass('icon-plus-sign');
			$('##toggleBKListText').text(isExpand ? 'Collapse Databases' : $('##toggleBKListText').data('initbuttontext'));
			$('##toggleBKListIcon').toggleClass('icon-plus-sign').toggleClass('icon-minus-sign');
			$('##bk_list').toggleClass('hidden-phone');
			$('##bk_content').toggleClass('hidden-phone');

			if(isPostSearchOp){
				$('##resultTotalCountHolder').toggle(isExpand ? true : false);
				$('##toggleBKListCountDisplay').toggle(isExpand ? false : true);
			}
		}
		function gotoBKContainer(bkContainerID) {
			let bkContainer = $('##'+bkContainerID);
			if(bkContainer.length && bkContainer.is(':visible')) {
				$('html, body').animate({
					scrollTop: bkContainer.offset().top - 300
				}, 750);
			}
		}
		function bk_slideToggle(id,p,el) {
			$('##'+id).slideToggle(500);
			gotoBKContainer(id);
			$(el).find('.fa-chevron-down').toggle();
			$(el).find('.fa-chevron-up').toggle();
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.searchAppJS,'\s{2,}',' ','ALL')#">