<cfif NOT variables.cfcuser_isLoggedIn>
	<cfoutput>
		<h3>#ucase(local.qryBucketInfo.bucketName)#</h3>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</cfoutput>
<cfelse>
	<cfsavecontent variable="local.customJS">
        <script type="text/javascript">
            function b_search() {
                $('#searchBoxError').html('').hide();
                var isEmptyForm = bk_isEmptySearchForm('frms_Search',true,true);
                if (isEmptyForm){
                    $('#searchBoxError').html('Search using at least one criteria below.').show();
                    $("form#frms_Search select").first().focus();
                    return false;
                }
                else {
                    $('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
                    return true;
                }
            }
        </script>
    </cfsavecontent>
    <cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

	<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

	<div id="searchBoxError" class="alert alert-error hide"></div>
	
	<cfif StructKeyExists(local.settingsStruct.appRightsStruct,"fsAddDocuments") and local.settingsStruct.appRightsStruct.fsAddDocuments EQ "1">
		<div class="visible-desktop pull-right" style="margin-left:10px;">
			<a href="/?#variables.thisBaseLink#&fsAction=addDocument"><img src="#application.paths.images.url#images/search/up24.png" border="0" align="absmiddle">Upload a document</a>
		</div>
		<div class="hidden-desktop hidden-phone p-10">
			<div class="pull-right">
				<a class="btn" href="/?#variables.thisBaseLink#&fsAction=addDocument"><i class="icon-upload icon-large"></i> Upload a document</a>
			</div>
			<div style="clear:both;"></div>
		</div>
    </cfif>	
    <form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
		<cfif local.settingsStruct.fileShareID NEQ "">
			<input type="hidden" name="s_applicationinstanceid"  id="s_applicationinstanceid" value="#val(local.fileShares.applicationInstanceID)#">
		</cfif>
		<cfif local.fileShares.showAuthor EQ "0">
			<input type="hidden" name="s_aut"  id="s_aut" value="">
		</cfif>
		<cfif local.fileShares.showContributedBy EQ "0">
			<input type="hidden" name="s_fname"  id="s_fname" value="">
			<input type="hidden" name="s_lname"  id="s_lname" value="">
		</cfif>

		<cfif local.settingsStruct.fileShareID EQ "">
            <div class="control-group">
                <label class="control-label" for="s_applicationinstanceid">FileShare:</label>
                <div class="controls">
                    <select id="s_applicationinstanceid" name="s_applicationinstanceid">
						<option value="0">-- All FileShares --</option>
                        <cfloop query="local.fileShares">
                            <option value="#local.fileShares.applicationInstanceID#" <cfif local.strSearchForm.s_applicationinstanceid eq local.fileShares.applicationInstanceID>selected="selected"</cfif>>
                                #local.fileShares.fileShareName#
                            </option>
                        </cfloop>
                    </select>
                </div>
            </div>
        </cfif>
		<div class="control-group">
			<label class="control-label" for="s_category">Folders:</label>
			<div class="controls">
				<select id="s_category" name="s_category">
					<option value="0">-- All Folders --</option>
				</select>
			</div>
		</div>
		<cfif local.fileShares.showAuthor EQ "1">
			<div class="control-group">
				<label class="control-label" for="s_aut">#local.fileShares.authorLabel#:</label>
				<div class="controls">
					<input type="text" name="s_aut"  id="s_aut" value="#local.strSearchForm.s_aut#" maxlength="200">
				</div>
			</div>
		</cfif>
		<cfif local.fileShares.showContributedBy EQ "1">
			<div class="control-group">
				<label class="control-label" for="s_fname">Contributor First Name:</label>
				<div class="controls">
					<input type="text" name="s_fname"  id="s_fname" value="#local.strSearchForm.s_fname#" maxlength="50">
				</div>
			</div>
			<div class="control-group">
				<label class="control-label" for="s_lname">Contributor Last Name:</label>
				<div class="controls">
					<input type="text" name="s_lname"  id="s_lname" value="#local.strSearchForm.s_lname#" maxlength="50">
				</div>
			</div>
		</cfif>
		<div class="control-group">
			<label class="control-label">Created Date between:</label>
			<div class="controls">
				<input type="text" id="s_datefrom" name="s_datefrom" value="#DateFormat(local.strSearchForm.s_datefrom, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol"> and 
				<input type="text" id="s_dateto" name="s_dateto" value="#DateFormat(local.strSearchForm.s_dateto, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol">
				<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_datefrom','s_dateto');return false;">clear dates</a>
			</div>
		</div>
		<div class="control-group" style="margin-top:30px;">
            <label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
            <div class="controls">
                <input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
            <div class="controls">
                <input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
            <div class="controls">
                <input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="s_key_x"><b>Without</b> the words:</label>
            <div class="controls">
                <input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" maxlength="200">
            </div>
        </div>
		<div class="control-group">
            <div class="controls">
                <input type="hidden" name="s_frm" id="s_frm" value="1">
                <button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
            </div>
        </div>
	</form>
	</cfoutput>
</cfif>