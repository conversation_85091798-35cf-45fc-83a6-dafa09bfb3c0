<cfsavecontent variable="javascriptIncludes">
	<cfoutput>
	<script language="javascript" src="/assets/common/javascript/getElementsByClassName.js"></script>
	<script language="JavaScript"> 
		
		function addCategory()
		{
			$.colorbox( {innerWidth:600, innerHeight:275, href:'#dataStruct.baseURL#&fsAction=addCategory&mode=direct&hideCommunityNav=1', iframe:true, overlayClose:false} );			
		}
		
		function closeBox() { $.colorbox.close(); location.reload();}
		
		function resizeBox() { $.colorbox.resize(); }
		
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(javascriptIncludes)#">

<cfset local.parentCatID = getParentCategoryID(dataStruct.parentCategoryID) />
<cfif dataStruct.qryGetSubCategories.RecordCount EQ 0 AND dataStruct.qryGetCategoryDocuments.recordcount EQ 0>
	<cfif local.parentCatID EQ "">
		<div class="ui-state-error">Warning: there are no documents available here.</div>
	<cfelse>
		<cflocation url="#dataStruct.browseLink#&catID=#local.parentCatID#&byT=#dataStruct.byTreeID#" addtoken="false">
	</cfif>
</cfif>
<cfoutput>
	<table width="100%" cellspacing="0" border="0">
		<tr valign="bottom">
			<td>
				<cfif fileShareSettings.showInteriorText EQ "1">
					<div style="padding:5px;">
						<div class="tsAppHeading">Browse #fileShareSettings.applicationInstanceName# by #dataStruct.qryCurrentCategoryTree.categoryTreeName#</div>
						<div style="margin-top:6px;" class="tsAppBodyText">Browsing the #fileShareSettings.applicationInstanceName#.</div>
					</div>
				<cfelse>
					&nbsp;
				</cfif>				
			</td>
			<td class="tsAppBodyText" style="line-height:1.3em;" width="235">
				<cfif StructKeyExists(appRightsStruct, 'fsAddDocuments') AND appRightsStruct.fsAddDocuments eq 1>
					<br/><a href="#dataStruct.uploadsURL#&from=browse<cfif dataStruct.parentCategoryID NEQ "">&catID=#dataStruct.parentCategoryID#</cfif>&byT=#dataStruct.byTreeID#"><img src="#application.paths.images.url#images/search/up24.png" border="0" align="absmiddle">Upload a document</a>
				</cfif>					
				<br/>You are <a href="#dataStruct.browseLink#&byT=#dataStruct.byTreeID#">browsing by #dataStruct.qryCurrentCategoryTree.categoryTreeName#.</a><br/>
				<cfloop query="dataStruct.qryGetCategoryTrees">
					<cfif dataStruct.qryGetCategoryTrees.categoryTreeID NEQ dataStruct.byTreeID>
					Browse by <a href="#dataStruct.browseLink#&byT=#dataStruct.qryGetCategoryTrees.categoryTreeID#">#dataStruct.qryGetCategoryTrees.categoryTreeName#</a>.<br/>
					</cfif>
				</cfloop>
			</td>
		</tr>
		<cfif StructKeyExists(appRightsStruct, 'fsAddSubFolder') AND appRightsStruct.fsAddSubFolder eq 1>
			<tr>
				<td></td>
				<td><button type="button" class="tsAppBodyButton" onClick="addCategory();"> Add Category</button></td>
			</tr>
		</cfif> 
	</table>
	
	<!--- if showing a subcategory, display the crumbtrail --->
	<cfif dataStruct.qryGetCategoryPathUp.recordcount>
		<br />
		<div class="breadcrumb">
			<div class="breadcrumb-trail">
				<a href="#dataStruct.browseLink#&byT=#dataStruct.byTreeID#" class="tsAppBodyText trail-begin">All <cfif dataStruct.byTreeID is 1>#dataStruct.qryCurrentCategoryTree.categoryTreeName#<cfelse>#dataStruct.qryCurrentCategoryTree.categoryTreeName#</cfif></a>
				<cfloop query="dataStruct.qryGetCategoryPathUp" endrow="#dataStruct.qryGetCategoryPathUp.recordcount - 1#">
					<a href="#dataStruct.browseLink#&catID=#dataStruct.qryGetCategoryPathUp.categoryID#&byT=#dataStruct.byTreeID#">#dataStruct.qryGetCategoryPathUp.CategoryName#</a>
				</cfloop>
				<cfif dataStruct.parentCategoryID gt 0>
					<span class="trail-end">#dataStruct.qryGetCategoryPathUp.CategoryName[dataStruct.qryGetCategoryPathUp.recordcount]#</span>
				</cfif>
			</div>
		</div>
		<br />
	</cfif>
	
	<!--- Category Count and display --->
	<cfif dataStruct.qryGetSubCategories.RecordCount>
		<div class="s_rhrd s_pgtop tsAppBB tsAppBT">
		<table cellpadding="0" cellspacing="0" width="100%" border="0">
			<tr bgcolor="##DEDEDE">
				<td class="tsAppBodyText">
					<b>#NumberFormat(dataStruct.numDocsInSubCats)#</b> document<cfif dataStruct.numDocsInSubCats is not 1>s</cfif> in <b>#dataStruct.qryGetSubCategories.RecordCount#</b> <cfif dataStruct.qryGetCategoryPathUp.recordcount>subc<cfelse>c</cfif>ategor<cfif dataStruct.qryGetSubCategories.RecordCount is 1>y<cfelse>ies</cfif> 
				</td>
			</tr>
		</table>
		</div>
	</cfif>

	<!--- Category table --->
	<cfif dataStruct.qryGetSubCategories.recordCount gt 0>
		<br />
		<table width="100%" cellspacing="0" cellpadding="0" border="0">
			<tr valign="top">
				<td style="width:2%;"></td>
				<td style="line-height:1.4em;" width="47%">
					<cfloop query="dataStruct.qryGetSubCategories" startrow="#dataStruct.startrow#" endRow="#dataStruct.startrow+(dataStruct.MaxPerColumn * 2)-1#">
						<div class="tsAppBodyText" style="height:20px;"><img src="#application.paths.images.url#images/search/folder_close.png" width="19" height="16" border="0" align="left" title="Category"/> <cfif dataStruct.qryGetSubCategories.categoryCount gt 0 or dataStruct.qryGetSubCategories.documentCount gt 0><a href="#dataStruct.browseLink#&catID=#dataStruct.qryGetSubCategories.categoryID#&byT=#dataStruct.byTreeID#"></cfif>#dataStruct.qryGetSubCategories.categoryName#<cfif dataStruct.qryGetSubCategories.categoryCount gt 0 OR dataStruct.qryGetSubCategories.documentCount gt 0></a></cfif> &nbsp; (#Numberformat(dataStruct.qryGetSubCategories.documentCount)# document<cfif dataStruct.qryGetSubCategories.documentCount is not 1>s</cfif>)</div>
						<cfif dataStruct.qryGetSubCategories.currentrow mod dataStruct.MaxPerColumn is 0>
							</td><td style="width:2%;"></td><td style="line-height:1.4em;" width="47%">
						</cfif>
					</cfloop>
				</td>
				<td style="width:2%;"></td>
			</tr>
		</table>
		<br />
	</cfif>	

	<!--- Form count and display --->
	<cfif dataStruct.qryGetCategoryDocuments.recordcount>
	#showSearchResultsPaging(topOrBottom='top',
							searchid=0,
							startrow=dataStruct.startrow,
							numTotalPages=dataStruct.NumTotalPages,
							NumCurrentPage=dataStruct.NumCurrentPage,
							itemCount=dataStruct.documentCount,
							itemWord='document',
							itemWordSuffix='s',
							arrSort=local.tmpSort, 
							currentSort=dataStruct.currSort,
							currentOrder=dataStruct.currOrder)#		

	<!--- results table --->
	<cfset local.objDocVersions = CreateObject("component","model.admin.common.modules.documentDetails.documentDetails") />
	<cfloop query="dataStruct.qryGetCategoryDocuments">
		<cfoutput>
		<div class="tsAppBodyText tsDocViewerDocument s_row <cfif dataStruct.qryGetCategoryDocuments.currentrow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#dataStruct.qryGetCategoryDocuments.documentID#">
			<cfset icon = dataStruct.getIcon(dataStruct.qryGetCategoryDocuments.fileExt)>
			<cfset description = dataStruct.getFileDescription(dataStruct.qryGetCategoryDocuments.fileExt)>
			<a href="/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#" target="_blank" title="Click to view document"><img src="#application.paths.images.url#images/fileExts/#icon#" width="16" height="16" border="0" align="left" /></a>&nbsp;&nbsp;<a href="/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#" target="_blank" title="Click to view document"><b>#dataStruct.qryGetCategoryDocuments.docTitle#</b></a> <span class="fsFileDescription">(#description#)</span>
			<div class="tsAppBodyText s_row <cfif dataStruct.qryGetCategoryDocuments.currentRow mod 2 is 0>s_row_alt</cfif>">
				<div class="tsAppBodyText s_dtl">
					<cfif fileShareSettings.showTags EQ "1">
						<span class="fsTagsTitle">Tags:</span> <span class="fsTags">#dataStruct.getTags(dataStruct.qryGetCategoryDocuments.docSiteResourceID, fileShareSettings.showTagsAsLinks,'','&nbsp;&nbsp;&nbsp;' )#</span><br/>
					</cfif>

					<cfloop query="fileShareSettings.customFields">
						<cfset local.columnData = dataStruct.methods.getExtraFSColumnDisplay(dataStruct.qryGetCategoryDocuments.docSiteResourceID, fileShareSettings.customFields.columnID) />
						<cfif listFind(fileShareSettings.showCustomFields,fileShareSettings.customFields.columnID) AND len(trim(local.columnData))>
							<cfif fileShareSettings.customFields.columnDesc EQ "Hot Document">
								<span class="fsHotDocument">#fileShareSettings.customFields.columnDesc#</span><br />
							<cfelse>
								<span class="fsCustomTitle">#fileShareSettings.customFields.columnDesc#:</span> <span class="fsCustom">#local.columnData#</span><br/>
							</cfif>
						</cfif>
					</cfloop>					
					
					<cfif len(trim(dataStruct.qryGetCategoryDocuments.sectionPath))>
						<span class="fsSectionPathTitle">Folder:</span> <span class="fsSectionPath">#dataStruct.qryGetCategoryDocuments.sectionPath#</span><br />
					</cfif>
					
					<cfif val(fileShareSettings.showPublicationDate) AND len(trim(dataStruct.qryGetCategoryDocuments.publicationDate))>
						<span class="fsPublicationDateTitle">Publication Date:</span> <span class="fsPublicationDate">#DateFormat(dataStruct.qryGetCategoryDocuments.publicationDate, "MM/DD/YYYY")#</span><br />
					</cfif>
					
					<cfif val(fileShareSettings.showAuthor) AND len(trim(dataStruct.qryGetCategoryDocuments.author))>
						<span class="fsAuthorTitle">#fileShareSettings.authorLabel#:</span> <span class="fsAuthor">#dataStruct.qryGetCategoryDocuments.author#</span><br />
					</cfif>
					
					<cfif val(fileShareSettings.showContributedBy) AND len(trim(dataStruct.qryGetCategoryDocuments.firstName))>
						<span class="fsContributedByTitle">Contributor:</span> <span class="fsContributedBy">#dataStruct.qryGetCategoryDocuments.firstName# #dataStruct.qryGetCategoryDocuments.lastName#</span><br />
					</cfif>
					
					<cfif val(fileShareSettings.showFirm) AND len(trim(dataStruct.qryGetCategoryDocuments.company))>
						<span class="fsFirmTitle">Firm:</span> <span class="fsFirm">#dataStruct.qryGetCategoryDocuments.company#</span><br />
					</cfif>
					
					<cfif val(fileShareSettings.showAddress)>
						<cfset local.address = getAddress(dataStruct.qryGetCategoryDocuments.contributorMemberID)>
						<cfif len(trim(local.address.address1))>
							<span class="fsAddressTitle">Address:</span> <span class="fsAddress">#local.address.address1# #local.address.address2# #local.address.city# , #local.address.code# #local.address.postalCode#</span><br />
						</cfif>
						<cfif len(trim(local.address.email))>
							<span class="fsEmailTitle">Email:</span> <A HREF="mailto:#local.address.email#">#local.address.email#</a><br />
						</cfif>
					</cfif>
					
					<cfif len(trim(dataStruct.qryGetCategoryDocuments.docDesc))>
						<span class="fsDescription">#dataStruct.qryGetCategoryDocuments.docDesc#</span><br />
					</cfif>
					
					<cfif val(fileShareSettings.showDocDownloadCountToMembers) OR application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<br /><span class="fsDownloadsTitle">Downloads:</span> <span class="fsDownloads">#downloadCount#</span><br />
					</cfif>

					<cfif fileShareSettings.showVersioning EQ "1">
						<span class="fsVersioning">
						Version(s): #local.objDocVersions.getDocumentVersions(dataStruct.qryGetCategoryDocuments.documentLanguageID).recordCount#  
						Created: #DateFormat(dataStruct.qryGetCategoryDocuments.dateCreated, "MM/DD/YYYY")# #TimeFormat(dataStruct.qryGetCategoryDocuments.dateCreated, "hh:mm tt")#  
						Revised: #DateFormat(dataStruct.qryGetCategoryDocuments.dateModified, "MM/DD/YYYY")# #TimeFormat(dataStruct.qryGetCategoryDocuments.dateModified, "hh:mm tt")# 
						</span>
					</cfif>
					
					<div id="#dataStruct.qryGetCategoryDocuments.docSiteResourceID#" style="display:none;">
						<a href="###dataStruct.qryGetCategoryDocuments.docSiteResourceID#"></a>
						<cfset local.document = local.objDocVersions.getDocumentVersions(dataStruct.qryGetCategoryDocuments.documentLanguageID) />
						<cfif local.document.recordcount gt 1>
							<table cellpadding="3" cellspacing="3" border="0" class="fsVersioning">
								<tr><th>Source</th><th>File Name</th><th>Created</th><th>Revised</th></tr>
							<cfloop query="local.document">						
								<cfif local.document.isActive EQ 0>
								<tr>
									<td>#local.document.author#</td>
									<td><a href="/docDownload/#local.document.documentID#&VID=#local.document.documentVersionID#&lang=#local.document.languageCode#" target="_blank" title="Click to view document">#local.document.filename#</a></td>
									<td>#DateFormat(local.document.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.document.dateCreated, "hh:mm tt")#  </td>
									<td>#DateFormat(local.document.dateModified, "MM/DD/YYYY")# #TimeFormat(local.document.dateModified, "hh:mm tt")# </td>
								</tr>
								</cfif>
							</cfloop>
							</table>
						</cfif>
					</div>
					
					<br/><br/>
					<div class="appIcons">
						<a href="/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#" target="_blank" title="Click to download document"><img src="#application.paths.images.url#images/JRC/save.png" width="16" height="16" border="0" align="left" /></a>
						<cfif dataStruct.qryGetCategoryDocuments.fileExt eq "pdf">
							<a href="javascript:docViewer2(#dataStruct.qryGetCategoryDocuments.documentID#,0);" title="Click to view document"><img src="#application.paths.images.url#images/JRC/view.png" width="16" height="16" border="0" align="left" /></a>
						</cfif>
						<cfif val(fileShareSettings.showShareButton)>	
							<a href="mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#dataStruct.qryGetCategoryDocuments.documentID#"><img src="#application.paths.images.url#images/JRC/share.png" width="16" height="16" border="0" align="left" title="Share with colleagues" /></a>
						</cfif>	

						<cfif canEditAny or (canEditOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetCategoryDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
							<a href="#dataStruct.editURL#&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#" title="Edit document"><img src="#application.paths.images.url#images/JRC/edit.png" width="16" height="16" border="0" align="left" /></a>
						</cfif>
						<cfif #CreateObject("component","model.admin.common.modules.documentDetails.documentDetails").getDocumentVersions(dataStruct.qryGetCategoryDocuments.documentLanguageID).recordCount# GT "1">
							<a href="##" onClick="javascript:togglePriorVersions(#dataStruct.qryGetCategoryDocuments.docSiteResourceID#); return false;"><img src="#application.paths.images.url#images/JRC/history.png" width="16" height="16" border="0" align="left" title="View prior versions" /></a>
						</cfif>		
						<a href="#dataStruct.baseURL#&from=browse&panel=sendNote<cfif dataStruct.parentCategoryID NEQ "">&catID=#dataStruct.parentCategoryID#&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#</cfif>&byT=#dataStruct.byTreeID#"><img src="#application.paths.images.url#images/JRC/notify.png" width="16" height="16" border="0" align="left" title="Notify Admin"/></a>
					</div>
					<div style="float:right" class="appIcons">
					<cfif canDeleteAny or (canDeleteOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetCategoryDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
						<a href="##" onClick="javascript:copyDocument('/?event=cms.showResource&resID=#fileShareSettings.siteResourceID#&#baseQueryString#&fsAction=copyDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#'); return false;" title="Copy Document"><img src="#application.paths.images.url#images/grid/folder_add.png" width="16" height="16" border="0" align="left" /></a>
						<a href="##" onClick="javascript:moveDocument('/?event=cms.showResource&resID=#fileShareSettings.siteResourceID#&#baseQueryString#&fsAction=moveDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#'); return false;" title="Move Document"><img src="#application.paths.images.url#images/grid/folder_go.png" width="16" height="16" border="0" align="left" /></a>
					</cfif>
					<cfif canDeleteAny or (canDeleteOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetCategoryDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
						<a href="##" onClick="javascript:confirmDelete('/?#baseQueryString#&fsAction=deleteDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#'); return false;" title="Delete document"><img src="#application.paths.images.url#images/JRC/delete.PNG" width="16" height="16" border="0" align="left" /></a>
					</cfif>
					<cfif canDeleteAny>
						<cfif #dataStruct.qryGetCategoryDocuments.siteResourceStatusDesc# EQ "Active">
							<a href="##" onClick="javascript:confirmHide('/?#baseQueryString#&fsAction=hideDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#'); return false;"><img src="#application.paths.images.url#images/JRC/hide.png" width="16" height="16" border="0" align="left" title="Hide document"/>#dataStruct.qryGetCategoryDocuments.siteResourceStatusDesc#</a>
						<cfelse>
							<a href="##" onClick="javascript:confirmUnhide('/?#baseQueryString#&fsAction=unhideDocument&fsDocumentID=#dataStruct.qryGetCategoryDocuments.documentID#'); return false;"><img src="#application.paths.images.url#images/JRC/unhide.png" width="16" height="16" border="0" align="left" title="Unhide document"/>#dataStruct.qryGetCategoryDocuments.siteResourceStatusDesc#</a>
						</cfif>
					</cfif>
					
					</div>
					<br/>					
				</div>
			</div>			
		</div>
		</cfoutput>
	</cfloop>
	<cfif dataStruct.numTotalPages GT 1>
		#showSearchResultsPaging(topOrBottom='bottom',
								searchid=0,
								startrow=dataStruct.startrow,
								numTotalPages=dataStruct.NumTotalPages,
								NumCurrentPage=dataStruct.NumCurrentPage,
								itemCount=dataStruct.documentCount,
								itemWord='document',
								itemWordSuffix='s',
								arrSort=local.tmpSort, 
								currentSort=dataStruct.currSort,
								currentOrder=dataStruct.currOrder)#	
	</cfif>
	
	</cfif>	
	
</cfoutput>


<cffunction name="showSearchResultsPaging" access="public" output="yes" returntype="void">
	<cfargument name="topOrBottom" required="yes" type="string">
	<cfargument name="searchid" required="yes" type="numeric">
	<cfargument name="startrow" required="yes" type="numeric">
	<cfargument name="numTotalPages" required="yes" type="numeric">
	<cfargument name="NumCurrentPage" required="yes" type="numeric">
	<cfargument name="itemCount" required="yes" type="numeric">
	<cfargument name="itemWord" required="yes" type="string">
	<cfargument name="itemWordSuffix" required="yes" type="string">
	<cfargument name="arrSort" required="no" type="array" default="#arrayNew(1)#">
	<cfargument name="currentSort" required="no" type="string" default="">
	<cfargument name="currentOrder" required="no" type="string" default="">
	<cfargument name="filter" required="no" type="string" default="">
	
	<cfoutput>
		<cfif arguments.topOrBottom eq "bottom"><br /></cfif>
		<div class="<cfif arguments.topOrBottom eq "bottom">s_rhrd s_pgbtm tsAppBT<cfelse>s_rhrd s_pgtop tsAppBB tsAppBT</cfif>">		
			<cfif arguments.numTotalPages gt 1>
				<span <cfif arguments.topOrBottom eq "top">style="float:right;"</cfif>>
					<cfif arguments.NumCurrentPage gt 1>
						<a href="#dataStruct.browseLink#&catID=#dataStruct.parentCategoryID#&byT=#dataStruct.byTreeID#&startRow=#arguments.startrow - dataStruct.MaxPerPage#&currSort=#arguments.currentSort#&currOrder=#arguments.currentOrder#&filter=#arguments.filter#">Previous</a> &##149;
					</cfif>
					Page #arguments.NumCurrentPage# of #numberformat(arguments.NumTotalPages)# 
					<cfif arguments.NumCurrentPage lt arguments.NumTotalPages> 
						&##149; <a href="#dataStruct.browseLink#&catID=#dataStruct.parentCategoryID#&byT=#dataStruct.byTreeID#&startRow=#arguments.startrow + dataStruct.MaxPerPage#&currSort=#arguments.currentSort#&currOrder=#arguments.currentOrder#&filter=#arguments.filter#">Next</a>
					</cfif>
				</span>
			</cfif>
			<cfif arguments.topOrBottom eq "top"><b>#numberformat(arguments.itemCount)#</b> #arguments.itemword#<cfif arguments.itemCount is not 1>#arguments.itemwordSuffix#</cfif> found</cfif>
			<cfif ArrayLen(arguments.arrSort) and arguments.topOrBottom eq "top">
				<br/>Sort by:
				<cfloop from="1" to="#arraylen(arguments.arrSort)#" index="local.arrEl">
					<cfset local.icon = "">
					<cfif listLen(arguments.arrSort[local.arrEl],"|") gt 2 and len(trim(arguments.currentSort)) and arguments.currentSort eq getToken(arguments.arrSort[local.arrEl],1,"|")> 
						<cfif getToken(arguments.arrSort[local.arrEl],3,"|") eq "ASC">
							<cfset local.icon = "<i class='icon-arrow-down' style='margin-bottom:-3px;' title='Click to sort in descending order'></i>">
						<cfelse>
							<cfset local.icon = "<i class='icon-arrow-up' style='margin-bottom:-3px;' title='Click to sort in ascending order'></i>">
						</cfif>
					</cfif>					
					<a href="#dataStruct.browseLink#&catID=#dataStruct.parentCategoryID#&byT=#dataStruct.byTreeID#&startRow=1&sortType=#GetToken(arguments.arrSort[local.arrEl],1,'|')#&sortOrder=#GetToken(arguments.arrSort[local.arrEl],3,'|')#&filter=#arguments.filter#" <cfif GetToken(arguments.arrSort[local.arrEl],1,"|") eq arguments.currentSort>class="currSort "</cfif>>#GetToken(arguments.arrSort[local.arrEl],2,"|")# #local.icon#</a><cfif local.arrEl lt arraylen(arguments.arrSort)> &bull; </cfif>
				</cfloop>
			</cfif>			
		</div>
	</cfoutput>
</cffunction>

<cffunction name="getParentCategoryID" access="public" output="yes" returntype="string">
	<cfargument name="categoryID" required="yes" type="string">
	
	<cfset local.tag = "">
	
	<cfquery name="local.qryParent" datasource="#application.dsn.membercentral.dsn#">
		SELECT c.parentCategoryID
		FROM dbo.cms_categories AS c 
		WHERE c.categoryID = <cfqueryparam value="#arguments.categoryID#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	
	<cfreturn local.qryParent.parentCategoryID>
</cffunction>

<cffunction name="getAddress" access="public" output="yes" retuntype="string">
	<cfargument name="memberid" required="yes" type="string">
	
	<cfquery name="local.qyrAdd" datasource="#application.dsn.membercentral.dsn#">
		select email, address1, address2, city, code, postalcode
		from dbo.ams_members m
		inner join dbo.ams_memberEmailTypes t on t.orgID = m.orgID
		inner join dbo.ams_memberEmails e on e.memberID = m.memberID
		inner join dbo.ams_memberAddresses a on a.memberid = m.memberid
		inner join dbo.ams_states s on s.stateID = a.stateID
		where e.memberid =  <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER"> 

	</cfquery>
	
	<cfreturn local.qyrAdd>
</cffunction>