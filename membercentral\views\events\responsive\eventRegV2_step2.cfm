<cfinclude template="../commonEventRegStep2JS.cfm">

<cfoutput>
<div class="evreg-card evreg-mt-3 evreg-p-3">
	<cfif local.qryEventRegRates.recordCount>
		<form name="frmEventRegStep2" id="frmEventRegStep2" autocomplete="off">
			<input type="hidden" name="registrantName" id="registrantName" value="#encodeForHTMLAttribute(local.qryCurrentRegMember.firstname & ' ' & local.qryCurrentRegMember.lastname)#">

			<cfif local.registrantID GT 0>
				<div id="evSelectedRegRate">
					<div class="evreg-d-flex">
						<div class="evreg-col">
							<div id="evSelectedRegRateName" class="evreg-font-size-lg evreg-font-weight-bold evRegRateName">#local.evRegV2.currentReg.s2.rateName#</div>
							<div id="evSelectedRegRateMessage" class="evreg-text-dim evRegRateMessage">#local.evRegV2.currentReg.s2.rateMessage#</div>
						</div>
						<div id="evSelectedRegRatePrice" class="evreg-ml-auto evreg-font-size-lg evreg-font-weight-bold">#local.evRegV2.currentReg.s2.ratePriceDisplay#</div>
					</div>
				</div>
			<cfelse>
				<div id="evRegRatesContainer"<cfif local.rateID GT 0> style="display:none;"</cfif>>
					<div class="evreg-mb-3 evreg-font-size-lg evreg-text-dim">Select your event rate:</div>
					<cfif local.qryEventRegRates.recordcount GT 1>
						<cfoutput query="local.qryEventRegRates" group="rateGroupingOrder">
							<cfif len(local.qryEventRegRates.rateGrouping)>
								<div class="evreg-mb-3">
									<span class="evreg-font-weight-bold">#encodeForHTML(local.qryEventRegRates.rateGrouping)#</span>
									<div class="evreg-pl-3 evreg-mt-2">
							</cfif>
							<cfoutput group="rateID">
								<cfset local.thisRegRatePriceDisp = "#local.qryEventRegRates.rate IS 0 ? local.qryEventRegRates.freeRateDisplay : dollarformat(local.qryEventRegRates.rate)##local.displayedCurrencyType#">
								<div class="evreg-d-flex evreg-mb-2">
									<input type="radio" name="ev_rateID" id="ev_rateID#local.qryEventRegRates.rateID#" class="evreg-align-self-start evreg-mr-1" data-regratepricedisp="#encodeForHTMLAttribute(local.thisRegRatePriceDisp)#" value="#local.qryEventRegRates.rateID#" onclick="saveRegRate();"<cfif local.rateID EQ local.qryEventRegRates.rateID> checked</cfif>>
									<div class="evreg-col">
										<label for="ev_rateID#local.qryEventRegRates.rateID#" class="evreg-d-inline-block evreg-mb-1 evRegRateName">#encodeForHTML(local.qryEventRegRates.rateName)#</label>
										<cfif len(local.qryEventRegRates.rateMessage)>
											<div id="ev_rateMsg#local.qryEventRegRates.rateID#" class="evreg-text-dim evRegRateMessage"<cfif NOT local.qryEventRegRates.rateMessageDisplay> style="display:none;"</cfif>>#encodeForHTML(local.qryEventRegRates.rateMessage)#</div>
										</cfif>
									</div>
									<div class="evreg-ml-auto evreg-font-size-lg evreg-font-weight-bold">#local.thisRegRatePriceDisp#</div>
								</div>
							</cfoutput>
							<cfif len(local.qryEventRegRates.rateGrouping)>
									</div>
								</div>
							</cfif>
						</cfoutput>
					<cfelse>
						<cfset local.thisRegRatePriceDisp = "#local.qryEventRegRates.rate IS 0 ? local.qryEventRegRates.freeRateDisplay : dollarformat(local.qryEventRegRates.rate)##local.displayedCurrencyType#">
						<div class="evreg-d-flex evreg-mb-3">
							<input type="radio" name="ev_rateID" id="ev_rateID#local.qryEventRegRates.rateID#" class="evreg-align-self-start evreg-mr-1" data-regratepricedisp="#encodeForHTMLAttribute(local.thisRegRatePriceDisp)#" value="#local.qryEventRegRates.rateID#" checked>
							<div class="evreg-col">
								<label for="ev_rateID#local.qryEventRegRates.rateID#" class="evreg-d-inline-block evreg-mb-1 evRegRateName">#encodeForHTML(local.qryEventRegRates.rateName)#</label>
								<cfif len(local.qryEventRegRates.rateMessage)>
									<div id="ev_rateMsg#local.qryEventRegRates.rateID#" class="evreg-text-dim evRegRateMessage">#encodeForHTML(local.qryEventRegRates.rateMessage)#</div>
								</cfif>
							</div>
							<div class="evreg-ml-auto evreg-font-size-lg evreg-font-weight-bold">#local.thisRegRatePriceDisp#</div>
						</div>
						<div class="evreg-text-right">
							<button type="button" name="btnContinueRateSelection" id="btnContinueRateSelection" class="btn btn-primary" onclick="saveRegRate();">Continue</button>
						</div>
					</cfif>
				</div>
				<div id="evSelectedRegRate"<cfif local.qryEventRegRates.recordcount GT 1> class="evRegStepSummary evreg-cursor-pointer" data-evregsummarystep="2"</cfif><cfif NOT local.rateID> style="display:none;"</cfif>>
					<div class="evreg-d-flex">
						<cfif local.qryEventRegRates.recordcount GT 1>
							<a href="##" class="evreg-align-self-center evreg-mr-2 evreg-font-size-lg evreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
						</cfif>
						<div class="evreg-col">
							<div id="evSelectedRegRateName" class="evreg-font-size-lg evreg-font-weight-bold evRegRateName">#local.evRegV2.currentReg.s2.rateName#</div>
							<div id="evSelectedRegRateMessage" class="evreg-text-dim evRegRateMessage">#local.evRegV2.currentReg.s2.rateMessage#</div>
						</div>
						<div id="evSelectedRegRatePrice" class="evreg-ml-auto evreg-font-size-lg evreg-font-weight-bold">#local.evRegV2.currentReg.s2.ratePriceDisplay#</div>
					</div>
				</div>
			</cfif>
			<div id="evRegRateSaveLoading" style="display:none;"></div>
		</form>
	<cfelse>
		<div class="alert alert-warning"><b>This registrant does not qualify for any pricing options established for this event.</b></div>
	</cfif>
</div>
</cfoutput>