<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div style="padding-bottom:10px;">
	<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
</div>
</cfoutput>
<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		<div class="s_rnfne">#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName)#</div>
	</cfoutput>
<cfelse>
	<cfset local.tmpSort = ArrayNew(1)>
	<cfset ArrayAppend(local.tmpSort,"rank|Relevance")>
	<cfset ArrayAppend(local.tmpSort,"date|Date")>
	<cfset ArrayAppend(local.tmpSort,"case|Case")>
	<cfset ArrayAppend(local.tmpSort,"witness|Witness")>

	<cfoutput>
	#showSearchResultsPaging('top',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'document','s',local.tmpSort,arguments.sortType)#
	</cfoutput>
	<div style="clear:both;"></div>
	<div id="searchSponsorRightSideContainer"></div>

	<!--- results table --->
	<cfloop query="local.qryResults">
		<cfset local.outerCurrentRow = local.qryResults.currentrow>
		<cfif local.qryResults.inCart>
			<cfset local.inCartStyle = "" />
			<cfset local.addToCartStyle = "display:none;" />
		<cfelse>
			<cfset local.inCartStyle = "display:none;" />
			<cfset local.addToCartStyle = "" />
		</cfif>
		<cfoutput>
		<div class="tsAppBodyText tsDocViewerDocument s_row <cfif local.outerCurrentRow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.qryResults.mcRecordID#">
			<div class="s_act">
				<div class="s_opt" style="#local.inCartStyle#" id="s_incart#local.qryResults.mcRecordID#">
					<a href="javascript:viewCart();" title="View your cart/checkout"><img src="#application.paths.images.url#images/search/cart.png" width="16" height="16" align="left" border="0" />In Cart</a>
				</div>
				<cfif not local.qryResults.owned and variables.cfcuser_DisplayVersion gt 1 >
					<div class="s_opt" style="#local.addToCartStyle#" id="s_addtocart#local.qryResults.mcRecordID#">
						<a href="javascript:oneClickPurchaseDTReport(#local.qryResults.mcRecordID#,#arguments.searchid#);" title="Buy Actions"><img src="#application.paths.images.url#images/search/page_white_put.png" width="16" height="16" align="left" border="0">View Report</a>
					</div>
				<cfelseif local.qryResults.owned>
					<div class="s_opt"><a href="javascript:viewDTReport(#local.qryResults.owned#);" title="View Report"><img src="#application.paths.images.url#images/search/page_white_put.png" width="16" height="16" align="left" border="0" />View Report</a></div>
				</cfif>
			</div>

			<div>
				<img src="#application.paths.images.url#images/search/page.png" width="16" height="16" border="0" align="left" alt="[Document]">
				<b><cfif len(trim(expertname))>#trim(expertname)#<cfelse>Unknown Expert</cfif></b>
			</div>
			<div class="s_dtl">
				<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned>
					Document ###local.qryResults.mcRecordID# - #DateFormat(local.qryResults.OpinionDate, "mmm d, yyyy")# - #local.qryResults.jurisdiction#<br/>
					#local.qryResults.casecaption#<br/>
					Disposition: Testimony challenged; not ruled upon <cfif len(local.qryResults.areaOfLaw)>(#local.qryResults.areaOfLaw#)</cfif><br/>
				<cfelse>
					<a href="/?pg=upgradeTrialSmith">Hidden - Click here to upgrade your TrialSmith Plan and view ALL Daubert Tracker reports</a>
				</cfif>
			</div>
		</div>
		</cfoutput>
	</cfloop>
	<cfoutput>
	<br clear="all"/>
	#showSearchResultsPaging('bottom',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'document','s',local.tmpSort,arguments.sortType)#
	</cfoutput>
</cfif>