<cfsavecontent variable="local.js">
	<script type="text/javascript">
		function b_search() {
			$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
			return true;
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.js,'\s{2,}',' ','ALL')#">

<cfoutput>
#showHeader(settings=local.qryBucketInfo, viewDirectory=arguments.viewDirectory)#

<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal">
	<div class="control-group">
		<label class="control-label" for="s_cat">Issues:</label>
		<div class="controls">
			<select name="s_cat"  id="s_cat">
				<option value="">All Years</option>
				<cfloop query="local.qryIssues">
					<option value="#local.qryIssues.issueID#" <cfif local.qryIssues.issueID eq local.strSearchForm.s_cat>selected="selected"</cfif>>#local.qryIssues.IssueYear# - #getMonthAsString(local.qryIssues.IssueMonth)#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_category">Categories:</label>
		<div class="controls">
			<select name="s_category"  id="s_category">
				<option value="">All Categories</option>
				<cfloop query="local.qryCategories">
					<option value="#local.qryCategories.categoryID#" <cfif local.qryCategories.categoryID eq local.strSearchForm.s_category>selected="selected"</cfif>>#local.qryCategories.categoryName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_aut">Authors: <AUTHORS>
		<div class="controls">
			<select name="s_aut"  id="s_aut" multiple="true">
				<option value="">All Authors</option>
				<cfloop query="local.qryAuthors">
					<option value="#local.qryAuthors.authorID#" <cfif listFindNoCase(local.strSearchForm.s_aut,local.qryAuthors.authorID)>selected="selected"</cfif>>#local.qryAuthors.authorName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>any</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>