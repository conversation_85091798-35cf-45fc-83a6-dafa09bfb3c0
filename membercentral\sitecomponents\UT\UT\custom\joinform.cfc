<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Add step 1 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="Step2TopContent", type="CONTENTOBJ", desc="Content at top of page 2", value="Add step 2 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DuesCheckoffContent", type="CONTENTOBJ", desc="Content at top of Dues Checkoff Section", value="Add Dues Checkoff content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for CC", value="UAJCC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you. You have successfully submitted your application for membership with Utah Association for Justice. You will be contacted by UAJ after your application has been reviewed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="UAJ Membership Submission Confirmation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application Submitted" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MembershipTypeInfo", type="STRING", desc="membership type Instruction Text", value="Please select the membership rate desired below" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="DuesCheckoffInfo", type="STRING", desc="dues checkoff Instruction Text", value="Support the UAJ's Grassroots Legislative Program" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="UAJCC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="PaymentProfileCodeCheck", type="STRING", desc="pay profile code for Check", value="PaybyCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you.  Your Membership application has been submitted for review. You will receive an email from UTAH Association for Justice with the information you provided." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ActiveAcceptedMessage", type="CONTENTOBJ", desc="Active or Accepted subscription exists message", value="UAJ records indicate that you are currently a UAJ member. Please <a href='/?pg=login'>click here</a> to login." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="contentMembershipInfo", type="CONTENTOBJ", desc="Content above Membership Information", value="Please complete/verify the following personal information. Be sure to fill in any blanks and make any updates necessary.You may also download our Membership Application and mail it with your payment to Utah Association for Justice, 645 South 200 East, Salt Lake City, UT 84111. This is required if you wish to pay for membership by check.Once your application is received and processed, you will receive a confirmation mail. At that time, you will be able to set up your login information on utahassociationforjustice.org and complete your unique profile." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="subscriptionUID",type="STRING",desc="UID of the Root Subscription that this form offers",value="ce7c1b87-476e-4376-8307-a4aab12e2600" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Membership Application',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form.";

		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;	
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processPayment(rc=arguments.event.getCollection())) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {}
				function validateMembershipInfoForm(){
					var arrReq = new Array();					
					if (!$('input.subRateCheckbox').is(':checked')) { 
						arrReq[arrReq.length] = "Choose a rate.";
					}
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm() {
					if(typeof mccf_validatePPForm == 'function') {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.membershipDuesUID = "abfdadae-4c59-4d7b-a786-10c303ac033c">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.membershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.membershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.stReturn = "success">
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='913b8a42-8370-40df-8e43-a420ec3026e2', mode="collection", strData=local.strData)>
		<!--- Business Address Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='dc149797-d4f9-40ec-ad91-4bde7db12ca0', mode="collection", strData=local.strData)>
		<!--- Home Address Information --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='6006b217-d0b9-40e1-b7ab-f9dd0fb624f2', mode="collection", strData=local.strData)>
		<!--- Address Preferences --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='cde984af-7744-4f0d-a2d5-7bb97b859cec', mode="collection", strData=local.strData)>			
		<!--- Demographic Information --->
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='876A5BDC-82C9-4022-9B28-96A5A3D020C0', mode="collection", strData=local.strData)>
		<!--- get  Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.strFieldSetContent4.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent4.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.strFieldSetContent4.strFields[local.thisField] eq "Use this address for Mailing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>
		
		<!--- get Membership Category --->
		<cfset local.memberCatNum = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Membership Category">
				<cfset local.memberCatNum = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.utahLicenseNumber = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Utah License Number">
				<cfset local.utahLicenseNumber = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.utahAdmissionDate = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Admission Date for Utah Bar">
				<cfset local.utahAdmissionDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.utahStatus = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Utah Status">
				<cfset local.utahStatus = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.otherStateLicenseNumber = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Other State License Number">
				<cfset local.otherStateLicenseNumber = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.otherStateAdmissionDate = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Admission Date for Other State Bar">
				<cfset local.otherStateAdmissionDate = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.otherStateStatus = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Other State Status">
				<cfset local.otherStateStatus = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.workEmail = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Work Email">
				<cfset local.workEmail = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				table tr td:nth-child(2) {
					white-space: normal;
				}
				td input[type='text'] {
					height: 30px;
				}
			</style>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					#local.strFieldSetContent1.jsValidation#
					#local.strFieldSetContent5.jsValidation#
					#local.strFieldSetContent2.jsValidation#
					#local.strFieldSetContent3.jsValidation#
					#local.strFieldSetContent4.jsValidation#

					<cfif len(trim(local.memberCatNum))>
						var mcSel = $('###variables.formName# ###local.memberCatNum# option:selected').text();
						if (mcSel == 'Attorney') {
							<cfif isDefined("local.utahLicenseNumber") and len(trim(local.utahLicenseNumber))>
								if ($.trim($('###variables.formName# ###local.utahLicenseNumber#').val()) == '') {
									<cfif isDefined("local.otherStateLicenseNumber") and len(trim(local.otherStateLicenseNumber))>
										if ($.trim($('###variables.formName# ###local.otherStateLicenseNumber#').val()) == '') {
											arrReq[arrReq.length] = "Utah License Number and Admission Date to Utah Bar OR Other State License Number and Admission Date to Other State are required for Attorney membership.";
										}
										else {
											<cfif isDefined("local.otherStateAdmissionDate") and len(trim(local.otherStateAdmissionDate))>
												if ($.trim($('###variables.formName# ###local.otherStateAdmissionDate#').val()) == '') 
												arrReq[arrReq.length] = "Admission Date for Other State Bar is required for Attorney membership.";
											</cfif>
											<cfif isDefined("local.otherStateStatus") and len(trim(local.otherStateStatus))>
												if ($.trim($('###variables.formName# ###local.otherStateStatus#').val()) == '') 
												arrReq[arrReq.length] = "Other State Status is required for Attorney membership.";
											</cfif>
										}
									</cfif>
								}
								else {
									<cfif isDefined("local.utahAdmissionDate") and len(trim(local.utahAdmissionDate))>
										if ($.trim($('###variables.formName# ###local.utahAdmissionDate#').val()) == '') 
										arrReq[arrReq.length] = "Admission Date to Utah Bar is required for Attorney membership.";
									</cfif>
									<cfif isDefined("local.utahStatus") and len(trim(local.utahStatus))>
										if ($.trim($('###variables.formName# ###local.utahStatus#').val()) == '') 
										arrReq[arrReq.length] = "Utah Status is required for Attorney membership.";
									</cfif>
								}
							</cfif>						
						}					
					</cfif>

					<cfif len(trim(local.workEmail))>
						var workEmail = $('###variables.formName# ###local.workEmail#').val();
						$('##workEmail').val(workEmail);
					</cfif>		
						
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<cfif len(variables.strPageFields.contentMembershipInfo)>
				<div>#variables.strPageFields.contentMembershipInfo#</div>
				<br/>
			</cfif>	

			<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent1.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent5.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent5.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent2.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent3.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent4.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent4.fieldSetContent#
			</div>
			<input type="hidden" id='workEmail' name="workEmail" value=''>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

			<script language="javascript">	
				
				$(document).ready(function(){
					
					$('*[id^=mat_]').css('min-width', '140px');
				
					<cfloop query="local.qryOrgAddressTypes">
						addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
						function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
							var _address = _this.val();
							
							if(_address.length >0){
								if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
									$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
								}
							} else {
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							}
						}
						
						$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
						});
					</cfloop>
				});

			
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>	

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	
				
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			
			<style type="text/css">
				.membershipType {padding-top: 10px;padding-bottom: 5px;}
				.memberType {padding: 5px;padding-left: 10%;}
			</style>
			
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfif StructKeyExists(arguments.rc,'workEmail')>
				<cfinput type="hidden" name="workEmail" value="#arguments.rc.workEmail#">
			</cfif>
			
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#
						<br/><br/>				
					</div>
				</div>
			</div> 

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
				
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
				
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		
		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID,
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<style type="text/css">
				##selectedRates tr.even {background-color: ##dddddd;}
				##selectedRates table {width:100%;border-spacing:0px;}	
				##selectedRates td,##selectedRates th{padding:5px;}
				##selectedRates tr td:first-child, ##selectedRates tr th:first-child {border-right:1px solid;width: 80%;}
				##selectedRates div.table-container {margin-top:10px;}
				##selectedRates span.alignRight {float: right;margin-right: 66px;}
			</style>
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			
			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
				<br/>
			</div>
			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				#dollarFormat(local.strResult.totalFullPrice)#
			</div>
			
			<cfif StructKeyExists(arguments.rc,'workEmail')>
				<input type="hidden" name="workEmail" value='#arguments.rc.workEmail#' />
			</cfif>
			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=variables.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>	

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
				
		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='913b8a42-8370-40df-8e43-a420ec3026e2', mode="confirmation", strData=arguments.rc)>
		<!--- Business Address Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='dc149797-d4f9-40ec-ad91-4bde7db12ca0', mode="confirmation", strData=arguments.rc)>
		<!--- Home Address Information --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='6006b217-d0b9-40e1-b7ab-f9dd0fb624f2', mode="confirmation", strData=arguments.rc)>
		<!--- Address Preferences --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='cde984af-7744-4f0d-a2d5-7bb97b859cec', mode="confirmation", strData=arguments.rc)>
		<!--- Member Information --->
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='876A5BDC-82C9-4022-9B28-96A5A3D020C0', mode="confirmation", strData=arguments.rc)>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent  variable="local.submissionDetails">
			<cfoutput>
				<div class="confirmationDetails">
					<!--@@specialcontent@@-->
	
					<div>Here are the details of your application:</div><br/>
					
					#local.strFieldSetContent1.fieldSetContent#
					#local.strFieldSetContent5.fieldSetContent#
					#local.strFieldSetContent2.fieldSetContent#
					#local.strFieldSetContent3.fieldSetContent#
					#local.strFieldSetContent4.fieldSetContent#
					
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join UAJ - Membership Selections</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<div>#local.strResult.formContent#</div>
								<br/>
								<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
								<br/>					
							</td>
						</tr>
					</table>
					<br/>
					
					<cfif local.paymentRequired>
						<br/>
						<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
									<table cellpadding="3" border="0" cellspacing="0">
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
										</td>
									</tr>
									</table>
								<cfelse>
									None selected.
								</cfif>
							</td>
						</tr>
						</table>
					</cfif>	
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.confirmationPageHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<fieldset>
						<legend>Form submission</legend>
						<div class="tsAppSectionContentContainer">#variables.strPageFields.ConfirmationMessage#</div>
						#local.submissionDetails#
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationMessage)>
				<div>#variables.strPageFields.ConfirmationMessage#</div>
			</cfif>
			#local.submissionDetails#
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfif StructKeyExists(arguments.rc,'workEmail')>
				<cfset variables.memberEmail.TO = arguments.rc.workEmail>
			</cfif>	
		</cfif>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.emailSentToStaff = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to UAJ", emailContent=local.confirmationHTMLToStaff)>

		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
					
				</style>
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF)>

		<cfreturn local.confirmationPageHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound" or arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ActiveAcceptedMessage#
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="storeMembershipApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
					parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', 
					docDesc='Membership Application Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
					 contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>
</cfcomponent>