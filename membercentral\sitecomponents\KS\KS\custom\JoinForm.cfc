<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		
		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CCPayProfileCode", type="STRING", desc="pay profile code for CC", value="AUTHCIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="CheckPayProfileCode", type="STRING", desc="pay profile code for Check", value="PaybyCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="errActiveFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="KTLA records indicate you are currently an KTLA member. <a href='/?pg=manageSubscriptions'>Click here</a> to continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="errAcceptedFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="KTLA records indicate you are currently an KTLA member. <a href='/?pg=manageSubscriptions'>Click here</a> to continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="errBilledFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="You need to renew your KTLA membership. <a href='/?pg=manageSubscriptions'>Click here</a> to continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (518) 445-7691 or email <a href='mailto:<EMAIL>'><EMAIL></a>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="contentMembershipInfo", type="CONTENTOBJ", desc="Content above Membership Information", value="Fields marked with a * are required." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMembershipCategory", type="CONTENTOBJ", desc="Content in Membership Category", value="Pricing for different membership categories" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMembershipType", type="CONTENTOBJ", desc="Content in Membership Type", value="CONTENT HERE" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMemberBenefits", type="CONTENTOBJ", desc="Content in Member Benefits", value="You will receive additional information about all the benefits of membership in KTLA. Please indicate your interest in the following benefits:" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentAdditionalInfoTax", type="CONTENTOBJ", desc="Tax Content in Additional Info", value="Dues to KTLA are not deductible as charitable contributions for federal income tax purposes, but may be deductible as ordinary and necessary business expenses. Please consult your tax advisor." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentAdditionalInfoVerify", type="CONTENTOBJ", desc="Verify Content in Additional Info", value="I verify that all information contained in this membership application is current. By providing my mailing address, email address, telephone and fax number, I agree to receive communications sent by or on behalf of the Kansas Trial Lawyers Association, Legacy of Justice Foundation and KTLA Consumer/Civil Justice PAC via mail, email, telephone or fax." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentConfirmation", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you. Your membership application has been submitted for review. You will receive an email from Kansas Trial Lawyers Association once your application has been processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = {
			name="membershipCategory",
			type="STRING",
			desc="Membership Category",
			value="Join KTLA - Membership Category"
		};
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = {
			name="membershipType",
			type="STRING",
			desc="Membership Type",
			value="Join KTLA - Membership Type"
		};
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = {
			name="memberBenefits",
			type="STRING",
			desc="Member Benefits",
			value="Join KTLA - Member Benefits"
		};
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = {
			name="additionalInformation",
			type="STRING",
			desc="Additional Information",
			value="Join KTLA - Additional Information"
		};
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = {
			name="Confirmation",
			type="STRING",
			desc="Confirmation",
			value="Join KTLA - Confirmation"
		};
		arrayAppend(local.arrCustomFields, local.tmpField);

		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Join KTLA',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('historyID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('historyID')));
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Mbrapp', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Mbrapp', subName='Completed');
		variables.historyStartedText = "Member started join form.";
		variables.historyCompletedText = "Member completed join form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo();
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processPayment(rc=arguments.event.getCollection())) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				.ui-multiselect-checkboxes label input{
					top: 2px !important;
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
					var mcSel = $('###variables.formName# ##mccf_memberCategory').val();
				}
				function validateMembershipInfoForm(){
					var arrReq = new Array();
					if (!$('###variables.formName# ##mccf_RFID').is(':checked')) arrReq[arrReq.length] = "Make a membership type selection.";
					if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES') {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
											
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = "16203ed6-0dde-4cb2-8ffc-eeb0c631e2c6">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>

		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>			
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>	

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='becde174-f2d6-4101-972d-7bcb732c9250', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='66dfb740-ff1e-43d0-ad96-b7d0cae7472d', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='1962a737-7aa4-4979-a4a8-67732960ded9', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='597ff269-9486-46e0-a763-3c1c2d261790', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='8c96b8a6-9cef-4cd6-ab47-d9b2e84d6b62', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='f7d8d434-82c1-4b49-80c2-e7aeab511436', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='6b84c201-5550-4f0d-b11b-d92e9f2ecc04', mode="collection", strData=local.strData)>

		<!--- get Kansas Bar Num, Admission Date --->
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Kansas Bar No.">
				<cfset local.ksBarNum = local.thisField>
			<cfelseif local.strFieldSetContent1.strFields[local.thisField] eq "Admission Date for Kansas Bar">
				<cfset local.ksBarDate = local.thisField>
			<cfelseif local.strFieldSetContent1.strFields[local.thisField] eq "Law School">
				<cfset local.ksLawSchool = local.thisField>
			<cfelseif local.strFieldSetContent1.strFields[local.thisField] eq "Graduation Date">
				<cfset local.ksGradDate = local.thisField>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#
					#local.strFieldSetContent2.jsValidation#
					#local.strFieldSetContent3.jsValidation#
					#local.strFieldSetContent4.jsValidation#
					#local.strFieldSetContent5.jsValidation#
					#local.strFieldSetContent6.jsValidation#
					#local.strFieldSetContent7.jsValidation#

					var mcSel = $('###variables.formName# ##mccf_memberCategory').val();
					if (mcSel == '') arrReq[arrReq.length] = "Membership Category is required.";
					else if (mcSel == 'Attorney' || mcSel == 'Defense Attorney' || mcSel == 'Government Attorney') {
						<cfif isDefined("local.ksBarNum")>
							if ($.trim($('###variables.formName# ###local.ksBarNum#').val()) == '') arrReq[arrReq.length] = "Kansas Bar No. is required.";
						</cfif>
						<cfif isDefined("local.ksBarDate")>
							if ($.trim($('###variables.formName# ###local.ksBarDate#').val()) == '') arrReq[arrReq.length] = "Admission Date for Kansas Bar is required.";
						</cfif>
					} else if (mcSel == 'Law Student') {
						<cfif isDefined("local.ksLawSchool")>
							if ($.trim($('###variables.formName# ###local.ksLawSchool#').val()) == '') arrReq[arrReq.length] = "Law School is required.";
						</cfif>
						<cfif isDefined("local.ksGradDate")>
							if ($.trim($('###variables.formName# ###local.ksGradDate#').val()) == '') arrReq[arrReq.length] = "Graduation Date is required.";
						</cfif>
					}

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<cfif len(variables.strPageFields.contentMembershipInfo)>
				<div>#variables.strPageFields.contentMembershipInfo#</div>
				<br/>
			</cfif>

			<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent1.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent2.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent3.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent4.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent4.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent5.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent5.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent6.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent6.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent7.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent7.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#variables.strPageFields.membershipCategory#</div>
			<div class="tsAppSectionContentContainer">
				<table cellpadding="3" border="0" cellspacing="0">
				<tr>
					<td colspan="4" class="tsAppBodyText">#variables.strPageFields.contentMembershipCategory#</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="10">*&nbsp;</td>
					<td class="tsAppBodyText" nowrap>Membership Category you qualify for</td>
					<td class="tsAppBodyText">
						<select name="mccf_memberCategory" id="mccf_memberCategory" class="tsAppBodyText" onChange="toggleFTM();">
							<option value=""></option>
							<option value="Attorney" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Attorney">selected</cfif>>Regular Attorney (Sustaining)</option>
							<option value="Defense Attorney" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Defense Attorney">selected</cfif>>Defense Attorney (Subscribing)</option>
							<option value="Government Attorney" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Government Attorney">selected</cfif>>Government Attorney (Attorney employed by State of Kansas)</option>
							<option value="Judicial" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Judicial">selected</cfif>>Judge</option>
							<option value="Law School Faculty" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Law School Faculty">selected</cfif>>Law School Faculty</option>
							<option value="Retired Attorney" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Retired Attorney">selected</cfif>>Retired Attorney</option>
							<option value="Paralegal" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Paralegal">selected</cfif>>Paralegal</option>
							<option value="Law Student" <cfif structKeyExists(local.strData, "mccf_memberCategory") and local.strData.mccf_memberCategory eq "Law Student">selected</cfif>>Law Student</option>
						</select>
					</td>					
				</tr>
				</table>
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- set Contact Type based on selection of mccf_memberCategory --->
		<cfset local.ksContactTypeNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnName="Contact Type"]')>
		<cfset local.ksContactTypeValueID = XMLSearch(local.ksContactTypeNode[1],'string(columnvalue[@columnValueString="#arguments.rc.mccf_memberCategory#"]/@valueID)')>
		<cfset structInsert(arguments.rc, "MD_#local.ksContactTypeNode[1].xmlAttributes.columnID#", local.ksContactTypeValueID)>

		<!--- set prof license status --->
		<cfif listFindNoCase("Attorney,Defense Attorney,Government Attorney",arguments.rc.mccf_memberCategory)>
			<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
			<cfloop query="local.qryOrgPlTypes">
				<cfif local.qryOrgPlTypes.PLName eq "Kansas">
					<cfset structInsert(arguments.rc, "mpl_#local.qryOrgPlTypes.PLTypeID#_status", "Active")>
					<cfbreak>
				</cfif>
			</cfloop>
		</cfif>

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=variables.siteID,
																					 memberID=variables.useMID, 
																					 subscriptionUID='f078ac32-b37d-469d-8202-7ef5e5f01acd', 
																					 isRenewal=0,
																					 frequencyShortName='F',
																					 allowFrontEnd="true")>
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>			

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">#variables.strPageFields.membershipType#</div>
			<div class="tsAppSectionContentContainer">
				<table cellpadding="3" border="0" cellspacing="0">
				<cfif len(variables.strPageFields.contentMembershipType)>
					<tr>
						<td colspan="2" class="tsAppBodyText">#variables.strPageFields.contentMembershipType#</td>
					</tr>
				</cfif>
				<cfloop query="local.qryRates">
					<tr valign="top">
						<td class="tsAppBodyText" width="30">
							<cfinput type="radio" name="mccf_RFID" id="mccf_RFID" value="#local.qryRates.RFID#" checked="#local.qryRates.currentRow is 1 or structKeyExists(local.strData, 'mccf_RFID') and local.strData.mccf_RFID eq local.qryRates.RFID#">
						</td>
						<td class="tsAppBodyText">
							#local.qryRates.rateName# - #dollarFormat(local.qryRates.rateAmt)#
						</td>
					</tr>
				</cfloop>
				</table>
			</div>

			<div class="tsAppSectionHeading">#variables.strPageFields.memberBenefits#</div>
			<div class="tsAppSectionContentContainer">
				<table cellpadding="3" border="0" cellspacing="0">
				<cfif len(variables.strPageFields.contentMemberBenefits)>
					<tr>
						<td colspan="2" class="tsAppBodyText">#variables.strPageFields.contentMemberBenefits#</td>
					</tr>
				</cfif>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_1" value="CJPAC" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'CJPAC')#"></td>
					<td class="tsAppBodyText">Civil Justice Political Action Committee (CJ PAC)</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_2" value="CLE" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'CLE')#"></td>
					<td class="tsAppBodyText">Continuing Legal Education (CLE)</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_3" value="CounselFinancial" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'CounselFinancial')#"></td>
					<td class="tsAppBodyText">Counsel Financial law firm financing for plaintiffs firms</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_4" value="eClipslegalnews" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'eClipslegalnews')#"></td>
					<td class="tsAppBodyText">eClips legal news</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_5" value="FindAnAttorney" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'FindAnAttorney')#"></td>
					<td class="tsAppBodyText">"Find An Attorney" searchable public database</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_6" value="KTLAJournal" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'KTLAJournal')#"></td>
					<td class="tsAppBodyText">KTLA Journal</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_7" value="KTLAMembershipDirectory" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'KTLAMembershipDirectory')#"></td>
					<td class="tsAppBodyText">KTLA Membership Directory and Desk Reference</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_8" value="ListservesandPracticeSections" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'ListservesandPracticeSections')#"></td>
					<td class="tsAppBodyText">Listserves and Practice Sections</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_9" value="LOJFoundation" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'LOJFoundation')#"></td>
					<td class="tsAppBodyText">Legacy of Justice Foundation (501c3)</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_10" value="NetworkingEvents" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'NetworkingEvents')#"></td>
					<td class="tsAppBodyText">Networking Events</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_11" value="SBAJusticeLoans" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'SBAJusticeLoans')#"></td>
					<td class="tsAppBodyText">SBA Justice Loans small business loan financing</td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_benefits" id="mccf_benefits_12" value="TrialSmith" checked="#structKeyExists(local.strData, 'mccf_benefits') and listFindNoCase(local.strData.mccf_benefits,'TrialSmith')#"></td>
					<td class="tsAppBodyText">TrialSmith deposition database</td>
				</tr>
				</table>
			</div>

			<div class="tsAppSectionHeading">#variables.strPageFields.additionalInformation#</div>
			<div class="tsAppSectionContentContainer">
				<table cellpadding="3" border="0" cellspacing="0">
				<cfif len(variables.strPageFields.contentAdditionalInfoTax)>
					<tr>
						<td colspan="2" class="tsAppBodyText">#variables.strPageFields.contentAdditionalInfoTax#</td>
					</tr>
				</cfif>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_optoutdir" id="mccf_optoutdir" value="1" checked="#structKeyExists(local.strData, 'mccf_optoutdir')#"></td>
					<td class="tsAppBodyText">Opt out of being listed in the "Find an Attorney" online search database directory</td>
				</tr>
				<cfif len(variables.strPageFields.contentAdditionalInfoVerify)>
					<tr>
						<td colspan="2" class="tsAppBodyText">#variables.strPageFields.contentAdditionalInfoVerify#</td>
					</tr>
				</cfif>
				<tr valign="top">
					<td class="tsAppBodyText" width="30"><cfinput type="checkbox" name="mccf_verify" id="mccf_verify" value="1" checked="#structKeyExists(local.strData, 'mccf_verify')#"></td>
					<td class="tsAppBodyText">I verify all information contained in this membership application is current</td>
				</tr>
				</table>
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- set opt out field --->
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID)>
		<cfif isDefined("arguments.rc.mccf_optoutdir")>
			<cfset local.objSaveMember.setCustomField(field="Exclude from Find An Attorney", value="1")>
		<cfelse>
			<cfset local.objSaveMember.setCustomField(field="Exclude from Find An Attorney", value="0")>
		</cfif>
		<cfset local.strResult = local.objSaveMember.saveData()>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>				
			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=variables.siteID, memberID=variables.useMID, subscriptionUID='f078ac32-b37d-469d-8202-7ef5e5f01acd', isRenewal=0)>
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>
		<cfset local.paymentRequired = local.qryRatesSelected.rateAmt gt 0>
		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.CCPayProfileCode, variables.strPageFields.CheckPayProfileCode ]>
			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																						siteID=variables.siteID, 
																						memberID=variables.useMID, 
																						title="Join KTLA - Payment Method", 
																						formName=variables.formName, 
																						backStep="showMembershipInfo")>
		</cfif>

		<cfif local.paymentRequired>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#arguments.rc.historyID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">#variables.strPageFields.Confirmation#</div>
			<div class="tsAppSectionContentContainer">
				<div class="tsAppBodyText">#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
			</div>

			<cfif local.qryRatesSelected.rateAmt gt 0>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=variables.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>

		<cfif structKeyExists(arguments.rc,"mccf_benefits")>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"CJPAC")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='CJPAC')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='Civil Justice Political Action Committee (CJ PAC)', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"CLE")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='CLE')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='Continuing Legal Education (CLE)', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"CounselFinancial")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='CounselFinancial')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='Counsel Financial law firm financing for plaintiffs firms', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"eClipslegalnews")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='eClipslegalnews')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='eClips legal news', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"FindAnAttorney")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='FindAnAttorney')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='"Find An Attorney" searchable public database', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"KTLAJournal")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='KTLAJournal')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='KTLA Journal', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"KTLAMembershipDirectory")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='KTLAMembershipDirectory')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='KTLA Membership Directory and Desk Reference', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"ListservesandPracticeSections")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='ListservesandPracticeSections')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='Listserves and Practice Sections', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"LOJFoundation")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='LOJFoundation')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='Legacy of Justice Foundation (501c3)', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"NetworkingEvents")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='NetworkingEvents')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='Networking Events', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"SBAJusticeLoans")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='SBAJusticeLoans')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='SBA Justice Loans small business loan financing', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
			<cfif listFindNoCase(arguments.rc.mccf_benefits,"TrialSmith")>
				<cfset local.qryHistory = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='TrialSmith')>
				<cfif local.qryHistory.recordcount>
					<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=local.qryHistory.categoryID, subCategoryID=0, description='TrialSmith deposition database', enteredByMemberID=variables.useMID, newAccountsOnly=false)>
				</cfif>
			</cfif>
		</cfif>

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='becde174-f2d6-4101-972d-7bcb732c9250', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='66dfb740-ff1e-43d0-ad96-b7d0cae7472d', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='1962a737-7aa4-4979-a4a8-67732960ded9', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='597ff269-9486-46e0-a763-3c1c2d261790', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='8c96b8a6-9cef-4cd6-ab47-d9b2e84d6b62', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='f7d8d434-82c1-4b49-80c2-e7aeab511436', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='6b84c201-5550-4f0d-b11b-d92e9f2ecc04', mode="confirmation", strData=arguments.rc)>

		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=variables.siteID, memberID=variables.useMID, subscriptionUID='f078ac32-b37d-469d-8202-7ef5e5f01acd', isRenewal=0)>
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>

		<cfset local.memberPayProfileDetail = "">
		<cfif local.qryRatesSelected.rateAmt gt 0>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.contentConfirmation)>
				<div>#variables.strPageFields.contentConfirmation#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div>Here are the details of your application:</div><br/>

			#local.strFieldSetContent1.fieldSetContent#
			#local.strFieldSetContent2.fieldSetContent#
			#local.strFieldSetContent3.fieldSetContent#
			#local.strFieldSetContent4.fieldSetContent#
			#local.strFieldSetContent5.fieldSetContent#
			#local.strFieldSetContent6.fieldSetContent#
			#local.strFieldSetContent7.fieldSetContent#

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KTLA - Membership Category</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<table cellpadding="3" border="0" cellspacing="0">
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Membership Category you qualify for: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">#arguments.rc.mccf_memberCategory#</td>
					</tr>
					</table>
				</td>
			</tr>
			</table>
			<br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KTLA - Membership Type</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<div>#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
				</td>
			</tr>
			</table>
			<br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KTLA - Member Benefits</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<cfif structKeyExists(arguments.rc,"mccf_benefits")>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"CJPAC")>
							<div>Civil Justice Political Action Committee (CJ PAC)</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"CLE")>
							<div>Continuing Legal Education (CLE)</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"CounselFinancial")>
							<div>Counsel Financial law firm financing for plaintiffs firms</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"eClipslegalnews")>
							<div>eClips legal news</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"FindAnAttorney")>
							<div>"Find An Attorney" searchable public database</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"KTLAJournal")>
							<div>KTLA Journal</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"KTLAMembershipDirectory")>
							<div>KTLA Membership Directory and Desk Reference</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"ListservesandPracticeSections")>
							<div>Listserves and Practice Sections</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"LOJFoundation")>
							<div>Legacy of Justice Foundation (501c3)</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"NetworkingEvents")>
							<div>Networking Events</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"SBAJusticeLoans")>
							<div>SBA Justice Loans small business loan financing</div>
						</cfif>
						<cfif listFindNoCase(arguments.rc.mccf_benefits,"TrialSmith")>
							<div>TrialSmith deposition database</div>
						</cfif>
					<cfelse>
						<div>None selected</div>
					</cfif>
				</td>
			</tr>
			</table>
			<br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KTLA - Additional Information</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<table cellpadding="3" border="0" cellspacing="0">
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Opt out of being listed in the "Find an Attorney" online search database directory: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
							<cfif structKeyExists(arguments.rc,"mccf_optoutdir")>
								#YesNoFormat(arguments.rc.mccf_optoutdir)#
							<cfelse>
								No
							</cfif>
						</td>
					</tr>
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>I verify all information contained in this membership application is current: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
							<cfif structKeyExists(arguments.rc,"mccf_verify")>
								#YesNoFormat(arguments.rc.mccf_verify)#
							<cfelse>
								No
							</cfif>
						</td>
					</tr>
					</table>
				</td>
			</tr>
			</table>

			<cfif local.qryRatesSelected.rateAmt gt 0>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KTLA - Payment</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to KTLA",
			emailhtmlcontent = local.confirmationHTML,
			siteID = variables.siteID,
			memberID = variables.useMID,
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to KTLA", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.errActiveFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errAcceptedFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.errBilledFound#	
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>					
