<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#
<cfif local.strResults.itemCount EQ 0>
	#showCommonNotFound(bucketid=arguments.bucketid, searchid=arguments.searchid, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<cfelse>
	<cfset local.expertName = UcFirst(replace('#local.strSearch.expertFName# #local.strSearch.expertLName#','"','','all'),true)>
	
	<div id="MCSearchSummaryContainer" class="bk-mb-5">
		<div id="divExpertConnectResults" class="row-fluid bk-mt-4">
			<div class="span6">
				<div>Expert Name: <b>#local.expertName#</b></div>
				<div class="bk-mb-4 bk-mt-2">
					We found <b>#Numberformat(local.strResults.itemCount)#</b> unique plaintiff lawyers with knowledge of #local.expertName# in <b>#local.strResults.numStates#</b> states.
				</div>
				<button type="button" class="btn btn-success btn-large" onclick="b_showLoginContainer();"><i class="icon-envelope bk-align-baseline bk-mr-1" style="max-width:300px;"></i> Preview List of Lawyers</button>
			</div>
			<div class="span6">
				<div class="bk-d-flex bk-flex-wrap">
					<div id="MCSearchSummaryLocationChart" class="bk-mb-3"></div>
					<div id="bk_tooltip"></div>
				</div>
			</div>
		</div>
	</div>

	<div id="MCNotLoggedInContainer" style="display:none;">
		<div class="bk-mb-2 bk-mt-2">
			We found <b>#Numberformat(local.strResults.itemCount)#</b> unique plaintiff lawyers with knowledge of <b>#local.expertName#</b> in <b>#local.strResults.numStates#</b> states.
		</div>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</div>
	#showFooter(viewDirectory=arguments.viewDirectory)#
</cfif>
</cfoutput>