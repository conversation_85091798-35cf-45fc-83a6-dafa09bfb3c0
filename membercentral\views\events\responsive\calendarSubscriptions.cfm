<cfparam name="attributes.data" default="#structNew()#">

<cfset dataStruct = attributes.data.actionStruct>
<cfset calendarSettings = attributes.data.calendarSettings>
<cfset baseQueryString = attributes.data.baseQueryString>
<cfset arrCalendars = attributes.data.actionstruct.arrSubscribableCalendars>
<cfset selectedCalendar = attributes.data.actionstruct.selectedCalendar>

<cfsavecontent variable="local.calJS">
	<cfoutput>
	<link rel="stylesheet" href="/assets/common/javascript/tablesaw/tablesaw.css">	
 	<script src="/assets/common/javascript/tablesaw/tablesaw.js"></script>
 	<script type="text/javascript" src="/assets/common/javascript/clipboard.js/1.7.1/clipboard.min.js"></script>

	<style type="text/css">
	  ##d_clip_button {
	    width:150px;
	    text-align:center;
	    border:1px solid black;
	    background-color:##ccc;
	    margin:10px; padding:10px;
	  }
	  ##d_clip_button.hover { background-color:##eee; }
	  ##d_clip_button.active { background-color:##aaa; }
	  .calendarContainer {display:none;}
	  .icalLinks {display:none;}
	  .urlContainers {display:none;}


		/* mobile first for custom block */
		@media (max-width: 39.9375em) {
			.tablesaw td {border-top-width: 0px;}
		}

	</style>
	<script type="text/javascript">
		$(function() {
			var addcpyClip = new Clipboard('.linkcopy');
			addcpyClip.on('success', function(e) {
				Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
					var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa icon-check\"></i><div class=\"mc-noty\">We\'ve copied the link to your clipboard.</div></div>';
					new Noty({ 
						type:'success',
						layout:'bottomLeft', 
						theme:'bootstrap-v4', 
						text:mc_noty_msg,
						closeWith:['button','click'],
						timeout:3000
					}).show();	
				});
			});

			$('.urlContainers').focus(function(){
				$(this).one('mouseup', function(event){
					event.preventDefault();
				}).select();
			});

		});

		function handleCalendarSelect(googleOriCal) {
	    	$('.googleLinks').hide();
	    	$('.icalLinks').hide();
			var calendarType = "";

		    if (googleOriCal == 'google') {
		    	$('.googleLinks').show();
				calendarType = "Google Calendar";
			}else if (googleOriCal == 'apple') {
				$('.icalLinks').show();
				calendarType = "Apple Calendar";
			}else {
		    	$('.icalLinks').show();
				calendarType = "Outlook, Yahoo, iCAL, or Other Calendar";
		    }
			$('.calendarContainer').attr("calendarType", calendarType);

			$('.calendarContainer').show();
		}
		
		<cfif NOT StructIsEmpty(selectedCalendar)>
		function handleGtagCalendarAction(calendarType){
			if (typeof triggerSubscribeToMyCalendar === "function") {
				triggerSubscribeToMyCalendar("#dataStruct.pageName#", calendarType);
			} else {
				MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {triggerSubscribeToMyCalendar("#dataStruct.pageName#", calendarType);});
			}
		}
		</cfif>
	</script>
	
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.calJS#">

<!--- common js --->
<cfinclude template="../commonBaseView.cfm">


<div class="container-fluid">
	<!--- Nav Bar --->
	<cfinclude template="calendarNavbar.cfm">

	<!--- main content --->
	<cfif arrayLen(arrCalendars) OR NOT StructIsEmpty(selectedCalendar)>
		<cfoutput>
			<div class="row-fluid">
				<div class="span12">
					<h3>Subscribe to Calendar</h3>
					<p>Let us keep you up to date by automatically adding events from this calendar to your mobile or desktop calendar. Many different applications support the calendar syncing method that we are using (iCal), including Microsoft Outlook 2007 and later, Apple iCal (iPhone and iPad), Google Calendar, and Yahoo Calendar.</p>

					<p><strong>Caveats:</strong></p>
					<ul>
						<li>Includes all future events and those from up to 30 days in the past.</li>
						<li>You won't be able to edit the events</li>
						<li>The initial sync to your calendar should be instant, but updates to the calendar may take a while to reach your calendar - sometimes up to a day. It depends on your calendar program or service and its settings.</li>
					</ul>
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="alert alert-info">
							<strong>Public View</strong><br/>							
							You are currently are not logged in.  Your event feed will only contains events visible by the public.  If you login before subscribing, your feed subscription may include additional events based on your involvement in the organization.
							<br /><br />
							<a class="btn btn-small btn-info" href="/?pg=login">Login Now</a>
						</div>
					<cfelse>
						<div class="alert alert-warning">
							<strong>Private Calendar Feed for #session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#</strong><br/>
							Subscription links shown below are specific to you, based on your involvement with the association. Do not share them with others. There may be events shown that are not visible by the public or some other users.
						</div>
					</cfif>
				</div>
			</div>
			<div class="row-fluid">
				<div class="span12">
					<h4>Choose your Calendar Software/Service</h4>

					<ul id="calendarTypes" class="nav nav-pills nav-stacked">
						<li id="googleCalendar"><a href="##googleInstructions" onclick="handleCalendarSelect('google');" data-toggle="tab"><i class="icon-google-plus icon-large"></i> Google Calendar</a></li>
						<li id="appleCalendar"><a href="##appleInstructions" onclick="handleCalendarSelect('apple');" data-toggle="tab"><i class="icon-apple icon-large"></i> Apple iCal (iPhone/iPad/Mac)</a></li>
						<li id="otherCalendar"><a href="##generalInstructions" onclick="handleCalendarSelect('other');" data-toggle="tab"><i class="icon-calendar icon-large"></i> Outlook, Yahoo, iCAL, or Other Calendar</a></li>
					</ul>
				</div>
			</div>
			<cfif NOT StructIsEmpty(selectedCalendar)>
				<div class="calendarContainer row-fluid">
					<div class="span12">
						<div class="alert alert-info">
							<div class="form-horizontal">
								<button class="btn btn-large googleLinks" onclick="handleGtagCalendarAction($('.calendarContainer').attr('calendarType')); window.open('http://www.google.com/calendar/render?cid=#URLEncodedFormat(selectedCalendar.iCalHTTPLink)#', 'Google Calendar');">Launch Google Calendar</button>
								<button class="btn btn-large icalLinks" onclick="handleGtagCalendarAction($('.calendarContainer').attr('calendarType')); window.location.href='#selectedCalendar.iCalWebCalLink#';">Import into Calendar</button>
								 or 
								<span class="linkContainers">
									<button class="btn btn-large linkcopy googleLinks" data-clipboard-text="#selectedCalendar.iCalHTTPLink#" onclick="handleGtagCalendarAction($('.calendarContainer').attr('calendarType'));">Copy Link for Manual Setup</button>
									<button class="btn btn-large linkcopy icalLinks" data-clipboard-text="#selectedCalendar.iCalWebCalLink#" onclick="handleGtagCalendarAction($('.calendarContainer').attr('calendarType'));">Copy Link for Manual Setup</button>
								</span>
								<span class="urlContainers">
									<input type="text" class="input-small googleLinks" value="#selectedCalendar.iCalHTTPLink#"/>
									<input type="text" class="input-small icalLinks" value="#selectedCalendar.iCalWebCalLink#"/>
									(Manually Copy and Paste Link into your calendar)
								</span>
								
							</div>
						</div>
					</div>
				</div>
			</cfif>

			<div class="row-fluid">
				<div class="span12">
					<div class="tabbable">
						<div class="tab-content">
							<div class="tab-pane alert alert-info" id="googleInstructions">
								<p>Click the <strong>Open in Google Calendar</strong> button to attempt to automatically subscribe to this calendar.</p>
								<p>If you have trouble using the subscribe button below, press the "Copy Address" button and manually add the calendar in Google <a target="_blank" href="https://support.google.com/calendar/answer/37100?hl=en">using the instructions found on this Google Help page</a>.</p>
								<p>If you are trying to add the subscription to Google Calendar on your mobile device, you must first add it to the web version of Google Calendar. Once you do that, the calendar will be available on your device within a few hours.</p>
							</div>
							<div class="tab-pane alert alert-info" id="appleInstructions">
								<p>Click the <strong>Import into Calendar</strong> button to attempt to automatically subscribe to this calendar using Apple iCalendar.</p>
								<p>If the button doesn't work for you, you may have to copy the subscription link and manually add the subscription.</p>
							</div>
							<div class="tab-pane alert alert-info" id="generalInstructions">
								<p>Click the <strong>Import into Calendar</strong> button to attempt to automatically subscribe to this calendar using your default calendar application.</p>
								<p>If the button doesn't work for you, you may have to copy the subscription link and manually add the subscription within you calendar software or service.</p>
								<p><strong>Instructions for popular calendar solutions:</strong></p>
								<ul>
									<li><a target="_blank" href="http://office.microsoft.com/en-us/outlook-help/view-and-subscribe-to-internet-calendars-HA010167325.aspx##BM2">Outlook: Add Internet Calendar Subscription</a></li>
									<li><a target="_blank" href="http://windows.microsoft.com/en-us/windows/outlook/calendar-import-vs-subscribe">Outlook.com : Subscribe to a calendar</a></li>
									<li><a target="_blank" href="http://help.yahoo.com/qe/uk/tutorials/cal/cal_subscribe5.html">Yahoo: Subscribing to Calendars</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>

			<cfif arrayLen(arrCalendars)>
				<div class="calendarContainer row-fluid">
					<div class="span12">
						<h4>Other Calendars Available for Subscription</h4>
						<table class="table table-stripped tablesaw" border="0" cellpadding="3" cellspacing="0" width="100%" data-mode="stack">
							<thead>
								<tr bgcolor="##DEDEDE">
									<td class="tsAppBodyText tsAppBB tsAppBT">Calendar</td>
									<td class="tsAppBodyText tsAppBB tsAppBT">Import to Calendar</td>
									<td class="tsAppBodyText tsAppBB tsAppBT">Subscription Link</td>
								</tr>
							</thead>
							<tbody>
								<cfloop index="x" from="1" to="#arrayLen(arrCalendars)#">
									<cfset thisEvent = arrCalendars[x]>
									<tr valign="top">
										<td class="tsAppBodyText"><div class="tablesawCustomBlock">#thisEvent.calendarName#</div></td>
										<td class="tsAppBodyText">
											<div class="tablesawCustomBlock">
												<div class="googleLinks form-horizontal">
													<button class="btn btn-small" onClick="window.open('http://www.google.com/calendar/render?cid=#URLEncodedFormat(thisEvent.iCalHTTPLink)#','Google Calendar');">Launch Google Calendar</button>
												</div>
												<div class="icalLinks form-horizontal" style="display:none;">
													<button class="btn btn-small" onClick="window.location.href='#thisEvent.iCalWebCalLink#'">Import into Calendar</button>
												</div>
											</div>
										</td>
										<td class="tsAppBodyText">
											<div class="tablesawCustomBlock">
												<div class="googleLinks form-horizontal">
													<span class="urlContainers"><input type="text" class="input-small" value="#thisEvent.iCalHTTPLink#"/></span>
													<span class="linkContainers"><button class="btn btn-small linkcopy" data-clipboard-text="#thisEvent.iCalHTTPLink#">Copy Link</button></span>
												</div>
												<div class="icalLinks form-horizontal tablesawCustomBlock" style="display:none;">
													<span class="urlContainers"><input type="text" class="input-small" value="#thisEvent.iCalWebCalLink#"/></span>
													<span class="linkContainers"><button class="btn btn-small linkcopy" data-clipboard-text="#thisEvent.iCalWebCalLink#">Copy Link</button></span>
												</div>
											</div>
										</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
					</div>
				</div>
			</cfif>
		</cfoutput>
	</cfif>
</div>

<!--- footer code --->
<cfsavecontent variable="footerCode">
	<cfinclude template="commonBaseViewFooter.cfm">
</cfsavecontent>
<cfoutput>#application.objCommon.minText(footerCode)#</cfoutput>
