<cfinclude template="../commonEventRegJS.cfm">
<cfinclude template="../commonEventRegStyles.cfm">

<cfsavecontent variable="local.evRegDefVwHead">
	<cfoutput>
	<style type="text/css">
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
		div##actionswrapper { float:right; width:200px; margin:6px 0px 10px 20px; }
		div##actionswrapper div.sidebox { border:1px solid ##DEDEDE; }
		div##actionswrapper div.sidebox div.sideboxtitle { padding:4px; background-color:##DEDEDE; font-weight:bold; }
		div##actionswrapper div.sidebox div.sideboxbody { padding:4px; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evRegDefVwHead)#">

<cfoutput>
<div id="evRegContainer" class="evRegV2">
	<div id="actionswrapper" class="tsAppBodyText">
		<div class="sidebox">
			<div class="sideboxtitle">Questions?</div>
			<div class="sideboxbody">
				<a href="#arguments.event.getValue('mainurl')#&evAction=showDetail&eid=#arguments.event.getValue('eid')#">View event details</a>
			</div>
		</div>
	</div>
	<div class="tsAppHeading">#encodeForHTML(arguments.strEvent.qryEventMeta.eventContentTitle)#</div>
	<cfif len(arguments.strEvent.qryEventMeta.eventSubTitle)><div class="evreg-text-dim"><b>#encodeForHTML(arguments.strEvent.qryEventMeta.eventSubTitle)#</b></div></cfif>
	<div class="tsAppBodyText" style="margin-top:4px;"><i class="icon-calendar"></i> #local.eventtime#</div>
	<br clear="all" />

	<cfif local.evRegV2.currentReg.currentStep EQ 1>
		<cfinclude template="eventRegV2_step1.cfm">
	<cfelse>
		<div class="evreg-card evreg-p-3 tsAppBodyText">
			<div class="evreg-mb-3 evreg-font-size-lg evreg-text-dim">Registration for:</div>
			<div class="evreg-d-flex">
				<div class="evreg-d-flex evreg-col">
					<cfif local.showMemberPhoto>
						<div><img src="#local.strRegMember.hasPhoto ? '/memberphotosth/#LCASE(local.strRegMember.memberphoto)#' : '/assets/common/images/directory/default.jpg'#" class="evreg-mr-3 evreg-img-thumbnail"></div>
					</cfif>
					<div>
						<h4 class="evreg-mb-1 evreg-mt-2">
							#local.strRegMember.mc_combinedName#
							<cfif len(local.strRegMember.company)><div class="evreg-p-1"><small>#local.strRegMember.company#</small></div></cfif>
						</h4>
						<div class="evreg-mt-1 evreg-p-1">
							<cfif len(local.strRegMember.mc_combinedAddresses)>#local.strRegMember.mc_combinedAddresses#</cfif>
							<cfif len(local.strRegMember.mc_extraInfo)>#local.strRegMember.mc_extraInfo#</cfif>
							<cfif len(local.strRegMember.mc_recordType)><div>#local.strRegMember.mc_recordType#</div></cfif>
							<cfif len(local.strRegMember.mc_memberType)><div>#local.strRegMember.mc_memberType#</div></cfif>
							<cfif len(local.strRegMember.mc_lastlogin)><div>#local.strRegMember.mc_lastlogin#</div></cfif>
						</div>
					</div>
				</div>
				<div class="evreg-d-flex evreg-flex-column evreg-ml-auto">
					<button type="button" class="tsAppBodyButton" onclick="searchEventReg();">Register Someone Else</button>
					<button type="button" class="tsAppBodyButton evreg-mt-3" onclick="doCancelEventReg();">Cancel</button>
				</div>
			</div>
		</div>

		<div id="EvRegStep2" style="display:none;"></div>
		<div id="EvRegStep3" style="display:none;"></div>
		<div id="EvRegStep4" style="display:none;"></div>
		<cfif local.evRegV2.currentReg.registrantID GT 0>
			<div id="EvRegStep5" style="display:none;"></div>
		</cfif>

		<cfif local.evRegV2.currentReg.isRegCartItem EQ 1 OR local.evRegV2.currentReg.registrantID GT 0>
			<cfif local.evRegV2.currentReg.registrantID GT 0>
				<div id="saveEditRegErr" class="alert evreg-mt-3" style="display:none;"></div>
				<div id="saveEditRegSaveLoading" class="evreg-mt-3 evreg-text-center" style="display:none;"></div>
			</cfif>
			<div class="evreg-mt-5 evreg-text-center">
				<button type="button" name="btnGotoRegCart" id="btnGotoRegCart" class="btnFinalizeReg" onclick="#local.evRegV2.currentReg.isRegCartItem EQ 1 ? 'regCartCheckout();' : 'saveEditReg();'#" style="width:200px;" disabled>
					<cfif local.evRegV2.currentReg.isRegCartItem EQ 1>Checkout<cfelse>Save Changes</cfif>
				</button>
			</div>
		</cfif>
	</cfif>
</div>
<div id="EvRegLoading" class="evRegV2" style="display:none;">
	<div class="evreg-card evreg-mt-3 evreg-p-3">
		<div class="evreg-skeleton evreg-skeleton-text"></div>
		<div class="evreg-skeleton evreg-skeleton-text"></div>
		<div class="evreg-skeleton evreg-skeleton-text"></div>
		<div class="evreg-skeleton evreg-skeleton-text"></div>
	</div>
</div>
</cfoutput>