<cfsavecontent variable="local.commonJS">
	<cfoutput>
	<script type="text/javascript">
		function sendConfirmation() {
			var btn = $('##btnResendConfirmation');
			var ind = $('##spanResendInd');
			var msg = $('##spanResendMsg');
			var em = $('##sendToEmail');
			msg.html('').removeClass('tsAppBodyTextImportant').hide();
			ind.show();
			btn.attr("disabled", true);

			var resendConfirmationResult = function(r) {
				ind.hide();
				btn.attr("disabled", false);
				if (r.success && r.success.toLowerCase() == 'true') {
					msg.html('<i class="icon-check icon-2x"></i> &nbsp; <b>Confirmation sent to '+em.val()+'.</b>').show();
					em.val('');
				} else {
					msg.html('There was a problem e-mailing this receipt. Try again.').addClass('tsAppBodyTextImportant').show();
				}
			};
			
			em.val($.trim(em.val()));
			if (em.val().length == 0 || !_CF_checkregex(em.val(), /#application.regEx.email#/, true)) {
				ind.hide();
				btn.attr("disabled", false);
				msg.html('Enter a valid e-mail address.').addClass('tsAppBodyTextImportant').show();
				return false;
			}

			var objParams = { contributionID:#attributes.data.contributionID#, sendToEmail:em.val() };
			TS_AJX('CONTRIB','resendConfirmationEmail',objParams,resendConfirmationResult,resendConfirmationResult,20000,resendConfirmationResult);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.commonJS)#">