<cfoutput>
<div style="padding:5px">
	<h5>Purchased Medline Documents</h5>

	#getPaginationHTML(topOrBottom="top",recordCount=local.qryAllMedline.recordCount,itemCount=val(local.qryAllMedline.itemCount), docType="document",maxRows=local.maxRows,startRow=local.startrow,tab='PMD',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any Medline documents.")#

	<!--- results table --->
	<cfoutput query="local.qryAllMedline">
		<cfif local.qryAllMedline.inCart>
			<cfset local.inCartStyle = "" />
			<cfset local.addToCartStyle = "display:none;" />
		<cfelse>
			<cfset local.inCartStyle = "display:none;" />
			<cfset local.addToCartStyle = "" />
		</cfif>								
		<div class="row-fluid s_row <cfif local.qryAllMedline.currentrow mod 2 is 0>s_row_alt</cfif>">
			<div class="span12">
				<div class="row-fluid">
					<div class="span10">
						<i class="icon icon-file-text" style="line-height:23px;"></i>
						<b>#local.qryAllMedline.docTitle#</b>
						<div class="s_dtl">
							<div class="hidden-phone">
								#local.qryAllMedline.expertName#<br/>
								PMID ###local.qryAllMedline.pmid# - #DateFormat(local.qryAllMedline.lastUpdated, "mmm d, yyyy")# #timeformat(local.qryAllMedline.lastUpdated,"h:mm tt")# - #local.qryAllMedline.message#<br/>
								<b class="s_label">OrderID:</b> #local.qryAllMedline.orderid# - RndID: #local.qryAllMedline.rndid#
							</div>
							<div class="alert">
								<cfif local.qryAllMedline.medlinestatus EQ "orderPlaced">
									<b class="s_label">Disclaimer:</b> Your order is being processed.  Please check back.  In addition, we will email you when your document has arrived.<br>
								<cfelseif local.qryAllMedline.medlinestatus EQ "done" AND local.qryAllMedline.documentURL NEQ "" AND now() LTE local.qryAllMedline.urlExpiration>
									<b class="s_label">Disclaimer:</b> You have until #local.qryAllMedline.urlExpiration# to download your document. Once your download link expires in #local.qryAllMedline.urlExpirationInDays# you will need to repurchase the document in order to download.<br>
								<cfelseif local.qryAllMedline.medlinestatus EQ "done" AND local.qryAllMedline.documentURL NEQ "" AND now() GT local.qryAllMedline.urlExpiration>
									<b class="s_label">Disclaimer:</b> Your download URL has expired.  You will need to repurchase this document.<br>
								</cfif>
							</div>
						</div>
					</div>
					<div class="span2 s_act">
						<cfif  (local.qryAllMedline.documentURL NEQ "" and local.qryAllMedline.urlExpirationInDays GTE 0)>
							<div class="s_opt"><a href="javascript:downloadMedlineDoc(#local.qryAllMedline.documentID#);" title="Download Medline Document"><i class="icon icon-download icon-large" style="line-height:22px;"></i>Download</a></div>
							<div class="s_opt">
								<b class="s_label">Link Expires:</b> #dateformat(local.qryAllMedline.urlExpiration,"m/d/yyyy")#<br/>
								<cfset local.warning = "">
								<cfif local.qryAllMedline.urlExpirationInDays LTE 5>
									<cfset local.warning = "Important">
								</cfif>
								<span class="#local.warning#">#local.qryAllMedline.urlExpirationInDays# days left</span>
							</div>
						<cfelseif (local.qryAllMedline.medlinestatus EQ "done" AND local.qryAllMedline.documentURL NEQ "" AND now() GT local.qryAllMedline.urlExpiration)>

							<div class=" medlineArticles" data-tsdocumentid="#local.qryAllMedline.pmid#" data-sn="#local.objDocuments.getStandardNumber(local.qryAllMedline.issn, local.qryAllMedline.eissn, local.qryAllMedline.isbn)#" data-year="#left(local.qryAllMedline.pubdate,4)#">
								<div class="s_opt" style="#local.inCartStyle#" id="s_incart#local.qryAllMedline.pmid#">
									<a href="javascript:viewCart();" title="View your cart/checkout"><i class="icon icon-shopping-cart icon-large" style="line-height:22px;"></i>In Cart</a>
								</div>	
								<cfif local.qryAllMedline.owned EQ "">
									<div class="s_opt" style="#local.addToCartStyle#" id="s_addtocart#local.qryAllMedline.pmid#">
										<a href="javascript:addToCart(#local.qryAllMedline.pmid#,null);" title="Buy Article Again"><i class="icon icon-shopping-cart icon-large" style="line-height:22px;"></i>Buy</a>  <span id="price#local.qryAllMedline.pmid#" style="display: none;"></span>
									</div>
								</cfif>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</div>
	</cfoutput>
	#getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllMedline.recordCount,itemCount=val(local.qryAllMedline.itemCount), docType="document",maxRows=local.maxRows,startRow=local.startrow,tab='PMD',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any Medline documents.")#
</div>
<script type='text/javascript'>medlinePriceEstimates();</script>
</cfoutput>