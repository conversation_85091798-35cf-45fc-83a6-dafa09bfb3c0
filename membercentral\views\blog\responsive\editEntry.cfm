<cfinclude template="../commonEditEntry.cfm">

<cfsavecontent variable="local.formJS">
<cfoutput>
	
	<script type="text/javascript">
		function showNewAuthor(r) {
			var rowNum = Number($('##tbodyAuthor tr.authorRow').length) + 1;
			$('##tbodyAuthor').append('<tr class="authorRow" id="authorRow' + r.memberid + '"><td>' + rowNum + '.</td><td>' + r.firstname + ' ' + r.lastname + '</td><td><a href="##" onclick="deleteAuthor(' + r.memberid + ');return false;" class="btn btn-small btn-danger">Remove</a></td></tr>');
		}
		function showNewBlogInstances(arrNewBID) {
			var rowNum = 0;
			var blogName = '';

			for (var k=0; k<arrNewBID.length;k++) { 
				rowNum = Number($('##tbodyBlogInstances tr.blInstanceRow').length) + 1;
				blogName = $('##newBlogInstance option[value="'+arrNewBID[k]+'"]').text();
				$('##tbodyBlogInstances').append('<tr class="blInstanceRow" id="blInstanceRow' + arrNewBID[k] + '"><td>' + rowNum + '.</td><td>' + blogName + '</td><td><a href="##" onclick="removeBlogInstance(' + arrNewBID[k] + ');return false;" class="btn btn-small btn-danger">Remove</a></td></tr>');
			}
			
			$('##btnAddBlogInstance').prop('disabled',false);
			$('##divNewBlogInstanceAddForm').hide();
		}
		function onChangePostType() {
			var ptid = $('##blPostTypeID').val() || '';

			$('.be-settings-well').toggleClass('mc_entry_hide', !(ptid && ptid != ''));
			$('##divPostTypeFieldsContainer, .div_posttype_cf').addClass('mc_entry_hide');
			$('##divCatAssignmentsContainer, tr.catTreeRow').hide();

			if($('##div_posttype_' + ptid + '_cf').length){
				$('##div_posttype_' + ptid + '_cf').removeClass('mc_entry_hide');
				$('##divPostTypeFieldsContainer').removeClass('mc_entry_hide');
			}

			var catTreeIDList = '';
			<cfif local.dataStruct.qryAllowedPostTypesForBlog.recordCount eq 1>
				catTreeIDList = $('##blPostTypeID').data('cattreeidlist') || '';
			<cfelse>
				catTreeIDList = $('##blPostTypeID').find(':selected').data('cattreeidlist') || '';

				var description = $('##blPostTypeID').find(':selected').data('desc') || '';
				$("##postTypeDesc").remove();
				if(description.length) $('##blPostTypeID').parent().append('<div id="postTypeDesc" class="blogWell-pl-2 blogWell-mt-1"><small><i>'+description+'</i></small></div>');
			</cfif>
			
			if (catTreeIDList && catTreeIDList.toString().length) {
				var arrCatTreeIDs = catTreeIDList.toString().split('|');
				arrCatTreeIDs = arrCatTreeIDs.map(function(e) { return '##catTree' + e; });
				$(arrCatTreeIDs.join(', ')).show();
				$('##divCatAssignmentsContainer').show();
				$('tr.catTreeRow:hidden').find('select.afterArchiveCat,select.beforeArchiveCat').val('').multiselect('refresh');
			}
		}
		function onChangeArchiveDate() {
			if($('##expirationDate').length){
				var expirationDate = $('##expirationDate').val().trim();
			}else{
				var expirationDate = "#dateFormat(local.dataStruct.getDocData.expirationDate,'m/d/yyyy')#";
			}
			
			if (expirationDate == '') {
				$('.mcblog_archivedatedepend').addClass('hiddenRow');
				$('.catmultisel').removeClass('hasarchivedate');
				$('select.afterArchiveCat').val('').multiselect('refresh');
			} else {
				$('.mcblog_archivedatedepend').removeClass('hiddenRow');
				$('.catmultisel').addClass('hasarchivedate');
				$('select.afterArchiveCat').multiselect('refresh');
			}
		}
		function initBlogEntryFields() {
			var fd = new Object();
				fd.siteid = #attributes.event.getValue('mc_siteInfo.siteid')#;
				fd.blogid = #local.instanceSettings.blogID#;
				fd.blogentryid = #val(local.dataStruct.getDocData.blogEntryID)#;
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					fd.hideadminonly = 0;
				<cfelse>
					fd.hideadminonly = 1;
				</cfif>
			var entryFieldTemplateSource = $('##mc_blogEntryFieldTemplate').html();
			var entryFieldTemplate = Handlebars.compile(entryFieldTemplateSource);

			$.ajax({
				url: '?event=proxy.ts_json&c=ADMBLOG&m=getPostTypeFieldsInfo',
				type: 'POST',
				data: {fd:JSON.stringify(fd)},
				dataType: 'json',
				success: function(response) { 
					var tmpResponse = JSON.stringify(response).replace(/\^~~~\^/g,'');
					var responseResult = JSON.parse(tmpResponse);
					
					if(responseResult.arrPostTypes && responseResult.arrPostTypes.length) {
						responseResult.arrPostTypes.forEach(function(ptype) {
							if(ptype.hasPostTypeFields) {
								var postTypeFields = new Object();
								postTypeFields.arrPostTypeFields = ptype.arrPostTypeFields;

								var postTypeFieldHTML = '<div id="div_posttype_'+ptype.postTypeID+'_cf" class="div_posttype_cf mc_entry_hide">' + entryFieldTemplate(postTypeFields) + '</div>';

								$('##divMCPostTypeFieldContainer').append(postTypeFieldHTML);

								if($('##blPostTypeID').val() == ptype.postTypeID){
									$('##div_posttype_'+ptype.postTypeID+'_cf').removeClass('mc_entry_hide');
									$('##divPostTypeFieldsContainer').removeClass('mc_entry_hide');
								}
							}
						});

						if ($('div.div_posttype_cf').length)  {
							$('##divMCPostTypeFieldLoading').hide();
							initializeBlogEntryFieldControls($('##divMCPostTypeFieldContainer'));
						} else {
							$('##divPostTypeFieldsContainer').addClass('mc_entry_hide');
						}
					} else {
						$('##divPostTypeFieldsContainer').addClass('mc_entry_hide');
					}

					$('##btnSaveBlogEntry').attr('disabled',false);
					
				}, fail: function(response) { 
					alert('Some error occured while loading post type fields. Please contact MemberCentral.');
				}
			});
		}
	<cfif local.dataStruct.getDocData.blogEntryID gt 0>
		<cfset local.linkDomain = "#application.objPlatform.isRequestSecure() ? 'https' : 'http'#://#attributes.event.getValue('mc_siteinfo.mainhostname')#">
		function getBlogEntryDocs() {
			var getDocsResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.arrdocuments.length) {
						var html = '';
						for (var i=0; i<r.arrdocuments.length; i++) {
							html += '<tr id="entryDocRow'+r.arrdocuments[i].documentid+'" class="entryDocRow"><td><a href="/docDownload/'+r.arrdocuments[i].documentid+'" target="_blank">'+r.arrdocuments[i].filename+'</a></td><td style="white-space: nowrap;"><a href="#local.linkDomain#/docDownload/'+r.arrdocuments[i].documentid+'" class="copylinkBtn btn btn-small" id="'+r.arrdocuments[i].documentid+'" title="Copy Link"><i class="icon-copy"></i> Copy Link</a> <a href="##" onclick="confirmDeleteBEDoc('+r.arrdocuments[i].documentid+');return false;" class="btn btn-small btn-danger">Remove</a></td></tr>';
						}
						$('##tbodyDocs').append(html);
						initCopyEntryDocLinks();
						$('##divEntryDocsHolder').show();
						$('##BlogDocumentsFlexWrapper').addClass('has-existing')
					} else {
						$('##divEntryDocsHolder').hide();
					}
				} else {
					alert('We were unable to list blog entry documents.');
				}
			};

			$('##tbodyDocs tr.entryDocRow').remove();

			var objParams = { siteID:#arguments.event.getValue('mc_siteinfo.siteID')#, blogEntryID:mcblog_entryid };
			TS_AJX('ADMBLOG','getBlogEntryDocuments',objParams,getDocsResult,getDocsResult,10000,getDocsResult);
		}
	</cfif>
	</script>
	<style type="text/css">
		##articleDate, ##postDate, ##expirationDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:99%; background-repeat:no-repeat; }
		a.btn-danger { color:##fff !important; }
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
		.warning { background:##fff6bf url(/assets/common/images/warning.gif) 16px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border:2px solid ##fc6; }
		h3 { margin-bottom:10px !important;line-height:20px !important; }
		form {margin:0!important;}
		.blogWell-text-light {color:##fff !important;}
		.blogWell-text-danger {color:##f83245!important}
		.blogWell-text-dim {color:##808080!important}
		.blogWell-text-center {text-align:center !important;}
		.blogWell-text-right {text-align:right !important;}
		.blogWell-text-normal {font-weight:normal !important;}
		.blogWell-text-bold {font-weight:bold !important;}
		.blogWell-d-inline-block {display:inline-block !important;}
		.blogWell-d-flex {display:flex !important;}
		.blogWell-flex-wrap {flex-wrap:wrap !important;}
		.blogWell-flex-column {flex-direction: column !important;}
		.blogWell-col {flex-basis:0;flex-grow:1;max-width:100%;padding-right:5px;padding-left:5px;}
		.blogWell-col-auto {flex:0 0 auto;width:auto;max-width:100%;padding-right:5px;padding-left:5px;}
		.blogWell-align-self-start{align-self:start !important;}
		.blogWell-align-self-center{align-self:center !important;}
		.blogWell-p-0 {padding:0!important;}
		.blogWell-pt-0 {padding-top:0!important;}
		.blogWell-pr-0 {padding-right:0!important;}
		.blogWell-pr-2 {padding-right:.5em!important;}
		.blogWell-p-1 {padding:.25em!important;}
		.blogWell-p-2 {padding:.5em!important;}
		.blogWell-p-2 {padding:.5em!important;}
		.blogWell-p-3 {padding:1em!important;}
		.blogWell-pt-3 {padding-top:1em!important;}
		.blogWell-pl-0 {padding-left:0!important;}
		.blogWell-pl-1 {padding-left:.25em!important;}
		.blogWell-pl-2 {padding-left:.5em!important;}
		.blogWell-pl-3 {padding-left:1em!important;}
		.blogWell-pl-5 {padding-left:2em!important;}
		.blogWell-pb-0 {padding-bottom:0!important;}
		.blogWell-pb-1 {padding-bottom:.25em!important;}
		.blogWell-pb-2 {padding-bottom:.5em!important;}
		.blogWell-pb-3 {padding-bottom:1em!important;}
		.blogWell-m-0 {margin:0!important;}
		.blogWell-m-1 {margin:.25em!important;}
		.blogWell-m-2 {margin:.5em!important;}
		.blogWell-mt-0 {margin-top:0!important;}
		.blogWell-mt-1 {margin-top:.25em!important;}
		.blogWell-mt-2 {margin-top:.5em!important;}
		.blogWell-mt-3 {margin-top:1em!important;}
		.blogWell-mt-4 {margin-top:1.5em!important;}
		.blogWell-mt-5 {margin-top:2em!important;}
		.blogWell-mb-0 {margin-bottom:0!important;}
		.blogWell-mb-1 {margin-bottom:.25em!important;}
		.blogWell-mb-2 {margin-bottom:.5em!important;}
		.blogWell-mb-3 {margin-bottom:1em!important;}
		.blogWell-mb-4 {margin-bottom:1.5em!important;}
		.blogWell-mb-5 {margin-bottom:2em!important;}
		.blogWell-ml-1 {margin-left:.25em!important;}
		.blogWell-ml-2 {margin-left:.5em!important;}
		.blogWell-mr-1 {margin-right:.25em!important;}
		.blogWell-mr-2 {margin-right:.5em!important;}
		.blogWell-mr-3 {margin-right:1em!important;}
		.blogWell-mr-4 {margin-right:1.5em!important;}
		.blogWell-mr-5 {margin-right:2em!important;}
		.blogWell-font-size-xs { font-size:.79em!important; }
		.blogWell-font-size-sm { font-size:.85em!important; }
		.blogWell-font-size-md {font-size:.95em!important;}
		.blogWell-font-size-lg {font-size:1.1875em!important;}
		.blogWell-font-size-xl {font-size:1.425em!important;}
		.blogWell-font-weight-bold {font-weight:bold!important;}
		.blogWell-ml-auto {margin-left:auto !important;}
		.blogWell-mr-auto {margin-right:auto !important;}
		.blogWell-mx-auto {margin-left:auto !important;margin-right:auto !important;}
		.blogWell-border-gray {border-color:gray!important;}
		.blogWell-align-top {vertical-align:top!important;}
		.blogWell-text-nowrap {white-space:nowrap !important;}
		.blogWell-formcontrol {height:35px!important;box-sizing:border-box!important;margin:0!important;}
		.blogWell-card { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal;
			-ms-flex-direction: column;   flex-direction: column; min-width: 0; word-wrap: break-word; background-color: ##fff; background-clip: border-box; 
			border: 0 solid rgba(122, 123, 151, 0.3); border-radius: 0.65em; border-width: 1px; box-shadow: 0 .46875em 2.1875em rgba(0,0,0,.03),0 .9375em 1.40625em rgba(0,0,0,.03),0 .25em .53125em rgba(0,0,0,.05),0 .125em .1875em rgba(0,0,0,.03); }
		.blogWell-card-header { padding:.75em;margin-bottom:0;border-bottom: 1px solid rgba(7,9,25,.125);border-radius: .65em .65em 0 0; }
		.blogWell-card-body {padding:1.25em;}
		.blogWell-img-thumbnail {padding: 0.25em; background-color: ##fff; border: 1px solid ##eeeff8; border-radius: 0.65em; max-width: 100%; height: auto;}
		.blogWell-btn-outline-primary {color: ##3c44b1;border-color:rgba(122, 123, 151, 0.3);border-width: 1px;background-color:transparent;border-radius:.2em;padding:.3em 1.1em;}
		.blogWell-btn-outline-primary:hover {color: ##fff;background-color: ##04c;border-color: ##04c;box-shadow: 0 .22em .525em rgba(60,68,177,.4),0 .0625em .385em rgba(60,68,177,.54);}
		.blogWell-bg-light {background-color:##f4f5fd !important;}
		.blogWell-bg-whitesmoke {background-color:##f5f5f5 !important;}
		.blogWell-text-decoration-none {text-decoration: none !important;}
		.blogWell-appendbtn {border-top-right-radius:3px;border-bottom-right-radius:3px;justify-content:center;}
		.blogWell-input-append, .blogWell-input-prepend {display: inline-flex!important;margin-bottom:0px!important;white-space:nowrap;}
		.blogWell-input-prepend .blogWell-add-on:first-child {margin-right:-1px!important;}
		.blogWell-input-prepend .blogWell-add-on {display: inline-flex!important;
			width:auto!important;height:35px!important;min-width:16px!important;padding:7px!important;font-size:14px;font-weight:normal!important;
			line-height:25px!important;text-align:center!important;text-shadow:0 1px 0 ##fff!important;background-color:##eee!important;
			border:1px solid ##ccc!important;box-sizing:border-box!important;align-items:center;}
		.blogWell-input-append .blogWell-add-on {margin-left:-1px!important;vertical-align: top!important;display: inline-flex!important;
			width:auto!important;height:35px!important;min-width:16px!important;padding:7px!important;font-size:14px;font-weight:normal!important;
			line-height:25px!important;text-align:center!important;text-shadow:0 1px 0 ##fff!important;background-color:##eee!important;
			border:1px solid ##ccc!important;box-sizing:border-box!important;align-items:center;}
		.blogWell-input-prepend input {border-radius:0!important;width:auto!important;background:##fff!important;}
		.blogWell-input-append input {border-radius: 4px 0 0 4px!important;width:auto!important;background:##fff!important;}
		.blogWell-cursor-pointer {cursor: pointer;}
		.blogWell-strike { text-decoration:line-through; }
		.blogWell-opacity-1 {opacity: 0.1 !important;}
		.blogWell-border {border: 1px solid ##eeeff8 !important;}
		.blogWell-border-top {border-top: 1px solid ##eeeff8 !important;}
		.blogWell-border-right {border-right: 1px solid ##eeeff8 !important;}
		.blogWell-border-bottom {border-bottom: 1px solid ##eeeff8 !important;}
		.blogWell-w-100 {width:100% !important;}
		.blogWell-h-100 {height:100% !important;}
		.blogWell-w-25 {width:25% !important;}
		.blogWell-w-50 {width:50% !important;}
		.blogWell-mw-25 {min-width:25% !important;}
		.renewCartItem:not(:first-child), .renewReceiptItem:not(:first-child), .renewSearchMem:not(:first-child) {border-top: 1px solid ##ddd;padding-top: 15px;}
		.blogWell-shadow-none {-webkit-box-shadow: none !important;box-shadow: none !important;}
		.renewTicketPackageFieldsContainer{padding-left:25px;}
		.renewTicketFieldsContainer{padding-left:60px;}
		
		.mc_entry_hide { display:none !important; }
		.fimg_info {line-height: 1.2em;}
		.blogWell-skeleton {opacity: .7;animation: blogWell-skeleton-loading 2s linear infinite alternate;}
		.blogWell-skeleton-text {width: 100%;height:1em;margin-bottom:1em;border-radius:.125em;}
		.blogWell-skeleton-text:last-child {margin-bottom: 0;width: 80%;}
		@keyframes blogWell-skeleton-loading {
			0% {background-color: hsl(200, 20%, 70%);}
			100% {background-color: hsl(200, 20%, 95%);}
		}
		.wellLoading-skeleton{
			width: 100%;
			height: 1em;
			margin-bottom: 1em;
			border-radius: 0.125em;
		}
		.wellLoading-skeleton:last-child {
			margin-bottom: 0;
			width: 85% !important;
		}
		.wellLoading-skeleton:nth-child(2) {
			width: 90% !important;
		}
		.wellLoading-skeleton:nth-child(4) {
			width: 95% !important;
		}
		.wellLoading-skeleton{
			opacity: .7;
			animation: well-skeleton-loading 2s linear infinite alternate;
		}
		
		@keyframes well-skeleton-loading {
			0% {background-color: hsl(200, 20%, 70%);}
			100% {background-color: hsl(200, 20%, 95%);}
		}
		.text-left{
			text-align:left !important;
		}
		##isStickyBlog{
			vertical-align: top !important;
		}
		.isStickyBlogWrap{
			margin-top:2px;
		}
		.ml-0{
			margin-left:0px !important;
		}
		.cardBody .form-group.row-fluid{
			margin-bottom: 10px;
		}
		.hiddenRow{
			display:none !important;
		}
		div.ui-multiselect-menu.catmultisel {width:220px!important;}
		table##tblCatAssignments tr td { border:0!important; }

		@media screen and (min-width:768px){
			.catTreeRow button.ui-multiselect.catmultisel:not(.hasarchivedate){
				width:400px!important;
			}
			.catTreeRow button.ui-multiselect.catmultisel.hasarchivedate{
				width:250px!important;
			}
			td.catTreeNameColumn { text-align:right!important; }
		}
		@media screen and (max-width:767px){
			.dateWell input{
				margin-bottom:8px;
			}
			.catTreeRow button.ui-multiselect.catmultisel{
				width:auto !important;
				min-width:200px !important;
			}
		}
		##mcBEDocUploader_container{
			padding-left:0px !important;
		}
		@media screen and (min-width:979px) and (max-width:1199px){
			.row-fluid > .span10{
				width:79%!important;
			}
			##newRedirectName{
				width:67% !important;
				padding-right:0px;
			}

		}
		@media screen and (min-width:1200px){
			##newRedirectName{
				width:69% !important;
				padding-right:0px;
			}
		}
		@media screen and (max-width:978px){
			##newRedirectName{
				width:99% !important;
				padding-right:0px;
			}
		}
	</style>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.formJS#">

<cfoutput>
	<cfinclude template="navigation.cfm">
	<cfform name="frmBlog" id="frmBlog" class="form-horizontal">
		<cfinput type="hidden" name="blBlogSaved" id="blBlogSaved" value="1">
		<cfinput type="hidden" name="blBlogEntryID" id="blBlogEntryID" value="#local.dataStruct.getDocData.blogEntryID#">
		<cfinput type="hidden" name="blBlogContentID" id="blBlogContentID" value="#local.dataStruct.getDocData.blogContentID#">
		<cfinput type="hidden" name="blAuthorMIDList" id="blAuthorMIDList" value="#local.dataStruct.getDocData.authorMemberIDList#">
		<cfinput type="hidden" name="blogIDList" id="blogIDList" value="#local.dataStruct.getDocData.blogIDList#">
		<cfinput type="hidden" name="redirectID" id="redirectID" value="#local.dataStruct.getDocData.redirectID#">
		<cfinput type="hidden" name="currentRedirectName" id="currentRedirectName" value="#local.dataStruct.getDocData.redirectName#">
		<cfinput type="hidden" name="ownBlogID" id="ownBlogID" value="#local.dataStruct.getDocData.ownBlogID#">
		<cfif not local.instanceSettings.URL>
			<cfinput type="hidden" name="blURL" id="blURL" value="">
		</cfif>
		<cfif not local.instanceSettings.Title>
			<cfinput type="hidden" name="blTitle" id="blTitle" value="">
		</cfif>

		<div id="divBEFormSubmitArea" class="mc_entry_hide"></div>
		<div id="divBESaveLoading" class="mc_entry_hide">
			<h3><cfif local.editMode>Edit #local.instanceSettings.Singular#<cfelse>Add #local.instanceSettings.Singular#</cfif></h3>
			<div style="text-align:center;margin:30px 0;">
				<i class="icon-spin icon-spinner icon-3x"></i> <b><span class="loadingMsg">Please wait while we validate and save the details.</span></b>
			</div>
		</div>

		<div class="row-fluid blogEntryHideWhileSaving">
			<div class="span8">
				<h3><cfif local.editMode>Edit #local.instanceSettings.Singular#<cfelse>Add #local.instanceSettings.Singular#</cfif></h3>
			</div>
			<div class="span4">
				<div class="text-right <cfif isdefined('local.dataStruct.msg')>span6<cfelse>span12</cfif>">
					<cfif local.editMode and local.canDelete>
						<button class="btn btn-danger" id="deleteButton" type="button" onClick="javascript:confirmDelete('/?#local.baseQueryString#&blAction=deleteEntry&blBlogEntryID=#local.dataStruct.getDocData.blogEntryID#')">Delete</button>
					</cfif>
				</div>
			</div>
		</div>
		<cfif len(trim(local.instanceSettings.addEntryFormBeforeContent))>
			<div class="row-fluid blogEntryHideWhileSaving">
				<div class="span12">
					#local.instanceSettings.addEntryFormBeforeContent#
				</div>
			</div>
		</cfif>
		<div id="wellLoading">
			<div class="wellLoading-skeleton"></div>
			<div class="wellLoading-skeleton"></div>
			<div class="wellLoading-skeleton"></div>
			<div class="wellLoading-skeleton"></div>
			<div class="wellLoading-skeleton"></div>
			<div class="wellLoading-skeleton"></div>
		</div>
		<!--- error placeholder --->
		<a name="entryFrmTop"></a>
		<div id="entry_frm_err" class="alert" style="display:none;margin:12px 0 0 0;"></div>

		<cfif local.dataStruct.getDocData.blogEntryID gt 0>
			<div id="divEntryConnectedWarning" class="warning blogEntryHideWhileSaving" style="display:none;margin:12px 0 10px 0;">
				Editing this #local.instanceSettings.Singular# will affect associated blogs.
			</div>
		</cfif>
		
		<div class="row-fluid">
			<cfif isdefined('local.dataStruct.msg')>
				<cfswitch expression="#local.dataStruct.msg#">
					<cfcase value="1"><cfset local.msgText = "#local.instanceSettings.Singular# has been Added"></cfcase>
					<cfcase value="2"><cfset local.msgText = "#local.instanceSettings.Singular# has been Saved"></cfcase>
					<cfdefaultcase><cfset local.msgText = ""></cfdefaultcase>
				</cfswitch>
				<div class="span6">#local.msgText#</div>
			</cfif>			
		</div>

		<cfif local.dataStruct.qryAllowedPostTypesForBlog.recordCount eq 1>
			<input type="hidden" name="blPostTypeID" id="blPostTypeID" value="#local.dataStruct.qryAllowedPostTypesForBlog.postTypeID#" data-cattreeidlist="#local.dataStruct.qryAllowedPostTypesForBlog.categoryTreeIDList#">
		<cfelse>
			<fieldset class="blogWell-card blogWell-mt-3 postTypeWell mc_entry_hide blogEntryHideWhileSaving">
				<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-3 cardBody">
					<div class="blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
						Choose #ucFirst(local.instanceSettings.Singular)# Type: *
					</div>
					<div class="span12 ml-0 blogWell-mt-1">
						<select name="blPostTypeID" id="blPostTypeID" onchange="onChangePostType();" class="form-control span12">
							<option value=""></option>
							<cfloop query="local.dataStruct.qryAllowedPostTypesForBlog">
								<option value="#local.dataStruct.qryAllowedPostTypesForBlog.postTypeID#" data-cattreeidlist="#local.dataStruct.qryAllowedPostTypesForBlog.categoryTreeIDList#"<cfif local.dataStruct.qryAllowedPostTypesForBlog.postTypeID eq local.dataStruct.getDocData.postTypeID> selected="selected"</cfif> data-desc="#local.dataStruct.qryAllowedPostTypesForBlog.description#">#local.dataStruct.qryAllowedPostTypesForBlog.typeName#</option>
							</cfloop>
						</select>
					</div>
				</div>
			</fieldset>
		</cfif>

		<fieldset class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 blogEntryHideWhileSaving">
			<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-3 cardBody">      
				<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
					<cfif not local.editMode or local.canEditMetaData> 
						<cfif local.instanceSettings.Title>
							<div class="form-group row-fluid">
								<label for="blTitle" class="control-label span2 text-left blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">Title: *</label>
								<div class="span10">
									<cfinput class="form-control span12" type="text" name="blTitle"  id="blTitle" value="#local.dataStruct.getDocData.blogTitle#" size="50">
								</div>
							</div> 
						</cfif>
						<cfset local.statusID = local.dataStruct.getDocData.statusName EQ 'Approved' ? local.dataStruct.getDocData.statusID : 0>
						
						<cfloop query="local.dataStruct.qryBlogStatuses">
							<cfif local.dataStruct.getDocData.statusID eq local.dataStruct.qryBlogStatuses.statusID> 
								<cfset local.statusID = local.dataStruct.qryBlogStatuses.statusID>
							<cfelseif local.dataStruct.getDocData.blogEntryID is 0 and local.dataStruct.qryBlogStatuses.statusName eq 'Draft'> 
								<cfset local.statusID = local.dataStruct.qryBlogStatuses.statusID>
							</cfif>
						</cfloop>
						
						<input name="blStatusID" id="blStatusID" type="hidden" value="#local.statusID#">
					</cfif>
					<cfif local.dataStruct.isOwnBlogEntry and local.instanceSettings.appRightsStruct.manageQuickLinks is 1>
						<div class="form-group row-fluid">
							<label for="newRedirectName" class="control-label span2 text-left blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">Quick Link:</label>
							<div class="span10" style="padding-top:5px;">
								<span class="mr-2">#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/</span>
								<cfinput class="form-control" type="text" id="newRedirectName" name="newRedirectName" value="#local.dataStruct.getDocData.redirectName#" onblur="onBlurRedirectName(this.value);">							
								<div id="redirectBox" class="py-2 d-none">
									<i class="fas" id="redirectImg"></i> <span id="redirectText"></span>
								</div>
							</div>
						</div> 
					</cfif>
					
					<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates eq 1>
						<div class="form-group row-fluid isStickyBlogWrap">
							<div class="span10 offset2">
								<label><input type="checkbox" name="isStickyBlog" id="isStickyBlog" value="1"<cfif local.dataStruct.getDocData.isSticky is 1> checked="checked"</cfif>> Keep Entry at top of list</label>
							</div>
						</div> 
					</cfif>

					<cfif (not local.editMode) or local.canEditMetaData>
						<div id="divCatAssignmentsContainer" class="blogWell-card-body blogWell-pb-2 blogWell-pl-0 blogWell-pr-0 cardBody" style="display:none;">
							<div class="blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
								Category Assignments:
							</div>
							<div class="form-group row-fluid">
								<p class="mcblog_archivedatedepend">#local.instanceSettings.plural# can be automatically reassigned to different categories on the Archive Date you select.</p>
								<div class="row-fluid">
									<style>
										iframe{width:100%!important;}
										@media screen and (max-width:767px){
											.table:not(.gridTable) td{ display: block;}
											.table:not(.gridTable) td.visible-phone:not(.hiddenRow){ display: block!important;}
										}
									</style>
									
									<table id="tblCatAssignments" class="table" style="max-width:100%;margin-bottom:10px;">
										<thead class="hidden-phone mcblog_archivedatedepend">
											<tr valign="top">
												<th>&nbsp;</th>
												<th>Before Archive Date</th>
												<th>After Archive Date</th>
											</tr>
										</thead>
										<cfloop query="local.dataStruct.qryGetCategoryTrees">
											<cfset local.treeCategories = local.objCategory.getIndentedCategoriesForTree(categoryTreeID=local.dataStruct.qryGetCategoryTrees.categoryTreeID,ordertype="categoryPath")>
											<tr id="catTree#local.dataStruct.qryGetCategoryTrees.categoryTreeID#" class="catTreeRow" valign="top">
												<td class="r catTreeNameColumn">#local.dataStruct.qryGetCategoryTrees.categoryTreeName#:</td>
												<td class="r visible-phone mcblog_archivedatedepend">Before Archive Date</td>
												<td>
													<cfif local.treeCategories.qryCategories.recordCount gte 10>
														<cfset local.listBoxSize = 10>
													<cfelseif local.treeCategories.qryCategories.recordCount eq 0>
														<cfset local.listBoxSize = 1>
													<cfelse>
														<cfset local.listBoxSize = local.treeCategories.qryCategories.recordCount>
													</cfif>
													<cfset local.categoryName = "s_#local.dataStruct.qryGetCategoryTrees.categoryTreeID#ID">
													<select name="#local.categoryName#" id="#local.categoryName#" class="beforeArchiveCat" multiple="multiple" size="#local.listBoxSize#">
														<cfloop query="local.treeCategories.qryCategories">
															<option value="#local.treeCategories.qryCategories.categoryID#" <cfif StructKeyExists(local.dataStruct.getExtraDocData, "#local.categoryName#") AND  find("#local.treeCategories.qryCategories.categoryID#", local.dataStruct.getExtraDocData["#local.categoryName#"]) >selected</cfif>>
																<cfloop from="1" to="#local.treeCategories.qryCategories.catlevel#" index="local.currCatCount">
																	&nbsp;&nbsp;&nbsp;&nbsp;
																</cfloop>#local.treeCategories.qryCategories.categoryName#
															</option>					
														</cfloop>
													</select>
													<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates is 0>
														<cfloop query="local.treeCategories.qryCategories">
															<cfif StructKeyExists(local.dataStruct.getExtraDocData, "#local.categoryName#") AND find("#local.treeCategories.qryCategories.categoryID#", local.dataStruct.getExtraDocData["#local.categoryName#"])>
																<input type="hidden" name="#local.categoryName#" value="#local.treeCategories.qryCategories.categoryID#">
															</cfif>
														</cfloop>
													</cfif>
												</td>
												<td class="r visible-phone mcblog_archivedatedepend">After Archive Date</td>
												<td class="mcblog_archivedatedepend">
													<cfif local.treeCategories.qryCategories.recordCount gte 10>
														<cfset local.listBoxSize = 10>
													<cfelseif local.treeCategories.qryCategories.recordCount eq 0>
														<cfset local.listBoxSize = 1>
													<cfelse>
														<cfset local.listBoxSize = local.treeCategories.qryCategories.recordCount>
													</cfif>
													<cfset local.categoryName = "s_#local.dataStruct.qryGetCategoryTrees.categoryTreeID#ID">
													<select name="#local.categoryName#_Archived" id="#local.categoryName#_Archived" class="afterArchiveCat" multiple="multiple" size="#local.listBoxSize#" <cfif local.instanceSettings.appRightsStruct.canEditPublishingDates is 0> disabled</cfif>>
														<cfloop query="local.treeCategories.qryCategories">
															<option value="#local.treeCategories.qryCategories.categoryID#" <cfif structKeyExists(local.dataStruct.getDocData,"archiveCategories") and StructKeyExists(local.dataStruct.getDocData.archiveCategories, local.categoryName) AND  listfind(local.dataStruct.getDocData.archiveCategories[local.categoryName], local.treeCategories.qryCategories.categoryID) >selected</cfif>>
																<cfloop from="1" to="#local.treeCategories.qryCategories.catlevel#" index="local.currCatCount">
																	&nbsp;&nbsp;&nbsp;&nbsp;
																</cfloop>#local.treeCategories.qryCategories.categoryName#
															</option>					
														</cfloop>
													</select>
													<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates is 0>
														<cfloop query="local.treeCategories.qryCategories">
															<cfif structKeyExists(local.dataStruct.getDocData,"archiveCategories") and StructKeyExists(local.dataStruct.getDocData.archiveCategories, local.categoryName) AND  listfind(local.dataStruct.getDocData.archiveCategories[local.categoryName], local.treeCategories.qryCategories.categoryID)>
																<input type="hidden" name="#local.categoryName#_Archived" value="#local.treeCategories.qryCategories.categoryID#">
															</cfif>
														</cfloop>
													</cfif>
												</td>
											</tr>
										</cfloop>
									</table>	
								</div>							
							</div>
						</div>
					</cfif>
					<div id="divPostTypeFieldsContainer" class="row-fluid mc_entry_hide">
						<div id="divMCPostTypeFieldLoading" class="c"><i class="icon-spin icon-spinner"></i><br/><b>Please Wait...</b></div>
						<div id="divMCPostTypeFieldContainer" class="MCPostTypeFieldContainer"></div>
						<br/>
					</div>
				</cfif>
			</div>
		</fieldset>

		<cfif (not local.editMode) or local.canEditMetaData or local.canReupload>
			<cfif (not local.editMode) or local.canEditMetaData>
				<cfif local.instanceSettings.appRightsStruct.canEditPublishingDates eq 1>
					<fieldset class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 dateWell blogEntryHideWhileSaving">
						<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-2 cardBody">
							<div class="form-group row-fluid">
								<label for="articleDate" class="control-label span2 text-left blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">Article Date:</label>
								<div class="span10">
									<input class="form-control span8" name="articleDate" id="articleDate" type="text" value="#dateFormat(local.dataStruct.getDocData.articleDate,'m/d/yyyy')# - #timeFormat(local.dataStruct.getDocData.articleDate,'h:mm tt')#" size="26"/>
									<span class="col-auto align-self-center">Central</span>
									<a href="javascript:mca_clearDateRangeField('articleDate');"  class="btn">Clear Date</a>
								</div>
							</div>
							<div class="form-group row-fluid">
								<label for="postDate" class="control-label span2 text-left blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
									<cfif len(trim(local.instanceSettings.publishDateLabel)) GT 0 >
										#local.instanceSettings.publishDateLabel#:
									<cfelse>
										Publish Date:
									</cfif>
								</label>
								<div class="span10">
									<input class="form-control span8" name="postDate" id="postDate" type="text" value="#dateFormat(local.dataStruct.getDocData.postDate,'m/d/yyyy')# - #timeFormat(local.dataStruct.getDocData.postDate,'h:mm tt')#" size="26"/>
									<span class="col-auto align-self-center">Central</span>
									<a href="javascript:mca_clearDateRangeField('postDate');" class="btn">Clear Date</a><br/>
									<small><i>Schedule an article to be released at a later date by entering a Publish Date in the future</i></small>
								</div>
							</div>
							<div class="form-group row-fluid">
								<label for="expirationDate" class="control-label span2 text-left blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">Archive Date:</label>
								<div class="span10">
									<input class="form-control span8" name="expirationDate" id="expirationDate" type="text" value="#dateFormat(local.dataStruct.getDocData.expirationDate,'m/d/yyyy')#" size="26" onchange="onChangeArchiveDate();">
									<span class="col-auto align-self-center">Central</span>
									<a href="##" onclick="mca_clearDateRangeField('expirationDate');onChangeArchiveDate();return false;" class="btn">Clear Date</a>
								</div>
							</div>
						</div>
					</fieldset>
				</cfif>
				<cfif local.canCrossPost or local.canManageAuthors>
					<fieldset class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 blogEntryHideWhileSaving">
						<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-2 cardBody">							
							<cfif local.canManageAuthors>						
								<div class="control-group">
									<div class="table-responsive">
										<table class="table gridTable" >
											<tbody id="tbodyAuthor">
												<tr>
													<td colspan="3" style="border-top:0;">
														<button type="button" name="btnAddAuthor" id="btnAddAuthor" class="btn pull-right" onclick="selectBlogAuthor();">Add New Author</button>
													</td>
												</tr>
												<tr>
													<th style="width:10%">##</th>
													<th>Author</th>
													<th style="width:10%"></th>
												</tr>
												<cfset local.thisNum = 1>
												<cfloop list="#local.dataStruct.getDocData.authors#" delimiters="^~~~^" index="local.thisAuthor">
													<tr class="authorRow" id="authorRow#ListFirst(local.thisAuthor,'|')#">
														<td>#local.thisNum#.</td>
														<td>#ListGetAt(local.thisAuthor,2,'|')# #ListGetAt(local.thisAuthor,3,'|')#</td>													
														<td>
															<a href="##" onclick="deleteAuthor(#ListFirst(local.thisAuthor,'|')#);return false;" class="btn btn-small btn-danger">Remove</a>
														</td>
													</tr>
													<cfset local.thisNum = local.thisNum + 1>
												</cfloop>
											</tbody>
										</table>
									</div>
								</div>					
							</cfif>
							<cfif local.canCrossPost>
								<div class="control-group">
									<div class="table-responsive">
										<table class="table gridTable" >
											<tbody id="tbodyBlogInstances">
												<cfif local.dataStruct.allowedCrossPostingTargets.len()>
													<tr>
														<td colspan="3" style="border-top:0;">
															<button type="button" name="btnSelectBlogInstance" id="btnSelectBlogInstance" class="btn pull-right" onclick="selectBlogInstance();">Connect to New Blog</button>
														</td>
													</tr>
													<tr>
														<td colspan="3" style="border-top:0;">
															<div id="divBlogInstancesLoading" style="padding-top:10px;padding-left:5px;display:none;"><i class="icon-spin icon-spinner"></i> <b>Loading...</b></div>
															<div id="divNewBlogInstanceAddForm" style="margin:10px 0;display:none;">
																<select id="newBlogInstance" name="newBlogInstance" multiple="multiple"></select>
																<button type="button" name="btnAddBlogInstance" id="btnAddBlogInstance" class="btn" onclick="doAddBlogInstance();">Add</button>
															</div>
														</td>
													</tr>
												</cfif>
												<tr>
													<th style="width:10%">##</th>
													<th>Title</th>
													<th style="width:10%"></th>
												</tr>
												<cfset var thisSharedBlogID = 0>
												<cfloop query="local.dataStruct.qrySharedBlogInstances">
													<cfset local.thisSharedBlogPerms = "">
													<cfset thisSharedBlogID = local.dataStruct.qrySharedBlogInstances.blogID>
													<cfset local.thisSharedBlog = local.dataStruct.allowedCrossPostingTargets.filter((item) => item.blogID eq thisSharedBlogID)>
													<cfif local.thisSharedBlog.len()>
														<cfset local.thisSharedBlogPerms = local.thisSharedBlog.first().functions.replace('|',',',"ALL")>
													<cfelseif local.dataStruct.qrySharedBlogInstances.blogID eq local.instanceSettings.blogID>
														<cfif local.instanceSettings.appRightsStruct.AddBlog>
															<cfset local.thisSharedBlogPerms = local.thisSharedBlogPerms.listAppend("AddBlog")>
														</cfif>
														<cfif local.instanceSettings.appRightsStruct.editOwn>
															<cfset local.thisSharedBlogPerms = local.thisSharedBlogPerms.listAppend("editOwn")>
														</cfif>
														<cfif local.instanceSettings.appRightsStruct.editAny>
															<cfset local.thisSharedBlogPerms = local.thisSharedBlogPerms.listAppend("editAny")>
														</cfif>
													</cfif>
													<tr class="blInstanceRow" id="blInstanceRow#local.dataStruct.qrySharedBlogInstances.blogID#">
														<td>#local.dataStruct.qrySharedBlogInstances.currentRow#.</td>
														<td>#local.dataStruct.qrySharedBlogInstances.applicationInstanceName#</td>
														<td>
															<cfif local.dataStruct.getDocData.blogID eq local.dataStruct.qrySharedBlogInstances.blogID>
																<small>Primary</small>
															<cfelseif (listFindNoCase(local.thisSharedBlogPerms,"editAny") or (listFindNoCase(local.thisSharedBlogPerms,"editOwn") and listFind(local.dataStruct.getDocData.authorMemberIDList,session.cfcuser.memberdata.memberID)))>
																<a href="##" onclick="removeBlogInstance(#local.dataStruct.qrySharedBlogInstances.blogID#);return false;" class="btn btn-small btn-danger">Remove</a>
															</cfif>
														</td>
													</tr>
												</cfloop>
											</tbody>
										</table>
									</div>
								</div>
							</cfif>					
						</div>
					</fieldset>
				</cfif>
		
				<fieldset class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 blogEntryHideWhileSaving">
					<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-2 cardBody">
						<div class="blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
							Summary:
						</div>
						<div class="span12 ml-0">
							<cfif len(local.instanceSettings.shortDescForContentSummary)>
								<div>#paragraphFormat(local.instanceSettings.shortDescForContentSummary)#</div>
							</cfif>
							<cfif local.instanceSettings.WYSIWYG>
								#local.dataStruct.getDocData.blogSummary.html#
							<cfelse>
								<textarea class="form-control span8" id="rawContentSummary" name="rawContent" rows="5" cols="50">#local.dataStruct.getDocData.blogSummary.text#</textarea>
							</cfif>
						</div>
					</div>
				</fieldset>
				<fieldset class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 blogEntryHideWhileSaving">
					<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-2 cardBody">
						<div class="blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
							#local.instanceSettings.NameDesc# *:
						</div>
						<div class="span12 ml-0">
							<cfif len(local.instanceSettings.shortDescForContentBody)>
								<div>#paragraphFormat(local.instanceSettings.shortDescForContentBody)#</div>
							</cfif>
							<cfif local.instanceSettings.WYSIWYG>
								#local.dataStruct.getDocData.blogBody.html#
							<cfelse>
								<cfif local.instanceSettings.NameDesc eq "Wall">
									<cfif not len(local.dataStruct.getDocData.blogBody.text)>
										<cfset local.dataStruct.getDocData.blogBody.text = "What's on your mind?">
									</cfif>
									<cfinput type="text" id="rawContent" name="rawContent" value="#local.dataStruct.getDocData.blogBody.text#" style="max-width:280px;" onfocus="this.value='';" required="false"  message="Enter a #local.instanceSettings.NameDesc#.">
									<button class="btn" name="btnSaveBlogEntry" id="btnSaveBlogEntry" type="button" onclick="validateAndSaveEntryDetails();" disabled="disabled">Save & Review</button> <button class="btn" id="cancelButton" type="button" onClick="<cfif attributes.data.blAction EQ "addEntry">self.location.href='/?#local.baseQueryString#<cfelse>self.location.href='/?#local.baseQueryString#&blAction=showEntry&blogEntry=#local.dataStruct.getDocData.blogEntryID#</cfif>';">Cancel</button>
								<cfelse>
									<textarea id="rawContent" name="rawContent" rows="5" cols="50">#local.dataStruct.getDocData.blogBody.text#</textarea>
								</cfif>
							</cfif>
						</div>
					</div>
				</fieldset>
				<cfif local.dataStruct.showFeatureImageControls>
					<fieldset id="featuredImageWell" class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 blogEntryHideWhileSaving">
						<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-2 cardBody">							
							<cfset local.doesImageExist = val(local.dataStruct.getDocData.featureImageID) gt 0 and fileExists("#local.dataStruct.featuredImageFullRootPath##local.dataStruct.getDocData.featureImageID#.#local.dataStruct.getDocData.featureImageFileExt#")>	
							<div class="blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
								<cfif NOT local.doesImageExist>Add </cfif>Featured Image:
							</div>
							<div id="featuredImageFlexWrapper" <cfif val(local.dataStruct.getDocData.featureImageID) gt 0>class="has-existing"</cfif>>
								<div id="featuredImageUppyWrapper" class"ml-0">
									<div id="mcBEFeaturedImageUploader" class="blogEntryHideWhileSaving"></div>
								</div>
								<cfif val(local.dataStruct.getDocData.featureImageID) gt 0>
									<div id="featuredImageExistingImageWrapper" class="ml-2">
										<div class="control-group blogEntryHideWhileSaving">
											<div id="imgDiv">
												<cfif local.doesImageExist>
													<div id="divBlogEntryFeaturedImage" class="mcblogentry_ftdimage">
														<cfif local.dataStruct.qryFeaturedImageConfigSizes.recordCount>
															<div style="margin-bottom:10px;">
																Existing Image
																<button type="button" name="btnDeletePostImage" id="btnDeletePostImage" class="btn btn-danger btn-small mcblogentry_ftdimage" onclick="removeFeaturedImage(#local.dataStruct.getDocData.blogEntryID#,'blogEntry');">Remove</button>
															</div>
														</cfif>
														<a href="#local.dataStruct.featuredOriginalImageRootPath##local.dataStruct.getDocData.featureImageID#.#local.dataStruct.getDocData.featureImageFileExt#?#local.dataStruct.imageUUID#" target="_blank">
															<img id="blogEntryFtdImg" src="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-preview-large.#local.dataStruct.getDocData.featureImageFileExt#?#local.dataStruct.imageUUID#" class="mcblog_img">
														</a>
													</div>
													<div class="mcblogentry_ftdimage" style="margin:5px;">
														<cfif local.dataStruct.qryFeaturedImageConfigSizes.recordCount>
															<div>Thumbnail Previews (click to see in new window)</div>
														</cfif>
														<cfloop query="local.dataStruct.qryFeaturedImageConfigSizes">
															<div style="float:left;margin:2px;">
																<div style="text-align:center;height:100px;display:table-cell;vertical-align:bottom;">
																	<a href="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.dataStruct.qryFeaturedImageConfigSizes.featureImageSizeID#.#local.dataStruct.qryFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" target="_blank">
																		<img src="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.dataStruct.qryFeaturedImageConfigSizes.featureImageSizeID#-preview-small.#local.dataStruct.qryFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" class="mcblog_img">
																	</a>
																	<div class="fimg_info"><i><small>#local.dataStruct.qryFeaturedImageConfigSizes.featuredImageSizeCode#</small></i></div>
																	<div class="fimg_info"><i><small>#local.dataStruct.qryFeaturedImageConfigSizes.width#x#local.dataStruct.qryFeaturedImageConfigSizes.height#</small></i></div>
																</div>
															</div>
														</cfloop>
														<div style="clear:both;"></div>
													</div>
													<cfif listLen(local.dataStruct.relatedFeatureImageConfigIDList) gt 0>
														<div style="margin-top:20px;" class="mcblogentry_ftdimage">
															<b>Image Configs Used By Connected Blogs:</b>														
															<cfloop list="#local.dataStruct.relatedFeatureImageConfigIDList#" index="local.relatedFeatureImageConfigID">
																<cfquery name="local.qryRelatedFeaturedImageConfigSizes" dbtype="query">
																	select featureImageSizeID, featuredImageSizeName, featuredImageSizeDesc, featuredImageSizeCode, width, height, fileExtension
																	from local.dataStruct.qryAllRelatedFeaturedImageConfigSizes
																	where featureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.relatedFeatureImageConfigID#">
																	order by featureImageSizeID;
																</cfquery>
																<cfquery name="local.qryConnectedBlogsAndImageConfigInfo" dbtype="query">
																	select blogName
																	from local.dataStruct.qryAllConnectedBlogsAndImageConfigInfo
																	where featureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.relatedFeatureImageConfigID#">
																	order by blogName;
																</cfquery>															
																<div style="margin-top:10px;">
																	<label>#valueList(local.qryConnectedBlogsAndImageConfigInfo.blogName," / ")#</label>
																	<div class="mcblogentry_ftdimage" style="margin:5px;">
																		<cfloop query="local.qryRelatedFeaturedImageConfigSizes">
																			<div style="float:left;margin:2px;">
																				<div style="text-align:center;height:100px;display:table-cell;vertical-align:bottom;">
																					<a href="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.qryRelatedFeaturedImageConfigSizes.featureImageSizeID#.#local.qryRelatedFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" target="_blank">
																						<img src="#local.dataStruct.featuredThumbImageRootPath##local.dataStruct.getDocData.featureImageID#-#local.qryRelatedFeaturedImageConfigSizes.featureImageSizeID#-preview-small.#local.qryRelatedFeaturedImageConfigSizes.fileExtension#?#local.dataStruct.imageUUID#" class="mcblog_img">
																					</a>
																					<div class="fimg_info"><i><small>#local.qryRelatedFeaturedImageConfigSizes.featuredImageSizeCode#</small></i></div>
																					<div class="fimg_info"><i><small>#local.qryRelatedFeaturedImageConfigSizes.width#x#local.qryRelatedFeaturedImageConfigSizes.height#</small></i></div>
																				</div>
																			</div>
																		</cfloop>
																		<div style="clear:both;"></div>
																	</div>
																</div>
															</cfloop>
														</div>														
													</cfif>						
												</cfif>
											</div>							
										</div>
									</div>
								</cfif>	
							</div>
						</div>
					</fieldset>
				</cfif>
				<fieldset id="documentsWell" class="be-settings-well mc_entry_hide blogWell-card blogWell-mt-3 blogEntryHideWhileSaving">
					<div class="blogWell-card-body blogWell-pl-3 blogWell-pb-2 cardBody">
						<div class="blogWell-mb-1 blogWell-font-size-lg blogWell-font-weight-bold">
							Add Documents:
						</div>
						<div class="span12 ml-0">
							<div style="margin-top:10px;">
								Files uploaded here will be available for you to link in your #local.instanceSettings.Singular#. Use the <i class="icon-copy"></i> Copy button to copy the link to your clipboard.
								You can then paste the link as the URL hyperlink for text in your #local.instanceSettings.Singular# or paste it directly into your #local.instanceSettings.Singular#.<br/>

								<div id="BlogDocumentsFlexWrapper" style="margin-top:10px;">
									<div id="BlogDocumentsUppyWrapper" class"ml-0">
										<div id="mcBEDocUploader"></div>
									</div>
									<div id="divEntryDocsHolder" class="table-responsive blogEntryHideWhileSaving" style="display:none;">
										<table class="table" style="width:100%;">
											<tbody id="tbodyDocs">
												<tr>
													<th style="width:40%;">Document</th>
													<th style="width:10%;">Actions</th>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</fieldset>
			</cfif>
		</cfif>

		<cfif len(trim(local.instanceSettings.addEntryFormAfterContent))>
			<div class="row-fluid blogEntryHideWhileSaving">
				<div class="span12">
					<br>
					#local.instanceSettings.addEntryFormAfterContent#
				</div>
			</div>
		</cfif>

		<cfif local.instanceSettings.NameDesc neq "Wall">
			<div class="row-fluid text-right blogWell-mt-3 formActionBtns mc_entry_hide blogEntryHideWhileSaving">
				<button class="btn btn-success" name="btnSaveBlogEntry" id="btnSaveBlogEntry" type="button" onclick="validateAndSaveEntryDetails();" disabled="disabled">Save & Review</button> 
				<button class="btn btn-dark" id="cancelButton" type="button" onClick="<cfif attributes.data.blAction EQ "addEntry">self.location.href='/?#local.baseQueryString#<cfelse>self.location.href='/?#local.baseQueryString#&blAction=showEntry&blogEntry=#local.dataStruct.getDocData.blogEntryID#</cfif>';">Cancel</button>
			</div>
		</cfif>
	</cfform>

	<script id="mc_blogEntryFieldTemplate" type="text/x-handlebars-template">
		<div class="MCBlogEntryFieldsWrapper">
			{{##each arrPostTypeFields}}
				{{##compare fieldGroupingID '>' 0}}
					<div style="padding-left:0px;">
						<fieldset style="padding:10px;border:1px solid ##ccc;margin:10px 0;">
						<legend style="width:auto;font-size:1.1875em;font-weight:400;margin:0;padding:0 8px;border:0;line-height:20px;">{{fieldGrouping}}</legend>
						{{##compare fieldGroupingDesc.length '>' 0}}
							<div style="margin-bottom:15px;">{{fieldGroupingDesc}}</div>
						{{/compare}}
				{{/compare}}

				{{##each arrFields}}
					{{##unless allOptionEmptyOrDisabled}}
						{{##compare itemID '>' 0}}
							<input type="hidden" name="old_cf_{{fieldID}}" value="{{value}}">
						{{/compare}}

						<div style="padding:4px 0;">
							{{##compare displayTypeCode '==' "LABEL"}}
								<span class="blogWell-font-size-lg blogWell-font-weight-bold">{{{attributes.fieldText}}}</span>
							{{/compare}}
							{{##compare displayTypeCode '!=' "LABEL"}}
								{{##if isRequired}}* {{/if}}
								<span class="blogWell-font-size-lg blogWell-font-weight-bold">{{attributes.fieldText}}</span>
							{{/compare}}

							<!--- textbox --->
							{{##compare displayTypeCode '==' "TEXTBOX"}}

								<div style="padding:6px 8px 6px 8px;">

									{{##if supportQty}}
										
										{{##unless maxQtyAllowed}}
											<input type="hidden" name="cf_{{fieldID}}" value="{{value}}">
										{{/unless}}

										Quantity: <input type="text" size="6" autocomplete="off" maxlength="4" id="{{##unless maxQtyAllowed}}disabled_{{/unless}}cf_{{fieldID}}" name="{{##unless maxQtyAllowed}}disabled_{{/unless}}cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldOfferQTY="{{supportQty}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldMaxQtyAllowed="{{maxQtyAllowed}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" value="{{value}}" {{##unless maxQtyAllowed}}disabled{{/unless}} class="MCBlogEntryField form-control span12">
										{{##unless maxQtyAllowed}}
											&nbsp; <span class="tsAppBodyTextImportant">[SOLD OUT]</span>
										{{/unless}}

									{{else}}
										<input type="text" size="60" autocomplete="off" id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCBlogEntryField  form-control span12">
									{{/if}}

								</div>
							{{/compare}}

							<!--- Drop-Down List --->
							{{##compare displayTypeCode '==' "SELECT"}}
								<div style="padding:6px 8px 6px 8px;">
									<select id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" class="MCBlogEntryField form-control span12">
										<option value=""></option>
										{{##each children}}

											<option value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}selected{{/compare}} {{##if unavailable}}disabled{{/if}}>
											{{attributes.fieldValue}}
											{{##if unavailable}}
												&nbsp; [SOLD OUT]
											{{/if}}
											</option>

										{{/each}}
									</select>
								</div>
							{{/compare}}

							<!--- Radio Controls --->
							{{##compare displayTypeCode '==' "RADIO"}}
								<div style="padding:6px 0 6px 20px;">
									{{##each children}}
										<input type="radio" id="cf_{{../fieldID}}" name="cf_{{../fieldID}}" value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}checked{{/compare}} {{##if unavailable}}disabled{{/if}} data-MCBlogEntryFieldDisplayTypeCode="{{../displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{../dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{../attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{../attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{../attributes.fieldText}}" class="MCBlogEntryField"> {{attributes.fieldValue}}
										{{##if unavailable}}
											&nbsp; [SOLD OUT]
										{{/if}}
										<br/>
									{{/each}}
								</div>
							{{/compare}}

							<!--- Checkboxes --->
							{{##compare displayTypeCode '==' "CHECKBOX"}}
								<div style="padding:6px 8px 6px 8px;">
									{{##each children}}
										<label class="checkbox">
											<input type="checkbox" id="cf_{{../fieldID}}" name="cf_{{../fieldID}}" value="{{attributes.valueID}}" {{##if unavailable}}disabled{{/if}} data-MCBlogEntryFieldDisplayTypeCode="{{../displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{../dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{../attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{../attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{../attributes.fieldText}}" {{##each ../value}}{{##compare this '==' ../attributes.valueID}}checked{{/compare}}{{/each}} class="MCBlogEntryField"> {{attributes.fieldValue}}
											{{##if unavailable}}
												&nbsp; [SOLD OUT]
											{{/if}}
										</label>
									{{/each}}
								</div>
							{{/compare}}

							<!--- Date --->
							{{##compare displayTypeCode '==' "DATE"}}
								<div style="padding:6px 8px 6px 8px;">
									<input type="text" size="16" autocomplete="off" id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCBlogEntryField MCAdminDateControl form-control span8"> <a href="##" class="MCAdminDateControlClearLink" data-linkedDateControl="cf_{{fieldID}}">clear</a>
								</div>
							{{/compare}}

							<!--- Textarea --->
							{{##compare displayTypeCode '==' "TEXTAREA"}}
								<div style="padding:6px 8px 6px 8px;">
									<cfoutput><textarea cols="62" rows="5" id="cf_{{fieldID}}" name="cf_{{fieldID}}" class="MCBlogEntryField" data-MCBlogEntryFieldDisplayTypeCode="{{displayTypeCode}}" data-MCBlogEntryFieldDataTypeCode="{{dataTypeCode}}" data-MCBlogEntryFieldIsRequired="{{attributes.isRequired}}" data-MCBlogEntryFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCBlogEntryFieldDesc="{{attributes.fieldText}}">{{value}}</textarea></cfoutput>
								</div>
							{{/compare}}
						</div>
					{{/unless}}
				{{/each}}

				{{##compare fieldGroupingID '>' 0}}
						</fieldset>
					</div>
				{{/compare}}
			{{/each}}
		</div>
	</script>
</cfoutput>