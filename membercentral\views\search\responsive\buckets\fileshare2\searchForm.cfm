<!--- if not logged in, show login/signup/join form --->
<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
    <cfoutput>
        <h3>#ucase(local.qryBucketInfo.bucketName)#</h3>
        #showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
    </cfoutput>
<cfelse>
    <cfsavecontent variable="local.customJS">
        <script type="text/javascript">
            function b_search() {
                $('#searchBoxError').html('').hide();
                var isEmptyForm = bk_isEmptySearchForm('frms_Search',true,true,true);
                if (isEmptyForm){
                    $('#searchBoxError').html('Search using at least one criteria below.').show();
                    $("form#frms_Search input:text").first().focus();
                    return false;
                }
                else {
                    $('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
                    return true;
                }
            }
            function setAdvExpandIcon(){
                isAdvFiltersVisible = $('.advancedFiltersWrapper').css('display') != 'none';
                $('span.advFilters i').attr('class', (!isAdvFiltersVisible) ? 'icon-plus-sign' : 'icon-minus-sign');
            }
        </script>
    </cfsavecontent>
    <cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

    <cfif local.settingsStruct.fileShareID NEQ "">
        <cfset local.bucketTextParam = local.bucketText>
        <cfset local.overrideFSIDParam = local.settingsStruct.fileShareID>
        <cfset local.applicationInstanceNameParam = local.fileShares.applicationInstanceName>    
    <cfelse>
        <cfset local.bucketTextParam = "">
        <cfset local.overrideFSIDParam = 0>
        <cfif local.settingsStruct.bucketHeading NEQ "">
            <cfset local.applicationInstanceNameParam = local.settingsStruct.bucketHeading>
        <cfelse>
            <cfset local.applicationInstanceNameParam = valueList(local.fileshares.fileShareName, ", ")>
        </cfif>
    </cfif>

    <cfoutput>
    #showHeader(bucketID=local.qryBucketInfo.bucketID, bucketName=local.qryBucketInfo.bucketName, bucketText=local.bucketTextParam, applicationInstanceName=local.applicationInstanceNameParam, overrideFSID=local.overrideFSIDParam, viewDirectory=arguments.viewDirectory)#

    <div id="searchBoxError" class="alert alert-error hide"></div>
    <form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
        <cfif local.settingsStruct.fileShareID NEQ "">
            <input type="hidden" name="s_applicationinstanceid" id="s_applicationinstanceid" value="#local.fileShares.applicationInstanceID#">
        </cfif>
        <cfif local.fileShares.showAuthor EQ "0">
            <input type="hidden" name="s_aut" id="s_aut" value="">
        </cfif>
        <cfif local.fileShares.showContributedBy EQ "0">
            <input type="hidden" name="s_fname" id="s_fname" value="">
            <input type="hidden" name="s_lname" id="s_lname" value="">
        </cfif>
        <input type="hidden" name="s_category" id="s_category" value="0">

        <cfif local.settingsStruct.fileShareID EQ "">
            <div class="control-group">
                <label class="control-label" for="s_applicationinstanceid">FileShare:</label>
                <div class="controls">
                    <select id="s_applicationinstanceid" name="s_applicationinstanceid" multiple="yes" size="5">
                        <cfloop query="local.fileShares">
                            <option value="#local.fileShares.applicationInstanceID#" <cfif listFindNoCase(local.strSearchForm.s_applicationinstanceid,local.fileShares.applicationInstanceID)>selected="selected"</cfif>>
                                #local.fileShares.fileShareName#
                            </option>
                        </cfloop>
                    </select>
                </div>
            </div>
        </cfif>
        <div class="control-group">
            <label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
            <div class="controls">
                <input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
            <div class="controls">
                <input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
            <div class="controls">
                <input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="s_key_x"><b>Without</b> the words:</label>
            <div class="controls">
                <input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
            </div>
        </div>
        <div class="control-group">
            <span class="advFilters control-label" title="Advanced Filters">
                <i class="icon-plus-sign" title=" Advanced Filters" class="advFilters" style="vertical-align:middle;"></i> <strong>Advanced Filters</strong>
            </span>
        </div>

        <div id="optionalFilters" class="advancedFiltersWrapperDynamic hide"></div>

        <div class="advancedFiltersWrapper hide">
            <!--- PLAT-447 fileshare can search more than one FS2 bucket --->
            <cfif local.settingsStruct.fileShareID NEQ "">
                <cfloop query="local.settingsStruct.qryCategoryTrees">
                    <cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
                        SET NOCOUNT ON;
                        SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

                        WITH categories(categoryID, categoryName, parentCategoryID, catlevel, sort)
                        AS (
                            SELECT categoryID, categoryName, parentCategoryID, 0 AS catlevel, cast(categoryName AS nvarchar(2048))
                            FROM dbo.cms_categories 
                            WHERE parentCategoryID IS NULL
                            AND categoryTreeId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#">
                            AND isActive = 1
                                UNION ALL
                            SELECT c2.categoryID, c2.categoryName, c2.parentCategoryID, catlevel + 1, cast(sort + '|' + c2.categoryName AS nvarchar(2048))
                            FROM dbo.cms_categories c2 
                            INNER JOIN categories c ON c2.parentCategoryID = c.categoryID
                            WHERE categoryTreeId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#">
                            AND isActive = 1
                        )
                        SELECT categoryID, categoryName, parentCategoryID,catlevel, sort
                        FROM categories 
                        ORDER BY sort;

                        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
                    </cfquery>
                    
                    <!--- Check to see if category id in this list ---> 
                    <cfset local.tmpCategoryID = local.settingsStruct.categoryID>
                    <cfif local.tmpCategoryID EQ "">
                        <cfset local.tmpCategoryID = 0>
                    </cfif>
                    <cfquery name="local.isHere" dbtype="query"> 
                        select categoryID, categoryName from [local].qryCategories where categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.tmpCategoryID#">
                    </cfquery>

                    <cfif local.qryCategories.recordCount gte 10>
                        <cfset local.listBoxSize = 10>
                    <cfelseif local.qryCategories.recordCount eq 0>
                        <cfset local.listBoxSize = 2>
                    <cfelse>
                        <cfset local.listBoxSize = local.qryCategories.recordCount>
                    </cfif>

                    <div class="control-group">
                        <cfif local.isHere.recordCount>
                            <label class="control-label">#local.settingsStruct.qryCategoryTrees.categoryTreeName#:</label>
                            <div class="controls">
                                <label>#local.isHere.categoryName#</label>
                            </div>
                        <cfelse>
                            <label class="control-label bk-control-label-large" for="s_jurisdiction#local.settingsStruct.qryCategoryTrees.categoryTreeID#">#local.settingsStruct.qryCategoryTrees.categoryTreeName#:<cfif local.fileShares.isMultipleSelectSearch EQ "1"><br/>(Select 1 or More)</cfif></label>
                            <div class="controls bk-controls-large">
                                <select id="s_jurisdiction#local.settingsStruct.qryCategoryTrees.categoryTreeID#" name="s_jurisdiction" <cfif local.fileShares.isMultipleSelectSearch EQ "1">multiple="true"</cfif> size="#local.listBoxSize#">                                
                                    <cfloop query="local.qryCategories">
                                        <option value="#local.qryCategories.categoryID#" <cfif listFindNoCase(local.strSearchForm.s_jurisdiction, local.qryCategories.categoryID)>selected</cfif>>
                                            <cfif local.qryCategories.parentCategoryID NEQ "">&nbsp;&nbsp;</cfif>
                                            #local.qryCategories.categoryName#
                                        </option>
                                    </cfloop>
                                </select>
                            </div>
                        </cfif>
                    </div>
                </cfloop>

                <cfif local.fileShares.showAuthor EQ "1">
                    <div class="control-group">
                        <label class="control-label" for="s_aut">#local.fileShares.authorLabel#:</label>
                        <div class="controls">
                            <input type="text" name="s_aut"  id="s_aut" value="#local.strSearchForm.s_aut#" size="32" maxlength="200">
                        </div>
                    </div>
                </cfif>
                <cfif local.fileShares.showContributedBy EQ "1">
                    <div class="control-group">
                        <label class="control-label" for="s_fname">Contributor First Name:</label>
                        <div class="controls">
                            <input type="text" name="s_fname"  id="s_fname" value="#local.strSearchForm.s_fname#" size="25" maxlength="50">
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="s_lname">Contributor Last Name:</label>
                        <div class="controls">
                            <input type="text" name="s_lname"  id="s_lname" value="#local.strSearchForm.s_lname#" size="25" maxlength="50">
                        </div>
                    </div>
                </cfif>
            </cfif>

            <div class="control-group" style="margin-top:30px;">
                <label class="control-label">Created between:</label>
                <div class="controls">
                    <input type="text" id="s_datefrom" name="s_datefrom" value="#DateFormat(local.strSearchForm.s_datefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol"> and 
                    <input type="text" id="s_dateto" name="s_dateto" value="#DateFormat(local.strSearchForm.s_dateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol">
                    <a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_datefrom','s_dateto');return false;">clear dates</a>
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">Published between:</label>
                <div class="controls">
                    <input type="text" id="s_depodatefrom" name="s_depodatefrom" value="#DateFormat(local.strSearchForm.s_depodatefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol"> and 
                    <input type="text" id="s_depodateto" name="s_depodateto" value="#DateFormat(local.strSearchForm.s_depodateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol">
                    <a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_depodatefrom','s_depodateto');return false;">clear dates</a>
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">Uploaded between:</label>
                <div class="controls">
                    <input type="text" id="s_postdatefrom" name="s_postdatefrom" value="#DateFormat(local.strSearchForm.s_postdatefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol"> and 
                    <input type="text" id="s_postdateto" name="s_postdateto" value="#DateFormat(local.strSearchForm.s_postdateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol">
                    <a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_postdatefrom','s_postdateto');return false;">clear dates</a>
                </div>
            </div>
        </div>
        
        <div class="control-group">
            <div class="controls">
                <input type="hidden" name="s_frm" id="s_frm" value="1">
                <button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
            </div>
        </div>
    </form>
    </cfoutput>
</cfif>