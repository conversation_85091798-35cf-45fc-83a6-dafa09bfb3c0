<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	<div style="padding-bottom:10px;">
		<cfif local.hasDepositionBucket>
			<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
		</cfif>
		<button class="tsAppBodyButton bucketHeaderButton" onclick="document.location.href='/?pg=listViewer&lsAction=listBrowse'"><i class="icon-envelope-alt"></i> Browse My Lists</button>
	</div>	
</cfoutput>								
<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		<div class="s_rnfne">#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName)#</div>
	</cfoutput>
<cfelse>
	<cfoutput>
	#showSearchResultsPaging('top',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'message','s')#
	</cfoutput>
	<!--- results table --->
	<table width="100%" cellpadding="2" cellspacing="0" border="0">
	<tr class="s_rhrdtr">
		<td class="tsAppBB tsAppBodyText" style="width:70px;"><b>Date</b></td>
		<td class="tsAppBB tsAppBodyText" style="width:200px;"><b>From</b></td>
		<td class="tsAppBB tsAppBodyText" style="width:102px;"><b>List</b></td>
		<td class="tsAppBB tsAppBodyText" style="width:14px;"><b>Att</b></td>
		<td class="tsAppBB tsAppBodyText"><b>Subject</b></td>
	</tr>
	<cfoutput query="local.qryResults">
		<cfset local.hdrSubject_ = local.qryResults.hdrSubject_>
		<cfif ReFindNoCase(application.regEx.encodedWord,local.hdrSubject_)>
			<cfset local.hdrSubject_ = application.objMimeUtility.decodeText(local.hdrSubject_)>
		</cfif>
		<cfif len(local.hdrSubject_) gt 35>
			<cfset local.hdrSubject_ = "#left(local.hdrSubject_,35)#...">
		</cfif>

		<tr <cfif local.qryResults.currentrow mod 2 is 0>class="s_row_alt"</cfif>>
			<td class="tsAppBodyText">#dateformat(local.qryResults.creatstamp_,"m/d/yy")#</td>
			<td class="tsAppBodyText">#trim(replace(local.qryResults.hdrfrom_,"""","","all"))#</td>
			<td class="tsAppBodyText">#local.qryResults.list#</td>
			<td width="14"><cfif local.qryResults.attachmentflag is 1><img border="0" src="#application.paths.images.url#images/paperclip.gif" alt="Attachment" width="14" height="17" /></cfif></td>
			<td class="tsAppBodyText"><a href="javascript:b_loadmsgjson(#local.qryResults.messageid_#,'','#arguments.viewDirectory#');">#local.hdrSubject_#</td>
		</tr>
	</cfoutput>
	</table>
	<br/>
	<div id="s_lyris_messagewindow"></div>
	<iframe id="MCcontenttoprint" style="height: 0px; width: 0px; position: absolute;" frameborder="0"></iframe>
	<cfoutput>
	#showSearchResultsPaging('bottom',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'message','s')#
	</cfoutput>
</cfif>