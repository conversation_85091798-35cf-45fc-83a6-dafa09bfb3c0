<cfoutput>
    <div style="padding:5px;">
        <h5>Previously Opened Disciplinary Action Reports</h5>

        #getPaginationHTML(topOrBottom="top",recordCount=local.qryAllDiscActions.recordCount,itemCount=val(local.qryAllDiscActions.itemCount), docType="discipinary action",maxRows=local.maxRows,startRow=local.startrow,tab='PDA',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any disciplinary action reports.")#

        <!--- results table --->
        <cfoutput query="local.qryAllDiscActions">
            <cfquery name="local.qryGetDAForName" datasource="#application.dsn.tlasites_trialsmith.dsn#">
                select count(*) as DiscActionCount,
                    jurisdictions = REPLACE((
                            select distinct a2.state as [data()]
                            from dbo.da_purchasedReportItems AS un2
                            inner join disiplinaryActions.dbo.professional as p2 on p2.professionalID = un2.professionalID
                            left outer join disiplinaryActions.dbo.authority as a2 on a2.authorityID = p2.authorityID
                            where p2.professionalId = un2.professionalID and un2.reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryAllDiscActions.reportID#">
                            FOR XML PATH ('')
                            ), ' ', ', ')

                from dbo.da_purchasedReportItems
                where reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryAllDiscActions.reportID#">
            </cfquery>

            <div class="row-fluid s_row <cfif local.qryAllDiscActions.currentrow mod 2 is 0>s_row_alt</cfif>">
                <div class="span12">
                    <div class="row-fluid">
                        <div class="span10">
                            <i class="icon icon-file-text" style="line-height:23px;"></i><b>
                            <cfif len(companyName)>#local.qryAllDiscActions.companyName#<cfelse>#local.qryAllDiscActions.salutation# #local.qryAllDiscActions.firstname# #local.qryAllDiscActions.middlename# #local.qryAllDiscActions.lastname# #local.qryAllDiscActions.suffix#<cfif len(local.qryAllDiscActions.professionalSuffix)>, #local.qryAllDiscActions.professionalSuffix#</cfif></cfif></b> <div class="s_action">(#local.qryGetDAForName.DiscActionCount# action<cfif local.qryGetDAForName.discActionCount is not 1>s</cfif>)</div>
                            <div class="s_dtl">
                                <div class="hidden-phone">
                                    <cfif len(local.qryAllDiscActions.companyName)>
                                        <b class="s_label">Company Name:</b> #local.qryAllDiscActions.companyName#
                                        <br/>
                                    </cfif>
                                    <cfif len(local.qryAllDiscActions.lastname) GT 0 OR len(local.qryAllDiscActions.firstname) GT 0>								
                                        <b class="s_label">Full Name:</b> #local.qryAllDiscActions.salutation# #local.qryAllDiscActions.firstname# #local.qryAllDiscActions.middlename# #local.qryAllDiscActions.lastname# #local.qryAllDiscActions.suffix# 
                                            <cfif len(local.qryAllDiscActions.professionalSuffix)>, #local.qryAllDiscActions.professionalSuffix#</cfif>
                                            <br/>
                                    </cfif>	
                                    <cfif len(local.qryGetDAForName.jurisdictions)>
                                        <b class="s_label">Jurisdiction<cfif listLen(local.qryGetDAForName.jurisdictions) gt 1>s</cfif>:</b> #local.qryGetDAForName.jurisdictions#<br/>
                                    </cfif>
                                </div>
                                <div align="left"><b class="s_label">Generated:</b> #dateformat(local.qryAllDiscActions.dateEntered,"m/d/yyyy")#</div>
                            </div>
                        </div>
                        <div class="span2 s_act">
                            <div class="s_opt"><a href="javascript:viewDAReport(#local.qryAllDiscActions.reportID#);" title="View Action Report"><i class="icon icon-file-text" style="line-height:23px;"></i> View Report</a></div>
                        </div>
                    </div>
                </div>
            </div>
        </cfoutput>
        #getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllDiscActions.recordCount,itemCount=val(local.qryAllDiscActions.itemCount), docType="discipinary action",maxRows=local.maxRows,startRow=local.startrow,tab='PDA',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any disciplinary action reports.")#
    </div>
</cfoutput>