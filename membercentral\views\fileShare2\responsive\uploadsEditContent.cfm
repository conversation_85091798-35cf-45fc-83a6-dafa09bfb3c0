
<cfoutput>
	<cfsavecontent variable="fileShareHeader">
		<div class="alert alert-info clearfix">
			<strong>#fileShareSettings.applicationInstanceName#</strong>
			<div class="pull-right">
				<cfif StructKeyExists(appRightsStruct, 'fsAddDocuments') AND appRightsStruct.fsAddDocuments eq 1>
					<button class="btn btn-info btn-mini" type="button" onclick="document.location.href='#dataStruct.uploadsURL#&from=browse<cfif dataStruct.parentCategoryID NEQ "">&catID=#dataStruct.parentCategoryID#</cfif>&byT=#dataStruct.byTreeID#';"><i class="icon-upload"></i>Upload file</button>
				</cfif>	
				<div class="btn-group">
					<a class="btn btn-info btn-mini dropdown-toggle" data-toggle="dropdown" href="##">Browsing by #dataStruct.qryCurrentCategoryTree.categoryTreeName# <span class="caret"></span></a>
					<ul class="dropdown-menu">
						<cfloop query="dataStruct.qryGetCategoryTrees">
							<cfif dataStruct.qryGetCategoryTrees.categoryTreeID NEQ dataStruct.byTreeID>
								<li class=""><a href="#dataStruct.browseLink#&byT=#dataStruct.qryGetCategoryTrees.categoryTreeID#">Browse by #dataStruct.qryGetCategoryTrees.categoryTreeName#</a></li>
							</cfif>
						</cfloop>
					</ul>
				</div>
			</div>
		</div>
	</cfsavecontent>
	<cfsavecontent variable="filesharebreadcrumbs">
		<cfif dataStruct.qryGetCategoryPathUp.recordcount>
			<ul class="breadcrumb">
				<li><a href="#dataStruct.browseLink#&byT=#dataStruct.byTreeID#">All #dataStruct.qryCurrentCategoryTree.categoryTreeName#</a></li>
				<cfloop query="dataStruct.qryGetCategoryPathUp" endrow="#dataStruct.qryGetCategoryPathUp.recordcount - 1#">
					<li><span class="divider">/</span><a href="#dataStruct.browseLink#&catID=#dataStruct.qryGetCategoryPathUp.categoryID#&byT=#dataStruct.byTreeID#">#dataStruct.qryGetCategoryPathUp.CategoryName#</a></li>
				</cfloop>
				<cfif dataStruct.parentCategoryID gt 0>
					<li><span class="divider">/</span><strong>#dataStruct.qryGetCategoryPathUp.CategoryName[dataStruct.qryGetCategoryPathUp.recordcount]#</strong></li>
				</cfif>
			</ul>
		</cfif>
	</cfsavecontent>
</cfoutput>

<cfsavecontent variable="javascriptIncludes">
	<cfoutput>
	<script language="javascript" src="/assets/common/javascript/getElementsByClassName.js"></script>
	<script language="JavaScript"> 
		
		function addCategory()
		{
			$.colorbox( {innerWidth:420, innerHeight:275, href:'#dataStruct.baseURL#&fsAction=addCategory&mode=direct', iframe:true, overlayClose:false} );			
		}
		
		function closeBox() { $.colorbox.close(); location.reload();}
		
		function resizeBox() { $.colorbox.resize(); }
		
		function fs2JumpPage (pagenumber)
		{
			baselink = "#dataStruct.uploadsEditLink#&catID=#dataStruct.parentCategoryID#&byT=#dataStruct.byTreeID#&currSort=#dataStruct.currSort#&currOrder=#dataStruct.currOrder#&filter="
			maxPerPage = #dataStruct.MaxPerPage#;
			startRow = ((pagenumber-1) * maxPerPage) + 1;
			$('##fs2pagination-bottom').text('Loading ....');
			window.location.href = baselink + "&startrow=" + startRow;
		}


		<cfif dataStruct.NumTotalPages gt 1>
			$(document).ready(function () {
				$('##fs2pagination-top').twbsPagination({
					totalPages: #dataStruct.NumTotalPages#,
					startPage: #dataStruct.NumCurrentPage#,
					visiblePages: 5,
					first:"<<",
					last:">>",
					next:">",
					prev:"<",
					onPageClick: function (event, page) {
						$('##fs2pagination-top').text('Loading ....');
						fs2JumpPage(page);
					}
				});

				$('##fs2pagination-bottom').twbsPagination({
					totalPages: #dataStruct.NumTotalPages#,
					startPage: #dataStruct.NumCurrentPage#,
					visiblePages: 7,
					first:"<<",
					last:">>",
					next:">",
					prev:"<",
					onPageClick: function (event, page) {
						$('##fs2pagination-bottom').text('Loading ....');
						fs2JumpPage(page);
					}
				});

			});
		</cfif>

	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#javascriptIncludes#">

<cfoutput>
<div class="row-fluid ">
	<div class="span12">
		#fileShareHeader#
	</div>
</div>
</cfoutput>
This page shows a list of documents that need descriptions added to them.

<br/>
<cfoutput>
	<cfif dataStruct.qryGetUploadedDocuments.recordcount>
	<br/>

	<div class="clearfix tsAppBB" style="margin-bottom:20px;">
		<b>#dataStruct.documentCount#</b> file(s) found
		<cfif dataStruct.numTotalPages GT 1>
			<div class="pull-right pagination pagination-small" style="margin:0px 0px;">
				<ul id="fs2pagination-top" style="margin:0px 0px;"></ul>
			</div>
		</cfif>
	</div>
	
	<cfset local.objDocVersions = CreateObject("component","model.admin.common.modules.documentDetails.documentDetails") />
	<table class="table table-striped">
		<cfoutput>
			<cfloop query="dataStruct.qryGetUploadedDocuments">
				<tr>
					<td>
						<cfset icon = dataStruct.getIcon(dataStruct.qryGetUploadedDocuments.fileExt)>
						<cfset description = dataStruct.getFileDescription(dataStruct.qryGetUploadedDocuments.fileExt)>
						<cfif dataStruct.qryGetUploadedDocuments.siteResourceStatusDesc EQ "Active">
							<a href="/docdownload/#dataStruct.qryGetUploadedDocuments.documentID#" target="_blank" title="Click to view document"><img src="#application.paths.images.url#images/fileExts/#icon#" width="16" height="16" border="0" align="left" /></a>&nbsp;&nbsp;<a href="/docdownload/#dataStruct.qryGetUploadedDocuments.documentID#" target="_blank" title="Click to view document"><b>#dataStruct.qryGetUploadedDocuments.docTitle#</b></a> <em>(#description#)</em>
						<cfelse>
							<img src="#application.paths.images.url#images/fileExts/#icon#" width="16" height="16" border="0" align="left" />&nbsp;&nbsp;#dataStruct.qryGetUploadedDocuments.docTitle#</b> <em>(#description#)</em>
						</cfif>						
						<br/>
						<cfif fileShareSettings.showTags EQ "1">
							<strong>Tags:</strong> #dataStruct.getTags(dataStruct.qryGetUploadedDocuments.SiteResourceID, fileShareSettings.showTagsAsLinks,'<span class="label" style="font-weight:normal;">','</span>&nbsp;' )#<br/>
						</cfif>

						<cfloop query="fileShareSettings.customFields">
							<cfset local.columnData = dataStruct.methods.getExtraFSColumnDisplay(dataStruct.qryGetUploadedDocuments.SiteResourceID, fileShareSettings.customFields.columnID) />
							<cfif listFind(fileShareSettings.showCustomFields,fileShareSettings.customFields.columnID) AND len(trim(local.columnData))>
								<cfif fileShareSettings.customFields.columnDesc EQ "Hot Document">
									<strong>#fileShareSettings.customFields.columnDesc#</strong><br />
								<cfelse>
									<strong>#fileShareSettings.customFields.columnDesc#:</strong> #local.columnData#<br/>
								</cfif>
							</cfif>
						</cfloop>					
						
						<cfif val(fileShareSettings.showPublicationDate) AND len(trim(dataStruct.qryGetUploadedDocuments.publicationDate))>
							<strong>Publication Date:</strong> #DateFormat(dataStruct.qryGetUploadedDocuments.publicationDate, "MM/DD/YYYY")#<br />
						</cfif>
						
						<cfif val(fileShareSettings.showAuthor) AND len(trim(dataStruct.qryGetUploadedDocuments.author))>
							<strong>#fileShareSettings.authorLabel#:</strong> #dataStruct.qryGetUploadedDocuments.author#<br />
						</cfif>
						
						<cfif val(fileShareSettings.showContributedBy) AND len(trim(dataStruct.qryGetUploadedDocuments.firstName))>
							<strong>Contributor:</strong> #dataStruct.qryGetUploadedDocuments.firstName# #dataStruct.qryGetUploadedDocuments.lastName#<br />
						</cfif>
						
						<cfif val(fileShareSettings.showFirm) AND len(trim(dataStruct.qryGetUploadedDocuments.company))>
							<strong>Firm:</strong> #dataStruct.qryGetUploadedDocuments.company#<br />
						</cfif>
						
						<cfif val(fileShareSettings.showAddress)>
							<cfset local.address = getAddress(dataStruct.qryGetUploadedDocuments.contributorMemberID)>
							<cfif len(trim(local.address.address1))>
								<strong>Address:</strong> #local.address.address1# #local.address.address2# #local.address.city# , #local.address.code# #local.address.postalCode#<br />
							</cfif>
							<cfif len(trim(local.address.email))>
								<strong>Email:</strong> <A HREF="mailto:#local.address.email#">#local.address.email#</a><br />
							</cfif>
						</cfif>
						
						<cfif fileShareSettings.showVersioning EQ "1">
							<strong>Version(s):</strong> #local.objDocVersions.getDocumentVersions(dataStruct.qryGetUploadedDocuments.documentLanguageID).recordCount#  
							<strong>Created:</strong> #DateFormat(dataStruct.qryGetUploadedDocuments.dateCreated, "MM/DD/YYYY")# #TimeFormat(dataStruct.qryGetUploadedDocuments.dateCreated, "hh:mm tt")#  
							<strong>Revised:</strong> #DateFormat(dataStruct.qryGetUploadedDocuments.dateModified, "MM/DD/YYYY")# #TimeFormat(dataStruct.qryGetUploadedDocuments.dateModified, "hh:mm tt")# 
						</cfif>
						
						<div id="#dataStruct.qryGetUploadedDocuments.SiteResourceID#" style="display:none;">
							<a href="###dataStruct.qryGetUploadedDocuments.SiteResourceID#"></a>
							<cfset local.document = local.objDocVersions.getDocumentVersions(dataStruct.qryGetUploadedDocuments.documentLanguageID) />
							<cfif local.document.recordcount gt 1>
								<table class="table table-condensed" style="margin-top:10px;" border="0">
									<tr><th>Source</th><th>File Name</th><th>Created</th><th>Revised</th></tr>
								<cfloop query="local.document">						
									<cfif local.document.isActive EQ 0>
									<tr>
										<td>#local.document.author#</td>
										<td><a href="/docDownload/#local.document.documentID#&VID=#local.document.documentVersionID#&lang=#local.document.languageCode#" target="_blank" title="Click to view document">#local.document.filename#</a></td>
										<td>#DateFormat(local.document.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.document.dateCreated, "hh:mm tt")#  </td>
										<td>#DateFormat(local.document.dateModified, "MM/DD/YYYY")# #TimeFormat(local.document.dateModified, "hh:mm tt")# </td>
									</tr>
									</cfif>
								</cfloop>
								</table>
							</cfif>
						</div>
						
						<br/><br/>
						<div class="btn-toolbar visible-phone">
							<cfif dataStruct.qryGetUploadedDocuments.siteResourceStatusDesc EQ "Active">
								<button class="btn btn-large" onclick="document.location.href='/docdownload/#dataStruct.qryGetUploadedDocuments.documentID#';" title="Download Document"><i class="icon-cloud-download"></i> &nbsp;View</button>
								<button class="btn btn-large" onclick="document.location.href='mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#dataStruct.qryGetUploadedDocuments.documentID#';" title="Share Document by Email"><i class="icon-share-alt"></i> &nbsp;Email</button>
							</cfif>
						</div>
						<div class="btn-toolbar hidden-phone">
							<cfif dataStruct.qryGetUploadedDocuments.siteResourceStatusDesc EQ "Active">
								<div class="btn-group">
									<cfif canEditAny or (canEditOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetUploadedDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
										<button class="btn btn-small hidden-phone" onclick="document.location.href='#dataStruct.editURL#&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#';"><i class="icon-pencil" title="Edit Document"></i> Edit</button>
									</cfif>
									<button class="btn btn-small" onclick="document.location.href='/docdownload/#dataStruct.qryGetUploadedDocuments.documentID#';"><i class="icon-cloud-download" title="Download Document"></i> View</button>
									<button class="btn btn-small" onclick="document.location.href='mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#dataStruct.qryGetUploadedDocuments.documentID#';" title="Share Document by Email"><i class="icon-share-alt"></i> Email</button>
								</div>
							</cfif>
							<div class="btn-group pull-right">
								<cfif dataStruct.qryGetUploadedDocuments.siteResourceStatusDesc EQ "Active">
									<cfif local.document.recordCount gt 1>
										<button class="btn btn-small" onClick="javascript:togglePriorVersions(#dataStruct.qryGetUploadedDocuments.SiteResourceID#);" title="View Prior Versions"><i class="icon-time"></i></button>
									</cfif>						
									<cfif canDeleteAny or (canDeleteOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetUploadedDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
										<button class="btn btn-small" onClick="javascript:copyDocument('/?#baseQueryString#&fsAction=copyDocument&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#');" title="Copy Document"><i class="icon-copy"></i></button>
										<button class="btn btn-small" onClick="javascript:moveDocument('/?#baseQueryString#&fsAction=moveDocument&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#');" title="Move Document"><i class="icon-move"></i></button>
									</cfif>
									<cfif canDeleteAny or (canDeleteOwn  and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (dataStruct.qryGetUploadedDocuments.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=fileShareSettings.orgID)) )>
										<button class="btn btn-small" onClick="javascript:confirmDelete('/?#baseQueryString#&fsAction=deleteDocument&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#');" title="Delete Document"><i class="icon-remove"></i></button>
									</cfif>
								</cfif>
								<cfif canDeleteAny>
									<cfif dataStruct.qryGetUploadedDocuments.siteResourceStatusDesc EQ "Active">
										<button class="btn btn-small" onClick="javascript:confirmHide('/?#baseQueryString#&fsAction=hideDocument&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#');" title="Hide Document"><i class="icon-eye-close"></i></button>
									<cfelse>
										<button class="btn btn-small" onClick="javascript:confirmUnhide('/?#baseQueryString#&fsAction=unhideDocument&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#');" title="Unhide Document"><i class="icon-eye-open"></i></button>
									</cfif>
								</cfif>
								<cfif dataStruct.qryGetUploadedDocuments.siteResourceStatusDesc EQ "Active">
									<button class="btn btn-small" onclick="document.location.href='#dataStruct.baseURL#&from=browse&panel=sendNote<cfif dataStruct.parentCategoryID NEQ "">&catID=#dataStruct.parentCategoryID#&fsDocumentID=#dataStruct.qryGetUploadedDocuments.documentID#</cfif>&byT=#dataStruct.byTreeID#';" title="Notify Admin"><i class="icon-warning-sign"></i></button>
								</cfif>
							</div>
						</div>
						<br/>					
					</td>			
				</tr>
			</cfloop>
		</cfoutput>
	</table>

	<cfif dataStruct.numTotalPages GT 1>
		<div class="clearfix tsAppBT text-center" style="margin-top:20px;">
			<div class="pagination pagination-small">
				<ul id="fs2pagination-bottom"></ul>
			</div>
		</div>
	</cfif>

	<cfelse>
		<br/>
		All the documents contributed have descriptions defined for them.
	</cfif>
</cfoutput>

