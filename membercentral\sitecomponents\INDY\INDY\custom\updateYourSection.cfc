<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<!--- variables --->
		<cfset variables.orgID = arguments.event.getTrimValue('mc_siteinfo.orgID')>
		<cfset variables.siteID = arguments.event.getTrimValue('mc_siteinfo.siteID')>

		<cfset variables.formName = "frmUpdateSections">
		<cfset variables.formNameDisplay = "Update Your Sections Form">
		<cfset variables.organization = arguments.event.getValue('mc_siteInfo.ORGShortName')>

		<cfset variables.profile_1._profileCode = "INDYCC">
		<cfset variables.profile_1._profileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=variables.profile_1._profileCode)>
		<cfset variables.profile_1._description = "#variables.organization# - #variables.formNameDisplay#">

		<cfscript>
		
		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
				
		local.tmpField = { name="SectionFormTitle", type="STRING", desc="Sections/Divisions Form Title", value="Update Your Sections and Divisions" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = { name="SectionIntroText", type="CONTENTOBJ", desc="Sections/Divisions Intro Text", value="<p>If you would like to add group affiliations, check the box next to the group(s) you wish to join.</p> <p>Basic Membership - Choose the Basic membership option to have access to basic section/division benefits like list-serves, open meetings, social events and section/division communications. Register for section/division programming at standard IndyBar CLE rates.</p> <p>Plus CLE Membership - Choose the Plus CLE membership option to join a section or division AND attend designated Plus CLE programs at no additional cost throughout the calendar year! Each section/division will offer four Plus CLE programs each year.</p>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

		</cfscript>

		<!--- Email confirmation settings --->
		<cfset variables.strEMailSettings_staff = { 
				from="<EMAIL>", 
				to="<EMAIL>", 
				subject=variables.formNameDisplay,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			}>
		<cfset variables.strEMailSettings_member = { 
				from="<EMAIL>", 
				to=session.cfcUser.memberData.email, 
				subject="Thank you for updating your sections/divisions!",
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			}>
		
		<!--- for form action --->
		<cfset variables.applicationReservedURLParams = "fa,sid">
		<cfset variables.baselink = "/?#getBaseQueryString(false)#">

		<cfset variables.activeStudent = application.objCustomPageUtils.mem_getGroups(session.cfcUser.memberData.memberID,variables.orgID,'ActiveStudent')>
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">

		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>
		<cfset local.error = false>

		<cfswitch expression="#arguments.event.getValue('fa','showForm')#">
			<cfcase value="processStep1">
				<cfset local.returnHTML = processStep1(event=arguments.event)>
			</cfcase>

			<cfcase value="processStep2">	
				<cfset processStep2(event=arguments.event)>
			</cfcase>

			<cfcase value="complete">
				<cfif NOT isDefined("session.invoice")>
					<cflocation url="#variables.baselink#" addtoken="false">
				<cfelse>
					<cfset StructDelete(session,"invoice")>
				</cfif>
				
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<div class="BodyText">
						<p>Thank you for updating your sections/divisions. If you have any questions, please contact IndyBar by email at <a href="mailto:<EMAIL>" target="_top"><EMAIL></a> or call (317) 269-2000 during normal business hours.</p>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfdefaultcase>
				<cfset local.qryCurrentSubscriptions = getCurrentSubscriptions()>
				<cfset local.qryAvailableSections = getAvailableSections(qryCurrentSubscriptions=local.qryCurrentSubscriptions, memberID=session.cfcUser.memberData.memberID)>
				
				<cfsavecontent variable="local.headCode">
					<cfoutput>
					<style type="text/css">
						div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
						div.CPSection div.CPSectionTitle {font-size: 14pt;height: auto;font-weight: bold;color: ##ffffff;padding: 10px;background: ##899461;}
						div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }								
						div.frmButtons{ padding:12px; border-top:1px solid ##6C793E; border-bottom:1px solid ##6C793E; }
						.bb { border-bottom:1px solid ##0d3566; }	
						.borderN{border-top: none !important;}	
						.required { color:red; }						
						##divTotals {font-weight:bold;}		
						.mrg0{margin-left: 0 !important}
						.pad10{padding: 10px;}	
						##subbox input{margin: 2px;}
					</style>
					<script type="text/javascript">
						function hideAlert() { $('##issuemsg').html('').hide(); };
						function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };
						
						<cfif variables.activeStudent.recordCount>
							var isStudent = true;
						<cfelse>
							var isStudent = false;
						</cfif>

						function getTotalAmount() {
							var totalAmount = 0;
							var secCount = #local.qryCurrentSubscriptions.recordcount#;
							if (isStudent) {
								secCount = secCount - $('##remsubbox input[type=checkbox]:checked').length;
								$("##remsubbox input[type=checkbox]").not(':checked').each(function(){
									if ($(this).attr('data-canbefree') == 1) secCount--;
								});
							}
							$("##subbox input[type=checkbox]:checked").each(function(){
								if (isStudent && secCount < 2 && $(this).attr('data-canbefree') == 1) {
									secCount++;
								} else {
									totalAmount += parseFloat($(this).attr('data-price')); 
								}
							});
							return totalAmount;
						}

						function checkPrice(cb){
							$('##subbox input[type=checkbox]:checked').each(function(){
								if ($(this).attr('data-chkboxgroup') == cb.attr('data-chkboxgroup') && $(this).attr('id') != cb.attr('id'))
									$(this).attr('checked',false);
							});
							var totalAmount = getTotalAmount();
								totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
								    return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
								});	
							$("##totalAmount").html("$ " + totalAmount);
						}

						function _FB_validateForm() {
							hideAlert();

							$('.submitFrmBtn').attr("disabled", true);	
							$(".submitTxt").show();							

							var arrReq = new Array();
							var numSubsToRemove = $('##remsubbox input[type=checkbox]:checked').length;
							var numSubsToAdd = $('##subbox input[type=checkbox]:checked').length;

							if (numSubsToAdd==0 && numSubsToRemove==0) arrReq[arrReq.length] = 'You did not make any changes to your sections.';

							if (arrReq.length > 0) {
								var msg = 'Please address the following issues:<br/>';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								$('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
								$('.submitFrmBtn').attr("disabled", false);
								$(".submitTxt").hide();	
								return false;
							}
							return true;
						}
						$(document).ready(function(){
							 $(".submitTxt").hide();
						});							
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#local.headCode#">

				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<div id="issuemsg" style="display:none;padding:10px;"></div>
					</cfoutput>

					<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
					<cfinput type="hidden" name="fa" id="fa" value="processStep1">
					<cfoutput>
					<div>
						<h2>#variables.strPageFields.SectionFormTitle#</h2>
						<br/>
						<cfif local.qryCurrentSubscriptions.recordcount>
							<p>Your IndyBar membership for this year includes affiliation with the following section(s) or division(s). 
								To remove a section or division, check the box next to the group name.</p>
							
							<div class="well" id="remsubbox">
								<div class="row-fluid" >
									<cfloop query="local.qryCurrentSubscriptions">
										<div class="form-group">
											<div class="checkbox">
												<input type="checkbox" name="subsToExpire" id="subsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
													value="#local.qryCurrentSubscriptions.subscriberID#" onChange="checkPrice($(this));" 
													data-canbefree="#local.qryCurrentSubscriptions.canBeFree#" 
													data-chkboxgroup="existsectionDivisionGrp#local.qryAvailableSections.subscriptionID#">
													<cfif NOT variables.activeStudent.recordCount>
														<b>#local.qryCurrentSubscriptions.subscriptionName#</b>: #replaceNoCase(local.qryCurrentSubscriptions.rateName,' Renewal','')#
													<cfelse>
														#local.qryCurrentSubscriptions.subscriptionName#
													</cfif>
											</div>  												
										</div>
									</cfloop>
								</div>
							</div>
						
						</cfif>

						<cfif variables.activeStudent.recordCount>
							<p>If you would like to add group affiliations, check the box next to the group(s) you wish to join. Students receive free membership in two sections or divisions free each year with additional groups charged at $25 each.</p>
						<cfelse>
							<div>
								#variables.strPageFields.SectionIntroText#
							</div>
						</cfif>
						<div class="well">
							<div class="row-fluid" id="subbox">
					</cfoutput>				

					<cfset local.i = 0>
					<cfoutput query="local.qryAvailableSections" group="subscriptionID">
						<cfif local.i%3 eq 0 && local.i neq 0>
							</div>
						</cfif>
						<cfif local.i%3 eq 0>
							<div class="span12 mrg0 pad10">
						</cfif>	
						<div class="span4">
							<cfif variables.activeStudent.recordCount>
								<input type="checkbox" data-price="#local.qryAvailableSections.rateAmt#" value="#local.qryAvailableSections.subIDrateID#" 
									data-chkboxgroup="sectionDivisionGrp#local.qryAvailableSections.subscriptionID#" name="newSubs" 
									data-canbefree="#local.qryAvailableSections.canBeFree#" 
									id="sectionDivision#local.qryAvailableSections.subIDrateID#" onChange="checkPrice($(this));"> 
									#local.qryAvailableSections.subscriptionName#
							<cfelse>
								<div><strong>#local.qryAvailableSections.subscriptionName#</strong></div>
								<cfoutput>
									<div>
										<input type="checkbox" data-price="#local.qryAvailableSections.rateAmt#" value="#local.qryAvailableSections.subIDrateID#" 
										data-chkboxgroup="sectionDivisionGrp#local.qryAvailableSections.subscriptionID#" name="newSubs" 
										id="sectionDivision#local.qryAvailableSections.subIDrateID#" onChange="checkPrice($(this));"> 
										#local.qryAvailableSections.rateName#<cfif NOT variables.activeStudent.recordCount> - #dollarFormat(local.qryAvailableSections.rateAmt)#</cfif>
									</div>
								</cfoutput>
							</cfif>
						</div>
						<cfset local.i = local.i+1>											
					</cfoutput>

					<cfoutput>
							</div>
						</div>
					</div>
					<div class="well">
						<div class="row-fluid">
							<div class="span10 BodyTextLarge">
								<b>Amount Due for New Sections/Divisions:</b>
							</div>
							<div class="span2 BodyTextLarge">
								<b><span id="totalAmount">$ 0.00</span></b>
							</div>
						</div>
					</div>
					<div class="frmButtons">
						<div class="row-fluid">
							<div class="span12">
								<button type="submit" class="btn btn-default submitFrmBtn" name="btnToStep2">Continue</button>
						
								<span class="submitTxt">Submission being processed...</span>
							</div>
						</div>
					</div>
					</cfoutput>	
					</cfform>
				</cfsavecontent>
			</cfdefaultcase>
			
		</cfswitch>

		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>	

	<cffunction name="validateSubsToExpire" access="private" output="false" returntype="struct">
		<cfargument name="subsToExpire" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfset local.strReturn.subsToExpire = arguments.subsToExpire>
		<cfif listlen(local.strReturn.subsToExpire)>
			<cfset local.strReturn.qrySubsToExpire = getCurrentSubscriptions(subsToExpire=local.strReturn.subsToExpire)>
			<cfset local.strReturn.subsToExpire = valueList(local.strReturn.qrySubsToExpire.subscriberID)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="validateNewSubs" access="private" output="false" returntype="struct">
		<cfargument name="newSubs" type="string" required="true">
		<cfargument name="subsToExpire" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfset local.strReturn.newSubsAmount = 0>
		<cfset local.strReturn.newSubs = arguments.newSubs>
		<cfif listlen(local.strReturn.newSubs)>
			<cfset local.qryCurrentSubscriptions = getCurrentSubscriptions()>
			<cfset local.strReturn.qrySubsToAdd = getAvailableSections(qryCurrentSubscriptions=local.qryCurrentSubscriptions, newSubs=local.strReturn.newSubs, memberID=session.cfcUser.memberData.memberID)>
			<cfset local.strReturn.newSubs = valueList(local.strReturn.qrySubsToAdd.subIdRateId)>

			<!--- for students, first two subs are free. so change the price of them to be free if needed. --->
			<!--- EXCEPT Indy Attorneys Network which can never be free --->
			<cfif variables.activeStudent.recordcount>
				<cfset local.secCount = local.qryCurrentSubscriptions.recordcount - listLen(arguments.subsToExpire)>
				<cfloop query="local.qryCurrentSubscriptions">
					<cfif NOT listFind(arguments.subsToExpire,local.qryCurrentSubscriptions.subscriberID) AND local.qryCurrentSubscriptions.canbefree is 1>
						<cfset local.secCount = local.secCount - 1>
					</cfif>
				</cfloop>
				<cfloop query="local.strReturn.qrySubsToAdd">
					<cfif local.secCount lt 2 and local.strReturn.qrySubsToAdd.canbefree is 1>
						<cfset querySetCell(local.strReturn.qrySubsToAdd, "rateAmt", 0.00, local.strReturn.qrySubsToAdd.currentrow)>
						<cfset local.secCount = local.secCount + 1>
					</cfif>
				</cfloop>
			</cfif>

			<cfquery name="local.qrySubsToAddTotal" dbtype="query">
				select sum(rateAmt) as total
				from [local].strReturn.qrySubsToAdd
			</cfquery>
			<cfset local.strReturn.newSubsAmount = val(local.qrySubsToAddTotal.total)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="processStep1" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.event.getValue('subsToExpire',''))>
		<cfset local.strNewSubs = validateNewSubs(newSubs=arguments.event.getValue('newSubs',''), subsToExpire=local.strsubsToExpire.subsToExpire)>

		<cfif local.strNewSubs.newSubsAmount gt 0>
			<cfset variables.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=variables.siteID,
																profilecode=variables.profile_1._profileCode, pmid=session.cfcUser.memberData.memberID,
																showCOF=true, usePopup=false, usePopupDIVName='ccForm', autoShowForm=true)>
			<cfif len(variables.profile_1.strPaymentForm.headCode)>
				<cfhtmlhead text="#application.objCommon.minText(variables.profile_1.strPaymentForm.headCode)#">
			</cfif>
		</cfif>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
				div.CPSection div.CPSectionTitle {font-size: 14pt;height: auto;font-weight: bold;color: ##ffffff;padding: 10px;background: ##899461;}
				div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }								
				div.frmButtons{ padding:12px; border-top:1px solid ##6C793E; border-bottom:1px solid ##6C793E; }
				.bb { border-bottom:1px solid ##0d3566; }	
				.borderN{border-top: none !important;}	
				.mrg0{margin-left: 0 !important;}
				.pad10{padding: 10px;}
				##ccform [class^="icon-"], [class*=" icon-"]{
					width: auto !important;
    				height: auto !important;
				}
			</style>
			
			<cfif local.strNewSubs.newSubsAmount gt 0>
				<script type="text/javascript"> 
					function _FB_validateForm() {
						var thisForm = document.forms["#variables.formName#"];
						var arrReq = new Array();
						
						#variables.profile_1.strPaymentForm.jsvalidation#	

						$('.submitFrmBtn').attr("disabled", true);	
					 	$(".submitTxt").show();						
						
						if (arrReq.length > 0) {
							var msg = 'Please address the following issues with your application:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							$('.submitFrmBtn').attr("disabled", false);	
					 		$(".submitTxt").hide();							
							return false;
						}
						return true;
					}					
					
					function hideAlert() { $('##issuemsg').html('').hide(); };
					function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };
					$(document).ready(function(){
						 $(".submitTxt").hide();
					});						
				</script>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<h2>Section/Division Change Confirmation</h2>
			<div class="row-fluid">
				<a id="gotoStep2" class="btn pull-right" href="#variables.baselink#">Back to Previous Step</a>
			</div>
			<div class="row-fluid">
				<p class="BodyText">Confirm your changes below:</p>
			</div>

			<div class="well">
				<div class="row-fluid">
					<cfif listLen(local.strsubsToExpire.subsToExpire)>
						<cfloop query="local.strSubsToExpire.qrySubsToExpire">
							<div class="mrg0 span12">
								REMOVE: #local.strSubsToExpire.qrySubsToExpire.subscriptionName#<cfif NOT variables.activeStudent.recordcount> - #replaceNoCase(local.strSubsToExpire.qrySubsToExpire.rateName," Renewal","","ALL")#</cfif>
							</div>
						</cfloop>
					</cfif>
					<cfif listLen(local.strNewSubs.newSubs)>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<div class="mrg0 span12">
								<div class="span10">ADD: #local.strNewSubs.qrySubsToAdd.subscriptionName#<cfif NOT variables.activeStudent.recordcount> - #local.strNewSubs.qrySubsToAdd.rateName#</cfif></div>
								<div class="span2 pull-right">#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#</div>
							</div>
						</cfloop>
						<cfif local.strNewSubs.newSubsAmount gt 0>
							<div class="mrg0 span12">
								<div class="span10 BodyTextLarge"><b>Amount Due for New Sections/Divisions:</b></div>
								<div class="span2 pull-right BodyTextLarge"><b>#dollarFormat(local.strNewSubs.newSubsAmount)#</b></div>
							</div>
						</cfif>
					</cfif>
				</div>
			</div>

			<cfif local.strNewSubs.newSubsAmount gt 0>
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form BodyText">
						<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
						<cfinput type="hidden" name="fa" id="fa" value="processStep2">
						<cfinput type="hidden" name="subsToExpire" id="subsToExpire" value="#local.strsubsToExpire.subsToExpire#">
						<cfinput type="hidden" name="newSubs" id="newSubs" value="#local.strNewSubs.newSubs#">

 										
						<div class="CPSection">
							<div class="CPSectionTitle BB borderN">Credit Card Information</div>
							<div id="ccForm" class="pad10">
								<div>#variables.profile_1.strPaymentForm.inputForm#</div>
								<div class="pad10">
									<button type="submit" class="btn btn-default submitFrmBtn" name="btnSubmit">Finalize Changes</button>
									<span class="submitTxt">Submission being processed...</span>
								</div>						
							</div>
						</div>
						</cfform>
					</div>
				</div>
			<cfelse>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processStep2">
				<cfinput type="hidden" name="subsToExpire" id="subsToExpire" value="#local.strsubsToExpire.subsToExpire#">
				<cfinput type="hidden" name="newSubs" id="newSubs" value="#local.strNewSubs.newSubs#">
					<div class="CPSection frmRow1 mrg0 pad10 span12">
						<button type="submit" class="btn btn-default" name="btnSubmit">Finalize Changes</button>
					</div>
				</cfform>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep2" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryCurrentSubscriptions = getCurrentSubscriptions()>
		<cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.event.getValue('subsToExpire',''))>
		<cfset local.strNewSubs = validateNewSubs(newSubs=arguments.event.getValue('newSubs',''), subsToExpire=local.strsubsToExpire.subsToExpire)>

		<cfif local.strNewSubs.newSubsAmount gt 0 and NOT val(arguments.event.getTrimValue('p_#variables.profile_1._profileID#_mppid','0'))>
			<cflocation url="#variables.baselink#&fa=showForm&sid=50BA4017-F01F-AF51-C9CCE480104528F0" addtoken="no">
		</cfif>

		<!--- DETERMINE CASE
		A. If a student and NOW is between August 1-April 30
		B. If NOT a student and NOW is between January 1–September 30
		C. If a student and NOW is NOT between August 1-April 30
		D. If NOT a student and NOW is between October 1-December 31
 		--->
		<cfset local.subCase = 'X'>
		<cfif variables.activeStudent.recordcount AND NOT listFind("5,6,7",month(now()))>
			<cfset local.subCase = 'A'>
		<cfelseif NOT variables.activeStudent.recordcount AND NOT listFind("10,11,12",month(now()))>
			<cfset local.subCase = 'B'>
		<cfelseif variables.activeStudent.recordcount AND listFind("5,6,7",month(now()))>
			<cfset local.subCase = 'C'>
		<cfelseif NOT variables.activeStudent.recordcount AND listFind("10,11,12",month(now()))>
			<cfset local.subCase = 'D'>
		</cfif>


		<!--- -------------- --->
		<!--- 1. Delete Subs --->
		<!--- -------------- --->
		<!--- 
		Originally this was to expire the child subs, but there was no way to add the sub back once it had been expired. 
		So we are deleting the child subs instead.
		A. If case C or D and the member has an Renewal Not Sent or Billed membership, then delete the section from the renewal.
		B. If case C or D and the member has an Accepted membership, then add message to confirmation email.
		--->
		<cfset local.strRemoveMessages = structNew()>
		<cfif listLen(local.strsubsToExpire.subsToExpire)>
			<cfset local.objSubAdmin = CreateObject("component","model.admin.subscriptions.subscriptions")>

			<cfif listFindNoCase("C,D",local.subCase)>
				<cfset local.qryBilledSubscriber = getMembershipByStatus(statusCodeList='R,O')>
				<cfset local.qryAcceptedSubscriber = getMembershipByStatus(statusCodeList='P')>
			</cfif>

			<cfloop query="local.strSubsToExpire.qrySubsToExpire">
				<cfset local.strExpireResult = local.objSubAdmin.removeMemberSubscription(actorMemberID=session.cfcuser.memberdata.memberID,
													actorStatsSessionID=session.cfcuser.statsSessionID, memberID=session.cfcuser.memberdata.memberID,
													subscriberID=local.strSubsToExpire.qrySubsToExpire.subscriberID, siteID=variables.siteID,
													AROption='C')>

				<cfif listFindNoCase("C,D",local.subCase) and local.qryBilledSubscriber.subscriberID gt 0>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryBilledSub">
						select top 1 s.subscriberID
						from dbo.sub_subscribers s
						inner join dbo.sub_statuses st on st.statusID = s.statusID
							and st.statusCode in ('R','O')
						inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
							and subs.subscriptionID = #local.strSubsToExpire.qrySubsToExpire.subscriptionID#
							and s.rootSubscriberID = #local.qryBilledSubscriber.subscriberID#
						inner join dbo.sub_types t on t.typeID = subs.typeID
							and t.siteID = #variables.siteID#
							AND t.typeName = 'Sections'
						inner join dbo.ams_members m on m.memberID = s.memberID
							and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
					</cfquery>
					<cfset local.strExpireResult = local.objSubAdmin.removeMemberSubscription(actorMemberID=session.cfcuser.memberdata.memberID,
														actorStatsSessionID=session.cfcuser.statsSessionID, memberID=session.cfcuser.memberdata.memberID,
														subscriberID=local.qryBilledSub.subscriberID, siteID=variables.siteID,
														AROption='C')>

					<cfif local.subCase eq "C" and local.qryCurrentSubscriptions.recordcount gt 2>
						<cfset StructInsert(local.strRemoveMessages,local.strSubsToExpire.qrySubsToExpire.subscriberID,"RENEWAL MAY REQUIRE PRICE CHANGE")>
					</cfif>
				<cfelseif listFindNoCase("C,D",local.subCase) and local.qryAcceptedSubscriber.subscriberID gt 0>
					<cfset StructInsert(local.strRemoveMessages,local.strSubsToExpire.qrySubsToExpire.subscriberID,"NOT REMOVED FROM ACCEPTED SUBSCRIPTION")>
				</cfif>
			</cfloop>
		</cfif>

		<!--- -------------- --->
		<!--- 2. Create Subs --->
		<!--- -------------- --->
		<!---
		Case A. add new section to existing active membership
		Case B. add new section to existing active membership
		Case C.	- if the member has an Renewal Not Sent or Billed membership:
				- add new section to existing active membership, defer to the upcoming Aug 1
				- add to renewal at $0
		   	- if the member has an Accepted membership:
				- add new section to existing active membership at $0
				- add to accepted, defer to upcoming Aug 1
		Case D. 	- if the member has an Renewal Not Sent or Billed membership:
				- add new section to existing active membership, defer to Jan 1 of next year
				- add to renewal at $0
			- if the member has an Accepted membership, 
				- add new section to existing active membership at $0
				- add to accepted, defer to upcoming Jan 1
		--->
		<cfif listLen(local.strNewSubs.newSubs)>
			<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
			<cfset local.arrCreatedSubs = arrayNew(1)>

			<cfswitch expression="#local.subCase#">
				<cfcase value="A">
					<cfset local.qryActiveSubscriber = getMembershipByStatus(statusCodeList='A')>
					<cfif local.qryActiveSubscriber.subscriberID gt 0>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<cfset arrayAppend(local.arrCreatedSubs,
											   local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryActiveSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryActiveSubscriber.statusCode,
														pcFree=local.strNewSubs.qrySubsToAdd.rateAmt is 0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt,
														recordedByMemberID=session.cfcuser.memberdata.memberID, statsSessionID=session.cfcuser.statsSessionID))>
						</cfloop>
					</cfif>
				</cfcase>
				<cfcase value="B">
					<cfset local.qryActiveSubscriber = getMembershipByStatus(statusCodeList='A')>
					<cfif local.qryActiveSubscriber.subscriberID gt 0>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<cfset arrayAppend(local.arrCreatedSubs,
											   local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryActiveSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryActiveSubscriber.statusCode,
														pcFree=0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt, recordedByMemberID=session.cfcuser.memberdata.memberID,
														statsSessionID=session.cfcuser.statsSessionID))>
						</cfloop>
					</cfif>
				</cfcase>
				<cfcase value="C">
					<cfset local.qryActiveSubscriber = getMembershipByStatus(statusCodeList='A')>
					<cfset local.qryBilledSubscriber = getMembershipByStatus(statusCodeList='R,O')>
					<cfset local.qryAcceptedSubscriber = getMembershipByStatus(statusCodeList='P')>

					<cfif local.qryActiveSubscriber.subscriberID gt 0 and local.qryBilledSubscriber.subscriberID gt 0>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<cfset arrayAppend(local.arrCreatedSubs,
											   local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryActiveSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryActiveSubscriber.statusCode,
														pcFree=local.strNewSubs.qrySubsToAdd.rateAmt is 0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt,
														recordedByMemberID=session.cfcuser.memberdata.memberID, statsSessionID=session.cfcuser.statsSessionID, 
														recogStartDate='8/1/#year(now())#', recogEndDate='8/1/#year(now())#'))>
							<cfset local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryBilledSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryBilledSubscriber.statusCode,
														pcFree=0, rateAmt=0, recordedByMemberID=session.cfcuser.memberdata.memberID, 
														statsSessionID=session.cfcuser.statsSessionID)>
						</cfloop>
					<cfelseif local.qryActiveSubscriber.subscriberID gt 0 and local.qryAcceptedSubscriber.subscriberID gt 0>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<cfset arrayAppend(local.arrCreatedSubs,
												local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryActiveSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryActiveSubscriber.statusCode,
														pcFree=0, rateAmt=0, recordedByMemberID=session.cfcuser.memberdata.memberID, 
														statsSessionID=session.cfcuser.statsSessionID))>
							<cfset arrayAppend(local.arrCreatedSubs,
												local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryAcceptedSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryAcceptedSubscriber.statusCode,
														pcFree=local.strNewSubs.qrySubsToAdd.rateAmt is 0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt, 
														recordedByMemberID=session.cfcuser.memberdata.memberID, statsSessionID=session.cfcuser.statsSessionID,
														recogStartDate='8/1/#year(now())#', recogEndDate='8/1/#year(now())#'))>
						</cfloop>
					</cfif>
				</cfcase>
				<cfcase value="D">
					<cfset local.qryActiveSubscriber = getMembershipByStatus(statusCodeList='A')>
					<cfset local.qryBilledSubscriber = getMembershipByStatus(statusCodeList='R,O')>
					<cfset local.qryAcceptedSubscriber = getMembershipByStatus(statusCodeList='P')>

					<cfif local.qryActiveSubscriber.subscriberID gt 0 and local.qryBilledSubscriber.subscriberID gt 0>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<cfset arrayAppend(local.arrCreatedSubs,
											   local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryActiveSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryActiveSubscriber.statusCode,
														pcFree=0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt, recordedByMemberID=session.cfcuser.memberdata.memberID, 
														statsSessionID=session.cfcuser.statsSessionID, recogStartDate='1/1/#year(now())+1#', recogEndDate='1/1/#year(now())+1#'))>
							<cfset local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryBilledSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryBilledSubscriber.statusCode,
														pcFree=0, rateAmt=0, recordedByMemberID=session.cfcuser.memberdata.memberID, statsSessionID=session.cfcuser.statsSessionID)>
						</cfloop>
					<cfelseif local.qryActiveSubscriber.subscriberID gt 0 and local.qryAcceptedSubscriber.subscriberID gt 0>
						<cfloop query="local.strNewSubs.qrySubsToAdd">
							<cfset arrayAppend(local.arrCreatedSubs,
												local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryActiveSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryActiveSubscriber.statusCode,
														pcFree=0, rateAmt=0, recordedByMemberID=session.cfcuser.memberdata.memberID, statsSessionID=session.cfcuser.statsSessionID))>
							<cfset arrayAppend(local.arrCreatedSubs,
												local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.qryAcceptedSubscriber.subscriberID, 
														memberID=session.cfcuser.memberdata.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
														rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.qryAcceptedSubscriber.statusCode,
														pcFree=0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt, recordedByMemberID=session.cfcuser.memberdata.memberID, 
														statsSessionID=session.cfcuser.statsSessionID, recogStartDate='1/1/#year(now())+1#', recogEndDate='1/1/#year(now())+1#'))>
						</cfloop>
					</cfif>
				</cfcase>
			</cfswitch>
		</cfif>

		<!--- ------------------------- --->
		<!--- 3. Payment and accounting --->
		<!--- ------------------------- --->
		<cfif local.strNewSubs.newSubsAmount gt 0>
			<cfset local.strAccTemp = { totalPaymentAmount=local.strNewSubs.newSubsAmount, assignedToMemberID=session.cfcuser.memberdata.memberID, recordedByMemberID=session.cfcuser.memberdata.memberID, rc=arguments.event.getCollection() } >
			<cfset local.strAccTemp.payment = { detail=variables.profile_1._description, amount=local.strAccTemp.totalPaymentAmount, profileID=variables.profile_1._profileID, profileCode=variables.profile_1._profileCode }>
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

			<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>
				<cfloop array="#local.arrCreatedSubs#" index="local.thisSub">
					<cfif local.thisSub.invoiceID gt 0 and QueryAddRow(local.objAccounting.invoicePool)>
						<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.thisSub.invoiceID)>
						<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.thisSub.invoiceProfileID)>
						<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.thisSub.invoiceAmount)>
					</cfif>
				</cfloop>
				<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=session.cfcuser.memberdata.memberID, transactionDate=now())>
			</cfif>
		</cfif>

		<!--- -------------- --->
		<!--- 4. Email Staff --->
		<!--- -------------- --->
		<cfsavecontent variable="local.pageCSS">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.msgHeader{ background:##7a864e; font-weight:bold; padding:5px; }
				.frmText{ font-size:12pt; color:##505050; } 
				.b{ font-weight:bold; }
			</style>
			</cfoutput>
		</cfsavecontent>	

		<cfsavecontent variable="local.invoice">
			<cfoutput>
			<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>

			<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
			<tr class="msgHeader"><td colspan="2" class="b">Member Information</td></tr>
			<tr><td class="frmText b">MemberNumber:</td><td class="frmText">#session.cfcuser.memberdata.membernumber#&nbsp;</td></tr>
			<tr><td class="frmText b">First Name:</td><td class="frmText">#session.cfcUser.memberData.firstName#&nbsp;</td></tr>	
			<tr><td class="frmText b">Last Name:</td><td class="frmText">#session.cfcUser.memberData.lastName#&nbsp;</td></tr>	
			<tr><td class="frmText b">Email:</td><td class="frmText">#session.cfcUser.memberData.email#&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>

			<tr class="msgHeader"><td colspan="2" class="b">Section/Division Changes</td></tr>
			<cfif listLen(local.strsubsToExpire.subsToExpire)>
				<cfloop query="local.strSubsToExpire.qrySubsToExpire">
					<tr>
						<td class="frmText b">Removed</td>
						<td class="frmText">
							#local.strSubsToExpire.qrySubsToExpire.subscriptionName# - #replaceNoCase(local.strSubsToExpire.qrySubsToExpire.rateName," Renewal","","ALL")#
							<cfif structKeyExists(local.strRemoveMessages,local.strSubsToExpire.qrySubsToExpire.subscriberID)>
								<!--MSG#local.strSubsToExpire.qrySubsToExpire.subscriberID#-->
							</cfif>
						</td>
					</tr>
				</cfloop>
			</cfif>
			<cfif listLen(local.strNewSubs.newSubs)>
				<cfloop query="local.strNewSubs.qrySubsToAdd">
					<tr><td class="frmText b">Added</td><td class="frmText">#local.strNewSubs.qrySubsToAdd.subscriptionName# - #local.strNewSubs.qrySubsToAdd.rateName# (#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#)</td></tr>
				</cfloop>
			</cfif>
			<tr><td colspan="2">&nbsp;</td></tr>

			<tr class="msgHeader"><td colspan="2" class="b">Payment Information</td></tr>
			<tr><td class="frmText b"><b>Amount Due for New Sections/Divisions:</b> &nbsp;</td><td class="frmText"><b>#dollarFormat(local.strNewSubs.newSubsAmount)#</b>&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>

			<cfif local.strNewSubs.newSubsAmount gt 0>
				<tr>
					<td class="frmText b">Pay Method:</td><td class="frmText">
						Credit Card
						<cfset arguments.event.setValue('p_#variables.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)))) />
						
						<cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid') gt 0>
							<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
									mppid     = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'),
									memberID  = session.cfcuser.memberdata.memberID,
									profileID = variables.profile_1._profileID)>
							- #local.qrySavedInfoOnFile.detail#
						</cfif>						
					</td>
				</tr>
			</cfif>
			</table>
			</cfoutput>
		</cfsavecontent>
	
		<!--- email submitter --->

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				#local.pageCSS#
				<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
				#local.invoice#	
			</cfoutput>
		</cfsavecontent>

		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.strEMailSettings_member.from },
			emailto=[
				{ name="", email=variables.strEMailSettings_member.to }
			],
			emailreplyto=variables.strEMailSettings_staff.to,
			emailsubject=variables.strEMailSettings_member.subject,
			emailtitle = "#arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay#",
			emailhtmlcontent = local.mailContent,
			siteID = variables.siteID,
			memberID = session.cfcuser.memberdata.memberID,
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		
		<cfset local.emailSentToUser = local.responseStruct.success>

		<cfset local.invoiceForStaff = local.invoice>
		<cfloop collection="#local.strRemoveMessages#" item="local.thisMsg">
			<cfset local.invoiceForStaff = replaceNoCase(local.invoiceForStaff, "<!--MSG#local.thisMsg#-->", "<span style='color:red'><b>#local.strRemoveMessages[local.thisMsg]#</b></span>")>
		</cfloop>

		<!--- email staff --->
		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif NOT local.emailSentToUser>
					<p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
				</cfif>
				#local.pageCSS#
				#local.invoiceForStaff#
			</cfoutput>
		</cfsavecontent>
		<cfscript>
			local.arrEmailTo = [];
			variables.strEMailSettings_staff.to = replace(variables.strEMailSettings_staff.to,",",";","all");
			local.toEmailArr = listToArray(variables.strEMailSettings_staff.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		</cfscript>
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.strEMailSettings_staff.from},
			emailto=local.arrEmailTo,
			emailreplyto=variables.strEMailSettings_staff.from,
			emailsubject=variables.strEMailSettings_staff.subject,
			emailtitle=event.getTrimValue('mc_siteinfo.sitename') & " - " &  variables.formNameDisplay,
			emailhtmlcontent=local.mailContent,
			siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<!--- relocate to message page --->
		<cfset session.invoice = local.invoice>
		
		<cflocation url="#variables.baselink#&fa=complete" addtoken="false">
	</cffunction>

	<cffunction name="getCurrentSubscriptions" access="private" output="false" returntype="query">
		<cfargument name="subsToExpire" type="string" required="false" default="">

		<cfset var memberSubs = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="memberSubs">
			select subscriberID, subscriptionID, subscriptionName, rateName, canBeFree
			from (
				select s.subscriberID, s.subscriptionID, subs.subscriptionName, r.rateName, 0 as canBeFree
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
				inner join dbo.sub_rateFrequencies as rf on rf.RFID = s.RFID
				inner join dbo.sub_rates as r on r.rateID = rf.rateID
				inner join dbo.sub_types t on t.typeID = subs.typeID
					and t.siteID = #variables.siteID#
					AND t.typeName = 'Sections'
				inner join dbo.ams_members m on m.memberID = s.memberID
					and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
					union all
				select s.subscriberID, s.subscriptionID, subs.subscriptionName, r.rateName, 1 as canBeFree
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
					and subs.uid = '0102897C-86E8-43A8-B5DB-A26CCB8EB5E0'
				inner join dbo.sub_rateFrequencies as rf on rf.RFID = s.RFID
				inner join dbo.sub_rates as r on r.rateID = rf.rateID
				inner join dbo.ams_members m on m.memberID = s.memberID
					and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
			) as tmp
			<cfif listlen(arguments.subsToExpire)>
				where subscriberID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subsToExpire#" list="true">)
			</cfif>
			order by subscriptionName
		</cfquery>

		<cfreturn memberSubs>
	</cffunction>

	<cffunction name="getAvailableSections" access="private" output="false" returntype="query">
		<cfargument name="qryCurrentSubscriptions" type="query" required="true">
		<cfargument name="newSubs" type="string" required="false" default="">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.lstExistingSubs = ""> 
		<cfloop query="arguments.qryCurrentSubscriptions">
			<cfset local.lstExistingSubs = listAppend(local.lstExistingSubs, arguments.qryCurrentSubscriptions.subscriptionID)>
		</cfloop>	

		<!--- This is the Indy Attorneys Network rate --->
		<cfset local.attorneysNetworkRateUID = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=arguments.memberID,
																													subscriptionUID="0102897C-86E8-43A8-B5DB-A26CCB8EB5E0",
																													isRenewalRate=0,
																													frequencyShortName="F").rateUID>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAvailableSections" result="local.qryAvailableSectionsResult">
			select *
			from (
				select r.rateID, r.rateName, s.uid, rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, 1 as canBeFree, 
					cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID
				from dbo.sub_subscriptionSets st
				inner join dbo.sub_sets s2 on st.setId = s2.setID
				inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID 
					and s.status = 'A'
				inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
					AND sch.status = 'A'
				inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
					and r.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.frequencyShortName = 'F'
					and f.status = 'A'
				WHERE s2.siteID = #variables.siteID#
				<cfif variables.activeStudent.recordcount>
					AND s2.uid = '43F5E259-75FA-414F-8BFF-81B22F9B8F3C' 
					AND r.uid IN ('FAC0C60E-665F-493C-9367-BFEB92664C1C','95CE3844-6998-47A2-B2E3-D2016E91C8C6')
				<cfelse>
					AND s2.uid = '7FA6870E-CCBC-4F71-9627-A53FA0AF036E' 
					AND r.uid IN ('3248A928-5DA7-4483-B6E7-8EA2C0E657A7','B5997CCB-B025-4793-B7FA-FE98F8E95334','5AD8E6CC-350E-4959-BBE6-379B07F44A0C','06B031DC-8931-453E-9379-52A4F7DD6EEA')
				</cfif>
					union all
				select r.rateID, r.rateName, s.uid, rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, 0 as canBeFree, 
					cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID
				from dbo.sub_subscriptions s
				inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
					AND sch.status = 'A'
				inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
					and r.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.frequencyShortName = 'F'
					and f.status = 'A'
				WHERE s.uid = '0102897C-86E8-43A8-B5DB-A26CCB8EB5E0'
				AND r.uid = <cfqueryparam  value="#local.attorneysNetworkRateUID#" cfsqltype="CF_SQL_VARCHAR">
			) as tmp
			WHERE 1=1
			<cfif listlen(local.lstExistingSubs)>
				AND subscriptionID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.lstExistingSubs#" list="true">)
			</cfif>
			<cfif listlen(arguments.newSubs)>
				and subIDrateID in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.newSubs#" list="true">)
			</cfif>
			order by subscriptionName, subscriptionID, rateName
		</cfquery>

		<cfreturn local.qryAvailableSections>
	</cffunction>

	<cffunction name="getMembershipByStatus" access="private" output="false" returntype="query">
		<cfargument name="statusCodeList" type="string" required="true">

		<cfset var qrySubscriber = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubscriber">
			select top 1 s.subscriberID, st.statusCode
			from dbo.sub_subscribers as s
			inner join dbo.sub_statuses as st on st.statusID = s.statusID 
				AND st.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusCodeList#" list="true">)
			inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
			inner join dbo.sub_types as t on t.typeID = sub.typeID 
				AND t.siteID = #variables.siteID# 
				AND t.uid = '9E90F0A7-B62C-4E1D-B1B2-7347CD3519C9'
			inner join dbo.ams_members as m on m.memberID = s.memberID
				and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			where s.parentSubscriberID is null
			order by s.subscriberID desc
		</cfquery>

		<cfreturn qrySubscriber>
	</cffunction>

</cfcomponent>					
