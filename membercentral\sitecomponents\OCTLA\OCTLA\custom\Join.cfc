<cfcomponent extends="model.customPage.customPage" output="true">

	<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];
            local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ApplicationTitle", type="STRING", desc="Application Title", value="OCTLA Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ApplicationtionIntroText", type="CONTENTOBJ", desc="Application introduction text", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
          	local.tmpField = { name="MemberTypeTitle", type="STRING", desc="Membership Rate Title", value="Membership Type" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipAgreement", type="CONTENTOBJ", desc="Required Membership Agreement", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for Credit Card", value="OCTLACC" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);				
            local.tmpField = { name="SubscriptionUID", type="STRING", desc="UID of the Root Subscription that this form offers", value="e61955ec-a1ea-4964-947b-dc0d12ea2d7d" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page", value="Thank you for submitting your application. This Page has been emailed to the email address on file. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="EmailConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation email", value="Thank you for submitting your application. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="OCTLA - Membership Application Form received" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="OCTLA - Membership Application Form" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);			
            local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberActive", type="CONTENTOBJ", desc="Display message for active members", value="OCTLA records indicate that you are currently a member. Please <a href='/?pg=login'> click here </a> to log in." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberBilled", type="CONTENTOBJ", desc="Display message for billed members", value="You need to renew your OCTLA membership. You will be re-directed to your renewal shortly." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AnnualMembershipUID", type="STRING", desc="Annual Membership UID", value="2ddfdaa0-4ed7-44c5-b964-570c8e9b4c44" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="WorkingCommitteesUID", type="STRING", desc="Working Committees UID", value="f60b5533-e4ba-413c-b513-232d9379679b" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);		
			local.tmpField = { name="WorkingCommitteesRateUID", type="STRING", desc="Membership due UID", value="9ab53bc6-d252-4f37-a507-9f2d70c77db0" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="SubscriptionHelpText", type="CONTENTOBJ", desc="Member Subscription Help Text", value="Select your membership desired." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CommitteeHelpText", type="CONTENTOBJ", desc="Committee Help Text", value="Please select a committee that you are interested in serving on. This is optional." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PracticeAreaHelpText", type="CONTENTOBJ", desc="Practice Area Help Text", value="Select up to 5 practice areas to be included in the membership directory." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AffiliateHelpText", type="CONTENTOBJ", desc="Affiliate Specialty Help Text", value="Select up to 5 specialties to be included in the affiliate directory." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ADRHelpText", type="CONTENTOBJ", desc="ADR Help Text", value="	Select your practice area to be included in the membership directory." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
		
            variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
            
            /* ***************** */
            /* set form defaults */
            /* ***************** */
            StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='join',
                formNameDisplay=variables.strPageFields.ApplicationTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
          
            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));			
            variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
			
			if(session.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = session.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }
	
            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            switch (local.formAction) {
				case "processLookup":
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and not len(arguments.event.getTrimValue('mk',''))) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
				case "processMembershipInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}					
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}

					saveAdditionalFields(rc=arguments.event.getCollection(),event=arguments.event);
					local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
					local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
					application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
					structDelete(session, "formFields");
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup(memberKey=arguments.event.getTrimValue('mk',''));
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>

    </cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">
		
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
		
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##cboxOverlay{
					z-index: 99998 !important;
				}
				##colorbox{
					z-index: 99999 !important;
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
					$(".ContactTypeHolder tr:first-child td").eq(0).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(0).width());
					$(".ContactTypeHolder tr:first-child td").eq(1).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(1).width());
				
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				function addMember(memObj) {
					$.colorbox.close();
					assignMemberData(memObj);
				}

				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				
								<h2>#variables.strPageFields.AccountLocatorTitle#</h2>
				
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="195" style="text-align:center;vertical-align:middle;">					
							<a href="javascript:void(0)" id="btnAddAssoc" class="btnCustom btnBlue" >#variables.strPageFields.AccountLocatorButton#</a>
						</td>
						<td>#variables.strPageFields.AccountLocatorInstructions#</td>
					</tr>
				</table>
				
			</cfform>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
		
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.AnnualMembershipUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.AnnualMembershipUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>

					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.AnnualMembershipUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>	
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>		
		
		<!--- Member Type --->
		<cfset local.contactTypeField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
		
		<!--- Applicant Information --->
		<cfset local.applicantInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='f55be0fb-d7b4-4c26-9de7-df5f21313a70', mode="collection", strData=local.strData)>
		
		<!--- Attorney Information --->
		<cfset local.attorneyInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='ec8ee63c-38a4-448c-8bd6-ee3d334fc4da', mode="collection", strData=local.strData)>
		
		<!--- Law Student Information --->
		<cfset local.lawStudentInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='fe916c16-a457-4f6d-a3f5-22d7e6c4f42b', mode="collection", strData=local.strData)>
		
		<!--- ADR Information --->
		<cfset local.ADRInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='9605d381-f9f1-480c-8bc7-79bfc88e020e', mode="collection", strData=local.strData)>
		
		<!--- Law Staff / Paralegal Information --->
		<cfset local.lawStaffParalegalInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='cdccad00-ecf9-4496-a5d8-0beb1e2aac49', mode="collection", strData=local.strData)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				
				.content input[type="text"] {
					width:206px!important;
				}
				.content select{
					width:220px!important;
				}
			
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationtionIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
							
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
				
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							
			
				div.alert-danger{padding: 10px !important;}				
				
			</style>
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}

				function adjustFieldsetDisplay(contactTypeField) {
					var memType = $(this).find('option:selected').text();
					switch(memType) {
						case 'Attorney (Plaintiff)': 
							showFieldsByContainerClass('attorneyInformationFieldSet');
							resetFormFieldsByContainerClass('lawStudentInformationFieldSetHolder,ADRInformationFieldSetHolder,lawStaffParalegalInformationFieldSetHolder');
							break;
						case 'Attorney (Defense)': 
							showFieldsByContainerClass('attorneyInformationFieldSet');
							resetFormFieldsByContainerClass('lawStudentInformationFieldSetHolder,ADRInformationFieldSetHolder,lawStaffParalegalInformationFieldSetHolder');
							break;
						case 'Law Student':
							showFieldsByContainerClass('lawStudentInformationFieldSet');
							resetFormFieldsByContainerClass('attorneyInformationFieldSetHolder,ADRInformationFieldSetHolder,lawStaffParalegalInformationFieldSetHolder');
							break;
						case 'ADR (Mediators and Arbitrators)': 
							showFieldsByContainerClass('ADRInformationFieldSet');
							resetFormFieldsByContainerClass('attorneyInformationFieldSetHolder,lawStudentInformationFieldSetHolder,lawStaffParalegalInformationFieldSetHolder');
							break;
						case 'Legal Staff': 
							showFieldsByContainerClass('lawStaffParalegalInformationFieldSet');
							resetFormFieldsByContainerClass('attorneyInformationFieldSetHolder,lawStudentInformationFieldSetHolder,ADRInformationFieldSetHolder');
							break;
						case 'Paralegal': 
							showFieldsByContainerClass('lawStaffParalegalInformationFieldSet');
							resetFormFieldsByContainerClass('attorneyInformationFieldSetHolder,lawStudentInformationFieldSetHolder,ADRInformationFieldSetHolder');
							break;
						default:
							showFieldsByContainerClass('');
							break;
					}
				}
				function reloadElements(){
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						var _function = _this.data('function');

						if(typeof _function != "undefined" &&_function.includes('multiSelect')){
							_this.next().remove();
						} 
						eval(_function)();
					});
				}
				function showFieldsByContainerClass(classList)				
				{
					var elementArray = [];
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						elementArray.push(_this);
					});
					
					$(".fieldSetHolder").html('');
					
					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());							
						}			
					});
					reloadElements();
					elementArray.forEach(function(element) {
						var elementId = element.attr('id');
						$("##"+elementId).val(element.val());
					});
				}
				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]).html('');
						});
					}
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.applicantInformationFieldSet.jsValidation#
					if ($('.contactType').val().length == 0) arrReq[arrReq.length] = 'Select your Member Type.';	
					var memType = $('.contactType').val();
			
					if(memType == 'Attorney (Plaintiff)' || memType == 'Attorney (Defense)' )
					{
						<cfloop collection="#local.attorneyInformationFieldSet.STRFIELDS#" item="key">
							<cfset local.attorneyInformationFieldSet.jsValidation = ReplaceNoCase(local.attorneyInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".attorneyInformationFieldSetHolder [name='"& key &"']"").val().length")/>
							
							<cfset local.attorneyInformationFieldSet.jsValidation = ReplaceNoCase(local.attorneyInformationFieldSet.jsValidation, "_CF_this['"& key &"'].options[_CF_this['"& key &"'].selectedIndex].value.length == 0","$("".attorneyInformationFieldSetHolder [name='"& key &"']"").val().length == 0")/>
						</cfloop>
						#local.attorneyInformationFieldSet.jsValidation#
					}
					if(memType == 'Law Student')	
					{
						<cfloop collection="#local.lawStudentInformationFieldSet.STRFIELDS#" item="key">
							<cfset local.lawStudentInformationFieldSet.jsValidation = ReplaceNoCase(local.lawStudentInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".lawStudentInformationFieldSetHolder [name='"& key &"']"").val().length")/>
							
							<cfset local.lawStudentInformationFieldSet.jsValidation = ReplaceNoCase(local.lawStudentInformationFieldSet.jsValidation, "_CF_this['"& key &"'].options[_CF_this['"& key &"'].selectedIndex].value.length == 0","$("".lawStudentInformationFieldSetHolder [name='"& key &"']"").val().length == 0")/>
						</cfloop>
						#local.lawStudentInformationFieldSet.jsValidation#
					}	
					if(memType == 'ADR (Mediators and Arbitrators)')	
					{						
						<cfloop collection="#local.ADRInformationFieldSet.STRFIELDS#" item="key">
							<cfset local.ADRInformationFieldSet.jsValidation = ReplaceNoCase(local.ADRInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".ADRInformationFieldSetHolder [name='"& key &"']"").val().length")/>
							
							<cfset local.ADRInformationFieldSet.jsValidation = ReplaceNoCase(local.ADRInformationFieldSet.jsValidation, "_CF_this['"& key &"'].options[_CF_this['"& key &"'].selectedIndex].value.length == 0","$("".ADRInformationFieldSetHolder [name='"& key &"']"").val().length == 0")/>
						</cfloop>
						#local.ADRInformationFieldSet.jsValidation#
					}		
					if(memType == 'Legal Staff' || memType == 'Paralegal' )
					{
						<cfloop collection="#local.lawStaffParalegalInformationFieldSet.STRFIELDS#" item="key">
							<cfset local.lawStaffParalegalInformationFieldSet.jsValidation = ReplaceNoCase(local.lawStaffParalegalInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".lawStaffParalegalInformationFieldSetHolder [name='"& key &"']"").val().length")/>
							
							<cfset local.lawStaffParalegalInformationFieldSet.jsValidation = ReplaceNoCase(local.lawStaffParalegalInformationFieldSet.jsValidation, "_CF_this['"& key &"'].options[_CF_this['"& key &"'].selectedIndex].value.length == 0","$("".lawStaffParalegalInformationFieldSetHolder [name='"& key &"']"").val().length == 0")/>
						</cfloop>
						#local.lawStaffParalegalInformationFieldSet.jsValidation#
					}

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					if(memType == 'Attorney (Plaintiff)' || memType == 'Attorney (Defense)')
					{
						$(".lawStudentInformationFieldSetHolder").html('');
						$(".ADRInformationFieldSetHolder").html('');
						$(".lawStaffParalegalInformationFieldSetHolder").html('');					
					}
					if(memType == 'Affiliate (Law-Related Business)')
					{											
						$(".attorneyInformationFieldSetHolder").html('');
						$(".lawStudentInformationFieldSetHolder").html('');
						$(".ADRInformationFieldSetHolder").html('');
						$(".lawStaffParalegalInformationFieldSetHolder").html('');			
					}
				
					if(memType == 'Law Student')	
					{
						$(".attorneyInformationFieldSetHolder").html('');
						$(".ADRInformationFieldSetHolder").html('');
						$(".lawStaffParalegalInformationFieldSetHolder").html('');
					}	
					if(memType == 'ADR (Mediators and Arbitrators)')	
					{						
						$(".attorneyInformationFieldSetHolder").html('');
						$(".lawStudentInformationFieldSetHolder").html('');
						$(".lawStaffParalegalInformationFieldSetHolder").html('');
					}		
					if(memType == 'Legal Staff' )
					{
						$(".attorneyInformationFieldSetHolder").html('');
						$(".lawStudentInformationFieldSetHolder").html('');
						$(".ADRInformationFieldSetHolder").html('');
					}
					if(memType == 'Paralegal')	
					{						
						$(".attorneyInformationFieldSetHolder").html('');
						$(".lawStudentInformationFieldSetHolder").html('');
						$(".ADRInformationFieldSetHolder").html('');
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>
					<cfif structKeyExists(local.strData, "contactTypeField")>
						var contactTypeFieldVal = "#local.strData.contactTypeField#";
						$("##contactTypeField").val(contactTypeFieldVal);										
					</cfif>		
					var contactTypeField = $('.contactType');
					$(contactTypeField).change(adjustFieldsetDisplay);
					adjustFieldsetDisplay(contactTypeField);
					
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">				
					<cfinclude template="/model/cfformprotect/cffp.cfm">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.ApplicationTitle)>
						<div class="row-fluid" id="ApplicationTitleId"><h2>#variables.strPageFields.ApplicationTitle#</h2><br/></div>
					</cfif>		
				
					<div id="content-wrapper" class="row-fluid">
						<cfif len(variables.strPageFields.ApplicationtionIntroText)>
							<div class="row-fluid" id="ApplicationtionIntroText">#variables.strPageFields.ApplicationtionIntroText#</div>
						</cfif>				
						<div class="row-fluid">
							<div class="span12 tsAppSectionHeading">#local.applicantInformationFieldSet.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer" id="applicantInformationFieldSet">
								<table cellpadding="3" border="0" cellspacing="0" class="ContactTypeHolder">
									<tbody>
										<tr valign="top">
											<td class="tsAppBodyText" width="10">*&nbsp;</td>
											<td class="tsAppBodyText" nowrap="">Member Type</td>
											<td class="tsAppBodyText">&nbsp;</td>
											<td class="tsAppBodyText">
												<select name="contactTypeField" id="contactTypeField" class="contactType tsAppBodyText">
													<option value="">--- Please Select ---</option>
													<cfloop array="#local.contactTypeField.columnValueArr#" index="local.thisContactType">
														<option value="#local.thisContactType.columnValueString#">#local.thisContactType.columnValueString#</option>>
													</cfloop>
												</select>							
											</td>
										</tr>
									</tbody>
								</table>		
								#local.applicantInformationFieldSet.fieldSetContent#
							</div>
						</div>
						<span class="attorneyInformationFieldSetHolder fieldSetHolder"></span>
						<span class="lawStudentInformationFieldSetHolder fieldSetHolder"></span>
						<span class="ADRInformationFieldSetHolder fieldSetHolder"></span>	
						<span class="lawStaffParalegalInformationFieldSetHolder fieldSetHolder"></span>
						
						<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					</div>
					
				</form>
				
				<span class="attorneyInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="span12 tsAppSectionHeading">#local.attorneyInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">	
						#local.attorneyInformationFieldSet.fieldSetContent#
						</div>
					</div>	
				</span>
				<span class="lawStudentInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="span12 tsAppSectionHeading">#local.lawStudentInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">	
						#local.lawStudentInformationFieldSet.fieldSetContent#															
						</div>
					</div>
				</span>
				<span class="ADRInformationFieldSetWrapper hide">	
					<div class="row-fluid">
						<div class="span12 tsAppSectionHeading">#local.ADRInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">	
						#local.ADRInformationFieldSet.fieldSetContent#																
						</div>
					</div>
				</span>	
				<span class="lawStaffParalegalInformationFieldSetWrapper hide">
					<div class="row-fluid">
						<div class="span12 tsAppSectionHeading">#local.lawStaffParalegalInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">	
						#local.lawStaffParalegalInformationFieldSet.fieldSetContent#																
						</div>
					</div>	
				</span>
				
				
				
				#application.objWebEditor.showEditorHeadScripts()#
				
				<script language="javascript">			
					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>
			
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">
		<cfset var local = structNew()>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>
		
		<cfif session.cfcuser.memberdata.identifiedAsMemberID gt 0 OR application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc,memberID=variables.useMID)>
		<cfelse>
			<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		</cfif>
		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strResult.memberID = session.formFields.step1.memberID>	
		</cfif>
		
		<cfset local.objSaveMember = variables.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>

		<!--- save member info and record history --->		
		<cfset local.objSaveMember.setRecordType(recordType='Individual')>
		<cfset local.objSaveMember.setMemberType(memberType='User')>

		<cfif arguments.rc['contactTypeField'] EQ "Attorney (Plaintiff)" OR arguments.rc['contactTypeField'] EQ "Attorney (Defense)" OR arguments.rc['contactTypeField'] EQ "ADR (Mediators and Arbitrators)">
			<cfset local.strData = {}>
			<cfset local.strData.memberID = local.strResult.memberID>		
			<cfset local.strData.orgID = variables.orgID>
			<cfset local.strData.siteID = variables.siteID>
			<cfset local.strData.p1Visited = 0>
		
			<!--- Attorney Information --->
			<cfset local.attorneyInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='ec8ee63c-38a4-448c-8bd6-ee3d334fc4da', mode="collection", strData=local.strData)>
			<!--- ADR Information --->
 			<cfset local.ADRInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='9605d381-f9f1-480c-8bc7-79bfc88e020e', mode="collection", strData=local.strData)>
		</cfif>

		<!--- Member Type --->
		<cfif structKeyExists(arguments.rc, "contactTypeField") AND listLen(arguments.rc['contactTypeField'])>
			<cfset local.objSaveMember.setCustomField(field='Member Type', value=arguments.rc['contactTypeField'])>
		</cfif>	
		
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			
		<cfif local.strResult.success>			
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = variables.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
  
  	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(siteID=variables.siteID,uid = variables.strPageFields.subscriptionUID)>	
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>		
			<cfset local.strData = session.formFields.step2>
		</cfif>		

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscription">
			SELECT distinct t.typeID, t.TypeName,t.UID,s.SUBSCRIPTIONNAME,s.SUBSCRIPTIONID
			FROM
			sub_types t
			INNER JOIN sub_subscriptions s ON s.typeID = t.typeid
			WHERE s.status = 'A'
			AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			AND s.UID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.subscriptionUID#">
		</cfquery>

		<cfset local.qryRateInfo = variables.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.useMID, subscriptionUID=variables.strPageFields.SubscriptionUID,frequencyShortName='F', isRenewal=false)>
	
		<!--- Practice Areas --->
		<cfset local.practiceAreasField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Practice Areas')>	

		<!--- Affiliate Specialties --->
		<cfset local.affiliateSpecialtiesField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Affiliate Specialties')>

		<!--- ADR Specialties --->
		<cfset local.ADRSpecialtiesField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='ADR Specialties')>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryWorkingCommittees">
			SELECT t.typeID, t.TypeName,t.UID,s.subscriptionID,s.subscriptionName,s.UID as subscriptionUID
			FROM
			sub_types t
			INNER JOIN sub_subscriptions s ON s.typeID = t.typeid
			WHERE s.status = 'A'
			AND t.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.WorkingCommitteesUID#">
			AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			ORDER BY s.subscriptionName
		</cfquery>				 

		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
				 	function afterFormLoad(){
						var _CF_this = document.forms['#variables.formName#'];
						$('html, body').animate({ scrollTop: 0 }, 500);
						$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
						setTimeout(function() {
							$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
						}, 5200);
					}
	 				function validateMembershipInfoForm(){
						 var _CF_this = document.forms['#variables.formName#'];
						var arrReq = new Array();	
						if(!$("input[name='eligibilityRate']:checked").val()){
							arrReq[arrReq.length] = "Please select the Membership.";
						}
						if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
						$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
				</script>
 			</cfoutput>
 		</cfsavecontent>
 		<cfhtmlhead text="#local.resultHtmlHeadText#">
				
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
				<cfif len(variables.strPageFields.MemberTypeTitle)>
					<div class="row-fluid"  id="MemberTypeTitle"><h2>#variables.strPageFields.MemberTypeTitle#</h2><br/></div>
				</cfif>	
						
				<cfform name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
					<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
					
					<cfloop collection="#session.formFields.step1#" item="local.thisField">
						<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
							or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
							or left(local.thisField,5) eq "mccf_" or local.thisField eq "contactTypeField" >
							<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
							<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
						</cfif>
					</cfloop>
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
					<cfinclude template="/model/cfformprotect/cffp.cfm">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">									

								<div>
									<div class="well">
										<legend>#local.qrySubscription.TYPENAME#</legend>
										<cfset local.pos = 1/>
										<p>#variables.strPageFields.SubscriptionHelpText#</p>
										#local.qrySubscription.SUBSCRIPTIONNAME#
										<cfloop query="local.qryRateInfo">
											<label class="radio subRateLabel" for="eligibilityRate#local.pos#">												
												<input type="radio" class="subRateCheckbox" name="eligibilityRate" id="eligibilityRate#local.pos#" value="#local.qryRateInfo.RATEUID#" >

												<span class="labelText">
													#local.qryRateInfo.rateName#
													<span class="joinFrequency joinFrequency_F" >(#dollarFormat(val(local.qryRateInfo.rateAmt))# #local.qryRateInfo.FREQUENCYNAME#)</span>
												</span>
											</label>

											<cfset local.pos = local.pos + 1/>
										</cfloop>
									</div>
								</div>

								<cfif arguments.rc['contactTypeField'] EQ "Attorney (Plaintiff)" OR arguments.rc['contactTypeField'] EQ "Attorney (Defense)">
									<div>
										<div class="well">
											<legend>Working Committees</legend>
											<cfset local.totalCount = local.qryWorkingCommittees.recordCount>	
											<cfset local.numberofRows = Int(local.totalCount/2)+ (local.totalCount MOD 2)>
											
											<p>#variables.strPageFields.CommitteeHelpText#</p>

											<table width="100%" cellpadding="3" border="0" cellspacing="0">	
												<cfloop query="local.qryWorkingCommittees"  startRow="1" endRow="#local.numberofRows#">
													<cfset local.secondColumnCount = local.qryWorkingCommittees.currentRow + local.numberofRows>
													<tr>
													<td width="50%">
														<label class="checkbox subLabel" for="WC#local.qryWorkingCommittees.currentRow#">
															<input class="subCheckbox" type="checkbox" name="workingCommitteesField" id="WC#local.qryWorkingCommittees.currentRow#" value="#local.qryWorkingCommittees.subscriptionUID[local.qryWorkingCommittees.currentRow]#"> 
															#local.qryWorkingCommittees.SUBSCRIPTIONNAME[local.qryWorkingCommittees.currentRow]#
														</label>
													</td> 
													<cfif local.secondColumnCount LTE local.totalCount>
														<td width="50%">
															<label class="checkbox subLabel" for="WC#local.secondColumnCount#">
																<input class="subCheckbox" type="checkbox" name="workingCommitteesField" id="WC#local.secondColumnCount#" value="#local.qryWorkingCommittees.subscriptionUID[local.secondColumnCount]#"> 
																#local.qryWorkingCommittees.SUBSCRIPTIONNAME[local.secondColumnCount]#
															</label>
														</td> 
													</cfif>
													</tr>		

												</cfloop>
											</table>
										</div>
									</div>
									<div>
										<div class="well">
											<legend>Practice Areas</legend>
											<cfset local.totalCount = ArrayLen(local.practiceAreasField.columnValueArr)>	
											<cfset local.numberofRows = Int(local.totalCount/2)+ (local.totalCount MOD 2)>

											<p>#variables.strPageFields.PracticeAreaHelpText#</p>
				
											<table width="100%" cellpadding="3" border="0" cellspacing="0">	
												<cfloop from="1" to="#local.numberofRows#" index="local.currentRow" >
													<cfset local.secondColumnCount = local.currentRow + local.numberofRows>
													<tr>
													<td width="50%">
														<label class="checkbox subLabel" for="pa#local.currentRow#">
															<input class="subCheckbox" type="checkbox" name="practiceAreasField" id="pa#local.currentRow#" value="#local.practiceAreasField.columnValueArr[local.currentRow].columnValueString#"> 
															#local.practiceAreasField.columnValueArr[local.currentRow].columnValueString#
														</label>
													</td> 
													<cfif local.secondColumnCount LTE local.totalCount>
														<td width="50%">
															<label class="checkbox subLabel" for="pa#local.secondColumnCount#">
																<input class="subCheckbox" type="checkbox" name="practiceAreasField" id="pa#local.secondColumnCount#" value="#local.practiceAreasField.columnValueArr[local.secondColumnCount].columnValueString#"> 
																#local.practiceAreasField.columnValueArr[local.secondColumnCount].columnValueString#
															</label>
														</td> 
													</cfif>
													</tr>		

												</cfloop>
											</table>

										</div>
									</div>
								<cfelseif arguments.rc['contactTypeField'] EQ "Affiliate (Law-Related Business)">
									<div>
										<div class="well">
											<legend>Affiliate Specialties</legend>
											<cfset local.totalCount = ArrayLen(local.affiliateSpecialtiesField.columnValueArr)>	
											<cfset local.numberofRows = Int(local.totalCount/2)+ (local.totalCount MOD 2)>

											<p>#variables.strPageFields.AffiliateHelpText#</p>

											<table width="100%" cellpadding="3" border="0" cellspacing="0">	
												<cfloop from="1" to="#local.numberofRows#" index="local.currentRow" >
													<cfset local.secondColumnCount = local.currentRow + local.numberofRows>
													<tr>
													<td width="50%">
														<label class="checkbox subLabel" for="as#local.currentRow#">
															<input class="subCheckbox" type="checkbox" name="affiliateSpecialtiesField" id="as#local.currentRow#" value="#local.affiliateSpecialtiesField.columnValueArr[local.currentRow].columnValueString#"> 
															#local.affiliateSpecialtiesField.columnValueArr[local.currentRow].columnValueString#
														</label>
													</td> 
													<cfif local.secondColumnCount LTE local.totalCount>
														<td width="50%">
															<label class="checkbox subLabel" for="as#local.secondColumnCount#">
																<input class="subCheckbox" type="checkbox" name="affiliateSpecialtiesField" id="as#local.secondColumnCount#" value="#local.affiliateSpecialtiesField.columnValueArr[local.secondColumnCount].columnValueString#"> 
																#local.affiliateSpecialtiesField.columnValueArr[local.secondColumnCount].columnValueString#
															</label>
														</td> 
													</cfif>
													</tr>		

												</cfloop>
											</table>
											
										</div>
									</div>	
								<cfelseif arguments.rc['contactTypeField'] EQ "ADR (Mediators and Arbitrators)">
									<div>
										<div class="well">
											<legend>ADR Specialties</legend>
											<cfset local.totalCount = ArrayLen(local.ADRSpecialtiesField.columnValueArr)>	
											<cfset local.numberofRows = Int(local.totalCount/2)+ (local.totalCount MOD 2)>

											<p>#variables.strPageFields.ADRHelpText#</p>

											<table width="100%" cellpadding="3" border="0" cellspacing="0">	
												<cfloop from="1" to="#local.numberofRows#" index="local.currentRow" >
													<cfset local.secondColumnCount = local.currentRow + local.numberofRows>
													<tr>
													<td width="50%">
														<label class="checkbox subLabel" for="as#local.currentRow#">
															<input class="subCheckbox" type="checkbox" name="ADRSpecialtiesField" id="as#local.currentRow#" value="#local.ADRSpecialtiesField.columnValueArr[local.currentRow].columnValueString#"> 
															#local.ADRSpecialtiesField.columnValueArr[local.currentRow].columnValueString#
														</label>
													</td> 
													<cfif local.secondColumnCount LTE local.totalCount>
														<td width="50%">
															<label class="checkbox subLabel" for="as#local.secondColumnCount#">
																<input class="subCheckbox" type="checkbox" name="ADRSpecialtiesField" id="as#local.secondColumnCount#" value="#local.ADRSpecialtiesField.columnValueArr[local.secondColumnCount].columnValueString#"> 
																#local.ADRSpecialtiesField.columnValueArr[local.secondColumnCount].columnValueString#
															</label>
														</td> 
													</cfif>
													</tr>		

												</cfloop>
											</table>
											
										</div>
									</div>
								</cfif>
							</div>
						</div>
						<div>					
							<div class="well " id="">
								<div>									
									<div class="">
										<label class="checkbox subLabel" for="mccf_verify">
											<input class="subCheckbox" type="checkbox" name="mccf_verify" id="mccf_verify" value="1"> 
											<span class="labelText">#variables.strPageFields.MembershipAgreement#</span>
										</label>
									</div>								
								</div>
							</div>	
						</div>
					</div>
					<button name="btnContinue" type="submit" class="btn-default tsAppBodyButton" onClick="hideAlert();">Continue</button>
					<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
				</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.subscriptionUID)>
				
		<cfset local.strResult = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfif local.strResult.success>			

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
	
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscription">
			SELECT distinct t.typeID, t.TypeName,t.UID,s.SUBSCRIPTIONNAME,s.SUBSCRIPTIONID
			FROM
			sub_types t
			INNER JOIN sub_subscriptions s ON s.typeID = t.typeid
			WHERE s.status = 'A'
			AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			AND s.UID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.subscriptionUID#">
		</cfquery>

		<cfset local.qryRateInfo = variables.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.useMID, subscriptionUID=variables.strPageFields.SubscriptionUID,frequencyShortName='F', isRenewal=false)>
		<cfset local.rateAmt=0/>
		<cfif structKeyExists(arguments.rc, "eligibilityRate") >
			<cfquery name="local.qryRatesSelected" dbtype="query">
				select rateAmt, rateName, FREQUENCYNAME
				from [local].qryRateInfo					
				where RATEUID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rc['eligibilityRate']#">			
			</cfquery>
			<cfset local.rateAmt = local.qryRatesSelected.rateAmt/>			
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryWorkingCommittees">
			SELECT t.typeID, t.TypeName,t.UID,s.subscriptionID,s.subscriptionName,s.UID as subscriptionUID
			FROM
			sub_types t
			INNER JOIN sub_subscriptions s ON s.typeID = t.typeid
			WHERE s.status = 'A'
			AND t.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.WorkingCommitteesUID#">
			AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			ORDER BY s.subscriptionName
		</cfquery>	

		<cfset local.workingCommitteesValuelist = "">
		<cfif structKeyExists(arguments.rc, "workingCommitteesField") AND listLen(arguments.rc['workingCommitteesField'])>			
			<cfloop  query="local.qryWorkingCommittees">
				<cfif listFindNoCase(arguments.rc['workingCommitteesField'], local.qryWorkingCommittees.subscriptionUID)>
					<cfset local.workingCommitteesValuelist = listAppend(local.workingCommitteesValuelist,local.qryWorkingCommittees.subscriptionName)>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.paymentRequired = (local.rateAmt gt 0)>
		<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC]>
		<cfset local.strReturn = 
			variables.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
			
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>
					div.alert-danger{
						padding: 10px !important;
					}
				</style>
				<script type="text/javascript">
					$('##mccfdiv_#variables.strPageFields.ProfileCodePayCC# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
												
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub" or local.thisField eq "contactTypeField" or local.thisField eq "practiceAreasField" or local.thisField eq "affiliateSpecialtiesField" or local.thisField eq "ADRSpecialtiesField" or local.thisField eq  "workingCommitteesField" or local.thisField eq "eligibilityrate">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">
	
			<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Confirmation</div>

				<div class="tsAppSectionContentContainer">
					<cfif local.rateAmt GT 0 >
						<div>
							<div class="">
								<div>
									<strong>#local.qrySubscription.TYPENAME#</strong>
								</div>
								#local.qrySubscription.SUBSCRIPTIONNAME#
								<span class="selectedRate" >
								- #local.qryRatesSelected.rateName# <span class="joinFrequency joinFrequency_F">(#dollarFormat(val(local.qryRatesSelected.rateAmt))# #local.qryRatesSelected.FREQUENCYNAME#)</span>		
								</span>
							</div>
						</div>	
					</cfif>
					<cfif arguments.rc['contactTypeField'] EQ "Attorney (Plaintiff)" OR arguments.rc['contactTypeField'] EQ "Attorney (Defense)">
						
						<cfif structKeyExists(arguments.rc, "workingCommitteesField") AND listLen(arguments.rc['workingCommitteesField'])>
							<br/>
							<div>
								<div class="">								
									<div>
										<strong>Working Committees</strong>
									</div>
									<cfset local.pos=1/>
									<cfloop list="#local.workingCommitteesValuelist#" index="local.workingCommitteesValue" >
										#local.workingCommitteesValue# 
										<cfif listLen(local.workingCommitteesValuelist) NEQ local.pos>, </cfif>
										<cfset local.pos=local.pos+1/>
									</cfloop>									
								</div>
							</div>
						</cfif>
						<cfif structKeyExists(arguments.rc, "practiceAreasField") AND listLen(arguments.rc['practiceAreasField'])>
								<br/>
							<div>
								<div class="">								
									<div>
										<strong>Practice Areas</strong>
									</div>
									<cfset local.pos=1/>
									<cfloop list="#arguments.rc['practiceAreasField']#" index="local.practiceAreasColumnValue">
										#local.practiceAreasColumnValue# 
										<cfif listLen(arguments.rc['practiceAreasField']) NEQ local.pos>, </cfif>
										<cfset local.pos=local.pos+1/>
									</cfloop>									
								</div>
							</div>
						</cfif>
					<cfelseif arguments.rc['contactTypeField'] EQ "Affiliate (Law-Related Business)">
						<cfif structKeyExists(arguments.rc, "affiliateSpecialtiesField") AND listLen(arguments.rc['affiliateSpecialtiesField'])>
							<br/>
							<div>
								<div class="">								
									<div>
										<strong>Affiliate Specialties</strong>
									</div>
									<cfset local.pos=1/>
									<cfloop list="#arguments.rc['affiliateSpecialtiesField']#" index="local.affiliateSpecialtiesColumnValue">
										#local.affiliateSpecialtiesColumnValue# 
										<cfif listLen(arguments.rc['affiliateSpecialtiesField']) NEQ local.pos>, </cfif>
										<cfset local.pos=local.pos+1/>
									</cfloop>								
								</div>
							</div>
						</cfif>
					<cfelseif arguments.rc['contactTypeField'] EQ "ADR (Mediators and Arbitrators)">
						<cfif structKeyExists(arguments.rc, "ADRSpecialtiesField") AND listLen(arguments.rc['ADRSpecialtiesField'])>
							<br/>
							<div>
								<div class="">								
									<div>
										<strong>ADR Specialties</strong>
									</div>
									<cfset local.pos=1/>
									<cfloop list="#arguments.rc['ADRSpecialtiesField']#" index="local.ADRSpecialtiesColumnValue">
										#local.ADRSpecialtiesColumnValue# 
										<cfif listLen(arguments.rc['ADRSpecialtiesField']) NEQ local.pos>, </cfif>
										<cfset local.pos=local.pos+1/>
									</cfloop>								
								</div>
							</div>
						</cfif>
					</cfif>					
				</div>	
			</div>	
			<div class="row-fluid">
				<div class="tsAppSectionHeading">Total Price</div><br/>
				<div class="tsAppSectionContentContainer">						
					#dollarFormat(local.rateAmt)#	
				</div>
			</div>
			<cfif local.paymentRequired>
				<div id="paymentMethodContainer">#local.strReturn.paymentHTML#</div>
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton btn-default" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="saveAdditionalFields" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>		

		<cfset variables.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false)>

		<cfset local.objSaveMember = variables.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID) >
			
		<!--- Practice Area --->
		<cfset local.practiceAreasField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Practice Areas')>	

		<!--- Affiliate Specialties --->
		<cfset local.affiliateSpecialtiesField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Affiliate Specialties')>

		<!--- ADR Specialties --->
		<cfset local.ADRSpecialtiesField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='ADR Specialties')>
		
		<cfif structKeyExists(arguments.rc, "practiceAreasField") AND listLen(arguments.rc['practiceAreasField'])>
			<cfset local.practiceAreasColumnValueIDlist = "">
			<cfloop array="#local.practiceAreasField.columnValueArr#" index="local.thisOption">
				<cfif listFindNoCase(arguments.rc['practiceAreasField'], local.thisOption.columnValueString)>
					<cfset local.practiceAreasColumnValueIDlist = listAppend(local.practiceAreasColumnValueIDlist,local.thisOption.valueID)>
				</cfif>
			</cfloop>
			<cfif listLen(local.practiceAreasColumnValueIDlist)>
				<cfset local.objSaveMember.setCustomField(field='Practice Areas', valueID=local.practiceAreasColumnValueIDlist)>
			</cfif>
		</cfif>

		<cfif structKeyExists(arguments.rc, "affiliateSpecialtiesField") AND listLen(arguments.rc['affiliateSpecialtiesField'])>
			<cfset local.affiliateSpecialtiesColumnValueIDlist = "">
			<cfloop array="#local.affiliateSpecialtiesField.columnValueArr#" index="local.thisOption">
				<cfif listFindNoCase(arguments.rc['affiliateSpecialtiesField'], local.thisOption.columnValueString)>
					<cfset local.affiliateSpecialtiesColumnValueIDlist = listAppend(local.affiliateSpecialtiesColumnValueIDlist,local.thisOption.valueID)>
				</cfif>
			</cfloop>

			<cfif listLen(local.affiliateSpecialtiesColumnValueIDlist)>
				<cfset local.objSaveMember.setCustomField(field='Affiliate Specialties', valueID=local.affiliateSpecialtiesColumnValueIDlist)>
			</cfif>
		</cfif>		
		
		<cfif structKeyExists(arguments.rc, "ADRSpecialtiesField") AND listLen(arguments.rc['ADRSpecialtiesField'])>
			<cfset local.ADRSpecialtiesColumnValueIDlist = "">
			<cfloop array="#local.ADRSpecialtiesField.columnValueArr#" index="local.thisOption">
				<cfif listFindNoCase(arguments.rc['ADRSpecialtiesField'], local.thisOption.columnValueString)>
					<cfset local.ADRSpecialtiesColumnValueIDlist = listAppend(local.ADRSpecialtiesColumnValueIDlist,local.thisOption.valueID)>
				</cfif>
			</cfloop>

			<cfif listLen(local.ADRSpecialtiesColumnValueIDlist)>
				<cfset local.objSaveMember.setCustomField(field='ADR Specialties', valueID=local.ADRSpecialtiesColumnValueIDlist)>
			</cfif>
		</cfif>	
		
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>

		<cfreturn "success"/>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Member Type --->
		<cfset local.contactTypeField = variables.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>

		<!--- Applicant Information --->
		<cfset local.applicantInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='f55be0fb-d7b4-4c26-9de7-df5f21313a70', mode="confirmation", strData=arguments.rc)>
		
		<!--- Attorney Information --->
		<cfset local.attorneyInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='ec8ee63c-38a4-448c-8bd6-ee3d334fc4da', mode="confirmation", strData=arguments.rc)>
		
		<!--- Law Student Information --->
		<cfset local.lawStudentInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='fe916c16-a457-4f6d-a3f5-22d7e6c4f42b', mode="confirmation", strData=arguments.rc)>
		
		<!--- ADR Information --->
		<cfset local.ADRInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='9605d381-f9f1-480c-8bc7-79bfc88e020e', mode="confirmation", strData=arguments.rc)>
		
		<!--- Law Staff / Paralegal Information --->
		<cfset local.lawStaffParalegalInformationFieldSet = variables.objCustomPageUtils.renderFieldSet(uid='cdccad00-ecf9-4496-a5d8-0beb1e2aac49', mode="confirmation", strData=arguments.rc)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscription">
			SELECT distinct t.typeID, t.TypeName,t.UID,s.SUBSCRIPTIONNAME,s.SUBSCRIPTIONID
			FROM
			sub_types t
			INNER JOIN sub_subscriptions s ON s.typeID = t.typeid
			WHERE s.status = 'A'
			AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			AND s.UID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.subscriptionUID#">
		</cfquery>

		<cfset local.qryRateInfo = variables.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberid=variables.useMID, subscriptionUID=variables.strPageFields.SubscriptionUID,frequencyShortName='F', isRenewal=false)>
		<cfset local.rateAmt=0/>
		<cfif structKeyExists(arguments.rc, "eligibilityRate") >
			<cfquery name="local.qryRatesSelected" dbtype="query">
				select rateAmt, rateName, FREQUENCYNAME
				from [local].qryRateInfo					
				where RATEUID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rc['eligibilityRate']#">			
			</cfquery>
			<cfset local.rateAmt = local.qryRatesSelected.rateAmt/>			
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryWorkingCommittees">
			SELECT t.typeID, t.TypeName,t.UID,s.subscriptionID,s.subscriptionName,s.UID as subscriptionUID
			FROM
			sub_types t
			INNER JOIN sub_subscriptions s ON s.typeID = t.typeid
			WHERE s.status = 'A'
			AND t.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#variables.strPageFields.WorkingCommitteesUID#">
			AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			ORDER BY s.subscriptionName
		</cfquery>	

		<cfset local.workingCommitteesValuelist = "">
		<cfif structKeyExists(arguments.rc, "workingCommitteesField") AND listLen(arguments.rc['workingCommitteesField'])>			
			<cfloop  query="local.qryWorkingCommittees">
				<cfif listFindNoCase(arguments.rc['workingCommitteesField'], local.qryWorkingCommittees.subscriptionUID)>
					<cfset local.workingCommitteesValuelist = listAppend(local.workingCommitteesValuelist,local.qryWorkingCommittees.subscriptionName)>
				</cfif>
			</cfloop>
		</cfif>	
			
		<cfset local.paymentRequired = (local.rateAmt gt 0) >
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>		
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.ConfirmationContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.confirmationContentForMemberEmail">
			<cfoutput>
				<cfif len(variables.strPageFields.EmailConfirmationContent)>
					<div class="tsAppSectionContentContainer">
						<div class="span12">#variables.strPageFields.EmailConfirmationContent#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
					<div class="tsAppSectionContentContainer">
						<div class="span12">You have received an application for membership through the online Orange County Trial Lawyers Association membership application form.</br></br></div>
					</div>	
			</cfoutput>
		</cfsavecontent>		

		<cfset local.Name = ""/>
		<cfif structKeyExists(arguments.rc, "m_firstname")>
			<cfset local.Name = arguments.rc['m_firstname']/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(arguments.rc, "m_lastname")>
			<cfset local.Name = local.Name & arguments.rc['m_lastname']/>
		</cfif>		
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.applicantInformationFieldSet.fieldSetContent#			

				<cfif structKeyExists(arguments.rc, "contactTypeField")>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tbody>
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;width: 1%;white-space: nowrap;" nowrap="">Member Type: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333">
									<cfif listLen(arguments.rc['contactTypeField'])>
										<cfset local.contactTypelist = "">
										<cfloop array="#local.contactTypeField.columnValueArr#" index="local.thisOption">
											<cfif listFindNoCase(arguments.rc['contactTypeField'], local.thisOption.columnValueString)>
												<cfset local.contactTypelist = listAppend(local.contactTypelist,local.thisOption.columnValueString)>
											</cfif>
										</cfloop>
										#local.contactTypelist#
									</cfif>											
								</td>
							</tr>
						</tbody>
					</table>
					<br/>
				
					<cfif arguments.rc['contactTypeField'] EQ "Attorney (Plaintiff)" OR arguments.rc['contactTypeField'] EQ "Attorney (Defense)">								
						#local.attorneyInformationFieldSet.fieldSetContent#
					<cfelseif arguments.rc['contactTypeField'] EQ "Law Student">
						#local.lawStudentInformationFieldSet.fieldSetContent#
					<cfelseif arguments.rc['contactTypeField'] EQ "ADR (Mediators and Arbitrators)">
						#local.ADRInformationFieldSet.fieldSetContent#
					<cfelseif arguments.rc['contactTypeField'] EQ "Legal Staff">
						#local.lawStaffParalegalInformationFieldSet.fieldSetContent#
					<cfelseif arguments.rc['contactTypeField'] EQ "Paralegal">
						#local.lawStaffParalegalInformationFieldSet.fieldSetContent#
					</cfif>
				</cfif>
		
				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif local.rateAmt GT 0 >
								<div>
									<div class="">
										<div>
											<strong>#local.qrySubscription.TYPENAME#</strong>
										</div>
										#local.qrySubscription.SUBSCRIPTIONNAME#
										<span class="selectedRate" >
										- #local.qryRatesSelected.rateName# <span class="joinFrequency joinFrequency_F">(#dollarFormat(val(local.qryRatesSelected.rateAmt))# #local.qryRatesSelected.FREQUENCYNAME#)</span>		
										</span>
									</div>
								</div>	
							</cfif>
							<cfif arguments.rc['contactTypeField'] EQ "Attorney (Plaintiff)" OR arguments.rc['contactTypeField'] EQ "Attorney (Defense)">
								
								<cfif structKeyExists(arguments.rc, "workingCommitteesField") AND listLen(arguments.rc['workingCommitteesField'])>
									<br/>
									<div>
										<div class="">								
											<div>
												<strong>Working Committees</strong>
											</div>
											<cfset local.pos=1/>
											<cfloop list="#local.workingCommitteesValuelist#" index="local.workingCommitteesValue" >
												#local.workingCommitteesValue# 
												<cfif listLen(local.workingCommitteesValuelist) NEQ local.pos>, </cfif>
												<cfset local.pos=local.pos+1/>
											</cfloop>									
										</div>
									</div>
								</cfif>

								<cfif structKeyExists(arguments.rc, "practiceAreasField") AND listLen(arguments.rc['practiceAreasField'])>
										<br/>
									<div>
										<div class="">								
											<div>
												<strong>Practice Areas</strong>
											</div>
											<cfset local.pos=1/>
											<cfloop list="#arguments.rc['practiceAreasField']#" index="local.practiceAreasColumnValue">
												#local.practiceAreasColumnValue# 
												<cfif listLen(arguments.rc['practiceAreasField']) NEQ local.pos>, </cfif>
												<cfset local.pos=local.pos+1/>
											</cfloop>									
										</div>
									</div>
								</cfif>
							<cfelseif arguments.rc['contactTypeField'] EQ "Affiliate (Law-Related Business)">
								<cfif structKeyExists(arguments.rc, "affiliateSpecialtiesField") AND listLen(arguments.rc['affiliateSpecialtiesField'])>
									<br/>
									<div>
										<div class="">								
											<div>
												<strong>Affiliate Specialties</strong>
											</div>
											<cfset local.pos=1/>
											<cfloop list="#arguments.rc['affiliateSpecialtiesField']#" index="local.affiliateSpecialtiesColumnValue">
												#local.affiliateSpecialtiesColumnValue# 
												<cfif listLen(arguments.rc['affiliateSpecialtiesField']) NEQ local.pos>, </cfif>
												<cfset local.pos=local.pos+1/>
											</cfloop>								
										</div>
									</div>
								</cfif>
							<cfelseif arguments.rc['contactTypeField'] EQ "ADR (Mediators and Arbitrators)">
								<cfif structKeyExists(arguments.rc, "ADRSpecialtiesField") AND listLen(arguments.rc['ADRSpecialtiesField'])>
									<br/>
									<div>
										<div class="">								
											<div>
												<strong>ADR Specialties</strong>
											</div>
											<cfset local.pos=1/>
											<cfloop list="#arguments.rc['ADRSpecialtiesField']#" index="local.ADRSpecialtiesColumnValue">
												#local.ADRSpecialtiesColumnValue# 
												<cfif listLen(arguments.rc['ADRSpecialtiesField']) NEQ local.pos>, </cfif>
												<cfset local.pos=local.pos+1/>
											</cfloop>								
										</div>
									</div>
								</cfif>
								
							</cfif>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.rateAmt)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;border-top: none!important;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->		
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=variables.orgid)>
			<cfloop query="local.qryOrgEmailTypes">
				<cfif local.qryOrgEmailTypes.emailType eq "Email" and StructKeyExists(arguments.rc,'me_#local.qryOrgEmailTypes.emailTypeID#_email')>
					<cfset variables.memberEmail.TO = arguments.rc["me_#local.qryOrgEmailTypes.emailTypeID#_email"] >
					<cfbreak>
				</cfif>				
			</cfloop>
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMemberEmail)>
	
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="Thank you for your application to OCTLA",
			emailhtmlcontent=local.confirmationHTMLToMember,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>	
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#arguments.rc.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
			<div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
	
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & ' - From: ' & Trim(local.Name)>
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = variables.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to OCTLA", emailContent=local.confirmationHTMLToStaff)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>
					
				</style>
				</head>
				<body>
					#local.confirmationHTML#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
		
		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div class="tsAppSectionHeading">Submission Complete</div>			
								
				<div class="tsAppSectionContentContainer">
					<cfif len(variables.strPageFields.ConfirmationContent)>	
						<div class="span12">#variables.strPageFields.ConfirmationContent#</br></br></div>
					</cfif>
					#arguments.confirmationHTML#							
				</div>					
			
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>			
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.MemberActive#
						<script type="text/javascript">
							<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
								window.location = "/?pg=MyOCTLA";
							</cfif>						
						</script>
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.MemberActive#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						#variables.strPageFields.MemberBilled#
						<script type="text/javascript">
							setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
							function AJAXRenewSub(member){
								var redirect = function(r) {
									redirectLink = '/renewsub/' + r.data.directlinkcode[0];
									window.location = redirectLink;								
								};		
								
								var params = { memberID:member, status:'O', distinct:false };
								TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
							}						
						</script>
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
  </cfcomponent>