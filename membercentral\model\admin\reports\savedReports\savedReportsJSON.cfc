<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getSavedReports" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			arguments.event.paramValue('kw','');
			arguments.event.paramValue('crdateStart','');
			arguments.event.paramValue('crdateEnd','');
			arguments.event.paramValue('cbIDList','');
			arguments.event.paramValue('rtIDList','');
			arguments.event.paramValue('lrdateStart','');
			arguments.event.paramValue('lrdateEnd','');
			arguments.event.paramValue('showInactiveReports',0);

			if (len(trim(arguments.event.getValue('cbIDList'))))
				arguments.event.setValue('dsp','');
			else
				arguments.event.paramValue('dsp','all');
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"sr.reportName #arguments.event.getValue('orderDir')#, tt.toolType #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"logData.lastRunDate #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"sr.dateCreated #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qrySavedReports" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @totalCount int, @siteID int, @memberID int;

			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;

			IF OBJECT_ID('tempdb..##tmpRpt') IS NOT NULL
				DROP TABLE ##tmpRpt;

			SELECT ast.siteResourceID, sr.reportID, tt.toolDesc, tt.toolType, sr.reportName, sr.dateCreated, 
				mActive.firstname, mActive.lastname, mActive.memberid, logData.lastRunDate, sr.Status,
				logData.lastRunFirstName, logData.lastrunLastName,
				ROW_NUMBER() OVER (ORDER BY #local.orderBy#) AS row
			INTO ##tmpRpt
			FROM dbo.rpt_SavedReports sr
			INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = sr.toolTypeID
				AND sr.siteID = @siteID
				AND tt.includeInAllReportsGrid = 1
			INNER JOIN admin_siteTools ast ON ast.toolTypeID = sr.toolTypeID
				AND ast.siteID = sr.siteID
			INNER JOIN dbo.ams_members AS m ON m.memberid = sr.memberid
			INNER JOIN dbo.ams_members AS mActive ON mActive.memberid = m.activememberID
			OUTER APPLY (
				SELECT runlog.dateRun AS lastRunDate, mRunActive.firstname as lastRunFirstName, mRunActive.lastname as lastrunLastName
				FROM (
					SELECT max(logID) AS lastRunID
					FROM platformstatsMC.dbo.rpt_runLog 
					WHERE reportID = sr.reportID
				) AS tmp
				INNER JOIN platformstatsMC.dbo.rpt_runLog AS runlog ON runlog.logID = tmp.lastRunID
				INNER JOIN dbo.ams_members AS mRun ON mRun.memberid = runlog.memberID
				INNER JOIN dbo.ams_members AS mRunActive ON mRunActive.memberid = mRun.activememberID
			) AS logData
			WHERE 1=1
			<cfif arguments.event.getValue('dsp') neq "all" and not len(trim(arguments.event.getValue('cbIDList')))>
				AND mActive.memberid = @memberID
			</cfif>
			<cfif arguments.event.getValue('dsp') neq "all" and len(trim(arguments.event.getValue('cbIDList')))>
				AND mActive.memberid IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('cbIDList')#" list="true">)
			</cfif>
			<cfif len(arguments.event.getTrimValue('crdateStart'))>
				AND sr.dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('crdateStart')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('crdateEnd'))>
				AND sr.dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('crdateEnd')# 23:59:59.997">
			</cfif>
			<cfif val(arguments.event.getValue('rtIDList'))>
				AND sr.toolTypeID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('rtIDList')#" list="true">)
			</cfif>		
			<cfif len(arguments.event.getTrimValue('kw'))>
				AND lower(isNull(sr.reportName,'') + ' ' + isNull(tt.toolDesc,'') + ' ' + isNull(mActive.firstname,'') + ' ' + isNull(mActive.middlename,'') + ' ' + isNull(mActive.lastname,'')) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('kw')#%">
			</cfif>
			<cfif len(arguments.event.getTrimValue('lrdateStart'))>
				AND logData.lastRunDate >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('lrdateStart')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('lrdateEnd'))>
				AND logData.lastRunDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('lrdateEnd')# 23:59:59.997">
			</cfif>
			<cfif not arguments.event.getValue('showInactiveReports')>
				AND sr.Status = 'A'
			</cfif>;

			SET @totalCount = @@rowcount;

			SELECT siteResourceID, reportID, toolDesc, toolType, reportName, dateCreated, firstname, lastname, memberid, lastRunDate, Status,
				lastRunFirstName, lastrunLastName, row, dbo.fn_cache_perms_getResourceRightsXML(siteResourceID, @memberID, @siteID) AS reportPerms,
				@totalCount AS totalCount
			FROM ##tmpRpt
			WHERE row > #arguments.event.getValue('posStart')# AND row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
			ORDER by row;
	
			IF OBJECT_ID('tempdb..##tmpRpt') IS NOT NULL 
				DROP TABLE ##tmpRpt;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qrySavedReports">
			<cfset local.statusChangeTo = ''>
			<cfswitch expression="#local.qrySavedReports.Status#">
				<cfcase value="A">
					<cfset local.Status = "Active">
					<cfset local.statusChangeTo = 'I'>
					<cfset local.SwitchText = "Inactivate Report">
				</cfcase>
				<cfcase value="I">
					<cfset local.Status = "Inactive">
					<cfset local.statusChangeTo = 'A'>
					<cfset local.SwitchText = "Activate Report">
				</cfcase>
				<cfdefaultcase>
					<cfset local.Status = "N/A">
				</cfdefaultcase>
			</cfswitch> 
			<cfset local.deleteAnyReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='DeleteAnyReport']/@allowed)")>	
			<cfset local.deleteOwnReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='DeleteOwnReport']/@allowed)")>
			<cfset local.editAnyReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='EditAnyReport']/@allowed)")>
			<cfset local.editOwnReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='EditOwnReport']/@allowed)")>
			<cfset local.runAnyReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='RunAnyReport']/@allowed)")>
			<cfset local.runOwnReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='RunOwnReport']/@allowed)")>			

			<cfset local.reportLink = buildLinkToTool(toolType='#local.qrySavedReports.toolType#',mca_ta='showReport')>

			<cfset local.tmpStr = {
				"reportid": local.qrySavedReports.reportID,
				"status": local.Status,
				"reportname": local.qrySavedReports.reportName,
				"tooldesc": local.qrySavedReports.toolDesc,
				"creatorname": "#local.qrySavedReports.firstname# #local.qrySavedReports.lastName#",
				"lastrunname": "#local.qrySavedReports.lastrunFirstName# #local.qrySavedReports.lastrunLastName#",
				"lastrundatetime": "#len(local.qrySavedReports.lastRunDate) gt 0 ? dateTimeFormat(local.qrySavedReports.lastRunDate,'m/d/yy h:nn tt') & ' CT' : ''#",
				"createddatetime": "#dateTimeFormat(local.qrySavedReports.dateCreated,'m/d/yy h:nn tt')# CT",
				"tooltype": local.qrySavedReports.toolType,
				"reporturl": "#local.reportLink#&rptID=#local.qrySavedReports.reportID#",
				"statuschangeto": local.statusChangeTo,
				"viewrights": val(local.runAnyReport) or (val(local.runOwnReport) and local.qrySavedReports.memberID eq session.cfcuser.memberdata.memberID),
				"editrights": val(local.editAnyReport) or (val(local.editOwnReport) and local.qrySavedReports.memberID eq session.cfcuser.memberdata.memberID),
				"deleterights": val(local.deleteAnyReport) or (val(local.deleteOwnReport) and local.qrySavedReports.memberID eq session.cfcuser.memberdata.memberID),
				"switchtext": local.SwitchText,
				"DT_RowId": "row_#local.qrySavedReports.reportID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySavedReports.totalCount),
			"recordsFiltered": val(local.qrySavedReports.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSavedReportsAuditLog" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.arrAudit = CreateObject('component', 'model.system.platform.auditLog').getSavedReportsAuditLog(
			siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			keywords=arguments.event.getValue('fDescription',''),
			dateFrom=arguments.event.getValue('fDateFrom',''),
			dateTo=arguments.event.getValue('fDateTo',''),
			limit=50
		).arrValue>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop array="#local.arrAudit#" item="local.thisAuditEntry" index="local.index">
			<cfif NOT listFind(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
			</cfif>
			<cfset local.actionDate = parseDateTime(local.thisAuditEntry["ACTIONDATE"])>

			<cfset local.arrData.append({
				"date": "#DateFormat(local.actionDate, "m/d/yy")# #TimeFormat(local.actionDate, "h:mm tt")#",
				"memberid": local.thisAuditEntry["ACTORMEMBERID"],
				"description": replace(local.thisAuditEntry["MESSAGE"],"#chr(13)##chr(10)#","<br/>","ALL"),
				"DT_RowId": "auditlogrow_#local.index#"
			})>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
				WHERE m.memberID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberIDList#" list="true">);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>

			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["actor"] = structKeyExists(strMemberName, thisData.memberid) ? strMemberName["#thisData.memberid#"] : "";
				return thisData;
			})>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal":  arrayLen(local.arrData),
			"recordsFiltered":  arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getScheduledReports" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"tt.toolDesc #arguments.event.getValue('orderDir')#, sr.reportAction #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"sr.nextRunDate #arguments.event.getValue('orderDir')#, sr.interval #arguments.event.getValue('orderDir')#, stt.[name] #arguments.event.getValue('orderDir')#")>
		<cfset local.orderBy = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryScheduledReports" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpScheduledReports') IS NOT NULL
				DROP TABLE ##tmpScheduledReports;
			CREATE TABLE ##tmpScheduledReports (itemID int PRIMARY KEY, reportID int, reportType varchar(200), reportAction varchar(10), 
				reportName varchar(200), toolType varchar(100), interval int, intervalTypeName varchar(10), nextRunDate datetime, endRunDate datetime, 
				toEmail varchar(200), row int);

			DECLARE @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpScheduledReports (itemID, reportID, reportType, reportAction, reportName, toolType, interval, intervalTypeName, nextRunDate, endRunDate, toEmail, row)
			SELECT DISTINCT sr.itemID, sr.reportID, tt.toolDesc, case sr.reportAction when 'customcsv' then 'CSV' else 'PDF' end,
				rpt.reportName, tt.toolType, sr.interval, case when sr.interval = 1 then stt.singular else stt.[name] end, sr.nextRunDate, sr.endRunDate, 
				sr.toEmail, ROW_NUMBER() OVER (ORDER BY #local.orderBy#) AS row
			FROM dbo.rpt_scheduledReports AS sr
			INNER JOIN dbo.rpt_SavedReports AS rpt ON rpt.reportID = sr.reportID
				AND sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = rpt.toolTypeID
			INNER JOIN dbo.scheduledTaskIntervalTypes AS stt ON stt.intervalTypeID = sr.intervalTypeID
			<cfif len(local.searchValue)>
				WHERE (
					tt.toolDesc LIKE @searchValue
					OR sr.reportAction LIKE @searchValue
					OR rpt.reportName LIKE @searchValue
					OR sr.toEmail LIKE @searchValue
				)
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT itemID, reportID, reportType, reportAction, reportName, toolType, interval, intervalTypeName, nextRunDate, endRunDate, 
				replace(toEmail,';','; ') as toEmail, @totalCount AS totalCount
			FROM ##tmpScheduledReports
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpScheduledReports') IS NOT NULL
				DROP TABLE ##tmpScheduledReports;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryScheduledReports">
			<cfset local.reportLink = buildLinkToTool(toolType='#local.qryScheduledReports.toolType#',mca_ta='showReport')>

			<cfset local.tmpStr = {
				"itemid": local.qryScheduledReports.itemID,
				"reportid": local.qryScheduledReports.reportID,
				"reporttype": local.qryScheduledReports.reportType,
				"reportaction": local.qryScheduledReports.reportAction,
				"reportname": local.qryScheduledReports.reportName,
				"reporturl": "#local.reportLink#&rptID=#local.qryScheduledReports.reportID#",
				"interval": local.qryScheduledReports.interval,
				"intervaltypename": local.qryScheduledReports.intervalTypeName,
				"nextrundate": DateTimeFormat(local.qryScheduledReports.nextRunDate,"m/d/yy h:nn tt"),
				"endrundate": DateTimeFormat(local.qryScheduledReports.endRunDate,"m/d/yy h:nn tt"),
				"toemail": local.qryScheduledReports.toEmail,
				"DT_RowId": "row_#local.qryScheduledReports.itemID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryScheduledReports.totalCount),
			"recordsFiltered": val(local.qryScheduledReports.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReportsForCalendar" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.siteID = arguments.event.getValue('mc_siteinfo.siteID')>
		<cfset local.orgID = arguments.event.getValue('mc_siteinfo.orgID')>
		<cfset local.start = arguments.event.getValue('start',0)>
		<cfset local.end = arguments.event.getValue('end',0)>

		<cfquery name="local.qryReports" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">,
					@startDate datetime = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#dateformat(local.start,"m/d/yyyy")#">,
					@endDate datetime = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#dateformat(local.end,"m/d/yyyy")# 23:59:59.997">,
					@nowDate datetime = getdate(),
					@sysMemberID int;
				
				IF OBJECT_ID('tempdb..##tmpCalEntries') IS NOT NULL
					DROP TABLE ##tmpCalEntries;
				IF OBJECT_ID('tempdb..##tmpRecurringReports') IS NOT NULL
					DROP TABLE ##tmpRecurringReports;
				CREATE TABLE ##tmpCalEntries (rowID int IDENTITY(1,1), reportID int, sendDate datetime, sendTime varchar(8));
				CREATE TABLE ##tmpRecurringReports (rowID int IDENTITY(1,1), reportID int, scheduleDate datetime, interval int, SQLDatepart varchar(2), missedIntervals int, initialIntervalToAdvance int);
				
				SELECT @sysMemberID = dbo.fn_ams_getMCSystemMemberID();
				
				-- dates earlier than NOW (individual scheduled reports that have been sent)
				INSERT INTO ##tmpCalEntries(reportID, sendDate, sendTime)
				SELECT l.reportID, dateRun, FORMAT(dateRun, 'hh:mm tt')
				FROM platformStatsMC.dbo.rpt_runLog AS l
				INNER JOIN dbo.rpt_SavedReports AS r ON r.reportID = l.reportID
					AND r.siteID = @siteID
				WHERE l.memberID = @sysMemberID
				AND l.dateRun BETWEEN @startDate AND @endDate;

				-- scheduled and recurring reports
				INSERT INTO ##tmpRecurringReports (reportID, scheduleDate, interval, SQLDatepart)
				SELECT sr.reportID, sr.nextRunDate, sr.interval, stt.SQLDatepart
				FROM dbo.rpt_scheduledReports AS sr
				INNER JOIN dbo.scheduledTaskIntervalTypes AS stt ON stt.intervalTypeID = sr.intervalTypeID
				WHERE sr.siteID = @siteID
				AND sr.nextRunDate IS NOT NULL
				AND sr.nextRunDate <= @endDate
				AND (sr.endRunDate IS NULL OR (sr.endRunDate IS NOT NULL AND sr.nextRunDate <= sr.endRunDate))
				ORDER BY sr.nextRunDate;

				-- next run dates for scheduled reports
				INSERT INTO ##tmpCalEntries(reportID, sendDate, sendTime)
				SELECT reportID, scheduleDate, FORMAT(scheduleDate, 'hh:mm tt')
				FROM ##tmpRecurringReports
				WHERE scheduleDate > @nowDate;

				UPDATE ##tmpRecurringReports
				SET missedIntervals = (
						case SQLDatepart
							when 'dd' then datediff(dd, scheduleDate, getdate())
							when 'mm' then datediff(mm, scheduleDate, getdate())
							when 'yy' then datediff(yy, scheduleDate, getdate())
						end
					) / interval;
				
				UPDATE ##tmpRecurringReports
				SET initialIntervalToAdvance = CASE WHEN missedIntervals > 0 THEN missedIntervals + 1 ELSE 1 END;

				-- recurring dates
				WITH tmpRecurringDates AS (
					SELECT reportID,
						case SQLDatepart
							when 'dd' then dateadd(dd, interval * initialIntervalToAdvance, scheduleDate)
							when 'mm' then dateadd(mm, interval * initialIntervalToAdvance, scheduleDate)
							when 'yy' then dateadd(yy, interval * initialIntervalToAdvance, scheduleDate)
						end scheduleDate,
						interval, SQLDatepart
					FROM ##tmpRecurringReports
						UNION ALL
					SELECT reportID,
						case SQLDatepart
							when 'dd' then dateadd(dd, interval, scheduleDate)
							when 'mm' then dateadd(mm, interval, scheduleDate)
							when 'yy' then dateadd(yy, interval, scheduleDate)
						end scheduleDate,
						interval, SQLDatepart
					FROM tmpRecurringDates
					WHERE scheduleDate <= @endDate
					AND scheduleDate <= DATEADD(year, 1, @nowDate)
				)
				INSERT INTO ##tmpCalEntries(reportID, sendDate, sendTime)
				SELECT reportID, scheduleDate, FORMAT(scheduleDate, 'hh:mm tt')
				FROM tmpRecurringDates
				WHERE scheduleDate > @nowDate
				AND scheduleDate BETWEEN @startDate AND @endDate
				ORDER BY reportID, scheduleDate
				OPTION (MAXRECURSION 1200);

				SELECT DISTINCT tmp.sendDate, tmp.sendTime, r.reportID, r.reportName, tt.toolType, tt.toolDesc,
					mActive.firstName, mActive.lastName, r.dateCreated
				FROM ##tmpCalEntries AS tmp
				INNER JOIN dbo.rpt_SavedReports AS r ON r.reportID = tmp.reportID
					AND r.siteID = @siteID
				INNER JOIN dbo.rpt_scheduledReports AS sr ON sr.reportID = r.reportID 
					AND sr.siteID = @siteID AND (sr.endRunDate IS NULL OR (sr.endRunDate IS NOT NULL AND tmp.sendDate <= sr.endRunDate))
				INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
					AND tt.includeInAllReportsGrid = 1
				INNER JOIN dbo.ams_members AS m ON m.memberID = r.memberID
					AND m.orgID IN (@orgID,1)
				INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
					AND mActive.orgID = m.orgID
				ORDER BY sendDate, sendTime;

				IF OBJECT_ID('tempdb..##tmpCalEntries') IS NOT NULL
					DROP TABLE ##tmpCalEntries;
				IF OBJECT_ID('tempdb..##tmpRecurringReports') IS NOT NULL
					DROP TABLE ##tmpRecurringReports;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfset local.arrData = arrayNew(1)>
		<cfloop query="local.qryReports">
			<cfset local.reportLink = buildLinkToTool(toolType='#local.qryReports.toolType#',mca_ta='showReport')>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div>
					<h4>#local.qryReports.reportName#</h4>
					<table>
						<tr valign="top"><td nowrap="true"><b>Send Time:</b></td><td rowspan="4" width="10">&nbsp;</td><td>#DateFormat(local.qryReports.sendDate, "mmmm d, yyyy")# #local.qryReports.sendTime#</td></tr>
						<tr valign="top"><td nowrap="true"><b>Report Type:</b></td><td>#local.qryReports.toolDesc#</td></tr>
						<tr valign="top">
							<td><b>Created:</b></td>
							<td>
								<div>#dateTimeFormat(local.qryReports.dateCreated,'m/d/yy h:nn tt')# CT</div>
								<div>#local.qryReports.firstName# #local.qryReports.lastName#</div>
							</td>
						</tr>
						<cfif len(local.reportLink)>
							<tr valign="top"><td>&nbsp;</td><td><a href="javascript:viewReport('#local.reportLink#&rptID=#local.qryReports.reportID#');">View report in a new window</a></td></tr>
						</cfif>
					</table>
				</div>
				</cfoutput>
			</cfsavecontent>

			<cfset local.tmpStr = structNew() >
			<cfset local.tmpStr['start'] = dateTimeFormat(local.qryReports.sendDate,"yyyy-mm-dd'T'HH:nn:ss'Z'") > 
			<cfset local.tmpStr['end'] = local.tmpStr['start']>
			<cfset local.tmpStr['allDay'] = 0>
			<cfset local.tmpStr['title'] = local.qryReports.reportName>
			<cfset local.tmpStr['description'] = htmleditformat(trim(local.data))>
			
			<cfset arrayAppend(local.arrData,local.tmpStr)>
		</cfloop>

		<cfreturn SerializeJSON(local.arrData)>
	</cffunction>

</cfcomponent>