<cfoutput>
    <div style="padding:5px">
        <h5>Contributed Documents</h5>
        #getPaginationHTML(topOrBottom="top",recordCount=local.qryAllContributedDocuments.recordCount,itemCount=val(local.qryAllContributedDocuments.itemCount), docType="document",maxRows=local.maxRows,startRow=local.startrow,tab='CD',viewDirectory=local.viewDirectory,noRecordsMsg="You have not contributed any documents.", dspSaveBtn="#len(local.dropboxAppKey) gt 0#")#
        <!--- results table --->
        <cfoutput query="local.qryAllContributedDocuments">
            <cfset local.expertFileName = local.objTSDocument.getDownloadDocFileName(documentID=local.qryAllContributedDocuments.documentid, expertname=local.qryAllContributedDocuments.expertname, documentDate=local.qryAllContributedDocuments.DocumentDate)>
            <div class="tsDocViewerDocument row-fluid s_row <cfif local.qryAllContributedDocuments.currentrow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.qryAllContributedDocuments.documentid#">
                <div class="span12">
                    <div class="row-fluid">
                        <div class="span10">
                            <a href="javascript:docViewer(#local.qryAllContributedDocuments.documentid#,1);"><img src="/assets/common/images/search/page_acrobat.png" width="20" height="20" border="0" align="left" alt="[Adobe Acrobat Document]"><b><cfif len(trim(local.qryAllContributedDocuments.expertname))>#trim(local.qryAllContributedDocuments.expertname)#<cfelse>Unknown Expert</cfif></b></a><br/>
                            <div class="s_dtl hidden-phone">
                                Document ###local.qryAllContributedDocuments.documentid# - #DateFormat(local.qryAllContributedDocuments.DocumentDate, "mmm d, yyyy")# - #local.qryAllContributedDocuments.state#<br/>
                                #local.qryAllContributedDocuments.style# ... <cfif len(local.qryAllContributedDocuments.causedesc)>... #local.qryAllContributedDocuments.causedesc#</cfif>
                                <cfif len(local.qryAllContributedDocuments.notes)><br/><b class="s_label">Notes:</b> #local.qryAllContributedDocuments.notes#</cfif>
                            </div>
                        </div>
                        <div class="span2 s_act">
                            <div class="s_opt" style="line-height: 1.5em;">
                                <cfif len(local.qryAllContributedDocuments.uploadpdfdate)>
                                    <button name="pdf_btn_#local.qryAllContributedDocuments.currentRow#" type="button" class="btn btn-secondary btn-sm" onClick="downloadDocImmedate(#local.qryAllContributedDocuments.DocumentID#,'pdf');" style="width:150px;"><i class="icon-download-alt" title="Download PDF"></i> Download PDF</button>
                                </cfif>
                                <cfif len(local.qryAllContributedDocuments.uploadpdfdate) and len(local.dropboxAppKey)>
                                    <button name="dropb_btn_#local.qryAllContributedDocuments.currentRow#" id="dropb_btn_#local.qryAllContributedDocuments.currentRow#" type="button" class="btn btn-secondary btn-sm drop-box-file-list" onClick="doDropboxSave(#local.qryAllContributedDocuments.documentID#,'#local.expertFileName#.pdf','pdf',#local.qryAllContributedDocuments.currentRow#);"  data-elmid="#local.qryAllContributedDocuments.documentID#" data-elmname="#local.expertFileName#.pdf" data-elmext="pdf" data-rowid="#local.qryAllContributedDocuments.currentRow#" style="width:150px;margin-top:10px;"><img src="/assets/common/images/dropbox-icon-ios.png" id="dropb_img_#local.qryAllContributedDocuments.currentRow#" data-toggle="tooltip" width="15" height="15" border="0" title="Save to Dropbox"/> Save to Dropbox</button>
                                </cfif>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </cfoutput>

        #getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllContributedDocuments.recordCount,itemCount=val(local.qryAllContributedDocuments.itemCount), docType="document",maxRows=local.maxRows,startRow=local.startrow,tab='CD',viewDirectory=local.viewDirectory,noRecordsMsg="You have not contributed any documents.")#
    </div>
</cfoutput>