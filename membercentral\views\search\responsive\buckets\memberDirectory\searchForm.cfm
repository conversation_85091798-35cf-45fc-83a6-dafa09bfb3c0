<cfset local.showReqFlag = false>
<cfloop array="#local.settingsStruct.pageSettings.searchXMLFields.xmlRoot.xmlChildren#" index="local.thisfield">				
	<cfif local.thisfield.xmlattributes.isRequired is 1>
		<cfsavecontent variable="local.jsValidation">
			<cfoutput>
			#local.jsValidation#
			if(formFieldNames['#local.thisfield.xmlattributes.fieldCode#'])
				if(!_CF_hasValue(_CF_this[formFieldNames['#local.thisfield.xmlattributes.fieldCode#']], "TEXT", false)) 
					locateErr += "#htmleditformat(local.thisfield.xmlattributes.fieldLabel)# is required.<br/>";
			</cfoutput>
		</cfsavecontent>
		<cfset local.showReqFlag = true>
	</cfif>
</cfloop>

<cfsavecontent variable="local.customJS">
	<script type="text/javascript">		
		function b_search() {
			$('#searchBoxError').html('').hide();
			var _CF_this = document.forms['frms_Search'];
			var formFieldNames = {'m_firstname':'s_fname', 'm_lastname':'s_lname', 'm_company':'s_firm'};
			b_trimAllTxt(_CF_this);
			var locateErr = '';
			<cfoutput>#local.jsValidation#</cfoutput>
			if (locateErr.length > 0){
				$('#searchBoxError').html(locateErr).show();
				$("form#frms_Search input.requiredSearchField").filter(function(){return this.value == "";}).first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<cfif ArrayLen(local.settingsStruct.pageSettings.searchXMLFields.xmlRoot.xmlChildren) is 0 and local.qryClassifications.recordcount is 0>
	<p>Searching is not available at this time.</p>
<cfelse>	
	<div id="searchBoxError" class="alert alert-error hide"></div>
	<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
		<cfloop array="#local.settingsStruct.pageSettings.searchXMLFields.xmlRoot.xmlChildren#" index="local.thisfield">
			<cfif listFindNoCase("m_firstname,m_lastname,m_company",local.thisfield.xmlattributes.fieldCode)>
				<cfswitch expression="#local.thisfield.xmlattributes.fieldCode#">
					<cfcase value="m_firstname">
						<cfset local.fieldName = "s_fname">
						<cfset local.fieldValue = local.strSearchForm.s_fname>
					</cfcase>
					<cfcase value="m_lastname">
						<cfset local.fieldName = "s_lname">
						<cfset local.fieldValue = local.strSearchForm.s_lname>
					</cfcase>
					<cfcase value="m_company">
						<cfset local.fieldName = "s_firm">
						<cfset local.fieldValue = local.strSearchForm.s_firm>
					</cfcase>
				</cfswitch>

				<div class="control-group">
					<label class="control-label" for="#local.fieldName#">
						<cfif local.thisfield.xmlattributes.isRequired is 1>* </cfif>#htmleditformat(local.thisfield.xmlattributes.fieldLabel)#:
					</label>
					<div class="controls">
						<input type="text" name="#local.fieldName#"  id="#local.fieldName#" value="#local.fieldValue#" <cfif local.thisfield.xmlattributes.isRequired>class="requiredSearchField"</cfif>>
					</div>
				</div>
			</cfif>
		</cfloop>
		<cfloop query="local.qryClassifications">
			<cfif local.qryClassifications.allowSearch is 1>
				<cfset local.qryClassificationslinks = local.objMemberConfig.getGroupSets(local.qryClassifications.classificationid)>
				<div class="control-group">
					<label class="control-label" for="mg_gid">#local.qryClassifications.name#:</label>
					<div class="controls">
						<select name="mg_gid"  id="mg_gid">
							<option value=""></option>
							<cfloop query="local.qryClassificationslinks">
								<option value="#local.qryClassificationslinks.groupId#">#local.qryClassificationslinks.groupName#</option>					
							</cfloop>
						</select>
					</div>
				</div>
			</cfif>
		</cfloop>
		<div class="control-group">
			<div class="controls">
				<input type="hidden" name="s_frm" id="s_frm" value="1">
				<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
			</div>
		</div>
		<cfif local.showReqFlag>
			<div class="control-group">
				<div class="controls">
					<i>* denotes required field</i>
				</div>
			</div>
		</cfif>
	</form>
</cfif>
</cfoutput>