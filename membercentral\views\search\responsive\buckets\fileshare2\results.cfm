<cfif local.settingsStruct.fileShareID NEQ "">
    <cfset local.bucketTextParam = local.bucketText>
    <cfset local.overrideFSIDParam = local.settingsStruct.fileShareID>
    <cfset local.applicationInstanceNameParam = local.fileShares.fileShareName>
<cfelse>
    <cfset local.bucketTextParam = "">
    <cfset local.overrideFSIDParam = 0>
    <cfset local.applicationInstanceNameParam = valueList(local.fileshares.fileShareName, ", ")>
</cfif>
<cfoutput>#showHeader(bucketID=local.qryBucketInfo.bucketID, bucketName=local.qryBucketInfo.bucketName, bucketText=local.bucketTextParam, applicationInstanceName=local.applicationInstanceNameParam, overrideFSID=local.overrideFSIDParam, viewDirectory=arguments.viewDirectory)#</cfoutput>

<cfif cacheItemCount(arguments.searchID, arguments.bucketID) EQ 0>
    <cfoutput>
        #showCommonNotFound(bucketID=arguments.bucketid, searchID=arguments.searchid, bucketName=local.qryBucketInfo.bucketName, viewDirectory="responsive")#
    </cfoutput>
<cfelse>
    <cfif local.settingsStruct.fileShareID NEQ "">
        <cfset local.objDocVersions = CreateObject("component","model.admin.common.modules.documentDetails.documentDetails") />

        <cfif local.fileShares.alwaysShowFolders EQ "1" AND local.strSearch.filter_showAllResults eq "0" >
            <cfif listlen(arguments.filter,"|") is 4>
                <cfif local.strSearch.filter_category2 gt 0>
                    <cfset local.qryFolders = getFolders(arguments.searchID, arguments.bucketID, 0, 0, local.strSearch.filter_appInstanceID) />
                <cfelseif local.strSearch.filter_category1 gt 0>
                    <cfset local.qryFolders = getFolders(arguments.searchID, arguments.bucketID, 2, local.strSearch.filter_category1, local.strSearch.filter_appInstanceID) />
                <cfelse>
                    <cfset local.qryFolders = getFolders(arguments.searchID, arguments.bucketID, 1, 0, local.strSearch.filter_appInstanceID) />
                </cfif>
            <cfelse>
                <cfset local.qryFolders = getFolders(arguments.searchID, arguments.bucketID, 1, 0, local.strSearch.filter_appInstanceID) />
            </cfif>
    
            <cfif local.fileShares.alwaysShowFolders EQ "1" OR local.settingsStruct.hasManyFileshares EQ true>
                <cfoutput>
                    <div class="row-fluid">
                        <div class="span12">
                            <!--- crumbtrail --->
                            <div>
                                <p>Documents are organized into folders. You are viewing:</p>
                                <ul class="breadcrumb">
                                    <cfset local.dividerElement = "<span class='divider'>/</span>">
                                    <cfif local.strSearch.filter_category1 gt 0>
                                        <li><a href="/?pg=search&bid=#arguments.bucketid#&s_a=doSearch&sid=#arguments.searchid#">#local.qryBucketInfo.bucketName#</a></li>
                                        <cfif local.settingsStruct.restrictToFileshares NEQ "">
                                            <li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'0|0|0|#local.strSearch.filter_appInstanceID#');">#local.fileShares.applicationInstanceName#</a></li>
                                        </cfif>

                                        <cfif local.strSearch.filter_category2 eq 0>
                                            <li class="active">#local.dividerElement#<span>#getCategoryName(local.strSearch.filter_category1)#</span><li>
                                        <cfelse>
                                            <li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_category1#|0|0|#local.strSearch.filter_appInstanceID#');">#getCategoryName(local.strSearch.filter_category1)#</a></li>

                                            <cfif local.strSearch.filter_category2 gt 0>
                                                <li class="active">#local.dividerElement#<span>#getCategoryName(local.strSearch.filter_category2)#</span><li>
                                            <cfelseif local.strSearch.filter_category2 lt 0>
                                                <li class="active">#local.dividerElement#<span>Uncategorized</span><li>
                                            </cfif>
                                        </cfif>
                                    <cfelse>
                                        <cfif local.settingsStruct.restrictToFileshares NEQ "">
                                            <li><a href="/?pg=search&bid=#arguments.bucketid#&s_a=doSearch&sid=#arguments.searchid#">#local.qryBucketInfo.bucketName#</a></li>
                                            <li class="active">#local.dividerElement#<span>#local.fileShares.applicationInstanceName#</span><li>
                                        <cfelse>
                                            <li><span>#local.qryBucketInfo.bucketName#</span><li>
                                        </cfif>
                                    </cfif>
                                </ul>
                            </div>
                            <div class="pull-right">
                                <a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_category1#|#local.strSearch.filter_category2#|1|#local.strSearch.filter_appInstanceID#');">
                                    <i class="icon-refresh icon-large" title="Show all documents retrieved."></i> Show as List
                                </a>
                            </div>
                        </div>
                    </div>
                </cfoutput>
            </cfif>
            
            <cfif local.strSearch.filter_category1 gt 0 AND local.strSearch.filter_category2 neq 0>
                <cfoutput>
                <cfset local.tmpSort = ArrayNew(1)>
				<cfif len(local.strSearch.keywords)>
					<cfset ArrayAppend(local.tmpSort,"rank|Relevance|#local.sortOrder#")>
				</cfif>
                <cfif local.qryGetFileShareSettings.recordCount and val(local.qryGetFileShareSettings.showResultSorting)>
                    <cfset ArrayAppend(local.tmpSort,"date|Publication Date|#local.sortOrder#")>
                    <cfset ArrayAppend(local.tmpSort,"title|Document Title|#local.sortOrder#")>
                </cfif>
                <cfset local.filter = '#local.strSearch.filter_category1#|#local.strSearch.filter_category2#|0|#local.strSearch.filter_appInstanceID#'>

                #showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.count, itemWord='item', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=local.sortType, filter=local.filter, viewDirectory=arguments.viewDirectory)#

                <!--- results table --->
                <cfloop query="local.qryResults">
                    <cfset local.icon = getIcon(local.qryResults.fileExt)>
                    <cfset local.description = getFileDescription(local.qryResults.fileExt)>
                    <div class="row-fluid">
                        <div class="span12 s_row <cfif local.qryResults.currentRow mod 2 is 0>s_row_alt</cfif>">
                            <a href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Click to view document"><img src="#application.paths.images.url#images/fileExts/#local.icon#" width="16" height="16" border="0" align="left" class="hidden-phone"/></a><span class="hidden-phone">&nbsp; </span><a href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Click to view document"><b>#local.qryResults.docTitle#</b></a> <span class="hidden-phone">(#local.description#)</span>
                            <div class="bk_subContent">
                                <div class="bk_smallfont">
                                    <div id="documentInfo">
                                        <cfif local.fileShares.showTags EQ "1"><span><strong>Tags:</strong> #getTags(local.qryResults.docSiteResourceID, local.fileShares.showTagsAsLinks)#</span><br/></cfif>
                                        
                                        <!--- custom fields --->
                                        <cfset local.customFields = local.settingsStruct.fileShareSettings.customFields />                                       
                                        <cfset local.columnData = '' />
                                        <cfloop query="local.customFields">
                                            <cfset local.columnData = local.settingsStruct.objFS2.getExtraFSColumnDisplay(local.qryResults.docSiteResourceID, local.customFields.columnID) />
                                            <cfif listFind(local.settingsStruct.fileShareSettings.showCustomFields,local.customFields.columnID) AND len(trim(local.columnData))>
                                                <cfif local.customFields.columnDesc EQ "Hot Document">
                                                    <span><strong>#local.customFields.columnDesc#</strong></span><br />
                                                <cfelse>
                                                    <span><strong>#local.customFields.columnDesc#:</strong> #local.columnData#</span><br/>
                                                </cfif>
                                            </cfif>
                                        </cfloop>
                                        
                                        <cfif val(local.fileShares.showPublicationDate) AND len(trim(local.qryResults.publicationDate))><span><strong>Publication Date:</strong> #DateFormat(local.qryResults.publicationDate, "MM/DD/YYYY")#</span><br /></cfif>                            
                                        <cfif val(local.fileShares.showAuthor) AND len(trim(local.qryResults.author))><span><strong>#local.fileShares.authorLabel#:</strong> #local.qryResults.author#</span><br /></cfif>
                                        <cfif val(local.fileShares.showContributedBy) AND len(trim(local.qryResults.firstName))><span><strong>Contributor:</strong> #local.qryResults.firstName# #local.qryResults.lastName#</span><br /></cfif>
                                        <cfif val(local.fileShares.showFirm) AND len(trim(local.qryResults.company))><span><strong>Firm:</strong> #local.qryResults.company#</span><br /></cfif>                            
                                        <cfif val(local.fileShares.showAddress)>
                                            <cfset local.address = getAddress(local.qryResults.contributorMemberID)>
                                            <cfif len(trim(local.address.address1))>
                                                <span><strong>Address:</strong> #local.address.address1# #local.address.address2# #local.address.city# , #local.address.code# #local.address.postalCode#</span><br />
                                            </cfif>
                                            <cfif len(trim(local.address.email))>
                                                <span><strong>Email:</strong> <a href="mailto:#local.address.email#:">#local.address.email#</a></span><br />
                                            </cfif>
                                        </cfif>
                                        <cfif len(trim(local.qryResults.docDesc))><span>#local.qryResults.docDesc#</span><br /></cfif>
                                    </div>

                                    <!--- NEED TO INTEGRATE THIS CODE TO SHOW ON SEARCH RESULTS --->

                                    <cfset local.showDownloadCount = val(local.fileShares.showDocDownloadCountToMembers) OR application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                    <cfif local.showDownloadCount OR local.fileShares.showVersioning EQ "1">
                                        <div style="margin-top:8px;">
                                            <cfif local.showDownloadCount>
                                                <div><strong>Downloads:</strong> #local.qryResults.downloadCount#</div>
                                            </cfif>
                                            <cfif local.fileShares.showVersioning EQ "1">
                                                <div class="hidden-phone">
                                                    <strong>Version(s):</strong> #CreateObject("component","model.admin.common.modules.documentDetails.documentDetails").getDocumentVersions(local.qryResults.documentLanguageID).recordCount#  
                                                    <strong>Created:</strong> #DateFormat(local.qryResults.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.qryResults.dateCreated, "hh:mm tt")#  
                                                    <strong>Revised:</strong> #DateFormat(local.qryResults.dateModified, "MM/DD/YYYY")# #TimeFormat(local.qryResults.dateModified, "hh:mm tt")# 
                                                </div>
                                            </cfif>
                                        </div>
                                    </cfif>
                                </div>

                                <div class="visible-desktop">
                                    <div id="#local.qryResults.docSiteResourceID#" class="hide" style="margin-top:10px;">
                                        <a href="###local.qryResults.docSiteResourceID#"></a>
                                        <cfset local.document = local.objDocVersions.getDocumentVersions(local.qryResults.documentLanguageID) />
                                        <cfif local.document.recordcount gt 1>
                                            <table class="table table-condensed table-striped" style="font-size:9pt">
                                                <tr><th>Source</th><th>File Name</th><th>Created</th><th>Revised</th></tr>
                                                <cfloop query="local.document">
                                                    <cfif local.document.isActive EQ 0>
                                                    <tr>
                                                        <td>#local.document.author#</td>
                                                        <td>
                                                            <a href="/docDownload/#local.document.documentID#&VID=#local.document.documentVersionID#&lang=#local.document.languageCode#" target="_blank" title="Click to download document">#local.document.filename#</a>
                                                            <cfif local.qryResults.fileExt eq "pdf" and application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                                                <a href="javascript:docViewer2(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#);" title="Click to view document"><img src="#application.paths.images.url#images/JRC/view.png" width="16" height="16" border="0" align="left" /></a>
                                                            </cfif>
                                                        </td>
                                                        <td>#DateFormat(local.document.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.document.dateCreated, "hh:mm tt")#  </td>
                                                        <td>#DateFormat(local.document.dateModified, "MM/DD/YYYY")# #TimeFormat(local.document.dateModified, "hh:mm tt")# </td>
                                                    </tr>
                                                    </cfif>
                                                </cfloop>
                                            </table>
                                        </cfif>
                                    </div>
                                </div>

                                <cfset local.baseURL = "/?#variables.thisBaseLink#">
                                <div class="btn-toolbar visible-phone">
                                    <a class="btn btn-large" href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Download Document"><i class="fa fa-save"></i>&nbsp;Download</a>
                                    <cfif local.qryResults.fileExt eq "pdf" and application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                        <a class="btn btn-large" href="javascript:docViewer2(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#);" title="Click to view document"><i class="fa-solid fa-eye"></i> View</a>
                                    </cfif>
                                    <cfif val(local.fileShares.showShareButton)>
                                        <a class="btn btn-large" href="mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#local.qryResults.documentID#" title="Share with colleagues"><i class="fa fa-envelope"></i> &nbsp;Email</a>
                                    </cfif>
                                </div>
                                <div class="hidden-phone bk_appIcons" style="margin-top:30px;">
                                    <div>
                                        <a class="btn btn-small btn-primary" href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Click to view document"><i class="fa fa-save"></i> Download</a>
                                        <cfif local.qryResults.fileExt eq "pdf" and application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                            <a class="btn btn-small btn-primary" href="javascript:docViewer2(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#);" title="Click to view document"><i class="fa-solid fa-eye"></i> View</a>
                                        </cfif>
                                        <cfif val(local.fileShares.showShareButton)>
                                            <a class="btn btn-small btn-info" href="mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#local.qryResults.documentID#" title="Share with colleagues"><i class="fa-solid fa-share"></i> Share</a>
                                        </cfif>	
                                        <cfif local.settingsStruct.appRightsStruct.fsEditAnyMetadata or (local.settingsStruct.appRightsStruct.fsEditOwnMetadata and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.qryResults.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.settingsStruct.orgID)) )>
                                            <a class="btn btn-small btn-info" href="#local.baseURL#&fsAction=editDocument&lang=en&fsDocumentID=#local.qryResults.documentID#" title="Edit document"><i class="fa-solid fa-edit"></i> Edit</a>
                                        </cfif>
                                        <cfif #CreateObject("component","model.admin.common.modules.documentDetails.documentDetails").getDocumentVersions(local.qryResults.documentLanguageID).recordCount# GT "1">
                                            <a class="btn btn-small btn-info" href="##" onClick="javascript:fs_togglePriorVersions(#local.qryResults.docSiteResourceID#); return false;" title="View prior versions" class="hidden-phone hidden-tablet"><i class="icon-time icon-large"></i> Versions</a>
                                        </cfif>		
                                        <a class="btn btn-small btn-info" href="#local.baseURL#&panel=uploads&from=search" title="Upload a single document"><i class="icon-upload icon-large"></i> Upload</a>
                                        <a class="btn btn-small btn-danger" href="#local.baseURL#&from=search&panel=sendNote&fsDocumentID=#local.qryResults.documentID#" title="Notify Admin"><i class="icon-info-sign icon-large"></i> Notify</a>
                                    </div>
                                </div>
                                <br/>
                            </div>
                        </div>
                    </div>
                </cfloop>

                <cfif local.NumTotalPages GT 1>
                    #showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.count, itemWord='item', itemWordSuffix='s', filter=local.filter, viewDirectory=arguments.viewDirectory)#
                </cfif>
                </cfoutput>
            <cfelse>
                <cfoutput>
                    <cfquery dbtype="query" name="local.documents">
                        SELECT SUM(docCount) AS totalCount
                        FROM [local].qryFolders
                    </cfquery>
                    
                    <div class="well well-small">
                        <strong>#NumberFormat(local.documents.totalCount)#</strong> document<cfif local.documents.totalCount is not 1>s</cfif> 
                        in <strong>#local.qryFolders.recordCount#</strong> categor<cfif local.qryFolders.recordCount GT 1>ies<cfelse>y</cfif>
                    </div>

                    <div class="row-fluid">
                        <div class="span12" style="margin-left:15px;">
                            <div class="visible-desktop">
                                <cfset local.midPoint = Int(local.qryFolders.recordcount / 2) + local.qryFolders.recordcount mod 2 />
                                <cfloop from='1' to='#local.midPoint#' index='local.x'> 
                                    <div class="row-fluid">
                                        <div class="span6">
                                            #getCategoryLink(searchID=arguments.searchID, categoryID=local.qryFolders['categoryID'][local.x], filter_category1=local.strSearch.filter_category1, filter_category2=local.strSearch.filter_category2, categoryName=local.qryFolders['categoryName'][local.x], docCount=local.qryFolders['docCount'][local.x], appInstanceID=local.strSearch.filter_appInstanceID, viewDirectory=arguments.viewDirectory)#
                                        </div>
                                        <cfif local.midPoint + local.x lte local.qryFolders.recordcount>
                                            <div class="span6">
                                                #getCategoryLink(searchID=arguments.searchID, categoryID=local.qryFolders['categoryID'][local.midPoint+local.x], filter_category1=local.strSearch.filter_category1, filter_category2=local.strSearch.filter_category2, categoryName=local.qryFolders['categoryName'][local.midPoint+local.x], docCount=local.qryFolders['docCount'][local.midPoint+local.x], appInstanceID=local.strSearch.filter_appInstanceID, viewDirectory=arguments.viewDirectory)#
                                            </div>
                                        </cfif>
                                    </div>
                                </cfloop>
                            </div>
                            <div class="hidden-desktop">
                                <cfloop query="local.qryFolders">
                                    <div class="row-fluid">
                                        <div class="span12">
                                            #getCategoryLink(searchID=arguments.searchID, categoryID=local.qryFolders.categoryID, filter_category1=local.strSearch.filter_category1, filter_category2=local.strSearch.filter_category2, categoryName=local.qryFolders.categoryName, docCount=local.qryFolders.docCount, appInstanceID=local.strSearch.filter_appInstanceID, viewDirectory=arguments.viewDirectory)#
                                        </div>
                                    </div>
                                </cfloop>
                            </div>
                        </div>
                    </div>
                </cfoutput>
            </cfif>
        <cfelse>
            <cfset local.tmpSort = ArrayNew(1)>
			<cfif len(local.strSearch.keywords)>
				<cfset ArrayAppend(local.tmpSort,"rank|Relevance|#local.sortOrder#")>
			</cfif>
            <cfif local.qryGetFileShareSettings.recordCount and val(local.qryGetFileShareSettings.showResultSorting)>
                <cfset ArrayAppend(local.tmpSort,"date|Publication Date|#local.sortOrder#")>
                <cfset ArrayAppend(local.tmpSort,"title|Document Title|#local.sortOrder#")>
            </cfif>

            <cfoutput>
                <cfif local.fileShares.alwaysShowFolders EQ "1" AND NOT local.settingsStruct.fileShareID EQ "">
                    <div class="row-fluid">
                        <div class="span12">
                            <div class="pull-left bk-controls-large">
                                <!--- Display category tree filters for post filtering --->	
                                <cfif local.settingsStruct.fileShareID NEQ "">
                                    <cfloop query="local.settingsStruct.qryCategoryTrees">
                                        <cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
                                            SET NOCOUNT ON;
                                            SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
            
                                            DECLARE @searchID INT, @bucketID INT;
                                            SET @searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">;
                                            SET @bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">;

                                            WITH categories( categoryID, categoryName, parentCategoryID, catlevel, sort)
                                            AS (
                                                SELECT categoryID, categoryName, parentCategoryID, 0 AS catlevel, cast(categoryName AS nvarchar(2048))
                                                FROM dbo.cms_categories
                                                WHERE parentCategoryID is NULL
                                                AND categoryTreeId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#">	
                                                AND isActive = 1
                                                    UNION ALL
                                                SELECT c2.categoryID, c2.categoryName, c2.parentCategoryID, catlevel + 1, cast(sort + '|' + c2.categoryName as nvarchar(2048))
                                                FROM dbo.cms_categories c2 
                                                INNER JOIN categories c
                                                ON c2.parentCategoryID = c.categoryID
                                                WHERE categoryTreeId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#">	
                                                AND isActive = 1
                                            )
                                            SELECT  c.categoryID, categoryName, parentCategoryID,catlevel, sort
                                            FROM categories c
                                            INNER JOIN membercentral.dbo.cms_categorySiteResources sr ON sr.categoryID = c.categoryID
                                            INNER JOIN searchMC.dbo.tblSearchSiteResourceCache src ON src.siteResourceID = sr.siteResourceID
                                                AND src.searchID = @searchID
                                                AND src.bucketID = @bucketID
                                            GROUP BY c.categoryID, categoryName, parentCategoryID,catlevel, sort
                                            ORDER BY sort;
            
                                            SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
                                        </cfquery>
                                        
                                        <!--- Check to see if category id in this list ---> 
                                        <cfset local.tmpCategoryID = local.settingsStruct.categoryID>
                                        <cfif local.tmpCategoryID EQ "">
                                            <cfset local.tmpCategoryID = 0>
                                        </cfif>
                                        <cfquery name="local.isHere" dbtype="query"> 
                                            select categoryID, categoryName from [local].qryCategories where categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.tmpCategoryID#">
                                        </cfquery>

                                        <!--- Need to check all post filters --->
                                        <select name="s_postfilter" id="s_postfilter" onchange="fs_postfilter(#arguments.searchid#,'0|0|1|#local.strSearch.filter_appInstanceID#');">
                                            <option value="">Filter - #local.settingsStruct.qryCategoryTrees.categoryTreeName#</option>
                                            <cfloop query="local.qryCategories">
                                                <cfif local.qryCategories.parentCategoryID EQ "">
                                                    <option value="#local.qryCategories.categoryID#" <cfif listFindNoCase(arguments.postfilter, local.qryCategories.categoryID)>selected</cfif>>#local.qryCategories.categoryName#</option>
                                                <cfelse>
                                                    <option value="#local.qryCategories.categoryID#" <cfif listFindNoCase(arguments.postfilter, local.qryCategories.categoryID)>selected</cfif>>&nbsp;&nbsp;&nbsp;&nbsp;#local.qryCategories.categoryName#</option>
                                                </cfif>
                                            </cfloop>
                                        </select>
                                    </cfloop>
                                </cfif> 
                                <a href="##" class="btn btn-small" onClick="return fs_clearPostFilters(#arguments.searchid#,'0|0|1|#local.strSearch.filter_appInstanceID#');" style="margin-bottom:10px;">Clear Post Filters</a><br>
                            </div>
                            <div class="pull-right">
                                <a href="javascript:b_doSearchFilter(#arguments.searchid#,'0|0|0|#local.strSearch.filter_appInstanceID#');">
                                    <i class="icon-refresh icon-large" title="Show results in folders"></i> Show Folders
                                </a>
                            </div>
                        </div>
                    </div>

                    <!--- crumbtrail --->
                    <cfif local.settingsStruct.hasManyFileshares EQ true>
                        <ul class="breadcrumb">
                            <cfset local.dividerElement = "<span class='divider'>/</span>">
                            <cfif local.strSearch.filter_category1 gt 0>
                                <li><a href="/?pg=search&bid=#arguments.bucketid#&s_a=doSearch&sid=#arguments.searchid#">#local.qryBucketInfo.bucketName#</a></li>
                                <cfif local.settingsStruct.restrictToFileshares NEQ "">
                                    <li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'0|0|0|#local.strSearch.filter_appInstanceID#');">#local.fileShares.applicationInstanceName#</a></li>
                                </cfif>

                                <cfif local.strSearch.filter_category2 eq 0>
                                    <li class="active">#local.dividerElement#<span>#getCategoryName(local.strSearch.filter_category1)#</span><li>
                                <cfelse>
                                    <li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_category1#|0|0|#local.strSearch.filter_appInstanceID#');">#getCategoryName(local.strSearch.filter_category1)#</a></li>

                                    <cfif local.strSearch.filter_category2 gt 0>
                                        <li class="active">#local.dividerElement#<span>#getCategoryName(local.strSearch.filter_category2)#</span><li>
                                    <cfelseif local.strSearch.filter_category2 lt 0>
                                        <li class="active">#local.dividerElement#<span>Uncategorized</span><li>
                                    </cfif>
                                </cfif>
                            <cfelse>
                                <cfif local.settingsStruct.restrictToFileshares NEQ "">
                                    <li><a href="/?pg=search&bid=#arguments.bucketid#&s_a=doSearch&sid=#arguments.searchid#">#local.qryBucketInfo.bucketName#</a></li>
                                    <li class="active">#local.dividerElement#<span>#local.fileShares.applicationInstanceName#</span><li>
                                <cfelse>
                                    <li><span>#local.qryBucketInfo.bucketName#</span><li>
                                </cfif>
                            </cfif>
                        </ul>
                    </cfif>
                    
                    <cfset local.filterParam = '#local.strSearch.filter_category1#|#local.strSearch.filter_category2#|1|#local.strSearch.filter_appInstanceID#'>
                    <cfset local.postFilterParam = "">
                    <cfif local.settingsStruct.restrictToFileshares NEQ "">
                        <cfset local.qrySplitBuckets = cacheSplitBucketCounts(arguments.searchid,arguments.bucketid, local.strSearch.filter_appInstanceID)>
                        <cfset local.itemCountParam = local.qrySplitBuckets.itemcount>
                    <cfelse>
                        <cfif len(arguments.postfilter) gt 1>
                            <cfset local.postFilterParam = arguments.postfilter>
                            <cfset local.itemCountParam = val(local.qryResults.count)>
                        <cfelse>
                            <cfset local.itemCountParam = local.strCount.itemcount>
                        </cfif>
                    </cfif>
                <cfelse>
                    <cfset local.filterParam = '#local.strSearch.filter_category1#|#local.strSearch.filter_category2#|1|#local.strSearch.filter_appInstanceID#'>
                    <cfset local.postFilterParam = "">
                    <cfif local.settingsStruct.restrictToFileshares NEQ "">
                        <cfset local.qrySplitBuckets = cacheSplitBucketCounts(arguments.searchid,arguments.bucketid, local.strSearch.filter_appInstanceID)>
                        <cfset local.itemCountParam = local.qrySplitBuckets.itemcount>
                    <cfelse>
                        <cfif len(arguments.postfilter) gt 1>
                            <cfset local.itemCountParam = val(local.qryResults.count)>
                            <cfset local.postFilterParam = arguments.postfilter>
                        <cfelse>
                            <cfset local.itemCountParam = local.strCount.itemcount>
                            <cfset local.filterParam = "">
                        </cfif>
                    </cfif>
                </cfif>

                #showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.itemCountParam, itemWord='item', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=local.sortType, filter=local.filterParam, postFilter=local.postFilterParam, viewDirectory=arguments.viewDirectory)#
            </cfoutput>

            <!--- results table --->
            <cfoutput query="local.qryResults">
                <cfset local.icon = getIcon(local.qryResults.fileExt)>
                <cfset local.description = getFileDescription(local.qryResults.fileExt)>
                <div class="row-fluid tsDocViewerDocument"  data-tsdocumentid="#local.qryResults.documentid#">
                    <div class="span12 s_row <cfif local.qryResults.currentRow mod 2 is 0>s_row_alt</cfif>">
                        <a href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Click to view document"><img src="#application.paths.images.url#images/fileExts/#local.icon#" width="16" height="16" border="0" align="left" class="hidden-phone"/></a><span class="hidden-phone">&nbsp; </span><a href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Click to view document"><b>#local.qryResults.docTitle#</b></a> <span class="hidden-phone"><small>(#local.description#)</small></span>
                        <div class="bk_subContent">
                            <div class="bk_smallfont">
                                <div id="documentInfo">
                                    <cfif local.fileShares.showTags EQ "1"><span><strong>Tags:</strong> #getTags(local.qryResults.docSiteResourceID, local.fileShares.showTagsAsLinks)#</span><br/></cfif>
                                    <cfif local.fileShares.showPublicationDate EQ "1"><cfif len(local.qryResults.publicationDate)><span><strong>Publication Date:</strong> #DateFormat(local.qryResults.publicationDate, "MM/DD/YYYY")#</span><br /></cfif></cfif>
                                    <cfif local.fileShares.showAuthor EQ "1"><cfif len(local.qryResults.author)><span><strong>#local.fileShares.authorLabel#:</strong> #local.qryResults.author#</span><br /></cfif></cfif>
                                    <cfif local.fileShares.showContributedBy EQ "1"><cfif len(local.qryResults.firstName)><span><strong>Contributor:</strong> #local.qryResults.firstName# #local.qryResults.lastName#</span><br /></cfif></cfif>
                                    <cfif local.fileShares.showFirm EQ "1"><cfif len(local.qryResults.company)><span><strong>Firm:</strong> #local.qryResults.company#</span><br /></cfif></cfif>
                                    <cfif local.fileShares.showAddress EQ "1">
                                        <cfset local.address = getAddress(local.qryResults.contributorMemberID)>
                                        <span><strong>Address:</strong> #local.address.address1# #local.address.address2# #local.address.city# , #local.address.code# #local.address.postalCode#</span><br />
                                        <cfif len(local.address.email)>
                                            <span><strong>Email:</strong> <A HREF="mailto:#local.address.email#">#local.address.email#</a></span><br />
                                        </cfif>
                                    </cfif>
                                    <cfif len(local.qryResults.docDesc)><span>#local.qryResults.docDesc#</span></cfif>
                                </div>

                                <cfset local.showDownloadCount = val(local.fileShares.showDocDownloadCountToMembers) OR application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                <cfif local.showDownloadCount OR local.fileShares.showVersioning EQ "1">
                                    <div style="margin-top:8px;">
                                        <cfif local.showDownloadCount>
                                            <div><strong>Downloads:</strong> #local.qryResults.downloadCount#</div>
                                        </cfif>
                                        <cfif local.fileShares.showVersioning EQ "1">
                                            <div class="hidden-phone">
                                                <strong>Version(s):</strong> #CreateObject("component","model.admin.common.modules.documentDetails.documentDetails").getDocumentVersions(local.qryResults.documentLanguageID).recordCount#  
                                                <strong>Created:</strong> #DateFormat(local.qryResults.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.qryResults.dateCreated, "hh:mm tt")#  
                                                <strong>Revised:</strong> #DateFormat(local.qryResults.dateModified, "MM/DD/YYYY")# #TimeFormat(local.qryResults.dateModified, "hh:mm tt")# 
                                            </div>
                                        </cfif>
                                    </div>
                                </cfif>
                            </div>
                            
                            <div class="visible-desktop">
                                <div id="#local.qryResults.docSiteResourceID#" class="hide" style="margin-top:10px;">
                                    <a href="###local.qryResults.docSiteResourceID#"></a>
                                    <cfset local.document = local.objDocVersions.getDocumentVersions(local.qryResults.documentLanguageID) />
                                    <cfif local.document.recordcount gt 1>
                                        <table class="table table-condensed table-striped" style="font-size:9pt">
                                            <tr><th>Source</th><th>File Name</th><th>Created</th><th>Revised</th></tr>
                                            <cfloop query="local.document">						
                                                <cfif local.document.isActive EQ 0>
                                                <tr>
                                                    <td>#local.document.author#</td>
                                                    <td>
                                                        <a href="/docDownload/#local.document.documentID#&VID=#local.document.documentVersionID#&lang=#local.document.languageCode#" target="_blank" title="Click to download document">#local.document.filename#</a>
                                                        <cfif local.qryResults.fileExt eq "pdf" and application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                                            <a href="javascript:docViewer2(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#);" title="Click to view document"><img src="#application.paths.images.url#images/JRC/view.png" width="16" height="16" border="0" align="left" /></a>
                                                        </cfif>
                                                    </td>
                                                    <td>#DateFormat(local.document.dateCreated, "MM/DD/YYYY")# #TimeFormat(local.document.dateCreated, "hh:mm tt")#  </td>
                                                    <td>#DateFormat(local.document.dateModified, "MM/DD/YYYY")# #TimeFormat(local.document.dateModified, "hh:mm tt")# </td>
                                                </tr>
                                                </cfif>
                                            </cfloop>
                                        </table>
                                    </cfif>
                                </div>
                            </div>

                            <cfset local.baseURL = "/?#variables.thisBaseLink#">
                            <div class="btn-toolbar visible-phone">
                                <a class="btn btn-large" href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Download Document"><i class="fa fa-save"></i>&nbsp;Download</a>
                                <cfif local.qryResults.fileExt eq "pdf" and application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                    <a class="btn btn-large" href="javascript:docViewer2(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#);" title="Click to view document"><i class="fa-solid fa-eye"></i> View</a>
                                </cfif>
                                <cfif val(local.fileShares.showShareButton)>
                                    <a class="btn btn-large" href="mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#local.qryResults.documentID#" title="Share with colleagues"><i class="fa fa-envelope"></i> &nbsp;Email</a>
                                </cfif>
                            </div>
                            <div class="hidden-phone bk_appIcons" style="margin-top:30px;">
                                <div>
                                    <a class="btn btn-small btn-primary" href="/docdownload/#local.qryResults.documentID#" target="_blank" title="Click to view document"><i class="fa fa-save"></i> Download</a>
                                    <cfif local.qryResults.fileExt eq "pdf" and application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                                        <a class="btn btn-small btn-primary" href="javascript:docViewer2(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#);" title="Click to view document"><i class="fa-solid fa-eye"></i> View</a>
                                    </cfif>
                                    <cfif val(local.fileShares.showShareButton)>
                                        <a class="btn btn-small btn-info" href="mailto: ?subject=Information you might find interesting&body=Thought you might be interested in this document: http://#application.objPlatform.getCurrentHostname()#/docdownload/#local.qryResults.documentID#" title="Share with colleagues"><i class="fa-solid fa-share"></i> Share</a>
                                    </cfif>	
                                    <cfif local.settingsStruct.appRightsStruct.fsEditAnyMetadata or (local.settingsStruct.appRightsStruct.fsEditOwnMetadata and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.qryResults.contributorMemberID eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.settingsStruct.orgID)) )>
                                        <a class="btn btn-small btn-info" href="#local.baseURL#&fsAction=editDocument&lang=en&fsDocumentID=#local.qryResults.documentID#" title="Edit document"><i class="fa-solid fa-edit"></i> Edit</a>
                                    </cfif>
                                    <cfif #CreateObject("component","model.admin.common.modules.documentDetails.documentDetails").getDocumentVersions(local.qryResults.documentLanguageID).recordCount# GT "1">
                                        <a class="btn btn-small btn-info" href="##" onClick="javascript:fs_togglePriorVersions(#local.qryResults.docSiteResourceID#); return false;" title="View prior versions" class="hidden-phone hidden-tablet"><i class="icon-time icon-large"></i> Versions</a>
                                    </cfif>		
                                    <a class="btn btn-small btn-info" href="#local.baseURL#&panel=uploads&from=search" title="Upload a single document"><i class="icon-upload icon-large"></i> Upload</a>
                                    <a class="btn btn-small btn-danger" href="#local.baseURL#&from=search&panel=sendNote&fsDocumentID=#local.qryResults.documentID#" title="Notify Admin"><i class="icon-info-sign icon-large"></i> Notify</a>
                                </div>
                            </div>
                            <br/>
                        </div>
                    </div>
                </div>
            </cfoutput>

            <cfset local.filterParam = '#local.strSearch.filter_category1#|#local.strSearch.filter_category2#|1|#local.strSearch.filter_appInstanceID#'>
            <cfset local.postFilterParam = "">
            <cfif local.fileShares.alwaysShowFolders EQ "1" AND NOT local.settingsStruct.fileShareID EQ "">
                <cfif len(arguments.postfilter) gt 1>
                    <cfset local.itemCountParam = val(local.qryResults.count)>
                    <cfset local.postFilterParam = arguments.postfilter>
                <cfelse>
                    <cfset local.itemCountParam = local.strCount.itemcount>
                </cfif>
            <cfelse>
                <cfset local.itemCountParam = local.strCount.itemcount>
                <cfset local.filterParam = "">
            </cfif>
             <cfoutput>#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.itemCountParam, itemWord='item', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=local.sortType, filter=local.filterParam, postFilter=local.postFilterParam, viewDirectory=arguments.viewDirectory)#</cfoutput>
        </cfif>
    <cfelse>
        <!--- PLAT-447  top level split bucket by FS --->
        <!--- need new query to give back search split of FS ---->
        <cfset local.qrySplitBuckets = cacheSplitBucketCounts(arguments.searchid,arguments.bucketid)>
        <cfif local.qrySplitBuckets.recordCount>            
            <cfoutput>
            <div class="row-fluid">
                <div class="span12" style="margin-left:15px;">
                    <div class="visible-desktop">
                        <cfset local.midPoint = Int(local.qrySplitBuckets.recordcount / 2) + local.qrySplitBuckets.recordcount mod 2 />
                        <cfloop from='1' to='#local.midPoint#' index='local.x'> 
                            <div class="row-fluid">
                                <div class="span6">
                                    <a href="javascript:b_doSearchFilter(#arguments.searchid#,'0|0|#local.qrySplitBuckets['alwaysShowFolders'][local.x]#|#local.qrySplitBuckets['applicationInstanceID'][local.x]#');">
                                        <i class="icon-folder-close icon-large" title="Fileshare"></i> #local.qrySplitBuckets['fileShareName'][local.x]# (<strong>#local.qrySplitBuckets['itemCount'][local.x]#</strong>)
                                    </a>
                                </div>
                                <cfif local.midPoint + local.x lte local.qrySplitBuckets.recordcount>
                                    <div class="span6">
                                        <a href="javascript:b_doSearchFilter(#arguments.searchid#,'0|0|1|#local.qrySplitBuckets['applicationInstanceID'][local.midPoint+local.x]#');">
                                            <i class="icon-folder-close icon-large" title="Fileshare"></i> #local.qrySplitBuckets['fileShareName'][local.midPoint+local.x]# (<strong>#local.qrySplitBuckets['itemCount'][local.midPoint+local.x]#</strong>)
                                        </a>
                                    </div>
                                </cfif>
                            </div>
                        </cfloop>
                    </div>
                    <div class="hidden-desktop">
                        <cfloop query="local.qrySplitBuckets">
                            <div class="row-fluid">
                                <div class="span12">
                                    <a href="javascript:b_doSearchFilter(#arguments.searchid#,'0|0|#local.qrySplitBuckets.alwaysShowFolders#|#local.qrySplitBuckets.applicationInstanceID#');">
                                        <i class="icon-folder-close icon-large" title="Fileshare"></i> #local.qrySplitBuckets.fileShareName# (<strong>#local.qrySplitBuckets.itemCount#</strong>)
                                    </a>
                                </div>
                            </div>
                        </cfloop>
                    </div>
                </div>
            </div>
            </cfoutput>
        </cfif>
    </cfif>
</cfif>