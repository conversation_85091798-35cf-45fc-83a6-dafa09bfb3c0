<cfscript>
	variables.applicationReservedURLParams 	= "";
	local.customPage.baseURL	= "/?#getBaseQueryString(false)#";
	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	local.Organization			= arguments.event.getValue('mc_siteInfo.ORGShortName');
	// LOCAL SITE INFORMATION ------------------------------------------------------------------------------------------------
	local.orgID 				= event.getValue('mc_siteInfo.orgID');	
	local.orgCode 				= event.getValue('mc_siteInfo.orgCode');
	local.siteCode 				= event.getValue('mc_siteInfo.siteCode');
	local.siteID 				= event.getValue('mc_siteInfo.siteID');	
	
	local.memberID 				= session.cfcUser.memberData.memberID;
	local.qryMyECommunities 	= getMyECommunities(arguments.event.getValue('mc_siteinfo.siteid'),local.memberID );
	local.qryMyStorePurchases 	= getMyStorePurchases(arguments.event.getValue('mc_siteinfo.siteid'));
	local.qryLatestStoreProducts  = getLatestStoreProducts(arguments.event.getValue('mc_siteinfo.siteid'));
</cfscript>

<!--- Announcements --->
<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAnnouncements">
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">, @now datetime = getdate();

	SELECT n2.noticeID, dbo.fn_getResourcePagePlacementXML(@siteID, ai2.siteResourceID) as placementXML, tmp.contentTitle, ai2.applicationInstanceID
	FROM dbo.an_notices n2
	inner join an_centers c2 
		on n2.siteID = @siteID
		and n2.centerID = c2.centerID
	inner join cms_applicationInstances ai2 on ai2.applicationInstanceID = c2.applicationInstanceID and ai2.siteID = @siteID
	inner join 
		(
		select min(n.noticeID) as noticeID,
			noticeContent.contentTitle
		from dbo.an_notices n
		inner join cms_content content 
			on n.siteID = @siteID
			and content.siteID = @siteID
			and content.contentID = n.noticeContentID
		inner join cms_siteResources sr on sr.siteID = @siteID and n.siteID = @siteID and content.siteResourceID = sr.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		inner join an_centers c on n.centerID = c.centerID
		inner join cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID and ai.siteID = @siteID
		inner join cms_siteResources sr2 on sr2.siteID = @siteID and n.siteResourceID = sr2.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs2 on srs2.siteResourceStatusID = sr2.siteResourceStatusID and srs2.siteResourceStatusDesc = 'Active'
		inner join dbo.cms_contentLanguages as noticeContent on noticeContent.contentID = n.noticeContentID and noticeContent.languageID = <cfqueryparam value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#" cfsqltype="CF_SQL_INTEGER">
		where @now between n.startdate and n.enddate
		 and dbo.fn_cache_perms_getResourceRightsXML(n.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,@siteID).exist('(/rights/right[@allowed="1"])[1]') = 1
		group by noticeContent.contentTitle
		) tmp
	on n2.noticeID = tmp.noticeID

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
</cfquery>

<!--- Search History --->
<cfquery datasource="#application.dsn.TLASites_search.dsn#" name="local.qrySearch" maxrows="10">
	select top(10) searchID, dateEntered, bucketIDOrigin,searchVerbose
	from dbo.tblSearchHistory sh, dbo.tblSearchBuckets sb
	where depoMemberDataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.depoMemberDataID#">
	and sh.bucketIDorigin = sb.bucketID	
	and siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
	order by dateEntered DESC
</cfquery>

<cfset local.upcomingEvents = getRegisteredEvents(siteID=event.getValue('mc_siteInfo.siteID'))>

<cfsavecontent variable="local.evhead">
	<cfoutput>
	<script language="javascript">
	<cfoutput>
		$(document).ready(function(){
			if($('##tab11 ##zoneC .zoneresource').html() == ""){
				$('##leg_div').hide();
			}else{
				$('##leg_div').show();

			}

		});
	</cfoutput>
		<cfif local.upcomingEvents.recordCount gt 0>
			<cfoutput>
			function viewRegister(eid, rid) {
				$.colorbox( {innerWidth:800, innerHeight:500, href:'/?pg=events&evAction=printReg&eID=' + eid + '&mid=#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)#&registrantID=' + rid + '&mode=direct', iframe:true, overlayClose:false} );
				return false;
			}
			</cfoutput>
		</cfif>
			<cfoutput>
			function viewEvent(eid) {
				window.location = '/?pg=events&evAction=showDetail&eID=' + eid;
				return false;
			}
			</cfoutput>
		
	</script>
	<script type="text/javascript" language="javascript" src="/javascript/jquery.idTabs.js"></script>
	</cfoutput>	
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evhead)#">

<cfoutput>
	<div class="row-fluid">
		<div class="span12">
			<div class="dev_pro_img">
				<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
					<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
				<cfelse>
					<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
				</cfif>
			</div>
			<div class="dev_pro_heding">
				 <h4>#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#</h4>
			     <a href="/?pg=updateMember">View/Edit My Profile</a>
			</div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span12 dev_nxt_mainbox">
            <div class="row-fluid">
              	<div class="span4 tbboxmain_dev1">
                    <div class="tabbable">
                    	<ul class="nav nav-tabs custom_navtbs">
							<li ><a href="##tab1" data-toggle="tab">Upcoming Events</a></li>
							<li class="active"><a href="##tab2" data-toggle="tab" >My Events</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane " id="tab1">
								<div class="tb_box1">
									[[UpcomingEvents maxrows=[4] includeRegistered=[0] format=[json] jsonvariable=[upcomingevents]]]
									<div class="mcMergeTemplate" data-mcjsonvariable="upcomingevents">
										{{##if events}} 
											<div class="row-fluid">
												<div class="MastheadBody">
												<strong>Learn more about these upcoming events:</strong>
												</div>
												<div>
													<ul class="box-list">
														{{##events}}
														<li><span>{{{title}}}&nbsp;&nbsp;</span><a href="##" onClick="return viewEvent('{{{id}}}');">(View Details)</a></li>
														{{/events}} 
													</ul>
													<p><a style="color:##000" href="/?pg=events"><strong>View Full Calendar</strong></a></p>
												</div>
											</div>
										{{else}}
											<ul class="box-list">
												<li><strong>There are currently no upcoming Events.</strong></li>
											</ul>
										{{/if}}	
									</div>
								</div>
							</div>
							<div class="tab-pane active" id="tab2">
								<div class="tb_box1">
									<cfif local.upcomingEvents.recordCount>
										<div class="row-fluid">
											<div class="MastheadBody">
												<strong>You are currently registered for the following:</strong>
											</div>
											<div>
												<ul class="box-list">
													<cfloop query="local.upcomingEvents"><li><span>#local.upcomingEvents.eventTitle#&nbsp;&nbsp;<a href="##" onClick="return viewRegister('#local.upcomingEvents.EventID#', '#local.upcomingEvents.registrantID#');">(View Details)</a></span></li></cfloop>
												</ul>
											</div>
										</div>
									<cfelse>
										<p>
											<strong>You are currently not registered for any Events.</strong>
										</p>
									</cfif>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="span4 tbboxmain_dev2">
                    <div class="tabbable">
                    	<ul class="nav nav-tabs custom_navtbs">
							<li class="active"><a href="##tab3" data-toggle="tab">committees</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active " id="tab3">
								<div class="tb_box1 tb_box10">
				  					<cfif local.qryMyECommunities.recordCount>
										<div id="MyeCommunities" class="section">
											<div class="sectionContent bl_color">
					            				<div class="sectionTitle">
					            					<b>You have access to the following groups:</b>
					            				</div>
					            				<div>
					            					<ul class="box-list">
														<cfloop query="local.qryMyECommunities">
															<li><span>#local.qryMyECommunities.communityName# </span><a href="/?#getAppBaseLink(local.qryMyECommunities.applicationInstanceID,arguments.event.getValue('mc_siteinfo.siteid'))#">(View)</a></li>
														</cfloop>
													</ul>
			            						</div>
											</div>
										</div>
									<cfelse>
										<div id="MyeCommunities" class="section">
											<div class="sectionTitle">You are not currently a member of any Committees.</div><br />
											<div class="sectionContent">To learn more about joining a Committee, please call FHPCA at (850) 878-2632.</div>
										</div>
									</cfif>	
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="span4 tbboxmain_dev3">
					<div class="tabbable">
						<ul class="nav nav-tabs custom_navtbs">
                        	<li class="active"><a href="##tab5" data-toggle="tab">Announcements</a></li>
                        	<li><a href="##tab6" data-toggle="tab">Featured Resource</a></li>
                      	</ul>
						<div class="tab-content">
                        	<div class="tab-pane active" id="tab5">
                        		<div class="tb_box1 tb_box3">
									<cfif local.qryAnnouncements.recordcount NEQ 0>
										<ul class="box-list">
											<cfloop query="local.qryAnnouncements">
												<cfset local.placementXML = xmlParse(local.qryAnnouncements.placementXML)>
												<cftry>
													<cfset local.representivePageTitle = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageTitle>
													<cfcatch type="any">
														<cfset local.representivePageTitle = "Untitled Page"/>
													</cfcatch>	
												</cftry>
												<cftry>
													<cfset local.representivePageName = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageName>
													<cfcatch type="any">
														<cfset local.representivePageName = "Unnamed Page"/>
													</cfcatch>	
												</cftry>
			
												<cftry>
													<cfif len(local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName)>
														<cfset local.representiveCommunityPageName = local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName/>
														<cfset local.pageLink = "/?pg=#local.representiveCommunityPageName#&commpg=#local.representivePageName#">
														<cfelse>
														<cfset local.pageLink = "/?#getAppBaseLink(local.qryAnnouncements.applicationInstanceID,arguments.event.getValue('mc_siteinfo.siteid'))#">
													</cfif>
													<cfcatch type="any">
														<cfset local.representivePageName = ""/>
														<cfset local.pageLink = "##">
													</cfcatch>	
												</cftry>
			
												<li><a href="#local.pageLink#">#local.qryAnnouncements.contentTitle#</a></li>
											</cfloop>
										</ul>
									<cfelse>
										<div class="sectionContent">
											<div class="MastheadBody"><strong>Currently, there are no announcements.</strong></div>
										</div>
									</cfif>				
								</div>
							</div>
							<div class="tab-pane" id="tab6">
                          		<div class="tb_box1 tb_box3">
                          			<!--ZONEB-->
                          		</div>
                          	</div>
						</div>
					</div>
				</div>
			</div>
		</div>
    </div>
	<div class="row-fluid">
		<div class="span12 dev_nxt_mainbox1">
			<div class="row-fluid">
				<div class="span8 tbboxmain_dev4">
					<div class="tabbable">
						<ul class="nav nav-tabs custom_navtbs">
							<li class="active"><a href="##tab7" data-toggle="tab">Quick Links</a></li>
							<li><a href="##tab8" data-toggle="tab">Recent Searches</a></li>
							<li><a href="##tab9" data-toggle="tab">My Purchases</a></li>
							<li><a href="##tab10" data-toggle="tab">New Store Products</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active" id="tab7">
                                <div class="tb_box2">
                                	<!--ZONEA-->
                                </div>
                            </div>
                            <div class="tab-pane" id="tab8">
                            	<div class="tb_box2">
									<cfif local.qrySearch.recordCount>
										<cfsavecontent variable="local.recentSearches">
											<cfloop query="local.qrySearch">
												<cfset local.strVerbose = application.objSearchTranslate.printVerboseString(local.qrySearch.searchVerbose,'	','<br/>')>
												<cfset local.strVerbose = ReplaceNoCase(local.strVerbose,"Section:","<span class='searchTitle'>Section:</span>","ALL")>
												<cfset local.strVerbose = ReplaceNoCase(local.strVerbose,"Contains:",'<span class="searchTitle">Contains:</span>',"ALL")>
												<li><a href="/?pg=search&bid=#local.qrySearch.bucketIDOrigin#&s_a=doSearch&sid=#local.qrySearch.searchID#">#local.strVerbose#</a>#dateFormat(local.qrySearch.dateEntered,"mm/dd/yyyy")#</li>
											</cfloop>
										</cfsavecontent>
										<div id="RecentSearches" class="section">
											<div class="MastheadBody"><strong>My Recent Searches</strong></div>
											
											<div class="sectionContent">
												<cfif local.qrySearch.recordcount NEQ 0><ul>#local.recentSearches#</ul></cfif>
											</div>		
										</div>
									<cfelse>
										<div id="RecentSearches" class="section">
											<div class="MastheadBody"><strong>You have no recent searches.</strong></div>
										</div>
									</cfif>
								</div>
							</div>
							<div class="tab-pane" id="tab9">
								<div class="tb_box2">
									<cfif local.qryMyStorePurchases.recordcount>	
										
										<table width="98%" cellpadding="2" cellspacing="0" border="0" class="table table-striped table-hover" >
											<tr>
												<th width="90"><strong>Date</strong></th>
												<th><strong>Order Number</strong></th>
												<th><strong>Order Amount</strong></th>
											</tr>
											<cfset local.oddeven = 0>
											<cfloop query="local.qryMyStorePurchases">
												<cfset local.oddeven = local.oddeven + 1>
										
												<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
													<td>#dateformat(local.qryMyStorePurchases.DateOfOrder,"mm/d/yyyy")#</td>
													<td><a href="/?pg=store&sa=viewReceipt&ordernumber=#local.qryMyStorePurchases.orderNumber#">#arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryMyStorePurchases.orderID,"0000")#</a></td>
													<td nowrap>#dollarFormat(local.qryMyStorePurchases.totalFee)#</td>
												</tr>	
											</cfloop>
										</table>
										
									<cfelse>
										<div>
											<p><strong>
											There are no store purchases to display.</strong></p>
										</div>
									</cfif>	
								</div>
							</div>
							<div class="tab-pane" id="tab10">
              					<div class="tb_box2">
              						<cfif local.qryLatestStoreProducts.recordcount>	
										<b>These are the latest products added to the FHPCA Store</b>
										<br /><br />
										<table width="98%" cellpadding="2" cellspacing="0" border="0" class="table table-striped table-hover" >
										<tr>
											<th><strong>Product</strong></th>
										</tr>
										
										<cfset local.oddeven = 0>
										<cfloop query="local.qryLatestStoreProducts">
											<cfset local.oddeven = local.oddeven + 1>
											
											<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
												<td><a href="/?pg=store&sa=ViewDetails&ItemID=#local.qryLatestStoreProducts.ItemID#&cat=0" target="_blank">#local.qryLatestStoreProducts.contentTitle#</a></td>
											</tr>	
														
										</cfloop>										
										</table>										
									<cfelse>
										<div>
											<p>
											There are no store products to display.</p>
										</div>
									</cfif>	
									<div class="sectionContent">
										<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top:35px;" class="bottomLinks">
											<tr>
												<td width="5%" style="vertical-align:top;">
													<div class="circle-tile-heading-small green pull-right">
														<div class="circle-text-small">
															<a href="/?pg=store"><i class="icon-book" style="color:##fff">&nbsp;</i></a>
														</div>
													</div>
												</td>
												<td style="text-align:left;padding-left:3px;"><a href="/?pg=store">Browse the Online Store</td> 
											</tr>
										</table>
									</div>
              					</div>
              				</div>
              			</div>
              		</div>
              	</div>
              		<div id="leg_div" class="span4 tbboxmain_dev5">
  			<div class="tabbable">
				<ul class="nav nav-tabs custom_navtbs">
					<li class="active"><a href="##tab11" data-toggle="tab">legislative updates</a></li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="tab11">
						<div class="tb_box1 tb_box3">
							<!--ZONEC-->
						</div>
					</div>
				</div>
			</div>
		</div>
             </div>
        </div>  	
	</div>
</cfoutput>

<cffunction name="getRegisteredEvents" access="private" returntype="query">
	<cfargument name="siteID" type="numeric" required="true">
	
	<cfset var qryData = "">

	<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryData">
		set nocount on;

		declare @nowDate datetime;
		set @nowDate = getdate();

		-- get events on site
		IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
			DROP TABLE ##tmpEventsOnSite;
		CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
			startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
			displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
			displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, 
			altRegistrationURL varchar(300), eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
			categoryIDList varchar(max));
		EXEC dbo.ev_getEventsOnSite @siteID=#arguments.siteID#, @startDate=@nowDate, @endDate=null, @categoryIDList='';

		SELECT top 3 e.eventID, tmp.eventTitle, r.registrantid
		FROM dbo.ev_events e
		INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
		INNER JOIN dbo.ev_registration AS rn ON rn.eventID = e.eventID AND rn.siteID = e.siteID       
		INNER join dbo.ev_registrants AS r ON r.registrationID = rn.registrationID
		INNER JOIN dbo.ams_members AS mReg ON mReg.memberID = r.memberID
		WHERE e.status = 'A'
		AND rn.status = 'A'
		AND r.status = 'A'
		AND mReg.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
		ORDER BY tmp.startTime;

		IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
			DROP TABLE ##tmpEventsOnSite;
	</cfquery>

	<cfreturn qryData>
</cffunction>

<cffunction name="getMyECommunities" returntype="query">
	<cfargument name="siteID" type="numeric" required="yes">
	<cfargument name="memberID" type="numeric" required="yes">

	<cfset var local = structNew()>
		
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
		SET NOCOUNT ON;

		declare @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
		declare @memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;
		declare @groupPrintID int;

		select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @memberID;

		SELECT DISTINCT ai.applicationInstanceID, c.communityName
		FROM dbo.cms_applicationInstances ai
		INNER JOIN dbo.comm_communities AS c ON c.applicationInstanceID = ai.applicationInstanceID
		INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID 
			and ai.siteResourceID = sr.siteResourceID 
			and sr.siteResourceStatusID = 1
		INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID =  @siteID
			and srfrp.siteResourceID = sr.siteResourceID
			and srfrp.functionID = 4
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON srfrp.rightPrintID = gprp.rightPrintID 
			AND gprp.groupPrintID = @groupPrintID 
			AND gprp.siteID = @siteID
			AND gprp.rightPrintID is not null
		where ai.siteID = @siteID
		ORDER BY c.communityName;
	</cfquery>
	
	<cfreturn local.data>
</cffunction>

<cffunction name="getLatestStoreProducts" returntype="query">
	<cfargument name="siteID" type="numeric" required="yes">
		
	<cfset var local = structNew()>
	
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
		SET NOCOUNT ON;

		DECLARE @storeID int;

		SELECT @storeID = storeID
		FROM dbo.store
		WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteId#">;

		SELECT top 5 p.ItemID, pc.contentTitle
		FROM dbo.store_Products p
		inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
		where p.storeID = @storeID
		order by p.itemID desc;
	</cfquery>
	
	<cfreturn local.data>
</cffunction>

<cffunction name="getMyStorePurchases" returntype="query">
	<cfargument name="siteID" type="numeric" required="yes">
		
	<cfset var local = structNew()>
	
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
		SET NOCOUNT ON;

		declare @orgID int, @siteID int;
		set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteId#">;
		select @orgID = orgID from dbo.sites where siteID = @siteID;

		SELECT o.orderID, o.DateOfOrder, o.orderNumber, isnull(ttl.totalFee,0) as totalFee
		FROM dbo.store_orders as o
		cross apply (
			select sum(ts.cache_amountAfterAdjustment) as totalFee
			from dbo.fn_store_orderTransactions(@orgID,o.orderid) as rt
			inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = rt.transactionID
		) as ttl
		INNER JOIN dbo.store as s on s.storeID = o.storeID and s.siteID = @siteID
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = o.memberID 
		INNER JOIN dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
		WHERE o.orderCompleted = 1	
		and mActive.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		ORDER BY DateOfOrder desc;
	</cfquery>
	
	<cfreturn local.data>
</cffunction>