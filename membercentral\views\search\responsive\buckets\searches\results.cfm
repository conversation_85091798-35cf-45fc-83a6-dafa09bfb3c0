<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
</cfoutput>

<cfif local.hasDepositionBucket>
	<cfoutput>
	<div style="padding-bottom:10px;">
		<button class="btn bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
	</div>
	</cfoutput>
</cfif>				

<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		#showCommonNotFound(bucketID=arguments.bucketID, searchID=arguments.searchID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
<cfelse>
	<cfoutput>
	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='search', itemWordSuffix='es', viewDirectory=arguments.viewDirectory)#
	</cfoutput>
	<div class="clearfix"></div>
	<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>
	<!--- results table --->
	<cfset local.outerCurrentrow = 0>
	<cfoutput query="local.qryResults" group="depomemberdataid">
		<cfset local.outerCurrentrow = local.outerCurrentrow + 1>
		<div class="s_row<cfif local.outerCurrentrow mod 2 is 0> s_row_alt</cfif>">
			<cfif variables.cfcuser_DisplayVersion IS 1>
				#local.qryResults.firstname# #left(local.qryResults.lastname,1)#... <i>(upgrade to view full search information)</i>
			<cfelse>
				<cfif len(local.qryResults.email) and isValid("regex",local.qryResults.email,application.regEx.email)>
					<a href="mailto:#local.qryResults.email#"><b>#local.qryResults.firstname# #local.qryResults.lastname#</b></a>
				<cfelse>
					<b>#local.qryResults.firstname# #local.qryResults.lastname#</b>
				</cfif><br/>
				<cfset local.tmp = "">
				<cfif len(local.qryResults.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingfirm)></cfif>
				<cfif len(local.qryResults.billingaddress)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingaddress)></cfif>
				<cfif len(local.qryResults.billingaddress2)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingaddress2)></cfif>
				<cfif len(local.qryResults.billingcity)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingcity)></cfif>
				<cfif len(local.qryResults.billingstate)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingstate)></cfif>
				<cfif len(local.qryResults.phone)><cfset local.tmp = listAppend(local.tmp,"Ph: " & local.qryResults.phone)></cfif>
				#replace(local.tmp,",",", ","ALL")#
			</cfif>								
			<div style="margin:4px 10px;">
			<table cellpadding="2" cellspacing="0">
			<cfoutput>
				<cfset local.strVerbose = application.objSearchTranslate.printVerboseString(local.qryResults.searchVerbose,' ','<br/>')>
				<tr valign="top">
					<td width="80">#dateformat(local.qryResults.dateentered,"mm/dd/yyyy")#</td>
					<td>#local.strVerbose#</td>
				</tr>
			</cfoutput>
			</table>
			</div>
		</div>
	</cfoutput>
	<cfoutput>
	<br/>
	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startRow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='search', itemWordSuffix='es', viewDirectory=arguments.viewDirectory)#
	</cfoutput>
</cfif>