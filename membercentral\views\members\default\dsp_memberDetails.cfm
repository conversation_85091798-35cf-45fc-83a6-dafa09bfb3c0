<cfif attributes.data.qryMembersCount is 0 or val(attributes.data.arrMembers[1].qryMember.memberid) is 0>
	<div class="tsAppBodyText">Sorry, that member was not found in the directory.</div>
<cfelse>
	<cfset local.thisMember = attributes.data.arrMembers[1]>

	<cfset local.stFullName = local.thisMember.stFullName>
	<cfsavecontent variable="local.css">
		<style type="text/css">
		ul.groupImage { width:100%; text-align:left; float:left; padding:0; padding-left:0; }
		ul.groupImage li.groupImage { float:left; padding:0; padding-left:0; list-style-type:none; list-style-position:inside; }
		div#divContactForm div { line-height:1.6em; padding-bottom:12px; }
		div#mc_md_stats dl { margin-left:0px; padding-top:8px; font-size:1.1em; }
		div#mc_md_stats dd { margin-left:10px; display:inline; font-weight:bold; }
		div#mc_md_stats dt { display:inline; padding-left:10px; }
		#recordListContainer td {border:0;padding:0;}
		#recordListContainer div.span12 {width:375px !important; border-bottom:2px solid grey;margin:0;padding:0;margin-bottom:20px;padding-top:5px;}
		#recordListContainer [class*="span"] {float: left;}
		</style>
	</cfsavecontent>
	<cfhtmlhead text="#local.css#">
	
	<cfsavecontent variable="local.pageJS">
	<cfoutput>
		<script type="text/javascript" src="/assets/common/javascript/polyfills/media-match/2.0.2/media.match.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/enquire.js/2.0.2/enquire.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/inview/jquery.inview.min.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/mcapps/listviewer/listviewer-common.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/mcapps/listviewer/responsive-listviewer.js"></script>
		<script language="javascript">
			var jsonURL = '/?event=proxy.ts_json&c=MEM&m=getLinkRecordsList';
			var endOfListRecordslist=false;
			var isInitialLoadDone = false;
			var currentlyLoadingMessages = false;
			var autoLoadFirstMessage = true;
			var currentlyLoadingMessages = false;
			var row = 0;
			var posStart = 0;
			
			$(function() {
				if(!posStart) loadMembers();
				
				$('##endOfRecordsList').bind('inview', function(event, isInView, visiblePartX, visiblePartY) {
					if (isInView) {
					  /*element is now visible in the viewport*/
					  loadMembers();
					} else {
					  /*element has gone out of viewport*/
					}
				});
			});
			
			function loadMembers() {
				if (!currentlyLoadingMessages && !endOfListRecordslist) {
					currentlyLoadingMessages = true;					
					$('##loadingIndicator').show();
					$.ajax({
						url: jsonURL,
						type: 'POST',
						data: {orgID:#attributes.data.instanceSettings.orgID#, siteResourceID:#attributes.data.instanceSettings.siteResourceID#, memberDirectoryID:#attributes.data.instanceSettings.memberDirectoryID#, memberID:#attributes.data.activeMemberID#, showPhotoRule:#attributes.data.memberDirectoryInfo.showPhotoRule#, baseQryStr:'#attributes.data.baseQueryString#', posStart:posStart},
						dataType: 'json',
						success: function(response) {
							if($('##initialLoadingIndicator').is(':visible')) 
								$('##initialLoadingIndicator').hide();
										
							if(response.SUCCESS == true) {
								if(response.TOTALCOUNT == 0) {
									endOfListRecordslist = true;
									if($('##recordListContainer tr').length == 0){
										$('##recordListContainer').append('<tr><td>No Linked Records found.</td></tr>');
									}
								} else {
									var msg = [];
									for(var i=0;i<response.DATA.length;i++) {
										msg.push(response.DATA[i]);
									}
									$('##recordListContainer').append('<tr><td>'+msg.join("")+'</td></tr>');
									
									posStart += response.DATA.length;
									
									if(response.DATA.length > 0 && ! $('##linkedRecordsList').is(':visible')) 
										$('##linkedRecordsList').show();
								}
								$('##loadingIndicator').hide();
								currentlyLoadingMessages = false;	
							}
						},
						error: function(ErrorMsg) {
							$('##initialLoadingIndicator').hide();
							$('##loadingIndicator').hide();
							alert(ErrorMsg);
						}
					});
					currentlyLoadingMessages = false;
				}
			}
		</script>
	</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.pageJS#">
	
	
	<cfoutput>
	<div class="tsAppBodyText">
		<span style="float:right;">
			<a href="/?#attributes.data.baseQueryString#&dirAction=search">Search Directory</a>
		</span>
		<span class="tsAppHeading">#attributes.data.memberDirectoryInfo.applicationInstanceName# Details</span>
	</div>
	<br/>

	<table class="memDetailTable" cellpadding="6" cellspacing="0" border="0" width="100%"> 
	<tr valign="top">

	<cfset local.hasArrClassifications = arrayLen(local.thisMember.arrAboveClassifications) OR arrayLen(local.thisMember.arrBelowClassifications)>
	<cfif attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowPhoto>
		<cfif len(trim(local.thisMember.imgToUse)) OR local.hasArrClassifications>
			<td width="82" class="tsAppBT20">
				<cfif arrayLen(local.thisMember.arrAboveClassifications)>
					<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
						<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
					</cfloop>
				</cfif>

				<cfif len(trim(local.thisMember.imgToUse))>#local.thisMember.imgToUse#</cfif>
				
				<cfif arrayLen(local.thisMember.arrBelowClassifications)>
					<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
						<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
					</cfloop>
				</cfif>
			</td>
		</cfif>
	<cfelseif local.hasArrClassifications>
		<td width="1" class="tsAppBT20">
			<cfif arrayLen(local.thisMember.arrAboveClassifications)>
				<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
					<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
				</cfloop>
			</cfif>
			<cfif arrayLen(local.thisMember.arrBelowClassifications)>
				<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
					<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
				</cfloop>
			</cfif>
		</td>
	</cfif>

		<td class="tsAppBodyText tsAppBT20" style="width:600px !important;">
			<!--- Name/company --->
			<cfif len(local.thisMember.stFullName)>
				<b>#local.thisMember.stFullName#</b><br/>
			</cfif>
			<cfif not len(local.thisMember.stFullName) and len(local.thisMember.stCompany)>
				<b>#local.thisMember.stCompany#</b><br/>
			<cfelseif len(local.thisMember.stCompany)>
				#local.thisMember.stCompany#<br/>
			</cfif>

			<!--- addresses --->
			<cfif arrayLen(local.thisMember.mc_combinedAddresses)>
				<cfloop index="local.addri" from="1" to="#arrayLen(local.thisMember.mc_combinedAddresses)#">
					<cfif len(local.thisMember.mc_combinedAddresses[local.addri].addr)>
						<br/>
						<cfif attributes.data.multipleAddressTypesDetected>
							<strong>#local.thisMember.mc_combinedAddresses[local.addri].label#</strong><br/>
						</cfif>
						#local.thisMember.mc_combinedAddresses[local.addri].addr#
					</cfif>	
				</cfloop>
				<br/>
			</cfif>

			<!--- phones --->
			<cfloop array="#local.thisMember.arrPhones#" index="local.thisField">
				<cfif len(local.thisField.phone) and attributes.data.makePhoneNumbersClckable>
					#htmlEditFormat(local.thisField.fieldLabel)#: <a href="tel:#local.thisField.phone#">#local.thisField.phone#</a><br/>
				<cfelseif len(local.thisField.phone)>
					#htmlEditFormat(local.thisField.fieldLabel)#: #local.thisField.phone#<br/>
				</cfif>
			</cfloop>

			<!--- emails --->
			<cfloop array="#local.thisMember.arrEmails#" index="local.thisField">
				<cfif len(local.thisField.email)>
					#htmlEditFormat(local.thisField.fieldLabel)#: <a href="mailto:#local.thisField.email#">#local.thisField.email#</a><br/>
				</cfif>
			</cfloop>

			<!--- websites --->
			<cfloop array="#local.thisMember.arrWebsites#" index="local.thisField">
				<cfif len(local.thisField.website)>
					#htmlEditFormat(local.thisField.fieldLabel)#: <a href="#local.thisField.website#" target="_blank">#local.thisField.website#</a><br/>
				</cfif>
			</cfloop>

			<!--- website social icons --->
			<cfif ArrayLen(local.thisMember.arrWebsitesSocialIcons) gt 0 OR
					attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowVcard or 
						(attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr))>
				<div style="padding:2px;">
					<cfloop array="#local.thisMember.arrWebsitesSocialIcons#" index="local.thisField">
							#local.thisField.socialLink#
					</cfloop>

					<!--- VCARD / Google Maps link --->
					<cfif attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowVcard>
						<a href="/?event=cms.showResource&dirAction=memberDetailsVCard&resID=#attributes.data.instanceSettings.siteResourceID#&mode=stream&dirMemberid=#local.thisMember.memberID#">
							<img src="/assets/common/images/vcard-icon3.png" alt="[vCard]" width="32" height="32" border="0"></a>
					</cfif>
					<cfif attributes.data.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].detailsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr)>
						<cfset local.thisMap = attributes.data.mappingBaseLink & URLEncodedFormat(local.thisMember.mc_combinedAddresses[1].mapaddr)>
						<a href="#local.thisMap#" target="_blank"><img src="/assets/common/images/Maps-icon.png" alt="[View Map]" width="32" height="32" border="0"></a>
					</cfif>
				</div>
			</cfif>
				
			<!--- membernumber --->
			<cfif len(local.thisMember.stMemberNumber)>#local.thisMember.stMemberNumber#<br/></cfif>
						
			<!--- memberdata / districting --->
			<cfloop array="#local.thisMember.arrMemberDataDistricting#" index="local.thisField">
				<cfif len(local.thisField.value)>
					#htmlEditFormat(local.thisField.fieldLabel)#: 
					<cfif local.thisField.allowMultiple is 1>
						#ReplaceNoCase(local.thisField.value,"|",", ","ALL")#<br/>
					<cfelseif local.thisField.dataTypeCode EQ "DOCUMENTOBJ">
						<cfset local.stDocEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.thisField.columnID#|#right(GetTickCount(),5)#|#local.thisMember.memberID#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
						<cfoutput><a href="/?event=cms.showResource&dirAction=memberDetailsDocDownload&resID=#attributes.data.instanceSettings.siteResourceID#&mode=stream&doc=#local.stDocEnc#">#local.thisField.value#</a><br/></cfoutput>
					<cfelse>
						#local.thisField.value#<br/>
					</cfif>
				</cfif>
			</cfloop>

			<!--- member prof license data --->
			<cfloop array="#local.thisMember.arrLicenseData#" index="local.thisField">
				<cfif len(local.thisField.value)>
					#htmlEditFormat(local.thisField.fieldLabel)#: #local.thisField.value#<br/>
				</cfif>
			</cfloop>

			<!--- contact form --->
			<cfif attributes.data.offerContactForm and (len(local.thisMember.stFullName) or len(local.thisMember.stCompany))>
				<br/><br/>
				<fieldset class="tsApp">
				<legend class="tsAppLegend">Contact <cfif len(local.thisMember.stFullName)>#local.thisMember.stFullName#<cfelseif len(local.thisMember.stCompany)>#local.thisMember.stCompany#</cfif></legend>
				<cfif attributes.data.contactMsg eq "sent">
					Message has been sent.
				<!--- exceeded 10 contact submissions per session --->
				<cfelseif structKeyExists(session,"MCMemDirSendContactCounter") AND session.MCMemDirSendContactCounter GT 9>
					<div class="alert">You have reached the maximum number of messages allowed.</div>
				<cfelse>
					<cfif len(attributes.data.memberDirectoryInfo.contactContent)>
						<div>#attributes.data.memberDirectoryInfo.contactContent#</div>
					</cfif>
					<cfform name="frmContactMember" action="/?#attributes.data.baseQueryString#&dirAction=sendContact" method="post" onSubmit="return validateContactFrm();">
					<cfinput type="hidden" name="dirMemberID" value="#local.thisMember.qryMember.memberID#">
					<div id="divContactForm" class="tsAppBodyText" style="width:96%;">
						<div>
							Name:<br/>
							<cfinput type="text" id="frmName" name="frmName" style="width:100%;" value="#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#">
						</div>
						<div>
							E-mail:<br/>
							<cfinput type="text" id="frmEmail" name="frmEmail" style="width:100%;" value="#session.cfcuser.memberdata.email#">
						</div>
						<div>
							Phone:<br/>
							<cfinput type="text" id="frmPhone" name="frmPhone" style="width:100%;" value="">
						</div>
						<div>
							Message:<br/>
							<textarea id="frmMsg" name="frmMsg" style="width:100%;height:140px;"></textarea>
						</div>
						<div id="divContactErr" style="display:none;margin-bottom:6px;"></div>
						<div>
							<button type="submit" class="tsAppBodyButton">Send Message</button>
						</div>
					</div>
					<cfset local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init()>
					<cfinclude template="/model/cfformprotect/cffp.cfm" />
					</cfform>
					</fieldset>

					<cfsavecontent variable="local.contactJS">
						<cfoutput>
						<style type="text/css">
						.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
						</style>
						<script language="JavaScript">
						function hideAlert() { $('##divContactErr').html('').hide(); };
						function showAlert(msg) { $('##divContactErr').html(msg).attr('class','alert').show(); };

						function validateContactFrm() {
							hideAlert();
							var arrReq = new Array();

							if ($.trim($('##frmName').val()).length == 0) arrReq[arrReq.length] = 'Enter your name.';
							if ($.trim($('##frmEmail').val()).length == 0 && $.trim($('##frmPhone').val()).length == 0) arrReq[arrReq.length] = 'Enter a valid e-mail address or contact phone number.';
							if ($.trim($('##frmEmail').val()).length > 0) {
								var urlRegEx = new RegExp("#application.regEx.email#", "gi");
								if(!(urlRegEx.test($('##frmEmail').val()))) arrReq[arrReq.length] = 'Enter a valid e-mail address.';
							}
							if ($.trim($('##frmMsg').val()).length == 0) arrReq[arrReq.length] = 'Enter your message.';

							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								return false;
							}
							trigersubmissionEvent();
							return true;
						}
						function trigersubmissionEvent(){
							MCLoader.loadJS('/assets/common/javascript/mcapps/members/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
							.then( () => {
								try {
									trigerSearchDetailssubmissionEvent('#attributes.data.pageName#');						
								} catch (error) {
									console.error("Error parsing JSON:", error.message);
								}
							});
						}
						</script>
						</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#local.contactJS#">
				</cfif>
			</cfif>
			
			<div id="initialLoadingIndicator">
				<div style="width: 100%;"><strong><img src="/assets/common/images/progress.gif" height="20" /> Loading Linked Records</strong></div>
			</div>
		
			<div id="linkedRecordsList" style="display:none;margin-top:25px;">
				<h4>Linked Records</h4>
				<div id="linkedRecordsListDivContainer">
					<div id="linkedRecordsListDiv">
						<table class="table">
							<tbody id="recordListContainer"></tbody>
						</table>
						<div id="endOfRecordsList">
							<div id="loadingIndicator">
							  <div style="width: 100%;">
								  <strong><img src="/assets/common/images/progress.gif" height="20" /> Loading Linked Records</strong>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			
		</td> 
		<cfset local.showRightPanel = attributes.data.showStats OR arrayLen(local.thisMember.arrClValues) OR arrayLen(local.thisMember.arrUnderClassifications)>
		<td class="tsAppBodyText tsAppBT20" style="width:250px !important;<cfif local.showRightPanel>border-left: 1px solid ##ccc;</cfif>">
			<!--- profile stats --->
			<cfif attributes.data.showStats>
				<fieldset class="tsApp">
				<legend class="tsAppLegend">Statistics (Last 30 Days)</legend>
				<div id="mc_md_stats">
				<cfloop array="#attributes.data.arrListingStats#" index="local.thisEl">
					<dl><dd>#local.thisEl.count#</dd><dt>#local.thisEl.label#</dt></dl>
				</cfloop>
				</div>
				</fieldset>
				<br/><br/>
			</cfif>

			<cfif arrayLen(local.thisMember.arrClValues)>
				<cfset local.clTitle = "">
				<cfloop array="#local.thisMember.arrClValues#" index="local.thisClItem">
					<cfif local.clTitle neq local.thisClItem.clName>
						<cfif local.clTitle neq ""><br/></cfif>
						<cfset local.clTitle = local.thisClItem.clName>
						<b>#local.thisClItem.clName#</b><br>
					</cfif>
					#local.thisClItem.groupName#<br/>
				</cfloop>
				<br/>
			</cfif>
			&nbsp;
			<cfif arrayLen(local.thisMember.arrUnderClassifications)>
				<ul class="groupImage">
				<cfloop array="#local.thisMember.arrUnderClassifications#" index="local.thisImg">
					<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
				</cfloop>
				</ul>
			</cfif>
			<img src="/assets/common/images/spacer.gif" style="width:250px !important;" height="1" />
		</td> 
	</tr>
	</table>
	</cfoutput>				
</cfif>
