
<cfsavecontent variable="local.upgradeTrialSmithStep3Head">	
	<cfoutput>
	<script type="text/javascript">          
		function editUpgradeTrialSmithStep4() {
			$('##upgradeTrialSmithSelectedTermContainer').hide();
			$('##upgradeTrialSmithTermContainer').show(300);
		}
        
		function agreeSubmit(isAgree){
            var iagree = {};
            if(isAgree) iagree = {iagree:isAgree};
            $('##upgradeTrialSmithStep3SaveLoading')
                .html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
                .load('#arguments.event.getValue('upgradetrialsmithurl')#&action=saves3',iagree,
                    function(resp) {
                        let respObj = JSON.parse(resp);
                        let arrSteps = respObj.loadsteps.split(',');
                        let arrClearSteps = respObj.clearsteps.split(',');
                        
                        $('##upgradeTrialSmithTermContainer,##upgradeTrialSmithStep3SaveLoading').hide();
                        $('##upgradeTrialSmithSelectedTermContainer').show(300);

                        $('html, body').animate({
                            scrollTop: $('##upgradeTrialSmithStep'+arrSteps[0]).offset().top - 75
                        }, 750);

                        arrClearSteps.forEach(function(step,index) {
                            $('##upgradeTrialSmithStep'+step).html('');
                        });
                        
                        arrSteps.forEach(function(step,index) {
                            loadUpgradeTrialSmithSteps(step);
                        });
                    }
                ).show();
        }
        
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.upgradeTrialSmithStep3Head)#">