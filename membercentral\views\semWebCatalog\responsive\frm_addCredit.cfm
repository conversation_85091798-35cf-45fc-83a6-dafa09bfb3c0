<cfset local.qryGetCreditAuthorities = attributes.data.qryGetCreditAuthorities>
<cfset local.pageName = attributes.data.pageName>
<cfset local.formlink = attributes.data.formlink>

<cfsavecontent variable="local.zoneJS">
	<cfoutput>
		<style type="text/css">
		.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
		##eventDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
		</style>
		<script language="javascript">
		_validateForm = function(thisForm) {
			hideAlert();
			var arrReq = new Array();
			var i=5;
			var l=5;
			var ctcheck = 1;
			var numElements = document.frmZone.elements.length - 1;
			if (!_CF_hasValue(thisForm['eventName'], "TEXT", false)  
			&& !_CF_hasValue(thisForm['courseNumber'], "TEXT", false)) arrReq[arrReq.length] = 'Enter the name of the program or a program/credit identifier.';
			if (!_CF_hasValue(thisForm['eventDate'], "TEXT", false)) arrReq[arrReq.length] = 'Enter the date of the program.';
        	if (!_CF_checkdate(thisForm['eventDate'].value, false)) arrReq[arrReq.length] = 'Enter a valid date of the program.';
			
			//makes sure the at least one credit type is entered	
			while (i<numElements) {
				if (_CF_hasValue(thisForm[i], "TEXT", false)) ctcheck = 0;  
				i++;
			}
			if (ctcheck==1) arrReq[arrReq.length] = 'Enter Credit Information.';

        	while (l<numElements) {
				if (_CF_hasValue(thisForm[l], "TEXT", false) && !_CF_checknumber(thisForm[l].value, false)) arrReq[arrReq.length] = 'Enter valid credit amount for ' + thisForm[l].name.substring(10,thisForm[l].name.length) + '.';  
				l++;
			}

			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				return false;
			}
			return true;
			
		};		
		
		hideAlert = function() {
			var abox = document.getElementById('validateMessage');
				abox.innerHTML = '';
				abox.style.display = 'none';
		};
		showAlert = function(msg) {
			var abox = document.getElementById('validateMessage');
				abox.innerHTML 			= msg;
				abox.className 			= 'alert';
				abox.style.display 	= '';
		};

		$(document).ready(function(){
			mca_setupDatePickerField('eventDate');
		});
		</script>

	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.zoneJS#">

<div class="content">
	<div class="container-fluid">
		
<cfif local.qryGetCreditAuthorities.recordcount GT 1>
<cfoutput>
	<div class="row-fluid">
	  <h3>#local.pageName#</h3>
	  <h5>Complete the information below to record this self-reported credit in your totals.</h5>
	</div>
	<div class="row-fluid">
		<div class="span12">
	
			<cfform name="pickCA"  id="pickCA" action="/?#cgi.query_string#" method="post">
			
				<div class="form-group">
					<cfselect name="CSALinkID" id="CSALinkID" >
					<cfloop query="local.qryGetCreditAuthorities">
						<option value="#local.qryGetCreditAuthorities.CSALinkID#">#local.qryGetCreditAuthorities.authorityName#</option>
					</cfloop>
					</cfselect>
				</div>
				<div class="form-group">
					<button name="btnPickCA" id="btnPickCA" type="submit" class="btn btn-primary">Choose Credit Authority</button>
				</div>
			
			</cfform>
		</div>
	</div>
	</cfoutput>
<cfelse>
	<!---Convert the CreditTypes XML to array for use in form --->
	<cfwddx action="wddx2cfml" input="#local.qryGetCreditAuthorities.wddxCreditTypes#" output="CreditTypes">
	<cfoutput>
	<div class="row-fluid">
		<h3>#local.pageName#</h3>
		<h5>Complete the information below to record this self-reported credit in your totals.</h5>
	</div>
	
	<div class="row-fluid">
		<div class="span12">
			<cfform name="frmZone"  id="frmZone" action="#local.formlink#" method="post" onsubmit="return _validateForm(this);cleanupAddCredit();closeAddCredit();" class="form-horizontal">
			<cfinput type="hidden" name="CSALinkID"  id="CSALinkID" value="#local.qryGetCreditAuthorities.CSALinkID#">
			
				
				<div class="control-group">
					<label class="control-label"><b>Credit Authority</b></label>
					<div class="span6">#local.qryGetCreditAuthorities.authorityName#</div>
				</div>
				<div class="control-group">
					<label class="control-label"><b>Name of Program</b></label>
					<div class="controls">
						<cfinput type="text" name="eventName"  id="eventName" class="form-control input-xlarge" size="55" maxlength="100" value="">
					</div>
				</div>
				<div class="control-group">
					<label class="control-label"><b>Course/Credit Identifier</b></label>
					<div class="controls">
						<cfinput type="text" name="courseNumber"  id="courseNumber" class="form-control input-large" size="30" maxlength="50" value=""> <span class="help-inline">(if available)</span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label"><b>Date of Program</b></label>
					<div class="controls">
						<cfinput type="text" name="eventDate" id="eventDate" class="form-control" size="13">
						<a href="javascript:mca_clearDateRangeField('eventDate');" title="Clear Date"><i class="icon-remove-sign" style="vertical-align:text-bottom; margin: 0 0 -1px 3px;"></i></a>
					</div>
				</div>
				<br/>
				<cfif len(local.qryGetCreditAuthorities.creditIDText)>
					<div class="control-group">
						<label  class="control-label"><b>#local.qryGetCreditAuthorities.creditIDText#</b></label>
						<div class="controls">
							<cfinput type="text" name="IDNumber"  id="IDNumber" class="form-control input-large" maxlength="50" value="">
						</div>
					</div>
				<cfelse>
					<cfinput type="hidden" name="IDNumber"  id="IDNumber" value="">
				</cfif>
				
				<div class="control-group">
				<label class="control-label"><b>Credits Earned</b></label>
					<div class="controls">
						<cfloop index="local.ct" array="#CreditTypes#">
							<cfinput type="text" size="3" maxlength="4"  name="CreditType#local.ct['fieldname']#" id="CreditType#local.ct['fieldname']#" > #local.ct['displayname']#<br><br>
						</cfloop>
					</div>
				</div>				
				<br/>
				<div class="control-group">
					<div id="validateMessage" style="display:none;margin:6px 0;"></div>
					<div class="controls">
						<button name="btnSaveCredit" id="btnSaveCredit" type="submit" class="btn btn-primary">Save Information</button>
					</div>
				</div>
		</cfform>
		</div>
	</div>
	</cfoutput>
</cfif>
	</div>
</div>