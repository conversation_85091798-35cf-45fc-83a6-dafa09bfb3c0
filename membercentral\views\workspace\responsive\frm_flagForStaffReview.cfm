<cfsavecontent variable="local.js">
	<cfoutput>
	<script type="text/javascript">
		function validateStaffReviewForm() {
			$("##btnStaffReview").prop('disabled',true);

			if ($('##reason').val().trim().length == 0) {
				alert('Enter a Reason.');
				$("##btnStaffReview").prop('disabled',false);
				return false;
			}
			
			var saveStaffReviewNoteResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					window.location.reload();
				} else {
					alert('We were unable to flag #attributes.data.taskFieldLabel# for staff review.');
				}
			};

			$('##divStaffReviewForm').hide();
			$('##divStaffReviewFormLoading').show();

			var objParams = { projectID:#attributes.data.projectID#, taskID:#attributes.data.taskID#, reason:$('##reason').val() };
			TS_AJX('ADMWORKSPACE','doFlagTaskForStaffReview',objParams,saveStaffReviewNoteResult,saveStaffReviewNoteResult,10000,saveStaffReviewNoteResult);

			return false;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div id="divStaffReviewForm">
	<div style="margin:13px 0px 13px 10px;">
		<b>Ask Staff to Review this #attributes.data.taskFieldLabel#</b>
	</div>

	<form method="post" name="frmStaffReview" id="frmStaffReview" class="form-horizontal" onSubmit="return validateStaffReviewForm();">
	<div style="padding:8px 0;">
		<div class="control-group">
			<label class="control-label" for="reason">Reason:</label>
			<div class="controls">
				<textarea name="reason" id="reason" cols="80" rows="5" style="width:400px;"></textarea>
			</div>
		</div>
	</div>
	<div>
		<button type="submit" id="btnStaffReview" name="btnStaffReview" class="btn">Submit</button>
		<button type="button" id="btnCancelStaffReview" name="btnCancelStaffReview" class="btn" onclick="cancelStaffReview();">Cancel</button>
	</div>
	</form>
</div>
<div id="divStaffReviewFormLoading" style="display:none;">
	<div style="margin:13px 0px 13px 10px;">
		<b>Ask Staff to Review this #attributes.data.taskFieldLabel#</b>
	</div>
	<div>
		<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>
	</div>
</div>
</cfoutput>