<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div id="bk_expertsList">
<cfif arrayLen(local.arrExpert) EQ 0>
	<div>#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#</div>
<cfelse>
	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.totalChallengeCount, itemWord='challenge', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
	<cfloop array="#local.strExpertResults.arrexperts#" item="local.thisExpert" index="local.index">
		<div id="row#local.thisExpert.id#" class="bk_expertcard bk-card-box bk-shadow-none bk-p-3 bk-mt-1 bk-mb-3 bk-w-sm-100 bk-border-1 bk-border-gray" style="box-sizing:border-box;">
			<div id="row#local.thisExpert.id#summary" class="bk_expertsummary">
				<div class="bk-d-flex">
					<span class="bk_experttitle"><strong class="bk_expertname">#local.thisExpert.name#</strong><cfif len(local.thisExpert.location)>, <span class="bk_expertlocation">#local.thisExpert.location#</span></cfif></span>
					<cfif arrayLen(local.strExpertResults.arrexperts) gt 1><button class="exptogglebtn btn btn-secondary bk-ml-auto" onClick="toggleDCSExpert('#local.thisExpert.id#');">View <i class="icon-chevron-right"></i></button></cfif>
				</div>
				<div class="bk-font-size-md">
					<cfif arrayLen(local.thisExpert.arrdisciplines)>
						<div class="bk_expertArea">#arrayToList(local.thisExpert.arrdisciplines,"<br/>")#</div>
					</cfif>
					<div class="bk-mt-2"><strong>#local.thisExpert.totalline#</strong></div>
				</div>
			</div>

			<div id="row#local.thisExpert.id#detail" class="bk_expertdetail bk-mt-4" <cfif arrayLen(local.strExpertResults.arrexperts) gt 1>style="display:none;"</cfif>>
				<div class="bk-d-flex bk-flex-sm-column" style="gap:10px;">
					<cfloop array="#local.arrReportTypes#" item="local.thisReportType">
						<div class="bk-card-box bk-col <cfif len(local.thisReportType.badge)>bk-price-card-selected<cfelse>bk-border-1</cfif>">
							<div class="bk-card-body bk-font-size-md">
								<cfif len(local.thisReportType.badge)>
									<div class="bk-price-badge">#local.thisReportType.badge#</div>
								</cfif>
								<div class="bk-d-flex">
									<strong>#local.thisReportType.name#</strong>
									<strong class="bk-ml-auto bk-font-size-lg">#local.thisReportType.cost#</strong>
								</div>
								<div class="bk-mt-2 bk-mb-2">#local.thisReportType.desc#</div>
							</div>
							<div class="bk-d-flex bk-mt-auto bk-pb-2 bk-pr-3 bk-pl-3">
								<cfif local.thisReportType.keyExists("sampbtntxt")>
									<button class="btn" onclick="viewDCSSampleReport()">#local.thisReportType.sampbtntxt#</button>
								</cfif>
								<button class="btn btn-primary bk-ml-auto" onclick="orderDCSReport('#local.thisExpert.id#','#local.thisReportType.btnact#')">#local.thisReportType.btntxt#</button>
							</div>
						</div>
					</cfloop>
				</div>
				<div class="bk-mt-3 bk-font-size-xs bk-text-center bk-text-dim">Complete satisfaction guarantee with your first order or full refund.</div>
				<cfif local.thisExpert.numchallenges+local.thisExpert.numcases gt 0>
					<div class="bk-mt-5 bk-mb-3 bk-font-size-md bk-text-center bk-border-bottom bk-pb-2">Our last analysis of #local.thisExpert.name#</div>
					<div class="bk-d-flex bk-flex-sm-column" style="overflow-y:auto;">
						<cfif local.thisExpert.numchallenges gt 0>
							<div class="bk-col" id="mc-ExpertGroundsOfChallengeChart-#local.thisExpert.id#"></div>
						</cfif>
						<cfif local.thisExpert.numcases gt 0>
							<div class="bk-col" id="mc-ExpertStatewiseCasesChart-#local.thisExpert.id#"></div>
						</cfif>
					</div>
				</cfif>
			</div>
		</div>
	</cfloop>
</cfif>

<div class="bk-card-box bk-shadow-none bk-p-3 bk-mt-1 bk-mb-3 bk-w-sm-100 bk-dashed-container">
	<div class="bk-text-center">If your expert is not listed, <a href="##" class="mc_add_link" onclick="orderCustomDCSRpt();return false;">Click to Order a Custom Tracker Report.</a></div>
</div>
<div id="customDCSRptContainer" class="bk-card-box bk-shadow-none bk-p-3 bk-d-none">
	<div id="customDCSRptForm">
		<div class="bk-mb-2 bk-border-bottom bk-pb-2">
			<h5 class="bk-mt-0 bk-mb-0">EXPERT INFORMATION</h5>
		</div>
		<div class="bk-d-flex bk-mb-2 bk-flex-lg-column">
			<div class="bk-col">
				<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">First Name <span class="bk-text-danger">*</span></div>
				<input type="text" id="exp_firstName" name="exp_firstName" class="bk-formcontrol bk-w-100" value="#local.strSearch.expertfname#" maxlength="100">
				<div id="exp_firstName_err" class="bk-font-size-xs bk-text-danger exp_fld_err" style="display:none;"></div>
			</div>
			<div class="bk-col bk-mt-lg-2">
				<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">Middle Name or Initial</div>
				<input type="text" id="exp_middleName" name="exp_middleName" class="bk-formcontrol bk-w-100" value="" maxlength="50">
			</div>
			<div class="bk-col bk-mt-lg-2">
				<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">Last Name <span class="bk-text-danger">*</span></div>
				<input type="text" id="exp_lastName" name="exp_lastName" class="bk-formcontrol bk-w-100" value="#local.strSearch.expertlname#" maxlength="100">
				<div id="exp_lastName_err" class="bk-font-size-xs bk-text-danger exp_fld_err" style="display:none;"></div>
			</div>
		</div>
		<div class="bk-d-flex bk-mb-2 bk-flex-lg-column">
			<div class="bk-col bk-mt-lg-2">
				<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">City <span class="bk-text-danger">*</span></div>
				<input type="text" id="exp_city" name="exp_city" class="bk-formcontrol bk-w-100" value="" maxlength="100">
				<div id="exp_city_err" class="bk-font-size-xs bk-text-danger exp_fld_err" style="display:none;"></div>
			</div>
			<div class="bk-col bk-mt-lg-2">
				<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">State <span class="bk-text-danger">*</span></div>
				<input type="text" id="exp_state" name="exp_state" class="bk-formcontrol bk-w-100" value="" maxlength="100">
				<div id="exp_state_err" class="bk-font-size-xs bk-text-danger exp_fld_err" style="display:none;"></div>
			</div>
		</div>
		<div class="bk-d-flex bk-mb-2">
			<div class="bk-col">
				<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">Area of Expertise <span class="bk-text-danger">*</span></div>
				<textarea  id="exp_area" name="exp_area" class="bk-w-100 bk-mb-0" rows="4" maxlength="2000"></textarea>
				<div id="exp_area_err" class="bk-font-size-xs bk-text-danger exp_fld_err" style="display:none;"></div>
			</div>
		</div>
		<div class="bk-d-flex bk-mt-3">
			<button type="button" id="btnCancelDCSCustomRpt" class="btn" onclick="cancelOrderCustomDCSRpt();">Cancel</button>
			<button type="button" id="btnContinueDCSCustomRpt" class="btn btn-primary bk-ml-auto" onclick="validateDCSExpertInfo();">Continue</button>
		</div>
	</div>
	<div id="customDCSRptDetail" style="display:none;">
		<div id="rowcustomexp" class="bk_expertcard bk-card-box bk-shadow-none bk-p-3 bk-mt-1 bk-mb-3 bk-w-sm-100 bk-border-1 bk-border-gray">
			<div id="rowcustomexpsummary" class="bk_expertsummary">
				<div class="bk-d-flex">
					<span class="bk_experttitle"><strong class="bk_expertname"></strong>, <span class="bk_expertlocation"></span></span>
				</div>
				<div class="bk-font-size-md bk_expertArea"></div>
			</div>

			<div id="rowcustomexpdetail" class="bk-mt-4">
				<div class="bk-d-flex bk-flex-sm-column" style="gap:10px;">
					<cfloop array="#local.arrReportTypes#" item="local.thisReportType">
						<div class="bk-card-box bk-col <cfif len(local.thisReportType.badge)>bk-price-card-selected<cfelse>bk-border-1</cfif>">
							<div class="bk-card-body bk-font-size-md">
								<cfif len(local.thisReportType.badge)>
									<div class="bk-price-badge">#local.thisReportType.badge#</div>
								</cfif>
								<div class="bk-d-flex">
									<strong>#local.thisReportType.name#</strong>
									<strong class="bk-ml-auto bk-font-size-lg">#local.thisReportType.cost#</strong>
								</div>
								<div class="bk-mt-2 bk-mb-2">#local.thisReportType.desc#</div>
							</div>
							<div class="bk-d-flex bk-mt-auto bk-pb-2 bk-pr-3 bk-pl-3">
								<cfif local.thisReportType.keyExists("sampbtntxt")>
									<button class="btn" onclick="viewDCSSampleReport()">#local.thisReportType.sampbtntxt#</button>
								</cfif>
								<button class="btn btn-primary bk-ml-auto" onclick="orderDCSReport('customexp','#local.thisReportType.btnact#')">#local.thisReportType.btntxt#</button>
							</div>
						</div>
					</cfloop>
				</div>
				<div class="bk-mt-3 bk-font-size-xs bk-text-center bk-text-dim">Complete satisfaction guarantee with your first order or full refund.</div>
			</div>
		</div>
	</div>
</div>
<div class="bk-overlay bk-d-none"></div>

<cfif arrayLen(local.arrExpert)>
	<div id="bk_tooltip"></div>
	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.totalChallengeCount, itemWord='challenge', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
</cfif>
</div>
<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
	<div id="bk_notLoggedIn" style="display:none;">
		<cfif val(local.totalChallengeCount)>
			<div style="padding-bottom:3px;"><strong>#local.totalChallengeCount#</strong> challenge<cfif local.totalChallengeCount GT 1>s</cfif> found</div>
		</cfif>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</div>
</cfif>
</cfoutput>