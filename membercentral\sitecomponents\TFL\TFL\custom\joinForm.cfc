<cfcomponent extends="model.customPage.customPage" output="false">
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			
			local.arrCustomFields = [];
			local.tmpField = {
				name="AccountLocatorTitle",
				type="STRING",
				desc="Account Locator Title",
				value="Identify Yourself"
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="AccountLocatorButton",
				type="STRING",
				desc="Account Locator Button Text",
				value="Continue"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="AccountLocatorInstructions",
				type="CONTENTOBJ",
				desc="Account Locator Instruction Text",
				value="Click the Account Lookup button to the left. Enter the search criteria and click Continue."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="StaffConfirmationTo",
				type="STRING",
				desc="who do we send staff confirmations to",
				value="<EMAIL>"
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="MemberConfirmationFrom",
				type="STRING",
				desc="who do we send member confirmations from",
				value="<EMAIL>"
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="CCPayProfileCode",
				type="STRING",
				desc="pay profile code for CC",
				value="TFL-CC"
			};
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="errActiveFound",
				type="CONTENTOBJ",
				desc="Error message when active subscription found",
				value="Our records indicate you are currently a member. <a href='/?pg=manageSubscriptions'>Click here</a> to continue."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="errAcceptedFound",
				type="CONTENTOBJ",
				desc="Error message when active subscription found",
				value="Our records indicate you are currently a member. <a href='/?pg=manageSubscriptions'>Click here</a> to continue."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="errBilledFound",
				type="CONTENTOBJ",
				desc="Error message when active subscription found",
				value="You need to renew your membership. <a href='/?pg=manageSubscriptions'>Click here</a> to continue."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentMembershipInfo",
				type="CONTENTOBJ",
				desc="Content above Membership Information",
				value="Fields marked with a * are required."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentMembershipCategory",
				type="CONTENTOBJ",
				desc="Content in Membership Category",
				value="Pricing for different membership categories"
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentMembershipType",
				type="CONTENTOBJ",
				desc="Content in Membership Type",
				value="CONTENT HERE"
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentMemberBenefits",
				type="CONTENTOBJ",
				desc="Content in Member Benefits",
				value="You will receive additional information about all the benefits of membership. Please indicate your interest in the following benefits:"
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentAdditionalInfoTax",
				type="CONTENTOBJ",
				desc="Tax Content in Additional Info",
				value="Dues are not deductible as charitable contributions for federal income tax purposes, but may be deductible as ordinary and necessary business expenses. Please consult your tax advisor."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentAdditionalInfoVerify",
				type="CONTENTOBJ",
				desc="Verify Content in Additional Info",
				value="I verify that all information contained in this membership application is current. By providing my mailing address, email address, telephone and fax number, I agree to receive communications sent by or on behalf of the Texas Family Law Foundation via mail, email, telephone or fax."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = {
				name="contentConfirmation",
				type="CONTENTOBJ",
				desc="Content on Confirmation",
				value="Thank you. Your membership application has been submitted for review. You will receive an email from the Texas Family Law Foundation once your application has been processed."
			}; 
			arrayAppend(local.arrCustomFields, local.tmpField);

			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	

			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(
				siteID=arguments.event.getValue('mc_siteInfo.siteID'),
				siteResourceID=this.siteResourceID,
				arrCustomFields=local.arrCustomFields
			);

			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(
				event=arguments.event, 
				formName='frmJoin',
				formNameDisplay='Join the Texas Family Law Foundation',
				orgEmailTo=variables.strPageFields.StaffConfirmationTo,
				memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
			));

			variables.useHistoryID = 0;
			if (int(val(arguments.event.getValue('historyID',0))) gt 0) {
				variables.useHistoryID = int(val(arguments.event.getValue('historyID')));
			}
			variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MEMBERJOINHISTORY', subName='Started');
			variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MEMBERJOINHISTORY', subName='Completed');
			variables.historyStartedText = "Member started join form.";
			variables.historyCompletedText = "Member completed join form.";

			switch (local.formAction) {
				case "processLookup":
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo();
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
				case "processMembershipInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}
					switch (processPayment(rc=arguments.event.getCollection(),event=arguments.event)) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}

			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading' class='tsAppLoadingText'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>


	<!--- showLookup() --->
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfscript>
			var local = structNew();
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
			local.objCffp = variables.objCffp;

			variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid);
		</cfscript>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { 
					background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat;
					text-align:left;
					padding:5px 20px 5px 45px;
					border-top:2px solid ##f00;
					border-bottom:2px solid ##f00;
					font-family: Verdana, Arial, Helvetica, sans-serif;
					font-size: 10pt;
				} 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function validateMembershipInfoForm(){
					var arrReq = new Array();
					if (!$('###variables.formName# ##mccf_RFID').is(':checked')) arrReq[arrReq.length] = "Make a membership type selection.";

					var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
					$("##donorAmount").val( $("##donorAmount").val().replace(/\$|,/g,''));
					if($("select[name=donorFrequency]").val() != "" && ( $.trim($("##donorAmount").val()).length == 0 || ($.trim($("##donorAmount").val()).length > 0 && !amountRegex.test($.trim($("##donorAmount").val())) ) ))
						arrReq[arrReq.length] = "Enter a valid donor amount. Only positive amounts are allowed";				

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {
					if (location.hash.length > 0) {
						step = parseInt(location.hash.replace('##',''),10);
						if (prevStep > step){
							if(step==1) {
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							}
							if(step==2) {
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							}
							mc_loadDataForForm($('###variables.formName#'),'previous');
						}
					} else {
						step = 1;
					}
					prevStep = step;
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
											
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = "3046413a-a6f9-44ed-a191-63055b06d662">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(
			siteID=variables.siteID,
			orgID=variables.orgID,
			memberID=variables.useMID
		)>

		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>		
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
		</cfif>	

		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(
			uid='776b45b6-3152-4da8-81ba-e2b0e897ad12',
			mode="collection",
			strData=local.strData
		)>

		<!--- Lawyer Information --->
		<cfset local.strBarQuestions = application.objCustomPageUtils.renderFieldSet(
			uid='383e5b71-986e-4e19-a101-a1c746991e3e',
			mode="collection",
			strData=local.strData
		)>

		<!--- Law Student Information --->
		<cfset local.strStudentQuestions = application.objCustomPageUtils.renderFieldSet(
			uid='93e7c9d6-bbd6-4ef0-b68d-96d6585e2856',
			mode="collection",
			strData=local.strData
		)>

		<!--- Get Bar Number and Admission Date --->
		<cfloop collection="#local.strBarQuestions.strFields#" item="local.thisField">
			<cfswitch expression="#local.strBarQuestions.strFields[local.thisField]#">
				<cfcase value="Bar Number">
					<cfset local.barNumber = local.thisField>
				</cfcase>
				<cfcase value="Bar Date">
					<cfset local.barDate = local.thisField>
				</cfcase>
			</cfswitch>
		</cfloop>

		<!--- Get Graduation Date and Law School --->
		<cfloop collection="#local.strStudentQuestions.strFields#" item="local.thisField">
			<cfswitch expression="#local.strStudentQuestions.strFields[local.thisField]#">
				<cfcase value="Expected Graduation Date">
					<cfset local.graduationDate = local.thisField>
				</cfcase>
				<cfcase value="Law School">
					<cfset local.lawSchool = local.thisField>
				</cfcase>
			</cfswitch>
		</cfloop>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#

					if (
						!$('###variables.formName# ##iAmA_Lawyer').is(':checked')
						&& !$('###variables.formName# ##iAmAn_Associate').is(':checked')
						&& !$('###variables.formName# ##iAmA_LawStudent').is(':checked')
					) {
						arrReq[arrReq.length] = "Please choose whether you are a lawyer, non-lawyer, or a law student.";
					}

					if ($('###variables.formName# ##iAmA_Lawyer').is(':checked')) {
						#local.strBarQuestions.jsValidation#

						if ($('###variables.formName# ##barStatus').val() == '') {
							arrReq[arrReq.length] = "Please select the status of your bar license.";
						}
					}

					if ($('###variables.formName# ##iAmA_LawStudent').is(':checked')) {
						#local.strStudentQuestions.jsValidation#
					}

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					var mcSel = $('###variables.formName# ##mccf_memberCategory').val();
					if (mcSel == '') arrReq[arrReq.length] = "Membership Category is required.";
					else if (mcSel == 'Attorney' || mcSel == 'Defense Attorney' || mcSel == 'Government Attorney') {
						<cfif isDefined("local.barNumber")>
							if ($.trim($('###variables.formName# ###local.barNumber#').val()) == '') arrReq[arrReq.length] = "Texas Bar No. is required.";
						</cfif>
						<cfif isDefined("local.barDate")>
							if ($.trim($('###variables.formName# ###local.barDate#').val()) == '') arrReq[arrReq.length] = "Admission Date for Texas Bar is required.";
						</cfif>
					} else if (mcSel == 'Law Student') {
						<cfif isDefined("local.lawSchool")>
							if ($.trim($('###variables.formName# ###local.lawSchool#').val()) == '') arrReq[arrReq.length] = "Law School is required.";
						</cfif>
						<cfif isDefined("local.graduationDate")>
							if ($.trim($('###variables.formName# ###local.graduationDate#').val()) == '') arrReq[arrReq.length] = "Graduation Date is required.";
						</cfif>
					}

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				$(document).ready(function() {
					prefillData();
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<cfif len(variables.strPageFields.contentMembershipInfo)>
				<div class="tsAppNoteText">#variables.strPageFields.contentMembershipInfo#</div>
				<br/>
			</cfif>

			<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent1.fieldSetContent#

				<hr>

				<table cellpadding="3" border="0" cellspacing="0">
					<tbody>
						<tr valign="top">
							<td class="tsAppBodyText" width="10"></td>
							<td class="tsAppBodyText" nowrap="" valign="top">I am a(n):</td>
							<td class="tsAppBodyText">&nbsp;</td>
							<td class="tsAppBodyText" valign="top">
								<label for="iAmA_Lawyer" class="tsAppBodyText">
									<input type="radio" name="iAmA" id="iAmA_Lawyer" value="Lawyer" class="tsAppBodyText"> Lawyer
								</label>
								<br>
								<label for="iAmAn_Associate" class="tsAppBodyText">
									<input type="radio" name="iAmA" id="iAmAn_Associate" value="Associate" class="tsAppBodyText"> Non-Lawyer
								</label>
								<br>
								<label for="iAmA_LawStudent" class="tsAppBodyText">
									<input type="radio" name="iAmA" id="iAmA_LawStudent" value="Law Student" class="tsAppBodyText"> Law Student
								</label>
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<div name="barQuestions-canvas" id="barQuestions-canvas">
				<div class="tsAppSectionHeading">#local.strBarQuestions.fieldSetTitle#</div>
				#local.strBarQuestions.fieldSetContent#
			</div>

			<div name="studentQuestions-canvas" id="studentQuestions-canvas">
				<div class="tsAppSectionHeading">#local.strStudentQuestions.fieldSetTitle#</div>
				#local.strStudentQuestions.fieldSetContent#
			</div>

			<br>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			</form>

			<script>
				$(document).ready(function() {
					$('div##barQuestions-canvas').hide();
					$('div##studentQuestions-canvas').hide();

					var HTMLtoAppend = '<tr valign="top"><td class="tsAppBodyText" width="10">*</td><td class="tsAppBodyText" nowrap="" valign="top">Bar Status</td><td class="tsAppBodyText">&nbsp;</td><td class="tsAppBodyText" valign="top"><select name="barStatus" id="barStatus" class="tsAppBodyText"><option value="">Choose...</option><option value="Active">Active</option><option value="Inactive">Inactive</option><option value="Disbarred">Disbarred</option></select></td></tr>';
					$('div##barQuestions-canvas table tbody').append(HTMLtoAppend);

					$('input[name=iAmA]').change(function() {
						switch($('input[name=iAmA]:checked').val()) {
							case 'Lawyer':
								$('div##barQuestions-canvas').show();
								$('div##studentQuestions-canvas').hide();
								break;
							case 'Associate':
								$('div##barQuestions-canvas').hide();
								$('div##studentQuestions-canvas').hide();
								break;
							case 'Law Student':
								$('div##barQuestions-canvas').hide();
								$('div##studentQuestions-canvas').show();
								break;
						}
					});
				});
			</script>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- set Professional License status --->
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfloop query="local.qryOrgPlTypes">
			<cfif
				arguments.rc.iAmA eq 'Lawyer'
				and structKeyExists(arguments.rc, "mpl_#local.qryOrgPlTypes.PLTypeID#_licenseNumber")
				and len(arguments.rc["mpl_#local.qryOrgPlTypes.PLTypeID#_licenseNumber"])
			>
				<cfset structInsert(arguments.rc, "mpl_#local.qryOrgPlTypes.PLTypeID#_status", arguments.rc.barStatus)>
			</cfif>
		</cfloop>

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add history if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(
					memberID=variables.useMID,
					categoryID=variables.qryHistoryStarted.categoryID,
					subCategoryID=variables.qryHistoryStarted.subCategoryID,
					description=variables.historyStartedText,
					enteredByMemberID=variables.useMID,
					newAccountsOnly=false
				)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(
			siteID=variables.siteID,
			memberID=variables.useMID, 
			subscriptionUID='785583f4-2964-4bae-9343-a7f63bac8438', 
			isRenewal=0,
			allowFrontEnd="true"
		)>
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>			

		<cfset local.contributionQuestions = application.objCustomPageUtils.renderFieldSet(
			uid='a3130824-4940-4fc5-b17b-eb6fc6b845bd',
			mode="collection",
			strData=local.strData
		)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				$(document).ready(function() {
					$("##donorAmount").change(function(){
						var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
						$(this).val( $(this).val().replace(/\$|,/g,''));
						if($("select[name=donorFrequency]").val() != "" && ( $.trim($(this).val()).length == 0 || ($.trim($(this).val()).length > 0 && !amountRegex.test($.trim($(this).val())) ) ))
							alert('Enter a valid donor amount. Only positive amounts are allowed.');					
					});
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">		

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#session.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinput type="hidden" name="iAmA" id="iAmA" value="#arguments.rc.iAmA#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Type</div>
			<div class="tsAppSectionContentContainer">
				<cfif len(variables.strPageFields.contentMembershipType)>
					<div class="tsAppBodyText" style="padding-left: 5px;padding-bottom: 5px;">#variables.strPageFields.contentMembershipType#</div>
				</cfif>
				<table cellpadding="3" border="0" cellspacing="0">
				<tbody id="joinFormMembershipRates">
					<cfloop query="local.qryRates">
						<tr valign="top" class="rate#LCase(local.qryRates.rateUID)#">
							<td class="tsAppBodyText" width="30">
								<cfinput type="radio" name="mccf_RFID" id="mccf_RFID" value="#local.qryRates.RFID#" checked="#structKeyExists(local.strData, 'mccf_RFID') and local.strData.mccf_RFID eq local.qryRates.RFID#">
							</td>
							<td class="tsAppBodyText">
								#local.qryRates.rateName#
							</td>
						</tr>
					</cfloop>
				</tbody>
				</table>
			</div>
			
			<!--- Join TFL - Allow Auto-Renew --->
			<cfset local.strAllowAutoRenewFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='b74c2d7b-bf06-4687-95a9-add09583a6d9', mode="collection", strData=local.strData)>
			<div class="tsAppSectionHeading">#local.strAllowAutoRenewFieldSetContent.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strAllowAutoRenewFieldSetContent.fieldSetContent#
			</div>
			
			<div class="tsAppSectionHeading">#local.contributionQuestions.fieldSetTitle#</div>
			<table cellpadding="3" border="0" cellspacing="0">
				<tbody>
					<tr valign="top">
						<td class="tsAppBodyText" width="10">&nbsp;</td>
						<td class="tsAppBodyText" nowrap>I wish to make a(n)</td>
						<td class="tsAppBodyText">
							<select name="donorFrequency" id="donorFrequency" class="tsAppBodyText">
								<option value=""></option>
								<option value="Annual">Annual Contribution</option>
								<option value="Monthly">Monthly Contribution</option>
								<option value="One-Time">One-Time Contribution</option>
							</select>
						</td>
					</tr>
					<tr valign="top">
						<td class="tsAppBodyText" width="10">&nbsp;</td>
						<td class="tsAppBodyText" nowrap="">to the Texas Family Law Foundation in the amount of: $</td>
						<td class="tsAppBodyText">
							<input type="text" name="donorAmount" id="donorAmount" class="tsAppBodyText" size="8" value="" autocomplete="off">
						</td>
					</tr>
				</tbody>
			</table>
	
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID)>
		<cfif structKeyExists(arguments.rc, 'donorFrequency') AND Len(arguments.rc.donorFrequency)>
			<cfset local.objSaveMember.setCustomField(field="Donor Frequency", value="#arguments.rc.donorFrequency#")>
			<cfset local.objSaveMember.setCustomField(field="Donor Amount", value="#arguments.rc.donorAmount#")>
			<cfset local.objSaveMember.setCustomField(field="Donor Start Date", value="#dateFormat(now(),'m-d-yyyy')#")>
		</cfif>
		
		<cfset local.allowAutoRenewColumnName = "Allow Automatic Renewal">
		<cfset local.allowAutoRenewStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName=local.allowAutoRenewColumnName)>
		<cfset local.allowAutoRenewColumnId = local.allowAutoRenewStruct.columnId>
		<cfif structKeyExists(arguments.rc, 'md_'&local.allowAutoRenewColumnId)>
			<cfset local.allowAutoRenewSelected = arguments.rc['md_'&local.allowAutoRenewColumnId]>
			<cfquery name="local.qryAutoRenewSelected" datasource="#application.dsn.membercentral.dsn#">
				select columnValueString
				from ams_memberDataColumnValues
				where valueID = #local.allowAutoRenewSelected#
			</cfquery>
			<cfset local.objSaveMember.setCustomField(field="Allow Automatic Renewal", value="#local.qryAutoRenewSelected.columnValueString#")>
		</cfif>
		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
		
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(
			siteID=variables.siteID,
			memberID=variables.useMID,
			subscriptionUID='785583f4-2964-4bae-9343-a7f63bac8438',
			isRenewal=0)
		>

		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>

		<cfif local.qryRatesSelected.rateAmt gt 0>
			<cfset local.arrPayMethods = [
				variables.strPageFields.CCPayProfileCode
			]>

			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
		</cfif>

		<cfif local.qryRatesSelected.rateAmt gt 0>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>

			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#session.usehistoryID#">
			<cfinput type="hidden" name="donorAmount" id="donorAmount" value="#arguments.rc.donorAmount#">
			<cfinput type="hidden" name="donorFrequency" id="donorFrequency" value="#arguments.rc.donorFrequency#">
			<cfinput type="hidden" name="iAmA" id="iAmA" value="#arguments.rc.iAmA#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif
					left(local.thisField,2) eq "m_"
					or left(local.thisField,3) eq "me_"
					or left(local.thisField,3) eq "mw_"
					or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_"
					or left(local.thisField,4) eq "mat_"
					or left(local.thisField,3) eq "mp_"
					or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
				>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Confirmation</div>
			<div class="tsAppSectionContentContainer">
				<div class="tsAppBodyText">#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>

				<cfif arguments.rc.donorFrequency neq "">
					<div class="tsAppBodyText">#arguments.rc.donorFrequency# contribution of #dollarFormat(arguments.rc.donorAmount)#</div>
				<cfelse>
					<div class="tsAppBodyText">No donation</div>
				</cfif>
				
				<cfset local.totalAmount = val(local.qryRatesSelected.rateAmt) + val(arguments.rc.donorAmount)>
				<br/>
				<div class="tsAppBodyText"><b>Total amount : #dollarFormat(local.totalAmount)#</b></div>
			</div>

			<cfif local.qryRatesSelected.rateAmt gt 0>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>

			<hr>

			<div class="tsAppSectionContentContainer">
				<p class="tsAppBodyText">Want to send a check? Please download a printable application and send it along with a check to:</p>
				<address class="tsAppBodyText">
					Texas Family Law Foundation
					<br>P.O. Box 684886
					<br>Austin, TX 78768-4886
				</address>
				<p class="tsAppBodyText">Please send any inquiries to <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID,
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID,
			description=variables.historyCompletedText,
			newAccountsOnly=false
		)>	

		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(
				siteID=variables.siteID,
				memberID=variables.useMID,
				subscriptionUID='785583f4-2964-4bae-9343-a7f63bac8438',
				isRenewal=0)
			>
			
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName, rateUID, frequencyUID
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>
		
		<cfset local.qryContrRates = application.objCustomPageUtils.sub_getEligibleRates(
			siteID=variables.siteID,
			memberID=variables.useMID, 
			subscriptionUID='dc84b8ee-14d9-419f-864e-2df4882f4243', 
			isRenewal=0,
			allowFrontEnd="true"
		)>
		
		<!--- create subscriptions--->
			
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = '785583f4-2964-4bae-9343-a7f63bac8438')>
			
		<cfset local.subStruct = structNew()>
		<cfset local.subStruct.uid = "785583f4-2964-4bae-9343-a7f63bac8438">
		<cfset local.subStruct.rateUID = local.qryRatesSelected.rateUID>
		<cfset local.subStruct.freqUID = local.qryRatesSelected.frequencyUID>
	
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=duplicate(arguments.event), memberID=arguments.event.getTrimValue('memberid'), subStruct=local.subStruct, newAsBilled=false)>

		<cfset local.contReturn = structNew()>
		<cfset local.contReturn.success = false>
		<cfif len(trim(arguments.rc.donorFrequency)) and trim(arguments.rc.donorFrequency) neq 'One-Time'>
		
			<cfif trim(arguments.rc.donorFrequency) eq 'Monthly'>
				<cfquery name="local.qryContrRatesSelected" dbtype="query">
					select rateAmt, rateName, rateUID, frequencyUID, frequencyID
					from [local].qryContrRates
					where FREQUENCYSHORTNAME = 'M'
				</cfquery>
			<cfelse>
				<cfquery name="local.qryContrRatesSelected" dbtype="query">
					select rateAmt, rateName, rateUID, frequencyUID, frequencyID
					from [local].qryContrRates
					where FREQUENCYSHORTNAME = 'F'
				</cfquery>
			</cfif>

			<cfif trim(arguments.rc.donorFrequency) eq 'Monthly'>
				<cfset local.annualAmount = val(arguments.rc.donorAmount) * 12>
			<cfelse>
				<cfset local.annualAmount = val(arguments.rc.donorAmount)>
			</cfif>
			<cfset local.contStruct = structNew()>
			<cfset local.contStruct.uid = "dc84b8ee-14d9-419f-864e-2df4882f4243">
			<cfset local.contStruct.rateUID = local.qryContrRatesSelected.rateUID>
			<cfset local.contStruct.freqUID = local.qryContrRatesSelected.frequencyUID>
			<cfset local.contStruct.rateOverride = local.annualAmount>
			<cfset local.contReturn = local.objSubReg.autoSubscribe(event=duplicate(arguments.event), memberID=arguments.event.getTrimValue('memberid'), subStruct=local.contStruct, newAsBilled=false)>
	
		</cfif>	
		
		<cfif local.subReturn.success>
	
			<!--- come back with invoices created, need to pay first one --->
			<!--- find the first invoice for the subscription and pay it --->
			<!--- find all invoices for associating CC 					 --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice">
				set nocount on;

				declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.orgID')#">;

				select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount,
						dueNow= case when nv.dateDue < getdate() then 1 else 0 end
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions subs
					on subs.subscriptionID = s.subscriptionID
					and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
				inner join dbo.sub_types t
					on t.typeID = subs.typeID
					and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#">
				inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
					and ta.applicationTypeID = 17
					and ta.itemType = 'Dues'
				inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
				inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
				group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
				order by nv.dateDue
			</cfquery>

			<cfset local.ConInvoiceDueNowTotalAmount = 0>

			<cfif local.contReturn.success>
			
				<!--- come back with invoices created, need to pay first one --->
				<!--- find the first invoice for the subscription and pay it --->
				<!--- find all invoices for associating CC 					 --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryConInvoice">
					set nocount on;

					declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.orgID')#">;

					select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, ta.transactionID,
							dueNow= case when nv.dateDue < getdate() then 1 else 0 end
					from dbo.sub_subscribers s
					inner join dbo.sub_subscriptions subs
						on subs.subscriptionID = s.subscriptionID
						and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contReturn.rootSubscriberID#">
					inner join dbo.sub_types t
						on t.typeID = subs.typeID
						and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#">
					inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
						and ta.applicationTypeID = 17
						and ta.itemType = 'Dues'
					inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
					inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
					group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue, ta.transactionID
					order by nv.dateDue
				</cfquery>

				<cfquery name="local.qryConInvoiceDueNow" dbtype="query">
					select sum(totalAmount) as totalAmount from [local].qryConInvoice where dueNow = 1
				</cfquery>	

				<cfset local.ConInvoiceDueNowTotalAmount = val(local.qryConInvoiceDueNow.totalAmount)>				

			</cfif>

			<cfquery name="local.qryInvoiceDueNow" dbtype="query">
				select sum(totalAmount) as totalAmount from [local].qryInvoice where dueNow = 1
			</cfquery>		

			<cfset local.totalAmount = val(local.qryInvoiceDueNow.totalAmount) + local.ConInvoiceDueNowTotalAmount>

			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->			
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode='TFL-CC'
				)>	
			
			<cfset local.arrInvoicePaths = arrayNew(1)>
	
			<cfif local.totalAmount gt 0>
				<cfset local.strAccTemp = { totalPaymentAmount= local.totalAmount, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.event.getCollection() } >
				<cfif local.strAccTemp.totalPaymentAmount gt 0>
					<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
														amount=local.strAccTemp.totalPaymentAmount, 
														profileID=local.payProfileID, 
														profileCode='TFL-CC' }>			
	
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
	
					<!--- Set the activation status --->
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_overrideActivationMemberSubscription">
						<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#local.subReturn.rootSubscriberID#">
						<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
						<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('memberid')#">
						<cfprocparam type="in" cfsqltype="cf_sql_bit" value="0">
						<cfprocparam type="out" cfsqltype="cf_sql_bit" variable="local.statusUpdated">
					</cfstoredproc>
					<cfif local.contReturn.success>
						<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_overrideActivationMemberSubscription">
							<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#local.contReturn.rootSubscriberID#">
							<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
							<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('memberid')#">
							<cfprocparam type="in" cfsqltype="cf_sql_bit" value="0">
							<cfprocparam type="out" cfsqltype="cf_sql_bit" variable="local.statusUpdated">
						</cfstoredproc>
					</cfif>
	
					<!--- Associate Card On File to Subscription --->
					<cfif arguments.event.getValue('p_#local.payProfileID#_mppid') gt 0>
						<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.payProfileID, mppid=arguments.event.getValue('p_#local.payProfileID#_mppid'))>
						<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=local.payProfileID, mppid=arguments.event.getValue('p_#local.payProfileID#_mppid'))>				
					</cfif>	
					<cfif arguments.event.getValue('p_#local.payProfileID#_mppid') gt 0 and local.contReturn.success>
						<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.contReturn.rootSubscriberID, MPProfileID=local.payProfileID, mppid=arguments.event.getValue('p_#local.payProfileID#_mppid'))>
						<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryConInvoice.invoiceID), MPProfileID=local.payProfileID, mppid=arguments.event.getValue('p_#local.payProfileID#_mppid'))>				
					</cfif>				
	
				<cfelse>
					<cfset local.strACCResponse.accResponseMessage = "">
				</cfif>		
	
			</cfif> <!---  // if local.totalAmount gt 0 --->
	
			<!--- ------------------------------------------ --->
			<!--- Checks And CCs should generate and invoice --->
			<!--- ------------------------------------------ --->
			<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfloop query="local.qryInvoice">
				<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
																			invoiceID=local.qryInvoice.invoiceID, 
																			tmpFolder=local.strFolder.folderPath, 
																			encryptFile=true, 
																			namedForBundle=false)>
				<cfset arrayAppend(local.arrInvoicePaths,local.strInvoice.invoicePath)>
			</cfloop>

			<cfif local.contReturn.success>
				<cfloop query="local.qryConInvoice">
					<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
																				invoiceID=local.qryConInvoice.invoiceID, 
																				tmpFolder=local.strFolder.folderPath, 
																				encryptFile=true, 
																				namedForBundle=false)>
					<cfset arrayAppend(local.arrInvoicePaths,local.strInvoice.invoicePath)>
				</cfloop>
			</cfif>
			
			<!--- since we already have the invoice we will pay against, set that as the only invoice in the pool so we can allocate against it --->
			<cfif QueryAddRow(local.objAccounting.invoicePool)>
				<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.qryInvoice.invoiceID[1])>
				<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.qryInvoice.invoiceProfileID[1])>
				<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.qryInvoice.totalAmount[1])>
			</cfif>
			<cfif local.contReturn.success>
				<cfif QueryAddRow(local.objAccounting.invoicePool)>
					<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.qryConInvoice.invoiceID[1])>
					<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.qryConInvoice.invoiceProfileID[1])>
					<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",val(arguments.rc.donorAmount))>
				</cfif>
			</cfif>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
			<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>
				<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=variables.useMID, transactionDate=now())>
			</cfif>
	
		</cfif>

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID,
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID,
			description=variables.historyCompletedText,
			newAccountsOnly=false
		)>			

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(
			uid='776b45b6-3152-4da8-81ba-e2b0e897ad12',
			mode="confirmation",
			strData=arguments.rc
		)>

		<cfset local.strBarQuestions = application.objCustomPageUtils.renderFieldSet(
			uid='383e5b71-986e-4e19-a101-a1c746991e3e',
			mode="confirmation",
			strData=arguments.rc
		)>

		<cfset local.strStudentQuestions = application.objCustomPageUtils.renderFieldSet(
			uid='93e7c9d6-bbd6-4ef0-b68d-96d6585e2856',
			mode="confirmation",
			strData=arguments.rc
		)>

		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(
			siteID=variables.siteID,
			memberID=variables.useMID,
			subscriptionUID='785583f4-2964-4bae-9343-a7f63bac8438',
			isRenewal=0
		)>
		
		<cfset local.strAutoRenewFieldSetContent = application.objCustomPageUtils.renderFieldSet(
			uid='b74c2d7b-bf06-4687-95a9-add09583a6d9',
			mode="confirmation",
			strData=arguments.rc
		)>

		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>

		<cfset local.memberPayProfileDetail = "">
		<cfif local.qryRatesSelected.rateAmt gt 0>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.contentConfirmation)>
				<div class="tsAppNoteText">#variables.strPageFields.contentConfirmation#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div class="tsAppNoteText">Here are the details of your application:</div><br/>

			#local.strFieldSetContent1.fieldSetContent#
			
			<cfset userType = arguments.rc.iAmA>
			<cfif userType eq 'Lawyer'>
				#local.strBarQuestions.fieldSetContent#
			<cfelseif userType eq 'Law Student'>
				#local.strStudentQuestions.fieldSetContent#
			</cfif>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Type</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<div>#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
				</td>
			</tr>
			</table>
			<br/>
			
			#local.strAutoRenewFieldSetContent.fieldSetContent#

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join TFL - Contribution</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
				<cfif len(trim(arguments.rc.donorFrequency))>
					<div>#arguments.rc.donorFrequency# contribution of #dollarFormat(arguments.rc.donorAmount)#</div>
				<cfelse>
					<div>No donation</div>
				</cfif>	
				</td>
			</tr>
			</table>

			<cfif local.qryRatesSelected.rateAmt gt 0>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Total Amount: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#dollarFormat(local.qryRatesSelected.rateAmt + val(arguments.rc.donorAmount))#
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to TFL", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">
		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">
		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.errActiveFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errAcceptedFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.errBilledFound#	
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>
