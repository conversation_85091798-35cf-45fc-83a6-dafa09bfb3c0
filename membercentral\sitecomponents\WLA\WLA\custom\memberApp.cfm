<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";

	// default and defined custom page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="CCPayProfileCode", type="STRING", desc="pay profile code for CC", value="WLACIM" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MembershipLevel", type="CONTENTOBJ", desc="Membership Level", value="Membership Level Section" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	// set page defaults
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmJoin',
		formNameDisplay='Membership Application Form',
		orgEmailTo=local.strPageFields.StaffConfirmationTo,
		memberEmailFrom=local.strPageFields.MemberConfirmationFrom
	));

	// set payment gateways 
	local._paymentProfileCode = local.strPageFields.CCPayProfileCode;
	local.profile_1 = application.objCustomPageUtils.acct_getProfile(siteid=local.siteID, profileCode=local._paymentProfileCode);

	// custom data: ----------------------------------------------------------------------------------------------------------
	local.xmlAdditionalData = application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(orgID=local.orgID);
	local.practiceAreaStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Practice Areas", xmlAdditionalData=local.xmlAdditionalData);
	local.FindLawyerTypeStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Find a Lawyer", xmlAdditionalData=local.xmlAdditionalData);
	local.MemberDirectoryTypeStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Member Directory", xmlAdditionalData=local.xmlAdditionalData);
	local.contactTypeStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Contact Type", xmlAdditionalData=local.xmlAdditionalData);

	local.subscriptionUID = '8A4D5D10-C34A-44D0-9BC0-6E35D695D378';
</cfscript>

<cfsavecontent variable="local.pageCSS">
	<cfoutput>
	#local.pageCSS#
	<style type="text/css">
		.CPSection .frmText label{float:right;}
		input[disabled], select[disabled], textarea[disabled], input[readonly], select[readonly], textarea[readonly] {
		    background-color: ##fff !important;
		    cursor:text !important;
		}
		##barDate_new, ##gradDate_new { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageCSS#">

<cfsavecontent variable="local.pageJS">
	<cfoutput>
	#local.pageJS#
	<cfswitch expression="#event.getValue('isSubmitted',0)#">
	<cfcase value="0">
		<script type="text/javascript">
			function loadMember(memNumber){
				var objParams = { memberNumber:memNumber };
				assignMemberData(objParams);
			}
			function assignMemberData(memObj){
				var er_change = function(r) {
					if (r.success && r.success == 'true' && r.memberid > 0) {
						$('##memberNumber').val(r.membernumber);
						$('##memberID').val(r.memberid);
						if (r.prefix != '') $(':radio[value="' + r.prefix + '"]').attr('checked', 'checked');
						$('##firstName').val(r.firstname);
						$('##middleName').val(r.middlename);
						$('##lastName').val(r.lastname);
						$('##suffix').val(r.suffix);
						$('##firmName').val(r.company);
						$('##address').val(r.address1);
						$('##address2').val(r.address2);
						$('##city').val(r.city);
						$('##state').val(r.statecode);
						$('##zip').val(r.postalcode);
						$('##county').val(r.county);
						$('##bPhone').val(r.phone);
						$('##email').val(r.email);
						if (r['bar date'] != null && r['bar date'] != '') $('##barDate_new').val($.datepicker.formatDate('mm/dd/yy',new Date(r['bar date'])));
						if (r['practice areas'] != null) $("input[mc-data='"+r['practice areas']+"']").prop('checked',true);	

						AJAXCheckActiveSubs(r.memberid);
						AJAXGetSubRates(r.memberid);
					}
					$('##divAccountLookupLoading').html('');
					$('##tblAccountLookup').show();
				};
				$('##formToFill, ##hasActiveDuesSubContent, ##tblAccountLookup').hide();
				$('##divAccountLookupLoading').html('<i class="icon-spinner icon-2x"></i> &nbsp; &nbsp; Please wait while we load your information.<br/><br/><br/><br/><br/>');
				var arrKeys = ["Bar Date", "Practice Areas"];
				var objParams = { memberNumber:memObj.memberNumber, customfields:arrKeys };
				TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,30000,er_change);
			}		
			function AJAXCheckActiveSubs(mid){
				var AJAXCheckActiveSubsResult = function(r){
					var alertShown = 0;
					$.each(r.data.uid,function(x){
						if ( r.data.uid[x].toLowerCase() == '#local.subscriptionUID#'.toLowerCase() ){
							$('##formToFill').hide();
							$('##hasActiveDuesSubContent').show();	
							alertShown = 1;
						}
					});
					if (alertShown == 0) $('##formToFill').show();
				};
				$('##hasActiveDuesSubContent').hide();
				var params = { memberID:mid, status:'A' };
				TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,AJAXCheckActiveSubsResult,AJAXCheckActiveSubsResult,30000,AJAXCheckActiveSubsResult);
			}
			function AJAXGetSubRates(mid) {
				var AJAXGetSubRatesResult = function(r) {
					var tbl = '<table cellspacing="0" cellpadding="0" width="100%" border="0">';
					$.each(r.data.rfid,function(x){
						tbl = tbl + '<tr>';
						tbl = tbl + '<td class="P r" width="5px"><input type="radio" value="' + r.data.rateuid[x] + '" name="Membership"></td>';
						tbl = tbl + '<td class="P l">' + r.data.ratename[x] + '<label>$ ' + formatCurrency(parseFloat(r.data.rateamt[x])) + '</label></td>';
						tbl = tbl + '</tr>';
						tbl = tbl + '<tr><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>';
					});
					tbl = tbl + '</table>';
					$('##divRateSelection').html(tbl);
				}
				$('##divRateSelection').html('<div><i class="icon-spinner icon-spin icon-4x"></i> Please wait while we load your membership options.<br/><br/><br/><br/><br/></div>');
				var params = { memberID:mid, subscriptionUID:'#local.subscriptionUID#', isRenewal:false };
				TS_AJX('CUSTOM_FORM_UTILITIES','sub_getEligibleRatesAJX',params,AJAXGetSubRatesResult,AJAXGetSubRatesResult,30000,AJAXGetSubRatesResult);
			}
			function formatCurrency(num) {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				num = Math.floor(num/100).toString();
				if(cents<10) cents = "0" + cents;
				for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
				return (((sign)?'':'-') + num + '.' + cents);
			}		
			function _FB_validateForm() {
				var arrReq = new Array();
				var ct = $('##contactType').val();

				if ($('##firstName').val() == '') arrReq[arrReq.length] = 'First Name';
				if ($('##lastName').val() == '') arrReq[arrReq.length] = 'Last Name';
				if (ct == '') arrReq[arrReq.length] = 'Contact Type';
				if ($('##firmName').val() == '') arrReq[arrReq.length] = 'Firm Name';
				if ($('##bPhone').val() == '') arrReq[arrReq.length] = 'Business Phone';
				if ($('##email').val() == '') arrReq[arrReq.length] = 'Email Address';

				if (ct=='Lifetime' || ct=='Attorney - Black Robe' || ct=='Attorney - Gavel' || ct=='Attorney - Judiciary' || ct=='Attorney - New Admittee' || ct=='Attorney - New Admittee (attendance prize)' || ct=='Attorney - Private' || ct=='Attorney - Public Interest/Government' || ct=='Attorney - Scales of Justice') {
					if ($('##barDate_new').val() == '') arrReq[arrReq.length] = 'Bar Date';
					if ($('##professionalLicense').val() == '') arrReq[arrReq.length] = 'Professional License State';
					if ($('##barNumber').val() == '') arrReq[arrReq.length] = 'Bar Number';
				}

				if (ct=='Law Student') {
					if ($('##gradDate_new').val() == '') arrReq[arrReq.length] = 'Graduation Date';
				}

				if (!$("input[name='Membership']").is(':checked')) arrReq[arrReq.length] = 'Membership level';

				if (arrReq.length > 0) {
					var msg = 'The following questions are required:\n\n';
					for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
					alert(msg);
					return false;
				}
				return true;
			}
			$(document).ready(function(){
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					loadMember('#session.cfcUser.memberData.memberNumber#');
				</cfif>
				
				mca_setupDatePickerField('barDate_new');
				mca_setupDatePickerField('gradDate_new');
				$("##btnClearbarDate").click( function(e) { mca_clearDateRangeField('barDate_new');return false; } );
				$("##btnCleargradDate").click( function(e) { mca_clearDateRangeField('gradDate_new');return false; } );

				$("##bardate_row, ##gradate_row").hide();
				$("##contactType").change(function(){
					$("##bardate_row, ##gradate_row, ##license_state_row, ##license_num_row").hide();
					switch ($("##contactType").val()) {
						case 'Attorney - Black Robe':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Attorney - Gavel':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Attorney - Judiciary':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Attorney - Private':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Attorney - Public Interest/Government':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Attorney - Scales of Justice':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Law Student':
							$("##gradate_row").show();
							break;				
						case 'Lifetime':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;
						case 'Attorney - New Admittee':
						 	$("##bardate_row, ##license_state_row, ##license_num_row").show();
							break;					
					}
				});
			});
		</script>
	</cfcase>
	</cfswitch>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageJS#">

<cfoutput>
	<div id="customPage">
		<div class="TitleText" style="padding-bottom:15px;text-align:center;">#local.Organization#<br/>#local.formNameDisplay#</div>

		<cfswitch expression="#event.getValue('isSubmitted',0)#">

			<!--- FORM DISPLAY --->
			<cfcase value="0">
				<div class="r i frmText">*Denotes required field</div>
				<cfform name="#local.formName#" id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
				<cfinput type="hidden" id="isSubmitted" name="isSubmitted" value="1">
				<cfinput type="hidden" id="memberID" name="memberID" value="0">
				<cfinput type="hidden" id="memberNumber" name="memberNumber" value="">

				<!--- ACCOUNT LOCATOR --->
				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<div class="CPSection">
						<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
						<div style="padding:10px;">
							<table cellspacing="0" cellpadding="2" border="0" width="100%" id="tblAccountLookup">
								<tr>
									<td width="175" class="c">
										<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
											<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
										</div>
									</td>
									<td>
										<span class="frmText">
											<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
											<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
											<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
											<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
										</span>
									</td>
								</tr>
							</table>
							<div id="divAccountLookupLoading"></div>
						</div>
					</div>
				</cfif>

				<div id="hasActiveDuesSubContent" style="display:none;">
					<div class="CPSection">
						<div class="CPSectionTitle BB">Alert!</div>
						<div style="padding:10px;">
							<table cellspacing="0" cellpadding="2" border="0" width="100%">
							<tr>
								<td>
									You currently have an active WLA subscription. If you feel you have reached this message in error, please contact the <NAME_EMAIL>.
								</td>
							</tr>
							</table>
						</div>
					</div>
				</div>
					
				<div id="formToFill" style="display:none;">
					<div class="CPSection">
						<div class="CPSectionTitle BB">Contact Information</div>
						<div class=" frmRow1 frmText" style="padding:10px;">
							<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
								<tr>
									<td class="r" width="200">Prefix:</td>
									<td>
										<cfinput id="prefixMr." name="prefix" type="radio" value="Mr." class="tsAppBodyText">Mr.&nbsp;&nbsp;
										<cfinput id="prefixMs." name="prefix" type="radio" value="Ms." class="tsAppBodyText">Ms.&nbsp;&nbsp;
										<cfinput id="prefixMrs." name="prefix" type="radio" value="Mrs." class="tsAppBodyText">Mrs.&nbsp;&nbsp;
									</td>
								</tr>
								<tr>
									<td class="r" width="200"><span class="required">*</span>First Name:</td>
									<td><cfinput size="40" id="firstName" name="firstName" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r" width="200">Middle Name:</td>
									<td><cfinput size="40" id="middleName" name="middleName" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r"><span class="required">*</span>Last Name:</td>
									<td><cfinput size="40" id="lastName" name="lastName" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r" width="200">Suffix:</td>
									<td><cfinput size="40" id="suffix" name="suffix" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r" width="200"><span class="required">*</span>Contact Type:</td>	
									<td>
										<select class="tsAppBodyText largeBox" name="contactType" id="contactType" class="contactType">
											<option value="">&nbsp;- Please Select - </option>
											<cfloop array="#local.contactTypeStruct.columnValueArr#" index="local.contactType">
												<cfif not listFindNoCase("Attorney - New Admittee (attendance prize),STAFF,Deceased,Attorney - Black Robe,Attorney - Gavel,Attorney - Scales of Justice",local.contactType.columnValueString)>
													<option value="#local.contactType.columnValueString#">#local.contactType.columnValueString#</option>
												</cfif>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr>
									<td class="r"><span class="required">*</span>Firm Name:</td>
									<td><cfinput size="60" id="firmName" name="firmName" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r">Firm Address:</td>
									<td><cfinput size="60" id="address" name="address" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r">&nbsp;</td>
									<td><cfinput size="60" id="address2" name="address2" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r">City:</td>
									<td><cfinput size="60" id="city" name="city" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r">State:</td>
									<td><cfinput size="2" maxlength="2" id="state" name="state" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r">Zip:</td>
									<td><cfinput size="10" maxlength="15" id="zip" name="zip" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r">County:</td>
									<td><cfinput size="60" id="county" name="county" type="text" value="" class="tsAppBodyText"></td>
								</tr>
								<tr>
									<td class="r"><span class="required">*</span>Business Phone:</td>
									<td><cfinput size="13" maxlength="13" name="bPhone" id="bPhone" type="text" value="" class="tsAppBodyText" onchange="checkPhFormat(this);"></td>
								</tr>
								<tr id="bPhone_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter business phone number in the format: ************</div></td></tr>
								<tr>
									<td class="r">Cell Phone:</td>
									<td><cfinput size="13" maxlength="13" name="cPhone" id="cPhone" type="text" value="" class="tsAppBodyText" onchange="checkPhFormat(this);"></td>
								</tr>
								<tr id="cPhone_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter cell phone number in the format: ************</div></td></tr>
								<tr>
									<td class="r"><span class="required">*</span>Email Address:</td>
									<td><cfinput size="60" id="email" name="email" type="text" value="" class="tsAppBodyText"></td>
								</tr>

								<tr id="bardate_row">
									<td class="r" id="bardate"><span class="required">*</span>Date Admitted to Bar</td>
									<td>
										<cfinput type="hidden" name="barDate_old" id="barDate_old" value="">
										<cfinput class="tsAppBodyText" type="text" name="barDate_new" id="barDate_new" value="" autocomplete="off" size="16">
										<cfinput type="button" class="tsAppBodyButton" name="btnClearbarDate" id="btnClearbarDate" value="clear">
									</td>
								</tr>
								<tr id="gradate_row">
									<td class="r" id="gradate"><span class="required">*</span>Expected Graduation Date</td>
									<td>
										<cfinput type="hidden" name="gradDate_old" id="gradDate_old" value="">
										<cfinput class="tsAppBodyText" type="text" name="gradDate_new" id="gradDate_new" value="" autocomplete="off" size="16">
										<cfinput type="button" class="tsAppBodyButton" name="btnCleargradDate" id="btnCleargradDate" value="clear">
									</td>
								</tr>
								<tr id="license_state_row">
									<td class="r" width="200"><span class="required">*</span>Professional License State:</td>	
									<td>
										<select class="tsAppBodyText largeBox" name="professionalLicense" id="professionalLicense">
											<option value="">&nbsp;- Please Select - </option>
											<option value="MO">&nbsp;MO </option>
											<option value="Out Of State">&nbsp;Out Of State </option>
										</select>
									</td>
								</tr>
								<tr id="license_num_row">
									<td class="r" width="200"><span class="required">*</span>Bar Number:</td>	
									<td>
										<cfinput size="13" name="barNumber" id="barNumber" type="text" value="" class="tsAppBodyText">
									</td>
								</tr>
							</table>
						</div>
					</div>
						
					<div class="CPSection">
						<div class="CPSectionTitle BB"><span class="required">*</span>Membership Level</div>
						<div class=" subCPSectionArea1 BB">
							<div class=" subCPSectionText">
								#local.strPageFields.MembershipLevel#
							</div>
						</div>
						<div class="frmText" id="divRateSelection">
						</div>
					</div>

					<div class="CPSection">
						<div class="CPSectionTitle BB">Member Directory Preferences</div>
						<div class="subCPSectionArea1 BB frmText">
							Please choose your member directory preferences below.
						</div>

						<div class="frmText">
							<table cellspacing="0" cellpadding="0" width="100%" border="0">
								<tr class="frmRow1">
									<td class="P c">
										<b>Find a Lawyer</b>&nbsp;&nbsp;&nbsp;&nbsp;
										<select class="tsAppBodyText largeBox" name="findALawyer" id="findALawyer" style="width:120px;">
											<cfloop array="#local.FindLawyerTypeStruct.columnValueArr#" index="local.thisArea">
												<option value="#local.thisArea.columnValueString#" <cfif local.thisArea.columnValueString eq 'Yes'>selected="true"</cfif>>#local.thisArea.columnValueString# &nbsp;</option>
											</cfloop>
										</select>
									</td>
									<td class="P c">
										<b>Member Directory</b>&nbsp;&nbsp;&nbsp;&nbsp;
										<select class="tsAppBodyText largeBox" name="memberDirectory" id="memberDirectory" style="width:120px;">
											<cfloop array="#local.MemberDirectoryTypeStruct.columnValueArr#" index="local.thisArea">
												<option value="#local.thisArea.columnValueString#" <cfif local.thisArea.columnValueString eq 'Yes'>selected="true"</cfif>>#local.thisArea.columnValueString# &nbsp;</option>
											</cfloop>
										</select>
									</td>
								</tr>
							</table>
						</div>
					</div>						

					<div class="CPSection">
						<div class="CPSectionTitle BB">Areas of Practice</div>
						<div class="frmText">
							<table cellspacing="0" cellpadding="0" width="100%" border="0">
								<cfset local.itemCount = 1 />
								<cfloop array="#local.practiceAreaStruct.columnValueArr#" index="local.thisArea">
									<cfif local.itemCount MOD 2 neq 0 >
										<tr class="frmRow1">
									</cfif>
											<td width="5px" class="P"><input type="checkbox" value="#local.thisArea.valueID#" mc-data="#local.thisArea.columnValueString#" name="practiceAreas" /></td>
											<td class="P l">#local.thisArea.columnValueString#</td>
									<cfif local.itemCount MOD 2 eq 0 >
										</tr>
										<tr class="frmRow1"><td colspan="4" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									</cfif>
									<cfset local.itemCount = local.itemCount + 1 />
								</cfloop>
							</table>
						</div>
					</div>
						
					<div id="formButtons">
						<div style="padding:10px;">
							<div align="center" class="frmButtons">
								<input type="submit" value="Continue" name="btnSubmit">
							</div>
						</div>
					</div>
				</div>
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				</cfform>
			</cfcase>
			
			
			<!--- SAVE MEMBER AND PROMPT FOR PAYMENT INFO --->
			<cfcase value="1">
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>

				<!--- --------------------------------------------------------------------------------------------------------------- --->
				<!--- if we just created member or if logged in, use that memberID, otherwise create a new member to save all data to --->
				<!--- --------------------------------------------------------------------------------------------------------------- --->
				<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
				<cfif local.useMID eq session.cfcuser.memberdata.memberID OR (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),local.useMID))>	
					<cfset local.useMID = local.useMID>
				<cfelse>
					<cfset local.useMID = 0>
				</cfif>

				<cfset local.thisStateID = 0>
				<cfif len(trim(arguments.event.getValue('state')))>
					<cfset local.qryStates = application.objCommon.getStates()>
					<cfquery name="local.getStateID" dbtype="query">
						select stateID 
						from [local].qryStates 
						where StateCode = <cfqueryparam value="#arguments.event.getValue('state')#" cfsqltype="cf_sql_varchar">
						and countryID = 1
					</cfquery>
					<cfset local.thisStateID = val(local.getStateID.stateID)>
				</cfif>	

				<cfscript>
				local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.useMID);
				local.objSaveMember.setMemberType(memberType='User');
				local.objSaveMember.setRecordType(recordType='Individual');
				local.objSaveMember.setDemo(prefix=arguments.event.getValue('prefix',''),
										firstName=arguments.event.getValue('firstName',''),
										middleName=arguments.event.getValue('middleName',''), 
										lastName=arguments.event.getValue('lastName',''),
										company=arguments.event.getValue('firmName',''),
										suffix=arguments.event.getValue('suffix',''));

				if (len(arguments.event.getValue('email')))
					local.objSaveMember.setEmail(type='Work Email', value=arguments.event.getValue('email'));

				local.objSaveMember.setAddress(type='Business Address', 
										address1=arguments.event.getValue('address',''),
										address2=arguments.event.getValue('address2',''),
										city=arguments.event.getValue('city',''), 
										stateID=local.thisStateID, 
										postalCode=arguments.event.getValue('zip',''),
										county=arguments.event.getValue('county',''));
				if (len(arguments.event.getValue('bPhone','')))
					local.objSaveMember.setPhone(addressType='Business Address', type='Phone', value=arguments.event.getValue('bPhone'));
				if (len(arguments.event.getValue('cPhone','')))
					local.objSaveMember.setPhone(addressType='Business Address', type='Mobile', value=arguments.event.getValue('cPhone'));

				local.objSaveMember.setCustomField(field='Contact Type', value=arguments.event.getTrimValue('contactType',''));
				if (len(arguments.event.getValue('barDate_new')))
					local.objSaveMember.setCustomField(field='Bar Date', value=dateFormat(event.getValue('barDate_new'),'short'));
				else if (len(arguments.event.getValue('barDate_old')))
					local.objSaveMember.setCustomField(field='Bar Date', value=dateFormat(event.getValue('barDate_old'),'short'));
				if (len(arguments.event.getValue('gradDate_new')))
					local.objSaveMember.setCustomField(field='Graduation Date', value=dateFormat(event.getValue('gradDate_new'),'short'));
				else if (len(arguments.event.getValue('gradDate_old')))
					local.objSaveMember.setCustomField(field='Graduation Date', value=dateFormat(event.getValue('gradDate_old'),'short'));
				if (len(arguments.event.getValue('memberDirectory','')))
					local.objSaveMember.setCustomField(field='Member Directory', value=event.getValue('memberDirectory',''));
				if (len(arguments.event.getValue('findALawyer','')))
					local.objSaveMember.setCustomField(field='Find a Lawyer', value=event.getValue('findALawyer',''));
				if (len(arguments.event.getTrimValue('practiceAreas','')))
					local.objSaveMember.setCustomField(field='Practice Areas', valueID=arguments.event.getTrimValue('practiceAreas',''));
				if (len(arguments.event.getTrimValue('barNumber',''))) {
					local.thisBarDate = "";
					if (len(arguments.event.getTrimValue('barDate_new')))
						local.thisBarDate = dateFormat(arguments.event.getValue('barDate_new'));
					if (len(local.thisBarDate))
						local.objSaveMember.setProLicense(name='Missouri Bar', status='Active', license=arguments.event.getValue('barNumber',''), date=local.thisBarDate);
				}
				local.strResult = local.objSaveMember.saveData(runImmediately=1);
				</cfscript>

				<cfset local.useMID = local.strResult.memberID>
				<cfset arguments.event.setValue('memberID',local.useMID)>
				<cfif NOT local.strResult.success>
					<cflocation url="#local.customPage.baseURL#" addtoken="no">
				</cfif>
				
				<!--- determine rate amount --->
				<cfset local.rateAmt = 0>
				<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=local.siteID, memberID=local.useMID, subscriptionUID=local.subscriptionUID, isRenewal=false)>
				<cfloop query="local.qryRates">
					<cfif local.qryRates.rateUID eq arguments.event.getValue('membership','')>
						<cfset local.rateAmt = local.qryRates.rateAmt>
					</cfif>
				</cfloop>
				
				<cfif val(local.rateAmt) eq 0>
					<cfform name="redirect#local.formName#" id="redirect#local.formName#" method="POST" action="#local.customPage.baseURL#">
						<cfinput type="hidden" name="isSubmitted" id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
						<cfloop collection="#arguments.event.getCollection()#" item="local.key">
							<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
								and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
								and left(local.key,4) neq "fld_">
								<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
							</cfif>
						</cfloop>
						<input type="hidden" name="payMeth" value="CC">
						<img src="/assets/common/images/loading-dots.gif">
						<button type="submit" class="tsAppBodyButton" name="btnSubmitNewBar" style="display:none;">Continue</button>
					</cfform>
					<script type="text/javascript">
						window.onload = $('##redirect#local.formName#').submit();
					</script>
				<cfelse>
					<!--- prep payment form --->
					<cfset local.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																	siteid=arguments.event.getValue('mc_siteinfo.siteid'),
																	profilecode=local.profile_1._profileCode,
																	pmid = local.useMID,
																	showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																	usePopupDIVName='paymentTable')>
					<script type="text/javascript">
						function checkPaymentMethod() {
							var rdo = document.forms["#local.formName#"].payMeth;
							if (rdo[0].checked) {//credit card
								document.getElementById('CCInfo').style.display = '';
								document.getElementById('CheckInfo').style.display = 'none';
							}  
							else{//check
								document.getElementById('CheckInfo').style.display = '';
								document.getElementById('CCInfo').style.display = 'none';
							}
							
						}
						// -----------------------------------------------------------------------------------------------------------------
						function getMethodOfPayment() {
							var btnGrp = document.forms['#local.formName#'].payMeth;
							var i = getSelectedRadio(btnGrp);
							if (i == -1) return "";
							else {
								if (btnGrp[i]) return btnGrp[i].value;
								else return btnGrp.value;
							}
						}					
						// -----------------------------------------------------------------------------------------------------------------
						function _validate() {
							var thisForm = document.forms["#local.formName#"];
							var arrReq = new Array();
							// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
							var MethodOfPaymentValue =  getMethodOfPayment();
							
							if( MethodOfPaymentValue == 'CC' )	{
								#local.profile_1.strPaymentForm.jsvalidation#
								var confirmation 	= 0;
								var statement			= thisForm['confirmationStatement'];
								if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
								if(confirmation == 0) arrReq[arrReq.length] = 'Please accept the Confirmation Statement';
							}
							
							// -----------------------------------------------------------------------------------------------------------------
							if (arrReq.length > 0) {
								var msg = 'The following fields are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							return true;
						}
					</script>
					
					<script>
						function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
					</script>
					<cfif len(local.profile_1.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
					</cfif>
					
					<div id="paymentTable">
						<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
						<div class="form">
							<cfform name="#local.formName#" id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
								<cfinput type="hidden" name="isSubmitted" id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
								<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
										and left(local.key,4) neq "fld_">
										<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
									</cfif>
								</cfloop>
								<div>
									<!--- ----------------------------------------------------------------------------------------------------- --->
									<div class="CPSection">
										<div class="CPSectionTitle">*Method of Payment</div>
										<div class="P">
											<table cellpadding="2" cellspacing="0" width="100%" border="0">
												<tr valign="top">
													<td colspan="2">Please select your preferred method of payment from the options below.</td>
												</tr>
												<tr>
													<td>
														<table cellpadding="2" cellspacing="0" width="100%" border="0">
															<tr>
																<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
																<td>Credit Card</td>
															</tr>
															<tr>
																<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
																<td>Check</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</div>
									</div>
									
									<!--- CREDIT CARD INFO: ----------------------------------------------------------------------------------- --->
									<div id="CCInfo" style="display:none;" class="CPSection">
										<div class="CPSectionTitle">Credit Card Information</div>
										<div class="PL PR frmText paymentGateway BT BB">
											<cfif len(local.profile_1.strPaymentForm.inputForm)>
												<div>#local.profile_1.strPaymentForm.inputForm#</div>
											</cfif>
										</div>
										
										<div class="P">
											<div class="PB">* Please confirm the statement below:</div>
											<table width="100%">
												<tr>
													<td width="25"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
													<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
												</tr>
											</table>
										</div>
										
										<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">AUTHORIZE</button></div>
									</div>
									
									<!--- CHECK INFORMATION: ---------------------------------------------------------------------------------- --->
									<div id="CheckInfo" style="display:none;" class="CPSection">
										<div class="CPSectionTitle">Check Information</div>
										<div class="P">
											Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
											<strong>The Women Lawyers' Association of Greater St. Louis</strong><br />
											PO Box 50056<br />
											St. Louis, MO 63105
										</div>
										<div class="P">
											<button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button><br />
											<br />
											<strong>NOTE:</strong> Your membership will not be active until payment is received.
										</div>
									</div>
									
								</div>
							</cfform>
						</div>
					</div>
				</cfif>				
			</cfcase>
		
			<!--- PROCESS PAYMENT/CONFIRMATION --->
			<cfcase value="2">

				<!--- determine rate selected --->
				<cfset local.rateUID = "">
				<cfset local.rateAmt = 0>
				<cfset local.rateName = "">
				<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=local.siteID, memberID=local.useMID, subscriptionUID=local.subscriptionUID, isRenewal=false)>
				<cfloop query="local.qryRates">
					<cfif local.qryRates.rateUID eq arguments.event.getValue('membership','')>
						<cfset local.rateUID = local.qryRates.rateUID>
						<cfset local.rateAmt = local.qryRates.rateAmt>
						<cfset local.rateName = local.qryRates.rateName>
					</cfif>
				</cfloop>

				<cfset local.areasAsString = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=local.orgID, columnName='Practice Areas', valueIDList=arguments.event.getValue('practiceAreas','')),'|',', ','ALL')>


				<!--- -------------------- --->
				<!--- Create Subscriptions --->
				<!--- -------------------- --->
				<cfset local.subStruct = { children=arrayNew(1), uid=local.subscriptionUID, rateUID=local.rateUID }>
				<cfset local.subReturn = CreateObject("component","model.admin.subscriptions.SubscriptionReg").autoSubscribe(event=arguments.event, memberID=local.useMID, subStruct=local.subStruct, newAsBilled=false)>


				<!--- -------------------------------------------- --->
				<!--- Record Payment and allocate to subscriptions --->
				<!--- -------------------------------------------- --->
				<cfif arguments.event.getValue('payMeth') eq 'CC' and local.rateAmt gt 0>
					<cfset local.strAccTemp = { totalPaymentAmount=local.rateAmt, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=arguments.event.getCollection() } >
					<cfset local.strAccTemp.payment = { detail=local.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.profile_1._profileID, profileCode=local._paymentProfileCode }>
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

					<cfset local.qryInvoices = application.objCustomPageUtils.sub_getInvoicesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID)>
					<cfloop query="local.qryInvoices">
						<cfif local.qryInvoices.sumTotal gt 0 and QueryAddRow(local.objAccounting.invoicePool)>
							<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.qryInvoices.invoiceID)>
							<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.qryInvoices.invoiceProfileID)>
							<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.qryInvoices.sumTotal)>
						</cfif>
					</cfloop>

					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>			
				<cfelse>
					<cfset local.strACCResponse.accResponseMessage = "">
				</cfif>	


				<!--- prep confirmation --->
				<cfsavecontent variable="local.invoice">
					#local.pageCSS#

						<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
						<!-- @accResponseMessage@ -->

						<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
						<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">Prefix:</td><td class="frmText">#event.getValue('prefix','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Suffix:</td><td class="frmText">#event.getValue('suffix','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm:</td><td class="frmText">#event.getValue('firmName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm Address:</td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">&nbsp;</td><td class="frmText">#event.getValue('address2','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">County:</td><td class="frmText">#event.getValue('county','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Cell Phone:</td><td class="frmText">#event.getValue('cphone','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Business Phone:</td><td class="frmText">#event.getValue('bphone','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Email Address:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Date Admitted to Bar</td><td class="frmText">#event.getValue('barDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Expected Graduation Date</td><td class="frmText">#event.getValue('gradDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Professional License State</td><td class="frmText">#event.getValue('professionalLicense','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Bar Number</td><td class="frmText">#event.getValue('barNumber','')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP LEVEL</td></tr>
						<tr class="frmRow1"><td class="frmText b">Contact Type</td> <td class="frmText b">#event.getValue('contactType','')#</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.rateName#</td> <td class="frmText b">#dollarformat(local.rateAmt)#</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">MEMBER DIRECTORY PREFERENCES</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Find a Lawyer Directory:</td><td class="frmText">#event.getValue('findALawyer','')#&nbsp;</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Member Directory:</td><td class="frmText">#event.getValue('memberDirectory','')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">AREAS OF PRACTICE</td></tr>
						<tr class="frmRow1"><td colspan="2" class="frmText"><cfif len(local.areasAsString)>#local.areasAsString#<cfelse>(none selected)</cfif></td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
					</table>
				</cfsavecontent>


				<!--- email confirmations --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
						<hr/>
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>
				
				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.memberEmail.from },
					emailto=[{ name="", email=local.memberEmail.to }],
					emailreplyto=local.ORGEmail.to,
					emailsubject=local.memberEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val(local.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				  )>

				<cfset local.emailSentToUser = local.responseStruct.success>
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<b>#local.name# was not sent email confirmation.</b>
							<hr/>
						</cfif>
						#replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)#
					</cfoutput>
				</cfsavecontent>
				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}

					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle = "#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
							emailhtmlcontent = local.mailContent,
							siteID = arguments.event.getValue('mc_siteinfo.siteID'),
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID = this.siteResourceID
						);
					}
				</cfscript>
				
				<!--- relocate to message page --->
				<cfset session.invoice = local.invoice>
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			

			<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
			<cfcase value="99">
				<!--- output to screen --->
				<div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<div>This page has been emailed to the email address on file. If you would like, you could also print the page out as a receipt.</div>
				<br />
				<cfif isDefined("session.invoice")>
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
					<cfset StructDelete(session,"invoice")>
				</cfif>
			</cfcase>
			
			<!--- SPAM MESSAGE: ================================================================================================================================= --->
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>
