<cfinclude template="../commonJS.cfm">

<cfsavecontent variable="local.pageJS">
	<cfoutput>
	#application.objWebEditor.showEditorHeadScripts()#
	<script type="text/javascript">
		function seeMoreSolicitorInstr() {
			$('div.divSolicitorInstr').css('max-height','none');
			$('div.divSolicitorInstr').next().toggle();
		}
		function seeMoreProspectDetails(cls) {
			$('div.'+cls).css('height','auto');
			$('div.'+cls).next().toggle();
		}
		function loadTaskNotes() {
			var notesResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					taskNotesTemplateSource = $('##mc_taskNotesTempate').html();
					taskNotesTemplate = Handlebars.compile(taskNotesTemplateSource);
					$('##divMCTaskNotesLoading').hide();
					$('##divMCTaskNotes').html(taskNotesTemplate(r));
				} else {
					alert('We were unable to load task notes.');
				}
			};
			var objParams = { resourceType:'task', usageType:'taskNote', itemID:#attributes.data.taskID# };
			TS_AJX('RESOURCENOTES','getResourceNotes',objParams,notesResult,notesResult,10000,notesResult);
		}
		function flagTaskForStaffReview() {
			$('button.step3ActionBtn').show();
			$('##btnFlagForStaffReview').hide(100);
			$('##divStep3FormContainer').html('<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>').load('#attributes.data.flagTaskForStaffReviewLink#').show();
			return false;
		}
		function cancelStaffReview() {
			$('html,body').animate({ scrollTop: $('##divStep3FormContainer').offset().top - 100}, 'slow');
			$('##divStep3FormContainer').html('').hide();
			$('##btnFlagForStaffReview').show();
		}
		function doMarkTaskAsComplete() {
			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					window.location.reload();
				} else {
					alert('We were unable to update task status. Try again.');
				}
			};

			var objParams = { taskID:#attributes.data.taskID#, statusName:'Done' };
			TS_AJX('ADMINTASK','updateTaskStatus',objParams,saveResult,saveResult,10000,saveResult);
		}
		function getMarkAsCompleteFormDetails() {
			if ($('##btnMarkAsComplete')) $('##btnMarkAsComplete').prop('disabled',true).html('Please Wait...');

			var fd = new Object();
				fd.siteid = #attributes.data.siteID#;
				fd.taskid = #attributes.data.taskID#;
				fd.projectid = #attributes.data.projectID#;

			taskFieldTemplateSource = $('##mc_taskFieldTempate').html();
			taskFieldTemplate = Handlebars.compile(taskFieldTemplateSource);

			$.ajax({
				url: '?event=proxy.ts_json&c=ADMINTASK&m=getTaskProjectFieldsInfo',
				type: 'POST',
				data: {fd:JSON.stringify(fd)},
				dataType: 'json',
				success: function(response) { 
					var tmpResponse = JSON.stringify(response).replace(/\^~~~\^/g,'');
					var responseResult = JSON.parse(tmpResponse);
					
					if(responseResult.hasTaskTags) {
						if(fd.taskid > 0) {
							$('##divMCTaskTags').append('<input type="hidden" name="oldTaskTags" value="'+responseResult.arrSelectedTaskTags.join(',')+'">');
						}

						for (var i=0; i<responseResult.arrWorkspaceCatTrees.length; i++) {
							var taskTagsHTML = '<div style="margin-top:5px;"><b>'+responseResult.arrWorkspaceCatTrees[i].categoryTreeName+'</b></div>';

							for (var j=0; j<responseResult.arrWorkspaceCatTrees[i].arrTaskTags.length; j++) {
								var tskTag = responseResult.arrWorkspaceCatTrees[i].arrTaskTags[j];

								if (tskTag.isSelected) var checkedHTML = ' checked="checked"';
								else var checkedHTML = '';

								taskTagsHTML += '<div class="mc_tasktags"><label class="checkbox"><input type="checkbox" name="taskTags" value="'+tskTag.categoryID+'" onclick="showOrHideTaskTagFields(this.checked,'+tskTag.categoryID+');"'+checkedHTML+'>&nbsp;'+tskTag.categoryname+'</label></div>';

								/*task tag fields*/
								if(tskTag.hasTaskTagFields) {
									var taskTagFields = new Object();
									taskTagFields.categoryID = tskTag.categoryID;
									taskTagFields.instanceTitle = responseResult.arrWorkspaceCatTrees[i].categoryTreeName + ' - ' + tskTag.categoryname;
									taskTagFields.arrResourceFields = tskTag.arrTaskTagFields;

									$('##divMCTaskTagFields').append(taskFieldTemplate(taskTagFields));

									if(fd.taskid > 0 && responseResult.arrSelectedTaskTags.length > 0 && responseResult.arrSelectedTaskTags.map(Number).indexOf(Number(tskTag.categoryID)) != -1)
										$('##div_tasktag_'+tskTag.categoryID+'_cf').removeClass('mc_task_hide');
								}
							}

							$('##divMCTaskTags').append(taskTagsHTML);
						}

						initializeTaskFieldsControls($('div.div_tasktag_cf'));

						showProspectMarkAsCompleteForm();
						<cfif attributes.data.qryTaskDetails.statusName eq 'Confirmed'>
							$('##divMCTaskResultOptions input').prop('disabled', true);
							$('##divMCTaskResultOptions select').prop('disabled', true);
							$('##divMCTaskResultOptions textarea').prop('disabled', true);
						</cfif>

					}
					<cfif attributes.data.showProspectCCForm>
						else {
							showProspectMarkAsCompleteForm();
						}
					<cfelse>
						else {
							doMarkTaskAsComplete();
						}
					</cfif>

					$('##btnSaveTask').attr('disabled',false);
					
				}, fail: function(response) { 
					alert('We were unable to load result options.');
				}
			});
		}
		function initializeTaskFieldsControls (scope, mode) {
			if(mode === undefined) {
				mode = 'init';
			}
			
			mca_setupMultipleDatePickerFields(scope,'MCAdminDateControl');

			scope.find('.MCAdminDateControlClearLink').click(function(event){
				var linkedDateControlID = $(this).data('linkeddatecontrol');
				$('##' + linkedDateControlID).val('').change();
				event.preventDefault();
			});

			scope.find('button.MCShowTaskFieldLink').click(function(event){
				var linkedInstanceWrapper = $(this).data('linkedinstance');
				showTaskFieldInstance($('##' + linkedInstanceWrapper));
				event.preventDefault();
			});

			scope.find('button.MCHideTaskFieldLink').click(function(event){
				var linkedInstanceWrapper = $(this).data('linkedinstance');
				hideTaskFieldInstance($('##' + linkedInstanceWrapper));
				event.preventDefault();
			});

			scope.find('.MCTaskFieldTitle').click(function(event){
				var linkedInstanceWrapper = $(this).data('linkedinstance');
				toggleTaskFieldInstanceVisibility($('##' + linkedInstanceWrapper));
				event.preventDefault();
			});
		}
		function showOrHideTaskTagFields(chk,catid) {
			if (chk) $('##div_tasktag_'+catid+'_cf').removeClass('mc_task_hide');
			else $('##div_tasktag_'+catid+'_cf').addClass('mc_task_hide');
		}
		function hideTaskFieldInstance(wrapperElement) {
			$(wrapperElement).addClass('MCTaskFieldInstanceCollapsed');
		}
		function showTaskFieldInstance(wrapperElement) {
			$(wrapperElement).removeClass('MCTaskFieldInstanceCollapsed');
		}
		function toggleTaskFieldInstanceVisibility(wrapperElement) {
			$(wrapperElement).toggleClass('MCTaskFieldInstanceCollapsed');
		}
		function cancelTaskResultOptions() {
			$('##divMCTaskTags,##divMCTaskTagFields').html('');
			$('##divMCTaskResultOptions').hide();
			$('##btnMarkAsComplete').html('<i class="icon-check text-success"></i> Mark as Complete').prop('disabled',false).show();
			location.href="##tskProspectDetails";
		}
		function validateTaskResultOptionsForm() {
			$("##btnConfirmAsComplete").prop('disabled',true);

			var strErr = '';
			if ($('.MCTaskField').length) {
				var fieldsErrorArray = [];
				var fieldContainer = $('##divMCTaskResultOptions');
				fieldsErrorArray = $.map(fieldContainer,doTaskFieldsValidate);

				/*drop empty elements*/
				var fieldsErrorArray = $.map(fieldsErrorArray, function(thisError){
					if (thisError.length) return thisError;
					else return null;
				});

				strErr += fieldsErrorArray.join('<br/>');
			}

			<cfif attributes.data.showProspectCCForm>
				if($('input[name="addNewCC"]').is(':checked') && $('input[name="p_#val(attributes.data.profileID)#_mppid"]').length == 0){
					strErr += (strErr.length ? '<br/>' : '' ) + 'You must enter a credit card, or uncheck the "Add new card" checkbox to continue';
				}
			</cfif>

			if (strErr.length > 0) {
				showAlert(strErr);
				location.href="##taskFrmTop";
				
				$("##btnConfirmAsComplete").prop('disabled',false);
				return false;
			} else {
				hideAlert();
			}

			return true;
		}		
		function addTaskNote() {
			$('button.step3ActionBtn').show();
			$('##btnAddProgressNote').hide(100);
			$('##divStep3FormContainer').html('<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>').load('#attributes.data.addTaskNoteLink#').show();
			return false;
		}
		function cancelTaskNote() {
			$('html,body').animate({ scrollTop: $('##divStep3FormContainer').offset().top - 100}, 'slow');
			$('##divStep3FormContainer').html('').hide();
			$('##btnAddProgressNote').show();
		}
		function onAddTaskNoteResult(){
			loadTaskNotes();
			cancelTaskNote();
		}
		function emailTaskProspect() {
			$('button.step3ActionBtn').show();
			$('##btnSendEmailToProspect').hide(100);
			$('##divStep3FormContainer').html('<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>').load('#attributes.data.emailProspectLink#').show();
			return false;
		}
		function resendEmailTaskProspect(msgID){
			$('##divStep3FormContainer').html('<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>').load('#attributes.data.emailProspectLink#&resendMessageID='+msgID, loadEmailTemplateContent).show();
			return false;
		}

		<cfif attributes.data.appRightsStruct.unassignOwnTasks eq 1>
			function removeTaskSolicitor() {
				$('button.step3ActionBtn').show();
				$('##btnUnassignProspect').hide(100);
				$('##divStep3FormContainer').html('<i class="icon-spin icon-spinner"></i><b>Please Wait...</b>').load('#attributes.data.addTaskNoteForUnassignLink#', function(){
					$('##divStep3FormContainer ##divResourceNoteForm button##btnSaveResourceNote').text('Save Note & Unassign Prospect');
				}).show();
				return false;
			}
			function cancelTaskNoteForUnassign() {
				$('html,body').animate({ scrollTop: $('##divStep3FormContainer').offset().top - 100}, 'slow');
				$('##divStep3FormContainer').html('').hide();
				$('##btnUnassignProspect').show();
			}
			function onAddTaskNoteForUnassignResult(){
				doRemoveTaskSolicitor();
			}
			function doRemoveTaskSolicitor() {
				var solicitorID = #attributes.data.orgMemberID#;
				var removeResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						window.location = '#attributes.data.mainurl#&wsAction=showDetail&pid=#attributes.data.projectID#';
					} else {
						alert('We were unable to remove this #attributes.data.labels.prospectFieldLabel# from yourself.');
					}
				};

				var objParams = { taskID:#attributes.data.taskID#, memberID:solicitorID, projectSRID:#attributes.data.qryProjectDetails.siteResourceID# };
				TS_AJX('ADMINTASK','removeTaskAssignee',objParams,removeResult,removeResult,10000,removeResult);
			}
		</cfif>

		function loadEmailTemplateContent() {
			var result = function(r) {
				$('##loadingTemp').hide();
				if (r.success && r.success.toLowerCase() == 'true') {
					getPreviewMessage(r.emailcontent,r.subjectline,r.emailfromname,r.emailfrom);
				} else {
					alert('An error occurred while loading the template.');
				}
			};

			hideProspectEmailAlert();
			$('##divTestEmail,##btnEmailProspect').hide();

			if ($('##fEmailTemplateID').val() != 0) {
				$('##loadingTemp').show();
				var objParams = { emailTemplateID:$('##fEmailTemplateID').val() };
				TS_AJX('MASSEMAIL','getEmailTemplateContent',objParams,result,result,10000,result);
			}
		}

		function getPreviewMessage(templateContent,subjectLine,fromName,fromEmail) {
			var result = function(r) {
				$('##divPreviewEmailMessageLoading').hide();
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##spFrom').html(fromEmail + ' (' + decodeURIComponent(r.emailfrom) + ')');
					CKEDITOR.instances['emailProspectContent'].setData(r.parsedcontent);
					CKEDITOR.instances['emailProspectContent'].resize('100%','300',true);
					$('##spSubject').html(decodeURIComponent(r.subjectline));
					$('##divTestEmail,##btnEmailProspect').show();
				} else {
					alert('Unable to generate message preview.');
				}
			};

			$('##spSubject').html('');
			$('##divPreviewEmailMessageLoading').show();

			var objParams = { 
				itemID:'#attributes.data.taskID#|#attributes.data.qryTaskDetails.prospectMemberID#', 
				resourceType:'Tasks', recipientType:'', recipientMode:'prospect', 
				templateContent:encodeURIComponent(templateContent), subjectLine:encodeURIComponent(subjectLine), 
				emailFrom:encodeURIComponent(fromName) 
			};
			TS_AJX('MASSEMAIL','getPreviewEmailMessage',objParams,result,result,10000,result);
		}

		function validateProspectEmailForm() {
			hideProspectEmailAlert(); 
			$('##btnEmailProspect').prop('disabled',true).html('Sending...');

			var errMsg = '';
			var emailRegEx = new RegExp("#application.regEx.email#","gi");
			var objParams = { fEmailTemplateID:$('##fEmailTemplateID').val(), recipientEmail:$('##recipientEmail').val().trim(), 
								emailProspectContent:CKEDITOR.instances['emailProspectContent'].getData().trim() };
			
			if(!objParams.recipientEmail.length) errMsg += 'Enter the e-mail to address.<br/>';
			if (objParams.recipientEmail.length > 0 && !(emailRegEx.test(objParams.recipientEmail))) errMsg += 'Enter a valid e-mail to address.<br/>';
			if(objParams.fEmailTemplateID == "") errMsg += 'Select a Email Template.<br/>';
			if(!objParams.emailProspectContent.length) errMsg += 'Enter the e-mail body.<br/>';

			if (errMsg.length) {
				showProspectEmailAlert(errMsg);
				return false;
			}
			
			$.post($('##frmEmailProspect').attr('action'), objParams)
				.done(function(data) {
					var objJSON = JSON.parse(data);
					if (objJSON.success && objJSON.success == true) {
						cancelEmailForm();
					} else {
						showProspectEmailAlert(objJSON.error);
					}
				})
				.fail(function(xhr) {
					showProspectEmailAlert('An unexpected error occurred while sending this email.');
				});

			return false;
		}

		function cancelEmailForm() {
			$('html,body').animate({ scrollTop: $('##divStep3Actions').offset().top - 150}, 'slow');
			$('##btnSendEmailToProspect').show();
			$('##divStep3FormContainer').html('').hide();
		}
		function hideProspectEmailAlert() { $('##mc_tskprospect_err').html('').hide(); };
		function showProspectEmailAlert(msg) { 
			$('##mc_tskprospect_err').html(msg).show(); 
			$('##btnEmailProspect').prop('disabled',false).html('<i class="icon-envelope"></i> Send Email');
			$('html,body').animate({ scrollTop: $('##mc_tskprospect_err').offset().top - 100}, 'slow');
		};

		function getProspectAdditionalInfo() {
			var objParams = { projectID:#attributes.data.projectID#, memberID:#attributes.data.qryTaskDetails.prospectMemberID# };
			$.getJSON('/?event=proxy.ts_json&c=TASKPROSPECT&m=getProspectDetails', objParams)
				.done(loadProspectMemberAdditionalInfo)
				.fail(loadProspectMemberAdditionalInfo);
		}

		function loadProspectMemberAdditionalInfo(objResult) {
			var source = $('##mc_taskProspectDetailsTempate').html();
			var template = Handlebars.compile(source);
			$('##divMCTaskProspectAdditionalInfo').html(template(objResult));
			initTaskScreens();
		}

		function initTaskScreens() {
			$(".divSolicitorInstr,.divProspectDetails").each(function () {
				$(this).next().toggle(this.scrollHeight > this.offsetHeight);

				if (this.scrollWidth > this.offsetWidth)
					$(this).css('overflow-x','scroll');
				else if ($(this).find('table.tblProspectDetails').height() + 100 < this.offsetHeight)
					$(this).css('height','175px');
			});
		}

		function showProspectMarkAsCompleteForm() {
			$('##btnMarkAsComplete').hide();
			$('div##divMCTaskResultOptions').show();
		}

		<cfif attributes.data.showProspectCCForm>
			function initProspectCCForm() {
				var thisForm = document.forms["frmProspectMarkAsComplete"];

				if(thisForm.p_#val(attributes.data.profileID)#_mppid){
					$('##addNewCCContainer').hide();
					toggleProspectCCContainer(true);
				}
				if ((thisForm.p_#val(attributes.data.profileID)#_mppid) && (thisForm.p_#val(attributes.data.profileID)#_mppid.length))
				{
					for (var i=0; i < thisForm.p_#val(attributes.data.profileID)#_mppid.length; i++)
					{
						if (thisForm.p_#val(attributes.data.profileID)#_mppid[i].value == #val(attributes.data.qryTaskDetails.payProfileID)#)
						{
							thisForm.p_#val(attributes.data.profileID)#_mppid[i].checked = true;
							break;
						}
					}
				}
			}
			function toggleProspectCCContainer(ischecked){
				if(ischecked) $('##divProspectCCContainer').show();
				else $('##divProspectCCContainer').hide();
			}
		</cfif>

		<cfif val(attributes.data.qryProjectWorkspaceSettings.prospectContactFSID) gt 0>
			var memPageNum = 1;
			var endOfProspectContactRecordsList = false;
			var currentlyLoadingProspectContactRecords = false;

			function loadProspectContactRecords() {
				if (!currentlyLoadingProspectContactRecords && !endOfProspectContactRecordsList) {
					currentlyLoadingProspectContactRecords = true;
					$('##prospectContactRecordsLoadingIndicator').show();
					getProspectContactRecords(memPageNum);
				}
			}
			function getProspectContactRecords(n) {
				var objParams = { taskID:#attributes.data.taskID#, memberID:#attributes.data.qryTaskDetails.prospectMemberID#, 
									memPageNum:n, perPageResults:10, displayMode:'frontend' };
				$.getJSON('/?event=proxy.ts_json&c=ADMINTASK&m=getProspectContactRecords', objParams)
					.done(showProspectContactRecords)
					.fail(showProspectContactRecords);
			}
			function showProspectContactRecords(objResult) {
				$('##prospectContactRecordsLoadingIndicator').hide();
				var source = $('##mc_taskProspectContactRecord_template').html();
				var template = Handlebars.compile(source);
				$('##divMCTaskProspectContactRecords').append(template(objResult));

				if (objResult.success) {
					currentlyLoadingProspectContactRecords = false;
					memPageNum++;

					if (objResult.strpage.totalcount == 0 || objResult.strpage.nextcountstart > objResult.strpage.totalcount) {
						endOfProspectContactRecordsList = true;
					}
				}
			}
		</cfif>

		function MCTasksMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
					case "tspaymentformloadevent":
					case "mcgatewayevent":
					case "tsgatewayevent":
				}

			} else {
				return false;
			}
		}

		$(function() {
			mca_setupDatePickerField('reminderDate');
			mca_setupDatePickerField('dateDue');
			<cfif listFindNoCase("Done,Confirmed", attributes.data.qryTaskDetails.statusName) OR (attributes.data.qryTaskDetails.statusName EQ "Staff Review" AND attributes.data.hasTaskResultData)>
				getMarkAsCompleteFormDetails();
			</cfif>

			getProspectAdditionalInfo();
			loadTaskNotes();
			initTaskProspectsSection(#attributes.data.taskID#);

			<cfif attributes.data.showProspectCCForm>
				initProspectCCForm();
			</cfif>

			<cfif val(attributes.data.qryProjectWorkspaceSettings.prospectContactFSID) gt 0>
				loadProspectContactRecords();
			
				$('##endOfProspectContactRecordsList').bind('inview', function(event, isInView, visiblePartX, visiblePartY) {
					if (isInView) {
					  /*element is now visible in the viewport*/
					  loadProspectContactRecords();
					} else {
					  /*element has gone out of viewport*/
					}
				});
			</cfif>

			if (window.addEventListener) {
				window.addEventListener("message", MCTasksMessageHandler, false);
			} else if (window.attachEvent) {
				window.attachEvent("message", MCTasksMessageHandler);
			}
		});
	</script>

	<cfif val(attributes.data.qryProjectWorkspaceSettings.prospectContactFSID) gt 0>
		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/inview/jquery.inview.min.js"></script>
	</cfif>

	<style type="text/css">
		.divSolicitorInstr { max-height:80px; overflow:hidden; }
		div##divMCTaskTags { padding-left:15px; }
		div.mc_tasktags { padding-left:25px; padding-top:5px; }
		div.mc_task_hide { display:none !important; }
		div.div_tasktag_cf { margin-left:15px !important; width:98% !important; }
		input.MCAdminDateControl { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
		div.div_tasktag_cf, div##divStep3FormContainer, div##divMCProspectCCFormContainer { border: 2px solid ##d9dcdd; padding:8px; margin-top: 10px; margin-bottom: 15px; border-radius: 4px;}
		div.div_tasktag_cf .MCTaskFieldButtonBar {float:left;}
		div.div_tasktag_cf .MCTaskFieldButtonBar > button {margin-right:10px;}
		div.div_tasktag_cf:after {content: "";display: table; clear: both;}
		div.div_tasktag_cf textarea {width: 75%;}
		div.MCTaskFieldsWrapper { padding:15px; }
		div##divProspectCCContainer { margin:0 0 10px 15px; }
		div##divMCProspectCCInputForm { margin:10px 0; }
		div##divMCTaskProspectPaymentNote { margin-top:10px; }
		button.step3ActionBtn { margin-bottom:5px; }
		.MCShowTaskFieldLink {display:none;}
		.MCHideTaskFieldLink {}
		.MCTaskFieldInstanceCollapsed .MCHideTaskFieldLink {display:none;}
		.MCTaskFieldInstanceCollapsed .MCShowTaskFieldLink {display:inline;}
		.MCTaskFieldInstanceCollapsed .MCTaskFieldsWrapper {display:none;}
		.MCTaskFieldInstanceCollapsed .MCTaskFieldsWrapper {display:none;}
		.MCTaskFieldTitle {border-bottom:none; margin-top:0;}
		.MCTaskFieldTitleWrapper{margin-bottom:10px;}
		div.historyEntryWrapper { margin-bottom:10px; }
		div.historyEntryWrapper div.historyEntryCategory, 
		div.historyEntryWrapper div.historyEntryDate,
		div.historyEntryWrapper div.historyEntryLinkMember, 
		div.historyEntryWrapper div.historyEntryRecorder { margin-left:25px; }
		<cfif attributes.data.qryTaskDetails.statusName eq 'Confirmed'>
			div##divMCProspectCCFormContainer a.cof_add, .cof_links, div.divMCProspectCOFManage { display:none; }
		</cfif>
		div.ind { margin-left:10px; }
		div.ind div { margin-bottom:2px; }
		div.ind div.com { margin-bottom:6px; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfif attributes.data.showProspectCCForm and len(attributes.data.strPaymentForm.headcode)>
	<cfhtmlhead text="#application.objCommon.minText(attributes.data.strPaymentForm.headcode)#">

	<cfsavecontent variable="local.taskCCJS">
		<cfoutput>
		<script type="text/javascript">
			function removeTaskCCMethod(pid) {
				$('input[name="p_'+pid+'_mppid"][value="0"]').prop('checked',true);
				if (typeof window['p_'+pid+'_selectCard'] == 'function') window['p_'+pid+'_selectCard'](0);
			}
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.taskCCJS)#">
</cfif>

<cfoutput>
<div id="MCWorkspaceContainer" class="container-fluid" style="padding:0;">
	<div class="row-fluid display-flex">
		<cfif attributes.data.qryInCompleteTasks.recordCount>
			<div class="span3 hidden-phone sideBarContainer">
				<cfif attributes.data.availableProspectsCount gt 0>
					<div class="divChooseProspects">
						<button class="btn btn-primary btnChooseProspects btn-block" onclick="showChooseTasks(#attributes.data.qryProjectDetails.projectID#)">Choose #attributes.data.labels.taskFieldLabelPlural#</button>
					</div>
				</cfif>

				<!--- common sidebar --->
				<cfset local.strProjectData = {
					orgMemberID = attributes.data.orgMemberID,
					projectID = attributes.data.projectID,
					selectedTaskID = attributes.data.taskID,
					qryAssignedTasks = attributes.data.qryInCompleteTasks,
					qryFeaturedProjectTasksFieldData = attributes.data.qryFeaturedProjectTasksFieldData,
					qryFeaturedWorkspaceTasksFieldData = attributes.data.qryFeaturedWorkspaceTasksFieldData,
					taskFieldLabelPlural = attributes.data.labels.taskFieldLabelPlural
				}>
				<cfinclude template="dsp_projectAssignedTasksSidebar.cfm">
			</div>
		</cfif>

		<div id="tskProspectDetails" class="<cfif attributes.data.qryInCompleteTasks.recordCount>span9<cfelse>span12</cfif>">
			<div class="row-fluid">
				<div class="page-header span12">
					<ul class="breadcrumb lead">
						<li><a href="#attributes.data.mainurl#">#attributes.data.workspaceName#</a> <span class="divider">/</span></li>
						<li><a href="#attributes.data.mainurl#&wsAction=showDetail&pid=#attributes.data.projectID#">#attributes.data.qryProjectDetails.projectName#</a> <span class="divider">/</span></li>
						<li class="active">#attributes.data.strMember.mc_combinedNameNoMemberNumber#</li>
					</ul>
					<table style="margin-bottom:10px;">
						<tr>
							<cfif attributes.data.qryProjectDetails.showMemberPhotos is 1>
								<td style="vertical-align:top;">
									<cfif attributes.data.strMember.hasPhoto is 1>
										<img class="thumbnail" style="margin-right:20px;" src="/memberphotosth/#LCASE(attributes.data.strMember.memberPhoto)#">
									<cfelse>
										<img class="thumbnail" style="margin-right:20px;" src="/assets/common/images/directory/default.jpg" width="80" height="100">
									</cfif>
									<div class="label label-info" style="vertical-align:middle;width:80px;text-align:center">#attributes.data.qryTaskDetails.statusName#</div>
								</td>
							</cfif>
							<td style="vertical-align:top;">
								<h1>
									#attributes.data.strMember.mc_combinedNameNoMemberNumber# <cfif attributes.data.qryProjectDetails.showMemberPhotos is 0><span class="label label-info" style="vertical-align:middle;">#attributes.data.qryTaskDetails.statusName#</span></cfif>
									<cfif structKeyExists(attributes.data.strMember,"company") or structKeyExists(attributes.data.strMember,"primaryPhone")>
										<div>
											<small class="tskAlignTop">
												<cfif structKeyExists(attributes.data.strMember,"company") and len(attributes.data.strMember.company)><div>#attributes.data.strMember.company#</div></cfif>
												<cfif structKeyExists(attributes.data.strMember,"primaryPhone") and len(attributes.data.strMember.primaryPhone)><div>#attributes.data.strMember.primaryPhone#</div></cfif>
											</small>
										</div>
									</cfif>
								</h1>
							</td>
						</tr>
					</table>
					<table class="tblTaskAssignedMember table">
						<tr>
							<td width="50%">		
								<div class="ind">
									#attributes.data.strMember.mc_combinedAddresses#
									#attributes.data.strMember.mc_extraInfo#
								</div>
							</td>
							<td align="right" width="35%">
								<cfif len(attributes.data.strMember.mc_memberType)><p>#attributes.data.strMember.mc_memberType#</p></cfif>
								<cfif attributes.data.strMember.mcaccountstatus eq 'I'><p><span class="tsAppBodyTextImportant">ACCOUNT INACTIVE</span></p></cfif>
								<cfif attributes.data.strMember.mcaccountstatus neq 'I'>
									<p>#attributes.data.strMember.mc_memberStatus#</p>
								</cfif>
								<cfif len(attributes.data.strMember.mc_memberclassifications)><p>#attributes.data.strMember.mc_memberclassifications#</p></cfif>
							</td>
						</tr>
					</table>

				</div>
			</div>

			<div class="row-fluid">
				<div class="span12 well">
					<h4>Step 1 - Instructions for #attributes.data.labels.solicitorFieldLabel#</h4>
					<div class="subContentPadding divSolicitorInstr">
						#attributes.data.qryProjectDetails.instructionContent#
					</div>
					<div class="text-center" style="display:none;">
						<button type="button" name="btnSeeMoreSolicitorInstr" id="btnSeeMoreSolicitorInstr" class="btn" onclick="seeMoreSolicitorInstr();">
							Click to See Full Instructions
						</button>
					</div>
				</div>
			</div>

			<div class="row-fluid">
				<div class="span12 well">
					<h4>Step 2 - Objectives for #attributes.data.strMember.mc_combinedNameNoMemberNumber#</h4>
					<cfif len(trim(attributes.data.qryTaskDetails.taskObjective))>
						<div class="subContentPadding">
							<h5>#attributes.data.labels.taskFieldLabel# Objective:</h5>
							<div class="subContentPadding">
								#attributes.data.qryTaskDetails.taskObjective#
							</div>
						</div>
					</cfif>
					<cfif attributes.data.qryProjectTaskFieldData.recordCount>
						<div class="subContentPadding">
							<h5>#attributes.data.labels.taskFieldLabel# Objective Fields</h5>
							<div class="subContentPadding">
								<cfloop query="attributes.data.qryProjectTaskFieldData">
									<cfif len(attributes.data.qryProjectTaskFieldData.answer)>
										<p><strong>#attributes.data.qryProjectTaskFieldData.titleOnInvoice#:</strong> <cfif listFindNoCase("INTEGER,DECIMAL2",attributes.data.qryProjectTaskFieldData.dataTypeCode)>#NumberFormat(attributes.data.qryProjectTaskFieldData.answer,',')#<cfelse>#attributes.data.qryProjectTaskFieldData.answer#</cfif></p>
									</cfif>
								</cfloop>
							</div>
						</div>
					</cfif>
				</div>
			</div>
			<div id="divStep3Actions" class="row-fluid">
				<div class="span12 well">
					<h4>Step 3 - Take Action with #attributes.data.strMember.mc_combinedNameNoMemberNumber#</h4>
					<div class="subContentPadding" style="margin-top:15px;">
						<button type="button" name="btnAddProgressNote" id="btnAddProgressNote" class="btn step3ActionBtn <cfif not attributes.data.appRightsStruct.commentOwnTasks>disabled</cfif>" <cfif attributes.data.appRightsStruct.commentOwnTasks>onclick="addTaskNote();"</cfif>><i class="icon-pencil"></i> Add Progress Note</button> 
						<cfif attributes.data.qryTaskDetails.statusName neq 'Staff Review'>
							<button type="button" name="btnFlagForStaffReview" id="btnFlagForStaffReview" class="btn step3ActionBtn" onclick="flagTaskForStaffReview();"><i class="icon-search"></i> Flag for Staff Review</button>
						</cfif>
						<button type="button" name="btnSendEmailToProspect" id="btnSendEmailToProspect" class="btn step3ActionBtn" onclick="emailTaskProspect();"><i class="icon-envelope"></i> Send Email to #attributes.data.labels.prospectFieldLabel#</button>
						<cfif attributes.data.qryTaskDetails.statusName eq 'Open' and attributes.data.appRightsStruct.unassignOwnTasks eq 1>
							<button type="button" name="btnUnassignProspect" id="btnUnassignProspect" class="btn btn-danger step3ActionBtn" onclick="removeTaskSolicitor();"><i class="icon-user"></i> Unassign</button>
						</cfif>
					</div>
					<div id="divStep3FormContainer" class="subContentPadding" style="display:none;"></div>
				</div>
			</div>

			<div class="row-fluid">
				<div class="span12 well">
					<h4>Step 4 - Record Results for #attributes.data.strMember.mc_combinedNameNoMemberNumber#</h4>
					
					<cfif NOT listFindNoCase("Done,Confirmed", attributes.data.qryTaskDetails.statusName)>
						<button type="button" name="btnMarkAsComplete" id="btnMarkAsComplete" class="btn subContentPadding" onclick="getMarkAsCompleteFormDetails();"><i class="icon-check text-success"></i> Complete and Record Your Results</button> 
					</cfif>

					<cfif listFindNoCase("Done,Confirmed", attributes.data.qryTaskDetails.statusName) OR (attributes.data.qryTaskDetails.statusName EQ "Staff Review" AND attributes.data.hasTaskResultData)>
						<cfset local.divMCTaskResultOptionsDisplay = "">
					<cfelse>
						<cfset local.divMCTaskResultOptionsDisplay = "display:none;">
					</cfif>
					<div id="divMCTaskResultOptions" class="subContentPadding" style="#local.divMCTaskResultOptionsDisplay#">
						<!--- error placeholder --->
						<a name="taskFrmTop"></a>
						<div id="task_frm_err" class="alert" style="display:none;margin:12px 0;"></div>

						<form name="frmProspectMarkAsComplete" id="frmProspectMarkAsComplete" class="form-horizontal" method="post" action="#attributes.data.formLinkToConfirmAsComplete#" onsubmit="return validateTaskResultOptionsForm();">
							<cfif attributes.data.qryTaskDetails.statusName eq 'Confirmed'>
								<h5>Here's a copy of the submitted completion information</h5>
							<cfelse>
								<h5><cfif attributes.data.qryTaskDetails.statusName eq 'Done' OR (attributes.data.qryTaskDetails.statusName EQ "Staff Review" AND attributes.data.hasTaskResultData)>You can update<cfelse>Please complete</cfif> the information below and confirm completion. Depending on your selections, there may be followup questions.</h5>
							</cfif>
							<div id="divMCTaskTags"></div>
							<div id="divMCTaskTagFields" style="margin-left:0;"></div>

							<cfif attributes.data.showProspectCCForm>
								<div id="divMCProspectCC">
									<input type="hidden" name="oldPayProfileID" id="oldPayProfileID" value="#val(attributes.data.qryTaskDetails.payProfileID)#">

									<h5>Payment Instructions</h5>
									<cfif val(attributes.data.qryTaskDetails.payProfileID) eq 0>
										<div id="addNewCCContainer">
											<div class="alert alert-info" style="margin-bottom:10px!important;">No credit cards found.</div>
											<label class="checkbox" style="margin:0 0 15px 15px;">
												<input type="checkbox" name="addNewCC" value="1" onclick="toggleProspectCCContainer(this.checked);">&nbsp;Add new card
											</label>
										</div>
									</cfif>

									<div id="divProspectCCContainer"<cfif val(attributes.data.qryTaskDetails.payProfileID) eq 0> style="display:none;"</cfif>>
										<div>#attributes.data.qryProjectWorkspaceSettings.paymentInstructions#</div>
										<cfif len(attributes.data.strPaymentForm.inputForm)>
											<div class="divMCProspectCOFManage text-error">
												Payment Method will not be saved until #attributes.data.labels.taskFieldLabel# is saved
											</div>
											<div id="pf_#attributes.data.profileID#_">
												<div id="divMCProspectCCFormContainer">
													<div id="divMCProspectCCInputForm">
														#replaceNoCase(attributes.data.strPaymentForm.inputForm,'fld_','p_#attributes.data.profileID#_fld_','ALL')#
													</div>
													<cfif val(attributes.data.qryTaskDetails.payProfileID) gt 0 and attributes.data.qryTaskDetails.statusName neq 'Confirmed'>
														<div>
															<label class="radio">
																<input type="radio" name="p_#attributes.data.profileID#_mppid" value="0" onchange="removeTaskCCMethod(#attributes.data.profileID#);">
																Remove Pay Method Associated with this #attributes.data.labels.prospectFieldLabel#
															</label>
														</div>
													<cfelseif val(attributes.data.qryTaskDetails.payProfileID) eq 0>
														<input type="radio" name="p_#attributes.data.profileID#_mppid" value="0" style="display:none;">
														<script type="text/javascript">
															$(function() {
																removeTaskCCMethod(#attributes.data.profileID#);
															});
														</script>
													</cfif>
												</div>
											</div>
										</cfif>
										<div id="divMCTaskProspectPaymentNote" class="divMCProspectCOFManage">
											Additional Note to Staff regarding Payment<br/>
											<textarea name="additionalPmtNote" id="additionalPmtNote" cols="80" rows="5" style="width:400px;"></textarea>
										</div>
									</div>
								</div>
							</cfif>

							<cfif attributes.data.qryTaskDetails.statusName neq 'Confirmed'>
								<div class="text-center">
									<button type="submit" name="btnConfirmAsComplete" id="btnConfirmAsComplete" class="btn">
										<i class="icon-check text-success"></i> <cfif attributes.data.qryTaskDetails.statusName eq 'Done'>Update Information<cfelse>Confirm as Complete</cfif>
									</button>
									<cfif attributes.data.qryTaskDetails.statusName eq 'Open' OR (attributes.data.qryTaskDetails.statusName EQ "Staff Review" AND NOT attributes.data.hasTaskResultData)>
										<button type="button" name="btnCancelChanges" id="btnCancelChanges" class="btn" onclick="cancelTaskResultOptions();">Cancel</button>
									</cfif>
								</div>
							</cfif>
							<br/>
						</form>
					</div>
				</div>
			</div>
			<!--- notes --->
			<div class="row-fluid">
				<div class="span12 well">
					<h4>Notes on #attributes.data.strMember.mc_combinedNameNoMemberNumber#:</h4>
					<div id="divMCTaskNotes" class="subContentPadding"></div>
					<div id="divMCTaskNotesLoading" class="subContentPadding c"><i class="icon-spin icon-spinner"></i><br/><b>Please Wait...</b></div>
				</div>
			</div>	
			<!--- Due Date and Reminders commented out until we have a better plan on how we'll end this on front-end
			<div class="row-fluid">
				<div class="span12 well">
					<h4>#attributes.data.labels.taskFieldLabel# Date Settings:</h4>
					<form name="frmSaveTaskDates" id="frmSaveTaskDates" class="form-horizontal">
						<div class="control-group">
							<label class="control-label" for="reminderDate">Reminder Date:</label>
							<div class="controls">
								<input type="text" name="reminderDate" id="reminderDate" class="projectTaskdate" value="#DateFormat(attributes.data.qryTaskDetails.nextReminderDate,'m/d/yyyy')#" onchange="saveTaskReminderAndDueDates();">
							</div>
						</div>
						<div class="control-group">
							<label class="control-label" for="dateDue">Due Date:</label>
							<div class="controls">
								<input type="text" name="dateDue" id="dateDue" class="projectTaskdate" value="#DateFormat(attributes.data.qryTaskDetails.dateDue,'m/d/yyyy')#" onchange="saveTaskReminderAndDueDates();">
							</div>
						</div>
					</form>
				</div>
			</div>
			--->

			<div id="divMCTaskProspectAdditionalInfo"><div class="c"><i class="icon-spin icon-spinner"></i><b>Please Wait...</b></div></div>

			<cfif val(attributes.data.qryProjectWorkspaceSettings.prospectContactFSID) gt 0>
				<div class="row-fluid">
					<div class="span12 well">
						<h4>Records Linked to this Firm</h4>
						<div id="divMCTaskProspectContactRecords"></div>
						<div id="endOfProspectContactRecordsList">
							<div id="prospectContactRecordsLoadingIndicator" class="progress progress-striped active">
							  <div class="bar"style="width: 100%;"><strong>Loading Records</strong></div>
							</div>
						</div>
					</div>
				</div>
			</cfif>
		</div>
	</div>
</div>

<script id="mc_taskFieldTempate" type="text/x-handlebars-template">
	<div id="div_tasktag_{{categoryID}}_cf" class="div_tasktag_cf mc_task_hide container-fluid">
		<div class="MCTaskFieldTitleWrapper">
			<b id="MCTaskFieldTitle{{categoryID}}" class="MCTaskFieldTitle" data-linkedInstance="div_tasktag_{{categoryID}}_cf">Follow up questions: {{instanceTitle}}</b>
		</div>

		<div class="MCTaskFieldsWrapper">
			{{##each arrResourceFields}}
				{{##compare fieldGroupingID '>' 0}}
					<fieldset style="padding:10px;border:1px solid ##ccc;margin:10px 0;">
					<legend style="width:auto;font-size:1.1875em;font-weight:400;margin:0;padding:0 8px;border:0;line-height:20px;">{{fieldGrouping}}</legend>
					{{##compare fieldGroupingDesc.length '>' 0}}
						<div style="margin-bottom:15px;">{{fieldGroupingDesc}}</div>
					{{/compare}}
				{{/compare}}

				{{##each arrFields}}
					{{##unless allOptionEmptyOrDisabled}}
						{{##compare itemID '>' 0}}
							<input type="hidden" name="old_cf_{{fieldID}}" value="{{value}}">
						{{/compare}}

						<div class="row-fluid" style="padding:4px 0;">
							<div style="padding-left:25px;">
								{{##compare displayTypeCode '==' "LABEL"}}
									{{{attributes.fieldText}}}
								{{/compare}}

								{{##compare displayTypeCode '!=' "LABEL"}}
									{{##if isRequired}}* {{/if}}
									{{attributes.fieldText}}
								{{/compare}}

								<!--- textbox --->
								{{##compare displayTypeCode '==' "TEXTBOX"}}

									<div style="padding:6px 0 6px 20px;">

										{{##if supportQty}}
											
											{{##unless maxQtyAllowed}}
												<input type="hidden" name="cf_{{fieldID}}" value="{{value}}">
											{{/unless}}

											Quantity: <input type="text" size="6" autocomplete="off" maxlength="4" id="{{##unless maxQtyAllowed}}disabled_{{/unless}}cf_{{fieldID}}" name="{{##unless maxQtyAllowed}}disabled_{{/unless}}cf_{{fieldID}}" data-MCTaskFieldDisplayTypeCode="{{displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{dataTypeCode}}" data-MCTaskFieldOfferQTY="{{supportQty}}" data-MCTaskFieldIsRequired="{{attributes.isRequired}}" data-MCTaskFieldMaxQtyAllowed="{{maxQtyAllowed}}" data-MCTaskFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCTaskFieldDesc="{{attributes.fieldText}}" value="{{value}}" {{##unless maxQtyAllowed}}disabled{{/unless}} class="MCTaskField">
											{{##unless maxQtyAllowed}}
												&nbsp; <span class="tsAppBodyTextImportant">[SOLD OUT]</span>
											{{/unless}}

										{{else}}
											<input type="text" size="60" autocomplete="off" id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCTaskFieldDisplayTypeCode="{{displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{dataTypeCode}}" data-MCTaskFieldIsRequired="{{attributes.isRequired}}" data-MCTaskFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCTaskFieldDesc="{{attributes.fieldText}}" value="{{value}}" style="width:75%;" class="MCTaskField">
										{{/if}}

									</div>
								{{/compare}}

								<!--- Drop-Down List --->
								{{##compare displayTypeCode '==' "SELECT"}}
									<div style="padding:6px 0 6px 20px;">
										<select id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCTaskFieldDisplayTypeCode="{{displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{dataTypeCode}}" data-MCTaskFieldIsRequired="{{attributes.isRequired}}" data-MCTaskFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCTaskFieldDesc="{{attributes.fieldText}}" class="MCTaskField">
											<option value=""></option>
											{{##each children}}

												<option value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}selected{{/compare}} {{##if unavailable}}disabled{{/if}}>
												{{attributes.fieldValue}}
												{{##if unavailable}}
													&nbsp; [SOLD OUT]
												{{/if}}
												</option>

											{{/each}}
										</select>
									</div>
								{{/compare}}

								<!--- Radio Controls --->
								{{##compare displayTypeCode '==' "RADIO"}}
									<div style="padding:6px 0 6px 20px;">
										{{##each children}}
											<label class="radio">
												<input type="radio" id="cf_{{../fieldID}}" name="cf_{{../fieldID}}" value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}checked{{/compare}} {{##if unavailable}}disabled{{/if}} data-MCTaskFieldDisplayTypeCode="{{../displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{../dataTypeCode}}" data-MCTaskFieldIsRequired="{{../attributes.isRequired}}" data-MCTaskFieldRequiredMsg="{{../attributes.requiredMsg}}" data-MCTaskFieldDesc="{{../attributes.fieldText}}" class="MCTaskField"> {{attributes.fieldValue}}
												{{##if unavailable}}
													&nbsp; [SOLD OUT]
												{{/if}}
											</label>
										{{/each}}
									</div>
								{{/compare}}

								<!--- Checkboxes --->
								{{##compare displayTypeCode '==' "CHECKBOX"}}
									<div style="padding:6px 0 6px 20px;">
										{{##each children}}
											<label class="checkbox">	
												<input type="checkbox" id="cf_{{../fieldID}}" name="cf_{{../fieldID}}" value="{{attributes.valueID}}" {{##if unavailable}}disabled{{/if}} data-MCTaskFieldDisplayTypeCode="{{../displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{../dataTypeCode}}" data-MCTaskFieldIsRequired="{{../attributes.isRequired}}" data-MCTaskFieldRequiredMsg="{{../attributes.requiredMsg}}" data-MCTaskFieldDesc="{{../attributes.fieldText}}" {{##each ../value}}{{##compare this '==' ../attributes.valueID}}checked{{/compare}}{{/each}} class="MCTaskField"> {{attributes.fieldValue}}
												{{##if unavailable}}
													&nbsp; [SOLD OUT]
												{{/if}}
											</label>
										{{/each}}
									</div>
								{{/compare}}

								<!--- Date --->
								{{##compare displayTypeCode '==' "DATE"}}
									<div style="padding:6px 0 6px 20px;">
										<input type="text" size="16" autocomplete="off" id="cf_{{fieldID}}" name="cf_{{fieldID}}" data-MCTaskFieldDisplayTypeCode="{{displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{dataTypeCode}}" data-MCTaskFieldIsRequired="{{attributes.isRequired}}" data-MCTaskFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCTaskFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCTaskField MCAdminDateControl"> <a href="##" class="MCAdminDateControlClearLink" data-linkedDateControl="cf_{{fieldID}}">clear</a>
									</div>
								{{/compare}}

								<!--- Textarea --->
								{{##compare displayTypeCode '==' "TEXTAREA"}}
									<div style="padding:6px 0 6px 20px;">
										<cfoutput><textarea cols="62" rows="5" id="cf_{{fieldID}}" name="cf_{{fieldID}}" class="MCTaskField" data-MCTaskFieldDisplayTypeCode="{{displayTypeCode}}" data-MCTaskFieldDataTypeCode="{{dataTypeCode}}" data-MCTaskFieldIsRequired="{{attributes.isRequired}}" data-MCTaskFieldRequiredMsg="{{attributes.requiredMsg}}" data-MCTaskFieldDesc="{{attributes.fieldText}}">{{value}}</textarea></cfoutput>
									</div>
								{{/compare}}

							</div>
						</div>
					{{/unless}}
				{{/each}}

				{{##compare fieldGroupingID '>' 0}}
					</fieldset>
				{{/compare}}
			{{/each}}
		</div>
	</div>
</script>

<script id="mc_taskProspectDetailsTempate" type="text/x-handlebars-template">
	{{##compare arrGivingHistory.length '>' 0}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>Giving History:</h4>
				<div class="divProspectDetails divGivingHistory">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th></th>
							<th nowrap colspan="2" class="r">
								{{{moment arrGivingHistory.0.startR format='M/D/YYYY'}}} to {{{moment arrGivingHistory.0.endR format='M/D/YYYY'}}}
							</th>
							<th nowrap colspan="2" class="r">
								{{{moment arrGivingHistory.0.startR1 format='M/D/YYYY'}}} to {{{moment arrGivingHistory.0.endR1 format='M/D/YYYY'}}}
							</th>
							<th nowrap colspan="2" class="r">
								{{{moment arrGivingHistory.0.startR2 format='M/D/YYYY'}}} to {{{moment arrGivingHistory.0.endR2 format='M/D/YYYY'}}}
							</th>
						</tr>
						<tr>
							<th>GL Account</th>
							<th>Pledged/Billed</th>
							<th>Paid</th>
							<th>Pledged/Billed</th>
							<th>Paid</th>
							<th>Pledged/Billed</th>
							<th>Paid</th>
						</tr>
						{{##each arrGivingHistory}}
							<tr>
								<td>
									{{##compare GLAccountID '!=' ********}}
										{{accountName}}{{##compare accountCode.length '>' 0}} ({{accountCode}}){{/compare}}
									{{/compare}}
									{{##compare GLAccountID '==' ********}}
										<b>Totals</b>
									{{/compare}}
								</td>
								<td>${{billedR}}</td>
								<td>${{paidR}}</td>
								<td>${{billedR1}}</td>
								<td>${{paidR1}}</td>
								<td>${{billedR2}}</td>
								<td>${{paidR2}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMoreGivingHistory" id="btnSeeMoreGivingHistory" class="btn" onclick="seeMoreProspectDetails('divGivingHistory');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/compare}}

	{{##compare arrPaymentSummary.length '>' 0}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>Payment Summary:</h4>
				<div class="divProspectDetails divPmtSummary">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th></th>
							<th class="r">
								{{{moment arrPaymentSummary.0.startDate format='M/D/YYYY'}}} to {{{moment arrPaymentSummary.0.endDate format='M/D/YYYY'}}}
							</th>
						</tr>
						<tr>
							<th>Revenue GL Account</th>
							<th>Allocated Amount</th>
						</tr>
						{{##each arrPaymentSummary}}
							<tr>
								<td>
									{{##compare GLAccountID '!=' ********}}
										{{accountName}}{{##compare accountCode.length '>' 0}} ({{accountCode}}){{/compare}}
									{{/compare}}
									{{##compare GLAccountID '==' ********}}
										<b>Totals</b>
									{{/compare}}
								</td>
								<td>${{allocatedAmount}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMorePmtSummary" id="btnSeeMorePmtSummary" class="btn" onclick="seeMoreProspectDetails('divPmtSummary');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/compare}}

	{{##compare arrSubscriptions.length '>' 0}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>Subscriptions:</h4>
				<div class="divProspectDetails divMemSubs">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th>Subscription</th>
							<th>Start</th>
							<th>End</th>
							<th>Bill/Pledged</th>
							<th>Payments</th>
							<th>Balance</th>
							<th>Status</th>
						</tr>
						{{##each arrSubscriptions}}
							<tr>
								<td>
									{{typeName}}<br/>
									{{subscriptionName}} \ {{rateName}}
								</td>
								<td>{{{moment subStartDate format='M/D/YYYY'}}}</td>
								<td>{{{moment subEndDate format='M/D/YYYY'}}}</td>
								<td>${{amtBilled}}</td>
								<td>${{amtPaid}}</td>
								<td>${{amtDue}}</td>
								<td>{{SubscriptionStatus}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMoreMemSubs" id="btnSeeMoreMemSubs" class="btn" onclick="seeMoreProspectDetails('divMemSubs');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/compare}}

	{{##compare arrEvents.length '>' 0}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>Events:</h4>
				<div class="divProspectDetails divProspectEvents">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th>Date</th>
							<th>Event</th>
							<th>Attended</th>
							<th>Credits Awarded</th>
						</tr>
						{{##each arrEvents}}
							<tr>
								<td>{{{moment startTime format='M/D/YYYY hh:mm A'}}}</td>
								<td>{{{eventTitle}}}</td>
								<td>{{##switch attended}}{{##case 1}}Yes{{/case}}{{##case 0}}No{{/case}}{{/switch}}</td>
								<td>{{creditsAwarded}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMoreEvents" id="btnSeeMoreEvents" class="btn" onclick="seeMoreProspectDetails('divProspectEvents');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/compare}}

	{{##compare arrMemberHistory.length '>' 0}}
	{{##each arrMemberHistory}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>{{{sectionTitle}}}:</h4>
				<div class="divProspectDetails divProspectMemHistory">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th>Category</th>
							<th>Date</th>
							<th>Member</th>
							<th>Quantity</th>
							<th>Amount</th>
							<th>Note</th>
						</tr>
						{{##each arrItems}}
							<tr valign="top">
								<td>{{historyCategory}}</td>
								<td>
									{{##compare userDate.length '>' 0}}
										{{{moment userDate format='M/D/YYYY'}}}
									{{/compare}}
								</td>
								<td>
									{{memberName}}<br/>
									{{##compare memberCompany.length '>' 0}}
										<div class="com">{{memberCompany}}</div>
									{{/compare}}
									{{##compare linkedMember.length '>' 0}}
										<div class="ind">
											<i class="icon-link"></i>
											{{linkedMember}}
											{{##compare linkedMemberCompany.length '>' 0}}
												<div class="com">{{linkedMemberCompany}}</div>
											{{/compare}}
										</div>
									{{/compare}}
								</td>
								{{##compare includeQtyAmt '==' 1}}
									<td>{{quantity}}</td>
									<td>${{dollarAmt}}</td>
								{{/compare}}
								{{##compare includeQtyAmt '!=' 1}}
									<td></td>
									<td></td>
								{{/compare}}
								<td>{{{historyContent}}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMoreMemHistory" id="btnSeeMoreMemHistory" class="btn" onclick="seeMoreProspectDetails('divProspectMemHistory');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/each}}
	{{/compare}}

	{{##compare arrRelationships.length '>' 0}}
	{{##each arrRelationships}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>{{{sectionTitle}}}:</h4>
				<div class="divProspectDetails divProspectRelationships">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th>Date</th>
							<th>Member</th>
							<th>Category</th>
							<th>Note</th>
						</tr>
						{{##each arrItems}}
							<tr valign="top">
								<td>
									{{##compare userDate.length '>' 0}}
										{{{moment userDate format='M/D/YYYY'}}}
									{{/compare}}
								</td>
								<td>
									{{memberName}}<br/>
									{{##compare memberCompany.length '>' 0}}
										<div class="com">{{memberCompany}}</div>
									{{/compare}}
									{{##compare linkedMember.length '>' 0}}
										<div class="ind">
											<i class="icon-link"></i>
											{{linkedMember}}
											{{##compare linkedMemberCompany.length '>' 0}}
												<div class="com">{{linkedMemberCompany}}</div>
											{{/compare}}
										</div>
									{{/compare}}
								</td>
								<td>{{{historyCategory}}}</td>
								<td>{{{historyContent}}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMoreRelationships" id="btnSeeMoreRelationships" class="btn" onclick="seeMoreProspectDetails('divProspectRelationships');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/each}}
	{{/compare}}

	{{##compare arrNotes.length '>' 0}}
	{{##each arrNotes}}
		<div class="row-fluid">
			<div class="span12 well">
				<h4>{{{sectionTitle}}}:</h4>
				<div class="divProspectDetails divProspectNotes">
					<table class="table table-bordered tblProspectDetails">
						<tr>
							<th>Date</th>
							<th>Member</th>
							<th>Category</th>
							<th>Note</th>
						</tr>
						{{##each arrItems}}
							<tr>
								<td>
									{{##compare userDate.length '>' 0}}
										{{{moment userDate format='M/D/YYYY'}}}
									{{/compare}}
								</td>
								<td>
									{{memberName}}<br/>
									{{##compare memberCompany.length '>' 0}}
										<div class="com">{{memberCompany}}</div>
									{{/compare}}
									{{##compare linkedMember.length '>' 0}}
										<div class="ind">
											<i class="icon-link"></i>
											{{linkedMember}}
											{{##compare linkedMemberCompany.length '>' 0}}
												<div class="com">{{linkedMemberCompany}}</div>
											{{/compare}}
										</div>
									{{/compare}}
								</td>
								<td>{{{historyCategory}}}</td>
								<td>{{{historyContent}}}</td>
							</tr>
						{{/each}}
					</table>
				</div>
				<div class="text-center" style="display:none;">
					<button type="button" name="btnSeeMoreNotes" id="btnSeeMoreNotes" class="btn" onclick="seeMoreProspectDetails('divProspectNotes');">
						Click to See Full Details
					</button>
				</div>
			</div>
		</div>
	{{/each}}
	{{/compare}}
</script>

<script id="mc_taskNotesTempate" type="text/x-handlebars-template">
	{{##if hasnotes}}
		{{##each arrnotes}}
			<div class="historyEntryWrapper">
				{{##compare description '!=' ''}}<div class="historyEntryDescription">{{{description}}}</div>{{/compare}}
				<div class="historyEntryCategory">{{historycategory}}</div>
				{{##compare userdate '!=' ''}}<div class="historyEntryDate">{{userdate}}</div>{{/compare}}
				{{##compare linkmemberid '>' 0}}
					<div class="historyEntryLinkMember">
						{{linkmembername}}
					</div>
				{{/compare}}
				<div class="historyEntryRecorder">
					Recorded {{entereddate}} by {{enteredbyname}}
				</div>
			</div>
		{{/each}}
	{{else}}
		There are no #attributes.data.labels.taskFieldLabel# notes to display.
	{{/if}}
</script>

<cfif val(attributes.data.qryProjectWorkspaceSettings.prospectContactFSID) gt 0>
	<script id="mc_taskProspectContactRecord_template" type="text/x-handlebars-template">
		{{##if success}}
			{{##compare strpage.totalcount '>' 0}}
				{{##each arrmembers}}
					<div class="row-fluid">
						<div class="span12 well">
							<div class="row-fluid">
								{{##if ../strpage.showmemberphoto}}
									<div class="span2">
										{{##if hasPhoto}}
											<img style="max-width:80px;" src="/memberphotosth/{{memberphoto}}">
										{{else}}
											<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
										{{/if}}
									</div>
								{{/if}}
								<div class="span5">
									<b>{{mc_combinedName}}</b>
									<div class="ind container">
										<div class="com">{{company}}&nbsp;</div>
										{{{mc_combinedAddresses}}}
										{{{mc_extraInfo}}}
									</div>
								</div>
								<div class="{{##if ../strpage.showmemberphoto}}span5{{else}}span7{{/if}}">
									{{##compare mc_recordType.length '>' 0}}{{mc_recordType}}<br/>{{/compare}}
									{{##compare mc_memberType.length '>' 0}}{{mc_memberType}}<br/>{{/compare}}
									{{##compare mcaccountstatus '==' 'I'}}<span class="tsAppBodyTextImportant">ACCOUNT INACTIVE</span><br/>{{/compare}}
									{{##compare mcaccountstatus '!=' 'I'}}
										{{##compare mc_memberStatus.length '>' 0}}{{mc_memberStatus}}<br/>{{/compare}}
									{{/compare}}
									{{##compare mc_memberclassifications.length '>' 0}}{{{mc_memberclassifications}}}<br/>{{/compare}}
									<br/>
								</div>
							</div>
						</div>
					</div>
				{{/each}}
			{{/compare}}
			{{##compare strpage.totalcount '==' 0}}
				No Members Found.
			{{/compare}}
		{{else}}
		 	<div class="alert alert-danger">There was a problem displaying the data.</div>
		{{/if}}
	</script>
</cfif>
</cfoutput>