<cfcomponent extends="model.customPage.customPage" output="false">
	
	<cfset variables.objCustomPageUtils = createObject("component","model.customPage.customPageUtils")>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ApplicationTitle", type="STRING", desc="Application Title", value="Legal Staff Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ApplicationtionIntroText", type="CONTENTOBJ", desc="Application introduction text", value="intro text" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberTypeTitle", type="STRING", desc="Membership Rate Title", value="Membership Type" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MembershipAgreement", type="CONTENTOBJ", desc="Required Membership Agreement", value="Specified in Association Mock-up" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ComplianceText", type="CONTENTOBJ", desc="MATA Dues Compliance text", value="Specified in Association Mock-up" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for Credit Card", value="AuthorizeCIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayBD", type="STRING", desc="pay profile code for Bank Draft", value="MATABD" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCheck", type="STRING", desc="pay profile code for Bank Draft", value="OCC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page", value="Thank you for submitting your application. This Page has been emailed to the email address on file. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="EmailConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation email", value="Thank you for submitting your application. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="Missouri Association of Trial Attorneys - Paralegal Membership Application Form received" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="Missouri Association of Trial Attorneys - Paralegal Membership Application Form - From: FirstName LastName" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberActive", type="CONTENTOBJ", desc="Display message for active members", value="MATA records indicate that you are currently a WAJ member. Please click here to login.Click here <a href='/?pg=login'></a>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberBilled", type="CONTENTOBJ", desc="Display message for billed members", value="You need to renew your MATA membership. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
			
		variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='ParalegalApp',
			formNameDisplay='Paralegal Application',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='ParalegalApplicationHistory', subName='Started');
		variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID,parentCode='ParalegalApplicationHistory', subName='Completed');
		variables.historyStartedText = "Started Paralegal Application";
		variables.historyCompletedText = "Completed Paralegal Application";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;
				}
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##signatureSection input[type="text"], input[type="number"] {
					-moz-box-shadow: none;
					float: none;
					width:auto;
					height: 36px;
					border-width: 0 0 1px;
				}
				##fieldsetContent1 select,input[type="text"]{width:220px!important;}
				i.icon-spin.icon-spinner {margin-top:20px!important;font-size: 2em!important;}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
				}

				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							mc_loadDataForForm($('###variables.formName#'),'previous');			        	
						}
					} else {
						step = 1;
					}
					prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">
						
			<div class="row-fluid tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
			<div class="row-fluid tsAppSectionContentContainer">
				<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
				</tr>
				</table>
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.annualMembershipDuesUID = "00df1a8a-5061-47a6-9c0b-e598eb8e8411">

			<cfset local.qryActiveSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.annualMembershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.annualMembershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = variables.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.annualMembershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = variables.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID> 
			 </cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Paralegal  - Member Information --->
		<cfset local.strFieldSetContent1 = variables.objCustomPageUtils.renderFieldSet(uid='1969821b-f988-442d-9d54-8618d25d5518', mode="collection",strData =local.strData)>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
							
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			<div class="row-fluid">
				<div style="text-align:center;font-size:24px;">#variables.strPageFields.ApplicationTitle#</div>
			</div>	
			<br />
			<div class="row-fluid">
				<div style="text-align:left;">#variables.strPageFields.ApplicationtionIntroText#</div>
			</div>	
			<br/>				
			<div class="row-fluid">
				<div class="span6 tsAppSectionContentContainer" id="fieldsetContent1">
					#local.strFieldSetContent1.fieldSetContent#
				</div>
			</div>
			<button name="btnContinue" type="submit" style ="float:right;" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->	
		<cfset local.contactTypeValue = "Paralegal">
	
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
		
		<cfset local.strResult = local.objSaveMember.saveData(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			 <cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID,subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText,enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif> 
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = 'c62076d3-42f0-47fc-8eac-eaa1e4a339a9')>
		
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>		
		<cfset local.qryMembershipRateInfo = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="********-64F1-4D06-815A-C2ECD903A995", activeRatesOnly=1, ignoreRenewalRates=true)>	
		<cfset local.membership = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="AD5E3CB3-BDDA-4B34-8951-9754E2FD9769", activeRatesOnly=1, ignoreRenewalRates=true)>
	
		<cfset local.result = variables.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>
		<cfsavecontent variable="local.resultHtmlHeadText">
			<cfoutput>
				<script type="text/javascript">
					function validateMembershipInfoForm(){
						var arrReq = new Array();
						if (!$('###variables.formName# ##mccf_RFID').is(':checked')) arrReq[arrReq.length] = "Make a paralegal type selection.";
						if ($.trim($('##signatureField').val()).length == 0) arrReq[arrReq.length] = "Signature needed to be uploaded";

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
						#local.result.jsValidation#

						mc_continueForm($('###variables.formName#'));
						return false;
					}
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfhtmlhead text="#local.resultHtmlHeadText#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid well subAddonWrapper">
						<legend>#variables.strpagefields.MemberTypeTitle#</legend>
					<table cellpadding="3" border="0" cellspacing="0">
						<tbody id="joinFormMembershipRates">
							<cfloop query="local.qryMembershipRateInfo">
								<cfif local.qryMembershipRateInfo.allowfrontend eq 1>
									<tr valign="top" class="rate#LCase(local.qryMembershipRateInfo.rateUID)#">
										<td class="tsAppBodyText" width="30">
											<cfinput type="radio" name="mccf_RFID" id="mccf_RFID" value="#local.qryMembershipRateInfo.frequencyid#" checked="#structKeyExists(local.strData, 'mccf_RFID') and local.strData.mccf_RFID eq local.qryMembershipRateInfo.frequencyid#">
										</td>
										<td class="tsAppBodyText">
											#local.qryMembershipRateInfo.rateName# 
										</td>
										<td class="tsAppBodyText">
											#dollarFormat(local.qryMembershipRateInfo.rateAmt)#
										</td>
									</tr>
									</cfif>
								</cfloop>
								<cfloop query="local.membership">
									<cfif local.membership.allowfrontend eq 1>
									<tr valign="top" class="rate#LCase(local.membership.rateUID)#">
										<td class="tsAppBodyText" width="30">
											<cfinput type="radio" name="mccf_RFID" id="mccf_RFID" value="#local.membership.frequencyid#" checked="#structKeyExists(local.strData, 'mccf_RFID') and local.strData.mccf_RFID eq local.membership.frequencyid#">
										</td>
										<td class="tsAppBodyText">
											#local.membership.rateName# 
										</td>
										<td class="tsAppBodyText">
											#dollarFormat(local.membership.rateAmt)#
										</td>
									</tr>
									</cfif>
								</cfloop>
							</tbody>
						</table>
					</div>	
					<div class="well">
						#variables.strPageFields.MembershipAgreement#
					</div>
					<div class="row-fluid well subAddonWrapper" id="signatureSection">
						<div class="SummaryContent">Applicant Signature:
							<input name="signatureField" id="signatureField" type="text" size="30" class="BodyText input-medium" value="" />
						</div>
					</div>
					<div class="well subAddonWrapper">
						#variables.strPageFields.ComplianceText#
					</div>
				</div>
			<button name="btnBack" id="btnBack" type="button" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
			<button name="btnContinue" type="submit" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = 'c62076d3-42f0-47fc-8eac-eaa1e4a339a9')>
				
		<cfset local.strResult = variables.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfset local.strResult = variables.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfset local.objSaveMember = variables.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		
		<cfset local.objSaveMember.setCustomField(field="MATA Applicant Signature", value=arguments.rc.signatureField)>
		
		<cfset local.strResult = local.objSaveMember.saveData()>
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = variables.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		 <cfset local.qryMembershipRateInfo = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="********-64F1-4D06-815A-C2ECD903A995", activeRatesOnly=1, ignoreRenewalRates=true)>	
		 
		 <cfset local.membership = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="AD5E3CB3-BDDA-4B34-8951-9754E2FD9769", activeRatesOnly=1, ignoreRenewalRates=true)>
		
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryMembershipRateInfo
			where FREQUENCYID = #arguments.rc.mccf_RFID#
		</cfquery>
		
		<cfquery name="local.qryRatesSelectedExtended" dbtype="query">
			select rateAmt, rateName
			from [local].membership
			where frequencyid = #arguments.rc.mccf_RFID#
		</cfquery>
		
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = 'c62076d3-42f0-47fc-8eac-eaa1e4a339a9')>
				
			<cfset local.paymentRequired = (local.qryRatesSelected.rateAmt gt 0) + (local.qryRatesSelectedExtended.rateAmt gt 0) >
			
			<cfif (local.qryRatesSelected.rateAmt gt 0) OR  (local.qryRatesSelectedExtended.rateAmt GT 0) >
				<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC, variables.strPageFields.ProfileCodePayBD, variables.strPageFields.ProfileCodePayCheck ]>

				<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>
			</cfif>

		<cfif (local.qryRatesSelected.rateAmt gt 0) OR (local.qryRatesSelectedExtended.rateAmt gt 0)>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			
			<cfhtmlhead text="#local.headcode#">
		</cfif>
		
		<cfsavecontent variable="local.returnHTML">
		
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfinput type="hidden" name="signatureField" id="signatureField" value="#arguments.rc.signaturefield#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Confirmation</div>
			<div class="tsAppSectionContentContainer">
				<cfset local.totalAmount = 0>
				<cfif local.qryRatesSelected.rateAmt gt 0>
					<div class="tsAppBodyText">#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
				</cfif>
				<cfif local.qryRatesSelectedExtended.rateAmt gt 0>
					<div class="tsAppBodyText">#local.qryRatesSelectedExtended.rateName# - #dollarFormat(local.qryRatesSelectedExtended.rateAmt)#</div>
				</cfif>				
				<cfset local.totalAmount = val(local.qryRatesSelected.rateAmt) +val(local.qryRatesSelectedExtended.rateAmt) >
				<br/>
				<div class="tsAppBodyText"><b>Total amount : #dollarFormat(local.totalAmount)#</b></div>
			</div>
			<cfsavecontent variable="local.checkAddress">
				<div id="CheckInfo" style="display: block;" class="CPSection">
					<div class="CPSectionTitle">Check Information</div>
					<div class="P">
						Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br><br>
						<strong>Missouri Association of Trial Attorneys</strong><br>
						P.O. Box 1792<br>
						Jefferson City, Missouri 65102
					</div>
				</div>
			</cfsavecontent>
			
			<cfif local.totalAmount gt 0>				
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnBack" type="button" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
				<button name="btnContinue" type="submit" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();" disabled>Continue</button>
			</cfif>
			
			<script>
				$(document).ready(function(){
					setTimeout(function() {
						$('button').attr('disabled',false);
					}, 1200);
				});
					var #toScript(local.checkAddress, "paymentHTMLContent")#;
				$(document).ready(function(){
					setTimeout(function() {
						$('##mccfdiv_OCC').find('##mccfProfile3Form table').parent().html(paymentHTMLContent)
					}, 1200);
				});
			</script>
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
					
		<cfset local.qryMembershipRateInfo = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="********-64F1-4D06-815A-C2ECD903A995", activeRatesOnly=1, ignoreRenewalRates=true)>	
		
		<cfset local.membership = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="AD5E3CB3-BDDA-4B34-8951-9754E2FD9769", activeRatesOnly=1, ignoreRenewalRates=true)>
		
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryMembershipRateInfo
			where FREQUENCYID = #arguments.rc.mccf_RFID#
		</cfquery>
		
		<cfquery name="local.qryRatesSelectedExtended" dbtype="query">
			select rateAmt, rateName
			from [local].membership
			where FREQUENCYID = #arguments.rc.mccf_RFID#
		</cfquery>
		<cfset local.totalAmount = val(local.qryRatesSelected.rateAmt) +val(local.qryRatesSelectedExtended.rateAmt) >
		
		<cfset local.strAccTemp = {
				totalPaymentAmount=local.totalAmount,
				assignedToMemberID=variables.useMID,
				recordedByMemberID=variables.useMID,
				rc=arguments.rc
			}>
		<cfif StructKeyExists(arguments.rc,"MCCF_PAYMETH") AND arguments.rc["MCCF_PAYMETH"] eq 'AuthorizeCIM'>
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode=variables.strPageFields.ProfileCodePayCC
				)>
			<cfset local.strAccTemp.payment = {
					detail="#variables.formNameDisplay#",
					amount=local.strAccTemp.totalPaymentAmount,
					profileID=local.payProfileID,
					profileCode=variables.strPageFields.ProfileCodePayCC
				}>	
		<cfelseif StructKeyExists(arguments.rc,"MCCF_PAYMETH") AND  arguments.rc["MCCF_PAYMETH"] eq 'OCC' >
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode=variables.strPageFields.ProfileCodePayCheck
				)>
			<cfset local.strAccTemp.payment = {
					detail="#variables.formNameDisplay#",
					amount=local.strAccTemp.totalPaymentAmount,
					profileID=local.payProfileID,
					profileCode=variables.strPageFields.ProfileCodePayCheck
				}>
		<cfelse>
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
					siteid=variables.siteID,
					profileCode=variables.strPageFields.ProfileCodePayBD
				)>
			<cfset local.strAccTemp.payment = {
					detail="#variables.formNameDisplay#",
					amount=local.strAccTemp.totalPaymentAmount,
					profileID=local.payProfileID,
					profileCode=variables.strPageFields.ProfileCodePayBD
				}>
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
		</cfif>
		
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Join paralegal - Member Information --->
		<cfset local.strFieldSetContent1 = variables.objCustomPageUtils.renderFieldSet(uid='1969821b-f988-442d-9d54-8618d25d5518', mode="confirmation", strData=arguments.rc)>
	
		<cfset local.subscriptionID = variables.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = 'c62076d3-42f0-47fc-8eac-eaa1e4a339a9')>
		
		<cfset local.strResult = variables.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		 <cfset local.qryMembershipRateInfo = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="********-64F1-4D06-815A-C2ECD903A995", activeRatesOnly=1, ignoreRenewalRates=true)>	
		 
		 <cfset local.membership = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="90FA62ED-BA77-4418-AF91-BC02C9DEE6AB", rateUID="AD5E3CB3-BDDA-4B34-8951-9754E2FD9769", activeRatesOnly=1, ignoreRenewalRates=true)>
		 
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryMembershipRateInfo
			where FREQUENCYID = #arguments.rc.mccf_RFID#
		</cfquery>
		
		<cfquery name="local.qryRatesSelectedExtended" dbtype="query">
			select rateAmt, rateName
			from [local].membership
			where FREQUENCYID = #arguments.rc.mccf_RFID#
		</cfquery>
	
		<cfset local.memberPayProfileDetail = "">
		<cfif local.qryRatesSelected.rateAmt gt 0>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div>Here are the details of your application:</div>
			#local.strFieldSetContent1.fieldSetContent#						
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Type</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif local.qryRatesSelected.rateAmt GT 0>
								<div>#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
							</cfif>
							<cfif local.qryRatesSelectedExtended.rateAmt gt 0>
								<div>#local.qryRatesSelectedExtended.rateName# - #dollarFormat(local.qryRatesSelectedExtended.rateAmt)#</div>
							</cfif>
						</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;" >
							<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;width: 10%;" nowrap>Applicant Signature: &nbsp;</td>		
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" >#arguments.rc.signaturefield#</td>
								</tr>
							</table>		
					</tr>
				</table>
				<cfif (local.qryRatesSelected.rateAmt gt 0 ) OR (local.qryRatesSelectedExtended.rateAmt gt 0 )>
			
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
									<table class="table" cellpadding="3" border="0" cellspacing="0">
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
										</td>
									</tr>
									</table>
								<cfelse>
									None selected.
								</cfif>
							</td>
						</tr>
					</table>
					</cfif>					
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle=arguments.rc.mc_siteinfo.sitename & " - " & variables.formNameDisplay,
			emailhtmlcontent=local.confirmationHTML,
			siteID=arguments.rc.mc_siteInfo.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.emailsubject">
			<cfoutput>
			#arguments.rc.m_firstname# #arguments.rc.m_lastname#
			</cfoutput>
		</cfsavecontent>
		
		<cfset variables.ORGEmail.subject = replaceNoCase(variables.strPageFields.StaffConfirmationSub,"FirstName LastName",local.emailsubject)>
		
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to MATA", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.MemberActive#
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.MemberActive#
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.MemberBilled#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>