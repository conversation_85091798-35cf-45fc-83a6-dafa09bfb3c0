<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#
<cfif local.strResults.itemCount EQ 0>
	#showCommonNotFound(bucketID=arguments.bucketID, searchID=arguments.searchID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<cfelse>
	<cfset local.expertName = UcFirst(replace('#local.strSearch.expertFName# #local.strSearch.expertLName#','"','','all'),true)>
	<cfset local.exceedsLimit = local.strResults.itemCount gt local.strResults.matchesLimit>

	<div id="MCSearchSummaryContainer" class="bk-mb-5">
		<div id="divExpertConnectResults" class="row-fluid bk-mt-4">
			<div class="span6">
				<div>Expert Name: <b>#local.expertName#</b></div>
				<div class="bk-mb-4 bk-mt-2">
					We found <b>#Numberformat(local.strResults.itemCount)#</b> unique plaintiff lawyers with knowledge of #local.expertName# in <b>#local.strResults.numStates#</b> states.
				</div>
				<div class="bk-d-flex bk-flex-column" style="max-width:300px;">
					<button type="button" class="btn btn-success btn-large" onclick="toggleEmailInquiryForm(true);"><i class="icon-envelope bk-align-baseline bk-mr-1"></i> Preview List of Lawyers</button>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						<button type="button" id="btnDownloadExpMatches" class="btn btn-default btn-large bk-mt-1" onclick="downloadExpertMatches();"><i class="icon-download bk-align-baseline bk-mr-1"></i> <span id="downloadMatchBtnLabel">Download Matches</span></button>
					</cfif>
				</div>
			</div>
			<div class="span6">
				<div class="bk-d-flex bk-flex-wrap">
					<div id="MCSearchSummaryLocationChart" class="bk-mb-3"></div>
					<div id="bk_tooltip"></div>
				</div>
			</div>
		</div>
		
		<cfset local.sendingLawyerName = local.qryDepoMember.name>
		<cfset local.arrDepoMemberNodes = XMLSearch(local.strResults.lawyersDataXML,"//lawyer/@depomemberdataid")>
		<cfset local.depoMemberDataIDList = ArrayToList(arrayMap(local.arrDepoMemberNodes, function(item){ return item.xmlvalue; }))>
		<div id="divEmailInquiryContainer" style="display:none;">
			<cfif variables.cfcuser_DisplayVersion is 1>
				<div class="well">
					<p>TrialSmith DepoConnect allows members to easily email other TrialSmith members who might have experience with a specific Expert Witness.</p>
					<p>Lawyers can reply with messages and documents which are sent to your email inbox. </p>
					<p>Lawyers' responses to your questions are private and are not shared with other TrialSmith members.</p>
					<p>Any depositions shared may also be added to TrialSmith Deposition Bank.</p>
					<div>
					<br/>
					<div><a href="/?pg=upgradeTrialSmith"><b>UPGRADE NOW</b></a></div>
				</div>
			<cfelseif local.qryLoggedInUserOptOutInfo.isOptedOut>
				<cfset local.TSSiteInfo = application.objSiteInfo.getSiteInfo('TS')>
				<cfset local.emailPreferencesURL = "#local.TSSiteInfo.scheme#://#local.TSSiteInfo.mainhostname#/?pg=emailpreferences">
				<div class="well">
					<div>
						Your email address is currently <cfif len(local.qryLoggedInUserOptOutInfo.optOutListName)>opted out of these emails (#local.qryLoggedInUserOptOutInfo.optOutListName#) or it is</cfif> on the Global Opt-Out List. Since this is an email service, you must change your preferences if you want to participate.
					</div>
					<br/>
					<div>Click <a href="#local.emailPreferencesURL#" target="_blank"><b>here</b></a> to Manage Your Preferences</div>
				</div>
			<cfelse>
				<cfif local.exceedsLimit>
					<div class="bk-mb-3">
						<div class="text-warning bk-mb-2">
							<b>#local.strResults.itemCount#</b> matching lawyers seems like a high number and it exceeds our safeguard limit. So only the first <b>#local.strResults.matchesLimit#</b> lawyers will be shown.
						</div>
						Please verify your search criteria. Reach out to TrialSmith Support if you feel this message is in error.
					</div>
				</cfif>
				<button type="button" class="btn btn-link text-decoration-none bk-p-0" onclick="toggleEmailInquiryForm(false);"><i class="icon-chevron-left"></i> Back to Search Results</button>
				<div id="divEmailInquiryForm">
					<h3 class="bk-mt-1">Create Your Email Inquiry for #local.expertName#</h3>
					<p>First, preview the list of lawyers. Second, verify your profile information. Third, write your message. Fourth, review and send your email. Lawyers will respond directly to your email inbox.</p>
					<form name="frm_inquiry" id="frm_inquiry" class="form-horizontal">
						<input type="hidden" name="inq_depoMemberDataIDList" id="inq_depoMemberDataIDList" value="#local.depoMemberDataIDList#">
						<input type="hidden" name="inq_expertName" id="inq_expertName" value="#local.expertName#">
						<input type="hidden" name="inq_requestorName" id="inq_requestorName" value="#local.sendingLawyerName#">
						
						<div id="expertInquiryWizard">
							<!--- Step 1 --->
							<h3 class="font-weight-bold mb-3">Choose Recipients</h3>
							<section>
								<table id="tblExpertConnectLawyersToContact" class="display" style="width:100%">
									<thead>
										<tr>
											<th class="text-center">Selection</th>
											<th class="text-left">Name</th>
											<th class="text-left">Firm</th>
											<th class="text-left">State</th>
										</tr>
									</thead>
									<tbody>
										<cfset local.arrLawyerNodes = XMLSearch(local.strResults.lawyersDataXML,"//lawyer")>
										<cfloop array="#local.strResults.lawyersDataXML.xmlChildren#" index="local.thisfield">
											<cfset local.tmpAtt = local.thisfield.xmlattributes>
											<tr>
												<td class="text-center">
													<p class="elmCheckVal" style='display:none;'>1</p>
													<input type="checkbox" name="recipientCheckbox" class="recipientCheckbox" value="#local.tmpAtt.depoMemberDataID#" checked="checked">
												</td>
												<td>#local.tmpAtt.name#</td>
												<td>#local.tmpAtt.firm#</td>
												<td>#local.tmpAtt.state#</td>
											</tr>
										</cfloop>
									</tbody>
								</table>
							</section>
							<!--- Step 2 --->
							<h3 class="font-weight-bold mb-3">Verify Your Profile Data</h3>
							<section>
								<div class="row-fluid">
									<div class="span12">
										<cfoutput>
										<div class="row-fluid control-group">
											<label for="inq_name" class="control-label">Name</label>
											<div class="controls">
												<input type="text" name="inq_name" id="inq_name" class="input-large cursor-auto" value="#local.sendingLawyerName#" disabled>
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_email" class="control-label">Email Address</label>
											<div class="controls">
												<input type="text" name="inq_email" id="inq_email" class="input-large" maxlength="400" value="#local.qryDepoMember.Email#">
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_firm" class="control-label">Firm</label>
											<div class="controls">
												<input type="text" name="inq_firm" id="inq_firm" class="input-large" maxlength="200" value="#local.qryDepoMember.BillingFirm#">
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_address1" class="control-label">Address</label>
											<div class="controls">
												<input type="text" name="inq_address1" id="inq_address1" class="input-large" maxlength="150" value="#local.qryDepoMember.BillingAddress#">
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_address2" class="control-label">Address Line 2</label>
											<div class="controls">
												<input type="text" name="inq_address2" id="inq_address2" class="input-large" maxlength="150" value="#local.qryDepoMember.BillingAddress2#">
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_address3" class="control-label">Address Line 3</label>
											<div class="controls">
												<input type="text" name="inq_address3" id="inq_address3" class="input-large" maxlength="150" value="#local.qryDepoMember.BillingAddress3#">
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_city" class="control-label">City</label>
											<div class="controls">
												<input type="text" name="inq_city" id="inq_city" class="input-large" maxlength="100" value="#local.qryDepoMember.BillingCity#">
											</div>
										</div>
										</cfoutput>
										<div class="row-fluid control-group">
											<cfoutput><label for="inq_statecode" class="control-label">State</label></cfoutput>	
											<div class="controls">
												<select name="inq_statecode" id="inq_statecode" class="input-large">
													<option value=""></option>
													<cfoutput query="local.qryStates">
														<option value="#local.qryStates.code#" <cfif local.qryStates.code eq local.qryDepoMember.BillingState>selected</cfif>>#local.qryStates.name# (#local.qryStates.code#)</option>
													</cfoutput>
												</select>
											</div>
										</div>
										<cfoutput>
										<div class="row-fluid control-group">
											<label for="inq_postalcode" class="control-label input-medium">Postal Code</label>
											<div class="controls">
												<input type="text" name="inq_postalcode" id="inq_postalcode" class="input-medium" maxlength="15" value="#local.qryDepoMember.BillingZip#">
											</div>
										</div>
										<div class="row-fluid control-group">
											<label for="inq_website" class="control-label input-medium">Law Firm Website</label>
											<div class="controls">
												<input type="text" name="inq_website" id="inq_website" class="input-medium" maxlength="400" value="">
											</div>
										</div>
										<hr/>
										<div class="row-fluid">
											<span class="bk-mr-2">Save Changes to Your TrialSmith Record ?</span>
											<label class="radio inline bk-pt-0">
												<input type="radio" name="updateTSRecord" id="optionsRadio1" value="1" class="mc_inlinecheckbox">Yes
											</label>
											<label class="radio inline bk-pt-0">
												<input type="radio" name="updateTSRecord" id="optionsRadio2" class="mc_inlinecheckbox" value="0" checked>No
											</label>
										</div>
										</cfoutput>
									</div>
								</div>
							</section>
							<!--- Step 3 --->
							<h3 class="font-weight-bold mb-3">Create Your Email</h3>
							<section>
								<div class="bk-d-flex bk-mb-2">
									<div class="bk-align-self-center bk-mr-2">Subject:</div>
									<div class="bk-d-flex bk-w-100">
										<div class="input-prepend bk-input-prepend bk-d-flex bk-w-100">
											<span class="add-on bk-add-on">#local.expertName#:</span>
											<input class="span2 bk-flex-grow" id="inq_topic" type="text" maxlength="250" placeholder="Looking for Information and Depositions">
										</div>
									</div>
								</div>
								<div class="bk-mb-3 bk-mt-3">
									<div class="bk-mb-1">Email Message: Tell lawyers the types of information you're seeking on #local.expertName#</div>
									<textarea cols="50" rows="6" id="inq_descriptionOfAsk" name="inq_descriptionOfAsk" class="bk-w-100" placeholder="Type your detailed message, ask for depositions, and other specific information you need"></textarea>
								</div>
							</section>
							<!--- Step 4 --->
							<h3 class="font-weight-bold mb-3">Review and Send</h3>
							<section>
								<div id="divEmailInquiryPreviewLoading" class="text-center bk-mt-3" style="display:none;"><i class="icon-spin icon-spinner bk-mr-2"></i><b>Loading Preview...</b></div>
								<div id="divEmailInquiryPreview" style="display:none;">
									<p>
										We'll send your message to the <b id="selRecipientsCount">#Numberformat(local.strResults.itemCount)#</b> unique plaintiff attorneys you selected.<br/><br/>
										Your email address will be masked, but replies will be sent directly to your email account where you can reply and interact.<br/><br/>
										Lawyers can attach documents which will be sent directly to your email account at no charge. TrialSmith retains copies of new transcripts and gives a purchase credit to the lawyer who sends the transcript.
									</p>
									<div id="divMessagePreview" style="border:1px dashed ##666;padding:10px;overflow:auto;padding-bottom:20px;">
										<table cellpadding="1" cellspacing="0" style="font-size:smaller;">
											<tr><td class="">From:</td><td width="3"></td><td>#local.sendingLawyerName#<span id="spFromFirm"></span></td></tr>
											<tr><td class="">Reply-To:</td><td width="3"></td><td><span id="spReplyTo"></span> (your email address will be masked)</td></tr>
											<tr><td class="">Subject:</td><td width="3"></td><td>#local.expertName# - <span id="spSubject"></span></td></tr>
										</table>
										<div style="margin:12px 0;border-bottom:1px solid ##ccc;"></div>
										<div id="divInquiryEmailBody"></div>
									</div>
								</div>
							</section>
							<div id="err_email_inq" class="alert alert-danger" style="display:none;"></div>
						</div>
					</form>
				</div>
				<div id="divSendingInquiryLoading" class="text-center bk-mt-3" style="display:none;"><i class="icon-spin icon-spinner bk-mr-2"></i><b>Sending Email Inquiry...</b></div>
				<div id="divEmailInquirySuccess" class="alert alert-success" style="display:none;margin-top:10px;padding:15px;">Your Email Inquiry for <b>#local.expertName#</b> has been successfully submitted for approval.</div>
			</cfif>
		</div>
	</div>
</cfif>
</cfoutput>