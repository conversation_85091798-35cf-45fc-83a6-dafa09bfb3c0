<cfinclude template="../commonEventRegStep4JS.cfm">

<cfoutput>
<div id="evRegStep4Summary" class="evreg-card evreg-mt-3 evreg-p-3 evRegStepSummary evreg-cursor-pointer" data-evregsummarystep="4"<cfif local.evRegV2.currentReg.currentStep EQ 4> style="display:none;"</cfif>>
	<div class="evreg-d-flex">
		<a href="##" class="evreg-align-self-center evreg-mr-2 evreg-font-size-lg evreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
		<div class="evreg-col">
			<div class="evreg-font-size-lg evreg-font-weight-bold">Ticket Selections</div>
		</div>
		<div id="evRegStep4TotalPriceDisplay" class="evreg-ml-auto evreg-font-size-lg evreg-font-weight-bold">#dollarFormat(local.qryS4Totals.totalAmount)##local.displayedCurrencyType#</div>
	</div>
</div>
<div id="frmEventRegStep4Container" class="<cfif local.evRegV2.currentReg.currentStep NEQ 4>evreg-card evreg-p-3 </cfif>evreg-mt-3"<cfif local.evRegV2.currentReg.currentStep NEQ 4> style="display:none;"</cfif>>
	<cfif local.evRegV2.currentReg.currentStep NEQ 4><div class="evreg-mb-3 evreg-font-size-lg evreg-font-weight-bold">Ticket Selections:</div></cfif>
	<form name="frmEventRegStep4" id="frmEventRegStep4" autocomplete="off">
		<cfoutput query="local.qryTicketDetails" group="ticketID">
			<cfset local.showThisTicket = true>
			<cfif local.qryExcludedPackages.recordCount>
				<cfquery name="local.qryThisTicketPackages" dbtype="query">
					select ticketPackageID
					from [local].qryTicketDetails
					where ticketID = #val(local.qryTicketDetails.ticketID)#
				</cfquery>
				<cfquery name="local.qryThisTicketIncludedPackages" dbtype="query">
					select ticketPackageID
					from [local].qryIncludedPackages
					where ticketID = #val(local.qryTicketDetails.ticketID)#
				</cfquery>
				<cfquery name="local.qryThisTicketExcludedPackages" dbtype="query">
					select ticketPackageID
					from [local].qryExcludedPackages
					where ticketID = #val(local.qryTicketDetails.ticketID)#
				</cfquery>
				<cfif local.registrantID GT 0>
					<cfquery name="local.qryThisTicketPackagesSelected" dbtype="query">
						select distinct ticketPackageID
						from [local].qryTicketPackagesSelected
						where ticketID = #val(local.qryTicketDetails.ticketID)#
					</cfquery>
				</cfif>
				
				<cfif local.qryThisTicketExcludedPackages.recordCount EQ local.qryThisTicketPackages.recordCount AND local.qryThisTicketIncludedPackages.recordCount EQ 0>
					<cfif local.registrantID GT 0 AND local.qryThisTicketPackagesSelected.recordCount EQ 0>
						<cfset local.showThisTicket = false>
					<cfelseif local.registrantID EQ 0>
						<cfset local.showThisTicket = false>
					</cfif>
				</cfif>
			</cfif>

			<cfquery name="local.qryThisTicketHasAvailablePackages" dbtype="query" maxrows="1">
				select ticketPackageID
				from [local].qryTicketDetails
				where ticketID = #val(local.qryTicketDetails.ticketID)#
				and ticketAvailable = 1
				and ticketPackageAvailable = 1
			</cfquery>
			<cfset local.thisTicketSoldOut = local.qryThisTicketHasAvailablePackages.recordCount is 0>

			<cfif local.showThisTicket>
				<div class="MCTicketInstanceForm">
					<div class="evreg-card evreg-mt-3">
						<div class="evreg-card-header evreg-bg-whitesmoke evreg-pb-1">
							<div class="evreg-font-size-lg evreg-font-weight-bold">
								#local.qryTicketDetails.ticketName#
								<cfif local.thisTicketSoldOut>
									- <span class="evreg-text-danger">SOLD OUT</span>
								</cfif>
							</div>
							<div class="evreg-text-dim evreg-font-size-sm">#local.qryTicketDetails.ticketDescription#</div>
						</div>
						<div class="evreg-card-body">
							<cfset local.thisTicketInventoryCount = val(local.qryTicketDetails.ticketInventoryCount)>
							<cfif local.registrantID EQ 0 and local.qryTicketDetails.qtyIncludedByRate GT 0>
								<cfset local.thisPackageIncByRate = local.qryTicketDetails.qtyIncludedByRate>
								<cfset local.thisTicketInventoryCount = local.thisTicketInventoryCount + (local.thisPackageIncByRate * local.qryTicketDetails.ticketsInPackage)>
							</cfif>
							<input type="hidden" name="ticketInventory_#local.qryTicketDetails.ticketID#" id="ticketInventory_#local.qryTicketDetails.ticketID#" value="#local.qryTicketDetails.ticketInventory#">
							<input type="hidden" name="ticketInventoryCount_#local.qryTicketDetails.ticketID#" id="ticketInventoryCount_#local.qryTicketDetails.ticketID#" value="#local.thisTicketInventoryCount#">

							<cfoutput>
								<cfset local.thisRegPackageCount = 0>
								<cfset local.thisNewRegPackageCountNotIncByRate = 0>
								<cfset local.thisTicketPackageInventoryCount = val(local.qryTicketDetails.ticketPackageInventoryCount)>

								<cfif local.registrantID GT 0>
									<cfquery name="local.qryThisRegistrantPackage" dbtype="query">
										select instanceID
										from [local].qryTicketPackagesSelected
										where ticketPackageID = #local.qryTicketDetails.ticketPackageID#
									</cfquery>
									<cfset local.thisRegPackageCount = local.qryThisRegistrantPackage.recordCount>
								<cfelseif local.registrantID eq 0>
									<cfif local.qryTicketDetails.qtyIncludedByRate gt 0>
										<cfset local.thisTicketPackageInventoryCount = local.thisTicketPackageInventoryCount + local.qryTicketDetails.qtyIncludedByRate>
										<cfset local.thisRegPackageCount = local.qryTicketDetails.qtyIncludedByRate>
									</cfif>

									<cfif structKeyExists(local.evRegV2.currentReg.s4.tickets,local.qryTicketDetails.ticketID) and structKeyExists(local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID],local.qryTicketDetails.ticketPackageID) and arrayLen(local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID][local.qryTicketDetails.ticketPackageID].arrPackage) gt local.thisRegPackageCount>
										<cfset local.thisNewRegPackageCountNotIncByRate = arrayLen(local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID][local.qryTicketDetails.ticketPackageID].arrPackage) - local.thisRegPackageCount>
										<cfset local.thisRegPackageCount = arrayLen(local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID][local.qryTicketDetails.ticketPackageID].arrPackage)>
									</cfif>
								</cfif>

								<cfif local.qryTicketDetails.ticketPackageAvailable AND NOT (local.qryTicketDetails.isExcludedByRate EQ 1 AND local.thisRegPackageCount EQ 0)>
									<!--- this package hidden fields --->
									<input type="hidden" name="ticketID_#local.qryTicketDetails.ticketPackageID#" id="ticketID_#local.qryTicketDetails.ticketPackageID#" value="#local.qryTicketDetails.ticketID#">
									<input type="hidden" name="ticketPackageInventory_#local.qryTicketDetails.ticketPackageID#" id="ticketPackageInventory_#local.qryTicketDetails.ticketPackageID#" value="#local.qryTicketDetails.ticketPackageInventory#">
									<input type="hidden" name="ticketPackageInventoryCount_#local.qryTicketDetails.ticketPackageID#" id="ticketPackageInventoryCount_#local.qryTicketDetails.ticketPackageID#" value="#local.thisTicketPackageInventoryCount#">
									<input type="hidden" name="ticketsInPackage_#local.qryTicketDetails.ticketPackageID#" id="ticketsInPackage_#local.qryTicketDetails.ticketPackageID#" value="#local.qryTicketDetails.ticketsInPackage#">
									<input type="hidden" name="packageCount_#local.qryTicketDetails.ticketPackageID#" id="packageCount_#local.qryTicketDetails.ticketPackageID#" value="#local.thisRegPackageCount#">

									<input type="hidden" name="totalPackage_#local.qryTicketDetails.ticketPackageID#" id="totalPackage_#local.qryTicketDetails.ticketPackageID#" value="#val(local.thisRegPackageCount)#">
									<input type="hidden" name="ticketPackage_#local.qryTicketDetails.ticketPackageID#" id="ticketPackage_#local.qryTicketDetails.ticketPackageID#" class="ticketSelect" value="#local.thisNewRegPackageCountNotIncByRate#">

									<div class="well well-small clearfix evreg-mb-3">
										<div class="evreg-d-flex evreg-flex-sm-column">
											<div class="evreg-col">
												<div class="evreg-font-size-lg evreg-font-weight-bold">#local.qryTicketDetails.ticketPackageName#</div>
												<cfif len(local.qryTicketDetails.ticketPackageDescription)>
													<div class="evreg-text-dim evreg-font-size-sm">#local.qryTicketDetails.ticketPackageDescription#</div>
												</cfif>
												<div>
													<span class="evreg-font-size-md evreg-font-weight-bold evreg-mr-2"> Price: #DollarFormat(local.qryTicketDetails.ticketPackageAmount)##local.displayedCurrencyType#</span>
													<cfif val(local.qryTicketDetails.qtyIncludedByRate) gt 0>
														<span class="label label-success">#local.qryTicketDetails.qtyIncludedByRate# Included with Registration</span>
													<cfelse>
														<span class="label label-warning">Not Included with Registration</span>
													</cfif>
												</div>
											</div>
											<div class="evreg-col-auto evreg-text-right evreg-mt-sm-2 evreg-text-sm-left">
												<div id="totalPackageCountDisp_#local.qryTicketDetails.ticketPackageID#" class="evreg-font-size-lg evreg-font-weight-bold evreg-mb-2">You have #local.thisRegPackageCount# Ticket#local.thisRegPackageCount NEQ 1 ? 's' : ''#</div>
												<div id="totalPackagePriceDisp_#local.qryTicketDetails.ticketPackageID#" class="evreg-font-size-lg evreg-font-weight-bold evreg-mb-2">
													<cfif structKeyExists(local.evRegV2.currentReg.s4.tickets,local.qryTicketDetails.ticketID) and structKeyExists(local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID],local.qryTicketDetails.ticketPackageID) and arrayLen(local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID][local.qryTicketDetails.ticketPackageID].arrPackage)>
														Your fee for this package: #local.evRegV2.currentReg.s4.tickets[local.qryTicketDetails.ticketID][local.qryTicketDetails.ticketPackageID].totalAmtDisplay#
													</cfif>
												</div>
												<cfif NOT local.qryTicketDetails.adminOnlyPackage AND NOT local.qryTicketDetails.isExcludedByRate>
													<button type="button" class="btn btn-small btn-success btnTicketSel" name="btnTicketPackage_#local.qryTicketDetails.ticketPackageID#" id="btnTicketPackage_#local.qryTicketDetails.ticketPackageID#" data-maxPackagesPerRegistrant="#val(local.qryTicketDetails.maxPackagesPerRegistrant)#" data-packageID="#local.qryTicketDetails.ticketPackageID#" data-availablePriceID="#local.qryTicketDetails.availablePriceID#" data-ticketID="#local.qryTicketDetails.ticketID#" onclick="incrementPackageCount(this);" disabled="disabled"><i class="icon-plus"></i> Add</button>
												</cfif>
											</div>
										</div>
									</div>
									<div class="MCTicketPackageWrapper" id="MCTicketPackageID#local.qryTicketDetails.ticketpackageID#" data-mcticketname="#local.qryTicketDetails.ticketName#" data-mcticketpackagename="#local.qryTicketDetails.ticketPackageName#" data-mcticketpackageid="#local.qryTicketDetails.ticketpackageid#" data-mcticketid="#local.qryTicketDetails.ticketid#"></div>
								</cfif>
							</cfoutput>
						</div>
					</div>
				</div>
			</cfif>
		</cfoutput>

		<cfloop array="#local.arrPackages#" index="local.thisPackage">
			<input type="hidden" name="instanceCount_#local.thisPackage.ticketpackageID#" id="instanceCount_#local.thisPackage.ticketpackageID#" value="0">
		</cfloop>

		<div id="step4Err" class="alert alert-danger evreg-mt-3" style="display:none;"></div>
		<div class="evreg-mt-5 evreg-text-right">
			<button type="button" name="btnSaveStep4" id="btnSaveStep4" class="btn btn-success" onclick="doS4ValidateAndSave();">
				<cfif local.evRegV2.currentReg.isRegCartItem EQ 1 OR local.evRegV2.currentReg.registrantID GT 0>
					Save Changes
				<cfelse>
					Save Changes & Continue
				</cfif>
			</button>
		</div>
		<div id="evRegStep4SaveLoading" style="display:none;"></div>
	</form>
</div>

<script id="mc_evTPInstanceTempate" type="text/x-handlebars-template">
	{{##if showPackage}}
		{{##each instanceArray}}
			<div class="well well-small MCTicketPackageInstanceWrapper {{##compare includedFromRate '==' 1}}MCTicketPackageInstanceIncludedFromRate{{/compare}}" id="MCTicketPackage{{packageID}}Instance{{instanceID}}_{{instanceNum}}" {{##if tpRemoveInstance}}style="display:none;"{{/if}} data-MCInstanceTitle="{{instanceTitle}}" data-MCTicketPackageInstanceID="{{instanceID}}" data-mcticketid="{{ticketID}}">
				<div style="float:right">
					<span class="MCTicketPackageInstanceIncludedFromRate label label-success">Included with Registration</span>
					{{##compare includedFromRate '==' 0}}
						<button type="button" class="btn btn-small" id="doRemoveInstance_{{packageID}}_{{instanceNum}}" name="doRemoveInstance_{{packageID}}_{{instanceNum}}" style="float:right;" onclick="removeRegPackageInstance({{instanceNum}},{{packageID}},{{instanceID}},{{availablePriceID}})"><i class="icon-trash"></i></button>
						<input type="hidden" name="removeTicketPackageInstance_{{instanceID}}" id="removeTicketPackageInstance_{{instanceID}}" value="{{tpRemoveInstance}}">
					{{/compare}}
				</div>
				<div class="MCInstanceTitleWrapper">
					<b id="MCInstanceTitle_{{packageID}}_{{instanceID}}_{{instanceNum}}" class="MCInstanceTitle" data-linkedInstance="MCTicketPackage{{packageID}}Instance{{instanceID}}_{{instanceNum}}">{{instanceTitle}}</b>
					{{##compare instanceID '>' 0}}
						{{##compare packageAmount '!=' packageActualFee}}
							<span class="mcfieldfee pull-right" style="margin-right:10px;"><small>Current Package Price is {{dispPackageActualFee}}{{../displayedCurrencyType}}</small></span>
						{{/compare}}
					{{/compare}}
				</div>
				<input type="hidden" name="tpiInstanceID_{{packageID}}_{{instanceNum}}" id="tpiInstanceID_{{packageID}}_{{instanceNum}}" value="{{instanceID}}">
				<input type="hidden" name="tpiIncludedFromRate_{{packageID}}_{{instanceNum}}" id="tpiIncludedFromRate_{{packageID}}_{{instanceNum}}" value="{{includedFromRate}}">
				<input type="hidden" name="tpiAvailablePriceID_{{packageID}}_{{instanceNum}}" id="tpiAvailablePriceID_{{packageID}}_{{instanceNum}}" value="{{availablePriceID}}">
				<input type="hidden" name="packageInstance_{{packageID}}_{{availablePriceID}}_{{instanceNum}}" id="packageInstance_{{packageID}}_{{availablePriceID}}_{{instanceNum}}" value="{{instanceNum}}">
				
				<div id="MCInstanceError_{{packageID}}_{{instanceID}}_{{instanceNum}}" class="alert MCInstanceError" data-linkedInstance="MCTicketPackage{{packageID}}Instance{{instanceID}}_{{instanceNum}}"></div>
				<!--- ticket package fields --->
				{{##if ../hasPackageFields}}
					<div class="MCTicketPackageInstancePackageFieldsWrapper">
					{{##each arrPackageFields}}
						{{##compare fieldGroupingID '>' 0}}
							<div style="padding-left:25px;">
							<fieldset style="padding:10px;border:1px solid ##ccc;margin:10px 0;">
							<legend style="width:auto;font-size:1.1875em;font-weight:400;margin:0;padding:0 8px;border:0;line-height:20px;">{{fieldGrouping}}</legend>
							{{##compare fieldGroupingDesc.length '>' 0}}
								<div style="margin-bottom:15px;">{{fieldGroupingDesc}}</div>
							{{/compare}}
						{{/compare}}

						{{##each arrFields}}
							{{##unless allOptionEmptyOrDisabled}}
								<div style="padding:8px 0;">
									<div{{##compare ../fieldGroupingID '==' 0}} class="evregTicketPackageFieldsContainer"{{/compare}}>
										{{##compare displayTypeCode '==' "LABEL"}}
											{{{attributes.fieldText}}}
										{{/compare}}
										{{##compare displayTypeCode '!=' "LABEL"}}
											{{##if isRequired}}* {{/if}}
											<b>{{attributes.fieldText}}</b>
										{{/compare}}
										<!--- textbox --->
										{{##compare displayTypeCode '==' "TEXTBOX"}}
											<div style="padding:6px 0 6px 20px;">
												{{##if displayOnly}}
													{{value}}
												{{else if supportQty}}
													{{##if supportAmt}}
														{{##compare ../instanceID '>' 0}}
														{{##compare value '>' 0}}
															{{##if qtyIDArr.length}}
																<table cellpadding="2" cellspacing="0">
																	<tr><td class="tsAppBB20 text-center">&nbsp; <b>Remove</b> &nbsp;</td><td class="tsAppBB20"><b>Existing Quantity</b></td></tr>
																	{{##each qtyIDArr}}
																		<tr><td class="text-center"><input type="checkbox" name="evtpcqty_{{../ticketPackageID}}_{{../../../instanceNum}}_{{../fieldID}}" class="tsAppBodyText" value="{{subItemID}}"></td>
																		<td class="tsAppBodyText">{{detail}} {{row}}/{{../qtyIDArr.length}} - <span class="mcfieldfee">{{amount}}</span></td></tr>
																	{{/each}}
																</table>
																<br/>
																Additional 
																{{var "value" "0"}}
															{{/if}}
														{{/compare}}
														{{/compare}}
													{{/if}}

													{{##unless supportAmt}}
													{{##unless maxQtyAllowed}}
														<input type="hidden" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" value="{{value}}">
													{{/unless}}
													{{/unless}}

													Quantity: <input type="text" size="6" autocomplete="off" maxlength="4" id="{{##unless maxQtyAllowed}}disabled_{{/unless}}ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="{{##unless maxQtyAllowed}}disabled_{{/unless}}ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketOfferQTY="{{supportQty}}" data-MCEventTicketOfferAmount="0" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketMaxQtyAllowed="{{maxQtyAllowed}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" {{##unless maxQtyAllowed}}disabled{{/unless}} class="MCPackageField">
													{{##compare maxQtyAllowed '>' 0}}
													{{##compare attributes.amount '>' 0}}
														&nbsp;
														({{dispAmount}}{{displayedCurrencyType}} each)
													{{/compare}}
													{{/compare}}
													{{##unless maxQtyAllowed}}
														&nbsp; <span class="tsAppBodyTextImportant">[SOLD OUT]</span>
													{{/unless}}

												{{else if supportAmt}}
													$ <input type="text" size="8" autocomplete="off" maxlength="10" id="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketOfferQTY="{{attributes.supportQty}}" data-MCEventTicketOfferAmount="{{attributes.supportAmt}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketMaxQtyAllowed="0" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" onBlur="this.value=formatCurrency(this.value);" class="MCPackageField">{{displayedCurrencyType}}
													{{##compare ../instanceID '>' 0}}
													{{##compare value '!=' actualFee}}
														<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{dispActualFee}}{{displayedCurrencyType}}</span>
													{{/compare}}
													{{/compare}}

												{{else if isNameTextBoxField}}
													<input type="text" size="20" autocomplete="off" id="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCPackageField MCTicketNameField" data-MCEventTicketAutoFillRegistrant="{{attributes.autoFillRegistrant}}"> <a href="##" class="MCTicketNameControlLink" data-linkedNameControl="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" {{##if value.length}}style="display:none;"{{/if}}></a>  <a href="##" class="MCTicketNameClearControlLink" data-linkedNameControl="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" {{##unless value.length}}style="display:none;"{{/unless}}>Clear Name</a>

												{{else}}
													<input type="text" autocomplete="off" id="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCPackageField evreg-formcontrol span6">
												{{/if}}

											</div>
										{{/compare}}
										<!--- Drop-Down List --->
										{{##compare displayTypeCode '==' "SELECT"}}
											<div style="padding:6px 0 6px 20px;">
												{{##if displayOnly}}
													{{##each children}}
														{{##compare ../value '==' attributes.valueID}}
															{{attributes.fieldValue}}
														{{/compare}}
													{{/each}}
												{{else}}
													<select id="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" class="MCPackageField">
														<option value=""></option>
														{{##each children}}
															<option value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}selected{{/compare}} {{##if unavailable}}disabled{{/if}}>
															{{attributes.fieldValue}}
															{{##unless unavailable}}
																{{##compare attributes.amount '>' 0}}
																	(+ {{dispAmount}}{{../displayedCurrencyType}})
																{{/compare}}
															{{/unless}}
															{{##if unavailable}}
																&nbsp; [SOLD OUT]
															{{/if}}
															</option>
														{{/each}}
													</select>
												{{/if}}

												{{##if supportAmt}}
													{{##each children}}
														{{##compare actualFee '!=' ''}}
														{{##compare ../value '==' attributes.valueID}}
														{{##compare attributes.amount '!=' actualFee}}
															<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{dispActualFee}}{{../displayedCurrencyType}}</span>
														{{/compare}}
														{{/compare}}
														{{/compare}}
													{{/each}}
												{{/if}}
											</div>
										{{/compare}}
										<!--- Radio Controls --->
										{{##compare displayTypeCode '==' "RADIO"}}
											<div style="padding:6px 0 6px 20px;">
												{{##each children}}
													{{##if ../displayOnly}}
														{{##compare ../value '==' attributes.valueID}}
															{{attributes.fieldValue}}
														{{/compare}}
													{{else}}
														<label class="radio">
															<input type="radio" id="ev_tpc_{{../ticketPackageID}}_{{../../../instanceNum}}_{{../fieldID}}" name="ev_tpc_{{../ticketPackageID}}_{{../../../instanceNum}}_{{../fieldID}}" value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}checked{{/compare}} {{##if unavailable}}disabled{{/if}} data-MCEventTicketDisplayTypeCode="{{../displayTypeCode}}" data-MCEventTicketDataTypeCode="{{../dataTypeCode}}" data-MCEventTicketIsRequired="{{../attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{../attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}}" data-MCEventTicketFieldDesc="{{../attributes.fieldText}}" class="MCPackageField"> {{attributes.fieldValue}}
															{{##unless unavailable}}
																{{##compare attributes.amount '>' 0}}
																	(+ {{dispAmount}}{{../displayedCurrencyType}})
																{{/compare}}
															{{/unless}}
															{{##if unavailable}}
																&nbsp; [SOLD OUT]
															{{/if}}
															{{##if ../supportAmt}}
																{{##compare actualFee '!=' ''}}
																{{##compare ../value '==' attributes.valueID}}
																{{##compare attributes.amount '!=' actualFee}}
																	<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{dispActualFee}}{{../displayedCurrencyType}}</span>
																{{/compare}}
																{{/compare}}
																{{/compare}}
															{{/if}}
														</label>
													{{/if}}
												{{/each}}
											</div>
										{{/compare}}
										<!--- Checkboxes --->
										{{##compare displayTypeCode '==' "CHECKBOX"}}
											<div style="padding:6px 0 6px 20px;">
												{{##each children}}
													{{##if ../displayOnly}}
														{{##each ../value}}
														{{##compare this '==' ../attributes.valueID}}
															{{../../attributes.fieldValue}}<br/>
														{{/compare}}
														{{/each}}
													{{else}}
														<label class="checkbox">
															<input type="checkbox" id="ev_tpc_{{../ticketPackageID}}_{{../../../instanceNum}}_{{../fieldID}}" name="ev_tpc_{{../ticketPackageID}}_{{../../../instanceNum}}_{{../fieldID}}" value="{{attributes.valueID}}" {{##if unavailable}}disabled{{/if}} data-MCEventTicketDisplayTypeCode="{{../displayTypeCode}}" data-MCEventTicketDataTypeCode="{{../dataTypeCode}}" data-MCEventTicketIsRequired="{{../attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{../attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}}" data-MCEventTicketFieldDesc="{{../attributes.fieldText}}" {{##each ../value}}{{##compare this '==' ../attributes.valueID}}checked{{/compare}}{{/each}} class="MCPackageField"> {{attributes.fieldValue}}
															{{##unless unavailable}}
																{{##compare attributes.amount '>' 0}}
																	(+ {{dispAmount}}{{../displayedCurrencyType}})
																{{/compare}}
															{{/unless}}
															{{##if unavailable}}
																&nbsp; [SOLD OUT]
															{{/if}}
															{{##if ../supportAmt}}
																{{##each ../value}}
																	{{##compare ../../actualFee '!=' ''}}
																	{{##compare this '==' ../attributes.valueID}} 
																	{{##compare ../../attributes.amount '!=' ../../actualFee}} 
																		<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{../../dispActualFee}}{{../../../displayedCurrencyType}}</span>
																	{{/compare}}
																	{{/compare}}
																	{{/compare}}
																{{/each}}
															{{/if}}
														</label>
													{{/if}}
												{{/each}}
											</div>
										{{/compare}}
										<!--- Date --->
										{{##compare displayTypeCode '==' "DATE"}}
											<div style="padding:6px 0 6px 20px;">
												{{##if displayOnly}}
													{{value}}
												{{else}}
													<div class="evreg-d-flex">
														<div class="evreg-input-append evreg-col-auto">
															<input type="text" size="16" autocomplete="off" id="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="evreg-formcontrol dateControl MCPackageField MCAdminDateControl">
															<span class="evreg-add-on evreg-cursor-pointer calendar-button" data-target="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}"><i class="icon-calendar-empty"></i></span>
															<span class="evreg-add-on">
																<a href="##" class="MCAdminDateControlClearLink evreg-p-0 evreg-text-decoration-none evreg-text-danger" data-linkedDateControl="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}"><i class="icon-remove"></i></a>
															</span>
														</div>
													</div>
												{{/if}}
											</div>
										{{/compare}}
										<!--- Textarea --->
										{{##compare displayTypeCode '==' "TEXTAREA"}}
											<div style="padding:6px 0 6px 20px;">
												{{##if displayOnly}}
													{{value}}
												{{else}}
													<textarea cols="62" rows="5" id="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" name="ev_tpc_{{ticketPackageID}}_{{../../instanceNum}}_{{fieldID}}" class="tsAppBodyText MCPackageField" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../instanceTitle}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}">{{value}}</textarea>
												{{/if}}
											</div>
										{{/compare}}
									</div>
								</div>
							{{/unless}}
						{{/each}}

						{{##compare fieldGroupingID '>' 0}}
								</fieldset>
							</div>
						{{/compare}}
					{{/each}}
					</div>
				{{/if}}
			
				{{##if ../showTicketSelections}}
					{{##each arrTicketInstance}}
						<div class="MCTicketPackageInstanceTicketWrapper" data-MCTicketSeat="Ticket {{math @index "+" 1}}">
							{{##if ../../ticketAssignSeats}}
								<div class="span12" style="padding:8px 0;">
									<div class="span2" style="padding-left:30px;">
										<b>Ticket {{math @index "+" 1}}</b>
									</div>
									<div class="span6">
										<input type="text" name="ev_tg_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}" id="ev_tg_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}" value="" disabled style="width:225px;">
										<input type="hidden" name="ev_tgmid_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}" id="ev_tgmid_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}" value="">
									</div>
									<div class="span4" id="addGuest_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}" id="ev_tgmid_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}">
										<a href="javascript:addGuest({{../../ticketPackageID}},{{../instanceNum}},{{math @index "+" 1}})" id="ev_tgmid_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}">Assign Guest</a>&nbsp;&nbsp;
										<a href="javascript:assignToMyself({{../../ticketPackageID}},{{../instanceNum}},{{math @index "+" 1}})">Assign to Myself</a>
									</div>
									<div class="span4" class="hide" id="changeGuest_{{../../ticketPackageID}}_{{../instanceNum}}_{{math @index "+" 1}}">
										<a href="javascript:addGuest({{../../ticketPackageID}},{{../instanceNum}},{{math @index "+" 1}})">Change Guest</a>&nbsp;&nbsp;
										<a href="javascript:removeGuest({{../../ticketPackageID}},{{../instanceNum}},{{math @index "+" 1}})">Remove Guest</a>
									</div>
									<div style="clear:both;"></div>
								</div>
							{{else}}
								{{##compare ../../ticketsInPackage '>' 1}}
									<div class="span12" style="padding:8px 0;">
										<div class="span2" style="padding-left:30px;">
											<b>Ticket {{math @index "+" 1}}</b>
										</div>
									</div>
								{{/compare}}
							{{/if}}
							{{##if ../../hasTicketFields}}
								<!--- ticket fields --->
								<div class="MCTicketPackageInstanceTicketFieldsWrapper" data-MCTicketSeat="Ticket {{math @index "+" 1}}">
									{{##each this}}
										{{##compare fieldGroupingID '>' 0}}
											<div style="padding-left:50px;">
											<fieldset style="padding:10px;border:1px solid ##ccc;margin:10px 0;">
											<legend style="width:auto;font-size:1.1875em;font-weight:400;margin:0;padding:0 8px;border:0;line-height:20px;">{{fieldGrouping}}</legend>
											{{##compare fieldGroupingDesc.length '>' 0}}
												<div style="margin-bottom:15px;">{{fieldGroupingDesc}}</div>
											{{/compare}}
										{{/compare}}

										{{##each arrFields}}
											{{##unless allOptionEmptyOrDisabled}}
												<div style="padding:10px 0;">
													<div{{##compare ../fieldGroupingID '==' 0}} class="evregTicketFieldsContainer"{{/compare}}>
														{{##compare displayTypeCode '==' "LABEL"}}
															{{{attributes.fieldText}}}
														{{/compare}}
														{{##compare displayTypeCode '!=' "LABEL"}}
															{{##if isRequired}}<strong>*</strong> {{/if}}
															{{attributes.fieldText}}<br/>
														{{/compare}}
														<!--- textbox --->
														{{##compare displayTypeCode '==' "TEXTBOX"}}
															<div style="padding:6px 0 6px 20px;">
																{{##if displayOnly}}
																	{{value}}
																{{else if supportQty}}
																	{{##if supportAmt}}
																		{{##compare ../../instanceID '>' 0}}
																		{{##compare value '>' 0}}
																			{{##if qtyIDArr.length}}
																				<table cellpadding="2" cellspacing="0">
																					<tr><td class="tsAppBB20 text-center">&nbsp; <b>Remove</b> &nbsp;</td><td class="tsAppBB20"><b>Existing Quantity</b></td></tr>
																					{{##each qtyIDArr}}
																						<tr><td class="text-center"><input type="checkbox" name="evtcqty_{{../ticketPackageID}}_{{../ticketID}}_{{../../../../instanceNum}}_{{../seatNum}}_{{../fieldID}}" class="tsAppBodyText" value="{{subItemID}}"></td>
																						<td class="tsAppBodyText">{{detail}} {{row}}/{{../qtyIDArr.length}} - <span class="mcfieldfee">{{amount}}</span></td></tr>
																					{{/each}}
																				</table>
																				<br/>
																				Additional 
																				{{var "value" "0"}}
																			{{/if}}
																		{{/compare}}
																		{{/compare}}
																	{{/if}}

																	{{##unless supportAmt}}
																	{{##unless maxQtyAllowed}}
																		<input type="hidden" name="ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}" value="{{value}}">
																	{{/unless}}
																	{{/unless}}

																	Quantity: <input type="text" size="6" autocomplete="off" maxlength="4" id='{{##unless maxQtyAllowed}}disabled_{{/unless}}ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='{{##unless maxQtyAllowed}}disabled_{{/unless}}ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketOfferQTY="{{supportQty}}" data-MCEventTicketOfferAmount="0" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketMaxQtyAllowed="{{maxQtyAllowed}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" {{##unless maxQtyAllowed}}disabled{{/unless}} class="MCTicketField">
																	{{##compare maxQtyAllowed '>' 0}}
																	{{##compare attributes.amount '>' 0}}
																		&nbsp;
																		({{dispAmount}}{{displayedCurrencyType}} each)
																	{{/compare}}
																	{{/compare}}
																	{{##unless maxQtyAllowed}}
																		&nbsp; <span class="tsAppBodyTextImportant">[SOLD OUT]</span>
																	{{/unless}}

																{{else if supportAmt}}
																	$ <input type="text" size="8" autocomplete="off" maxlength="10" id='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketOfferQTY="{{attributes.supportQty}}" data-MCEventTicketOfferAmount="{{attributes.supportAmt}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketMaxQtyAllowed="0" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" onBlur="this.value=formatCurrency(this.value);" class="MCTicketField">{{displayedCurrencyType}}
																	{{##compare ../../instanceID '>' 0}}
																	{{##compare value '!=' actualFee}}
																		<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{dispActualFee}}{{displayedCurrencyType}}</span>
																	{{/compare}}
																	{{/compare}}
																	
																{{else if isNameTextBoxField}}
																	<input type="text" size="20" autocomplete="off" id='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCTicketField MCTicketNameField" data-MCEventTicketAutoFillRegistrant="{{attributes.autoFillRegistrant}}"> <a href="##" class="MCTicketNameControlLink" data-linkedNameControl="ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}" {{##if value.length}}style="display:none;"{{/if}}></a>  <a href="##" class="MCTicketNameClearControlLink" data-linkedNameControl="ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}" {{##unless value.length}}style="display:none;"{{/unless}}>Clear Name</a>

																{{else}}
																	<input type="text" autocomplete="off" id='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="MCTicketField evreg-formcontrol span6">
																{{/if}}
																
															</div>
														{{/compare}}
														<!--- Drop-Down List --->
														{{##compare displayTypeCode '==' "SELECT"}}
															<div style="padding:6px 0 6px 20px;">
																{{##if displayOnly}}
																	{{##each children}}
																		{{##compare ../value '==' attributes.valueID}}
																			{{attributes.fieldValue}}
																		{{/compare}}
																	{{/each}}
																{{else}}
																	<select id='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" class="MCTicketField">
																		<option value=""></option>
																		{{##each children}}
																			<option value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}selected{{/compare}} {{##if unavailable}}disabled{{/if}}>
																			{{attributes.fieldValue}}
																			{{##unless unavailable}}
																				{{##compare attributes.amount '>' 0}}
																					(+ {{dispAmount}}{{../displayedCurrencyType}})
																				{{/compare}}
																			{{/unless}}
																			{{##if unavailable}}
																				&nbsp; [SOLD OUT]
																			{{/if}}
																			</option>
																		{{/each}}
																	</select>
																{{/if}}

																{{##if supportAmt}}
																	{{##each children}}
																		{{##compare actualFee '!=' ''}}
																		{{##compare ../value '==' attributes.valueID}}
																		{{##compare attributes.amount '!=' actualFee}}
																			<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{dispActualFee}}{{../displayedCurrencyType}}</span>
																		{{/compare}}
																		{{/compare}}
																		{{/compare}}
																	{{/each}}
																{{/if}}
															</div>
														{{/compare}}
														<!--- Radio Controls --->
														{{##compare displayTypeCode '==' "RADIO"}}
															<div style="padding:6px 0 6px 20px;">
																{{##each children}}
																	{{##if ../displayOnly}}
																		{{##compare ../value '==' attributes.valueID}}
																			{{attributes.fieldValue}}
																		{{/compare}}
																	{{else}}
																		<label class="radio">
																			<input type="radio" id='ev_tc_{{../ticketPackageID}}_{{../ticketID}}_{{../fieldID}}_{{../seatNum}}_{{../../../../instanceNum}}' name='ev_tc_{{../ticketPackageID}}_{{../ticketID}}_{{../fieldID}}_{{../seatNum}}_{{../../../../instanceNum}}' value="{{attributes.valueID}}" {{##compare ../value '==' attributes.valueID}}checked{{/compare}} {{##if unavailable}}disabled{{/if}} data-MCEventTicketDisplayTypeCode="{{../displayTypeCode}}" data-MCEventTicketDataTypeCode="{{../dataTypeCode}}" data-MCEventTicketIsRequired="{{../attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{../attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../../instanceTitle}} - Ticket {{../seatNum}}" data-MCEventTicketFieldDesc="{{../attributes.fieldText}}" class="MCTicketField"> {{attributes.fieldValue}}
																			{{##unless unavailable}}
																				{{##compare attributes.amount '>' 0}}
																					(+ {{dispAmount}}{{../displayedCurrencyType}})
																				{{/compare}}
																			{{/unless}}
																			{{##if unavailable}}
																				&nbsp; [SOLD OUT]
																			{{/if}}
																			{{##if ../supportAmt}}
																				{{##compare actualFee '!=' ''}}
																				{{##compare ../value '==' attributes.valueID}}
																				{{##compare attributes.amount '!=' actualFee}}
																					<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{dispActualFee}}{{../displayedCurrencyType}}</span>
																				{{/compare}}
																				{{/compare}}
																				{{/compare}}
																			{{/if}}
																		</label>
																	{{/if}}
																{{/each}}
															</div>
														{{/compare}}
														<!--- Checkboxes --->
														{{##compare displayTypeCode '==' "CHECKBOX"}}
															<div style="padding:6px 0 6px 20px;">
																{{##each children}}
																	{{##if ../displayOnly}}
																		{{##each ../value}}
																		{{##compare this '==' ../attributes.valueID}}
																			{{../../attributes.fieldValue}}<br/>
																		{{/compare}}
																		{{/each}}
																	{{else}}
																		<label class="checkbox">
																			<input type="checkbox" id='ev_tc_{{../ticketPackageID}}_{{../ticketID}}_{{../fieldID}}_{{../seatNum}}_{{../../../../instanceNum}}' name='ev_tc_{{../ticketPackageID}}_{{../ticketID}}_{{../fieldID}}_{{../seatNum}}_{{../../../../instanceNum}}' value="{{attributes.valueID}}" {{##if unavailable}}disabled{{/if}} data-MCEventTicketDisplayTypeCode="{{../displayTypeCode}}" data-MCEventTicketDataTypeCode="{{../dataTypeCode}}" data-MCEventTicketIsRequired="{{../attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{../attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../../instanceTitle}} - Ticket {{../seatNum}}" data-MCEventTicketFieldDesc="{{../attributes.fieldText}}" {{##each ../value}}{{##compare this '==' ../attributes.valueID}}checked{{/compare}}{{/each}} class="MCTicketField"> {{attributes.fieldValue}}
																			{{##unless unavailable}}
																				{{##compare attributes.amount '>' 0}}
																					(+ {{dispAmount}}{{../displayedCurrencyType}})
																				{{/compare}}
																			{{/unless}}
																			{{##if unavailable}}
																				&nbsp; [SOLD OUT]
																			{{/if}}
																			{{##if ../supportAmt}}
																				{{##each ../value}}
																					{{##compare ../../actualFee '!=' ''}}
																					{{##compare this '==' ../attributes.valueID}} 
																					{{##compare ../../attributes.amount '!=' ../../actualFee}} 
																						<span class="mcfieldfee" style="margin-left:20px;">Current fee is {{../../dispActualFee}}{{../../../displayedCurrencyType}}</span>
																					{{/compare}}
																					{{/compare}}
																					{{/compare}}
																				{{/each}}
																			{{/if}}
																		</label>
																	{{/if}}
																{{/each}}
															</div>
														{{/compare}}
														<!--- Date --->
														{{##compare displayTypeCode '==' "DATE"}}
															<div style="padding:6px 0 6px 20px;">
																{{##if displayOnly}}
																	{{value}}
																{{else}}
																	<div class="evreg-d-flex">
																		<div class="evreg-input-append evreg-col-auto">
																			<input type="text" size="16" autocomplete="off" id='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}" value="{{value}}" class="evreg-formcontrol dateControl MCTicketField MCAdminDateControl">
																			<span class="evreg-add-on evreg-cursor-pointer calendar-button" data-target="ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}"><i class="icon-calendar-empty"></i></span>
																			<span class="evreg-add-on">
																				<a href="##" class="MCAdminDateControlClearLink evreg-p-0 evreg-text-decoration-none evreg-text-danger" data-linkedDateControl="ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}"><i class="icon-remove"></i></a>
																			</span>
																		</div>
																	</div>
																{{/if}}
															</div>
														{{/compare}}
														<!--- Textarea --->
														{{##compare displayTypeCode '==' "TEXTAREA"}}
															<div style="padding:6px 0 6px 20px;">
																{{##if displayOnly}}
																	{{value}}
																{{else}}
																	<textarea cols="62" rows="5" id='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' name='ev_tc_{{ticketPackageID}}_{{ticketID}}_{{fieldID}}_{{seatNum}}_{{../../../instanceNum}}' class="tsAppBodyText MCTicketField" data-MCEventTicketDisplayTypeCode="{{displayTypeCode}}" data-MCEventTicketDataTypeCode="{{dataTypeCode}}" data-MCEventTicketIsRequired="{{attributes.isRequired}}" data-MCEventTicketRequiredMsg="{{attributes.requiredMsg}}" data-MCEventTicketInstanceTitle="{{../../instanceTitle}} - Ticket {{seatNum}}" data-MCEventTicketFieldDesc="{{attributes.fieldText}}">{{value}}</textarea>
																{{/if}}
															</div>
														{{/compare}}
													</div>
												</div>
											{{/unless}}
										{{/each}}

										{{##compare fieldGroupingID '>' 0}}
												</fieldset>
											</div>
										{{/compare}}
									{{/each}}
								</div>
							{{/if}}
						</div>
					{{/each}}
				{{/if}}
			</div>
		{{/each}}
	{{/if}}
</script>
</cfoutput>