<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	<div style="padding-bottom:10px;">
		<cfif local.hasDepositionBucket>
			<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
		</cfif>
		<button class="btn" onclick="document.location.href='/?pg=listViewer&lsAction=listBrowse'"><i class="icon-envelope-alt"></i> Browse My Lists</button>
	</div>
</cfoutput>
<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#</cfoutput>
<cfelse>
	<cfoutput>#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='message', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#</cfoutput>
	<!--- results table --->
	<div class="hidden-phone">
		<table class="table table-striped">
			<tr>
				<td style="width:70px;"><b>Date</b></td>
				<td style="width:200px;"><b>From</b></td>
				<td style="width:102px;"><b>List</b></td>
				<td style="width:14px;"><b>Att</b></td>
				<td><b>Subject</b></td>
			</tr>
			<cfoutput query="local.qryResults">
				<cfset local.hdrSubject_ = local.qryResults.hdrSubject_>
				<cfif ReFindNoCase(application.regEx.encodedWord,local.hdrSubject_)>
					<cfset local.hdrSubject_ = application.objMimeUtility.decodeText(local.hdrSubject_)>
				</cfif>
				<cfif len(local.hdrSubject_) gt 45>
					<cfset local.hdrSubject_ = "#left(local.hdrSubject_,45)#...">
				</cfif>
				<tr>
					<td>#DateFormat(local.qryResults.creatstamp_,"m/d/yy")#</td>
					<td>#trim(replace(local.qryResults.hdrfrom_,"""","","all"))#</td>
					<td>#local.qryResults.list#</td>
					<td width="14"><cfif local.qryResults.attachmentflag is 1><i class="icon-paper-clip"></i></cfif></td>
					<td><a href="javascript:b_loadmsgjson(#local.qryResults.messageid_#,'','#arguments.viewDirectory#');">#local.hdrSubject_#</a></td>
				</tr>
			</cfoutput>
		</table>
	</div>
	<div class="visible-phone">
		<table class="table table-striped table-condensed lyrisMessageListMobile">
			<cfoutput query="local.qryResults">
				<cfset local.hdrSubject_ = local.qryResults.hdrSubject_>
				<cfif ReFindNoCase(application.regEx.encodedWord,local.hdrSubject_)>
					<cfset local.hdrSubject_ = application.objMimeUtility.decodeText(local.hdrSubject_)>
				</cfif>
				<cfif len(local.hdrSubject_) gt 45>
					<cfset local.hdrSubject_ = "#left(local.hdrSubject_,45)#...">
				</cfif>
				<tr>
					<td>
						<div>
							<span>#DateFormat(local.qryResults.creatstamp_,"m/d/yy")# on #local.qryResults.list#</span><br/>
							<span>#trim(replace(local.qryResults.hdrfrom_,"""","","all"))#</span><br/>
							<div>
								<cfif local.qryResults.attachmentflag is 1><i class="icon-paper-clip"></i></cfif>
								<span><a href="javascript:$('table.lyrisMessageListMobile').hide(); $('div##s_lyris_messagewindow,div##closeMessageMobile').show(); b_loadmsgjson(#local.qryResults.messageid_#,'','#arguments.viewDirectory#');">#local.hdrSubject_#</a></span>
							</div>
						</div>
					</td>
				</tr>
			</cfoutput>
		</table>
		<div id="closeMessageMobile" onclick="$('table.lyrisMessageListMobile').show(); $('div#s_lyris_messagewindow,div#closeMessageMobile').hide();" class="well well-small hide" style="margin-bottom:10px;">
			<strong><i class="icon-chevron-left"></i><span id="closeMessageText">Back to Messages</span></strong>
		</div>
	</div>
	<div id="s_lyris_messagewindow"></div>
	<iframe id="MCcontenttoprint" style="height:0px;width:0px;position:absolute;" frameborder="0"></iframe>
	<cfoutput>#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='message', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#</cfoutput>
</cfif>