<cfinclude template="../commonEventRegStep3JS.cfm">

<cfoutput>
<div class="evreg-card evreg-mt-3 evreg-p-3">
	<form name="frmEventRegStep3" id="frmEventRegStep3" autocomplete="off"<cfif local.evRegV2.currentReg.currentStep NEQ 3> style="display:none;"</cfif>>		
		<div class="evreg-mb-3 evreg-font-size-lg evreg-font-weight-bold">Registrant Details:</div>
		
		<!--- prompt for missing tax information --->
		<cfif local.evRegV2.currentReg.s3.stateIDforTax EQ 0 OR NOT len(local.evRegV2.currentReg.s3.zipForTax)>
			<div class="evreg-d-flex evreg-mb-3">
				<div class="evreg-col">
					<div class="evreg-font-size-md evreg-font-weight-bold">State/Province <span class="evreg-text-danger">*</span></div>
					<cfset local.qryStates = application.objCommon.getStates()>
					<select id="stateIDforTax" name="stateIDforTax" class="evreg-formcontrol evreg-w-100" data-displayTypeCode="SELECT" data-isRequired="1" data-requiredmsg="State/Province is required.">
						<option value=""></option>
						<cfset local.currentCountryID = 0>
						<cfloop query="local.qryStates">
							<cfif local.qryStates.countryID neq local.currentCountryID>
								<cfset local.currentCountryID = local.qryStates.countryID>
								<optgroup label="#local.qryStates.country#">
							</cfif>
							<option value="#local.qryStates.stateID#" <cfif local.evRegV2.currentReg.s3.stateIDforTax is local.qryStates.stateID>selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
							<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
								</optgroup>
							</cfif>
						</cfloop>
					</select>
					<div id="stateIDforTax_err" class="evreg-font-size-sm evreg-text-danger" style="display:none;"></div>
				</div>
				<div class="evreg-col">
					<div class="evreg-font-size-md evreg-font-weight-bold">Postal Code <span class="evreg-text-danger">*</span></div>
					<input type="text" id="zipForTax" name="zipForTax" class="evreg-formcontrol evreg-w-100" maxlength="25" value="#local.evRegV2.currentReg.s3.zipForTax#" data-displayTypeCode="TEXTBOX" data-isRequired="1" data-requiredmsg="Postal Code is required.">
					<div id="zipForTax_err" class="evreg-font-size-sm evreg-text-danger" style="display:none;"></div>
				</div>
			</div>
		<cfelse>
			<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.evRegV2.currentReg.s3.stateIDforTax#">
			<input type="hidden" id="zipForTax" name="zipForTax" value="#local.evRegV2.currentReg.s3.zipForTax#">
		</cfif>

		<cfif NOT len(local.mainEmail)>
			<div class="evreg-d-flex evreg-mb-3">
				<div class="evreg-col">
					<div class="evreg-font-size-md evreg-font-weight-bold">Specify an email address for a confirmation email that will be sent with important registration information. <span class="evreg-text-danger">*</span></div>
					<input type="text" id="regEmail" name="regEmail" class="evreg-formcontrol span6" value="#local.evRegV2.currentReg.s3.email#" maxlength="200">
					<div id="regEmail_err" class="evreg-font-size-sm evreg-text-danger" style="display:none;"></div>
				</div>
			</div>
		</cfif>

		<!--- event specific custom fields --->
		<cfif local.strEventSpecificFields.hasFields>
			#local.strEventSpecificFields.html#
		</cfif>

		<div class="evreg-mt-5">
			<div id="step3Err" class="alert alert-danger" style="display:none;"></div>
			<button type="button" name="btnSaveStep3" id="btnSaveStep3" class="btn btn-success" onclick="doS3ValidateAndSave();">
				<cfif local.evRegV2.currentReg.isRegCartItem EQ 1 OR local.evRegV2.currentReg.registrantID GT 0>
					Save Changes
				<cfelse>
					<i class="icon-arrow-right"></i> Continue
				</cfif>
			</button>
		</div>
		<div id="evRegStep3SaveLoading" style="display:none;"></div>
	</form>
	<div id="evRegStep3Summary" class="evRegStepSummary evreg-cursor-pointer" data-evregsummarystep="3"<cfif local.evRegV2.currentReg.currentStep EQ 3> style="display:none;"</cfif>>
		<div class="evreg-d-flex">
			<a href="##" class="evreg-align-self-center evreg-mr-2 evreg-font-size-lg evreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="evreg-col">
				<div class="evreg-font-size-lg evreg-font-weight-bold">Registrant Details</div>
			</div>
			<div id="evRegStep3TotalPriceDisplay" class="evreg-ml-auto evreg-font-size-lg evreg-font-weight-bold">#dollarFormat(val(local.qryS3Totals.totalAmount))##local.displayedCurrencyType#</div>
		</div>
	</div>
</div>
</cfoutput>