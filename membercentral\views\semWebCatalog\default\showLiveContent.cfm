<cfset local.strSeminar = attributes.data.strSeminar>
<cfset local.strCredit = attributes.data.strCredit>
<cfset local.JSStructCreditInfo = attributes.data.JSStructCreditInfo>
<cfset local.qryBundles = attributes.data.qryBundles>
<cfset local.seminarSuggestions = attributes.data.seminarSuggestions>
<cfset local.speakerBio = attributes.data.speakerBio>
<cfset local.qrySWP = attributes.data.qrySWP>
<cfset local.semWeb = attributes.data.semWeb>
<cfset local.seminarID = attributes.data.seminarID>
<cfset local.isRegOpen = attributes.data.isRegOpen>

<cfset local.featuredThumbImageFullRootPath = attributes.data.featuredThumbImageFullRootPath>
<cfset local.featuredThumbImageRootPath = attributes.data.featuredThumbImageRootPath>
<cfset local.programFeaturedImagePath = "">
<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.strSeminar.qrySeminar.featureImageID) AND fileExists("#local.featuredThumbImageFullRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#")>
	<cfset local.programFeaturedImagePath = "#local.featuredThumbImageRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#">
<cfelseif len(attributes.data.defaultFeaturedImagePathsStr.defaultSWLFeaturedImagePath)>
	<cfset local.programFeaturedImagePath = attributes.data.defaultFeaturedImagePathsStr.defaultSWLFeaturedImagePath>
</cfif>
<cfif isDefined("session.mcstruct.deviceProfile.is_bot") AND session.mcstruct.deviceProfile.is_bot is 1>
	<cfset local.isBot = 1>
<cfelse>
	<cfset local.isBot = 0>
</cfif>
<cfsavecontent variable="local.liveContentJS">
	<cfoutput>
	<script language="javascript">
		<cfif local.isRegOpen and NOT local.isBot>
			function enrollNow() { self.location.href='#attributes.event.getValue('mainurl')#&panel=reg&item=SWL-#local.strSeminar.qrySeminar.seminarID#'; }
		</cfif>
		function showCreditDetail(aID) { document.getElementById('creditdetailbox').innerHTML = eval('credit_a' + aID); return false; }
		<cfloop query="local.strCredit.qryCredit">
			var #ToScript(local.JSStructCreditInfo[local.strCredit.qryCredit.authorityID],"credit_a#local.strCredit.qryCredit.authorityID#")#
		</cfloop>
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.liveContentJS#">

<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#: #encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarSubTitle)#"'>
<cfelse>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#"'>
</cfif>
<cfif len(local.programFeaturedImagePath)>
	<cfset local.shareThisTitleAndImage = '#local.shareThisTitleAndImage# st_image="#local.semweb.baseurl##local.programFeaturedImagePath#"'>
</cfif>

<cfoutput>
<!--- content --->
<div id="sw_listing" class="tsAppBodyText" align="center">
	<div style="width:95%;" align="left">
		<div class="headersection sw-d-flex">
			<cfif len(local.programFeaturedImagePath)>
				<div id="headersectionimage"><img src="#local.programFeaturedImagePath#" style="width:200px;height:200px;" alt=""></div>
			</cfif>
			<div id="headersectiontitle">
				<div class="swWebinar sw-text-uppercase"><i class="bi bi-laptop" aria-hidden="true"></i> #attributes.data.qrySWP.brandSWLTab#</div>
				<!--- title --->
				<h2 id="programtitle" class="swPrimary sw-mt-1">
					#encodeForHTML(local.strSeminar.qrySeminar.seminarName)#
					<cfif NOT local.isBot>
						<a href="javascript:void(0);" class="swHeartIcon swMuted">
							<i class="sw_saveforlater bi <cfif attributes.data.isSavedProgram>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.strSeminar.qrySeminar.seminarID#" data-swprogramtype="swl" data-swsaveforlatermode="detail"></i>
						</a>
					</cfif>
				</h2>
				<cfif len(local.strSeminar.qrySeminar.seminarSubTitle) OR local.semWeb.sitecode neq "TS">
					<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
						<h4 id="programsubtitle" class="swPrimary">#encodeForHTML(local.strSeminar.qrySeminar.seminarSubTitle)#</h4>
					</cfif>
					<cfif local.semWeb.sitecode neq "TS">
						<div>
							Published by #local.strSeminar.qrySeminar.description#
							<cfif local.semWeb.orgname neq local.strSeminar.qrySeminar.description><br/>and #local.semWeb.orgname#</cfif>
						</div>
					</cfif>
				</cfif>

				<cfif local.strSeminar.qryLearningObjectives.recordCount>
					<div class="tsAppBodyText sw-mt-3">
						<b>#attributes.data.semWeb.qrySWP.brandLearnObjectives#</b><br/>
						<div style="margin:2px 20px 2px 0;">
							<ul class="learningobjectives sw-mt-2">
								<cfloop query="local.strSeminar.qryLearningObjectives">
									<li>#local.strSeminar.qryLearningObjectives.objective#</li>
								</cfloop>
							</ul>
						</div>
						<br/>
					</div>
				</cfif>
			</div>
		</div>
		<br clear="all">
		<br/>
	
		<div id="actionswrapper" class="tsAppBodyText">
			<cfif attributes.data.hasPendingRegistrations and NOT local.isBot>
				<div style="padding:3px;">
					<div class="tsAppBodyTextImportant">Reminder:</div>
					You have <a href="#attributes.event.getValue('mainurl')#&panel=showCart">pending registrations</a>.
				</div>
			</cfif>
			<div class="sidebox">
				<div class="sideboxtitle">Registration</div>
				<div class="sideboxbody">
					<cfif NOT local.isBot and now() gte local.strSeminar.qrySeminar.dateStart AND local.strSeminar.qrySeminar.linkedSWODSeminarID gt 0 AND NOT local.strSeminar.qrySeminar.isRegisteredForLinkedSWOD>
						<b>Missed the program?</b><br/>
						<a href="/?pg=semwebCatalog&panel=showSWOD&seminarid=#local.strSeminar.qrySeminar.linkedSWODSeminarID#" class="swPrimary tsAppBodyText" target="_blank">Register</a> for the on-demand version.<br/><br/>
					</cfif>
					Schedule:<br/>
					#DateFormat(local.strSeminar.qrySeminar.dspStartDate,'dddd, mmmm d, yyyy')#<br/><br/>
					#replace(TimeFormat(local.strSeminar.qrySeminar.dspStartDate,'h:mm TT'),":00 ","")# #UCASE(local.strSeminar.qrySeminar.dspTZ)#<br/>
					#local.strSeminar.qrySeminar.dspTZStr#<br/>
					Duration: #dateDiff("n",local.strSeminar.qrySeminar.dspStartDate,local.strSeminar.qrySeminar.dspendDate)# minutes<br/><br/><br/>
	
					<cfloop query="local.strSeminar.qrySeminarPrices">
						<cfif local.strSeminar.qrySeminarPrices.price gte 0>
							<cfif local.strSeminar.qrySeminarPrices.price is 0>
								#replace(local.strSeminar.qrySeminar.freeRateDisplay,".00","")#
							<cfelseif local.strSeminar.qrySeminarPrices.price gt 0>
								#replace(dollarformat(local.strSeminar.qrySeminarPrices.price),".00","")#<cfif local.strSeminar.qrySeminar.showUSD is 1> USD</cfif>
							</cfif>
							<cfif len(local.strSeminar.qrySeminarPrices.description)> <cfif local.strSeminar.qrySeminarPrices.price gt 0 or len(local.strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.strSeminar.qrySeminarPrices.description#</cfif><br/>
						</cfif>
					</cfloop>
	
					<cfif local.strSeminar.qrySeminarPrices.recordcount>
						<br/>
					</cfif>

					<cfif NOT local.isBot>
						<cfif local.isRegOpen and local.strSeminar.qrySeminar.isRegistered>
							<a href="javascript:enrollNow();" class="swPrimary tsAppBodyText">Register a colleague</a> for this program.<br/>
						<cfelseif local.strSeminar.qrySeminarPrices.recordcount is 0 or (local.strSeminar.qrySeminarPrices.recordcount is 1 and local.strSeminar.qrySeminarPrices.price lt 0)>
							<p class="text-center">This program is not available for purchase at this time. If you feel this is an error, contact customer service at <nobr>#local.qrySWP.supportPhone#</nobr>.</p>					
						<cfelseif local.isRegOpen>
							<div align="center" style="margin-top:6px;"><button type="button" class="tsAppBodyButton" name="btnRegNow" <cfif NOT local.isBot> onClick="enrollNow();" </cfif>>Register Now</button></div>
						<cfelse>
							<b>Registration Closed</b><br/>
							<div>
							<span class="tsAppBodyTextImportant">This program is not accepting registrations at this time.</span>
							</div>
						</cfif>
						<cfif local.strSeminar.qrySeminar.isRegistered>
							<br/>
							<span class="tsAppBodyTextImportant">You are registered for this program. Refer to confirmation e-mail for connection instructions.</span>
						<cfelse>
							<cfif attributes.data.memberID is 0>
								<br/>
								<b>Already Registered?</b><br/>
								Refer to confirmation e-mail for connection instructions.
								<br/>
							</cfif>
						</cfif>
					</cfif>
				</div>
			</div>
	
			<cfif local.qryBundles.recordcount>
				<br/>
				<div class="sidebox">
					<div class="sideboxtitle">Bundles Available</div>
					<div class="sideboxbody">
						This program is also offered in the following bundle<cfif local.qryBundles.recordcount gt 1>s</cfif>:<br/>
						<table>
						<cfloop query="local.qryBundles">
							<tr valign="top"><td><li></td><td><a href="/?pg=semwebCatalog&panel=showBundle&bundleID=#local.qryBundles.bundleID#" class="swPrimary tsAppBodyText">#local.qryBundles.bundleName#</a></td></tr>
						</cfloop>
						</table>
					</div>
				</div>
			</cfif>
	
			<br/>
			<div class="sidebox">
				<div class="sideboxtitle">Tell a Colleague!</div>
				<div class="sideboxbody">
					<span class='st_facebook_large' #local.shareThisTitleAndImage#></span>
					<span class='st_linkedin_large' #local.shareThisTitleAndImage#></span>
					<span class='st_email_large' #local.shareThisTitleAndImage# st_summary="Check out this program offered by #local.semWeb.qrySWP.description#"></span>
					<span class='st_twitter_large' #local.shareThisTitleAndImage#></span>
				</div>
			</div>
			<br/>

			<div class="sidebox addtomycalendar">
				<div class="sideboxtitle">Add to My Calendar</div>
				<div class="sideboxbody">
					<cfoutput>
						<div>
							<a title="Download webinar to your calendar" 
								style="text-decoration:none!important;font-weight:inherit!important;" 
								href="/?pg=semwebCatalog&panel=downloadICal&seminarid=#local.strSeminar.qrySeminar.seminarID#&frmView=1&mode=stream">
								<i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp; Outlook Calendar</a>
						</div>
						<div>
							<a title="Add webinar to your Google calendar" 
								target="_blank" 
								style="text-decoration:none!important;font-weight:inherit!important;" 
								href="/?pg=semwebCatalog&panel=downloadGCal&seminarid=#local.strSeminar.qrySeminar.seminarID#&frmView=1&mode=stream">
								<i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp; Google Calendar</a>
						</div>
						<div>
							<a title="Download webinar to your calendar" 
								style="text-decoration:none!important;font-weight:inherit!important;" 
								href="/?pg=semwebCatalog&panel=downloadICal&seminarid=#local.strSeminar.qrySeminar.seminarID#&frmView=1&mode=stream">
								<i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp; iCal Calendar</a>
						</div>
					</cfoutput>
					<br/>
					<div><em>Reminder: Adding to your calendar does not register you for the webinar.</em></div>
				</div>
			</div>
			
			<br/>
			<div class="sidebox">
				<div class="sideboxtitle">Questions?</div>
				<div class="sideboxbody">
					For immediate assistance please consult our <a href="/?pg=semwebCatalog&panel=showFAQ" class="swPrimary tsAppBodyText">FAQ page</a>. 
					<br/><br/>
					If you're unable to find the answer you need, please call #local.qrySWP.supportPhone# (#local.qrySWP.supportHours#) or <a href="mailto:#local.qrySWP.supportEmail#" class="swPrimary tsAppBodyText">e-mail customer service</a>
				</div>
			</div>
		</div>

		<div class="tsAppBodyText">
			<h5 class="swPrimary">Summary</h5>
			<div style="margin:2px 20px 2px 0;">#local.strSeminar.qrySeminar.SeminarDesc#</div>
			<div class="swMuted" style="margin-bottom:2px"><strong>#local.strSeminar.qrySeminar.programCode#</strong></div>
			<br/>
		</div>
		<cfif local.strSeminar.qrySponsors.recordCount>
			<cfset local.qrySponsors = local.strSeminar.qrySponsors>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Sponsors</h5>
				<div style="margin:10px 20px 2px 0;">
					<cfinclude template="/views/semwebCatalog/default/swProgramDetailsSpeakersCommon.cfm">
				</div>
				<br/>
			</div>
		</cfif>
		<cfif len(local.speakerBio)>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Presenters</h5>
				<div style="margin:2px 20px 2px 0;">#local.speakerBio#</div>
				<br/>
			</div>	
		</cfif>
		<cfif len(trim(local.strSeminar.qrySeminar.agenda))>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Program Agenda</h5>
				<div style="margin:2px 20px 2px 0;">
					#replace(local.strSeminar.qrySeminar.agenda,"#chr(13)##chr(10)#","<br/>","ALL")#
				</div>
				<br/>
			</div>
		</cfif>
		
		<div class="tsAppBodyText">
			<h5 class="swPrimary">How To Attend</h5>
			<div style="margin:2px 20px 2px 0;">
				Join the live program from your office, home, or hotel room using a computer with high speed internet. 
				You may ask questions, participate in evaluations, and post comments from your computer during the program. 
				Please note that credit (if available) is only provided to registered attendees participating at their 
				own computer and phone. Simple instructions with a link to the program will be sent when you register 
				and again the day before the webinar. 
			</div>
			<br/>
		</div>
		<cfif local.strSeminar.qrySeminar.offerCredit>
			<div class="tsAppBodyText">
				<h5 id="creditSection" class="swPrimary">Credit</h5>
				<div style="margin:2px 20px 2px 0;">
					<cfif local.strCredit.qryCreditDistinct.recordcount>
						If applicable, you may obtain credit in multiple jurisdictions simultaneously for this program (see pending/approved list below). 
						<cfif local.strSeminar.qrySeminar.offerCertificate>
							Registrants in jurisdictions not listed below will receive a Certificate of Attendance/Completion that may or may 
							not meet credit requirements in other jurisdictions. 
						</cfif>
						Where applicable, credit will be only awarded to a paid registrant attending the live program at their own computer and phone. 
						<br/><br/>
						<div style="border:1px solid ##DEDEDE;padding:0px;margin-left:20px;width:70%;">
							<span style="background-color:##DEDEDE;padding:2px;">&nbsp;<b>Click on jurisdiction for specific details</b>&nbsp;&nbsp;</span>
							<br/>
							<div style="padding:2px 20px 0 20px;">
							<cfloop query="local.strCredit.qryCreditDistinct">
								<a href="" onClick="return showCreditDetail(#local.strCredit.qryCreditDistinct.authorityID#);" class="swPrimary tsAppBodyText">#local.strCredit.qryCreditDistinct.authorityCode#</a><cfif local.strCredit.qryCreditDistinct.currentrow is not local.strCredit.qryCreditDistinct.recordcount>, </cfif>
							</cfloop>
							</div>
							<br/>
							<div style="padding:2px 20px 0 20px;" id="creditdetailbox"></div>
						</div>
					<cfelse>
						This program <i>has not been submitted</i> for credit in any jurisdiction. 
						<cfif local.strSeminar.qrySeminar.offerCertificate>
							Registrants will receive a Certificate of Attendance/Completion that may or may not meet credit 
							requirements in various jurisdictions. 
						</cfif>
						Where applicable, credit will be only awarded to a paid registrant attending the live program at their 
						own computer and phone.
					</cfif>
				</div>
				<br/>
			</div>
		</cfif>
	
		<cfif local.qrySWP.handlesOwnPayment is 0>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Refund Policy</h5>
				<div style="margin:2px 20px 2px 0;">
					SeminarWeb and #local.semWeb.orgname# programs are non-refundable. 
				</div>
				<br/>
			</div>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Privacy Statement</h5>
				<div style="margin:2px 20px 2px 0;">
					We respect and are committed to protecting your privacy. (<a href="javascript:sw_showPrivacyStatement();" class="swPrimary tsAppBodyText">Read Statement</a>) 
				</div>
				<br/>
			</div>
		</cfif>

		<cfif local.seminarSuggestions.recordcount>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">You May Also Be Interested In...</h5>
				<div style="margin:2px 20px 2px 0;">
					<cfloop query="local.seminarSuggestions">
						<cfset local.otherPgmsFeaturedThumbImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
						<cfset local.otherPgmsFeaturedThumbImageRootPath = "/userassets/#LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
						<cfset local.thisProgramFeaturedImagePath = "">
						<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.seminarSuggestions.featureImageID) AND fileExists("#local.otherPgmsFeaturedThumbImageFullRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#")>
							<cfset local.thisProgramFeaturedImagePath = "#local.otherPgmsFeaturedThumbImageRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#">
						<cfelseif local.seminarSuggestions.seminarType eq "On-Demand" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath)>
							<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath>
						<cfelseif local.seminarSuggestions.seminarType eq "Webinar" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath)>
							<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath>
						</cfif>
						<cfif local.seminarSuggestions.seminarType eq "On-Demand">
							<cfset local.programLink = "?pg=semwebCatalog&panel=showSWOD&seminarid=#local.seminarSuggestions.seminarID#">
						<cfelse>
							<cfset local.programLink = "?pg=semwebCatalog&panel=showLive&seminarid=#local.seminarSuggestions.seminarID#">
						</cfif>

						<div class="sw-d-flex sw-mb-3">
							<cfif len(local.thisProgramFeaturedImagePath)>
								<div class="sw-mr-2">
									<img src="#local.thisProgramFeaturedImagePath#" style="width:85px;height:85px;" alt="">
								</div>
							</cfif>
							<div>
								<div class="sw-d-flex">
									<cfif local.seminarSuggestions.seminarType eq "On-Demand">
										<div class="swOnDemand sw-font-size-sm sw-text-uppercase"><i class="zz" aria-hidden="true"></i> #attributes.data.qrySWP.brandSWODTab#</div>
									<cfelse>
										<div class="swWebinar sw-font-size-sm sw-text-uppercase"><i class="bi bi-laptop" aria-hidden="true"></i> #attributes.data.qrySWP.brandSWLTab#</div>
									</cfif>
									<span class="sw-font-size-sm sw-ml-3" title="<cfif local.seminarSuggestions.seminarType eq "On-Demand">Published on<cfelse>Being Held On </cfif> #dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#">
										<i class="bi bi-calendar-fill" aria-hidden="true"></i> #dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#
									</span>
								</div>
								<div class="sw-mt-1">
									<b>
										<a href="#local.programLink#" class="swPrimary tsAppBodyText" style="text-decoration:none;" target="_blank">
											#encodeForHTML(local.seminarSuggestions.seminarName)#<cfif len(local.seminarSuggestions.seminarSubTitle)>: #encodeForHTML(local.seminarSuggestions.seminarSubTitle)#</cfif>
										</a>
									</b>
								</div>
							</div>
						</div>
					</cfloop>
				</div>
			</div>
		</cfif>

		<br clear="all">
	</div>
</div>
</cfoutput>