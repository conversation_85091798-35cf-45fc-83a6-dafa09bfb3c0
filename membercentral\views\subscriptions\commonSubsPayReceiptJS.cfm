<cfsavecontent variable="local.payReceiptJS">
	<cfoutput>
	<script type="text/javascript">
        function viewInvoice(i) {
			self.location.href = '/?pg=invoices&va=show&item=' + escape(i) + '&mode=stream';
		}
		function downloadPaymentReceipt() {
			$('##mcsubs_download_receiptlink').addClass('mcsubs-d-none');
			$('##mcsubs_download_receiptLoading')
				.html('<i class="icon-spin icon-spinner mcsubs-font-size-sm mcsubs-mr-1"></i> Please wait...')
				.load('/?pg=manageSubscriptions&suba=downloadReceipt&rk=#arguments.event.getTrimValue('rk','')#&mode=stream')
				.removeClass('mcsubs-d-none');
		}
		function doDownloadPaymentReceipt(u) {
			self.location.href = '/tsdd/' + u;
			window.setTimeout(function() { 
				$('##mcsubs_download_receiptLoading').addClass('mcsubs-d-none');
				$('##mcsubs_download_receiptlink').removeClass('mcsubs-d-none');
			},2000);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.payReceiptJS)#">