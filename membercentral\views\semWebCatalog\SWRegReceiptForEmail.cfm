<cfoutput>
<table style="border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
<tr>
	<td style="font:bold 15px Verdana,Helvetica,Arial,sans-serif;padding:6px;color:##333;" colspan="2">Registration(s)</td>
	<td style="font:bold 15px Verdana,Helvetica,Arial,sans-serif;padding:6px;color:##333;text-align:right;">Total</td>
</tr>
</table>
<br/>
<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
</cfoutput>

<cfset local.recCountInd = 0>
<cfoutput query="local.qryItemsForReceiptSorted" group="memberID">
	<tr>
		<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;" colspan="3">
			#local.qryItemsForReceiptSorted.memberName#
		</td>
	</tr>
	<cfoutput>
		<cfset local.recCountInd++>
		<cfset local.swItemType = getToken(local.qryItemsForReceiptSorted.item,1,'-')>
		<cfset local.strThisProgram = duplicate(local.strRegProgram[local.qryItemsForReceiptSorted.item])>
		<tr>
			<td style="padding:6px" width="10" valign="top">
				#local.recCountInd#.
			</td>
			<td style="padding:6px">
				<b>#encodeForHTML(local.strThisProgram.programName)#</b><br/>
				<cfif len(local.strThisProgram.programSubTitle)><span style="color:##999"><b>#encodeForHTML(local.qryItemsForReceiptSorted.programSubTitle)#</b></span><br/></cfif>
				<cfif local.swItemType EQ 'SWL'>
					#DateFormat(local.strThisProgram.StartDate,"dddd, mmmm d, yyyy")#<br/>
					#replace(TimeFormat(local.strThisProgram.startDate,"h:mm TT"),":00","")#-#replace(TimeFormat(local.strThisProgram.enddate,"h:mm TT"),":00","")# #ucase(local.strThisProgram.tz)# #local.strThisProgram.tzstr#<br/>
				</cfif>
			</td>
			<td align="right" style="padding: 6px" valign="top">
				<cfif local.swItemType EQ 'SWL'>
					<nobr><a title="Download event to your calendar" style="text-decoration:none;" target="_blank" href="#local.strThisProgram.icalURL#"><i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp;Outlook</a></nobr>
					&nbsp;
					<nobr><a title="Add event to your Google calendar" style="text-decoration:none;" target="_blank" href="#local.strThisProgram.gcalURL#"><i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp;Google</a></nobr>
					&nbsp;
					<nobr><a title="Download event to your calendar" style="text-decoration:none;" target="_blank" href="#local.strThisProgram.icalURL#"><i class="icon-calendar" style="vertical-align:middle;"></i>&nbsp;iCal</a></nobr>
					<br/>
				</cfif>
				<b>
					<cfif local.qryItemsForReceiptSorted.amount eq 0>
						#local.strThisProgram.freeRateDisplay#
					<cfelse>
						#dollarFormat(local.qryItemsForReceiptSorted.discountAppliedTotal)##local.displayedCurrencyType#
					</cfif>
				</b>
			</td>
		</tr>
	</cfoutput>
</cfoutput>
<cfoutput>
</table>
<br/><br/>
<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid ##999;border-collapse:collapse;">
<tr bgcolor="##DEDEDE">
	<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;"><b>Payment</b></td>
</tr>	
<tr valign="top">
	<td style="padding:6px;border-bottom:1px solid ##ccc;">
		<cfif local.totalAmountOnReceipt.totalAmount gt 0 and local.strRegReceipt.qryPaymentTransaction.recordcount gt 0>
			#dollarFormat(local.strRegReceipt.qryPaymentTransaction.amount)# #local.strRegReceipt.qryPaymentTransaction.detail#<br/>
			Payment Date: #dateformat(local.strRegReceipt.qryPaymentTransaction.transactionDate,"m/d/yyyy")# #timeformat(local.strRegReceipt.qryPaymentTransaction.transactionDate,"h:mm tt")#
		<cfelseif local.strRegReceipt.qryPaymentTransaction.recordcount is 0 and local.strRegReceipt.qryPaymentGateway.gatewayID is 11>
			No payment was made.<br/><br/>
			<b>Payment instructions:</b><br/>
			<cfif len(local.strRegReceipt.qryPaymentGateway.paymentInstructions)>
				#local.strRegReceipt.qryPaymentGateway.paymentInstructions#
			<cfelse>
				No instructions have been provided. Contact the association for payment instructions.
			</cfif>
		<cfelseif local.totalAmountOnReceipt.totalAmount gt 0 and local.strRegReceipt.qryPaymentTransaction.recordcount is 0>
			No payment was made.
		<cfelse>
			No payment was due.
		</cfif>
		<br/><br/>
		#local.strReceipt.PurchaserName#<br/>
		<cfif len(local.strReceipt.PurchaserCompany)>#local.strReceipt.PurchaserCompany#<br/></cfif>
		#local.strReceipt.PurchaserAddress#
	</td>
</tr>
</table>
</cfoutput>