<cfcomponent extends="model.customPage.customPage" output="false">
	<cfset defaultEvent = "controller">
	<cfset defaultWidgetEvent = "widgetcontroller">
	
	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">
		<cfscript>
			variables.applicationReservedURLParams = "action,message,mode";
			variables.settings = structNew();
			arguments.event.paramValue('action','default');
		</cfscript>
	</cffunction> 
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			init(arguments.Event);
			// --------------------------------------------------------------------------------------------
			variables.applicationReservedURLParams 	= "action";
			local.customPage.baseURL								= "/?#getBaseQueryString(false)#";
			
			local.link.form				= '#local.customPage.baseURL#&action=form';
			local.link.results			= '#local.customPage.baseURL#&action=results';
			local.link.admin			= '#local.customPage.baseURL#&action=admin';
			local.link.addRate			= '#local.customPage.baseURL#&action=addRate';
			local.link.grid				= '#local.customPage.baseURL#&action=grid';
			// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
			local.Organization			= event.getValue('mc_siteInfo.ORGShortName');
			local.formName				= 'frmConductCalc';
			local.formNameDisplay		= 'Pre-judgment Calculator Form';
			
			local.orgID 				= event.getValue('mc_siteInfo.orgID');
			local.siteID 				= event.getValue('mc_siteInfo.siteID');
			local.statsSessionID 		= session.cfcUser.statsSessionID;
			local.objUtilities = CreateObject('component','sitecomponents.SK.SK.custom.customUtilities');
			// EMAIL SETTINGS: =======================================================================================================
			// DEFAULT ASSOCIATION EMAIL: ------------------------------------------------------------------------------------------
			local.ORGEmail 				= structNew();
			local.ORGEmail.TYPE			= 'text';
			local.ORGEmail.SUBJECT		= '#local.Organization# - #local.formNameDisplay# issue with dates';
			// ASSOCIATION EMAIL: --------------------------------------------------------------------------------------------------
			local.ORGEmail.FROM			= '<EMAIL>';//-: Who is the email to the ORG COMING FROM :-//
			local.ORGEmail.TO 			= '<EMAIL>';//-: Who is the email to the ORG GOING TO :-//			
		</cfscript>

		<cfif event.getValue('mode','') EQ 'STREAM'>
			<cfswitch expression="#arguments.event.getValue('action')#">
				<cfcase value="gridData">
					<cfset local.returnHTML =  local.objUtilities.gridXML(arguments.event) />
					<cfoutput>#local.returnHTML#</cfoutput>
					<cfabort>
				</cfcase>
			</cfswitch>
		<cfelse>
			
			<cfsavecontent variable="local.pageCSS"><cfinclude template="customFormStyles.cfm"></cfsavecontent>
			<cfsavecontent variable="local.pageJS"><cfinclude template="customFormJS.cfm"></cfsavecontent>
			
			<cfsavecontent variable="local.returnHTML">
				<cfoutput>
					
					#local.pageJS#
					#local.pageCSS#
					
					<div id="customPage">
						<div class="TitleText" style="padding-bottom:15px;">#local.formNameDisplay#</div>
						<cfswitch expression="#arguments.event.getValue('action')#">
							<cfcase value="results">
								<cfif NOT isDefined('judgmentAmount')>
									<cflocation url="#local.link.form#" addtoken="false" />
								</cfif>
								
								<cfif event.getValue('detailedReport', '') EQ "Detailed Report">
									<cfset event.setValue('lossIncurred', 'detailedReport') />
								</cfif>								
								<cfparam name="local.judgmentAmount" default="#ReReplaceNoCase(event.getValue('JUDGMENTAMOUNT'),'[^0-9\.\-]','','ALL')#">
								<cfparam name="local.dateLoss" default="#event.getValue('lossDate')#">
								<cfparam name="local.dateJudge" default="#event.getValue('judgeDate')#">
								<cfparam name="local.daysDifferent" default="#DateDiff('d', local.dateLoss, local.dateJudge)#">
								<cfparam name="local.totalQuarters" default="#DateDiff('m', local.dateLoss, local.dateJudge)/3#">
								<cfparam name="local.fromYear" default="#dateFormat(local.dateLoss,'yyyy')#">
								<cfparam name="local.fromQuarter" default="#quarter(local.dateLoss)#">
								<cfparam name="local.fromMonth" default="#dateFormat(local.dateLoss,'m')#">
								<cfparam name="local.fromDay" default="#dateFormat(local.dateLoss,'dd')#">
								<cfparam name="local.toYear" default="#dateFormat(local.dateJudge,'yyyy')#">
								<cfparam name="local.toQuarter" default="#quarter(local.dateJudge)#">
								<cfparam name="local.toMonth" default="#dateFormat(local.dateJudge,'m')#">
								<cfparam name="local.toDay" default="#dateFormat(local.dateJudge,'dd')#">
								<cfset local.link.detailedReport = "#local.link.results#&detailedReport=Detailed Report&judgmentAmount=#local.judgmentAmount#&lossDate=#local.dateLoss#&judgeDate=#local.dateJudge#">
								
								<cfscript>
									local.quarter.1.from.month 	= 1;
									local.quarter.1.from.day		= 1;
									local.quarter.1.from.date		= '1/1/';
				
									local.quarter.1.to.month 		= 3;
									local.quarter.1.to.day			= 31;
									local.quarter.1.to.date			= '3/31/';
									
									local.quarter.2.from.month 	= 4;
									local.quarter.2.from.day		= 1;
									local.quarter.2.from.date		= '4/1/';
				
									local.quarter.2.to.month 		= 6;
									local.quarter.2.to.day			= 30;
									local.quarter.2.to.date			= '6/30/';
									
									local.quarter.3.from.month 	= 7;
									local.quarter.3.from.day		= 1;
									local.quarter.3.from.date		= '7/1/';
				
									local.quarter.3.to.month 		= 9;
									local.quarter.3.to.day			= 30;
									local.quarter.3.to.date			= '9/30/';
									
									local.quarter.4.from.month 	= 10;
									local.quarter.4.from.day		= 1;
									local.quarter.4.from.date		= '10/1/';
				
									local.quarter.4.to.month 		= 12;
									local.quarter.4.to.day			= 31;
									local.quarter.4.to.date			= '12/31/';
								</cfscript>
								
								<cfset local.qryAll = getAllRates() />
								
								<cfquery dbtype="query" name="local.getFromRate">
									SELECT rate, rateQuarter, rateYear, rateID
									FROM [local].qryALL
									WHERE rateYear = #local.fromYear# AND rateQuarter = #local.fromQuarter#
								</cfquery>
								
								<cfquery dbtype="query" name="local.getToRate">
									SELECT rate, rateQuarter, rateYear, rateID
									FROM [local].qryALL
									WHERE rateYear = #local.toYear# AND rateQuarter = #local.toQuarter#
								</cfquery>
								<cfif local.getToRate.rateID NEQ "" and local.getFromRate.rateID NEQ "" >
									<cfset local.fromCombined = #local.fromYear# & #local.fromQuarter#>
									<cfset local.toCombined = #local.toYear# & #local.toQuarter#>
									<cfquery datasource="#application.dsn.customApps.dsn#" name="local.getRatesUsed">
										SELECT rateID,rate,rateYear,rateQuarter,modifiedBy,modifiedDate,isActive
										FROM dbo.SK_PreJudgementCalculator_Rates
										where cast(rateyear as varchar(10)) + cast(ratequarter as varchar(10)) >= '#local.fromCombined#'
										and cast(rateyear as varchar(10)) + cast(ratequarter as varchar(10)) <= '#local.toCombined#'										
										order by rateYear,rateQuarter
									</cfquery>									
									<cfset local.numOfRates = local.getRatesUsed.RecordCount>
									<cfset local.rateTotal = 0>
									<cfloop index="local.i" from="1" to="#local.numOfRates#">
										<cfset local.rateTotal = local.rateTotal + local.getRatesUsed.rate[local.i]>
									</cfloop>
									<cfset local.rateAvg = local.rateTotal / local.numOfRates>
								
									<cfset local.totalIntAmt = 0>
					
									<cfset local.judgeMentAmount = ReReplaceNoCase(event.getValue('JUDGMENTAMOUNT'),'[^0-9\.\-]','','ALL')>
									<cfset local.principalAmount = local.judgeMentAmount />
									<cfset local.totalAmount = 0>
									<cfset local.totalInt = 0>
									<cfset local.strPrincipalAmt = StructNew() />
									<cfset local.strDaysCount = StructNew() />
									<cfset local.strDetails = StructNew()>
									
									<cfloop query="local.getRatesUsed">
										<cfset local.strEntry = StructNew()>
										<cfset local.cYear = local.getRatesUsed.rateYear />
										<cfset local.cRate = local.getRatesUsed.rate / 100 />
					
										<cfswitch expression="#local.getRatesUsed.rateQuarter#">
											<cfcase value="1">
												<cfset local.fDate = createDate(local.cYear,1,1) />
												<cfset local.tDate = createDate(local.cYear,3,31) />
											</cfcase>
											
											<cfcase value="2">
												<cfset local.fDate = createDate(local.cYear,4,1) />
												<cfset local.tDate = createDate(local.cYear,6,30) />
											</cfcase>
											
											<cfcase value="3">
												<cfset local.fDate = createDate(local.cYear,7,1) />
												<cfset local.tDate = createDate(local.cYear,9,30) />
											</cfcase>
											
											<cfcase value="4">
												<cfset local.fDate = createDate(local.cYear,10,1) />
												<cfset local.tDate = createDate(local.cYear,12,31) />
											</cfcase>
										</cfswitch>
										
										<cfif event.getValue('lossDate') GTE local.fDate>
											<cfset local.fDate = event.getValue('lossDate') />
										</cfif>
										<cfif event.getValue('judgeDate') LTE local.tDate>
											<cfset local.tDate = event.getValue('judgeDate') />
										</cfif>
										<cfset local.strEntry.fDate = local.fDate />
										<cfset local.strEntry.tDate = local.tDate />
										
										<cfset local.cDays = dateDiff('d',local.fDate, local.tDate) />
										<cfif local.getRatesUsed.rateID neq local.getFromRate.rateID>
											<cfset local.cDays = local.cDays + 1 />
										</cfif>
										<cfif isLeapYear(Year(local.fDate)) eq true>
											<cfset local.cDays = local.cDays / 366 />
										<cfelse>
											<cfset local.cDays = local.cDays / 365 />
										</cfif>										
										
										<cfset local.strEntry.cDays = local.cDays />

										<cfset local.totalAmount = local.principalAmount * ( 1 + (local.cRate * local.cDays )) />

										<cfset local.strEntry.totalAmount = local.totalAmount />
										
										<cfset local.previousTotal = local.principalAmount />
										<cfset local.strEntry.previousTotal = local.previousTotal />
										
										<cfif local.getRatesUsed.rateID eq local.getFromRate.rateID>
											<cfset local.strPrincipalAmt[#local.getFromRate.rateID#] = dollarFormat(local.totalAmount - local.judgementAmount) />
										</cfif>
										
										<cfset local.strPrincipalAmt[#local.getRatesUsed.rateID#] = dollarFormat(local.totalAmount - local.judgementAmount) />
										<cfset local.strEntry.accruedInt = local.totalAmount - local.judgementAmount />
										
										<cfset local.strDaysCount[#local.getRatesUsed.rateID#] = #local.cDays# * 365 />
										<cfset local.strEntry.DaysCount = round(local.strDaysCount[#local.getRatesUsed.rateID#]) />
										<cfif local.strEntry.DaysCount gt 0>
											<cfset local.strEntry.perDiem = local.strEntry.accruedInt / local.strEntry.DaysCount />
										<cfelse>
											<cfset local.strEntry.perDiem = 0 />
										</cfif>
										
										<cfif local.getRatesUsed.rateID eq local.getFromRate.rateID>
											<cfset local.totalInt = local.strEntry.accruedInt />	
										<cfelse>
											<cfset local.totalInt = local.totalInt + local.strEntry.accruedInt />	
										</cfif>
										
										<cfset local.strEntry.totalInt = local.totalInt />
										
										<cfset local.strDetails[local.getRatesUsed.rateID] = local.strEntry />		
									</cfloop>
									<cfset local.totalIntAmt = local.strEntry.totalInt>
								<cfelse>
									<cfset event.setValue('lossIncurred', 'Invalid')>
								</cfif>
								
								<cfswitch expression="#event.getValue('lossIncurred', 'No')#">
									<cfcase value="No">
										<div class="CPSection">
											<div class="CPSectionTitle BB">Results </div>
											<div id="print" class="InfoText noprint">
												<table>
													<tr>
														<td valign="top" class="c"><a href="#local.link.form#"><i class="icon-search"></i><br /> New Search</a></td>
														<td></td>
														<td valign="top" class="c"><a href="javascript:window.print()"><i class="icon-print"></i><br /> Print</a></td>
														<td></td>
														<td valign="top" class="c"><a href="#local.link.detailedReport#"><i class="icon-list"></i><br /> Detailed<br/>Report</a></td>
													</tr>
												</table>
											</div>
											<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
												<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
													<tr>
														<td class="c" width="50%"><strong>Dates</strong></td>
														<td class="c"><strong>Judgment Interest Rate</strong></td>
													</tr>
													<cfloop query="local.getRatesUsed" >
														<tr>
															<td class="c">
																<cfswitch expression="#local.getRatesUsed.rateQuarter#">
																	<cfcase value="1">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 1>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to March 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 1>
																				January 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 1 AND local.fromQuarter eq 1>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 1>
																				January 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 1>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to March 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																	<cfcase value="2">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 2>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to June 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 2>
																				April 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 2 AND local.fromQuarter eq 2>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 2>
																				April 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 2>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to June 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																	<cfcase value="3">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 3>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to September 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 3>
																				July 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 3 AND local.fromQuarter eq 3>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 3>
																				July 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 3>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to September 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																	<cfcase value="4">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 4>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to December 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 4>
																				October 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 4 AND local.fromQuarter eq 4>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 4>
																				October 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 4>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to December 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																</cfswitch>
															</td>
															<td class="c">#numberFormat(local.getRatesUsed.rate, "0.000")#%</td>
														</tr>
													</cfloop>
													<tr>
														<td class="c">&nbsp;</td>
														<td class="c">&nbsp;</td>
													</tr>
													<tr>
														<td class="c"><strong>Total Days:</strong> #local.daysDifferent#</td>
														<td class="c"><strong>Average Rate:</strong> #numberFormat(local.rateAvg, "0.000")#%</td>
													</tr>
													<tr>
														<td class="c"><strong>Original Amount:</strong> #DollarFormat(local.judgmentAmount)#</td>
														<td class="c"><strong>Total Interest:</strong> #DollarFormat(local.totalIntAmt)#</td>
													</tr>
												</table>
											</div>
										</div>
									</cfcase>
									<cfcase value="Yes">
										<div class="CPSection">
											<div class="CPSectionTitle BB">Results</div>
											<div id="print" class="InfoText noprint">
												<table>
													<tr>
														<td valign="top" class="c"><a href="#local.link.form#"><i class="icon-search"></i><br /> New Search</a></td>
														<td></td>
														<td valign="top" class="c"><a href="javascript:window.print()"><img src="/images/printIco.png" /><br /> Print</a></td>
														<td></td>
														<td valign="top" class="c"><a href="#local.link.detailedReport#"><i class="icon-list"></i><br /> Detailed<br/>Report</a></td>
													</tr>
												</table>
											</div>
											<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
												<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
													<tr>
														<td class="c" width="50%"><strong>Dates</strong></td>
														<td class="c"><strong>Judgment Interest Rate</strong></td>
														<td class="c"><strong>Interest Earned</strong></td>
													</tr>
													<cfloop query="local.getRatesUsed">
														<tr>
															<td class="c">
																<cfswitch expression="#local.getRatesUsed.rateQuarter#">
																	<cfcase value="1">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 1>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to March 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 1>
																				January 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 1 AND local.fromQuarter eq 1>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 1>
																				January 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 1>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to March 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				January 1, #local.getRatesUsed.rateYear# to March 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																	<cfcase value="2">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 2>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to June 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 2>
																				April 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 2 AND local.fromQuarter eq 2>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 2>
																				April 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 2>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to June 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				April 1, #local.getRatesUsed.rateYear# to June 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																	<cfcase value="3">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 3>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to September 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 3>
																				July 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 3 AND local.fromQuarter eq 3>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 3>
																				July 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 3>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to September 30, #local.getRatesUsed.rateYear#
																			<cfelse>
																				July 1, #local.getRatesUsed.rateYear# to September 30, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																	<cfcase value="4">
																		<cfif local.getRatesUsed.rateYear eq #local.fromYear# AND local.fromYear neq local.toYear>
																			<cfif local.fromQuarter eq 4>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to December 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.fromYear neq local.toYear>
																			<cfif local.toQuarter eq 4>
																				October 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelse>
																				October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelseif local.getRatesUsed.rateYear neq #local.toYear# AND local.getRatesUsed.rateYear neq #local.fromYear#>
																			October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																		<cfelseif local.getRatesUsed.rateYear eq #local.toYear# AND local.getRatesUsed.rateYear eq #local.fromYear#>
																			<cfif local.toQuarter eq 4 AND local.fromQuarter eq 4>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.toQuarter eq 4>
																				October 1, #local.getRatesUsed.rateYear# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																			<cfelseif local.fromQuarter eq 4>
																				#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to December 31, #local.getRatesUsed.rateYear#
																			<cfelse>
																				October 1, #local.getRatesUsed.rateYear# to December 31, #local.getRatesUsed.rateYear#
																			</cfif>
																		<cfelse>
																			#DateFormat(CreateDate(local.fromYear,local.fromMonth,local.fromDay),"MMMM d, yyyy")# to #DateFormat(CreateDate(local.toYear,local.toMonth,local.toDay),"MMMM d, yyyy")#
																		</cfif>
																	</cfcase>
																</cfswitch>
															</td>
															<td class="c">#numberFormat(local.getRatesUsed.rate, "0.000")#%</td>
															<td class="c">#local.strPrincipalAmt[local.getRatesUsed.rateID]#</td>
														</tr>
													</cfloop>
													<tr>
														<td class="c">&nbsp;</td>
														<td class="c">&nbsp;</td>
														<td class="c">&nbsp;</td>
													</tr>
													<tr>
														<td class="c">&nbsp;</td>
														<td class="c"><strong>Original Amount:</strong> #DollarFormat(local.judgmentAmount)#</td>
														<td class="c"><strong>Total Interest:</strong> #DollarFormat(local.totalIntAmt)#</td>
													</tr>
												</table>
											</div>
										</div>
									</cfcase>
									<cfcase value="detailedReport">
										<div class="CPSection">
											<div class="CPSectionTitle BB">Detailed Results</div>
											<div id="print" class="InfoText noprint">
												<table>
													<tr>
														<td valign="top" class="c"><a href="#local.link.form#"><i class="icon-search"></i><br /> New Search</a></td>
														<td></td>
														<td valign="top" class="c"><a href="javascript:window.print()"><img src="/images/printIco.png" /><br /> Print</a></td>
													</tr>
												</table>
											</div>
											<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
												<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
													<tr>
														<td>Judgment Amount:</td><td>#DollarFormat(local.judgmentAmount)#</td>
														<td width="200">&nbsp;</td>
														<td></td><td></td>
													</tr>
													<tr>
														<td>Date of Loss or Damage:</td><td>#local.dateLoss#</td>
														<td width="200">&nbsp;</td>
														<td>No of Days:</td><td>#local.daysDifferent#</td>
													</tr>
													<tr>
														<td>Date of Judgment:</td><td>#local.dateJudge#</td>
														<td width="200">&nbsp;</td>
														<td>Average Rate:</td><td>#numberFormat(local.rateAvg, "0.000")#%</td>
													</tr>
													<tr>
														<td>Total Interest:</td><td>#DollarFormat(local.totalIntAmt)#</td>
														<td width="200">&nbsp;</td>
														<td>Average Per Diem</td><td>#DollarFormat(local.totalIntAmt/local.daysDifferent)#</td>
													</tr>
												</table>												
												<br/>
												<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
													<tr>
														<td class="c"><strong>From</strong></td>
														<td class="c"><strong>To</strong></td>
														<td class="c"><strong>No Days</strong></td>
														<td class="c"><strong>Rate(%)</strong></td>
														<td class="c"><strong>Accrued Interest</strong></td>
														<td class="c"><strong>Total Interest</strong></td>
														<td class="c"><strong>Per Diem</strong></td>
													</tr>
													<cfloop query="local.getRatesUsed">
													<tr>
														<td class="c">#DateFormat(local.strDetails[local.getRatesUsed.rateID].fDate, "mm/dd/yyyy")#</td>
														<td class="c">#DateFormat(local.strDetails[local.getRatesUsed.rateID].tDate, "mm/dd/yyyy")#</td>
														<td class="c">#local.strDetails[local.getRatesUsed.rateID].daysCount#</td>
														<td class="c">#numberFormat(local.getRatesUsed.rate, "0.000")#%</td>
														<td class="c">#dollarFormat(local.strDetails[local.getRatesUsed.rateID].accruedInt)#</td>
														<td class="c">#dollarFormat(local.strDetails[local.getRatesUsed.rateID].totalInt)#</td>
														<td class="c">#dollarFormat(local.strDetails[local.getRatesUsed.rateID].perDiem)#</td>
													</tr>
													</cfloop>													
													<tr>
														<td class="c"></td>
														<td class="c"></td>
														<td class="c"></td>
														<td class="c"></td>
														<td class="c"></td>
														<td class="c"></td>
														<td class="c"></td>
													</tr>
													<tr>
														<td class="c"></td>
														<td class="c"></td>
														<td class="c"><strong>#local.daysDifferent#</strong></td>
														<td class="c"></td>
														<td class="c"><strong>#DollarFormat(local.totalIntAmt)#</strong></td>
														<td class="c"> </td>
														<td class="c"></td>
													</tr>
													
												</table>
											</div>
										</div>
									</cfcase>
									<cfdefaultcase> 
										<cfsavecontent variable="local.mailContent">
											<cfoutput>												
												Member Number: #session.cfcUser.memberData.membernumber#<br/>
												Name: #session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#<br/><br/>

												The rates are not defined for the dates entered on the #local.formNameDisplay#.<br/><br/>
		
												Date of Loss or Damage: #local.dateLoss#<br/>
												Date of Judgment: #local.dateJudge#
											</cfoutput>
										</cfsavecontent>

										<cfscript>
											local.arrEmailTo = [];
											local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
											local.toEmailArr = listToArray(local.ORGEmail.to,';');
											for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
												local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
											}
											local.responseStruct = application.objEmailWrapper.sendMailESQ(
												emailfrom={ name="", email=local.ORGEmail.from },
												emailto=local.arrEmailTo,
												emailreplyto=local.ORGEmail.from,
												emailsubject=local.ORGEmail.SUBJECT,
												emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
												emailhtmlcontent=local.mailContent,
												siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
												memberID=val(arguments.event.getTrimValue('mc_siteinfo.sysMemberID')),
												messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
												sendingSiteResourceID=this.siteResourceID
											);
										</cfscript>
										
										<div class="CPSection">
											<div class="CPSectionTitle BB">Results</div>
											<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
												The rates are not defined for the dates entered.
											</div>
										</div>
									</cfdefaultcase> 
						
								</cfswitch>
							
							</cfcase>

							<cfcase value="admin">
								
								<cfsavecontent variable="local.gridJS">
									<link rel="stylesheet" type="text/css" href="/assets/common/javascript/dhtmlxgrid/dhtmlxgrid.css" />
									<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/memberCentral_grid.js"></script>
									<script type="text/javascript">dhtmlxError.catchError("LoadXML", mcg_ErrorHandler);</script>
								</cfsavecontent>
								<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">
								
								<cfset local.gridXML 		= "/?pg=prejudgementCalc&mode=stream&action=gridData" />
								<!--- CUSTOM_SK_UTILITIES --->
									
								<cfsavecontent variable="local.gridJS">
									<script>
										var mcg_itemsingle = 'rate'; 
										var mcg_itemplural = 'rates';
										var sectionGridXML = '#local.gridXML#';
										var mcg_gridQString = sectionGridXML;
										
										function pickYear(year) {
											sectionGridXML = '/?pg=prejudgementCalc&mode=stream&action=gridData&ry=' + year;
											mcg_gridQString = sectionGridXML;
											mcg_reloadGrid(); 
										}
										function doDelete(rID) {
											var removeData	= function(s) {
												if (s.success && s.success.toLowerCase() == 'true'){ 
													mcg_reloadGrid(); 
													} 
												else { alert('We were unable to remove this item.'); }
											};
											var msg = 'Are you sure you want to remove this item?';
											if( confirm(msg)){
												var objParams = { rateID:rID };
												TS_AJX('CUSTOM_SK_UTILITIES','removeRate',objParams,removeData,removeData,10000,removeData);
											}
										}
										function closeBox() { $.colorbox.close(); }
										$(function() {
											mcg_init();
										});
										function _FB_validateForm() {
											var thisForm = document.forms["#local.formName#"];
											var arrReq = new Array();
											var lastMtrxErr = '';
											
											if (!_FB_hasValue(thisForm['rateYear'], 'TEXT')) arrReq[arrReq.length] 			= 'Rate Year';
											if (!_FB_hasValue(thisForm['rateQuarter'], 'TEXT')) arrReq[arrReq.length] 		= 'Rate Quarter';
											if (!_FB_hasValue(thisForm['rate'], 'TEXT')) arrReq[arrReq.length] 				= 'Rate';
											
											if (arrReq.length > 0) {
												var msg = 'The following questions are required:\n\n';
												for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
												alert(msg);
												return false;
											}
											return true;
										}
									</script>
								</cfsavecontent>
								<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">
								
								<cfset local.qryYears = getYears() />
								
								<div>
									<table border="0">
										<tr>
											<td width="250"><div id="mcg_rnum" class="tsAppBodyText" style="height:13px;"></div></td>
											<td width="250" align="right">
												Years
												<select name="rateYear" onchange="pickYear(this.value);">
													<option value="">All</option>
													<cfloop query="local.qryYears">
														<option value="#local.qryYears.rateYear#">#local.qryYears.rateYear#</option>
													</cfloop>
												</select>
											</td>
										</tr>
										<tr>
											<td colspan="2">
												<div id="mcg_gridbox" style="width:100%;height:200px;"></div>
											</td>
										</tr>
										<tr>
											<td colspan="2">
												<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.link.addRate#" onsubmit="return _FB_validateForm();">
													<table>
														<tr>
															<td>Year:</td>
															<td><input type="text" name="rateYear" size="5" value=""></td>
															<td></td>
															
															<td>Quarter:</td>
															<td><input type="text" name="rateQuarter" size="3" value=""></td>
															<td></td>
															
															<td>Rate:</td>
															<td><input type="text" name="rate" size="3" value="">%</td>
															<td></td>
															
															<td><button name="btn" type="submit">Add</button></td>
															<td></td>
															
															<td><button name="btn" onClick="self.location='#local.link.form#'" type="button">Back</button></td>
														</tr>
													</table>
												</cfform>
											</td>
										</tr>
									</table>
								</div>
							</cfcase>

							<cfcase value="addRate">
								<cfset local.objUtilities.addRate(rateYear=event.getValue('rateYear'), rateQuarter=event.getValue('rateQuarter'), rate=event.getValue('rate'))>
								<cflocation url="#local.link.admin#" addtoken="false" />
							</cfcase>

							<cfdefaultcase>
								<cfsavecontent variable="variables.JS">
									<cfoutput>
									<style type="text/css">
										##lossDate, ##judgeDate  { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
									</style>			
									<script language="JavaScript" type="text/javascript">
										$(document).ready(function(){
											mca_setupDatePickerField('lossDate');
											mca_setupDatePickerField('judgeDate');
										});				
									</script>
									</cfoutput>	
								</cfsavecontent>
								<cfhtmlhead text="#variables.JS#">								
								<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.link.results#">
									<cfinput type="hidden" value="Yes" name="lossIncurred" id="lossIncurred" >
									<div id="formToFill">
										<!--- PERSONAL INFORMATION: ========================================================================================================================= --->
										<div class="CPSection">
											<div class="CPSectionTitle BB"></div>
											<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
												<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
													<tr>
														<td class="r" width="350">Judgment Amount:</td>
														<td>
															$&nbsp;<cfinput type="text" required="true" message="Please enter a valid Judgment Amount." validate="float" class="tsAppBodyText" name="judgmentAmount" id="judgmentAmount" />
															*
														</td>
													</tr>
													<tr>
														<td class="r">Date of Loss or Damage:</td>
														<td>
															<cfinput type="text" required="true" message="Please use the specified date format with slashes." validate="date"  class="tsAppBodyText" name="lossDate" id="lossDate">
															<a href="javascript:mca_clearDateRangeField('lossDate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -2px 7px;"></i></a>
															<span class="info">(MM/DD/YYYY)</span>
														</td>
													</tr>
													<tr>
														<td class="r">Date of Judgment:</td>
														<td>
															<cfinput type="text" required="true" message="Please use the specified date format with slashes." validate="date"  class="tsAppBodyText" name="judgeDate" id="judgeDate">
															<a href="javascript:mca_clearDateRangeField('judgeDate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -2px 7px;"></i></a>
															<span class="info">(MM/DD/YYYY)</span>
														</td>
													</tr>
													<tr class="c">
														<td colspan="2"><br />* Please only enter numbers for the Judgment Amount. The dollar sign or commas should not be entered.</td>
													</tr>
												</table>
											</div>
										</div>
										<!--- BUTTONS: ====================================================================================================================================== --->				
										<div id="formButtons">
											<div style="padding:10px;">
												<div align="center" class="frmButtons">
													<input type="submit" value="Calculate Interest" name="submit">
													<input type="submit" value="Detailed Report" name="detailedReport">
													<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
													<button name="btn" onClick="javascript:{parent.location='#local.link.admin#';}" type="button">Add New Interest Rate</button>
													</cfif>
												</div>
											</div>
										</div>
									</div>
								</cfform>
							</cfdefaultcase>
							
						</cfswitch>	
					</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfreturn returnAppStruct(local.returnHTML,"echo")>	
	</cffunction>
	<!--- GRID XML FUNCTION --->
	
	<!--- query data functions --->
	<cffunction name="getAllRates" access="public" returntype="query">
		<cfset var local = structNew() />
		
		<cfquery datasource="#application.dsn.customApps.dsn#" name="local.data">
			SELECT rateID,rate,rateYear,rateQuarter,modifiedBy,modifiedDate,isActive
			FROM dbo.SK_PreJudgementCalculator_Rates
		</cfquery>
		
		<cfreturn local.data />		
	</cffunction>	
	
	<cffunction name="getYears" access="public" returntype="query">
		<cfset var local = structNew() />
		
		<cfquery datasource="#application.dsn.customApps.dsn#" name="local.data">
			SELECT DISTINCT rateYear
			FROM dbo.SK_PreJudgementCalculator_Rates 
			ORDER BY rateYear desc
		</cfquery>
		
		<cfreturn local.data />		
	</cffunction>	
	
	<cffunction name="getDupes" access="public" returntype="query">
		<cfargument name="rateYear" type="numeric" required="true">
		<cfargument name="rateQuarter" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery dbtype="query" name="local.dupes">
			SELECT rateID
			FROM dbo.SK_PreJudgementCalculator_Rates
			WHERE
				rateYear = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateYear#">
				AND rateQuarter = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateQuarter#">
		</cfquery>

		<cfreturn local.dupes />	
	</cffunction>	
	
	<cffunction name="widgetcontroller" access="public" output="false" returntype="struct" hint="controller for this widget">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			init(arguments.Event);
		</cfscript>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>
</cfcomponent>