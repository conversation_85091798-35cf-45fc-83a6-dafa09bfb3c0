<cfset dataStruct = attributes.data.actionStruct>
<cfset local.stepNum = 2>

<cfinclude template="../commonJS_step2.cfm">

<cfoutput>
<div class="container-fluid">
	<div class="contribution-section">

		<cfinclude template="navigation.cfm">

		<div class="tab-content">
			<div class="tab-pane active" id="cpStep2">
				<div class="contribution">
					<h2 class="title-blue">#dataStruct.intakeFormTitle#</h2>

					<div id="cp2_err_div" style="display:none;margin:12px 0 5px 0;"></div>

					<cfform name="frmContribution" id="frmContribution" method="post" action="#dataStruct.mainformurl#" onsubmit="return doS2Validate(false)">
						<input type="hidden" name="nextStep" id="nextStep" value="payment">
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">
					</cfform>

					<div class="well" id="divCPCreateAccountArea">
						<h3><cfif len(dataStruct.newAcctFormTitle)>#dataStruct.newAcctFormTitle#<cfelse>Create New Account</cfif></h3>
						#CreateObject("component","model.system.user.accountLocater").displayLocatorNewAccount(fieldsetID=dataStruct.fieldsetID, postURL=dataStruct.newaccturl, buttonText="Continue", showProfessionalLicenses=dataStruct.showProfLicenses)#
						<div id="divCPCreateAccountSubmitArea"></div>
					</div>
					<div class="CPSection waitingtxt" id="divCPCreateAccountAreaLoading" style="display:none;">
						<div style="text-align:center;margin:30px 10px;"><i class="icon-spinner icon-spin"></i> Please wait while we prepare your contribution for the next step.</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>