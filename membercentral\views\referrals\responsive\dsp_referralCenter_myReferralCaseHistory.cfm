<cfoutput>
<cfquery name="local.qryGetReferralHistoryStatusSet3" dbtype="query">
	select clientReferralStatusID, statusName, isRetainedCase, isOpen from [variables.structData.qryGetReferralHistoryStatusSet1]
	union
	select clientReferralStatusID, statusName, isRetainedCase, isOpen from [variables.structData.qryGetReferralHistoryStatusSet2]
</cfquery>
<cfquery name="local.qryGetReferralHistoryStatus" dbtype="query">
	select clientReferralStatusID, statusName, isRetainedCase, isOpen from [local.qryGetReferralHistoryStatusSet3]
	order by isRetainedCase, isOpen desc				
</cfquery>
<div class="form-group row-fluid">
	<div class="span12" style="text-align:left;">
		<h4><b>My Referral/Case History</b></h4>
	</div>
</div>
<div class="form-group row-fluid">
	<div class="span12">
		<form class="form-inline refFilterForm" action="/" method="get">
			<input type="hidden" name="pg" value="referrals">
			<input type="hidden" name="panel" value="browse">
			<input type="hidden" name="tab" value="#variables.structData.tabToShow#">
			<input type="hidden" name="sort" value="#variables.structData.sort#">
			<input type="hidden" name="count" value="#variables.structData.count#">
			<input type="hidden" name="orderBy" value="#variables.structData.orderBy#">
			<input type="hidden" name="start" value="1">
			<label class="refDateRange"><br/>
				<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
					<option <cfif variables.structData.refDateRanges eq 0 > selected </cfif> value="0">My Referral/Case History</option>
					<option <cfif variables.structData.refDateRanges eq 30 > selected </cfif>  value="30">Last 30 Days</option>
					<option <cfif variables.structData.refDateRanges eq 60 > selected </cfif> value="60">Last 60 Days</option>
					<option <cfif variables.structData.refDateRanges eq 90 > selected </cfif> value="90">Last 90 Days</option>
				</select>
			</label>
			<label class="refferalID"><b>Referral ID</b><br/>
				<input type="text" name="filterReferralID" class="input-small refferalID" <cfif variables.structData.filterReferralID GT 0> value="#variables.structData.filterReferralID#" </cfif>>
			</label>
			<label class="clientLastName"><b>Client Last Name</b><br/>
				<input type="text" name="filterClientLastName" class="input-medium clientLastName" value="#variables.structData.filterClientLastName#">
			</label>
			<label class="clientFirstName"><b>Client First Name</b><br/>
				<input type="text" name="filterClientFirstName" class="input-medium clientFirstName" value="#variables.structData.filterClientFirstName#">
			</label>
			<label class="Status"><b>Status</b><br/>
				<select class="Status input-medium" name="filterStatus">
					<option value="0"></option>
					<cfloop query="local.qryGetReferralHistoryStatus">
						<option <cfif variables.structData.filterStatus eq local.qryGetReferralHistoryStatus.clientReferralStatusID > selected </cfif> value="#local.qryGetReferralHistoryStatus.clientReferralStatusID#">#local.qryGetReferralHistoryStatus.statusName#</option>
					</cfloop>
				</select>
			</label>
			<label class="amountDue"><b>Amount Due</b><br/>
				<select class="amountDue input-small" name="filterAmountDue">
					<option value=""></option>
					<option <cfif variables.structData.filterAmountDue eq "Yes" > selected </cfif>  value="Yes">Yes</option>
					<option <cfif variables.structData.filterAmountDue eq "No" > selected </cfif>  value="No">No</option>
				</select>
			</label>								
			<button type="submit" class="btn filterRefferals">Search</button>
			<button type="reset" class="btn filterClear">Clear Filters</button>
		</form>
	</div>							
</div>
<div class="form-group row-fluid <cfif arrayLen(variables.structData.mrchData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink">Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>	
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>		
</div>
<cfif arrayLen(variables.structData.mrchData) gt 0> 
	<div class="dataResp">
		<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.mrchData)#">
			<div class="row-fluid" bgcolor="##DEDEDE">
				<div class="span12 clearfix well well-small">
					<div class="row-fluid">
						<div class="span8 eventLeft list-text">			
							<p>
								<cfif variables.structData.mrchData[local.Counter].isCase>
								<a href="javascript:editCase(#variables.structData.mrchData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>Referral ##  #variables.structData.mrchData[local.Counter].CLIENTREFERRALID#</b></a>
								<cfelse>
								<a href="javascript:editReferral(#variables.structData.mrchData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>Referral ##  #variables.structData.mrchData[local.Counter].CLIENTREFERRALID#</b></a>														
								</cfif>												
							</p>
							<p><b>Client Name :</b>  #variables.structData.mrchData[local.Counter].CLIENTNAME#</p>			
							<cfset local.issueDesc = "">
							<cfif structKeyExists(variables.structData.mrchData[local.Counter], "ISSUEDESC")>
								<cfset local.issueDesc = variables.structData.mrchData[local.Counter].ISSUEDESC>
							</cfif>									
							<div class="mb-10"><b>Description : </b><span class="show-read-more">#htmleditformat(local.issueDesc)#</span></div>
							<p><b>Status :</b> #variables.structData.mrchData[local.Counter].STATUSNAME#</p>
							<p><b>Is Retained Case? :</b> #yesNoFormat(variables.structData.mrchData[local.Counter].isCase)#</p>
						</div>											
						<div class="span4 eventRight">
							<p class="ReferralDate"><b>Referral Date :</b> #variables.structData.mrchData[local.Counter].clientReferralDate#</p>
						</div>
					</div>											
				</div>
			</div>									
		</cfloop>
	</div>	
	
	<div class="row-fluid  dataTable hide">
		<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
			<tr class="gridHead">
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referralid">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="referralid" data-order="#local.order#"><b>Referral ID##</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "clientname">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="clientname" data-order="#local.order#"><b>Client Name</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "description">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="description" data-order="#local.order#"><b>Description</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "status">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="status" data-order="#local.order#"><b>Status</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "iscase">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="iscase" data-order="#local.order#"><b>Is Retained Case?</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'asc'>
				<cfif variables.structData.sort EQ "isamountdue">
					<cfif variables.structData.orderBy EQ "asc"><cfset local.order = 'desc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="isamountdue" data-order="#local.order#"><b>Is Amount Due?</b>&nbsp;<i class="fa fa-sort"></i></td>
				
				<cfset local.order = 'desc'>
				<cfif variables.structData.sort EQ "referraldate">
					<cfif variables.structData.orderBy EQ "desc"><cfset local.order = 'asc'></cfif>
				</cfif>
				<td class="sortColumn" data-sort="referraldate" data-order="#local.order#"><b>Referral Date </b>&nbsp;<i class="fa fa-sort"></i></td>
			</tr>
			<cfloop index="local.Counter" from=1 to="#arrayLen(variables.structData.mrchData)#">
				<cfset local.hasPendingDue = 'No'>
				<cfif NOT IsNull(variables.structData.mrchData[local.Counter].duePending) AND len(variables.structData.mrchData[local.Counter].duePending) AND variables.structData.mrchData[local.Counter].duePending gt 0>
					<cfset local.hasPendingDue = 'Yes'>
				</cfif>
				<tr class="gridData">
					<td width="140" style="text-align:left;">
						<cfif variables.structData.mrchData[local.Counter].isCase>
						<a href="javascript:editCase(#variables.structData.mrchData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Case"><b>#variables.structData.mrchData[local.Counter].CLIENTREFERRALID#</b></a>
						<cfelse>
						<a href="javascript:editReferral(#variables.structData.mrchData[local.Counter].CLIENTREFERRALID#)" target="_self" title="View/Edit Referral"><b>#variables.structData.mrchData[local.Counter].CLIENTREFERRALID#</b></a>
						</cfif>
					</td>
					<td width="175" style="text-align:left;">
						#variables.structData.mrchData[local.Counter].CLIENTNAME#
					</td>
					<td width="175" style="text-align:left;">
						<cfset local.issueDesc = "">
						<cfset local.fullIssueDesc = "">
						<cfif structKeyExists(variables.structData.mrchData[local.Counter], "ISSUEDESC")>
							<cfset local.fullIssueDesc = variables.structData.mrchData[local.Counter].ISSUEDESC>
							<cfset local.issueDesc = local.fullIssueDesc >
							<cfif len(trim(local.fullIssueDesc)) gt 100>
								<cfset local.issueDesc = left(variables.structData.mrchData[local.Counter].ISSUEDESC, "97") & "...">
							</cfif>
						</cfif>	
						#htmleditformat(local.issueDesc)#
						<cfif len(trim(local.fullIssueDesc)) gt 100>
							<div class="MCIssueDescBox hide">
								#htmleditformat(local.fullIssueDesc)#
							</div>
						</cfif>
					</td>
					<td width="190" style="text-align:left;">
						#variables.structData.mrchData[local.Counter].STATUSNAME#
					</td>
					<td width="175" style="text-align:left;">												
						#yesNoFormat(variables.structData.mrchData[local.Counter].isCase)#
					</td>
					<td width="175" style="text-align:left;">
						#local.hasPendingDue#
					</td>
					<td width="175" style="text-align:left;">
						#variables.structData.mrchData[local.Counter].clientReferralDate#
					</td>												
				</tr>											
			</cfloop>
		</table>							
	</div>							
</cfif>
<div class="form-group row-fluid <cfif arrayLen(variables.structData.mrchData) eq 0> hide</cfif> paginationHolder">
	<div class="span6 text-left loadingGrid">&nbsp;</div>
	<div class="span6 paginationValue text-right">
		#variables.structData.currentpage# of #variables.structData.totalpages# pages  &nbsp;
		
		<cfif variables.structData.currentpage neq 1 > <a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage-1#" start="#variables.structData.currentpage-1#" class="paginationChange prevPageLink" >Previous Page</a> </cfif>
		<cfif variables.structData.currentpage neq variables.structData.totalpages >&nbsp;<a style="text-decoration:underline;" href="#local.paginationRedirectUrl#&start=#variables.structData.currentpage+1#" start="#variables.structData.currentpage+1#" class="paginationChange nextPageLink">Next Page</a> </cfif>	
		<cfif variables.structData.totalpages GT 1>
			&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">			
			&nbsp;<a href="##" style="text-decoration:underline;" totalpages="#variables.structData.totalpages#" class="goToPage">Go To Page</a>
		</cfif>
	</div>			
</div>
<div class="row-fluid <cfif arrayLen(variables.structData.mrchData) gt 0> hide</cfif> noReferrals" bgcolor="##DEDEDE">
	<div class="span12 clearfix well well-small">
		<div class="row-fluid">
			<p>No Referrals match this criteria.</p>
		</div>
	</div>
</div>
</cfoutput>