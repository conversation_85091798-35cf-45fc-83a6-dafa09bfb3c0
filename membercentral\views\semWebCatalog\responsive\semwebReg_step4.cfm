<cfinclude template="../commonSWRegStep4JS.cfm">

<cfoutput>
<div class="swreg-card swreg-mt-3 swreg-p-3">
	<form name="frmSWRegStep4" id="frmSWRegStep4" onsubmit="doS4ValidateAndSave();return false;"<cfif local.swReg.currentReg.currentStep NEQ 4> style="display:none;"</cfif>>
		<div class="swreg-mb-3 swreg-font-size-lg swreg-font-weight-bold">Credit Selection</div>
		
		<div class="row-fluid">
			<div class="span12">
				<cfif GetToken(local.rc.item,1,"-") eq "SWB">
					
					<div class="swreg-mt-2 swreg-mb-4">
						Select the programs you would like credit for:<br/>
						<div style="margin-top:5px;margin-left:4px;"><input type="checkbox" id="parent" style="margin:0 10px 0 0;"> Select All</div>
					</div>
					<cfloop query="local.qryDistSeminars">
						<cfquery name="local.qryCreditBySeminar" dbtype="query">
							select *
							from [local].strCredit.qryCredit
							where seminarID = #local.qryDistSeminars.contentID#
						</cfquery>
						<cfquery name="local.qryCreditDistinct" dbtype="query">
							select distinct authorityID
							from [local].qryCreditBySeminar
						</cfquery>
						<cfquery name="local.qryCredRequired" dbtype="query">
							select distinct seminarCreditID
							from [local].qryCreditBySeminar
							where isCreditRequired = 1
						</cfquery>
						<cfquery name="local.qryCredDefaulted" dbtype="query">
							select distinct seminarCreditID
							from [local].qryCreditBySeminar
							where isCreditDefaulted = 1
						</cfquery>
						<div class="swreg-d-flex">
							<div class="swreg-col-auto">
								
								<cfif local.qryCreditDistinct.recordcount is 0>	
									&nbsp;&nbsp;&nbsp;
								<cfelse>
									<cfif local.qryCredRequired.recordcount Gt 0>
										<input type="checkbox" data-isCredRequired="1" data-linkedcredits="#valueList(local.qryCreditBySeminar.seminarCreditID)#" class="child"  id="creditSelect#local.qryDistSeminars.contentID#" name="creditSelect#local.qryDistSeminars.contentID#" value="1" checked onclick="return false" style="margin:12px 0 0 0;">	
									<cfelse>
										<input type="checkbox" data-isCredRequired="0" data-linkedcredits="#valueList(local.qryCreditBySeminar.seminarCreditID)#" class="child" id="creditSelect#local.qryDistSeminars.contentID#" name="creditSelect#local.qryDistSeminars.contentID#" <cfif local.qryCredDefaulted.recordcount GT 0>checked </cfif> onclick="showhideCreditSelect(this,#local.qryDistSeminars.contentID#)" style="margin:12px 0 0 0;">
									
									</cfif>										
								</cfif>	
							</div>
							<div class="swreg-col">
								<div class="swreg-mt-2">#local.qryDistSeminars.currentrow#. #EncodeForHTML(local.qryDistSeminars.contentName)#</div>
								<cfif local.qryCreditDistinct.recordcount is 0>
									<p style="margin-bottom:0px;">This program <i>has not been submitted</i> for credit in any jurisdiction.
									<cfif local.qryDistSeminars.offerCertificate>
										 Registrants will receive a Certificate of Attendance/Completion that may or may not meet credit requiremements in various jurisdictions.
									</cfif>
									 Where applicable, credit will be only awarded to a paid registrant completing the program at their own computer and phone.</p>
								<cfelse>
								<div class="well creditSelContainer swreg-mt-2" id="creditDIV#local.qryDistSeminars.contentID#" <cfif local.qryCredRequired.recordcount is 0>data-linkedcreditselect="creditSelect#local.qryDistSeminars.contentID#" <cfif local.qryCredDefaulted.recordcount is 0>style="display:none;"</cfif><cfelse> data-linkedcreditselect=""</cfif>>
									<cfloop query="local.qryCreditBySeminar">
											<input class="creditcbox"  <cfif local.qryCreditBySeminar.isCreditRequired >data-isCredRequired="1"<cfelse> data-isCredRequired="0"</cfif> type="checkbox" value="#local.qryCreditBySeminar.seminarCreditID#" onClick="checkIDNum(this,#local.qryCreditBySeminar.isCreditRequired#)"  id="frmcreditlink#local.qryCreditBySeminar.seminarCreditID#" name="frmcreditlink" <cfif local.qryCreditBySeminar.isCreditRequired or local.qryCreditBySeminar.isCreditDefaulted>checked</cfif>>
											<cfset local.qryCreditMatch = local.objCredit.getCreditsFromWDDX(local.qryCreditBySeminar.wddxCreditTypes,local.qryCreditBySeminar.wddxCreditsAvailable,true)>
											<b>#local.qryCreditBySeminar.authorityJurisdiction#</b> via #local.qryCreditBySeminar.sponsorName#
										</label>
										<div style="padding-left:20px;">
											<small>
											#local.qryCreditBySeminar.status# credits: 
											<cfloop query="local.qryCreditMatch">
												<cfif local.qryCreditMatch.numcredits gt 0>#local.qryCreditMatch.numcredits# #local.qryCreditMatch.displayname#<cfif local.qryCreditMatch.currentrow is not local.qryCreditMatch.recordcount>, </cfif></cfif>
											</cfloop>
											</small>
										</div>
										<div style="padding-left:20px;">
											<div id="dividnum#local.qryCreditBySeminar.seminarCreditID#" <cfif local.qryCreditBySeminar.isCreditRequired or local.qryCreditBySeminar.isCreditDefaulted><cfelse>style="display:none;"</cfif>>
												<small>
													<cfif len(local.qryCreditBySeminar.creditIDText)>
														#local.qryCreditBySeminar.creditIDText#: 																
														<input type="text" class="frmTxt#local.qryCreditBySeminar.authorityID#" name="frm#local.qryCreditBySeminar.seminarCreditID#ID" id="frm#local.qryCreditBySeminar.seminarCreditID#ID" value="" size="25" maxlength="50" onKeyUp="showCopyDown(#local.qryCreditBySeminar.seminarCreditID#);"><a style="display:none; cursor: pointer;" id="frmlink#local.qryCreditBySeminar.seminarCreditID#ID" onClick="copyDown($('##frm#local.qryCreditBySeminar.seminarCreditID#ID'),#local.qryCreditBySeminar.authorityID#);">Apply to All </a>																
														<cfif local.qryCreditBySeminar.isIDRequired>(required)</cfif>
													</cfif>
												</small>
											</div>
										</div>
									</cfloop>
								</div>
								</cfif>	
							</div>
						</div>
					</cfloop>
				<cfelse>

					<cfif local.strCredit.qryCreditDistinct.recordcount is 0>
						<p>This program <i>has not been submitted</i> for credit in any jurisdiction. Registrants will receive a 
							Certificate of Attendance/Completion that may or may not meet credit requiremements in various jurisdictions. Where 
							applicable, credit will be only awarded to a paid registrant completing the program at their 
							own computer and phone.</p>
					<cfelse>
						<cfif local.qryCredRequired.recordcount is 0>							
							<p>If you are interested in earning credit for this program, select the appropriate response below.
							If you do not select credit now, your participation in this program may not be accepted for 
							credit at the conclusion of the program.</p>
							<br/>
							<label class="radio">
								<input type="radio" value="0" name="creditSelect" onclick="hideCreditSelect()"> I am NOT interested in applying for credit for this program.
							</label>
							<label class="radio">
								<input type="radio" value="1" name="creditSelect" #local.radioChecked# onclick="showCreditSelect()"> I am interested in applying for credit for this program.
							</label>
							<div class="well creditSelContainer" id="creditDIV" data-linkedcreditselect="creditSelect" style="display:none;">
						<cfelse>
							<p>This program requires credit selection in one or more of the following jurisdictions. 
							These jurisdictions have been pre-selected for you.</p>
							<div style="display:none;"><input type="radio" value="0" name="creditSelect"> <input type="radio" name="creditSelect" value="1" checked></div>
							<br/>
							<div class="well creditSelContainer" id="creditDIV" data-linkedcreditselect="">
						</cfif>
					
						
							<p><b>Select the jurisdictions in which you wish to apply for credit.</b></p>
						
							<div class="row-fluid">
								<div class="span12">
									<cfloop query="local.strCredit.qryCredit">
										<label class="checkbox" style="display:inline-block;">
											<input class="creditcbox" type="checkbox" value="#local.strCredit.qryCredit.seminarCreditID#" onClick="checkIDNum(this,#local.strCredit.qryCredit.isCreditRequired#)" id="frmcreditlink#local.strCredit.qryCredit.seminarCreditID#" name="frmcreditlink" <cfif local.strCredit.qryCredit.isCreditRequired or local.strCredit.qryCredit.isCreditDefaulted>checked</cfif>>
											
											<cfset local.qryCreditMatch = local.objCredit.getCreditsFromWDDX(local.strCredit.qryCredit.wddxCreditTypes,local.strCredit.qryCredit.wddxCreditsAvailable,true)>
											
											<small><b>#local.strCredit.qryCredit.authorityJurisdiction#</b> via #local.strCredit.qryCredit.sponsorName#</small>
										</label>
										<div style="padding-left:20px;">
											<small>
												<strong>#local.strCredit.qryCredit.status# credits:</strong><br>
												<cfloop query="local.qryCreditMatch">
													<cfif local.qryCreditMatch.numcredits gt 0>#local.qryCreditMatch.numcredits# #local.qryCreditMatch.displayname#<cfif local.qryCreditMatch.currentrow is not local.qryCreditMatch.recordcount>, </cfif></cfif>
												</cfloop>
												
												<div id="dividnum#local.strCredit.qryCredit.seminarCreditID#" <cfif local.strCredit.qryCredit.isCreditRequired or local.strCredit.qryCredit.isCreditDefaulted><cfelse>style="display:none;"</cfif>>
													<cfif len(local.strCredit.qryCredit.creditIDText)>
														#local.strCredit.qryCredit.creditIDText#: 
														<input type="text" name="frm#local.strCredit.qryCredit.seminarCreditID#ID" id="frm#local.strCredit.qryCredit.seminarCreditID#ID" value="" size="25" maxlength="50" >
														<cfif local.strCredit.qryCredit.isIDRequired>(required)</cfif>
													</cfif>
												</div>
											</small>
										</div>
										<br>
									</cfloop>
								</div>
							</div>
						</div>
					</cfif>
				</cfif>
			</div>
		</div>

		<div class="swreg-mt-5">
			<div id="step4Err" class="alert alert-danger" style="display:none;"></div>
			<button type="button" name="btnSaveStep4" id="btnSaveStep4" class="btn btn-success" onclick="doS4ValidateAndSave();" disabled>
				<cfif local.swReg.currentReg.isRegCartItem EQ 1>
					Save Changes
				<cfelse>
					<i class="icon-arrow-right"></i> Continue
				</cfif>
			</button>
		</div>
		<div id="swRegStep4SaveLoading" style="display:none;"></div>
	</form>
	<div id="swRegStep4Summary" class="swRegStepSummary swreg-cursor-pointer" data-swregsummarystep="4"<cfif local.swReg.currentReg.currentStep EQ 4> style="display:none;"</cfif>>
		<div class="swreg-d-flex">
			<a href="##" class="swreg-align-self-center swreg-mr-2 swreg-font-size-lg swreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="swreg-col">
				<div class="swreg-font-size-lg swreg-font-weight-bold">Credit Selection</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>