<cfsavecontent variable="local.js">
	<script type="text/javascript">
		function b_search() {
			$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
			return true;
		}
		$(function(){
			mca_setupDatePickerRangeFields('s_datefrom','s_dateto');
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.js,'\s{2,}',' ','ALL')#">

<cfoutput>
#showHeader(settings=local.qryBucketInfo, viewDirectory=arguments.viewDirectory)#

<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal">
	<div class="control-group">
		<label class="control-label">Resolution Date between:</label>
		<div class="controls">
			<input type="text" id="s_datefrom" name="s_datefrom" value="#DateFormat(local.strSearchForm.s_datefrom, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol"> and 
			<input type="text" id="s_dateto" name="s_dateto" value="#DateFormat(local.strSearchForm.s_dateto, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_datefrom','s_dateto');return false;">clear dates</a>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_aut">County:</label>
		<div class="controls">
			<select name="s_aut" id="s_aut">
				<option value="">All</option>
				<cfloop query="local.qryCountyNames">
					<option value="#local.qryCountyNames.countyID#" <cfif local.qryCountyNames.countyID eq local.strSearchForm.s_aut>selected="selected"</cfif>>#local.qryCountyNames.countyName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_cat">Courthouse:</label>
		<div class="controls">
			<select name="s_cat" id="s_cat">
				<option value="">All</option>
				<cfloop query="local.qryCourtNames">
					<option value="#local.qryCourtNames.courtID#" <cfif local.qryCourtNames.courtID eq local.strSearchForm.s_cat>selected="selected"</cfif>>#local.qryCourtNames.courtName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_category">Action Type:</label>
		<div class="controls">
			<select name="s_category" id="s_category">
				<option value="">All</option>
				<cfloop query="local.qryActionTypes">
					<option value="#local.qryActionTypes.actionID#" <cfif local.qryActionTypes.actionID eq local.strSearchForm.s_category>selected="selected"</cfif>>#local.qryActionTypes.actionType#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_fname">Resolution Type:</label>
		<div class="controls">
			<select name="s_fname" id="s_fname">
				<option value="">All</option>
				<cfloop query="local.qryResolutionTypes">
					<option value="#local.qryResolutionTypes.resolutionID#" <cfif local.qryResolutionTypes.resolutionID eq local.strSearchForm.s_fname>selected="selected"</cfif>>#local.qryResolutionTypes.resolutionType#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_lname">Insurance Company:</label>
		<div class="controls">
			<select name="s_lname" id="s_lname">
				<option value="">All</option>
				<cfloop query="local.qryInsuranceNames">
					<option value="#local.qryInsuranceNames.insuranceCoID#" <cfif local.qryInsuranceNames.insuranceCoID eq local.strSearchForm.s_lname>selected="selected"</cfif>>#local.qryInsuranceNames.CompanyName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_type">Expert:</label>
		<div class="controls">
			<select name="s_type" id="s_type">
				<option value="">All</option>
				<cfloop query="local.qryExpertNames">
					<option value="#local.qryExpertNames.expertID#" <cfif local.qryExpertNames.expertID eq local.strSearchForm.s_type>selected="selected"</cfif>>#local.qryExpertNames.expertName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_jurisdiction">Judge:</label>
		<div class="controls">
			<select name="s_jurisdiction" id="s_jurisdiction">
				<option value="">All</option>
				<cfloop query="local.qryJudgeNames">
					<option value="#local.qryJudgeNames.judgeID#" <cfif local.qryJudgeNames.judgeID eq local.strSearchForm.s_jurisdiction>selected="selected"</cfif>>#local.qryJudgeNames.judgeName#</option>
				</cfloop>
			</select>
		</div>
	</div>	
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>any</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>