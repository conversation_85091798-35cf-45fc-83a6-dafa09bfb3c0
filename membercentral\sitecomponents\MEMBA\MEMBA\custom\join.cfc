<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			
			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SubTypeTest", type="STRING", desc="Check for existing accepted/active/billed subscriptions of this type", value="069698ca-c1b9-4c4c-90ec-0d576e8595a2" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="ActiveAcceptedMessage", type="CONTENTOBJ", desc="Message displayed when account selected has active/accepted subscription", value="Our records indicate you are already a MBA member. If you have questions about your membership, please call 901.527.3573." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledMessage", type="CONTENTOBJ", desc="Message displayed when account selected has billed subscription", value="You need to renew your Memphis Bar membership. You will be re-directed to your renewal shortly" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 901.527.3573." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="MBA Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step1TopContent", type="CONTENTOBJ", desc="Content at top of page 1", value="Step 1 - Please complete the following information." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step2TopContent", type="CONTENTOBJ", desc="Content at top of page 2", value="Step 2 - Make your selections below." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Step3TopContent", type="CONTENTOBJ", desc="Content at top of page 3", value="Step 3 - Please review your selections and proceed with payment." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MainSubscription", type="STRING", desc="UID for subscription tree", value="5e0a2cde-1568-439a-824b-2b6e7f69440d" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="2e961f95-c750-462b-9f62-35f880354cbd" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);			
			local.tmpField = { name="ProfileCodeCredit", type="STRING", desc="pay profile code for credit card", value="MBACIM" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeCheck", type="STRING", desc="pay profile code for check", value="MBAPayLater" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfileCodeACH", type="STRING", desc="pay profile code for ACH", value="MBADraft" }; 
			    arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you for your application to the Memphis Bar Association." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="MBA Membership Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
				
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
			
			variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
				
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));
				
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
			
			switch (local.formAction) {
				case "processLookup":	
					if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and not len(arguments.event.getTrimValue('mk',''))) {
						local.returnHTML = showError(errorCode='spam');
						break;
					}
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;
						default:
							application.objCommon.redirect(variables.baselink);
							break;		
					}
					break;
				case "processMemberInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;
					}
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;		
					}
					break;
				case "processMembershipInfo":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}					
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
					if (NOT variables.objCffp.testSubmission(form)) {
						local.returnHTML = showError(errorCode='spam');
						break;		
					}

					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup(memberkey=arguments.event.getTrimValue('mk',''));
					break;
			}
			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="memberkey" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
		<cfif arguments.memberkey neq ''>
			<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		</cfif>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##cboxOverlay{
					z-index: 99998 !important;
				}
				##colorbox{
					z-index: 99999 !important;
				}
				.acntLookUpBtn,.acntLookUpMessage{
					display:inline!important;
					float: right;
					width: 48%!important;
				}
				.acntLookUpBtn{	margin-right: 5px; }
				.acntLookUpMessage .span12{
					margin-left:0px !important;
				}
				##zoneMain{margin-bottom:30px;}
				@media screen and (min-width: 632px) and (max-width: 980px){
					.acntLookUpBtn {
						margin-top: 52px;
					}
				}
				@media screen and (min-width: 980px){
					.acntLookUpBtn {
						margin-top: 45px;
					}
				}				
				.acntLookUpBtn  {
				   position: absolute;
				   width: 50%;
				   height: 100%;
				   min-height: 100%;
				}
				.centerit {
				   position: absolute;
				   top: 50%;
				   width: 100%;
				   text-align: center;
				}
				.centerit button {
					position: relative;
					top: -35px;
				}
				.center-holder{
					position: relative;
				}
				.acntLookUpMessage p{font-size:15px;}
				.acntLookUpMessage {
					margin-left: 48%!important;
				}
				@media screen and (min-width: 0px){
					.acntLookUpBtn {
						margin-top: 0px!important;
					}
				}
				@media screen and (min-width: 359px) and (max-width: 368px){
					.acntLookUpBtn button{
						font-size:13px;
					}
				}
				@media screen and (max-width: 359px){
					.acntLookUpBtn button{
						font-size:11px;
					}
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
					$(".ContactTypeHolder tr:first-child td").eq(0).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(0).width());
					$(".ContactTypeHolder tr:first-child td").eq(1).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(1).width());
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}
				
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				
				$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				
				<h2 class="TitleText">#variables.strPageFields.FormTitle#</h2>
									
				<div class="CPSection step1" style="padding:0px 0px;">
					<h3>#variables.strPageFields.AccountLocatorTitle#</h3>
					<div style="padding-top:10px;">
						<div class="row-fluid center-holder">
							<div class="span4 acntLookUpBtn c">
								<div class="centerit" id="associatedMemberIDSelect">
									<button name="btnAddAssoc" type="button" id="btnAddAssoc" class="btn btnCustom">#variables.strPageFields.AccountLocatorButton#</button>
								</div>
							</div>
							<div class="span8 acntLookUpMessage frmText pull-right">
								#variables.strPageFields.AccountLocatorInstructions#
							</div>
						</div>
					</div>
				</div>
			
			</cfform>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>
		
	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
			<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfif local.qryBilledSubs.isRenewalRate>
					<cfset local.stReturn = "billedfound">
				<cfelse>
					<cfset local.stReturn = "billedjoinfound">
				</cfif>
			<cfelse>
				<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
		
				<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "activefound">
				<cfelse>
					<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
					<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfset local.stReturn = "acceptedfound">
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>	
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='contact type')>
		
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfset local.categoryFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='3151d8f2-2504-40b7-9d11-a5cef183cf9e', mode="collection", strData=local.strData)>
		<cfset local.personalFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='6b82941e-f96e-4aa9-8b27-a0809ce0d7ca', mode="collection", strData=local.strData)>
		<cfset local.orgAddressFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='f3d4eb6c-2393-483f-bc26-d6f2fe1d4aa8', mode="collection", strData=local.strData)>
		<cfset local.professionalFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='70fbbfd9-63cc-4744-8b99-ef1c2900ff34', mode="collection", strData=local.strData)>
		<cfset local.demographicFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='6062dc59-b44a-4fee-a8d8-4eade1838aff', mode="collection", strData=local.strData)>
		<cfset local.secondaryAddressFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='d5ae7827-c1d5-4d87-afbc-a61884ccaa25', mode="collection", strData=local.strData)>
		<cfset local.addressPreferenceFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='c383a2d3-0d6c-4b1b-9d3f-24b08f752ed5', mode="collection", strData=local.strData)>

		<!--- get Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.addressPreferenceFieldSetContent.strFields#" item="local.thisField">
			<cfif local.addressPreferenceFieldSetContent.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.addressPreferenceFieldSetContent.strFields[local.thisField] eq "Use this address for Mailing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>		
		<cfset local.contacttype = "">
		<cfloop collection="#local.categoryFieldSetContent.strFields#" item="local.thisField">
			<cfif local.categoryFieldSetContent.strFields[local.thisField] eq "Category">
				<cfset local.contacttype = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>				
				
		<cfset local.email = "">
		<cfloop collection="#local.categoryFieldSetContent.strFields#" item="local.thisField">
			<cfif local.categoryFieldSetContent.strFields[local.thisField] eq "Email">
				<cfset local.email = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset index=1 >
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[index] = local.qryOrgProLicenseStatuses.statusName>	
			<CFSET index=index + 1>
		</cfloop> 	
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				##content-wrapper input[type="text"] {
					width:206px!important;
				}
				##content-wrapper select{
					width:220px!important;
				}
			
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationtionIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
							
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
				
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}	
			</style>
			<script language="javascript">
				var lawSchoolSelector = '';
				var yearGraduatedSelector = '';
				var #toScript(local.demographicFieldSetContent.strfields, "demographicInfoFieldsObj")#;
				var #toScript(local.orgAddressFieldSetContent.strfields, "organizationAddressFieldsObj")#;
				
				$(document).ready(function(){
					$.each(demographicInfoFieldsObj, function (key, val) {
						if(val == 'Law School'){
							lawSchoolSelector = key;
						}else if(val == 'Law School Graduation Year'){
							yearGraduatedSelector = key;
						}
					});

					<cfif len(local.contacttype)>
						lawSchoolObj = '###variables.formName# ##'+lawSchoolSelector;
						yearGraduatedObj = '###variables.formName# ##'+yearGraduatedSelector;
							
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						if(mcSel == 'Attorney' || mcSel == 'Judge' || mcSel == 'Faculty'){
							$('##professionalLicenseSection,##professionalInfoSection').show();
						} else {
							$('##professionalLicenseSection,##professionalInfoSection').hide();
						}
						
						processContactTypeChange();
						
						$('###variables.formName# ###local.contacttype#').on('change',function(){
							processContactTypeChange();
						});
						
						function processContactTypeChange(){
							var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
							
							if(mcSel == 'Attorney' || mcSel == 'Judge' || mcSel == 'Faculty'){
								$('##professionalLicenseSection,##professionalInfoSection').show();
							} else {
								$('##professionalLicenseSection,##professionalInfoSection').hide();
							}
							
							
							if(mcSel == 'Law Student'){
								$(lawSchoolObj).parent().parent().children().first().text('*');
								$(yearGraduatedObj).parent().parent().children().first().text('*');
								$.each(organizationAddressFieldsObj, function (key, val) {
									$('###variables.formName# ##'+key).parent().parent().children().first().hide();
								});
							} else {
								$(lawSchoolObj).parent().parent().children().first().text('');
								$(yearGraduatedObj).parent().parent().children().first().text('');
								$.each(organizationAddressFieldsObj, function (key, val) {
									$('###variables.formName# ##'+key).parent().parent().children().first().show();
								});
							}
						}
					</cfif>
					<cfset local.qryEarliestLicenseDate = getCustomFieldData(orgID=variables.orgid,columnname='Earliest License Date',isGetData=false)>
					<cfset local.earliestLicenseDateColumnId = local.qryEarliestLicenseDate.columnId>
					$('##md_#local.earliestLicenseDateColumnId#').parents('tr').hide();
					$(document).on('change','input[id^="mpl_"][id$="_activeDate"]',function(){
						var _licenseDate = "";
						
						$('input[id^="mpl_"][id$="_activeDate"]').each(function(){
							var _this = $(this);
							if(_licenseDate.length > 0 ){
								if( _licenseDate >  _this.val())
									_licenseDate =  _this.val();
							} else {
								_licenseDate =  _this.val();
							}
						});
						$('##md_#local.earliestLicenseDateColumnId#').val(_licenseDate);
					});
				});
									
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					#local.categoryFieldSetContent.jsValidation#
					#local.personalFieldSetContent.jsValidation#
					<cfif len(local.contacttype)>
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						if(mcSel != 'Law Student'){		
							#local.orgAddressFieldSetContent.jsValidation#
						}
					</cfif>
					#local.secondaryAddressFieldSetContent.jsValidation#
					#local.addressPreferenceFieldSetContent.jsValidation#
					
					<cfif len(trim(local.email))>
						var email = $('###variables.formName# ###local.email#').val();
						$('##email').val(email);
					</cfif>		
					
					<cfif len(local.contacttype)>
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						
						if(mcSel == 'Law Student'){
							lawSchoolVal = $('###variables.formName# ##'+lawSchoolSelector).val();
							yearGraduatedVal = $('###variables.formName# ##'+yearGraduatedSelector).val();
							
							if(lawSchoolVal == ''){
								arrReq.push('Law School is required.');
							}
							if(yearGraduatedVal == ''){
								arrReq.push('Graduated Year  is required.');
							}
							
						} else if(mcSel == 'Attorney'){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $(".mpl_"+val+"_licensenumber").parent().prev().text();
									if($(".mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
									if($(".mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}	
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						} else if(mcSel == '') {
							arrReq.push('Category is required.');
						}
					</cfif>
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#						
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) {
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>
					
					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text);											
					   },
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##state_table tbody tr').length == 1){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##state_table tbody tr').length == 1){
								$("##state_table").hide();
							}
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text());	
							});
						}
					});	
					
					function licenseChange(isChecked,val,text)	{
						$("##state_table").show();
						if(isChecked){								
							$('##state_table tbody tr:last').after('<tr id="tr_state_'+val+'">'
							+'<td class="visible-phone">State Name:</td>'
															+'<td align="right" class="tsAppBodyText">'+text+'</td>'
															+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseNumberLabel#:</td>'
															+'<td align="center" class="tsAppBodyText">'
															+'	<input size="13" maxlength="13" name="mpl_'+val+'_licensenumber" class="mpl_'+val+'_licensenumber" type="text" value="" />'
															+'	<input name="mpl_'+val+'_licensename" class="mpl_'+val+'_licensename" type="hidden" value="'+text+'" />'
															+'</td>'
															+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy):</td>'
															+'<td align="center" class="tsAppBodyText">'
															+'	<input size="13" maxlength="10" name="mpl_'+val+'_activeDate" class="mpl_'+val+'_activeDate tsAppBodyText" id="mpl_'+val+'_activeDate" type="text" value="" />'
															+'</td>'
															+'<td align="center" class="tsAppBodyText" style="display:none">'
															+'	<select name="mpl_'+val+'_status" class="mpl_'+val+'_status"><option value="active" selected="selected">Active</option></select>'
															+'</td>'

													+'</tr>');
							if ($("##tr_state_"+val).is(':visible') &&  $('.mpl_'+val+'_activeDate').is(':visible')) {
								mca_setupDatePickerField('mpl_'+val+'_activeDate');
							}
							$('.mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:pointer !important');							
						} else {
							$("##tr_state_"+val).remove();								
						}
						if($('##state_table tbody tr').length == 1){
							$("##state_table").hide();
						}	
					}							
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div>
				<cfif len(variables.strPageFields.FormTitle)>
						<h2 class="TitleText" id="FormIntro">#variables.strPageFields.FormTitle#</h2>
				</cfif>	
			<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="hidden" id='email' name="email" value=''>
			<cfif NOT local.isLoggedIn AND local.identifiedMemberID GT 0>
				<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
			</cfif>
			<cfinclude template="/model/cfformprotect/cffp.cfm">			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			<div id="content-wrapper" class="row-fluid">

				<cfif len(variables.strPageFields.Step1TopContent)>
					<h3>#variables.strPageFields.Step1TopContent#</h3>
				</cfif>
				
				<div class="row-fluid">
					<div class="span12 tsAppSectionHeading">#local.categoryFieldSetContent.fieldSetTitle#</div>
					<div class="span6 tsAppSectionContentContainer">
						#local.categoryFieldSetContent.fieldSetContent#
					</div>
				</div></br>

				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.personalFieldSetContent.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.personalFieldSetContent.fieldSetContent#
					</div>
				</div>
				
				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.orgAddressFieldSetContent.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">	
						#local.orgAddressFieldSetContent.fieldSetContent#
					</div>
				</div>
				
				<div class="row-fluid" id="professionalLicenseSection">
					<div class="row-fluid tsAppSectionHeading">Professional License Information</div>
					<div class="row-fluid tsAppSectionContentContainer">
						<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
						<table cellpadding="3" border="0" cellspacing="0">		
							<tr class="top">
								<th class="tsAppBodyText" colspan="3" align="left">
									&nbsp;
								</th>
							</tr>							
							<tr align="top">
								<td class="tsAppBodyText" width="10">&nbsp;</td>
								<td class="tsAppBodyText" width="365">Professional License:</td>
								<td class="tsAppBodyText">
									<select id="mpl_pltypeid" name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
										<cfloop query="local.qryOrgPlTypes">
											<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
											<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
										</cfloop>
									</select>
								</td>
							</tr>
							<tr class="top">
								<td class="tsAppBodyText" width="10"></td>
								<td class="tsAppBodyText"></td>
								<td class="tsAppBodyText"></td>
							</tr>
						</table>
						<table cellpadding="3" border="0" cellspacing="0">
							<tr>
								<td class="tsAppBodyText" width="375">&nbsp;</td>
								<td>
									<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
										<thead class="hidden-phone">
											<tr valign="top">
												<th align="center" class="tsAppBodyText">State Name</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
											</tr>
										</thead>
										<tbody>
										<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
											<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
												<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
												<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
												<cfset  local.license_status  = 'Active'>
												<tr class="tr_state_#local.thisItem#" id="tr_state_#local.thisItem#">
													<td class="visible-phone">State Name:</td>
													<td align="right" class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</td>
													<td align="center" class="tsAppBodyText">
														<input name="mpl_#local.thisItem#_licensenumber" id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText mpl_#local.thisItem#_licensenumber" type="text" value="#local.license_no#" size="13" maxlength="13" />
														<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" class="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
													</td>
													<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy):</td>
													<td align="center" class="tsAppBodyText">
														<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText mpl_#local.thisItem#_activeDate" size="13" maxlength="10" />
														<cfsavecontent variable="local.datejs">
															<cfoutput>
															<script language="javascript">
																$(document).ready(function() { 
																	mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
																});
															</script>
															<style type="text/css">
															##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
															</style>
															</cfoutput>
														</cfsavecontent>
														<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
													</td>
													<td align="center" class="tsAppBodyText">
														<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText mpl_#local.thisItem#_status">
															<option value="">Please Select</option>
															<cfloop collection="#local.licenseStatus#" item="i" >
																	<option <cfif local.license_status eq LCase(local.licenseStatus[i]) >selected="selected"</cfif> value="#LCase(local.licenseStatus[i])#">#local.licenseStatus[i]#</option>
															</cfloop>															
														</select>
													</td>
												</tr>
											</cfloop>
											<tr></tr>
										<cfelse>
											<tr></tr>
										</cfif>									
										</tbody>
									</table>
								</td>
							</tr>					
						</table>
					</div>
				</div>
				
				<div class="row-fluid" id="professionalInfoSection">
					<div class="row-fluid tsAppSectionHeading">#local.professionalFieldSetContent.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.professionalFieldSetContent.fieldSetContent#
					</div>
				</div>

				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.demographicFieldSetContent.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.demographicFieldSetContent.fieldSetContent#
					</div>
				</div>	

				<div class="row-fluid" id="addresspreference">
					<div class="row-fluid tsAppSectionHeading">#local.secondaryAddressFieldSetContent.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.secondaryAddressFieldSetContent.fieldSetContent#
					</div>
				</div>

				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.addressPreferenceFieldSetContent.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.addressPreferenceFieldSetContent.fieldSetContent#
					</div>
				</div>
			
				<button name="btnContinue" type="submit" class="tsAppBodyButton  btn btn-default" onClick="hideAlert();">Continue</button>
			</div>
			#application.objWebEditor.showEditorHeadScripts()#
			
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

			<script language="javascript">	
				$(document).ready(function(){
					<cfloop query="local.qryOrgAddressTypes">
						addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
						function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
							var _address = _this.val();
							
							if(_address.length >0){
								if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
									$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
								}
							} else {
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							}
						}
						
						$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
						});
					</cfloop>
				});
					
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			
			</form>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>
		
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	
		
		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		
		<cfset local.memberShipCategoryColumnName = "Contact Type">
		<cfset local.memberShipCategoryStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName=local.memberShipCategoryColumnName)>
		<cfset local.membershipCategoryColumnId = local.memberShipCategoryStruct.columnId>
		<cfif local.membershipCategoryColumnId NEQ '' AND local.membershipCategoryColumnId NEQ 0>
			<cfset local.membershipCategorySelected = arguments.rc['md_'&local.membershipCategoryColumnId]>
			<cfset local.membershipCategorySelectedValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(variables.orgid,local.memberShipCategoryColumnName,local.membershipCategorySelected)>
			<cfset local.contactTypeValue = local.membershipCategorySelectedValue>
		</cfif>
		
		<cfset local.earliestLicenseDate = "">
		<cfif structKeyExists(arguments.rc,"mpl_pltypeid")>
			<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
				<cfif len(arguments.rc['mpl_#local.key#_activeDate'])>
					<cfif len(local.earliestLicenseDate) AND (local.earliestLicenseDate GT arguments.rc['mpl_#local.key#_activeDate']) OR not len(local.earliestLicenseDate)>
						<cfset local.earliestLicenseDate = arguments.rc['mpl_#local.key#_activeDate']>						
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
		<cfset local.strResult1 = local.objSaveMember.saveData(runImmediately=1)>
		
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only adds history if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>		

 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>
 		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
					#local.result.jsAddonValidation#
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();
						
						if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
							arrReq[arrReq.length] = " Select Membership.";
						}
								
						#local.result.jsValidation#
						
						$('div [data-setuid="AA4D08CA-0A8C-4038-967C-0E0B0AFA8375"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
							var minlp = $(this).data("minallowed");
							if($('input:checkbox:checked',this).length == 0){
								arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $('legend',this).eq(0).text()+".";
							}
						});

						$('div [data-setuid="AA4D08CA-0A8C-4038-967C-0E0B0AFA8375"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
							var maxlp = $(this).data("maxallowed");
							if($('input:checkbox:checked',this).length > maxlp){
								arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $('legend',this).eq(0).text()+".";
							}
						});

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}					

						mc_continueForm($('###variables.formName#'));
						return false;
					}
					
					function subscriptionRadioHandler() {						
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").prop('checked', false).attr("disabled", true);
						$("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").prop('checked', false).attr("disabled", false);
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio][data-frequencyuid='"+$(this).attr("data-frequencyuid")+"']").attr("disabled", true).attr("disabled", false);

						var frequencyUid = $(this).attr("data-frequencyuid");	
						$("##sub#local.subscriptionID#_addons .subAvailableRates").each(function() {
							var subRateControls = $(this).find(".subRateCheckbox[type=radio]");
							if(subRateControls.length > 0){
								var matchedFrequencyCount = subRateControls.filter("[data-frequencyuid='"+frequencyUid+"']").length;
								if(matchedFrequencyCount == 0){
									subRateControls.attr("disabled", false);
								}								
							}							
						});
					}

					function addOnSubscriptionRadioHandler() {
						$(this).parent().parent().parent().find('input[type="checkbox"].subCheckbox').prop('checked', true).attr("disabled", false);
					}

					function addOnSubscriptionCheckboxHandler() {
						if($(this).is(':checked')){
							$(this).parents('label').next().find(".subRateCheckbox[type=radio][data-frequencyuid='"+$("[name=sub#local.subscriptionID#_rate][type=radio]:checked").attr("data-frequencyuid")+"']").prop('checked', true);
						}else{
							$(this).parents('label').next().find('.subRateCheckbox[type=radio]').prop('checked', false);
						}						
					}
					if($("[name=sub#local.subscriptionID#_rate]").length > 1){
						$("[name=sub#local.subscriptionID#_rate][type=radio]").on('click',subscriptionRadioHandler);
					} else if($("[name=sub#local.subscriptionID#_rate]").length == 0){
					} else {
						var _freqUID = $('##sub#local.subscriptionID#_selectedRate .subRateCheckbox').attr('data-frequencyuid');
						
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").attr("disabled", true);
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio][data-frequencyuid='"+_freqUID+"']").attr("disabled", true).attr("disabled", false);
						
					}
					$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").on('click',addOnSubscriptionRadioHandler);
					$("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").on('click',addOnSubscriptionCheckboxHandler);
					
				</script>
 			</cfoutput>
 		</cfsavecontent>

 		<cfhtmlhead text="#local.resultHtmlHeadText#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.FormTitle)>
				<h2 class="TitleText" id="FormIntro">#variables.strPageFields.FormTitle#</h2>
			</cfif>	
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid" >
				<div class="row-fluid">
					<div class="span12">
						<cfif len(variables.strPageFields.Step2TopContent)>
							<h3>#variables.strPageFields.Step2TopContent#</h3>
						</cfif>
						
						#local.result.formcontent#
					</div>
				</div>
			</div>

			<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>
	
	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfset local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		
		<cfset local.arrPayMethods = ArrayNew(1)>
		<cfif len(trim(variables.strPageFields.ProfileCodeCredit)) AND trim(variables.strPageFields.ProfileCodeCredit) NEQ "NULL">
			<cfset ArrayAppend(local.arrPayMethods,variables.strPageFields.ProfileCodeCredit)>
		</cfif>
		<cfif len(trim(variables.strPageFields.ProfileCodeCheck))  AND trim(variables.strPageFields.ProfileCodeCheck) NEQ "NULL">
			<cfset ArrayAppend(local.arrPayMethods,variables.strPageFields.ProfileCodeCheck)>
		</cfif>
		<cfif len(trim(variables.strPageFields.ProfileCodeACH))  AND trim(variables.strPageFields.ProfileCodeACH) NEQ "NULL">
			<cfset ArrayAppend(local.arrPayMethods,variables.strPageFields.ProfileCodeACH)>
		</cfif>
		
		<cfset local.strReturn = 
			application.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
			
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div>
				<cfif len(variables.strPageFields.FormTitle)>
					<h2 class="TitleText" id="FormIntro">#variables.strPageFields.FormTitle#</h2>
				</cfif>	
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
				<cfinput type="hidden" name="fa" id="fa" value="processPayment">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
				<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
				<cfloop collection="#arguments.rc#" item="local.thisField">
					<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
						or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
						or left(local.thisField,5) eq "mccf_"
						or left(local.thisField,3) eq "sub">
						<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
					</cfif>
				</cfloop>
				<cfinclude template="/model/cfformprotect/cffp.cfm">
			
				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

				<cfif len(variables.strPageFields.Step3TopContent)>
					<h3>#variables.strPageFields.Step3TopContent#</h3>
				</cfif>
				<div class="BodyText">						
					#local.strResult.formContent#
					<br/>
				</div>
				<div class="BodyText"><b>Total Price</b></div>
				<div class="BodyText">						
					Total Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
				</div>
				<br/>
				<cfif local.paymentRequired>
					#local.strReturn.paymentHTML#
				<cfelse>
					<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();" disabled>Continue</button>
					<button name="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
				</cfif>
				<script>
					$(document).ready(function(){
						setTimeout(function() {
							$('button').attr('disabled',false);
						}, 1200);
					});
				</script>
				</cfform>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="true" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>
			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 
		
		<!--- find the invoice for the subscription and pay it; find all invoices for associating CC --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
		
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- Save card on file to subscription and all invoices --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#,#variables.strPageFields.ProfileCodeACH#",arguments.rc.mccf_payMeth)>
			<!--- Payment and accounting --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.categoryFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='3151d8f2-2504-40b7-9d11-a5cef183cf9e', mode="confirmation", strData=arguments.rc)>
		<cfset local.personalFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='6b82941e-f96e-4aa9-8b27-a0809ce0d7ca', mode="confirmation", strData=arguments.rc)>
		<cfset local.orgAddressFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='f3d4eb6c-2393-483f-bc26-d6f2fe1d4aa8', mode="confirmation", strData=arguments.rc)>
		<cfset local.professionalFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='70fbbfd9-63cc-4744-8b99-ef1c2900ff34', mode="confirmation", strData=arguments.rc)>
		<cfset local.demographicFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='6062dc59-b44a-4fee-a8d8-4eade1838aff', mode="confirmation", strData=arguments.rc)>
		<cfset local.secondaryAddressFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='d5ae7827-c1d5-4d87-afbc-a61884ccaa25', mode="confirmation", strData=arguments.rc)>
		<cfset local.addressPreferenceFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='c383a2d3-0d6c-4b1b-9d3f-24b08f752ed5', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
				
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<fieldset>
						<div class="row-fluid HeaderText">#replace(replace(variables.strPageFields.ConfirmationContent,'<p>',''),'</p>','')#</br></div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="row-fluid HeaderText">
						<div class="span12">#replace(replace(variables.strPageFields.ConfirmationContent,'<p>',''),'</p>','')#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div class="row-fluid HeaderText">
					<div class="span12">You have received an application for membership.</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails BodyText">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.categoryFieldSetContent.fieldSetContent#				
				#local.personalFieldSetContent.fieldSetContent#
				#local.orgAddressFieldSetContent.fieldSetContent#
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>	
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br/>
				
				#local.professionalFieldSetContent.fieldSetContent#
				#local.demographicFieldSetContent.fieldSetContent#
				#local.secondaryAddressFieldSetContent.fieldSetContent#
				#local.addressPreferenceFieldSetContent.fieldSetContent#

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->		
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>		
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.memberEmail.from },
                            emailto=[{ name="", email=variables.memberEmail.to }],
                            emailreplyto=variables.ORGEmail.to,
                            emailsubject=variables.memberEmail.SUBJECT,
                            emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
                            emailhtmlcontent=local.confirmationHTMLToMember,
                            siteID=arguments.rc.mc_siteinfo.siteid,
                            memberID=val(variables.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to Memphis Bar", emailContent=local.confirmationHTMLToStaff)>
		
		<cftry>
			<cfset local.uid = createuuid()>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
			<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<html>
					<head>
					
					</head>
					<body>
						#local.confirmationHTMLToMember#
					</body>
					</html>
				</cfoutput>
			</cfdocument>
			
			<cfset local.strPDF = structNew()>
			<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
			<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
			<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
			<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
			<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
			<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Unable to add document to member record.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
			</cfcatch>
		</cftry>
		
		<cfreturn local.confirmationHTMLToMember>
	</cffunction>
	
	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div>						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>

		<cfreturn getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)>
	</cffunction>

	<cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', totalFullPrice = 0 }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">			
		</cfif>
		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								- 
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
										<!--- #local.thisRate.rateName#  --->										
										<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
											#local.thisRate.rateName#  (<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> Full)
											<cfset local.strReturn.totalFullPrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
										<cfelse>
										   <cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid )>
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
													<cfif arguments.recursionLevel eq 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>																									
														#local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
													<cfelseif arguments.recursionLevel gt 1>														
														#local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>												
													</cfif>	
												</cfif> 						
											</cfloop>											
										</cfif>
									</cfif>
								</cfloop>
							</span>
						</div>
					</div>	
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">		
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false />							
									<cfset local.pcNumCounter = 1 />									
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false />
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true />
										</cfif>										
										<cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree) />
										
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
										<cfif len(trim(local.thisAddonSubForm.formContent))>										
											<cfset local.selectionFound = true />
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
											</cfif>
											
											<cfset local.pcNumCounter = local.pcNumCounter + 1 />											
											#local.thisAddonSubForm.formContent#
										</cfif>									
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>						
						</div>
					</cfif>					
				</cfif>
			</cfoutput>				
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>
	
    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="row-fluid tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class=" row-fluid tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "billedfound">					
					#variables.strPageFields.BilledMessage#	
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "activefound" OR arguments.errorCode eq "acceptedfound">
					<a href ='/?pg=login'>#variables.strPageFields.ActiveAcceptedMessage#</a>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="getCustomFieldData" access="private" output="false" returntype="Query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="columnName" type="string" required="true">
		<cfargument name="columnValueString" type="string" required="false">
		<cfargument name="isGetData" type="boolean"  required="true">
		<cfset var local = structNew()>
		
		<cfquery name="local.getColumnValues" datasource="#application.dsn.membercentral.dsn#">
			select <cfif NOT arguments.isGetData> TOP 1 mdcv.columnID <cfelse> mdcv.columnID , mdcv.valueID,  mdcv.columnValueString </cfif>
			from ams_memberDataColumns mdc
			inner join ams_memberdatacolumnvalues mdcv
				on mdcv.columnID = mdc.columnID
				and mdc.columnName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnName#">
				<cfif isDefined("arguments.columnValueString") and len(trim(arguments.columnValueString))>
					and mdcv.columnValueString = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnValueString#">
				</cfif>
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
		</cfquery>

		<cfreturn local.getColumnValues>
	</cffunction>
</cfcomponent>