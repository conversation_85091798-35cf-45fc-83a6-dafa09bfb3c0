<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfset local.dropboxAppKey = "">
<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
	<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
</cfif>

<cfsavecontent variable="local.js">
	<cfoutput>
	<link rel="canonical" href="http://#application.objPlatform.getCurrentHostname()#">
	<script type='text/javascript' src='/assets/common/javascript/search.js#local.assetCachingKey#'></script>

	<script type="text/javascript">
		var local_siteid = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#';
		var local_sitecode = '#session.mcStruct.siteCode#';
		var local_spiu = '#application.paths.images.url#';
		var local_caserefs = [#listQualify(arrayToList(session.mcstruct.doccartCaseRefs), "'")#];

		<cfif val(session.mcstruct.lastSearch.sid) gt 0>
			var local_bid = #session.mcstruct.lastSearch.bid#;
			var lastsearchid = #session.mcstruct.lastSearch.sid#;
			<cfif local.dataStruct.tab eq "PMD">
				var local_bid = #local.dataStruct.medlineBucketID#;
				searchbucket(local_siteid,local_bid,46);							
			</cfif>							
		<cfelse>

			<cfset local.depositionBucketID = local.dataStruct.objSearch.getBucketIDByType(siteID=local.rc.mc_siteinfo.siteID, bucketType='Depositions')>
			var lastsearchid = 0;
			<cfif local.dataStruct.tab eq "PMD">
				var local_bid = #local.dataStruct.medlineBucketID#;
				searchbucket(local_siteid,local_bid,46);							
			<cfelse>
				var local_bid = #local.dataStruct.depositionBucketID#;
			</cfif>
		</cfif>

		loadSearchGlobals(local_siteid,local_bid,local_spiu,local_caserefs,local_sitecode);
		setSearchID(lastsearchid);
		rebuildTSSearchBar();

		function d_doSearchPN(_tab,_srow){
			var fd = {};
			fd.tab = _tab;
			fd.startrow = _srow;
			$('##s_listing > .tab-pane')
				.html('<div align="center"><img src="assets/common/images/indicator.gif" alt="Loading..." width="100" height="100"></div>')
                .load('/?pg=myDocuments&mode=stream',fd,	function() {
            });
		}
		$(document).ready(function(){
            var fd = {};
			fd.tab = '#local.dataStruct.tab#';
            $('##s_listing > .tab-pane')
				.html('<div align="center"><img src="assets/common/images/indicator.gif" alt="Loading..." width="100" height="100"></div>')
                .load('/?pg=myDocuments&mode=stream',fd,function() {});
			$('a[data-toggle=tooltip]').tooltip();
        });
	</script>			
	<style type="text/css">
		input.cal { background:##fff url(/assets/common/images/search/calendar.png) no-repeat right center; padding-right:18px; border:1px solid ##7f9db9; font-size:9pt; width:100px; }
		div.s_rbnm { padding:0 0 4px 0; }
		div.s_rnfne { padding:10px 0; font-weight:bold; }
		div.s_row { padding:6px; }
		div.s_row_alt { background-color:##DEDEDE; }
		div.s_dtl { margin-left:20px; margin-top:4px; }
		div.s_opt { padding:3px; }
		div.s_act { float:right; padding:0 0 5px 5px; }
		a.ctv { text-decoration:none; }
		div.s_pgbtm { padding:6px 3px 0px 3px;text-align:right; }
		div.s_pgtop { padding:3px 3px 6px 3px; }
		div.s_rhrd { background-color:##DEDEDE;padding:3px;margin-bottom:3px; min-height:18px; }
		tr.s_rhrdtr { background-color:##DEDEDE;padding:3px; }
		div.s_nli { margin:10px 10px 0 0;padding:6px;border:1px solid ##ccc; }
		div.s_nliint { margin-bottom:10px; }
		div.s_options { margin-top:10px; clear:both;}
		div.s_err { font-weight:bold; color:##f00; }
		div.s_lh2 { padding:2px 0; }
		div##atcdiv { visibility:hidden; position:absolute; left:-300px; z-index:10000; }
		div.modalpopup { width:410px; }
		div##modalpopuphead { font-weight:bold; margin-bottom:6px; }
		div.modalpopupcontent { background-color:##BBB; border:1px solid ##666; border-bottom:3px solid ##666; color:##000; padding:10px; }
		select##modalpopupSuggestResults { position:absolute; visibility:hidden; }
		div.modalpopupinput { margin-top:6px; }
		div.modalpopuparrow { background-image:url(/assets/common/images/search/modal_triangler.gif); background-repeat:no-repeat; height:12px; position:relative; top:1px; left:385px; }
		div.modalpopuparrow span { display:none; }
		span##modalpopupreq { float:right; color:##f00; font-style:italic; font-weight:bold; }
		a.currSort { text-decoration:none; font-weight:bold; }
		div##s_caldiv { position:absolute;visibility:hidden;background-color:white;layer-background-color:white; }
	
		ul##s_tabs { 
			border-bottom:1px solid ##ccc; 
			margin:0; 
			padding-bottom:21px; 
			padding-left:10px;
		}
		ul##s_tabs ul, ul##s_tabs li { display:inline; list-style-type:none; margin:0; padding:0; }	
		ul##s_tabs a:link, ul##s_tabs a:visited { background:##E8EBF0; border:1px solid ##ccc; color:##666; float:left; line-height:14px; margin-right:8px; padding:2px 10px; text-decoration:none; }
		ul##s_tabs a:link.active, ul##s_tabs a:visited.active { border-bottom:1px solid ##fff; color:##000; }
		ul##s_tabs a:hover { color:##f00; }
		div##s_tabsetSM ul##s_tabs li##nav_SM a, 
		div##s_tabsetSR ul##s_tabs li##nav_SR a, 
		div##s_tabsetNS ul##s_tabs li##nav_NS a, 
		div##s_tabsetRS ul##s_tabs li##nav_RS a, 
		div##s_tabsetAD ul##s_tabs li##nav_AD a, 
		div##s_tabsetVC ul##s_tabs li##nav_VC a { background:##fff; border-bottom:1px solid ##fff; color:##000; }
		ul##s_tabs ul a:hover { color:##f00 !important; }
		div##s_listing { 
			/*border:1px solid ##ccc; */
			border-top:none; 
			clear:both; 
			margin:0px; 
			padding:5px 0;
		}
		.bucketHeaderButton {-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px; padding: 2px;}	
		.bucketListButton {-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px;width:100%; margin-bottom:10px;background-color:##a0ce67;color:##000000;font-size:9px;}
		.hiddenButton {display:none;}
		.google-drive-hidden, .download-pdf-label, .download-tiff-label {display:none;}
		.grayed-out-img {
    		opacity: 0.4;
    		filter: alpha(opacity=40); /* msie */
		}
		.dropb-img { background-image:url(/assets/common/images/dropbox-icon-ios.png); background-repeat:no-repeat; height:15px; position:relative; top:1px; }
	</style>
	<cfif len(local.dropboxAppKey)>
		<script type="text/javascript" src="https://www.dropbox.com/static/api/2/dropins.js" id="dropboxjs" data-app-key="#local.dropboxAppKey#"></script>
		<script>
			window.___gcfg = { parsetags: 'explicit' };
		</script>
	</cfif>
	<script language="javascript">
		function downloadDoc(docid) {
            remote = open('/?pg=tsDocDownload&da=download&mode=direct&did=' + docid, "docViewer", "scrollbars=no,resizable,width=750,height=450");
        }
		function downloadDocImmedate(docid,format) {
			remote = document.location.href='/?pg=tsDocDownload&da=download&mode=direct&immediate=1&t=' + format + '&did=' + docid;
		}
		function viewVerdict(vid) {
			remote = open('/?pg=myDocuments&tab=VPV&mode=direct&verdictid=' + vid, "docViewer", "scrollbars=yes,resizable,width=600,height=400");
		}
		function viewDAReport(rid) {
			remote = open('/?pg=myDocuments&tab=VDA&mode=direct&reportid=' + rid, "docViewer", "scrollbars=yes,resizable,width=725,height=400");
		}
		function viewDaubertReport(rid) {
			remote = open('/?pg=myDocuments&tab=VDT&mode=direct&reportid=' + rid, "docViewer", "scrollbars=yes,resizable,width=725,height=400");
		}
		function downloadMedlineDoc(rid) {
			remote = open('/?pg=myDocuments&tab=VMD&mode=direct&reportid=' + rid, "docViewer", "scrollbars=yes,resizable,width=725,height=600");
		}
		<cfif len(local.dropboxAppKey)>
			function doSaveToDropbox(thisArr) {
				if (thisArr.length > 0) {
					var filesArr = [];
					var tmpFilesArr = JSON.stringify(thisArr).replace(/\^~~~\^/g,'');
					var docArrFinal = JSON.parse(tmpFilesArr);
					
					docArrFinal.forEach(function(thisIndex) {
						var thisItemstr = {};
						thisItemstr.url = thisIndex.url;
						thisItemstr.filename = thisIndex.docname;
						filesArr.push(thisItemstr);
					});	

					var tmpFilesArr2 = JSON.stringify(filesArr).replace(/\^~~~\^/g,'');
					var docArrFinal2 = JSON.parse(tmpFilesArr2);
					var options = {		
						files: docArrFinal2,																						
						success: function () {
							var isSelectedItem = false;
							docArrFinal.forEach(function(thisIndex) {
								if(thisIndex.selected == true){
									$("##dropb_btn_" + thisIndex.rowid).html('<i class="icon-check icon-1x"></i> Saved to Dropbox');
									isSelectedItem = true;
								}
							});	
							/* if all files in screen are being saved */
							if (!isSelectedItem){
								$("##dropdownMenuLink").html('<b><i class="icon-check icon-1x"></i> Saved to Dropbox</b>');
								$("##paginationBar a").unbind('click');
								$("##paginationBar a").removeAttr('style');
							}
						},
						progress: function (progress) {
							var isSelectedItem = false;
							docArrFinal.forEach(function(thisIndex) {
								if(thisIndex.selected == true){
									$("##dropb_btn_" + thisIndex.rowid).html('<i class="icon-spin icon-spinner"></i> Saving file...');
									isSelectedItem = true;
								}
							});	
							/* if all files in screen are being saved */
							if (!isSelectedItem){
								$("##dropdownMenuLink").html('<i class="icon-spin icon-spinner"></i> Saving files...');
							}					
						},
						cancel: function () {						
							docArrFinal.forEach(function(thisIndex) {
								$("##dropb_btn_" + thisIndex.rowid).attr("disabled", false);
							});	
							$("##paginationBar a").unbind('click');
							$("##paginationBar a").removeAttr('style');	
						},
						error: function (errorMessage) {
							alert("We ran into an error saving your file. Try again.");
							docArrFinal.forEach(function(thisIndex) {
								$("##dropb_btn_" + thisIndex.rowid).attr("disabled", false);
							});	
							$("##paginationBar a").unbind('click');
							$("##paginationBar a").removeAttr('style');
						}
					};																				
					Dropbox.save(options);
				} else {
					alert("Error found");	
				}
			}
			function doDropboxSave(docid,filename,format,rowid) {
				var getURL = function(r) {
					doSaveToDropbox(r);
					$("##dropb_pdf_img_" + rowid).removeClass("grayed-out-img");
				};
				var docArr = [];
				var docstr = {};
				docstr.docid = docid;
				docstr.docname = filename;
				docstr.format = format;
				docstr.rowid = rowid;
				docstr.selected = true;
				docstr.url = "";
				docArr.push(docstr);
				$("##dropb_btn_" + rowid).attr("disabled", true);
 				var tmpDocArr = JSON.stringify(docArr).replace(/\^~~~\^/g,'');
				var objParams = { docarr:tmpDocArr };
				TS_AJX('MYDOCUMENTS','s3DocumentURL',objParams,getURL,getURL,10000,getURL);																
			}				
			function doDropboxSaveAll() {
				var getURL = function(r) {
					doSaveToDropbox(r)
				};	
				var docArr = [];
				$('.drop-box-file-list').each(function(i,o) {
					var docstr = {};
					docstr.docid = $(this).attr('data-elmid');
					docstr.docname = $(this).attr('data-elmname');
					docstr.format = $(this).attr('data-elmext');
					docstr.rowid = $(this).attr('data-rowid');
					docstr.selected = false;
					docstr.url = "";
					docArr.push(docstr);
				});
				$("##paginationBar a").css({"color":"##888888", cursor: "default"}).click(function(e) {
					e.preventDefault();
				});
				docArr.forEach(function(thisIndex) {
					$("##dropb_btn_" + thisIndex.rowid).attr("disabled", true);
				});
				var tmpDocArr = JSON.stringify(docArr).replace(/\^~~~\^/g,'');
				var objParams = { docarr:tmpDocArr };
				TS_AJX('MYDOCUMENTS','s3DocumentURL',objParams,getURL,getURL,10000,getURL);													
			}		
		</cfif>
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.js,'\s{2,}',' ','ALL')#">	

<cfsavecontent variable="local.htmlhead">
	<cfoutput>
		<script src="/sitecomponents/COMMON/javascript/webviewer/6.3.2/webviewer.min.js"></script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.htmlhead#">
