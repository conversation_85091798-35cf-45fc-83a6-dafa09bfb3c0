<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true,true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search #s_categoryid").focus();
				return false;
			}
			else {
				$('#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}			
		}
		$(document).ready(function(){
			mca_setupDatePickerRangeFields('s_datefrom','s_dateto');
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>
<form name="frms_Search"  id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	<div class="control-group">
		<label class="control-label" for="s_type">Category:</label>
		<div class="controls">
			<select name="s_categoryid"  id="s_categoryid">
				<option value="">All Categories</option>
				<cfloop query="local.categories">
					<option value="#local.categories.categoryID#" 
						<cfif local.strSearchForm.s_categoryid eq local.categories.categoryID>selected="selected"</cfif>
						<cfif local.categories.itemCount eq 0>disabled="true" style="background-color:##eee;"</cfif>>
						<cfloop from="1" to="#listLen(local.categories.thePath,".")#" index="local.x">
							<cfif local.x gt 1>&nbsp;&nbsp;&nbsp;</cfif>
						</cfloop>#local.categories.categoryname# 
						<cfif local.categories.itemCount gt 0>(#local.categories.itemCount#)</cfif>
					</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label">Product Date Between:</label>
		<div class="controls">
			<input type="text" id="s_datefrom" name="s_datefrom" value="#DateFormat(local.strSearchForm.s_datefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol"> and 
			<input type="text" id="s_dateto" name="s_dateto" value="#DateFormat(local.strSearchForm.s_dateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_datefrom','s_dateto');return false;">clear dates</a>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one"><b>Without</b> the words:</label>
		<div class="controls">
			<input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm"  id="s_frm" value="1">
			<button type="submit" name="s_btn"  id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>

</cfoutput>