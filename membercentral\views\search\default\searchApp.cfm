<cfscript>
local.dataStruct = attributes.data;
local.rc = attributes.event.getCollection();
</cfscript>

<cfinclude template="/views/search/commonJS.cfm">
<cfinclude template="/views/search/commonStyles.cfm">
<cfinclude template="/views/search/default/commonSearchJS.cfm">

<cfhtmlhead text="<META NAME=""ROBOTS"" CONTENT=""INDEX,NOFOLLOW"">">

<cfoutput>
    <!--- searchid holder for reference --->
    <div id="bk_sid_holder" style="display:none;">#local.dataStruct.intSearchID#</div>
    <div id="tsSearchAppWrapper">
        <div>
            <!--- tabs --->
            <cfif arrayLen(local.dataStruct.strTopBanner.resultArr)>
                <cfset local.tabHighlight = 1>
                <cfif local.dataStruct.searchAction eq "OtherTools">
                    <cfset local.tabHighlight = arrayLen(local.dataStruct.strTopBanner.resultArr)>
                </cfif>
                <div class="tsAppBodyText" id="tssearchbararea">
                    <ul id="tssearchbar" class="tsAppB50">
                        <cfloop from="1" to="#arrayLen(local.dataStruct.strTopBanner.resultArr)#" index="local.thisEl">
                            <li id="nav_#local.dataStruct.strTopBanner.resultArr[local.thisEl].tab#">#local.dataStruct.strTopBanner.resultArr[local.thisEl].tablink#</li>
                        </cfloop>
                    </ul>
                    <ul id="tsdocumentviewerbar" class="tsAppB50" style="display:none;margin-bottom:10px;"></ul>
                    <ul id="tsinlinecartbar" class="tsAppB50"  style="display:none;margin-bottom:10px;">
                        <li id="cartnav_checkout"><a href="javascript:doCheckoutDocumentCart(#session.cfcuser.memberdata.memberid#, #local.dataStruct.hasMissingTaxInfo#);"><i class="icon-shopping-cart" style="vertical-align: inherit;"></i> Checkout</a></li>
                        <li id="cartnav_close"><a href="javascript:cart_closeInlineCart();"><i class="icon-remove-circle" style="vertical-align: inherit;"></i> Close Cart</a></li>
                    </ul>
                </div>
            </cfif>

            <div id="s_listing" class="tsAppBodyText">
                <table cellspacing="0" cellpadding="0" width="100%">
                <tr valign="top">
            </cfoutput>
                <!--- bucket list --->
                <cfif local.dataStruct.bitShowBucketList>
                    <cfoutput>
                    <td class="tsAppBodyText tsAppB50 s_row_alt" width="210" style="padding:12px 10px 8px 8px;">
                        <div id="bk_list">
                    </cfoutput>
                        <cfoutput query="local.dataStruct.qryBuckets" group="bucketGroup">
                            <div style="font-weight:bold;padding:0 0 4px 0;"><span style="float:right;" id="bk_#local.dataStruct.qryBuckets.bucketid#_grpnum"></span>#UCASE(replaceNoCase(local.dataStruct.qryBuckets.bucketGroup,"*shortname*",local.rc.mc_siteinfo.sitename))#</div>
                            <cfif local.dataStruct.qryBuckets.bucketGroup eq "TrialSmith Documents" and local.dataStruct.hasDepositionBucket and local.dataStruct.intSearchID gt 0 and local.dataStruct.searchAction eq "doSearch">
                                <button class="tsAppBodyButton bucketListButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file"></i> Download Search Report</button>
                            </cfif>										
                            <cfoutput>
                                <cfset local.bucketSettingsXML = XMLParse(local.dataStruct.qryBuckets.bucketSettings)>
                                <cfset local.innerDivStyle = "padding:1px 0;">
                                <cfif (local.dataStruct.qryBuckets.hideUntilSearched and not (local.dataStruct.intSearchID gt 0 and local.dataStruct.searchAction eq "doSearch")) OR 
                                        (StructKeyExists(local.bucketSettingsXML,"settings") and StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"showInBucketList") and local.bucketSettingsXML.settings.XmlAttributes.showInBucketList eq 0)>
                                    <cfset local.innerDivStyle = local.innerDivStyle & "display:none;">
                                </cfif>
                                <cfif val(local.dataStruct.qryBuckets.restrictToGroupID) gt 0 and local.dataStruct.qryBuckets.isMemberInRestrictedGroup is not 1>
                                    <cfset local.bucketImage = "/assets/common/images/search/folder_locked.png">
                                <cfelseif local.dataStruct.qryBucketInfo.bucketid eq local.dataStruct.qryBuckets.bucketid>
                                    <cfset local.bucketImage = "/assets/common/images/search/folder_open.png">
                                <cfelse>
                                    <cfset local.bucketImage = "/assets/common/images/search/folder_close.png">
                                </cfif>
                                <div id="bk_#local.dataStruct.qryBuckets.bucketid#_" style="#local.innerDivStyle#">
                                    <span id="bk_#local.dataStruct.qryBuckets.bucketid#_cnt" style="float:right;height:18px;"></span><a href="/?pg=search&bid=#local.dataStruct.qryBuckets.bucketid#<cfif local.dataStruct.intSearchID gt 0 and local.dataStruct.searchAction eq "doSearch">&sid=#local.dataStruct.intSearchID#&s_a=doSearch</cfif>" class="tsAppBodyText"><img src="#local.bucketImage#" width="19" height="16" align="absmiddle" border="0" />#local.dataStruct.qryBuckets.bucketName#</a><br clear="all"/>
                                </div>
                            </cfoutput>
                            <br/>
                        </cfoutput>
                    <cfoutput>
                        </div>
                    <div id="searchSponsorContainer" style="text-align:center;"></div>
                    <cfsavecontent variable="local.sponsorAdJS">
                        <cfoutput>
                            <script type="text/javascript">
                                $().ready( function() {
                                    MCPromises.BackendPlatformServices.then(function() {
                                        var MCadlimit = 2;
                                        var MCadContainer = $("##searchSponsorContainer");
                                        var MCadSitecode = '#local.rc.mc_siteInfo.sitecode#';
                                        var MCadZoneType = 'search';
                                        
                                        var overrideCallback = function (adsArray) {
                                            if (adsArray != null && adsArray.length) {
                                                for (var i=0;i<Math.min(adsArray.length,MCadlimit);i++) {
                                                    if (adsArray[i].ADLINK.length)
                                                        $(MCadContainer).append('<a href="' + adsArray[i].ADLINK + '" title="Click for more information" target="_blank"><img style="width:' + adsArray[i].WIDTH + ';height:' + adsArray[i].HEIGHT + ';" src="' + adsArray[i].IMAGEURL + '" /></a><br/><br/>');
                                                    else
                                                        $(MCadContainer).append('<a href="/?pg=search&bid=#local.dataStruct.intBucketID#&s_a=otherTools&adID=' + adsArray[i].ADID + '" title="Click for more information" target="_self"><img style="width:' + adsArray[i].WIDTH + ';height:' + adsArray[i].HEIGHT + ';" src="' + adsArray[i].IMAGEURL + '" /></a><br/><br/>');
                                                }
                                            } else {
                                                $(MCadContainer).remove();
                                            }
                                        };												
                                        MCBackendPlatformServices.SponsorAdsService.MCIncludeAds(MCadContainer, MCadZoneType,MCadSitecode,MCadlimit,'_top',overrideCallback);
                                    }).catch(error => {
                                        let msg = 'Failed to get ads for listViewerSponsorContainer';
                                        if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
                                            MCJSErrorReporting.promiseRejectionHandler(msg)
                                        else 
                                            console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`);
                                    });
                                });
                            </script>
                        </cfoutput>
                    </cfsavecontent>
                    <cfhtmlhead text="#local.sponsorAdJS#">

                    <cfsavecontent variable="local.htmlhead">
                        <cfoutput>
                            <script src="/sitecomponents/COMMON/javascript/webviewer/6.3.2/webviewer.min.js"></script>
                        </cfoutput>
                    </cfsavecontent>
                    <cfhtmlhead text="#local.htmlhead#">

                    </td>
                    </cfoutput>
                </cfif>

                <!--- content --->
                <cfoutput>
                <td class="tsAppBodyText" style="padding:12px 8px 8px 10px;" id="bk_content">
                    <cfswitch expression="#local.dataStruct.searchAction#">
                    <cfcase value="doSearch">
                        <cfoutput>#local.dataStruct.objCurrentBucket.showLoading(bucketID=local.dataStruct.qryBucketInfo.bucketID,searchID=local.dataStruct.intSearchID, viewDirectory="default")#</cfoutput>
                    </cfcase>
                    <cfcase value="NoKnownBucket">
                        <!--- if the site has buckets, go to 1st bucket. else, kick out and go to homepage --->
                        <cfif local.dataStruct.qryBuckets.recordcount>
                            <cflocation url="/?pg=search&bid=#local.dataStruct.qryBuckets.bucketid#" addtoken="no">
                        <cfelse>
                            <cflocation url="/" addtoken="no">
                        </cfif>
                    </cfcase>
                    <cfcase value="refineSearch">
                        <cfoutput>#local.dataStruct.objCurrentBucket.showSearchForm(bucketID=local.dataStruct.qryBucketInfo.bucketID,searchID=local.dataStruct.intSearchID, viewDirectory="default")#</cfoutput>
                    </cfcase>
                    <cfcase value="otherTools">
                        <cfoutput>#local.dataStruct.strOtherTools#</cfoutput>
                    </cfcase>
                    <cfdefaultcase>
                        <cfoutput>#local.dataStruct.objCurrentBucket.showSearchForm(bucketID=local.dataStruct.qryBucketInfo.bucketID, viewDirectory="default")#</cfoutput>
                    </cfdefaultcase>
                    </cfswitch>
                </td>
                </tr>
                </table>
            </div>

            </div>
            <div id="tsDocumentViewer" style="height:1500px;overflow:hidden;display:none;"></div>
            <div id="tsCartViewer" style="display:none;"></div>
			<div class="mc-modal">
				<div class="mc-modal-header"><h4 class="mc-modal-title"></h4><span class="mc-modal-close">&times;</span></div>
				<div class="mc-modal-body"></div>
			</div>
        </div>
    </cfoutput>