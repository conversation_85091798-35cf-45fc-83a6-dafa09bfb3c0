<cfscript>

	// default and defined custom page custom fields
	local.arrCustomFields = [];

	local.tmpField = { name="UpcomingEvents_Title", type="STRING", desc="pcoming Events Tab Title", value="Upcoming Events" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyEvents_Title", type="STRING", desc="My Events Tab Title", value="My Events" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyOnlinePrograms_Title", type="STRING", desc="My Online Programs Tab Title", value="My Online Programs" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="QuickLinks_Title", type="STRING", desc="Quick Links Tab Title", value="Quick Links" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyRecentSearches_Title", type="STRING", desc="My Recent Searches Tab Title", value="My Recent Searches" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="FeaturedResources_Title", type="STRING", desc="Featured Resources Tab Title", value="Featured Resources" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyECommunities_Title", type="STRING", desc="My eCommunities Tab Title", value="My eCommunities" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="NewFilesAdded_Title", type="STRING", desc="New Files Added Tab Title", value="New Files Added" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyFileDownloads_Title", type="STRING", desc="My File Downloads Tab Title", value="My File Downloads" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="NewStoreItems_Title", type="STRING", desc="New Store Items Tab Title", value="New Store Items" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyStorePurchases_Title", type="STRING", desc="My Store Purchases Tab Title", value="My Store Purchases" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="UpcomingEvents_Content", type="CONTENTOBJ", desc="Upcoming Events Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyEvents_Content", type="CONTENTOBJ", desc="My Events Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyOnlinePrograms_Content", type="CONTENTOBJ", desc="My Online Programs Box Content", value="
		<a class='btn WSAJbtn' href='/?pg=semwebCatalog&panel=My'>View My Online Programs</a>
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="QuickLinks1_Content", type="CONTENTOBJ", desc="Quick Links Box Content for Group 1", value="
		<p>
			<span class='BodyText'>
				As a WSAJ EAGLE member you have exclusive access to the following resources:
			</span>
		</p>
		<ul>
			<li><strong><a href='/docDownload/812594'>2017 Amicus Program Year in Review</a></strong>&nbsp;</li>
			<li><strong><a href='/docDownload/768659'>Subrogation Deskbook Now Online</a></strong><span style='color: rgb(111, 111, 111);'>&nbsp;</span></li>
			<li><a href='/docDownload/724863'>IFCA After <em>Perez</em> Recording from 2/24/17</a></li>
			<li><strong><a href='http://www.trialnewsonline.org/trialnews/april_2018' target='_blank'>April Issue of <em>Trial News</em> now online</a>&nbsp;<span style='color: rgb(111, 111, 111);'>&nbsp;</span></strong><span style='color: rgb(111, 111, 111);'>-&nbsp;</span><span style='color: rgb(255, 0, 0);'>New!</span></li>
			<li><span class='BodyText'><a href='/?pg=EagleDocuments'>Document Exchange</a></span></li>
			<li><span class='BodyText'><a href='/?pg=EagleExchange&amp;fsAction=editDocument&amp;lang=en&amp;fsDocumentID=724146'>Updated Tom Chambers Quick Citations</a> (Updated Aug 2017)</span></li>
			<li><a href='/?pg=TCVideos'><span class='BodyText'>Tom Chambers Videos</span></a></li>
			<li><span class='BodyText'><a href='/?pg=EaglePublications'>CLE Materials &amp; Publications</a>&nbsp;</span></li>
			<li><span class='BodyText'><a href='/?pg=CaseSpotlight'>Case Spotlight</a>&nbsp;</span></li>
			<li><span class='BodyText'><a href='/?pg=listviewer'>List Server</a></span></li>
			<li><span class='BodyText'><a href='/?pg=EagleDirectory'>EAGLE Directory</a></span></li>
			<li><a href='http://secure.ultracart.com/cgi-bin/UCEditor?MERCHANTID=CCC&amp;COUPON=wsaj10eagle' target='_blank'>10% off Trial Guides</a></li>
		</ul>
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="QuickLinks2_Content", type="CONTENTOBJ", desc="Quick Links Box Content for Group 2", value="
	
		<p>
			<span class='BodyText'>
				As a WSAJ Legal Staff of an EAGLE member you have exclusive access to the following resources:
			</span>
		</p>
		<ul>
			<li><span class='BodyText'><a href='/?pg=search&amp;bid=3503'>Eagle Legal Staff Documents</a></span></li>
			<li><span class='BodyText'><a href='/?commpg=WSAJEAGLELegalStaffCommMemberDirectory&amp;pg=WSAJEAGLELegalStaffComm&amp;dirAction=search'>Eagle Legal Staff Directory</a></span></li>
			<li><span class='BodyText'><a href='/?pg=listviewer'>Browse My Listserver</a></span></li>
			<li><a href='/?pg=TCVideos'><span class='BodyText'>Tom Chambers Videos</span></a></li>
			<li><span class='BodyText'>Need Help? <a href='mailto:<EMAIL>'>Contact Linda Snider</a></span></li>
		</ul>
	
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="QuickLinks3_Content", type="CONTENTOBJ", desc="Quick Links Box Content for Group 3", value="
			
		<p>
			<span class='BodyText'>
				As a WSAJ member you have access to the following resources:
			</span>
		</p>
		<ul>
			<li><strong><a href='/docDownload/812594'>2017 Amicus Program Year in Review</a>&nbsp;</strong>-&nbsp;<span style='color: rgb(255, 0, 0);'>New!</span></li>
			<li><span class='BodyText'><a href='/?pg=listviewer'>Browse My Listservers</a></span></li>
			<li><span class='BodyText'><a href='/?pg=NewMemberResources'>New Member Resources</a></span></li>
			<li><a href='/?pg=NMChecklists'><span class='BodyText'>Updated Checklists for New Lawyers</span></a></li>
			<li><span class='BodyText'><a href='/?pg=ConvenienceLearning'>On Demand Seminars</a></span></li>
			<li><span class='BodyText'><a href='/?pg=members&amp;configid=153'>Member Directory</a></span></li>
			<li><a href='/?pg=MediaToolKit'><span class='BodyText'>Media Tool Kit</span></a></li>
		</ul>
	
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyRecentSearches_Content", type="CONTENTOBJ", desc="My Recent Searches Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="FeaturedResources1_Content", type="CONTENTOBJ", desc="Featured Resources Box Content for Group 1", value="
	
		<p>
			<span class='HeaderText'>
				<!-- 
					<p>
						<span class='HeaderText'>Expert Documents</span>
					</p>
				<p>
					<img alt='' src='/userimages/filefolderimage.jpg' style='color: rgb(111, 111, 111); width: 100px; height: 69px; float: right; margin-left: 5px; margin-right: 5px;' /><span style='color: rgb(111, 111, 111); letter-spacing: 0px;'>This folder has documents for over 200 different experts.  You can browse by expert name and see all the different documents available.</span></p>
					<div>
					<p>
						<span class='BodyText'>
							<a href='/?pg=search&bid=3501'>Search the EXPERT Documents Now</a>
							<br />
							<a href='/?pg=ExpertDocs&fsAction=addDocuments'>Upload a Document</a>
						</span>
					</p>
				-->
			</span>
		</p>

		<p>
			<span class='HeaderText'>On The Go CLE</span>
		</p>

		<p>
			Free video-recorded CLE available from April 1 - May 31, 2018<br /><strong>(.50 FREE CLE credit for EAGLE members)</strong>
		</p>

		<p>
			<a href='/?pg=VL040517'>
				<img alt='Paul Veillon' src='/userimages/vlpaulveillon.jpg' style='width: 300px; height: 167px;' />
			</a>
		</p>

		<p>
			<b>Property Damage Claims: Win, Protect Your Clients, Earn a Living<br /><em>Paul Veillon</em></b>
		</p>

	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="FeaturedResources2_Content", type="CONTENTOBJ", desc="Featured Resources Box Content for Group 2", value="
	
		<p>
			<em><span class='HeaderText'>Trial News</span></em>
		</p>
		<p>
			<a href='http://www.trialnewsonline.org/trialnews/april_2018/'>
				<img alt='April Trial News' src='/userimages/april2018tnbox.jpg' style='width: 294px; height: 236px;' />
			</a>
		</p>

	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="FeaturedResources3_Content", type="CONTENTOBJ", desc="Featured Resources Box Content for Group 3", value="
	
		<p>
			<font color='##333333'><span style='font-size: 18px;'><b><em>Trial News</em> On-Line</b></span></font>
		</p>
		<p>
			<font color='##333333'>
				<span style='font-size: 18px;'>
					<b>
						<a href='http://www.trialnewsonline.org/trialnews/april_2018/' target='_blank'>
							<img alt='' src='/userimages/april(1).jpg' style='width: 264px; height: 240px;' />
						</a>
					</b>
				</span>
			</font>
		</p>

	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyECommunities_Content", type="CONTENTOBJ", desc="My eCommunities Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="NewFilesAdded_Content", type="CONTENTOBJ", desc="New Files Added Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyFileDownloads_Content", type="CONTENTOBJ", desc="My File Downloads Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="NewStoreItems_Content", type="CONTENTOBJ", desc="New Store Items Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MyStorePurchases_Content", type="CONTENTOBJ", desc="My Store Purchases Box Content", value="" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="Group1UID", type="STRING", desc="Group1UID", value="02805121-fe82-4436-a511-3bde71495cdb" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="Group2UID", type="STRING", desc="Group2UID", value="070d4277-ee7b-43e2-bc26-e4e0cee7d117" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="Group3UID", type="STRING", desc="Group3UID", value="3f49d068-e479-4d1e-87e5-62ac8b2a702b" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="SubNavGroup1", type="CONTENTOBJ", desc="SubNavGroup1", value="

		<h1>EAGLE Members</h1>
		<ul>
			<li><a href='/?pg=ContactUs'>CONTACT US</a></li>
			<li><a href='/?pg=Leadership'>LEADERSHIP</a></li>
			<li><a href='/?pg=Awards'>AWARDS</a></li>
			<li><a href='/?pg=Locations'>STAFF &amp; OFFICE LOCATIONS</a></li>
		</ul>

	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	
	local.tmpField = { name="SubNavGroup2", type="CONTENTOBJ", desc="SubNavGroup2", value="
	
		<h1>Eagle Legal Staff Members</h1>
		<ul>
			<li><a href='/?pg=ContactUs'>CONTACT US</a></li>
			<li><a href='/?pg=Leadership'>LEADERSHIP</a></li>
			<li><a href='/?pg=Awards'>AWARDS</a></li>
			<li><a href='/?pg=Locations'>STAFF &amp; OFFICE LOCATIONS</a></li>
		</ul>
	
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="SubNavGroup3", type="CONTENTOBJ", desc="SubNavGroup3", value="

		<h1> Regular Members</h1>

		<ul>
			<li><a href='/?pg=ContactUs'>CONTACT US</a></li>
			<li><a href='/?pg=Leadership'>LEADERSHIP</a></li>
			<li><a href='/?pg=Awards'>AWARDS</a></li>
			<li><a href='/?pg=Locations'>STAFF &amp; OFFICE LOCATIONS</a></li>
		</ul>
	
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="BottomContent", type="CONTENTOBJ", desc="BottomContent", value="

		<div>
			<h3>Sponsors</h3>
		</div>

		<ul>
			<li><img alt='image' src='/userimages/lat(2).jpg'/></li>
			<li><img alt='image' src='/userimages/ringler.jpg'/></li>
			<li><img alt='image' src='/userimages/coldstream.jpg' /></li>
			<li><img alt='image' src='/userimages/physicianlifeweb.gif'/></li>
			<li><img alt='image' src='/userimages/tscanweb.gif'/></li>
		</ul>
			
	" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	variables.ORGID = arguments.event.getValue('mc_siteInfo.ORGID');

	variables.GroupUIDFound="";
</cfscript>


<cfif GetMemberGroupByUID(local.strPageFields.Group1UID, variables.ORGID).recordCount GT 0>
	<cfset variables.GroupUIDFound = GetMemberGroupByUID(local.strPageFields.Group1UID, variables.ORGID).uid/>						
<cfelseif GetMemberGroupByUID(local.strPageFields.Group2UID, variables.ORGID).recordCount GT 0>
	<cfset variables.GroupUIDFound = GetMemberGroupByUID(local.strPageFields.Group2UID, variables.ORGID).uid/>
<cfelseif GetMemberGroupByUID(local.strPageFields.Group3UID, variables.ORGID).recordCount GT 0>
	<cfset variables.GroupUIDFound = GetMemberGroupByUID(local.strPageFields.Group3UID, variables.ORGID).uid/>
</cfif>	

<cfoutput>

<script>
	$( document ).ready(function() {
		$.fn.digits = function(){ 
			return this.each(function(){ 
				$(this).text(formatCurrency($.trim($(this).text()))); 
			})
		};

		$.fn.replaceSiteCode = function(){ 
			return this.each(function(){ 
				
				$(this).text("#arguments.event.getValue('mc_siteinfo.sitecode')#" + $.trim($(this).text())); 
			})
		};
		
		var currencyHolder = Handlebars.compile($('##currencyHolder').html());
		$('##currencyHolder .currencyFormatMe').digits();


		var siteCodeHolder = Handlebars.compile($('##siteCodeHolder').html());
		$('##siteCodeHolder .sitecode').replaceSiteCode();

		
	});


	function formatCurrency(num) {
		num = num.toString().replace(/\$|\,/g,'');
		if(isNaN(num)) num = "0";
		num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
		sign = (num == (num = Math.abs(num)));
		num = Math.floor(num*100+0.50000000001);
		cents = num%100;
		num = Math.floor(num/100).toString();
		if(cents<10) cents = "0" + cents;
		for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
		num.substring(num.length-(4*i+3));
		return (((sign)?'':'-') + '$' + num + '.' + cents);
	}	
</script>

<style>
	##myWSAJ {
		margin-left: 0px !important;
		margin-bottom: 0px !important;
	}
	##myWSAJ .nav {
		margin-bottom: 0px;
	}

	##myWSAJ .nav-tabs > .active > a,##myWSAJ .nav-tabs>.active>a:hover {
		color:white!important; 
		background:##0a6ca9!important; 
		font-weight: normal; 
		text-decoration: none; 
	}

	##myWSAJ .nav-tabs a, ##myWSAJ .nav-tabs a:hover { 
		color:##0a6ca9; 
		background:##d3d3d3 !important; 
		font-weight: lighter; 
		text-decoration: none;  
	}

	##myWSAJ .nav-tabs>li>a { 
		margin-right:23px; 
		background:##ececec!important; 
	}

	##myWSAJ .nav-tabs>li:last-child>a { 
		margin-right:auto; 
	}

	##myWSAJ .nav-tabs li {
		margin-bottom: 0!important;
	}

	##myWSAJ .nav-tabs > li > a { 
		border: 1px solid transparent; 
		border-radius: 4px 4px 0 0; 
		line-height: 1.42857; 
		margin-right: 2px;
	}
	##myWSAJ .nav-tabs>li>a,##myWSAJ .nav-pills>li>a{
		padding:7px 7px 7px 7px!important; 
	}
	##myWSAJ .nav-tabs li.active a{
		text-decoration: none!important;
	}
	##myWSAJ .tab-content {
		border: 2px solid ##ddd;
		min-height: 340px;
		padding: 10px;
		margin-bottom: 20px;
		background: ##fff;
	}
	##myWSAJ .tab-content .tab-pane {
		overflow-y: auto;
		min-height: 340px;
		max-height: 340px;
	}

	##myWSAJ .tab-content .tab-pane ul {
		margin-left: 18px!important;
		list-style-type: disc!important;
	}

	##myWSAJ .eventList > div:first-child > div { 
   		 margin-bottom: 5px!important;
	}

	##myWSAJ .eventList > div:first-child > div > div:last-child > a:first-child {
		margin-bottom: -5px!important;
    	font-size: 16px!important;
		height: auto!important;
		line-height: 23px!important;
	}

	##myWSAJ .eventList > div:first-child > div > div:first-child {
    	padding: 4px 0px!important;
    	width: 60px;
		min-height: 60px!important;   
	}
	##myWSAJ .eventList > div:first-child > div > div:last-child {
    	left: 68px!important;
	}
	##myWSAJ .eventList > div:first-child > div > div:last-child > a:last-child {  
		font-size: 12px!important;
		margin-left: -7px!important;
	}

	##myWSAJ .eventList > div:first-child > div > div:first-child > span:first-child {	
		font-size: 18px;
		line-height: 21px;
	}
	##myWSAJ .eventList > div:first-child > div > div:first-child h2 {	
		font-size: 26px!important;
		line-height: 30px!important;
	}

	##myWSAJ .eventView a {
    	font-size: 15px;
	}

	.icon2x{
		line-height: 24px;
	}

		##myPage .Table > div:nth-of-type(odd) {
    	background: ##f9f9f9;
	}
    ##myPage .Table
    {
        display: table;
		width: 100%;
		background-color: transparent;
		border-collapse: collapse;
		border-spacing: 0;
    }
    ##myPage .Title
    {
        display: table-caption;
        text-align: center;
        font-weight: bold;
        font-size: larger;
    }
    ##myPage .Heading
    {
        display: table-row;
        font-weight: bold;
    }
    ##myPage .Row
    {
        display: table-row;
    }
    ##myPage .Cell
    {
        display: table-cell;
		padding: 8px;
		line-height: 20px;
		text-align: left;
		vertical-align: top;
		border-top: 1px solid ##ddd;
    }


</style>

	<!-- Banner Start -->
	
	<cfif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group1UID)>
		<div class="eduBanner">
			#ReReplace(ReReplace(local.strPageFields.SubNavGroup1,'<p>',''),'</p>','')#
		</div>
	<cfelseif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group2UID)>
		<div class="eduBanner">
			#ReReplace(ReReplace(local.strPageFields.SubNavGroup2,'<p>',''),'</p>','')#
		</div>
	<cfelseif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group3UID)>
		<div class="eduBanner">
			#ReReplace(ReReplace(local.strPageFields.SubNavGroup3,'<p>',''),'</p>','')#
		</div>
	</cfif>	
	
	<!-- Banner End -->
	
	<div class="paddingMain" id="myPage">

		<div class="span12 row-fluid" id="myWSAJ">
			<div class="span4">
				<div class="row-fluid">
					<ul class="nav nav-tabs" id="FirstTabs">
						<li class="active">
							<a href="##UpcomingEvents" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.UpcomingEvents_Title,'<p>',''),'</p>','')#</a>
						</li>
						<li>
							<a href="##MyEvents" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.MyEvents_Title,'<p>',''),'</p>','')#</a>
						</li>
						<li>
							<a href="##MyOnlinePrograms" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.MyOnlinePrograms_Title,'<p>',''),'</p>','')#</a>
						</li>				
					</ul>
					<div class="tab-content">
						<div class="tab-pane active BodyText" id="UpcomingEvents">	
							<div class="eventList">
								#REReplace(REReplace(local.strPageFields.UpcomingEvents_Content,"<p>",""),"</p>","")# 
							</div>
							<div class="eventView"> 
								<a href="/?pg=events&evAction=listAll"><i class="icon-calendar icon2x">&nbsp;</i> View the Full Calendar </a> 
							</div>
						</div>
						<div class="tab-pane BodyText" id="MyEvents">	
							<div class="eventList">
								#REReplace(REReplace(local.strPageFields.MyEvents_Content,"<p>",""),"</p>","")# 
							</div>						
						</div>
						<div class="tab-pane BodyText" id="MyOnlinePrograms">
							#local.strPageFields.MyOnlinePrograms_Content#	
						</div>
					</div>
				</div>
			</div>	

			<div class="span4">
				<div class="row-fluid">
					<ul class="nav nav-tabs" id="SecondTabs">
						<li class="active">
							<a href="##QuickLinks" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.QuickLinks_Title,'<p>',''),'</p>','')#</a>
						</li>
						<li>
							<a href="##MyRecentSearches" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.MyRecentSearches_Title,'<p>',''),'</p>','')#</a>
						</li>
								
					</ul>
					<div class="tab-content">
						<div class="tab-pane active BodyText" id="QuickLinks">
							<cfif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group1UID)>
								#local.strPageFields.QuickLinks1_Content#								
							<cfelseif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group2UID)>
								#local.strPageFields.QuickLinks2_Content#
							<cfelseif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group3UID)>
								#local.strPageFields.QuickLinks3_Content#
							<cfelse>
								<span>You have no recent searches.</span>
							</cfif>									
						</div>
						<div class="tab-pane BodyText" id="MyRecentSearches">
							#REReplace(REReplace(local.strPageFields.MyRecentSearches_Content,"<p>",""),"</p>","")# 
						</div>					
					</div>
				</div>
			</div>	

			<div class="span4">
				<div class="row-fluid">
					<ul class="nav nav-tabs" id="ThirdTabs">
						<li class="active">
							<a href="##FeaturedResources" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.FeaturedResources_Title,'<p>',''),'</p>','')#</a>
						</li>	
					</ul>
					<div class="tab-content">
						<div class="tab-pane active BodyText" id="FeaturedResources">		
							<cfif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group1UID)>
								#local.strPageFields.FeaturedResources1_Content#								
							<cfelseif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group2UID)>
								#local.strPageFields.FeaturedResources2_Content#
							<cfelseif LCase(variables.GroupUIDFound) EQ LCase(local.strPageFields.Group3UID)>
								#local.strPageFields.FeaturedResources3_Content#
							<cfelse>
								<span>You have no featured resources.</span>
							</cfif>	
						</div>									
					</div>
				</div>
			</div>				
		</div>

		<div class="span12 row-fluid" id="myWSAJ">
			<div class="span4">
				<div class="row-fluid">
					<ul class="nav nav-tabs" id="FourthTabs">
						<li class="active">
							<a href="##MyECommunities" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.MyECommunities_Title,'<p>',''),'</p>','')#</a>
						</li>
								
					</ul>
					<div class="tab-content">
						<div class="tab-pane active BodyText" id="MyECommunities">
							#REReplace(REReplace(local.strPageFields.MyECommunities_Content,"<p>",""),"</p>","")# 	
						</div>
				
					</div>
				</div>
			</div>	

			<div class="span8">
				<div class="row-fluid">
					<ul class="nav nav-tabs" id="fifthTabs">
						<li class="active">
							<a href="##NewFilesAdded" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.NewFilesAdded_Title,'<p>',''),'</p>','')#</a>
						</li>
						<li>
							<a href="##MyFileDownloads" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.MyFileDownloads_Title,'<p>',''),'</p>','')#</a>
						</li>
						<li>
							<a href="##NewStoreItems" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.NewStoreItems_Title,'<p>',''),'</p>','')#</a>
						</li>
						<li >
							<a href="##MyStorePurchases" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.MyStorePurchases_Title,'<p>',''),'</p>','')#</a>
						</li>
								
					</ul>
					<div class="tab-content">
						<div class="tab-pane active BodyText" id="NewFilesAdded">
							#REReplace(REReplace(local.strPageFields.NewFilesAdded_Content,"<p>",""),"</p>","")# 	
						</div>
						<div class="tab-pane BodyText" id="MyFileDownloads">	
							#REReplace(REReplace(local.strPageFields.MyFileDownloads_Content,"<p>",""),"</p>","")# 	
						</div>
						<div class="tab-pane BodyText" id="NewStoreItems">
							#REReplace(REReplace(local.strPageFields.NewStoreItems_Content,"<p>",""),"</p>","")# 
							<br/>
							<div>
								<a href="/?pg=store"><i class="icon-book icon2x">&nbsp;</i>Browse the Online Store</a>
							</div>	
						</div>
						<div class="tab-pane BodyText" id="MyStorePurchases">	
							#REReplace(REReplace(local.strPageFields.MyStorePurchases_Content,"<p>",""),"</p>","")# 	
							<br/>
							<div>
								<a href="/?pg=store&sa=myPurchases"><i class="icon-book icon2x">&nbsp;</i>Browse All My Purchases</a>
							</div>
						</div>
				
					</div>
				</div>
			</div>	
		</div>	

	</div>

	<!-- Sponser Slider Start-->
	<div class="sponser">
		#ReReplace(ReReplace(local.strPageFields.BottomContent,'<p>',''),'</p>','')#
	</div>
	<!-- Sponser Slider End-->


	<cffunction name="GetMemberGroupByUID" >
		<cfargument  name="GroupUID" type="string">
		<cfargument  name="ORGID" type="string">

		<cfset local.qryGroupMembership = application.objMember.getMemberGroups(session.cfcuser.memberData.memberID, arguments.ORGID)/>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberGroup">
			select groupID
			from dbo.ams_groups
			where uid = <cfqueryparam value="#arguments.GroupUID#" cfsqltype="CF_SQL_VARCHAR"> 
		</cfquery>

		<cfquery dbtype="query" name="local.qryIsMemberGroupExist">
			select groupID,'#arguments.GroupUID#' as uid 
			from [local].qryGroupMembership
			where groupID =  <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryIsMemberGroupExist>
	</cffunction>

</cfoutput>

