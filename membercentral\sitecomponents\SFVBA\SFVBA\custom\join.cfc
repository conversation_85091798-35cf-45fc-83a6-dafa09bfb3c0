<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            variables.redirectlink = "/?pg=join";
            local.formAction = arguments.event.getValue('fa','showLookup');
            variables.cfcuser = structNew();
			variables.formFields = structNew();

			variables.cfcuser = session.cfcuser;
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=variables.cfcuser);

			if(application.mcCacheManager.sessionValueExists('formFields')){
				variables.formFields = application.mcCacheManager.sessionGetValue('formFields',{});
			}
			
			variables.contactInformationFieldSetUID = "4F6E1AEB-465B-4DB4-AD61-3AA2205F3CBC";            
			variables.personalInformationFieldSetUID = "51DAB7D7-1533-4289-896F-6E3FC0976F8B";
            variables.officeAddressFieldSetUID = "5FE1AA2E-C1C4-4D19-9DFD-1D2304A25788";
            variables.addressPreferenceFieldSetUID = "4A24E3F0-7137-4F25-802E-0B8F28EEB08D";
			variables.professionalInformationFieldSetUID = "ECFE27EE-C949-4E5F-8684-D7AAD3454970";
			variables.lawStudentInformationFieldSetUID = "6F0E2594-ACAD-441A-8D28-DF9B61CB2A0E";			
			variables.otherInformationFieldSetUID = "A71148BA-19D7-43C2-B18B-14BF58778701";			
            
            variables.currentDate = dateTimeFormat(now());

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];

            local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			    arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="28EE7C2B-9BD2-432E-B023-912D28AA6DC1" };
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already an SFVBA member. If you have questions about your membership, please contact us at (818) 227-0490." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call (818) 227-0490." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="FormTitle", type="STRING", desc="Form Title", value="SFVBA Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalLicContent",type="CONTENTOBJ",desc="Content at top of Professional License fields",value="Please enter your bar admission date(s) and license number(s)." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);            
			local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="5041A1D0-5330-4E12-B875-C3D17B14E9E3" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PreCheckedAddOns", type="STRING", desc="UIDs for Add-Ons that qualified users will have to opt out of", value="NULL" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for credit card",value="SFVBACC" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="SFVBACashChecks" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="NULL" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="SFVBA Membership Application Receipt" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New SFVBA Member Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);    
			local.tmpField = { name="SectionMembershipSetUID",type="STRING",desc="UID for Section MembershipSet",value="8217BAD2-17FF-4B23-B866-B9EAE6517B01" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SectionMembership3YearSetUID",type="STRING",desc="UID for Section Membership 3 Year Set",value="5E758E0E-A27C-4D43-B544-E82271275F2A" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);

            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo= variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom= variables.strPageFields.MemberConfirmationFrom
            ));

           variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");

            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;

			variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form."
            variables.origMemberID = variables.useMID;
            if(local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                (structCount(variables.formFields) gt 0) and
                structKeyExists(variables.formFields, "step0") and 
                structKeyExists(variables.formFields.step0, "memberID") and 
                int(val(variables.formFields.step0.memberID)) gt 0){            
                    variables.useMID = variables.formFields.step0.memberID;
                    if(structKeyExists(variables.formFields.step0, "origMemberID") and int(val(variables.formFields.step0.origMemberID)) gt 0){
                        variables.origMemberID = variables.formFields.step0.origMemberID;
                    }              

            }else if(variables.cfcuser.memberdata.identifiedAsMemberID){
                variables.useMID = variables.cfcuser.memberdata.identifiedAsMemberID;
                variables.origMemberID = variables.useMID;
            }
            if( local.formAction neq "showLookup" and 
                local.formAction neq "processLookup" and 
                local.formAction neq "showMemberInfo" and 
                local.formAction neq "processMemberInfo" and 
                structKeyExists(variables.formFields, "step1") and 
                structKeyExists(variables.formFields.step1, "useHistoryID") and 
                int(val(variables.formFields.step1.useHistoryID)) gt 0){
                    variables.useHistoryID = int(val(variables.formFields.step1.useHistoryID));
            }

            local.isSuperUser = application.objUser.isSuperUser(cfcuser=variables.cfcuser);
            local.subStatus = "";
            if(NOT local.isSuperUser AND int(variables.useMID) GT 0){
                local.subStatus = hasSub(int(variables.useMID));
            }

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            if(local.isSuperUser){
                local.returnHTML = showError(errorCode='admin');  
            }else if(len(local.subStatus) GT 0 AND local.subStatus NEQ "success"){
                local.returnHTML = showError(errorCode=local.subStatus);  
            }else{
                switch (local.formAction) {
                    case "processLookup":
                        switch (processLookup(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMemberInfo();
                                break;		
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMemberInfo":
                        switch (processMemberInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showMembershipInfo();
                                break;
                            case "spam":
                                local.returnHTML = showError(errorCode='spam');
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;
                    case "processMembershipInfo":
                        switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                            case "success":
                                local.returnHTML = showPayment();
                                break;
                            default:
                                local.returnHTML = showError(errorCode='error');
                                break;				
                        }
                        break;                   
                     case "processPayment":
                        local.processStatus = processPayment(event=arguments.event);
                        switch (local.processStatus) {
                            case "success":
                                local.returnHTML = showConfirmation();
                                application.objUser.setIdentifiedMemberIDfromID(cfcuser=variables.cfcuser, memberID=0);
                                variables.formFields = structNew();
								application.mcCacheManager.sessionDeleteValue("formFields")
                                break;
                            default:
                                local.returnHTML = showError(errorCode='failpayment');
                                break;				
                        }
                        break;
                    case "showMembershipInfo":
                        local.returnHTML = showMembershipInfo();
                        break;	
                    case "showMemberInfo":
                        local.returnHTML = showMemberInfo();
                        break;                    		
                    default:
						if(application.mcCacheManager.sessionValueExists("captchaEntered")) {	
							application.mcCacheManager.sessionDeleteValue("captchaEntered")
						}
						
						variables.formFields = structNew();
		                if(application.mcCacheManager.sessionValueExists("formFields")) {
							application.mcCacheManager.sessionDeleteValue("formFields")
						}
                        local.returnHTML = showLookup();
                        break;
                }
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");        
    	</cfscript>
    </cffunction>


    <cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>

		<!--- get identified memberid if available to bypass lookup --->
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=variables.cfcuser, orgID=variables.orgid)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>          

            #variables.pageJS# 

			<style type="text/css">
			</style>

			<script type="text/javascript">

				var step = 0;
				var prevStep = 0;

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}

				function assignMemberData(memObj) {
					$.colorbox.close();
					$('###variables.formName# ##memberID').val(memObj.memberID);
					$('###variables.formName# ##isNewRecord').val(memObj.isNewRecord);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}

				function selectMember() {
					hideAlert();
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}				

				function addMember(memObj) {
					assignMemberData(memObj);
				}

				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}				

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMemberInfo");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");                            

				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}

				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});

				$(document).ready(function() {	
					<cfif variables.useMID>					
						var mo = { memberID:#variables.useMID#,isNewRecord:0 };
						assignMemberData(mo);
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
				function subscriptionRateOverrideBoxHandler(event) {
	
					var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());

					//check subscription if not already checked
					if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
						$(subRateRadioButton)[0].click();
						$(this).focus();
					} else if (subRateRadioButton) {
						$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
					}
				}

				function subscriptionCheckboxHandler() {
					if ($(this)[0].checked) {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
					} else {
						$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
						$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
					}
				}

				function subscriptionRateRadioButtonHandler() {

					if ($(this)[0].checked) {
						var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
						var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
						var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();

						if (rateOverrideBox.length) {
							//rateoverride box is present
							rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
						}

						//put label of selected rate radio button next to subscription
						rateDescription = ' - ' + rateDescription;
						$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);

						//check subscription if not already checked
						if (!$(subCheckbox)[0].checked)
							$(subCheckbox)[0].click();
					}
				}

				function initializeAddons() {
					$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
				}

				function selectAllSubscriptionsIfRequired() {
					var addonData = $(this).data();
					// select all addons if minimum required by set is gte available count
					// hide checkboxes so they can not be unselected
					if (addonData.minallowed >= $('.subCheckbox',this).length) {
						$('.subCheckbox:not(:checked)',this).click().hide();
						$('.subCheckbox',this).hide();
					}
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
                <cfinput type="hidden" name="fa" id="fa" value="processLookup">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="0">
                <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">

                <cfif len(variables.strPageFields.FormTitle)>
                    <div class="row-fluid" id="FormTitleId">
                        <div class="span12">
                            <span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
                        </div>
                    </div>
                </cfif>	

				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>	
			</cfform>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step0")>
            <cfset structDelete(variables.formFields, "step0")>
        </cfif>	

        <cfset variables.formFields.step0 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>        
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfset local.response = "success">
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>		

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.fieldSetUIDlist = '#variables.contactInformationFieldSetUID#,#variables.professionalInformationFieldSetUID#,#variables.personalInformationFieldSetUID#,#variables.officeAddressFieldSetUID#,#variables.addressPreferenceFieldSetUID#,#variables.otherInformationFieldSetUID#'>
		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>
		
        <cfset local.memberTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.companyField = {fieldCode="",fieldLabel=""}>
		<cfset local.associateTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.studentTypeField = {fieldCode="",fieldLabel=""}>
		<cfset local.officeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.officestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.officepostalcodeField = {fieldCode="",fieldLabel=""}>		
		<cfset local.OfficePhoneField = {fieldCode="",fieldLabel=""}>		
		<cfset local.officeMobileField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.homeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.homestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.homepostalcodeField = {fieldCode="",fieldLabel=""}>
        <cfset local.homePhoneField = {fieldCode="",fieldLabel=""}>
		<cfset local.homeMobileField = {fieldCode="",fieldLabel=""}>
		<cfset local.dbAddressField = {fieldCode="",fieldLabel=""}>
		<cfset local.dmAddressField = {fieldCode="",fieldLabel=""}>
		<cfset local.committeeInterestField = {fieldCode="",fieldLabel=""}>
		<cfset local.committeeInterestSelectionField = {fieldCode="",fieldLabel=""}>
		<cfset local.CSBCSpecialistField = {fieldCode="",fieldLabel=""}>
		<cfset local.CSBCSpecialtyField = {fieldCode="",fieldLabel=""}>


        <cfset local.strData = {}>
		<cfset local.strData.zero = checkSessionExist("step0")/>
         <cfset local.strData.one = checkSessionExist("step1")/>
		
        <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
			
			<cfset local.memberTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Contact Type']")/>
            <cfif arrayLen(local.memberTypeData)>				
                <cfset local.memberTypeField.fieldCode = local.memberTypeData[1].XmlAttributes.fieldCode>
                <cfset local.memberTypeField.fieldLabel = local.memberTypeData[1].XmlAttributes.fieldLabel>
            </cfif> 
			<cfset local.companyData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='company']")/>
            <cfif arrayLen(local.companyData)>				
                <cfset local.companyField.fieldCode = local.companyData[1].XmlAttributes.fieldCode>
                <cfset local.companyField.fieldLabel = local.companyData[1].XmlAttributes.fieldLabel>
            </cfif> 
			<cfset local.associateTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Associate Type']")/>
            <cfif arrayLen(local.associateTypeData)>				
                <cfset local.associateTypeField.fieldCode = local.associateTypeData[1].XmlAttributes.fieldCode>
                <cfset local.associateTypeField.fieldLabel = local.associateTypeData[1].XmlAttributes.fieldLabel>
            </cfif>
			<cfset local.studentTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Student Type']")/>
            <cfif arrayLen(local.studentTypeData)>				
                <cfset local.studentTypeField.fieldCode = local.studentTypeData[1].XmlAttributes.fieldCode>
                <cfset local.studentTypeField.fieldLabel = local.studentTypeData[1].XmlAttributes.fieldLabel>
            </cfif>

			<cfset local.dbAddressFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Designated Billing_addressType']")/>
            <cfif arrayLen(local.dbAddressFieldData)>				
                <cfset local.dbAddressField.fieldCode = local.dbAddressFieldData[1].XmlAttributes.fieldCode >
                <cfset local.dbAddressField.fieldLabel = local.dbAddressFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.dmAddressFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Designated Mailing_addressType']")/>
            <cfif arrayLen(local.dmAddressFieldData)>				
                <cfset local.dmAddressField.fieldCode = local.dmAddressFieldData[1].XmlAttributes.fieldCode >
                <cfset local.dmAddressField.fieldLabel = local.dmAddressFieldData[1].XmlAttributes.fieldLabel >
            </cfif> 
			<cfset local.officeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_address1']")/>
            <cfif arrayLen(local.officeAddress1FieldData)>				
                <cfset local.officeAddress1Field.fieldCode = local.officeAddress1FieldData[1].XmlAttributes.fieldCode >
                <cfset local.officeAddress1Field.fieldLabel = local.officeAddress1FieldData[1].XmlAttributes.fieldLabel >
            </cfif>
            <cfset local.officeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_city']")/>
            <cfif arrayLen(local.officeCityFieldData)>				
                <cfset local.officeCityField.fieldCode = local.officeCityFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officeCityField.fieldLabel = local.officeCityFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_stateprov']")/>
            <cfif arrayLen(local.officestateprovFieldData)>				
                <cfset local.officestateprovField.fieldCode = local.officestateprovFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officestateprovField.fieldLabel = local.officestateprovFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.officepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office Address_postalcode']")/>
            <cfif arrayLen(local.officepostalcodeFieldData)>				
                <cfset local.officepostalcodeField.fieldCode = local.officepostalcodeFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officepostalcodeField.fieldLabel = local.officepostalcodeFieldData[1].XmlAttributes.fieldLabel >
            </cfif>    
			<cfset local.OfficePhoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_Phone']")/>
            <cfif arrayLen(local.OfficePhoneFieldData)>				
                <cfset local.OfficePhoneField.fieldCode = local.OfficePhoneFieldData[1].XmlAttributes.fieldCode >
                <cfset local.OfficePhoneField.fieldLabel = local.OfficePhoneFieldData[1].XmlAttributes.fieldLabel >
            </cfif>			
			<cfset local.officeMobileFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Office_Mobile']")/>
            <cfif arrayLen(local.officeMobileFieldData)>				
                <cfset local.officeMobileField.fieldCode = local.officeMobileFieldData[1].XmlAttributes.fieldCode >
                <cfset local.officeMobileField.fieldLabel = local.officeMobileFieldData[1].XmlAttributes.fieldLabel >
            </cfif>       
            <cfset local.homeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home Address_address1']")/>
            <cfif arrayLen(local.homeAddress1FieldData)>				
                <cfset local.homeAddress1Field.fieldCode = local.homeAddress1FieldData[1].XmlAttributes.fieldCode >
                <cfset local.homeAddress1Field.fieldLabel = local.homeAddress1FieldData[1].XmlAttributes.fieldLabel >
            </cfif>
            <cfset local.homeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home Address_city']")/>
            <cfif arrayLen(local.homeCityFieldData)>				
                <cfset local.homeCityField.fieldCode = local.homeCityFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homeCityField.fieldLabel = local.homeCityFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.homestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home Address_stateprov']")/>
            <cfif arrayLen(local.homestateprovFieldData)>				
                <cfset local.homestateprovField.fieldCode = local.homestateprovFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homestateprovField.fieldLabel = local.homestateprovFieldData[1].XmlAttributes.fieldLabel >
            </cfif>	
            <cfset local.homepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Home Address_postalcode']")/>
            <cfif arrayLen(local.homepostalcodeFieldData)>				
                <cfset local.homepostalcodeField.fieldCode = local.homepostalcodeFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homepostalcodeField.fieldLabel = local.homepostalcodeFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.homePhoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Personal_Phone']")/>
            <cfif arrayLen(local.homePhoneFieldData)>				
                <cfset local.homePhoneField.fieldCode = local.homePhoneFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homePhoneField.fieldLabel = local.homePhoneFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.homeMobileFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Personal_Cell']")/>
            <cfif arrayLen(local.homeMobileFieldData)>				
                <cfset local.homeMobileField.fieldCode = local.homeMobileFieldData[1].XmlAttributes.fieldCode >
                <cfset local.homeMobileField.fieldLabel = local.homeMobileFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.committeeInterestFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Committee Interest']")/>
            <cfif arrayLen(local.committeeInterestFieldData)>				
                <cfset local.committeeInterestField.fieldCode = local.committeeInterestFieldData[1].XmlAttributes.fieldCode >
                <cfset local.committeeInterestField.fieldLabel = local.committeeInterestFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.committeeInterestSelectionFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Committee Interest Selection']")/>
            <cfif arrayLen(local.committeeInterestSelectionFieldData)>				
                <cfset local.committeeInterestSelectionField.fieldCode = local.committeeInterestSelectionFieldData[1].XmlAttributes.fieldCode >
                <cfset local.committeeInterestSelectionField.fieldLabel = local.committeeInterestSelectionFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.CSBCSpecialistFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='California State Bar Certified Specialist']")/>
            <cfif arrayLen(local.CSBCSpecialistFieldData)>				
                <cfset local.CSBCSpecialistField.fieldCode = local.CSBCSpecialistFieldData[1].XmlAttributes.fieldCode >
                <cfset local.CSBCSpecialistField.fieldLabel = local.CSBCSpecialistFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfset local.CSBCSpecialtyFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='California State Bar Certified Specialty']")/>
            <cfif arrayLen(local.CSBCSpecialtyFieldData)>				
                <cfset local.CSBCSpecialtyField.fieldCode = local.CSBCSpecialtyFieldData[1].XmlAttributes.fieldCode >
                <cfset local.CSBCSpecialtyField.fieldLabel = local.CSBCSpecialtyFieldData[1].XmlAttributes.fieldLabel >
            </cfif>
			<cfif StructIsEmpty(local.strData.one) AND ListFindNoCase('#variables.contactInformationFieldSetUID#,#variables.professionalInformationFieldSetUID#,#variables.personalInformationFieldSetUID#,#variables.officeAddressFieldSetUID#',local.fieldSetUid)>
				<cfset StructAppend(local.memberFieldData,application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields))>
				<cfif NOT variables.isLoggedIn AND (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord EQ 0)>				
					<cfloop collection="#local.memberFieldData#" item="local.key" >
						<cfif NOT ListFindNoCase('m_firstname,m_middlename,m_lastname,m_suffix,m_prefix,m_professionalsuffix',local.key)>
							<cfset StructDelete(local.memberFieldData, local.key)>
						</cfif>					
					</cfloop>
				</cfif>
			</cfif>
        </cfloop>
        <cfset StructAppend(local.strData.one,local.memberFieldData)/>

		<cfset local.strData.one.memberID = variables.useMID>
		<cfset local.strData.one.orgID = variables.orgID>	
		<cfset local.strData.one.siteID = variables.siteID>

		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID = variables.orgid, includeTags=0)>
        <cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID = variables.orgid)>

		<cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.professionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.lawStudentInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.lawStudentInformationFieldSetUID, mode="collection", strData=local.strData.one)>
		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.officeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.officeAddressFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.addressPreferenceFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="collection", strData=local.strData.one)>
        <cfset local.otherInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.otherInformationFieldSetUID, mode="collection", strData=local.strData.one)>
       
		<cfset local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
        <cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.one.mpl_pltypeid>	
		</cfif>

        <cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
        <cfset local.licenseStatus = {}>
        <cfset local.index = 1>
        <cfloop query="local.qryOrgProLicenseStatuses">
            <cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
            <cfset local.index = local.index + 1>
        </cfloop> 
        <cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}

				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}

					##content-wrapper div.ui-multiselect-menu{width:auto!important;}

					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}
					

				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							

				div.alert-danger{padding: 10px !important;}		
				.checkboxLabel{
					vertical-align:top !important;
				}		

			</style>
			<script language="javascript">
				function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}

				function adjustFieldsetDisplay() {
					var _CF_this = document.forms['#variables.formName#'];
					var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					$(".professionalLicensesHolder .mpl_pltypeid").parent().parent().children().first().text('');
					$("###local.companyField.fieldCode#").parent().parent().children().first().text('');
					switch(memType) {
						case 'Attorney':
						case 'Judge':
							showFieldsByContainerClass('officeAddressFieldSet,professionalInformationFieldSet,professionalLicenses');
							resetFormFieldsByContainerClass('lawStudentInformationFieldSet');	
							if(memType == 'Attorney')	
								$("###local.companyField.fieldCode#").parent().parent().children().first().text('*');	
							break;	
						case 'Law Student':
							showFieldsByContainerClass('lawStudentInformationFieldSet');		
							resetFormFieldsByContainerClass('officeAddressFieldSet,professionalInformationFieldSet,professionalLicensesHolder');
							break;
						case 'Non-Attorney Professional':
							showFieldsByContainerClass('officeAddressFieldSet');		
							resetFormFieldsByContainerClass('lawStudentInformationFieldSet,professionalInformationFieldSet,professionalLicensesHolder');
							break;
						case 'Paralegal / Legal Secretary':
							showFieldsByContainerClass('officeAddressFieldSet');	
							resetFormFieldsByContainerClass('lawStudentInformationFieldSet,professionalInformationFieldSet,professionalLicensesHolder');
							break;
						default:
							showFieldsByContainerClass('');					
							break;
					}
					$("[id$='_address1']").trigger('change');
				}
			
				function reloadElements(){
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						var _function = _this.data('function');

						if(typeof _function != "undefined" &&_function.includes('multiSelect')){
							_this.next().remove();
						} 
						eval(_function)();
					});
				}

				function showFieldsByContainerClass(classList)				
				{
					var elementArray = [];
					$(".fieldSetHolder [data-function]").each(function(){
						var _this = $(this);
						elementArray.push(_this);
					});

					$(".fieldSetHolder").html('');

					var classListArray=(classList).split(",");
					$.each(classListArray,function(i){
						if($.trim(classListArray[i]).length){
							$("."+classListArray[i]+"Holder").html($("."+classListArray[i]+"Wrapper").html());							
						}			
					});					
					reloadElements();
					elementArray.forEach(function(element) {
						var elementId = element.attr('id');
						$("##"+elementId).val(element.val());
					});
				}

				function showFieldByFieldList(fieldName)				
				{
					var _CF_this = document.forms['#variables.formName#'];
					var fieldNameListArray = (fieldName).split(",");
					$.each(fieldNameListArray,function(i){	
						$(_CF_this[fieldNameListArray[i]]).parents('tr').show();
					});			
				}

				function resetFormFieldsByContainerClass(containerClass){
					if(containerClass.length){
						var containerClassArray=(containerClass).split(",");
						$.each(containerClassArray,function(i){		
							$("."+containerClassArray[i]).html('');
						});
					}
				}

				function resetFormFieldByFieldList(fieldName){
					var _CF_this = document.forms['#variables.formName#'];
					if(fieldName.length){
						var fieldNameListArray = (fieldName).split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}else{
						var fieldNameListArray=("").split(",");
						$.each(fieldNameListArray,function(i){	
							$(_CF_this[fieldNameListArray[i]]).val(function() {
								return this.defaultValue;
							});		
						});	
					}
				}

				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();	
                    var memType = $.trim($(_CF_this['#local.memberTypeField.fieldCode#']).find('option:selected').text());
					
                    #local.contactInformationFieldSet.jsValidation#
                    #local.personalInformationFieldSet.jsValidation#

					var officeAddressRequired = false;
					var homeAddressRequired = false;

					if(memType.length > 0 && (memType != 'Law Student')){						
						#local.officeAddressFieldSet.jsValidation#
						officeAddressRequired = true;
					}
					if(memType != '' && memType == 'Law Student'){
						homeAddressRequired = true;
					}
					
                   
                    #local.professionalInformationFieldSet.jsValidation#
					if(memType != '' && memType == 'Law Student'){
						#local.lawStudentInformationFieldSet.jsValidation#
					}
                    #local.otherInformationFieldSet.jsValidation#
					
					if(memType.length == 0){
						arrReq[arrReq.length] = "Contact Type is required.";
					}
                    
                    if(memType.length > 0 && (memType == 'Attorney')){
						<cfif len(trim(local.companyField.fieldCode))>
                            if(!_CF_hasValue(_CF_this['#local.companyField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Company is required.";
                        </cfif>
						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();    
									if($("##mpl_"+val+"_licenseNumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' #local.strProfLicenseLabels.profLicenseNumberLabel#.'; }                                
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' #local.strProfLicenseLabels.profLicenseDateLabel#.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}

					}

					var isSetAllOfficeAddress = 0;		
                    if(memType.length > 0 && (memType != 'Law Student')){			
						<cfif len(trim(local.officeAddress1Field.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllOfficeAddress = isSetAllOfficeAddress + 1;}
						</cfif>
						<cfif len(trim(local.officeCityField.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officeCityField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						</cfif>
						<cfif len(trim(local.officestateprovField.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officestateprovField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						</cfif>
						<cfif len(trim(local.officepostalcodeField.fieldCode))>
							if(_CF_hasValue(_CF_this['#local.officepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllOfficeAddress = isSetAllOfficeAddress + 1;
						</cfif>
					}

                    var isSetAllHomeAddress = 0;		
                    <cfif len(trim(local.homeAddress1Field.fieldCode))>                        
						if(_CF_hasValue(_CF_this['#local.homeAddress1Field.fieldCode#'], "TEXT", false)){ isSetAllHomeAddress = isSetAllHomeAddress + 1;}
                    </cfif>
                    <cfif len(trim(local.homeCityField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.homeCityField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
                    </cfif>
                    <cfif len(trim(local.homestateprovField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.homestateprovField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
                    </cfif>
                    <cfif len(trim(local.homepostalcodeField.fieldCode))>
                        if(_CF_hasValue(_CF_this['#local.homepostalcodeField.fieldCode#'], "TEXT", false)) isSetAllHomeAddress = isSetAllHomeAddress + 1;
                    </cfif>

					var addressSet = false;

					if(officeAddressRequired && isSetAllOfficeAddress != 4 && isSetAllHomeAddress == 0){
						arrReq[arrReq.length] = "The following fields are required for Office Address:<ul><li>Address</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
						addressSet = true;
					}
					
					if(homeAddressRequired && isSetAllHomeAddress != 4 && isSetAllOfficeAddress == 0){
						arrReq[arrReq.length] = "The following fields are required for Personal Address:<ul><li>Address</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
						addressSet = true;
					}	

                    if(addressSet == false && isSetAllOfficeAddress == 0 && isSetAllHomeAddress == 0){
                        arrReq[arrReq.length] = "Either Personal Address or Office Address is required:<ul><li>Address</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
                    }else if(addressSet == false){
						var isSet = 0;
						if(isSetAllOfficeAddress == 4){
							isSet = 1;								
						}else if(isSetAllHomeAddress == 4){
							isSet = 1;								
						}

						if(isSet == 0){
							arrReq[arrReq.length] = "Either Personal Address or Office Address is required:<ul><li>Address</li><li>City</li><li>State</li><li>Zip Code</li></ul>";
						}
                    }

					<cfif len(trim(local.dbAddressField.fieldCode))>
						if(!_CF_hasValue(_CF_this['#local.dbAddressField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Designated Billing Address is required.";
					</cfif>
					<cfif len(trim(local.dmAddressField.fieldCode))>
						if(!_CF_hasValue(_CF_this['#local.dmAddressField.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "Designated Mailing Address is required.";
					</cfif>


					if (arrReq.length > 0) {
						var msg = '';

						for (var i=0; i < arrReq.length; i++){
							var breakLine = '<br/>';
							if(arrReq[i].indexOf('<li>City') != -1){
								var breakLine = '';
							}
							msg += arrReq[i] + breakLine;
						}
						showAlert(msg,afterFormLoad);
						return false;
					}

					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

                function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
                    $("##state_table").show();
                    if(status == ''){
                        status = 'Active';
                    }
                    strOption = '';
                    <cfloop collection="#local.licenseStatus#" item="local.i" >
                        strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
                    </cfloop>
                    if(isChecked){
                        $('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
                                '<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
                                '<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
                                '<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
                                '<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
                                '<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
                                '</div>');
                        $('##mpl_'+val+'_status').val(status);
                        mca_setupDatePickerField('mpl_'+val+'_activeDate');
                        $('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
                    }									
                    else{
                        $("##tr_state_"+val).remove();								
                    }
                    if($('##selectedLicense .row-fluid').length == 0){
                        $("##state_table").hide();
                    }	
                }
				
				function professionalLicensesmultiSelectReload(){									

					<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.one.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData.one['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData.one['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData.one['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData.one['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					<cfelseif local.qryProLicenses.recordCount GT 0>
						<cfloop query="local.qryProLicenses">
							licenseChange(true,'#local.qryProLicenses.PLTypeID#','#local.qryProLicenses.PLName#','#local.qryProLicenses.LicenseNumber#','#local.qryProLicenses.ActiveDate#','#local.qryProLicenses.StatusName#');	
						</cfloop>
						<cfloop query="local.qryProLicenses">
							<cfset local.profLicenseIDList = listAppend(local.profLicenseIDList, local.qryProLicenses.PLTypeID)>
						</cfloop>						
					</cfif>

					$(".professionalLicensesHolder .mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' Please Select ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".professionalLicensesHolder .mpl_pltypeid option").each(function(){
								$('.professionalLicensesHolder ##tr_state_'+$(this).attr("value")).remove();
							});
							if($('.professionalLicensesHolder ##selectedLicense .row-fluid').length == 0){
								$(".professionalLicensesHolder ##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".professionalLicensesHolder .mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
				}

				$(document).ready(function() {	

					<cfif structKeyExists(local.strData.one, "mccf_firstTimeMember")>
						toggleFTM();
					</cfif>

					<cfif NOT application.mcCacheManager.sessionValueExists('captchaEntered') >
						showCaptcha();
					</cfif>

                    var memberTypeField = $('##'+"#local.memberTypeField.fieldCode#");	
					$(memberTypeField).change(adjustFieldsetDisplay);
					$(memberTypeField).trigger('change');

					$('.tsAppBodyText').find('input[type="checkbox"]:first-child').each(function(k,v){
						$(v).closest('tr').find('td').eq(1).addClass('checkboxLabel')
					});

					$(document).on('change','###local.committeeInterestField.fieldCode#',function(){
						strVal = $("###local.committeeInterestField.fieldCode# option:selected").text();
						if(strVal.toLowerCase() != 'yes'){
							$('###local.committeeInterestField.fieldCode#').closest('table').find('tr').each(function(){
								$(this).find('td').each(function(){
									strCnt = $(this).html();
									if(strCnt != undefined){
										strCnt = strCnt.trim();
										if(strCnt == 'Committee Interest Selection'){
											$(this).closest('tr').hide();
											$(this).closest('tr').find('input[type="checkbox"]').removeAttr('checked');
										}
									}
								});
							});
						}else{
							$('###local.committeeInterestField.fieldCode#').closest('table').find('tr').each(function(){
								$(this).find('td').each(function(){
									strCnt = $(this).html();
									if(strCnt != undefined){
										strCnt = strCnt.trim();
										if(strCnt == 'Committee Interest Selection'){
											$(this).closest('tr').show();
										}
									}
								});
							});
						}						
					});

					$(document).on('change','###local.CSBCSpecialistField.fieldCode#',function(){
						strVal = $("###local.CSBCSpecialistField.fieldCode# option:selected").text();
						if(strVal.toLowerCase() != 'yes'){
							$('###local.CSBCSpecialistField.fieldCode#').closest('table').find('tr').each(function(){
								$(this).find('td').each(function(){
									strCnt = $(this).html();
									if(strCnt != undefined){
										strCnt = strCnt.trim();
										if(strCnt == 'California State Bar Certified Specialty'){
											$(this).closest('tr').hide();
											$(this).closest('tr').find('input[type="checkbox"]').removeAttr('checked');
										}
									}
								});
							});
						}else{
							$('###local.CSBCSpecialistField.fieldCode#').closest('table').find('tr').each(function(){
								$(this).find('td').each(function(){
									strCnt = $(this).html();
									if(strCnt != undefined){
										strCnt = strCnt.trim();
										if(strCnt == 'California State Bar Certified Specialty'){
											$(this).closest('tr').show();
										}
									}
								});
							});
						}						
					});

					if($('###local.committeeInterestField.fieldCode#').length > 0){
						$('###local.committeeInterestField.fieldCode#').trigger('change');
					}
					if($('###local.CSBCSpecialistField.fieldCode#').length > 0){
						$('###local.CSBCSpecialistField.fieldCode#').trigger('change');
					}
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>				
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="step1form" <cfif application.mcCacheManager.sessionValueExists("captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif> enctype="multipart/form-data">
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                    <input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>		

					<div id="content-wrapper">
						<cfif len(variables.strPageFields.Step1TopContent)>
							<div class="row-fluid" id="Step1TopContent"><div class="span12">#variables.strPageFields.Step1TopContent#</div></div>
						</cfif> 

						<span class="contactInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.contactInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.contactInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>	

						<span class="personalInformationFieldSetHolder addressHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.personalInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.personalInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>	

						<span class="officeAddressFieldSetHolder fieldSetHolder addressHolder"></span>

						<span class="addressPreferenceFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.addressPreferenceFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.addressPreferenceFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>	

						<span class="professionalInformationFieldSetHolder fieldSetHolder "></span>
						
						<span class="lawStudentInformationFieldSetHolder fieldSetHolder "></span>
                        
						<span class="professionalLicensesHolder fieldSetHolder"></span>
						
						<span class="otherInformationFieldSetHolder">
							<div class="row-fluid">
								<div class="tsAppSectionHeading">#local.otherInformationFieldSet.fieldSetTitle#</div>								
								<div class="tsAppSectionContentContainer">										
									#local.otherInformationFieldSet.fieldSetContent#									
								</div>
							</div>
						</span>	

						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div>                         

						<div class="row-fluid">
							<div class="span12">
								<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
							</div>
						</div>
					</div>
				</form>

				<span class="officeAddressFieldSetWrapper hide">
                    <div class="row-fluid">
                        <div class="tsAppSectionHeading">#local.officeAddressFieldSet.fieldSetTitle#</div>								
                        <div class="tsAppSectionContentContainer">										
                            #local.officeAddressFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
				
				<span class="professionalInformationFieldSetWrapper hide">
                    <div class="row-fluid">
                        <div class="tsAppSectionHeading">#local.professionalInformationFieldSet.fieldSetTitle#</div>								
                        <div class="tsAppSectionContentContainer">										
                            #local.professionalInformationFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
				
				<span class="lawStudentInformationFieldSetWrapper hide">
                    <div class="row-fluid">
                        <div class="tsAppSectionHeading">#local.lawStudentInformationFieldSet.fieldSetTitle#</div>								
                        <div class="tsAppSectionContentContainer">										
                            #local.lawStudentInformationFieldSet.fieldSetContent#									
                        </div>
                    </div>
                </span>
				
				<span class="professionalLicensesWrapper hide">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">Professional License Information</div>
						<div class="tsAppSectionContentContainer fieldSetContainer">
							<p>#variables.strPageFields.ProfessionalLicContent#</p>									
							<table cellpadding="3" border="0" cellspacing="0" >									
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText" >Professional License</td>
									<td class="tsAppBodyText">&nbsp;</td>
									<td class="tsAppBodyText">
										<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple" data-function="professionalLicensesmultiSelectReload">
											<cfloop query="local.qryOrgPlTypes">	
												<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif> <cfif local.qryOrgPlTypes.PLName EQ "Pennsylvania">required="true"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
											</cfloop>
										</select>
									</td>
								</tr>
								<tr class="top">
									<td class="tsAppBodyText" width="10"></td>
									<td class="tsAppBodyText"></td>
									<td class="tsAppBodyText"></td>
								</tr>
							</table>
							<table cellpadding="3" border="0" cellspacing="0" style="width:100%">
								<tr>
									<td>
										<div class="row-fluid hide" id="state_table">
											<div class="span3 proLicenseLabel">
												<b>Type</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseNumberLabel#</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseDateLabel#</b>
											</div>
											<div class="span3 proLicenseLabel">
												<b>#local.strProfLicenseLabels.profLicenseStatusLabel#</b>
											</div>
										</div>
										<span id="selectedLicense">
										</span>
									</td>
								</tr>					
							</table>
						</div>
					</div>
				</span>
				
				#application.objWebEditor.showEditorHeadScripts()#

				<script language="javascript">
					$(document).ready(function(){
						<cfloop query="local.qryOrgAddressTypes">                       
                            <cfif ListFindNoCase('Office Address,Home Address',local.qryOrgAddressTypes.addressType)>
                                function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _CF_this = document.forms['#variables.formName#'];
									var _address = _this.val();
									if(_address.length > 0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}
										if($('*[id^=mat_]').val() == ''){
											if($('*[id^=mat_] option:eq(1)').val() != undefined)
												$('*[id^=mat_]').val($('*[id^=mat_] option:eq(1)').val());
										}
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
										if($('*[id^=mat_]').val() == ''){
											if($('*[id^=mat_] option:eq(1)').val() != undefined)
												$('*[id^=mat_]').val($('*[id^=mat_] option:eq(1)').val());
										}
									}
								}

								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));

								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});

								$(document).on('change','.addressHolder ##ma_#local.qryOrgAddressTypes.addresstypeid#_address1',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
                            <cfelse>
                                $("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
                            </cfif>
						</cfloop>
					});

					function editContentBlock(cid,srid,tname) {
						var editMember = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								$('##frmmd_'+cid).html(r.html);
								var x = div.getElementsByTagName("script");
								for(var i=0;i<x.length;i++) eval(x[i].text); 
							}
						};
						var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
						TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
					}
				</script>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>		

        <cfset local.response = "failure">

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step1")>
            <cfset structDelete(variables.formFields, "step1")>
        </cfif>

        <cfset variables.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		<cfset local.strData.zero = checkSessionExist("step0")/>	
        <cfset local.strData.one = checkSessionExist("step1")/>

		<cfset local.strData.one.mc_siteinfo = arguments.rc.mc_siteinfo>

        <cfif variables.isLoggedIn OR variables.cfcuser.memberdata.identifiedAsMemberID OR (structKeyExists(local.strData.zero, "isNewRecord") and local.strData.zero.isNewRecord)>
            <cfset local.strData.one.memberID = variables.useMID>
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one, memberID=variables.useMID)>
		<cfelse>
            <cfset local.strData.one.memberID = 0>         
			<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.one)>
		</cfif>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>					
				<script type="text/javascript">
					$(document).ready(function(){
						if (typeof arrUploaders !== 'undefined') {
							$.each(arrUploaders, function() {
								this.uploader.bind('BeforeUpload', function(uploader, file) {
									uploader.setOption('url', uploader.settings.url.replace("memberid=", "memberid=#local.strResult.memberID#"));
								});
								this.uploader.start();
							});
						}
					});
                </script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfset variables.formFields.step0.origMemberID = variables.useMID/>
        <cfset variables.formFields.step0.memberID = local.strResult.memberID/>

		<cfif local.strResult.success>	
            <cfset variables.formFields.step1.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=variables.qryHistoryStarted.categoryID, 
                                                subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
                                                enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>	
            <cfset application.mcCacheManager.sessionSetValue("captchaEntered", 1)>	
			<cfset local.response = "success">
		</cfif>	

		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfreturn local.response>
	</cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

		<cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>
        <cfset local.strData.zero = checkSessionExist("step0")/>
        <cfset variables.useMID = local.strData.zero.memberID/>

        <cfif StructIsEmpty(local.strData.one)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.thisAddonSubscriptionID = "">
		
		<cfif variables.strPageFields.PreCheckedAddOns neq 'NULL' AND variables.strPageFields.PreCheckedAddOns neq ''>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData.two["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
        <cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData.two
				)>				

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <style type="text/css">
					div.alert-danger{padding: 10px !important;}
                </style>

                <script type="text/javascript">	
					$(document).ready(function(){
						$('input.subCheckbox:checkbox').on('change',subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').on('change',subscriptionRateRadioButtonHandler);
						$('input.subRateOverrideBox').on('change',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('focus',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('blur',subscriptionRateOverrideBoxHandler);

						initializeAddons();

						$('input.subCheckbox:checkbox').each(subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').each(subscriptionRateRadioButtonHandler);

						$(document).on('change',"*[id^='sub#local.subscriptionID#_rate']",function(){

							MembershipTypeCloned = $(this).closest('label.subRateLabel').find('.labelText').clone();
							MembershipTypeClonedSpan = $(MembershipTypeCloned).find('span').remove();
							if(MembershipTypeCloned.html() != undefined)
								strMembershipType = MembershipTypeCloned.html().trim();

							if($(this).is(':checked')){
								switch(strMembershipType) {
									case 'Essential Member (4+ years) 3-Year Term':
									case 'Essential Member (4+ years) 3-Year Term - Professional Discount':
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').hide();
										$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').show();
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[type="checkbox"]').removeAttr('checked');
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[type="radio"]').removeAttr('checked');
										break;
									default:
										frequencyfname = $(this).data('frequencyfname');
										$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').hide();
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').show();
										$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').find('input[type="checkbox"]').removeAttr('checked');
										$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').find('input[type="radio"]').removeAttr('checked');
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[type="radio"]').prop('disabled','disabled');
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[type="radio"]').closest('.subRateLabel').hide();
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[data-frequencyfname="'+frequencyfname+'"]').removeAttr('disabled');
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[data-frequencyfname="'+frequencyfname+'"]').closest('.subRateLabel').show();
										$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('.subLabelHolder').before('<div class="frequencyMsg"> </div>')
										if(frequencyfname != 'F'){
											$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"] .joinFrequency_F ').closest('.subLabelHolder').each(function(){
												radioLength = $(this).find('input[type="radio"]').length;
												if(radioLength == 0){
													$(this).hide();
													$(this).find('input[type="checkbox"].subCheckbox').removeAttr('checked');
												}
											})
											
											
										}else{
											$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"] .joinFrequency_F ').closest('.subLabelHolder').show();
										}
									
									}
							}else{
								$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').hide();
								$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').show();
								$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[type="checkbox"]').removeAttr('checked');
								$('div[data-setuid="#variables.strPageFields.SectionMembershipSetUID#"]').find('input[type="radio"]').removeAttr('checked');
								$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').find('input[type="checkbox"]').removeAttr('checked');
								$('div[data-setuid="#variables.strPageFields.SectionMembership3YearSetUID#"]').find('input[type="radio"]').removeAttr('checked');
							}

						});

						$("*[id^='sub#local.subscriptionID#_rate']").trigger('change');
					});
					
                    #local.result.jsAddonValidation#

				    function validateMembershipInfoForm(){
						var arrReq = new Array();						
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}
						
						$('div [data-setname="ARS Membership"] input:checkbox').parents("[data-minallowed]").not("[data-minallowed=0]").each(function(){
							var minlp = $(this).data("minallowed");
							if($("##"+$(this).attr("id").split('_')[0]+":checked").length){
								if($("##"+$(this).attr("id").split('_')[0]+"_addons>div>div.subLabelHolder input:checkbox:checked").length == 0){
									arrReq[arrReq.length] = "Please select a minimum of " + minlp + " " + $("##"+$(this).attr("id").split('_')[0]+"_addons legend").eq(0).text()+".";
								}							
							}
						});
						
						$('div [data-setname="ARS Membership"] input:checkbox').parents("[data-maxallowed]").not("[data-maxallowed=0]").each(function(){
							var maxlp = $(this).data("maxallowed");
							if($("##"+$(this).attr("id").split('_')[0]+":checked").length){
								if($("##"+$(this).attr("id").split('_')[0]+"_addons>div>div.subLabelHolder input:checkbox:checked").length > maxlp){
									arrReq[arrReq.length] = "Please select no more than " + maxlp + " " + $("##"+$(this).attr("id").split('_')[0]+"_addons legend").eq(0).text()+".";
								}							
							}
						});
						
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}
						$(".subRateOverrideBox").parent().siblings('input.subRateCheckbox[type=radio]').each(function(){
							if($(this).is(':checked') == false){
								$(this).parent().find('.subRateOverrideBox').remove();
							}
						});

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					
					function rateReset(){
						$('div [data-setname="ARS Membership"] input:checkbox').each(function(){							
							if($(this).is(":checked")){
								$("##"+$(this).attr("name").split('_')[0]+"_addons").show();
							}else{
								$("##"+$(this).attr("name").split('_')[0]+"_addons").hide();
								$("##"+$(this).attr('id')+"_addons").find('input:checkbox').each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
								$("##"+$(this).attr('id')+"_addons").find('input:radio').each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
								$("[name="+$(this).attr('id')+"_rate]").each(function(){if($(this).is(":checked")){$(this).prop('checked', false);}});
							}
						});
					}
					
					$(document).ready(function(){
						$('div [data-setname="ARS Membership"] .subRateCheckbox').change(function(){
							if($(this).is(":checked")){
								if($(this).closest('.subAvailableRates').parent().find('.subCheckbox:checked').length == 0){
									$(this).closest('.subAvailableRates').parent().find('.subCheckbox').trigger('click');
								}
							}
						});

						$('div [data-setname="ARS Membership"] input:checkbox').change(function(){
							rateReset();
						});

						rateReset();
					});
					
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">			
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
					<cfif len(variables.strPageFields.FormTitle)>
						<div class="row-fluid" id="FormTitleId">
							<div class="span12">
								<span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
							</div>
						</div>
					</cfif>	

					<cfif len(variables.strPageFields.Step2TopContent)>
						<div class="row-fluid" id="Step2TopContent"><div class="span12">#variables.strPageFields.Step2TopContent#</div></div>
					</cfif>

					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span12">
								<cfoutput>#local.result.formcontent#</cfoutput>
							</div>
						</div>
					</div>	
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>
				</cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step2")>
            <cfset structDelete(variables.formFields, "step2")>
        </cfif>		
        <cfset variables.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

		<cfset local.strData.two = checkSessionExist("step2")/>
		<cfset local.strData.one = checkSessionExist("step1")/>
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>
		<cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two)>
			<cfset application.objCommon.redirect(variables.redirectlink)/>
		</cfif>

        <cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">

        <cfset local.strData = {}>        

        <cfset local.strData.two = checkSessionExist("step2")/>

        <cfif StructIsEmpty(local.strData.two)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = []>
			<cfif len(variables.strPageFields.ProfileCodeCredit) gt 0 AND variables.strPageFields.ProfileCodeCredit neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCredit)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeCheck) gt 0 AND variables.strPageFields.ProfileCodeCheck neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeCheck)>		
			</cfif>
			<cfif len(variables.strPageFields.ProfileCodeACH) gt 0 AND variables.strPageFields.ProfileCodeACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.ProfileCodeACH)>		
			</cfif>

			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>
		</cfif>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>

                <script type="text/javascript">	
                    function validatePaymentForm(isPaymentRequired) {

						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
                    $('##mccfdiv_#variables.strPageFields.ProfileCodeCredit# iframe').load(function() {
						var iframeThis = this;

						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);

						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					}); 
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
                    <cfinput type="hidden" name="fa" id="fa" value="processPayment">
                    <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

                    <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
                    <cfif len(variables.strPageFields.FormTitle)>
                        <div class="row-fluid" id="FormTitleId">
                            <div class="span12">
                                <span class="TitleText" style="margin-bottom: 40px;">#variables.strPageFields.FormTitle#</span>
                            </div>
                        </div>
                    </cfif>	

                    <cfif len(variables.strPageFields.Step3TopContent)>
                        <div class="row-fluid" id="Step3TopContent"><div class="span12">#variables.strPageFields.Step3TopContent#</div></div>
                    </cfif>

                    <div class="tsAppSectionHeading">Membership Selections Confirmation</div>
                    <div class="tsAppSectionContentContainer">						
                        #local.strResult.formContent#
                    </div>
                    <br/>

                    <div class="tsAppSectionHeading">Total Price</div>
                    <div class="tsAppSectionContentContainer">						
                        Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
                    </div>

                    <br/><br/>

                    <cfif local.paymentRequired>
                        #local.strReturn.paymentHTML#
                    <cfelse>
                        <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                        <button name="btnBack" type="button" class="btn pull-right" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
                    </cfif>

                </cfform>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="processPayment" access="private" output="false" returntype="string">
        <cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfset local.response = "failure">
		<cfset local.rc = arguments.event.getCollection()>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, "step3")>
            <cfset structDelete(variables.formFields, "step3")>
        </cfif>			
        <cfset variables.formFields.step3 = application.objCustomPageUtils.createSessionStructure(rc=arguments.event.getCollection())>

        <cfset local.strData.three = checkSessionExist("step3")/>
        <cfset local.strData.two = checkSessionExist("step2")/>
        <cfset local.strData.one = checkSessionExist("step1")/>

        <cfif StructIsEmpty(local.strData.one) OR StructIsEmpty(local.strData.two) OR StructIsEmpty(local.strData.three)>
            <cfset application.objCommon.redirect(variables.redirectlink)/>
        </cfif>

        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

        <cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = local.strData.two)/>

        <cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = local.strData.two)/>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")/>


		<cfset local.strData.one.memberID = variables.useMID>
        <cfset local.strData.one.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
		<cfset local.strData.two.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>
        <cfset local.strData.three.mc_siteinfo = arguments.event.getCollection().mc_siteinfo>

		<cfset application.objCustomPageUtils.mh_updateHistory(
			memberID=variables.useMID, 
			historyID=variables.useHistoryID, 
			subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
			description=variables.historyCompletedText, 
			newAccountsOnly=false
		)/>

		<cfset local.strData.two.memberID = variables.useMID>

		<cfset application.objCustomPageUtils.mem_saveMemberInfo(rc=local.strData.two, memberID=variables.useMID)>
	
        <cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 
        
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
		
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(local.rc, 'mccf_payMethID') and structKeyExists(local.rc, 'p_#local.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = local.rc['p_#local.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>
        <!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->     
        
        <cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=local.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=local.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

        <cfif local.totalAmountDueNow gt 0 and local.rc.mccf_payMeth eq variables.strPageFields.ProfileCodeCredit>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=local.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and local.rc.mccf_payMeth eq variables.strPageFields.ProfileCodeCredit>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.rc.mccf_payMethID, profileCode=local.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			</cfif>
		</cfif>

        <cfset local.response = "success">

        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

        <cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
            <cfif structKeyExists(local.strData.three,"p_#local.strData.three.mccf_payMethID#_mppid")>
                <cfset local.memberPayProfileSelected = int(val(local.strData.three["p_#local.strData.three.mccf_payMethID#_mppid"]))>
            <cfelse>
                <cfset local.memberPayProfileSelected = 0>
            </cfif>
            <cfif local.memberPayProfileSelected gt 0>
                <cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=local.strData.three.mccf_payMethID).detail>
            </cfif>
        </cfif>

        <cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
        <cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData.one["#local.memberTypeFieldCode#"])>

		<cfset local.contactInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.contactInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.professionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.professionalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.lawStudentInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.lawStudentInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
		<cfset local.personalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.personalInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.officeAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.officeAddressFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.addressPreferenceFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.addressPreferenceFieldSetUID, mode="confirmation", strData=local.strData.one)>
        <cfset local.otherInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid=variables.otherInformationFieldSetUID, mode="confirmation", strData=local.strData.one)>
       
              
		<cfset local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		
        <cfsavecontent variable="local.invoice">
            <cfoutput>
				#local.contactInformationFieldSet.fieldSetContent#
				#local.personalInformationFieldSet.fieldSetContent#
				<cfif local.memberTypeSelected EQ 'Attorney' OR local.memberTypeSelected EQ 'Judge'>
					#local.officeAddressFieldSet.fieldSetContent#
				</cfif>	
				#local.addressPreferenceFieldSet.fieldSetContent#
				<cfif local.memberTypeSelected EQ 'Attorney' OR local.memberTypeSelected EQ 'Judge'>
					#local.professionalInformationFieldSet.fieldSetContent#
				</cfif>
				
				<cfif local.memberTypeSelected EQ 'Law Student'>
					#local.lawStudentInformationFieldSet.fieldSetContent#
				</cfif>

				<cfif local.memberTypeSelected EQ 'Attorney' OR local.memberTypeSelected EQ 'Judge'>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
						</tr>				
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
												<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
													<thead>
														<tr valign="top">
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseNumberLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseDateLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strProfLicenseLabels.profLicenseStatusLabel#</th>
														</tr>
													</thead>
													<tbody>
													<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
														<tr id="tr_state_#local.key#">
															<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
														</tr>
													</cfloop>
													</tbody>
												</table>
											</cfif>
										</td>
									</tr>						
								</table>
							</td>
						</tr>
					</table>
					<br>
				</cfif>

				#local.otherInformationFieldSet.fieldSetContent#
				
				
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            #local.strResult.formContent#
                            <br/>
                        </div>
                        <br/>
                        <strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
                        <br/>
                        </td>
                    </tr>
                </table>

                <cfif local.paymentRequired>
                    <br/>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(local.strData.three,"mccf_payMeth")>
                                <table cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif>

            </cfoutput>
        </cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>

                <p>Here are the details of your application:</p>	

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

        <cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle="#local.strData.one.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>
		<cfset local.emailSentToUser = local.responseStruct.success>
		
        <cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<!--- email to association --->
        <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.origMemberID, orgID=variables.orgID)>
        <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

        <cfsavecontent variable="local.specialText">
			<cfoutput>

            <div style="padding-bottom:4px;">Member Number Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
            <div style="padding-bottom:4px;">Member Number of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>

			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(local.strData.three,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#local.strData.three.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                </cfif>
                 #local.specialText#
                <p>Here are the details of your application:</p>

                #local.invoice#	
            </cfoutput>
        </cfsavecontent>

        <cfset local.Name = ""/>
		<cfif structKeyExists(local.strData.one, "m_firstname")>
			<cfset local.Name = local.strData.one.m_firstname/>
		</cfif>
		<cfset local.Name = local.Name & " " />
		<cfif structKeyExists(local.strData.one, "m_lastname")>
			<cfset local.Name = local.Name & local.strData.one.m_lastname/>
		</cfif>

        <cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: #trim(local.Name)#">
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=local.strData.one.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application.", emailContent=local.mailContent)>
		
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				<style>

				</style>
				</head>
				<body>
                    <cfif len(variables.strPageFields.ConfirmationContent)>
                        <p>#variables.strPageFields.ConfirmationContent#</p>
                    </cfif>
                    <p>Here are the details of your application:</p>
					#local.invoice#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(variables.currentDate,'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(variables.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Application Confirmation')>

        <!--- relocate to message page --->
        <cfset session.invoice = local.invoice />
		<cfset application.mcCacheManager.sessionSetValue("formFields", variables.formFields)>

		<cfreturn local.response>
	</cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">

		<cfset var local = structNew()>

		<cfset local.doNotIncludeList = "fa">
		<cfset local.strData = {}>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <div class="tsAppSectionHeading">#variables.strPageFields.ConfirmationContent#</div>
                </cfif>
                <div class="tsAppSectionContentContainer">
                    <p>Here are the details of your application:</p>						
                    #session.invoice#
                </div>
            </cfoutput>
        </cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

    <cffunction name="hasSub" access="private" output="false" returntype="string">
        <cfargument name="memberID" type="Number" required="true">

		<cfset var local = structNew()>

        <cfset variables.useMID = arguments.memberID/>

		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

    <cffunction name="checkSessionExist" access="private" output="false" returntype="struct">
		<cfargument name="step" type="string" required="true">

		<cfset var local = structNew()>
        <cfset local.isExist = false/>
        <cfset local.strData = {}>

        <cfif application.mcCacheManager.sessionValueExists("formFields") and structKeyExists(variables.formFields, arguments.step)>
            <cfset local.strData = variables.formFields[arguments.step]/>
        </cfif>			

		<cfreturn local.strData>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
			<cfoutput>	
				<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
				<div class="tsAppSectionContentContainer">						
					<cfif arguments.errorCode eq "activefound">		
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "acceptedfound">
						#variables.strPageFields.ActiveAcceptedMessage#
					<cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#
					<cfelseif arguments.errorCode eq "billedfound">
						<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
						<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
						<cflocation url="#local.redirectLink#" addtoken="false">
					<cfelseif arguments.errorCode eq "failsavemember">
						We were unable to save the member information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failsavemembership">
						We were unable to process the membership information provided. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "failpayment">
						We were unable to process your selected payment method. Please contact the association or try again later.
					<cfelseif arguments.errorCode eq "spam">
						Your submission was blocked and will not be processed at this time.
                     <cfelseif arguments.errorCode eq "admin">
                        <i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.
					<cfelse>
						An error occurred. Please contact the association or try again later.
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>