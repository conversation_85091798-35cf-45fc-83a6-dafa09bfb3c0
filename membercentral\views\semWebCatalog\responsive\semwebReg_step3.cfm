<cfinclude template="../commonSWRegStep3JS.cfm">

<cfoutput>
<div class="swreg-card swreg-mt-3 swreg-p-3">
	<form name="frmSWRegStep3" id="frmSWRegStep3" onsubmit="doS3ValidateAndSave();return false;" autocomplete="off"<cfif local.swReg.currentReg.currentStep NEQ 3> style="display:none;"</cfif>>		
		<div class="swreg-mb-3 swreg-font-size-lg swreg-font-weight-bold">Registrant Details:</div>
		
		<!--- prompt for missing tax information --->
		<cfif local.qrySWP.handlesOwnPayment is 1>
			<cfif local.swReg.currentReg.s3.stateIDforTax EQ 0 OR NOT len(local.swReg.currentReg.s3.zipForTax)>
				<div class="swreg-d-flex swreg-mb-3">
					<div class="swreg-col">
						<div class="swreg-font-size-md swreg-font-weight-bold swreg-pb-1">State/Province <span class="swreg-text-danger">*</span></div>
						<cfset local.qryStates = application.objCommon.getStates()>
						<select id="stateIDforTax" name="stateIDforTax" class="swreg-formcontrol swreg-w-100" data-displayTypeCode="SELECT" data-isRequired="1" data-requiredmsg="State/Province is required.">
							<option value=""></option>
							<cfset local.currentCountryID = 0>
							<cfloop query="local.qryStates">
								<cfif local.qryStates.countryID neq local.currentCountryID>
									<cfset local.currentCountryID = local.qryStates.countryID>
									<optgroup label="#local.qryStates.country#">
								</cfif>
								<option value="#local.qryStates.stateID#" <cfif local.swReg.currentReg.s3.stateIDforTax is local.qryStates.stateID>selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
								<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
									</optgroup>
								</cfif>
							</cfloop>
						</select>
						<div id="stateIDforTax_err" class="swreg-font-size-sm swreg-text-danger" style="display:none;"></div>
					</div>
					<div class="swreg-col">
						<div class="swreg-font-size-md swreg-font-weight-bold swreg-pb-1">Postal Code <span class="swreg-text-danger">*</span></div>
						<input type="text" id="zipForTax" name="zipForTax" class="swreg-formcontrol swreg-w-100" maxlength="25" value="#local.swReg.currentReg.s3.zipForTax#" data-displayTypeCode="TEXTBOX" data-isRequired="1" data-requiredmsg="Postal Code is required.">
						<div id="zipForTax_err" class="swreg-font-size-sm swreg-text-danger" style="display:none;"></div>
					</div>
				</div>
			<cfelse>
				<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.swReg.currentReg.s3.stateIDforTax#">
				<input type="hidden" id="zipForTax" name="zipForTax" value="#local.swReg.currentReg.s3.zipForTax#">
			</cfif>
		<cfelse>
			<!--- SW handles payment --->
			<cfif NOT len(local.swReg.currentReg.s3.billingState) OR NOT len(local.swReg.currentReg.s3.zipForTax)>
				<div class="swreg-d-flex swreg-mb-3">
					<div class="swreg-col">
						<div class="swreg-font-size-md swreg-font-weight-bold swreg-pb-1">State/Province <span class="swreg-text-danger">*</span></div>
						<cfset local.qryStates = application.objCommon.getTSStates()>
						<select id="billingstate" name="billingstate" class="swreg-formcontrol swreg-w-100" data-displayTypeCode="SELECT" data-isRequired="1" data-requiredmsg="State/Province is required.">
							<option value=""></option>
							<cfloop query="local.qryStates">
								<option value="#local.qryStates.code#" <cfif local.swReg.currentReg.s3.billingState is local.qryStates.code>selected</cfif>>#local.qryStates.name#</option>
							</cfloop>
						</select>
						<div id="billingstate_err" class="swreg-font-size-sm swreg-text-danger" style="display:none;"></div>
					</div>
					<div class="swreg-col">
						<div class="swreg-font-size-md swreg-font-weight-bold swreg-pb-1">Postal Code <span class="swreg-text-danger">*</span></div>
						<input type="text" id="zipForTax" name="zipForTax" class="swreg-formcontrol swreg-w-100" maxlength="25" value="#local.swReg.currentReg.s3.zipForTax#" data-displayTypeCode="TEXTBOX" data-isRequired="1" data-requiredmsg="Postal Code is required.">
						<div id="zipForTax_err" class="swreg-font-size-sm swreg-text-danger" style="display:none;"></div>
					</div>
				</div>
			<cfelse>
				<input type="hidden" id="billingstate" name="billingstate" value="#local.swReg.currentReg.s3.billingState#">
				<input type="hidden" id="zipForTax" name="zipForTax" value="#local.swReg.currentReg.s3.zipForTax#">
			</cfif>
		</cfif>

		<cfif NOT len(local.mainEmail)>
			<div class="swreg-d-flex swreg-mb-3">
				<div class="swreg-col">
					<div class="swreg-font-size-md swreg-font-weight-bold swreg-pb-1">Specify an email address for a confirmation email that will be sent with important registration information. <span class="swreg-text-danger">*</span></div>
					<input type="text" id="regEmail" name="regEmail" class="swreg-formcontrol span6" value="#local.swReg.currentReg.s3.email#" maxlength="200">
					<div id="regEmail_err" class="swreg-font-size-sm swreg-text-danger" style="display:none;"></div>
				</div>
			</div>
		</cfif>

		<!--- program specific custom fields --->
		<cfif listFindNoCase("SWL,SWOD",local.programType) AND local.strProgramRegFields.hasFields>
			#local.strProgramRegFields.html#
		</cfif>

		<div class="swreg-mt-5">
			<div id="step3Err" class="alert alert-danger" style="display:none;"></div>
			<button type="button" name="btnSaveStep3" id="btnSaveStep3" class="btn btn-success" onclick="doS3ValidateAndSave();">
				<cfif local.swReg.currentReg.isRegCartItem EQ 1>
					Save Changes
				<cfelse>
					<i class="icon-arrow-right"></i> Continue
				</cfif>
			</button>
		</div>
		<div id="swRegStep3SaveLoading" style="display:none;"></div>
	</form>
	<div id="swRegStep3Summary" class="swRegStepSummary swreg-cursor-pointer" data-swregsummarystep="3"<cfif local.swReg.currentReg.currentStep EQ 3> style="display:none;"</cfif>>
		<div class="swreg-d-flex">
			<a href="##" class="swreg-align-self-center swreg-mr-2 swreg-font-size-lg swreg-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="swreg-col">
				<div class="swreg-font-size-lg swreg-font-weight-bold">Registrant Details</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>