<cfscript>
	variables.applicationReservedURLParams 	= "issubmitted";
	local.customPage.baseURL			= "/?#getBaseQueryString(false)#";
	
	 local.arrCustomFields = [];
	 local.tmpField = { name="ProfileCode", type="STRING", desc="Profile Code", value="TCRACCCIM" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);
	 local.tmpField = { name="formNameDisplay", type="STRING", desc="Form name Display", value="Membership Application Form" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);
	 local.tmpField = { name="OrgEmailRecipient", type="STRING", desc="Organization Email Recipient", value="<EMAIL>" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);
	 local.tmpField = { name="memberEmailFrom", type="STRING", desc="Member Email From", value="<EMAIL>" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);
	 local.tmpField = { name="sponsorAStudentPrice", type="STRING", desc="Sponsor a student price", value="66.00" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);
	 local.tmpField = { name="InformationConfirmation", type="CONTENTOBJ", desc="Confirmation of Information and Membership Agreement", value="<p>I certify that the above information is correct and I hereby make application for membership in the Texas Court Reporters Association. I pledge myself, if accepted, to abide by the requirements of the Bylaws and Code of Professional Conduct of the Association as they may be amended in the future and to support and subscribe to the preservation and advancement of the field of verbatim reporting by the use of stenotype machine shorthand.</p>" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);
	 local.tmpField = { name="alertMessage", type="CONTENTOBJ", desc="Alert message", value="We've found an issue with your application. We apologize, but it is not available online. Please contact the TCRA office by calling 903.675.1806 or via email at <a href='mailto:<EMAIL>'><EMAIL></a>" }; 
	 arrayAppend(local.arrCustomFields, local.tmpField);	 

	 local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

	 StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
	  formName='frmJoin', 	  
	  formNameDisplay='#local.strPageFields.formNameDisplay#',
	  orgEmailTo='#local.strPageFields.OrgEmailRecipient#',
	  orgEmailFrom='<EMAIL>',
	  memberEmailFrom='#local.strPageFields.memberEmailFrom#'
	 ));
	 
	// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
	local.profile_1._profileCode 		= local.strPageFields.ProfileCode;
	local.profile_1._profileID 			= application.objCustomPageUtils.acct_getProfileID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),profileCode=local.profile_1._profileCode);

	// FORM PROTECTION: ------------------------------------------------------------------------------------------------------

	local.objCffp						= CreateObject("component","model.cfformprotect.cffpVerify").init();
	local.proffRateId					= '55E16013-DA6E-43F0-9B79-BB8CB48AF71C,733D269F-2311-4192-A066-632F9B4E6197';
	local.proffRateScheduleId			= '2AE77255-CAA3-49D4-8CF3-8ADF2AFA6D06';
	
	local.associateRateId 				= '188123E1-92C1-4AA5-8ABD-127C1D044F54,D8DD2BFA-248A-401A-97FC-36F0F0F7DFEE';
	local.associateRateScheduleId 		= '6AB451A5-8D2F-408F-B073-F78162DEFB72';
	
	local.studentRateId 				= '527DC299-BF1A-47C8-BEB9-232B04484398,105AA474-E900-4D94-BF1B-F714CEDB68BD';
	local.studentRateScheduleId 		= '4E7A75C6-BBD7-4FF2-B990-B1E73E8C6B46';

	local.retiredRateId 				= '2699118F-2F84-48C2-B43D-3EC64F9B8BC1,9DB49E6B-03B9-4411-8DAF-1C432A87B476';
	local.retiredRateScheduleId 		= '0B812644-30CD-424E-BB5F-4A2767DF6582';
	
	local.sponsorRateId 				= 'd9a66496-1d19-442b-96e9-f2e9cd4ffac2,d4c3f662-639c-44aa-9939-0fefcf6e7d9a';
	local.sponsorRateScheduleId 		= '8659D8C4-AAF0-455C-832C-33672CB8A5CF';
	
	local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID="E233AFD9-DC9E-4E0D-983F-0524C776CED9");
	local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http');
</cfscript>

<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscription">
	select 
		s.subscriberID, s.directLinkCode, ss.statusCode as status, count(s2.subscriberID) as currAccepted
	from 
		dbo.sub_subscribers s
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
		inner join dbo.sub_types t on 
			t.typeID = subs.typeID 
			and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			and t.typeName = 'Membership Dues'
		inner join dbo.sub_statuses ss on 
			ss.statusID = s.statusID
		left outer join dbo.sub_subscribers s2 
			inner join dbo.sub_subscriptions subs2 on subs2.subscriptionID = s2.subscriptionID
			inner join dbo.sub_types t2 on 
				t2.typeID = subs2.typeID 
				and t2.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
				and t2.typeName = 'Membership Dues'
			inner join dbo.sub_statuses ss2 on ss2.statusID = s2.statusID on 
				s2.memberID = s.memberID
				and ss2.statusCode = 'P'
				and s2.parentSubscriberID is null
	where 
		s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberid#">
		and (ss.statusCode = 'O')
		and s.parentSubscriberID is null
	group by 
		s.subscriberID, s.directLinkCode, ss.statusCode
</cfquery>

<!--- If user has a record with a billed Membership Dues subscription type, direct them to renewal page  --->
<cfif local.qrySubscription.recordcount eq 1>	
	<cflocation url="/renewsub/#local.qrySubscription.directLinkCode#" addtoken="no">
</cfif>

<cfquery name="local.counties" datasource="#application.dsn.membercentral.dsn#">
	select mdcv.valueID, mdcv.columnValueString as columnValue
	from ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
	on mdcv.columnID = mdc.columnID
	and mdc.columnName = 'Counties'

	where orgID = #event.getValue('mc_siteInfo.orgID')#
</cfquery>
<cfquery name="local.reporters" datasource="#application.dsn.membercentral.dsn#">
	select mdcv.valueID, mdcv.columnValueString as columnValue
	from ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
	on mdcv.columnID = mdc.columnID
	and mdc.columnName = 'Type of Reporter'

	where orgID = #event.getValue('mc_siteInfo.orgID')#
</cfquery>
<cfquery name="local.testedMethod" datasource="#application.dsn.membercentral.dsn#">
	select mdcv.valueID, mdcv.columnValueString as columnValue
	from ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
	on mdcv.columnID = mdc.columnID
	and mdc.columnName = 'Method of Reporting'

	where orgID = #event.getValue('mc_siteInfo.orgID')#
</cfquery>


<cfquery name="local.certifications" datasource="#application.dsn.membercentral.dsn#">
	select mdcv.valueID, mdcv.columnValueString as columnValue
	from ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
	on mdcv.columnID = mdc.columnID
	and mdc.columnName = 'Certifications'
	where orgID = #event.getValue('mc_siteInfo.orgID')#
</cfquery>

<cfquery name="local.cartLevel" datasource="#application.dsn.membercentral.dsn#">
	select mdcv.valueID, mdcv.columnValueString as columnValue
	from ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
	on mdcv.columnID = mdc.columnID
	and mdc.columnName = 'Texas CART Certified'
	where orgID = #event.getValue('mc_siteInfo.orgID')#
</cfquery>
<cfquery name="local.getMemBerShipDues" datasource="#application.dsn.membercentral.dsn#">
	select subscriptionName,r.uid,rs.uid,s.scheduleID,rf.rateAmt,r.rateName
			  FROM dbo.sub_Types t
			 inner JOIN dbo.sub_subscriptions s on t.typeID = s.typeID
			 inner join dbo.sub_rates r on s.scheduleID = r.scheduleID
			 inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID
			 inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			 inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID

			  where t.siteID= #event.getValue('mc_siteInfo.siteID')#
			  AND rs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.associateRateScheduleId#">
			 AND r.uid IN  (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.associateRateId#" list="true">)
			 AND getDate() between  r.rateAFStartDate and r.rateAFEndDate

			  UNION

			  SELECT subscriptionName,r.uid,rs.uid,s.scheduleID,rf.rateAmt,r.rateName
			  FROM dbo.sub_Types t
			 inner JOIN dbo.sub_subscriptions s on t.typeID = s.typeID
			 inner join dbo.sub_rates r on s.scheduleID = r.scheduleID
			 inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID
			 inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			 inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID

			  where t.siteID=#event.getValue('mc_siteInfo.siteID')#
			  AND rs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.proffRateScheduleId#">
			  AND r.uid  IN  (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.proffRateId#" list="true">)
			  AND getDate() between  r.rateAFStartDate and r.rateAFEndDate
			  UNION
			  SELECT subscriptionName,r.uid,rs.uid,s.scheduleID,rf.rateAmt,r.rateName
			  FROM dbo.sub_Types t
			 inner JOIN dbo.sub_subscriptions s on t.typeID = s.typeID
			 inner join dbo.sub_rates r on s.scheduleID = r.scheduleID
			 inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID
			 inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			 inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID

			  where t.siteID = #event.getValue('mc_siteInfo.siteID')#
			  AND rs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.studentRateScheduleId#">
			  AND r.uid IN  (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.studentRateId#" list="true">)
			  AND getDate() between  r.rateAFStartDate and r.rateAFEndDate
			  UNION

			  SELECT subscriptionName,r.uid,rs.uid,s.scheduleID,rf.rateAmt,r.rateName
			  FROM dbo.sub_Types t
			 inner JOIN dbo.sub_subscriptions s on t.typeID = s.typeID
			 inner join dbo.sub_rates r on s.scheduleID = r.scheduleID
			 inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID
			 inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			 inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID

			  where t.siteID=#event.getValue('mc_siteInfo.siteID')#
			  AND rs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.retiredRateScheduleId#">
			  AND r.uid IN  (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.retiredRateId#" list="true">) 
			  AND getDate() between  r.rateAFStartDate and r.rateAFEndDate
	</cfquery>
	
<cfoutput>
	
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Arial, Helvetica, sans-serif;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##03408b; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##03408B; border-bottom:1px solid ##03408B; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.CPSection{ border:1px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:14pt; height:20px; font-weight:bold; color:##fff; padding:10px; background:rgb(31,122,154); }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.tsAppBodyText { color:##03408b;}
			select.tsAppBodyText{color:##666;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			
		</style>
	</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<cfsavecontent variable="local.pageJS">
		<script type="text/javascript">
			
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
				var cnt = 0;
				for (var i=0; i < obj.length; i++) {
					if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
					else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
				}					
				if (cnt <= optRespLimit) return true;
				else return false;
			}
			function formatCurrency(num) {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				num = Math.floor(num/100).toString();
				if(cents<10) cents = "0" + cents;
				for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
				num.substring(num.length-(4*i+3));
				return (((sign)?'':'-') + '$' + num + '.' + cents);
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
			
			function selectMember() {
				$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
			}
			function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
			function addMember(memObj) {
				$.colorbox.close();
				assignMemberData(memObj);
			}
		</script>
	</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	#local.pageJS#
	#local.pageCSS#
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<div id="customPage">
		<div class="TitleText" style="padding-bottom:15px;">#local.Organization# - #local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: ========================================================================================================================================= --->
			<cfcase value="0">
				
				<script type="text/javascript">
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] 				= 'First Name';
							if (!_FB_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] 					= 'Last Name';
							if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] 					= 'Email';
							if (!_FB_hasValue(thisForm['b_phone'], 'TEXT')) arrReq[arrReq.length] 					= 'Work Phone';
						// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['address1'], 'TEXT')) arrReq[arrReq.length] 					= 'Mailing Address';
							if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] 						= 'Mailing City';
							if (!_FB_hasValue(thisForm['state'], 'TEXT')) arrReq[arrReq.length] 					= 'Mailing State';
							if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] 						= 'Mailing Zip';
						// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['membership'], 'SELECT')) arrReq[arrReq.length] 				= 'Membership Type';
							if (!_FB_hasValue(thisForm['membershipAgreement'], 'CHECKBOX')) arrReq[arrReq.length]   = 'Membership Agreement';
							if (!_FB_hasValue(thisForm['county'], 'SELECT')) arrReq[arrReq.length]   = 'County';
							if (!_FB_hasValue(thisForm['reporterType'], 'SELECT')) arrReq[arrReq.length]   = 'Type of Reporter';
							var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;

							if($("##jayeThompson").is(':checked')){
								if( ($.trim($('##donationAmount').val()).length == 0) ||( $.trim($('##donationAmount').val()).length > 0 && !amountRegex.test($.trim($('##donationAmount').val() ))))  {
									arrReq[arrReq.length] = 'Donation Amount - Enter a valid amount. Only positive amounts are allowed.';
								}
								
							}
							
						// -----------------------------------------------------------------------------------------------------------------
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
					// -----------------------------------------------------------------------------------------------------------------
					function assignMemberData(memObj){
						var thisForm = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								thisForm['memberNumber'].value 	= results.membernumber;
								thisForm['memberID'].value 		= results.memberid;
								thisForm['firstName'].value 	= results.firstname;
								thisForm['lastName'].value 		= results.lastname;
								thisForm['firmCourt'].value		= results.company;
								thisForm['address1'].value 		= results.address1;
								thisForm['address2'].value 		= results.address2;
								thisForm['city'].value 			= results.city;
								thisForm['state'].value 		= results.statecode;
								thisForm['zip'].value 			= results.postalcode;
								thisForm['county'].value 		= results.county;
								thisForm['b_phone'].value 		= results.phone;
								thisForm['email'].value 		= results.email;
								
								thisForm['paymentDate'].value 	 = results["payment date"];
								$.each(results.licenses , function(i, val) { 
										
									  if(results.licenses [i]['plname'] == "Certified Shorthand Reporter"){
									  	thisForm['csrDate'].value 			= results.licenses [i]['activedate'];
									  	return false;
									  }
									});	
								//---------------------------------------------------------------------------------------
								// un hide form
								var objParams = { typeName:'Membership Dues', memberID:results.memberid };
								TS_AJX('CUSTOM_FORM_UTILITIES','sub_isbilledSubscription',objParams,sub_change,sub_change,1000000,sub_change);

								AJAXCheckActiveSubs(results.memberid);
								//document.getElementById('formToFill').style.display 			= '';
							}
							else{ /*alert('not success');*/ }
						};
						/************************************************************************************************/
						var arrKeys = ["Payment Date"] ;
						var objParams = { memberNumber:memObj.memberNumber, customfields:arrKeys };
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);

					}

					var sub_change = function(r) {
						if(r.success == 'true'){
							window.location.href = '#local.thisScheme#://#application.objPlatform.getCurrentHostname()#/renewsub/'+r.directlinkcode;
						}
					};
					// -----------------------------------------------------------------------------------------------------------------
					
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}

					function AJAXCheckActiveSubs(member){
							var stopProgress = function(r){
								
								if ( r == true){
										$('##formToFill').hide();
										$('##hasActiveDuesSubContent').show();
										advForm(1);
								}
								else { 
									$('##hasActiveDuesSubContent').hide();
									$('##formToFill').show();	
								}
							};
							
							var params = { memberID:member, typeID:#local.membershipDuesTypeID# };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_hasSubsciptionInType',params,stopProgress);
						}
					
					function showDonation(){
						var jaye = document.getElementById('jayeThompson');
						var span = document.getElementById('donationAmt');
						var donateAmt = document.getElementById('donationAmount');
						if (jaye.checked == 1){
							span.style.display = '';
						}
						else {
							span.style.display = 'none';
							donateAmt.value = '';
							calcDues();
						}
					}

					function calcDues(){
						var membership = $("##membership").val();
						var dues = 0;
						var currentTime = new Date()
						var thisMonth = currentTime.getMonth() + 1;
						
						if(membership != ""){
							var valarr = membership.split('|');
							dues = valarr[1];
						}
						calcExtras(dues);
					}

					function calcExtras(dues){
						var donate = document.getElementById('jayeThompson');
						var donateAmt = document.getElementById('donationAmount');
						var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
					

						if (donateAmt.value != '' && donate.checked == 1 && amountRegex.test($.trim($('##donationAmount').val() ))){
							dues = Math.round(parseFloat(dues)) + parseFloat(donateAmt.value);
						}
						if (donateAmt.value != '' && donate.checked == 1 && !amountRegex.test($.trim($('##donationAmount').val() ))){
							alert("Donation Amount - Enter a valid amount. Only positive amounts are allowed."); 
							$('##donationAmount').val("");
						}						
						calcSponsor(parseFloat(dues));
					}
					
					function calcSponsor(dues){
						sponsorRate = $('##sponsorStu').data('sponsorrate');
						rate = 0;
						if(!isNaN(sponsorRate) && sponsorRate != ''){
							rate = sponsorRate;
						}
						var sponStu = document.getElementById('sponsorStu');
						if (sponStu.checked == 1){
							dues = parseFloat(dues) + parseFloat(rate);
							$('##sponsorStudentRate').val(rate);
						}else{
							$('##sponsorStudentRate').val(0);
						}
						showTotal(parseFloat(dues));	
					}
										
					function showTotal(dues){
						var totalSpan = document.getElementById('currTotal');
						totalSpan.innerHTML = '$' + dues;
					}
					$(document).ready(function(){
							mca_setupDatePickerField('birthDate');
							mca_setupDatePickerField('csrDate');
						});	
				</script>
				
					
				
				<div class="r i frmText">*Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					<input type="hidden" name="sponsorStudentRate" id="sponsorStudentRate" value="0" />
					<!--- =============================================================================================================================================== --->
					
					<!--- Payment: ---><cfinput type="hidden" name="paymentDate"  id="paymentDate" value="" /> 
					
					<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="c">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
												<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
												<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
												<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div id="hasActiveDuesSubContent" style="display:none;">
									<div class="CPSection">
										<div class="CPSectionTitle BB">Alert!</div>
										<div class="frmRow1" style="padding:10px;">
											<table cellspacing="0" cellpadding="2" border="0" width="100%">
												<tr>
													<td>
														#local.strPageFields.alertMessage#
													</td>
												</tr>
											</table>
										</div>
									</div>
								</div>
					</cfif>
					<!--- =============================================================================================================================================== --->
					
					
					
					<div id="formToFill" style="display:none;">
						<!--- PERSONAL INFORMATION: ========================================================================================================================= --->
					
						<!--- <cfdump var="#local.contactTypeColumnValueIDs#">
						<cfabort> --->
						<cfoutput>#local.data.address.county#</cfoutput>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Personal Information</div>
							<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="r"width="135">*First Name:</td>
										<td><input size="40" name="firstName" type="text" value="#session.cfcUser.memberData.firstname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Last Name:</td>
										<td><input size="40" name="lastName" type="text" value="#session.cfcUser.memberData.lastname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Date of Birth:</td>
										<td class="tsAppBodyText frmText">
										 <input size="20" name="birthDate" type="text" value="" id="birthDate" class="tsAppBodyText" />
										 <a href="javascript:mca_clearDateRangeField('birthDate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -2px 7px;"></i></a>
										</td>
									</tr>
									<tr>
										<td class="r">*Email Address:</td>
										<td><input size="40" name="email" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Website Address:</td>
										<td><input size="40" name="website" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Texas CSR Number:</td>
										<td><input size="40" name="csrNum" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Date Passed CSR:</td>
										<td><input id="csrDate" size="20" name="csrDate" type="text" value="" class="tsAppBodyText" />
										<a href="javascript:mca_clearDateRangeField('csrDate');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -2px 7px;"></i></a>
									</td>
									</tr>
									<tr>
										<td class="r" width="115">Company/Court:</td>
										<td><input size="40" name="firmCourt" type="text" value="#session.cfcUser.memberData.Company#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Mailing Address 1:</td>
										<td><input size="40" name="address1" type="text" value="#local.data.address.address1#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Address 2:</td>
										<td><input size="40" name="address2" type="text" value="#local.data.address.address2#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*City:</td>
										<td class="tsAppBodyText frmText">
											<input size="25" name="city" type="text" value="#local.data.address.city#" class="tsAppBodyText" />
											&nbsp;*State:
											<input size="2" maxlength="2" name="state" type="text" value="#local.data.address.stateCode#" class="tsAppBodyText" />
											&nbsp;*Zip:
											<input size="10" maxlength="15" name="zip" type="text" value="#local.data.address.postalCode#" class="tsAppBodyText" />
										</td>
									</tr>

									<tr>
										<td class="r">*County:</td>
										<td>
											<select name="county" class="tsAppBodyText">
												<option value="">Select county</option>
												<cfloop query="local.counties">
													<option value="#local.counties.columnValue#" 
															"selected=selected"
														>
												#local.counties.columnValue#</option>
												</cfloop>

											</select>
										</td>
									</tr>
									<tr>
										<td class="r">*Work Phone:</td>
										<td class="tsAppBodyText frmText">
											<input size="13" maxlength="13" name="b_phone" type="text" value="#local.data.phone.phone#" class="tsAppBodyText" />
											&nbsp;&nbsp;
											Fax:&nbsp;
											<input size="13" maxlength="13" name="b_fax" type="text" value="" class="tsAppBodyText" />
										</td>
									</tr>
									<tr>
										<td class="r">Home Phone:</td>
										<td class="tsAppBodyText frmText">
											<input size="13" maxlength="13" name="h_phone" type="text" value="" class="tsAppBodyText" />
											&nbsp;
											
										</td>
									</tr>
									<tr>
										<td class="r">Cell Phone:</td>
										<td class="tsAppBodyText frmText">
											<input size="13" maxlength="13" name="c_phone" type="text" value="" class="tsAppBodyText" />
											&nbsp;
											
										</td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->

						<div class="CPSection">
							<div class="CPSectionTitle">Professional Information</div>
							<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="tsAppBodyText frmText r" width="135px">*Type of Reporter:</td>
										<td class="tsAppBodyText frmText">
											<select name="reporterType" value="">
												<option value="" select="yes"> - Please Select - </option>
												<cfloop query="local.reporters">
													<option value="#local.reporters.columnValue#">
												#local.reporters.columnValue#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText frmText r" width="135px">Tested Method:</td>
										<td class="tsAppBodyText frmText">
											<select name="testMethod" value="">
												<option value=""> - Please Select - </option>
												<cfloop query="local.testedMethod">
													<option value="#local.testedMethod.columnValue#">
												#local.testedMethod.columnValue#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText frmText r" width="135px">Realtime Reporter:</td>
										<td class="tsAppBodyText frmText">
											<select name="realtimeReporter" value="">
												<option value="" select="yes"> - Please Select - </option>
												<option value="Yes">Yes</option>
												<option value="No">No</option>
											</select>
										</td>
									</tr>
									<tr>
										<td class="r">Software Provider:</td>
										<td><input size="40" name="softwareProvider" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									
									<tr>
										<td class="tsAppBodyText frmText r" width="135px">Certifications:</td>
										<td class="tsAppBodyText frmText">
											<cfloop query="local.certifications">
												<input type="checkbox" name="certs" value="#local.certifications.valueID#">
											#local.certifications.columnValue#&nbsp;
											</cfloop>
										</td>
									</tr>
									
									<tr>
										<td class="tsAppBodyText frmText r" width="135px">CART Reporter Level:</td>
										<td class="tsAppBodyText frmText">
											<select name="cartLevel" value="">
												<option value=""> - Please Select - </option>
												<cfloop query="local.cartLevel">
													<option value="#local.cartLevel.columnValue#">
												#local.cartLevel.columnValue#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText frmText r" width="135px">Current Employment:</td>
										<td class="tsAppBodyText frmText">
											<select name="currentEmployment" value="">
												<option value="" select="yes"> - Please Select - </option>
												<option value="Full-time Reporter">Full-time Reporter</option>
												<option value="Part-time Reporter">Part-time Reporter</option>
												<option value="In Another Occupation">In Another Occupation</option>
											</select>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						
						
						<div class="CPSection">
							<div class="CPSectionTitle">*Membership Type</div>
							<div class="tsAppBodyText subCPSectionArea1 BB">
								<-- ZONE G -->
							</div>
							<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
								<tr>
									<td width="135px" class="r">Membership Type:</td>
									<td>
									
										<select name="membership" value="" id="membership" onClick="calcDues();">
											<option value="" selected="yes"> - Please Select - </option>
											<cfloop query="local.getMemBerShipDues">
													<option value="#local.getMemBerShipDues.rateName#|#local.getMemBerShipDues.rateAmt#">
												#local.getMemBerShipDues.rateName# - #DollarFormat(local.getMemBerShipDues.rateAmt)#</option>
												</cfloop>	
										</select>
									</td>
								</tr>
							</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle">Would you like to:</div>
							<div class="tsAppBodyText frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="tsAppBodyText frmText" colspan="2">
											<input type="checkbox" name="proBono" value="1">&nbsp;Donate your time and services to the Pro Bono Program?
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText frmText" colspan="2">
											<input type="checkbox" name="tcraCommittee" value="">&nbsp;Serve on a TCRA committee?
										</td>
									</tr>
									<tr>
										<td class="tsAppBodyText frmText" colspan="2">
											<input type="checkbox" name="jayeThompson" id="jayeThompson" value="Yes" onClick="showDonation();">&nbsp;Donate towards the Jaye Thompson Student Scholarship?
										</td>
									</tr>
									<tr>
										<td>
											<table id="donationAmt" style="display:none;">
												<tr>
													<td class="r" width="155px">Enter Donation Amount:</td>
													<td><input size="40" name="donationAmount" id="donationAmount" type="text" value="" class="tsAppBodyText" onBlur="calcDues();" /><span class="info">(numbers only)</span></td>
												</tr>
											</table>
										</td>
									</tr>
									<cfif local.strPageFields.sponsorAStudentPrice neq ''>
										<tr>
											<td class="tsAppBodyText frmText" colspan="2">
												<input type="checkbox" name="sponsorStu" data-sponsorrate="#numberformat(local.strPageFields.sponsorAStudentPrice,'__.00')#" id="sponsorStu" onClick="calcDues();">&nbsp;Sponsor a student for $#numberformat(local.strPageFields.sponsorAStudentPrice,'__.00')#  ?
											</td>
										</tr>
									</cfif>
									
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle">Current Dues Total</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table cellspacing="0" cellpadding="4" width="40%" border="0" align="center">
									<tr>
										<td class="subCPSectionTitle tsAppBodyText b l P">Current Total:</td>
										<td class="tsAppBodyText frmText r P">
											<span id="currTotal">$0</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle">*Confirmation of Information and Membership Agreement</div>
							<div class="tsAppBodyText subCPSectionArea1 BB">
								<div class="tsAppBodyText subCPSectionText">
									#local.strPageFields.InformationConfirmation#
								</div>
							</div>
							<div class="tsAppBodyText frmRow1 frmText">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="tsAppBodyText frmText c P">
											<input type="checkbox" name="membershipAgreement" value="">&nbsp;Check here to agree to the above.
										</td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
					
						<!---  style="display:none;" --->
						<!--- BUTTONS: ====================================================================================================================================== --->					
						<div id="formButtons">
							<div style="padding:10px;">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
								</div>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->					
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
				
			</cfcase>
			
			<!--- PAYMENT INFO: ================================================================================================================================= --->
			<cfcase value="1">
				<cfscript>
					// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
					local.profile_1.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																			siteid=arguments.event.getValue('mc_siteinfo.siteid'),
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopupDIVName='paymentTable'
																		);
				</cfscript>
				<script type="text/javascript">
					function checkPaymentMethod() {
						var rdo = document.forms["#local.formName#"].payMeth;
						if (rdo[0].checked) {//credit card
							document.getElementById('CCInfo').style.display = '';
							document.getElementById('CheckInfo').style.display = 'none';
						}  
						else if (rdo[1].checked) {//check
							document.getElementById('CCInfo').style.display = 'none';
							document.getElementById('CheckInfo').style.display = '';
						}  
						
					}
					// -----------------------------------------------------------------------------------------------------------------
					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					// -----------------------------------------------------------------------------------------------------------------
					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatement'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}
						
						// -----------------------------------------------------------------------------------------------------------------
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
				</script>
				
				<script>
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
							<div>
								<!--- ----------------------------------------------------------------------------------------------------- --->
								<div class="CPSection">
									<div class="CPSectionTitle">*Method of Payment</div>
									<div class="P">
										<table cellpadding="2" cellspacing="0" width="100%" border="0">
											<tr valign="top">
												<td colspan="2">Please select your preferred method of payment from the options below.</td>
											</tr>
											<tr>
												<td>
													<table cellpadding="2" cellspacing="0" width="100%" border="0">
														<tr>
															<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
															<td>Credit Card</td>
														</tr>
														<tr>
															<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
															<td>Check</td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
								
								<!--- CREDIT CARD INFO: ----------------------------------------------------------------------------------- --->
								<div id="CCInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Credit Card Information</div>
									<div class="PL PR frmText paymentGateway BT BB">
										<cfif len(local.profile_1.strPaymentForm.inputForm)>
											<div>#local.profile_1.strPaymentForm.inputForm#</div>
										</cfif>
									</div>
									
									<div class="P">
										<div class="PB">* Please confirm the statement below:</div>
										<table width="100%">
											<tr>
												<td width="25"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
												<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
											</tr>
										</table>
									</div>
									
									<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">AUTHORIZE</button></div>
								</div>
								
								<!--- CHECK INFORMATION: ---------------------------------------------------------------------------------- --->
								<div id="CheckInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Check Information</div>
									<div class="P">
										
												Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
												<strong>Texas Court Reporters Association</strong><br />
												P.O. Box 2379<br />
												Athens, TX 75751
												
												
									</div>
									<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button></div>
								</div>
								
							</div>
							<cfinclude template="/model/cfformprotect/cffp.cfm" />
						</cfform>
					</div>
				</div>
			</cfcase>
		
			<!--- PROCESS: ====================================================================================================================================== --->
			<cfcase value="2">
				
				<!--- CHECK FOR SPAM SUBMISSION: -------------------------------------------------------------------------------- --->
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>

				<cfset local.USStates = application.objCustomPageUtils.mem_getStatesByCountry('United States')>
				<cfquery name="local.qryStateByStateCode" dbtype="query">
					select stateID
					from [local].USStates
					where code = '#arguments.event.getValue("state")#'
				</cfquery>

				<cfset local.recordUpdated = false>
				<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
				<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),event.getTrimValue('memberid',0))>			
					<cfscript>
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'));
					local.objSaveMember.setDemo(firstName=arguments.event.getTrimValue('firstname',''), 
												lastName=arguments.event.getTrimValue('lastName',''), 
												company=arguments.event.getTrimValue('firmCourt',''),
												membernumber=arguments.event.getTrimValue('memberNumber',''));
					local.objSaveMember.setMemberType(memberType='User');
					local.objSaveMember.setMemberStatus(memberStatus='Active');
					local.objSaveMember.setRecordType(recordType='Individual');
					local.objSaveMember.setEmail(type='Email', value=arguments.event.getTrimValue('email',''));
					local.objSaveMember.setWebsite(type='Website', value=arguments.event.getTrimValue('website',''));
					local.objSaveMember.setAddress(type='Address', address1=arguments.event.getValue('address1',''),address2=arguments.event.getValue('address2',''), city=arguments.event.getValue('city',''), stateID=local.qryStateByStateCode.stateID, postalCode=arguments.event.getValue('zip',''), county=arguments.event.getValue('county',''));
					local.objSaveMember.setPhone(addressType='Address', type='Work Phone', value=arguments.event.getValue('b_phone',''));
					local.objSaveMember.setPhone(addressType='Address', type='Home Phone', value=arguments.event.getValue('h_phone',''));
					local.objSaveMember.setPhone(addressType='Address', type='Cell Phone', value=arguments.event.getValue('c_phone',''));
					local.objSaveMember.setPhone(addressType='Address', type='Fax', value=arguments.event.getValue('b_fax',''));
					local.objSaveMember.setCustomField(field='Birthdate', value=arguments.event.getValue('birthDate'));
					local.objSaveMember.setCustomField(field='Counties', value=arguments.event.getValue('county',''));
					local.objSaveMember.setCustomField(field='Type of Reporter', value=arguments.event.getValue('reporterType','0'));
					local.objSaveMember.setCustomField(field='Method of Reporting', value=arguments.event.getValue('testMethod','0'));
					if (arguments.event.getValue('certs','0'))
						local.objSaveMember.setCustomField(field='Certifications', valueID=arguments.event.getValue('certs','0'));
					local.objSaveMember.setCustomField(field='Texas CART Certified', value=arguments.event.getValue('cartLevel','0'));
					local.objSaveMember.setCustomField(field='Software Provider', value=arguments.event.getValue('softwareProvider','0'));
					local.objSaveMember.setCustomField(field='CSR Number', value=arguments.event.getValue('csrNum','0'));
					local.objSaveMember.setCustomField(field='Date Passed CSR', value=arguments.event.getValue('csrDate','0'));
					if (arguments.event.getValue('proBono','0'))
						local.objSaveMember.setCustomField(field='Pro Bono Volunteer', value='Y');
					else
						local.objSaveMember.setCustomField(field='Pro Bono Volunteer', value='N');
					if (arguments.event.getValue('realtimeReporter','') eq "Yes")
						local.objSaveMember.setCustomField(field='Realtime Provider', value='Y');
					else
						local.objSaveMember.setCustomField(field='Realtime Provider', value='N');
					local.objSaveMember.setCustomField(field='Payment Date', value=dateFormat(createDate(year(now()),month(now()),day(now())),"mm/dd/yyyy"));

					local.strResult = local.objSaveMember.saveData();
					</cfscript>

					<cfif local.strResult.success>
						<cfset local.recordUpdated = true>	
					<cfelse>					
						<cfset local.recordUpdated = false>	
					</cfif>
				</cfif>

				<!--- ----------------------------------------------------------------------------------------------------------- --->
				<cfscript>
					event.paramValue('certs','');
					local.membership = ListToArray(event.getValue('membership'),'|');
					local.memberShipName = local.membership[1];
					local.memberShipDues = local.membership[2];

					local.totalAmount = local.memberShipDues;
				</cfscript>
				
				<cfif (event.getValue('donationAmount') neq '')>
					<cfset local.donationAmount = event.getValue('donationAmount')>
					<cfset local.totalAmount = local.totalAmount + int(local.donationAmount)>
				</cfif>
				
				<cfif (isDefined('sponsorStu'))>
					<cfset local.totalAmount = local.totalAmount + event.getValue('sponsorStudentRate')>
				</cfif>
				
				<cfsavecontent variable="local.name">
					#event.getValue('firstName','')# <cfif len(trim(event.getValue('middleName','')))>#event.getValue('middleName','')# </cfif>#event.getValue('lastName','')#
				</cfsavecontent>

				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & local.name />
				
				<cfset local.certificatesList = "">
				<cfif event.valueExists('certs') and len(trim(event.getValue('certs')))>
					<cfquery name="local.getCertificates" datasource="#application.dsn.membercentral.dsn#">
					select mdcv.valueID, mdcv.columnValueString as columnValue
					from ams_memberDataColumns mdc
					inner join ams_memberdatacolumnvalues mdcv
						on mdcv.columnID = mdc.columnID
						and mdc.columnName = 'Certifications'
						and mdcv.valueID IN (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#event.getValue('certs')#">)
					where orgID = #event.getValue('mc_siteInfo.orgID')#
					</cfquery>
					<cfset local.certificatesList = valueList(local.getCertificates.columnValue)>
				</cfif>				

				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					
					<!-- @accResponseMessage@ -->
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					
					<tr class="msgHeader"><td colspan="2" class="b">PERSONAL INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Date of Birth:</td><td class="frmText">#event.getValue('birthDate')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Email:</td><td class="frmText">#event.getValue('email')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Texas CSR Number:</td><td class="frmText">#event.getValue('csrNum')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Date Passed CSR:</td><td class="frmText">#event.getValue('csrDate')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Company/Court:</td><td class="frmText">#event.getValue('firmCourt')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Mailing Address 1:</td><td class="frmText">#event.getValue('address1')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Address 2:</td><td class="frmText">#event.getValue('address2')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip')#&nbsp;</td></tr>

						<tr class="frmRow1"><td class="frmText b">County:</td><td class="frmText">#event.getValue('county')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Work Phone:</td><td class="frmText">#event.getValue('b_phone')#&nbsp;</td>
						</tr>
						<tr class="frmRow1"><td class="frmText b">Fax:</td><td class="frmText">#event.getValue('b_fax')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home Phone:</td><td class="frmText">#event.getValue('h_phone')#&nbsp;</td>
						</tr>
						<tr class="frmRow2"><td class="frmText b">Cell Phone:</td><td class="frmText">#event.getValue('c_phone')#&nbsp;</td>
						</tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">PROFESSIONAL INFORMATION</td></tr>						

						<tr class="frmRow1"><td class="frmText b">Type of Reporter:</td><td class="frmText">#event.getValue('reporterType')#&nbsp;</td></tr>

						<tr class="frmRow2"><td class="frmText b">Tested Method:</td><td class="frmText">#event.getValue('testMethod')#&nbsp;</td></tr>

						
						<tr class="frmRow2"><td class="frmText b">Realtime Reporter:</td><td class="frmText">#event.getValue('realtimeReporter')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Software Provider:</td><td class="frmText">#event.getValue('softwareProvider')#&nbsp;</td></tr>

						<tr class="frmRow2"><td class="frmText b">Certifications:</td><td class="frmText">#local.certificatesList#</td></tr>						
						<tr class="frmRow1"><td class="frmText b">CART Reporter Level:</td><td class="frmText">#event.getValue('cartLevel')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Current Employment:</td><td class="frmText">#event.getValue('currentEmployment')#&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP TYPE</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.memberShipName#:</td><td class="frmText">#dollarFormat(local.memberShipDues)#&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">I WOULD LIKE TO</td></tr>
						<tr class="frmRow1"><td class="frmText b">Donate time and services to the Pro Bono Program:</td><td class="frmText"><cfif isDefined('proBono')>Yes<cfelse>No</cfif>&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Serve on a TCRA Committee:</td><td class="frmText"><cfif isDefined('tcraCommittee')>Yes<cfelse>No</cfif>&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Donate towards the Jaye Thompson Student Scholarship:</td><td class="frmText"><cfif isDefined('jayeThompson')>Yes<cfelse>No</cfif>&nbsp;</td></tr>
						<cfif event.getValue('donationAmount') neq ''>
							<tr class="frmRow2"><td class="frmText b">Donation amount:</td><td class="frmText">$#event.getValue('donationAmount')#&nbsp;</td></tr>
						</cfif>
						<tr class="frmRow1"><td class="frmText b">Sponsor a student:</td><td class="frmText"><cfif isDefined('sponsorStu')>Yes - $#event.getValue('sponsorStudentRate')#<cfelse>No</cfif>&nbsp;</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2">PAYMENT INFORMATION</td></tr>
						<tr class="frmRow2"><td class="frmText b">Payment Type: </td><td class="frmText"><cfif event.getValue('payMeth','CC') EQ 'CC'>Credit Card<cfelse>Check</cfif></td></tr>
						<tr class="frmRow1"><td class="frmText b">Payment Total:</td><td class="frmText">#dollarFormat(local.totalAmount)#&nbsp;</td></tr>
					
					</table>
				</cfsavecontent>
				
				<!--- email member ---------------------------------------------------------------------------------------------- --->

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! It will now be reviewed and processed by TCRA staff. Please print this page for your records</p>	
						<hr />
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>
				
				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.memberEmail.from },
					emailto=[{ name="", email=local.memberEmail.to }],
					emailreplyto=local.ORGEmail.to,
					emailsubject=local.memberEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val(local.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				  )>
				  
				<cfset local.emailSentToUser = local.responseStruct.success>

				<!--- email association ----------------------------------------------------------------------------------------- --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p style="color:red;">We were not able to send #local.name# an e-mail confirmation.</p>
						</cfif>
						#local.invoice#
					</cfoutput>
				</cfsavecontent>
				
				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>
				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.ORGEmail.from },
					emailto=local.arrEmailTo,
					emailreplyto=local.ORGEmail.from,
					emailsubject=local.ORGEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getValue('mc_siteinfo.siteID'),
					memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>
				
				<!--- relocate to message page --->
				<cfset session.invoice = local.invoice />
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			
			<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
			<cfcase value="99">
				<!--- output to screen --->
				<div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<cfif isDefined("session.invoice")>
					<div>It will now be reviewed and processed by TCRA staff. Please print this page for your records.</div>
					<br />
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
				</cfif>
			</cfcase>
			
			<!--- SPAM MESSAGE: ================================================================================================================================= --->
			<cfcase value="100">
					<div>
						Error! you Can't Post Here.
					</div>
			</cfcase>
			
		</cfswitch>
	</div>

</cfoutput>