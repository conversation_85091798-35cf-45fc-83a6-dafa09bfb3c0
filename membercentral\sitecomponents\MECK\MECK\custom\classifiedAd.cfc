<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			local.crlf = chr(13) & chr(10);

			/* ************************* */
			/* Custom Page Custom Fields */
			/* ************************* */
			local.arrCustomFields = [];
			
			local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Identify Yourself" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Continue" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormTitle",type="STRING",desc="form title",value="Submit a Classified Ad" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormInstructions",type="CONTENTOBJ",desc="text below form title",value="For questions, Contact <EMAIL> or 704/375-8624 ext.124" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ContactInfoFieldSet",type="STRING",desc="Field Set UID for Contact Info",value="fcee3ce0-0316-4cad-9d07-80f45403e83a" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CategoryOptions",type="STRING",desc="pipe-delimited list of options for the Category dropdown",value="Attorney Positions Available	| Attorney Positions Sought | For Sale | Office Space | Paralegal/ Administrative Positions Available" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AdTextNote",type="STRING",desc="note below Ad Text label",value="50 words included in price below, $3/word thereafter" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="Formats",type="STRING",desc="format options",value="Online - www.MeckBar.org (one issue: $80, up to 50 words)|Online with photo - www.MeckBar.org (one issue: $90, up to 50 words)|MCB Newsletter (one issue: $200, up to 50 words)|Online and Newsletter ($260, up to 50 words)" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormatPrices",type="STRING",desc="format option prices",value="80|90|200|260" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ImgUploadNote",type="STRING",desc="note next to image upload button",value="Note about image size goes here." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="WordsIncluded",type="STRING",desc="number of words included in prices",value="50" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ExtraWordPrice",type="STRING",desc="price per word beyond words included",value="3.00" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CommentsLabel",type="CONTENTOBJ",desc="text before comment box",value="Would you like to submit any additional comments with your order?" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="SaleAccount",type="STRING",desc="GL account for revenue",value="5190" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaybyCheckCode",type="STRING",desc="Payment profile code for checks",value="PayByCheck" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaybyCreditCode",type="STRING",desc="Payment profile code for credit cards",value="MCB_CC" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
				local.tmpField = { name="PaymentCodeACH",type="STRING",desc="Payment profile code for bank draft",value="MCB_ACH" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfSubject",type="STRING",desc="Subject line for confirmation emails (staff and user)",value="Classified Ad Confirmation" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfContent",type="CONTENTOBJ",desc="Content at the top of the user confirmation page and email",value="Thank you for submitting your classified ad to the Mecklenburg County Bar." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfTo",type="STRING",desc="comma-delimited email addresses where staff confirmations are sent",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfFrom",type="STRING",desc="staff email address that user confirmation will come from",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			/* ***************** */
			/* set form defaults */
			/* ***************** */
			
			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='frmClassified',
				formNameDisplay='Classified Ad',
				orgEmailTo=variables.strPageFields.StaffConfTo,
				memberEmailFrom=variables.strPageFields.ConfFrom
			));

			variables.siteName = arguments.event.getTrimValue('mc_siteinfo.sitename');
			
			/* ******************* */
			/* Member History Vars */
			/* ******************* */
			variables.useHistoryID = 0;
			variables.uploadedImages = '';
			if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
				variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
			if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
				variables.useHistoryID = int(val(session.useHistoryID));
				
			/* ***************** */
			/* Form Process Flow */
			/* ***************** */
			
			switch (local.formAction) {
				case "processLookup":
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
					
				case "processPayment":
					local.classifiedStruct = processPayment(rc=arguments.event.getCollection());
					switch (local.classifiedStruct.status) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection(),classifiedAdStruct=local.classifiedStruct.classifiedAdStruct);
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				default:
					local.returnHTML = showLookup();
					break;
			}
			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='member type')>
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;
					function assignMemberData(memObj) {
						$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
						mc_continueForm($('###variables.formName#'));
					}
	
					function validateMembershipInfoForm(){
						var arrReq = new Array();
	
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
	
						mc_continueForm($('###variables.formName#'));
						return false;
					}
					function validatePaymentForm() {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
						
						mc_continueForm($('###variables.formName#'));
						return true;
					}
	
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous');			        	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}
					function hideAlert() { 
						$('##divFrmErr').html('').hide(); 
					}
					
					$(document).ready(function() {
						<cfif variables.useMID and NOT local.isSuperUser>					
							var mo = { memberID:#variables.useMID# };
							assignMemberData(mo);					
						<cfelseif local.isSuperUser>
							$('div##div#variables.formName#wrapper').hide();
							$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
						</cfif>
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">				
				<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
					</tr>
					</table>
				</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">
		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.stReturn = "success">
		</cfif>	
		<cfreturn local.stReturn>
	</cffunction>
	
	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structkeyexists(session,"useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>			
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>
		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>
		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>
		
		<cfset local.strFieldSetContactInfo = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.ContactInfoFieldSet, mode="collection", strData=local.strData)>
		<cfset local.contactEmail = "">
		<cfloop collection="#local.strFieldSetContactInfo.strFields#" item="local.thisField">
			<cfif local.strFieldSetContactInfo.strFields[local.thisField] eq "Email">
				<cfset local.contactEmail = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.classifiedAd { margin-bottom:30px; }
				.subTotalField input.subTotal { display:none; }
				.subTotalField .subTotalLabel { margin-top: 5px; }
				.wordCount { border: 0px!important; background: white!important; cursor: auto!important; margin-top:10px;}
				.contactInfo td:nth-child(2), .contactInfo td:first-child  {	padding-top: 8px; }
				.contactInfo td input { height: 30px; }
				span.wordLabel { font-size: 14px; font-weight: normal; line-height: 20px; }
				.wordCount { margin-top: 0px; }
				@media screen and (max-width: 630px){
					div##contact-wrapper .tsAppSectionContentContainer table td input { width:100% }
				}
				@media screen and (min-width: 480px) and  (max-width: 980px) {
					div.imageContainer { padding-left: 195px; }
				}
				@media screen and (max-width: 1280px) and (min-width: 980px) {
					.section-inner div.container div.row-fluid {
						padding-left: 0;
						padding-right: 0;
					}
				}
				@media screen and (max-width: 767px) {
					div.imageContainer { margin-top: 15px!important; }
				}
				.removeLink { text-align:right; padding-right: 25px; padding-bottom: 15px; }
				##summaryTable table td { text-align:center; }
				##summaryTable table tr>td {  padding-bottom: 15px; }
				##summaryTable table tr:first>td {  padding-bottom: 0px; }
				##summaryTable table td.leftAlign, ##summaryTable table tr th.leftAlign { text-align:left; }
				##summaryTable table td.leftAlignText { padding-left:15px;text-align:left;}
				##summaryTable .tsAppSectionContentContainer { margin-bottom:5px; }
				##summaryTable table tr:last-child td { padding-top: 15px; }
				##summaryTable table tr th { padding-bottom: 20px; }
				div##summaryTable td.dollarSubTotal { white-space:nowrap; }
				@media screen and (max-width: 500px){
					div##summaryTable .tsAppSectionContentContainer table{ font-size:10px }
					div##summaryTable .tsAppSectionContentContainer { padding-left:4px; }
					
				}
				
			</style>
			<script language="javascript">
				var sizeMB = 0;
				var totalAmount = 0;
				
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				
				var _URL = window.URL || window.webkitURL;
				function displayPreview(files) {
				    var file = files[0];
				    var img = new Image();
				    sizeMB = (file.size / 1024)/1000 ;
				}
				
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var _URL = window.URL || window.webkitURL;
					#local.strFieldSetContactInfo.jsValidation#
					<cfif len(trim(local.contactEmail))>
						var contactEmail = $('###variables.formName# ###local.contactEmail#').val();
						$('##contactEmail').val(contactEmail);
					</cfif>
					var clonenum = $(".classifiedAd").length;
					$('##boxCount').val(clonenum);
					$("##summaryTable").find("tr:not(:first)").remove();
					var selectCount =  1;
					$('[id^="categoryOptions"] option:selected').each(function() {
						if($(this).val()==''){
							arrReq[arrReq.length] = 'Select a Category for classified Ad '+selectCount;
						}
						selectCount++;
					}); 
					var selectCount =  1;
					$('[id^="adtext"]').each(function() {
						if($(this).val()==''){
							arrReq[arrReq.length] = 'Enter Ad text for classified Ad '+selectCount;
						}
						else {
						var textAd = $(this).val();
						var replacedAdtext = $(this).val().replace(/\,/g, 'ClAdComma');
						}
						$(this).val(replacedAdtext);
						selectCount++;
					}); 
					var selectCount =  1;
					$('[id^="formatOptions"] option:selected').each(function() {
						if($(this).val()==''){
							arrReq[arrReq.length] = 'Select Where would you like to post? for classified Ad '+selectCount;
						}
						selectCount++;
					}); 
					var selectCount =  1;
					$('[id^="monthIssues"] option:selected').each(function() {
						if($(this).val()==''){
							arrReq[arrReq.length] = 'Select How many issues? for classified Ad '+selectCount;
						}
						selectCount++;
					});
					if (sizeMB > 1) arrReq[arrReq.length] = 'Image size should be 1MB or less.';
					$("##summaryTable").find('tr:last').after('<tr><td colspan="5" style="border-top:1px solid black;"></td></tr><br>');
					var totalAmount = 0;
					$('.classifiedAd').each(function() {
						var categorySelected = $(this).find('.categoryOptions option:selected').val();
						var wordCount = $(this).find('.wordCount').val();
						var formatSelected = $(this).find('.formatOptionsText').val();
						var formatOptionsText = formatSelected.replace('ClAdComma',',');
						var monthsSelected = $(this).find('.monthIssues option:selected').val();
						var subTotalAmount = $(this).find('.subTotalAmount').val();
						totalAmount = parseInt(totalAmount,10)+parseInt(subTotalAmount,10);
						$('##summaryTable').find('tr:last').after('<tr><td class="leftAlign">'+categorySelected+'</td><td>'+wordCount+'</td><td class="leftAlignText">'+formatOptionsText+'</td><td>'+monthsSelected+'</td><td>$'+subTotalAmount+'</td></tr><br>');
					});
					$('##totalAmount').val(totalAmount);
					$("##summaryTable").find('tr:last').after('<tr><td colspan="5" style="border-bottom:1px solid black;"></td></tr><br>');
					$('##summaryTable').find('tr:last').after('<tr><td colspan="3"></td><td>Total</td><td class="dollarSubTotal">$'+totalAmount+'</td></tr>');
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					else {
						$('##page1').hide();
						hideAlert();
						$('##summaryTable').show();
					}
				}
				function hideAlert() { $('##divFrmErr').html('').hide(); }	
				
				
				function adtextCount(){
					var _ele = $('.adtext');
					if(_ele.val().length) {
						var words = _ele.val().match(/\S+/g).length;
						_ele.siblings('span').children('input').val(words);
					} else {
						_ele.siblings('span').children('input').val(0);
					}
				}
				
				$(document).on('keyup', '.adtext', function() {
					adtextCount();
				});

				var clonenum = $(".classifiedAd").length;
				
				$(function() {
					$('[id^="formatOptions"]').on('change', function(){
						var replacedFormatText = $(this).find(':selected').text().replace(/\,/g, 'ClAdComma');
						$(this).siblings('.formatOptionsText').val($(this).find(':selected').text(replacedFormatText));
					});
				});
				
				$(document).ready(function() {
					prefillData();
					adtextCount();
					var clonenum = $(".classifiedAd").length;
					$('##summaryTable').hide();
					$('##paymentContainer').hide();
					
					$(document).on("change click",".classifiedAd", function() {
						var wordCount = $(this).find('.wordCount').val();
						$(this).find('.wordCount').val(wordCount);
						var addnlWords = 0;
						var formatPrice = 0;
						var monthIssues = 0;
						$(function() {
							$('.formatOptions').on('change', function(){
								$(this).siblings('.formatOptionsText').val($(this).find(':selected').text());
							});
						});
						if($('##wordsIncluded').val()==''){
							<cfif len(trim(variables.strPageFields.WordsIncluded))>
								$('##wordsIncluded').val(#variables.strPageFields.WordsIncluded#);
							</cfif>
						}
						var wordsIncluded = $('##wordsIncluded').val();
						var  checkAddnlWords = wordCount-wordsIncluded;
						if (checkAddnlWords>0) {
							addnlWords = checkAddnlWords;
						}
						var extraWordPrice = $("##extraWordPrice").val();
						var addnlWordsPrice = addnlWords * extraWordPrice;
						if($(this).find('.formatOptions option:selected').val()) {
							formatPrice =$(this).find('.formatOptions option:selected').val();
						}
						var formatAndAddnl = parseInt(formatPrice, 10)+parseInt(addnlWordsPrice, 10);
						if($(this).find('.monthIssues option:selected').val()) {
							monthIssues =$(this).find('.monthIssues option:selected').val();
						}
						var subTotalAmount = formatAndAddnl * monthIssues;
						var subTotalText = '$'+formatPrice+' (base) + $'+addnlWordsPrice+' (extra words) = $'+formatAndAddnl+' x '+monthIssues+' (issues) = $'+subTotalAmount+'';
						$(this).find('.subTotal').val(subTotalText);
						$(this).find('.subTotalLabel').html(subTotalText);
						$(this).find('.subTotalAmount').val(subTotalAmount);
					});
				});
				var regex = /^(.+?)(\d+)$/i;
				var clonenum = $(".classifiedAd").length;
				var cloneIndex = clonenum;
				if(cloneIndex==1){
					$('.remove').hide();
				}
				$(".addAnotherBtn").on("click", function(){
					cloneIndex++;
					$(this).parents(".mainAdContainer").find('.classifiedAd:last').clone()
						.appendTo(".classifiedAdContainer")
						.attr("id", "classifiedAd_" +  cloneIndex)
						.find("*").each(function() {
							if($(this).hasClass('uploadImage')){
								var name = this.name || "";
								var match = name.match(regex) || [];
								if (match.length == 3) {
									this.name = match[1] + (cloneIndex);
								}
							}
							var id = this.id || "";
							var match = id.match(regex) || [];
							if (match.length == 3) {
								this.id = match[1] + (cloneIndex);
							}
							if($(this).hasClass('adtext') || $(this).hasClass('wordCount') || $(this).hasClass('subTotal') || $(this).hasClass('uploadImage') || $(this).hasClass('formatOptionsText')){
								$(this).val('');
							}
							if($(this).hasClass('subTotalLabel')){
								$(this).html('');
							}
						});
					adtextCount();
					$('.remove').show();
					
					$(function() {
						$('[id^="formatOptions"]').on('change', function(){
							var replacedFormatText = $(this).find(':selected').text().replace(/\,/g, 'ClAdComma');
						   $(this).siblings('.formatOptionsText').val($(this).find(':selected').text(replacedFormatText));
					   });
					});
				});
				
				$(document).on("click",".remove", function() {
					$(this).closest(".classifiedAd").remove();
					cloneIndex--;
					if(cloneIndex==1){
						$('.remove').hide();
					}
				});
				function gotoPreviousPage(){
					$('##page1').show();
					$("##summaryTable").find("tr:not(:first)").remove();
					$('##summaryTable').hide();
				}
				
				function goBackToSummaryTable(){
					$('##summaryTable').show();
					$('##paymentContainer').hide();
				}
				function gotoPaymentPage(){
					$('##summaryTable').hide();
					$('##paymentContainer').show();
					$('##noPaymentReq').hide();
					
					var totalAmount = $('##totalAmount').val();
					if(totalAmount>0){
						$('##paymentBlock').show();
						$('##noPaymentReq').hide();
					}
					else {
						$('##paymentBlock').hide();
						$('##noPaymentReq').show();
					}
				}
					
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" class="form-horizontal" id="#variables.formName#" enctype="multipart/form-data" method="post">
				<input type="hidden" name="fa" id="fa" value="processPayment">
				<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
				<input type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
				<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
				<input type="hidden" name="extraWordPrice" id="extraWordPrice" value="#variables.strPageFields.ExtraWordPrice#">
				<input type="hidden" name="contactEmail" id="contactEmail" value="">
				<input type="hidden" name="boxCount" id="boxCount" value="">
				<input type="hidden" name="wordsIncluded" id="wordsIncluded" value="">
				<input type="hidden" name="totalAmount" id="totalAmount" value="">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0; "></div>
				
				<div id="fullPageContainer">
					<div id="page1">
						<div>
							<div class="tsAppSectionHeading" style="background-color:white;font-size:17px;">#variables.strPageFields.FormTitle#</div>
							<div class="tsAppSectionContentContainer">#variables.strPageFields.FormInstructions#</div>
						</div>
						<div id="contact-wrapper" class="fieldsetFormWrapper contactInfo row-fluid" style="border:1px solid grey;margin-bottom:20px;">
							<div class="tsAppSectionHeading row-fluid">#local.strFieldSetContactInfo.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer row-fluid">
								#local.strFieldSetContactInfo.fieldSetContent#
							</div>
						</div>
						<div class="mainAdContainer row-fluid" id="mainAdContainer">
							<div id="classifiedAdContainer1" class="classifiedAdContainer row-fluid">
								<div id="classifiedAd_1" class="fieldsetFormWrapper classifiedAd row-fluid" style="border:1px solid grey;">
									<div id="classifiedAdContent" class="row-fluid">
										<div class="tsAppSectionHeading row-fluid">Classified Ad</div>
										<div class="row-fluid"><br>
											<div class="row-fluid">
												<div class="row-fluid">
													<div class="row-fluid control-group">
														<div class="span6">
															<label class="control-label" for="categoryOptions">* Category</label>
															<div class="controls">
																<select name="categoryOptions" class="categoryOptions input-xlarge" id="categoryOptions1">
																	<option value="">Please Select</option>
																	<cfloop list="#variables.strPageFields.CategoryOptions#" index="name" delimiters="|">
																		<cfoutput>
																			<option value="#name#">#name#</option>
																		</cfoutput>
																	</cfloop>
																</select>	
															</div>
														</div>	
													</div>
												</div><br/>
												<div class="row-fluid">
													<div class="span6 control-group">
														<label class="control-label" for="adtext">* Ad text <br><br><span>#variables.strPageFields.AdTextNote#</span></label>
														<div class="controls">
															<textarea name="adtext" class="adtext input-xlarge" id="adtext1" rows="6" onkeyup="adtextCount();"></textarea><br/>
															<span class="wordLabel">Word count:<input type="text" class="wordCount" id="wordCount1" name="wordCount" size="2" readonly></span>	
														</div>
													</div>
												</div>
												<div class="row-fluid">
													<div class="control-group row-fluid">
														<div class="span12">
															<label class="control-label" for="categoryOptions">* Where would you like to post?</label>
															<div class="controls">
																<select name="formatOptions" id="formatOptions1" class="formatOptions form-control input-xlarge">
																	<option value="">Please Select</option>
																	<cfset local.formatCount =  1>
																	<cfloop list="#variables.strPageFields.Formats#" index="formats" delimiters="|">
																		<option value="#trim(listGetAt(variables.strPageFields.FormatPrices,local.formatCount,"|"))#">#formats#</option>
																		<cfset local.formatCount =  local.formatCount + 1>
																	</cfloop>
																</select>
																<input type="file" name="uploadImage1" id="uploadImage1" value="Upload Image" onchange="displayPreview(this.files);"><br/>
																<span>#variables.strPageFields.ImgUploadNote#</span>
																<input type="hidden" class="formatOptionsText" name="formatOptionsText" id="formatOptionsText1" value="">
															</div>
														</div>														
													</div>
												</div>
												<div class="row-fluid">
													<div class="span6 control-group">
														<label class="control-label" for="categoryOptions">* How many issues?</label>
														<div class="controls">
															<select name="monthIssues" id="monthIssues1" class="monthIssues form-control input-xlarge">
																<option value="">Please Select</option>
																<cfloop index="monthNum" from="1" to="6">
																	<cfoutput>
																		<option value="#monthNum#">#monthNum#</option>
																	</cfoutput>
																</cfloop>
															</select>
														</div>
													</div>
												</div>
												<div class="row-fluid">
													<div class="span5">&nbsp;</div>
													<div class="span7 control-group">
														<label class="control-label" for="subTotal">SUBTOTAL :</label>
														<div class="controls subTotalField">
															<input type="text" id="subTotal1" class="subTotal" name="subTotal" size='50' readonly>
															<label id="subTotalLabel1" class="subTotalLabel">
														</div>
													</div>
												</div>
												<div class="row-fluid removeLink">
													<a class="remove" name="remove" id="remove1"><i class="fa fa-trash-o remove" aria-hidden="true" style="font-size:15px;"></i>remove</a>
												</div>
												<input type="hidden" name="totalWordCount" id="totalWordCount1" value="">
												<input type="hidden" class="subTotalAmount" name="subTotalAmount" id="subTotalAmount1" value="">
											</div>
										</div>
									</div>
								</div>
							</div>
							<span class="addAnother" style="float:right;"><i class="fa fa-plus-circle addAnotherBtn" aria-hidden="true" style="font-size: 20px;"></i> Add another </span><br>
						</div>
						<div id="commentBox" class="fieldsetFormWrapper" style="border:1px solid grey;margin-top:20px;">
							<div class="tsAppSectionHeading">Comments</div>
							<div class="tsAppSectionContentContainer">
								<div style="padding:10px">
									<div style="padding-bottom:10px">#variables.strPageFields.CommentsLabel#</div>			
									<div><textarea name="commentText" id="commentText" rows="4" style="width:100%"></textarea></div>
								</div>
							</div>	
						</div>
						<button name="btnContinue" id="buttonContinue" type="button" class="tsAppBodyButton  btn btn-default" style="float:right; margin-top:20px;"  onclick="return validateMemberInfoForm();">Continue</button>
					</div>
					
					<!--- showmembershipinfo --->
					<div id="summaryTable">
						<div class="fieldsetFormWrapper" style="border:1px solid grey;margin-top:40px;">
							<div class="tsAppSectionHeading">Order summary</div>
							<div class="tsAppSectionContentContainer">
								<table width='100%' style="border-collapse:collapse;">
									<tbody>
										<tr valign="top">
											<th class="leftAlign">Category</th>
											<th>Words</th>
											<th class="leftAlign" style="padding-left:15px;">Format</th>
											<th>Issues</th>
											<th>Total</th>
										</tr><br>
									</tbody>
								</table>		
							</div>
						</div> 
						<button name="btnBack" id="btnBack" type="button" style="margin-top:20px;" class="tsAppBodyButton btn btn-default" onclick="gotoPreviousPage();">&lt;&lt; Back</button>	
						<button name="btnContinue" type="button" class="tsAppBodyButton btn btn-default" style="float:right;margin-top:20px;" onclick="gotoPaymentPage();">Continue</button>
					</div> 
					<!--- showmembershipinfo --->
					
					<!--- showpayment --->
					<cfset local.arrPayMethods = []>

					<!---<cfset local.arrPayMethods = [ variables.strPageFields.PaybyCreditCode , variables.strPageFields.PaybyCheckCode]>--->
					<cfset local.PaybyCreditCode = trim(variables.strPageFields.PaybyCreditCode)>
					<cfset local.PaybyCheckCode = trim(variables.strPageFields.PaybyCheckCode)>
					<cfset local.PaymentCodeACH = trim(variables.strPageFields.PaymentCodeACH)>

					<cfif len(local.PaybyCreditCode) GT 0>
						<cfset arrayAppend(local.arrPayMethods, local.PaybyCreditCode)>
					</cfif>
					<cfif len(local.PaybyCheckCode) GT 0>
						<cfset arrayAppend(local.arrPayMethods, local.PaybyCheckCode)>
					</cfif>
					<cfif len(local.PaymentCodeACH) GT 0>
						<cfset arrayAppend(local.arrPayMethods, local.PaymentCodeACH)>
					</cfif>
					<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
												arrPayMethods=local.arrPayMethods, 
												siteID=variables.siteID, 
												memberID=variables.useMID,
												title="Choose Your Payment Method", 
												formName=variables.formName, 
												backStep="showMembershipInfo"
											)>																				
					<cfsavecontent variable="local.headcode">
						<cfoutput>#local.strReturn.headcode#</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#local.headcode#">
					
					<cfinclude template="/model/cfformprotect/cffp.cfm">
					<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
					<div class="paymentContainer" id="paymentContainer">
						<script type="text/javascript">
							$(document).ready(function() {
								$('##paymentContainer [name="btnBack"]').attr('onclick','goBackToSummaryTable();');
							});
						</script>
						<div class="paymentBlock" id="paymentBlock">
							#local.strReturn.paymentHTML#
						</div>
						<div id="noPaymentReq">
							<button name="btnBack" type="button"  class="tsAppBodyButton btn btn-default"  onclick="";>&lt;&lt; Back</button>
							<button name="btnContinue" type="submit" style="float:right;" class="tsAppBodyButton btn btn-default">Continue</button>
						</div>
					</div>
					<!--- showpayment --->
					
				</div>
			</form>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfset var local = structNew()>
		
		<!--- upload image --->
		<cfset local.uploadedImages = ''>
		<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.orgcode)>
		<cfset local.imagePath = local.strImportFile.strFolder.folderPath>
		<cfloop from="1" to="#arguments.rc.boxCount#" index="imgCount">
			<cfset uploadImg = 'uploadImage'&imgCount>
			<cfif StructKeyExists(arguments.rc,uploadImg) and (arguments.rc[uploadImg] neq "")>
				<cffile action="UPLOAD" 
						filefield="#uploadImg#" 
						destination="#local.imagePath#" 
						result="local.uploadResult" 
						nameconflict="OVERWRITE"
						accept="image/jpeg,image/jpg,image/pjpeg,image/png" />
				<cfif local.uploadResult.filesize GT 1000000>
					<cffile action="DELETE"	file="#local.uploadResult.serverdirectory#/#local.uploadResult.serverfile#" />
				</cfif>
				<cfif imgCount eq 1>
					<cfset local.uploadedImages = local.uploadResult.serverfile>
				<cfelse>
					<cfset local.uploadedImages = local.uploadedImages & ',' & local.uploadResult.serverfile>
				</cfif>
			<cfelse>
				<cfif imgCount eq 1>
					<cfset local.uploadedImages = 'No image'>
				<cfelse>
					<cfset local.uploadedImages = local.uploadedImages & ',' & 'No image'>
				</cfif>
			</cfif>
		</cfloop>
		<cfset variables.uploadedImages = local.uploadedImages >
		<!--- upload image --->
		
		<cfset local.categoryName = ''>
		<cfif arguments.rc.MCCF_PAYMETH eq variables.strPageFields.PaybyCreditCode OR arguments.rc.MCCF_PAYMETH eq variables.strPageFields.PaymentCodeACH>
			<cfset local.categoryName = 'Paid Online'>
		<cfelseif arguments.rc.MCCF_PAYMETH eq 'PayByCheck'>
			<cfset local.categoryName = 'Not Yet Paid'>
		</cfif>		
		<cfquery name="variables.qryHistory" datasource="#application.dsn.membercentral.dsn#">	
			select cP.categoryID, cP.categoryCode, cp.categoryName, cp.categoryDesc, c.categoryID as subCategoryID, c.categoryName 
			from dbo.cms_categoryTrees as ct 
			inner join dbo.cms_categories as cP on ct.categoryTreeID = cP.categoryTreeID 
			inner join dbo.cms_categories as c on c.parentCategoryID = cP.categoryID  
			and c.isActive = 1 
			where ct.siteID = <cfqueryparam value="#variables.siteID#" cfsqltype="CF_SQL_INTEGER"> 
			and ct.categoryTreeName = 'MemberHistoryTypes' and cP.categoryCode in ('Classified') and cP.isActive = 1
			and c.categoryName = <cfqueryparam value="#local.categoryName#" cfsqltype="CF_SQL_VARCHAR"> 
		</cfquery>
		<cfset local.br = "#chr(13)##chr(10)#">
		<cfset local.qty = 0>
		<cfset local.dollarAmt = 0>
		<cfset local.description = ''>
		<cfset local.boxCount = arguments.rc.boxCount>
		
		<cfif structKeyExists(arguments.rc,"m_firstname")>
			<cfset local.description = local.description & 'First Name : ' & arguments.rc.m_firstname & local.br >
		</cfif>
		<cfif structKeyExists(arguments.rc,"m_lastname")>
			<cfset local.description = local.description & 'Last Name : ' & arguments.rc.m_lastname & local.br >
		</cfif>
		<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.rc.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgEmailTypes">
			<cfif isDefined("arguments.rc.me_#local.qryOrgEmailTypes.emailTypeID#_email")>
				<cfset local.description = local.description & local.qryOrgEmailTypes.emailType & ' : ' & arguments.rc["me_#local.qryOrgEmailTypes.emailTypeID#_email"] & local.br >
			</cfif>
		</cfloop>
		
		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>
		<cfset local.qryOrgPhoneTypes = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.rc.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgAddressTypes">
			<cfset local.tmpAddressTypeID = local.qryOrgAddressTypes.addressTypeID>
			<cfloop query="local.qryOrgPhoneTypes">
				<cfif isDefined("arguments.rc.mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#")>
					<cfset local.tmpVal = left(arguments.rc["mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#"],40)>
					<cfif len(local.tmpVal)>
						<cfset local.description = local.description & local.qryOrgPhoneTypes.phoneType & ' : ' & local.tmpVal & local.br >
					</cfif>
				</cfif>
			</cfloop>
		</cfloop>
		
		<cfset local.arr_classifiedad_properties = ArrayNew(1)>
		<cfif structKeyExists(arguments.rc,"categoryOptions") >
			<cfset local.categoryList = arguments.rc.categoryOptions>
		</cfif>
		<cfif structKeyExists(arguments.rc,"formatoptions") >
			<cfset local.formatList = arguments.rc.formatoptions>
		</cfif>
		<cfif structKeyExists(arguments.rc,"formatOptionsText") >
			<cfset local.formatTextList = arguments.rc.formatOptionsText>
		</cfif>
		<cfif structKeyExists(arguments.rc,"monthissues") >
			<cfset local.issuesList = arguments.rc.monthissues>
		</cfif>
		<cfif structKeyExists(arguments.rc,"adtext") >
			<cfset local.adtextList = arguments.rc.adtext>
		</cfif>
		<cfif structKeyExists(arguments.rc,"subTotalAmount") >
			<cfset local.subList = arguments.rc.subTotalAmount>
		</cfif>
		<cfset local.imgList = variables.uploadedImages>
		<!--- <cfset local.imgList = 'image1,image2,image3,image4'> --->
		<cfset local.response.classifiedAdStruct = StructNew()>
		
		 <cfloop from="1" to="#local.boxCount#" index="local.count">
			<cfif local.count eq 1>
				<cfset local.description = local.description & local.br & '--------------------'& local.br>
			</cfif>
			<cfset local.adTextContent = ListGetAt(local.adtextList,local.count)>
			<cfset local.description = local.description & 'Category : ' & ListGetAt(local.categoryList,local.count) & local.br >
			<cfset local.description = local.description & 'Format : ' & ListGetAt(local.formatList,local.count) & local.br >
			<cfset local.description = local.description & 'Image : ' & ListGetAt(local.imgList,local.count) & local.br >
			<cfset local.description = local.description & 'Issues : ' & ListGetAt(local.issuesList,local.count) & local.br >
			<cfset local.description = local.description & 'Ad Text : ' & replace(local.adTextContent,"ClAdComma",",","all") & local.br >
			<cfset local.description = local.description & '--------------------' & local.br>
			
			<cfset ArrayAppend(local.arr_classifiedad_properties,ListGetAt(local.categoryList,local.count))>
			<cfset ArrayAppend(local.arr_classifiedad_properties,replace(ListGetAt(local.formatTextList,local.count),"ClAdComma",",","all"))>
			<cfset ArrayAppend(local.arr_classifiedad_properties,ListGetAt(local.imgList,local.count))>
			<cfset ArrayAppend(local.arr_classifiedad_properties,ListGetAt(local.issuesList,local.count))>
			<cfset local.qty = local.qty + ListGetAt(local.issuesList,local.count) >
			<cfset ArrayAppend(local.arr_classifiedad_properties,replace(local.adTextContent,"ClAdComma",",","all"))>
			<cfset ArrayAppend(local.arr_classifiedad_properties,ListGetAt(local.subList,local.count))>
		</cfloop>
	
		<cfif structKeyExists(arguments.rc,"commentText")>
			<cfset local.description = local.description & 'Comments : ' & arguments.rc.commentText & local.br >
			 <cfset local.response.classifiedAdStruct.comments = arguments.rc.commentText>
		</cfif>
		<cfif structKeyExists(arguments.rc,"totalAmount")>
			<cfset local.dollarAmt = arguments.rc.totalAmount >
			<cfset local.response.classifiedAdStruct.dollarAmt = arguments.rc.totalAmount>
		</cfif>
		<cfset local.response.classifiedAdStruct.boxCount =	local.boxCount>
		<cfif structKeyExists(arguments.rc,"contactEmail")>
			<cfset local.response.classifiedAdStruct.contactEmail = arguments.rc.contactEmail>
		</cfif>
		<cfset local.response.classifiedAdStruct.imageList = local.imgList>
		<cfset local.response.classifiedAdStruct.imagePath = local.imagePath>
		<!--- Add history --->
		<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistory.categoryID, 
											subCategoryID=variables.qryHistory.subCategoryID, description=local.description, 
											enteredByMemberID=variables.useMID, newAccountsOnly=false,qty=local.qty, dollarAmt=local.dollarAmt)>	
		<cfset session.useHistoryID = variables.useHistoryID>
		<cfset local.response.classifiedAdStruct.arrayAllAds =  local.arr_classifiedad_properties>
		
		
		
		<cfset local.totalAmount = local.dollarAmt >
			
		<!--- This is the accounting part --->
		<cfset local.strAccTemp = {
			totalPaymentAmount=local.totalAmount,
			assignedToMemberID=variables.useMID,
			recordedByMemberID=variables.useMID,
			rc=arguments.rc
		}>

		<cfif structKeyExists(arguments.rc,"MCCF_PAYMETH") and arguments.rc["MCCF_PAYMETH"] eq variables.strPageFields.PaybyCreditCode>
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
				siteid=variables.siteID,
				profileCode=variables.strPageFields.PaybyCreditCode
			)>
			<cfset local.profileCode = variables.strPageFields.PaybyCreditCode>
		<cfelseif structKeyExists(arguments.rc,"MCCF_PAYMETH") and arguments.rc["MCCF_PAYMETH"] eq variables.strPageFields.PaymentCodeACH>
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
				siteid=variables.siteID,
				profileCode=variables.strPageFields.PaymentCodeACH
			)>
			<cfset local.profileCode = variables.strPageFields.PaymentCodeACH>
		<cfelse>
			<cfset local.payProfileID = application.objCustomPageUtils.acct_getProfileID(
				siteid=variables.siteID,
				profileCode=variables.strPageFields.PaybyCheckCode
			)>
			<cfset local.profileCode = variables.strPageFields.PaybyCheckCode>
		</cfif>

		<cfset local.strAccTemp.payment = {
				detail="#variables.formName#",
				amount=local.strAccTemp.totalPaymentAmount,
				profileID=local.payProfileID,
				profileCode=local.profileCode
			}>
	
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
		<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
		
		<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>
			<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=variables.useMID, transactionDate=now())>
		</cfif>

		
		<!--- Record sale --->
		<cfif structKeyExists(arguments.rc,"totalAmount")>
			<cfset local.dollarAmount = LSParseNumber(rereplace(arguments.rc.totalAmount, "[^0-9\.]", "", "all"))>
		<cfelse>
			<cfset local.dollarAmount = 0>
		</cfif>
		<cfset local.AdDetail = 'Classified ad'>
		<cfset local.strACCTemp = { assignedToMemberID	= variables.useMID,
														recordedByMemberID	= variables.useMID,
														amount = local.dollarAmount,
														detail = local.AdDetail,
														transactionDate = now() }>
		<cfset local.strACCTemp.revenueGLAccountCode = variables.strPageFields.SaleAccount>
		
		<!--- Allocate payment to sale --->
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
		<cfset local.strACCSale = local.objAccounting.recordSale(argumentcollection=local.strACCTemp)>

		<cfif val(local.dollarAmount) gt 0 and local.strACCResponse.paymentResponse.mc_transactionID gt 0 and local.strACCSale.transactionID gt 0>
			<cfset local.strACCTemp = { recordedOnSiteID=variables.siteID, 
														recordedByMemberID=variables.useMID, 
														statsSessionID=val(session.cfcUser.statsSessionID), 
														amount=numberFormat(local.dollarAmount,"0.00"),
														transactionDate=now(), 
														paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, 
														saleTransactionID=local.strACCSale.transactionID } />
			<cfset local.strACCAllocate = local.objAccounting.allocateToSale(argumentcollection=local.strACCTemp) />
		</cfif>
		
		
		<cfset local.response.status = "success">
		<cfreturn local.response>
	</cffunction>	
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="classifiedAdStruct" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strFieldSetContactInfo = application.objCustomPageUtils.renderFieldSet(uid=variables.strPageFields.ContactInfoFieldSet, mode="confirmation", strData=arguments.rc)>	
		<cfset local.paymentRequired = arguments.rc.totalAmount >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		<cfset local.imageList = arguments.classifiedAdStruct.imageList>
		<cfsavecontent variable="local.confirmationPageHTML">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfContent)>
					<fieldset>
						<legend>Form submission</legend>
						<div class="tsAppSectionContentContainer">#variables.strPageFields.ConfContent#</div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfContent)>
				<div>#variables.strPageFields.ConfContent#</div>
			</cfif>
			<div class="confirmationDetails">
				<!--@@specialcontent@@-->
				
				<div>Here are the details of your application:</div><br/>
				
				#local.strFieldSetContactInfo.fieldSetContent#
				<cfset local.boxCount = arguments.classifiedAdStruct.boxCount>
				<cfset local.arrIndex = 1>
				<cfloop from="1" to="#local.boxCount#" index="local.count">
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Classified Ad #local.count#</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								Category	:	#arguments.classifiedAdStruct.arrayAllAds[local.arrIndex]#
								<cfset local.arrIndex = local.arrIndex + 1>
							</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								Format	:	#arguments.classifiedAdStruct.arrayAllAds[local.arrIndex]#
								<cfset local.arrIndex = local.arrIndex + 1>
							</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								Image	:	#arguments.classifiedAdStruct.arrayAllAds[local.arrIndex]#
								<cfset local.arrIndex = local.arrIndex + 1>
							</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								Issues	:	#arguments.classifiedAdStruct.arrayAllAds[local.arrIndex]#
								<cfset local.arrIndex = local.arrIndex + 1>
							</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								Ad text	:	#arguments.classifiedAdStruct.arrayAllAds[local.arrIndex]#
								<cfset local.arrIndex = local.arrIndex + 1>
							</td>
						</tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								Amount	:	#arguments.classifiedAdStruct.arrayAllAds[local.arrIndex]#
								<cfset local.arrIndex = local.arrIndex + 1>
							</td>
						</tr>
					</table>
					<br/>
				</cfloop>
				
				<cfif arguments.classifiedAdStruct.comments neq ''>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Comments</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								#arguments.classifiedAdStruct.comments#
							</td>
						</tr>
					</table>
				</cfif>
				
				<cfif local.paymentRequired>
					<br/>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>
		<cfset local.emailTitle = 'Thank you for submitting classified ads'>
		<cfset variables.memberEmail.Subject = variables.strPageFields.ConfSubject >
		<cfset variables.memberEmail.To = arguments.classifiedAdStruct.contactEmail >

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteid,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>


		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber: <b>#local.stOrigMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>

		<cfsavecontent variable="local.emailMessage">
			<cfoutput>
				<table cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="background-color:##eee;padding:10px 20px 20px 20px;">
						<table style="background-color:##eee;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 18px Verdana,Helvetica,Arial,sans-serif;color:##069;padding-left:20px;padding-bottom:8px;">
								#local.emailTitle#
							</td>
						</tr>
						<tr>
							<td style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:20px;">
								#local.confirmationHTMLToStaff#
							</td>
						</tr>
						</table>
					</td>
				</tr>
				</table>
			</cfoutput>		
		</cfsavecontent>

		<cfset local.mailAttach = []>
		<cfloop from="1" to="#local.boxCount#" index="count">
			<cfset local.imageName = ListGetAt(local.imageList,count)>
			<cfif local.imageName neq 'No image'>
				<cfset ArrayAppend( local.mailAttach, { file:local.imageName, folderpath:arguments.classifiedAdStruct.imagePath })>
			</cfif>
		</cfloop>

		<cfscript>
			local.arrEmailTo = [];
			variables.ORGEmail.to = replace(variables.ORGEmail.to,",",";","all");
			local.toEmailArr = listToArray(variables.ORGEmail.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		</cfscript>

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				#local.emailMessage#
			</cfoutput>
		</cfsavecontent>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.ORGEmail.from},
			emailto=local.arrEmailTo,
			emailreplyto=variables.ORGEmail.from,
			emailsubject=variables.strPageFields.ConfSubject,
			emailtitle=variables.siteName & " - " & variables.formNameDisplay,
			emailhtmlcontent=local.mailContent,
			emailAttachments=local.mailAttach,
			siteID=variables.siteID,
			memberID=arguments.rc.mc_siteInfo.sysMemberID,
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<cfreturn local.confirmationHTML>
	</cffunction>
	
	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Submission Complete.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
</cfcomponent>