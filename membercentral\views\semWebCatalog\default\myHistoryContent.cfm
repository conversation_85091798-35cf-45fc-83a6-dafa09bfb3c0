<cfinclude template="/views/semwebCatalog/default/swCatalogcommonCSS.cfm">
<cfsavecontent variable="local.myHistoryJS">
	<cfoutput>
	<script type="text/javascript">
		function viewCert(eId) {
			var certURL = '#attributes.event.getValue('mainurl')#&panel=viewCert&mode=direct&eId=' + eId;
			window.open(certURL,'ViewCertificate','width=670,height=500');
		}
		function viewCPCert(pId) {
			var certURL = '#attributes.event.getValue('mainurl')#&panel=viewCPCert&mode=direct&pId=' + pId + '&did=#attributes.data.depomemberdataid#';
			window.open(certURL,'ViewCertificate','width=670,height=500');
		}
		function showBlockedPopup(pg) { self.location.href=pg + '&nopop=1'; }
			
		function addCredit() {
			$.colorbox( {innerWidth:700, innerHeight:450, href:'#attributes.event.getValue('mainurl')#&panel=addCredit&mode=direct', iframe:true, overlayClose:false} );
		}	
		function confirmDelete(srcid) {
			if (confirm("Are you sure you want to delete this Self-Reported Credit?")) self.location.href = '#attributes.event.getValue('mainurl')#&panel=deleteSelfCLE&ID=' + srcid;
		}
		function onSubmitMyHistoryFilter() {
			$('##btnFilterMyHistory').html('<div><i class="icon-refresh icon-spin"></i> Please Wait...</div>').prop('disabled',true);
			return true;
		}
		function exportMyHistoryPDF(){
			var filtersUrlString = $(".swHistory ##frmFilter :input").filter(function(index, element) { return $(element).val() != '';}).serialize();
			self.location.href = '#attributes.event.getValue('mainurl')#&panel=My&exportPDF=1&mode=stream' + (filtersUrlString.length ? '&' + filtersUrlString : '');
		}
		function closeBox() { $.colorbox.close(); }

		$(function() {
			mca_setupDatePickerRangeFields('fStartDate','fEndDate');
			mca_setupCalendarIcons('frmFilter');
			
			<cfif attributes.data.semWeb.qrySWP.isSWOD AND arguments.event.getTrimValue('_swft','') EQ 'SWOD'>
				$('.swPrimaryHover[data-programtype="SWOD"]').click();
			<cfelseif attributes.data.semWeb.qrySWP.isSWL AND arguments.event.getTrimValue('_swft','') EQ 'SWL'>
				$('.swPrimaryHover[data-programtype="SWL"]').click();
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.myHistoryJS#">

<cfoutput>
<div class="wrapper swCatalog swHistory">
	<cfinclude template="/views/semwebCatalog/default/swPanelHeaderSectionCommon.cfm">
	
	<div>
		<div class="tsAppBodyText sw-mb-4">Access all of your purchased programs and titles, reprint certificates, and review supporting materials discussed in your programs.</div>
		<div class="tsAppBodyText sw-mb-2 sw-d-print-none">
			<form name="frmFilter" id="frmFilter" class="form-inline" method="post" action="#attributes.event.getValue('mainurl')#&panel=My" onsubmit="return onSubmitMyHistoryFilter();">
				<table class="tsAppBodyText">
					<tr>
						<td class="sw-pl-0" colspan="4">
							<small><strong>FILTER BY DATE </strong></small> <i class="bi bi-info-circle-fill" title="Filter looks at start date for live programs and enrollment date for on demand programs."></i>
						</td>
					</tr>
					<tr>
						<td class="sw-pl-0" width="20%">
							<input type="text" name="fStartDate" id="fStartDate" value="#attributes.data.strHistory.fStartDate#" class="dateControl" placeholder="Start Date">
							<div style="text-align:right;"><small><a href="javascript:mca_clearDateRangeField('fStartDate');" class="swMuted">clear</a></small></div>
						</td>
						<td width="20%">
							<input type="text" name="fEndDate" id="fEndDate" value="#attributes.data.strHistory.fEndDate#" class="dateControl" placeholder="End Date">
							<div style="text-align:right;"><small><a href="javascript:mca_clearDateRangeField('fEndDate');" class="swMuted">clear</a></small></div>
						</td>
						<td width="20%" valign="top">
							<button type="submit" name="btnFilterMyHistory" id="btnFilterMyHistory" class="tsAppBodyButton"><i class="bi bi-funnel-fill"></i> Filter</button>
						</td>
						<td width="40%" style="text-align:right;">
							<a href="javascript:exportMyHistoryPDF();" title="Export as PDF" class="swPrimary" style="text-decoration: none;"><i class="bi bi-file-earmark-text" aria-hidden="true"></i> PDF</a>
						</td>
					</tr>
				</table>
			</form>
		</div>

		<div class="sw-mb-4 sw-d-print-none">
			<div class="tsAppBodyText"><small><strong>JUMP TO:</strong></small></div>
			<table>
				<tr>
					<cfif attributes.data.semWeb.qrySWP.isSWL>
						<td>
							<a href="##mySWL" class="swMuted swPrimaryHover" data-programtype="SWL">#attributes.data.semWeb.qrySWP.brandSWLTab#</a>
							<cfif attributes.data.semWeb.qrySWP.isSWOD + attributes.data.strHistory.qryCertPrograms.recordCount + attributes.data.semWeb.qrySWP.isSelfReportCredit GT 0>| </cfif>
						</td>
					</cfif>
					<cfif attributes.data.semWeb.qrySWP.isSWOD>
						<td>
							<a href="##mySWOD" class="swMuted swPrimaryHover" data-programtype="SWOD">#attributes.data.semWeb.qrySWP.brandSWODTab#</a>
							<cfif attributes.data.strHistory.qryCertPrograms.recordCount + attributes.data.semWeb.qrySWP.isSelfReportCredit GT 0>| </cfif>
						</td>
					</cfif>
					<cfif attributes.data.strHistory.qryCertPrograms.recordCount>
						<td>
							<a href="##myCP" class="swMuted swPrimaryHover">Certificate Programs</a>
							<cfif attributes.data.semWeb.qrySWP.isSelfReportCredit>| </cfif>
						</td>
					</cfif>
					<cfif attributes.data.semWeb.qrySWP.isSelfReportCredit>
						<td><a href="##mySRC" class="swMuted swPrimaryHover">Self Reported Credits</a></td>
					</cfif>
				</tr>
			</table>
		</div>

		<div>
			<cfif attributes.data.semWeb.qrySWP.isSWL>
				<div id="mySWL" class="sw-mb-3">
					<h5 class="swPrimary">#attributes.data.semWeb.qrySWP.brandSWLTab#</h5>

					<table class="tsAppBodyText sw-w-100 swProgramEnrollTable">
						<thead>
							<tr>
								<th width="20%" class="swMuted" style="text-align:left;" nowrap>Program Date</th>
								<th width="50%" class="swMuted" style="text-align:left;" nowrap>Program Title / Credits</th>
								<th width="15%" class="swMuted" style="text-align:left;">Status</th>
								<th width="15%" class="swMuted sw-d-print-none" style="text-align:center;">Options</th>
							</tr>
						</thead>
						<tbody>
							<cfif arrayLen(attributes.data.strHistory.arrSWL)>
								<cfloop array="#attributes.data.strHistory.arrSWL#" index="local.thisProgram">
									<tr>
										<td>#local.thisProgram.date#</td>
										<td>
											<cfif local.thisProgram.linkToDetailPage>
												<a href="#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.seminarID#" class="swPrimary tsAppBodyText">#local.thisProgram.title#</a>
											<cfelse>
												#local.thisProgram.title#
											</cfif>
											<cfif len(local.thisProgram.creditDetails)>
												<div><small>#local.thisProgram.creditDetails#</small></div>
											</cfif>
										</td>
										<td nowrap>
											<cfif len(local.thisProgram.enterProgramLink)>
												<a href="#local.thisProgram.SWLPlayerLink#" class="swPrimary" target="_blank">Enter Program</a>
											<cfelse>
												#local.thisProgram.status#
											</cfif>
											<cfif len(local.thisProgram.completedDate)><br/>#local.thisProgram.completedDate#</cfif>
										</td>
										<td style="text-align:center;" class="sw-d-print-none" nowrap>
											<cfif local.thisProgram.canViewCertificate>
												<a href="##" onclick="viewCert('#local.thisProgram.encryptedEID#');return false;" class="swPrimary tsAppBodyText"><i class="bi bi-award tooltip-icon" aria-hidden="true" title="View Certificate"></i></a>
											<cfelse>
												<i class="bi bi-award swMuted" aria-hidden="true" title="View Certificate"></i>
											</cfif>
											<cfif local.thisProgram.canViewReplayLink AND len(local.thisProgram.replayVideoLink)>
												<a href="#local.thisProgram.SWLPlayerLink#" target="_blank" class="swPrimary tsAppBodyText"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Replay Video"></i></a>
											<cfelseif attributes.data.semWeb.qrySWP.offerSWLReplays is 1>
												<i class="bi bi-arrow-counterclockwise swMuted" aria-hidden="true" title="Replay Video"></i>
											</cfif>
											<cfif local.thisProgram.materialsDoc gt 0>
												<a href="#local.thisProgram.SWLPlayerLink#" class="swPrimary tsAppBodyText" target="_blank"><i class="bi bi-download tooltip-icon" aria-hidden="true" title="Download Materials"></i></a>
											<cfelse>
												<i class="bi bi-download swMuted" aria-hidden="true" title="Download Materials"></i>
											</cfif>
										</td>
									</tr>
								</cfloop>
							<cfelse>
								<tr>
									<td colspan="4" style="text-align:center;">No Registrations Found.</td>
								</tr>
							</cfif>
						</tbody>
					</table>
					
				</div>
			</cfif>

			<cfif attributes.data.semWeb.qrySWP.isSWOD>
				<div id="mySWOD" class="sw-mb-3">
					<h5 class="swPrimary">#attributes.data.semWeb.qrySWP.brandSWODTab#</h5>
					
					<table class="tsAppBodyText sw-w-100 swProgramEnrollTable">
						<thead>
							<tr>
								<th width="20%" class="swMuted" style="text-align:left;" nowrap>Enrolled Date</th>
								<th width="50%" class="swMuted" style="text-align:left;" nowrap>Program Title / Credits</th>
								<th width="15%" class="swMuted" style="text-align:left;">Status</th>
								<th width="15%" class="swMuted sw-d-print-none" style="text-align:center;">Options</th>
							</tr>
						</thead>
						<tbody>
							<cfif arrayLen(attributes.data.strHistory.arrSWOD)>
								<cfloop array="#attributes.data.strHistory.arrSWOD#" index="local.thisProgram">
									<tr>
										<td>#local.thisProgram.dateEnrolled#</td>
										<td>
											<span<cfif len(local.thisProgram.creditCompleteByDate)> class="sw-mr-2"</cfif>>
												<cfif local.thisProgram.linkToDetailPage>
													<a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.seminarID#" class="swPrimary tsAppBodyText">#local.thisProgram.title#</a>
												<cfelse>
													#local.thisProgram.title#
												</cfif>
											</span>
											<cfif len(local.thisProgram.creditCompleteByDate)><span class="sw-d-inline-block" style="color:##c09853;">Must be completed by #local.thisProgram.creditCompleteByDate# #local.thisProgram.creditCompleteByDateTime# CT to earn credit.</span></cfif>
											<cfif len(local.thisProgram.creditDetails)>
												<div><small>#local.thisProgram.creditDetails#</small></div>
											</cfif>
										</td>
										<td nowrap>
											<cfif len(local.thisProgram.enterProgramLink)>
												<a href="#local.thisProgram.enterProgramLink#" class="swPrimary tsAppBodyText" target="_blank">Enter Program</a>
											<cfelseif len(local.thisProgram.status)>
												#local.thisProgram.status#
											</cfif>
											<cfif len(local.thisProgram.completedDate)><br/>#local.thisProgram.completedDate#</cfif>
										</td>
										<td style="text-align:center;" class="sw-d-print-none" nowrap>
											<cfif local.thisProgram.canViewCertificate>
												<a href="##" onclick="viewCert('#local.thisProgram.encryptedEID#');return false;" class="swPrimary tsAppBodyText"><i class="bi bi-award tooltip-icon" aria-hidden="true" title="View Certificate"></i></a>
											<cfelse>
												<i class="bi bi-award swMuted" aria-hidden="true" title="Certificate Not Available"></i>
											</cfif>
											<cfif len(local.thisProgram.reviewProgramLink)>
												<a href="#local.thisProgram.reviewProgramLink#" class="swPrimary tsAppBodyText" target="_blank"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Review Program"></i></a>
											<cfelse>
												<i class="bi bi-arrow-counterclockwise swMuted" aria-hidden="true" title="Review Program"></i>
											</cfif>
										</td>
									</tr>
								</cfloop>
							<cfelse>
								<tr>
									<td colspan="4" style="text-align:center;">No Registrations Found.</td>
								</tr>
							</cfif>
						</tbody>
					</table>

				</div>
			</cfif>

			<cfif attributes.data.strHistory.qryCertPrograms.recordCount>
				<div id="myCP" class="sw-mb-3">
					<h5 class="swPrimary">Certificate Programs</h5>

					<table class="tsAppBodyText sw-w-100 swProgramEnrollTable">
						<thead>
							<tr>
								<th width="50%" class="swMuted" style="text-align:left;" nowrap>Program Name</th>
								<th width="20%" class="swMuted" style="text-align:left;" nowrap>Enrolled Date</th>
								<th width="15%" class="swMuted" style="text-align:left;">Status</th>
								<th width="15%" class="swMuted sw-d-print-none" style="text-align:center;">Options</th>
							</tr>
						</thead>
						<tbody>
							<cfif arrayLen(attributes.data.strHistory.arrCertPrograms)>
								<cfloop array="#attributes.data.strHistory.arrCertPrograms#" index="local.thisProgram">
									<tr>
										<td>#local.thisProgram.programName#</td>
										<td></td>
										<td nowrap><small>#local.thisProgram.status#</small></td>
										<td style="text-align:center;" class="sw-d-print-none" nowrap>
											<cfif local.thisProgram.canViewCertificate>
												<a href="##" onclick="viewCPCert('#local.thisProgram.programID#');return false;" class="swPrimary tsAppBodyText"><i class="bi bi-award tooltip-icon" aria-hidden="true" title="View Certificate"></i></a>
											<cfelse>
												<i class="bi bi-award swMuted" aria-hidden="true" title="Certificate Not Earned"></i>
											</cfif>
										</td>
									</tr>
									<cfloop array="#local.thisProgram.arrEnrollments#" index="local.thisEnrollment">
										<tr>
											<td>
												<cfif len(local.thisEnrollment.dateCompleted)>
													<i class="icon-check"></i>
												<cfelse>
													<i class="icon-check-empty"></i>
												</cfif>
												#encodeForHTML(local.thisEnrollment.seminarname)#
											</td>
											<td>#local.thisEnrollment.dateEnrolled#</td>
											<td>
												<small>#local.thisEnrollment.status#</small>
												<cfif len(local.thisEnrollment.seminarLink)>
													<a href="#local.thisEnrollment.seminarLink#" class="swPrimary tsAppBodyText" target="_blank">#local.thisEnrollment.seminarLinkText#</a>
												</cfif>
											</td>
											<td style="text-align:center;" class="sw-d-print-none" nowrap>
												<cfif len(local.thisEnrollment.swodReviewLink)>
													<a href="#local.thisEnrollment.swodReviewLink#" class="swPrimary tsAppBodyText" target="_blank"><i class="bi bi-arrow-counterclockwise tooltip-icon" aria-hidden="true" title="Review Program"></i></a>
												</cfif>
											</td>
										</tr>
									</cfloop>
								</cfloop>
							<cfelse>
								<tr><td colspan="4" style="text-align:center;">You have not enrolled in or completed any certificate programs.</td></tr>
							</cfif>
						</tbody>
					</table>
					
				</div>
			</cfif>

			<cfif attributes.data.semWeb.qrySWP.isSelfReportCredit>
				<div id="mySRC" class="sw-mb-3">
					<h5 class="swPrimary">Self Reported Credits</h5>

					<table class="tsAppBodyText sw-w-100 swProgramEnrollTable">
						<thead>
							<tr>
								<th width="20%" class="swMuted" style="text-align:left;" nowrap>Event Date</th>
								<th width="50%" class="swMuted" style="text-align:left;" nowrap>Event Name / Course Identifier</th>
								<th width="15%" class="swMuted" style="text-align:left;">Credits Earned</th>
								<th width="15%" class="swMuted sw-d-print-none" style="text-align:center;">Options</th>
							</tr>
						</thead>
						<tbody>
							<cfif arrayLen(attributes.data.strHistory.strSelfReportedCredits.arrSelfReportedCredits)>
								<cfloop array="#attributes.data.strHistory.strSelfReportedCredits.arrSelfReportedCredits#" index="local.thisCredit">
									<tr>
										<td>#local.thisCredit.eventDate#</td>
										<td>#local.thisCredit.eventName#<cfif len(local.thisCredit.eventName) and len(local.thisCredit.courseNumber)> / </cfif>#local.thisCredit.courseNumber#</td>
										<td nowrap>
											<cfloop array="#local.thisCredit.arrCreditTypes#" index="local.thisCreditType">
												<small>#local.thisCreditType.value# #local.thisCreditType.displayname#</small>
												<br/>
											</cfloop>
										</td>
										<td style="text-align:center;" class="sw-d-print-none" nowrap>
											<a href="##" onclick="confirmDelete(#local.thisCredit.selfID#);return false;" class="tsAppBodyText"><i class="bi bi-trash-fill sw-text-danger" aria-hidden="true" title="Delete Self Reported Credit"></i></a>
										</td>
									</tr>
								</cfloop>
								<tr><td colspan="4"></td></tr>
								<tr>
									<td colspan="2">Self-Reported Credit Totals:</td>
									<td colspan="2">
										<cfloop collection="#attributes.data.strHistory.strSelfReportedCredits.strCredits#" item="local.fieldname">
											<p>
												#NumberFormat(attributes.data.strHistory.strSelfReportedCredits.strCredits[local.fieldname].value,"0.00")#
												#attributes.data.strHistory.strSelfReportedCredits.strCredits[local.fieldname].displayname#
											</p>
										</cfloop>
									</td>
								</tr>
							<cfelse>
								<tr><td colspan="4" style="text-align:center;">No Self Reported Credits Found.</td></tr>
							</cfif>
						</tbody>
					</table>
					<div class="tsAppBodyText sw-d-print-none">
						<button type="button" name="btnAdd" class="tsAppBodyButton" onclick="addCredit();">Add Self-Reported Credit</button>
					</div>
				</div>
			</cfif>
		</div>
	</div>
</div>
</cfoutput>