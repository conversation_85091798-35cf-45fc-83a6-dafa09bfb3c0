<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="MCBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCheck", type="STRING", desc="pay profile code for Check", value="PaybyCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="MCBA records indicate you are currently an MCBA member. Please <a href='/?pg=login'>click here</a> to log in." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Error message when billed subscription found", value="You need to renew your MCBA membership. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you. Your Membership application has been submitted. You will receive an email from Maricopa County Bar Association with the information you provided." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMembershipInfo", type="CONTENTOBJ", desc="Content above Membership Information", value="Fields marked with a * are required." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="subscriptionUID",type="STRING",desc="UID of the Root Subscription that this form offers",value="4e68955b-9c49-4a74-854e-cb83e59dc7d4" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="defaultAddonSubscriptionUIDs",type="STRING",desc="List of optional Addon Subscription UIDs that should be added by default",value="0f2b45cf-b7cc-47fa-8f94-b52954c62b2f" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="memberInformationIntroText", type="CONTENTOBJ", desc="Information text for content block for memberships", value="Some optional info goes in here... " }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Membership Application',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));

		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="frmJoin");

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					case "spam":
						local.returnHTML = showError(errorCode='spam');
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
		<cfif structKeyExists(session, "captchaEntered")>
			<cfset structDelete(session, "captchaEntered")>
		</cfif>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);	
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function validatePaymentForm(paymentrequired) {
					if(paymentrequired){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
					if (location.hash.length > 0) {        
						step = parseInt(location.hash.replace('##',''),10);     
						if (prevStep > step){
							if(step==1)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
								$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
							mc_loadDataForForm($('###variables.formName#'),'previous');			        	
						}
					} else {
						step = 1;
					}
					prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.annualMembershipDuesUID = "578e0b56-52f9-4bf7-8ac5-483b30e52582">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.annualMembershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.annualMembershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.annualMembershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>	
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='1dc0acf1-ac90-4580-aa82-591188340ca1', mode="collection", strData=local.strData)>
		<!--- Professional Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='3adc1e0b-0271-430e-bd86-06818bdea225', mode="collection", strData=local.strData)>
		<!--- Law School Information --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='8decd605-2c4d-41b4-aec4-f0744fb68eca', mode="collection", strData=local.strData)>
		<!--- Law Student Information --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='becb4220-6cb2-4c66-9132-807a1ff8beb0', mode="collection", strData=local.strData)>
		<!--- Preferred Address Information --->		
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='4cddb7d0-ff39-4d8c-abe6-4014d959be50', mode="collection", strData=local.strData)>
		<!--- Personal Address Information --->		
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='740e4edd-78a1-426a-a6a9-25e1e2311217', mode="collection", strData=local.strData)>		
		<!--- Address Preferences --->		
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='be9c1b9c-55a0-423c-afbf-1e9378721553', mode="collection", strData=local.strData)>
		<!--- Private Attorney Information --->		
		<cfset local.strFieldSetContent8 = application.objCustomPageUtils.renderFieldSet(uid='a9a29661-f27e-40fd-bc55-8387890741ee', mode="collection", strData=local.strData)>
		
		<!--- get Member Type (Contact Type)--->
		<cfset local.memberTypeNum = "">
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Member Type">
				<cfset local.memberTypeNum = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<!--- get Professional Information --->
		<cfset local.dateAdmittedDtAR = "">
		<cfset local.stateBarIdAR = "">
		<cfset local.stateBarStatusAR = "">
		<cfset local.dateAdmittedDtOther = "">
		<cfset local.stateBarIdOther = "">
		<cfset local.stateBarStatusOther = "">		
		<cfloop collection="#local.strFieldSetContent2.strFields#" item="local.thisField">
			<cfswitch expression="#local.strFieldSetContent2.strFields[local.thisField]#">
				<cfcase value="Date Admitted to Arizona Bar">
					<cfset local.dateAdmittedDtAR = local.thisField>
				</cfcase>
				<cfcase value="State Bar ID ##">
					<cfset local.stateBarIdAR = local.thisField>
				</cfcase>
				<cfcase value="Arizona Status">
					<cfset local.stateBarStatusAR = local.thisField>
				</cfcase>
				<cfcase value="Other State Active Date">
					<cfset local.dateAdmittedDtOther = local.thisField>
				</cfcase>
				<cfcase value="Other State License Number">
					<cfset local.stateBarIdOther = local.thisField>
				</cfcase>
				<cfcase value="Other State Status">
					<cfset local.stateBarStatusOther = local.thisField>
				</cfcase>
			</cfswitch>
		</cfloop>
		
		<!--- get Preferred Address Information --->
		<cfloop collection="#local.strFieldSetContent5.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent5.strFields[local.thisField] eq "Address 1">
				<cfset local.memberPreferredAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<!--- get Personal Address Information --->
		<cfloop collection="#local.strFieldSetContent6.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent6.strFields[local.thisField] eq "Address 1">
				<cfset local.memberPersonalAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	
		
		<!--- get  Address Preferences --->		
		<cfset local.memberPrefBillAddress = "">
		<cfset local.memberPrefMailAddress = "">	
		<cfloop collection="#local.strFieldSetContent7.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent7.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>
			<cfif local.strFieldSetContent7.strFields[local.thisField] eq "Use this address for Mailing">
				<cfset local.memberPrefMailAddress = local.thisField>
			</cfif>				
		</cfloop>					

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}

				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#
					
					if ($("##professional-information-wrapper").is(':visible')) {
						#local.strFieldSetContent2.jsValidation#

						<cfif len(trim(local.dateAdmittedDtAR)) and len(trim(local.stateBarIdAR)) and len(trim(local.stateBarStatusAR))>
							if( ($.trim($('###local.dateAdmittedDtAR#').val()).length > 0 && ($.trim($('###local.stateBarIdAR#').val()).length == 0 || $.trim($('###local.stateBarStatusAR#').val()).length == 0)) || 
								($.trim($('###local.stateBarIdAR#').val()).length > 0 && ($.trim($('###local.dateAdmittedDtAR#').val()).length == 0 || $.trim($('###local.stateBarStatusAR#').val()).length == 0)) ||	
								($.trim($('###local.stateBarStatusAR#').val()).length > 0 && ($.trim($('###local.dateAdmittedDtAR#').val()).length == 0 || $.trim($('###local.stateBarIdAR#').val()).length == 0)) )
								arrReq[arrReq.length] = " Date Admitted to Arizona Bar, State Bar ID ## and Arizona Status are required.";														
						</cfif>

						<cfif len(trim(local.dateAdmittedDtOther)) and len(trim(local.stateBarIdOther)) and len(trim(local.stateBarStatusOther))>
							if( ($.trim($('###local.dateAdmittedDtOther#').val()).length > 0 && ($.trim($('###local.stateBarIdOther#').val()).length == 0 || $.trim($('###local.stateBarStatusOther#').val()).length == 0)) ||
								($.trim($('###local.stateBarIdOther#').val()).length > 0 && ($.trim($('###local.dateAdmittedDtOther#').val()).length == 0 || $.trim($('###local.stateBarStatusOther#').val()).length == 0)) ||
								($.trim($('###local.stateBarStatusOther#').val()).length > 0 && ($.trim($('###local.dateAdmittedDtOther#').val()).length == 0 || $.trim($('###local.stateBarIdOther#').val()).length == 0)) )
								arrReq[arrReq.length] = " Other State Active Date, 	Other State License Number and Other State Status are required.";
						</cfif>
					}	
					
					if ($("##law-school-information-wrapper").is(':visible')) {
						#local.strFieldSetContent3.jsValidation#
					}
					
					if ($("##law-student-information-wrapper").is(':visible')) {
						#local.strFieldSetContent4.jsValidation#
					}														
					
					if ($("##private-attorney-information-wrapper").is(':visible')) {
						#local.strFieldSetContent8.jsValidation#
					}
					
					#local.strFieldSetContent5.jsValidation#
					#local.strFieldSetContent6.jsValidation#
					#local.strFieldSetContent7.jsValidation#

					var memberPrefBillAddress = "";
					<cfif len(trim(local.memberPrefBillAddress))>
						memberPrefBillAddress = $('###variables.formName# ###local.memberPrefBillAddress# option:selected').text();
					</cfif>	
					
					var memberPrefMailAddress = "";
					<cfif len(trim(local.memberPrefMailAddress))>
						memberPrefMailAddress = $('###variables.formName# ###local.memberPrefMailAddress# option:selected').text();
					</cfif>						

					<cfif isDefined("local.memberPreferredAddress1")>						
						if ( (memberPrefMailAddress == "Preferred Address" || memberPrefBillAddress == "Preferred Address") && $.trim($('###variables.formName# ###local.memberPreferredAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Preferred Address is required.";
					</cfif>

					<cfif isDefined("local.memberPersonalAddress1")>						
						if ( (memberPrefMailAddress == "Personal Address" || memberPrefBillAddress == "Personal Address") && $.trim($('###variables.formName# ###local.memberPersonalAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Personal Address is required.";
					</cfif>					

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>

					$("##professional-information-wrapper").hide();
					$("##law-school-information-wrapper").hide();								
					$("##law-student-information-wrapper").hide();								
					$("##private-attorney-information-wrapper").hide();								

					<cfif len(trim(local.memberTypeNum))>					
						var mtSel = $('###local.memberTypeNum#').val();
						if(mtSel != "" && mtSel != null){		
							var str = "";						
					        $("###local.memberTypeNum# option:selected").each(function() {
					          	str = $(this).text();	
								if (str == 'Attorney - Private') {
									$("##private-attorney-information-wrapper").show();
									$("##professional-information-wrapper").show();
									$("##law-school-information-wrapper").show();
								}
								if (str == 'Attorney - Public') {
									$("##professional-information-wrapper").show();
									$("##law-school-information-wrapper").show();
								}
								if (str == 'Law Student') {
									$("##law-school-information-wrapper").show();							
									$("##law-student-information-wrapper").show();
								}
							});									
						}					
					
						$('###variables.formName# ###local.memberTypeNum#').change(function(){		
							$("##professional-information-wrapper").hide();
							$("##law-school-information-wrapper").hide();								
							$("##law-student-information-wrapper").hide();											
							$("##private-attorney-information-wrapper").hide();											
							var mtSel = $('###local.memberTypeNum#').val();
							if(mtSel != "" && mtSel != null){		
								var str = "";						
						        $("###local.memberTypeNum# option:selected").each(function() {
						          	str = $(this).text();						
									if (str == 'Law Student') {
										$("##law-school-information-wrapper").show();							
										$("##law-student-information-wrapper").show();
									}
									if (str == 'Attorney - Private') {
										$("##private-attorney-information-wrapper").show();
										$("##professional-information-wrapper").show();
										$("##law-school-information-wrapper").show();
									}
									if (str == 'Attorney - Public') {
										$("##professional-information-wrapper").show();
										$("##law-school-information-wrapper").show();
									}
								});									
							}
						});
					</cfif>				

				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<cfif len(variables.strPageFields.contentMembershipInfo)>
				<div>#variables.strPageFields.contentMembershipInfo#</div>
				<br/>
			</cfif>			

			<div id="member-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent1.fieldSetContent#				
				</div>
			</div>

			<div id="professional-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent2.fieldSetContent#
				</div>
			</div>
			
			<div id="private-attorney-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent8.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent8.fieldSetContent#
				</div>
			</div>

			<div id="law-school-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent3.fieldSetContent#
				</div>
			</div>

			<div id="law-student-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent4.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent4.fieldSetContent#
				</div>
			</div>

			<div id="preferred-address-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent5.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent5.fieldSetContent#
				</div>
			</div>

			<div id="personal-address-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent6.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent6.fieldSetContent#
				</div>
			</div>

			<div id="address-preferences-information-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContent7.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContent7.fieldSetContent#
				</div>
			</div>

			<div class="row-fluid">
				<div class="span12">							
					#variables.captchaDetails.htmlContent#
				</div>
			</div>

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Setting Captcha submitted flag --->
			<cfset session.captchaEntered = 1>	
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		 
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>		
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.defaultAddonSubscriptionUIDs#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				#local.result.jsAddonValidation#
				function validateMembershipInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.result.jsValidation#			
					if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
						arrReq[arrReq.length] = " Select Membership.";
					}	

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				function selectAllSubscriptionsIfRequired() {
					var addonData = $(this).data();
					// select all addons if minimum required by set is gte available count
					// hide checkboxes so they can not be unselected
					if (addonData.minallowed >= $('.subCheckbox',this).length) {
						$('.subCheckbox:not(:checked)',this).attr('checked', true).hide();
						$('.subCheckbox',this).hide();
					}
				}

				$(document).ready(function() {
					$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#"> 		

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<cfif len(variables.strPageFields.memberInformationIntroText)>
						<div>					
							<div class="well subAddonWrapper" id="memberInformation">
								<legend>Membership Information</legend>								
								<div id="memberInformationIntroText">#variables.strPageFields.memberInformationIntroText#</div><br/>								
							</div>	
						</div>
						</cfif>	
						<br/>
						#local.result.formcontent#
						<div class="addonMessageArea"></div>	
						<br/>						
					</div>	<!--- /div class="span12" --->
				</div>	<!--- /div class="row-fluid" --->
			</div> <!--- /div class="container-fluid" --->

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		
		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC, variables.strPageFields.PaymentProfileCodeCheck ]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
				<br/>
			</div>
			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>

		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=variables.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>

		<!--- create subscriptions --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.subscriptionUID)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = rc)>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,orgID=variables.orgID, siteID=variables.siteID)>
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq lTrim(rTrim(variables.strPageFields.PaymentProfileCodeCC))>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq lTrim(rTrim(variables.strPageFields.PaymentProfileCodeCC))>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>		

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>	

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='1dc0acf1-ac90-4580-aa82-591188340ca1', mode="confirmation", strData=arguments.rc)>
		<!--- Professional Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='3adc1e0b-0271-430e-bd86-06818bdea225', mode="confirmation", strData=arguments.rc)>
		<!--- Law School Information --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='8decd605-2c4d-41b4-aec4-f0744fb68eca', mode="confirmation", strData=arguments.rc)>
		<!--- Law Student Information --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='becb4220-6cb2-4c66-9132-807a1ff8beb0', mode="confirmation", strData=arguments.rc)>
		<!--- Preferred Address Information --->		
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='4cddb7d0-ff39-4d8c-abe6-4014d959be50', mode="confirmation", strData=arguments.rc)>
		<!--- Personal Address Information --->		
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='740e4edd-78a1-426a-a6a9-25e1e2311217', mode="confirmation", strData=arguments.rc)>		
		<!--- Address Preferences --->		
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='be9c1b9c-55a0-423c-afbf-1e9378721553', mode="confirmation", strData=arguments.rc)>
		<!--- Private Attorney Information --->		
		<cfset local.strFieldSetContent8 = application.objCustomPageUtils.renderFieldSet(uid='a9a29661-f27e-40fd-bc55-8387890741ee', mode="confirmation", strData=arguments.rc)>

		<cfset local.getMemberTypeFieldData = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Contact Type", columnValueStringList="Attorney,Attorney - Private,Attorney - Public,Law Student")>		
		
		
		<cfset local.isAttorney = false>
		<cfset local.isPrivateAttorney = false>
		<cfset local.isPublicAttorney = false>
		<cfset local.isLawStudent = false>
		
		<cfif not structIsEmpty(local.getMemberTypeFieldData)>
			<cfif structKeyExists(arguments.rc, "md_#local.getMemberTypeFieldData.columnID#")>
				<cfset local.memberTypeIDList = arguments.rc["md_#local.getMemberTypeFieldData.columnID#"]>
				<cfif arrayLen(local.getMemberTypeFieldData.columnValueArr)>
					<cfloop array="#local.getMemberTypeFieldData.columnValueArr#" index="local.thisItem">
						<cfif local.thisItem.columnValueString eq "Attorney" and listFindNoCase(local.memberTypeIDList, local.thisItem.valueID)>
							<cfset local.isAttorney = true>
						</cfif>
						<cfif local.thisItem.columnValueString eq "Attorney - Private" and listFindNoCase(local.memberTypeIDList, local.thisItem.valueID)>
							<cfset local.isPrivateAttorney = true>
						</cfif>
						<cfif local.thisItem.columnValueString eq "Attorney - Public" and listFindNoCase(local.memberTypeIDList, local.thisItem.valueID)>
							<cfset local.isPublicAttorney = true>
						</cfif>
						<cfif local.thisItem.columnValueString eq "Law Student" and listFindNoCase(local.memberTypeIDList, local.thisItem.valueID)>
							<cfset local.isLawStudent = true>
						</cfif>						
					</cfloop>
				</cfif>
			</cfif>
		</cfif>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationMessage)>
				<div>#variables.strPageFields.ConfirmationMessage#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div>Here are the details of your application:</div><br/>

			#local.strFieldSetContent1.fieldSetContent#	
			<cfif local.isPrivateAttorney or local.isPublicAttorney>
				#local.strFieldSetContent2.fieldSetContent#
			</cfif>
			<cfif local.isPrivateAttorney>
				#local.strFieldSetContent8.fieldSetContent#
			</cfif>
			<cfif local.isPrivateAttorney or local.isPublicAttorney or local.isLawStudent>
				#local.strFieldSetContent3.fieldSetContent#
			</cfif>								
			<cfif local.isLawStudent>
				#local.strFieldSetContent4.fieldSetContent#
			</cfif>	
			#local.strFieldSetContent5.fieldSetContent#
			#local.strFieldSetContent6.fieldSetContent#
			#local.strFieldSetContent7.fieldSetContent#		

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join MCBA - Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<div>#local.strResult.formContent#</div>
					<br/>
					<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
					<br/>					
				</td>
			</tr>
			</table>				

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>					
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteid,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to Maricopa County Bar Association", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.errorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script language="javascript">
						window.location = "/?pg=renew";
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>