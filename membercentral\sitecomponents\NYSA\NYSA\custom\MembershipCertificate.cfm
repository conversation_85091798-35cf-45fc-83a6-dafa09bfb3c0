<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
<cfscript>
	variables.applicationReservedURLParams 	= "";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	event.paramValue('membernumber','');
	event.paramValue('downloadTxt',0);
   
	local.membernumber = event.getValue('membernumber','');
	local.downloadTxt = event.getValue('downloadTxt');
	if(local.membernumber eq '') {
		local.membernumber = session.cfcUser.memberData.memberNumber;
    }
    if(len(event.getValue('mk',''))){
        try{
            local.strMemberKey = deserializeJSON(decrypt(event.getValue('mk'),"M@!6T$", "CFMX_COMPAT", "Hex"));
            local.membernumber = toString(local.strMemberKey.m);
        } catch(e){
            local.membernumber = '';
        }
    }
    local.qryMember = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=event.getValue('mc_siteinfo.orgID'), memberNumber=local.membernumber);
    local.useMemberID = local.qryMember.memberID;
    local.extendedName = local.qryMember.firstName & ' ' & local.qryMember.middleName & ' ' & local.qryMember.lastName & ' ' & local.qryMember.suffix;
    if(local.membernumber EQ  session.cfcUser.memberData.memberNumber)
        local.extendedName = session.cfcUser.memberData.firstName & ' ' & session.cfcUser.memberData.middleName & ' ' & session.cfcUser.memberData.lastName & ' ' & session.cfcUser.memberData.suffix;
    local.admissiondate = '';

    local.arrCustomFields = [];
    local.tmpField = { name="PresidentName",type="STRING",desc="Name of  the President",value="LAMBROS LAMBROU, ESQ." };
    arrayAppend(local.arrCustomFields, local.tmpField);
    local.tmpField = { name="PresidentSignature",type="CONTENTOBJ",desc="Signature image of President",value="President Signature." }; 
    arrayAppend(local.arrCustomFields, local.tmpField);
    local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
        arrayAppend(local.arrCustomFields, local.tmpField);
    local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmation to",value="<EMAIL>" }; 
        arrayAppend(local.arrCustomFields, local.tmpField);
    variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

    StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
        formName='frmMemberCert',
        formNameDisplay='Membership Certificate',
        orgEmailTo=variables.strPageFields.StaffConfirmationTo,
        memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
    ));
</cfscript>

<cfif len(local.useMemberID)>
    <cfquery name="local.qryGetJoinDateValue" datasource="#application.dsn.membercentral.dsn#">
        set nocount on
        declare @orgID int, @memberID int, @joinDate dateTime;
        set @orgID = #event.getValue('mc_siteinfo.orgID')#
        set @memberID = #local.useMemberID#
        select @joinDate = [column].columnValueDate
        from dbo.ams_memberData as md
        inner join dbo.ams_members as m 
            on m.memberID = md.memberID 
            and m.memberID = m.activeMemberID 
            and m.memberID = @memberID
        inner join dbo.ams_memberdataColumnValues as [column]  
            on [column].valueID = md.valueID
        inner join dbo.ams_memberDataColumns as mdc 
            on mdc.columnID = [column].columnID
            and mdc.columnName = 'Rejoin Date'

        if @joinDate is null OR @joinDate = ''
        begin
            select @joinDate = [column].columnValueDate
            from dbo.ams_memberData as md
            inner join dbo.ams_members as m 
                on m.memberID = md.memberID 
                and m.memberID = m.activeMemberID 
                and m.memberID = @memberID
            inner join dbo.ams_memberdataColumnValues as [column]  
                on [column].valueID = md.valueID
            inner join dbo.ams_memberDataColumns as mdc 
                on mdc.columnID = [column].columnID
                and mdc.columnName = 'Join Date'
        end

        select case when @joinDate is null or @joinDate = '' then getDate() else @joinDate end as memberDate;
        set nocount off
    </cfquery>
     <cfset local.admissiondate = DateFormat(local.qryGetJoinDateValue.memberDate,"YYYY")>
</cfif>
<cfsavecontent variable="local.CLEHead">
    <cfoutput>
        <script language="JavaScript">
            function _FB_hasValue(obj, obj_type){
                if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
                else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
                else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
                else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
                else{ return true; }
            }
            function hideAlert() { $('##issuemsg').html('').hide(); };
            function showAlert(msg) { $('##issuemsg').html(msg).show(); };
            function _FB_validateForm(){
                var theForm = document.forms["frmMemberCert"];
                var arrReq = new Array();
                <cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or  application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
                    if (typeof $('##membernumber') != "undefined" && !_FB_hasValue(theForm['membernumber'], 'TEXT')) arrReq[arrReq.length] ='Must enter MemberNumber before you can generate a certificate.';
                </cfif>
                if (arrReq.length > 0) {
                    var msg = '';
                    for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
                    showAlert(msg);
                    return false;
                }
            }
            function downloadCertificate() {
                $("##downloadTxt").val(1);                 
                $("##frmMemberCert").submit();
                $("##downloadTxt").val(0); 
            }
            function printdiv(printdivname) {
                var headstr = "<html><head><title>Booking Details</title></head><body>";
                var footstr = "</body>";
                var newstr = document.getElementById(printdivname).innerHTML;
                var oldstr = document.body.innerHTML;
                document.body.innerHTML = headstr+newstr+footstr;
                window.print();
                document.body.innerHTML = oldstr;
                return false;
            }
        </script>
    </cfoutput>
</cfsavecontent>

<cfhtmlhead text="#local.CLEHead#">
<cfoutput>
    <h1 class="TitleText noprint">Membership Certificate</h1>
    <div id="issuemsg" class="alert alert-error hide noprint"></div>
    <cfform class="form-inline noprint" method="POST" action="#local.customPage.baseURL#" name="frmMemberCert" id="frmMemberCert" onsubmit="return _FB_validateForm();">
        <cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
        <label for="membernumber"><b>MemberNumber</b>:</label>
        <cfinput class="input-large" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber">
        <button type="submit" class="btn btn-default btnSubmit" name="btnSubmit">Generate Certificate</button>
        <cfelseif len(event.getValue('mk',''))>
        <cfinput type="hidden" name="membernumber" id="membernumber" value="#local.membernumber#">   
        </cfif>
        <cfinput type="hidden" name="downloadTxt" id="downloadTxt" value="0">
        <button class="btn pull-right downloadBtn" type="button" onClick="downloadCertificate();"><i class="icon-download"></i> Download</button> &nbsp;
        <button class="btn pull-right" type="button" onClick="printdiv('certContainer');"><i class="icon-print"></i> Print</button>
    </cfform>

    <cfsavecontent variable = "local.certContent">
        <div id="certContainer">
            <style>
                .captilize{text-transform: uppercase;}
                .font10{font-size:10pt;}
                .font9{font-size:9pt;}
                .font8{font-size:8pt;}
                .font28{font-size:28pt;}
                .line12{line-height:12pt;}
                .line15{line-height:15pt;}
                .line44{line-height:44pt;}
                .certContainer,.certTable{height:100%;}
                .certTextContainer{height:100%;}
                .mb-20{margin-bottom:20pt!important;}
                .fontBlue{color:##1191cc!important}
                 @media print{
                    @page {size:11in 8.5in ;  margin: 0;}
                    .noprint,header,footer,.sidebar{ display:none !important; }
                    .print{display:block !important;}
                    .font10{font-size:12pt;}
                    .font9{font-size:11pt;}
                    .font8{font-size:10pt;}
                    .font28{font-size:34pt;}
                    .line12{line-height:12pt;}
                    .line15{line-height:18pt;}
                    .line44{line-height:44pt;}
                    .mb-20{margin-bottom:35pt!important;}
                    .mb-40{margin-bottom:45pt!important;}
                    body{size:11in 8.5in ;}
                    .certContainer,.certTable{height:754px!important;}
                    .certTextContainer{height:700px!important;}
                }
            </style>
            <div class="certContainer" style="position:relative;margin:30px;">
                <img  class="print" src="/images/certificateBgBig.jpg" style="position:absolute;top:0;z-index:-1;">
                <table class="certTable" border="0" style="z-index:999999" style="width:100%;">
                    <tr>
                        <td>
                            <div class="certTextContainer" style="text-align:center;width:80%;margin:0;">
                                <img src="/images/certificateLogo.png" style="margin:50pt 60pt 6pt 60pt;width:80%;">
                                <div class="captilize font10 line12">hereby certifies that</div>
                                <div class="captilize font28 line44 fontBlue" style="text-align:center;font-weight:bold;margin-top:13pt;font-family:'Times New Roman', serif;">
                                   <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND NOT len(arguments.event.getValue('membernumber','')) AND NOT len(arguments.event.getValue('mk',''))>[[extendedname]]<cfelse>#local.extendedName#</cfif>
                                </div>
                                <div class="font9 line15 mb-20" style="margin:20pt 18pt 14pt 18pt;text-align:center;">
                                    is a Member in Good Standing<br>
                                    of the New York State Academy of Trial Lawyers<br>
                                    and is accorded all the rights and privileges thereof,<br>
                                    and, furthermore, is dedicated to the Academy's mission<br>
                                    to promote the common professional interests of its attorney members,<br>
                                    to improve the administration of justice, and<br>
                                    to preserve the civil justice system.
                                </div>
                                <div class="font10 mb-40" style="font-style:italic">Admitted to Membership #local.admissiondate#</div>
                                <div class="captilize" style="margin-left:40pt;">
                                    <div style="width:50%;">#variables.strPageFields.PresidentSignature#</div>
                                    <div class="font10" style="width:50%;font-weight:600;border-top:1pt solid ##1191cc;font-family:'Times New Roman', serif;">
                                        #variables.strPageFields.PresidentName#
                                    </div>
                                    <div class="font8 fontBlue" style="width:50%;font-weight:600;">President</div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
                
            </div>
        </div>
    </cfsavecontent>
    #local.certContent#
</cfoutput>
<cfif local.downloadTxt eq 1>
	<cfset local.uid = createuuid()>
	<cfdocument format="PDF" filename="#application.paths.SharedTempNoWeb.path##local.uid#.pdf" overwrite="Yes" marginLeft="0" marginTop="0" marginBottom="0" marginRight="0" pageType="letter" orientation="landscape" >
        <cfoutput>
            <!DOCTYPE html>
			<html>
                <head>
                    <style>
                    body { margin:0px;padding:0px;}
                    .certWrapper{height:2500px;width:3300px;background: url(#event.getValue('mc_siteinfo.INTERNALASSETSURL')#images/certificateBgBig.jpg) no-repeat;margin:105px 105px 65px 105px;}
                    .captilize{text-transform: uppercase;}
                    </style>
                </head>
                <body lang=EN-US>
                    <div class="certWrapper" style="">
                        <div style="margin:0px;text-align:center;width:80%;height:2330px;padding-top:90pt;">
                            <img src="#event.getValue('mc_siteinfo.INTERNALASSETSURL')#images/certificateLogo.png" style="margin:95pt 60pt 20pt 60pt;width:80%;">
                            <div class="captilize" style="line-height: 62pt;font-size:40pt;font-family:'Arial', 'san-serif';margin-bottom:25pt;">hereby certifies that</div>
                            <div class="captilize" style="text-align:center;font-size:110pt;line-height:120pt;font-weight:bold;color:##1191cc;margin-top:13pt;margin-bottom:90pt;font-family:'Times New Roman', serif;"> #local.extendedName#</div>
                            <div style="margin:18pt 18pt 14pt 18pt;text-align:center;font-size:35pt;line-height:60pt;font-family:'Arial', 'san-serif';margin-bottom:80pt;">
                                is a Member in Good Standing<br>
                                of the New York State Academy of Trial Lawyers<br>
                                and is accorded all the rights and privileges thereof,<br>
                                and, furthermore, is dedicated to the Academy's mission<br>
                                to promote the common professional interests of its attorney members,<br>
                                to improve the administration of justice, and<br>
                                to preserve the civil justice system.
                            </div>
                            <div style="font-size:38pt;font-style:italic;font-family:'Arial', 'san-serif';margin-bottom:50pt;">Admitted to Membership #local.admissiondate#</div>
                            <div align="left" class="captilize" style="margin-left:40pt;margin-top:50pt;">
                                <div align="center"  style="width:50%;">#variables.strPageFields.PresidentSignature#</div>
                                <div align="center" style="width:50%;font-weight:600;font-size:40pt;line-height:60pt;margin-top:10pt;border-top:1.5pt solid ##1191cc;font-family:'Times New Roman', serif;">
                                    #variables.strPageFields.PresidentName#
                                </div>
                                <div  align="center" style="width:50%;font-size:30pt;line-height:50pt;color:##1191cc;font-weight:400;font-family:'Arial', 'san-serif';">President</div>
                            </div>
                        </div>
                    </div>
                </body>
			</html>
		</cfoutput>
	</cfdocument>
	<cfheader name="Content-Disposition" value="attachment;filename=file.pdf">
	<cfcontent type="application/octet-stream" file="#application.paths.SharedTempNoWeb.path##local.uid#.pdf" deletefile="Yes">
</cfif>