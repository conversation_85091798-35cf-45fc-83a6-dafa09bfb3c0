<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			variables.applicationReservedURLParams = "fa";
			variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
			local.formAction = arguments.event.getValue('fa','showLookup');
			local.crlf = chr(13) & chr(10);
			local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

			local.arrCustomFields = [];
			local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Identify Yourself" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Click Here to Begin" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorIntroText",type="STRING",desc="Text to show above account locator",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationEmailStaffTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" };
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationEmailStaffFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentProfileCodeCC",type="STRING",desc="pay profile code for CC",value="WCBACC" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="PaymentProfileCodeCheck",type="STRING",desc="pay profile code for Check",value="PaybyCheck" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ErrorActiveSubscriptionFound",type="CONTENTOBJ",desc="Error message when active subscription found",value="Westchester County Bar Association records indicate you are currently a member. Please <a href='/?pg=login'>Click here</a> to log in." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ErrorAcceptedSubscriptionFound",type="CONTENTOBJ",desc="Error message when accepted subscription found",value="Westchester County Bar Association records indicate you are currently a member. Please <a href='/?pg=login'>Click here</a> to log in." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ErrorBilledSubscriptionFound",type="CONTENTOBJ",desc="Error message when billed subscription found",value="You need to renew your WCBA membership. You will be re-directed to your renewal shortly." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="FormIntro", type="CONTENTOBJ", desc="Form Introduction", value="This is a one-year membership for the Westchester County Bar Association. The WCBA membership year runs from January 1st to December 31st." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RequiredStatement",type="CONTENTOBJ",desc="Content at the top of Page 1",value="<span style='color:red;'>* Required data" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipType",type="CONTENTOBJ",desc="Membership and Additional Membership Options  Intro",value="Per the information provided on the previous page, below iare your Westchester County Bar Membership selections and dues amounts.<br/>Sustaining Membership: The WCBA recognizes the generosity and support of sustaining members by listing their names in the Annual Banquet Journal, the Westchester Bar Journal, the Westchester Lawyermagazine’s annual sustaining member feature, and on our website with a homepage link to your organization and member profile on all subpages." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationMessage",type="CONTENTOBJ",desc="Content on Confirmation",value="Thank you. Your membership application has been submitted for review. You will receive an email from Westchester County Bar Association once your application payment is processed." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="WCBA Membership Submission Confirmation" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
			local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="New Member Application Submitted" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ProfessionalLicense",type="CONTENTOBJ",desc="Professional License",value="Please add all State Licensures. For Other State, please include state abbrevation in the License Number along with number (if known). You can add several entries in the Other State License Number. You must enter the 'earliest' License Date for the Other State Active Date field. To select dates, click on the calendar tool to update year (< >), month, date." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="TrueCertification",type="CONTENTOBJ",desc="True Certification Statement",value="Contributions or gifts to WCBA are not tax deductible as charitable contributions; however, they may be tax deductible as ordinary and necessary business expenses.<br/>I certify that the statements I made herein are true. By providing my mailing address, email address, telephone and fax number, I agree to receive communications sent by or on behalf of the Westchester County Bar Association via mail, email, telephone or fax." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="subscriptionUID",type="STRING",desc="UID of the Root Subscription that this form offers",value="73ad6125-8748-4ba4-9064-0ef16cfcaf62" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="annualMembershipTypeUID",type="STRING",desc="Type UID of the Root Subscription that this form offers",value="30abd6a6-6e4e-4f7a-a4e4-02771dc5c857" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);

			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
				formName='joinform',
				formNameDisplay='Membership Application',
				orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
				memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
			));

			variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));
			variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MembershipApplicationHistory', subName='Started');
			variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MembershipApplicationHistory', subName='Completed');
			variables.historyStartedText = "Member started join form.";
			variables.historyCompletedText = "Member completed join form.";
			variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=variables.formName);

			 switch (local.formAction) {
				case "processLookup":
					switch (processLookup()) {
						case "success":
							local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
							break;		
						case "activefound":
							local.returnHTML = showError(errorCode='activefound');
							break;
						case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;		
						case "acceptedfound":
							local.returnHTML = showError(errorCode='acceptedfound');
							break;		
						case "billedfound":
							local.returnHTML = showError(errorCode='billedfound');
							break;		
						default:
							application.objCommon.redirect(variables.baselink);
							break;				
					}
					break;
				case "processMemberInfo":
					switch (processMemberInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						case "spam":
							local.returnHTML = showError(errorCode='spam');
							break;	
						default:
							local.returnHTML = showError(errorCode='failsavemember');
							break;				
					}
					break;
				case "processMembershipInfo":
					switch (processMembershipInfo(rc=arguments.event.getCollection())) {
						case "success":
							local.returnHTML = showPayment(rc=arguments.event.getCollection());
							break;
						default:
							local.returnHTML = showError(errorCode='failsavemembership');
							break;				
					}
					break;
				case "processPayment":
				
					local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
					
					if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
						arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
					}
					switch (local.processPaymentResponse.response) {
						case "success":
							local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
							local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
							structDelete(session, "formFields");
							break;
						default:
							local.returnHTML = showError(errorCode='failpayment');
							break;				
					}
					break;
				case "showMembershipInfo":
					local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
					break;				
				default:
					local.returnHTML = showLookup();
					break;
			}
			local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
			return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
		<cfif structKeyExists(session, "captchaEntered")>
			<cfset structDelete(session, "captchaEntered")>
		</cfif> 
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					##cboxOverlay{z-index: 99998 !important;}
					##colorbox{z-index: 99999 !important;}
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					function assignMemberData(memObj) {
						$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
						mc_continueForm($('###variables.formName#'),afterFormLoad);
					}
					function toggleFTM() {
					}
					function selectMember() {
						hideAlert();
						$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
					}
					$(document).on('click', '##btnAddAssoc', function(){
						selectMember();
					});
					function addMember(memObj) {
						$.colorbox.close();
						assignMemberData(memObj);
					}
					function validatePaymentForm(isPaymentRequired) {
						if(isPaymentRequired == 'YES'){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg,afterFormLoad);
								return false;
							}
						}
						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);     
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}				
					function resizeBox(newW,newH) { 
						var windowWidth = $(window).width();
						var _popupWidth = newW;
						if(windowWidth < 585) {
							_popupWidth = windowWidth - 30;
						}
						$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
					}
					$(document).ready(function() {
						<cfif variables.useMID and NOT local.isSuperUser>					
							var mo = { memberID:#variables.useMID# };
							assignMemberData(mo);					
						<cfelseif local.isSuperUser>
							$('div##div#variables.formName#wrapper').hide();
							$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
						</cfif>
						$(window).on("resize load", function() {
							var windowWidth = $(window).width();
							if(windowWidth < 585) {
								$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
							} else{
								$.colorbox.resize({innerWidth:550, innerHeight:330});		
							}				
						});
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

				<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
					<cfinput type="hidden" name="fa" id="fa" value="processLookup">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">

					<cfif len(variables.strPageFields.AccountLocatorIntroText)>
						<div class="row-fluid">#variables.strPageFields.AccountLocatorIntroText#</div>
					</cfif>		

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#variables.strPageFields.AccountLocatorTitle#</div>
						<div class="tsAppSectionContentContainer">
							<table class="table" cellspacing="0" cellpadding="2" border="0" width="100%">
							<tr>
								<td width="175" style="text-align:center;vertical-align:middle;">					
									<button type="button" id="btnAddAssoc" class="tsAppBodyButton">#variables.strPageFields.AccountLocatorButton#</button>
								</td>
								<td class="tsAppBodyText">#variables.strPageFields.AccountLocatorInstructions#</td>
							</tr>
							</table>
						</div>
					</div>
				</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>			
			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.annualMembershipTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.annualMembershipTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.annualMembershipTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfset local.memberInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='c227352f-c449-4f55-942d-998f6d1e6a6e', mode="collection", strData=local.strData)>
		<cfset local.socialMediaFieldSet = application.objCustomPageUtils.renderFieldSet(uid='9dd5d3de-e978-4ff3-8b14-7b8c47cc36bf', mode="collection", strData=local.strData)>
		<cfset local.optionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='02940603-0b47-45c4-93e3-9f8a3cb505cd', mode="collection", strData=local.strData)>
		<cfset local.mailingAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='0c32b21e-b878-41a0-a017-a2dc4e987838', mode="collection", strData=local.strData)>
		<cfset local.physicalAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='e1ebfea5-45c1-4510-b77a-b50f58fdb6c4', mode="collection", strData=local.strData)>
		<cfset local.homeAddressInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='5047f7d2-0511-45e1-95ff-162cb699981c', mode="collection", strData=local.strData)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid='8a6468ec-c3ae-4a8e-aff1-82912957b8f7', mode="collection", strData=local.strData)>
		<cfset local.membershipCategoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid='f7ebc394-840c-4f1e-902d-fe8352c9851d', mode="collection", strData=local.strData)>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.membershipCategory = "">
		<cfloop collection="#local.membershipCategoryFieldSet.strFields#" item="local.thisField">
			<cfif local.membershipCategoryFieldSet.strFields[local.thisField] eq "Membership Category you qualify for">
				<cfset local.membershipCategory = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.lawSchool = "">
		<cfloop collection="#local.memberInformationFieldSet.strFields#" item="local.thisField">
			<cfif local.memberInformationFieldSet.strFields[local.thisField] eq "Name of Law School">
				<cfset local.lawSchool = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>	
		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">	
					.contentWrapperinput[type="text"] {width:206px!important;}
					.contentWrapperselect{width:220px!important;}
					##ProfessionalLicenseFields input[type="text"] {width:auto!important;}
					##ProfessionalLicenseFields select{width:auto!important;}
					##ProfessionalLicenseFields{width:100%!important;}
					div.tsAppSectionHeading{margin-bottom:20px}
					##Step1TopContent{margin-left:14px;margin-bottom: 15px;}
					##Autorenew-Position table tr > td:nth-child(2){width:30%!important;}
					##addressPreference table tr > td:nth-child(2){width:30%!important;}
					.contentWrapper table td:nth-child(2) {white-space: initial!important;}
					@media screen and (max-width: 767px){
						.contentWrapper table td {display: block;margin-bottom:0px;}
						.contentWrapper table td:nth-child(1) {display: inline;margin: 0;padding: 0;}
						.contentWrapper table td:nth-child(2) {display: inline;margin: 0;padding: 0;}
						.contentWrapper table td:nth-child(3) {display: inline;margin: 0;padding: 0;}
						.contentWrapper table td:nth-child(4) {margin-left: 0;padding-left: 0;}
						.contentWrapper div.ui-multiselect-menu{width:auto!important;}
						##ProfessionalLicenseFields input[type="text"] {width:206px!important;}
						##ProfessionalLicenseFields select{width:220px!important;}
					}	
					.contentWrapper button.ui-multiselect {width:220px!important;}
					.contentWrapper div.ui-multiselect-menu {width:214px!important;}							
					div.alert-danger{padding: 10px !important;}
					@media (min-width: 1200px){
						.eachRow{margin-bottom: 5px;}
						.proLicenseLabel{font-weight:700;text-align:left;font-size: 9pt;}
						.areaStatus{text-align:left;margin-left: 0px!important;}
						.areaState{text-align:left;}
						##state_table  .proLicenseLabel{display:block;}
						.wrapLeft{display: table-cell!important;}
						.span3 input {width: 90%!important;}
					}
					.wrapLeft{display:none;}
					.jsLabel{display:none !important;font-weight:700;font-size: 9pt;}
					.areaStatus{margin-left:0 !important;}
					.span3{margin-left:0 !important;margin-right:10px !important;}
					@media  (min-width: 767px) and  (max-width: 1200px){
						.span3 input{width: 90%!important;}
						.proLicenseLabel{font-weight:700;text-align:left;font-size: 9pt;}
						.eachRow{margin-bottom: 5px;}
						.areaState{text-align:left;}
					}
					@media (max-width: 979px) and (min-width: 768px){
						.span3{margin-left:0 !important;margin-right:10px !important;}
						.span3 input{width: 90%!important;}
						.proLicenseLabel{font-weight:700;text-align:left;font-size: 9pt;}
						.eachRow{margin-bottom: 5px;}
						.areaState{text-align:left;}
					}
					@media (max-width: 767px){
						.eachRow{margin-bottom: 5px;}
						##state_table  .proLicenseLabel{display:none !important;}
						.jsLabel{display:block !important;margin-top: -5px;}
					}
				</style>
				<script language="javascript">
					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
					function checkCaptchaAndValidate(){
						var thisForm = document.forms["#variables.formName#"];
						var status = false;
						var captcha_callback = function(captcha_response){
							if (captcha_response.response && captcha_response.response != 'success') {
								status = false;
							} else {
								status = true;
							}
						}
						if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
							alert('Please enter the correct code shown in the graphic.');
							return false;
						} else {
							#variables.captchaDetails.jsvalidationcode#
						}
						if(status){
							return validateMemberInfoForm();
						} else {
							alert('Please enter the correct code shown in the graphic.');
							return false;
						}
					}
					function validateMemberInfoForm(){
						var _CF_this = document.forms['#variables.formName#'];
						var arrReq = new Array();
						
						#local.memberInformationFieldSet.jsValidation#
						#local.socialMediaFieldSet.jsValidation#						
						#local.optionalInformationFieldSet.jsValidation#	
						#local.mailingAddressFieldSet.jsValidation#
						#local.physicalAddressFieldSet.jsValidation#
						#local.homeAddressInformationFieldSet.jsValidation#
						#local.addressPreferencesFieldSet.jsValidation#
						#local.membershipCategoryFieldSet.jsValidation#

						<cfif len(trim(local.membershipCategory))>
							var mcSel = $.trim($('###variables.formName# ###local.membershipCategory# option:selected').text());
							var lS = $.trim($('###variables.formName# ###local.lawSchool#').val());

							if(mcSel != 'Paralegal' || mcSel != 'Paralegal Student'){
								var prof_license = $('##mpl_pltypeid').val();
								var isProfLicenseSelected = false;
								if(prof_license != "" && prof_license != null){
									isProfLicenseSelected = true;
									$.each(prof_license,function(i,val){
										var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
										if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
										if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
									});
								}
							}

							if(mcSel == ''){
								arrReq[arrReq.length] = "Membership Category is required.";	
							}
							else if ((mcSel == 'Municipal/Nonprofit Attorney' || mcSel == 'Attorney' || mcSel == 'Judge (Sitting or Acting)') && !isProfLicenseSelected){
								arrReq[arrReq.length] = "Professional License is required.";	
							}
							else if(mcSel == 'Law Student' && lS == ''){
								arrReq[arrReq.length] = "Name of Law School is required.";	
							}
							else if((mcSel == 'Municipal/Nonprofit Paralegal' || mcSel == 'Paralegal' || mcSel == 'Paralegal Student') && isProfLicenseSelected){
								arrReq[arrReq.length] = "A professional license has been defined so this membership category is not available. Please uncheck any License state if not licensed as an attorney.";	
							}
						</cfif>									

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}

						mc_continueForm($('###variables.formName#'));
						return false;
					}
					function prefillData() {
						var objPrefill = new Object();
						<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
							#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
						</cfloop>
						for (var key in objPrefill) {
							if (objPrefill.hasOwnProperty(key)) { 
								$('###variables.formName# ##'+key).val(objPrefill[key]);
							}
						}
						<cfif NOT structKeyExists(session, "captchaEntered")>
							showCaptcha();
						</cfif>
					}
					$(document).ready(function() {
						prefillData();
						<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
							toggleFTM();
						</cfif>
						<cfif listLen(local.profLicenseIDList)>
							$("##state_table").show();
						</cfif>

						$("##mpl_pltypeid").multiselect({
							header: true,
							noneSelectedText: ' - Please Select - ',
							selectedList: 1,
							minWidth: 400,
							click: function(event, ui){
								if(ui.checked){
									$("##state_table").show();
									$('##state_table').after('<div class="row-fluid eachRow" id="tr_state_'+ui.value+'" >'
														+'<div style="margin-bottom: -1px;margin-top: 15px;" class="row-fluid eachRow jsLabel">State Name</div>'
														+'<div class="span3 areaState" >'
														+'<span  class="tsAppBodyText">'+ui.text+'</span>'
														+'</div>'
														+'<div class="row-fluid eachRow jsLabel" >#variables.strProfLicenseLabels.profLicenseNumberLabel#</div>'
														+'<div class="span3" >'
														+'<input class="licensenumber" size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" />'
														+'<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
														+'</div>'
														+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</div>'
														+'<div class="span3" >'
														+'<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="tsAppBodyText" />'
														+'</div>'
														+'<div style="margin-top: 2px;" class="row-fluid eachRow jsLabel">#variables.strProfLicenseLabels.profLicenseStatusLabel#</div>'
														+'<div class="span3 areaStatus" >'
														+'<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="">Please Select</option><option value="active" selected="selected">Active</option><option value="inactive">Inactive</option><option value="disbarred">Disbarred</option><option value="suspended">Suspended</option></select>'
														+'</div>'
														+'</div>');

									if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
										mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
									}
									$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
									}else{
									$("##tr_state_"+ui.value).remove();
								}
						},
						});	
					});				
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.FormIntro)>
						<div class="row-fluid">#variables.strPageFields.FormIntro#</div>
					</cfif>		
					
					<cfif len(variables.strPageFields.RequiredStatement)>
						<div class="row-fluid">#variables.strPageFields.RequiredStatement#</div>
					</cfif>						

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.memberInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.memberInformationFieldSet.fieldSetContent#
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">Professional License Information</div>
						<div class="tsAppSectionContentContainer professionalLisenceWrap">
							<cfif len(variables.strPageFields.ProfessionalLicense)>
								<div id="ProfessionalLicense">#variables.strPageFields.ProfessionalLicense#</div><br/>
							</cfif>
							<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
							<table cellpadding="3" border="0" cellspacing="0">		
								<tr class="top">
									<th class="tsAppBodyText" colspan="3" align="left">
										&nbsp;
									</th>
								</tr>
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText">Professional License:</td>
									<td class="tsAppBodyText">
										<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
											<cfloop query="local.qryOrgPlTypes">
												<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
												<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr class="top">
									<td class="tsAppBodyText" width="10"></td>
									<td class="tsAppBodyText"></td>
									<td class="tsAppBodyText"></td>
								</tr>
							</table>
							<table cellpadding="3" border="0" cellspacing="0" id="ProfessionalLicenseFields">
								<tr>
									<td class="tsAppBodyText wrapLeft" width="">&nbsp;</td>
									<td>
										<div class="row-fluid" id="state_table" style="display:none;" >
											<div class="span3 proLicenseLabel" >
												State Name
											</div>
											<div class="span3 proLicenseLabel"  >
												#variables.strProfLicenseLabels.profLicenseNumberLabel#
											</div>
											<div class="span3 proLicenseLabel"  >
												#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)
											</div>
											<div class="span3 proLicenseLabel" >
												#variables.strProfLicenseLabels.profLicenseStatusLabel#
											</div>
										</div>									
											
										<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
											<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
												<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
												<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
												<cfset  local.license_status  = 'Active'>
												<div class="row-fluid"  >
													<div class="span3" id="tr_state_#local.thisItem#">
														<span  class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</span>
														
													</div>
													<div class="span3" >
														<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
														<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
														
													</div>
													<div class="span3" >
														<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" />
															<cfsavecontent variable="local.datejs">
																<cfoutput>
																	<script language="javascript">
																		$(document).ready(function() { 
																			mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
																		});
																	</script>
																	<style type="text/css">
																	##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
																	</style>
																</cfoutput>
															</cfsavecontent>
															<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
														
													</div>
													<div class="span3" >
														<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText">
																<option value="">Please Select</option>
																<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>																	
															</select>	
													</div>
												</div>
											</cfloop>
										</cfif>										
									</td>
								</tr>					
							</table>
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.socialMediaFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.socialMediaFieldSet.fieldSetContent#
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.optionalInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.optionalInformationFieldSet.fieldSetContent#
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.mailingAddressFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.mailingAddressFieldSet.fieldSetContent#
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.physicalAddressFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.physicalAddressFieldSet.fieldSetContent#
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.homeAddressInformationFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.homeAddressInformationFieldSet.fieldSetContent#
						</div>
					</div>

					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.addressPreferencesFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.addressPreferencesFieldSet.fieldSetContent#
						</div>
					</div>
					
					<div class="row-fluid">
						<div class="tsAppSectionHeading">#local.membershipCategoryFieldSet.fieldSetTitle#</div>
						<div class="tsAppSectionContentContainer">
							#local.membershipCategoryFieldSet.fieldSetContent#
						</div>
					</div>
					<div class="row-fluid">
						<div class="tsAppSectionContentContainer" >
							#variables.captchaDetails.htmlContent#
						</div>
					</div>

					<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
					#application.objWebEditor.showEditorHeadScripts()#
			
					<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

					<script language="javascript">	
						$(document).ready(function(){
							<cfloop query="local.qryOrgAddressTypes">
								addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
								function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
									var _address = _this.val();
									
									if(_address.length >0){
										if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
											$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
										}
									} else {
										$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
									}
								}
								
								$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
									addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
								});
							</cfloop>
						});
						function editContentBlock(cid,srid,tname) {
							var editMember = function(r) {
								if (r.success && r.success.toLowerCase() == 'true') {
									$('##frmmd_'+cid).html(r.html);
									var x = div.getElementsByTagName("script");
									for(var i=0;i<x.length;i++) eval(x[i].text); 
								}
							};
							var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
							TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
						}
					</script>
				</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>	
		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		
		<!--- Setting Captcha submitted flag --->
		<cfset session.captchaEntered = 1>	
		
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
			<!--- save member info and record history --->		
		<cfset local.objSaveMember.setRecordType(recordType='Individual')>
		<cfset local.objSaveMember.setMemberType(memberType='User')>		

		<cfset local.membershipCategoryField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Membership Category')>	
		
		<cfif structKeyExists(arguments.rc, "md_" & local.membershipCategoryField.COLUMNID) AND listLen(arguments.rc["md_" & local.membershipCategoryField.COLUMNID])>			
			
			<cfloop array="#local.membershipCategoryField.columnValueArr#" index="local.thisOption">
				<cfif arguments.rc["md_" & local.membershipCategoryField.COLUMNID] EQ local.thisOption.VALUEID>
					<cfset local.membershipCategoryID = local.thisOption.valueID>

					<cfif local.thisOption.COLUMNVALUESTRING EQ 'Attorney' OR local.thisOption.COLUMNVALUESTRING EQ 'Municipal/Nonprofit Attorney'>
						<cfset local.objSaveMember.setCustomField(field='Contact Type', value='Attorney')>
					<cfelseif local.thisOption.COLUMNVALUESTRING EQ 'Judge (Sitting or Acting)'>
						<cfset local.objSaveMember.setCustomField(field='Contact Type', value='Judge')>
					<cfelseif local.thisOption.COLUMNVALUESTRING EQ 'Affiliate/Non-Lawyer'>
						<cfset local.objSaveMember.setCustomField(field='Contact Type', value='Affiliate')>
					<cfelseif local.thisOption.COLUMNVALUESTRING EQ 'Paralegal' OR local.thisOption.COLUMNVALUESTRING EQ 'Municipal/Nonprofit Paralegal'>
						<cfset local.objSaveMember.setCustomField(field='Contact Type', value='Paralegal')>
					<cfelseif local.thisOption.COLUMNVALUESTRING EQ 'Law Student'>
						<cfset local.objSaveMember.setCustomField(field='Contact Type', value='Law Student')>
					<cfelseif local.thisOption.COLUMNVALUESTRING EQ 'Paralegal Student'>
						<cfset local.objSaveMember.setCustomField(field='Contact Type', value='Paralegal Student')>						
					</cfif>
				</cfif>
			</cfloop>
		</cfif>
	
		<cfset local.strResult1 = local.objSaveMember.saveData()>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>								

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
	
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>	

		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>		
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>	

		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
				 	function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();	

						<cfif val(local.subscriptionID)>
							if($(".well").eq(1).find('input[type="radio"],input[type="checkbox"],input[name="sub#local.subscriptionID#"]').length == 0){
								arrReq[arrReq.length] = " Select Membership.";
							}
							else if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
								arrReq[arrReq.length] = " Select Membership.";
							}
						</cfif>		

						if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}

						#local.result.jsValidation#

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}
				</script>
 			</cfoutput>
 		</cfsavecontent>
 		<cfhtmlhead text="#local.resultHtmlHeadText#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>div.alert-danger{padding: 10px !important;}</style>						

				<cfform name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
					<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
					
					<cfloop collection="#session.formFields.step1#" item="local.thisField">
						<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
							or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
							or left(local.thisField,5) eq "mccf_">
							<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
							<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
						</cfif>
					</cfloop>
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<cfif len(variables.strPageFields.MembershipType)>
						<div class="well">#variables.strPageFields.MembershipType#</div>
					</cfif>
					<div class="row-fluid">						
						#local.result.formcontent#						
					</div>
				
					<div class="well subAddonWrapper" >
						<legend>Agreement Confirmation</legend>
						<cfif len(variables.strPageFields.TrueCertification)>
							<div class="">
								#variables.strPageFields.TrueCertification#<br/><br/>
								<label class="checkbox subLabel" for="mccf_verify">
									<input type="checkbox" class="subCheckbox" name="mccf_verify" id="mccf_verify" value="0"> 
									I verify all information contained in this membership application is current.
								</label>	
						
							</div>
						</cfif>
					</div>
		
					<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
					<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid =variables.strPageFields.subscriptionUID)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
		
		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
		
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc)/>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC, variables.strPageFields.PaymentProfileCodeCheck ]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<style>div.alert-danger{padding: 10px !important;}</style>
				<script type="text/javascript">
					$('##mccfdiv_#variables.strPageFields.PaymentProfileCodeCC# iframe').load(function() {
						var iframeThis = this;
						$(iframeThis).contents().find('button[type="submit"]:contains("Continue")').click(function (e) {
							setTimeout(function(){ 
								if($.trim($(iframeThis).contents().find('##everr').text()).length == 0){
									$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
								}	
							}, 100);
						});
						$(iframeThis).contents().find('button[type="button"]:contains("Cancel")').click(function (e) {
							$('html,body').animate({scrollTop: $('##paymentMethodContainer').position().top},500);
						});
					});
				</script>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
				<cfinput type="hidden" name="fa" id="fa" value="processPayment">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
				<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
				<cfloop collection="#arguments.rc#" item="local.thisField">
					<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
						or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
						or left(local.thisField,5) eq "mccf_"
						or left(local.thisField,3) eq "sub">
						<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
					</cfif>
				</cfloop>
		
				<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
				<div class="row-fluid">
					<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
					<div class="tsAppSectionContentContainer">						
						#local.strResult.formContent#
						<br/><br/>
					</div>
				</div>	
				<div class="row-fluid">
					<div class="tsAppSectionHeading">Total Price</div><br/>
					<div class="tsAppSectionContentContainer">						
						#dollarFormat(local.strResult.totalFullPrice)#
						<br/><br/>					
					</div>
				</div>
				<cfif local.paymentRequired>
					#local.strReturn.paymentHTML#
				<cfelse>
					<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
					<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
				</cfif>
			</cfform>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.returnstruct = structNew()>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>
		
		<!--- create subscriptions--->
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
			siteID=variables.siteID,
			uid = variables.strPageFields.subscriptionUID)>

		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
			subscriptionID = local.subscriptionID,
			memberID = variables.useMID,
			isRenewalRate = false,
			siteID = variables.siteID,
			rc = arguments.rc)>

		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)>

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice">
			set nocount on;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;
			
			select nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, dueNow= case when nv.dateDue < getdate() then 1 else 0 end
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs
				on subs.subscriptionID = s.subscriptionID
				and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
			inner join dbo.sub_types t
				on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.siteID#">
			inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
				and ta.applicationTypeID = 17
				and ta.itemType = 'Dues'
			inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
			inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
			group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
			order by nv.dateDue
		</cfquery>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.PaymentProfileCodeCC>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.PaymentProfileCodeCC>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.memberInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='c227352f-c449-4f55-942d-998f6d1e6a6e', mode="confirmation", strData=arguments.rc)>
		<cfset local.socialMediaFieldSet = application.objCustomPageUtils.renderFieldSet(uid='9dd5d3de-e978-4ff3-8b14-7b8c47cc36bf', mode="confirmation", strData=arguments.rc)>
		<cfset local.optionalInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='02940603-0b47-45c4-93e3-9f8a3cb505cd', mode="confirmation", strData=arguments.rc)>
		<cfset local.mailingAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='0c32b21e-b878-41a0-a017-a2dc4e987838', mode="confirmation", strData=arguments.rc)>
		<cfset local.physicalAddressFieldSet = application.objCustomPageUtils.renderFieldSet(uid='e1ebfea5-45c1-4510-b77a-b50f58fdb6c4', mode="confirmation", strData=arguments.rc)>
		<cfset local.homeAddressInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='5047f7d2-0511-45e1-95ff-162cb699981c', mode="confirmation", strData=arguments.rc)>
		<cfset local.addressPreferencesFieldSet = application.objCustomPageUtils.renderFieldSet(uid='8a6468ec-c3ae-4a8e-aff1-82912957b8f7', mode="confirmation", strData=arguments.rc)>
		<cfset local.membershipCategoryFieldSet = application.objCustomPageUtils.renderFieldSet(uid='f7ebc394-840c-4f1e-902d-fe8352c9851d', mode="confirmation", strData=arguments.rc)>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		
		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
			
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<div style="padding-bottom:4px;">#variables.strPageFields.ConfirmationMessage#</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.ConfirmationMessageForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationMessage)>
					<div style="padding-bottom:4px;">#variables.strPageFields.ConfirmationMessage#</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.ConfirmationMessageForStaff">
			<cfoutput>
				<div style="padding-bottom:4px;">You have received an application for membership through the online Westchester County Bar Association membership application form.</div>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
				<!--@@ConfirmationMessageforEmail@@-->
				<div class=" row-fluid confirmationDetails">
					<!--@@specialcontent@@-->

					<div class="row-fluid">Here are the details of your application:</div><br/>
					
					#local.memberInformationFieldSet.fieldSetContent#
					#local.socialMediaFieldSet.fieldSetContent#
					#local.optionalInformationFieldSet.fieldSetContent#
					#local.mailingAddressFieldSet.fieldSetContent#
					#local.physicalAddressFieldSet.fieldSetContent#
					#local.homeAddressInformationFieldSet.fieldSetContent#
					#local.addressPreferencesFieldSet.fieldSetContent#
					#local.membershipCategoryFieldSet.fieldSetContent#		

					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
						</tr>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<div>#local.strResult.formContent#</div>
								<br/><br/>							
								<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#				
							</td>
						</tr>
					</table>
					<br/>
					
					<cfif local.paymentRequired>
						<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
							<tr>
								<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
							</tr>
							<tr>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
									<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
										<table cellpadding="3" border="0" cellspacing="0">
										<tr valign="top">
											<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
											<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
												#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
											</td>
										</tr>
										</table>
									<cfelse>
										None selected.
									</cfif>
								</td>
							</tr>
						</table>
					</cfif>	
				</div>		
			</cfoutput>
		</cfsavecontent>		

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationMessageforEmail@@-->",local.ConfirmationMessageForMember)>		

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[{ name="", email=variables.memberEmail.to }],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.SUBJECT,
			emailtitle="#arguments.rc.mc_siteinfo.sitename# - #variables.formNameDisplay#",
			emailhtmlcontent=local.confirmationHTML,
			siteID=variables.siteID,
			memberID=val(variables.useMID),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- create pdf and put on member's record --->
		<cfset local.uid = createuuid()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
		<cfdocument filename="#local.strFolder.folderPath#\un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<html>
				<head>
				
				</head>
				<body>
					#local.confirmationHTMLToMember#
				</body>
				</html>
			</cfoutput>
		</cfdocument>
		
		<cfset local.strPDF = structNew()>
		<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
		<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
		<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
		<cfset storeMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF)>
		

		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>	

		<cfsavecontent variable="local.specialText">
			<cfoutput>
				<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#arguments.rc.origMemberID#">#local.stOrigMemberNumber#</a></b></div>
				<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b><a target="_blank" href="#local.thisScheme#://#local.thisHostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#variables.useMID#">#local.stFinalMemberNumber#</a></b></div>
				<cfif NOT local.emailSentToUser>
					<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
				</cfif>
				<br/>
				<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
					<div style="padding-bottom:4px;">
						<b><u>Payment Processing results</u></b><br/>
						#arguments.rc.processPaymentResponse.accResponseMessage#
					</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub>
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationMessageforEmail@@-->",local.ConfirmationMessageForStaff)>		
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.emailSentToStaff = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to WCBA", emailContent=local.confirmationHTMLToStaff)>
	
		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<div class="tsAppSectionContentContainer">		
					<cfif len(variables.strPageFields.ConfirmationMessage)>
						<div style="padding-bottom:4px;">#variables.strPageFields.ConfirmationMessage#</div>	
						<br/>
					</cfif>				
					#arguments.confirmationHTML#
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ErrorAcceptedSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#		
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>	
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="storeMembershipApplication" access="private" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=variables.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.parentSiteResourceID = application.objSiteInfo.getSiteInfo(variables.orgDefaultSiteCode).memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=variables.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
				parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, docTitle='Membership Application - #DateFormat(now(),'m/d/yyyy')#', 
				docDesc='Membership Application Confirmation', author='', fileData=local.newFile, isActive=1, isVisible=true, 
				contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

</cfcomponent>					
