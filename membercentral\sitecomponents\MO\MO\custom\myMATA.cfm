<cfscript>
	variables.applicationReservedURLParams 	= "";
	local.customPage.baseURL	= "/?#getBaseQueryString(false)#";
	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	local.Organization			= arguments.event.getValue('mc_siteInfo.ORGShortName');
	// LOCAL SITE INFORMATION ------------------------------------------------------------------------------------------------
	local.orgID 				= event.getValue('mc_siteInfo.orgID');	
	local.orgCode 				= event.getValue('mc_siteInfo.orgCode');
	local.siteCode 				= event.getValue('mc_siteInfo.siteCode');
	local.siteID 				= event.getValue('mc_siteInfo.siteID');	
	
	local.memberID 				= session.cfcUser.memberData.memberID;
</cfscript>

<!--- Announcements --->
<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAnnouncements">
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">, @now datetime = getdate();
	SELECT top 5 n2.noticeID, dbo.fn_getResourcePagePlacementXML(@siteID, ai2.siteResourceID) as placementXML, tmp.contentTitle, ai2.applicationInstanceID
	FROM dbo.an_notices n2
	inner join an_centers c2 on n2.siteID = @siteID and n2.centerID = c2.centerID
	inner join cms_applicationInstances ai2 on ai2.applicationInstanceID = c2.applicationInstanceID and ai2.siteID = @siteID
	inner join 
		(
		select min(n.noticeID) as noticeID,
			noticeContent.contentTitle
		from dbo.an_notices n
		inner join cms_content content on n.siteID = @siteID and content.siteID = @siteID and content.contentID = n.noticeContentID
		inner join cms_siteResources sr on sr.siteID = @siteID and content.siteResourceID = sr.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		inner join an_centers c on n.centerID = c.centerID
		inner join cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID and ai.siteID = @siteID
		inner join cms_siteResources sr2 on sr2.siteID = @siteID and n.siteResourceID = sr2.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs2 on srs2.siteResourceStatusID = sr2.siteResourceStatusID and srs2.siteResourceStatusDesc = 'Active'
		inner join dbo.cms_contentLanguages as noticeContent on noticeContent.contentID = n.noticeContentID and noticeContent.languageID = <cfqueryparam value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#" cfsqltype="CF_SQL_INTEGER">
		where @now between n.startdate and n.enddate
		and dbo.fn_cache_perms_getResourceRightsXML(n.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,@siteID).exist('(/rights/right[@allowed="1"])[1]') = 1
		group by noticeContent.contentTitle
		) tmp
	on n2.noticeID = tmp.noticeID

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
</cfquery>

<!--- Search History --->
<cfquery datasource="#application.dsn.TLASites_search.dsn#" name="qrySearch" maxrows="10">
	select top(10) searchID, dateEntered, bucketIDOrigin,searchVerbose
	from dbo.tblSearchHistory sh, dbo.tblSearchBuckets sb
	where depoMemberDataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.depoMemberDataID#">
	and sh.bucketIDorigin = sb.bucketID	
	and siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
	order by dateEntered DESC
</cfquery>

<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscription">
	select s.subscriberID, s.directLinkCode, ss.statusCode as status, count(s2.subscriberID) as currAccepted
	from dbo.sub_subscribers s
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
	inner join dbo.sub_types t on t.typeID = subs.typeID 
																and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
																and t.typeName = 'Membership Dues'
	inner join dbo.sub_statuses ss on ss.statusID = s.statusID
	left outer join dbo.sub_subscribers s2 
		inner join dbo.sub_subscriptions subs2 on subs2.subscriptionID = s2.subscriptionID
		inner join dbo.sub_types t2 on t2.typeID = subs2.typeID 
																	and t2.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
																	and t2.typeName = 'Membership Dues'
		inner join dbo.sub_statuses ss2 on ss2.statusID = s2.statusID
		on s2.memberID = s.memberID
			and ss2.statusCode = 'P'
			and s2.parentSubscriberID is null
	where s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
	and (ss.statusCode = 'O' or ss.statusCode = 'R')
	and s.parentSubscriberID is null
	group by s.subscriberID, s.directLinkCode, ss.statusCode
</cfquery>


<!--- event registration in the passed --->
<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
	select top 4 e.eventid, r.registrantID, r.dateRegistered, c.contentTitle
	from dbo.ev_registrants as r
	inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID and r.recordedOnSiteID = evr.siteID
	inner join dbo.ev_events as e on e.eventid = evr.eventid and e.siteID = evr.siteID
	inner join dbo.cms_contentLanguages as c on c.contentID = e.eventContentID and c.languageID = 1
	inner join dbo.ams_members as m on m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
	inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID
	where r.memberid = mMerged.memberID
	and e.status <> 'D'	
	order by r.dateRegistered desc, e.eventid
</cfquery>


<!--- seminarweb history --->
<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.siteCode).qryAssociation>
<cfset local.showSW = false>
<cfif val(session.cfcUser.memberData.depomemberdataid) gt 0>
	<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
		<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#" null="No">
		<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.siteCode#" null="No">
		<cfprocresult name="local.qrySWL" resultset="1">
		<cfprocresult name="local.qrySWOD" resultset="2">
		<cfprocresult name="local.qryCertPrograms" resultset="3">
	</cfstoredproc>
	<cfset local.showSW = true>
</cfif>

<cfoutput>
	<div class="span6">
		<div class="row-fluid">
			<div class="span12 ">
				<div class="span2 myPhoto">
					<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
						<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="80" height="100">
					<cfelse>
						<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
					</cfif>
				</div>
				<div class="span10">
					<span class="HeaderText">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#</span><br />
					<span class="BodyText">
						<ul>
							<li><a href="/?pg=updateMember">Edit Profile</a></li>
							<li><a href="/?pg=updatemember##memberUpdateSectionLoginInfo">Change Login</a></li>
							<li><a href="mailto:<EMAIL>">Update Photo</a></li>
							<cfif (local.qrySubscription.recordcount eq 1) AND (local.qrySubscription.currAccepted eq 0)>								
								<li> <a href="/?pg=Renew">Renew My Membership</a></li>
							</cfif>	
						</ul>
					</span>
				</div>
			</div>
		</div>
		<div class="row-fluid">&nbsp;</div>
		<div class="row-fluid">
			<div class="HeaderText topBar">Announcements</div>
			<div>
				<cfif local.qryAnnouncements.recordcount NEQ 0>
					<ul>
						<cfloop query="local.qryAnnouncements">
							<cfset local.placementXML = xmlParse(local.qryAnnouncements.placementXML)>
							<cftry>
								<cfset local.representivePageTitle = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageTitle>
								<cfcatch type="any">
									<cfset local.representivePageTitle = "Untitled Page"/>
								</cfcatch>	
							</cftry>
							<cftry>
								<cfset local.representivePageName = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageName>
								<cfcatch type="any">
									<cfset local.representivePageName = "Unnamed Page"/>
								</cfcatch>	
							</cftry>

							<cftry>
								<cfif len(local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName)>
									<cfset local.representiveCommunityPageName = local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName/>
									<cfset local.pageLink = "/?pg=#local.representiveCommunityPageName#&commpg=#local.representivePageName#">
									<cfelse>
									<cfset local.pageLink = "/?#getAppBaseLink(local.qryAnnouncements.applicationInstanceID,arguments.event.getValue('mc_siteinfo.siteid'))#">
								</cfif>
								<cfcatch type="any">
									<cfset local.representivePageName = ""/>
									<cfset local.pageLink = "##">
								</cfcatch>	
							</cftry>

							<li><a href="#local.pageLink#">#local.qryAnnouncements.contentTitle#</a></li>
						</cfloop>
					</ul>
				<cfelse>
					<div class="sectionContent">
						<div class="BodyText"><strong>Currently, there are no announcements.</strong></div>
					</div>
				</cfif>				
			</div>
		</div>


		<div class="row-fluid">&nbsp;</div>
		<div class="row-fluid">
			<div class="HeaderText topBar">Member Links</div>
			<div class="row-fluid">
				<!--ZONEB-->
			</div>
		</div>

	</div>
	<div class="span6">
		<div class="row-fluid">
			<div class="span12 ">

			<div class="HeaderText topBar">Upcoming</div>

			<div class="BodyText">
				[[upcomingEvents includeRegistered=[false] includeNotRegistered=[true] maxrows=[5] format=[json] jsonvariable=[upcomingevents] noresultstext=[There are currently no upcoming events.]]]
					<div class="mcMergeTemplate" data-mcjsonvariable="upcomingevents">
						{{##if events}} 
							<div class="row-fluid">
								<div>
									<ul>
									{{##events}}
										<li style="padding-bottom:5px;"><span class="TitleText">{{moment startDateISO format='MM'}}/{{moment startDateISO format='DD'}}</span> {{{title}}}&nbsp;&nbsp;<a href="##" onClick="return viewEvent('{{{id}}}');">(view details)</a></li>
									{{/events}}
									</ul>
								</div>
							</div>							 
						{{else}}
							<div class="row-fluid">
								<div class="BodyText"><strong>There are currently no upcoming Events.</strong></div>
							</div>
						{{/if}}
					</div>	
				<div>
					<a href="/?pg=events&evAction=listAll"><i class="icon-calendar icon2x">&nbsp;</i> View the Full Calendar</a>
				</div>				
			</div>
		</div>
		<div class="row-fluid">&nbsp;</div>

		<div class="row-fluid">
			<div class="span12 ">
				<div class="HeaderText sponsorsBar">Sponsors</div>
				<div class="container-fluid sponsorsBox">
					<div id="myCarousel" class="carousel slide">
						<!-- Carousel items -->
						<div class="carousel-inner">
							<!--ZONEC-->
						</div>
					</div>									

				</div>
			</div>
		</div>		

		<div class="row-fluid">&nbsp;</div>

		<div class="row-fluid">
			<div class="span12 ">
				<div class="HeaderText topBar">ListServes</div>
				<!--ZONED-->
			</div>
		</div>		
	</div>
</cfoutput>

