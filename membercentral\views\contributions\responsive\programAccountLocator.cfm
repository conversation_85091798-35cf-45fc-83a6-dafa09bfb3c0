<cfset dataStruct = attributes.data.actionStruct>
<cfset local.stepNum = 2>

<cfinclude template="../commonJS_step2.cfm">

<cfoutput>
<div class="container-fluid">
	<div class="contribution-section">

		<cfinclude template="navigation.cfm">

		<div class="tab-content">
			<div class="tab-pane active" id="cpStep2">
				<div class="contribution">
					<h2 class="title-blue">#dataStruct.intakeFormTitle#</h2>

					<div id="cp2_err_div" style="display:none;margin:12px 0 5px 0;"></div>

					<cfform name="frmContribution" id="frmContribution" method="post" action="#dataStruct.mainformurl#" onsubmit="return doS2Validate(false)">
						<input type="hidden" name="nextStep" id="nextStep" value="payment">
						<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">

						<div class="well" id="divLookupArea">
							<h3>Account Lookup / Create New Account</h3>
							<ol style="margin-left:20px;">
								<li>Click the <span class="b">Account Lookup</span> button below.</li>
								<li>Enter the search criteria and click <span class="b">Continue</span>.</li>
								<li>If you see your name, please press the <span class="b">Choose</span> button next to your name.</li>
								<li>If you do not see your name, click the <span class="b">Create an Account</span> link.</li>
							</ol>

							<div id="associatedMemberIDSelect">
								<button name="btnAddAssoc" type="button" onClick="selectMember()" class="btn btn-large btn-primary">Account Lookup</button>
							</div>
						</div>
						<div class="CPSection waitingtxt" id="divLookupAreaLoading" style="display:none;">
							<div style="text-align:center;margin:30px 10px;"><i class="icon-spinner icon-spin"></i> Please wait while we prepare your contribution for the next step.</div>
						</div>
					</cfform>

				</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>