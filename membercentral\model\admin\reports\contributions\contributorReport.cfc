<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>	

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = CreateObject('component','model.admin.contributions.contributions');
		
		local.siteID = arguments.event.getValue('mc_siteinfo.siteid');
		local.qryFrequencies = local.objContributions.getFrequencyOptions(siteID=local.siteID);
		local.qryCPStatuses = local.objContributions.getCPStatuses();
		local.qryProgramsForFilter = local.objContributions.getAllSitePrograms(siteID=local.siteID);
		</cfscript>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.frmContribStartDateFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcontribstartdatefrom/text())")>
			<cfset local.frmContribStartDateTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcontribstartdateto/text())")>
			<cfset local.frmContribEndDateFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcontribenddatefrom/text())")>
			<cfset local.frmContribEndDateTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcontribenddateto/text())")>
			<cfset local.frmEnteredDateFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmentereddatefrom/text())")>
			<cfset local.frmEnteredDateTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmentereddateto/text())")>

			<cfset local.frmCPProgram = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcpprogram/text())")>
			<cfset local.frmCPCampaign = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcpcampaign/text())")>
			<cfset local.frmCPStatus = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcpstatus/text())")>
			<cfset local.frmCPFrequency = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcpfrequency/text())")>
			<cfset local.frmCPDuration = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcpduration/text())")>
			<cfset local.frmHasCard = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmhascard/text())")>

			<cfset local.frmFirstInstallFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmfirstinstallfrom/text())")>
			<cfset local.frmFirstInstallTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmfirstinstallto/text())")>
			<cfset local.frmRecurringInstallFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrecurringinstallfrom/text())")>
			<cfset local.frmRecurringInstallTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrecurringinstallto/text())")>
			<cfset local.frmlimittopastdue = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmlimittopastdue/text())")>
			<cfset local.frmLimitInstallFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmlimitinstallfrom/text())")>
			<cfset local.frmLimitInstallTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmlimitinstallto/text())")>
			<cfset local.frmView = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmview/text())")>

			<cfif NOT len(local.frmView)>
				<cfset local.frmView = 4>
			</cfif>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				<script language="javascript">
					function frmReportViewChange() {
						var frmView = parseInt($('##frmView').val());
						var arrShowFldSetOpt = [3,5,6,7,8,9];
						if(arrShowFldSetOpt.indexOf(frmView) != -1)  {
							$('##fieldsetDiv').show();
						}
						else {
							$('##fieldsetDiv').hide();
						}

						if(frmView == 3) $('.tbody_ovfsmc').hide(); 
						else $('.tbody_ovfsmc').show(); 
						if(frmView == 9) $('##btnReportBarscreen,##btnReportBarpdf').hide();
						else $('##btnReportBarscreen,##btnReportBarpdf').show();
					}
					function loadProgramSelections() {
						if($('##frmCPProgram').val() != null && $('##frmCPProgram').val().length == 1) {
							loadCPCampaigns($('##frmCPProgram').val()[0]);
						} else {
							$('##frmCPCampaign').empty().trigger("change");
							$('##campaignRow').hide();
						}
					}
					function loadCPCampaigns(pid,cid='') {
						var loadCampaignResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								var newOpts = '';
								for (var i=0; i<r.arrcampaigns.length; i++) {
									newOpts += '<option value="' + r.arrcampaigns[i]["campaignid"] + '">' + r.arrcampaigns[i]["campaignname"] + '</option>';
								}
								if (newOpts.length) {
									$('##frmCPCampaign').empty().append(newOpts).trigger("change");
									if (cid.length) {
										$('##frmCPCampaign').val(cid.split(','));
									}					
									$('##campaignRow').show();
								} else {
									$('##frmCPCampaign').find('option').remove();
									$('##campaignRow').hide();
								}
							} else {
								alert('There was a problem loading the campaigns. Try again.');
							}
						};
						var objParams = { programID:pid };
						TS_AJX_SYNC('ADMINCONTRIBUTION','getProgramCampaigns',objParams,loadCampaignResult,loadCampaignResult,10000,loadCampaignResult);
					}

					$(function() {
						setupRptFilterDateRange('frmContribStartDateFrom','frmContribStartDateTo');
						setupRptFilterDateRange('frmContribEndDateFrom','frmContribEndDateTo');
						setupRptFilterDateRange('frmEnteredDateFrom','frmEnteredDateTo');
						setupRptFilterDateRange('frmLimitInstallFrom','frmLimitInstallTo');
						mca_setupCalendarIcons('frmReport');
						mca_setupSelect2();

						$('##frmFirstInstallFrom, ##frmFirstInstallTo, ##frmRecurringInstallFrom, ##frmRecurringInstallTo').blur(function() {
							if($(this).val().length) {
  								$(this).val(formatCurrency($(this).val()));
							}
						});

						<cfif ListLen(local.frmCPProgram) is 1 and val(local.frmCPProgram) gt 0>
							loadCPCampaigns(#local.frmCPProgram#,'#local.frmCPCampaign#');
						</cfif>

						frmReportViewChange();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.dataHead#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfform name="frmReport" id="frmReport" method="post">
					<input type="hidden" name="reportAction" id="reportAction" value="">
					<div class="mb-4 stepDIV">
						<h5>Contribution Filter</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmCPProgram" class="col-md-4 col-sm-12 col-form-label">Program</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmCPProgram" id="frmCPProgram" onchange="loadProgramSelections();" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Select one or more programs">
											<cfloop query="local.qryProgramsForFilter">
												<option value="#local.qryProgramsForFilter.programID#" <cfif ListFindNoCase(local.frmCPProgram,local.qryProgramsForFilter.programID)>selected="selected"</cfif>>#local.qryProgramsForFilter.ProgramName#<cfif local.qryProgramsForFilter.siteResourceStatusDesc is 'Inactive'> (Inactive)</cfif></option>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row" id="campaignRow" style="display:none;">
									<label for="frmCPCampaign" class="col-md-4 col-sm-12 col-form-label">Campaign</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmCPCampaign" id="frmCPCampaign" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Any or No Campaign"></select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmCPStatus" class="col-md-4 col-sm-12 col-form-label">Status</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmCPStatus" id="frmCPStatus" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Select one or more statuses">
											<cfloop query="local.qryCPStatuses">
												<option value="#local.qryCPStatuses.statusID#" <cfif ListFindNoCase(local.frmCPStatus,local.qryCPStatuses.statusID)>selected="selected"</cfif>>#local.qryCPStatuses.statusName#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmCPFrequency" class="col-md-4 col-sm-12 col-form-label">Frequency</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmCPFrequency" id="frmCPFrequency" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Select one or more frequencies">
											<cfloop query="local.qryFrequencies">
												<option value="#local.qryFrequencies.frequencyID#" <cfif ListFindNoCase(local.frmCPFrequency,local.qryFrequencies.frequencyID)>selected="selected"</cfif>>#local.qryFrequencies.frequency#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmCPDuration" class="col-md-4 col-sm-12 col-form-label">Duration</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmCPDuration" id="frmCPDuration" class="form-control form-control-sm">
											<option value="">Either Perpetual or Non-Perpetual Contributions</option>
											<option value="1" <cfif local.frmCPDuration is 1>selected="selected"</cfif>>Perpetual Contributions Only</option>
											<option value="0" <cfif local.frmCPDuration is 0>selected="selected"</cfif>>Non-Perpetual Contributions Only</option>
										</select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmFirstInstallFrom" class="col-md-4 col-sm-12 col-form-label">First installment amount between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<div class="input-group-prepend">
														<span class="input-group-text">$</span>
													</div>
													<input type="text" name="frmFirstInstallFrom" id="frmFirstInstallFrom" value="#local.frmFirstInstallFrom#" class="form-control form-control-sm" placeholder="Amt From">
												</div>
											</div>
											<div class="col-md-auto col-sm-12 px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 pl-md-0">
												<div class="input-group input-group-sm">
													<div class="input-group-prepend">
														<span class="input-group-text">$</span>
													</div>
													<input type="text" name="frmFirstInstallTo" id="frmFirstInstallTo" value="#local.frmFirstInstallTo#" class="form-control form-control-sm" placeholder="Amt To">
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmRecurringInstallFrom" class="col-md-4 col-sm-12 col-form-label">Recurring installment amount between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<div class="input-group-prepend">
														<span class="input-group-text">$</span>
													</div>
													<input type="text" name="frmRecurringInstallFrom" id="frmRecurringInstallFrom" value="#local.frmRecurringInstallFrom#" class="form-control form-control-sm" placeholder="Amt From">
												</div>
											</div>
											<div class="col-md-auto col-sm-12 px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 pl-md-0">
												<div class="input-group input-group-sm">
													<div class="input-group-prepend">
														<span class="input-group-text">$</span>
													</div>
													<input type="text" name="frmRecurringInstallTo" id="frmRecurringInstallTo" value="#local.frmRecurringInstallTo#" class="form-control form-control-sm" placeholder="Amt To">
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmContribStartDateFrom" class="col-md-4 col-sm-12 col-form-label">Contribution start date between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmContribStartDateFrom" id="frmContribStartDateFrom" value="#local.frmContribStartDateFrom#" mcrdtxt="Contribution Start Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmContribStartDateFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmContribStartDateTo" id="frmContribStartDateTo" value="#local.frmContribStartDateTo#" mcrdtxt="Contribution Start Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmContribStartDateTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmContribStartDateFrom','frmContribStartDateTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmContribEndDateFrom" class="col-md-4 col-sm-12 col-form-label">Contribution end date between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmContribEndDateFrom" id="frmContribEndDateFrom" value="#local.frmContribEndDateFrom#" mcrdtxt="Contribution End Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmContribEndDateFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmContribEndDateTo" id="frmContribEndDateTo" value="#local.frmContribEndDateTo#" mcrdtxt="Contribution End Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmContribEndDateTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmContribEndDateFrom','frmContribEndDateTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmEnteredDateFrom" class="col-md-4 col-sm-12 col-form-label">Date entered between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmEnteredDateFrom" id="frmEnteredDateFrom" value="#local.frmEnteredDateFrom#" mcrdtxt="Date Entered Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmEnteredDateFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmEnteredDateTo" id="frmEnteredDateTo" value="#local.frmEnteredDateTo#" mcrdtxt="Date Entered End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmEnteredDateTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmEnteredDateFrom','frmEnteredDateTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmHasCard" class="col-md-4 col-sm-12 col-form-label">Pay Method Associated</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmHasCard" id="frmHasCard" class="form-control form-control-sm">
											<option value="">With or Without Pay Method Associated</option>
											<option value="Y" <cfif local.frmHasCard is 'Y'>selected="selected"</cfif>>With Pay Method Associated</option>
											<option value="N" <cfif local.frmHasCard is 'N'>selected="selected"</cfif>>Without Pay Method Associated</option>
										</select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmLimitInstallFrom" class="col-md-4 col-sm-12 col-form-label">Show contribution value for installments between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmLimitInstallFrom" id="frmLimitInstallFrom" value="#local.frmLimitInstallFrom#" mcrdtxt="Contribution Value Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmLimitInstallFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmLimitInstallTo" id="frmLimitInstallTo" value="#local.frmLimitInstallTo#" mcrdtxt="Contribution Value End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmLimitInstallTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmLimitInstallFrom','frmLimitInstallTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<div class="col-md-8 offset-md-4 col-sm-12">
										<div class="form-check">
											<input type="checkbox" name="frmlimittopastdue" id="frmlimittopastdue" value="1" class="form-check-input" <cfif local.frmlimittopastdue is 1>checked</cfif>>
											<label class="form-check-label" for="frmlimittopastdue">Limit to contributions with a past due amount</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					#showStepRollingDates(event=arguments.event)#

					<div class="mb-5 stepDIV">
						<h5>Report Options</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmView" id="frmView" onchange="frmReportViewChange()" class="form-control form-control-sm">
											<option value="1" <cfif local.frmView EQ "1">selected</cfif>>Group by Company - totals by company</option>
											<option value="2" <cfif local.frmView EQ "2">selected</cfif>>Group by Company - totals by company with program sub-totals</option>
											<option value="3" <cfif local.frmView EQ "3">selected</cfif>>Group by Company - totals by company with contribution detail</option>
											<option value="4" <cfif local.frmView EQ "4">selected</cfif>>Group by Program - totals by program</option>
											<option value="5" <cfif local.frmView EQ "5">selected</cfif>>Group by Program - totals by program with contribution detail</option>
											<option value="6" <cfif local.frmView EQ "6">selected</cfif>>Group by Member - totals by member</option>
											<option value="7" <cfif local.frmView EQ "7">selected</cfif>>Group by Member - totals by program</option>
											<option value="8" <cfif local.frmView EQ "8">selected</cfif>>Group by Member - totals by member with contribution detail</option>
											<option value="9" <cfif local.frmView EQ "9">selected</cfif>>Raw Data</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					#showStepMemberCriteria(event=arguments.event, title="Optionally Define Member Filter", desc="Optionally filter the members included on this report using the defined criteria below.")#

					<div class="mb-5 stepDIV" id="fieldsetDiv" style="display:none;">
						#showStepFieldsets(event=arguments.event)#
					</div>

					#showButtonBar(event=arguments.event)#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML, 
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.frmReportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>
			<cfset local.frmCPProgram = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcpprogram/text())")>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>

			<cfset local.strReport = generateData(strSQLPrep=local.strSQLPrep, reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>
	
				<cfif local.strReport.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfif listFind("1,2,4",local.frmReportView)>
						<cfinvoke method="screenReport_#local.frmReportView#" returnvariable="local.reportViewData" qryData="#local.strReport.qryData#" reportAction="#arguments.reportAction#">
					<cfelseif listFind("3,5,6,7,8",local.frmReportView)>
						<cfset local.strReport.strSQLPrep = local.strSQLPrep>
						<cfset local.strReport.reportAction = arguments.reportAction>
						<cfset local.strReport.frmShowPhotos = local.frmShowPhotos>
						<cfset local.strReport.frmShowMemberNumber = local.frmShowMemberNumber>
						<cfset local.strReport.frmShowCompany = local.frmShowCompany>
						<cfset local.strReport.orgcode = local.mc_siteInfo.orgcode>
						<cfset local.strReport.sitecode = local.mc_siteInfo.sitecode>
						<cfif listLen(local.frmCPProgram) is 1>
							<cfset local.strReport.qryCustomColumns = getProgramCustomColumnNames(programID=local.frmCPProgram)>
						</cfif>
						<cfinvoke method="screenReport_#local.frmReportView#" returnvariable="local.reportViewData" strReport="#local.strReport#">
					</cfif>

					<cfoutput>
					<table class="table table-sm table-borderless">
					#local.reportViewData#
					</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.strReport.qryData", strQryResult=local.strReport.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="screenReport_1" access="private" output="false" returntype="string">
		<cfargument name="qryData" type="query" required="true">
		<cfargument name="reportAction" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left">Company</th>
				<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
			</tr>
			</thead>
			<tbody>
			<cfloop query="arguments.qryData">
				<cfif arguments.qryData.reportRow eq 2>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<tr>
					<td class="align-top #local.ttlRow#">
						<cfif arguments.qryData.reportRow eq 1>
							#arguments.qryData.Company#
						<cfelseif arguments.qryData.reportRow eq 2>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.BalanceDue)#</td>
				</tr>
			</cfloop>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_2" access="private" output="false" returntype="string">
		<cfargument name="qryData" type="query" required="true">
		<cfargument name="reportAction" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left">Company / Program</th>
				<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
			</tr>
			</thead>
			<tbody>
			<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
				<cfset local.colspan = 5>
			<cfelse>
				<cfset local.colspan = 4>
			</cfif>
			<cfloop query="arguments.qryData">
				<cfif arguments.qryData.reportRow eq 1 and arguments.qryData.rowNum eq 1>
					<tr>
						<td><b>#arguments.qryData.Company#</b></td>
						<td colspan="#local.colspan - 1#">&nbsp;</td>
					</tr>
				</cfif>
				<cfif ListFindNoCase("2,3",arguments.qryData.reportRow)>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<tr>
					<td class="align-top <cfif arguments.qryData.reportRow eq 3>#local.ttlRow#</cfif>">
						<cfif arguments.qryData.reportRow eq 1>
							<div class="pl-3">#arguments.qryData.ProgramName#</div>
						<cfelseif arguments.qryData.reportRow eq 3>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.BalanceDue)#</td>
				</tr>
				<cfif arguments.qryData.reportRow eq 2>
					<tr><td colspan="#local.colspan#">&nbsp;</td></tr>
				</cfif>
			</cfloop>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_3" access="private" output="false" returntype="string">
		<cfargument name="strReport" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit') & "&tab=contributions">
		<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(arguments.strReport.orgcode) & "/memberphotosth/">

		<cfif arguments.strReport.qryData.recordcount>
			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=arguments.strReport.qryData.mc_outputFieldsXML)>
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				select *
				from [local].qryOutputFields
				where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
			</cfquery>
		</cfif>	

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left" <cfif arguments.strReport.frmShowPhotos is 1>colspan="2"</cfif>>Company / Contributor</th>
				<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
				<th class="text-right">Total Balance</th>
			</tr>
			</thead>
			<tbody>
			<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
				<cfset local.colspan = 6>
			<cfelse>
				<cfset local.colspan = 5>
			</cfif>
			<cfif arguments.strReport.frmShowPhotos is 1>
				<cfset local.colspan = local.colspan + 1>
			</cfif>
			</cfoutput>
			<cfoutput query="arguments.strReport.qryData">
				<cfif arguments.strReport.qryData.reportRow eq 1 and arguments.strReport.qryData.rowNum eq 1>
					<tr>
						<td colspan="#local.colspan#"><b>#arguments.strReport.qryData.Company#</b></td>
					</tr>
				</cfif>
				<cfif ListFindNoCase("2,3",arguments.strReport.qryData.reportRow)>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<cfif arguments.strReport.qryData.reportRow eq 1 and arguments.strReport.qryData.memberRowNum is 1>
					<tr>
						<cfif arguments.strReport.frmShowPhotos is 1>
							<td class="align-top pl-3" style="width:80px;">
								<cfif arguments.strReport.qryData.hasMemberPhotoThumb is 1>
									<cfif arguments.strReport.reportAction eq "screen">
										<img class="mc_memthumb" src="/memberphotosth/#LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									<cfelse>
										<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									</cfif>
								<cfelse>
									<cfif arguments.strReport.reportAction eq "screen">
										<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
									<cfelse>
										<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
									</cfif>
								</cfif>
							</td>
						</cfif>
						<td class="align-top <cfif arguments.strReport.frmShowPhotos is 0>pl-3</cfif>" <cfif arguments.strReport.frmShowPhotos is 1>colspan="2"</cfif>>
							<cfif arguments.strReport.reportAction eq "screen">
								<a href="#local.memberLink#&memberid=#arguments.strReport.qryData.memberid#" target="_blank"><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></a><br/>
							<cfelse>
								<b><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></b><br/>
							</cfif>
							
							<cfloop query="local.qryOutputFields">
								<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
									<cfset local.AddrToShow = arguments.strReport.qryData[local.qryOutputFields.dbfield][arguments.strReport.qryData.currentrow]>
									<cfloop condition="Find(', , ',local.AddrToShow)">
										<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
									</cfloop>
									<cfif len(local.AddrToShow)>
										#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
									</cfif>
								</cfif>
							</cfloop>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
									#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow])#<br/>
								<cfelseif len(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][arguments.strReport.qryData.currentrow])>
									<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
										#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow], "m/d/yyyy")#<br/>
									<cfelse>
										#local.qryOutputFieldsForLoop.fieldlabel#: #arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfif>
							</cfloop>
						</td>
					</tr>
				</cfif>
				<tr>
					<cfif arguments.strReport.frmShowPhotos is 1>
						<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif>">&nbsp;</td>
					</cfif>
					<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif> <cfif arguments.strReport.qryData.reportRow is 1>pl-5</cfif>">
						<cfif arguments.strReport.qryData.reportRow is 1>
							#arguments.strReport.qryData.ProgramName#<br/>
							<cfif len(arguments.strReport.qryData.campaignName)>#arguments.strReport.qryData.campaignName#<br/></cfif>
							<cfif len(arguments.strReport.qryData.EndDate)>
								#DateFormat(arguments.strReport.qryData.StartDate,"m/d/yyyy")# to #DateFormat(arguments.strReport.qryData.EndDate,"m/d/yyyy")#
							<cfelse>							
								From #DateFormat(arguments.strReport.qryData.StartDate,"m/d/yyyy")#
							</cfif>
							(#arguments.strReport.qryData.Status#)<br/>
							#arguments.strReport.qryData.Frequency# &bull; #arguments.strReport.qryData.TotalInstallmentCount# installment<cfif arguments.strReport.qryData.TotalInstallmentCount gt 1>s</cfif><br/>
							<cfif len(arguments.strReport.qryData.LastPaymentDate)>
								Last Payment: #DateFormat(arguments.strReport.qryData.LastPaymentDate,"m/d/yyyy")#<br/>
							</cfif>
							First Installment: #DollarFormat(arguments.strReport.qryData.FirstInstallmentAmount)#<br/>
							<cfif arguments.strReport.qryData.RecurringInstallmentAmount gt 0>
								Recurring Installment: #DollarFormat(arguments.strReport.qryData.RecurringInstallmentAmount)#<br/>
							</cfif>
							<cfif len(arguments.strReport.qryData.PledgedAmount)>
								Pledged Total Value: #DollarFormat(arguments.strReport.qryData.PledgedAmount)#<br/>
							</cfif>
							<cfif structKeyExists(arguments.strReport,"qryCustomColumns")>
								<cfloop query="arguments.strReport.qryCustomColumns">
									<cfif structKeyExists(arguments.strReport.qryData,arguments.strReport.qryCustomColumns.fieldReference) and len(arguments.strReport.qryData[arguments.strReport.qryCustomColumns.fieldReference][arguments.strReport.qryData.currentrow])>
										#arguments.strReport.qryCustomColumns.fieldReference#: #arguments.strReport.qryData[arguments.strReport.qryCustomColumns.fieldReference][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfloop>
							</cfif>
						<cfelseif arguments.strReport.qryData.reportRow is 3>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.BalanceDue)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBalance)#</td>
				</tr>
				<cfif arguments.strReport.qryData.reportRow neq 3>
					<tr><td colspan="#local.colspan#">&nbsp;</td></tr>
				</cfif>
			</cfoutput>
			<cfoutput>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_4" access="private" output="false" returntype="string">
		<cfargument name="qryData" type="query" required="true">
		<cfargument name="reportAction" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left">Program</th>
				<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
			</tr>
			</thead>
			<tbody>
			<cfloop query="arguments.qryData">
				<cfif arguments.qryData.reportRow eq 2>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<tr>
					<td class="align-top #local.ttlRow#">
						<cfif arguments.qryData.reportRow eq 1>
							#arguments.qryData.ProgramName#
						<cfelseif arguments.qryData.reportRow eq 2>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.qryData.BalanceDue)#</td>
				</tr>
			</cfloop>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_5" access="private" output="false" returntype="string">
		<cfargument name="strReport" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit') & "&tab=contributions">
		<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(arguments.strReport.orgcode) & "/memberphotosth/">

		<cfif arguments.strReport.qryData.recordcount>
			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=arguments.strReport.qryData.mc_outputFieldsXML)>
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				select *
				from [local].qryOutputFields
				where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left" <cfif arguments.strReport.frmShowPhotos is 1>colspan="2"</cfif>>Program / Contributor</th>
				<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
				<th class="text-right">Total Balance</th>
			</tr>
			</thead>
			<tbody>
			<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
				<cfset local.colspan = 6>
			<cfelse>
				<cfset local.colspan = 5>
			</cfif>
			<cfif arguments.strReport.frmShowPhotos is 1>
				<cfset local.colspan = local.colspan + 1>
			</cfif>
			</cfoutput>
			<cfoutput query="arguments.strReport.qryData">
				<cfif arguments.strReport.qryData.reportRow eq 1 and arguments.strReport.qryData.rowNum eq 1>
					<tr>
						<td colspan="#local.colspan#"><b>#arguments.strReport.qryData.ProgramName#</b></td>
					</tr>
				</cfif>
				<cfif ListFindNoCase("2,3",arguments.strReport.qryData.reportRow)>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<cfif arguments.strReport.qryData.reportRow eq 1 and arguments.strReport.qryData.memberRowNum is 1>
					<tr>
						<cfif arguments.strReport.frmShowPhotos is 1>
							<td class="align-top pl-3" style="width:80px;">
								<cfif arguments.strReport.qryData.hasMemberPhotoThumb is 1>
									<cfif arguments.strReport.reportAction eq "screen">
										<img class="mc_memthumb" src="/memberphotosth/#LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									<cfelse>
										<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									</cfif>
								<cfelse>
									<cfif arguments.strReport.reportAction eq "screen">
										<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
									<cfelse>
										<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
									</cfif>
								</cfif>
							</td>
						</cfif>
						<td class="align-top <cfif arguments.strReport.frmShowPhotos is 0>pl-3</cfif>" <cfif arguments.strReport.frmShowPhotos is 1>colspan="#local.colspan - 1#"<cfelseif arguments.strReport.frmShowPhotos is 0>colspan="#local.colspan#"</cfif>>
							<cfif arguments.strReport.reportAction eq "screen">
								<a href="#local.memberLink#&memberid=#arguments.strReport.qryData.memberid#" target="_blank"><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></a><br/>
							<cfelse>
								<b><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></b><br/>
							</cfif>
							<cfif arguments.strReport.frmShowCompany is 1 AND len(arguments.strReport.qryData.company)>#arguments.strReport.qryData.company#<br/></cfif>
							
							<cfloop query="local.qryOutputFields">
								<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
									<cfset local.AddrToShow = arguments.strReport.qryData[local.qryOutputFields.dbfield][arguments.strReport.qryData.currentrow]>
									<cfloop condition="Find(', , ',local.AddrToShow)">
										<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
									</cfloop>
									<cfif len(local.AddrToShow)>
										#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
									</cfif>
								</cfif>
							</cfloop>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
									#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow])#<br/>
								<cfelseif len(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][arguments.strReport.qryData.currentrow])>
									<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
										#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow], "m/d/yy")#<br/>
									<cfelse>
										#local.qryOutputFieldsForLoop.fieldlabel#: #arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfif>
							</cfloop>
						</td>
					</tr>
				</cfif>
				<tr>
					<cfif arguments.strReport.frmShowPhotos is 1>
						<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif>">&nbsp;</td>
					</cfif>
					<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif> <cfif arguments.strReport.qryData.reportRow is 1>pl-5</cfif>">
						<cfif arguments.strReport.qryData.reportRow is 1>
							<cfif len(arguments.strReport.qryData.campaignName)>#arguments.strReport.qryData.campaignName#<br/></cfif>
							<cfif len(arguments.strReport.qryData.EndDate)>
								#DateFormat(arguments.strReport.qryData.StartDate,"m/d/yy")# to #DateFormat(arguments.strReport.qryData.EndDate,"m/d/yy")#
							<cfelse>							
								From #DateFormat(arguments.strReport.qryData.StartDate,"m/d/yy")#
							</cfif>
							(#arguments.strReport.qryData.Status#)<br/>
							#arguments.strReport.qryData.Frequency# &bull; #arguments.strReport.qryData.TotalInstallmentCount# installment<cfif arguments.strReport.qryData.TotalInstallmentCount gt 1>s</cfif><br/>
							<cfif len(arguments.strReport.qryData.LastPaymentDate)>
								Last Payment: #DateFormat(arguments.strReport.qryData.LastPaymentDate,"m/d/yy")#<br/>
							</cfif>
							First Installment: #DollarFormat(arguments.strReport.qryData.FirstInstallmentAmount)#<br/>
							<cfif arguments.strReport.qryData.RecurringInstallmentAmount gt 0>
								Recurring Installment: #DollarFormat(arguments.strReport.qryData.RecurringInstallmentAmount)#<br/>
							</cfif>
							<cfif len(arguments.strReport.qryData.PledgedAmount)>
								Pledged Total Value: #DollarFormat(arguments.strReport.qryData.PledgedAmount)#<br/>
							</cfif>
							<cfif structKeyExists(arguments.strReport,"qryCustomColumns")>
								<cfloop query="arguments.strReport.qryCustomColumns">
									<cfif structKeyExists(arguments.strReport.qryData,arguments.strReport.qryCustomColumns.fieldReference) and len(arguments.strReport.qryData[arguments.strReport.qryCustomColumns.fieldReference][arguments.strReport.qryData.currentrow])>
										#arguments.strReport.qryCustomColumns.fieldReference#: #arguments.strReport.qryData[arguments.strReport.qryCustomColumns.fieldReference][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfloop>
							</cfif>
						<cfelseif arguments.strReport.qryData.reportRow is 3>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.BalanceDue)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBalance)#</td>
				</tr>
				<cfif arguments.strReport.qryData.reportRow neq 3>
					<tr><td colspan="#local.colspan#">&nbsp;</td></tr>
				</cfif>
			</cfoutput>
			<cfoutput>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_6" access="private" output="false" returntype="string">
		<cfargument name="strReport" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit') & "&tab=contributions">
		<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(arguments.strReport.orgcode) & "/memberphotosth/">

		<cfif arguments.strReport.qryData.recordcount>
			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=arguments.strReport.qryData.mc_outputFieldsXML)>
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				select *
				from [local].qryOutputFields
				where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
			</cfquery>
		</cfif>	

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left" <cfif arguments.strReport.frmShowPhotos is 1>colspan="2"</cfif>>Contributor</th>
				<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
			</tr>
			</thead>
			<tbody>
			</cfoutput>
			<cfoutput query="arguments.strReport.qryData">
				<cfif arguments.strReport.qryData.reportRow is 2>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<tr>
					<cfif arguments.strReport.frmShowPhotos is 1 and arguments.strReport.qryData.reportRow is 1>
						<td class="align-top pl-3" style="width:80px;">
							<cfif arguments.strReport.qryData.hasMemberPhotoThumb is 1>
								<cfif arguments.strReport.reportAction eq "screen">
									<img class="mc_memthumb" src="/memberphotosth/#LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
								<cfelse>
									<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
								</cfif>
							<cfelse>
								<cfif arguments.strReport.reportAction eq "screen">
									<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
								<cfelse>
									<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
								</cfif>
							</cfif>
						</td>
					</cfif>
					<td class="align-top #local.ttlRow#" <cfif arguments.strReport.frmShowPhotos is 1 and arguments.strReport.qryData.reportRow is 2>colspan="2"</cfif>>
						<cfif arguments.strReport.qryData.reportRow is 1>
							<cfif arguments.strReport.reportAction eq "screen">
								<a href="#local.memberLink#&memberid=#arguments.strReport.qryData.memberid#" target="_blank"><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></a><br/>
							<cfelse>
								<b><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></b><br/>
							</cfif>
							<cfif arguments.strReport.frmShowCompany is 1 AND len(arguments.strReport.qryData.company)>#arguments.strReport.qryData.company#<br/></cfif>
							
							<cfloop query="local.qryOutputFields">
								<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
									<cfset local.AddrToShow = arguments.strReport.qryData[local.qryOutputFields.dbfield][arguments.strReport.qryData.currentrow]>
									<cfloop condition="Find(', , ',local.AddrToShow)">
										<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
									</cfloop>
									<cfif len(local.AddrToShow)>
										#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
									</cfif>
								</cfif>
							</cfloop>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
									#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow])#<br/>
								<cfelseif len(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][arguments.strReport.qryData.currentrow])>
									<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
										#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow], "m/d/yyyy")#<br/>
									<cfelse>
										#local.qryOutputFieldsForLoop.fieldlabel#: #arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfif>
							</cfloop>
						<cfelseif arguments.strReport.qryData.reportRow is 2>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.BalanceDue)#</td>
				</tr>
			</cfoutput>
			<cfoutput>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_7" access="private" output="false" returntype="string">
		<cfargument name="strReport" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit') & "&tab=contributions">
		<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(arguments.strReport.orgcode) & "/memberphotosth/">

		<cfif arguments.strReport.qryData.recordcount>
			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=arguments.strReport.qryData.mc_outputFieldsXML)>
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				select *
				from [local].qryOutputFields
				where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
			</cfquery>
		</cfif>	

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left" <cfif arguments.strReport.frmShowPhotos is 1>colspan="2"</cfif>>Contributor / Program</th>
				<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
			</tr>
			</thead>
			<tbody>
			<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
				<cfset local.colspan = 5>
			<cfelse>
				<cfset local.colspan = 4>
			</cfif>
			<cfif arguments.strReport.frmShowPhotos is 1>
				<cfset local.colspan = local.colspan + 1>
			</cfif>
			</cfoutput>
			<cfoutput query="arguments.strReport.qryData">
				<cfif ListFindNoCase("2,3",arguments.strReport.qryData.reportRow)>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<cfif arguments.strReport.qryData.reportRow eq 1 and arguments.strReport.qryData.rowNum is 1>
					<tr>
						<cfif arguments.strReport.frmShowPhotos is 1>
							<td class="align-top pl-3" style="width:80px;">
								<cfif arguments.strReport.qryData.hasMemberPhotoThumb is 1>
									<cfif arguments.strReport.reportAction eq "screen">
										<img class="mc_memthumb" src="/memberphotosth/#LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									<cfelse>
										<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									</cfif>
								<cfelse>
									<cfif arguments.strReport.reportAction eq "screen">
										<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
									<cfelse>
										<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
									</cfif>
								</cfif>
							</td>
						</cfif>
						<td class="align-top" colspan="<cfif arguments.strReport.frmShowPhotos is 1>#local.colspan - 1#<cfelseif arguments.strReport.frmShowPhotos is 0>#local.colspan#</cfif>">
							<cfif arguments.strReport.reportAction eq "screen">
								<a href="#local.memberLink#&memberid=#arguments.strReport.qryData.memberid#" target="_blank"><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></a><br/>
							<cfelse>
								<b><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></b><br/>
							</cfif>
							<cfif arguments.strReport.frmShowCompany is 1 AND len(arguments.strReport.qryData.company)>#arguments.strReport.qryData.company#<br/></cfif>
							
							<cfloop query="local.qryOutputFields">
								<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
									<cfset local.AddrToShow = arguments.strReport.qryData[local.qryOutputFields.dbfield][arguments.strReport.qryData.currentrow]>
									<cfloop condition="Find(', , ',local.AddrToShow)">
										<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
									</cfloop>
									<cfif len(local.AddrToShow)>
										#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
									</cfif>
								</cfif>
							</cfloop>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
									#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow])#<br/>
								<cfelseif len(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][arguments.strReport.qryData.currentrow])>
									<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
										#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow], "m/d/yyyy")#<br/>
									<cfelse>
										#local.qryOutputFieldsForLoop.fieldlabel#: #arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfif>
							</cfloop>
						</td>
					</tr>
				</cfif>
				<tr>
					<cfif arguments.strReport.frmShowPhotos is 1>
						<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif>">&nbsp;</td>
					</cfif>
					<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif> <cfif arguments.strReport.qryData.reportRow is 1>pl-5</cfif>">
						<cfif arguments.strReport.qryData.reportRow is 1>
							#arguments.strReport.qryData.ProgramName#
						<cfelseif arguments.strReport.qryData.reportRow is 3>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.BalanceDue)#</td>
				</tr>
				<cfif arguments.strReport.qryData.reportRow neq 3>
					<tr><td colspan="#local.colspan#">&nbsp;</td></tr>
				</cfif>
			</cfoutput>
			<cfoutput>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="screenReport_8" access="private" output="false" returntype="string">
		<cfargument name="strReport" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit') & "&tab=contributions">
		<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(arguments.strReport.orgcode) & "/memberphotosth/">

		<cfif arguments.strReport.qryData.recordcount>
			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=arguments.strReport.qryData.mc_outputFieldsXML)>
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				select *
				from [local].qryOutputFields
				where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
			</cfquery>
		</cfif>	

		<cfsavecontent variable="local.data">
			<cfoutput>
			<thead>
			<tr>
				<th class="text-left" <cfif arguments.strReport.frmShowPhotos is 1>colspan="2"</cfif>>Contributor / Program</th>
				<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
					<th class="text-right">Pledged Range Value</th>
				</cfif>
				<th class="text-right">Billed Total</th>
				<th class="text-right">Paid Total</th>
				<th class="text-right">Balance Due</th>
			</tr>
			</thead>
			<tbody>
			<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
				<cfset local.colspan = 5>
			<cfelse>
				<cfset local.colspan = 4>
			</cfif>
			<cfif arguments.strReport.frmShowPhotos is 1>
				<cfset local.colspan = local.colspan + 1>
			</cfif>
			</cfoutput>
			<cfoutput query="arguments.strReport.qryData">
				<cfif ListFindNoCase("2,3",arguments.strReport.qryData.reportRow)>
					<cfset local.ttlRow = "font-weight-bold">
				<cfelse>
					<cfset local.ttlRow = "">
				</cfif>
				<cfif arguments.strReport.qryData.reportRow eq 1 and arguments.strReport.qryData.rowNum eq 1>
					<tr>
						<cfif arguments.strReport.frmShowPhotos is 1>
							<td class="align-top pl-3" style="width:80px;">
								<cfif arguments.strReport.qryData.hasMemberPhotoThumb is 1>
									<cfif arguments.strReport.reportAction eq "screen">
										<img class="mc_memthumb" src="/memberphotosth/#LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									<cfelse>
										<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(arguments.strReport.qryData.MemberNumber)#.jpg">
									</cfif>
								<cfelse>
									<cfif arguments.strReport.reportAction eq "screen">
										<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
									<cfelse>
										<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
									</cfif>
								</cfif>
							</td>
						</cfif>
						<td class="align-top">
							<cfif arguments.strReport.reportAction eq "screen">
								<a href="#local.memberLink#&memberid=#arguments.strReport.qryData.memberid#" target="_blank"><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></a><br/>
							<cfelse>
								<b><cfif arguments.strReport.frmShowMemberNumber is 1>#arguments.strReport.qryData["Extended MemberNumber"]#<cfelse>#arguments.strReport.qryData["Extended Name"]#</cfif></b><br/>
							</cfif>
							<cfif arguments.strReport.frmShowCompany is 1 AND len(arguments.strReport.qryData.company)>#arguments.strReport.qryData.company#<br/></cfif>
							
							<cfloop query="local.qryOutputFields">
								<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
									<cfset local.AddrToShow = arguments.strReport.qryData[local.qryOutputFields.dbfield][arguments.strReport.qryData.currentrow]>
									<cfloop condition="Find(', , ',local.AddrToShow)">
										<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
									</cfloop>
									<cfif len(local.AddrToShow)>
										#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
									</cfif>
								</cfif>
							</cfloop>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
									#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow])#<br/>
								<cfelseif len(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][arguments.strReport.qryData.currentrow])>
									<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
										#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow], "m/d/yy")#<br/>
									<cfelse>
										#local.qryOutputFieldsForLoop.fieldlabel#: #arguments.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfif>
							</cfloop>
						</td>
					</tr>
				</cfif>
				<tr>
					<cfif arguments.strReport.frmShowPhotos is 1>
						<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif>">&nbsp;</td>
					</cfif>
					<td class="align-top <cfif arguments.strReport.qryData.reportRow is 3>#local.ttlRow#</cfif> <cfif arguments.strReport.qryData.reportRow is 1>pl-5</cfif>">
						<cfif arguments.strReport.qryData.reportRow is 1>
							#arguments.strReport.qryData.ProgramName#<br/>
							<cfif len(arguments.strReport.qryData.campaignName)>#arguments.strReport.qryData.campaignName#<br/></cfif>
							<cfif len(arguments.strReport.qryData.EndDate)>
								#DateFormat(arguments.strReport.qryData.StartDate,"m/d/yy")# to #DateFormat(arguments.strReport.qryData.EndDate,"m/d/yy")#
							<cfelse>							
								From #DateFormat(arguments.strReport.qryData.StartDate,"m/d/yy")#
							</cfif>
							(#arguments.strReport.qryData.Status#)<br/>
							#arguments.strReport.qryData.Frequency# &bull; #arguments.strReport.qryData.TotalInstallmentCount# installment<cfif arguments.strReport.qryData.TotalInstallmentCount gt 1>s</cfif><br/>
							<cfif len(arguments.strReport.qryData.LastPaymentDate)>
								Last Payment: #DateFormat(arguments.strReport.qryData.LastPaymentDate,"m/d/yy")#<br/>
							</cfif>
							First Installment: #DollarFormat(arguments.strReport.qryData.FirstInstallmentAmount)#<br/>
							<cfif arguments.strReport.qryData.RecurringInstallmentAmount gt 0>
								Recurring Installment: #DollarFormat(arguments.strReport.qryData.RecurringInstallmentAmount)#<br/>
							</cfif>
							<cfif len(arguments.strReport.qryData.PledgedAmount)>
								Pledged Total Value: #DollarFormat(arguments.strReport.qryData.PledgedAmount)#<br/>
							</cfif>
							<cfif structKeyExists(arguments.strReport,"qryCustomColumns")>
								<cfloop query="arguments.strReport.qryCustomColumns">
									<cfif structKeyExists(arguments.strReport.qryData,arguments.strReport.qryCustomColumns.fieldReference) and len(arguments.strReport.qryData[arguments.strReport.qryCustomColumns.fieldReference][arguments.strReport.qryData.currentrow])>
										#arguments.strReport.qryCustomColumns.fieldReference#: #arguments.strReport.qryData[arguments.strReport.qryCustomColumns.fieldReference][arguments.strReport.qryData.currentrow]#<br/>
									</cfif>
								</cfloop>
							</cfif>
						<cfelseif arguments.strReport.qryData.reportRow is 3>
							<b>Report Total</b>
						</cfif>
					</td>
					<cfif structKeyExists(arguments.strReport.qryData,"PledgedRangeValue")>
						<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.PledgedRangeValue)#</td>
					</cfif>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalBilled)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.TotalPaid)#</td>
					<td class="align-top text-right #local.ttlRow#">#dollarformat(arguments.strReport.qryData.BalanceDue)#</td>
				</tr>
				<cfif arguments.strReport.qryData.reportRow neq 3>
					<tr><td colspan="#local.colspan#">&nbsp;</td></tr>
				</cfif>
			</cfoutput>
			<cfoutput>
			</tbody>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.strReport = generateData(strSQLPrep=local.strSQLPrep, reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
			
			<cfscript>
			local.arrInitialReportSort = arrayNew(1);
			local.strTemp = { field='LastName', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='FirstName', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strReportQry = { qryReportFields=local.strReport.qryData, strQryResult=local.strReport.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="generateData" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.frmReportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>
		<cfset local.frmContribStartDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcontribstartdatefrom/text())")>
		<cfset local.frmContribStartDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcontribstartdateto/text())")>
		<cfset local.frmContribEndDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcontribenddatefrom/text())")>
		<cfset local.frmContribEndDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcontribenddateto/text())")>
		<cfset local.frmEnteredDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmentereddatefrom/text())")>
		<cfset local.frmEnteredDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmentereddateto/text())")>
		<cfset local.frmCPProgram = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcpprogram/text())")>
		<cfset local.frmCPCampaign = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcpcampaign/text())")>
		<cfset local.frmCPStatus = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcpstatus/text())")>
		<cfset local.frmCPFrequency = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcpfrequency/text())")>
		<cfset local.frmCPDuration = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcpduration/text())")>
		<cfset local.frmHasCard = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmhascard/text())")>
		<cfset local.frmFirstInstallFrom = rereplace(XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmfirstinstallfrom/text())"),'[^\d.]+','','ALL')>
		<cfset local.frmFirstInstallTo = rereplace(XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmfirstinstallto/text())"),'[^\d.]+','','ALL')>
		<cfset local.frmRecurringInstallFrom = rereplace(XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrecurringinstallfrom/text())"),'[^\d.]+','','ALL')>
		<cfset local.frmRecurringInstallTo = rereplace(XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrecurringinstallto/text())"),'[^\d.]+','','ALL')>
		<cfset local.frmlimittopastdue = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmlimittopastdue/text())")>
		<cfset local.frmLimitInstallFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmlimitinstallfrom/text())")>
		<cfset local.frmLimitInstallTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmlimitinstallto/text())")>
		<cfif arguments.reportAction eq "customcsv">
			<cfset local.mode = "export">
		<cfelse>
			<cfset local.mode = "report">
		</cfif>
		<cfset local.thisReportViewSupportsFieldSets = listFind("3,5,6,7,8,9",local.frmReportView) GT 0>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strReturn.qryData" result="local.strReturn.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">,
					@outputFieldsXML xml;

				<cfif len(arguments.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3;
				IF OBJECT_ID('tempdb..###local.tempTableName#_4') IS NOT NULL
					DROP TABLE ###local.tempTableName#_4;
				IF OBJECT_ID('tempdb..###local.tempTableName#_5') IS NOT NULL
					DROP TABLE ###local.tempTableName#_5;
				IF OBJECT_ID('tempdb..###local.tempTableName#_6') IS NOT NULL
					DROP TABLE ###local.tempTableName#_6;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_7') IS NOT NULL
					DROP TABLE #####local.tempTableName#_7;
				IF OBJECT_ID('tempdb..###local.tempTableName#_8') IS NOT NULL
					DROP TABLE ###local.tempTableName#_8;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_9') IS NOT NULL
					DROP TABLE #####local.tempTableName#_9;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_10') IS NOT NULL
					DROP TABLE #####local.tempTableName#_10;
				IF OBJECT_ID('tempdb..##tmpProgramDistCodes') IS NOT NULL
					DROP TABLE ##tmpProgramDistCodes;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				IF OBJECT_ID('tempdb..##tmpTxnForContributions') IS NOT NULL
					DROP TABLE ##tmpTxnForContributions;
				IF OBJECT_ID('tempdb..##tmpTxnForContributionsResult') IS NOT NULL
					DROP TABLE ##tmpTxnForContributionsResult;

				CREATE TABLE ###local.tempTableName#_2 (memberID int, lastname varchar(75), firstname varchar(75), membernumber varchar(50), Company varchar(200), 
					hasMemberPhotoThumb bit, contributionID int PRIMARY KEY, programid int, ProgramName varchar(200), CampaignName varchar(400), RateName varchar(200), 
					contribDate datetime, startDate date, endDate date, status varchar(20), Frequency varchar(20), cancellationDate date, isPastDue bit);
				CREATE TABLE ###local.tempTableName#_3 (contributionID int PRIMARY KEY, totalPledgeFirst decimal(16,2), totalPledgeRecurring decimal(16,2), 
					pledgedValue decimal(16,2));
				CREATE TABLE ###local.tempTableName#_4 (contributionID int PRIMARY KEY, TotalInstallmentCount int, TotalBilled decimal(16,2),
					TotalPaid decimal(16,2), BalanceDue decimal(16,2), TotalBalance decimal(16,2), LastPaymentDate datetime, PledgedRangeValue decimal(16,2));
				CREATE TABLE ###local.tempTableName#_5 (contributionID int, columnName varchar(100), columnValue varchar(max));
				CREATE TABLE ###local.tempTableName#_8 (contributionID int, columnName varchar(100), columnValue varchar(max));
				CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
				CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);
				CREATE TABLE ##tmpProgramDistCodes (distribID int, distCode varchar(20));
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);	
				CREATE TABLE ##tmpTxnForContributions (contributionID int PRIMARY KEY);
				CREATE TABLE ##tmpTxnForContributionsResult (transactionID int, contributionID int, ownedByOrgID int, recordedOnSiteID int, statusID int, detail varchar(500), 
					parentTransactionID int, amount decimal(18,2), dateRecorded datetime, transactionDate datetime, assignedToMemberID int, recordedByMemberID int,
					statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int,
					INDEX tmpTxnForContributionsResult_statusID_typeID (statusID,typeID,contributionID,transactionDate));

				declare @contributionIDList varchar(max), @fundList varchar(max), @colList varchar(max), @dynSQL varchar(max), @nowDate date = getdate();

				insert into ###local.tempTableName#_2
				select distinct m.memberID, m.lastname, m.firstname, m.memberNumber, m.company, m.hasMemberPhotoThumb, c.contributionID, cp.programid, cp.programName, cpc.campaignName, 
					r.rateName, c.contribDate, c.startDate, c.endDate, cps.statusName, f.frequency, 
					case when cps.statusCode = 'C' then csh.updateDate else c.cancellationDate end as cancellationDate,
					0 as isPastDue
				from dbo.cp_contributions as c
				inner join dbo.cp_programs as cp on cp.programID = c.programID
				inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
				inner join dbo.cp_frequencies as f on f.frequencyID = c.frequencyID
				inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
				inner join (select cpsh.contributionID, cpsh.statusID, max(cpsh.updateDate) as updateDate from dbo.cp_statusHistory cpsh group by cpsh.contributionID, cpsh.statusID) as csh on csh.contributionID = c.contributionID and csh.statusID = cps.statusID
				inner join dbo.ams_members as mOrig on mOrig.orgID = @orgID and mOrig.memberID = c.memberID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mOrig.activeMemberID and m.isProtected = 0
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
				left outer join dbo.cp_rates as r on r.rateID = c.rateID
				left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
				where ai.siteID = #local.mc_siteInfo.siteID#
				<cfif len(local.frmCPProgram)>
					and cp.programID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.frmCPProgram#">)
				</cfif>
				<cfif ListLen(local.frmCPProgram) is 1 and len(local.frmCPCampaign)>
					and c.campaignID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.frmCPCampaign#">)
				</cfif>
				<cfif len(local.frmContribStartDateFrom)>
					and c.startDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmContribStartDateFrom#">
				</cfif>
				<cfif len(local.frmContribStartDateTo)>
					and c.startDate <= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmContribStartDateTo#">
				</cfif>
				<cfif len(local.frmContribEndDateFrom)>
					and c.endDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmContribEndDateFrom#">
				</cfif>
				<cfif len(local.frmContribEndDateTo)>
					and c.endDate <= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmContribEndDateTo#">
				</cfif>
				<cfif len(local.frmEnteredDateFrom)>
					and c.contribDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmEnteredDateFrom#">
				</cfif>
				<cfif len(local.frmEnteredDateTo)>
					and c.contribDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.frmEnteredDateTo# 23:59:59.997">
				</cfif>
				<cfif len(local.frmCPFrequency)>
					and c.frequencyID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.frmCPFrequency#">)
				</cfif>
				<cfif len(local.frmCPStatus)>
					and c.statusID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.frmCPStatus#">)
				</cfif>
				<cfif len(local.frmCPDuration)>
					and c.isPerpetual = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.frmCPDuration#">
				</cfif>
				<cfif len(local.frmHasCard)>
					<cfif local.frmHasCard eq 'Y'>
						and exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
					<cfelseif local.frmHasCard eq 'N'>
						and not exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
					</cfif>
				</cfif>;

				<cfif local.frmlimittopastdue is 1>
					UPDATE tmp2
					set tmp2.isPastDue = 1
					from ###local.tempTableName#_2 as tmp2
					cross apply dbo.fn_cp_contributionTransactions(tmp2.contributionID) as ct
					inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ct.transactionID
					inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
					inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
					where invs.status in ('Closed','Delinquent')
					and i.dateDue < @nowDate;

					DELETE FROM ###local.tempTableName#_2 WHERE isPastDue = 0;
				</cfif>

				<!--- get first installment amount, recurring installment amount of the filtered contributions --->
				select @contributionIDList = COALESCE(@contributionIDList + ',','') + cast(contributionID as varchar(10)) from ###local.tempTableName#_2;
				INSERT INTO ###local.tempTableName#_3 (contributionID, totalPledgeFirst, totalPledgeRecurring, pledgedValue)
				EXEC dbo.cp_getContributionAmountSplit @contributionIDList=@contributionIDList;

				insert into ###local.tempTableName#_4 (contributionID, TotalInstallmentCount)
				select tmp3.contributionID, count(distinct cps.dueDate) as TotalInstallmentCount
				from ###local.tempTableName#_3 as tmp3
				inner join dbo.cp_contributionSchedule as cps on cps.contributionID = tmp3.contributionID
				left outer join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.scheduleID = cps.scheduleID and ti.isActive = 1
				where 1=1 
				<cfif len(local.frmFirstInstallFrom)>
					and tmp3.totalPledgeFirst >= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.frmFirstInstallFrom#">
				</cfif>
				<cfif len(local.frmFirstInstallTo)>
					and tmp3.totalPledgeFirst <= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.frmFirstInstallTo#">
				</cfif>
				<cfif len(local.frmRecurringInstallFrom)>
					and tmp3.totalPledgeRecurring >= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.frmRecurringInstallFrom#">
				</cfif>
				<cfif len(local.frmRecurringInstallTo)>
					and tmp3.totalPledgeRecurring <= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.frmRecurringInstallTo#">
				</cfif>
				group by tmp3.contributionID;

				insert into ##tmpTxnForContributions (contributionID)
				select contributionID
				from ###local.tempTableName#_4;

				EXEC dbo.cp_getContributionTransactionsBulk;

				UPDATE tmp4
				set tmp4.LastPaymentDate = (
					select max(transactionDate)
					from ##tmpTxnForContributionsResult
					where contributionID = tmp4.contributionID
					and statusID = 1
					and typeID = 2
				)
				from ###local.tempTableName#_4 as tmp4;

				UPDATE tmp4
				set tmp4.TotalBilled = tmp.totalBilled
				from ###local.tempTableName#_4 as tmp4
				cross apply (
					select sum(case 
						when i.dateBilled <= @nowDate then it.cache_invoiceAmountAfterAdjustment 
						when i.dateBilled > @nowDate then it.cache_activePaymentAllocatedAmount
						else 0 end) as totalBilled
					from ##tmpTxnForContributionsResult as ct
					inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ct.transactionID
					inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID and i.statusID in (3,4,5)
					where ct.contributionID = tmp4.contributionID
				) tmp;

				UPDATE tmp4
				set tmp4.totalPaid = isnull(contribPaid.totalPaid,0),
					tmp4.balanceDue = isnull(tmp4.TotalBilled,0) + isnull(c.amtPaidOnCreate,0) - isnull(contribPaid.totalPaid,0),
					tmp4.totalBalance = case when isnull(tmp3.pledgedValue,0) <> 0 then tmp3.pledgedValue - isnull(contribPaid.totalPaid,0) else 0 end
				from ###local.tempTableName#_4 as tmp4
				inner join ###local.tempTableName#_3 as tmp3 on tmp3.contributionID = tmp4.contributionID
				inner join dbo.cp_contributions as c on c.contributionID = tmp4.contributionID
				cross apply dbo.fn_cp_totalFeeAndPaid(@orgID,tmp4.contributionID) as contribPaid;

				<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>
					IF OBJECT_ID('tempdb..##tmpSaleTIDs') IS NOT NULL
						DROP TABLE ##tmpSaleTIDs;
					CREATE TABLE ##tmpSaleTIDs (contributionID int, transactionID int);
					
					declare @t_Sale int = dbo.fn_tr_getTypeID('Sale');
					declare @tr_InstallOffsetTrans int = dbo.fn_tr_getRelationshipTypeID('InstallOffsetTrans');
					declare @tr_InstallSaleTrans int = dbo.fn_tr_getRelationshipTypeID('InstallSaleTrans');
					declare @tr_SplitSaleTrans int = dbo.fn_tr_getRelationshipTypeID('SplitSaleTrans');
					declare @frmLimitInstallFrom date = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmLimitInstallFrom#">;
					declare @frmLimitInstallTo date = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmLimitInstallTo#">;

					-- installments converted to sales
					insert into ##tmpSaleTIDs (contributionID, transactionID)
					select tmp2.contributionID, rs.transactionID
					from ###local.tempTableName#_2 as tmp2
					inner join dbo.cp_contributionSchedule as cs on cs.contributionID = tmp2.contributionID
					inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID
						and ti.scheduleID = cs.scheduleID
						and ti.isActive = 1
						and ti.isConverted = 1
						and ti.isPaidOnCreate = 0
						and ti.dueDate between @frmLimitInstallFrom and @frmLimitInstallTo
					inner join dbo.tr_relationships as rO on rO.orgID = @orgID and rO.typeID = @tr_InstallOffsetTrans and rO.appliedToTransactionID = ti.transactionID
					inner join dbo.tr_relationships as rS on rS.orgID = @orgID and rS.typeID = @tr_InstallSaleTrans and rS.appliedToTransactionID = rO.transactionID;

					-- other sales (splits)
					with tblReclass as (
						select contributionID, transactionID
						from ##tmpSaleTIDs
							union all
						select rt.contributionID, t.transactionID
						from dbo.tr_transactions as t
						inner join dbo.tr_relationships as tr on tr.orgID = @orgID 
							and tr.typeID = @tr_SplitSaleTrans
							and tr.transactionID = t.transactionID
						inner join tblReclass as rt on rt.transactionID = tr.appliedToTransactionID
						where t.ownedByOrgID = @orgID
						and t.typeID = @t_Sale
						and t.statusID = 1
					)
					insert into ##tmpSaleTIDs (contributionID, transactionID)
					select distinct contributionID, transactionID
					from tblReclass
					except
					select contributionID, transactionID
					from ##tmpSaleTIDs;

					UPDATE tmp4
					set tmp4.PledgedRangeValue = outerTmp.pledgedValue
					from ###local.tempTableName#_4 as tmp4
					INNER JOIN (
						select contributionID, sum(amount) as pledgedValue
						from (
							select tmp2.contributionID, t.amount
							from ###local.tempTableName#_2 as tmp2
							inner join dbo.cp_contributionSchedule as cs on cs.contributionID = tmp2.contributionID
							inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID 
								and ti.scheduleID = cs.scheduleID
								and ti.isActive = 1
								and ti.isConverted = 0
								and ti.isPaidOnCreate = 0
								and ti.dueDate between @frmLimitInstallFrom and @frmLimitInstallTo
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID
								and t.transactionID = ti.transactionID 
								and t.statusID = 1

							union all

							select tmp2.contributionID, t.amount
							from ###local.tempTableName#_2 as tmp2
							inner join dbo.cp_contributionSchedule as cs on cs.contributionID = tmp2.contributionID
							inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID
								and ti.scheduleID = cs.scheduleID
								and ti.isActive = 1
								and ti.isConverted = 1
								and ti.isPaidOnCreate = 1
								and ti.dueDate between @frmLimitInstallFrom and @frmLimitInstallTo
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID
								and t.transactionID = ti.transactionID 
								and t.statusID = 1

							union all

							select tmp.contributionID, sum(tFull.cache_amountAfterAdjustment)
							from ##tmpSaleTIDs as tmp
							cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,tmp.transactionID) as tFull
							group by tmp.contributionID
						) tmp
						group by contributionID
					) as outerTmp on outerTmp.contributionID = tmp4.contributionID;

					IF OBJECT_ID('tempdb..##tmpSaleTIDs') IS NOT NULL
						DROP TABLE ##tmpSaleTIDs;
				</cfif>

				<cfif ListFindNoCase("3,5,8,9",local.frmReportView)>
					<cfif ListLen(local.frmCPProgram) is 1>
						-- truncate temp field tables
						TRUNCATE TABLE ##tmp_CF_ItemIDs;
						TRUNCATE TABLE ##tmp_CF_FieldData;

						-- contribution program custom fields
						INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
						SELECT DISTINCT contributionID, 'ContributionProgram'
						FROM ###local.tempTableName#_2;

						EXEC dbo.cf_getFieldData;

						INSERT INTO ###local.tempTableName#_5 (contributionID, columnName, columnValue)
						SELECT fd.itemID, f.fieldReference, fd.fieldValue
						FROM ##tmp_CF_FieldData AS fd
						INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID;

						IF EXISTS (select 1 from ###local.tempTableName#_5) BEGIN
							set @colList = null;
							select @colList = COALESCE(@colList + ',','') + quoteName(columnName) from ###local.tempTableName#_5 group by columnName;

							set @dynSQL = 'select contributionID as f7ContributionID, ' + @colList + '
								into #####local.tempTableName#_7
								from (
									select contributionID, columnName, columnValue
									from ###local.tempTableName#_5
								) as outerTmp
								PIVOT (max(columnValue) FOR columnName in (' + @colList + ')) as pvt;';
							EXEC(@dynSQL);
						END 
						ELSE 
							select contributionID as f7ContributionID 
							into #####local.tempTableName#_7
							from ###local.tempTableName#_2;
					</cfif>

					<!--- Cross-Program Contribution Fields --->
					<cfif arguments.reportAction eq 'customcsv'>
						-- truncate temp field tables
						TRUNCATE TABLE ##tmp_CF_ItemIDs;
						TRUNCATE TABLE ##tmp_CF_FieldData;

						-- contribution role custom fields
						INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
						SELECT DISTINCT contributionID, 'ContributionRole'
						FROM ###local.tempTableName#_2;

						EXEC dbo.cf_getFieldData;

						INSERT INTO ###local.tempTableName#_8 (contributionID, columnName, columnValue)
						SELECT fd.itemID, f.fieldReference, fd.fieldValue
						FROM ##tmp_CF_FieldData AS fd
						INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID;

						IF EXISTS (select 1 from ###local.tempTableName#_8) BEGIN
							set @colList = null;
							select @colList = COALESCE(@colList + ',','') + quoteName(columnName) from ###local.tempTableName#_8 group by columnName;

							set @dynSQL = 'select contributionID as f9ContributionID, ' + @colList + '
								into #####local.tempTableName#_9
								from (
									select contributionID, columnName, columnValue
									from ###local.tempTableName#_8
								) as outerTmp
								PIVOT (max(columnValue) FOR columnName in (' + @colList + ')) as pvt;';
							EXEC(@dynSQL);
						END 
						ELSE 
							select contributionID as f9ContributionID 
							into #####local.tempTableName#_9
							from ###local.tempTableName#_2;

						INSERT INTO ##tmpProgramDistCodes (distribID, distCode)
						select distinct cd.distribID, cd.distCode
						from ###local.tempTableName#_2 as tmp
						inner join dbo.cp_distributions as cd on cd.programID = tmp.programID;

						select @fundList = coalesce(@fundList + ',','') + quoteName(distCode) from ##tmpProgramDistCodes group by distCode;
						IF EXISTS (select 1 from ##tmpProgramDistCodes) BEGIN
							set @dynSQL = 'select contributionID as f10contributionID, ' + @fundList + '
								into #####local.tempTableName#_10
								from (
									select tmp.contributionID, tmppdc.distCode, cd.distAmount
									from ###local.tempTableName#_2 as tmp
									inner join dbo.cp_contributionDistributions as cd on cd.contributionID = tmp.contributionID
									inner join ##tmpProgramDistCodes as tmppdc on tmppdc.distribID = cd.distribID
								) as outerTmp
								PIVOT (sum(distAmount) FOR distCode in (' + @fundList + ')) as pvt;';
							EXEC(@dynSQL);
						END 
						ELSE 
							select contributionID as f10ContributionID 
							into #####local.tempTableName#_10
							from ###local.tempTableName#_2;
					</cfif>
				</cfif>

				<cfif local.thisReportViewSupportsFieldSets>
					-- get members fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.ovNameFormat#">,
						@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.ovMaskEmails#">,
						@membersTableName='###local.tempTableName#_2', @membersResultTableName='##tmpMembersFS', 
						@linkedMembers=0, @mode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.mode#">, 
						@outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				</cfif>

				<cfif local.frmReportView eq 1>
					CREATE TABLE ###local.tempTableName# (Company varchar(200), TotalBilled decimal(16,2), TotalPaid decimal(16,2), 
						BalanceDue decimal(16,2), <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue decimal(16,2), </cfif>
						reportRow int);
					INSERT INTO ###local.tempTableName# (Company, TotalBilled, TotalPaid, BalanceDue, 
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
					select case when len(tmp2.Company) = 0 then '[No company listed]' else tmp2.Company end as Company, 
						sum(tmp4.TotalBilled) as TotalBilled, sum(tmp4.TotalPaid) as TotalPaid, sum(tmp4.BalanceDue) as BalanceDue,
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(tmp4.PledgedRangeValue) as PledgedRangeValue,</cfif>
						1 as reportRow
					from ###local.tempTableName#_2 as tmp2
					inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
					group by tmp2.Company;

				<cfelseif local.frmReportView eq 2>
					CREATE TABLE ###local.tempTableName# (Company varchar(200), ProgramName varchar(200), TotalBilled decimal(16,2), TotalPaid decimal(16,2), 
						BalanceDue decimal(16,2), <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue decimal(16,2), </cfif>
						reportRow int);
					INSERT INTO ###local.tempTableName# (Company, ProgramName, TotalBilled, TotalPaid, BalanceDue, 
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
					select case when len(tmp2.Company) = 0 then '[No company listed]' else tmp2.Company end as Company, tmp2.ProgramName,
						sum(tmp4.TotalBilled) as TotalBilled, sum(tmp4.TotalPaid) as TotalPaid, sum(tmp4.BalanceDue) as BalanceDue,
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(tmp4.PledgedRangeValue) as PledgedRangeValue,</cfif>
						1 as reportRow
					from ###local.tempTableName#_2 as tmp2
					inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
					group by tmp2.Company, tmp2.ProgramName;

				<cfelseif ListFindNoCase("3,5,8",local.frmReportView)>
					<cfif arguments.reportAction eq 'customcsv'>
						select tmp2.LastName, tmp2.FirstName, tmp2.MemberNumber, case when len(tmp2.Company) = 0 then '[No company listed]' else tmp2.Company end as Company, 
							tmp2.ProgramName, tmp2.CampaignName, tmp2.RateName, tmp2.StartDate, tmp2.EndDate, tmp2.Status, tmp2.Frequency, tmp2.CancellationDate,
							tmp3.totalPledgeFirst as FirstInstallmentAmount, tmp3.totalPledgeRecurring as RecurringInstallmentAmount, 
							tmp3.pledgedValue as PledgedAmount, tmp4.TotalInstallmentCount, tmp4.TotalBilled, tmp4.TotalPaid, tmp4.BalanceDue, 
							tmp4.TotalBalance, tmp4.LastPaymentDate
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>, tmp4.PledgedRangeValue</cfif>
							<cfif listLen(local.frmCPProgram) is 1>, tmp7.*</cfif>
							, tmp9.*, m.*
						into ###local.tempTableName#
						from ###local.tempTableName#_2 as tmp2
						inner join ###local.tempTableName#_3 as tmp3 on tmp3.contributionID = tmp2.contributionID
						inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
						<cfif listLen(local.frmCPProgram) is 1>
							left outer join #####local.tempTableName#_7 as tmp7 on tmp7.f7ContributionID = tmp2.contributionID
						</cfif>
						left outer join #####local.tempTableName#_9 as tmp9 on tmp9.f9ContributionID = tmp2.contributionID
						INNER JOIN ##tmpMembersFS AS m on tmp2.memberID = m.memberID;

					<cfelse>
						CREATE TABLE ###local.tempTableName# (contributionID int, LastName varchar(75), FirstName varchar(75), MemberNumber varchar(50),
							memberID int, Company varchar(200), hasMemberPhotoThumb bit, ProgramName varchar(200), CampaignName varchar(400), 
							StartDate date, EndDate date, Status varchar(20), Frequency varchar(20), CancellationDate date, 
							FirstInstallmentAmount decimal(16,2), RecurringInstallmentAmount decimal(16,2), PledgedAmount decimal(16,2),
							TotalInstallmentCount int, TotalBilled decimal(16,2), TotalPaid decimal(16,2), BalanceDue decimal(16,2), 
							TotalBalance decimal(16,2), LastPaymentDate datetime, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue decimal(16,2), </cfif>
							reportRow int);
						INSERT INTO ###local.tempTableName# (contributionID, LastName, FirstName, MemberNumber, memberID, Company, hasMemberPhotoThumb,
							ProgramName, CampaignName, StartDate, EndDate, Status, Frequency, CancellationDate, FirstInstallmentAmount, 
							RecurringInstallmentAmount, PledgedAmount, TotalInstallmentCount, TotalBilled, TotalPaid, BalanceDue,
							TotalBalance, LastPaymentDate, <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select tmp2.contributionID, tmp2.LastName, tmp2.FirstName, tmp2.MemberNumber, tmp2.memberID, 
							case when len(tmp2.Company) = 0 then '[No company listed]' else tmp2.Company end as Company, tmp2.hasMemberPhotoThumb,
							tmp2.ProgramName, tmp2.CampaignName, tmp2.StartDate, tmp2.EndDate, tmp2.Status, tmp2.Frequency, tmp2.CancellationDate,
							tmp3.totalPledgeFirst as FirstInstallmentAmount, tmp3.totalPledgeRecurring as RecurringInstallmentAmount, 
							tmp3.pledgedValue as PledgedAmount, tmp4.TotalInstallmentCount, tmp4.TotalBilled, tmp4.TotalPaid, tmp4.BalanceDue, 
							tmp4.TotalBalance, tmp4.LastPaymentDate, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp4.PledgedRangeValue,</cfif> 1 as reportRow
						from ###local.tempTableName#_2 as tmp2
						inner join ###local.tempTableName#_3 as tmp3 on tmp3.contributionID = tmp2.contributionID
						inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID;

					</cfif>
				<cfelseif local.frmReportView eq 4>
					CREATE TABLE ###local.tempTableName# (ProgramName varchar(200), TotalBilled decimal(16,2), TotalPaid decimal(16,2), 
						BalanceDue decimal(16,2), <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue decimal(16,2), </cfif>
						reportRow int);
					INSERT INTO ###local.tempTableName# (ProgramName, TotalBilled, TotalPaid, BalanceDue, 
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
					select tmp2.ProgramName, sum(tmp4.TotalBilled) as TotalBilled, sum(tmp4.TotalPaid) as TotalPaid, sum(tmp4.BalanceDue) as BalanceDue,
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(tmp4.PledgedRangeValue) as PledgedRangeValue,</cfif>
						1 as reportRow
					from ###local.tempTableName#_2 as tmp2
					inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
					group by tmp2.ProgramName;

				<cfelseif local.frmReportView eq 6>
					select tmp2.memberID, tmp2.LastName, tmp2.firstName, tmp2.memberNumber, tmp2.hasMemberPhotoThumb,
						case when len(tmp2.Company) = 0 then '[No company listed]' else tmp2.Company end as Company, 
						sum(tmp4.TotalBilled) as TotalBilled, sum(tmp4.TotalPaid) as TotalPaid, sum(tmp4.BalanceDue) as BalanceDue,
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(tmp4.PledgedRangeValue) as PledgedRangeValue,</cfif>
						1 as reportRow
					into ###local.tempTableName#_6
					from ###local.tempTableName#_2 as tmp2
					inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
					group by tmp2.memberID, tmp2.LastName, tmp2.firstName, tmp2.memberNumber, tmp2.company, tmp2.hasMemberPhotoThumb;

					<cfif arguments.reportAction eq 'customcsv'>
						select tmp6.LastName, tmp6.firstName, tmp6.memberNumber, tmp6.hasMemberPhotoThumb,
							tmp6.Company, tmp6.TotalBilled, tmp6.TotalPaid, tmp6.BalanceDue,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp6.PledgedRangeValue,</cfif>
							tmp6.reportRow, m.*
						into ###local.tempTableName#
						from ###local.tempTableName#_6 as tmp6
						INNER JOIN ##tmpMembersFS AS m on tmp6.memberID = m.memberID
						order by tmp6.reportRow, tmp6.lastName, tmp6.firstname, tmp6.memberNumber;
					<cfelse>
						CREATE TABLE ###local.tempTableName# (memberID int, LastName varchar(75), FirstName varchar(75), memberNumber varchar(50), 
							hasMemberPhotoThumb bit, Company varchar(200), TotalBilled decimal(16,2), TotalPaid decimal(16,2), 
							BalanceDue decimal(16,2), <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue decimal(16,2), </cfif>
							reportRow int);
						INSERT INTO ###local.tempTableName# (memberID, LastName, FirstName, memberNumber, hasMemberPhotoThumb, Company, 
							TotalBilled, TotalPaid, BalanceDue, <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select memberID, LastName, FirstName, memberNumber, hasMemberPhotoThumb, Company, TotalBilled, TotalPaid, BalanceDue,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow
						from ###local.tempTableName#_6;
					</cfif>

				<cfelseif local.frmReportView eq 7>
					select tmp2.memberID, tmp2.LastName, tmp2.firstName, tmp2.memberNumber, tmp2.hasMemberPhotoThumb, tmp2.ProgramName, 
						case when len(tmp2.Company) = 0 then '[No company listed]' else tmp2.Company end as Company, 
						sum(tmp4.TotalBilled) as TotalBilled, sum(tmp4.TotalPaid) as TotalPaid, sum(tmp4.BalanceDue) as BalanceDue,
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(tmp4.PledgedRangeValue) as PledgedRangeValue,</cfif>
						1 as reportRow
					into ###local.tempTableName#_6
					from ###local.tempTableName#_2 as tmp2
					inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
					group by tmp2.memberID, tmp2.LastName, tmp2.firstName, tmp2.memberNumber, tmp2.ProgramName, tmp2.company, tmp2.hasMemberPhotoThumb;

					<cfif arguments.reportAction eq 'customcsv'>
						select tmp6.LastName, tmp6.firstName, tmp6.memberNumber, tmp6.hasMemberPhotoThumb, tmp6.ProgramName, 
						tmp6.Company, tmp6.TotalBilled, tmp6.TotalPaid, tmp6.BalanceDue,
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp6.PledgedRangeValue,</cfif>
						tmp6.reportRow, m.*
						into ###local.tempTableName#
						from ###local.tempTableName#_6 as tmp6
						INNER JOIN ##tmpMembersFS AS m on tmp6.memberID = m.memberID
						order by tmp6.reportRow, tmp6.lastName, tmp6.firstname, tmp6.memberNumber;
					<cfelse>
						CREATE TABLE ###local.tempTableName# (memberID int, LastName varchar(75), FirstName varchar(75), memberNumber varchar(50), 
							hasMemberPhotoThumb bit, ProgramName varchar(200), Company varchar(200), TotalBilled decimal(16,2), TotalPaid decimal(16,2), 
							BalanceDue decimal(16,2), <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue decimal(16,2), </cfif>
							reportRow int);
						INSERT INTO ###local.tempTableName# (memberID, LastName, FirstName, memberNumber, hasMemberPhotoThumb, ProgramName, Company, 
							TotalBilled, TotalPaid, BalanceDue, <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select memberID, LastName, FirstName, memberNumber, hasMemberPhotoThumb, ProgramName, Company, TotalBilled, TotalPaid, BalanceDue,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow
						from ###local.tempTableName#_6;
					</cfif>

				<cfelseif local.frmReportView eq 9>
					select tmp2.LastName, tmp2.FirstName, tmp2.MemberNumber, tmp2.Company, tmp2.hasMemberPhotoThumb, 
						tmp2.ProgramName, tmp2.CampaignName, tmp2.RateName, tmp2.StartDate, tmp2.EndDate, tmp2.Status, tmp2.Frequency, tmp2.CancellationDate,
						tmp3.totalPledgeFirst as FirstInstallmentAmount, tmp3.totalPledgeRecurring as RecurringInstallmentAmount, 
						tmp3.pledgedValue as PledgedAmount, tmp4.TotalInstallmentCount, tmp4.TotalBilled, tmp4.TotalPaid, tmp4.BalanceDue, 
						tmp4.TotalBalance, tmp4.LastPaymentDate
						<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>, tmp4.PledgedRangeValue</cfif>
						<cfif listLen(local.frmCPProgram) is 1>, tmp7.*</cfif>
						, tmp9.*, tmp10.*, m.*
					into ###local.tempTableName#
					from ###local.tempTableName#_2 as tmp2
					inner join ###local.tempTableName#_3 as tmp3 on tmp3.contributionID = tmp2.contributionID
					inner join ###local.tempTableName#_4 as tmp4 on tmp4.contributionID = tmp2.contributionID
					<cfif listLen(local.frmCPProgram) is 1>
						left outer join #####local.tempTableName#_7 as tmp7 on tmp7.f7ContributionID = tmp2.contributionID
					</cfif>
					left outer join #####local.tempTableName#_9 as tmp9 on tmp9.f9ContributionID = tmp2.contributionID
					left outer join #####local.tempTableName#_10 AS tmp10 ON tmp10.f10contributionid = tmp2.contributionid
					INNER JOIN ##tmpMembersFS AS m on tmp2.memberID = m.memberID;
				</cfif>

				<cfif arguments.reportAction eq 'customcsv'>
					#generateFinalBCPTable(tblName="###local.tempTableName#", dropFields="f7ContributionID,f9ContributionID,f10ContributionID,memberID,hasMemberPhotoThumb,reportRow")#
				<cfelse>
					<cfif local.frmReportView eq 1>
						insert into ###local.tempTableName# (Company, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select null, sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#;

						select *
						from ###local.tempTableName#
						order by case when nullif(Company,'[No company listed]') is null then 'zzzzzzzz' else Company end, reportRow;

					<cfelseif local.frmReportView eq 2>
						insert into ###local.tempTableName# (Company, ProgramName, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select Company, null, sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#
						group by Company;

						insert into ###local.tempTableName# (Company, ProgramName, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select null, null, sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 3
						from ###local.tempTableName#
						where reportRow = 1;

						select *, ROW_NUMBER() over (partition by Company 
							order by case when ProgramName is null then 0 else 1 end desc, reportRow) as rowNum
						from ###local.tempTableName#
						order by case when nullif(Company,'[No company listed]') is null then 'zzzzzzzz' else Company end, 
							case when ProgramName is null then 0 else 1 end desc, reportRow;

					<cfelseif local.frmReportView eq 3>
						insert into ###local.tempTableName# (Company, ProgramName, contributionID, memberID, TotalBilled, TotalPaid, BalanceDue, TotalBalance,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select Company, null, max(contributionID), max(memberID), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), sum(TotalBalance), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#
						group by Company;

						insert into ###local.tempTableName# (Company, ProgramName, contributionID, memberID, TotalBilled, TotalPaid, BalanceDue, TotalBalance,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select null, null, max(contributionID), max(memberID), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), sum(TotalBalance), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 3
						from ###local.tempTableName#
						where reportRow = 1;

						SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT tmp.Company, tmp.ProgramName, tmp.contributionID, tmp.TotalBilled, tmp.TotalPaid, tmp.BalanceDue, tmp.TotalBalance,
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp.PledgedRangeValue,</cfif> tmp.reportRow, 
								tmp.LastName, tmp.FirstName, tmp.MemberNumber, tmp.CampaignName, tmp.StartDate, tmp.EndDate, tmp.Status, 
								tmp.Frequency, tmp.CancellationDate, tmp.FirstInstallmentAmount, tmp.RecurringInstallmentAmount, 
								tmp.PledgedAmount, tmp.TotalInstallmentCount, tmp.LastPaymentDate,
								<cfif listLen(local.frmCPProgram) is 1>tmp7.*,</cfif>
								m.*, 
								ROW_NUMBER() over (partition by tmp.Company 
												order by case when nullif(tmp.Company,'[No company listed]') is null then 'zzzzzzzz' else tmp.Company end, 
														case when tmp.ProgramName is null then 1 else 0 end,  tmp.reportRow, 
														tmp.lastname, tmp.firstName, tmp.memberNumber, tmp.contributionID desc) as rowNum,
								ROW_NUMBER() over (partition by tmp.Company, tmp.memberID 
												order by case when nullif(tmp.Company,'[No company listed]') is null then 'zzzzzzzz' else tmp.Company end, 
														case when tmp.ProgramName is null then 1 else 0 end,  tmp.reportRow, 
														tmp.lastname, tmp.firstName, tmp.memberNumber, tmp.contributionID desc) as memberRowNum,
								ROW_NUMBER() OVER (ORDER BY case when nullif(tmp.Company,'[No company listed]') is null then 'zzzzzzzz' else tmp.Company end, 
												case when tmp.ProgramName is null then 1 else 0 end, tmp.reportRow, tmp.lastname, tmp.firstName, tmp.memberNumber, 
												tmp.contributionID desc) as mc_row
							FROM ###local.tempTableName# as tmp
							<cfif listLen(local.frmCPProgram) is 1>
								LEFT OUTER JOIN #####local.tempTableName#_7 as tmp7 on tmp7.f7ContributionID = tmp.contributionID
							</cfif>
							INNER JOIN ##tmpMembersFS AS m ON tmp.memberID = m.memberID
						) AS finalData
						ORDER BY mc_row;
						
					<cfelseif local.frmReportView eq 4>
						insert into ###local.tempTableName# (ProgramName, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select null, sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#;

						select *
						from ###local.tempTableName#
						order by case when ProgramName is null then 0 else 1 end desc, reportRow;

					<cfelseif local.frmReportView eq 5>
						insert into ###local.tempTableName# (ProgramName, contributionID, memberID, TotalBilled, TotalPaid, BalanceDue, TotalBalance,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select ProgramName, max(contributionID), max(memberID), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), sum(TotalBalance), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#
						group by ProgramName;

						insert into ###local.tempTableName# (ProgramName, contributionID, memberID, TotalBilled, TotalPaid, BalanceDue, TotalBalance,
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select null, max(contributionID), max(memberID), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), sum(TotalBalance), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 3
						from ###local.tempTableName#
						where reportRow = 1;

						SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT tmp.Company, tmp.ProgramName, tmp.contributionID, tmp.TotalBilled, tmp.TotalPaid, tmp.BalanceDue, tmp.TotalBalance,
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp.PledgedRangeValue,</cfif> tmp.reportRow, 
								tmp.LastName, tmp.FirstName, tmp.MemberNumber, tmp.CampaignName, tmp.StartDate, tmp.EndDate, tmp.Status, 
								tmp.Frequency, tmp.CancellationDate, tmp.FirstInstallmentAmount, tmp.RecurringInstallmentAmount, 
								tmp.PledgedAmount, tmp.TotalInstallmentCount, tmp.LastPaymentDate,
								<cfif listLen(local.frmCPProgram) is 1>tmp7.*,</cfif>
								m.*, 
								ROW_NUMBER() over (partition by tmp.ProgramName 
											order by case when tmp.ProgramName is null then 'zzzzzzzz' else tmp.ProgramName end, tmp.reportRow, tmp.lastname, 
																tmp.firstName, tmp.memberNumber, tmp.contributionID desc) as rowNum,
								ROW_NUMBER() over (partition by tmp.ProgramName, tmp.memberID 
											order by case when tmp.ProgramName is null then 'zzzzzzzz' else tmp.ProgramName end, tmp.reportRow, tmp.lastname, 
																tmp.firstName, tmp.memberNumber, tmp.contributionID desc) as memberRowNum,
								ROW_NUMBER() OVER (ORDER BY case when tmp.ProgramName is null then 'zzzzzzzz' else tmp.ProgramName end,
													tmp.reportRow, tmp.lastname, tmp.firstName, tmp.memberNumber, tmp.contributionID desc) AS mc_row
							FROM ###local.tempTableName# as tmp
							<cfif listLen(local.frmCPProgram) is 1>
								LEFT OUTER JOIN #####local.tempTableName#_7 as tmp7 on tmp7.f7ContributionID = tmp.contributionID
							</cfif>
							INNER JOIN ##tmpMembersFS AS m ON tmp.memberID = m.memberID
						) AS finalData
						ORDER BY mc_row;

					<cfelseif local.frmReportView eq 6>
						insert into ###local.tempTableName# (memberNumber, memberID, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select 'zzzzzzzz', max(memberID), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#;

						SELECT *, CASE WHEN rowNum = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT  tmp.LastName, tmp.firstName, tmp.memberNumber, tmp.hasMemberPhotoThumb, tmp.Company, tmp.TotalBilled, tmp.TotalPaid, tmp.BalanceDue,
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp.PledgedRangeValue,</cfif>
								tmp.reportRow, m.*, ROW_NUMBER() over (order by tmp.reportRow, tmp.lastName, tmp.firstname, tmp.memberNumber) as rowNum
							FROM ###local.tempTableName# as tmp
							INNER JOIN ##tmpMembersFS AS m ON tmp.memberID = m.memberID
						) AS finalData
						ORDER BY rowNum;

					<cfelseif local.frmReportView eq 7>
						insert into ###local.tempTableName# (memberNumber, memberID, lastName, firstname, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select memberNumber, memberID, lastName, firstname, sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#
						group by memberNumber, memberID, lastName, firstname;

						insert into ###local.tempTableName# (memberNumber, memberID, lastName, firstname, TotalBilled, TotalPaid, BalanceDue, 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select max(memberNumber), max(memberID), max(lastName), max(firstname), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
							<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 3
						from ###local.tempTableName#
						where reportRow = 1;

						SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT tmp.LastName, tmp.firstName, tmp.memberNumber, tmp.hasMemberPhotoThumb, tmp.ProgramName, 
								tmp.Company, tmp.TotalBilled, tmp.TotalPaid, tmp.BalanceDue,
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp.PledgedRangeValue,</cfif>
								tmp.reportRow, m.*,
								ROW_NUMBER() over (partition by tmp.memberID order by tmp.lastname, tmp.firstName, tmp.memberNumber,
										case when tmp.ProgramName is null then 'zzzzzzzz' else tmp.ProgramName end, tmp.reportRow) as rowNum,
								ROW_NUMBER() OVER (order by tmp.lastname, tmp.firstName, tmp.memberNumber, 
													case when tmp.ProgramName is null then 'zzzzzzzz' else tmp.ProgramName end, tmp.reportRow) as mc_row
							FROM ###local.tempTableName# as tmp
							INNER JOIN ##tmpMembersFS AS m ON tmp.memberID = m.memberID
						) AS finalData
						ORDER BY mc_row;

					<cfelseif local.frmReportView eq 8>
						insert into ###local.tempTableName# (memberID, membernumber, lastname, firstname, contributionID, TotalBilled, TotalPaid, BalanceDue, 
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
						select memberID, membernumber, lastname, firstname, max(contributionID), sum(TotalBilled), sum(TotalPaid), sum(BalanceDue), 
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 2
						from ###local.tempTableName#
						group by memberID, membernumber, lastname, firstname;

						IF EXISTS (select 1 from ###local.tempTableName# where reportRow = 1)
							insert into ###local.tempTableName# (memberID, membernumber, lastname, firstname, contributionID, TotalBilled, TotalPaid, BalanceDue,
									<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>PledgedRangeValue,</cfif> reportRow)
							select max(memberID), max(membernumber), max(lastname), max(firstname), max(contributionID), sum(TotalBilled), sum(TotalPaid), 
								sum(BalanceDue), <cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>sum(PledgedRangeValue),</cfif> 3
							from ###local.tempTableName#
							where reportRow = 1;

						SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT tmp.contributionID, tmp.LastName, tmp.FirstName, tmp.MemberNumber, tmp.Company, tmp.hasMemberPhotoThumb,
								tmp.ProgramName, tmp.CampaignName, tmp.StartDate, tmp.EndDate, tmp.Status, tmp.Frequency, tmp.CancellationDate,
								tmp.FirstInstallmentAmount, tmp.RecurringInstallmentAmount, tmp.PledgedAmount, tmp.TotalInstallmentCount, 
								tmp.TotalBilled, tmp.TotalPaid, tmp.BalanceDue, tmp.TotalBalance, tmp.LastPaymentDate, 
								<cfif len(local.frmLimitInstallFrom) and len(local.frmLimitInstallTo)>tmp.PledgedRangeValue,</cfif> tmp.reportRow
								<cfif listLen(local.frmCPProgram) is 1>, tmp7.*</cfif>, 
								m.*,
								ROW_NUMBER() over (partition by tmp.memberID order by tmp.lastname, tmp.firstName, tmp.memberNumber, tmp.reportRow) as rowNum,
								ROW_NUMBER() OVER (order by tmp.lastname, tmp.firstName, tmp.memberNumber, tmp.reportRow) AS mc_row
							FROM ###local.tempTableName# as tmp
							<cfif listLen(local.frmCPProgram) is 1>
								left outer join #####local.tempTableName#_7 as tmp7 on tmp7.f7ContributionID = tmp.contributionID
							</cfif>
							INNER JOIN ##tmpMembersFS AS m ON tmp.memberID = m.memberID
						) AS finalData
						ORDER BY mc_row;
					<cfelse>
						select *
						from ###local.tempTableName#;
					</cfif>
				</cfif>

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3;
				IF OBJECT_ID('tempdb..###local.tempTableName#_4') IS NOT NULL
					DROP TABLE ###local.tempTableName#_4;
				IF OBJECT_ID('tempdb..###local.tempTableName#_5') IS NOT NULL
					DROP TABLE ###local.tempTableName#_5;
				IF OBJECT_ID('tempdb..###local.tempTableName#_6') IS NOT NULL
					DROP TABLE ###local.tempTableName#_6;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_7') IS NOT NULL
					DROP TABLE #####local.tempTableName#_7;
				IF OBJECT_ID('tempdb..###local.tempTableName#_8') IS NOT NULL
					DROP TABLE ###local.tempTableName#_8;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_9') IS NOT NULL
					DROP TABLE #####local.tempTableName#_9;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_10') IS NOT NULL
					DROP TABLE #####local.tempTableName#_10;
				IF OBJECT_ID('tempdb..##tmpProgramDistCodes') IS NOT NULL
					DROP TABLE ##tmpProgramDistCodes;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				IF OBJECT_ID('tempdb..##tmpTxnForContributions') IS NOT NULL
					DROP TABLE ##tmpTxnForContributions;
				IF OBJECT_ID('tempdb..##tmpTxnForContributionsResult') IS NOT NULL
					DROP TABLE ##tmpTxnForContributionsResult;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.strFields = structNew();
			local.strFields.frmContribStartDateFrom = { label="Contribution Start Date Start", value=arguments.event.getValue('frmContribStartDateFrom','') };
			local.strFields.frmContribStartDateTo = { label="Contribution Start Date End", value=arguments.event.getValue('frmContribStartDateTo','') };
			local.strFields.frmContribEndDateFrom = { label="Contribution End Date Start", value=arguments.event.getValue('frmContribEndDateFrom','') };
			local.strFields.frmContribEndDateTo = { label="Contribution End Date End", value=arguments.event.getValue('frmContribEndDateTo','') };
			local.strFields.frmEnteredDateFrom = { label="Date Entered Start Date", value=arguments.event.getValue('frmEnteredDateFrom','') };
			local.strFields.frmEnteredDateTo = { label="Date Entered End Date", value=arguments.event.getValue('frmEnteredDateTo','') };
			local.strFields.frmCPProgram = { label="Program", value=arguments.event.getValue('frmCPProgram','') };
			local.strFields.frmCPCampaign = { label="Campaign", value=arguments.event.getValue('frmCPCampaign','') };
			local.strFields.frmCPStatus = { label="Status", value=arguments.event.getValue('frmCPStatus','') };
			local.strFields.frmCPFrequency = { label="Frequency", value=arguments.event.getValue('frmCPFrequency','') };
			local.strFields.frmCPDuration = { label="Duration", value=arguments.event.getValue('frmCPDuration','') };
			local.strFields.frmHasCard = { label="Pay Method Associated", value=arguments.event.getValue('frmHasCard','') };
			local.strFields.frmFirstInstallFrom = { label="First Installment Amount From", value=arguments.event.getValue('frmFirstInstallFrom','') };
			local.strFields.frmFirstInstallTo = { label="First Installment Amount To", value=arguments.event.getValue('frmFirstInstallTo','') };
			local.strFields.frmRecurringInstallFrom = { label="Recurring Installment Amount From", value=arguments.event.getValue('frmRecurringInstallFrom','') };
			local.strFields.frmRecurringInstallTo = { label="Recurring Installment Amount To", value=arguments.event.getValue('frmRecurringInstallTo','') };
			local.strFields.frmlimittopastdue = { label="Contributions with a past due amount", value=arguments.event.getValue('frmlimittopastdue','') };
			local.strFields.frmLimitInstallFrom = { label="Contribution Value Start Date", value=arguments.event.getValue('frmLimitInstallFrom','') };
			local.strFields.frmLimitInstallTo = { label="Contribution Value End Date", value=arguments.event.getValue('frmLimitInstallTo','') };
			local.strFields.frmView = { label="Report View", value=arguments.event.getValue('frmView','') };

			reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
			return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

	<cffunction name="getProgramCustomColumnNames" access="private" output="false" returntype="query">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryCustomColumns = "">

		<cfquery name="qryCustomColumns" datasource="#application.dsn.membercentral.dsn#">
			select distinct f.fieldReference
			from dbo.cf_fieldData as fd 
			inner join dbo.cp_contributions as c on c.contributionID = fd.itemID and fd.itemType = 'ContributionProgram'
			inner join dbo.cf_fields as f on f.fieldID = fd.fieldID
			where c.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and len(f.fieldReference) > 0;
		</cfquery>

		<cfreturn qryCustomColumns>
	</cffunction>
</cfcomponent>