<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#
<div id="MCNotLoggedInContainer"<cfif val(local.strResultsCount.itemCount) GT 0> style="display:none;"</cfif>>
	<cfif local.strResultsCount.itemCount EQ 0>
		#showCommonNotFound(bucketid=arguments.bucketid, searchid=arguments.searchid, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	<cfelse>
		<div style="padding-bottom:10px;">
			<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
			<button class="tsAppBodyButton" onclick="document.location.href='/?pg=uploadDocuments'"><i class="icon-upload"></i> Upload a deposition <span style="font-weight:normal;font-style:italic;">(Earn up to $30 in purchase credits)</span></button>
		</div>
		<cfif val(local.strResultsCount.itemCount) gt 0>
			<div class="tsAppBodyText tsAppBB" style="padding-bottom:3px;"><strong>#Numberformat(local.strResultsCount.itemCount)#</strong> documents found</div>
		</cfif>
		#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
	</cfif>
</div>
<cfif val(local.strResultsCount.itemCount) GT 0>
	<div id="MCSearchSummaryContainer">
		<cfif len(trim(local.stsearchVerboseNoName))>
			<div>
				<strong>Search Criteria:</strong> <em>#local.stsearchVerboseNoName#</em>
			</div>
		</cfif>

		<div class="bk-mb-3 bk-mt-4">
			<button type="button" class="tsAppBodyButton" onclick="b_showLoginContainer();">
				<cfif arguments.strSearch.strSettings.search.deposedexpertonly>View Details and Connect with Lawyers<cfelse>View Details and Read Depositions</cfif>
			</button>
		</div>

		<div class="bk-d-flex bk-mt-3">
			<div class="bk-mr-3">
				<div class="bk-summary-circle bk-text-light bk-mx-auto">#Numberformat(local.strResultsCount.itemCount)#</div>
				<div class="bk-font-size-sm bk-mt-2">
					<cfif arguments.strSearch.strSettings.search.deposedexpertonly>Deposing Lawyers<cfelse>Depositions Available</cfif>
				</div>
			</div>
		</div>

		<div class="bk-d-flex bk-flex-wrap bk-mt-4">
			<div id="MCSearchSummaryLocationChart" class="bk-mb-3"></div>
			<div id="bk_tooltip"></div>
		</div>
	</div>
	#showFooter(viewDirectory=arguments.viewDirectory)#
</cfif>
</cfoutput>	