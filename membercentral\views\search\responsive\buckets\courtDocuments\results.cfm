<cfoutput>
	#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
	
	<div style="padding-bottom:10px;">
		<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
		<button class="btn" onclick="document.location.href='/?pg=uploadDocuments'"><i class="icon-upload"></i> Upload a document</button>
	</div>
</cfoutput>
<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#
	</cfoutput>
<cfelse>
	<cfoutput>
	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startRow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.documentsFound, itemWord='document', itemWordSuffix='s', filter=arguments.filter, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
	<!--- crumbtrail --->
	<cfoutput>	
	<div>
		<p>Documents are organized into folders. You are viewing:</p>
		<ul class="breadcrumb">
			<cfset local.dividerElement = "<span class='divider'>/</span>">
			<li><a href="/?pg=search&bid=#arguments.bucketid#&s_a=doSearch&sid=#arguments.searchid#">#local.qryBucketInfo.bucketName#</a></li>
			<cfif local.strSearch.filter_group gt 0>
				<li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|0|0');">#local.qryResults.grpLabel#</a></li>
				<cfif local.strSearch.filter_casetype gt 0>
					<li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|0');">#local.qryResults.caseLabel#</a></li>
					<cfif local.strSearch.filter_doctype gt 0>
						<li>#local.dividerElement#<a href="javascript:b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|#local.strSearch.filter_doctype#');">#local.qryResults.docLabel#</a></li>
					</cfif>
				</cfif>
			</cfif>
		</ul>
	</div>
	</cfoutput>
	
	<cfif local.strSearch.filter_doctype gt 0>
		<cfoutput>
		<div style="clear:both;"></div>
		<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>
		</cfoutput>
		<cfset local.currentDocument = CreateObject('component','model.system.platform.tsDocument')>
		<cfloop query="local.qryResults">
			<cfset local.currentDocument.load(local.qryResults.documentid)>
			<cfset local.mycost = local.currentDocument.getPrice(
				membertype=variables.cfcuser_membertype,
				billingstate=variables.cfcuser_billingstate,
				billingzip=variables.cfcuser_billingzip,
				websiteorgcode=session.mcstruct.sitecode,
				depomemberdataid=variables.cfcuser_depomemberdataid)>
			<cfset local.docCaseRefDescTxt = '#trim(local.qryResults.expertname)# from #DateFormat(local.qryResults.DocumentDate, "m/d/yyyy")# - #replace(dollarformat(local.mycost.price),".00","")#'>

			<cfoutput>
			<div class="bk-d-flex well tsDocViewerDocument s_row bk-mb-2" data-tsdocumentid="#local.qryResults.documentid#">
				<div class="bk-col">
					<div>
						<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchid#);" title="Click to view document"><img src="#application.paths.images.url#images/search/page_acrobat.png" width="16" height="16" border="0" align="left" class="hidden-phone"/><span class="hidden-phone">&nbsp;</span><b><cfif len(trim(local.qryResults.expertname))>#trim(local.qryResults.expertname)#<cfelse>Unknown Expert</cfif></b></a>
						<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchid#);" title="Click to view document" class="hidden-phone hidden-tablet">(click to view)</a><br/>
					</div>
					<div class="bk_subContent">
						Document ###local.qryResults.documentid# - #DateFormat(local.qryResults.DocumentDate, "mmm d, yyyy")# - #local.qryResults.state# <cfif local.qryResults.pages gt 0>- #local.qryResults.pages# pages</cfif><br/>
						#local.qryResults.style# ... <cfif len(local.qryResults.causedesc)>... #local.qryResults.causedesc#</cfif>
						<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned or listfind(local.memberGroups,local.qryResults.groupid)>
							<cfif len(local.qryResults.notes)><br/>Notes: #local.qryResults.notes#</cfif>
							<br/>Contributed by: <cfif len(trim(local.qryResults.email))><a href="mailto:#trim(local.qryResults.email)#"></cfif>#local.qryResults.FirstName# #local.qryResults.LastName#<cfif len(trim(local.qryResults.email))></a></cfif>, 
							<cfset local.tmp = "">
							<cfif len(local.qryResults.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingfirm)></cfif>
							<cfif len(local.qryResults.phone)><cfset local.tmp = listAppend(local.tmp,"Ph: " & local.qryResults.phone)></cfif>
							#replace(local.tmp,",",", ","ALL")#<br/>
						<cfelse>
							<br/>Notes: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
							<br/>Contributed by: <a href="/?pg=upgradeTrialSmith">Hidden - Upgrade to view details and preview document</a>
						</cfif>
					</div>
					<cfif isnumeric(local.qryResults.numdocflags) and local.qryResults.numdocflags gt 0>
						<cfquery name="local.getFlags" datasource="#application.dsn.tlasites_trialsmith.dsn#">
							select f.name
							from dbo.docflags f
							inner join dbo.docflaglinks as fl on fl.docflagid = f.docflagid
							where f.groupid = #local.qryResults.groupid#
							and fl.documentid = #local.qryResults.documentid#
							order by name
						</cfquery>
						<div class="s_dtl">
							Flags: #replace(valuelist(local.getflags.name), ",", ", ")#
						</div>
					</cfif>
				</div>
				<div class="bk-col-auto p-3">
					<div class="s_opt">
						<div class="p-2 text-center<cfif NOT local.qryResults.inCart> hide</cfif>" id="s_incart#local.qryResults.documentID#">
							<button type="button" class="btn btn-link btn-sm text-center" onClick="viewCart();" style="width:150px;">
								<i class="icon-shopping-cart icon-large bk-mr-2"></i>In Cart
							</button>
						</div>
						<cfif not local.qryResults.owned>
							<div class="p-3 text-center <cfif local.qryResults.inCart>hide</cfif>" id="s_addtocart#local.qryResults.documentID#">
								<div class="bk-mb-1">#replace(dollarformat(local.mycost.price),".00","")#</div>
								<button name="addCart_btn_#local.qryResults.documentID#" id="addCart_btn_#local.qryResults.documentID#" type="button" class="btn btn-secondary btn-sm text-center" onClick="addDepoDocToCartPrompt(#local.qryResults.documentid#,#arguments.searchid#);" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" style="width:150px;"><i class="icon-download-alt" title="Add to Cart"></i> Add to Cart</button>
							</div>
						</cfif>
						<cfif len(local.qryResults.uploadpdfdate)>
							<cfset local.fileName = '#local.currentDocument.expertFileName#.pdf'>

							<cfif local.qryResults.owned>
								<cfset local.onClickFn = "downloadDocImmedate(#local.qryResults.DocumentID#,'pdf');">
							<cfelse>
								<cfset local.onClickFn = "oneClickPurchasePrompt(#local.qryResults.documentid#,#arguments.searchid#,'#encodeForJavaScript(local.fileName)#','pdf',#local.qryResults.currentRow#,'dload');">
							</cfif>
							<div class="p-3 text-center">
								<button type="button" name="pdf_btn_#local.qryResults.currentRow#" id="pdf_btn_#local.qryResults.currentRow#" class="btn btn-secondary btn-sm" onClick="#local.onClickFn#" style="width:150px;" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" data-downloaddocid="#local.qryResults.documentID#" data-downloaddocformat="pdf">
									<i class="icon-download-alt" title="Download PDF"></i> Download PDF
								</button>
							</div>
						</cfif>
						<cfif len(local.qryResults.uploadpdfdate) and len(local.dropboxAppKey)>
							<cfif local.qryResults.owned>
								<cfset local.onClickFn = "doDropboxSave(#local.qryResults.documentID#,'#local.currentDocument.expertFileName#.pdf','pdf',#local.qryResults.currentRow#);">
							<cfelse>
								<cfset local.onClickFn = "oneClickPurchasePrompt(#local.qryResults.documentid#,#arguments.searchid#,'#local.currentDocument.expertFileName#.pdf','pdf',#local.qryResults.currentRow#,'dbox');">
							</cfif>
							<div class="p-3 text-center">
								<button name="dropb_btn_#local.qryResults.currentRow#" id="dropb_btn_#local.qryResults.currentRow#" type="button" class="btn btn-secondary btn-sm drop-box-file-list" onClick="#local.onClickFn#" data-elmid="#local.qryResults.documentID#" data-elmname="#local.currentDocument.expertFileName#.pdf" data-elmext="pdf" data-rowid="#local.qryResults.currentRow#" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" data-dropboxdownloaddocid="#local.qryResults.documentID#" style="width:150px;"><img src="/assets/common/images/dropbox-icon-ios.png" id="dropb_img_#local.qryResults.currentRow#" data-toggle="tooltip" width="15" height="15" border="0" title="Save to Dropbox"/> Save to Dropbox</button>
							</div>
						</cfif>
						<cfif not len(local.qryResults.uploadpdfdate)>
							<div class="p-3" id="txt_#local.qryResults.documentID#" style="text-align:center;">
								<cfif local.qryResults.owned>
									<button name="txt_btn_#local.qryResults.currentRow#" type="button" class="btn btn-secondary btn-sm" onClick="downloadDocImmedate(#local.qryResults.DocumentID#,'txt');" style="width:150px; margin-top:10px; text-align:center;"><i class="icon-download-alt" title="Download TEXT" data-downloaddocid="#local.qryResults.documentID#" data-downloaddocformat="txt"></i> Download TEXT</button>
								</cfif>
							</div>
						</cfif>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfloop>
	<cfelse>
        <cfif local.qryResults.recordCount>
            <cfoutput>
            <div class="row-fluid">
                <div class="span12" style="margin-left:15px;">
					<!--- if more than 20 records, show in 2 columns --->
					<cfif local.qryResults.recordcount gt 20>
						<div class="span6">
					</cfif>
					<cfoutput query="local.qryResults">
						<cfif listlen(arguments.filter,"|") is 3>
							<cfif local.strSearch.filter_casetype gt 0>
								<cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|#local.qryResults.doctypeid#');">
								<cfset local.folderLabel = local.qryResults.doctypedesc>
							<cfelseif local.strSearch.filter_group gte 0>
								<cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#local.strSearch.filter_group#|#local.qryResults.casetypeid#|0');">
								<cfset local.folderLabel = local.qryResults.casetypedesc>
							<cfelse>
								<cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#local.qryResults.groupid#|0|0');">
								<cfset local.folderLabel = local.qryResults.description>
							</cfif>
						<cfelse>
							<cfset local.filterLink = "b_doSearchFilter(#arguments.searchid#,'#local.qryResults.groupid#|0|0');">
							<cfset local.folderLabel = local.qryResults.description>
						</cfif>

						<div class="span12">
							<a href="javascript:#local.filterLink#"><i class="icon-folder-close icon-large"></i> #local.folderLabel#</a> (#numberformat(local.qryResults.doccount)#)								
						</div>
						<cfif local.qryResults.recordcount gt 20 and local.qryResults.currentrow is int(local.qryResults.recordcount/2)+1>
							</div><div class="span6">
						</cfif>
					</cfoutput>
					<cfif local.qryResults.recordcount gt 20>
						</div>
					</cfif>
                </div>
            </div>
            </cfoutput>
        </cfif>
		<br/>
	</cfif>

	<cfoutput>
	<br clear="all"/>
	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startRow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.documentsFound, itemWord='document', itemWordSuffix='s', filter=arguments.filter, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
</cfif>