<cfsavecontent variable="local.evRegStep5JS">
	<cfoutput>
 	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/plupload.full.min.js"></script>
	<cfloop query="local.qrySelectedRegistrantRoles">
		<cfif local.strRoleFields[local.qrySelectedRegistrantRoles.categoryID].hasFields>
			#local.strRoleFields[local.qrySelectedRegistrantRoles.categoryID].head#
		</cfif>
	</cfloop>
	<script type='text/javascript'>
		var arrUploaders = [];

		showStep5Alert = function(msg) { $('##step5Err').html(msg).show(); };
		hideStep5Alert = function() { $('##step5Err').html('').hide(); };
		function doS5ValidateAndSave() {
			$('##btnSaveStep5').html('<i class="icon icon-spinner"></i> Please wait..').prop('disabled',true);
			var strErr = '';
			var errorMsgArray = [];
			<cfloop query="local.qrySelectedRegistrantRoles">
				<cfif local.strRoleFields[local.qrySelectedRegistrantRoles.categoryID].hasFields>
					#local.strRoleFields[local.qrySelectedRegistrantRoles.categoryID].jsValidation#
				</cfif>
			</cfloop>

			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return thisError;
				else return null;
			});
			
			strErr += finalErrors.join('<br/>');

			if (strErr.length > 0) {
				$('##btnSaveStep5').html('Save Changes').prop('disabled',false);
				showStep5Alert(strErr);
				return false;
			} else {
				hideStep5Alert();

				$('##frmEventRegStep5 .evreg_fileselect').each(function() {
					let fieldID = $(this).data('fieldid');
					let remDoc = $('##cf_remDoc_'+fieldID+'_').val();
					if ($('##cf_newDoc_'+fieldID+'_').val() == 1 && remDoc) {
						$('##cf_remDoc_'+fieldID+'_').val(0);
					}
				});
				
				return new Promise(function(resolve, reject) {
					doSaveStep5()
					.then(doSaveStep5Docs)
					.then(function() {
						onCompleteSavingStep5();
						resolve();
					}).catch(function(e) {console.log(e);
						let errMsg = e && typeof e == 'string' ? e : 'We were unable to save Event Roles. Try again.';
						reject(errMsg);
						showStep5Alert(errMsg);
						$('##btnSaveStep5').html('Save Changes').prop('disabled',false);
					});
				});
			}
		}
		function doSaveStep5() {
			return new Promise(function(resolve, reject) {
				$('##evRegStep5SaveLoading')
					.html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
					.load('#arguments.event.getValue('evregresourceurl')#&regaction=saves5', $('##frmEventRegStep5').serializeArray(),
						function(resp) {
							let respObj = JSON.parse(resp);
							resolve();
						}
					);
			});
		}
		function doSaveStep5Docs() {
			if (!arrUploaders.length)
				return;

			$.each(arrUploaders, function() { 
				if (this.fileUploaded == 0) this.uploader.start(); 
			});

			return new Promise(function(resolve, reject) {
				const interval = setInterval(() => {
					let arrUncompletedUploads = arrUploaders.filter(function (f) { return f.uploader.files.length && f.fileUploaded == 0; });

					if (arrUncompletedUploads.length) {
						return;
					}

					clearInterval(interval);
					resolve();
				}, 100);

				/*5 min timeout*/
				setTimeout(() => {
					clearInterval(interval);
					reject('Upload Timed out.');
				}, 300000);
			});
		}
		function onCompleteSavingStep5() {
			$('##frmEventRegStep5').hide();
			$('##btnSaveStep5').html('Save Changes').prop('disabled',false);
			$('##evRegStep5Summary').show(300);
			$('html, body').animate({
				scrollTop: $('##EvRegStep5').offset().top - 175
			}, 750);
		}
		function initializeRoleFieldControls(scope) {
			arrUploaders = [];

			scope.find('button.MCShowRoleCategoryFieldsLink').click(function(event){
				var linkedRoleCatWrapper = $(this).data('linkedrolecategory');
				showRoleCategoryFields($('##' + linkedRoleCatWrapper));
				event.preventDefault();
			});

			scope.find('button.MCHideRoleCategoryFieldsLink').click(function(event){
				var linkedRoleCatWrapper = $(this).data('linkedrolecategory');
				hideRoleCategoryFields($('##' + linkedRoleCatWrapper));
				event.preventDefault();
			});

			$.each(scope.find('.evreg_fileselect'), function() {
				var el = $(this);
				var evRegFileSelBtnID = el.attr('id');
				var evRegFileFieldID = el.data('fieldid');
				var uploadURL = '#arguments.event.getValue('evregresourceurl')#&regaction=saves5doc';

				var uploader = new plupload.Uploader({
					runtimes:'html5',
					browse_button:evRegFileSelBtnID,
					url:uploadURL,
					file_data_name:evRegFileSelBtnID,
					multi_selection:false
				});

				uploader.bind('FilesAdded', function(up, files) {
					plupload.each(files, function(file) {
						$('##cf_newDoc_'+evRegFileFieldID+'_').val(1);
						$('##'+evRegFileSelBtnID+'newFileDetails').html(file.name + ' (' + plupload.formatSize(file.size) + ')');
					});
				});

				uploader.bind('BeforeUpload', function(up, file) {
					up.settings.multipart_params = { 'fieldID':evRegFileFieldID, 'oldDoc':$('##cf_oldDoc_'+evRegFileFieldID+'_').val() };
					up.setOption('params', up.settings.multipart_params);
				});

				uploader.bind('FileUploaded', function () {
					$('##cf_newDoc_'+evRegFileFieldID+'_').val(0);
					arrUploaders.find(function (f) {
						return f.fieldID == evRegFileFieldID;
					}).fileUploaded = 1;
				});

				uploader.init();
				arrUploaders.push({ uploader:uploader, fieldID:evRegFileFieldID, fileUploaded:0 });
			});
		}
		function hideRoleCategoryFields(wrapperElement) {
			$(wrapperElement).addClass('MCRoleCategoryCollapsed');
		}
		function showRoleCategoryFields(wrapperElement) {
			$(wrapperElement).removeClass('MCRoleCategoryCollapsed');
		}
		function editRegStep5() {
			$('##evRegStep5Summary').hide();
			$('##frmEventRegStep5').show(300);
		}

		$(function(){
			initializeRoleFieldControls($('##EvRegStep5'));
		});
	</script>
	<style type="text/css">
		div.MCRoleCategoryFieldsWrapper{border: 2px solid ##d9dcdd; padding:8px; margin-top: 10px; margin-bottom: 15px; border-radius: 4px;}
		div.MCRoleCategoryFieldsWrapper .MCRoleCategoryButtonBar {float:left;}
		div.MCRoleCategoryFieldsWrapper .MCRoleCategoryButtonBar > button {margin-right:10px;}
		div.MCRoleCategoryFieldsWrapper:after {content: "";display: table; clear: both;}
		div.MCRoleCategoryTitleWrapper{margin-bottom:10px;}
		.MCShowRoleCategoryFieldsLink {display:none;}
		.MCRoleCategoryCollapsed .MCHideRoleCategoryFieldsLink {display:none;}
		.MCRoleCategoryCollapsed .MCShowRoleCategoryFieldsLink {display:inline;}
		.MCRoleCategoryCollapsed .MCRoleFieldsWrapper {display:none;}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.evRegStep5JS)#">