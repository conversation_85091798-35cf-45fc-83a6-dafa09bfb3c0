<cfsavecontent variable="local.clientFormJS">
	<cfoutput>
        <script language="JavaScript">
			function doFormProcess(){
				var errorMsg = "";
				
				$('.saveFeeBtn').attr('disabled','disabled');
				$("##frmCaseFee").attr('action', '#local.mainurl#&ra=saveForm');
				var changeInd = checkFormChanged();

				errorMsg += validateClientReferral();

				if (errorMsg.length > 0) {
					if (errorMsg != 'cancel') {
						alert("There were errors with your submission.\n\n" + errorMsg);
					}
				} else {
					$('##frmCaseFee').hide();
					$('##formSubmitting').show();
					$('html,body').animate({scrollTop: $('##formSubmitting').offset().top},100);
					return true;
				}
				$('.saveFeeBtn').attr('disabled',false);
				return false;
			}
            function checkFormChanged(){
                var changeInd = false;
                if (($("##frmCaseFee").serialize() != formOriginalData)) {
                    $("##formChanged").val('1');
                    changeInd = true;
                }
                return changeInd;
            }
            function validateClientFrm(){
                var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
                var errorMsg = "";

                if( ($.trim($('##collectedFee').val()).length == 0) || (($.trim($('##collectedFee').val()).length > 0) && (!amountRegex.test($.trim($('##collectedFee').val())))) ) {
                    errorMsg += '- Enter a valid client fee collected.';
                }
                
				var errorMsgArray = [];

				/*drop empty elements*/
				var finalErrors = $.map(errorMsgArray, function(thisError){
					if (thisError.length) return "- "+thisError;
					else return null;
				});
				errorMsg += finalErrors.join('\n');	

                return errorMsg;
            }
            function validateClientReferral(){		
                var errorMsg = "";	
                errorMsg = validateClientFrm();
                return errorMsg;
            }
            function formatCurrency(num) {
                num = num.toString().replace(/\$|\,/g,'');
                if(isNaN(num)) num = "0";
                sign = (num == (num = Math.abs(num)));
                num = Math.floor(num*100+0.50000000001);
                cents = num%100;
                num = Math.floor(num/100).toString();
                if(cents<10) cents = "0" + cents;
                for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
                return (((sign)?'':'-') + num + '.' + cents);
            }
            $(document).ready(function(){
                if($('##isFeeSubmitted').val() == 1){
                    localStorage.feesubmitted_#local.clientReferralID# = 1;
                }

                if(localStorage.feesubmitted_#local.clientReferralID# != undefined && localStorage.feesubmitted_#local.clientReferralID#== 1){
                    $('.form-buttons').remove();
                    $('##collectedFee').attr('disabled',true);
                    $('##collectedFee').attr('readonly',true);
                    $('##isFeeSubmitted').val(1);
                }

				if(localStorage.resultViewed != undefined && localStorage.resultViewed == 1){
					$("##frmCaseFee").hide();
					localStorage.resultViewed = 0;
					window.location.href = '#local.mainurl#';
				}
                var subCheckHasRun1 = false;

                formOriginalData = $("##frmCaseFee").serialize();		
				
                $('##collectedFee').change(function(){
                    if($(this).val() != ''){
                        $(this).val(formatCurrency($(this).val()));
                    }
                });
            });
        </script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.clientFormJS#" />
<cfoutput>
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
<div id="divMCClientReferralCenterContainer">
    <cfform name="frmCaseFee" id="frmCaseFee" method="POST" class="form-horizontal" onsubmit="return doFormProcess();">
        <input name="clientReferralID" id="clientReferralID" type="hidden" value="#local.clientReferralID#">
        <input name="isFeeSubmitted" id="isFeeSubmitted" type="hidden" value="<cfif val(arguments.event.getValue('success',0))>1<cfelse>0</cfif>">
        <div class="container-fluid">
            <h4 class="tsAppHeading">#variables.referralPageTitle#</h4><br>
            <div class="tsAppBodyText">#variables.referralPageDesc#</div>
            <cfif val(arguments.event.getValue('success',0))>
                <div class="row-fluid" id="msgSuccess">
                    <div class="alert alert-success" role="alert"><span>#local.successMsgText#</span><button type="button" class="close" data-dismiss="alert" aria-label="Close" >&times;</button></div>
                </div>
            </cfif>

            <cfif structKeyExists(local,"errorMsgText") AND  len(local.errorMsgText)>
                <div class="row-fluid" id="msgError">
                    <div class="alert alert-danger" role="alert"><span>#local.errorMsgText#</span><button type="button" class="close" data-dismiss="alert" aria-label="Close" >&times;</button></div>
                </div>
            <cfelse>
                
                <cfif NOT val(arguments.event.getValue('success',0))>
                    <fieldset class="tsApp tsAppBodyText">
                        <legend class="tsAppLegendTitle"> &nbsp; Referral Information &nbsp; </legend>
                        <br>
                        <table id="clientFormTbl" width="100%" cellspacing="3" border="0">
                            <tr> 
                                <td class="r"><label>Referral Number:</label></td> 
                                <td></td> 
                                <td><label>#local.clientReferralID#</label></td> 
                            </tr>
                            <tr> 
                                <td class="r"><label>Attorney Name:</label></td> 
                                <td></td> 
                                <td><label>#local.attorneyFullName#</label></td> 
                            </tr>
                            <tr> 
                                <td class="r"><label>Case Closed Date:</label></td> 
                                <td></td> 
                                <td><label>#DateFormat(local.caseClosedDate,'MM/DD/YYYY')#</label></td> 
                            </tr>
                            <tr> 
                                <td class="r"><label for="collectedFee">Fee Collected:</label></td> 
                                <td>*</td> 
                                <td><input name="collectedFee"  id="collectedFee" class="input-large" type="text" maxlength="75" value="#numberFormat(local.caseFees,"0.00")#"></td> 
                            </tr>
                        </table>
                    </fieldset>
                    </br></br>

                    <div align="right" class="form-buttons">
                        <button type="submit" id="saveFeeBtn" class="saveFeeBtn tsAppBodyButton" >Submit Form</button>
                        <button type="button" id="cancelBtn" class="tsAppBodyButton" onClick="parent.location.href='#local.mainurl#';">Cancel</button>
                    </div>
                </cfif>
            </cfif>
        </div>
    </cfform>
</div>

<div id="formSubmitting" style="display:none;text-align:center;margin:50px;">
	<i class="icon-spin icon-spinner icon-3x"></i> <b>Please wait while we process your application.</b>
</div>
</cfoutput>