<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true,true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfset local.selectedResourceID = 0>

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>
<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	<div class="control-group">
		<label class="control-label" for="s_topic">Title:</label>
		<div class="controls">
			<input type="text" name="s_topic" id="s_topic" value="#local.strSearchForm.s_topic#" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_aut">Author:</label>
		<div class="controls">
			<input type="text" name="s_aut" id="s_aut" value="#local.strSearchForm.s_aut#" maxlength="200">
		</div>
	</div>
	<cfif local.settingsStruct.qryBlogs.recordCount GT 0>
		<div class="control-group">
			<label class="control-label" for="s_applicationinstanceid">Blog:</label>
			<div class="controls">
				<select name="s_applicationinstanceid" id="s_applicationinstanceid">
					<option value=""></option>
					<cfloop query="local.settingsStruct.qryBlogs">
						<option value="#local.settingsStruct.qryBlogs.applicationInstanceID#" <cfif listFindNoCase(local.strSearchForm.s_applicationinstanceid, local.settingsStruct.qryBlogs.applicationInstanceID) or local.settingsStruct.qryBlogs.recordCount eq 1>selected="selected" <cfset local.selectedResourceID=local.settingsStruct.qryBlogs.siteResourceID></cfif>>#local.settingsStruct.qryBlogs.applicationInstanceName#</option>
					</cfloop>
				</select>
			</div>
		</div>
	</cfif>
	<cfloop query="local.settingsStruct.qryCategoryTrees">
		<cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
			WITH categories( categoryID, categoryName, parentCategoryID, catlevel, sort)
			AS (
				SELECT categoryID, categoryName, parentCategoryID, 0 AS catlevel, cast(categoryName AS nvarchar(2048))
				FROM dbo.cms_categories 
				WHERE parentCategoryID is NULL
				AND categoryTreeId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#">	
				AND isActive = 1
					UNION ALL
				SELECT c2.categoryID, c2.categoryName, c2.parentCategoryID, catlevel + 1, cast(sort + '|' + c2.categoryName AS nvarchar(2048))
				FROM dbo.cms_categories c2 
				INNER JOIN categories c ON c2.parentCategoryID = c.categoryID
				WHERE categoryTreeId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#">	
				AND isActive = 1
			)
			SELECT categoryID, categoryName, parentCategoryID,catlevel, sort
			FROM categories
			ORDER BY sort
		</cfquery>
		
		<!--- Check to see if category id in this list ---> 
		<cfset local.tmpCategoryID = local.settingsStruct.categoryID>
		<cfif local.tmpCategoryID EQ "">
			<cfset local.tmpCategoryID = 0>
		</cfif>

		<cfquery name="local.isHere" dbtype="query"> 
			SELECT categoryID, categoryName FROM [local].qryCategories WHERE categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.tmpCategoryID#">
		</cfquery>

		<div class="control-group categoryTree_#local.settingsStruct.qryCategoryTrees.controllingSiteResourceID# <cfif local.selectedResourceID NEQ local.settingsStruct.qryCategoryTrees.controllingSiteResourceID>hide</cfif>">
			<label class="control-label" for="s_category">#local.settingsStruct.qryCategoryTrees.categoryTreeName#:</label>
			<cfif local.isHere.recordCount>
				<div class="controls">
					#local.isHere.categoryName#
				</div>				
			<cfelse>
				<div class="controls">
					<select name="s_category" id="s_category" class="catselection" size="5">
						<option value="">Any</option>
						<cfloop query="local.qryCategories">
							<option value="#local.qryCategories.categoryID#" <cfif listFindNoCase(local.strSearchForm.s_category, local.qryCategories.categoryID)>selected="selected"</cfif>>
								<cfif local.qryCategories.parentCategoryID NEQ "">&nbsp;&nbsp;&nbsp;</cfif>
								#local.qryCategories.categoryName#
							</option>
						</cfloop>
					</select>
				</div>
			</cfif>
		</div>
	</cfloop>
	<div class="control-group">
		<label class="control-label">Posted between:</label>
		<div class="controls">
			<input type="text" id="s_postdatefrom" name="s_postdatefrom" value="#DateFormat(local.strSearchForm.s_postdatefrom, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol"> and 
			<input type="text" id="s_postdateto" name="s_postdateto" value="#DateFormat(local.strSearchForm.s_postdateto, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_postdatefrom','s_postdateto');return false;">clear dates</a>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label">Article date between:</label>
		<div class="controls">
			<input type="text" id="s_moddatefrom" name="s_moddatefrom" value="#DateFormat(local.strSearchForm.s_moddatefrom, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol"> and 
			<input type="text" id="s_moddateto" name="s_moddateto" value="#DateFormat(local.strSearchForm.s_moddateto, 'm/d/yyyy')#" maxlength="10" autocomplete="off" class="datecontrol">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_moddatefrom','s_moddateto');return false;">clear dates</a>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one"><b>Without</b> the words:</label>
		<div class="controls">
			<input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>