<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfset local.robotCommands = "nofollow, noindex, noarchive">
<cfheader name="X-Robots-Tag" value="#local.robotCommands#">


<cfif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1 and arguments.event.getValue('mc_siteinfo.useRemoteLoginForm') is 1>
	<cfset local.useRemoteLoginForm = true>
<cfelse>
	<cfset local.useRemoteLoginForm = false>
</cfif>
<cfif arguments.event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct"> 
	<!DOCTYPE HTML>
	<html>
	<head>
		<title>Control Panel Sign In</title>
		<cfoutput>
			<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
			<meta name="robots" content="#local.robotCommands#">
			<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
			<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/css/bamburgh.min.css">
		</cfoutput>
	</head>
	<body>
		<div class="app-wrapper">
			<div class="app-main">
				<div class="app-content p-0">
					<div class="container-fluid h-100">
						<div class="row align-items-center h-100">
							<div class="col-lg-6 d-flex mx-auto">
								<div class="pl-0 pl-lg-5 mx-auto">
									<div class="text-black">
										<h1 class="display-4 text-lg-left text-center mb-3 font-weight-bold">
											<cfoutput>#arguments.event.getValue('mc_siteinfo.sitename')#</cfoutput>
										</h1>
										<p class="font-size-lg text-lg-left text-center mb-0 text-black-50">
											Sign in to access the administrative Control Panel.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	</html>
<cfelse>
	<!DOCTYPE HTML>
	<html>
	<head>
		<title>Control Panel Sign In</title>
		<cfoutput>
			<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
			<meta name="robots" content="#local.robotCommands#">
			<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
			<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/css/bamburgh.min.css">
			<script type="text/javascript" src="/assets/common/javascript/common.js"></script>
			<script type="text/javascript" src="/assets/admin/javascript/admin.js#local.assetCachingKey#"></script>
			<script language="javascript">
				$(function() {
					if ($('input##username').val().length > 0) {
						$('input##password').focus();
					} else {
						$('input##username').focus();
					}
				});
			</script>
		</cfoutput>
	</head>
	<body>
		<div class="app-wrapper">
			<div class="app-main">
				<div class="app-content p-0">
					<div class="container-fluid h-100">
						<div class="row align-items-center h-100">
							<div class="col-lg-6 d-flex mx-auto">
								<div class="pl-0 pl-lg-5 mx-auto">
									<div class="text-black">
										<h1 class="display-4 text-lg-left text-center mb-3 font-weight-bold">
											<cfoutput>#arguments.event.getValue('mc_siteinfo.sitename')#</cfoutput>
										</h1>
										<p class="font-size-lg text-lg-left text-center mb-0 text-black-50">
											Sign in to access the administrative Control Panel.
										</p>
										
										<cfif local.useRemoteLoginForm>
											<div class="mt-3">
												<button type="button" name="btnStaffLogin" class="btn btn-lg btn-primary btn-block" onClick="self.location.href='/?pg=login';">Staff Login</button>
											</div>
											<div class="mt-3 text-center">
												<img src="/assets/admin/images/MCLogo.png" alt="Powered by MemberCentral.com" class="img-fluid" style="cursor:pointer;" onclick="if ($('#mcStaffLogin.d-none')) $('#mcStaffLogin').removeClass('d-none');">
											</div>
											<div id="mcStaffLogin" class="mt-3 text-center d-none">
												<a class="btn btn-sm btn-outline-second btn-block" rel="nofollow" href="/?pg=login&logact=MCStaffLogin">MC Staff Login</a>
											</div>
										<cfelseif arguments.event.getValue('mc_siteinfo.orgcode') EQ 'MC' AND arguments.event.getValue('mc_siteinfo.sitecode') NEQ 'MC'>
											<div class="mt-3 text-center">
												<img src="/assets/admin/images/MCLogo.png" alt="Powered by MemberCentral.com" class="img-fluid" style="cursor:pointer;" onclick="if ($('#mcStaffLogin.d-none')) $('#mcStaffLogin').removeClass('d-none');">
											</div>
											<div id="mcStaffLogin" class="mt-3 text-center">
												<a class="btn btn-sm btn-outline-second btn-block" rel="nofollow" href="/?pg=login&logact=MCStaffLogin">MC Staff Login</a>
											</div>
										<cfelse>
											<div class="mt-3">
												<cfoutput>
												<form name="frmLogin" id="frmLogin" method="post" action="/?pg=login" class="mc_form_login">
													#application.objUser.renderSecurityKeyElement()#
													<div class="form-group">
														<input type="text" name="username" id="username" class="form-control" placeholder="Enter your username" value="#session.cfcuser.memberdata.username#">
													</div>
													<div class="form-group mb-4">
														<input type="password" name="password" id="password" class="form-control" placeholder="Enter your password" value="">
													</div>

													<button type="submit" name="btnLogin" class="btn btn-lg btn-primary btn-block">Login</button>
												</form>
												</cfoutput>
											</div>
											<div class="mt-3 text-center">
												<img src="/assets/admin/images/MCLogo.png" alt="Powered by MemberCentral.com" class="img-fluid" style="cursor:pointer;" onclick="if ($('#mcStaffLogin.d-none')) $('#mcStaffLogin').removeClass('d-none');">
											</div>
											<cfif arguments.event.getValue('mc_siteinfo.sitecode') NEQ "mc">
												<div id="mcStaffLogin" class="mt-3 text-center d-none">
													<a class="btn btn-sm btn-outline-second btn-block" rel="nofollow" href="/?pg=login&logact=MCStaffLogin">MC Staff Login</a>
												</div>
											</cfif>
										</cfif>										
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	</html>
</cfif>
