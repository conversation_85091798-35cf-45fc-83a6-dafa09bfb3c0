<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=MyCLE&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div class="tsAppHeading">NCBA MCLE History</div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact NCBA for assistance.
	</div>
	</cfoutput>
<cfelse>
	<cfscript>
		local.orgCode	= event.getValue('mc_siteInfo.orgCode');
		variables.applicationReservedURLParams 	= "";
		local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	
		arguments.event.paramValue('ca','showList');
		arguments.event.paramValue('periodStartDate',DateFormat(DateFormat(DateAdd("yyyy",-3,Now())),'mm/dd/yyyy'));
		arguments.event.paramValue('periodEndDate',dateFormat(now(),'m/d/yyyy'));
		arguments.event.paramValue('membernumber','');
	
		local.periodStartDate = arguments.event.getValue('periodStartDate');
		local.periodEndDate = arguments.event.getValue('periodEndDate');
		local.membernumber = arguments.event.getValue('membernumber');
		// CUSTOM FIELD: ------------------------------------------------------------------------------------------------------
		local.arrCustomFields = [];
		local.tmpField = { name="myCLELiveEventsText", type="CONTENTOBJ", desc="myCLELiveEventsText", value="EDITABLE INSTRUCTIONS GO HERE" };
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);	
	</cfscript>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberDetails">
			select firstName, middleName, lastName, suffix, professionalSuffix, company 
			from dbo.ams_members
			where memberNumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR">
			AND orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
		</cfquery>
	</cfif>
	
	<!--- CLE history based on event registration with credit SELECTions --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		SELECT a.authorityID, a.authorityName as authorityName, eco.ApprovalNum, 
			r.attended,e.eventid,evr.startDate,evr.endDate,r.registrantID, r.dateRegistered, et.startTime as eventStartDate,
			cl.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,et.startTime) as CLEYear
		FROM dbo.ev_registrants as r
		INNER JOIN dbo.ev_registration as evr on evr.registrationID = r.registrationID AND r.recordedOnSiteID = evr.siteID
		INNER JOIN dbo.ev_events as e on e.eventid = evr.eventid AND e.siteID = evr.siteID
		INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
		INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID AND cl.languageID = 1
		INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID AND rc.creditAwarded = 1 AND r.status='A'
		INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		INNER JOIN dbo.crd_offerings as eco on eco.offeringID = ect.offeringID
		INNER JOIN dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		INNER JOIN dbo.crd_authoritySponsors as ecas on ecas.asid = ast.asid
		INNER JOIN dbo.crd_authorities as a on a.authorityID = ecas.authorityID	
		INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID 
		INNER JOIN dbo.ams_members as m1 on m1.memberID = r.memberID	
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			INNER JOIN dbo.ams_members as m on m.memberID = m1.activeMemberID AND m.orgID = #arguments.event.getValue('mc_siteinfo.orgID')# 
			<cfif local.membernumber NEQ ''>
			AND m.membernumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR"> 	
			</cfif>
		<cfelse>
			INNER JOIN dbo.ams_members as m on m.memberID = m1.activeMemberID
			AND m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		WHERE 1=1  
		 <cfif len(trim(local.periodStartDate))>
			AND et.startTime >=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
		</cfif> 
		<cfif len(trim(local.periodEndDate))>
			AND et.startTime <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodEndDate,"m/d/yyyy")# 23:59:59.997">
		</cfif>
		ORDER BY et.startTime desc, e.eventid, a.authorityName, creditType
	</cfquery> 
	<!--- CLE Totals for Live Conferences & Events --->
	<cfquery name="local.qryCLETotals" dbtype="query">
		SELECT CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
		FROM [local].qryCLE
		GROUP BY CLEYear,  authorityName, creditType
		ORDER BY CLEYear desc, totalCLE
	</cfquery>
	
	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<style type="text/css">
			##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
			.container {
				min-width: auto !important;
			}
			@media print
			{
				body * { visibility: hidden; }
				##zoneMain * { visibility: visible; }				
				##zoneMain {left: 50px; 
					margin-top:0px; 
					page-break-after: avoid;
					page-break-before: avoid; }
			}
			@media (max-width: 979px) and (min-width: 768px) {
				.container {
					width: 724px !important;
				}
			} 
			@media (max-width: 767px) {
				.container {
					width: auto !important;
				}
			}
			td.tsAppBodyText {
				white-space: normal !important;
			}
			@media (max-width: 350px) {
				.filterSection td {
					display: table-row;
				}
			}
			@media only screen and (max-width:770px){ 
				.periodDate{margin-left: 115px;}
			}
			##periodStartDate, ##periodEndDate {cursor:pointer !important;}
		</style>
		<script language="JavaScript">
			function viewEVCert(rid) {
				var certURL = '/?pg=myCLE&panel=viewCert&mode=stream&rid=' + rid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">

	<cfsavecontent variable="local.dataHead">
		<cfoutput>
		<script language="javascript">
			$(function(){
				mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
			});
	
			function _FB_validateForm(){
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					if ($('##membernumber').val() == '') { 
						alert('Must enter MemberNumber before you can filter report.');
						return false;
					}
				</cfif>
				return true;
			}
		</script>
		<style type="text/css">
			##periodStartDate, ##periodEndDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
			.creditTable td, .creditTable th { border:1px solid ##707070; border-collapse:collapse; padding:4px; }
			.balTable td { border:0px; padding:2px; }
			
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

	<cfform method="POST" action="#local.customPage.baseURL#" name="frmCLE" onsubmit="return _FB_validateForm();">
		<cfoutput>
		<div class="row-fluid">
			<span style="float:right;">
				<button class="btn" type="button" onClick="window.print();"><i class="icon-print"></i> Print</button>
			</span>
			<h3><b>NCBA MCLE History</b></h3></br>
			<table cellpadding="4" cellspacing="0" class="filterSection">			
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<tr valign="top">
						<td colspan="4" >
							<b>MemberNumber:</b> &nbsp;
							<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber.">
						</td>
					</tr>
				</cfif>
				<tr valign="top">
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<cfif local.memberDetails.recordcount>
						<td colspan="4" >
						<div>
							<b>#local.memberDetails.firstName# #local.memberDetails.middleName# #local.memberDetails.lastName# #local.memberDetails.suffix#</b><br/>
							#local.memberDetails.company#
						</div>
						</td>
					</cfif>
				<cfelse>		
					<td colspan="4" >
						<div>#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.middlename# #session.cfcuser.memberdata.lastname# #session.cfcuser.memberdata.suffix#</div>
					</td>
				</cfif>
				</tr>
				<tr valign="top">
					<td>
						<b>Select your dates:</b>&nbsp;&nbsp;
						<cfinput type="text"  name="periodStartDate" id="periodStartDate" value="#local.periodStartDate#" size="14"> 
						&nbsp;to&nbsp;
						<cfinput type="text" class="periodDate"  name="periodEndDate" id="periodEndDate" value="#local.periodEndDate#" size="14">
					</td>
					<td>
						<button class="btn" type="submit">Filter Report</button>
					</td>
				</tr>
			</table>
		</div>
		</cfoutput>
	</cfform>
	
	<!--- Live Conferences & Events --->
	<cfoutput>
		<div><h4 style="color: ##000;"><b>Live Conferences & Events</b></h4>
			<div class="tsAppBodyText">
				#local.strPageFields.myCLELiveEventsText#		
			</div>
		</div>
	</cfoutput>	
	<cfif local.qryCLE.recordcount>	
		<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
		<cfloop query="local.qryCLETotals">
			<cfset local.arr[local.qryCLETotals.cleYear][local.qryCLETotals.authorityName][local.qryCLETotals.creditType] = local.qryCLETotals.totalCLE>
		</cfloop>
		<cfoutput>
		<table cellpadding="2" cellspacing="0" border="0">			
			<tr>
				<td class="tsAppBodyText">
					<div class="total">
						<b>Credit Totals</b>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<cfloop collection="#(local.arr)#" item="local.key">
						<div class="block">
							<div class="total yearDate tsAppBodyText">
								<b><u>#local.key#</u></b>
							</div>
							<cfloop collection="#local.arr[local.key]#" item="local.key1">
								<div class="total tsAppBodyText">
									#local.key1#
								</div>
								<cfloop collection="#local.arr[local.key][local.key1]#" item="local.key2">
									<div class="creditType tsAppBodyText">
										#local.arr[local.key][local.key1][local.key2]# #local.key2#
									</div>
								</cfloop>
							</cfloop>
						</div>
					</cfloop>
				</td>
			</tr>
		</table>
		<br/>
		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory">
		<tr class="tsAppBodyText">
			<th width="90">Date</th>
			<th>Title</th>
			<th colspan="2">Credit Awarded</th>
		</tr>
		</cfoutput>	
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText">#dateformat(local.qryCLE.eventStartDate,"mm/dd/yyyy")#</td>
				<td class="tsAppBodyText">#local.qryCLE.contentTitle#</td>
				<td><a href="javascript:viewEVCert('#local.rID#');" title="Print certificate"><i class="icon-print"></i></a></td>
				<td class="tsAppBodyText" nowrap>					
					<table>
						<tr>
							<td>
								<cfoutput group="authorityName">
									#local.qryCLE.authorityName#<br/>
									<cfif len(local.qryCLE.ApprovalNum)>
										Approval Number: #local.qryCLE.ApprovalNum#<br/>
									</cfif>
									<cfoutput>
										#local.qryCLE.creditValueAwarded# #local.qryCLE.creditType#<br/>
									</cfoutput>
									<br>
								</cfoutput>
							</td>
						</tr>
					</table>
				</td>
			</tr>	
		</cfoutput>	
		<cfoutput></table></cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText">		
			There are no Live Conferences & Events to display.
		</div>
		</cfoutput>
	</cfif>
</cfif>