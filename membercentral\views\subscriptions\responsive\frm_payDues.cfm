<cfset local.extrapayJS = "">
<cfinclude template="../commonManageSubsStyles.cfm">
<cfinclude template="../commonSubsPayDuesJS.cfm">

<cfoutput>
<div class="manageSubsApp">
	<div class="mcsubs-card mcsubs-p-4 mcsubs-mb-3">
		<div class="mcsubs-font-size-xl mcsubs-font-weight-bold">#local.qryMember.firstName# #local.qryMember.lastName#</div>
		<cfif len(local.qryMember.company)><div class="mcsubs-text-dim"><small>#local.qryMember.company#</small></div></cfif>
		<div class="mcsubs-d-flex mcsubs-flex-wrap mcsubs-mt-3">
			<div class="mcsubs-col mcsubs-font-weight-bold">
				Amount Due<cfif local.qryOtherInvoices.recordCount gt 0> Today</cfif>: 
				#DollarFormat(val(local.amtDueToday))#<cfif val(local.amtDueTodayTax) gt 0> + #DollarFormat(val(local.amtDueTodayTax))# tax</cfif><cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif>
			</div>
			<cfif local.amtDueToday gt 0>
				<div class="mcsubs-ml-auto mcsubs-mt-sm-3">
					<a href="#local.invoiceLink#" target="_blank"><i class="icon-file-text"></i> View Invoice</a>
				</div>
			</cfif>
		</div>
		<cfif local.qryOtherInvoices.recordcount gt 0>
			<div class="mcsubs-pl-4 mcsubs-mt-5 mcsubs-mb-2">
				<div class="mcsubs-font-weight-bold mcsubs-mb-2">Schedule of remaining invoices:</div>
				<cfloop query="local.qryOtherInvoices">
					<div class="mcsubs-mb-2 mcsubs-pl-1">
						#dollarformat(local.qryOtherInvoices.sumRevTotal)#<cfif local.qryOtherInvoices.sumTaxTotal gt 0> + #dollarformat(local.qryOtherInvoices.sumTaxTotal)# tax</cfif>
						-  Due: #dateFormat(local.qryOtherInvoices.datedue,'m/d/yyyy')#
					</div>
				</cfloop>
			</div>
		</cfif>
	</div>

	<cfif local.showPaymentArea>
		<div class="mcsubs-card mcsubs-mt-5">
			<div class="mcsubs-card-header mcsubs-bg-whitesmoke mcsubs-pb-1">
				<div class="mcsubs-font-size-lg mcsubs-font-weight-bold">Payment Information</div>
			</div>
			<div class="mcsubs-card-body mcsubs-pb-2<cfif local.paymentGateways.recordcount EQ 1> mcsubs-pl-0</cfif>">
				<form name="frmPayDues" id="frmPayDues" method="post" action="/?pg=manageSubscriptions&suba=doPayDues" onsubmit="return checkPayForm();">
					<input type="hidden" name="confirmPurchase" id="confirmPurchase" value="1">
					<input type="hidden" name="profileid" id="profileid" value="0">
					<input type="hidden" name="mid" id="mid" value="#local.memberID#">
					<input type="hidden" name="rsid" id="rsid" value="#local.rootSubscriberID#">
					
					<div id="paymentTabsWrapper">
						<!--- area for payment error --->
						<div id="payDuesErr" style="display:none;margin:6px;"></div>
						
						<div id="paymentTabs">
							<cfif local.paymentGateways.recordcount gt 1>
								<div class="mcsubs-mb-3 mcsubs-text-dim"><b>Choose from the following payment methods:</b></div>

								<ul id="payDuesPmtTabs" class="nav nav-tabs">
									<cfoutput query="local.paymentGateways">
										<li<cfif local.paymentGateways.currentRow eq 1> class="active"</cfif>><a href="##profile#local.paymentGateways.profileID#" data-toggle="tab">#local.paymentGateways.tabTitle#</a></li>
									</cfoutput>
								</ul>
								<div class="tab-content">
									<cfinclude template="frm_payment.cfm">
								</div>
							<cfelse>
								<div id="paymentTypeTabs">
									<cfinclude template="frm_payment.cfm">
								</div>
							</cfif>
						</div>
					</div>
				</form>
			</div>
		</div>
	</cfif>
</div>
</cfoutput>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<style>
		/* stacked tabs styling for screen below 576px */
		@media only screen and (max-width:576px) {
			##paymentTabs > ul.nav-tabs {border-bottom: 0;margin-bottom:0;}
			##paymentTabs > ul.nav-tabs > li {float: none;}
			##paymentTabs > ul.nav-tabs > li > a {margin-right:0;border:1px solid ##ddd;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}
			##paymentTabs > ul.nav-tabs > li:last-child > a {margin-bottom:1px}	
			##paymentTabs > ul.nav-tabs  > li.active a {background-color:##eee!important; }
			##paymentTabs .tab-content {border: 1px solid ##ccc;padding:0.5em;}
		}
	</style>
	<script language="javascript">
	var checkPayFormInProgress = false;
	function checkPayForm() {

		/* disable payment buttons while validation is running */
		$('button[type="submit"]',$('##frmPayDues')).each(function(index,thisButton){
			$(thisButton).attr('disabled','disabled');
		});
	
		var validationPassed = true;

		/* prevent race condition caused by double submitting before validation can be completed */
		if (checkPayFormInProgress) {
			validationPassed = false;
		} else {
			checkPayFormInProgress = true;
			hideAlert();
			var arrReq = new Array();

			<cfif len(local.extrapayJS)>
				var thisForm = document.forms["frmPayDues"];
				#local.extrapayJS#
			</cfif>
			
			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				validationPassed = false;
			}

			if (validationPassed) {
				/* change text of payment buttons and leave disabled */
				$('button[type="submit"]',$('##frmPayDues')).each(function(index,thisButton){
					$(thisButton).text('Please Wait...');
				});
			} else {
				/* reenable buttons */
				$('button[type="submit"]',$('##frmPayDues')).each(function(index,thisButton){
					$(thisButton).removeAttr("disabled");
				});
			}
			checkPayFormInProgress = false;
		}
		
		return validationPassed;
		
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">