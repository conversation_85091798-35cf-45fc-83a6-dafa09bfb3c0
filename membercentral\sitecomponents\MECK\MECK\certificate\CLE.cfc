<cfcomponent output="false">

	<cffunction name="getCertMergeCodes" access="public" output="false" returntype="struct">
		<cfscript>
			var strCertMergeCodes = {
				"member": "prefix,firstname,middlename,lastname,suffix,professionalsuffix,North Carolina_licenseNumber,South Carolina_licenseNumber,Office Address_address1,Office Address_address2,Office Address_city,Office Address_stateprov,Office Address_postalCode,Office Address_Phone,Primary Email"
			};
			return strCertMergeCodes;
		</cfscript>
	</cffunction>
	

	<cffunction name="generateCertBody" access="public" output="false" returntype="string">
		<cfargument name="strCertMergeCodes" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.certificates = arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate>

		<cfset local.groupedAuthorities = structNew()>

        <!--- Group credits by authorityName --->
        <cfloop query="local.certificates">
            <cfif creditValueAwarded GT 0>
                <cfset local.key = authorityName>
                <cfif NOT structKeyExists(local.groupedAuthorities, local.key)>
                    <cfset local.groupedAuthorities[local.key] = []>
                </cfif>
                <cfset arrayAppend(local.groupedAuthorities[local.key], {
                    "courseApproval": courseApproval,
                    "creditValueAwarded": creditValueAwarded,
                    "creditType": creditType
                })>
            </cfif>
        </cfloop>

		<cfsavecontent variable="local.registrantName">
			<cfoutput>#UCASE(arguments.strCertMergeCodes.member.prefix & " " & arguments.strCertMergeCodes.member.firstname & " " & arguments.strCertMergeCodes.member.middlename & " " & arguments.strCertMergeCodes.member.lastname & " " & arguments.strCertMergeCodes.member.suffix & " " & arguments.strCertMergeCodes.member.professionalsuffix)#</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<html>
				<head>
					<style>
						html, body { width:8in; padding:0; margin:0; }
					</style>
				</head>
				<body>
					<div style="font-family:arial,sans-serif;">
						<div>
							<div align="center">
								<img src="#arguments.strCertMergeCodes.certificate.imagesurl#MCB-black_medium.png" height="130" weight="400"/>
							</div>
							<h2 style="margin-top:10px;font-size:21px;font-weight:bold;text-align:center">CERTIFICATE OF CREDIT</h2>
							<div>
								<div style="line-height:18px;font-size:16px;text-align:center;font-style:italic;">This is to acknowledge your completion of the continuing legal education course shown below. This copy
									is for your records and can be submitted to your board of licensing for continuing legal education credit.
									A permanent record of your attendance is maintained by MBA.
								</div>
							</div>
						</div>
						<div style="border-bottom:2px solid ##adadad;padding-top:5px;">&nbsp;</div>
						<div style="font-size:16px;margin-top:15px;"><b>COURSE TITLE : </b> #arguments.strCertMergeCodes.event.qryEventMeta.eventContentTitle#<br></div>
						<cfif dateFormat(arguments.strCertMergeCodes.event.qryEventTimes.startTime,"yyyy-mm-dd") neq dateFormat(arguments.strCertMergeCodes.event.qryEventTimes.endTime,"yyyy-mm-dd")>
							<div style="font-size:16px;margin-top:8px;"><b>DATES : </b>#dateFormat(arguments.strCertMergeCodes.event.qryEventTimes.startTime,"mmmm dd, yyyy")#  to #dateFormat(arguments.strCertMergeCodes.event.qryEventTimes.endTime,"mmmm dd, yyyy")#</div>
						<cfelse>
							<div style="font-size:16px;margin-top:8px;"><b>DATE : </b>#dateFormat(arguments.strCertMergeCodes.event.qryEventTimes.startTime,"mmmm dd, yyyy")#</div> 
						</cfif>
						<div style="margin-top:18px;background:##f2f2f2;">
							<div style="font-size:18px;font-weight:bold;margin-left:10px;margin-top:10px;">ACCREDITING AUTHORITY and APPROVED HOURS:</div>
							 <div style="padding:10px;">
								<cfloop collection="#local.groupedAuthorities#" item="local.key">
									<div style="font-size:17px;font-weight:bold;margin-left:20px;margin-top:10px;">#local.key#</div>
									<div style="line-height:18px;font-size:16px;margin-left:30px;margin-top:4px;">
										
										<cfset local.courseApprovalPrinted = false>
										<cfloop array="#local.groupedAuthorities[local.key]#" index="local.credit">
											<cfif len(local.credit.courseApproval) AND NOT local.courseApprovalPrinted>
												Course Number: #local.credit.courseApproval#<br>
												<cfset local.courseApprovalPrinted = true>
											</cfif>
										</cfloop>

										Approved Hours:
										<cfset local.hoursList = []>
										<cfloop array="#local.groupedAuthorities[local.key]#" index="local.credit">
											<cfset arrayAppend(local.hoursList, "#NumberFormat(local.credit.creditValueAwarded, '____._')# #local.credit.creditType#")>
										</cfloop>
										<cfoutput>#arrayToList(local.hoursList, ", ")#</cfoutput>
									</div>
								</cfloop>
							</div>
						</div>
						<div style="margin-top:25px;">
							<table width="100%">
								<tbody>
									<tr><td style="font-size:18px;font-weight:bold;">ATTENDED BY:</td></tr>
									<tr>
										<td width="50%">#local.registrantName#</td>
										<td width="50%">
											<table style="float:right">
												<tbody>
													<cfset local.hasNC = ListFindNoCase(ValueList(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.state), "NC")>
													<cfset local.hasSC = ListFindNoCase(ValueList(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.state), "SC")>
													<cfif local.hasNC AND structKeyExists(arguments.strCertMergeCodes.member, "North Carolina_licenseNumber") AND len(arguments.strCertMergeCodes.member["North Carolina_licenseNumber"])>
														<tr style="width:1%" nowrap='nowrap'>
															<td>NC&nbsp;License: #arguments.strCertMergeCodes.member["North Carolina_licenseNumber"]#</td>
														</tr>
													</cfif>
													<cfif local.hasSC AND structKeyExists(arguments.strCertMergeCodes.member, "South Carolina_licenseNumber") AND len(arguments.strCertMergeCodes.member["South Carolina_licenseNumber"])>
														<tr style="width:1%" nowrap='nowrap'>
															<td>SC&nbsp;License: #arguments.strCertMergeCodes.member["South Carolina_licenseNumber"]#</td>
														</tr>
													</cfif>
												</tbody>
											</table>
										</td>
									</tr>
									<tr><td>#arguments.strCertMergeCodes.member["Office Address_address1"]#</td></tr>	
									<cfif len(arguments.strCertMergeCodes.member["Office Address_address2"])><tr><td>#arguments.strCertMergeCodes.member["Office Address_address2"]#</td></tr></cfif>	
									<tr><td>#arguments.strCertMergeCodes.member["Office Address_city"]#, #arguments.strCertMergeCodes.member["Office Address_stateprov"]# #arguments.strCertMergeCodes.member["Office Address_postalCode"]#</td></tr>	
									<tr><td>#arguments.strCertMergeCodes.member["Office Address_Phone"]#</td></tr>	
									<tr><td>#arguments.strCertMergeCodes.member["Primary Email"]#</td></tr>	
								</tbody>
							</table>			
						</div>
						<div>
							<div style="border-bottom:1px solid black;margin-top:20px;">&nbsp;</div>
							<div style="font-size:17px;font-weight:bold;margin-top:3px;">Attendee Signature</div>
							<div><img src="#arguments.strCertMergeCodes.certificate.imagesurl#LisaSignature.png" height="50" /></div>
							<div style="border-bottom:1px solid black;margin-top:-25px;">&nbsp;</div>
							<div style="font-size:17px;font-weight:bold;margin-top:3px">Lisa A. Armanini, Director of Continuing Legal Education</div>
							<div style="margin-top:40px">
								<div style="font-size:18px;text-align:center;">Mecklenburg Bar Association</div>
								<div style="font-size:13px;text-align:center;">P.O. Box 30008, Charlotte, North Carolina 28230 <b style="font-size:20px">&diams;</b> (p) ************<b style="font-size:20px">&diams;</b> <a href="http://www.meckbar.org/">www.MeckBar.org</a></div>
							</div>
						</div>
					</div>
				</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>	