<cfinclude template="../commonEventRegJS.cfm">
<cfinclude template="../commonEventRegStyles.cfm">

<cfoutput>
<div id="evRegContainer" class="container-fluid evRegV2">
	<div<cfif local.evRegV2.currentReg.currentStep NEQ 1> class="evreg-card evreg-p-3 evreg-mb-3 evreg-bg-whitesmoke"</cfif>>
		<div class="evreg-d-flex">
			<h3 class="evreg-col evreg-p-0">#encodeForHTML(arguments.strEvent.qryEventMeta.eventContentTitle)#</h3>
			<a class="evreg-col-auto evreg-text-decoration-none evreg-font-size-sm evreg-text-right evreg-align-self-center hidden-phone" href="#arguments.event.getValue('mainurl')#&evAction=showDetail&eid=#arguments.event.getValue('eid')#">
				<i class="icon icon-arrow-left"></i> Back to event details
			</a>
		</div>
		<cfif len(arguments.strEvent.qryEventMeta.eventSubTitle)><div class="evreg-pl-2 evreg-text-dim">#encodeForHTML(arguments.strEvent.qryEventMeta.eventSubTitle)#</div></cfif>
		<div class="evreg-pl-2 evreg-mb-3"><i class="icon-calendar"></i> #local.eventtime#</div>
		<div class="evreg-mb-2 visible-phone">
			<a class="evreg-col-auto evreg-text-decoration-none evreg-font-size-sm evreg-text-right evreg-align-self-center" href="#arguments.event.getValue('mainurl')#&evAction=showDetail&eid=#arguments.event.getValue('eid')#">
				<i class="icon icon-arrow-left"></i> Back to event details
			</a>
		</div>
	</div>
	<cfif local.evRegV2.currentReg.currentStep EQ 1>
		<cfinclude template="eventRegV2_step1.cfm">
	<cfelse>
		<div class="evreg-card evreg-p-3">
			<div class="evreg-mb-3 evreg-font-size-lg evreg-text-dim">Registration for:</div>
			<div class="evreg-d-flex">
				<div class="evreg-d-flex evreg-col evreg-flex-sm-column">
					<cfif local.showMemberPhoto>
						<div><img src="#local.strRegMember.hasPhoto ? '/memberphotosth/#LCASE(local.strRegMember.memberphoto)#' : '/assets/common/images/directory/default.jpg'#" class="evreg-mr-3 evreg-img-thumbnail"></div>
					</cfif>
					<div>
						<h4 class="evreg-mb-1 evreg-mt-1">
							#local.strRegMember.mc_combinedName#
							<cfif len(local.strRegMember.company)><div class="evreg-p-1"><small>#local.strRegMember.company#</small></div></cfif>
						</h4>
						<div class="evreg-mt-1 evreg-p-1">
							<cfif len(local.strRegMember.mc_combinedAddresses)>#local.strRegMember.mc_combinedAddresses#</cfif>
							<cfif len(local.strRegMember.mc_extraInfo)>#local.strRegMember.mc_extraInfo#</cfif>
							<cfif len(local.strRegMember.mc_recordType)><div>#local.strRegMember.mc_recordType#</div></cfif>
							<cfif len(local.strRegMember.mc_memberType)><div>#local.strRegMember.mc_memberType#</div></cfif>
							<cfif len(local.strRegMember.mc_lastlogin)><div>#local.strRegMember.mc_lastlogin#</div></cfif>
						</div>
					</div>
				</div>
				<div class="evreg-d-flex evreg-flex-column evreg-ml-auto">
					<button type="button" class="evreg-btn-outline-primary" onclick="searchEventReg();">Register Someone Else</button>
					<button type="button" class="evreg-btn-outline-primary evreg-mt-3" onclick="doCancelEventReg();">Cancel</button>
				</div>
			</div>
		</div>

		<div id="EvRegStep2" style="display:none;"></div>
		<div id="EvRegStep3" style="display:none;"></div>
		<div id="EvRegStep4" style="display:none;"></div>
		<cfif local.evRegV2.currentReg.registrantID GT 0>
			<div id="EvRegStep5" style="display:none;"></div>
		</cfif>

		<cfif local.evRegV2.currentReg.isRegCartItem EQ 1 OR local.evRegV2.currentReg.registrantID GT 0>
			<cfif local.evRegV2.currentReg.registrantID GT 0>
				<div id="saveEditRegErr" class="alert alert-danger evreg-mt-3" style="display:none;"></div>
				<div id="saveEditRegSaveLoading" class="evreg-mt-3 evreg-text-center" style="display:none;"></div>
			</cfif>
			<div class="evreg-mt-5 evreg-text-center">
				<button type="button" name="btnGotoRegCart" id="btnGotoRegCart" class="btn btn-success btnFinalizeReg" onclick="#local.evRegV2.currentReg.isRegCartItem EQ 1 ? 'regCartCheckout();' : 'saveEditReg();'#" style="width:200px;" disabled>
					<cfif local.evRegV2.currentReg.isRegCartItem EQ 1>Checkout<cfelse>Save Changes</cfif>
				</button>
			</div>
		</cfif>
	</cfif>
</div>
<div id="EvRegLoading" class="evRegV2" style="display:none;">
	<div class="evreg-card evreg-mt-3 evreg-p-3">
		<div class="evreg-skeleton evreg-skeleton-text"></div>
		<div class="evreg-skeleton evreg-skeleton-text"></div>
		<div class="evreg-skeleton evreg-skeleton-text"></div>
		<div class="evreg-skeleton evreg-skeleton-text"></div>
	</div>
</div>
</cfoutput>