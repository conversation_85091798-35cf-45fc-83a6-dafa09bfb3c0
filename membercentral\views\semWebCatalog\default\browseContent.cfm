<cfset local.strFilters = attributes.data.strFilters>
<cfset local.strBrowse = attributes.data.strBrowse>

<cfset local.availableFormats = "">
<cfif attributes.data.semWeb.qrySWP.isConf>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'conf')>
</cfif>
<cfif attributes.data.semWeb.qrySWP.isSWL>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'swl')>
</cfif>
<cfif attributes.data.semWeb.qrySWP.isSWOD>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'swod')>
</cfif>
<cfif (attributes.data.semWeb.qrySWP.isSWL OR attributes.data.semWeb.qrySWP.isSWOD) and attributes.data.hasActiveBundles>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'swb')>
</cfif>
<cfif isDefined("session.mcstruct.deviceProfile.is_bot") AND session.mcstruct.deviceProfile.is_bot is 1>
	<cfset local.isBot = 1>
<cfelse>
	<cfset local.isBot = 0>
</cfif>
<cfinclude template="/views/semwebCatalog/default/swCatalogcommonCSS.cfm">

<cfsavecontent variable="local.browseProgramsJS">
	<cfoutput>
		<!--- Share This Scripts --->
		<script type="text/javascript">var switchTo5x=true;</script>	
		<script type="text/javascript" src="https://ws.sharethis.com/button/buttons.js"></script>
		<script type="text/javascript">stLight.options({publisher:'b8181e8a-537f-4fcd-9253-53d15b7080c3'});</script>

		<script type="text/javascript">
			var #ToScript("#attributes.event.getValue('mainurl')#&panel=browse","swBrowseUrl")#
			var #ToScript("#local.strFilters.URLQueryString#","filtersURLQueryString")#
			
			function initSWBrowsePrograms() {
				if ($('##_swca').length) {
					$("##_swca").multiselect({ multiple:true, noneSelectedText:'Select Credit Authorities', maxWidth:200, header:false, selectedList:1 });
					$("##_swcat").multiselect({ multiple:true, noneSelectedText:'Select Credit Types', maxWidth:200, header:false, selectedList:2 });
					$("##_swcam").multiselect({ multiple:true, noneSelectedText:'Select Credit Amounts', maxWidth:200, header:false, selectedList:3 });

					var arrCreditTypes = '#local.strFilters.creditTypes#'.split(',');
					onChangeSWCreditAuthority(arrCreditTypes);
				}
				if ($('##_sws').length)
					$("##_sws").multiselect({ multiple:true, noneSelectedText:'Select Subjects', maxWidth:200, header:true, selectedList:2 });
				if ($('##_swp').length)
					$("##_swp").multiselect({ multiple:true, noneSelectedText:'Select Publishers', maxWidth:200, header:true, selectedList:1 });

				var #toScript(local.strBrowse.arrAuthors,"sw_arrauthors")#
				
				if (sw_arrauthors.length) {
					var sw_authorsdata = sw_arrauthors.map(function(r){ return { value:r.authorname, id:r.authorid }; });

					$('##sw_speakers')
						.on( "keydown", function( event ) {
							if ( event.keyCode === $.ui.keyCode.TAB &&
								$( this ).autocomplete( "instance" ).menu.active ) {
									event.preventDefault();
								}
						})
						.autocomplete({
							minLength: 0,
							source:sw_authorsdata,
							focus: function() {
								return false;
							},
							select: function( event, ui ) {
								var nonExistingAID = true;
								var arrAID = $('##_swa').val().split(',');
								for (var j=0; j<arrAID.length;j++) { 
									if(arrAID[j]==ui.item.id) { nonExistingAID = false; break; }
								}
								if (nonExistingAID) {
										arrAID.push(ui.item.id);
										arrAID = arrAID.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
									$('##_swa').val(arrAID);

									$('##sw_speakerslist').append('<li id="sw_author'+ui.item.id+'"><a href="##" class="swPrimary" onclick="clearSWAuthorsFilter(this,\'list\','+ui.item.id+');return false;"><i class="bi bi-x" aria-hidden="true"></i> '+ ui.item.value +'</a></li>');
									if ($('##sw_speakerslist').is(':hidden')) $('##sw_speakerslist').show();
								}

								$('##sw_speakers').val('');
								return false;
							}

						});
				}

				<cfif listLen(local.availableFormats) eq 1>
					$(".swBrowsePrograms input.formatCheckbox").on("click", function (e) { $(this).prop("checked",true); });
				</cfif>

				var activeFiltersCount = $('ul.sw_filterSummary li').length;
				$('##swActiveFiltersCount').html(activeFiltersCount + ' active filter' + (activeFiltersCount > 1 ? 's' : ''));
			}
			<cfif NOT local.isBot>
				function openSWVideoPreview(previewVideoURL,title) {
					window.open(previewVideoURL, title, "width=480,height=300,scrollbars=no,titlebar=no,menubar=no,toolbar=no,location=no,status=no,resizable=yes")
				}
			</cfif>
			function onChangeSWCreditAuthority(ct) {
				var #toScript(local.strBrowse.arrCreditsInfo,"arrCreditsInfo")#
				var aidlist = $('##_swca').val() || '';
				$('##_swcat').find('option').remove();
				
				if (aidlist != '' && aidlist.length == 1) {
					var arrCreditsInfoFiltered = arrCreditsInfo.filter(function(c){ return c.authorityid == aidlist[0]; });
					if (arrCreditsInfoFiltered.length) {
						strCreditTypes = arrCreditsInfoFiltered[0].strcredittypes;
						
						var arrCreditTypes = $('##_swca').find(':selected').data('swcredittypes').split(',');
						$.each(arrCreditTypes, function (i,item) {
							$('##_swcat').append( $('<option>', { value:item, text:strCreditTypes[item] }) );
						});
						if (ct && ct.length) $('##_swcat').val(ct);
						$('.sw_ctselect').show();
					}
				} else {
					$('.sw_ctselect').hide();
					$('.sw_camselect').hide();
				}
				$('##_swcat').multiselect('refresh');
				onChangeSWCreditType();
			}
			function onChangeSWCreditType() {
				var aidlist = $('##_swca').val() || '';
				var ctlist = $('##_swcat').val() || '';
				$('##_swcam').find('option').remove();

				// Show credit amount dropdown only if exactly 1 authority and 1 credit type are selected
				if (aidlist != '' && aidlist.length == 1 && ctlist != '' && ctlist.length == 1) {
					// Make AJAX call to get available credit amounts
					var objParams = {
						catalogOrgCode: '#attributes.data.catalogOrgCode#',
						authorityID: aidlist[0],
						creditType: ctlist[0]
					};

					var successCallback = function(data) {
						if (data && data.length > 0) {
							// Store currently selected values or use filter values from server
							var selectedValues = $('##_swcam').val() || [];
							var filterCreditAmounts = '#local.strFilters.creditAmountList#'.split(',').filter(function(v) { return v.trim() !== ''; });

							$.each(data, function(i, item) {
								var isSelected = false;
								// Check if this option should be selected based on current selection or filter
								if (selectedValues.length > 0) {
									isSelected = selectedValues.indexOf(item.value.toString()) !== -1;
								} else if (filterCreditAmounts.length > 0) {
									isSelected = filterCreditAmounts.indexOf(item.value.toString()) !== -1;
								}

								var option = $('<option>', {
									value: item.value,
									text: item.display
								});

								if (isSelected) {
									option.prop('selected', true);
								}

								$('##_swcam').append(option);
							});
							$('.sw_camselect').show();
							$('##_swcam').multiselect('refresh');
						} else {
							$('.sw_camselect').hide();
						}
					};

					var errorCallback = function() {
						$('.sw_camselect').hide();
					};

					TS_AJX('SWBROWSE', 'getCreditAmountsForAuthorityAndType', objParams, successCallback, errorCallback, 10000, errorCallback);
				} else {
					$('.sw_camselect').hide();
					$('##_swcam').val('');
				}
			}
			function clearAllSWFilters() {
				$('input[name="_swft"]').prop('checked',true);
				$('input[name="_swir"],input[name="_swis"]').prop('checked',false);
				$('##_swkwl,##_sws,##_swca,##_swa,##_swp').val('');
				$('##_swca').trigger('change');
				$('.sw_filterSummary').remove();
				searchSWPrograms();
			}
			function clearSWKeywordFilter(el) {
				var keyword = $(el).find('span').text();
				var arrKeywords = $('##_swkwl').val().split('~');
				if (arrKeywords.length) {
					var keywordIndex = arrKeywords.indexOf(keyword);
					if (keywordIndex != -1) {
						arrKeywords.splice(keywordIndex,1);
						$('##_swkwl').val(arrKeywords.length ? arrKeywords.join('~') : '');
					
						$(el).parent().remove();
						if (!arrKeywords.length) 
							$('.sw_filterSummary').remove();
					}
				}
				searchSWPrograms();
			}
			function clearSWFormatsFilter(el) {
				$('input[name="_swft"]:checked').trigger('click');
				$(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWMyProgramsFilter(el) {
				$('input[name="_swir"]:checked').trigger('click');
				$(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWSavedProgramsFilter(el) {
				$('input[name="_swis"]:checked').trigger('click');
				$(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWCreditsFilter(el,mode) {
				$('##_swca').val('').trigger('change');
				$('##_swca').multiselect('refresh');
				$('##_swcam').val('');
				$('##_swcam').multiselect('refresh');
				$('.sw_camselect').hide();
				if (mode == 'summary') $(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWSubjectsFilter(el,mode) {
				$('##_sws').val('');
				$('##_sws').multiselect('refresh');
				if (mode == 'summary') $(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWAuthorsFilter(el,mode,aid) {
				$(el).parent().remove();
				if (mode == 'summary') {
					$('##_swa').val('');
					searchSWPrograms();
				} else {
					var arrAID = $('##_swa').val().split(',').map(Number);
					if (arrAID.indexOf(aid) != -1) {
						arrAID.splice(arrAID.indexOf(aid), 1);
						arrAID = arrAID.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
						$('##_swa').val(arrAID);
					}
				}
			}
			function clearSWPublishersFilter(el,mode) {
				$('##_swp').val('');
				$('##_swp').multiselect('refresh');
				if (mode == 'summary') $(el).parent().remove();
				searchSWPrograms();
			}
			function getFiltersUrlString() {
				var kw = $('##_swkw').val().trim();
				if (kw != '') {
					var arrKeywords = $('##_swkwl').val().length ? $('##_swkwl').val().split('~') : [];
					if ((arrKeywords.length && arrKeywords.indexOf(kw) == -1) || !arrKeywords.length) arrKeywords.push(kw);
					if (arrKeywords.length) $('##_swkwl').val(arrKeywords.join('~'));
				}
				return $("##frmFilterSWPrograms :input").not('##_swkw').filter(function(index, element) { return $(element).attr('name') !== undefined && $(element).val() != '';}).serialize();
			}
			function searchSWPrograms() {
				$('.filter-btn').addClass('disabled').html('<i class="icon-spinner icon-spin"></i> Applying Filters');
				location.href = swBrowseUrl + '&' + getFiltersUrlString();
			}
			function gotoSWBrowsePage(pos) {
				location.href = swBrowseUrl + (filtersURLQueryString.length > 0 ? '&' + filtersURLQueryString : '') + '&_sw_sp=' + pos;
			}
			function sortSWBrowse(opt) {
				$('##_sw_so').val(opt);
				searchSWPrograms();
			}

			$(function() {
				initSWBrowsePrograms();
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.browseProgramsJS#">

<cfoutput>
<!--- wrapper for application --->
<div class="swCatalog swBrowsePrograms">
	<cfif NOT structIsEmpty(local.strBrowse.strCarousel)>
		<div class="SWCatalogBanner">#local.strBrowse.strCarousel.html#</div>
	</cfif>

	<div class="swBrowseProgramsTopLinks" style="margin-bottom:20px;">
		<span style="float:right;">
			<a href="##" onclick="viewSWSavedPrograms(this);return false;" class="swSavedProgramsIconLink" style="padding-left:12px;" title="<cfif attributes.data.savedProgramsCount>View saved</cfif>">
				<i class="bi bi-heart-fill sw_savedprogramscounticon <cfif attributes.data.savedProgramsCount>swRed swIconBadge<cfelse>swMuted</cfif>" data-swsavedprogramscount="#attributes.data.savedProgramsCount#" aria-hidden="true"></i>
			</a>
			<cfif attributes.data.swRegCart.recordCount>
				<cfif NOT local.isBot>
					<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="swPrimary" title="View Cart" style="padding-left:12px;">
						<i class="bi bi-cart-fill sw_regcartcounticon swIconBadge" aria-hidden="true" data-swregcartcount="#attributes.data.swRegCart.recordCount#" aria-hidden="true"></i>
					</a>
				</cfif>
			<cfelse>
				<a href="javascript:void(0);" style="padding-left:12px;"><i class="bi bi-cart-fill swMuted" aria-hidden="true"></i></a>
			</cfif>
			<a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary" title="#attributes.data.qrySWP.brandMyCLETab#" style="padding-left:12px;">
				<i class="bi bi-person-lines-fill" aria-hidden="true"></i>
			</a>
			<a href="#attributes.event.getValue('mainurl')#&panel=showFAQ" class="swPrimary" title="FAQ" style="padding-left:12px;"><i class="bi bi-question-circle-fill" aria-hidden="true"></i></a>
			<a href="#attributes.event.getValue('mainurl')#" class="swPrimary" title="Catalog Home" style="padding-left:12px;"><i class="bi bi-house-fill" aria-hidden="true"></i></a>
		</span>
		<span>
			<cfif listFindNoCase(local.availableFormats,"conf")>
				<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=conf" class="mcEvent tsAppBodyText sw-text-uppercase">
					<i class="bi bi-geo-alt-fill sw-mr-1" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandConfTab#
				</a>
			</cfif>
			<cfif listFindNoCase(local.availableFormats,"swl")>
				<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=swl" class="swWebinar tsAppBodyText sw-text-uppercase" style="padding-left:12px;">
					<i class="bi bi-laptop sw-mr-1" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandSWLTab#
				</a>
			</cfif>
			<cfif listFindNoCase(local.availableFormats,"swod")>
				<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=swod" class="swOnDemand tsAppBodyText sw-text-uppercase" style="padding-left:12px;">
					<i class="bi bi-play-circle sw-mr-1" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandSWODTab#
				</a>
			</cfif>
			<cfif listFindNoCase(local.availableFormats,"swb")>
				<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=swb" class="swBundle tsAppBodyText sw-text-uppercase" style="padding-left:12px;">
					<i class="bi bi-basket sw-mr-1" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandBundleTab#
				</a>
			</cfif>
		</span>
	</div>

	<hr />

	<table class="tsAppBodyText" style="width:100%;">
		<tr>
			<td class="tsAppBodyText" style="width:25%;">
				<span id="swActiveFiltersCount"></span>
			</td>
			<td class="tsAppBodyText" style="width:45%; text-align:center;">
				<cfif local.strBrowse.strPagination.totalCount gt 0>
					<cfif local.strBrowse.strPagination.totalCount GT local.strBrowse.strPagination.count>
						<span class="muted">showing #local.strBrowse.strPagination.startPos#-<cfif ((local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count) - 1) lte local.strBrowse.strPagination.totalCount>#local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count - 1#<cfelse>#local.strBrowse.strPagination.totalCount#</cfif> of #local.strBrowse.strPagination.totalCount# results</span>
					<cfelse>
						<span class="muted">showing #local.strBrowse.strPagination.totalCount# result<cfif local.strBrowse.strPagination.totalCount gt 1>s</cfif></span>
					</cfif>
				</cfif>
			</td>
			<td class="tsAppBodyText" style="width:30%;text-align:right;">
				Sort By:
				<select name="sw_browsesort" id="sw_browsesort" class="tsAppBodyText" onchange="sortSWBrowse(this.value);">
					<option value="date"<cfif local.strFilters.sortoption eq 'date'> selected</cfif>>Date</option>
					<option value="nameasc"<cfif local.strFilters.sortoption eq 'nameasc'> selected</cfif>>Name A-Z</option>
					<option value="namedesc"<cfif local.strFilters.sortoption eq 'namedesc'> selected</cfif>>Name Z-A</option>
					<option value="priceasc"<cfif local.strFilters.sortoption eq 'priceasc'> selected</cfif>>Price Low-High</option>
					<option value="pricedesc"<cfif local.strFilters.sortoption eq 'pricedesc'> selected</cfif>>Price High-Low</option>
					<cfif len(local.strFilters.keywordsList)>
						<option value="rank"<cfif local.strFilters.sortoption eq 'rank'> selected</cfif>>Relevancy</option>
					</cfif>
				</select>
			</td>
		</tr>
		<tr><td colspan="3"><hr /></td></tr>
		<tr>
			<td style="width:25%;" class="sw-bg-browsefilter sw-align-top">
				<form name="frmFilterSWPrograms" id="frmFilterSWPrograms" onsubmit="searchSWPrograms();return false;">
					<input type="hidden" name="_swkwl" id="_swkwl" value="#local.strFilters.keywordsList#">
					<input type="hidden" name="_swa" id="_swa" value="#local.strFilters.authorIDList#">
					<input type="hidden" name="_sw_so" id="_sw_so" value="#local.strFilters.sortoption#">

					<div class="tsAppBodyText">
						<cfif local.strFilters.hasSearchParams>
							<ul class="sw_filterSummary sw-mt-1">
								<cfloop list="#local.strFilters.keywordsList#" index="local.thisKeyword" delimiters="~">
									<li>
										<a href="##" onclick="clearSWKeywordFilter(this);return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i> "<span>#local.thisKeyword#</span>"
										</a>
									</li>
								</cfloop>
								<cfif listLen(local.strFilters.formats)>
									<li>
										<a href="##" onclick="clearSWFormatsFilter(this);return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">format (#listLen(local.strFilters.formats)#)</span>
										</a>
									</li>
								</cfif>
								<cfif local.strFilters.isRegistered>
									<li>
										<a href="##" onclick="clearSWMyProgramsFilter(this);return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">Registered</span>
										</a>
									</li>
								</cfif>
								<cfif local.strFilters.isSaved>
									<li>
										<a href="##" onclick="clearSWSavedProgramsFilter(this);return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">saved</span>
										</a>
									</li>
								</cfif>
								<cfif listLen(local.strFilters.creditAuthorityList)>
									<li>
										<a href="##" onclick="clearSWCreditsFilter(this,'summary');return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">credit (#listLen(local.strFilters.creditAuthorityList)#)</span>
										</a>
									</li>
								</cfif>
								<cfif listLen(local.strFilters.subjects)>
									<li>
										<a href="##" onclick="clearSWSubjectsFilter(this,'summary');return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">subject areas (#listLen(local.strFilters.subjects)#)</span>
										</a>
									</li>
								</cfif>
								<cfif listLen(local.strFilters.authorIDList)>
									<li>
										<a href="##" onclick="clearSWAuthorsFilter(this,'summary');return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">speaker (#listLen(local.strFilters.authorIDList)#)</span>
										</a>
									</li>
								</cfif>
								<cfif listLen(local.strFilters.participantIDList)>
									<li>
										<a href="##" onclick="clearSWPublishersFilter(this,'summary');return false;" class="swMuted">
											<i class="bi bi-x" aria-hidden="true"></i><span class="sw-pl-2">publisher (#listLen(local.strFilters.participantIDList)#)</span>
										</a>
									</li>
								</cfif>
							</ul>
							<div class="sw_filterSummary" style="text-align:right;">
								<small><a href="##" class="clear swMuted" onclick="clearAllSWFilters();return false;">clear all</a></small>
							</div>
						</cfif>
						<div class="tsAppBodyText sw-mt-4">
							<div class="sw-d-flex sw-pl-3">
								<input type="text" name="_swkw" id="_swkw" class="sw-m-0" style="width:80%;" placeholder="keyword" autocomplete="off">
								<button type="submit" class="sw-btn-link sw-p-0"><i class="bi bi-search"></i></button>
							</div>
						</div>
					</div>
					<div class="swBrowseFilters sw-mt-3">
						<div class="swbrowse-filter-group">
							<div class="swbrowse-filter-heading">Format</div>
							<div class="swbrowse-filter-body">
								<div class="sw-pl-3 sw-pt-1">
									<cfif listFindNoCase(local.availableFormats,"conf")>
										<div>
											<label>
												<small>
													<input type="checkbox" name="_swft" value="conf"<cfif listFindNoCase(local.strFilters.formats,'conf')> checked</cfif>>
													#attributes.data.semWeb.qrySWP.brandConfTab#
												</small>
											</label>
										</div>
									</cfif>
									<cfif listFindNoCase(local.availableFormats,"swl")>
										<div>
											<label>
												<small>
													<input type="checkbox" name="_swft" value="swl"<cfif listFindNoCase(local.strFilters.formats,'swl')> checked</cfif>>
													#attributes.data.semWeb.qrySWP.brandSWLTab#
												</small>
											</label>
										</div>
									</cfif>
									<cfif listFindNoCase(local.availableFormats,"swod")>
										<div>
											<label>
												<small>
													<input type="checkbox" name="_swft" value="swod"<cfif listFindNoCase(local.strFilters.formats,'swod')> checked</cfif>>
													#attributes.data.semWeb.qrySWP.brandSWODTab#
												</small>
											</label>
										</div>
									</cfif>
									<cfif listFindNoCase(local.availableFormats,"swb")>
										<div>
											<label>
												<small>
													<input type="checkbox" name="_swft" value="swb"<cfif listFindNoCase(local.strFilters.formats,'swb')> checked</cfif>>
													#attributes.data.semWeb.qrySWP.brandBundleTab#
												</small>
											</label>
										</div>
									</cfif>
								</div>
							</div>
						</div>
						<div class="swbrowse-filter-group sw-mt-3">
							<div class="swbrowse-filter-heading">My Programs</div>
							<div class="swbrowse-filter-body">
								<div class="sw-pl-3 sw-pt-1">
									<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
										<div>
											<label>
												<small>
													<input type="checkbox" name="_swir" value="1"<cfif local.strFilters.isRegistered> checked</cfif>>
													Registered
												</small>
											</label>
										</div>
									</cfif>
									<div>
										<label>
											<small>
												<input type="checkbox" name="_swis" value="1"<cfif local.strFilters.isSaved> checked</cfif>>
												Saved
											</small>
										</label>
									</div>
								</div>
							</div>
						</div>
						<cfif arrayLen(local.strBrowse.arrCreditsInfo)>
							<div class="swbrowse-filter-group sw-mt-3">
								<div class="swbrowse-filter-heading">Credit</div>
								<div class="swbrowse-filter-body">
									<div class="sw-pl-3 sw-pt-1">
										<div class="sw-pt-2">Credit Authority:</div>
										<select name="_swca" id="_swca" multiple="multiple" onchange="onChangeSWCreditAuthority();">
											<cfloop array="#local.strBrowse.arrCreditsInfo#" index="local.thisCreditInfo">
												<option value="#local.thisCreditInfo.authorityID#" data-swcredittypes="#lCase(structKeyList(local.thisCreditInfo.strCreditTypes))#"<cfif listFind(local.strFilters.creditAuthorityList,local.thisCreditInfo.authorityID)> selected</cfif>>#local.thisCreditInfo.authorityName#</option>
											</cfloop>
										</select>
										<div class="sw_ctselect sw-mt-3">
											<div class="sw-pt-2">Credit Types:</div>
											<select id="_swcat" name="_swcat" multiple="multiple" onchange="onChangeSWCreditType();"></select>
										</div>
										<div class="sw_camselect sw-mt-3" style="display:none;">
											<div class="sw-pt-2">Credit Amount:</div>
											<select id="_swcam" name="_swcam" multiple="multiple"></select>
										</div>
										<div class="sw-mt-2" style="text-align:right;">
											<small><a href="##" onclick="clearSWCreditsFilter(this,'select');return false;" class="swMuted">clear</a></small>
										</div>
									</div>
								</div>
							</div>
						</cfif>
						<cfif local.strBrowse.qryCategories.recordCount>
							<div class="swbrowse-filter-group sw-mt-3">
								<div class="swbrowse-filter-heading">Subject Areas</div>
								<div class="swbrowse-filter-body">
									<div class="sw-pl-3 sw-pt-1">
										<select name="_sws" id="_sws" multiple="multiple">
											<cfloop query="local.strBrowse.qryCategories">
												<option value="#local.strBrowse.qryCategories.categoryValue#"<cfif listFindNoCase(local.strFilters.subjects,local.strBrowse.qryCategories.categoryValue)> selected</cfif>>#local.strBrowse.qryCategories.categoryName#</option>
											</cfloop>
										</select>
										<div class="sw-mt-2" style="text-align:right;">
											<small><a href="##" onclick="clearSWSubjectsFilter(this,'select');return false;" class="swMuted">clear</a></small>
										</div>
									</div>
								</div>
							</div>
						</cfif>
						<cfif arrayLen(local.strBrowse.arrAuthors)>
							<div class="swbrowse-filter-group sw-mt-3">
								<div class="swbrowse-filter-heading">Speaker</div>
								<div class="swbrowse-filter-body">
									<div class="sw-pl-3 sw-pt-1">
										<div class="tsAppBodyText">
											<ul id="sw_speakerslist" class="sw-m-0 sw-pb-4" <cfif NOT listLen(local.strFilters.authorIDList)>style="display:none;"</cfif>>
												<cfif listLen(local.strFilters.authorIDList)>
													<cfloop array="#local.strBrowse.arrAuthors#" index="local.thisAuthor">
														<cfif listFind(local.strFilters.authorIDList,local.thisAuthor.authorid)>
															<li class="sw-pb-2">
																<a href="##" onclick="clearSWAuthorsFilter(this,'list',#local.thisAuthor.authorid#);return false;">
																	<i class="bi bi-x" aria-hidden="true"></i> #local.thisAuthor.authorname#
																</a>
															</li>
														</cfif>
													</cfloop>
												</cfif>
											</ul>
											<div class="tsAppBodyText">
												<p class="sw-mt-1 sw-mb-2"><small class="swMuted">Begin typing then click or tap the speaker's name to add to filters.</small></p>
												<input type="text" name="sw_speakers" id="sw_speakers" class="sw-pr-0" style="width:180px;" placeholder="Start typing name...">
											</div>
										</div>
									</div>
								</div>
							</div>
						</cfif>
						<cfif local.strBrowse.qryPublishers.recordCount gt 1>
							<div class="swbrowse-filter-group sw-mt-3">
								<div class="swbrowse-filter-heading">Publisher</div>
								<div class="swbrowse-filter-body">
									<div class="sw-pl-3 sw-pt-1">
										<select name="_swp" id="_swp" multiple="multiple">
											<cfloop query="local.strBrowse.qryPublishers">
												<option value="#local.strBrowse.qryPublishers.participantID#"<cfif listFindNoCase(local.strFilters.participantIDList,local.strBrowse.qryPublishers.participantID)> selected</cfif>>#local.strBrowse.qryPublishers.publisherName#</option>
											</cfloop>
										</select>
										<div class="sw-mt-2" style="text-align:right;">
											<small><a href="##" class="clear swMuted" onclick="clearSWPublishersFilter(this,'select');return false;">clear</a></small>
										</div>
									</div>
								</div>
							</div>
						</cfif>
					</div>
					<button type="button" class="filter-btn sw-mt-3" onclick="searchSWPrograms();return false;" style="float:right;height:30px;">Apply Filters</button>
				</form>
			</td>
			<td style="width:75%;" class="sw-align-top" colspan="2">
				<cfif arrayLen(local.strBrowse.arrPrograms)>
					<cfloop array="#local.strBrowse.arrPrograms#" index="local.thisProgram">
						<cfswitch expression="#local.thisProgram.ft#">
							<cfcase value="SWL">
								<cfset local.programDetailPageLink = "#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.programID#">
								<cfif len(local.thisProgram.dspCredits)>
									<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{creditsViewLink}}', local.programDetailPageLink & "&jumpTo=creditSection")>
									<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{classList}}', "swPrimary")>
								</cfif>
								<cfif len(local.thisProgram.programSubTitle)>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
								<cfelse>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
								</cfif>
								<!--- is registration closed? reg closes when event starts or when allowregistrants is 0--->
								<cfset local.isRegOpen = true>
								<cfif now() gte local.thisProgram.dspStartDate OR NOT local.thisProgram.allowRegistrants>
									<cfset local.isRegOpen = false>
								</cfif>

								<table class="tsAppBodyText sw-mb-3" style="width:100%;border-bottom:1px solid ##ccc;">
									<tr>
										<cfif len(local.thisProgram.featuredImagePath)>
											<td class="sw-align-top" style="width:100px;">
												<img src="#local.thisProgram.featuredImagePath#" style="width:100px;height:100px;" />
												<br /><br />
												<div class="sw-d-flex">
													<span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
												</div>
											</td>
										</cfif>
										<td class="sw-align-top">
											<cfif local.thisProgram.isFeatured><span class="swFtdProgramLabel">Featured</span></cfif>
											<h5 class="prgmTitle sw-mt-0">
												<a href="#local.programDetailPageLink#" class="swPrimary">
													#local.thisProgram.programTitle#
												</a>
												<cfif NOT local.isBot>
													<a href="javascript:void(0);" class="swHeartIcon swMuted" title="Save for later">
														<i class="sw_saveforlater bi <cfif local.thisProgram.isSaved>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swl" data-swsaveforlatermode="browse" aria-hidden="true"></i>
													</a>
												</cfif>
											</h5>
											<cfif len(local.thisProgram.programSubTitle)>
												<h6 class="swPrimary sw-mt-0">#local.thisProgram.programSubTitle#</h6>
											</cfif>
											<p>#Replace(DateTimeFormat(local.thisProgram.dspStartDate,'dddd, m/d/yyyy - h:nn aa'),":00 ","")# #UCASE(local.thisProgram.dspTZ)#</p>
											<cfif len(local.thisProgram.dspCredits)><p>#local.thisProgram.dspCredits#</p></cfif>
											<cfif len(local.thisProgram.incBundlesList)>
												<p><small>Available in bundle(s).</small></p>
											</cfif>
											<cfif local.thisProgram.linkedSWODSeminarID gt 0><p><small>Recording will be available on-demand.</small></p></cfif>
											<cfif NOT len(local.thisProgram.featuredImagePath)>
												<div class="sw-d-flex sw-mt-2">
													<span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle#></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle#></span>
													<span class="st_twitter" #local.shareThisURLAndTitle#></span>
												</div>
											</cfif>
										</td>
										<td class="sw-align-top" style="width:205px;">
											<p class="swWebinar sw-m-0 sw-text-uppercase"><i class="bi bi-laptop" aria-hidden="true"></i> #local.thisProgram.programBrand#</p>
											<cfif local.thisProgram.attended>
												<p class="sw-mt-2 swAttended"><i class="bi bi-person-check-fill"></i> Attended<small><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary sw-ml-2">#attributes.data.qrySWP.brandMyCLETab#</a></small></p>
											<cfelseif local.thisProgram.isRegistered>
												<p class="sw-mt-2 swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
											</cfif>
											<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
												<div class="sw-mt-1">
													<cfif local.thisRate.price gt 0>
														#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
													<cfelse>
														#Replace(local.thisProgram.freeRateDisplay,'.00','')# for #local.thisRate.description#
													</cfif>
												</div>
											</cfloop>
											<div class="sw-mt-2">
												<table class="sw-w-100">
													<tr>
														<td>
															<a href="#local.programDetailPageLink#">
																<button type="button" class="sw-btn-vwdetail">View Details</button>
															</a>
														</td>
														<td>
															<cfif NOT local.isBot>
																<cfif local.thisProgram.inCart>
																	<a href="#attributes.event.getValue('mainurl')#&panel=showCart">
																		<button type="button" class="sw-btn-vwdetail">In Cart</button>
																	</a>
																<cfelseif local.isRegOpen AND len(local.thisProgram.arrRates)>
																	<a href="#attributes.event.getValue('mainurl')#&panel=reg&item=SWL-#local.thisProgram.programID#">
																		<button type="button" class="sw-btn-vwdetail">Add to Cart</button>
																	</a>
																</cfif>
															</cfif>
														</td>
													</tr>
												</table>
											</div>
										</td>
									</tr>
									<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
										<tr>
											<td class="sw-align-top" colspan="<cfif len(local.thisProgram.featuredImagePath)>3<cfelse>2</cfif>">
												<p class="sw-mb-0"><b>#attributes.data.semWeb.qrySWP.brandLearnObjectives#</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
														<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
													</cfloop>
												</ul>
											</td>
										</tr>
									</cfif>
									<cfif structKeyExists(local.thisProgram, "arrSpeakers") AND arrayLen(local.thisProgram.arrSpeakers)>
										<tr>
											<td class="sw-align-top" colspan="<cfif len(local.thisProgram.featuredImagePath)>3<cfelse>2</cfif>">
												<p class="sw-mb-0"><b>Speakers</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrSpeakers#" index="local.thisSpeaker">
														<li class="swSpeakersPoint">#local.thisSpeaker.speakerName#</li>
													</cfloop>
												</ul>
											</td>
										</tr>
									</cfif>
								</table>
							</cfcase>
							<cfcase value="SWOD">
								<cfset local.programDetailPageLink = "#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.programID#">
								<cfif len(local.thisProgram.dspCredits)>
									<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{creditsViewLink}}', local.programDetailPageLink & "&jumpTo=creditSection")>
									<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{classList}}', "swPrimary")>
								</cfif>
								<cfif len(local.thisProgram.programSubTitle)>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
								<cfelse>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
								</cfif>
								<!--- is registration closed? reg closes when event starts or when allowregistrants is 0--->
								<cfset local.isRegOpen = true>
								<cfif NOT local.thisProgram.allowRegistrants>
									<cfset local.isRegOpen = false>
								</cfif>

								<table class="tsAppBodyText sw-mb-3" style="width:100%;border-bottom:1px solid ##ccc;">
									<tr>
										<cfif len(local.thisProgram.featuredImagePath)>
											<td class="sw-align-top" style="width:100px;">
												<img src="#local.thisProgram.featuredImagePath#" style="width:100px;height:100px;" />
												<br /><br />
												<div class="sw-d-flex">
													<span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
												</div>
											</td>
										</cfif>
										<td class="sw-align-top">
											<cfif local.thisProgram.isFeatured><span class="swFtdProgramLabel">Featured</span></cfif>
											<h5 class="prgmTitle sw-mt-0">
												<a href="#local.programDetailPageLink#" class="swPrimary">	
													#local.thisProgram.programTitle#
												</a>
												<cfif NOT local.isBot>
													<a href="javascript:void(0);" class="swHeartIcon swMuted" title="Save for later">
														<i class="sw_saveforlater bi <cfif local.thisProgram.isSaved>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swod" data-swsaveforlatermode="browse" aria-hidden="true"></i>
													</a>
												</cfif>
											</h5>
											<cfif len(local.thisProgram.programSubTitle)>
												<h6 class="swPrimary sw-mt-0">#local.thisProgram.programSubTitle#</h6>
											</cfif>
											<cfif len(local.thisProgram.dspCredits)><p>#local.thisProgram.dspCredits#</p></cfif>
											<cfif len(local.thisProgram.incBundlesList)>
												<p><small>Available in bundle(s).</small></p>
											</cfif>
											<cfif NOT len(local.thisProgram.featuredImagePath)>
												<div class="sw-d-flex sw-mt-2">
													<span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle#></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle#></span>
													<span class="st_twitter" #local.shareThisURLAndTitle#></span>
												</div>
											</cfif>
											<cfif NOT local.isBot and len(local.thisProgram.videoPreviewLink)>
												<div class="sw-mt-2">
													<a href="##" title="Preview" onclick="openSWVideoPreview('#jsStringFormat(local.thisProgram.videoPreviewLink)#','#jsStringFormat(local.thisProgram.videoPreviewDisplayName)#')">
														<i class="bi bi-play-circle" aria-hidden="true"></i> Video Preview
													</a>
												</div>
											</cfif>
										</td>
										<td class="sw-align-top" style="width:205px;">
											<p class="swOnDemand sw-m-0 sw-text-uppercase"><i class="bi bi-play-circle" aria-hidden="true"></i> #local.thisProgram.programBrand#</p>
											<cfif local.thisProgram.passed>
												<p class="sw-mt-2 swAttended"><i class="bi bi-person-check-fill"></i> Completed<small><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary sw-ml-2">#attributes.data.qrySWP.brandMyCLETab#</a></small></p>
											<cfelseif local.thisProgram.isRegistered>
												<p class="sw-mt-2 swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
											</cfif>
											<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
												<div class="sw-mt-1">
													<cfif local.thisRate.price gt 0>
														#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
													<cfelse>
														#Replace(local.thisProgram.freeRateDisplay,'.00','')# for #local.thisRate.description#
													</cfif>
												</div>
											</cfloop>
											<div class="sw-mt-2">
												<table class="sw-w-100">
													<tr>
														<td>
															<a href="#local.programDetailPageLink#">
																<button type="button" class="sw-btn-vwdetail">View Details</button>
															</a>
														</td>
														<td>
															<cfif NOT local.isBot>
																<cfif local.thisProgram.inCart>
																	<a href="#attributes.event.getValue('mainurl')#&panel=showCart">
																		<button type="button" class="sw-btn-vwdetail">In Cart</button>
																	</a>
																<cfelseif local.isRegOpen AND  len(local.thisProgram.arrRates)>
																	<a href="#attributes.event.getValue('mainurl')#&panel=reg&item=SWOD-#local.thisProgram.programID#">
																		<button type="button" class="sw-btn-vwdetail">Add to Cart</button>
																	</a>
																</cfif>
															</cfif>
														</td>
													</tr>
												</table>
											</div>
										</td>
									</tr>
									<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
										<tr>
											<td class="sw-align-top" colspan="<cfif len(local.thisProgram.featuredImagePath)>3<cfelse>2</cfif>">
												<p class="sw-mb-0"><b>#attributes.data.semWeb.qrySWP.brandLearnObjectives#</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
														<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
													</cfloop>
												</ul>
											</td>
										</tr>
									</cfif>
									<cfif structKeyExists(local.thisProgram, "arrSpeakers") AND arrayLen(local.thisProgram.arrSpeakers)>
										<tr>
											<td class="sw-align-top" colspan="<cfif len(local.thisProgram.featuredImagePath)>3<cfelse>2</cfif>">
												<p class="sw-mb-0"><b>Speakers</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrSpeakers#" index="local.thisSpeaker">
														<li class="swSpeakersPoint">#local.thisSpeaker.speakerName#</li>
													</cfloop>
												</ul>
											</td>
										</tr>
									</cfif>
								</table>
							</cfcase>
							<cfcase value="EV">
								<cfif len(local.thisProgram.programSubTitle)>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.thisProgram.eventDetailLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
								<cfelse>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.thisProgram.eventDetailLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
								</cfif>

								<table class="tsAppBodyText sw-mb-3" style="width:100%;border-bottom:1px solid ##ccc;">
									<tr>
										<cfif len(local.thisProgram.featuredImagePath)>
											<td class="sw-align-top" style="width:100px;">
												<img src="#local.thisProgram.featuredImagePath#" style="width:100px;height:100px;" />
												<br /><br />
												<div class="sw-d-flex">
													<span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
												</div>
											</td>
										</cfif>
										<td class="sw-align-top">
											<cfif local.thisProgram.isFeatured><span class="swFtdProgramLabel">Featured</span></cfif>
											<h5 class="prgmTitle sw-mt-0">
												<a href="#local.thisProgram.eventDetailLink#" class="swPrimary">
													#local.thisProgram.programTitle#
												</a>
												<cfif NOT local.isBot>
													<a href="javascript:void(0);" class="swHeartIcon swMuted" title="Save for later">
														<i class="sw_saveforlater bi <cfif local.thisProgram.isSaved>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="ev" data-swsaveforlatermode="browse" aria-hidden="true"></i>
													</a>
												</cfif>
											</h5>
											<cfif len(local.thisProgram.programSubTitle)>
												<h6 class="swPrimary sw-mt-0">#local.thisProgram.programSubTitle#</h6>
											</cfif>
											<p>#Replace(DateTimeFormat(local.thisProgram.displayStartTime,'dddd, m/d/yyyy - h:nn aa'),":00 ","")# #UCASE(local.thisProgram.displayTimeZoneAbbr)#</p>
											<cfif len(local.thisProgram.programLocation)>
												<p>#local.thisProgram.programLocation#</p>
											</cfif>
											<cfif NOT len(local.thisProgram.featuredImagePath)>
												<div class="sw-d-flex sw-mt-2">
													<span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle#></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle#></span>
													<span class="st_twitter" #local.shareThisURLAndTitle#></span>
												</div>
											</cfif>
										</td>
										<td class="sw-align-top" style="width:205px;">
											<p class="mcEvent sw-m-0 sw-text-uppercase"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i> #local.thisProgram.programBrand#</p>
											<cfif local.thisProgram.isRegistered>
												<p class="sw-mt-2 swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
											</cfif>
											<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
												<div class="sw-mt-1">
													<cfif local.thisRate.rate gt 0>
														#Replace(DollarFormat(local.thisRate.rate),'.00','')##local.thisProgram.displayedCurrencyType# #local.thisRate.rateName#
													<cfelse>
														#Replace(local.thisRate.freeRateDisplay,'.00','')# for #local.thisRate.rateName#
													</cfif>
												</div>
											</cfloop>
											<div class="sw-mt-2">
												<table class="sw-w-100">
													<tr>
														<td>
															<a href="#local.thisProgram.eventDetailLink#" target="_blank">
																<button type="button" class="sw-btn-vwdetail">View Details</button>
															</a>
														</td>
														<td>
															<cfif NOT local.isBot>
																<cfif local.thisProgram.inCart>
																	<a href="/?pg=events&regcartv2">
																		<button type="button" class="sw-btn-vwdetail">In Cart</button>
																	</a>
																<cfelseif local.thisProgram.allowRegister and (local.thisProgram.showRates or len(local.thisProgram.altRegistrationURL))>
																	<a href="#local.thisProgram.eventRegV2Link#" target="_blank">
																		<button type="button" class="sw-btn-vwdetail">Add to Cart</button>
																	</a>
																</cfif>
															</cfif>
														</td>
													</tr>
												</table>
											</div>
										</td>
									</tr>
								</table>
							</cfcase>
							<cfcase value="SWB">
								<cfset local.programDetailPageLink = "#attributes.event.getValue('mainurl')#&panel=showBundle&bundleid=#local.thisProgram.programID#">
								<cfif len(local.thisProgram.programSubTitle)>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
								<cfelse>
									<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
								</cfif>
								
								<table class="tsAppBodyText sw-mb-3" style="width:100%;border-bottom:1px solid ##ccc;">
									<tr>
										<cfif len(local.thisProgram.featuredImagePath)>
											<td class="sw-align-top" style="width:100px;">
												<img src="#local.thisProgram.featuredImagePath#" style="width:100px;height:100px;" />
												<br /><br />
												<div class="sw-d-flex">
													<span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
													<span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span>
												</div>
											</td>
										</cfif>
										<td class="sw-align-top">
											<cfif local.thisProgram.isFeatured><span class="swFtdProgramLabel">Featured</span></cfif>
											<h5 class="prgmTitle sw-mt-0">
												<a href="#local.programDetailPageLink#" class="swPrimary">
													#local.thisProgram.programTitle#
												</a>
												<cfif NOT local.isBot>
													<a href="javascript:void(0);" class="swHeartIcon swMuted" title="Save for later">
														<i class="sw_saveforlater bi <cfif local.thisProgram.isSaved>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swb" data-swsaveforlatermode="browse" aria-hidden="true"></i>
													</a>
												</cfif>
											</h5>
											<cfif len(local.thisProgram.programSubTitle)>
												<h6 class="swPrimary sw-mt-0">#local.thisProgram.programSubTitle#</h6>
											</cfif>
											<cfif NOT local.thisProgram.isSWOD>
												<cfset local.displayDate = "#DateTimeFormat(local.thisProgram.dspStartDate,'dddd, m/d/yyyy')#" & (DateDiff("d",local.thisProgram.dspStartDate,local.thisProgram.dspEndDate) gt 0 ? " - #DateTimeFormat(local.thisProgram.dspEndDate,'dddd, m/d/yyyy')#" : "")>
												<p>#local.displayDate#</p>
											</cfif>
											<cfif NOT len(local.thisProgram.featuredImagePath)>
												<div class="sw-d-flex sw-mt-2">
													<span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span>
													<span class="st_facebook" #local.shareThisURLAndTitle#></span>
													<span class="st_linkedin" #local.shareThisURLAndTitle#></span>
													<span class="st_twitter" #local.shareThisURLAndTitle#></span>
												</div>
											</cfif>
										</td>
										<td class="sw-align-top" style="width:205px;">
											<p class="swBundle sw-m-0 sw-text-uppercase"><i class="bi bi-basket-fill" aria-hidden="true"></i> #local.thisProgram.programBrand#</p>
											<cfif local.thisProgram.isRegistered>
												<p class="swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
											</cfif>
											<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
												<div class="sw-mt-1">
													<cfif local.thisRate.price gt 0>
														#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
													<cfelse>
														#Replace(local.thisProgram.freeRateDisplay,'.00','')# for #local.thisRate.description#
													</cfif>
												</div>
											</cfloop>
											<div class="sw-mt-2">
												<table class="sw-w-100">
													<tr>
														<td>
															<a href="#local.programDetailPageLink#">
																<button type="button" class="sw-btn-vwdetail">View Details</button>
															</a>
														</td>
														<td>
															<cfif NOT local.isBot>
																<cfif local.thisProgram.inCart>
																	<a href="#attributes.event.getValue('mainurl')#&panel=showCart">
																		<button type="button" class="sw-btn-vwdetail">In Cart</button>
																	</a>
																<cfelseif len(local.thisProgram.arrRates)>
																	<a href="#attributes.event.getValue('mainurl')#&panel=reg&item=SWB-#local.thisProgram.programID#">
																		<button type="button" class="sw-btn-vwdetail">Add to Cart</button>
																	</a>
																</cfif>
															</cfif>
														</td>
													</tr>
												</table>
											</div>
										</td>
									</tr>
									<tr>
										<td class="sw-align-top" colspan="<cfif len(local.thisProgram.featuredImagePath)>3<cfelse>2</cfif>">
											<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
												<p class="sw-mb-0"><b>#attributes.data.semWeb.qrySWP.brandLearnObjectives#</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
														<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
													</cfloop>
												</ul>
											</cfif>
											<cfif structKeyExists(local.thisProgram, "arrSpeakers") AND arrayLen(local.thisProgram.arrSpeakers)>
												<p class="sw-mb-0"><b>Speakers</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrSpeakers#" index="local.thisSpeaker">
														<li class="swSpeakersPoint">#local.thisSpeaker.speakerName#</li>
													</cfloop>
												</ul>
											</cfif>
											<cfif arrayLen(local.thisProgram.arrIncPrograms)>
												<p class="sw-mb-0"><b>Included Programs</b></p>
												<ul style="margin-top:0;">
													<cfloop array="#local.thisProgram.arrIncPrograms#" index="local.thisIncludedProgram">
														<li class="swIncProgramPoint">#local.thisIncludedProgram.contentName#<cfif NOT local.thisProgram.isSWOD> (#DateFormat(local.thisIncludedProgram.dateStart,'m/d/yyyy')#)</cfif></li>
													</cfloop>
												</ul>
											</cfif>
										</td>
									</tr>
								</table>
							</cfcase>
						</cfswitch>
					</cfloop>

					<cfif local.strBrowse.strPagination.numTotalPages gt 1>
						<div class="swPagination">
							<ul>
								<cfif local.strBrowse.strPagination.numCurrentPage gt 1>
									<li>
										<a href="##" onclick="gotoSWBrowsePage(1);return false;" title="First Page">&lt;&lt;</a>
									</li>
									<li>
										<a href="##" onclick="gotoSWBrowsePage(#local.strBrowse.strPagination.startPos - local.strBrowse.strPagination.count#);return false;" title="Previous Page">&lt;</a>
									</li>
								</cfif>

								<cfloop from="#local.strBrowse.strPagination.startPage#" to="#local.strBrowse.strPagination.endPage#" index="local.i">
									<cfset local.thisStartRow = local.i>
									<cfif local.i gt 1>
										<cfset local.thisStartRow = (local.strBrowse.strPagination.count * (local.i-1)) + 1>
									</cfif>
									
									<cfif local.strBrowse.strPagination.numCurrentPage eq local.i>
										<li class="active"><a href="javascript:void(0);">#local.i#</a></li>
									<cfelse>
										<li><a href="##" onclick="gotoSWBrowsePage(#local.thisStartRow#);return false;">#local.i#</a></li>
									</cfif>
								</cfloop>
								
								<cfif local.strBrowse.strPagination.numCurrentPage lt local.strBrowse.strPagination.numTotalPages>
									<li>
										<a href="##" onclick="gotoSWBrowsePage(#local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count#);return false;" title="Next Page">
											&gt;
										</a>
									</li>
									<li>
										<a href="##" onclick="gotoSWBrowsePage(#(local.strBrowse.strPagination.count * (local.strBrowse.strPagination.numTotalPages-1)) + 1#);return false;" title="Last Page">
											&gt;&gt;
										</a>
									</li>
								</cfif>
							</ul>
						</div>
					</cfif>
				<cfelse>
					<div class="tsAppBodyTextImportant sw-mt-4" style="text-align:center;">
						Sorry, we didn't find any programs to display.
					</div>
				</cfif>
			</td>
		</tr>
	</table>
</div>
</cfoutput>