<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function validatePassword() {
			if ($('##mcpwd').val().trim().length == 0) {
				return false;
			}

			$('##frmPassword button').prop('disabled',true).html('Please Wait...');
			return true;
		}
		function togglePwdDisplay() {
			if ($('##mcPwdDsp .icon-eye-open').is(':visible')) $('##mcpwd').attr('type','text');
			else $('##mcpwd').attr('type','password');

			$('##mcPwdDsp').find('.icon-eye-open,.icon-eye-close').toggle();
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">