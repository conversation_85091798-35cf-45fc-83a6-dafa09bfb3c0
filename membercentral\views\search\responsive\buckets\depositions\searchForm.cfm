<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
		$(document).ready(function(){
			mca_setupDatePickerRangeFields('s_depodatefrom','s_depodateto');
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>
<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	<div class="control-group">
		<label class="control-label" for="s_fname">Expert First Name:</label>
		<div class="controls">
			<input type="text" name="s_fname"  id="s_fname" value="#local.strSearchForm.s_fname#" size="14" maxlength="50">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_lname">Expert Last Name:</label>
		<div class="controls">
			<input type="text" name="s_lname"  id="s_lname" value="#local.strSearchForm.s_lname#" size="14" maxlength="50">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_casename">Name of Case:</label>
		<div class="controls">
			<input type="text" name="s_casename"  id="s_casename" value="#local.strSearchForm.s_casename#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label">Deposition taken between:</label>
		<div class="controls">
			<input type="text" id="s_depodatefrom" name="s_depodatefrom" value="#DateFormat(local.strSearchForm.s_depodatefrom, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol"> and 
			<input type="text" id="s_depodateto" name="s_depodateto" value="#DateFormat(local.strSearchForm.s_depodateto, 'm/d/yyyy')#" size="14" maxlength="10" autocomplete="off" class="datecontrol">
			<a class="btn btn-small" href="" onClick="mca_clearDateRangeField('s_depodatefrom','s_depodateto');return false;">clear dates</a>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_jurisdiction">Jurisdiction:</label>
		<div class="controls">
			<select name="s_jurisdiction"  id="s_jurisdiction">
				<option value="">All Jurisdictions</option>
				<cfloop query="local.qryStates">
					<option value="#local.qryStates.code#" <cfif local.strSearchForm.s_jurisdiction eq local.qryStates.code>selected="selected"</cfif>>#local.qryStates.name#</option>					
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<input type="text" name="s_key_phrase" id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<input type="text" name="s_key_one" id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one"><b>Without</b> the words:</label>
		<div class="controls">
			<input type="text" name="s_key_x" id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>