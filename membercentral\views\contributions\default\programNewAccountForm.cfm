<cfset dataStruct = attributes.data.actionStruct>
<cfset local.stepNum = 2>

<cfinclude template="navigation.cfm">
<cfinclude template="../commonJS_step2.cfm">

<cfoutput>
<div class="tsAppBodyText">
	<div>
		<div class="TitleText" style="margin-bottom:30px;">#dataStruct.intakeFormTitle#</div>

		<div id="cp2_err_div" style="display:none;margin:12px 0 5px 0;"></div>

		<cfform name="frmContribution" id="frmContribution" method="post" action="#dataStruct.mainformurl#" onsubmit="return doS2Validate(false)">
			<input type="hidden" name="nextStep" id="nextStep" value="payment">
			<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">
		</cfform>

		<div class="CPSection" id="divCPCreateAccountArea">
			<div class="CPSectionTitle BB"><cfif len(dataStruct.newAcctFormTitle)>#dataStruct.newAcctFormTitle#<cfelse>Create New Account</cfif></div>
			<div style="padding:10px;">
				#CreateObject("component","model.system.user.accountLocater").displayLocatorNewAccount(fieldsetID=dataStruct.fieldsetID, postURL=dataStruct.newaccturl, buttonText="Continue", showProfessionalLicenses=dataStruct.showProfLicenses)#
				<div id="divCPCreateAccountSubmitArea"></div>
			</div>
		</div>
		<div class="CPSection waitingtxt" id="divCPCreateAccountAreaLoading" style="display:none;">
			<div style="text-align:center;margin:30px 10px;"><i class="icon-spinner icon-spin"></i> Please wait while we prepare your contribution for the next step.</div>
		</div>
	</div>
</div>
</cfoutput>