<cfsavecontent variable="local.pageHeadJS">
	<cfoutput>
		<script type="text/javascript">
			const MFAPhNo = $("##MFAPhNo")[0];
			const MFAPhNoErrMsg = $("##MFAPhNoErrMsg");
			const MFAPhNoValidMsg = $("##MFAPhNoValidMsg");
			const MFAPhNoErrMap = ["Invalid number", "Invalid country code", "Too short", "Too long", "Invalid number"];
			let MFAPhNoOTPTimer = 0;
		
			/* initialise plugin */
			const MFAPhNoInput = window.intlTelInput(MFAPhNo, {
				utilsScript: "/assets/common/javascript/intl-tel-input/18.1.1/js/utils.js",
				preferredCountries: [ 'us', 'ca' ]
			});
		
			const MFAPhNoReset = () => {
				MFAPhNoErrMsg.html('').hide();
				MFAPhNoValidMsg.hide();
			};
		
			/* on blur: validate */
			MFAPhNo.addEventListener('blur', () => {
				MFAPhNoReset();
				if (MFAPhNo.value.trim()) {
					if (MFAPhNoInput.isValidNumber()) {
						MFAPhNoValidMsg.show();
					} else {
						const MFAPhNoErrCode = MFAPhNoInput.getValidationError();
						if (MFAPhNoErrMap.length >= MFAPhNoErrCode + 1)
							MFAPhNoErrMsg.html(MFAPhNoErrMap[MFAPhNoErrCode]).show();
					}
				}
			});
		
			/* on keyup / change flag: reset */
			MFAPhNo.addEventListener('change', MFAPhNoReset);
			MFAPhNo.addEventListener('keyup', MFAPhNoReset);

			function editMFAPhNo() {
				$('.MFAPhNoTool').hide();
				$('##editMFAPhNo').show();
			}
			function verifyMFAPhNo(resendCode) {
				/* invalid */
				if (MFAPhNo.value.trim().length == 0 || !MFAPhNoInput.isValidNumber())
					return false;

				let isResend = typeof resendCode != "undefined" && resendCode ? true : false;

				let channel = $('##mfasms_channel').length ? $('##mfasms_channel').val() : 'sms';
				let strChannel = getMFASMSChannelInfo(channel);

				if (isResend) {
					$('##btnVerifyMFAPhNo').prop('disabled',true);
					$('##resendMFAPhNoOTPControl').html('<i class="icon-refresh icon-spin"></i> ' + strChannel.attempting);
				} else {
					$('##btnVerifyMFAPhNo').html('Please wait...').prop('disabled',true);
				}
				
				$.ajax({
					url: '#local.manageSettingsResourceLink#&la=mfasms',
					type: 'POST',
					data: { "MFAPhNo":MFAPhNoInput.getNumber(), verifyMFAPhNo:true, mfasms_channel:channel },
					dataType: 'json',
					success: function(response) { 
						$('##btnVerifyMFAPhNo').html('Verify').prop('disabled',false);

						if (response.success) {
							if (response.validphno) {
								MFAPhNoErrMsg.html('').hide();
								showMFASMSOTPContainer(response);
								if (isResend && ['sms','whatsapp'].indexOf(channel) != -1) {
									$('##MFAPhNoOTPValidMsg').show().html('Successfully sent code').fadeOut(5000);
								}
							} else {
								editMFAPhNo();
								MFAPhNoErrMsg.html('Invalid Phone Number').show();
							}
						} else {
							if (isResend) {
								alert('We were unable to '+strChannel.text+'. Please try again.');
								clearResendOTP();
							} else {
								alert('We were unable to verify this phone number. Please try again.');
							}
						}
						
					}, fail: function(response) { 
						if (isResend) {
							alert('We were unable to '+strChannel.text+'. Please try again.');
							$('##btnVerifyMFAPhNo').html('Verify').prop('disabled',false);
							clearResendOTP();
						} else {
							alert('We were unable to verify this phone number. Please try again.');
							$('##btnVerifyMFAPhNo').html('Verify').prop('disabled',false);
						}
					}
				});
			}
			function showMFASMSOTPContainer(r) {				
				let strChannel = getMFASMSChannelInfo(r.channel);
				
				let MFAPhNoOTPResendOption = function() {
					if (timeLeft == -1) {
						clearTimeout(MFAPhNoOTPTimer);
						$('##resendMFAPhNoOTPControl').html('<a href="##" onclick="verifyMFAPhNo(1);return false;"><i class="icon-refresh"></i> '+strChannel.text+'</a>');
						$('.mfasms_channels').removeClass('login-disabled');
					} else {
						$('##resendMFAPhNoOTPRemainingTime').html(timeLeft);
						timeLeft--;
					}
				};
				
				let timeLeft = r.reattemptdelay;
				let MFASMSVerifyTemplate = Handlebars.compile($('##MFASMSVerifyTemplate').html());
				r.mfaphno = MFAPhNoInput.getNumber();
				r.resendcodetext = '<i class="icon-refresh"></i> '+strChannel.text+' in <span id="resendMFAPhNoOTPRemainingTime">'+timeLeft+'</span> seconds';
				r.disablesmschannels = r.codeattempts > 1;
				
				$('.MFAPhNoTool').hide();
				$('##verifyMFAPhNoOTP').html(MFASMSVerifyTemplate(r)).show();
				
				clearTimeout(MFAPhNoOTPTimer);
				MFAPhNoOTPTimer = setInterval(MFAPhNoOTPResendOption, 1000);
			}
			function verifySecurityCode() {
				/* invalid */
				if (MFAPhNo.value.trim().length == 0 || !MFAPhNoInput.isValidNumber() || $('##securityCode').val().trim().length == 0)
					return false;
				
				/* verify ph no */
				$('##btnVerifyCode').html('Please wait...').prop('disabled',true);
				$('##MFAPhNoOTPErrMsg').hide();
				clearResendOTP();
				
				$.ajax({
					url: '#local.manageSettingsResourceLink#&la=mfasms',
					type: 'POST',
					data: { "MFAPhNo":MFAPhNoInput.getNumber(), securityCode:$('##securityCode').val().trim(), verifyMFAPhNoOTP:true },
					dataType: 'json',
					success: function(response) { 
						
						if (response.success) {
							if (response.verified) {
								gotoManageSettings();
							} else {
								$('##MFAPhNoOTPErrMsg').html('Invalid Code').show();
								$('##btnVerifyCode').html('Verify').prop('disabled',false);
							}
						} else {
							alert('We were unable to verify this code. Please try again.');
							$('##btnVerifyCode').html('Verify').prop('disabled',false);
						}
						
					}, fail: function(response) { 
						alert('We were unable to verify this code. Please try again.');
						$('##btnVerifyCode').html('Verify').prop('disabled',false);
					}
				});
			}
			function clearResendOTP() {
				$('##resendMFAPhNoOTPControl').html('');
				$('.mfasms_channels').addClass('login-disabled');
			}
			function deleteMFAPhoneNo() {
				$('##delMFAPhNo').html('<i class="icon-spinner icon-spin"></i>');
				self.location.href = '/?pg=login&logact=manageSettings&la=mfasms&deleteMFAPhNo';
			}
			function chooseMFASMSChannel(channel) {
				$('##mfasms_channel').val(channel);
				verifyMFAPhNo();
			}
			function getMFASMSChannelInfo(channel) {
				let strChannel = {};
				switch (channel.toLowerCase()) {
					case 'sms':
						strChannel = { text:'Resend code', attempting:'Resending code' };
						break;
					case 'call':
						strChannel = { text:'Call again', attempting:'Calling' };
						break;
					case 'whatsapp':
						strChannel = { text:'Resend code', attempting:'Resending code' };
						break;
				}
				return strChannel;
			}
			function gotoManageSettings() {
				self.location.href = '/?pg=login&logact=manageSettings';
			}

			$(function() {
				$('##MFAPhNo').focus();
			})
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageHeadJS#">