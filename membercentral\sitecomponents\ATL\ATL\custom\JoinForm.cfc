<cfcomponent extends="model.customPage.customPage" output="false">
    <cfset variables.objCustomPageUtils = application.objCustomPageUtils>
    <cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
        <cfscript>
            var local = structNew();
    
            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            local.crlf = chr(13) & chr(10);

			variables.organizationGroupUID = "4C5E9FD4-44B4-4ECD-A869-3CB3016525BA";

            local.arrCustomFields = [];
		    local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="5B457B68-8C4A-4BCD-AD98-BB876A66E5E8" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a Atlanta Bar member. If you have questions about your membership, please call (999) 999-9999 <NAME_EMAIL>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="BilledMessage",type="CONTENTOBJ",desc="Message displayed when account selected billed subscription",value="Our records indicate either your application is currently under review or you are a renewing member. If you are looking to renew, please click [[click here]] to review your renewal statement. If you have questions about your membership, please call (999) 999-9999 <NAME_EMAIL>." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership,please call (999) 999-9999 <NAME_EMAIL>." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="Atlanta Bar Membership Application" };
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Step 1 - Please complete the following information." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Step 2 - Make your selections below." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Step 3 - Please review your selections and proceed with payment." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="8C31AFCB-7ACC-4605-8D95-EF396174A608" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCredit",type="STRING",desc="pay profile code for CC",value="ABACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeCheck",type="STRING",desc="pay profile code for check",value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodeACH",type="STRING",desc="pay profile code for ACH",value="" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="licenseFieldMessage",type="CONTENTOBJ",desc="",value="Please provide the license date and number for all currently active State licensures." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="Atlanta Bar Membership Application Receipt" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" };
			arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" };
			arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CompanyFirmSelector",type="CONTENTOBJ",desc="Company/Firm Information tool",value="Please find your company/firm in the search tool below. If you cannot find your company/firm, we will save the name that you enter on the form." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
            variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

            StructAppend(variables, variables.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.FormTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
            variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="#variables.formname#");

            variables.useHistoryID = 0;
			if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
				variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
			if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
				variables.useHistoryID = int(val(session.useHistoryID));	
            
			variables.qryHistoryStarted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = variables.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started join form.";
            variables.historyCompletedText = "Member completed join form.";

            switch (local.formAction) {
                case "processLookup":
                    switch (processLookup()) {
                        case "success":
                            local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
                            break;		
                        case "activefound":
                            local.returnHTML = showError(errorCode='activefound');
                            break;		
                        case "acceptedfound":
                            local.returnHTML = showError(errorCode='acceptedfound');
                            break;		
                        case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;
						case "billedfound":
                            local.returnHTML = showError(errorCode='billedfound');
                            break;		
                        default:
                            application.objCommon.redirect(variables.baselink);
                            break;				
                    }
                    break;
                case "processMemberInfo":
                    switch (processMemberInfo(rc=arguments.event.getCollection())) {
                        case "success":
                            local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
							break;
						case "spam":
							local.returnHTML = showError(errorCode='spam');
                            break;
                        default:
                            local.returnHTML = showError(errorCode='failsavemember');
                            break;				
                    }
                    break;
                case "processMembershipInfo":
                    switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                        case "success":
                            local.returnHTML = showPayment(rc=arguments.event.getCollection());
                            break;
                        default:
                            local.returnHTML = showError(errorCode='failsavemembership');
                            break;				
                    }
                    break;
                case "processPayment":
                    local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
                    if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
                        arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
                    }
                    switch (local.processPaymentResponse.response) {
                        case "success":
                            local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
                            local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
                            application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
                            structDelete(session, "formFields");
                            break;
                        default:
                            local.returnHTML = showError(errorCode='failpayment');
                            break;				
                    }
                    break;
                case "showMembershipInfo":
                    local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
                    break;				
                default:
                    local.returnHTML = showLookup(memberkey=arguments.event.getTrimValue('mk',''));
                    if(structKeyExists(session, "captchaEntered")) structDelete(session, "captchaEntered");
                    break;
            }
    
            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";
    
            return returnAppStruct(local.returnHTML,"echo");

        </cfscript>
    </cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="memberkey" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfif arguments.memberkey neq ''>
			<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		</cfif>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				##cboxOverlay{
					z-index: 99998 !important;
				}
				##colorbox{
					z-index: 99999 !important;
				}
				.acntLookUpBtn,.acntLookUpMessage{
					display:inline!important;
					float: right;
					width: 48%!important;
				}
				.acntLookUpBtn{	margin-right: 5px; }
				.acntLookUpMessage .span12{
					margin-left:0px !important;
				}
				##zoneMain{margin-bottom:30px;}
				@media screen and (min-width: 632px) and (max-width: 980px){
					.acntLookUpBtn {
						margin-top: 52px;
					}
				}
				@media screen and (min-width: 980px){
					.acntLookUpBtn {
						margin-top: 45px;
					}
				}				
				.acntLookUpBtn  {
				   position: absolute;
				   width: 50%;
				   height: 100%;
				   min-height: 100%;
				}
				.centerit {
				   position: absolute;
				   top: 50%;
				   width: 100%;
				   text-align: center;
				}
				.centerit button {
					position: relative;
					top: -35px;
				}
				.center-holder{
					position: relative;
				}
				.acntLookUpMessage p{font-size:15px;}
				.acntLookUpMessage {
					margin-left: 48%!important;
				}
				@media screen and (min-width: 0px){
					.acntLookUpBtn {
						margin-top: 0px!important;
					}
				}
				@media screen and (min-width: 359px) and (max-width: 368px){
					.acntLookUpBtn button{
						font-size:13px;
					}
				}
				@media screen and (max-width: 359px){
					.acntLookUpBtn button{
						font-size:11px;
					}
				}
				.captchaWrap img{
					display: unset !important;
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);					
					$(".ContactTypeHolder tr:first-child td").eq(0).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(0).width());
					$(".ContactTypeHolder tr:first-child td").eq(1).width($("##applicantInformationFieldSet table:last-child tr:first-child td").eq(1).width());
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {}
				$(document).on('click', '##btnAddAssoc', function(){
					selectMember();
				});
				function showAlertCustom(msg,afterProcessCallBack) { 
					$('##divFrmErr').html(msg).show(); 
					if($.isFunction(afterProcessCallBack)){
						afterProcessCallBack(); 
					}else{
						$('html,body').animate({scrollTop: $('.banner').position().top + 250},500);
					}					
				};
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlertCustom(msg,afterFormLoad);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);	   	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}
				
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				
				$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					$(window).on("resize load", function() {
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
						} else{
							$.colorbox.resize({innerWidth:550, innerHeight:330});		
						}				
					});
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" class="form-horizontal" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
				
				<h2 class="TitleText">#variables.strPageFields.FormTitle#</h2>
									
				<div class="CPSection step1" style="padding:0px 0px;">
					<h3>#variables.strPageFields.AccountLocatorTitle#</h3>
					<div style="padding-top:10px;">
						<div class="row-fluid center-holder">
							<div class="span4 acntLookUpBtn c">
								<div class="centerit" id="associatedMemberIDSelect">
									<button name="btnAddAssoc" type="button" id="btnAddAssoc" class="btn btnCustom">#variables.strPageFields.AccountLocatorButton#</button>
								</div>
							</div>
							<div class="span8 acntLookUpMessage frmText pull-right">
								#variables.strPageFields.AccountLocatorInstructions#
							</div>
						</div>
					</div>
				</div>
			
			</cfform>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>

		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
			<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.SubTypeTest)>
				<cfif local.qryBilledSubs.isRenewalRate>
					<cfset local.stReturn = "billedfound">
				<cfelse>
					<cfset local.stReturn = "billedjoinfound">
				</cfif>
			<cfelse>
				<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
		
				<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.SubTypeTest)>
					<cfset local.stReturn = "activefound">
				<cfelse>
					<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
					<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.SubTypeTest)>
						<cfset local.stReturn = "acceptedfound">
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>	
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>
		
		<cfset local.fieldSetUIDlist = '57D49809-EDB0-4348-A67B-BD2A8C07069D'>
		<cfset local.memberFieldDetails = structNew()>
		<cfset local.memberFieldData = structNew()>
		
		<cfset local.officeAddress1Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeAddress2Field = {fieldCode="",fieldLabel=""}>
		<cfset local.officeCityField = {fieldCode="",fieldLabel=""}>
		<cfset local.officestateprovField = {fieldCode="",fieldLabel=""}>
		<cfset local.officepostalcodeField = {fieldCode="",fieldLabel=""}>
		<cfset local.officecountyField = {fieldCode="",fieldLabel=""}>
		<cfset local.officephoneField = {fieldCode="",fieldLabel=""}>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='member type')>
		
		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>
		
		 <cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
			<cfset local.officeAddress1FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_address1']")/>
			<cfif arrayLen(local.officeAddress1FieldData)>				
				<cfset local.officeAddress1Field.fieldCode = local.officeAddress1FieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeAddress1Field.fieldLabel = local.officeAddress1FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.officeAddress2FieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_address2']")/>
			<cfif arrayLen(local.officeAddress2FieldData)>				
				<cfset local.officeAddress2Field.fieldCode = local.officeAddress2FieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeAddress2Field.fieldLabel = local.officeAddress2FieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.officeCityFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_city']")/>
			<cfif arrayLen(local.officeCityFieldData)>				
				<cfset local.officeCityField.fieldCode = local.officeCityFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officeCityField.fieldLabel = local.officeCityFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.officestateprovFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_stateprov']")/>
			<cfif arrayLen(local.officestateprovFieldData)>				
				<cfset local.officestateprovField.fieldCode = local.officestateprovFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officestateprovField.fieldLabel = local.officestateprovFieldData[1].XmlAttributes.fieldLabel >
			</cfif>	
			<cfset local.officepostalcodeFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_postalcode']")/>
			<cfif arrayLen(local.officepostalcodeFieldData)>				
				<cfset local.officepostalcodeField.fieldCode = local.officepostalcodeFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officepostalcodeField.fieldLabel = local.officepostalcodeFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.officecountyFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_county']")/>
			<cfif arrayLen(local.officecountyFieldData)>				
				<cfset local.officecountyField.fieldCode = local.officecountyFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officecountyField.fieldLabel = local.officecountyFieldData[1].XmlAttributes.fieldLabel >
			</cfif>
			<cfset local.officephoneFieldData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Organization Address_Phone']")/>
			<cfif arrayLen(local.officephoneFieldData)>				
				<cfset local.officephoneField.fieldCode = local.officephoneFieldData[1].XmlAttributes.fieldCode >
				<cfset local.officephoneField.fieldLabel = local.officephoneFieldData[1].XmlAttributes.fieldLabel >
			</cfif>

        </cfloop>

		<cfset local.strFieldSetAppCred = variables.objCustomPageUtils.renderFieldSet(uid='9DBD72D3-7526-4020-8E15-BBAE0F0C8BCA', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetAttrnyCred = variables.objCustomPageUtils.renderFieldSet(uid='11DF5818-3432-4CBA-BE2E-CBBE9A490ABE', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetAffCred = variables.objCustomPageUtils.renderFieldSet(uid='904732DB-AF8A-4E8D-9B9A-BE282118B16E', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetPrsnlEductn = variables.objCustomPageUtils.renderFieldSet(uid='8B1D287F-62E0-46DD-93F1-F811A5470C43', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetOffAddrProInfo = variables.objCustomPageUtils.renderFieldSet(uid='57D49809-EDB0-4348-A67B-BD2A8C07069D', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetHomAddr = variables.objCustomPageUtils.renderFieldSet(uid='A672A619-D337-4075-8DC0-06F894FD7D5D', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetAddrPrefs = variables.objCustomPageUtils.renderFieldSet(uid='8E5406BA-F21D-4666-A98D-865CA586A4F1', mode="collection", strData=local.strData)>

		<!--- get Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.strFieldSetAddrPrefs.strFields#" item="local.thisField">
			<cfif local.strFieldSetAddrPrefs.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.strFieldSetAddrPrefs.strFields[local.thisField] eq "Use this address for Mailing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>		
		<cfset local.contacttype = "">
		<cfloop collection="#local.strFieldSetAppCred.strFields#" item="local.thisField">
			<cfif local.strFieldSetAppCred.strFields[local.thisField] eq "Member Type">
				<cfset local.contacttype = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>				
				
		<cfset local.email = "">
		<cfloop collection="#local.strFieldSetAppCred.strFields#" item="local.thisField">
			<cfif local.strFieldSetAppCred.strFields[local.thisField] eq "Email">
				<cfset local.email = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset index=1 >
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[index] = local.qryOrgProLicenseStatuses.statusName>	
			<CFSET index=index + 1>
		</cfloop> 	
		<cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID='#variables.organizationGroupUID#', orgID=variables.orgID)>
		<cfquery name="local.qryCompanyByGroup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;
			SELECT distinct parentMember.memberNumber, parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber,
			rt.recordTypeCode,rt.recordTypeName from
			dbo.ams_recordRelationships AS rr  
			INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
			INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
			INNER JOIN dbo.ams_recordTypes AS rt ON rt.recordTypeID = rtrt.masterRecordTypeID			
			INNER JOIN dbo.ams_members AS parentMember ON parentMember.orgID = @orgID and parentMember.memberID = rr.masterMemberID
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID and mg.memberID = parentMember.memberID
			INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = mg.groupID
			WHERE rr.orgID = @orgID AND
			g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
			AND rt.recordTypeCode IN ('FirmOrganization')
			AND g.status = 'A' AND ISNULL(parentMember.company,'') <> '' ORDER BY parentMember.company

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="local.qryLinkedParentCompany" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">;

			SELECT parentMember.memberID, parentMember.company, parentMember.firstName, parentMember.lastName, parentMember.memberNumber,rt.recordTypeCode
			FROM dbo.ams_members AS childMember
			INNER JOIN dbo.ams_recordRelationships AS rr ON rr.orgID = @orgID and rr.childMemberID = childMember.memberID and rr.isActive = 1
			INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
			INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
			INNER JOIN dbo.ams_recordTypes AS rt ON rt.recordTypeID = rtrt.childRecordTypeID
			INNER JOIN dbo.ams_members AS parentMember ON parentMember.orgID = @orgID and parentMember.memberID = rr.masterMemberID
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID and mg.memberID = parentMember.memberID
			INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = mg.groupID
			WHERE childMember.memberID = <cfqueryparam value="#local.strData.memberID#" cfsqltype="CF_SQL_INTEGER">
			AND rt.recordTypeCode IN ('FirmOrganization')
			AND g.groupID = <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">	
				##content-wrapper input[type="text"] {
					width:206px!important;
				}
				##content-wrapper select{
					width:220px!important;
				}
			
				div.tsAppSectionHeading{margin-bottom:20px}
				##ApplicationtionIntroText{margin-left:14px;margin-bottom: 15px;}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}
							
					##content-wrapper div.ui-multiselect-menu{width:auto!important;}
				
				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}	
			</style>
			<script language="javascript">
				var lawSchoolSelector = '';
				var yearGraduatedSelector = '';
				var amsaSelector = '';
				var officeAddressSelector = '';
				var homeAddressSelector = '';
				
				var #toScript(local.strFieldSetPrsnlEductn.strfields, "strFieldSetPrsnlEductnObj")#;
				var #toScript(local.strFieldSetAffCred.strfields, "organizationAddressFieldsObj")#;
				var #toScript(local.strFieldSetAttrnyCred.strfields, "attoneyCreditFieldsObj")#;
				var #toScript(local.strFieldSetOffAddrProInfo.strfields, "officeAddressFieldsObj")#;
				var #toScript(local.strFieldSetHomAddr.strfields, "homeAddressFieldsObj")#;
				
				function assignCompanyMemberData(memberNumber){
					var er_change = function(r) {
						var results = r;
						if( results.success ){
							var fieldMapping = {
								address1: "###local.officeAddress1Field.fieldCode#",
								address2: "###local.officeAddress2Field.fieldCode#",
								city: "###local.officeCityField.fieldCode#",
								stateid: "###local.officestateprovField.fieldCode#",
								postalcode: "###local.officepostalcodeField.fieldCode#",
								county: "###local.officecountyField.fieldCode#",
								phone: "###local.officephoneField.fieldCode#"
							};

							for (var key in fieldMapping) {
								if (results[key]) {
									var selector = fieldMapping[key];
									$(selector).val(results[key]);
									$(selector).change();
								}
							}
						}
					};

					var objParams = { memberNumber:memberNumber };
					TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
				}

				function changeCompany(value,text,firstName,lastName,memberNumber,recordTypeCode) {					
					if(value != "Please Select"){
						$("##m_company").parents('tr').show()
						if(text.length==0 && $("##m_company").length && $("##m_company").val().length){
							
						}else{
							$("##m_company").val(text);
						}
						assignCompanyMemberData(memberNumber);
						//Need to change this depending on the custom form
						$("##orgMemberID").val(value);
						$("##orgCompanyName").val(text);						
						$("##orgFirstName").val(firstName);
						$("##orgLastName").val(lastName);
						$("##orgMemberNumber").val(memberNumber);
						$("##recordTypeCode").val(recordTypeCode);
						$("##m_company").siblings('a').remove();
						$("##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">Change Company/Firm</a>');
						$(".organizationsFirmsHolder").hide();
						$("##changeCompany").unbind("click");
						$("##changeCompany").click(function(){
							$(".organizationsFirmsHolder").show();
						});
						$("##m_company").unbind("change");
						$("##m_company").on('change', function() {		
							var company = $('##companyField option').filter(function () { return $(this).html() == $("##m_company").val(); });					
							if(company.length){
								$("##orgMemberID").val(company.val());
								$("##orgCompanyName").val(company.text());
								$("##orgFirstName").val(company.attr("firstName"));
								$("##orgLastName").val(company.attr("lastName"));
								$("##orgMemberNumber").val(company.attr("memberNumber"));
								$("##recordTypeCode").val(recordTypeCode);
							}else{								
								$("##orgMemberID").val(0);
								$("##orgCompanyName").val('');
								$("##orgFirstName").val('');
								$("##orgLastName").val('');
								$("##orgMemberNumber").val('');
								$("##recordTypeCode").val('');
							}
						});
					}else{
						if(text.length==0 && $("##m_company").length && $("##m_company").val().length){

						}else{
							$("##m_company").val('');
							$("##m_company").parents('tr').hide();
						}						
						$("##orgMemberID").val(0);
						$("##orgCompanyName").val('');
						$("##orgFirstName").val('');
						$("##orgLastName").val('');
						$("##orgMemberNumber").val('');
						$("##recordTypeCode").val('');
					}
				}
				
				$(document).ready(function(){
					$.each(strFieldSetPrsnlEductnObj, function (key, val) {
						if(val == 'Law School'){
							lawSchoolSelector = key;
						}else if(val == 'Law School Graduation Date'){
							yearGraduatedSelector = key;
						}
					});
					$.each(attoneyCreditFieldsObj, function (key, val) {
						if(val == 'Do You Practice or Reside in Atlanta Metropolitan Statistical Area?'){
							amsaSelector = key;
						}
					});

					$.each(strFieldSetPrsnlEductnObj, function (key, val) {
						if(val == 'Law School'){
							lawSchoolSelector = key;
						}else if(val == 'Law School Graduation Date'){
							yearGraduatedSelector = key;
						}
					});
					arrAddress = ['City','State','Zip Code'];
					arrOfficeAddressObj = [];
					arrHomeAddressObj = [];
					$.each(officeAddressFieldsObj, function (key, val) {
						if(val == 'Office Address'){
							officeAddressSelector = key;
						}
						if( $.inArray(val, arrAddress) !== -1 ) {
							arrOfficeAddressObj.push({'strKey':val,'strVal':key});
						}
					});
					$.each(homeAddressFieldsObj, function (key, val) {
						if(val == 'Home Address'){
							homeAddressSelector = key;
						}
						if( $.inArray(val, arrAddress) !== -1 ) {
							arrHomeAddressObj.push({'strKey':val,'strVal':key});
						}
					});
					<cfif len(local.contacttype)>
						processContactTypeChange();
					</cfif>
					lawSchoolObj = '###variables.formName# ##'+lawSchoolSelector;
					yearGraduatedObj = '###variables.formName# ##'+yearGraduatedSelector;
					
					$('###variables.formName# ###local.contacttype#').on('change',function(){
						processContactTypeChange();
					});
					<cfif structKeyExists(local.strData, "orgMemberID") and val(local.strData["orgMemberID"]) NEQ 0>
						$("##orgMemberID").val('#val(local.strData["orgMemberID"])#');
						$("##orgCompanyName").val('#local.strData["orgCompanyName"]#');
						$("##orgFirstName").val('#local.strData["orgFirstName"]#');
						$("##orgLastName").val('#local.strData["orgLastName"]#');
						$("##orgMemberNumber").val('#local.strData["orgMemberNumber"]#');
						$("##recordTypeCode").val('#local.strData["recordTypeCode"]#');
					</cfif>
					$("##m_company").parent().append('<a href="javascript:void(0)" id="changeCompany">New Company/Firm</a>');				

					$("##companyField").multiselect({
						header: "",
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							var companyOption = $("option[value='"+ui.value+"']",this);
							var firstName = "";
							var lastName = "";
							var memberNumber = "";
							if(companyOption.length){
								firstName = companyOption.attr("firstName");
								lastName = companyOption.attr("lastName");
								memberNumber = companyOption.attr("memberNumber");
								recordTypeCode = companyOption.attr("recordTypeCode");
							}
							changeCompany(ui.value,ui.text,firstName,lastName,memberNumber,recordTypeCode);
						}
					}).multiselectfilter();
					
					function processContactTypeChange(){
						var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
						$('##hidCategory').val(mcSel);
						if(mcSel == 'Attorney') {
							$('##attrCredSection').show();
							$('##professionalLicenseSection').show();
						}
						else {
							$('##attrCredSection').hide();
							$('##professionalLicenseSection').hide();
						}
							
						if(mcSel == 'Legal Affiliate')
							$('##affCredSection').show();
						else
							$('##affCredSection').hide();
						
						
						
						if(mcSel == 'Law Student'){
							$('##officeAddProfInfoSection').hide();
						}
						else {
							$('##officeAddProfInfoSection').show();
						}
						/*
							$(lawSchoolObj).parent().parent().children().first().text('*');
							$(yearGraduatedObj).parent().parent().children().first().text('*');
							$.each(organizationAddressFieldsObj, function (key, val) {
								$('###variables.formName# ##'+key).parent().parent().children().first().hide();
							});
						} else {
							$(lawSchoolObj).parent().parent().children().first().text('');
							$(yearGraduatedObj).parent().parent().children().first().text('');
							$.each(organizationAddressFieldsObj, function (key, val) {
								$('###variables.formName# ##'+key).parent().parent().children().first().show();
							});
						}*/
					}

					var orgMemberID = $("##orgMemberID").val();
					var firstName = $("##orgFirstName").val();
					var lastName = $("##orgLastName").val();
					var memberNumber = $("##orgMemberNumber").val();
					var orgCompanyName = $("##orgCompanyName").val();					
					var recordTypeCode = $("##recordTypeCode").val();					
					if(parseInt(orgMemberID) == 0){
						orgMemberID = "Please Select";
						firstName = "";
						lastName = "";
						memberNumber = "";
						orgCompanyName = "";
						recordTypeCode = "";
					}

					changeCompany(orgMemberID,orgCompanyName,firstName,lastName,memberNumber,recordTypeCode);
					$("##companyField").multiselect("refresh");
					
					$("##newCompany").click(function(){
						changeCompany(0,'','','','');
					});

					if($("##m_company").length && $("##m_company").val().length){
						$("##m_company").trigger('change');
					}
					
					<cfset local.qryEarliestLicenseDate = application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgid,columnName='Earliest License Date')>
					<cfset local.earliestLicenseDateColumnId = local.qryEarliestLicenseDate.columnId>
					$('##md_#local.earliestLicenseDateColumnId#').parents('tr').hide();
					$(document).on('change','input[id^="mpl_"][id$="_activeDate"]',function(){
						var _licenseDate = "";
						
						$('input[id^="mpl_"][id$="_activeDate"]').each(function(){
							var _this = $(this);
							if(_licenseDate.length > 0 ){
								if( _licenseDate >  _this.val())
									_licenseDate =  _this.val();
							} else {
								_licenseDate =  _this.val();
							}
						});
						$('##md_#local.earliestLicenseDateColumnId#').val(_licenseDate);
					});
				});
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}				
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					
					var mcSel = $('###variables.formName# ###local.contacttype# option:selected').text();
					#local.strFieldSetAppCred.jsValidation#
					if(mcSel == 'Attorney'){
						#local.strFieldSetAttrnyCred.jsValidation#
					}else if(mcSel == 'Legal Affiliate'){
						#local.strFieldSetAffCred.jsValidation#
					}
					
					#local.strFieldSetPrsnlEductn.jsValidation#
					if(mcSel != 'Law Student'){
						#local.strFieldSetOffAddrProInfo.jsValidation#
					}
					#local.strFieldSetHomAddr.jsValidation#
					#local.strFieldSetAddrPrefs.jsValidation#

					if(mcSel == 'Attorney'){
						var prof_license = $('.mpl_pltypeid').val();
						var isProfLicenseSelected = false;
						strFl = 0;
						if(prof_license != "" && prof_license != null){
							isProfLicenseSelected = true;
							
							$.each(prof_license,function(i,val){ 
								var text = $(".mpl_"+val+"_licensenumber").parent().prev().text();
								if(mcSel == 'Attorney' && strFl == 0){
									$.each($('.licenseText'),function(k,strVal){
										if(strVal != undefined){
											if($(".mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = $(strVal).html()+' License Number is required'; }
											if($(".mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = $(strVal).html()+' License Date is required'; }
										}											
									});
									strFl = 1;
								}			
							});
						}	
						if (!isProfLicenseSelected && strFl == 0)	{							
							if(mcSel == 'Attorney' && $('##'+amsaSelector+' option:selected').text() == 'Yes'){
								arrReq[arrReq.length] = "Georgia License is required.";
							}else{
								arrReq[arrReq.length] = "Professional License is required.";
							}
						}
					}

					if(mcSel == 'Attorney' || mcSel == 'Judge' || mcSel == 'Law Faculty' || mcSel == 'Legal Affiliate'){							
						if($('##'+officeAddressSelector).val() == ''){
							arrReq[arrReq.length] = "Office Address is required.";
						}
						$(arrOfficeAddressObj).each(function(oKey,oVal){
							if(this.strVal != undefined){
								checkVal = $('##'+this.strVal).val();
								checkFieldName = this.strKey;
								if(checkVal == ''){
									arrReq[arrReq.length] = "Office "+checkFieldName+" is required.";
								}
							}
						});
					}else{
						if($('##'+homeAddressSelector).val() == ''){
							arrReq[arrReq.length] = "Home Address is required.";
						}
						$(arrHomeAddressObj).each(function(oKey,oVal){
							if(this.strVal != undefined){
								checkVal = $('##'+this.strVal).val();
								checkFieldName = this.strKey;
								if(checkVal == ''){
									arrReq[arrReq.length] = "Home "+checkFieldName+" is required.";
								}
							}
						});
						
						if($(lawSchoolObj).val() == ''){
							arrReq[arrReq.length] = "Law School is required.";
						}
						if($(yearGraduatedObj).val() == ''){
							arrReq[arrReq.length] = "Law School Graduation Date is required.";
						}
					}
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlertCustom(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#						
					</cfloop>
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) {
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>
						$(".mpl_pltypeid").multiselect({
							header: true,
							noneSelectedText: ' - Please Select - ',
							selectedList: 1,
							minWidth: 400,
							click: function(event, ui){
								licenseChange(ui.checked,ui.value,ui.text);											
						},
							uncheckAll: function(){
								$(".mpl_pltypeid option").each(function(){
									$('##tr_state_'+$(this).attr("value")).remove();
								});
								if($('##state_table tbody tr').length == 1){
									$("##state_table").hide();
								}	
							},
							checkAll: function( e ){
								$(".mpl_pltypeid option").each(function(){
									$('##tr_state_'+$(this).attr("value")).remove();
								});
								if($('##state_table tbody tr').length == 1){
									$("##state_table").hide();
								}
								$(".mpl_pltypeid option").each(function(){
									licenseChange(true,$(this).attr("value"),$(this).text());	
								});
							}
						});	
					
					function licenseChange(isChecked,val,text)	{
						$("##state_table").show();
						if(isChecked){								
							$('##state_table tbody tr:last').after('<tr id="tr_state_'+val+'">'
							+'<td class="visible-phone">State Name:</td>'
															+'<td align="right" class="tsAppBodyText licenseText">'+text+'</td>'
															+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseNumberLabel#:</td>'
															+'<td align="center" class="tsAppBodyText">'
															+'	<input size="13" maxlength="13" name="mpl_'+val+'_licensenumber" class="mpl_'+val+'_licensenumber" type="text" value="" />'
															+'	<input name="mpl_'+val+'_licensename" class="mpl_'+val+'_licensename" type="hidden" value="'+text+'" />'
															+'</td>'
															+'<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy):</td>'
															+'<td align="center" class="tsAppBodyText">'
															+'	<input size="13" maxlength="10" name="mpl_'+val+'_activeDate" class="mpl_'+val+'_activeDate tsAppBodyText" id="mpl_'+val+'_activeDate" type="text" value="" />'
															+'</td>'
															+'<td align="center" class="tsAppBodyText" style="display:none">'
															+'	<select name="mpl_'+val+'_status" class="mpl_'+val+'_status"><option value="active" selected="selected">Active</option></select>'
															+'</td>'

													+'</tr>');
							if ($("##tr_state_"+val).is(':visible') &&  $('.mpl_'+val+'_activeDate').is(':visible')) {
								mca_setupDatePickerField('mpl_'+val+'_activeDate');
							}
							$('.mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:pointer !important');							
						} else {
							$("##tr_state_"+val).remove();								
						}
						if($('##state_table tbody tr').length == 1){
							$("##state_table").hide();
						}	
					}							
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div>
				<cfif len(variables.strPageFields.FormTitle)>
					<h2 class="TitleText" id="FormIntro">#variables.strPageFields.FormTitle#</h2>
				</cfif>	
			<form name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="hidCategory" id="hidCategory" value="">
			<input type="hidden" id='email' name="email" value=''>
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">

			<cfif local.qryLinkedParentCompany.recordCount>
				<input type="hidden" name="orgMemberID" id="orgMemberID" value="#local.qryLinkedParentCompany.memberID#">
				<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="#local.qryLinkedParentCompany.company#">
				<input type="hidden" name="orgFirstName" id="orgFirstName" value="#local.qryLinkedParentCompany.firstName#">
				<input type="hidden" name="orgLastName" id="orgLastName" value="#local.qryLinkedParentCompany.lastName#">
				<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="#local.qryLinkedParentCompany.memberNumber#">
				<input type="hidden" name="recordTypeCode" id="recordTypeCode" value="#local.qryLinkedParentCompany.recordTypeCode#">
			<cfelse>
				<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">
				<input type="hidden" name="orgCompanyName" id="orgCompanyName" value="">
				<input type="hidden" name="orgFirstName" id="orgFirstName" value="">
				<input type="hidden" name="orgLastName" id="orgLastName" value="">
				<input type="hidden" name="orgMemberNumber" id="orgMemberNumber" value="">
				<input type="hidden" name="recordTypeCode" id="recordTypeCode" value="">
			</cfif>
			
			<cfif NOT local.isLoggedIn AND local.identifiedMemberID GT 0>
				<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
			</cfif>
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
			<div id="content-wrapper" class="row-fluid">

				<cfif len(variables.strPageFields.Step1TopContent)>
					<h3>#variables.strPageFields.Step1TopContent#</h3>
				</cfif>
				
				<div class="row-fluid">
					<div class="span12 tsAppSectionHeading">#local.strFieldSetAppCred.fieldSetTitle#</div>
					<div class="span6 tsAppSectionContentContainer">
						#local.strFieldSetAppCred.fieldSetContent#
					</div>
				</div>

				<div class="row-fluid" id="attrCredSection">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetAttrnyCred.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetAttrnyCred.fieldSetContent#
					</div>
				</div>
				
				<div class="row-fluid" id="affCredSection">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetAffCred.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">	
						#local.strFieldSetAffCred.fieldSetContent#
					</div>
				</div>
				
				<div class="row-fluid" style="display: none;" id="professionalLicenseSection">
					<div class="row-fluid tsAppSectionHeading" style="margin-bottom:0px;">Professional License Information</div>
					<div class="row-fluid tsAppSectionContentContainer">
						<p style="margin-bottom:0px;">#variables.strPageFields.licenseFieldMessage#</p>
						<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>
						<table cellpadding="3" border="0" cellspacing="0">		
							<tr class="top">
								<th class="tsAppBodyText" colspan="3" align="left">
									&nbsp;
								</th>
							</tr>							
							<tr align="top">
								<td class="tsAppBodyText" width="10">&nbsp;</td>
								<td class="tsAppBodyText" width="365">Professional License:</td>
								<td class="tsAppBodyText">
									<select id="mpl_pltypeid" name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
										<cfloop query="local.qryOrgPlTypes">
											<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
											<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
										</cfloop>
									</select>
								</td>
							</tr>
							<tr class="top">
								<td class="tsAppBodyText" width="10"></td>
								<td class="tsAppBodyText"></td>
								<td class="tsAppBodyText"></td>
							</tr>
						</table>
						<table cellpadding="3" border="0" cellspacing="0">
							<tr>
								<td class="tsAppBodyText" width="375">&nbsp;</td>
								<td>
									<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
										<thead class="hidden-phone">
											<tr valign="top">
												<th align="center" class="tsAppBodyText">State Name</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
											</tr>
										</thead>
										<tbody>
										<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
											<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
												<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
												<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
												<cfset  local.license_status  = 'Active'>
												<tr class="tr_state_#local.thisItem#" id="tr_state_#local.thisItem#">
													<td class="visible-phone">State Name:</td>
													<td align="right" class="tsAppBodyText licenseText">#local.licenseTextArr[local.thisItem]#</td>
													<td align="center" class="tsAppBodyText">
														<input name="mpl_#local.thisItem#_licensenumber" id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText mpl_#local.thisItem#_licensenumber" type="text" value="#local.license_no#" size="13" maxlength="13" />
														<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" class="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
													</td>
													<td class="visible-phone">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy):</td>
													<td align="center" class="tsAppBodyText">
														<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText mpl_#local.thisItem#_activeDate" size="13" maxlength="10" />
														<cfsavecontent variable="local.datejs">
															<cfoutput>
															<script language="javascript">
																$(document).ready(function() { 
																	mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
																});
															</script>
															<style type="text/css">
															##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
															</style>
															</cfoutput>
														</cfsavecontent>
														<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
													</td>
													<td align="center" class="tsAppBodyText">
														<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText mpl_#local.thisItem#_status">
															<option value="">Please Select</option>
															<cfloop collection="#local.licenseStatus#" item="i" >
																	<option <cfif local.license_status eq LCase(local.licenseStatus[i]) >selected="selected"</cfif> value="#LCase(local.licenseStatus[i])#">#local.licenseStatus[i]#</option>
															</cfloop>															
														</select>
													</td>
												</tr>
											</cfloop>
											<tr></tr>
										<cfelse>
											<tr></tr>
										</cfif>									
										</tbody>
									</table>
								</td>
							</tr>					
						</table>
					</div>
				</div>
				
				<div class="row-fluid" id="professionalInfoSection">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetPrsnlEductn.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetPrsnlEductn.fieldSetContent#
					</div>
				</div>

				<span class="organizationsFirmsHolder">
					<div class="row-fluid">
						<div class="tsAppSectionHeading">Company/Firm Information</div>
						<div class="tsAppSectionContentContainer fieldSetContainer">	
							<p>#variables.strPageFields.CompanyFirmSelector#</p>							
							<table cellpadding="3" border="0" cellspacing="0" >									
								<tr align="top">
									<td class="tsAppBodyText" width="10">&nbsp;</td>
									<td class="tsAppBodyText" nowrap>Firm Lookup</td>
									<td class="tsAppBodyText">											
										<select name="companyField" class="companyField" id="companyField">
											<option "">Please Select</option>
											<cfloop query="local.qryCompanyByGroup">
												<option recordTypeCode="#local.qryCompanyByGroup.recordTypeCode#" value="#local.qryCompanyByGroup.memberID#" firstName="#local.qryCompanyByGroup.firstName#" lastname="#local.qryCompanyByGroup.lastName#" memberNumber="#local.qryCompanyByGroup.memberNumber#"  <cfif local.qryLinkedParentCompany.recordCount and local.qryLinkedParentCompany.company EQ local.qryCompanyByGroup.company>Selected</cfif>>#local.qryCompanyByGroup.company#</option>
											</cfloop>
										</select>
										<a href="javascript:void(0)" id="newCompany"> New Company/Firm</a>
									</td>
								</tr>
							</table>									
						</div>
					</div>
				</span>	

				<div class="row-fluid" id="officeAddProfInfoSection">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetOffAddrProInfo.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetOffAddrProInfo.fieldSetContent#
					</div>
				</div>	

				<div class="row-fluid" id="addresspreference">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetHomAddr.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetHomAddr.fieldSetContent#
					</div>
				</div>

				<div class="row-fluid">
					<div class="row-fluid tsAppSectionHeading">#local.strFieldSetAddrPrefs.fieldSetTitle#</div>
					<div class="row-fluid tsAppSectionContentContainer">
						#local.strFieldSetAddrPrefs.fieldSetContent#
					</div>
				</div>
				
				<div class="row-fluid fieldsetFormWrapper">
					<div class="span12 tsAppSectionContentContainer captchaWrap">							
						#variables.captchaDetails.htmlContent#
					</div>
				</div> 
				
				<button name="btnContinue" type="submit" class="tsAppBodyButton  btn btn-default" onClick="hideAlert();">Continue</button>
			</div>
			#application.objWebEditor.showEditorHeadScripts()#
			
			<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.rc.mc_siteinfo.orgid, includeTags=0)>

			<script language="javascript">	
				$(document).ready(function(){
					<cfloop query="local.qryOrgAddressTypes">
						addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1'));
						function addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#(_this){
							var _address = _this.val();
							
							if(_address.length >0){
								if($("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").length==0) {
									$('*[id^=mat_]').append('<option value="#local.qryOrgAddressTypes.addresstypeid#">#local.qryOrgAddressTypes.addresstype#</option>');
								}
							} else {
								$("select[id^=mat_] option[value='#local.qryOrgAddressTypes.addresstypeid#']").remove();
							}
						}
						
						$('##ma_#local.qryOrgAddressTypes.addresstypeid#_address1').on('change',function(){
							addressFieldUpdate#local.qryOrgAddressTypes.addresstypeid#($(this));
						});
					</cfloop>
				});
					
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			
			</form>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>

		<cfset local.response = "failure">
		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>

		<cfset local.rc = arguments.rc>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	
		
		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		
		<cfset local.memberShipCategoryColumnName = "Contact Type">
		<cfset local.memberShipCategoryStruct = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName=local.memberShipCategoryColumnName)>
		<cfset local.membershipCategoryColumnId = local.memberShipCategoryStruct.columnId>
		<cfif local.membershipCategoryColumnId NEQ '' AND local.membershipCategoryColumnId NEQ 0>
			<cfset local.membershipCategorySelected = arguments.rc['md_'&local.membershipCategoryColumnId]>
			<cfset local.membershipCategorySelectedValue = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(variables.orgid,local.memberShipCategoryColumnName,local.membershipCategorySelected)>
			<cfset local.contactTypeValue = local.membershipCategorySelectedValue>
		</cfif>
		
		<cfset local.earliestLicenseDate = "">
		<cfset local.selectedCategory = arguments.rc.hidCategory>
		<cfif local.selectedCategory EQ 'Attorney' AND structKeyExists(arguments.rc,"mpl_pltypeid")>
			<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
				<cfif len(arguments.rc['mpl_#local.key#_activeDate'])>
					<cfif len(local.earliestLicenseDate) AND (local.earliestLicenseDate GT arguments.rc['mpl_#local.key#_activeDate']) OR not len(local.earliestLicenseDate)>
						<cfset local.earliestLicenseDate = arguments.rc['mpl_#local.key#_activeDate']>						
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>
		<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeValue)>
		<cfset local.strResult1 = local.objSaveMember.saveData(runImmediately=1)>
		
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only adds history if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										
			<cfset session.captchaEntered = 1>
			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>
	
		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		 
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<!---<cfelse>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>--->
		</cfif>		
		
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>
		
 		<cfsavecontent variable="local.resultHtmlHeadText">
 			<cfoutput>
 				<script type="text/javascript">
	 				function validateMembershipInfoForm(){
						var arrReq = new Array();
						
						if(($("*[id^='sub#local.subscriptionID#_rate']").is(':checkbox') || $("*[id^='sub#local.subscriptionID#_rate']").is(':radio')) && !$("input[name='sub#local.subscriptionID#_rate']:checked").val()){
							arrReq[arrReq.length] = " Select Membership.";
						}
								
						#local.result.jsValidation#
						
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlertCustom(msg,afterFormLoad);
							return false;
						}					

						mc_continueForm($('###variables.formName#'));
						return false;
					}
					
					function subscriptionRadioHandler() {						
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").prop('checked', false).attr("disabled", true);
						$("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").prop('checked', false).attr("disabled", false);
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio][data-frequencyuid='"+$(this).attr("data-frequencyuid")+"']").attr("disabled", true).attr("disabled", false);

						var frequencyUid = $(this).attr("data-frequencyuid");	
						$("##sub#local.subscriptionID#_addons .subAvailableRates").each(function() {
							var subRateControls = $(this).find(".subRateCheckbox[type=radio]");
							if(subRateControls.length > 0){
								var matchedFrequencyCount = subRateControls.filter("[data-frequencyuid='"+frequencyUid+"']").length;
								if(matchedFrequencyCount == 0){
									subRateControls.attr("disabled", false);
								}								
							}							
						});
					}

					function addOnSubscriptionRadioHandler() {
						$(this).parent().parent().parent().find('input[type="checkbox"].subCheckbox').prop('checked', true).attr("disabled", false);
					}

					function addOnSubscriptionCheckboxHandler() {
						if($(this).is(':checked')){
							$(this).parents('label').next().find(".subRateCheckbox[type=radio][data-frequencyuid='"+$("[name=sub#local.subscriptionID#_rate][type=radio]:checked").attr("data-frequencyuid")+"']").prop('checked', true);
						}else{
							$(this).parents('label').next().find('.subRateCheckbox[type=radio]').prop('checked', false);
						}						
					}
					if($("[name=sub#local.subscriptionID#_rate]").length > 1){
						$("[name=sub#local.subscriptionID#_rate][type=radio]").on('click',subscriptionRadioHandler);
					} else if($("[name=sub#local.subscriptionID#_rate]").length == 0){
					} else {
						var _freqUID = $('##sub#local.subscriptionID#_selectedRate .subRateCheckbox').attr('data-frequencyuid');
						
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").attr("disabled", true);
						$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio][data-frequencyuid='"+_freqUID+"']").attr("disabled", true).attr("disabled", false);
						
					}
					$("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").on('click',addOnSubscriptionRadioHandler);
					$("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").on('click',addOnSubscriptionCheckboxHandler);

					<cfif arguments.rc.hidCategory EQ 'Law Student'>

						$(document).on('change',$('div[data-setname="Sections for Students"] > div > div > label > input[type="checkbox"]'),function(){
							if($('div[data-setname="Sections for Students"] > div > div > label > input[type="checkbox"]:checked').length >= 4){
								$('div[data-setname="Sections for Students"] > div > div > label > input[type="checkbox"]:not(:checked)').attr('disabled','disabled')
							}else{
								$('div[data-setname="Sections for Students"] > div > div > label > input[type="checkbox"]:not(:checked)').removeAttr('disabled');
							}
						});
					</cfif>
					$(document).ready(function(){
						$('.subAddonsArrayWrapper div.subAddonWrapper > .subAddonsArrayWrapper').each(function(k,val){
							thisId = $(this).attr('id');
							thisId0 = thisId.split('_')[0];
							if($('label[for="'+thisId0+'"]').find('input[type="checkbox"]:is(:checked)')){
								$(this).hide();
							}else{
								$(this).show();
							}
						});

						$('div.well.subAddonWrapper label.checkbox.subLabel> input[type="checkbox"]').on('click',function(){
							thisId = $(this).attr('id');
							if($('##'+thisId+'_addons').length > 0){
								if($(this).is(':checked')){
									$('##'+thisId+'_addons').show();
								}else{
									$('##'+thisId+'_addons').hide();
								}								
							}
						});
					});		

				</script>
 			</cfoutput>
 		</cfsavecontent>

 		<cfhtmlhead text="#local.resultHtmlHeadText#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.FormTitle)>
				<h2 class="TitleText" id="FormIntro">#variables.strPageFields.FormTitle#</h2>
			</cfif>	
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="hidCategory" id="hidCategory" value="#arguments.rc.hidCategory#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid" >
				<div class="row-fluid">
					<div class="span12">
						<cfif len(variables.strPageFields.Step2TopContent)>
							<h3>#variables.strPageFields.Step2TopContent#</h3>
						</cfif>
						
						#local.result.formcontent#
					</div>
				</div>
			</div>

			<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.rc>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
				
		<cfset local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
		
		<cfset local.arrPayMethods = ArrayNew(1)>
		<cfif len(trim(variables.strPageFields.ProfileCodeCredit)) AND trim(variables.strPageFields.ProfileCodeCredit) NEQ "NULL">
			<cfset ArrayAppend(local.arrPayMethods,variables.strPageFields.ProfileCodeCredit)>
		</cfif>
		<cfif len(trim(variables.strPageFields.ProfileCodeCheck))  AND trim(variables.strPageFields.ProfileCodeCheck) NEQ "NULL">
			<cfset ArrayAppend(local.arrPayMethods,variables.strPageFields.ProfileCodeCheck)>
		</cfif>
		<cfif len(trim(variables.strPageFields.ProfileCodeACH))  AND trim(variables.strPageFields.ProfileCodeACH) NEQ "NULL">
			<cfset ArrayAppend(local.arrPayMethods,variables.strPageFields.ProfileCodeACH)>
		</cfif>
		
		<cfset local.strReturn = 
			application.objCustomPageUtils.renderPaymentForm(
				arrPayMethods=local.arrPayMethods, 
				siteID=variables.siteID, 
				memberID=variables.useMID, 
				title="Choose Your Payment Method", 
				formName=variables.formName, 
				backStep="showMembershipInfo"
			)>
		<cfsavecontent variable="local.headcode">
			<cfoutput>#local.strReturn.headcode#</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headcode#">
	
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div>
				<cfif len(variables.strPageFields.FormTitle)>
					<h2 class="TitleText" id="FormIntro">#variables.strPageFields.FormTitle#</h2>
				</cfif>	
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm('#local.paymentRequired#')">
				<cfinput type="hidden" name="fa" id="fa" value="processPayment">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
				<cfinput type="hidden" name="hidCategory" id="hidCategory" value="#arguments.rc.hidCategory#">
				<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
				<cfloop collection="#arguments.rc#" item="local.thisField">
					<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
						or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
						or left(local.thisField,5) eq "mccf_"
						or left(local.thisField,3) eq "sub">
						<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
					</cfif>
				</cfloop>
			
				<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

				<cfif len(variables.strPageFields.Step3TopContent)>
					<h3>#variables.strPageFields.Step3TopContent#</h3>
				</cfif>
				<div class="BodyText">						
					#local.strResult.formContent#
					<br/>
				</div>
				<div class="BodyText"><b>Total Price</b></div>
				<div class="BodyText">						
					Total Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
				</div>
				<br/>
				
				<cfif local.paymentRequired>
					#local.strReturn.paymentHTML#
				<cfelse>
					<button name="btnContinue" type="submit" class="btn btn-default" onClick="hideAlert();" disabled>Continue</button>
					<button name="btnBack" type="button" style="float:right;" class="btn btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
				</cfif>
				<script>
					$(document).ready(function(){
						setTimeout(function() {
							$('button').attr('disabled',false);
						}, 1200);
					});
				</script>
				</cfform>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processPayment" access="private" output="true" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>
			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>

		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
		</cfif>

		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Contact Type')>
		<cfset local.memberTypeFieldCode = "md_" & local.memberTypeFieldInfo.COLUMNID/>
		<cfset local.memberTypeSelected = application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgid,columnname='Contact Type',valueIDList=local.strData["#local.memberTypeFieldCode#"])>
		<cfif val(local.strData["orgMemberID"]) NEQ 0>
			<cfset local.objAdminMember = CreateObject("component","model.admin.members.members") />
			<cfset local.availableRecordRelationships = local.objAdminMember.getAvailableRecordRelationships(orgID=variables.orgID, masterMemberID=val(local.strData["orgMemberID"]), childMemberID=variables.useMID) />

			<cfset local.relationShipCode = "">
			<cfif findNoCase("Attorney", local.memberTypeSelected) EQ 1>
				<cfset local.relationShipCode = "Attorney">
			<cfelseif findNoCase("Graduated Student, Awaiting Bar Results", local.memberTypeSelected) EQ 1>
				<cfset local.relationShipCode = "LawStudent">
			<cfelseif findNoCase("Judge", local.memberTypeSelected) EQ 1>
				<cfset local.relationShipCode = "Attorney">
			<cfelseif findNoCase("Law Faculty", local.memberTypeSelected) EQ 1>
				<cfset local.relationShipCode = "Staff">
			<cfelseif findNoCase("Law Student", local.memberTypeSelected) EQ 1>
				<cfset local.relationShipCode = "LawStudent">
			<cfelseif findNoCase("Legal Affiliate", local.memberTypeSelected) EQ 1>
				<cfset local.relationShipCode = "LegalAffiliate">
			</cfif>        

			<cfif len(local.relationShipCode)>
				<cfquery dbtype="query" name="local.qryStaffRecordRelationship">
					select recordTypeRelationshipTypeID, relationshipTypeName
					from [local].availableRecordRelationships.qryRecordRelationshipTypes
					where relationshipTypeCode = '#local.relationShipCode#' and recordTypeCode = '#local.strData["recordTypeCode"]#'
				</cfquery>
				<cfif len(local.qryStaffRecordRelationship)>
					<cfset local.objAdminMember.addRecordRelationship(mcproxy_orgID=variables.orgID, mcproxy_siteID=variables.siteID, masterMemberID=val(local.strData["orgMemberID"]), childMemberID=variables.useMID, recordTypeRelationshipTypeID=local.qryStaffRecordRelationship.recordTypeRelationshipTypeID, isActive=1)>
				</cfif>
			</cfif>
		</cfif>
		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 
		
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID, siteID=variables.siteID, orgID=variables.orgID)>

		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- Save card on file to subscription and all invoices --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.ProfileCodeCredit#,#variables.strPageFields.ProfileCodeACH#",arguments.rc.mccf_payMeth)>
			<!--- Payment and accounting --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
										assignedToMemberID=variables.useMID, 
										recordedByMemberID=variables.useMID, 
										rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
													amount=local.strAccTemp.totalPaymentAmount, 
													profileID=arguments.rc.mccf_payMethID, 
													profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>

		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.strFieldSetAppCred = application.objCustomPageUtils.renderFieldSet(uid='9DBD72D3-7526-4020-8E15-BBAE0F0C8BCA', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetAttrnyCred = application.objCustomPageUtils.renderFieldSet(uid='11DF5818-3432-4CBA-BE2E-CBBE9A490ABE', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetAffCred = application.objCustomPageUtils.renderFieldSet(uid='904732DB-AF8A-4E8D-9B9A-BE282118B16E', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetPrsnlEductn = application.objCustomPageUtils.renderFieldSet(uid='8B1D287F-62E0-46DD-93F1-F811A5470C43', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetOffAddrProInfo = application.objCustomPageUtils.renderFieldSet(uid='57D49809-EDB0-4348-A67B-BD2A8C07069D', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetHomAddr = application.objCustomPageUtils.renderFieldSet(uid='A672A619-D337-4075-8DC0-06F894FD7D5D', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetAddrPrefs = application.objCustomPageUtils.renderFieldSet(uid='8E5406BA-F21D-4666-A98D-865CA586A4F1', mode="confirmation", strData=arguments.rc)>
		<cfset local.selectedCategory = arguments.rc.hidCategory>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		
		<cfset local.strResult = showSubscriptionFormSelectionsCustom(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>
				
		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
		
		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.confirmationPageHTMLContent">
			<cfoutput>			
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<fieldset>
						<div class="row-fluid HeaderText">#replace(replace(variables.strPageFields.ConfirmationContent,'<p>',''),'</p>','')#</br></div>
					</fieldset>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForMember">
			<cfoutput>
				<cfif len(variables.strPageFields.ConfirmationContent)>
					<div class="row-fluid HeaderText">
						<div class="span12">#replace(replace(variables.strPageFields.ConfirmationContent,'<p>',''),'</p>','')#</br></br></div>
					</div>	
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationContentForStaff">
			<cfoutput>
				<div class="row-fluid HeaderText">
					<div class="span12">You have received an application for membership.</br></br></div>
				</div>	
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			
			<!--@@ConfirmationContentforEmail@@-->
			<div class=" row-fluid confirmationDetails BodyText">
				<!--@@specialcontent@@-->

				<div class="row-fluid">Here are the details of your application:</div><br/>
				
				#local.strFieldSetAppCred.fieldSetContent#		
				<cfif local.selectedCategory EQ 'Attorney'>			
					#local.strFieldSetAttrnyCred.fieldSetContent#
				</cfif>
				<cfif local.selectedCategory EQ 'Legal Affiliate'>	
					#local.strFieldSetAffCred.fieldSetContent#
				</cfif>

				<cfif local.selectedCategory EQ 'Attorney'>	
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
						</tr>				
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
								<table cellpadding="3" border="0" cellspacing="0">						
									<tr valign="top">
										<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
											<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
												<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
													<thead>
														<tr valign="top">
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">State Name</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
															<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
														</tr>
													</thead>
													<tbody>
													<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
														<tr id="tr_state_#local.key#">
															<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensename']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
															<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
														</tr>
													</cfloop>
													</tbody>
												</table>
											</cfif>	
										</td>
									</tr>						
								</table>
							</td>
						</tr>
					</table>
					<br/>
				</cfif>
				#local.strFieldSetPrsnlEductn.fieldSetContent#
				<cfif local.selectedCategory NEQ 'Law Student'>	
					#local.strFieldSetOffAddrProInfo.fieldSetContent#
				</cfif>
				#local.strFieldSetHomAddr.fieldSetContent#
				#local.strFieldSetAddrPrefs.fieldSetContent#

				<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<div>#local.strResult.formContent#</div>
							<br/><br/>							
							<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
							<br/>					
						</td>
					</tr>
				</table>
				<br/>
				
				<cfif local.paymentRequired>
					<br/>
					<table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
					</tr>
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
								<table class="table" cellpadding="3" border="0" cellspacing="0">
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
									</td>
								</tr>
								</table>
							<cfelse>
								None selected.
							</cfif>
						</td>
					</tr>
					</table>
				</cfif>	
			</div>	
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->		
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = '' >
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForMember)>		
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.memberEmail.from },
                            emailto=[{ name="", email=variables.memberEmail.to }],
                            emailreplyto=variables.ORGEmail.to,
                            emailsubject=variables.memberEmail.SUBJECT,
                            emailtitle="#arguments.rc.mc_siteInfo.sitename# - Membership Application",
                            emailhtmlcontent=local.confirmationHTMLToMember,
                            siteID=arguments.rc.mc_siteinfo.siteid,
                            memberID=val(variables.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to the Atlanta Bar", emailContent=local.confirmationHTMLToStaff)>
		
		<cftry>
			<cfset local.uid = createuuid()>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
			<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<html>
					<head>
					
					</head>
					<body>
						#local.confirmationHTMLToMember#
					</body>
					</html>
				</cfoutput>
			</cfdocument>
			
			<cfset local.strPDF = structNew()>
			<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
			<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
			<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
			<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
			<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
			<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID)>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Unable to add document to member record.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
			</cfcatch>
		</cftry>
		
		<cfreturn local.confirmationHTMLToMember>
	</cffunction>
	
	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div>						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>

		<cfreturn getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)>
	</cffunction>

	<cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', totalFullPrice = 0 }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">			
		</cfif>
		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								- 
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
										<!--- #local.thisRate.rateName#  --->										
										<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
											#local.thisRate.rateName#  (<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> Full)
											<cfset local.strReturn.totalFullPrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
										<cfelse>
										   <cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid )>
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
													<cfif arguments.recursionLevel eq 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>																									
														#local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
													<cfelseif arguments.recursionLevel gt 1>														
														#local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>												
													</cfif>	
												</cfif> 						
											</cfloop>											
										</cfif>
									</cfif>
								</cfloop>
							</span>
						</div>
					</div>	
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">		
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false />							
									<cfset local.pcNumCounter = 1 />									
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false />
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true />
										</cfif>										
										<cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree) />
										
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
										<cfif len(trim(local.thisAddonSubForm.formContent))>										
											<cfset local.selectionFound = true />
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
											</cfif>
											
											<cfset local.pcNumCounter = local.pcNumCounter + 1 />											
											#local.thisAddonSubForm.formContent#
										</cfif>									
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>						
						</div>
					</cfif>					
				</cfif>
			</cfoutput>				
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>
	
    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="row-fluid tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class=" row-fluid tsAppSectionContentContainer">
				<cfif arguments.errorCode eq "billedfound">
					<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
					<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
					#replaceNoCase(variables.strPageFields.BilledMessage,"[[click here]]","<a href='#local.redirectLink#'>click here</a>")#
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "activefound" OR arguments.errorCode eq "acceptedfound">
					<a href ='/?pg=login'>#variables.strPageFields.ActiveAcceptedMessage#</a>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>