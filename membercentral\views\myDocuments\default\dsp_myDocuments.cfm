<cfset local.dataStruct = attributes.data>
<cfset local.rc = attributes.event.getCollection()>

<cfinclude template="../commonJS.cfm">

<!--- extra style sheet info for tabs --->
<cfsavecontent variable="local.tabs">
    <cfoutput>
    <style type="text/css">
        ##tabs { border-bottom:1px solid ##ccc; margin:0; padding-bottom:19px; padding-left:10px; }
        ##tabs ul, ##tabs li { display:inline; list-style-type:none; margin:0; padding:0; }	
        ##tabs a:link, ##tabs a:visited { background:##E8EBF0; border:1px solid ##ccc; color:##666; float:left; 
        line-height:14px; margin-right:8px; padding:2px 10px; text-decoration:none; }	
        ##tabs a:link.active, ##tabs a:visited.active { border-bottom:1px solid ##fff; color:##000; }
        ##tabs a:hover { color:##f00; }
        .tabsetCD ##tabs li##nav_CD a, 
        .tabsetPD ##tabs li##nav_PD a, 
        .tabsetPMD ##tabs li##nav_PMD a, 
        .tabsetPV ##tabs li##nav_PV a,
        .tabsetPDA ##tabs li##nav_PDA a,
        .tabsetPDT ##tabs li##nav_PDT a { background:##fff; border-bottom:1px solid ##fff; color:##000; }
        ##tabs ul a:hover { color:##f00 !important; }
        ##s_listing { border:1px solid ##ccc; border-top:none; clear:both; margin:0px; padding:10px 0; }
        div.s_pgtop { padding:3px 3px 6px 3px; }
        div.s_rhrd { background-color:##DEDEDE;padding:3px;margin-bottom:3px; }
        div.s_row { padding:6px; }
        div.s_row_alt { background-color:##DEDEDE; }
        div.s_act { float:right; padding:0 0 5px 5px; }
        div.s_dtl { margin-left:20px; }
        div.s_opt { padding:3px; }
    </style>
    </cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.tabs#">
<cfoutput>
    <table width="100%" cellpadding="10" cellspacing="0">
        <tr>
            <td>
                <div id="tsSearchAppWrapper">
                    <div class="tsAppBodyText" id="tssearchbararea">
                        <ul id="tssearchbar" class="tsAppB50" style="margin-bottom:10px;"></ul>
                        <ul id="tsdocumentviewerbar" class="tsAppB50" style="display:none;margin-bottom:10px;"></ul>
                        <ul id="tsinlinecartbar" class="tsAppB50"  style="display:none;margin-bottom:10px;">
                            <li id="cartnav_checkout"><a href="javascript:doCheckoutDocumentCart(#session.cfcuser.memberdata.memberid#, #local.dataStruct.hasMissingTaxInfo#);"><i class="icon-shopping-cart" style="vertical-align: inherit;"></i> Checkout</a></li>
                            <li id="cartnav_close"><a href="javascript:cart_closeInlineCart();"><i class="icon-remove-circle" style="vertical-align: inherit;"></i> Close Cart</a></li>
                        </ul>
                    </div>
                    <!--- header --->
                    <div style="margin-top:8px;" id="searchMyDocumentsButtonDiv">
                        <div class="tsAppHeading">My Documents</div>
                        <button  class="tsAppHeading" style="float:right;" onClick="document.location.href='/?pg=search&bid=#local.dataStruct.mydocumentsBucketID#';"><img align="absmiddle" src="/assets/common/images/search/magnifier.png"/> Search My Documents</button>
                        <button  class="tsAppHeading" style="float:right; margin-right:10px;" onClick="document.location.href='/?pg=uploaddocuments&ul=1';"><img align="absmiddle" src="/assets/common/images/search/up24.png" height="16px" width="16px"/> Upload Documents</button>
                        
                    </div>
                    <!--- tabs --->
                    <div style="margin-top:20px;clear:both;" class="tsAppBodyText tabset#local.dataStruct.tab#" id="myDocumentsTabs">
                        <ul id="tabs">
                            <li id="nav_CD"><a href="/?pg=myDocuments&tab=CD">Contributed Documents</a></li>
                            <li id="nav_PD"><a href="/?pg=myDocuments&tab=PD">Documents</a></li>
                            <li id="nav_PV"><a href="/?pg=myDocuments&tab=PV">Verdicts</a></li>
                            <li id="nav_PMD"><a href="/?pg=myDocuments&tab=PMD">Medline Documents</a></li>
                            <li id="nav_PDA"><a href="/?pg=myDocuments&tab=PDA">Disciplinary Actions</a></li>
                            <li id="nav_PDT"><a href="/?pg=myDocuments&tab=PDT">Expert Challenges</a></li>
                        </ul>
                    </div>

                    <!--- switch depending on tab --->
                    <div id="s_listing" class="tab-content tsAppBodyText">
                        <div class="tab-pane active">
                        </div>
                    </div>
                    
                    <div id="tsDocumentViewer" style="height: 500px; overflow: hidden;display:none;"></div>
                    <div id="tsCartViewer" style="display:none;"></div>
                </div>	
            </td>
        </tr>
    </table>
</cfoutput>