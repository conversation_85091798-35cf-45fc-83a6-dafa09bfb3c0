<cfinclude template="../commonJoinTrialSmithStep2JS.cfm">

<cfoutput>
<cfif len(local.qryContentJoinTSPromotionalMessageContent.rawContent)>
	<div class="joinTrialSmith-card joinTrialSmith-mt-3 joinTrialSmith-p-3">	
		<div class="row-fluid">									
			<div class="span12">			
				<div>#local.qryContentJoinTSPromotionalMessageContent.rawContent#</div>
			</div>
		</div>
	</div>
</cfif>
<div class="joinTrialSmith-card joinTrialSmith-mt-3 joinTrialSmith-p-3">
	<div id="joinTrialSmithPlanContainer" <cfif len(local.selectedPlanString)> style="display: none;"</cfif>>
		<cfif len(local.codeErr)>
			<div class="alert alert-danger planError">
				#local.codeErr#
			</div>         
		</cfif> 
		<div class="page-header joinTrialSmith-mt-0">
			<h3>Choose a TrialSmith Subscription Plan</h3>
		</div>
		<div class="joinTrialSmith-mb-3">
			<form name="frmJoin" action="#arguments.event.getValue('jointrialsmithurl')#" method="POST" class="form-inline">
				<div class="row-fluid">									
					<div class="span9">                                              
						<cfif len(session.cfcuser.subscriptionData['4'].strJoinTS.promoCode) is 0 >
							<p>If you have a Promotional Code, enter it here to view updated pricing.</p>
						<cfelseif len(session.cfcuser.subscriptionData['4'].strJoinTS.promoCode)> 
							<cfif len(local.qryPlans.codeDescription) and local.strValidate.isvalid>        
								<p>
									<b>#session.cfcuser.subscriptionData['4'].strJoinTS.promoCode#</b> - #local.qryPlans.codeDescription#
								</p>
							</cfif>                       
						</cfif>                    
					</div>
					<div class="span3 text-right">
						<div class="input-append">
							<cfif len(session.cfcuser.subscriptionData['4'].strJoinTS.promoCode) and local.strValidate.isvalid>
								<button type="button" name="btnPromo" id="btnPromo" class="btn" onClick="promoSubmit(0)" value="#session.cfcuser.subscriptionData['4'].strJoinTS.promoCode#">Remove Promotional Code</button>
							<cfelse>                     
								<input type="text" name="promoCode" id="promoCode" size="12" value="">          
								<button type="button" name="btnPromo" id="btnPromo" class="btn" onClick="promoSubmit(1)">Check Code</button>                            
							</cfif>
						</div>      
					</div>
				</div>  
				<cfloop query="local.qryPlans">
					<cfset local.theMemberTypeID = local.qryPlans.membertypeid>
					<!--- get the best price (dont show tax in this table, so dont pass in billingstate) --->
					<cfset local.currmemtype = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='MemberType')>
					<cfset local.strPlanDetails = local.objPlans.getPlanDetails(orgcode=session.cfcuser.subscriptionData['4'].strJoinTS.tlastate,
						billingstate='', billingzip='', depoMemberDataID=session.cfcuser.memberdata.depoMemberDataID, membertypeid=local.theMemberTypeID,
						action='join', CurrentMembertypeID=IIF(local.currmemtype EQ "","0",local.currmemtype), promoCode=session.cfcuser.subscriptionData['4'].strJoinTS.promoCode)>

					<div class="well">
						<!--- plan must be 1 to show button --->
						<!--- expired should show all buttons --->
						<!--- not expired should show all buttons except current plan --->
						<label class="radio">
							<cfif local.qryPlans.memberTypeStatus is 1>
								<input type="Radio" name="membertype" id="membertype" value="#local.theMemberTypeID#" <cfif session.cfcuser.subscriptionData['4'].strJoinTS.membertype is local.qryPlans.membertypeid> checked</cfif>>
							</cfif>
							<b>#local.qryPlans.membertype#</b> - #local.strPlanDetails.sel_PriceShown# <cfif val(local.strPlanDetails.sel_YouSave) gt 0><b><span class="red">(you save #DollarFormat(local.strPlanDetails.sel_YouSave)#)</span></b></cfif>
						</label>
						<div class="row-fluid">									
							<div class="span6">                          
								#local.qryPlans.Description#</u>
							</div>
							<div class="span6">                            
								#local.qryPlans.Description2#</u>
							</div>
						</div>                            
					</div>	
				</cfloop>
			</form>
		</div>
	</div>
	
	<div id="joinTrialSmithSelectedPlanContainer" class="joinTrialSmithSummary joinTrialSmith-cursor-pointer" data-jointrialsmithsummarystep="3" <cfif len(local.selectedPlanString) EQ 0> style="display:none;" </cfif>>
		<div class="joinTrialSmith-d-flex">
			<a href="##" class="joinTrialSmith-align-self-center joinTrialSmith-mr-2 joinTrialSmith-font-size-lg joinTrialSmith-text-decoration-none" onclick="return false;"><i class="icon icon-pencil"></i></a>
			<div class="joinTrialSmith-col">
				<div id="joinTrialSmithSelectedPlan" class="joinTrialSmith-font-size-lg joinTrialSmith-font-weight-bold">
					#local.selectedPlanString#
				</div>
			</div>
		</div>
	</div>
	<div id="joinTrialSmithStep2SaveLoading" style="display:none;"></div>
</div>
</cfoutput>