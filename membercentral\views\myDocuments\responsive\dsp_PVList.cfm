<cfoutput>
    <div style="padding:5px">
        <h5>Purchased Verdicts</h5>
        #getPaginationHTML(topOrBottom="top",recordCount=local.qryAllVerdicts.recordCount,itemCount=val(local.qryAllVerdicts.itemCount), docType="verdict",maxRows=local.maxRows,startRow=local.startrow,tab='PV',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any verdict reports.")#

        <cfoutput query="local.qryAllVerdicts">
            <div class="row-fluid s_row <cfif local.qryAllVerdicts.currentrow mod 2 is 0>s_row_alt</cfif>">
                <div class="span12">
                    <div class="row-fluid">
                        <div class="span10">
                            <i class="icon icon-file-text" style="line-height:22px;"></i><b>&nbsp;#local.qryAllVerdicts.casename#</b>
                            <div class="s_dtl">
                                <div class="hidden-phone">
                                    <b class="s_label">Date:</b> <cfif isDate(local.qryAllVerdicts.VerdictDate)>#DateFormat(local.qryAllVerdicts.VerdictDate,"m/d/yyyy")#<cfelse>#local.qryAllVerdicts.VerdictDate#</cfif><br/>
                                    #local.qryAllVerdicts.court#, #local.qryAllVerdicts.state#
                                    <cfif len(local.qryAllVerdicts.experts)><br/>
                                        <b class="s_label">Experts:</b><div class="span12">#local.qryAllVerdicts.experts#</div>
                                    </cfif>
                                </div>
                                <div align="left"><b class="s_label">Purchased:</b> #dateformat(local.qryAllVerdicts.dateadded,"m/d/yyyy")#</div>
                            </div>
                        </div>
                        <div class="span2 s_act">
                            <div class="s_opt">
                                <a href="javascript:viewVerdict(#local.qryAllVerdicts.verdictid#);" title="View Verdict"><i class="icon icon-file-text" style="line-height:23px;"></i> View Verdict</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </cfoutput>
        #getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllVerdicts.recordCount,itemCount=val(local.qryAllVerdicts.itemCount), docType="verdict",maxRows=local.maxRows,startRow=local.startrow,tab='PV',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any verdict reports.")#
    </div>
</cfoutput>		