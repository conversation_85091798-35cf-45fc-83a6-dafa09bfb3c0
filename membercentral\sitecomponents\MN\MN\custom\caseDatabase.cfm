<cfscript>
	local.vAction = event.getValue('action','search');
	
	local.baseLink 		= '/?pg=caseDatabase';
	local.viewLink		= local.baseLink & '&action=view';
	local.editLink		= local.baseLink & '&action=edit';
	local.deleteLink	= local.baseLink & '&action=delete';
	local.saveLink		= local.baseLink & '&action=save';
	local.resultsLink	= local.baseLink & '&action=results';
</cfscript>

<cfset variables.strEMailSettings_staff = { 
	from="<EMAIL>", 
	to="<EMAIL>", 
	subject="CaseDatabase Form Submitted.",
	type="HTML",
	mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
}>

	<cfswitch expression="#local.vAction#">
	
		<!--- view record --->
		<cfcase value="view">
			<cfoutput>
				<cfform action="#local.resultsLink#" method="post">
					<cfloop collection="#session.fieldArr#" item="local.key">
						<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#session.fieldArr[local.key]#">
					</cfloop>
					
				<cfset local.qryCase = getCase(int(val(event.getValue('caseID',0))))>
				<cfif local.qryCase.recordcount is 0>
					<cflocation url="#local.baseLink#" addtoken="No">
				</cfif>
				<p class="headerText">Viewing Information in MAJ's Case Reports Database.</p>
				<div><input type="submit" value="Back to Listing" class="bodyText" /></div>
				</cfform>
				<br/>
				<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
					<tr valign="top">
						<td width="50%">&nbsp;</td>
						<td>&nbsp;</td>						
					</tr>					
					<cfif len(local.qryCase.dispostionDt) and isdate(local.qryCase.dispostionDt)>
						<tr valign="top">
							<td><strong>Approval Status:</strong></td>
							<td>#local.qryCase.caseStatusName#</td>
							
						</tr>
					</cfif>
					<cfif len(local.qryCase.typeID) or len(local.qryCase.typeID)>
						<tr valign="top">
							<td><strong>Case Type:</strong></td>
							<td>#local.qryCase.caseTypeName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.courtID) or len(local.qryCase.courtID)>
						<tr valign="top">
							<td><strong>Court:</strong></td>
							<td>#local.qryCase.caseCourtName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.caseName) or len(local.qryCase.caseName)>
						<tr valign="top">
							<td><strong>Name of Case:</strong></td>
							<td>#local.qryCase.caseName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.courtCaseNum)>
						<tr valign="top">
							<td><strong>Court Case Number:</strong></td>
							<td>#local.qryCase.courtCaseNum#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.dispostionDt) or len(local.qryCase.dispostionDt)>
						<tr valign="top">
							<td><strong>Date of Disposition:</strong></td>
							<td>#dateformat(local.qryCase.dispostionDt,"mm/dd/yyyy")#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.legalIssueTxt)>
						<tr valign="top">
							<td><strong>Legal Issue or Holding:</strong></td>
							<td>#local.qryCase.legalIssueTxt#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.caseDesc)>
						<tr valign="top">
							<td><strong>Description of Cases (Facts,Damges,etc.):</strong></td>
							<td>#local.qryCase.caseDesc#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.plantiffExpertTxt)>
						<tr valign="top">
							<td><strong>Plantiff's Expert(s):</strong></td>
							<td>#local.qryCase.plantiffExpertTxt#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.defenseExpertTxt)>
						<tr valign="top">
							<td><strong>Defense Expert(s):</strong></td>
							<td>#local.qryCase.defenseExpertTxt#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.demandAmt)>
						<tr valign="top">
							<td><strong>Demand Amount:</strong></td>
							<td>#dollarFormat(local.qryCase.demandAmt)#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.offerAmt)>
						<tr valign="top">
							<td><strong>Offer:</strong></td>
							<td>#dollarFormat(local.qryCase.offerAmt)#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.caseResolutionName)>
						<tr valign="top">
							<td><strong>Resolution:</strong></td>
							<td>#local.qryCase.caseResolutionName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.resolutionAmt)>
						<tr valign="top">
							<td><strong>Resolution Amount:</strong></td>
							<td>#dollarFormat(local.qryCase.resolutionAmt)#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.structureSettlementBit)>
						<tr valign="top">
							<td><strong>Was this a structured settlement?:</strong></td>
							<td>
								<cfif local.qryCase.structureSettlementBit>
									Yes
									<cfelse>
										No
								</cfif>
							</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.structureDesc)>
						<tr valign="top">
							<td><strong>Structure and Present Value:</strong></td>
							<td>#local.qryCase.structureDesc#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.judgeName)>
						<tr valign="top">
							<td><strong>Name of Judge:</strong></td>
							<td>#local.qryCase.judgeName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.defenseAttorneyName)>
						<tr valign="top">
							<td><strong>Defense Attorney:</strong></td>
							<td>#local.qryCase.defenseAttorneyName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.insuranceCompanyName)>
						<tr valign="top">
							<td><strong>Insurance Company:</strong></td>
							<td>#local.qryCase.insuranceCompanyName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.insuranceAdjusterName)>
						<tr valign="top">
							<td><strong>Insurance Adjuster:</strong></td>
							<td>#local.qryCase.insuranceAdjusterName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.submitAttorneyName)>
						<tr valign="top">
							<td><strong>Submitting Attorney:</strong></td>
							<td>#local.qryCase.submitAttorneyName#</td>
						</tr>
					</cfif>
					<cfif len(local.qryCase.submitFirmName)>
						<tr valign="top">
							<td><strong>Submitting Attorney Firm:</strong></td>
							<td>#local.qryCase.submitFirmName#</td>
						</tr>
					</cfif>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="edit">
			<cfset local.qryCase = getCase(int(val(event.getValue('caseID',0))))>


			<cfset allowForm = false>
			
			<cfoutput>

				
				<cfif val(event.getValue('caseID',0))>
					<!--- EDIT Case: --->
					<p class="headerText">Edit Information in MAJ's Case and Settlement Exchange Database</p>
					<p class="bodyText">Complete the form below to edit this record.</p>
					<cfif
						val(event.getValue('customPage.myRights.customAddDatabase',0))
						AND (session.cfcUser.memberData.memberID eq local.qryCase.createdBy OR application.objUser.isSiteAdmin(cfcuser=session.cfcuser))
					>
						<cfset allowForm = true>
					<cfelse>
						<p class="bodyText">You do not have permission to edit this information.</p>
					</cfif>
					
					
				<cfelse>
					<!--- ADD Case: --->
					<p class="headerText">Add to MAJ's Case Reports Database</p>
					<p class="bodyText">Complete the form below to add a Case to the database.</p>
					
					<cfif val(event.getValue('customPage.myRights.view',0))>
						<cfset allowForm = true>
					<cfelse>
						<p class="bodyText">You do not have permission to add to this database.</p>
					</cfif>
					
				</cfif>
			</cfoutput>
			
			<cfif allowForm>
				<cfoutput>
				<cfset local.qryCaseTypes 		= getCaseTypes()>
				<cfset local.qryResolutionTypes = getResolutionTypes()>
				
				<cfset local.qrycourtIDs 			= getcourtIDs()>
				<cfset local.qryStatus 				= getStatus()>

				<cfsavecontent variable="local.JS">
					<style type="text/css">
						##date { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
						input[type=checkbox],input[type=radio]{padding: 5px;}
						.checkLabel{display: inline;padding: 0 2px;}
						.chkgrp td{padding:5px;}
						textarea{width: 100%;}
						.cal a{float: right;}
						.cal{width: 30%;}
					</style>			
				
					<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">

						function hideAlert() { $('##issuemsg').html('').hide(); };
						function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert').show(); };
						function showAlertDone(msg) { $('##issuemsg').html(msg).attr('class','success').show(); };

						function _FB_validateForm() {

							hideAlert();
							
							var arrReq = new Array();

							if ($.trim($('##CaseForm ##typeID').val()).length == 0 && $.trim($('##CaseForm ##typeIDNew').val()).length == 0 ) arrReq[arrReq.length] = 'Enter Case Type.';							
							if ($.trim($('##CaseForm ##caseName').val()).length == 0) arrReq[arrReq.length] = 'Enter Name of Case.';
							if ($.trim($('##CaseForm ##caseDesc').val()).length == 0) arrReq[arrReq.length] = 'Enter Description of Case.';
							if ($.trim($('##CaseForm ##submitAttorneyName').val()).length == 0) arrReq[arrReq.length] = 'Enter Submitting Attorney Name.';												
							
							
							var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;

							var amount = $('##offerAmt').val();
							amount = Number(amount.replace(/[^0-9\.]+/g,""));

							if( ( $.trim(amount).length) > 0 && !amountRegex.test($.trim(amount)))  {
								arrReq[arrReq.length] = 'Enter a valid Offer Amount. Only positive amounts are allowed.';
							}

							var amount = $('##demandAmt').val();
							amount = Number(amount.replace(/[^0-9\.]+/g,""));

							if( ( $.trim(amount).length) > 0 && !amountRegex.test($.trim(amount)))  {
								arrReq[arrReq.length] = 'Enter a valid Demand Amount. Only positive amounts are allowed.';
							}

							var amount = $('##resolutionAmt').val();
							amount = Number(amount.replace(/[^0-9\.]+/g,""));

							if( ( $.trim(amount).length) > 0 && !amountRegex.test($.trim(amount)))  {
								arrReq[arrReq.length] = 'Enter a valid Resolution Amount. Only positive amounts are allowed.';
							}

							if (arrReq.length > 0) {
								var msg = 'Please address the following issues with your application:<br/>';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								$('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
								return false;
							}
							return true;
						}

						function changeAvailability(formfieldid,disableflag) {
							var ff = document.getElementById(formfieldid);
							ff.disabled = disableflag;
							if (disableflag) ff.value='disabled';
							else ff.value='';
						}  
						//-->		
						
						$(document).ready(function(){
							mca_setupDatePickerField('dispostionDt');
							$("input[name='structureSettlementBit']").click(function(){
								if($(this).val()==1){
									$("##structuredPara").show();
								}else{
									$("##structuredPara").hide();
								}
							});

							$("##offerAmt").change(function(event) {	
								var selectionAmt = $("##offerAmt").val();
								selectionAmt = Number(selectionAmt.replace(/[^0-9\.]+/g,""));
								var totalAmount = selectionAmt;
								totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
								    return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
								});	
								$("##offerAmt").val("$ " + totalAmount);	
												
							});

							$("##demandAmt").change(function(event) {	
								var selectionAmt = $("##demandAmt").val();
								selectionAmt = Number(selectionAmt.replace(/[^0-9\.]+/g,""));
								var totalAmount = selectionAmt;
								totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
								    return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
								});	
								$("##demandAmt").val("$ " + totalAmount);	
												
							});

							$("##resolutionAmt").change(function(event) {	
								var selectionAmt = $("##resolutionAmt").val();
								selectionAmt = Number(selectionAmt.replace(/[^0-9\.]+/g,""));
								var totalAmount = selectionAmt;
								totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
								    return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
								});	
								$("##resolutionAmt").val("$ " + totalAmount);	
												
							});
						});					
					</SCRIPT>
				</cfsavecontent>
				<cfform id="cancelForm" action="#local.resultsLink#" method="post">
					<cfif structKeyExists(session, "fieldArr") AND structCount(session.fieldArr) gt 0>
						<cfloop collection="#session.fieldArr#" item="local.key">
							<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#session.fieldArr[local.key]#">
						</cfloop>
						<cfelse>
								<input id="CASENAME" type="hidden" name="CASENAME">
								<input id="DISPOSTIONDT" type="hidden" name="DISPOSTIONDT">
								<input id="PAGE" type="hidden" value="1" name="PAGE">
								<input id="CASETYPES" type="hidden" name="CASETYPES">
								<input id="CASEDESC" type="hidden" name="CASEDESC">
								<input id="SUBMITATTORNEYNAME" type="hidden" name="SUBMITATTORNEYNAME">
					</cfif>
				</cfform>
				<cfhtmlhead text="#local.JS#">
					<cfform name="CaseForm"  id="CaseForm" action="#local.saveLink#" method="post"  onsubmit="return _FB_validateForm();">
						<cfinput type="hidden" name="caseID"  id="caseID" value="#val(local.qryCase.caseID)#">
						<div>
							<input type="submit" value="Save Case" name="btnSave" class="bodyText" /> &nbsp;
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) AND val(local.qryCase.caseID) gt 0>
								<input type="button" name="btnDelete" value="Delete Case" class="bodyText" onclick="var msg='Are you sure you want to delete this Case?\nIf you click OK, this Case will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&caseID=#val(local.qryCase.caseID)#';"/> &nbsp;
							</cfif>
							<input type="button" value="Cancel" onclick="$('##cancelForm').submit();" class="bodyText" />
						</div>
						<br/>
						<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
						<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
							<tr valign="top">
								<td nowrap><strong>Approval Status:</strong></td>
								<td>
									<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
										<select id="statusID" name="statusID" class="bodyText" >
										<option value="">Select Status</option>
										<cfloop query="local.qryStatus">
											<option value="#local.qryStatus.caseStatusID#" <cfif (local.qryCase.statusID eq local.qryStatus.caseStatusID) or (not len(trim(local.qryCase.caseID)) and local.qryStatus.caseStatusName eq "Not Approved")>selected</cfif>>#local.qryStatus.caseStatusName#</option>
										</cfloop>
										</select>
									<cfelse>
										<select id="statusID"  name="statusID" class="bodyText" >
										<cfloop query="local.qryStatus">
											<cfif local.qryStatus.caseStatusID eq 2>
												<option value="#local.qryStatus.caseStatusID#" <cfif local.qryCase.statusID eq local.qryStatus.caseStatusID>selected</cfif>>#local.qryStatus.caseStatusName#</option>
											</cfif>
										</cfloop>
										</select>
									</cfif>
								</td>
							</tr>
							<tr valign="top">
								<td nowrap><strong>Case Type:</strong></td>
								<td>
									<select id="typeID" name="typeID" class="bodyText" >
										<option value="">Select Case:</option>
										<cfloop query="local.qrycasetypes">
											<option value="#local.qrycasetypes.caseTypeID#" <cfif local.qryCase.typeID eq local.qrycasetypes.caseTypeID>selected</cfif>>#local.qrycasetypes.caseTypeName#</option>
										</cfloop>
									</select>
									<input type="text" name="typeIDNew" id="typeIDNew">
								</td>
							</tr>

							<tr valign="top">
									<td nowrap>
										<strong>Court - </strong>Select From List OR Add New Entry:
									</td>
									<td>
										<select id="courtID" name="courtID" class="bodyText" >
											<option value="">Select Court:</option>
											<cfloop query="local.qrycourtIDs">
												<option value="#local.qrycourtIDs.caseCourtID#" <cfif local.qryCase.courtID eq local.qrycourtIDs.caseCourtID>selected</cfif>>#local.qrycourtIDs.caseCourtName#</option>
											</cfloop>
										</select>
										<input type="text" name="courtIDNew">
									</td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Name of Case:</strong></td>
								<td><input type="text" name="caseName"  id="caseName"  size="70" value="#local.qryCase.caseName#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Court Case Number:</strong></td>
								<td><input type="text" name="courtCaseNum"  id="courtCaseNum"  size="20" value="#local.qryCase.courtCaseNum#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Date of Disposition:</strong></td>
								<td>
									<div class="cal">
									<cfinput type="text" name="dispostionDt" id="dispostionDt" size="10" value="#dateformat(local.qryCase.dispostionDt,"mm/dd/yyyy")#">
									<a href="javascript:mca_clearDateRangeField('dispostionDt');" title="Clear Date">
										<i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 5px 7px;"></i>
									</a>
									</div>
								</td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Legal Issue or Holding:</strong></td>
								<td><textArea name="legalIssueTxt"  id="legalIssueTxt" maxlength="1000" rows="5" >#local.qryCase.legalIssueTxt#</textarea></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Description of Cases (Facts,Damges,etc.):</strong></td>
								<td><textArea name="caseDesc"  id="caseDesc" rows="5" >#local.qryCase.caseDesc#</textarea></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Plaintiff's Expert(s):</strong></td>
								<td><textArea name="plantiffExpertTxt"  id="plantiffExpertTxt" maxlength="1000" rows="5" >#local.qryCase.plantiffExpertTxt#</textarea></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Defense Expert(s):</strong></td>
								<td><textArea name="defenseExpertTxt"  id="defenseExpertTxt" maxlength="1000" rows="5" >#local.qryCase.defenseExpertTxt#</textarea></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Demand:</strong></td>
								<td><input type="text" name="demandAmt"  id="demandAmt"  size="15" value="#dollarFormat(local.qryCase.demandAmt)#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Offer:</strong></td>
								<td><input type="text" name="offerAmt"  id="offerAmt"  size="15" value="#dollarFormat(local.qryCase.offerAmt)#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Resolution:</strong></td>
								<td>
									<select id="resolutionID" name="resolutionID" class="bodyText">
									<option value="">Select Resolution</option>
									<cfloop query="local.qryresolutiontypes">
										<option value="#local.qryresolutiontypes.caseResolutionID#" <cfif local.qryCase.resolutionID eq local.qryresolutiontypes.caseResolutionID>selected</cfif>>#local.qryresolutiontypes.caseResolutionName#</option>
									</cfloop>
									</select>
								</td>
							</tr>
							
							<tr valign="top">
								<td nowrap><strong>Resolution Amount:</strong></td>
								<td><input type="text" name="resolutionAmt"  id="resolutionAmt"  size="15" value="#dollarFormat(local.qryCase.resolutionAmt)#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Was this a structured settlement?:</strong></td>
								<td>
									<input type="radio" name="structureSettlementBit" checked id="structureSettlementBit"  size="70" value="0"><label class="checkLabel">No</label>
									<input type="radio" name="structureSettlementBit" 
									<cfif local.qryCase.structureSettlementBit eq 1>
										checked
									</cfif> id="structureSettlementBit"  size="70" value="1"><label class="checkLabel">Yes</label>
								</td>
							</tr>

							<tr id="structuredPara" valign="top" style="display:none;">
								<td nowrap><strong>Structure and Present Value:</strong></td>
								<td>
									<textArea name="structureDesc"  id="structureDesc">#local.qryCase.structureDesc#</textarea>
								</td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Name of Judge:</strong></td>
								<td><input type="text" name="judgeName"  id="judgeName"  size="70" value="#local.qryCase.judgeName#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Defense Attorney:</strong></td>
								
								<td><input type="text" name="defenseAttorneyName"  id="defenseAttorneyName"  size="70" value="#local.qryCase.defenseAttorneyName#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Insurance Company:</strong></td>
								<td><input type="text" name="insuranceCompanyName"  id="insuranceCompanyName"  size="70" value="#local.qryCase.insuranceCompanyName#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Insurance Adjuster:</strong></td>
								<td><input type="text" name="insuranceAdjusterName"  id="insuranceAdjusterName"  size="70" value="#local.qryCase.insuranceAdjusterName#"></td>
							</tr>

													
							<tr valign="top">
								<td nowrap><strong>Submitting Attorney:</strong></td>
								<td><input type="text" name="submitAttorneyName"  id="submitAttorneyName"  size="70" value="#local.qryCase.submitAttorneyName#"></td>
							</tr>

							<tr valign="top">
								<td nowrap><strong>Submitting Attorney Firm:</strong></td>
								<td><input type="text" name="submitFirmName"  id="submitFirmName"  size="70" value="#local.qryCase.submitFirmName#"></td>
							</tr>
						</table>
						<br/>
						<div>
							<input type="submit" value="Save Case" name="btnSave" class="bodyText" /> &nbsp;
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) AND val(local.qryCase.caseID) gt 0>
								<input type="button" name="btnDelete" value="Delete Case" class="bodyText" onclick="var msg='Are you sure you want to delete this Case?\nIf you click OK, this Case will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='#local.deleteLink#&caseID=#val(local.qryCase.caseID)#';"/> &nbsp;
							</cfif>


							<input type="button" value="Cancel" onclick="$('##cancelForm').submit();" class="bodyText" />
						</div>
					</cfform>
				</cfoutput>
			</cfif>
		</cfcase>
	
		<cfcase value="delete">
			<cfset local.caseID = int(val(event.getValue('caseID',0)))>
			<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
				<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
					delete from MN_cases
					where caseID = <cfqueryparam value="#local.caseID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
			</cfif>
			<cfoutput>
				<p class="headerText">Information Updated</p>
				<br />
				<table border="0" cellpadding="2">
					<tr>
					<cfif isdefined("session.lastCasesearch") and IsStruct(session.lastCasesearch) and structcount(session.lastCasesearch) gt 0>
						<form action="#local.resultsLink#" method="post">
						<CFLOOP INDEX="local.form_element" LIST="#session.lastCasesearch.fieldnames#">
							<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#session.lastCasesearch[local.form_element]#">
						</CFLOOP>
						<td><input type="submit" value="Return to Results" class="bodyText"/></td>
						</form>
					</cfif>
					<td><input type="button" onclick="document.location.href='#local.baseLink#';" value="New Search" class="bodyText" /></td>
					</tr>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="save">
			<cfset local.caseID = int(val(event.getValue('caseID',0)))>

			<cfif trim(len(event.getValue('courtIDNew',0)))>
				<cfquery name="insertCourt" datasource="#application.dsn.customApps.dsn#">
				insert into dbo.MN_caseCourts (
						caseCourtName
						)
				VALUES(
					<cfqueryparam value="#event.getValue('courtIDNew',0)#" cfsqltype="CF_SQL_VARCHAR">
				)
				select SCOPE_IDENTITY() as caseCourtID
				set nocount off
				</cfquery>
				<cfset local.courtID = insertCourt.caseCourtID>
			<cfelse>
				<cfset local.courtID = int(val(event.getValue('courtID',0)))>
			</cfif>

			<cfif trim(len(event.getValue('typeIDNew',0)))>
				<cfquery name="local.insertType" datasource="#application.dsn.customApps.dsn#">
				insert into dbo.MN_caseTypes (
						caseTypeName
						)
				VALUES(
					<cfqueryparam value="#event.getValue('typeIDNew',0)#" cfsqltype="CF_SQL_VARCHAR">
				)
				select SCOPE_IDENTITY() as caseTypeID
				set nocount off
				</cfquery>
				<cfset local.typeID = local.insertType.caseTypeID>
			<cfelse>
				<cfset local.typeID = int(val(event.getValue('typeID',0)))>
			</cfif>
			
			<!--- <cfdump var="#dateFormat(now(), 'yyyy-mm-d') & ' '&timeformat(now(),'HH:mm:ss')#"><cfabort> --->
			<!--- INSERT RECORD --->
			<cfif local.caseID is 0>

				<cfquery name="insertCase" datasource="#application.dsn.customApps.dsn#">
					set nocount on
					insert into dbo.MN_cases (
						
						typeID,
						statusID,
						caseName,
						courtID,
						courtCaseNum,
						dispostionDt,
						legalIssueTxt,
						caseDesc,
						plantiffExpertTxt,
						defenseExpertTxt,
						demandAmt,
						offerAmt,
						resolutionID,
						resolutionAmt,
						structureSettlementBit,
						structureDesc,
						judgeName,
						insuranceCompanyName,
						insuranceAdjusterName,
						submitAttorneyName,
						submitFirmName,
						defenseAttorneyName,
						createdDt,
						createdBy
						)
					VALUES (
						
						<cfif  val(local.typeID)>
							<cfqueryparam value="#val(local.typeID)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							NULL,
						</cfif>
						<cfif val(event.getValue('statusID',0))>
							<cfqueryparam value="#int(val(event.getValue('statusID',0)))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							NULL,
						</cfif>

						<cfqueryparam value="#trim(event.getValue('caseName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif val(local.courtID)>
							<cfqueryparam value="#local.courtID#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							NULL,
						</cfif>
						<cfqueryparam value="#trim(event.getValue('courtCaseNum'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(dateformat(event.getValue('dispostionDt'),'yyyy-mm-d'))#" cfsqltype="CF_SQL_DATE">,
						<cfqueryparam value="#trim(event.getValue('legalIssueTxt'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('caseDesc'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('plantiffExpertTxt'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('defenseExpertTxt'))#" cfsqltype="CF_SQL_VARCHAR">,
						
						<cfqueryparam value="#RereplaceNoCase(event.getValue('demandAmt',0),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#RereplaceNoCase(event.getValue('offerAmt',0),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif val(event.getValue('resolutionID',0))>
							<cfqueryparam value="#int(val(event.getValue('resolutionID',0)))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfelse>
							NULL,
						</cfif>
						<cfqueryparam value="#RereplaceNoCase(event.getValue('resolutionAmt',0),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_VARCHAR">,
						
						<cfqueryparam value="#trim(event.getValue('structureSettlementBit'))#" cfsqltype="CF_SQL_BIT">,
						<cfqueryparam value="#trim(event.getValue('structureDesc'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('judgeName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('insuranceCompanyName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('insuranceAdjusterName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('submitAttorneyName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('submitFirmName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(event.getValue('defenseAttorneyName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#dateFormat(now(), 'yyyy-mm-d') & ' '&timeformat(now(),'HH:mm:ss')#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#session.cfcUser.memberData.memberid#" cfsqltype="CF_SQL_INTEGER">
						
					)
					select SCOPE_IDENTITY() as caseID
					set nocount off
				</cfquery>
				
				<cfset local.thiscaseID = insertCase.caseID>

				<!--- Email IN about Case --->

				<cfsavecontent variable="local.invoice">
				<cfoutput>
					<cfset local.qryCase = getCase(int(val(local.thiscaseID)))>
						<table border="0" class="bodyText" width="100%" cellpadding="2" cellspacing="0">
							<cfif len(local.qryCase.dispostionDt) and isdate(local.qryCase.dispostionDt)>
								<tr valign="top">
									<td><strong>Approval Status:</strong></td>
									<td>#local.qryCase.caseStatusName#</td>
									
								</tr>
							</cfif>
							<cfif len(local.qryCase.typeID) or len(local.qryCase.typeID)>
								<tr valign="top">
									<td><strong>Case Type:</strong></td>
									<td>#local.qryCase.caseTypeName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.courtID) or len(local.qryCase.courtID)>
								<tr valign="top">
									<td><strong>Court:</strong></td>
									<td>#local.qryCase.caseCourtName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.caseName) or len(local.qryCase.caseName)>
								<tr valign="top">
									<td><strong>Name of Case:</strong></td>
									<td>#local.qryCase.caseName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.courtCaseNum)>
								<tr valign="top">
									<td><strong>Court Case Number:</strong></td>
									<td>#local.qryCase.courtCaseNum#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.dispostionDt) or len(local.qryCase.dispostionDt)>
								<tr valign="top">
									<td><strong>Date of Disposition:</strong></td>
									<td>#dateformat(local.qryCase.dispostionDt,"mm/dd/yyyy")#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.legalIssueTxt)>
								<tr valign="top">
									<td><strong>Legal Issue or Holding:</strong></td>
									<td>#local.qryCase.legalIssueTxt#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.caseDesc)>
								<tr valign="top">
									<td><strong>Description of Cases (Facts,Damges,etc.):</strong></td>
									<td>#local.qryCase.caseDesc#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.plantiffExpertTxt)>
								<tr valign="top">
									<td><strong>Plantiff's Expert(s):</strong></td>
									<td>#local.qryCase.plantiffExpertTxt#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.defenseExpertTxt)>
								<tr valign="top">
									<td><strong>Defense Expert(s):</strong></td>
									<td>#local.qryCase.defenseExpertTxt#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.demandAmt)>
								<tr valign="top">
									<td><strong>Demand Amount:</strong></td>
									<td>#dollarFormat(local.qryCase.demandAmt)#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.offerAmt)>
								<tr valign="top">
									<td><strong>Offer:</strong></td>
									<td>#dollarFormat(local.qryCase.offerAmt)#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.caseResolutionName)>
								<tr valign="top">
									<td><strong>Resolution:</strong></td>
									<td>#local.qryCase.caseResolutionName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.resolutionAmt)>
								<tr valign="top">
									<td><strong>Resolution Amount:</strong></td>
									<td>#dollarFormat(local.qryCase.resolutionAmt)#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.structureSettlementBit)>
								<tr valign="top">
									<td><strong>Was this a structured settlement?:</strong></td>
									<td>
										<cfif local.qryCase.structureSettlementBit>
											Yes
											<cfelse>
												No
										</cfif>
									</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.structureDesc)>
								<tr valign="top">
									<td><strong>Structure and Present Value:</strong></td>
									<td>#local.qryCase.structureDesc#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.judgeName)>
								<tr valign="top">
									<td><strong>Name of Judge:</strong></td>
									<td>#local.qryCase.judgeName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.defenseAttorneyName)>
								<tr valign="top">
									<td><strong>Defense Attorney:</strong></td>
									<td>#local.qryCase.defenseAttorneyName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.insuranceCompanyName)>
								<tr valign="top">
									<td><strong>Insurance Company:</strong></td>
									<td>#local.qryCase.insuranceCompanyName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.insuranceAdjusterName)>
								<tr valign="top">
									<td><strong>Insurance Adjuster:</strong></td>
									<td>#local.qryCase.insuranceAdjusterName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.submitAttorneyName)>
								<tr valign="top">
									<td><strong>Submitting Attorney:</strong></td>
									<td>#local.qryCase.submitAttorneyName#</td>
								</tr>
							</cfif>
							<cfif len(local.qryCase.submitFirmName)>
								<tr valign="top">
									<td><strong>Submitting Attorney Firm:</strong></td>
									<td>#local.qryCase.submitFirmName#</td>
								</tr>
							</cfif>
						</table>
					</cfoutput>
					</cfsavecontent>
				
				<!--- email staff (no error shown to user) --->

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p><b>Case successfully submitted by : #session.cfcUser.memberData.firstname#  #session.cfcUser.memberData.lastname#</b></p>						
						#local.invoice#   
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.strEMailSettings_staff.from},
					emailto=[{ name="", email=variables.strEMailSettings_staff.to }],
					emailreplyto=variables.strEMailSettings_staff.from,
					emailsubject=variables.strEMailSettings_staff.subject,
					emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - Case Database",
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getValue('mc_siteinfo.siteid'),
					memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>

			<cfelse>
				<!--- UPDATE RECORD --->
				<cfquery name="updateCase" datasource="#application.dsn.customApps.dsn#">
					update dbo.MN_cases
					set 
						<cfif val(event.getValue('typeID',0))>
							typeID = <cfqueryparam value="#int(val(local.typeID))#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							typeID = NULL,
						</cfif>
						<cfif val(event.getValue('statusID',0))>
							statusID = <cfqueryparam value="#int(val(event.getValue('statusID',0)))#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							statusID = NULL,
						</cfif>
						
						caseName = <cfqueryparam value="#trim(event.getValue('caseName'))#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif val(local.courtID)>
							courtID = <cfqueryparam value="#int(val(local.courtID))#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							courtID = NULL,
						</cfif>
						
						courtCaseNum = <cfqueryparam value="#trim(event.getValue('courtCaseNum'))#" cfsqltype="CF_SQL_VARCHAR">,
						dispostionDt = <cfqueryparam value="#trim(dateformat(event.getValue('dispostionDt'),'yyyy-mm-d'))#" cfsqltype="CF_SQL_DATE">,
						legalIssueTxt = <cfqueryparam value="#trim(event.getValue('legalIssueTxt'))#" cfsqltype="CF_SQL_VARCHAR">,
						caseDesc = <cfqueryparam value="#trim(event.getValue('caseDesc'))#" cfsqltype="CF_SQL_VARCHAR">,
						plantiffExpertTxt = <cfqueryparam value="#trim(event.getValue('plantiffExpertTxt'))#" cfsqltype="CF_SQL_VARCHAR">,
						defenseExpertTxt = <cfqueryparam value="#trim(event.getValue('defenseExpertTxt'))#" cfsqltype="CF_SQL_VARCHAR">,
						demandAmt = <cfqueryparam value="#RereplaceNoCase(event.getValue('demandAmt'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						offerAmt = <cfqueryparam value="#RereplaceNoCase(event.getValue('offerAmt'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						<cfif val(event.getValue('resolutionID',0))>
							resolutionID = <cfqueryparam value="#int(val(event.getValue('resolutionID',0)))#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							resolutionID = NULL,
						</cfif>

						resolutionAmt = <cfqueryparam value="#RereplaceNoCase(event.getValue('resolutionAmt'),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						structureSettlementBit = <cfqueryparam value="#trim(event.getValue('structureSettlementBit'))#" cfsqltype="CF_SQL_BIT">,
						structureDesc = <cfqueryparam value="#trim(event.getValue('structureDesc'))#" cfsqltype="CF_SQL_VARCHAR">,
						judgeName = <cfqueryparam value="#trim(event.getValue('judgeName'))#" cfsqltype="CF_SQL_VARCHAR">,
						insuranceCompanyName = <cfqueryparam value="#trim(event.getValue('insuranceCompanyName'))#" cfsqltype="CF_SQL_VARCHAR">,
						insuranceAdjusterName = <cfqueryparam value="#trim(event.getValue('insuranceAdjusterName'))#" cfsqltype="CF_SQL_VARCHAR">,
						submitAttorneyName = <cfqueryparam value="#trim(event.getValue('submitAttorneyName'))#" cfsqltype="CF_SQL_VARCHAR">,
						submitFirmName = <cfqueryparam value="#trim(event.getValue('submitFirmName'))#" cfsqltype="CF_SQL_VARCHAR">,
						defenseAttorneyName = <cfqueryparam value="#trim(event.getValue('defenseAttorneyName'))#" cfsqltype="CF_SQL_VARCHAR">,
						lastUpdatedDt = <cfqueryparam value="#dateFormat(now(), 'yyyy-mm-d') & ' '&timeformat(now(),'HH:mm:ss')#" cfsqltype="CF_SQL_VARCHAR">,
						lastUpdatedBy = <cfqueryparam value="#session.cfcUser.memberData.memberid#" cfsqltype="CF_SQL_INTEGER">

					where 
						caseID = <cfqueryparam value="#event.getValue('caseID')#" cfsqltype="cf_sql_integer">
				</cfquery>
			</cfif>

			<cfoutput>
				<p class="headerText">Your case has been submitted. 
				<cfif NOT val(event.getValue('customPage.myRights.customAddDatabase',0))>Once approved, the information you entered will be searchable in the database.</cfif>
				</p>
				<br />
				<table border="0" cellpadding="2">
					<tr>
						<cfif isdefined("session.lastCasesearch") and IsStruct(session.lastCasesearch) and structcount(session.lastCasesearch) gt 0>
							<form action="#local.resultsLink#" method="post">
							<CFLOOP INDEX="local.form_element" LIST="#session.lastCasesearch.fieldnames#">
								<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#session.lastCasesearch[local.form_element]#">
							</CFLOOP>
							<td><input type="submit" value="Return to Results" class="bodyText"/></td>
							</form>
						</cfif>
						<td>
							<input type="button" onclick="parent.location='#local.baseLink#';" value="New Search" class="bodyText" />
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Case" class="bodyText">
							</cfif>						
						</td>
					</tr>
				</table>
			</cfoutput>
		</cfcase>
	
		<cfcase value="results">
			
			<cfif NOT structKeyExists(arguments.event.getCollection(), "FIELDNAMES")  >
				<cflocation url="/?pg=caseDatabase" addtoken="no">
			</cfif>

			<cfset local.pageID = int(val(event.getValue('page',0)))>
			<cfset local.maxrows = 10>
			<cfset fieldArr = 	{}>

			<cfloop collection="#arguments.event.getCollection()#" item="local.key">
				<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
					and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
					and left(local.key,9) neq "formfield"
					and left(local.key,4) neq "fld_">
					<cfset fieldArr[local.key] = 	arguments.event.getValue(local.key)>
				</cfif>
			</cfloop>
			
			<cfset session.fieldArr = fieldArr>
			<cfquery name="local.qryMatches" datasource="#application.dsn.customApps.dsn#" result="local.qryMatchesResults">
				SELECT
					caseID,
					typeID,
					resolutionID,
					caseResolutionName,
					caseTypeName,
					courtID,
					courtCaseNum,
					statusID,
					caseStatusName,
					caseName,
					dispostionDt,
					caseCourtName,
					createdBy
				FROM
					MN_cases c					
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						LEFT OUTER JOIN MN_caseStatuses cs ON c.statusID = cs.caseStatusID
					<cfelse>
						INNER JOIN MN_caseStatuses cs ON c.statusID = cs.caseStatusID
							and cs.caseStatusName = 'Approved'						
					</cfif>							
					LEFT OUTER JOIN MN_caseCourts co ON c.courtID = co.caseCourtID
					LEFT OUTER JOIN MN_casetypes ct ON c.typeID = ct.casetypeID					
					LEFT OUTER JOIN MN_caseResolutions cr ON c.resolutionID = cr.caseResolutionID	
				WHERE
					(1=1)
					<cfif len(event.getValue('typeID',''))>
						AND c.typeID = <cfqueryparam value="#event.getValue('typeID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif len(event.getValue('caseName',''))>
						AND c.caseName  like <cfqueryparam value="%#event.getValue('caseName')#%" cfsqltype = "CF_SQL_VARCHAR">
					</cfif>
					<cfif len(event.getValue('caseDesc',''))>
						AND c.caseDesc  like <cfqueryparam value="%#event.getValue('caseDesc')#%" cfsqltype = "CF_SQL_VARCHAR">
					</cfif>
					<cfif len(event.getValue('submitAttorneyName',''))>
						AND c.submitAttorneyName  like <cfqueryparam value="%#event.getValue('submitAttorneyName')#%" cfsqltype = "CF_SQL_VARCHAR">
					</cfif>
					<cfif NOT val(event.getValue('customPage.myRights.customAddDatabase',0))>
						AND cs.caseStatusName = 'Approved'
					</cfif>
					<cfif len(event.getValue('dispostionDt',''))>
						AND CONVERT(VARCHAR(25),c.dispostionDt,126)  like <cfqueryparam value="%#dateformat(event.getValue('dispostionDt'),"yyyy-mm-dd")#%" cfsqltype="CF_SQL_VARCHAR">
					</cfif>
				ORDER BY
					caseID
			</cfquery>
			
			<cfset session.lastCaseSearch = duplicate(form)>
			<cfif local.qryMatches.recordcount>
				<cfset local.numpages = ceiling(local.qryMatches.recordcount / local.maxrows)>
				<cfset local.startrow = ((local.pageID-1) * local.maxrows) + 1>
				<cfset local.endrow = local.startrow + local.maxrows - 1>
				<cfif local.qryMatches.recordcount lt local.endrow>
					<cfset local.endrow = local.qryMatches.recordcount>
				</cfif>
			<cfelse>
				<cfset local.numpages = 0>
				<cfset local.startrow = 0>
				<cfset local.endrow = 0>
			</cfif>
			<cfoutput>
				<div class="headerText">Cases and Settlements Search Results</div>
				<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
					<div class="bodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
				</cfif>
				<script language="JavaScript">
					function prevPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')-1#';
						objForm.submit();
					}
					function nextPage() {
						var objForm = document.forms['frmHidden'];
						objForm.page.value = '#event.getValue('page')+1#';
						objForm.submit();
					}
				</script>
				<br/>
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td class="bodyText">Showing #local.startrow# to #local.endrow# of #local.qryMatches.recordcount# matches</td>
						<td align="right">
							<cfif form.page gt 1>
								<input type="button" value="&lt;&lt; Previous Page" class="bodyText" onclick="prevPage();">
							</cfif>
							<cfif local.qryMatches.recordcount gt (form.page*local.maxrows)>
								<input type="button" value="Next Page &gt;&gt;" class="bodyText" onclick="nextPage();">
							</cfif>
							<input type="button" onclick="self.location.href='#local.baseLink#';" value="New Search" class="bodyText">
							<!--- ADD BUTTON: --->
							<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
								<input type="button" onclick="self.location.href='#local.editLink#';" value="Add Case" class="bodyText">
							</cfif>
							
						</td>
					</tr>
				</table>
				<br/>
			</cfoutput>
			<cfif local.qryMatches.recordcount eq 0>
				<cfoutput><div class="bodyText">No records match your search criteria.</div></cfoutput>
			<cfelse>
				<cfoutput>
				<table border="0" class="bodyText" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
					<tr bgcolor="##999999">
						<td colspan="2"></td>
						<th align="left">Case Name</th>
						<th align="left">Disposition Date</th>
						<th align="left">Court / Case</th>
						<th align="left">Resolution</th>
					</tr>
				</cfoutput>
				<cfoutput query="local.qryMatches" startrow="#local.startrow#" maxrows="#local.maxrows#">
					<tr valign="top" <cfif local.qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and  (local.qryMatches.caseStatusName neq "Approved")>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#local.qryMatches.currentrow#.</td>
						<td <cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and  (local.qryMatches.caseStatusName neq "Approved")>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>>
							<a href="#local.viewLink#&caseID=#local.qryMatches.caseID#">View</a>
							<cfif
								val(event.getValue('customPage.myRights.customAddDatabase',0))
								AND (session.cfcUser.memberData.memberID eq local.qryMatches.createdBy OR application.objUser.isSiteAdmin(cfcuser=session.cfcuser))
							>
								<br/><a href="#local.editLink#&caseID=#local.qryMatches.caseID#">Edit</a>
							</cfif>
						</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and  (local.qryMatches.caseStatusName neq "Approved")>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">#local.qryMatches.caseName#&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and  (local.qryMatches.caseStatusName neq "Approved")>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;"><cfif isdate(local.qryMatches.dispostionDt)>#dateformat(local.qryMatches.dispostionDt,"m/d/yyyy")#</cfif>&nbsp;</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and  (local.qryMatches.caseStatusName neq "Approved")>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;</cfif>border-right:1px solid ##666;">
							#local.qryMatches.caseCourtName#
							<cfif len(local.qryMatches.typeID) and len(local.qryMatches.typeID)><br/></cfif>
							#left(local.qryMatches.caseTypeName,100)#<cfif len(local.qryMatches.caseTypeName) gt 100>...</cfif>
							&nbsp;
						</td>
						<td style="<cfif val(event.getValue('customPage.myRights.customAddDatabase',0)) and (local.qryMatches.caseStatusName neq "Approved")>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-right:2px solid ##FF0000;<cfelse>border-right:1px solid ##666;</cfif>">#local.qryMatches.caseResolutionName#&nbsp;</td>
					</tr>
				</cfoutput>
				<cfoutput></table></cfoutput>
			</cfif>
			<cfoutput>	
				<form name="frmHidden" action="#local.resultsLink#" method="post">
				<input type="hidden" name="page" value="">
				<cfloop INDEX="local.form_element" LIST="#FORM.fieldnames#">
					<cfif local.form_element neq "page">
						<INPUT TYPE="hidden" NAME="#local.form_element#" VALUE="#form[local.form_element]#">
					</cfif>
				</CFLOOP>
				</form>
			</cfoutput>
		</cfcase>
	
		<cfcase value="search">
			
			<cfset local.qryCaseTypes = getCaseTypes()>
			
			<cfoutput>
				<style>
				.cal a{float: right;}
				.cal{width: 42%;}
				</style>
				<script>
					$(document).ready(function(){
						mca_setupDatePickerField('dispostionDt');
					});					
				</script>
				<p><span class="headerText"><b>MAJ's Verdicts and Settlement Database</b></span></p>
				<p><span class="bodyText">The Case Reports Database contains reports provided by MAJ members. The database is a valuable service to our members and we welcome all reports.</span></p>
			
				<cfform action="#local.resultsLink#" method="post">
					<input type="hidden" name="page" value="1" />
					<table class="bodyText">
					
					<tr>
						<td>Disposition Date:</td>
						<td>
							<div class="cal">
							<cfinput type="text" name="dispostionDt" id="dispostionDt">
							<a href="javascript:mca_clearDateRangeField('dispostionDt');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 5px 7px;"></i></a>
							</div>
						</td>
						
					</tr>
					
					<tr>
						<td>Case Name:</td>
						<td><input type="text" name="caseName" class="bodyText" maxlength="70" size="70" /></td>
					</tr>		

					<tr>
						<td>Case Type:</td>
						<td>
							<select name="casetypes" class="bodyText">
							<option value="">All</option>
							<cfloop query="local.qryCaseTypes">
								<option value="#local.qryCaseTypes.caseTypeID#">#local.qryCaseTypes.caseTypeName#</option>
							</cfloop>
							</select>
						</td>
					</tr>

					<tr>
						<td>Case Description:</td>
						<td><textarea name="caseDesc" class="bodyText" maxlength="250" rows="5" /></textArea>
					</tr>

					<tr>
						<td>Submitting Attorney:</td>
						<td><input type="text" name="submitAttorneyName" class="bodyText" size="70" rows="5" >
					</tr>

					
					</table>
					<br />
					<input type="submit" value="Search Reports" class="bodyText"/>
					<!--- ADD BUTTON: --->
					<cfif val(event.getValue('customPage.myRights.customAddDatabase',0))>
						&nbsp;&nbsp;<input type="button" onclick="document.location.href='#local.editLink#';" value="Add Case" class="bodyText" />
					</cfif>
					
				</cfform>
			</cfoutput>
		</cfcase>
	
		<!--- search --->
		<cfdefaultcase></cfdefaultcase>
	
	</cfswitch>

<cffunction name="getStatus" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT caseStatusID,caseStatusName
		FROM MN_caseStatuses
		WHERE caseStatusName IS NOT NULL AND caseStatusName <> ''
		ORDER BY caseStatusName
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getCaseTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT caseTypeName,caseTypeID
		FROM MN_casetypes
		WHERE caseTypeName IS NOT NULL AND caseTypeName <> ''
		ORDER BY caseTypeName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getResolutionTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT caseResolutionID,caseResolutionName
		FROM MN_caseResolutions
		WHERE caseResolutionName IS NOT NULL AND caseResolutionName <> ''
		ORDER BY caseResolutionName
	</cfquery>
	<cfreturn qry>
</cffunction>

<cffunction name="getcourtIDs" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT casecourtID, caseCourtName
		FROM MN_caseCourts
		WHERE caseCourtName IS NOT NULL AND caseCourtName <> ''
		ORDER BY caseCourtName
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCase" returntype="query" output="No">
	<cfargument name="caseID" type="numeric" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select 
			caseID,
			typeID,
			statusID,
			caseName,
			courtID,
			courtCaseNum,
			dispostionDt,
			legalIssueTxt,
			caseDesc,
			plantiffExpertTxt,
			defenseExpertTxt,
			demandAmt,
			offerAmt,
			resolutionID,
			resolutionAmt,
			structureSettlementBit,
			structureDesc,
			judgeName,
			insuranceCompanyName,
			insuranceAdjusterName,
			submitAttorneyName,
			submitFirmName,
			defenseAttorneyName,
			caseTypeName,
			caseCourtName,
			caseResolutionName,
			caseStatusName,
			createdBy
		from
			MN_cases c
			left outer  join MN_caseCourts co on c.courtID = co.caseCourtID
			left outer  join MN_casetypes ct on c.typeID = ct.casetypeID
			left outer  join MN_caseStatuses cs on c.statusID = cs.caseStatusID
			left outer  join MN_caseResolutions cr on c.resolutionID = cr.caseResolutionID		
		where
			c.caseID = <cfqueryparam value="#arguments.caseID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfreturn qry>
</cffunction>