<cfinclude template="../commonJS.cfm">
<cfsavecontent variable="local.customJS">
<cfoutput>	
<link href="/assets/common/javascript/bootstrapToggle/css/bootstrap2-toggle.min.css" rel="stylesheet">
<script src="/assets/common/javascript/bootstrapToggle/js/bootstrap2-toggle.min.js"></script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#" />

<cfform name="frmEmailPreferences" id="frmEmailPreferences">
<cfoutput>
	<div>
		<p>Manage your email delivery preferences by choosing <strong>ON</strong> for each type of email you'd like to receive or <strong>OFF</strong> if you wish to no longer receive those types of emails. Certain types of emails are required and cannot be turned off.</p>
		<cfif listLen(local.memberEmailList) gt 1>
			<p>You have multiple email addresses. Use the quicklinks below to jump to the settings for each email address.</p>
			<ul>
				<cfloop list="#local.memberEmailList#" item="local.thisEmail" index="local.currentRow">
					<li><a href="##emailSettings#local.currentrow#">#local.thisEmail#</a></li>
				</cfloop>
			</ul>
		</cfif>
	</div>
</cfoutput>

<cfif listFind(local.memberEmailList, local.preferredEmail) and len(trim(local.excludedConsentIDs))>
	<cfquery name="local.qryEmailSelectedConsentListDetails" dbtype="query">
		SELECT *
		FROM [local].qryEmailConsentListDetails
		WHERE consentListID IN (<cfqueryparam value="#local.excludedConsentIDs#" cfsqltype="cf_sql_integer" list="true">)
	</cfquery>
	<cfquery name="local.qryEmailConsentListDetails" dbtype="query">
		SELECT *
		FROM [local].qryEmailConsentListDetails
		WHERE NOT consentListID IN (<cfqueryparam value="#local.excludedConsentIDs#" cfsqltype="cf_sql_integer" list="true">)
	</cfquery>
	
	<cfif local.qryEmailSelectedConsentListDetails.recordCount>
		<div class="consent-list-card consent-list-safe-card">
			<div class="consent-list-card-header consent-list-bg-whitesmoke consent-list-pb-1">
				<div class="consent-list-font-size-lg consent-list-font-weight-bold consent-list-mb-1">To unsubscribe from emails like this, toggle OFF below</div>
			</div>
			<div class="consent-list-card-body">
				<div class="consent-list-d-flex consent-list-col">
					<div>				
						<h4 class="consent-list-mb-0 consent-list-mt-1 consent-list-text-bold consent-list-pb-0 email-address-header"><i class="icon-envelope"></i><cfoutput>#local.preferredEmail#</cfoutput></h4>
					</div>
				</div>
				<cfset local.thisEmail = local.preferredEmail>

				<cfset local.currentRow = 0>
				<div class="consent-list-mb-2 consent-list-mt-2 consent-list-ml-5">
					<cfoutput query="local.qryEmailSelectedConsentListDetails" group="consentListTypeID">
						<h4 class="consent-list-type-header">#local.qryEmailSelectedConsentListDetails.consentListTypeName#</h4>
						<div class="consent-list-type-wrapper">
							<cfoutput>	
								<cfset local.thisFieldName = "mcemailpref_#local.qryEmailSelectedConsentListDetails.consentListID#_#local.currentRow#">
								<cfset local.isChecked = false>
								<cfset local.isDisabled = false>

								<cfif ListFindNoCase("Opt-In", local.qryEmailSelectedConsentListDetails.modeName) and structKeyExists(local.strConsentListMembers,local.thisEmail) and ListFind(local.strConsentListMembers[local.thisEmail],local.qryEmailSelectedConsentListDetails.consentListID)>
									<cfset local.isChecked = true>
								<cfelseif local.qryEmailSelectedConsentListDetails.modeName eq 'Opt-Out' and not (structKeyExists(local.strConsentListMembers,local.thisEmail) and ListFind(local.strConsentListMembers[local.thisEmail],local.qryEmailSelectedConsentListDetails.consentListID))>
									<cfset local.isChecked = true>
								<cfelseif local.qryEmailSelectedConsentListDetails.modeName eq 'Required'>
									<cfset local.thisFieldName = "disabled#local.thisFieldName#">
									<cfset local.isChecked = true>
									<cfset local.isDisabled = true>
								</cfif>
								<div class="consent-list">
									<div class="consent-list-header">
										<div class="consent-list-toggle">
											<div class="checkbox">
												<label>
													<input type="checkbox" name="#local.thisFieldName#" id="#local.thisFieldName#" class="emailPrefOpt" value="#local.thisEmail#" <cfif local.isDisabled>disabled</cfif> <cfif local.isChecked>checked</cfif> data-toggle="toggle" data-size="mini" data-on="ON" data-off="OFF" data-onstyle="success" data-consentlistid="#local.qryEmailSelectedConsentListDetails.consentListID#" data-listMode="#local.qryEmailSelectedConsentListDetails.modeName#" data-autochange="0">
												</label>
											</div>
										</div>
										<div class="consent-list-name">
											<strong>#local.qryEmailSelectedConsentListDetails.consentListName#</strong>
										</div>
										<div style="min-width:60px;"><span class="consent-list-saved label label-inverse pull-right">Saved</span></div>
									</div>
									<cfif len(local.qryEmailSelectedConsentListDetails.consentListDesc)>
										<div class="consent-list-description">
											#local.qryEmailSelectedConsentListDetails.consentListDesc#
										</div>
									</cfif>
								</div>
							</cfoutput>
						</div>
					</cfoutput>
				</div>
			</div>
		</div>
	</cfif>
</cfif>

<cfloop list="#local.memberEmailList#" item="local.thisEmail" index="local.currentRow">
	<cfoutput><h3 id="emailSettings#local.currentRow#" class="email-address-header consent-list-pt-3"><i class="icon-envelope"></i><strong>#local.thisEmail#</strong></h3></cfoutput>
	<cfoutput query="local.qryEmailConsentListDetails" group="consentListTypeID">
		
		<cfif local.qryEmailConsentListDetails.modeName eq "GlobalOptOut">
			<!--- Global Opt-Out --->
			<cfset local.thisFieldName = "globaloptout_mcemailpref_#local.qryEmailConsentListDetails.consentListID#_#local.currentRow#">
			<cfif structKeyExists(local.strConsentListMembers,local.thisEmail) and ListFind(local.strConsentListMembers[local.thisEmail],local.qryEmailConsentListDetails.consentListID)>
				<cfset local.isChecked = true>
			<cfelse>
				<cfset local.isChecked = false>
			</cfif>
			<div class="consent-list-header">
				<div class="consent-list-toggle" style="margin-left:10px;">
					<div class="checkbox">
						<label>
							<input type="checkbox" name="#local.thisFieldName#" id="#local.thisFieldName#" class="emailPrefOpt" value="#local.thisEmail#" <cfif local.isChecked>checked</cfif> data-toggle="toggle" data-size="mini" data-width="75" data-height="25" data-on="YES" data-off="NO" data-offstyle="default" data-onstyle="success" data-consentlistid="#local.qryEmailConsentListDetails.consentListID#" data-listMode="#local.qryEmailConsentListDetails.modeName#" data-autochange="0">
						</label>
					</div>
				</div>
				<div class="consent-list-name">
					<span class="consent-list-messageWrapper">
						<cfif local.isChecked>
							You have opted out of all email You can still edit preferences for specific types of email you would like to receive below.
						<cfelse>
							Opt-out of All Optional Email?
						</cfif>
					</span>
					<span class="consent-list-saved label label-inverse">Saved</span>
				</div>
			</div>
		<cfelse>
			<h3 class="consent-list-type-header">#local.qryEmailConsentListDetails.consentListTypeName#</h3>
			<div class="consent-list-type-wrapper">
				<cfoutput>
					<cfset local.thisFieldName = "mcemailpref_#local.qryEmailConsentListDetails.consentListID#_#local.currentRow#">
					<cfset local.isChecked = false>
					<cfset local.isDisabled = false>

					<cfif ListFindNoCase("Opt-In", local.qryEmailConsentListDetails.modeName) and structKeyExists(local.strConsentListMembers,local.thisEmail) and ListFind(local.strConsentListMembers[local.thisEmail],local.qryEmailConsentListDetails.consentListID)>
						<cfset local.isChecked = true>
					<cfelseif local.qryEmailConsentListDetails.modeName eq 'Opt-Out' and not (structKeyExists(local.strConsentListMembers,local.thisEmail) and ListFind(local.strConsentListMembers[local.thisEmail],local.qryEmailConsentListDetails.consentListID))>
						<cfset local.isChecked = true>
					<cfelseif local.qryEmailConsentListDetails.modeName eq 'Required'>
						<cfset local.thisFieldName = "disabled#local.thisFieldName#">
						<cfset local.isChecked = true>
						<cfset local.isDisabled = true>
					</cfif>
					<div class="consent-list">
						<div class="consent-list-header">
							<div class="consent-list-toggle">
								<div class="checkbox">
									<label>
										<input type="checkbox" name="#local.thisFieldName#" id="#local.thisFieldName#" class="emailPrefOpt" value="#local.thisEmail#" <cfif local.isDisabled>disabled</cfif> <cfif local.isChecked>checked</cfif> data-toggle="toggle" data-size="mini" data-on="ON" data-off="OFF" data-onstyle="success" data-consentlistid="#local.qryEmailConsentListDetails.consentListID#" data-listMode="#local.qryEmailConsentListDetails.modeName#" data-autochange="0">
									</label>
								</div>
							</div>
							<div class="consent-list-name">
								<strong>#local.qryEmailConsentListDetails.consentListName#</strong>
							</div>
							<div style="min-width:60px;"><span class="consent-list-saved label label-inverse pull-right">Saved</span></div>
						</div>
						<cfif len(local.qryEmailConsentListDetails.consentListDesc)>
							<div class="consent-list-description">
								#local.qryEmailConsentListDetails.consentListDesc#
							</div>
						</cfif>
					</div>
				</cfoutput>
			</div>
		</cfif>
	</cfoutput>
</cfloop>
</cfform>