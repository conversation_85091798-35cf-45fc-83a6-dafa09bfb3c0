<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();		
		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
		local.crlf = chr(13) & chr(10);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];

		local.tmpField = { name="AccountLocatorTitle",type="STRING",desc="Account Locator Title",value="Account Lookup / Create New Account" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton",type="STRING",desc="Account Locator Button Text",value="Account Lookup" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions",type="CONTENTOBJ",desc="Account Locator Instruction Text",value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="SubTypeTest",type="STRING",desc="Check for existing accepted/active/billed subscriptions of this type",value="1c98f1bb-ea76-405f-8402-68478eb4017c" };
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ActiveAcceptedMessage",type="CONTENTOBJ",desc="Message displayed when account selected has active/accepted subscription",value="Our records indicate you are already a NJAJ member. If you have questions about your membership, please call 1-609-396-0096." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="BilledMessage",type="CONTENTOBJ",desc="Message displayed when account selected has billed subscription",value="Our records indicate that you are eligible to renew an existing members. To access your renewal, please [[click here]]." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed. If you have questions about your membership, please call 1-609-396-0096." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="FormTitle",type="STRING",desc="Form Title",value="NJAJ Membership Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step1TopContent",type="CONTENTOBJ",desc="Content at top of page 1",value="Add step 1 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step2TopContent",type="CONTENTOBJ",desc="Content at top of page 2",value="Add step 2 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="Step3TopContent",type="CONTENTOBJ",desc="Content at top of page 3",value="Add step 3 content" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MainSubscription",type="STRING",desc="UID for subscription tree",value="eeda7a27-bbfe-4af7-ba77-136de978b2d7" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PreCheckedAddOns",type="STRING",desc="UIDs for Add-Ons that qualified users will have to opt out of",value="fb49fc20-549a-44c6-8083-c2fb4c683c91" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCC",type="STRING",desc="pay profile code for CC",value="NJCCCIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ProfileCodePayCheck",type="STRING",desc="pay profile code for check",value="CheckToFollow" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationContent",type="CONTENTOBJ",desc="Content at top of user confirmation page and email",value="Thank you for your application to join the New Jersey Association for Justice." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationSub",type="STRING",desc="Subject line of emailed user confirmation",value="NJAJ Membership Application Confirmation" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationSub",type="STRING",desc="Subject line of emailed staff confirmation",value="New Member Application" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="StaffConfirmationTo",type="STRING",desc="who do we send staff confirmations to",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MemberConfirmationFrom",type="STRING",desc="who do we send member confirmations from",value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
			
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Join NJAMS',
			orgEmailTo=variables.strPageFields.StaffConfirmationTo,
			memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
		));

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));	
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='Join', subName='Completed');
		variables.historyStartedText = "Member started join form.";
		variables.historyCompletedText = "Member completed join form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID);
				if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and local.identifiedMemberID eq 0) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				if (NOT variables.objCffp.testSubmission(form)) {
					local.returnHTML = showError(errorCode='spam');
					break;		
				}

				local.processPaymentResponse = processPayment(rc=arguments.event.getCollection(),event=arguments.event);
				if (structKeyExists(local.processPaymentResponse, "strACCResponse")) {
					arguments.event.setValue( name="processPaymentResponse", value=local.processPaymentResponse.strACCResponse);
				}
				switch (local.processPaymentResponse.response) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
		
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.objCffp = variables.objCffp>	
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Category')>	
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>

		<cfsavecontent variable="local.headCode">
			<cfoutput>
				<style type="text/css">
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px !important; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					.subRateLabel {font-weight:normal;}
					.subRatesDisabled {
						opacity: 0.6; /* Real browsers */
						filter: alpha(opacity = 60); /* MSIE */
					}
				</style>
				#variables.pageJS#
				<script type="text/javascript">
					var step = 0;
					var prevStep = 0;
	
					var memberTypeField;
	
					function assignMemberData(memObj) {
						$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
						mc_continueForm($('###variables.formName#'));
					}
	
					function validateMembershipInfoForm(){
						var arrReq = new Array();
	
						//make sure selected subscriptions all have selected rates.
						$('input.subCheckbox:checkbox:checked').each(function(x,item){
							var numRates = $('input.subRateCheckbox:radio', $(item).parent().parent()).length;
							var numRatesChecked = $('input.subRateCheckbox:radio:checked', $(item).parent().parent()).length;
							if ( numRates > 0 && numRatesChecked==0) {
								arrReq[arrReq.length] = "Choose a rate for " +  $("label[for='"+$(item).attr("id")+"']").text().trim();
							}
						});
	
						//make sure any chosen editable rates have amounts greater than zero.
	
						$('input.subRateCheckbox:radio:checked').each(function(index,item){
							var rateOverrideField = $('.subRateOverrideBox',$("label[for='"+$(item).attr("id")+"']"));
							if ($(rateOverrideField).length) {
								var overridePrice = parseFloat($(rateOverrideField)[0].value);
								if (!overridePrice) {
									var subscriptionName = $('legend',$(item).parent().parent().parent().parent().parent()).text().trim();
									subscriptionName = subscriptionName.replace(/<input.+?\/?>/g,'');
									arrReq[arrReq.length] = "Enter an amount for " + subscriptionName;
								}
							}
						});
	
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
	
						mc_continueForm($('###variables.formName#'));
						return false;
					}
					function validatePaymentForm() {
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
	
						mc_continueForm($('###variables.formName#'));
						return false;
					}
	
					window.onhashchange = function() {       
						if (location.hash.length > 0) {        
							step = parseInt(location.hash.replace('##',''),10);     
							if (prevStep > step){
								if(step==1)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
								if(step==2)
									$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
								mc_loadDataForForm($('###variables.formName#'),'previous');			        	
							}
						} else {
							step = 1;
						}
						prevStep = step;				    
					}				
					
					$(document).ready(function() {
					<cfif variables.useMID and NOT local.isSuperUser>					
						var mo = { memberID:#variables.useMID# };
						assignMemberData(mo);					
					<cfelseif local.isSuperUser>
						$('div##div#variables.formName#wrapper').hide();
						$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
					</cfif>
					});
				</script>
	
				<script type="text/javascript">
	
					function subscriptionRateOverrideBoxHandler(event) {
	
						var subRateRadioButton = $('.subRateCheckbox',$(this).parent().parent());
	
						//check subscription if not already checked
						if (subRateRadioButton[0] && !$(subRateRadioButton)[0].checked) {
							$(subRateRadioButton)[0].click();
							$(this).focus();
						} else if (subRateRadioButton) {
							$(subRateRadioButton).each(subscriptionRateRadioButtonHandler);
						}
					}
	
					function subscriptionCheckboxHandler() {
						if ($(this)[0].checked) {
							$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesDisabled')
							$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesEnabled')
						} else {
							$('.subAvailableRates',$(this).parent().parent()).removeClass('subRatesEnabled')
							$('.subAvailableRates',$(this).parent().parent()).addClass('subRatesDisabled')
						}
					}
	
					function subscriptionRateRadioButtonHandler() {
	
						if ($(this)[0].checked) {
							var subCheckbox = $('.subCheckbox',$(this).parent().parent().parent());
							var rateDescription = $($($("label[for='"+$(this).attr("id")+"']")[0]).children()[1]).html();
							var rateOverrideBox = $('input.subRateOverrideBox',$($($("label[for='"+$(this).attr("id")+"']")[0]).children())).first();
	
							if (rateOverrideBox.length) {
								//rateoverride box is present
								rateDescription = rateDescription.replace(/<input.+?\/?>/g,$(rateOverrideBox)[0].value);
							}
	
							//put label of selected rate radio button next to subscription
							rateDescription = ' - ' + rateDescription;
							$('.selectedRate',$(this).parent().parent().parent().first()).html(rateDescription);
	
							//check subscription if not already checked
							if (!$(subCheckbox)[0].checked)
								$(subCheckbox)[0].click();
						}
					}
					
					function initializeAddons() {
						$('.subAddonWrapper').each(selectAllSubscriptionsIfRequired);
					}
	
					function selectAllSubscriptionsIfRequired() {
						var addonData = $(this).data();
						// select all addons if minimum required by set is gte available count
						// hide checkboxes so they can not be unselected
						if (addonData.minallowed >= $('.subCheckbox',this).length) {
							$('.subCheckbox:not(:checked)',this).click().hide();
							$('.subCheckbox',this).hide();
						}
					}
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
				<cfinput type="hidden" name="fa" id="fa" value="processLookup">
				<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
				<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
				<cfinclude template="/model/cfformprotect/cffp.cfm">
				
				<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
				<div class="tsAppSectionContentContainer">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr>
						<td width="175" style="text-align:center;">
							<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
						</td>
						<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
					</tr>
					</table>
				</div>
	
			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.MembershipDuesTypeUID = "1c98f1bb-ea76-405f-8402-68478eb4017c">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.MembershipDuesTypeUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.MembershipDuesTypeUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.MembershipDuesTypeUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
		<cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif structkeyexists(session,"useHistoryID") and session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>			
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>	

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	
		<cfset local.fieldSetUIDlist = '5243125e-3c79-4dc5-8895-4bdcd3143eb6'>
		<cfset local.alliedMemberField = {fieldCode="",fieldLabel=""}>

		<cfloop list="#local.fieldSetUIDlist#" item="local.fieldSetUid">
            <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>
            <cfset local.memberTypeData = XMLSearch(local.memberFormXMLFields,"/fields/mf[@dbField='Allied NJAJ Member']")/>
			<cfif arrayLen(local.memberTypeData)>				
                <cfset local.alliedMemberField.fieldCode = local.memberTypeData[1].XmlAttributes.fieldCode>
                <cfset local.alliedMemberField.fieldLabel = local.memberTypeData[1].XmlAttributes.fieldLabel>
            </cfif>             
        </cfloop>
		
		<cfset local.strFieldSetMembershipCat = application.objCustomPageUtils.renderFieldSet(uid='5243125e-3c79-4dc5-8895-4bdcd3143eb6', mode="collection", strData=local.strData)>
		<cfset local.strFieldSetContactInfo = application.objCustomPageUtils.renderFieldSet(uid='1b7cad6a-76ed-4c1f-b8cc-22042b7fa607', mode="collection", strData=local.strData)>
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=variables.useMID, orgID=variables.orgID)/>

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>

		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		<cfset local.licenseStatus = {}>
		<cfset local.index = 1>
		<cfloop query="local.qryOrgProLicenseStatuses">
			<cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
			<cfset local.index = local.index + 1>
		</cfloop> 
		<cfset local.memberTypeFieldInfo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Category')>
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>

		<cfset local.alliedMemObj = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, UID='BA041F75-5B88-483C-8486-0622D6ED71C2')>
		<cfset local.strAlliedMemberColumn = local.alliedMemberField.fieldCode>
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				var memberTypeField;
				#toScript(local.strAlliedMemberColumn,"strAlliedMemberColumn")#
				function adjustFieldsetDisplay() {					
					var memType = $("option:selected",memberTypeField).text();
					$('div##professionalInfo-wrapper').hide();
					$('##'+strAlliedMemberColumn).val('');
					$('##'+strAlliedMemberColumn).closest('tr').hide();
					switch(memType) {
						case 'Student':
							$('div##professionalInfo-wrapper').hide();
							resetProfessionalLicenses();
							break;
						case 'Paralegal or Other Support Staff': 
							$('div##professionalInfo-wrapper').hide();
							resetProfessionalLicenses();
							$('##'+strAlliedMemberColumn).closest('tr').show();
							break;
						default:
							if(memType != ''){
								$('div##professionalInfo-wrapper').show();
							}							
							break;
					}
				}
				function toggleFTM() {
				}				
				function resetProfessionalLicenses(){
					$('.mpl_pltypeid').multiselect("uncheckAll"); 
					$('.mpl_pltypeid').multiselect('refresh');
					$(".mpl_pltypeid option").each(function(){
						$('##tr_state_'+$(this).attr("value")).remove();
					});
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}

				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();
					var memType = $(memberTypeField).val();
					var memTypeText = $(memberTypeField.selector+' option:selected').text();
					$('##memberTypeSelected').val($(memberTypeField.selector+' option:selected').text());
					if ($("##membership-wrapper").is(':visible')) {
						#local.strFieldSetMembershipCat.jsValidation#
					}
					if ($("##contactInfo-wrapper").is(':visible')) {
						#local.strFieldSetContactInfo.jsValidation#
					}

					if(memTypeText == 'Paralegal or Other Support Staff'){
						if($('##'+strAlliedMemberColumn).val().trim() == ''){
							arrReq[arrReq.length] = "Which regular member of NJAJ are you allied with? is required";
						}
					}

					if (memTypeText != '' && memTypeText != 'Paralegal or Other Support Staff' && memTypeText != 'Student') {
						var isProfLicenseRequired = true;

						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();    
									if($("##mpl_"+val+"_licenseNumber").val().length == 0){ arrReq[arrReq.length] = 'Enter your '+text +' #variables.strProfLicenseLabels.profLicenseNumberLabel#.'; }                                
									if($("##mpl_"+val+"_activeDate").val().length == 0){arrReq[arrReq.length] = 'Enter your  '+text +' #variables.strProfLicenseLabels.profLicenseDateLabel#.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required.";
						}
					}
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}
				function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
					$("##state_table").show();
					if(status == ''){
						status = 'Active';
					}
					strOption = '';
					<cfloop collection="#local.licenseStatus#" item="local.i" >
						strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
					</cfloop>
					if(isChecked){
						$('##selectedLicense').append('<div class="row-fluid" id="tr_state_'+val+'">'+
								'<div class="span3"><span class="tsAppBodyText">'+licenseName+'</span></div>'+
								'<div class="span3"><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></div>'+ 
								'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
								'<div class="span3"><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" readonly=""></div>'+
								'<div class="span3"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status"  class="tsAppBodyText">'+strOption+'</select></div>'+
								'</div>');
						$('##mpl_'+val+'_status').val(status);
						mca_setupDatePickerField('mpl_'+val+'_activeDate');
						$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
					}									
					else{
						$("##tr_state_"+val).remove();								
					}
					if($('##selectedLicense .row-fluid').length == 0){
						$("##state_table").hide();
					}	
				}
				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
						<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
							<cfset  local.license_name  = local.strData['mpl_#local.thisItem#_licenseName']>
							<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licenseNumber']>
							<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
							<cfset  local.license_status  = local.strData['mpl_#local.thisItem#_status']>
							licenseChange(true,'#local.thisItem#','#local.license_name#','#local.license_no#','#local.license_date#','#local.license_status#');	
						</cfloop>
					</cfif>	
					$(".mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' Please Select ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							licenseChange(ui.checked,ui.value,ui.text,'','','');                            										
						},
						uncheckAll: function(){
							$(".mpl_pltypeid option").each(function(){
								$('##tr_state_'+$(this).attr("value")).remove();
							});
							if($('##selectedLicense .row-fluid').length == 0){
								$("##state_table").hide();
							}	
						},
						checkAll: function( e ){
							$(".mpl_pltypeid option").each(function(){
								licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
							});
						}
					});
					memberTypeField = $('##md_#local.memberTypeFieldInfo.columnID#');
					$(memberTypeField).change(adjustFieldsetDisplay);
					adjustFieldsetDisplay();
					
				});				
			</script>
			
			<style type="text/css">
				button[type="button"].ui-multiselect {
					height: 30px;
					margin-bottom: 10px;
				}
				select {
					margin-bottom: 10px !important;
				}
			</style>
			
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.Step1TopContent)>
				<div id="Step1TopContent">#variables.strPageFields.Step1TopContent#</div>
			</cfif>

			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="hidden" name="memberTypeSelected" id="memberTypeSelected" value="">
			<cfif NOT local.isLoggedIn AND local.identifiedMemberID gt 0>
				<input type="hidden" name="isMemberKey" id="isMemberKey" value="1">
			</cfif>
			<cfinclude template="/model/cfformprotect/cffp.cfm">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div id="membership-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetMembershipCat.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetMembershipCat.fieldSetContent#
				</div>
			</div>
			<div id="contactInfo-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">#local.strFieldSetContactInfo.fieldSetTitle#</div>
				<div class="tsAppSectionContentContainer">
					#local.strFieldSetContactInfo.fieldSetContent#
				</div>
			</div>	
			<div id="professionalInfo-wrapper" class="fieldsetFormWrapper">
				<div class="tsAppSectionHeading">Professional License Information</div>
				<div class="tsAppSectionContentContainer">
					<table cellpadding="3" border="0" cellspacing="0" >									
						<tr align="top">
							<td class="tsAppBodyText" width="10">&nbsp;</td>
							<td class="tsAppBodyText" nowrap>Professional License</td>
							<td class="tsAppBodyText">
								<select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
									<cfloop query="local.qryOrgPlTypes">	
										<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif> <cfif local.qryOrgPlTypes.PLName EQ "Florida">required="true"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
									</cfloop>
								</select>
							</td>
						</tr>
						<tr class="top">
							<td class="tsAppBodyText" width="10"></td>
							<td class="tsAppBodyText"></td>
							<td class="tsAppBodyText"></td>
						</tr>
					</table>
					<table cellpadding="3" border="0" cellspacing="0" style="width:100%;margin-top: 20px;margin-left: 15px;">
						<tr>
							<td>
								<div class="row-fluid hide" id="state_table">
									<div class="span3 proLicenseLabel">
										<b>Type</b>
									</div>
									<div class="span3 proLicenseLabel">
										<b>#variables.strProfLicenseLabels.profLicenseNumberLabel#</b>
									</div>
									<div class="span3 proLicenseLabel">
										<b>#variables.strProfLicenseLabels.profLicenseDateLabel#</b>
									</div>
									<div class="span3 proLicenseLabel">
										<b>#variables.strProfLicenseLabels.profLicenseStatusLabel#</b>
									</div>
								</div>
								<span id="selectedLicense">
								</span>
							</td>
						</tr>					
					</table>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>

		<!--- save member info and record history --->		
		
		<cfset local.loggedMemberID = 0>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif val(local.isLoggedIn)> 
			<cfset local.loggedMemberID = session.cfcuser.memberdata.memberID>
		</cfif>
		<cfif structKeyExists(arguments.rc, "isMemberKey")> 
			<cfset local.loggedMemberID = arguments.rc.origMemberID>
		</cfif>
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=local.loggedMemberID)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subxml">
			set nocount on;

			declare @subID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.subscriptionID#">;
				
			select dbo.fn_sub_getSubscriptionStructureXML(@subID,1) as subxml;
		</cfquery>
		 
		<cfset local.objCffp = variables.objCffp>
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		<cfelse>
			<cfloop list="#variables.strPageFields.PreCheckedAddOns#" index="local.thisDefaultAddon">
				<cfset local.thisAddonSubscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
						siteID=variables.siteID,
						uid = local.thisDefaultAddon)>
				<cfif local.thisAddonSubscriptionID>
					<cfset local.strData["sub#local.thisAddonSubscriptionID#"] = "sub#local.thisAddonSubscriptionID#" >
				</cfif>
			</cfloop>
		</cfif>			
		<cfscript>
			local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				);
		</cfscript>

		<cfsavecontent variable="local.headtext">
			<cfoutput>
				<script type="text/javascript">
					$(function() {
						$('input.subCheckbox:checkbox').on('change',subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').on('change',subscriptionRateRadioButtonHandler);
						$('input.subRateOverrideBox').on('change',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('focus',subscriptionRateOverrideBoxHandler);
						$('input.subRateOverrideBox').on('blur',subscriptionRateOverrideBoxHandler);

						initializeAddons();

						$('input.subCheckbox:checkbox').each(subscriptionCheckboxHandler);
						$('input.subRateCheckbox:radio').each(subscriptionRateRadioButtonHandler);

					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headtext#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>

			<cfif len(variables.strPageFields.Step2TopContent)>
				<div id="Step2TopContent">#variables.strPageFields.Step2TopContent#</div><br/>
			</cfif>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="memberTypeSelected" id="memberTypeSelected" value="#arguments.rc.memberTypeSelected#">

			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<cfoutput>#local.result.formcontent#</cfoutput>
					</div>
				</div>
			</div>
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>
		<cfscript>
			local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);
		</cfscript>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.objCffp = variables.objCffp>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfscript>
			local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc);
		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC, variables.strPageFields.ProfileCodePayCheck ]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)
			>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			
			<cfif len(variables.strPageFields.Step3TopContent)>
				<div id="Step3TopContent">#variables.strPageFields.Step3TopContent#</div><br/>
			</cfif>
			
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="memberTypeSelected" id="memberTypeSelected" value="#arguments.rc.memberTypeSelected#">
			<cfinput type="hidden" name="historyID" id="historyID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub"
					>
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>
			<cfinclude template="/model/cfformprotect/cffp.cfm">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
			</div>
			<br/>

			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processPayment" access="private" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfscript>

			var local = structNew();
			local.returnstruct = structNew();
			application.objCustomPageUtils.mh_updateHistory(
				memberID=variables.useMID, 
				historyID=variables.useHistoryID, 
				subCategoryID=variables.qryHistoryCompleted.subCategoryID, 
				description=variables.historyCompletedText, 
				newAccountsOnly=false
			);

			//create subscriptions
			local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription);

			local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = rc);

			local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg");

		</cfscript>
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 

		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
		
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>

		<cfif local.totalAmountDueNow gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and arguments.rc.mccf_payMeth eq variables.strPageFields.ProfileCodePayCC>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>
		<cfset local.returnstruct.response = "success">
		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.memberTypeSelected = arguments.rc.memberTypeSelected>

		<cfset local.strFieldSetMembershipCat = application.objCustomPageUtils.renderFieldSet(uid='5243125e-3c79-4dc5-8895-4bdcd3143eb6', mode="confirmation", strData=arguments.rc)>
		<cfset local.strFieldSetContactInfo = application.objCustomPageUtils.renderFieldSet(uid='1b7cad6a-76ed-4c1f-b8cc-22042b7fa607', mode="confirmation", strData=arguments.rc)>
		<cfset variables.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=variables.orgID),1)>
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.MainSubscription)>

		<cfscript>
			local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = rc);

		</cfscript>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationContent)>
				<div>#variables.strPageFields.ConfirmationContent#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<p>Here are the details of your application:</p>

			#local.strFieldSetMembershipCat.fieldSetContent#
			#local.strFieldSetContactInfo.fieldSetContent#
			<cfif local.memberTypeSelected NEQ '' AND local.memberTypeSelected NEQ 'Paralegal or Other Support Staff' AND local.memberTypeSelected NEQ 'Student'>

				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Professional License Information</td>
					</tr>				
					<tr>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
							<table cellpadding="3" border="0" cellspacing="0">						
								<tr valign="top">
									<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
										<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
											<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
												<thead>
													<tr valign="top">
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Type</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
														<th align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
													</tr>
												</thead>
												<tbody>
												<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
													<tr id="tr_state_#local.key#">
														<td align="right" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licenseName']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_licenseNumber']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#arguments.rc['mpl_#local.key#_activeDate']#</td>
														<td align="center" style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Active</td>
													</tr>
												</cfloop>
												</tbody>
											</table>
										</cfif>
									</td>
								</tr>						
							</table>
						</td>
					</tr>
				</table>
				<br>
			</cfif>
			
			<br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					#local.strResult.formContent#
					<br/>
				</div>
				<br/>
				<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
				<br/>
				</td>
			</tr>
			</table>

			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		<cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.memberEmail.from },
					emailto=[{ name="", email=variables.memberEmail.to }],
					emailreplyto=variables.ORGEmail.to,
					emailsubject=variables.memberEmail.SUBJECT,
					emailtitle=arguments.rc.mc_siteinfo.sitename & " - " & variables.formNameDisplay,
					emailhtmlcontent=local.confirmationHTML,
					siteID=variables.siteID,
					memberID=val(variables.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			<cfif local.paymentRequired and structKeyExists(arguments.rc,"processPaymentResponse") and structKeyExists(arguments.rc.processPaymentResponse,"accResponseMessage")>
				<div style="padding-bottom:4px;">
					<b><u>Payment Processing results</u></b><br/>
					#arguments.rc.processPaymentResponse.accResponseMessage#
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset variables.ORGEmail.Subject = variables.strPageFields.StaffConfirmationSub >
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to NJ", emailContent=local.confirmationHTMLToStaff)>
		<cfset local.emailSentToStaff = local.responseStruct.success>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.ActiveAcceptedMessage#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ActiveAcceptedMessage#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#			
				<cfelseif arguments.errorCode eq "billedfound">
					<cfset local.qrySubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID,memberID=variables.useMID,status='O',distinct=false)>
					<cfset local.redirectLink = '/renewsub/' & local.qrySubs.directlinkcode>
					#replaceNoCase(variables.strPageFields.BilledMessage,"[[click here]]","<a href='#local.redirectLink#'>click here</a>")#		
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>