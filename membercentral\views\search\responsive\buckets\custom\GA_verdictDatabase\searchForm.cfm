<cfsavecontent variable="local.js">
	<script type="text/javascript">
		function b_search() {
			$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
			return true;
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.js,'\s{2,}',' ','ALL')#">

<cfoutput>
#showHeader(settings=local.qryBucketInfo, viewDirectory=arguments.viewDirectory)#

<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal">
	<div class="control-group">
		<label class="control-label" for="s_fname">Resolution Type:</label>
		<div class="controls">
			<select name="s_fname" id="s_fname">
				<option value="">All</option>
				<cfloop query="local.qryResolutionTypes">
					<option value="#local.qryResolutionTypes.resolutionType#" <cfif local.qryResolutionTypes.resolutionType eq local.strSearchForm.s_fname>selected="selected"</cfif>>#local.qryResolutionTypes.resolutionType#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_lname">Case Category:</label>
		<div class="controls">
			<select name="s_lname" id="s_lname">
				<option value="">All</option>
				<cfloop query="local.qryCaseTypes">
					<option value="#local.qryCaseTypes.casetype#" <cfif local.qryCaseTypes.casetype eq local.strSearchForm.s_lname>selected="selected"</cfif>>#local.qryCaseTypes.casetype#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_type">Verdict Year:</label>
		<div class="controls">
			<select name="s_type" id="s_type">
				<option value="">All</option>
				<cfloop query="local.qryYears">
					<option value="#local.qryYears.year#" <cfif local.qryYears.year eq local.strSearchForm.s_type>selected="selected"</cfif>>#local.qryYears.year#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_jurisdiction">County:</label>
		<div class="controls">
			<select name="s_jurisdiction" id="s_jurisdiction">
				<option value="">All</option>
				<cfloop query="local.qryCountyNames">
					<option value="#local.qryCountyNames.countyname#" <cfif local.qryCountyNames.countyname eq local.strSearchForm.s_jurisdiction>selected="selected"</cfif>>#local.qryCountyNames.countyName#</option>
				</cfloop>
			</select>
		</div>
	</div>	
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">With <b>any</b> of these words:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>