<cfscript>
	variables.applicationReservedURLParams 	= "TestMode";
	local.customPage.baseURL				= "/?#getBaseQueryString(false)#";
	
	local.arrCustomFields = [];
	
	local.tmpField = { name="ConfirmationEmailTo", type="STRING", desc="who do we send confirmations to", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName = 'frmVerdictDatabase',
		formNameDisplay = 'Verdict Database',
		orgEmailTo = local.strPageFields.ConfirmationEmailTo,
		memberEmailFrom  = ""
	));	
	local.orgEmail.subject = "NHAJ Verdict and Settlement Database Updated";
</cfscript>

<cfif event.getValue('action', '') eq "view" >
	<cfset qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>
	<cfif qryVerdict.recordcount is 0>
		<cflocation url="/?pg=verdictDatabase" addtoken="No">
	</cfif>
	
	<cfsavecontent variable="local.caseDetails">
		<cfoutput>
			<table border="0" class="bodytext" cellpadding="2" cellspacing="0">
			<cfif len(qryVerdict.casename)>
				<tr valign="top"><td><b>Case Name:</b></td><td>#qryVerdict.casename#</td></tr>
			</cfif>
			<cfif len(qryVerdict.casetype)>
				<tr valign="top"><td><b>Case Type:</b></td><td>#qryVerdict.casetype#</td></tr>
			</cfif>
			<cfif len(qryVerdict.county)>
				<tr valign="top"><td><b>County:</b></td><td>#qryVerdict.county#</td></tr>
			</cfif>
			<cfif len(qryVerdict.dateInjury) and isdate(qryVerdict.dateInjury)>
				<tr valign="top"><td><b>Date of Injury:</b></td><td>#dateformat(qryVerdict.dateInjury,"mm/dd/yyyy")#</td></tr>
			</cfif>
			<cfif len(qryVerdict.resolutionYear)>
				<tr valign="top"><td><b>Resolution Year:</b></td><td>#qryVerdict.resolutionYear#</td></tr>
			</cfif>
			<cfif len(qryVerdict.facts)>
				<tr valign="top"><td><b>Facts:</b></td><td>#qryVerdict.facts#</td></tr>
			</cfif>
			<cfif len(trim(qryVerdict.plaintiffsex))>
				<tr valign="top"><td><b>Plaintiff Sex:</b></td><td>#qryVerdict.plaintiffsex#</td></tr>
			</cfif>
			<cfif len(qryVerdict.plaintiffage)>
				<tr valign="top"><td><b>Plaintiff Age at Time of Injury:</b></td><td>#qryVerdict.plaintiffage#</td></tr>
			</cfif>
			<cfif len(qryVerdict.plaintiffjob)>
				<tr valign="top"><td><b>Plaintiff Occupation:</b></td><td>#qryVerdict.plaintiffjob#</td></tr>
			</cfif>
			<cfif len(qryVerdict.plaintifftheory)>
				<tr valign="top"><td><b>Plaintiff's Theory of Liability:</b></td><td>#qryVerdict.plaintifftheory#</td></tr>
			</cfif>
			<cfif len(trim(qryVerdict.defendantsex))>
				<tr valign="top"><td><b>Defendant Sex:</b></td><td>#qryVerdict.defendantsex#</td></tr>
			</cfif>
			<cfif len(qryVerdict.defendantage)>
				<tr valign="top"><td><b>Defendant Age at Time of Injury:</b></td><td>#qryVerdict.defendantage#</td></tr>
			</cfif>
			<cfif len(qryVerdict.defendantjob)>
				<tr valign="top"><td><b>Defendant Occupation:</b></td><td>#qryVerdict.defendantjob#</td></tr>
			</cfif>
			<cfif len(qryVerdict.defenseNature)>
				<tr valign="top"><td><b>Nature of Defense:</b></td><td>#qryVerdict.defenseNature#</td></tr>
			</cfif>
			<cfif len(qryVerdict.Injuries)>
				<tr valign="top"><td><b>Injuries:</b></td><td>#qryVerdict.Injuries#</td></tr>
			</cfif>
			<cfif len(qryVerdict.Diagnosis)>
				<tr valign="top"><td><b>Diagnosis:</b></td><td>#qryVerdict.Diagnosis#</td></tr>
			</cfif>
			<cfif len(qryVerdict.Prognosis)>
				<tr valign="top"><td><b>Prognosis:</b></td><td>#qryVerdict.Prognosis#</td></tr>
			</cfif>
			<cfif len(qryVerdict.Permanency)>
				<tr valign="top"><td><b>Permanency:</b></td><td>#qryVerdict.Permanency#</td></tr>
			</cfif>
			<cfif len(qryVerdict.totMedExpenses)>
				<tr valign="top"><td><b>Total Medical Expenses:</b></td><td>#qryVerdict.totMedExpenses#</td></tr>
			</cfif>
			<cfif len(qryVerdict.totLostWages)>
				<tr valign="top"><td><b>Total Lost Wages:</b></td><td>#qryVerdict.totLostWages#</td></tr>
			</cfif>
			<cfif len(qryVerdict.estFutureMedical)>
				<tr valign="top"><td><b>Estimated Future Medical:</b></td><td>#qryVerdict.estFutureMedical#</td></tr>
			</cfif>
			<cfif len(qryVerdict.estFutureLostWages)>
				<tr valign="top"><td><b>Estimated Future Lost Wages:</b></td><td>#qryVerdict.estFutureLostWages#</td></tr>
			</cfif>
			<cfif len(qryVerdict.plaintiffLastDemand)>
				<tr valign="top"><td><b>Plaintiff's Last Demand:</b></td><td>#qryVerdict.plaintiffLastDemand#</td></tr>
			</cfif>
			<cfif len(qryVerdict.defendantLastOffer)>
				<tr valign="top"><td><b>Defendant's Last Offer:</b></td><td>#qryVerdict.defendantLastOffer#</td></tr>
			</cfif>
			<cfif len(qryVerdict.verdict)>
				<tr valign="top"><td><b>Verdict:</b></td><td>#qryVerdict.verdict#</td></tr>
			</cfif>
			<cfif len(qryVerdict.settlement)>
				<tr valign="top"><td><b>Settlement:</b></td><td>#qryVerdict.settlement#</td></tr>
			</cfif>
			<cfif len(qryVerdict.policyLimits)>
				<tr valign="top"><td><b>Policy Limits:</b></td><td>#qryVerdict.policyLimits#</td></tr>
			</cfif>
			<cfif val(qryVerdict.verdictResult) gt 0>
				<tr valign="top"><td><b>Result:</b></td><td>
					<cfif qryVerdict.verdictResult is 1>Settled Prior to Suit
					<cfelseif qryVerdict.verdictResult is 2>Settled after suit but prior to trial date
					<cfelseif qryVerdict.verdictResult is 3>Settled during trial
					<cfelseif qryVerdict.verdictResult is 4>Verdict
					<cfelseif qryVerdict.verdictResult is 5>Other (Arbitration, etc.)
					</cfif>
				</td></tr>
			</cfif>
			<cfif len(qryVerdict.pointsOfInterest)>
				<tr valign="top"><td><b>Point(s) of Interest:</b></td><td>#qryVerdict.pointsOfInterest#</td></tr>
			</cfif>
			<cfif len(qryVerdict.plaintiffExperts)>
				<tr valign="top"><td><b>Plaintiff's Experts:</b></td><td>#qryVerdict.plaintiffExperts#</td></tr>
			</cfif>
			<cfif len(qryVerdict.defendantExperts)>
				<tr valign="top"><td><b>Defendant's Experts:</b></td><td>#qryVerdict.defendantExperts#</td></tr>
			</cfif>
			<cfif len(qryVerdict.plaintiffCounsel)>
				<tr valign="top"><td><b>Plaintiff's Counsel:</b></td><td>#qryVerdict.plaintiffCounsel#</td></tr>
			</cfif>
			<cfif len(qryVerdict.defendantCounsel)>
				<tr valign="top"><td><b>Defendant Counsel/Insurance Carrier:</b></td><td>#qryVerdict.defendantCounsel#</td></tr>
			</cfif>
			</table>
		</cfoutput>
	</cfsavecontent>
	<cfoutput>
		<div class="row-fluid">
			<p class="HeaderText">Viewing Information in NHAJ's Verdict and Settlement Database</p><br>
			
			<div class="row-fluid">
				<div class="span6">
					<div class="span3">
						<input type="button" value="Back to Listing" onclick="history.go(-1);" class="bodytext" />
					</div>
					<div class="span3">
						<input type="button" value="Print" onClick="window.open('/?pg=verdictDatabase&action=view&verdictID=#int(val(event.getValue('verdictID',0)))#&subaction=print')" class="printlink noprint bodytext" />
					</div>
				</div>
			</div>
			#local.caseDetails#
		</div>
	</cfoutput>
	
	<cfif event.getValue('subaction', '') eq "print">
		<cfset local.reportFileName = "NHVerdict"&int(val(event.getValue('verdictID',0)))&".pdf">

		<cfdocument format="PDF"  saveAsName="#local.reportFileName#" orientation = "portrait">
			<cfoutput>
				<html>
					<head>
					<title>#local.reportFileName#</title>
						<style>
							html, body { width:8in; padding:0; margin: 0; }
							body{font-family:'Calibri';}
							##containerDiv{margin:.75in;}
							td{line-height:35px;width:250px;}
						</style>
					</head>
					<body>
						<div id="containerDiv">
							<h1>NHAJ's Verdict and Settlement Database</h1>
							#local.caseDetails#
						</div>
					</body>
				</html>
			</cfoutput>
		</cfdocument>
	</cfif>
<cfelseif event.getValue('action','') eq "edit">
	<cfset qryVerdict = getVerdict(int(val(event.getValue('verdictID',0))))>

	<cfset allowForm = false />
	
	<cfif qryVerdict.recordcount is 0>
		<cfoutput><p class="headertext">Add to NHAJ's Verdict and Settlement Database</p>		<p class="bodytext">Complete the form below to add a verdict or settlement to the database.</p></cfoutput>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) or event.getValue('customPage.myRights.customAddDatabase',0)>
			<cfset allowForm = true>
		<cfelse>
			<cfoutput><p class="bodytext">You do not have permission to add to this database.</p></cfoutput>
		</cfif>
	<cfelse>
		<cfoutput><p class="headertext">Edit Information in NHAJ's Verdict and Settlement Database</p>
		<p class="bodytext">Complete the form below to edit this record.</p></cfoutput>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfset allowForm = true>
		<cfelse>
			<cfoutput><p class="bodytext">You do not have permission to edit this information.</p></cfoutput>
		</cfif>
	</cfif>

	<cfif allowForm>
		<cfset qryCountyNames = getCountyNames()>

		<cfsavecontent variable="JS">
			<cfoutput>
			<style type="text/css">
				##dateInjury { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
			</style>
							
			<SCRIPT LANGUAGE="JavaScript" TYPE="text/javascript">
			<!--
			function changeAvailability(formfieldid,disableflag) {
				var ff = document.getElementById(formfieldid);
				ff.disabled = disableflag;
				if (disableflag) ff.value='disabled';
				else ff.value='';
			}  
			
			$(document).ready(function(){
				mca_setupDatePickerField('dateInjury');
			});				
			//-->
			</SCRIPT>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#JS#">

		<cfoutput>
		<cfform name="verdictForm"  id="verdictForm" action="/?pg=verdictDatabase&action=save" method="post">
		<cfinput type="hidden" name="verdictid"  id="verdictid" value="#val(qryVerdict.verdictid)#">

		<div>
			<input type="submit" value="Save Verdict" name="btnSave" class="bodytext" /> &nbsp;
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and val(qryVerdict.verdictID) gt 0>
				<input type="button" name="btnDelete" value="Delete Verdict" class="bodytext" onclick="var msg='Are you sure you want to delete this verdict?\nIf you click OK, this verdict will be deleted and cannot be retrieved.'; if (confirm(msg)) self.location.href='/?pg=verdictDatabase&action=delete&verdictID=#val(qryVerdict.verdictID)#';"/> &nbsp;
			</cfif>
			<input type="button" value="Cancel" onclick="history.go(-1);" class="bodytext" />
		</div>
		<br/>
		<table border="0" class="bodytext" cellpadding="2" cellspacing="0">

		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and val(qryVerdict.verdictID) gt 0>
			<tr valign="top">
				<td nowrap><strong>Approved Status:</strong></td>
				<td></td>
				<td>
					<cfinput required="yes" message="Select an approval status." type="radio" value="1" name="isApproved"  id="isApproved" checked="#val(qryVerdict.isApproved) is 1#"> Approved - available for viewing<br/>
					<cfinput type="radio" value="0" name="isApproved"  id="isApproved" checked="#val(qryVerdict.isApproved) is 0#"> Not Approved - not available for viewing<br/>
				</td>
			</tr>
		<cfelse>
			<cfinput type="hidden" name="isApproved"  id="isApproved" value="#val(qryVerdict.isApproved)#">
		</cfif>
		<tr valign="top">
				<td nowrap><strong>Full Entry Status:</strong></td>
				<td></td>
				<td>
					<cfinput required="yes" message="Select a full entry status." type="radio" value="1" name="isFullEntry"  id="isFullEntry" checked="#val(qryVerdict.isFullEntry) is 1#"> Full Entry - Contains detailed information<br/>
					<cfinput type="radio" value="0" name="isFullEntry"  id="isFullEntry" checked="#val(qryVerdict.isFullEntry) is 0#"> Not a Full Entry - Does not contain detailed information<br/>
				</td>
			</tr>
		<tr valign="top">
			<td nowrap><strong>Case Name:</strong></td>
			<td>(R)</td>
			<td><cfinput class="bodytext" type="text" name="casename"  id="casename" maxlength="300" required="yes" message="Enter the case name" size="70" value="#qryVerdict.casename#"></td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>Case Type:</strong></td>
			<td></td>
			<td><cfinput class="bodytext" type="text" name="casetype"  id="casetype" maxlength="300" size="70" value="#qryVerdict.casetype#"></td>
		</tr>
		<tr valign="top">
			<td nowrap><strong>County:</strong></td>
			<td></td>
			<td>
				<select name="county" class="bodytext" onchange="changeAvailability('countyNew',this.value.length);">
				<option value="">Add new entry or select from list</option>
				<cfloop query="qrycountynames">
					<option value="#trim(qrycountynames.county)#" <cfif trim(qryVerdict.county) eq trim(qrycountynames.county)>selected</cfif>>#trim(qrycountynames.county)#</option>
				</cfloop>
				</select>
				<cfinput class="bodytext" type="text" name="countyNew" id="countyNew" maxlength="20" size="20">
			</td>
		</tr>
		<tr valign="top">
			<td nowrap><b>Date of Injury:</b></td>
			<td></td>
			<td>
				<cfinput type="text" name="dateInjury" id="dateInjury" value="#dateformat(qryVerdict.dateInjury,"mm/dd/yyyy")#" validate = "date" message="Enter a correctly formatted date">
				<a href="javascript:mca_clearDateRangeField('dateInjury');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a>
			</td>
		</tr>
		<tr>
			<td nowrap><b>Resolution Year:</b></td>			
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="resolutionYear"  id="resolutionYear" validate="integer" maxlength="4" size="6" value="#qryVerdict.resolutionYear#"></td>
		</tr>
		<tr>
			<td valign="top"><b>Facts:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="facts" cols="70" rows="5">#qryVerdict.facts#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Plaintiff</b><br/><dd><b>Sex:</b></dd></td>
			<td></td>
			<td valign="top"><br/><select name="plaintiffsex" class="bodytext"><option value=""></option><option value="M" <cfif qryVerdict.plaintiffsex eq "M">selected</cfif>>Male</option><option value="F" <cfif qryVerdict.plaintiffsex eq "F">selected</cfif>>Female</option></select></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Age at time of injury:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="plaintiffAge"  id="plaintiffAge" validate="integer" maxlength="2" size="5" value="#qryVerdict.plaintiffAge#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Occupation:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="plaintiffJob"  id="plaintiffJob" maxlength="75" size="30" value="#qryVerdict.plaintiffJob#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Theory of Liability:</b></dd></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="plaintiffTheory" cols="70" rows="5">#qryVerdict.plaintiffTheory#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Defendant</b><br/><dd><b>Sex:</b></dd></td>
			<td></td>
			<td valign="top"><br/><select name="defendantsex" class="bodytext"><option value=""></option><option value="M" <cfif qryVerdict.defendantsex eq "M">selected</cfif>>Male</option><option value="F" <cfif qryVerdict.defendantsex eq "F">selected</cfif>>Female</option></select></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Age at time of injury:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="defendantAge"  id="defendantAge" validate="integer" maxlength="2" size="5" value="#qryVerdict.defendantAge#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Occupation:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="defendantJob"  id="defendantJob" maxlength="75" size="30" value="#qryVerdict.defendantJob#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Nature of Defense:</b></dd></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="defenseNature" cols="70" rows="5">#qryVerdict.defenseNature#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Injuries:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="injuries" cols="70" rows="5">#qryVerdict.injuries#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Diagnosis:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="Diagnosis" cols="70" rows="5">#qryVerdict.Diagnosis#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Prognosis:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="Prognosis" cols="70" rows="5">#qryVerdict.Prognosis#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Permanency:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="Permanency" cols="70" rows="5">#qryVerdict.Permanency#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Specials</b><br/><dd><b>Total Medical Expenses:</b></dd></td>
			<td></td>
			<td valign="top"><br/><cfinput class="bodytext" type="text" name="totMedExpenses"  id="totMedExpenses" maxlength="100" size="70" value="#qryVerdict.totMedExpenses#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Total Lost Wages:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="totLostWages"  id="totLostWages" maxlength="100" size="30" value="#qryVerdict.totLostWages#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Estimated Future Medical:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="estFutureMedical"  id="estFutureMedical" maxlength="100" size="70" value="#qryVerdict.estFutureMedical#"></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Estimated Future Lost Wages:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="estFutureLostWages"  id="estFutureLostWages" maxlength="100" size="30" value="#qryVerdict.estFutureLostWages#"></td>
		</tr>
		<tr>
			<td valign="top"><b>Negotiations</b><br/><dd><b>Plaintiff's Last Demand:</b></dd></td>
			<td></td>
			<td valign="top"><br/><textarea class="bodytext" name="plaintiffLastDemand" cols="70" rows="5">#qryVerdict.plaintiffLastDemand#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Defendant's Last Offer:</b></dd></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="defendantLastOffer" cols="70" rows="5">#qryVerdict.defendantLastOffer#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Verdict:</b></dd></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="verdict" cols="70" rows="5">#qryVerdict.verdict#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Settlement:</b></dd></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="settlement" cols="70" rows="5">#qryVerdict.settlement#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><dd><b>Policy Limits:</b></dd></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="policyLimits"  id="policyLimits" size="70" value="#qryVerdict.policyLimits#"></td>
		</tr>
		<tr>
			<td valign="top"><b>Result:</b></td>
			<td></td>
			<td valign="top">
				<select name="verdictResult" class="bodytext">
				<option value=""></option>
				<option value="1" <cfif qryVerdict.verdictResult is 1>selected</cfif>>Settled Prior to Suit</option>
				<option value="2" <cfif qryVerdict.verdictResult is 2>selected</cfif>>Settled after suit but prior to trial date</option>
				<option value="3" <cfif qryVerdict.verdictResult is 3>selected</cfif>>Settled during trial</option>
				<option value="4" <cfif qryVerdict.verdictResult is 4>selected</cfif>>Verdict</option>
				<option value="5" <cfif qryVerdict.verdictResult is 5>selected</cfif>>Other (Arbitration, etc.)</option>
				</select>
			</td>
		</tr>
		<tr>
			<td valign="top"><b>Point(s) of Interest:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="pointsOfInterest" cols="70" rows="5">#qryVerdict.pointsOfInterest#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Plaintiff's Experts:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="plaintiffExperts" cols="70" rows="5">#qryVerdict.plaintiffExperts#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Defendant's Experts:</b></td>
			<td></td>
			<td valign="top"><textarea class="bodytext" name="defendantExperts" cols="70" rows="5">#qryVerdict.defendantExperts#</textarea></td>
		</tr>
		<tr>
			<td valign="top"><b>Plaintiff's Counsel:</b></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="PlaintiffCounsel"  id="PlaintiffCounsel" maxlength="1000" size="70" value="#qryVerdict.PlaintiffCounsel#"></td>
		</tr>
		<tr>
			<td valign="top"><b>Defendant Counsel/Insurance Carrier:</b></td>
			<td></td>
			<td valign="top"><cfinput class="bodytext" type="text" name="DefendantCounsel"  id="DefendantCounsel" maxlength="1000" size="70" value="#qryVerdict.DefendantCounsel#"></td>
		</tr>
		</table>
		</cfform>
		</cfoutput>
	</cfif>
	
<cfelseif event.getValue('action', '') eq "delete">
	<cfset verdictID = int(val(event.getValue('verdictID',0)))>

	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfquery name="deleteInfo" datasource="#application.dsn.customApps.dsn#">
			delete from dbo.NH_Verdicts
			where verdictID = <cfqueryparam value="#verdictID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	</cfif>
	<cfoutput>	
	<p class="headertext">Information Updated</p>
	<br />
	<table border="0" cellpadding="2">
		<tr>
		<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
	
			<form action="/?pg=verdictDatabase" method="post">
			<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
				<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
			</CFLOOP>
			<td><input type="submit" value="Return to Results" class="bodytext"/></td>
			</form>
	
		</cfif>
		<td><input type="button" onclick="document.location.href='/?pg=VerdictDatabase';" value="New Search" class="bodytext" /></td>
		</tr>
	</table>
	</cfoutput>
<cfelseif event.getValue('action', '') eq "save">
	<cfset verdictID = int(val(event.getValue('verdictID',0)))>

	<cfif variables.verdictID is 0>
		<cfquery name="insertVerdict" datasource="#application.dsn.customApps.dsn#">
			set nocount on
			INSERT INTO NH_Verdicts (caseName,caseType,county,dateInjury,facts,plaintiffSex,plaintiffAge,
				plaintiffJob,plaintiffTheory,defendantSex,defendantAge,defendantJob,defenseNature,injuries,
				diagnosis,prognosis,permanency,totMedExpenses,totLostWages,estFutureMedical,
				estFutureLostWages,plaintiffLastDemand,defendantLastOffer,verdict,settlement,policyLimits,verdictResult,
				pointsOfInterest,plaintiffExperts,defendantExperts,plaintiffCounsel,defendantCounsel,
				depoMemberDataID,dateLastModified,isApproved, resolutionYear, isFullEntry)
			VALUES (
				<cfqueryparam value="#trim(event.getValue('caseName', ''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('caseType',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfif len(trim(event.getValue('countyNew',''))) and trim(event.getValue('countyNew','')) neq "disabled">
					<cfqueryparam value="#trim(replace(event.getValue('countyNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					<cfqueryparam value="#trim(replace(event.getValue('county',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfif isDate(event.getValue('dateInjury',''))>
					<cfqueryparam value="#event.getValue('dateInjury','')#" cfsqltype="CF_SQL_DATE">,
				<cfelse>
					<cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('facts',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('plaintiffSex',''))#" cfsqltype="CF_SQL_CHAR">,
				<cfif isValid("integer",event.getValue('plaintiffAge',''))>
					<cfqueryparam value="#trim(event.getValue('plaintiffAge',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('plaintiffJob',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('plaintiffTheory',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('defendantSex',''))#" cfsqltype="CF_SQL_CHAR">,
				<cfif isValid("integer",event.getValue('defendantAge',''))>
					<cfqueryparam value="#trim(event.getValue('defendantAge',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('defendantJob',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('defenseNature',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('injuries',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('diagnosis',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('prognosis',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('permanency',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('totMedExpenses',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('totLostWages',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('estFutureMedical',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('estFutureLostWages',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('plaintiffLastDemand',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('defendantLastOffer',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('verdict',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('settlement',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('policyLimits',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfif isValid("integer",event.getValue('verdictResult',''))>
					<cfqueryparam value="#trim(event.getValue('verdictResult',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('pointsOfInterest',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('plaintiffExperts',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('defendantExperts',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfqueryparam value="#trim(event.getValue('plaintiffCounsel',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#trim(event.getValue('defendantCounsel',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#session.cfcuser.memberData.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				getdate(),
				<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<cfqueryparam value="1" cfsqltype="CF_SQL_BIT">,
				<cfelse>
					<cfqueryparam value="0" cfsqltype="CF_SQL_BIT">,
				</cfif>
				<cfif isValid("integer",event.getValue('resolutionYear',''))>
					<cfqueryparam value="#trim(event.getValue('resolutionYear',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				<cfqueryparam value="#trim(event.getValue('isFullEntry',''))#" cfsqltype="CF_SQL_BIT">
			)
			select SCOPE_IDENTITY() as verdictid
			set nocount off
		</cfquery>
		<cfset thisVerdictID = insertVerdict.verdictid>

		<!--- Email about verdict --->
		<cfif NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfsavecontent variable="local.mailContent">
				<cfoutput>
					<p>A verdict has been added to the Verdict and Settlement Database.</p>
					<p>VerdictID: #thisverdictID#</p>
					<p><a href="https://www.nhaj.org/NH/?pg=verdictDatabase&action=edit&verdictID=#thisverdictid#">Click here</a> to review the verdict and approve it for display.</p>
				</cfoutput>
			</cfsavecontent>

			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(local.ORGEmail.to.replaceAll(',',';'),';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
				if (arrayLen(local.arrEmailTo)) {
					local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.ORGEmail.from },
						emailto=local.arrEmailTo,
						emailreplyto=local.ORGEmail.from,
						emailsubject=local.ORGEmail.subject,
						emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=arguments.event.getValue('mc_siteinfo.siteID'),
						memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					);
				}
			</cfscript>
		</cfif>

	<cfelse>
		<cfquery name="updateVerdict" datasource="#application.dsn.customApps.dsn#">
			update dbo.NH_Verdicts
			set caseName = <cfqueryparam value="#trim(event.getValue('caseName',''))#" cfsqltype="CF_SQL_VARCHAR">,
				caseType = <cfqueryparam value="#trim(event.getValue('caseType',''))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfif len(trim(event.getValue('countyNew',''))) and trim(event.getValue('countyNew','')) neq "disabled">
					county = <cfqueryparam value="#trim(replace(event.getValue('countyNew',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				<cfelse>
					county = <cfqueryparam value="#trim(replace(event.getValue('county',''),chr(34),"'","ALL"))#" cfsqltype="CF_SQL_VARCHAR">,
				</cfif>
				<cfif isDate(event.getValue('dateInjury',''))>
					dateInjury = <cfqueryparam value="#event.getValue('dateInjury','')#" cfsqltype="CF_SQL_DATE">,
				<cfelse>
					dateInjury = <cfqueryparam null="yes" cfsqltype="CF_SQL_DATE">,
				</cfif>
				facts = <cfqueryparam value="#trim(event.getValue('facts',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				plaintiffSex = <cfqueryparam value="#trim(event.getValue('plaintiffSex',''))#" cfsqltype="CF_SQL_CHAR">,
				<cfif isValid("integer",event.getValue('plaintiffAge',''))>
					plaintiffAge = <cfqueryparam value="#trim(event.getValue('plaintiffAge',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					plaintiffAge = <cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				plaintiffJob = <cfqueryparam value="#trim(event.getValue('plaintiffJob',''))#" cfsqltype="CF_SQL_VARCHAR">,
				plaintiffTheory = <cfqueryparam value="#trim(event.getValue('plaintiffTheory',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				defendantSex = <cfqueryparam value="#trim(event.getValue('defendantSex',''))#" cfsqltype="CF_SQL_CHAR">,
				<cfif isValid("integer",event.getValue('defendantAge',''))>
					defendantAge = <cfqueryparam value="#trim(event.getValue('defendantAge',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					defendantAge = <cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				defendantJob = <cfqueryparam value="#trim(event.getValue('defendantJob',''))#" cfsqltype="CF_SQL_VARCHAR">,
				defenseNature = <cfqueryparam value="#trim(event.getValue('defenseNature',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				injuries = <cfqueryparam value="#trim(event.getValue('injuries',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				diagnosis = <cfqueryparam value="#trim(event.getValue('diagnosis',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				prognosis = <cfqueryparam value="#trim(event.getValue('prognosis',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				permanency = <cfqueryparam value="#trim(event.getValue('permanency',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				totMedExpenses = <cfqueryparam value="#trim(event.getValue('totMedExpenses',''))#" cfsqltype="CF_SQL_VARCHAR">,
				totLostWages = <cfqueryparam value="#trim(event.getValue('totLostWages',''))#" cfsqltype="CF_SQL_VARCHAR">,
				estFutureMedical = <cfqueryparam value="#trim(event.getValue('estFutureMedical',''))#" cfsqltype="CF_SQL_VARCHAR">,
				estFutureLostWages = <cfqueryparam value="#trim(event.getValue('estFutureLostWages',''))#" cfsqltype="CF_SQL_VARCHAR">,
				plaintiffLastDemand = <cfqueryparam value="#trim(event.getValue('plaintiffLastDemand',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				defendantLastOffer = <cfqueryparam value="#trim(event.getValue('defendantLastOffer',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				verdict = <cfqueryparam value="#trim(event.getValue('verdict',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				settlement = <cfqueryparam value="#trim(event.getValue('settlement',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				policyLimits = <cfqueryparam value="#trim(event.getValue('policyLimits',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				<cfif isValid("integer",event.getValue('verdictResult',''))>
					verdictResult = <cfqueryparam value="#trim(event.getValue('verdictResult',''))#" cfsqltype="CF_SQL_INTEGER">,
				<cfelse>
					verdictResult = <cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
				</cfif>
				pointsOfInterest = <cfqueryparam value="#trim(event.getValue('pointsOfInterest',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				plaintiffExperts = <cfqueryparam value="#trim(event.getValue('plaintiffExperts',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				defendantExperts = <cfqueryparam value="#trim(event.getValue('defendantExperts',''))#" cfsqltype="CF_SQL_LONGVARCHAR">,
				plaintiffCounsel = <cfqueryparam value="#trim(event.getValue('plaintiffCounsel',''))#" cfsqltype="CF_SQL_VARCHAR">,
				defendantCounsel = <cfqueryparam value="#trim(event.getValue('defendantCounsel',''))#" cfsqltype="CF_SQL_VARCHAR">,
				dateLastModified = getdate(),
				isApproved = <cfqueryparam value="#event.getValue('isApproved','')#" cfsqltype="CF_SQL_BIT">,
				isFullEntry = <cfqueryparam value="#event.getValue('isFullEntry','')#" cfsqltype="CF_SQL_BIT">,
				<cfif isValid("integer",event.getValue('resolutionYear',''))>
					resolutionYear = <cfqueryparam value="#trim(event.getValue('resolutionYear',''))#" cfsqltype="CF_SQL_INTEGER">
				<cfelse>
					resolutionYear = <cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				
			where verdictid = <cfqueryparam value="#event.getValue('verdictid','')#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfset thisverdictid = event.getValue('verdictid','')>
	</cfif>
	<cfoutput>
	<p class="headertext">Information Saved. <cfif NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>Once approved, the information you entered will be searchable in the database.</cfif></p>
	<br />
	<table border="0" cellpadding="2">
	<tr>
		<cfif isdefined("session.lastverdictsearch") and IsStruct(session.lastverdictsearch) and structcount(session.lastverdictsearch) gt 0>
			<form action="/?pg=verdictDatabase" method="post">
			<CFLOOP INDEX="form_element" LIST="#session.lastverdictsearch.fieldnames#">
				<INPUT TYPE="hidden" NAME="#variables.form_element#" VALUE="#session.lastverdictsearch[variables.form_element]#">
			</CFLOOP>
			<td><input type="submit" value="Return to Results" class="bodytext"/></td>
			</form>
		</cfif>
		<td><input type="button" onclick="document.location.href='/?pg=verdictDatabase';" value="New Search" class="bodytext" /></td>
	</tr>
	</table>
</cfoutput>
<cfelseif int(val(event.getValue('page',0))) gt 0>
	<cfset pageID = int(val(event.getValue('page',0)))>
	<cfset maxrows = 10>

	<cfquery name="qryMatches" datasource="#application.dsn.customApps.dsn#">
		select verdictID, caseName, caseType, county, dateInjury, facts, plaintiffSex, plaintiffAge, plaintiffJob, plaintiffTheory, defendantSex, defendantAge, defendantJob, defenseNature, injuries, diagnosis, prognosis, permanency, totMedExpenses, totLostWages, estFutureMedical, estFutureLostWages, plaintiffLastDemand, defendantLastOffer, verdict, settlement, policyLimits, verdictResult, pointsOfInterest, plaintiffExperts, defendantExperts, plaintiffCounsel, defendantCounsel, depoMemberDataID, dateLastModified, isApproved, resolutionYear, isFullEntry 
		from dbo.NH_Verdicts
		where 
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and len(event.getValue('isApproved',''))>
			isApproved = <cfqueryparam value="#event.getValue('isApproved','')#" cfsqltype="CF_SQL_BIT">
		<cfelseif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			1=1
		<cfelse>
			isApproved = 1
		</cfif>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and len(event.getValue('isFullEntry',''))>
			and isFullEntry = <cfqueryparam value="#event.getValue('isFullEntry','')#" cfsqltype="CF_SQL_BIT">
		<cfelseif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			and 1=1
		<cfelse>
			and isFullEntry = 1
		</cfif>
		<cfif len(event.getValue('casetype',''))>
			and casetype = <cfqueryparam value="#event.getValue('casetype','')#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif len(event.getValue('county',''))>
			and county = <cfqueryparam value="#event.getValue('county','')#" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif len(event.getValue('resolutionYear',''))>
			and resolutionYear = <cfqueryparam value="#event.getValue('resolutionYear','')#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		<cfif len(event.getValue('plaintiffCounsel',''))>
			and plaintiffCounsel LIKE <cfqueryparam value="%#event.getValue('plaintiffCounsel','')#%" cfsqltype="CF_SQL_VARCHAR">
		</cfif>
		<cfif len(event.getValue('keywords',''))>
			and (
				isnull(casename,'') + ' ' + isnull(casetype,'') + ' ' + isnull(county,'') + ' ' + isnull(facts,'') + ' ' + isnull(plaintiffJob,'') + ' ' + 
				isnull(plaintiffTheory,'') + ' ' + isnull(defendantJob,'') + ' ' + isnull(defenseNature,'') + ' ' + isnull(injuries,'') + ' ' + 
				isnull(diagnosis,'') + ' ' + isnull(permanency,'') + ' ' + isnull(totMedExpenses,'') + ' ' + isnull(totLostWages,'') + ' ' + 
				isnull(estFutureMedical,'') + ' ' + isnull(estFutureLostWages,'') + ' ' + isnull(plaintiffLastDemand,'') + ' ' + 
				isnull(defendantLastOffer,'') + ' ' + isnull(verdict,'') + ' ' + isnull(settlement,'') + ' ' + isnull(policyLimits,'') + ' ' + 
				isnull(pointsofInterest,'') + ' ' + isnull(plaintiffExperts,'') + ' ' + isnull(defendantExperts,'') + ' ' + 
				isnull(plaintiffCounsel,'') + ' ' + isnull(defendantCounsel,'')
				LIKE <cfqueryparam value="%#event.getValue('keywords','')#%" cfsqltype="CF_SQL_VARCHAR">
				)
		</cfif>
	</cfquery>

	<cfset session.lastVerdictSearch = duplicate(form)>
	
	<cfif qryMatches.recordcount>
		<cfset numpages = ceiling(qryMatches.recordcount / variables.maxrows)>
		<cfset startrow = ((variables.pageID-1) * variables.maxrows) + 1>
		<cfset endrow = variables.startrow + variables.maxrows>
		<cfif qryMatches.recordcount lt variables.endrow>
			<cfset endrow = qryMatches.recordcount>
		</cfif>
	<cfelse>
		<cfset numpages = 0>
		<cfset startrow = 0>
		<cfset endrow = 1>
	</cfif>
	
	<cfoutput>
	<div class="headertext">Verdicts and Settlements Search Results</div>
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<div class="bodyText">The Site Administrator can see all results, including those not yet approved (which are outlined in red).</div>
	</cfif>
	
	<script language="JavaScript">
		function prevPage() {
			var objForm = document.forms['frmHidden'];
			objForm.page.value = '#event.getValue('page','')-1#';
			objForm.submit();
		}
		function nextPage() {
			var objForm = document.forms['frmHidden'];
			objForm.page.value = '#event.getValue('page','')+1#';
			objForm.submit();
		}
	</script>
		
	<br/>
	<table width="100%" cellpadding="0" cellspacing="0">
	<tr><td class="bodytext">
			<cfif qryMatches.recordcount>
				Showing #startrow# to #endrow# of #qryMatches.recordcount# matches
			<cfelse>
				No matches found.
			</cfif>
		</td>
		<td align="right">
			<cfif form.page gt 1>
				<input type="button" value="&lt;&lt; Previous Page" class="bodytext" onclick="prevPage();">
			</cfif>
			<cfif qryMatches.recordcount gt (form.page*maxrows)>
				<input type="button" value="Next Page &gt;&gt;" class="bodytext" onclick="nextPage();">
			</cfif>
			<input type="button" onclick="self.location.href='/?pg=verdictDatabase';" value="New Search" class="bodytext">
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) or event.getValue('customPage.myRights.customAddDatabase',0)>
				<input type="button" onclick="self.location.href='/?&pg=verdictDatabase&action=edit';" value="Add Verdict" class="bodytext">
			</cfif>
		</td>
	</tr>
	</table>
	<br/>
	</cfoutput>
	
	
	<cfif qryMatches.recordcount eq 0>
		<cfoutput><div class="bodytext">No records match your search criteria.</div></cfoutput>
	<cfelse>
		<cfoutput><table border="0" class="bodytext" width="100%" cellpadding="4" cellspacing="0" style="border:1px solid ##666">
		<tr bgcolor="##999999"><td colspan="3"></td></tr>
		</cfoutput>
		<cfoutput query="qryMatches" startrow="#startrow#" maxrows="#maxrows#">
			<tr valign="top" <cfif qryMatches.currentrow mod 2 is 0>bgcolor="##CCCCCC"</cfif>>
				<td width="15" <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-left:2px solid ##FF0000;"</cfif>>#qryMatches.currentrow#.</td>
				<td width="40" <cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT qryMatches.isApproved>style="border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;"</cfif>>
					<a href="/?pg=verdictDatabase&action=view&verdictID=#qryMatches.verdictID#">View</a>
					<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
						<br/><a href="/?pg=verdictDatabase&action=edit&verdictID=#qryMatches.verdictID#">Edit</a>
					</cfif>
				</td>
				<td style="<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT qryMatches.isApproved>border-top:2px solid ##FF0000;border-bottom:2px solid ##FF0000;border-right:2px solid ##FF0000;<cfelse>border-right:1px solid ##666;</cfif>">
					Case Name: #left(qryMatches.casename,100)#<cfif len(qryMatches.casename) gt 100>...</cfif><br/>
					<cfif len(qryMatches.Injuries)>Injuries: #left(qryMatches.Injuries,100)#<cfif len(qryMatches.Injuries) gt 100>...</cfif><br/></cfif>
					<cfif len(qryMatches.plaintiffCounsel)>Plaintiff's Counsel: #left(qryMatches.plaintiffCounsel,100)#<cfif len(qryMatches.plaintiffCounsel) gt 100>...</cfif><br/></cfif>
					<cfif len(qryMatches.defendantCounsel)>Defendant Counsel/Insurance Carrier: #left(qryMatches.defendantCounsel,100)#<cfif len(qryMatches.defendantCounsel) gt 100>...</cfif><br/></cfif>
					<cfif len(qryMatches.verdict)>Verdict: #left(qryMatches.verdict,100)#<cfif len(qryMatches.verdict) gt 100>...</cfif><br/></cfif>
					<cfif len(qryMatches.settlement)>Settlement: #left(qryMatches.settlement,100)#<cfif len(qryMatches.settlement) gt 100>...</cfif><br/></cfif>
					<cfif len(qryMatches.resolutionYear)>Resolution Year: #left(qryMatches.resolutionYear,100)#<cfif len(qryMatches.resolutionYear) gt 100>...</cfif><br/></cfif>
				</td>
			</tr>
		</cfoutput>

		<cfoutput></table></cfoutput>
	</cfif>

	<cfoutput>
	<form name="frmHidden" action="/?pg=verdictDatabase" method="post">
	<input type="hidden" name="page" value="">
	<cfloop INDEX="form_element" LIST="#FORM.fieldnames#">
		<cfif form_element neq "page">
			<INPUT TYPE="hidden" NAME="#form_element#" VALUE="#form[form_element]#">
		</cfif>
	</CFLOOP>
	</form>
	</cfoutput>

<cfelse>
	<cfset qryCaseTypes = getCaseTypes()>
	<cfset qryCountyNames = getCountyNames()>
	
<cfoutput>
	<p class="headertext">Search NHAJ Verdict and Settlement Database</p>
	<p class="bodytext">NHAJ's Verdict and Settlement Database contains reports submitted by members. 
	The database records include all information provided by members. The Verdict and Settlement 
	database is a valuable service to our members and we welcome all reports. </p>
	<cfform action="/?pg=verdictDatabase" method="post">
	<input type="hidden" name="page" value="1" />
	<table class="bodytext">
	<tr>
		<td>Case Type:</td>
		<td>
			<select name="casetype" class="bodytext">
			<option value="">All</option>
			<cfloop query="qryCaseTypes">
				<option value="#htmleditformat(qryCaseTypes.casetype)#">#qryCaseTypes.casetype#</option>
			</cfloop>
			</select>
		</td>
	</tr>
	<tr>
		<td>Plaintiff's Counsel:</td>
		<td><input type="text" name="plaintiffCounsel" class="bodytext" maxlength="70" size="70" /></td>
	</tr>
	<tr>
		<td>County:</td>
		<td>
			<select name="county" class="bodytext">
			<option value="">All</option>
			<cfloop query="qryCountyNames">
				<option value="#qryCountyNames.county#">#qryCountyNames.county#</option>
			</cfloop>
			</select>
		</td>
	</tr>
	<tr>
		<td>Keywords:</td>
		<td><input type="text" name="keywords" class="bodytext" maxlength="70" size="70" /></td>
	</tr>
	<tr>
		<td>Resolution Year:</td>
		<td><input type="text" name="resolutionYear" class="bodytext" maxlength="4" size="6" /></td>
	</tr>
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<tr>
			<td>Approval Status:</td>
			<td>
				<select name="isApproved" class="bodytext">
				<option value="">All</option>
				<option value="1">Approved Verdicts Only</option>
				<option value="0">Non-Approved Verdicts Only</option>
				</select>
			</td>
		</tr>
		<tr>
			<td>Full Entry Status:</td>
			<td>
				<select name="isFullEntry" class="bodytext">
				<option value="">All</option>	
				<option value="1">Full Entry Only</option>
				<option value="0">Non-Full Entries Only</option>
				</select>
			</td>
		</tr>
	</cfif>
	</table>
	<br />
	<input type="submit" value="Search Database" class="bodytext"/>
	<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) or event.getValue('customPage.myRights.customAddDatabase',0)>
		&nbsp;&nbsp;<input type="button" onclick="document.location.href='/?pg=verdictDatabase&action=edit';" value="Add Verdict" class="bodytext" />
	</cfif>
	</cfform>
</cfoutput>	
</cfif>

<cffunction name="getCaseTypes" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT casetype
		FROM dbo.NH_Verdicts
		WHERE casetype IS NOT NULL AND casetype <> ''
		ORDER BY casetype
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getCountyNames" returntype="query" output="No">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		SELECT DISTINCT county
		FROM dbo.NH_Verdicts
		WHERE county IS NOT NULL AND county <> ''
		ORDER BY county
	</cfquery>
	<cfreturn qry>
</cffunction>
<cffunction name="getVerdict" returntype="query" output="No">
	<cfargument name="verdictID" type="numeric" required="yes">
	<cfset var qry = "">
	<cfquery name="qry" datasource="#application.dsn.customApps.dsn#">
		select verdictID, caseName, caseType, county, dateInjury, facts, plaintiffSex, plaintiffAge, plaintiffJob, plaintiffTheory, defendantSex, defendantAge, defendantJob, defenseNature, injuries, diagnosis, prognosis, permanency, totMedExpenses, totLostWages, estFutureMedical, estFutureLostWages, plaintiffLastDemand, defendantLastOffer, verdict, settlement, policyLimits, verdictResult, pointsOfInterest, plaintiffExperts, defendantExperts, plaintiffCounsel, defendantCounsel, depoMemberDataID, dateLastModified, isApproved, resolutionYear, isFullEntry 
		from dbo.NH_Verdicts
		where verdictid = <cfqueryparam value="#arguments.verdictID#" cfsqltype="cf_sql_integer">
	</cfquery>
	<cfreturn qry>
</cffunction>
