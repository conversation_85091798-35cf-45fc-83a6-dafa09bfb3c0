<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

        <cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
            local.formAction = arguments.event.getValue('fa','showLookup');
            local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];
            local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ApplicationTitle", type="STRING", desc="Application Title", value="IAJ Membership Application" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ApplicationtionIntroText", type="CONTENTOBJ", desc="Application introduction text", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberTypeTitle", type="STRING", desc="Membership Rate Title", value="Membership Type" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MembershipAgreement", type="CONTENTOBJ", desc="Required Membership Agreement", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ProfileCodePayCC", type="STRING", desc="pay profile code for Credit Card", value="AuthCIM" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);              
            local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation page", value="Thank you for submitting your application. This Page has been emailed to the email address on file. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="EmailConfirmationContent", type="CONTENTOBJ", desc="Content at top of user confirmation email", value="Thank you for submitting your application. We will process your payment once your application has been approved. You will be notified via Email once your membership is active." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="IAJ - Membership Application Form received" }; 
                arrayAppend(local.arrCustomFields, local.tmpField); 
            local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="IAJ - Membership Application Form" }; 
                arrayAppend(local.arrCustomFields, local.tmpField); 
            local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);         
            local.tmpField = { name="MemberConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberActive", type="CONTENTOBJ", desc="Display message for active members", value="IAJ records indicate that you are currently a member. Please <a href='/?pg=login'>click here</a> to login." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="MemberBilled", type="CONTENTOBJ", desc="Display message for billed members", value="You need to renew your IAJ membership. You will be re-directed to your renewal shortly." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
                arrayAppend(local.arrCustomFields, local.tmpField);	
            local.tmpField = { name="ProfessionalLicenseTitle", type="STRING", desc="Professional License Title", value="Professional Licenses (Please Select All That Apply)" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);             
            local.tmpField = { name="MembershipDuesTypeUID", type="STRING", desc="Membership Dues Type", value="76d5a3c7-dcb6-427b-b585-5f93193b43bc" }; 
                arrayAppend(local.arrCustomFields, local.tmpField); 
            local.tmpField = { name="SubscriptionUID", type="STRING", desc="UID of the Root Subscription that this form offers", value="ada38989-cda4-4c27-bd11-a2b770db00e8" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);   
            local.tmpField = { name="MembershipIntroText", type="CONTENTOBJ", desc="Membership Rate Introduction Text", value='The membership term for "Late Year" membership runs from today until December 31 of next year.' }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="PracticeAreaIntroText", type="CONTENTOBJ", desc="Practice Area Introduction Text", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);         
        
            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
            
            /* ***************** */
            /* set form defaults */
            /* ***************** */
            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='join',
                formNameDisplay=variables.strPageFields.ApplicationTitle,
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.MemberConfirmationFrom
            ));
            
            /* ******************* */
            /* Member History Vars */
            /* ******************* */
            variables.useHistoryID = 0;
            if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
                variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
            if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
                variables.useHistoryID = int(val(session.useHistoryID));            
            variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Started');
            variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MemAppHistory', subName='Completed');
            variables.historyStartedText = "Member started Membership form.";
            variables.historyCompletedText = "Member completed Membership form.";
    
            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            switch (local.formAction) {
                case "processLookup":
                
                    if (NOT variables.objCffp.testSubmission(form) and NOT local.isLoggedIn and not len(arguments.event.getTrimValue('mk',''))) {
                        local.returnHTML = showError(errorCode='spam');
                        break;      
                    }
                    switch (processLookup()) {
                        case "success":
                            local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
                            break;      
                        case "activefound":
                            local.returnHTML = showError(errorCode='activefound');
                            break;      
                        case "acceptedfound":
                            local.returnHTML = showError(errorCode='acceptedfound');
                            break;
                        case "billedjoinfound":
                            local.returnHTML = showError(errorCode='billedjoinfound');
                            break;
                        case "billedfound":
                            local.returnHTML = showError(errorCode='billedfound');
                            break;      
                        default:
                            application.objCommon.redirect(variables.baselink);
                            break;              
                    }
                    break;
                case "processMemberInfo":
                    if (NOT variables.objCffp.testSubmission(form)) {
                        local.returnHTML = showError(errorCode='spam');
                        break;      
                    }
                    switch (processMemberInfo(rc=arguments.event.getCollection())) {
                        case "success":
                            local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
                            break;
                        default:
                            local.returnHTML = showError(errorCode='failsavemember');
                            break;              
                    }
                    break;
                case "processMembershipInfo":
                    if (NOT variables.objCffp.testSubmission(form)) {
                        local.returnHTML = showError(errorCode='spam');
                        break;      
                    }
                    
                    switch (processMembershipInfo(rc=arguments.event.getCollection())) {
                        case "success":
                            saveAdditionalFields(rc=arguments.event.getCollection(),event=arguments.event);
                            local.returnHTML = showPayment(rc=arguments.event.getCollection());
                            break;
                        default:
                            local.returnHTML = showError(errorCode='failsavemembership');
                            break;              
                    }
                    break;
                case "processPayment":
                    local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
                    local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
					application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
                    structDelete(session, "formFields");
                    break;
                case "showMembershipInfo":
                    local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
                    break;              
                default:
                    local.returnHTML = showLookup();
                    break;
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");    
        </cfscript>

    </cffunction>

    <cffunction name="showLookup" access="private" output="false" returntype="string">
       <cfset var local = structNew()>
        <cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
        <cfset local.objCffp = variables.objCffp>
        
		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
        
        <cfsavecontent variable="local.headCode">
            <cfoutput>
            #variables.pageJS#
            <script type="text/javascript">
                var step = 0;
                var prevStep = 0;
                function afterFormLoad(){
                    $('html, body').animate({ scrollTop: 0 }, 500);                 
                    $(".ContactTypeHolder tr:first-child td").eq(0).width($("##applicantInformationFieldSet table:last-child tr").eq(4).find('td').eq(0).width());  
                    $(".ContactTypeHolder tr:first-child td").eq(1).width($("##applicantInformationFieldSet table:last-child tr").eq(4).find('td').eq(1).width());              
                }
                function assignMemberData(memObj) {
                    $('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
                    mc_continueForm($('###variables.formName#'),afterFormLoad);
                }

                function validatePaymentForm(isPaymentRequired) {
                    if(isPaymentRequired == 'YES') {
                        var arrReq = mccf_validatePPForm();
                        if (arrReq.length > 0) {
                            var msg = '';
                            for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                            showAlert(msg,afterFormLoad);
                            return false;
                        }
                    }

                    mc_continueForm($('###variables.formName#'),afterFormLoad);
                    return false;
                }

                window.onhashchange = function() {       
                    if (location.hash.length > 0) {        
                        step = parseInt(location.hash.replace('##',''),10);     
                        if (prevStep > step){
                            if(step==1)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
                            if(step==2)
                                $('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
                            mc_loadDataForForm($('###variables.formName#'),'previous',afterFormLoad);       
                        }
                    } else {
                        step = 1;
                    }
                    prevStep = step;                    
                }                       
                
                $(document).ready(function() {
                    <cfif variables.useMID and NOT local.isSuperUser>                   
                        var mo = { memberID:#variables.useMID# };
                        assignMemberData(mo);                   
                    <cfelseif local.isSuperUser>
                        $('div##div#variables.formName#wrapper').hide();
                        $('div##div#variables.formName#loading').html('<br/><i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
                    </cfif>
                });                 
            </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
                <cfinput type="hidden" name="fa" id="fa" value="processLookup">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                <cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
                <cfinclude template="/model/cfformprotect/cffp.cfm">
                                                
                <div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
                <div class="tsAppSectionContentContainer">
                    <table cellspacing="0" cellpadding="2" border="0" width="100%">
                    <tr>
                        <td width="175" style="text-align:center;">
                            <button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
                        </td>
                        <td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
                    </tr>
                    </table>
                </div>

            </cfform>
            </cfoutput>
        </cfsavecontent>
    
        <cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="processLookup" access="private" output="false" returntype="string">
        <cfset var local = structNew()>
        <cfset local.stReturn = "nomatch">

        <cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>

        <cfif local.qryMember.recordcount is not 1>
            <cfset local.stReturn = "nomatch">
        <cfelse>

            <cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
        
            <cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), variables.strPageFields.MembershipDuesTypeUID)>
                <cfset local.stReturn = "activefound">
            <cfelse>
                <cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
                <cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), variables.strPageFields.MembershipDuesTypeUID)>
                    <cfset local.stReturn = "acceptedfound">
                <cfelse>
                    <cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>

                    <cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), variables.strPageFields.MembershipDuesTypeUID)>
                        <cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
                    <cfelse>
                        <cfset local.stReturn = "success">
                    </cfif>
                </cfif> 
            </cfif>
        </cfif>
        <cfreturn local.stReturn>
    </cffunction>

    <cffunction name="showMemberInfo" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

        <cfset var local = structNew()>
        <cfset local.objCffp = variables.objCffp>

        <!--- Load prefill data: either the fields from new acct form or from the members table --->
        <cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=variables.siteID, orgID=variables.orgID, memberID=variables.useMID)>

        <cfset local.strData = {}>
        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
            <cfset local.strData = session.formFields.step1>
            <cfset variables.origMemberID = session.formFields.step1.origMemberID>
            <cfif session.useHistoryID>
                <cfset variables.useHistoryID = session.useHistoryID>
            </cfif>         
        <cfelse>
            <cfset variables.origMemberID = variables.useMID>
        </cfif> 

        <!--- Member Type --->
        <cfset local.contactTypeField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
        
        <!--- Applicant Information --->
        <cfset local.applicantInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='27a5e7ee-965a-4927-8b54-d9cb3e081569', mode="collection", strData=local.strData)>
        
        <!--- Attorney Information --->
        <cfset local.attorneyInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='9c7dcd29-ab7c-4c29-96b5-dc7e84ac0c22', mode="collection", strData=local.strData)>
        
        <!---  Staff Public Defender Information--->
         <cfset local.staffDefenderInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='96DF646C-A7DE-4E21-AB9D-AC6B7D02BF63', mode="collection", strData=local.strData)>
        
        <!--- Additional Questions --->
        <cfset local.additionalQuestionsFieldSet = application.objCustomPageUtils.renderFieldSet(uid='3903ABA0-D885-409D-925F-F128A45CE7C3', mode="collection", strData=local.strData)>
        
        <!--- Law Student Information --->
        <cfset local.lawStudentInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='4724664e-712b-4ca7-825a-891f47fbafc6', mode="collection", strData=local.strData)>
    
        <cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
			<style type="text/css">
				form td.tsAppBodyText{
					vertical-align: middle;
				}
				form .tsAppSectionContentContainer tr{
					margin-bottom: 10px!important;
				}				
				.innerPage-content input[type="text"] {
					width:206px!important;
				}
				.innerPage-content select{
					width:220px!important;
				}
				div.tsAppSectionHeading{margin-bottom:20px}
				##content-wrapper table td:nth-child(2) {
					white-space: initial!important;
				}
				##selectedLicense input[type="text"]{
					width:unset!important;
					max-width:206px!important;
				}
				##selectedLicense select{
					width:unset!important;
					max-width:220px!important;
				}
				@media screen and (max-width: 767px){
					##content-wrapper table td {
						display: block;
						margin-bottom:0px;
					}
					##content-wrapper table td:nth-child(1) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(2) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(3) {
						display: inline;
						margin: 0;
    					padding: 0;
					}
					##content-wrapper table td:nth-child(4) {
						margin-bottom: 12px;
						margin-left: 0;
						padding-left: 0;
					}

					##content-wrapper div.ui-multiselect-menu{width:auto!important;}

					##state_table {
						display: none !important;
					}
					##selectedLicense input[type="text"]{
						width:206px!important;
						max-width:206px!important;
					}
					##selectedLicense select{
						width:206px!important;
						max-width:220px!important;
					}

				}	
				##content-wrapper button.ui-multiselect {width:220px!important;}
				##content-wrapper div.ui-multiselect-menu {width:214px!important;}							

				div.alert-danger{padding: 10px !important;}				

			</style>
            <script language="javascript">
                function afterFormLoad(){
                    $('html, body').animate({ scrollTop: 0 }, 500);                         
                }
            
                function adjustFieldsetDisplay(contactTypeField) {
                    var memType = $(this).find('option:selected').text();                    
                    switch(memType) {
                        case 'Attorney': 
                            showFieldsContactType('attorneyInformationFieldSetHolder,additionalQuestionsFieldSetHolder,professionalLicenseSetHolder');
                            resetFormFeilds('attorneyInformationFieldSetHolder');
                            resetFormFeilds('additionalQuestionsFieldSetHolder');
                            resetFormFeilds('professionalLicenseSetHolder');
                            if ($("mpl_pltypeid").is(':visible')) {
                                $('.mpl_pltypeid').multiselect('refresh');
                                $(".mpl_pltypeid option").each(function(){
                                    $('##tr_state_'+$(this).attr("value")).remove();
                                });
                            }
                            if($('##state_table tbody tr').length == 1){
                                $("##state_table").hide();
                            }   
                            break;
                        case 'Staff Public Defender': 
                            showFieldsContactType('staffDefenderInformationFieldSetHolder,professionalLicenseSetHolder');
                            resetFormFeilds('staffDefenderInformationFieldSetHolder');
                            resetFormFeilds('professionalLicenseSetHolder');
                            if ($("mpl_pltypeid").is(':visible')) {
                                $('.mpl_pltypeid').multiselect('refresh');
                                $(".mpl_pltypeid option").each(function(){
                                    $('##tr_state_'+$(this).attr("value")).remove();
                                });
                            }
                            if($('##state_table tbody tr').length == 1){
                                $("##state_table").hide();
                            }   
                            break;
                        case 'Law Student':
                            showFieldsContactType('lawStudentInformationFieldSetHolder'); 
                            resetFormFeilds('lawStudentInformationFieldSetHolder');
                            resetFormFeilds('professionalLicenseSetHolder');
                            if ($("mpl_pltypeid").is(':visible')) {
                                $('.mpl_pltypeid').multiselect('refresh');
                                $(".mpl_pltypeid option").each(function(){
                                    $('##tr_state_'+$(this).attr("value")).remove();
                                });
                            }
                            if($('##state_table tbody tr').length == 1){
                                $("##state_table").hide();
                            }   
                            break;
                        default:
                            showFieldsContactType('');
                            resetFormFeilds('attorneyInformationFieldSetHolder');
                            resetFormFeilds('additionalQuestionsFieldSetHolder');
                            resetFormFeilds('lawStudentInformationFieldSetHolder');
                            resetFormFeilds('professionalLicenseSetHolder');
                            if ($("mpl_pltypeid").is(':visible')) {                         
                                $('.mpl_pltypeid').multiselect('refresh');
                                $(".mpl_pltypeid option").each(function(){
                                    $('##tr_state_'+$(this).attr("value")).remove();
                                });
                            }
                            if($('##state_table tbody tr').length == 1){
                                $("##state_table").hide();
                            }   
                            break;
                    }
                }

                function showFieldsContactType(classList)               
                {
                    $(".attorneyInformationFieldSetHolder").hide();
                    $(".additionalQuestionsFieldSetHolder").hide();
                    $(".staffDefenderInformationFieldSetHolder").hide();
                    $(".lawStudentInformationFieldSetHolder").hide();
                    $(".professionalLicenseSetHolder").hide();
                    var classListArray=(classList).split(",");
                    $.each(classListArray,function(i){
                        if($.trim(classListArray[i]).length){
                            $("."+classListArray[i]).show();                            
                        }           
                    });                     
                }

                function resetFormFeilds(containerClass){
                    $("."+containerClass+" input,."+containerClass+" select,."+containerClass+" textarea").each(function(){
                         $(this).val(function() {
                            return this.defaultValue;
                        });
                    });
                }

                function validateMemberInfoForm(){
                    var _CF_this = document.forms['#variables.formName#'];
                    var arrReq = new Array();   

                    #local.applicantInformationFieldSet.jsValidation#

                    if ($('.contactType').val().length == 0) arrReq[arrReq.length] = 'Select your Member Type.';    
                
                    var memType = $('.contactType').val();
            
                    if(memType == 'Attorney')
                    {
                        <cfloop collection="#local.attorneyInformationFieldSet.STRFIELDS#" item="key">
                            <cfset local.attorneyInformationFieldSet.jsValidation = ReplaceNoCase(local.attorneyInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".attorneyInformationFieldSetHolder [name='"& key &"']"").val().length")/>
                        </cfloop>
                        #local.attorneyInformationFieldSet.jsValidation#

                        <cfloop collection="#local.additionalQuestionsFieldSet.STRFIELDS#" item="key">
                            <cfset local.additionalQuestionsFieldSet.jsValidation = ReplaceNoCase(local.additionalQuestionsFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".additionalQuestionsFieldSetHolder [name='"& key &"']"").val().length")/>
                        </cfloop>
                        #local.additionalQuestionsFieldSet.jsValidation#

                        

                        var prof_license = $('.mpl_pltypeid').val();
                        var isProfLicenseSelected = false;
                        if(prof_license != "" && prof_license != null){
                            isProfLicenseSelected = true;
                            $.each(prof_license,function(i,val){
                                var text = $(".mpl_"+val+"_licensenumber").parent().prev().text();
                                if($(".mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
                                if($(".mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
                            });
                        }   
                        if (!isProfLicenseSelected) arrReq[arrReq.length] = "Professional License is required.";
                    }
                    if(memType == 'Staff Public Defender')
                    {
                        <cfloop collection="#local.staffDefenderInformationFieldSet.STRFIELDS#" item="key">
                            <cfset local.staffDefenderInformationFieldSet.jsValidation = ReplaceNoCase(local.staffDefenderInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".staffDefenderInformationFieldSetHolder [name='"& key &"']"").val().length")/>
                        </cfloop>
                        #local.staffDefenderInformationFieldSet.jsValidation#
                        
                        var prof_license = $('.mpl_pltypeid').val();
                        var isProfLicenseSelected = false;
                        if(prof_license != "" && prof_license != null){
                            isProfLicenseSelected = true;
                            $.each(prof_license,function(i,val){
                                var text = $(".mpl_"+val+"_licensenumber").parent().prev().text();
                                if($(".mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
                                if($(".mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
                            });
                        }   
                        if (!isProfLicenseSelected) arrReq[arrReq.length] = "Professional License is required.";
                    }
                    
                    if(memType == 'Law Student')    
                    {
                        <cfloop collection="#local.lawStudentInformationFieldSet.STRFIELDS#" item="key">
                            <cfset local.lawStudentInformationFieldSet.jsValidation = ReplaceNoCase(local.lawStudentInformationFieldSet.jsValidation, "!_CF_hasValue(_CF_this['"& key &"'], ""TEXT"", false)", "!$("".lawStudentInformationFieldSetHolder [name='"& key &"']"").val().length")/>
                        </cfloop>
                        #local.lawStudentInformationFieldSet.jsValidation#
                    }                   

                    if (arrReq.length > 0) {
                        var msg = '';
                        for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                        showAlert(msg,afterFormLoad);
                        return false;
                    }

                    if(memType == 'Attorney' || memType == 'Staff Public Defender')
                    {
                        $(".lawStudentInformationFieldSetHolder").html('');             
                    }
                
                    if(memType == 'Law Student')    
                    {
                        $(".attorneyInformationFieldSetHolder").html('');
                        $(".staffDefenderInformationFieldSetHolder").html('');
                        $(".additionalQuestionsFieldSetHolder").html('');
                        $(".professionalLicenseSetHolder").html('');                        
                    }   

                    mc_continueForm($('###variables.formName#'),afterFormLoad);
                    return false;
                }

                function prefillData() {
                    var objPrefill = new Object();
                    <cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
                        #toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
                    </cfloop>
                    for (var key in objPrefill) {
                        if (objPrefill.hasOwnProperty(key)) { 
                            $('###variables.formName# ##'+key).val(objPrefill[key]);
                        }
                    }
                }

                function licenseChange(isChecked,val,text)
                {
                    $("##state_table").show();
                    if(isChecked){                              
                        $('##state_table tbody tr:last').after('<tr id="tr_state_'+val+'">'
                                                        +'<td align="right" class="tsAppBodyText">'+text+'</td>'
                                                        +'<td align="center" class="tsAppBodyText">'
                                                        +'  <input size="13" maxlength="13" name="mpl_'+val+'_licensenumber" class="mpl_'+val+'_licensenumber" type="text" value="" />'
                                                        +'  <input name="mpl_'+val+'_licensename" class="mpl_'+val+'_licensename" type="hidden" value="'+text+'" />'
                                                        +'</td>'
                                                        +'<td align="center" class="tsAppBodyText">'
                                                        +'  <input size="13" maxlength="10" name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" class="mpl_'+val+'_activeDate tsAppBodyText" type="text" value="" />'
                                                        +'</td>'
                                                        +'<td align="center" class="tsAppBodyText" style="display:none">'
                                                        +'  <select name="mpl_'+val+'_status" class="mpl_'+val+'_status"><option value="active" selected="selected">Active</option></select>'
                                                        +'</td>'

                                                +'</tr>');
                        mca_setupDatePickerField('mpl_'+val+'_activeDate');
                        $('.mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:pointer !important');                           
                    }                                   
                    else{
                        $("##tr_state_"+val).remove();                              
                    }
                    if($('##state_table tbody tr').length == 1){
                        $("##state_table").hide();
                    }   
                }

                $(document).ready(function() {
                    prefillData();
                    <cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
                        toggleFTM();
                    </cfif>
                    var contactTypeField = $('.contactType');
                    $(contactTypeField).change(adjustFieldsetDisplay);
                    adjustFieldsetDisplay(contactTypeField);
                
                    $(".mpl_pltypeid").multiselect({
                        header: true,
                        noneSelectedText: ' - Please Select - ',
                        selectedList: 1,
                        minWidth: 400,
                        click: function(event, ui){
                            licenseChange(ui.checked,ui.value,ui.text);                                         
                       },
                        uncheckAll: function(){
                            $(".mpl_pltypeid option").each(function(){
                                $('##tr_state_'+$(this).attr("value")).remove();
                            });
                            if($('##state_table tbody tr').length == 1){
                                $("##state_table").hide();
                            }   
                        },
                        checkAll: function( e ){
                            $(".mpl_pltypeid option").each(function(){
                                licenseChange(true,$(this).attr("value"),$(this).text());   
                            });
                        }
                    }); 
                });             
            </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMemberInfoForm()">
                    <input type="hidden" name="fa" id="fa" value="processMemberInfo">
                    <input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                    <input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
                    <input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
                    <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
                    <cfinclude template="/model/cfformprotect/cffp.cfm">                    
                   
                    <cfif len(variables.strPageFields.ApplicationTitle)>
                        <p><span class="TitleText">#variables.strPageFields.ApplicationTitle#</span></p>
                    </cfif>

                    <div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

                    <cfif len(variables.strPageFields.ApplicationtionIntroText)>
                        <p>#variables.strPageFields.ApplicationtionIntroText#</p>
                    </cfif> 
                    <div id="content-wrapper">
                        <div class="tsAppSectionHeading">#local.applicantInformationFieldSet.fieldSetTitle#</div>
                        <div class="tsAppSectionContentContainer" id="applicantInformationFieldSet">   
                            <table cellpadding="3" border="0" cellspacing="0" class="ContactTypeHolder">                
                                <tbody>
                                    <tr valign="top">
                                        <td class="tsAppBodyText" width="10">*&nbsp;</td>
                                        <td class="tsAppBodyText" >Member Type</td>
                                        <td class="tsAppBodyText">&nbsp;</td>
                                        <td class="tsAppBodyText">
                                            <select name="contactTypeField" class="contactType tsAppBodyText">
                                                <option value="">--- Please Select ---</option>
                                                <cfloop array="#local.contactTypeField.columnValueArr#" index="local.thisContactType">
                                                    <option value="#local.thisContactType.columnValueString#">#local.thisContactType.columnValueString#</option>>
                                                </cfloop>
                                            </select>                                         
                                        </td>
                                    </tr>
                                </tbody>
                            </table>                
                            #local.applicantInformationFieldSet.fieldSetContent#                       
                        </div>
                            
                        <span class="attorneyInformationFieldSetHolder">                        
                            <div class="tsAppSectionHeading">#local.attorneyInformationFieldSet.fieldSetTitle#</div>
                            <div class="tsAppSectionContentContainer">
                                #local.attorneyInformationFieldSet.fieldSetContent#
                            </div>
                        </span>
                        <span class="staffDefenderInformationFieldSetHolder">                        
                            <div class="tsAppSectionHeading">#local.staffDefenderInformationFieldSet.fieldSetTitle#</div>
                            <div class="tsAppSectionContentContainer">
                                #local.staffDefenderInformationFieldSet.fieldSetContent#
                            </div>
                        </span>

                        <span class="additionalQuestionsFieldSetHolder">                        
                            <div class="tsAppSectionHeading">#local.additionalQuestionsFieldSet.fieldSetTitle#</div>
                            <div class="tsAppSectionContentContainer">
                                #local.additionalQuestionsFieldSet.fieldSetContent#
                            </div>
                        </span>

                        <span class="lawStudentInformationFieldSetHolder">
                            <div class="tsAppSectionHeading">#local.lawStudentInformationFieldSet.fieldSetTitle#</div>
                            <div class="tsAppSectionContentContainer">
                                #local.lawStudentInformationFieldSet.fieldSetContent#
                            </div>
                        </span>

                        <span class="professionalLicenseSetHolder">
                            <div class="tsAppSectionHeading">Professional License Information</div>
                            <div class="tsAppSectionContentContainer">
                                <cfif len(variables.strPageFields.ProfessionalLicenseTitle)>
                                    <p class="tsAppBodyText">#variables.strPageFields.ProfessionalLicenseTitle#</p>
                                </cfif>                         
                                <table cellpadding="3" border="0" cellspacing="0">                                  
                                    <tr align="top">
                                        <td class="tsAppBodyText" width="10">*&nbsp;</td>
                                        <td class="tsAppBodyText">Professional License</td>
                                        <td class="tsAppBodyText">
                                            <select name="mpl_pltypeid" class="mpl_pltypeid" multiple="multiple">
                                                <cfloop query="local.qryOrgPlTypes">
                                                    <cfset local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
                                                    <option value="#local.qryOrgPlTypes.pltypeid#">#local.qryOrgPlTypes.PLName#</option>                                                    
                                                </cfloop>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr class="top">
                                        <td class="tsAppBodyText" width="10"></td>
                                        <td class="tsAppBodyText"></td>
                                        <td class="tsAppBodyText"></td>
                                    </tr>
                                </table>
                                <table cellpadding="3" border="0" cellspacing="0">
                                    <tr>
                                        <td>
                                            <table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
                                                <thead>
                                                    <tr valign="top">
                                                        <th align="center" class="tsAppBodyText">State Name</th>
                                                        <th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
                                                        <th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr></tr>                   
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>                   
                                </table>
                            </div>  
                        </span>             
                    </div>

                    <button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
                </form>
            </cfoutput>
        </cfsavecontent>

        <cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="processMemberInfo" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">
        <cfargument name="fieldNamesList" type="string" required="false" default="">
        <cfargument name="doNotIncludeList" type="string" required="false" default="">
        <cfset var local = structNew()>

        <cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
        <cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.strResult.memberID)>

        <!--- Member Type --->
        <cfset local.contactTypeField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>
    
        <cfif structKeyExists(arguments.rc, "contactTypeField") AND listLen(arguments.rc['contactTypeField'])>
            <cfset local.contactTypeColumnValueIDlist = "">
            <cfloop array="#local.contactTypeField.columnValueArr#" index="local.thisOption">
                <cfif listFindNoCase(arguments.rc['contactTypeField'], local.thisOption.columnValueString)>
                    <cfset local.contactTypeColumnValueIDlist = listAppend(local.contactTypeColumnValueIDlist,local.thisOption.valueID)>
                </cfif>
            </cfloop>
            <cfif listLen(local.contactTypeColumnValueIDlist)>
                <cfset local.objSaveMember.setCustomField(field='Member Type', valueID=local.contactTypeColumnValueIDlist)>
            </cfif>
        </cfif>         

        <cfset local.strResult1 = local.objSaveMember.saveData(runImmediately=1)>

        <cfif local.strResult.success>
            <cfset variables.useMID = local.strResult.memberID>

            <!--- Only add shistory if step 1 is visited for the first time --->
            <cfif not structKeyExists(session, "formFields")>
                <cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
                                                    subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
                                                    enteredByMemberID=variables.useMID, newAccountsOnly=false)> 
                <cfset session.useHistoryID = variables.useHistoryID>           
            </cfif>

            <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
                <cfset structDelete(session.formFields, "step1")>
            </cfif>         
            <cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>                                       

            <cfset local.response = "success">
        <cfelse>
            <cfset local.response = "failure">
        </cfif>

        <cfreturn local.response>
    </cffunction>

    <cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

        <cfset var local = structNew()>
    
        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(siteID=variables.siteID, uid = variables.strPageFields.subscriptionUID)>    
 
        <!--- Practice Areas --->
        <cfset local.practiceAreasField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Practice Areas')> 
        
        <cfset local.objCffp = variables.objCffp>
        <cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
        <cfset local.strData = {}>
        <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>      
            <cfset local.strData = session.formFields.step2>
        </cfif>     
    
        <cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
            subscriptionID = local.subscriptionID,
            memberID = variables.useMID,
            isRenewalRate = false,
            siteID = variables.siteID, 
            strData=local.strData
        )>  
        <cfif arguments.rc['contactTypeField'] EQ "Staff Public Defender">
            <cfset local.CDSSubId = application.objCustomPageUtils.sub_getSubscriptionFromUID(
                siteID=variables.siteID,
                uid = 'FB70F29D-27A7-4EEE-80D1-5A5D850B8640')>
        </cfif>
        
        <cfsavecontent variable="local.resultHtmlHeadText">
            <cfoutput>
                <script type="text/javascript">
                    function afterFormLoad(){
                        $('html, body').animate({ scrollTop: 0 }, 500);
                    }
                    function validateMembershipInfoForm(){
                        var arrReq = new Array();   
                        
                        <cfif val(local.subscriptionID)>
                            if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
								arrReq[arrReq.length] = " Select Membership.";
							}
                        </cfif>
                        <cfif arguments.rc['contactTypeField'] EQ "Staff Public Defender">
                            if($('input[type="radio"][name="sub#local.CDSSubId#_rate"]').length > 0 && $('input[type="radio"][name="sub#local.CDSSubId#_rate"]:checked').length == 0){
                                arrReq[arrReq.length] = " Select Section.";
                            }	
                        </cfif>

                        #local.result.jsValidation#         

                        if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";
                        
                        if (arrReq.length > 0) {
                            var msg = '';
                            for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                            showAlert(msg,afterFormLoad);
                            return false;
                        }                   

                        mc_continueForm($('###variables.formName#'),afterFormLoad);
                        return false;
                    }
                    <cfif arguments.rc['contactTypeField'] EQ "Attorney">
                        function subscriptionRadioHandler() {                       
                            $("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").prop('checked', false).attr("disabled", true);
                            $("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").prop('checked', false).attr("disabled", false);
                            $("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio][data-frequencyuid='"+$(this).attr("data-frequencyuid")+"']").attr("disabled", true).attr("disabled", false);
                        }

                        function addOnSubscriptionRadioHandler() {
                            $(this).parent().parent().parent().find('input[type="checkbox"].subCheckbox').prop('checked', true).attr("disabled", false);
                        }

                        function addOnSubscriptionCheckboxHandler() {
                            if($(this).is(':checked')){
                                $(this).parents('label').next().find(".subRateCheckbox[type=radio][data-frequencyuid='"+$("[name=sub#local.subscriptionID#_rate][type=radio]:checked").attr("data-frequencyuid")+"']").prop('checked', true);
                            }else{
                                $(this).parents('label').next().find('.subRateCheckbox[type=radio]').prop('checked', false);
                            }                       
                        }

                        $("[name=sub#local.subscriptionID#_rate][type=radio]").on('click',subscriptionRadioHandler);    
                        $("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").on('click',addOnSubscriptionRadioHandler);
                        $("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").on('click',addOnSubscriptionCheckboxHandler);

                        var selectedAddOn = $("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]:checked");                   
                        if(selectedAddOn.length==0)
                        {
                            $("##sub#local.subscriptionID#_addons .subRateCheckbox[type=radio]").prop('checked', false).attr("disabled", true);
                            $("##sub#local.subscriptionID#_addons input[type='checkbox'].subCheckbox").prop('checked', false).attr("disabled", true);
                            $("[name=sub#local.subscriptionID#_rate][type=radio]:checked").prop("checked", true).trigger("click");
                        }else{
                            $("[name=sub#local.subscriptionID#_rate][type=radio]:checked").prop("checked", true).trigger("click");
                            selectedAddOn.each(function(){
                                $(this).prop("checked", true).trigger("click");
                            }); 
                        }
                    <cfelseif arguments.rc['contactTypeField'] EQ "Staff Public Defender">
                        $('input[type="checkbox"][id="sub#local.CDSSubId#"]').prop('checked','checked');
                        $(document).on('click','input[type="checkbox"][id="sub#local.CDSSubId#"]',function(){
                            $('input[type="checkbox"][id="sub#local.CDSSubId#"]').prop('checked','checked');
                        });
                    </cfif>
                </script>
                <style type="text/css">
                    .renderSubscriptionContainer {
                        line-height: 18px;
                    }
                    .renderSubscriptionContainer legend{    
                        font-family: Verdana, Arial, Helvetica, sans-serif;
                        color: ##222;
                        background-color: ##F5F5F5;
                        padding: 10px;
                        font-size: 15px;
                        font-weight: bold;
                        margin-bottom: 12px;                        
                    }
                    .renderSubscriptionContainer label{
                        display:block;
                    }
                    .renderSubscriptionContainer .well{
                        margin-bottom: 24px;
                    }
                    .renderSubscriptionContainer .well > div, .renderSubscriptionContainer .well > label{
                        padding-left: 12px;     
                        padding-right: 12px;            
                    }
                    <cfif arguments.rc['contactTypeField'] EQ "Attorney" OR arguments.rc['contactTypeField'] EQ "Staff Public Defender"> 
                        .subAddonsArrayWrapper {
                            display:block;
                        }
                    <cfelse>
                        .subAddonsArrayWrapper {
                            display:none;
                        }
                    </cfif>
                </style>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.resultHtmlHeadText#">
                
        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
            <cfif len(variables.strPageFields.MemberTypeTitle)>
                <p><span class="TitleText">#variables.strPageFields.MemberTypeTitle#</span></p>
            </cfif>

            <cfif len(variables.strPageFields.MembershipIntroText)>
                <p>#variables.strPageFields.MembershipIntroText#</p>
            </cfif> 
            
            <cfform name="#variables.formName#" id="#variables.formName#" class="form-horizontal" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
                <cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                <cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
                <cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
                
                <cfloop collection="#session.formFields.step1#" item="local.thisField">
                    <cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
                        or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
                        or left(local.thisField,5) eq "mccf_" or local.thisField eq "contactTypeField">
                        <cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
                        <cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
                    </cfif>
                </cfloop>
                <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
                <cfinclude template="/model/cfformprotect/cffp.cfm">

                <div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
                <div class="renderSubscriptionContainer">  
                    #local.result.formcontent#
                </div>

                <cfif arguments.rc['contactTypeField'] EQ "Attorney">                   
                    <cfset local.totalCount = ArrayLen(local.practiceAreasField.columnValueArr)>    
                    <cfset local.numberofRows = Int(local.totalCount/2)+ (local.totalCount MOD 2)>
                    <div class="tsAppSectionHeading">Practice Areas</div>
                    <div class="tsAppSectionContentContainer"> 
                        <cfif len(variables.strPageFields.PracticeAreaIntroText)>
                            <p>#variables.strPageFields.PracticeAreaIntroText#</p>
                        </cfif> 
                        <table width="100%" cellpadding="3" border="0" cellspacing="0"> 
                            <cfloop from="1" to="#local.numberofRows#" index="local.currentRow" >
                                <cfset local.secondColumnCount = local.currentRow + local.numberofRows>
                                <tr>
                                    <td width="50%">
                                        <label class="checkbox subLabel" for="pa#local.currentRow#">
                                            <input class="subCheckbox" type="checkbox" name="practiceAreasField" id="pa#local.currentRow#" value="#local.practiceAreasField.columnValueArr[local.currentRow].columnValueString#"> 
                                            #trim(REReplace(local.practiceAreasField.columnValueArr[local.currentRow].columnValueString, "<\/?p[^>]*>", "", "all"))#
                                        </label>
                                    </td> 
                                    <cfif local.secondColumnCount LTE local.totalCount>
                                        <td width="50%">
                                            <label class="checkbox subLabel" for="pa#local.secondColumnCount#">
                                                <input class="subCheckbox" type="checkbox" name="practiceAreasField" id="pa#local.secondColumnCount#" value="#local.practiceAreasField.columnValueArr[local.secondColumnCount].columnValueString#"> 
												#trim(REReplace(local.practiceAreasField.columnValueArr[local.secondColumnCount].columnValueString, "<\/?p[^>]*>", "", "all"))#
                                            </label>
                                        </td> 
                                    </cfif>
                                </tr>   
                            </cfloop>
                        </table>
                    </div>
                </cfif>
                <div class="tsAppSectionContentContainer">
                    <label class="checkbox subLabel" for="mccf_verify">
                        <table width="100%" cellpadding="3" border="0" cellspacing="0"> 
                            <tr>
                                <td ><p><input class="subCheckbox" type="checkbox" name="mccf_verify" id="mccf_verify" value="1"></p></td> 
                                <td><span class="labelText">#variables.strPageFields.MembershipAgreement#</span></td> 
                            </tr>
                        </table>
                    </label>
                </div>
                <button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
                <button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button> 
            </cfform>
            </cfoutput>
        </cfsavecontent>
        <cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

        <cfset var local = structNew()>

        <!--- Updates fields if needed here  --->
        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
                siteID=variables.siteID,
                uid =variables.strPageFields.subscriptionUID)>
                
        <cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
                subscriptionID = local.subscriptionID,
                memberID = variables.useMID,
                isRenewalRate = false,
                siteID = variables.siteID,
                rc = arguments.rc)>
        
        <cfif local.strResult.success>
            <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
                <cfset structDelete(session.formFields, "step2")>
            </cfif>         
            <cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>
        
            <cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
                <cfset structDelete(session.formFields, "substruct")>
            </cfif>
            <cfset session.formFields.substruct = local.strResult.subscription>

            <cfset local.response = "success">
        <cfelse>
            <cfset local.response = "failure">
        </cfif>
    
        <cfreturn local.response>
    </cffunction>

    <cffunction name="showPayment" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">
    
        <cfset var local = structNew()>
        <cfset local.objCffp = variables.objCffp>
    
        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
                siteID=variables.siteID,
                uid =variables.strPageFields.subscriptionUID)>

        <cfset local.strResult = showSubscriptionFormSelectionsCustom(
                subscriptionID = local.subscriptionID,
                memberID = variables.useMID,
                isRenewalRate = false,
                siteID = variables.siteID,
                strdata = arguments.rc)>            
        
        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>
        <cfset local.arrPayMethods = [ variables.strPageFields.ProfileCodePayCC]>
            <cfset local.strReturn = 
                application.objCustomPageUtils.renderPaymentForm(
                    arrPayMethods=local.arrPayMethods, 
                    siteID=variables.siteID, 
                    memberID=variables.useMID, 
                    title="Choose Your Payment Method", 
                    formName=variables.formName, 
                    backStep="showMembershipInfo"
                )>
                
            <cfsavecontent variable="local.headcode">
                <cfoutput>#local.strReturn.headcode#</cfoutput>
            </cfsavecontent>
            <cfhtmlhead text="#local.headcode#">
        
    
        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
            <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
            <cfinput type="hidden" name="fa" id="fa" value="processPayment">
            <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
            <cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
            <cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
            <cfinput type="hidden" name="paymentTotal" id="paymentTotal" value="#local.strResult.totalFullPrice#">
            <cfloop collection="#arguments.rc#" item="local.thisField">
                <cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
                    or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
                    or left(local.thisField,5) eq "mccf_"
                    or left(local.thisField,3) eq "sub" or local.thisField eq "contactTypeField" or local.thisField eq "practiceAreasField" >
                    <cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
                </cfif>
            </cfloop>
            <cfinclude template="/model/cfformprotect/cffp.cfm">
            
            <style>
                div.tsAppSectionHeading{
                    font-family: Verdana, Arial, Helvetica, sans-serif;
                    color: ##222;
                    background-color: ##F5F5F5;
                    padding: 10px;
                    font-size: 15px;
                    font-weight: bold;
                }
                span.TitleText{font-size:18px!important;}   
            </style>
                <div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>
            
                <div class="tsAppSectionHeading">Membership Selections Confirmation</div>
                <div class="tsAppSectionContentContainer">                      
                    #local.strResult.formContent#
                    <br/><br/>
                </div>
                
            <div class="row-fluid">
                <div class="tsAppSectionHeading">Total Price</div><br/>
                <div class="tsAppSectionContentContainer">                      
                    #dollarFormat(local.strResult.totalFullPrice)#
                    <br/><br/>                  
                </div>
            </div>
            <cfif local.paymentRequired>
                #local.strReturn.paymentHTML#
            <cfelse>
                <button name="btnContinue" type="submit" class="tsAppBodyButton btn-default" onClick="hideAlert();">Continue</button>
                <button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton btn-default" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
            </cfif>

            </cfform>
            </cfoutput>
        </cfsavecontent>

        <cfreturn local.returnHTML>
    </cffunction>   

    <cffunction name="saveAdditionalFields" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">
        <cfargument name="event" type="any">

        <cfset var local = structNew()>     

        <cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID) >
            
        <!--- Practice Area --->
        <cfset local.practiceAreasField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Practice Areas')> 
        
        <cfif structKeyExists(arguments.rc, "practiceAreasField") AND listLen(arguments.rc['practiceAreasField'])>
            <cfset local.practiceAreasColumnValueIDlist = "">
            <cfloop array="#local.practiceAreasField.columnValueArr#" index="local.thisOption">
                <cfif listFindNoCase(arguments.rc['practiceAreasField'], local.thisOption.columnValueString)>
                    <cfset local.practiceAreasColumnValueIDlist = listAppend(local.practiceAreasColumnValueIDlist,local.thisOption.valueID)>
                </cfif>
            </cfloop>
            <cfif listLen(local.practiceAreasColumnValueIDlist)>
                <cfset local.objSaveMember.setCustomField(field='Practice Areas', valueID=local.practiceAreasColumnValueIDlist)>
            </cfif>
        </cfif>
                
        <cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>

        <cfreturn "success"/>
    </cffunction>
    
    <cffunction name="produceConfirmation" access="private" output="false" returntype="string">
        <cfargument name="rc" type="struct" required="true">

        <cfset var local = structNew()>

        <cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=session.useHistoryID, 
                    subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
                    newAccountsOnly=false)>
        
        <!--- Member Type --->
        <cfset local.contactTypeField = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnname='Member Type')>

        <!--- Applicant Information --->
        <cfset local.applicantInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='27a5e7ee-965a-4927-8b54-d9cb3e081569', mode="confirmation", strData=arguments.rc)>
        
        <!--- Attorney Information --->
        <cfset local.attorneyInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='9c7dcd29-ab7c-4c29-96b5-dc7e84ac0c22', mode="collection", strData=arguments.rc)>
        
        <!---  Staff Public Defender Information--->
        <cfset local.staffDefenderInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='96DF646C-A7DE-4E21-AB9D-AC6B7D02BF63', mode="collection", strData=arguments.rc)>
        
        <!--- Additional Questions --->
        <cfset local.additionalQuestionsFieldSet = application.objCustomPageUtils.renderFieldSet(uid='3903ABA0-D885-409D-925F-F128A45CE7C3', mode="confirmation", strData=arguments.rc)>
        
        <!--- Law Student Information --->
        <cfset local.lawStudentInformationFieldSet = application.objCustomPageUtils.renderFieldSet(uid='4724664e-712b-4ca7-825a-891f47fbafc6', mode="confirmation", strData=arguments.rc)>
        
        <cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
                siteID=variables.siteID,
                uid = variables.strPageFields.subscriptionUID)>     
    
        <cfset local.strResult = showSubscriptionFormSelectionsCustom(
                subscriptionID = local.subscriptionID,
                memberID = variables.useMID,
                isRenewalRate = false,
                siteID = variables.siteID,
                strdata = arguments.rc)>
            
        <cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >
        
        <cfset local.memberPayProfileDetail = "">
        <cfif local.paymentRequired>
            <cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
                <cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
            <cfelse>
                <cfset local.memberPayProfileSelected = 0>
            </cfif>
            <cfif local.memberPayProfileSelected gt 0>
                <cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
            </cfif>
        </cfif>
        
        <cfsavecontent variable="local.confirmationPageHTMLContent">
            <cfoutput>          
                <cfif len(variables.strPageFields.ConfirmationContent)>                 
                    <div class="tsAppSectionContentContainer">#variables.strPageFields.ConfirmationContent#</br></br></div>
                </cfif>
            </cfoutput>
        </cfsavecontent>
        
        <cfsavecontent variable="local.confirmationContentForMemberEmail">
            <cfoutput>
                <cfif len(variables.strPageFields.EmailConfirmationContent)>
                    <div class="tsAppSectionContentContainer">
                        #variables.strPageFields.EmailConfirmationContent#</br></br>
                    </div>  
                </cfif>
            </cfoutput>
        </cfsavecontent>
        
        <cfsavecontent variable="local.confirmationContentForStaff">
            <cfoutput>
                <div class="tsAppSectionContentContainer">
                    <div class="span12">You have received an application for membership through the online Iowa Association for Justice membership application form.</br></br></div>
                </div>  
            </cfoutput>
        </cfsavecontent>
        
        <cfsavecontent variable="local.confirmationHTML">
            <cfoutput>
            
            <!--@@ConfirmationContent@@-->
            <div class="confirmationDetails">
                <!--@@specialcontent@@-->

                <p>Here are the details of your application:</p><br/>
                
                #local.applicantInformationFieldSet.fieldSetContent#    

                <cfif structKeyExists(arguments.rc, "contactTypeField")>
                    <table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0"> 
                        <tbody>
                            <tr> 
                                <td style="padding:6px;">
                                    <table class="table" cellspacing="0" cellpadding="0" width="100%" border="0">
                                        <tbody>
                                            <tr valign="top">
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;width: 1%;white-space: nowrap;" nowrap="">Member Type: &nbsp;</td>
                                                <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333">
                                                    <cfif listLen(arguments.rc['contactTypeField'])>
                                                        <cfset local.contactTypelist = "">
                                                        <cfloop array="#local.contactTypeField.columnValueArr#" index="local.thisOption">
                                                            <cfif listFindNoCase(arguments.rc['contactTypeField'], local.thisOption.columnValueString)>
                                                                <cfset local.contactTypelist = listAppend(local.contactTypelist,local.thisOption.columnValueString)>
                                                            </cfif>
                                                        </cfloop>
                                                        #local.contactTypelist#
                                                    </cfif>                                         
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>                    
                    <br/>               
                    <cfif arguments.rc['contactTypeField'] EQ "Attorney">                               
                        #local.attorneyInformationFieldSet.fieldSetContent#
                        #local.additionalQuestionsFieldSet.fieldSetContent#
                    <cfelseif arguments.rc['contactTypeField'] EQ "Staff Public Defender">
                        #local.staffDefenderInformationFieldSet.fieldSetContent#
                    <cfelseif arguments.rc['contactTypeField'] EQ "Law Student">
                        #local.lawStudentInformationFieldSet.fieldSetContent#
                    </cfif>
                </cfif>     

                <table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join IAJ - Membership Selections</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <div>#local.strResult.formContent#</div>
                            <br/><br/>                          
                            <strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
                            <br/>                   
                        </td>
                    </tr>
                </table>
                <br/>
                
                <cfif local.paymentRequired>
                    <table class="table" style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
                    <tr>
                        <td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
                    </tr>
                    <tr>
                        <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
                            <cfif structKeyExists(arguments.rc,"mccf_payMeth")>
                                <table class="table" cellpadding="3" border="0" cellspacing="0">
                                <tr valign="top">
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
                                    <td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
                                        #arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
                                    </td>
                                </tr>
                                </table>
                            <cfelse>
                                None selected.
                            </cfif>
                        </td>
                    </tr>
                    </table>
                </cfif> 
            </div>  
            </cfoutput>
        </cfsavecontent>

        <!--- email to member --->
        <cfif application.MCEnvironment eq "production">
            <cfset variables.memberEmail.TO = '' >
            <cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
        </cfif>

        <cfset variables.memberEmail.subject = variables.strPageFields.ConfirmationSub>
        
        <cfset local.confirmationHTMLToMember = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContent@@-->",local.confirmationContentForMemberEmail)>
        
        <cfset local.confirmationHTMLToOnScreen = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContent@@-->",local.confirmationPageHTMLContent)>        
        
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to IAJ",
			emailhtmlcontent = local.confirmationHTMLToMember,
			siteID = variables.siteID,
			memberID = variables.useMID,
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		
        <cfset local.emailSentToUser = local.responseStruct.success>
        
        <!--- create pdf and put on member's record --->
        <cfset local.uid = createuuid()>
        <cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.rc.mc_siteinfo.sitecode)>
        <cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
            <cfoutput>
                <html>
                <head>
                
                </head>
                <body>
                    #local.confirmationHTMLToMember#
                </body>
                </html>
            </cfoutput>
        </cfdocument>
        
        <cfset local.strPDF = structNew()>
        <cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
        <cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
        <cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
        <cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
        <cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
        <cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=variables.useMID, strPDF=local.strPDF, siteID=variables.siteID)>
        <!--- email to association --->
        <cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
        <cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
        <cfsavecontent variable="local.specialText">
            <cfoutput>
            <div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
            <div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
            <cfif NOT local.emailSentToUser>
                <div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
            </cfif>
            <br/>
            </cfoutput>
        </cfsavecontent>
        <cfset local.Name = ""/>
        <cfif structKeyExists(arguments.rc, "m_firstname")>
            <cfset local.Name = arguments.rc['m_firstname']/>
        </cfif>
        <cfset local.Name = local.Name & " " />
        <cfif structKeyExists(arguments.rc, "m_lastname")>
            <cfset local.Name = local.Name & arguments.rc['m_lastname']/>
        </cfif> 
        <cfset variables.ORGEmail.subject = variables.strPageFields.StaffConfirmationSub & " - From: " & local.Name>
        <cfset local.confirmationToStaff = replaceNoCase(local.confirmationHTML,"<!--@@ConfirmationContentforEmail@@-->",local.confirmationContentForStaff)>
        <cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationToStaff,"<!--@@specialcontent@@-->",local.specialText)>
        <cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to IAJ", emailContent=local.confirmationHTMLToStaff)>

        <cfreturn local.confirmationHTMLToOnScreen>
    </cffunction>

    <cffunction name="showConfirmation" access="private" output="false" returntype="string">
        <cfargument name="confirmationHTML" type="string" required="true">

        <cfset var local = structNew()>
        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
            <div class="tsAppSectionHeading">Submission Complete.</div>
            <div class=" tsAppSectionContentContainer">                     
                #arguments.confirmationHTML#
            </div>
            </cfoutput>
        </cfsavecontent>

        <cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="showSubscriptionFormSelectionsCustom" access="public" output="false" returntype="struct">
        <cfargument name="subscriptionID" type="string" required="false">
        <cfargument name="memberID" type="numeric" required="false">
        <cfargument name="isRenewalRate" type="boolean" required="true">
        <cfargument name="siteID" type="numeric" required="true">
        <cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
    
        <cfset var local = structNew()>
    
        <cfset local.subDefinitionStruct = application.objCustomPageUtils.sub_getSubscriptionTreeStruct(
            subscriptionID = arguments.subscriptionID,
            memberID = arguments.memberID,
            isRenewalRate = arguments.isRenewalRate,
            siteID = arguments.siteID)>
    
        <cfreturn getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)>
    </cffunction>
    
    <cffunction name="getSubscriptionFormSelectionsCustom" access="private" output="false" returntype="struct">
        <cfargument name="subDefinitionStruct" type="struct" required="false">
        <cfargument name="recursionLevel" type="numeric" required="false" default="1">
        <cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
        <cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">
    
        <cfset var local = structNew()>
        <cfset local.strReturn = { jsValidation='', formContent='', formTitle='', totalFullPrice = 0 }>
    
        <cfif arguments.recursionLevel eq 1>
            <cfset local.subdivclass = "">
        <cfelse>
            <cfset local.subdivclass = "">          
        </cfif>
        <cfsavecontent variable="local.strReturn.formContent">
            <cfoutput>
                <cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
                    <div>
                        <div class="#local.subdivclass#">
                            <cfif arguments.recursionLevel eq 1>
                                <div>
                                    <strong>#arguments.subDefinitionStruct.typename#</strong>
                                </div>
                            </cfif>
                            #arguments.subDefinitionStruct.subscriptionName#
                            <span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
                                - 
                                <cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
                                    <cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
                                        <!--- #local.thisRate.rateName#  --->                                       
                                        <cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
                                            #local.thisRate.rateName#  (<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> Full)
                                            <cfset local.strReturn.totalFullPrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
                                        <cfelse>
                                           <cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
                                                <cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid )>
                                                    <cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
                                                    <cfif arguments.recursionLevel eq 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>                                                                                                    
                                                        #local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
                                                    <cfelseif arguments.recursionLevel gt 1>                                                        
                                                        #local.thisRate.rateName# <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>                                               
                                                    </cfif> 
                                                </cfif>                         
                                            </cfloop>                                           
                                        </cfif>
                                    </cfif>
                                </cfloop>
                            </span>
                        </div>
                    </div>  
                    <cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
                        <div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
                            <cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">     
                                <div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
                                    <strong>#local.thisAddon.setName#</strong>
                                    <div class="addonMessageArea"></div>
                                    <cfset local.selectionFound = false />                          
                                    <cfset local.pcNumCounter = 1 />                                    
                                    <cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
                                        <cfset local.isFree = false />
                                        <cfif local.thisAddon.PCnum gte local.pcNumCounter>
                                            <cfset local.isFree = true />
                                        </cfif>                                     
                                        <cfset local.thisAddonSubForm = getSubscriptionFormSelectionsCustom(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree) />
    
                                        <cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation />
                                        <cfif len(trim(local.thisAddonSubForm.formContent))>                                        
                                            <cfset local.selectionFound = true />
                                            <cfif local.pcNumCounter gt local.thisAddon.PCnum>
                                                <cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice />
                                            </cfif>
    
                                            <cfset local.pcNumCounter = local.pcNumCounter + 1 />                                           
                                            #local.thisAddonSubForm.formContent#
                                        </cfif>                                 
                                    </cfloop>
                                    <cfif local.selectionFound eq false>
                                        No Selections Made
                                    </cfif>
                                </div>
                            </cfloop>                       
                        </div>
                    </cfif>                 
                </cfif>
            </cfoutput>             
        </cfsavecontent>
    
        <cfreturn local.strReturn>
    </cffunction>
    
    <cffunction name="showError" access="private" output="false" returntype="string">
        <cfargument name="errorCode" type="string" required="true">

        <cfset var local = structNew()>

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>  
                <div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
                <div class="tsAppSectionContentContainer">                      
                    <cfif arguments.errorCode eq "activefound">     
                        #variables.strPageFields.MemberActive#
                    <cfelseif arguments.errorCode eq "acceptedfound">
                        #variables.strPageFields.MemberActive#
                    <cfelseif arguments.errorCode eq "billedjoinfound">
						#variables.strPageFields.BilledJoinMessage#	
                    <cfelseif arguments.errorCode eq "billedfound">
                        #variables.strPageFields.MemberBilled#
                        <script type="text/javascript">
                            setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
                            function AJAXRenewSub(member){
                                var redirect = function(r) {
                                    redirectLink = '/renewsub/' + r.data.directlinkcode[0];
                                    window.location = redirectLink;                             
                                };      
                                
                                var params = { memberID:member, status:'O', distinct:false };
                                TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
                            }                       
                        </script>
                    <cfelseif arguments.errorCode eq "failsavemember">
                        We were unable to save the member information provided. Please contact the association or try again later.
                    <cfelseif arguments.errorCode eq "failsavemembership">
                        We were unable to process the membership information provided. Please contact the association or try again later.
                    <cfelseif arguments.errorCode eq "failpayment">
                        We were unable to process your selected payment method. Please contact the association or try again later.
                    <cfelseif arguments.errorCode eq "spam">
                        Your submission was blocked and will not be processed at this time.
                    <cfelse>
                        An error occurred. Please contact the association or try again later.
                    </cfif>
                </div>
            </cfoutput>
        </cfsavecontent>
        <cfreturn local.returnHTML>
    </cffunction>
  </cfcomponent>