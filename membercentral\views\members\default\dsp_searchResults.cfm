<cfset local.strData = attributes.data>
<cfset local.searchTermList = []>
<cfset local.searchTermListStruct = {}>
<cfif StructKeyExists(local.strData,"arrSearchCriteria") AND ArrayLen(local.strData.arrSearchCriteria) >
	<cfloop from="1" to="#arrayLen(local.strData.arrSearchCriteria)#" index="local.thisItem">
		<cfset StructInsert(local.searchTermListStruct, 
			"#lcase(local.strData.arrSearchCriteria[local.thisItem].fieldlabel.replaceall(' '  ,'_'))#", trim(local.strData.arrSearchCriteria[local.thisItem].fieldValue),true
		)>
	</cfloop>
	<cfset arrayAppend(local.searchTermList,local.searchTermListStruct)>
</cfif>
<cfsavecontent variable="local.css">
	<style type="text/css">
	ul.groupImage { width:100%; text-align:left; float:left; padding:0; padding-left:0; }
	ul.groupImage li.groupImage { float:left; padding:0; padding-left:0; list-style-type:none; list-style-position:inside; }
	</style>
</cfsavecontent>
<cfsavecontent variable="local.searchResultsJS">
<cfoutput>
	<script language="javascript">
		$(function() {				
			<cfset local.searchTermsJSON = SerializeJSON(local.searchTermList)>
			<cfif IsJSON(local.searchTermsJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/members/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggersearchResultsList('#local.strData.pageName#',#local.searchTermsJSON#);						
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
	</script>
</cfoutput>	
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.searchResultsJS)#">	
<cfhtmlhead text="#local.css#">

<cfoutput>
<div class="tsAppBodyText">
	<span style="float:right;">
		<a href="/?#local.strData.baseQueryString#&dirAction=search">New Search</a>
		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			| <a href="/?pg=updateMember">Edit Your Information</a>
		</cfif>
	</span>
	<span class="tsAppHeading">#local.strData.memberDirectoryInfo.applicationInstanceName# Search Results</span>
</div>
<br/>

<cfif Arraylen(local.strData.arrSearchCriteria)>
	<div class="tsAppBT tsAppBodyText" style="padding:6px;">
		<b>Search Criteria</b>
		<cfif Arraylen(local.strData.arrSearchCriteria) gt 2> 
		    <div class="more-less">	
			<div class="more-block">
		<cfelse>
			<div>
		</cfif>
		<cfloop array="#local.strData.arrSearchCriteria#" index="local.x">
			#local.x.fieldLabel##local.x.fieldseparator#<i>#local.x.fieldValue#</i><br/>
		</cfloop>
		<cfif Arraylen(local.strData.arrSearchCriteria) gt 2> 
			</div>
			</div>
		<cfelse>
			</div>
		</cfif>
	</div>
</cfif>
</cfoutput>

<cfif local.strData.qryMembersCount is 0>
	<cfoutput>
	<div class="tsAppBodyText"><b>Sorry, there are no results based on your search criteria.</b></div><br/>
	<div class="tsAppBodyText"><a href="/?#local.strData.baseQueryString#&dirAction=search">Click here</a> to search again.</div>
	</cfoutput>
<cfelse>
	<cfoutput>

	<cfif isdefined("local.strData.overlimitmessage")>
		<div class="tsAppBT tsAppBodyText" style="padding:6px;">#local.strData.overlimitmessage#</div>
	</cfif>

	<cfif (local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0) OR ((local.strData.prevPage NEQ 0) OR (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount))>
		<div class="tsAppBT tsAppBodyText" style="padding:15px 6px;height:26px;">
			<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
				<button type="button" class="tsAppBodyButton" id="btnCompare" onClick="runCompare();">Compare Listings (#local.strData.numInCompare#)</button>
				<span id="spanCompMsg" class="tsAppBodyTextImportant"></span>
			</cfif>
			<cfif ((local.strData.prevPage NEQ 0) OR (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount))>
				<span style="float:right;">
					<cfif local.strData.prevPage NEQ 0>
						<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.prevPage#"><< Previous Page</a>
					</cfif>
					<cfif (local.strData.prevPage NEQ 0) AND (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount)> | </cfif>
					<cfif ((local.strData.nextPage-1)*local.strData.rowsize + 1) lte local.strData.qryMembersCount>
						<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.nextPage#">Next Page &gt;&gt;</a>
					</cfif>
				</span>
			</cfif>
		</div>
	</cfif>

	<table cellpadding="6" cellspacing="0" border="0" width="100%"> 
	<cfloop array="#local.strData.arrMembers#" index="local.thisMember">
		<cfset local.thisMemberPerms = local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID]>
		<cfif local.thisMemberPerms.resultsShowDetailsLink and (
				local.strData.resultDetailDisplayDifferences.isAnythingDifferent
				or local.strData.memberDirectoryInfo.includeContactFormOnDetails is 1
				or (local.thisMemberPerms.resultsShowVcard neq local.thisMemberPerms.detailsShowVcard) 
				or (local.thisMemberPerms.resultsShowPhoto neq local.thisMemberPerms.detailsShowPhoto)
				or (local.thisMemberPerms.resultsShowMap neq local.thisMemberPerms.detailsShowMap)
			)>
			<cfset local.thisMemberShowDetailLink = true>
		<cfelse>
			<cfset local.thisMemberShowDetailLink = false>
		</cfif>
		<tr valign="top">

			<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowPhoto>
				<td width="82" class="tsAppBT20">
					<cfif arrayLen(local.thisMember.arrAboveClassifications)>
						<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
							<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
						</cfloop>
					</cfif>

					<!--- replaced code block with <cfset local.imgToUse = local.thisMember.imgToUse> --->
					<cfset local.imgToUse = local.thisMember.imgToUse>
					
					<cfif len(trim(local.imgToUse))>#local.imgToUse#</cfif>
					
					<cfif arrayLen(local.thisMember.arrBelowClassifications)>
						<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
							<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
						</cfloop>
					</cfif>
				</td>
			<cfelse>
				<td width="1" class="tsAppBT20">
					<cfif arrayLen(local.thisMember.arrAboveClassifications)>
						<cfloop array="#local.thisMember.arrAboveClassifications#" index="local.thisImg">
							<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
						</cfloop>
					</cfif>
					<cfif arrayLen(local.thisMember.arrBelowClassifications)>
						<cfloop array="#local.thisMember.arrBelowClassifications#" index="local.thisImg">
							<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
						</cfloop>
					</cfif>
					<cfif arrayLen(local.thisMember.arrAboveClassifications) eq 0 AND arrayLen(local.thisMember.arrBelowClassifications) eq 0>
						&nbsp;
					</cfif>
				</td>
			</cfif>

			<td class="tsAppBodyText tsAppBT20" width="100%">
				<!--- Name/company --->
				<cfif len(local.thisMember.stFullName)>
					<div class="tsAppMemberDirectoryUnlinkedName"><b>#local.thisMember.stFullName#</b></div>
				</cfif>
				<cfif not len(local.thisMember.stFullName) and len(local.thisMember.stCompany)>
					<div class="tsAppMemberDirectoryUnlinkedName"><b>#local.thisMember.stCompany#</b></div>
				<cfelseif len(local.thisMember.stCompany)>
					<div>#local.thisMember.stCompany#</div>
				</cfif>

				<!--- addresses --->
				<cfif arrayLen(local.thisMember.mc_combinedAddresses)>
					<cfloop index="local.addri" from="1" to="#arrayLen(local.thisMember.mc_combinedAddresses)#">
						<cfif len(local.thisMember.mc_combinedAddresses[local.addri].addr)>
							<br/>
							<cfif local.strData.multipleAddressTypesDetected>
								<strong>#local.thisMember.mc_combinedAddresses[local.addri].label#</strong><br/>
							</cfif>
							#local.thisMember.mc_combinedAddresses[local.addri].addr#
						</cfif>	
					</cfloop>
					<br/>
				</cfif>
				
				<!--- phones --->
				<cfloop array="#local.thisMember.arrPhones#" index="local.thisField">
					<cfif len(local.thisField.phone) and local.strData.makePhoneNumbersClckable>
						#htmlEditFormat(local.thisField.fieldLabel)#: <a href="tel:#local.thisField.phone#">#local.thisField.phone#</a><br/>
					<cfelseif len(local.thisField.phone)>
						#htmlEditFormat(local.thisField.fieldLabel)#: #local.thisField.phone#<br/>
					</cfif>
				</cfloop>

				<!--- emails --->
				<cfloop array="#local.thisMember.arrEmails#" index="local.thisField">
					<cfif len(local.thisField.email)>
						#htmlEditFormat(local.thisField.fieldLabel)#: <a href="mailto:#local.thisField.email#">#local.thisField.email#</a><br/>
					</cfif>
				</cfloop>

				<!--- websites --->
				<cfloop array="#local.thisMember.arrWebsites#" index="local.thisField">
					<cfif len(local.thisField.website)>
						#htmlEditFormat(local.thisField.fieldLabel)#: <a href="#local.thisField.website#" target="_blank">#local.thisField.website#</a><br/>
					</cfif>
				</cfloop>

				<!--- website social icons --->
				<cfif ArrayLen(local.thisMember.arrWebsitesSocialIcons) gt 0 OR
						local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowVcard or 
							(local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr))>
					<div style="padding:2px;">
						<cfloop array="#local.thisMember.arrWebsitesSocialIcons#" index="local.thisField">
								#local.thisField.socialLink#
						</cfloop>

						<!--- VCARD / Google Maps link --->
						<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowVcard>
							<a href="/?event=cms.showResource&dirAction=memberDetailsVCard&resID=#local.strData.instanceSettings.siteResourceID#&mode=stream&dirMemberid=#local.thisMember.memberID#">
								<img src="/assets/common/images/vcard-icon3.png" alt="[vCard]" width="32" height="32" border="0"></a>
						</cfif>
						<cfif local.strData.MEMBERPERMISSIONS[local.thisMember.qrymember.groupPrintID].resultsShowMap and arrayLen(local.thisMember.mc_combinedAddresses) and len(local.thisMember.mc_combinedAddresses[1].mapaddr)>
							<cfset local.thisMap = local.strData.mappingBaseLink & URLEncodedFormat(local.thisMember.mc_combinedAddresses[1].mapaddr)>
							<a href="#local.thisMap#" target="_blank"><img src="/assets/common/images/Maps-icon.png" alt="[View Map]" width="32" height="32" border="0"></a>
						</cfif>
					</div>
				</cfif>

				<!--- membernumber --->
				<cfif len(local.thisMember.stMemberNumber)>#local.thisMember.stMemberNumber#<br/></cfif>

				<!--- memberdata / districting --->
				<cfloop array="#local.thisMember.arrMemberDataDistricting#" index="local.thisField">
					<cfif len(local.thisField.value)>
						#htmlEditFormat(local.thisField.fieldLabel)#: 
							<cfif local.thisField.allowMultiple is 1>
								#ReplaceNoCase(local.thisField.value,"|",", ","ALL")#<br/>
							<cfelseif local.thisField.dataTypeCode EQ "DOCUMENTOBJ">
								<cfset local.stDocEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.thisField.columnID#|#right(GetTickCount(),5)#|#local.thisMember.memberID#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
								<cfoutput><a href="/?event=cms.showResource&dirAction=memberResultsDocDownload&resID=#attributes.data.instanceSettings.siteResourceID#&mode=stream&doc=#local.stDocEnc#" target="_blank">#local.thisField.value#</a></cfoutput>
							<cfelse>
								#local.thisField.value#<br/>
							</cfif>
					</cfif>
				</cfloop>

				<!--- member prof license data --->
				<cfloop array="#local.thisMember.arrLicenseData#" index="local.thisField">
					<cfif len(local.thisField.value)>
						#htmlEditFormat(local.thisField.fieldLabel)#: #local.thisField.value#<br/>
					</cfif>
				</cfloop>

				<cfif local.thisMemberShowDetailLink>
					<button type="button" onclick="self.location.href='/?#local.strData.baseQueryString#&dirAction=memberDetails&dirMemberid=#local.thisMember.memberID#';">View Full Profile</button>
				</cfif>
			</td> 
		
			<td class="tsAppBodyText tsAppBT20" width="250">
				<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
					<div style="margin-bottom:12px;">
						<button type="button" class="tsAppBodyButton" onclick="toggleCompare($(this),#local.thisMember.memberID#);"><i class="<cfif local.thisMember.inCompare>icon-check<cfelse>icon-check-empty</cfif>"></i> Add to Compare</button>
					</div>
				</cfif>

				<cfif arrayLen(local.thisMember.arrClValues)>
					<cfset local.clTitle = "">
					<cfloop array="#local.thisMember.arrClValues#" index="local.thisClItem">
						<cfif local.clTitle neq local.thisClItem.clName>
							<cfif local.clTitle neq ""><br/></cfif>
							<cfset local.clTitle = local.thisClItem.clName>
							<b>#local.thisClItem.clName#</b><br>
						</cfif>
						#local.thisClItem.groupName#<br/>
					</cfloop>
					<br/>
				</cfif>
				&nbsp;
				<cfif arrayLen(local.thisMember.arrUnderClassifications)>
					<ul class="groupImage">
					<cfloop array="#local.thisMember.arrUnderClassifications#" index="local.thisImg">
						<div align="left"><img src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
					</cfloop>
					</ul>
				</cfif>

				<img src="/assets/common/images/spacer.gif" width="250" height="1" />
			</td> 
		</tr>
	</cfloop>
	
	</table>

	<cfif (local.strData.prevPage NEQ 0) OR (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount)>
		<div class="tsAppBT tsAppBodyText" style="padding:6px;text-align:right;">
			<cfif local.strData.prevPage NEQ 0>
				<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.prevPage#"><< Previous Page</a>
			</cfif>
			<cfif (local.strData.prevPage NEQ 0) AND (((local.strData.nextPage-1)*local.strData.rowsize + 1) LTE local.strData.qryMembersCount)> | </cfif>
			<cfif ((local.strData.nextPage-1)*local.strData.rowsize + 1) lte local.strData.qryMembersCount>
				<a href="#local.strData.linkForPaging#&memPageNum=#local.strData.nextPage#">Next Page &gt;&gt;</a>
			</cfif>
		</div>
	</cfif>

	</cfoutput>				

	<cfsavecontent variable="local.jsFunc">
		<cfoutput>
		<script language="javascript">		
			$(function(){
				// The height of the content block when it's not expanded
				var adjustheight = 28;
				// The "more" link text
				var moreText = "+  More";
				// The "less" link text
				var lessText = "- Less";
			
				// Sets the .more-block div to the specified height and hides any content that overflows
				$(".more-less .more-block").css('height', adjustheight).css('overflow', 'hidden');
				
				// The section added to the bottom of the "more-less" div
				$(".more-less").append('<a href="##" class="adjust"></a>');
				
				$("a.adjust").text(moreText);
				
				$(".adjust").toggle(function() {
					$(this).parents("div:first").find(".more-block").css('height', 'auto').css('overflow', 'visible');
					// Hide the [...] when expanded
					$(this).parents("div:first").find("p.continued").css('display', 'none');
					$(this).text(lessText);
					}, function() {
						$(this).parents("div:first").find(".more-block").css('height', adjustheight).css('overflow', 'hidden');
						$(this).parents("div:first").find("p.continued").css('display', 'block');
						$(this).text(moreText);
					});
			});
		
			<cfif local.strData.memberDirectoryInfo.enableCompare and local.strData.runningCompare is 0>
				<cfif local.strData.numInCompare lte 1>
					runCompareOK = false;
				<cfelse>
					runCompareOK = true;
				</cfif>
				function toggleCompare(btn,mid) {
					btn.html('<i class="icon-spinner icon-spin"></i> Add to Compare').attr('disabled',true);
			        $.ajax({
			            url: '#local.strData.linkForToggleCompare#&dirMemberID='+mid,
			            dataType: 'json',
			            success: function(response){ 
			            	if (response.SUCCESS == true) {
			            		if (response.MEMINCOMP == true) btn.html('<i class="icon-check"></i> Add to Compare').attr('disabled',false); 
		            			else btn.html('<i class="icon-check-empty"></i> Add to Compare').attr('disabled',false); 

			            		$('##btnCompare').html('Compare Listings (' + parseInt(response.NUMINCOMP) + ')');

				            	if (parseInt(response.NUMINCOMP) > 1)
				            		runCompareOK = true;
				            	else
				            		runCompareOK = false;

				            	$('##spanCompMsg').html('');
			            	} else {
			            		btn.attr('disabled',false);
			            		$('##spanCompMsg').html('');
			            	}	
			            },
			            error: function(ErrorMsg){ btn.attr('disabled',false);$('##spanCompMsg').html(''); }
			        })
				}
				function runCompare() {
					if (runCompareOK) {
						var #ToScript(local.strData.linkForCompare,'link')#
						$('##spanCompMsg').html('');
						self.location.href = link;
					} else {
						$('##spanCompMsg').html('You must select at least 2 listings to compare.');
					}
				}
			</cfif>
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.jsFunc#">
	
</cfif>
