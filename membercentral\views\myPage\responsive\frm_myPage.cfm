﻿<cfset local.myPageData = attributes.data>
<cfset local.settingsObj = local.myPageData.OBJSETTINGS>
<cfset local.objGrpMsgCnt = local.myPageData.objGrpMsgCnt>
<cfset local.objBoxTabCnt = local.myPageData.objBoxTabCnt>

<cfsavecontent variable="local.myPageFrmJS">
	<cfoutput>
		<style type="text/css">
			##myPage .myInfo .HeaderText{color: ##444444;font-size:25px;}
			##myPage .myInfo li{padding-top:5px;cursor:pointer;}
			##myPage .myInfo li a{color:##444444;}
			##myPage .profileWrap li a {font-size:15px;}
			##myPage .profileWrap li a:hover {color: ##618597;}
			##myPage .welcomeMessageWrap{
				padding-bottom: 15px;
			}
			.profileWrap{
				margin-bottom:20px;
			}
			##myPage .myInfo ul {
				margin-top: 2px;
			}
			##myPage  .nav-tabs{
				list-style: none;
			}
			##myPage  .nav-tabs > .active > a, ##myPage  .nav-tabs>.active>a:hover {
				font-weight: normal;
				text-decoration: none;
				cursor:default;
				font-size: 16px;
			}
			##myPage  .nav-tabs li.active a {
				text-decoration: none!important;
			}
			##myPage  .nav-tabs>li>a, ##myPage  .nav-pills>li>a {
				padding: 7px 7px 7px 7px!important;
			}
			##myPage  .nav-tabs > li > a {
				border: 1px solid transparent;
				border-radius: 4px 4px 0 0;
				line-height: 1.42857;
				margin-right: 2px;
			}
			##myPage  .tab-content {
				border: 2px solid ##ddd;
				padding: 10px;
				margin-bottom: 20px;
				background: ##fff;
				min-height: 250px;
				max-height: 250px;
				overflow: auto;
			}
			##myPage  .tab-content .tab-pane {
				min-height: 210px;
				max-height: 250px;
				overflow-y: visible!important;
			}
			##myPage  .nav {
				margin-bottom: 0px;
			}
			##myPage  .nav-tabs li {
				margin-bottom: 0!important;
			}
			##myPage  .nav-tabs>li:last-child>a {
				margin-right: auto!important;;
			}
			##myPage  .nav-tabs a, ##myPage  .nav-tabs a:hover {
				font-weight: normal;
				text-decoration: none;
				font-size: 16px;
			}
			##myPage  .nav-tabs{
				border-bottom: 0px;
			}
			##myPage  .carousel-inner .item p {
				float: left;
				display: table;
				width: 100%;
				margin-top: 27px;
			}
			##myPage  .carousel{
				margin-bottom: 0!important;
			}
			##myPage   .carousel-inner .item p a{
				display: table-cell;
				vertical-align: middle;
			}
			##myPage .carousel,##myPage .carousel div {
				height:225px;
			}
			##myPage .carousel div.item {
				height: 225px;
				position: relative;
			}
			##myPage .carousel div.item img{
				position: absolute;
				left: 49%;
				top: 48%;
				transform: translate(-50%, -50%);
			}
			.renewTextWrap {
				width: 100%;
				border: 1.4px solid ##2a4161;
				padding: 10px;
				margin-bottom: 16px;
				text-align: center;
			}
			##myPage .tab-pane .icon-spinner{width:1.3em;height:1.3em;line-height:1.3em}
			@media screen and (min-width: 768px)  and (max-width: 1700px) {
				##myPage .span1.myPhoto{
					width: 10.3646408839779% !important;
				}
			}
			
		</style>
		<script>
			var arrLoadedTabs = [];
			function renderMyPageTabContent(b,t) {
				if (!arrLoadedTabs.includes(t))
					$('##box'+b+'_'+t).load('#local.myPageData.tabContentURL#&tabid='+t, function() { 
						let injectedMergeTemplate = $('.mcMergeTemplate', this);
						mcExecuteSingleMergeTemplate(injectedMergeTemplate);
						arrLoadedTabs.push(t);
					}).show();
			}

			$(document).ready(function() {
				$("head").append('<style type="text/css"></style>');
				var newStyleElement = $("head").children(':last');
				newStyleElement.html('.tabLi.active a{background:#local.settingsObj.activeTabBgColor# !important;color:#local.settingsObj.activeTabTextColor#  !important;} .tabLi:active a,.tabLi:focus a, .tabLi a{background:#local.settingsObj.inactiveTabBgColor# !important;color:#local.settingsObj.inactiveTabTextColor# !important;}');
				$('##myPage a[data-toggle="tab"]').on('shown.bs.tab', function(e){
					var boxid = $(e.target).parent().data("boxid");
					var tabid = $(e.target).parent().data("tabid");
					renderMyPageTabContent(boxid,tabid);
				});
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.myPageFrmJS#" />

<cfoutput>	
	<div id="myPage">		
		<cfloop query="local.objGrpMsgCnt">
			<div class="row-fluid renewTextWrap groupMessageWrap">#local.objGrpMsgCnt.rawContent#</div>
		</cfloop>
		
		<div class="row-fluid welcomeMessageWrap">	
			#local.settingsObj.welcomeMessage#
		</div>
		
		<div class="row-fluid profileWrap">			
			<div class="span1 myPhoto">					
				<cfif attributes.data.hasMemberPhotoThumb is 1>
					<img src="/memberphotosth/#LCASE(attributes.data.memberNumber)#.jpg?cb=#getTickCount()#" width="100px" >
				<cfelse>
					<img src="/assets/common/images/directory/default.jpg" width="100px" >
				</cfif>
			</div>
			<div class="span5 myInfo" style="margin-top: 5px;">
				<span class="BodyText profileLinksWrap">
					#local.settingsObj.profileLinks#
				</span>
			</div>
		</div>	
		<cfset local.boxIdx = 0>
		<cfset local.currentBoxId = 0>
		<cfset local.prevBoxId = 0>
		<cfset local.newRow = 1>
		<cfset local.arrBoxTabId = ArrayNew(1)>
		<cfset local.arrBoxTabName = ArrayNew(1)>
		<cfset local.arrBoxTabContent = ArrayNew(1)>
		<cfset local.arrBoxTabIsCarousel = ArrayNew(1)>
		<cfset local.arrBoxTabCarouselInterval = ArrayNew(1)>
		
		<cfloop query="#local.objBoxTabCnt#">

			<cfif local.objBoxTabCnt.myPageBoxId neq local.currentBoxId>
				<cfif local.boxIdx % 3 eq 0>
					<cfif local.boxIdx eq 0>
						<div class="row-fluid">
					</cfif>						
				</cfif>
				<cfif local.boxIdx neq 0>					
					<div class="span4">
						<ul class="nav nav-tabs" id="box#local.currentBoxId#">
							<cfloop array="#local.arrBoxTabName#" index="local.idx"  item="local.tabName" >
								<li class="tabLi <cfif local.idx eq 1 >active</cfif>" data-boxid="#local.currentBoxId#" data-tabid="#local.arrBoxTabId[local.idx]#" data-tab="box#local.currentBoxId#_#local.idx#">
									<a href="##box#local.currentBoxId#_#local.idx#" data-toggle="tab">#local.tabName#</a>
								</li>
							</cfloop>							
						</ul>
						<div class="tab-content tabCntWrap">
							<cfloop array="#local.arrBoxTabContent#" index="local.idx" item="local.tabCnt" >
								<div class="tab-pane <cfif local.idx eq 1 >active</cfif>" id="box#local.currentBoxId#_#local.idx#">
									<cfif local.arrBoxTabIsCarousel[local.idx]>
										<div id="carousel_#local.currentBoxId#_#local.idx#" class="carousel slide text-center">
											<div class="carousel-inner text-center">
												<cfif len(trim(local.tabCnt))>
													<cfloop list="#local.tabCnt#" index="local.thisImage" delimiters="|">
														<div class="<cfif ListFirst(local.tabCnt,'|') eq local.thisImage>active </cfif>item">
															<p><a href="javascript:void(0);" >#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#</a></p>
														</div>
													</cfloop>
												</cfif>
											</div>
										</div>
										<cfif len(trim(local.tabCnt))>
											<script type='text/javascript'>
												$(document).ready(function() {
													$('##carousel_#local.currentBoxId#_#local.idx#').carousel({
														interval: #local.arrBoxTabCarouselInterval[local.idx]#
													})
												});    
											</script>
										</cfif>
									<cfelse>
										<div id="box#local.currentBoxId#_#local.arrBoxTabId[local.idx]#" class="hide"><i class="icon-spinner icon-spin icon-large"></i> Loading...</div>
										<cfif local.idx eq 1>
										<script>
										$(document).ready(function(){ renderMyPageTabContent(#local.currentBoxId#,#local.arrBoxTabId[local.idx]#); });
										</script>
										</cfif>
									</cfif>
								</div>
							</cfloop>	
						</div>
					</div>
					<cfif local.boxIdx % 3 eq 0>
						<cfif local.boxIdx neq 0>
							</div>
							<div class="row-fluid">	
						</cfif>
					</cfif>
					<cfset ArrayClear(local.arrBoxTabId)>
					<cfset ArrayClear(local.arrBoxTabName)>
					<cfset ArrayClear(local.arrBoxTabContent)>
					<cfset ArrayClear(local.arrBoxTabIsCarousel)>
					<cfset ArrayClear(local.arrBoxTabCarouselInterval)>
				</cfif>

				<cfset local.boxIdx =  local.boxIdx + 1>
			</cfif>

			<cfset ArrayAppend(local.arrBoxTabId, local.objBoxTabCnt.myPageBoxTabId)>
			<cfset ArrayAppend(local.arrBoxTabName, local.objBoxTabCnt.tabTitle)>
			<cfset ArrayAppend(local.arrBoxTabContent, local.objBoxTabCnt.rawContent)>
			<cfset ArrayAppend(local.arrBoxTabIsCarousel, local.objBoxTabCnt.isCarousel)>
			<cfset ArrayAppend(local.arrBoxTabCarouselInterval, local.objBoxTabCnt.carouselInterval)>
			<cfset local.currentBoxId = local.objBoxTabCnt.myPageBoxId>
		</cfloop>

		<cfif local.boxIdx neq 0>
				<div class="span4">
					<ul class="nav nav-tabs" id="box#local.currentBoxId#">
						<cfloop array="#local.arrBoxTabName#" index="local.idx"  item="local.tabName" >
							<li class="tabLi <cfif local.idx eq 1 >active</cfif>" data-boxid="#local.currentBoxId#" data-tabid="#local.arrBoxTabId[local.idx]#" data-tab="box#local.currentBoxId#_#local.idx#">
								<a href="##box#local.currentBoxId#_#local.idx#" data-toggle="tab">#local.tabName#</a>
							</li>
						</cfloop>							
					</ul>
					<div class="tab-content tabCntWrap">
						<cfloop array="#local.arrBoxTabContent#" index="local.idx"  item="local.tabCnt" >
							<div class="tab-pane <cfif local.idx eq 1 >active</cfif>" id="box#local.currentBoxId#_#local.idx#">
								<cfif local.arrBoxTabIsCarousel[local.idx]>
									<div id="carousel_#local.currentBoxId#_#local.idx#" class="carousel slide text-center">
										<div class="carousel-inner text-center">
											<cfif len(trim(local.tabCnt))>
												<cfloop list="#local.tabCnt#" index="local.thisImage" delimiters="|">
													<div class="<cfif ListFirst(local.tabCnt,'|') eq local.thisImage>active </cfif>item">
														<p><a href="javascript:void(0);" >#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#</a></p>
													</div>
												</cfloop>
											</cfif>
										</div>
									</div>
									<cfif len(trim(local.tabCnt))>
										<script type='text/javascript'>
											$(document).ready(function() {
												$('##carousel_#local.currentBoxId#_#local.idx#').carousel({
													interval: #local.arrBoxTabCarouselInterval[local.idx]#
												})
											});    
										</script>
									</cfif>
								<cfelse>
									<div id="box#local.currentBoxId#_#local.arrBoxTabId[local.idx]#" class="hide"><i class="icon-spinner icon-spin icon-large"></i> Loading...</div>
									<cfif local.idx eq 1>
									<script>
									$(document).ready(function(){ renderMyPageTabContent(#local.currentBoxId#,#local.arrBoxTabId[local.idx]#); });
									</script>
									</cfif>
								</cfif>
							</div>
						</cfloop>	
					</div>
				</div>
			</div>
		</cfif>
	</div>
</cfoutput>