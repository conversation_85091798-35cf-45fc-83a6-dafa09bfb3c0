<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<div style="padding-bottom:10px;">
	<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
</div>
</cfoutput>
<cfif local.qryResults.recordcount EQ 0>
	<cfoutput>
		#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#
	</cfoutput>
<cfelse>
	<cfset local.tmpSort = ArrayNew(1)>
	<cfset ArrayAppend(local.tmpSort,"rank|Relevance")>
	<cfset ArrayAppend(local.tmpSort,"date|Date")>
	<cfset ArrayAppend(local.tmpSort,"case|Case")>
	<cfset ArrayAppend(local.tmpSort,"witness|Witness")>

	<cfoutput>#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='document', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=arguments.sortType, viewDirectory=arguments.viewDirectory)#</cfoutput>
	<div style="clear:both;"></div>
	<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>

	<!--- results table --->
	<cfloop query="local.qryResults">
		<cfset local.outerCurrentRow = local.qryResults.currentrow>
		<cfoutput>
		<div class="tsDocViewerDocument s_row <cfif local.outerCurrentRow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.qryResults.mcRecordID#">
			<div class="pull-right">
				<div class="p-3 <cfif NOT local.qryResults.inCart>hide</cfif>" id="s_incart#local.qryResults.mcRecordID#">
					<a href="javascript:viewCart();" title="View your cart/checkout"><i class="icon-shopping-cart icon-large"></i> In Cart</a>
				</div>
				<cfif not local.qryResults.owned and variables.cfcuser_DisplayVersion gt 1 >
					<div class="p-3 <cfif local.qryResults.inCart>hide</cfif>" id="s_addtocart#local.qryResults.mcRecordID#">
						<a href="javascript:oneClickPurchaseDTReport(#local.qryResults.mcRecordID#,#arguments.searchID#);" title="Buy Actions"><i class="icon-download icon-large"></i> View Report</a>
					</div>
				<cfelseif local.qryResults.owned>
					<div class="p-3"><a href="javascript:viewDTReport(#local.qryResults.owned#);" title="View Report"><i class="icon-download icon-large"></i> View Report</a></div>
				</cfif>
			</div>
			<div>
				<i class="icon-file icon-large hidden-phone"></i><span class="hidden-phone">&nbsp;</span><b><cfif len(trim(expertname))>#trim(expertname)#<cfelse>Unknown Expert</cfif></b>
			</div>
			<div class="bk_subContent">
				<cfif variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned>
					Document ###local.qryResults.mcRecordID# - #DateFormat(local.qryResults.OpinionDate, "mmm d, yyyy")# - #local.qryResults.jurisdiction#<br/>
					#local.qryResults.casecaption#<br/>
					Disposition: Testimony challenged; not ruled upon <cfif len(local.qryResults.areaOfLaw)>(#local.qryResults.areaOfLaw#)</cfif><br/>
				<cfelse>
					<a href="/?pg=upgradeTrialSmith">Hidden - Click here to upgrade your TrialSmith Plan and view ALL Daubert Tracker reports</a>
				</cfif>
			</div>
		</div>
		</cfoutput>
	</cfloop>
	<cfoutput>
		<br clear="all"/>
		#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='document', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=arguments.sortType, viewDirectory=arguments.viewDirectory)#
	</cfoutput>
</cfif>