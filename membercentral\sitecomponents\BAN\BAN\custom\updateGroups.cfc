<cfcomponent extends="model.customPage.customPage" output="true">
    <cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">

    	<cfscript>
            var local = structNew();

            variables.applicationReservedURLParams = "fa";
            variables.baselink = "/?pg=updateGroups";
            variables.loginRedirectlink = "/?pg=login&returnurl=/?pg=updateGroups";
            local.formAction = arguments.event.getValue('fa','showStepOne');
            variables.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
            if(variables.isLoggedIn == false && session.cfcuser.memberdata.identifiedAsMemberID){
                variables.isLoggedIn = true;
            }
            if(variables.isLoggedIn == false){
                application.objCommon.redirect(variables.loginRedirectlink);
            }
            if(session.cfcuser.memberdata.identifiedAsMemberID){
                variables.qryMember = application.objMember.getMemberInfo(session.cfcuser.memberdata.identifiedAsMemberID);
            }else{
                variables.qryMember = application.objMember.getMemberInfo(session.cfcuser.memberdata.MemberID);
            }
            variables.userMail = application.objMember.getMainEmail(memberID=variables.qryMember.MemberID).email;            

            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];
            local.tmpField = { name="FormDescription", type="CONTENTOBJ", desc="Form Information", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationContent", type="CONTENTOBJ", desc="Confirmation Content", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="GroupList", type="STRING", desc="UID for field set holding custom fields for group membership", value="E67F60AB-13A4-400D-9ED8-FFDCD3802FF4" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="ConfirmationSubject", type="STRING", desc="Confirmation Email Subject Line", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="ConfirmationAuthor", type="STRING", desc="Who confirmation email is from", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="StaffConfirmationRecipient", type="STRING", desc="Email of staff member who receives notification emails", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="PermissionGroup", type="STRING", desc="UID of group allowed to use form", value="AE14B9BA-9FEB-4A7D-9402-D60D51EDA499" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);
            local.tmpField = { name="RestrictedMessage", type="CONTENTOBJ", desc="Content for message displayed when user is not a member of the permission group", value="" }; 
                arrayAppend(local.arrCustomFields, local.tmpField);

            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

            /* ***************** */
            /* set form defaults */
            /* ***************** */
            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='updateGroups',
                formNameDisplay='Groups update Application',
                orgEmailTo=variables.strPageFields.StaffConfirmationRecipient,
                memberEmailFrom=variables.strPageFields.ConfirmationAuthor
            ));

            variables.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser);
            variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID);

            /* ******************* */
            /* Member History Vars */
            /* ******************* */			
            variables.qryHistoryGroupJoined = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='GroupMemActionTaken', subName='Group Joined');
            variables.qryHistoryGroupLeft = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='GroupMemActionTaken', subName='Group Left');

            /* ***************** */
            /* Form Process Flow */
            /* ***************** */
            switch (local.formAction) {
                case "showStepTwo":
					local.returnHTML = validateUser();
                    if(len(trim(local.returnHTML)) == 0){
                        local.returnHTML = showStepTwo(Event=arguments.event);
                    }                    
                    break;				
                default:
                    local.returnHTML = validateUser();
                    if(len(trim(local.returnHTML)) == 0){
                        local.returnHTML = showStepOne();
                    }                    
                    break;
            }

            local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

            return returnAppStruct(local.returnHTML,"echo");	
        </cfscript>

    </cffunction>

    <cffunction name="validateUser" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
        <cfset local.returnHTML = ""/>

        <cfset local.qryGroupMembership = application.objMember.getMemberGroups(variables.useMID,variables.orgID)/>	
        <cfset local.canViewForm = false>
	
        <cfif len(trim(variables.strPageFields.PermissionGroup))>

            <cfset local.qryMemberGroup = application.objCustomPageUtils.getGroups(groupUID='#variables.strPageFields.PermissionGroup#', orgID=variables.orgID)>

            <cfquery dbtype="query" name="local.qryIsMemberGroupExist">
                select groupID
                from [local].qryGroupMembership
                where groupID =  <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
            </cfquery>
            <cfif local.qryIsMemberGroupExist.recordCount GT 0>
                <cfset local.canViewForm = true>
            </cfif>
        </cfif>
        <cfif variables.isSuperUser>
            <cfsavecontent variable="local.returnHTML">
                <cfoutput>
                    <div class="row-fluid">
                        <div class="span12" style="text-align:center">
                            <i class="icon-exclamation icon-2x"></i> 
                            <cfset local.errorMessage = showError(errorCode='superUser')/>
                            #local.errorMessage#
                        </div>
                    </div>
                </cfoutput>
            </cfsavecontent>
        <cfelseif NOT local.canViewForm>
            <cfsavecontent variable="local.returnHTML">
                <cfoutput>
                    <div class="row-fluid">
                        <div class="span12">
                            <cfset local.errorMessage = showError(errorCode='RestrictedGroup')/>
                            #local.errorMessage#
                        </div>
                    </div>
                </cfoutput>
            </cfsavecontent>
        </cfif>

		<cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="showStepOne" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
        <cfset local.returnHTML = ""/>
		
        <cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>
        
        <!--- Load prefill data: either the fields from new acct form or from the members table --->

        <cfset local.fieldSetUid = '#variables.strPageFields.GroupList#'>
        
        <cfset local.memberFieldData = structNew()>

        <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

        <cfset local.groupfieldSet = XMLSearch(local.memberFormXMLFields,"/fields/mf[@displayTypeCode='SELECT'][@dataTypeCode='BIT']")/>

        <cfset local.memberFieldData = application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields)>        
        
        <cfset local.strPrefillMemberData = local.memberFieldData>	

        <cfsavecontent variable="local.headCode">
            <cfoutput>
            <style type="text/css">				
            
                div.alert-danger{padding: 10px !important;}		
            
                ##content-wrapper .switch {
                    position: relative;
                    display: inline-block;
                    width: 60px;
                    height: 34px;
                }

                ##content-wrapper .switch input { 
                    opacity: 0;
                    width: 0;
                    height: 0;
                }

                ##content-wrapper .slider {
                    position: absolute;
                    cursor: pointer;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: ##ccc;
                    -webkit-transition: .4s;
                    transition: .4s;
                }

                ##content-wrapper .slider:before {
                    position: absolute;
                    content: "";
                    height: 26px;
                    width: 26px;
                    left: 4px;
                    bottom: 4px;
                    background-color: white;
                    -webkit-transition: .4s;
                    transition: .4s;
                }

                ##content-wrapper input:checked + .slider {
                    background-color: ##0675ba;
                }

                ##content-wrapper input:focus + .slider {
                    box-shadow: 0 0 1px ##0675ba;
                }

                ##content-wrapper input:checked + .slider:before {
                    -webkit-transform: translateX(26px);
                    -ms-transform: translateX(26px);
                    transform: translateX(26px);
                }

                /* Rounded sliders */
                ##content-wrapper .slider.round {
                    border-radius: 34px;
                }

                ##content-wrapper .slider.round:before {
                    border-radius: 50%;
                }	
                ##content-wrapper select {
                    visibility: hidden;
                    position: absolute;
                }                    
            </style>
            #variables.pageJS#
            <script type="text/javascript">
                var stepOneFormSerialize = "";

                function afterFormLoad(){					
					var _CF_this = document.forms['#variables.formName#'];
					$('html, body').animate({ scrollTop: 0 }, 500);
					$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
					setTimeout(function() {
						$("button[type='submit'],input[type='submit']", _CF_this).html("Continue").removeAttr('disabled');
					}, 5200);
				}	

                function validateStepOneForm(){
                    var _CF_this = document.forms['#variables.formName#'];
                    var arrReq = new Array();                	
                    console.log($("###variables.formName#").serialize());
                    console.log(stepOneFormSerialize);
                    if(stepOneFormSerialize == $("###variables.formName#").serialize()){
                        arrReq[arrReq.length] = "No changes have been made to the form.";
                    }
                    if (arrReq.length > 0) {
                        var msg = '';
                        
                        for (var i=0; i < arrReq.length; i++){
                            var breakLine = '<br/>';
                            if(arrReq[i].indexOf('<li>City') != -1){
                                var breakLine = '';
                            }
                            msg += arrReq[i] + breakLine;
                        }
                        showAlert(msg,afterFormLoad);
                        return false;
                    }

                    $("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');                    
                  
                    return true;
                }    

                function prefillData() {
                    var objPrefill = new Object();
                    <cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
                        <cfif FindNoCase("md_",local.thisKey)>
                            #toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
                        </cfif>
                    </cfloop>
                    for (var key in objPrefill) {
                        if (objPrefill.hasOwnProperty(key)) { 
                            if($("###variables.formName# [name='"+key+"']").length == 1){
                                $("###variables.formName# [name='"+key+"']").val(objPrefill[key]);
                            }
                            else{
                                $("###variables.formName# [value='"+objPrefill[key]+"']").prop("checked", true);
                            }
                        }
                    }
                } 

                $(document).ready(function(){   
                    prefillData();
                    
                    stepOneFormSerialize = $("###variables.formName#").serialize();

                    $('.toggleSwitch').click(function(){
                        if($(this).siblings('input:checked').length == 0){
                            $(this).siblings('input').prop('checked', false);
                            $(this).siblings('input').val('Yes');
                        }else{
                            $(this).siblings('input').prop('checked', true);
                            $(this).siblings('input').val('No');
                        }
                        
                        if($(this).siblings('input:checked').length == 0){
                            $('##'+$(this).siblings('input').attr('fieldCode') +' option:contains('+$(this).siblings('input').val()+')').attr('selected', 'selected');
                        }else{
                            $('##'+$(this).siblings('input').attr('fieldCode') +' option:contains('+$(this).siblings('input').val()+')').attr('selected', 'selected');
                        }
                    });
                    $("##content-wrapper select").each(function(){
                        if($(':selected',this).text() == 'Yes'){
                            $('input[fieldcode='+$(this).attr('id')+']').siblings('.toggleSwitch').trigger('click');
                        }
                    });
                    
                });           
            </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">

        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
            <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

            <cfform name="#variables.formName#" id="#variables.formName#" method="post" class="step1form" onsubmit="return validateStepOneForm();">
                <input type="hidden" name="fa" id="fa" value="showStepTwo">
                <cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
                <input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                <div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>                        
                
                <div id="content-wrapper">
                    <h1>#variables.formNameDisplay#<h1>
                    <cfif len(variables.strPageFields.FormDescription)>
                        <div class="row-fluid" id="FormDescription"><div class="span12">#variables.strPageFields.FormDescription#</div></div><br/>
                    </cfif>

                    <span class="groupListHolder">
                        <cfloop array="#local.groupfieldSet#" index="local.thisfield">
                            <div class="row-fluid">
                                <div class="span12">
                                    <h3>#local.thisfield.xmlattributes.fieldLabel#</h3>
                                    <p>#local.thisfield.xmlattributes.fieldDescription#</p>
                                    <p>
                                        <select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="tsAppBodyText">
                                            <cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt">
                                                <cfset local.thisOptColValue = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
                                                <option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOptColValue#</option>
                                            </cfloop>
                                        </select>

                                        <label class="switch">
                                            <input type="checkbox" fieldCode="#local.thisfield.xmlattributes.fieldCode#">
                                            <span class="slider round toggleSwitch"></span>
                                        </label>
                                    </p>
                                </div>
                            </div>
                        <br/>
                        </cfloop>                            
                    </span>	                   
                    
                    <div class="row-fluid">
                        <div class="span12">
                            <button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
                        </div>
                    </div>
                </div>
            </cfform>
            #application.objWebEditor.showEditorHeadScripts()#				

            <script language="javascript">
            
                function editContentBlock(cid,srid,tname) {
                    var editMember = function(r) {
                        if (r.success && r.success.toLowerCase() == 'true') {
                            $('##frmmd_'+cid).html(r.html);
                            var x = div.getElementsByTagName("script");
                            for(var i=0;i<x.length;i++) eval(x[i].text); 
                        }
                    };
                    var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
                    TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
                }
            </script>
            
            </cfoutput>
        </cfsavecontent>
       
		<cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="showStepTwo" access="private" output="false" returntype="string">
        <cfargument name="Event" type="any">

		<cfset var local = structNew()>

        <cfset local.objmemberFieldSet = CreateObject("component","model.system.platform.memberFieldsets")>
        <cfset local.fieldSetUid = '#variables.strPageFields.GroupList#'>
        
        <cfset local.memberFieldData = structNew()>
        <cfset local.memberFormXMLFields = local.objmemberFieldSet.getMemberFieldsXMLByUID(uid='#local.fieldSetUid#', usage='CustomPage')>

        <cfset local.memberFieldData = application.objCustomPageUtils.mem_fieldsetData(memberID=variables.useMID, xmlFields=local.memberFormXMLFields)> 
        <cfset local.joinedArray = []/> 
        <cfset local.leftArray = []/> 
        <cfset local.rc = arguments.Event.getCollection()/>

        <cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.rc, memberID=variables.useMID)>
        <cfloop collection="#local.memberFieldData#" item="local.key">
            <cfif len(local.memberFieldData[local.key])>
                <cfif local.rc[local.key] NEQ local.memberFieldData[local.key]>            
                    <cfset local.column = XMLSearch(local.memberFormXMLFields,"/fields/mf[@fieldCode='#local.key#']")/>
                    <cfif arrayLen(local.column)>
                        <cfset local.columnValue =  application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID = variables.orgID, columnID = ListToArray(local.key,"_")[2], valueIDList = local.memberFieldData[local.key])/>
                        <cfif local.columnValue>
                            <cfset ArrayAppend(local.leftArray,"Left " & local.column[1].XmlAttributes.fieldLabel)/>
                        <cfelse>
                            <cfset ArrayAppend(local.joinedArray,"Joined " & local.column[1].XmlAttributes.fieldLabel)/>
                        </cfif>
                    </cfif>
                </cfif> 
            </cfif>                
        </cfloop>

        <cfif arrayLen(local.joinedArray) EQ 0 AND arrayLen(local.leftArray) EQ 0>
            <cfset application.objCommon.redirect(variables.baselink)/>
        </cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
            <div id="content-wrapper">
                <h1>Your Changes Have Been Submitted</h1>

                <cfif len(variables.strPageFields.ConfirmationContent)>
                    <div class="row-fluid" id="ConfirmationContent"><div class="span12">#variables.strPageFields.ConfirmationContent#</div></div><br/>
                </cfif>
                <cfsavecontent variable="local.summary">
                    <cfif arrayLen(local.joinedArray) OR arrayLen(local.leftArray)>
                        <div class="row-fluid"><div class="span12">
                        <ul style="list-style: unset;padding: 0;margin: 0 0 10px 25px;">
                    </cfif>
                    <cfloop array="#local.joinedArray#" item="local.item1">
                        <li>#local.item1#</li>
                    </cfloop>
                    <cfloop array="#local.leftArray#" item="local.item2">
                        <li>#local.item2#</li>
                    </cfloop>
                    <cfif arrayLen(local.joinedArray) OR arrayLen(local.leftArray)>
                        </ul>
                        </div></div><br/>
                    </cfif>
                </cfsavecontent>
                #local.summary#
                <cfif len(variables.userMail)>
                    <div class="row-fluid"><div class="span12">A copy of this message has been emailed to <b>#variables.userMail#</b></div></div><br/>
                </cfif>
			
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.historyJoinText">
			<cfoutput>
                <cfsavecontent variable="local.summaryJoin">
                    <cfloop array="#local.joinedArray#" item="local.item1">
                        #local.item1#
                    </cfloop>
                </cfsavecontent>	
                #local.summaryJoin#	
			</cfoutput>
		</cfsavecontent>

        <cfsavecontent variable="local.historyLeftText">
			<cfoutput>
                <cfsavecontent variable="local.summaryLeft">
                    <cfloop array="#local.leftArray#" item="local.item2">
                        #local.item2#
                    </cfloop>
                </cfsavecontent>
                #local.summaryLeft#		
			</cfoutput>
		</cfsavecontent>
        
        <cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=local.rc, memberID=variables.useMID)>

        <cfif arrayLen(local.joinedArray)>
            <cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.qryMember.memberID, categoryID=variables.qryHistoryGroupJoined.categoryID, 
                                                        subCategoryID=variables.qryHistoryGroupJoined.subCategoryID, description=local.historyJoinText, 
                                                        enteredByMemberID=variables.qryMember.memberID, newAccountsOnly=false)>
        </cfif>

        <cfif arrayLen(local.leftArray)>
            <cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.qryMember.memberID, categoryID=variables.qryHistoryGroupLeft.categoryID, 
													subCategoryID=variables.qryHistoryGroupLeft.subCategoryID, description=local.historyLeftText, 
													enteredByMemberID=variables.qryMember.memberID, newAccountsOnly=false,startDate="",endDate="#dateformat(now(),'m/d/yyyy')#")>
        </cfif>
        <!--- email submitter --->
        <cfset local.emailSentToUser = false>

        <cfif len(variables.userMail)>
            <cfsavecontent variable="local.mailContent">
                <cfoutput>		
                    <p>Dear #variables.qryMember.firstname# #variables.qryMember.lastname#,</p>				
                    <p>#variables.strPageFields.ConfirmationContent#</p>
                    #local.summary#	
                </cfoutput>
            </cfsavecontent>

            <cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.strPageFields.ConfirmationAuthor },
					emailto=[{ name="#variables.qryMember.firstname# #variables.qryMember.lastname#", email=variables.userMail }],
					emailreplyto=variables.strPageFields.StaffConfirmationRecipient,
					emailsubject=variables.strPageFields.ConfirmationSubject,
					emailtitle=arguments.Event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getValue('mc_siteinfo.siteid'),
					memberID=val(variables.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
                )>
                
            <cfset local.emailSentToUser = local.responseStruct.success>
        </cfif> 
        
		<!--- email staff --->

        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif NOT local.emailSentToUser>
                    <p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
                </cfif>
                <p>Member #variables.qryMember.firstname# #variables.qryMember.lastname# (#variables.qryMember.membernumber#) made the following changes to their group membership:</p>
                #local.summary#
            </cfoutput>
        </cfsavecontent>
        
        <cfscript>
            local.arrEmailTo = [];
            local.toEmailArr = listToArray(variables.strPageFields.StaffConfirmationRecipient.replaceAll(',',';'),';');
            for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
                local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
            }
            local.responseStruct = application.objEmailWrapper.sendMailESQ(
                emailfrom={ name="", email=variables.strPageFields.ConfirmationAuthor },
                emailto=local.arrEmailTo,
                emailreplyto=variables.strPageFields.ConfirmationAuthor,
                emailsubject=variables.strPageFields.ConfirmationSubject,
                emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
                emailhtmlcontent=local.mailContent,
                emailAttachments=[],
                siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
                memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
                messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                sendingSiteResourceID=this.siteResourceID
            );
        </cfscript>
        
		<cfreturn local.returnHTML>
    </cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>	
                <cfif arguments.errorCode eq "failsavemember">
                    We were unable to save the member information provided. Please contact the association or try again later.
                <cfelseif arguments.errorCode eq "spam">
                    Your submission was blocked and will not be processed at this time.
                <cfelseif arguments.errorCode eq "RestrictedGroup">
                    #variables.strPageFields.RestrictedMessage#   
                <cfelseif arguments.errorCode eq "superUser">
                    This form is not available when logged in as a SuperUser.                     
                <cfelse>
                    An error occurred. Please contact the association or try again later.
                </cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
  </cfcomponent>