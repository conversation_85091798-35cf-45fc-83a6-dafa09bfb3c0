<cfsavecontent variable="local.swRegStep2JS">
	<cfoutput>
	<script type="text/javascript">
		function saveRegRate() {
			let rateID = $('input[type="radio"][name="sw_rateID"]:checked').val();
			if (rateID) {
				$('##swSelectedRegRateName').html($('label[for="sw_rateID'+rateID+'"]').html());
				$('##swSelectedRegRatePrice').html($('##sw_rateID'+rateID).data('regratepricedisp'));
				if ($('##btnContinueRateSelection').length) $('##btnContinueRateSelection').prop('disabled',true);
				$('##swRegRateSaveLoading')
					.html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
					.load('#arguments.event.getValue('swregresourceurl')#&regaction=saves2',{sw_rateID:rateID},
						function(resp) {
							let respObj = JSON.parse(resp);
							let arrSteps = respObj.loadsteps.split(',');
							
							$('##swRegRatesContainer,##swRegRateSaveLoading').hide();
							$('##swSelectedRegRate').show(300);

							$('html, body').animate({
								scrollTop: $('##SWRegStep2').offset().top - 175
							}, 750);
							
							arrSteps.forEach(function(step,index) {
								loadSWRegSteps(step);
							});
						}
					)
					.show();
			}
		}
		function editRegStep2() {
			$('##swSelectedRegRate').hide();
			$('##swRegRatesContainer').show(300);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.swRegStep2JS)#">