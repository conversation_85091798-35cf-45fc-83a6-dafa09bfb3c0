<cfset dataStruct = attributes.data.actionStruct>
<cfset local.stepNum = 2>

<cfinclude template="navigation.cfm">
<cfinclude template="../commonJS_step2.cfm">

<cfoutput>
<div class="tsAppBodyText">
	<div>
		<div class="TitleText" style="margin-bottom:30px;">#dataStruct.intakeFormTitle#</div>

		<div id="cp2_err_div" style="display:none;margin:12px 0 5px 0;"></div>

		<cfform name="frmContribution" id="frmContribution" method="post" action="#dataStruct.mainformurl#" onsubmit="return doS2Validate(false)">
			<input type="hidden" name="nextStep" id="nextStep" value="payment">
			<input type="hidden" name="orgMemberID" id="orgMemberID" value="0">

			<div class="CPSection" id="divLookupArea">
				<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
				<div style="padding:10px;">
					<table cellspacing="0" cellpadding="2" border="0" width="100%">
						<tr>
							<td width="175" class="c">
								<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
									<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
								</div>
							</td>
							<td>
								<span class="frmText">
									<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
									<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
									<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
									<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
								</span>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="CPSection waitingtxt" id="divLookupAreaLoading" style="display:none;">
				<div style="text-align:center;margin:30px 10px;"><i class="icon-spinner icon-spin"></i> Please wait while we prepare your contribution for the next step.</div>
			</div>
		</cfform>
	</div>
</div>
</cfoutput>