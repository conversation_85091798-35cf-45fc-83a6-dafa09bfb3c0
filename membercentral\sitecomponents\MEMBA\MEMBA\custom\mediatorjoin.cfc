<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		
		local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="Who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="Who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you.  Your Mediator Listing application has been submitted.   You will receive an email from the Memphis Bar Association with the information you provided and a receipt for Credit Card Payment (if selected).

		Your Mediator Listing will be activated once your payment has been processed. Please check for your listing and contact the Memphis Bar <NAME_EMAIL> if your listing does not appear after 5 minutes.

		You may update any information that you may have missed or want to update by clicking here.  We ask that you also provide a digital PHOTO for your listing.  The digital photo size to upload is restricted to 83 pixels wide by 124 pixels high." }; 
			
		arrayAppend(local.arrCustomFields, local.tmpField);
			
		local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="The Memphis Bar Association records indicate you currently have a Mediator Listing on the Memphisbar.org website. Please click here to log in." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
			
		local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Error message when billed subscription found", value="You need to renew your Mediator Listing. You will be re-directed to your renewal shortly.." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);	
			
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="Pay profile code for CC", value="MBACIM" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='mediatorjoin',
			formNameDisplay='Mediator Arbitrator Listing',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));
		
		variables.membershipDuesTypeUID = '069698CA-C1B9-4C4C-90EC-0D576E8595A2';
        variables.mediatorListingTypeUID = '327F5443-75FF-43FD-BCEA-495F12AF1065';
		variables.mediatorListingSubUID = 'D03F7855-5AFE-442A-861B-048E854E0804';
		
		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="mediatorjoin");

		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('historyID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('historyID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MediatorApp', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MediatorApp', subName='Completed');
		variables.historyStartedText = "Member started Mediator Arbitrator Listing form.";
		variables.historyCompletedText = "Member completed Mediator Arbitrator Listing form.";
		
		
		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				
				local.checkSubscriptionStatus = processLookUp();
				switch (local.checkSubscriptionStatus.status) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;
                    case "activemembershipfound":
                        local.returnHTML = showError(errorCode='activemembershipfound');
						break;
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;		
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound',redirecturl=local.checkSubscriptionStatus.redirectUrl);
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					case "spam":
						local.returnHTML = showError(errorCode='spam');
						break;	
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.confirmationHTML = produceConfirmation(event=arguments.event,rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");						
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup(memberKey=arguments.event.getTrimValue('mk',''));
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfargument name="memberkey" type="string" required="true">
	
		<cfset var local = structNew()>
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		
		<cfif arguments.memberkey neq ''>
			<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgid)>
		</cfif>
		<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
		<cfif structKeyExists(session, "captchaEntered")>
			<cfset structDelete(session, "captchaEntered")>
		</cfif>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				
				.fieldSetWrap .tsAppSectionContentContainer > table,
				.fieldSetWrap .tsAppSectionContentContainer > table > tbody{
					width:100% !important;
				}
				
				.fieldSetWrap .tsAppSectionContentContainer > table{				
					margin-top: 50px;
				}
				button.ui-multiselect .ui-icon{
					margin-top: 3px;
				}
				##mediatorjoin ##divFrmErr{
					padding: 10px;
					font-family: Verdana, Arial, Helvetica, sans-serif;
					font-size: 9pt;
				}
				##mediatorjoin .tsAppSectionHeading{
					background: ##003087;
					color: ##fff;
				}
				##mediatorjoin .tsAppSectionContentContainer{
					border:1px solid;
				}
				@media screen and (min-width: 980px) {
					  .fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(1) {
						width:1% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(2) {
						width:20% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(3) {
						width:0% !important;
						display:none;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) {
						width:50% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) input[type="text"],
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) select{
						width:60% !important;
					}
					div.ui-multiselect-menu{
						width:auto!important;
					}
					button.ui-multiselect
					{width:60%!important;}
				}
				@media only screen and (min-width: 850px) and (max-width: 979px){
					.header .navbar .container, .container {
						width: 85%;
					}
					.inner-content{
						padding: 0 20px;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) {
						width:50% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) input[type="text"],
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) select{
						width:60% !important;
					}
					div.ui-multiselect-menu{
						width:30%!important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(3) {
						width:0% !important;
						display:none;
					}
					button.ui-multiselect
					{width:60%!important;}
				}
				@media only screen and (min-width: 768px) and (max-width: 849px){
					.header .navbar .container, .container {
						width: 85%;
					}
					.inner-content{
						padding: 2px;
					}
					.inner-content > .container > .row,
					.inner-content > .container > .row > .span12{
						width:100% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) {
						width:50% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) input[type="text"],
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) select{
						width:60% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(3) {
						width:0% !important;
						display:none;
					}
					div.ui-multiselect-menu{
						width:30%!important;
					}
					button.ui-multiselect
					{width:60%!important;}
				}
				@media only screen and (max-width: 767px){
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(2) {
						width:10% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) {
						width:50% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) input[type="text"],
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(4) select{
						width:80% !important;
					}
					.fieldSetWrap .tsAppSectionContentContainer > table > tbody > tr > td:nth-child(3) {
						width:0% !important;
						display:none;
					}
					div.ui-multiselect-menu{
						width:30%!important;
					}
					button.ui-multiselect
					{width:80%!important;}
					.fieldSetWrap .tsAppSectionContentContainer > table{				
						margin-top: 0px;
					}
				}
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'));
				}
				function toggleFTM() {
					var mcSel = $('###variables.formName# ##mccf_memberCategory').val();
					if (mcSel == 'Attorney' || mcSel == 'Defense Attorney' || mcSel == 'Government Attorney') {
						$('##tbodymccf_firstTimeMember').show();
					} else {
						$('##tbodymccf_firstTimeMember').hide();
						$('###variables.formName# ##mccf_firstTimeMember').prop('checked', false);
					}
				}
				function validateMembershipInfoForm(){
					var arrReq = new Array();
					if (!$('###variables.formName# ##mccf_RFID').is(':checked')) arrReq[arrReq.length] = "Make a membership type selection.";
					if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function validatePaymentForm() {
					var arrReq = mccf_validatePPForm();
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
			<cfinput type="hidden" name="mk" id="mk" value="#arguments.memberkey#">
						
			<cfif len(variables.strPageFields.AccountLocatorIntroText)>
				<div id="AccountLocatorIntroText">#variables.strPageFields.AccountLocatorIntroText#</div><br/>
			</cfif>											
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="btn" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.stReturn.status = "nomatch">
		
		<cfif variables.useMID eq ''>
			<cfset variables.useMID = 0>
		</cfif>
		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
	
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn.status = "nomatch">
		<cfelse>
			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfquery dbtype="query" name="local.qryActiveSubsActvnReqMet">
                SELECT * FROM [local].qryActiveSubs WHERE paymentStatus = 'P' <!---Activation Requirement Met--->
            </cfquery>
            <cfset local.activeSubList = valueList(local.qryActiveSubs.typeUID)>
            <cfset local.activeSubListActvnReqMet = valueList(local.qryActiveSubsActvnReqMet.typeUID)>
           
            <cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
            <cfquery dbtype="query" name="local.qryActiveSubsActvnReqMet">
                SELECT * FROM [local].qryAcceptedSubs WHERE paymentStatus = 'P' <!---Activation Requirement Met--->
            </cfquery>
            <cfset local.acceptedSubList = valueList(local.qryAcceptedSubs.typeUID)>
            <cfset local.acceptedSubListActvnReqMet = valueList(local.qryActiveSubsActvnReqMet.typeUID)>
            
            <cfset local.qryAllBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
            <cfset local.billedSubList = valueList(local.qryAllBilledSubs.typeUID)>
            
            <cfset local.isActiveMember = listFindNoCase(local.activeSubListActvnReqMet, variables.membershipDuesTypeUID)>
            <cfset local.isAcceptedMember = listFindNoCase(local.acceptedSubListActvnReqMet, variables.membershipDuesTypeUID)>
            
            <cfset local.isActiveMediatorList = listFindNoCase(local.activeSubList, variables.mediatorListingTypeUID)>
            <cfset local.isAcceptedMediatorList = listFindNoCase(local.acceptedSubList, variables.mediatorListingTypeUID)>
            <cfset local.isBilledMediatorList = listFindNoCase(local.billedSubList, variables.mediatorListingTypeUID)>
            <cfset local.renewRedirectLinkCode = ''>
			<cfif local.isBilledMediatorList>
				<cfquery name="local.qryBilledSubs" dbtype="query">
					SELECT * FROM local.qryAllBilledSubs WHERE UID = '#variables.mediatorListingSubUID#'
				</cfquery>
				<cfset local.renewRedirectLinkCode = local.qryBilledSubs.directLinkCode>
			</cfif>

            <cfif ((local.isActiveMember OR local.isAcceptedMember) AND (local.isActiveMediatorList OR local.isAcceptedMediatorList)) AND NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)><!--- Active/Accepted Membership Dues subscription in the Activation Requirement Met status AND an Active/Accepted Mediator Listing Subscription. --->
                <cfif local.isActiveMediatorList>
                    <cfset local.stReturn.status = "activefound">
                <cfelse>
                    <cfset local.stReturn.status = "acceptedfound">
                </cfif>
            <cfelseif (local.isActiveMediatorList OR local.isAcceptedMediatorList)><!--- the Mediator Listing subscription in Active/Accepted status--->
                <cfif local.isActiveMediatorList>
                    <cfset local.stReturn.status = "activefound">
                <cfelse>
                    <cfset local.stReturn.status = "acceptedfound">
                </cfif>
            <cfelseif local.isBilledMediatorList><!---the Mediator Listing subscription in Billed status --->
				<cfif local.qryBilledSubs.isRenewalRate>
					<cfset local.stReturn.redirecturl="/renewsub/#local.renewRedirectLinkCode#" >
                	<cfset local.stReturn.status = "billedfound">
				<cfelse>
					<cfset local.stReturn.status = "billedjoinfound">
				</cfif>				
            <cfelse>
                <cfset local.stReturn.status = "success">
            </cfif>
        
		</cfif>
		
		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='22BE426B-12D7-4148-81FF-35EBB4DF282C', mode="collection", strData=local.strData)>
		
		<cfset local.autoRenewObj = application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgid,columnName='Auto Renew my Mediator subscription')>
		
		
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function validateMemberInfoForm(){
				
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'));
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
					
					var autoSelect = 'Yes';
					$("##md_#local.autoRenewObj.COLUMNID# option").filter(function() {
						return this.text == autoSelect; 
					}).attr('selected', true);
					
					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>
				}
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" class="form-horizontal" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
					<input type="hidden" name="fa" id="fa" value="processMemberInfo">
					<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
					<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
					<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
					<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>
			
					<div id="content-wrapper" class="row-fluid">
						<div class="row-fluid fieldSetWrap">
							<div class="span12 tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
							<div class="tsAppSectionContentContainer">	
							#local.strFieldSetContent1.fieldSetContent#
							</div>
						</div>
						<div class="row-fluid">
							<div class="span12">							
								#variables.captchaDetails.htmlContent#
							</div>
						</div> 						
					</div>
					
					<button name="btnContinue" type="submit" class="btn" onClick="hideAlert();">Continue</button>
					#application.objWebEditor.showEditorHeadScripts()#
					<script language="javascript">				
						function editContentBlock(cid,srid,tname) {
							var editMember = function(r) {
								if (r.success && r.success.toLowerCase() == 'true') {
									$('##frmmd_'+cid).html(r.html);
									var x = div.getElementsByTagName("script");
									for(var i=0;i<x.length;i++) eval(x[i].text); 
								}
							};
							var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
							TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
						}			
					</script>
				</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		
	
		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>
		<!--- set Contact Type based on selection of Licensed Attorney --->
		<cfset local.membaContactTypeValueIDList = "">
		<cfset local.membaContactTypeNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnName="Contact Type"]')>
		<cfset local.membaContactTypeValueID = XMLSearch(local.membaContactTypeNode[1],'string(columnvalue[@columnValueString="Mediator"]/@valueID)')>
		<cfset local.membaContactTypeValueIDList = local.membaContactTypeValueID>			

		<cfset local.membaLicensedAttorneyNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnName="Mediator Licensed Attorney"]')>
		<cfset local.membaLicensedAttorneyValueID = XMLSearch(local.membaLicensedAttorneyNode[1],'string(columnvalue[@columnValueBit=1]/@valueID)')>
		<cfif isDefined("arguments.rc.MD_#local.membaLicensedAttorneyNode[1].xmlAttributes.columnID#") and evaluate("arguments.rc.MD_#local.membaLicensedAttorneyNode[1].xmlAttributes.columnID#") eq local.membaLicensedAttorneyValueID>
			<cfset local.membaContactTypeValueID = XMLSearch(local.membaContactTypeNode[1],'string(columnvalue[@columnValueString="Attorney"]/@valueID)')>
			<cfset local.membaContactTypeValueIDList = listAppend(local.membaContactTypeValueIDList, local.membaContactTypeValueID)>			
		</cfif>
		<cfset structInsert(arguments.rc, "MD_#local.membaContactTypeNode[1].xmlAttributes.columnID#", local.membaContactTypeValueIDList)>		
		
		<!--- save member info and record history --->		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfif local.isLoggedIn>
			<cfset variables.useMID = session.cfcUser.memberData.memberID>
			
		</cfif>

		<!--- We need to make sure a new user is created when saving the data --->
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc, memberID=variables.useMID)>
		<cfscript>
				local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=variables.useMID);
				local.objSaveMember.setCustomField(field='Contact Type', value='Mediator');
				local.strResult = local.objSaveMember.saveData();
		</cfscript>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>
			<cfset session.captchaEntered = 1>	
			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(
			siteID=variables.siteID,
			memberID=variables.useMID,
			subscriptionUID=variables.mediatorListingSubUID,
			isRenewal=0,
			frequencyShortName='F',
			allowFrontEnd="true")>
																					 
			

		<cfif not local.qryRates.recordCount>		
			<cfreturn showError(errorCode='failsavemembership')>
		</cfif>

		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,historyID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>	

		<cfif local.qryRates.rateAmt gt 0>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC]>
			<cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(arrPayMethods=local.arrPayMethods, 
																						siteID=variables.siteID, 
																						memberID=variables.useMID, 
																						title="Mediator Join- Payment Method", 
																						formName=variables.formName, 
																						backStep="processLookup")>		
		</cfif>

		<cfif local.qryRates.rateAmt gt 0>
			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm()">
					<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
					<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
					<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
					<cfinput type="hidden" name="historyID" id="historyID" value="#arguments.rc.useHistoryID#">
					<cfloop collection="#session.formFields.step1#" item="local.thisField">
						<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
							or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
							or left(local.thisField,5) eq "mccf_">
							<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
							<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
						</cfif>
					</cfloop>
					<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">
					
					<div id="divFrmErr" class="alert-danger" style="display:none;margin:6px 0;"></div>

					<div class="tsAppSectionHeading">Mediator Listing Confirmation</div>
					<div class="tsAppSectionContentContainer">
						<table cellpadding="3" border="0" cellspacing="0" width="70%">	
						<tr valign="top">
							<th class="tsAppBodyText" width="50%" align="left">
								Item
							</th>
							<th class="tsAppBodyText" align="left">
								Price
							</th>
						</tr>
						<cfloop query="local.qryRates">
							<tr valign="top">
								<td class="tsAppBodyText">
									#local.qryRates.rateName#
									<cfinput type="hidden" name="mccf_RFID" id="mccf_RFID" value="#local.qryRates.RFID#">
								</td>
								<td class="tsAppBodyText">
									#dollarFormat(local.qryRates.rateAmt)#
								</td>
							</tr>
							<cfbreak>
						</cfloop>	
						</table>
					</div>

					<cfif local.qryRates.rateAmt gt 0>
						#local.strReturn.paymentHTML#
					<cfelse>
						<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
						<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	
					</cfif>	
				</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->

		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset structDelete(session.formFields, "step2")>
		</cfif>			
		<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>				
		<cfset local.response = "success">		

		<cfreturn local.response>
	</cffunction>

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='22BE426B-12D7-4148-81FF-35EBB4DF282C', mode="confirmation", strData=arguments.rc)>
		
		<cfset local.memberPayProfileDetail = "">
		
		<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(
			siteID=variables.siteID,
			memberID=variables.useMID,
			subscriptionUID=variables.mediatorListingSubUID,
			isRenewal=0,
			frequencyShortName='F',
			allowFrontEnd="true")>
																					 
		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.mediatorListingSubUID)>
				
		
		<cfloop query="local.qryRates">
		
			<cfset StructInsert(arguments.rc, 'sub#local.subscriptionID#', 'sub#local.subscriptionID#')>
			<cfset StructInsert(arguments.rc, 'sub#local.subscriptionID#_rate', '#local.qryRates.RFID#')>
			<cfset StructInsert(arguments.rc, 'sub#local.subscriptionID#_rateFrequencySelected', '#local.qryRates.frequencyUID#')>
		</cfloop>
		<cfset local.subStructResults = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>
			
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
		
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=arguments.event, memberID=variables.useMID, subStruct=local.subStructResults.subscription, newAsBilled=false)> 	

		
		<!--- find the invoice for the subscription and pay it --->
		<!--- find all invoices for associating CC 				 --->
		<cfset local.qryInvoice = application.objCustomPageUtils.sub_getInvoicesDuesForSubscription(rootSubscriberID=local.subReturn.rootSubscriberID,siteID=variables.siteID, orgID = variables.orgID)>
		
		<cfquery dbtype="query" name="local.qryInvoiceDueNow">
			select invoiceID, invoiceProfileID, totalAmount as amount
			from [local].qryInvoice
			where dueNow=1
		</cfquery>

		<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
		<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>

		<cfif structKeyExists(arguments.rc, 'mccf_payMethID') and structKeyExists(arguments.rc, 'p_#arguments.rc.mccf_payMethID#_mppid')>
			<cfset local.memberPayProfileID = arguments.rc['p_#arguments.rc.mccf_payMethID#_mppid']>
		<cfelse>
			<cfset local.memberPayProfileID = 0 >
		</cfif>

		<!--- -------------------------------------------------- --->
		<!--- Save card on file to subscription and all invoices --->
		<!--- -------------------------------------------------- --->
		<cfif local.totalAmount gt 0 and local.memberPayProfileID>
			<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
			<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.subReturn.rootSubscriberID, MPProfileID=arguments.rc.mccf_payMethID, mppID=local.memberPayProfileID)>
		</cfif>
		
		<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
			<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
		<cfelse>
			<cfset local.memberPayProfileSelected = 0>
		</cfif>
		
		<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>

		<cfif local.totalAmountDueNow gt 0 and ListFindNoCase("#variables.strPageFields.PaymentProfileCodeCC#",arguments.rc.mccf_payMeth)>
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, assignedToMemberID=variables.useMID, recordedByMemberID=variables.useMID, rc=arguments.rc } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=arguments.rc.mccf_payMethID, profileCode=arguments.rc.mccf_payMeth }>
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

				<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				
				<cfif val(local.strACCResponse.paymentResponse.transactionID)>
					<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.useMID, subscriberID=0, bypassQueue=0)>
				</cfif>
			<cfelse>
				<cfset local.strACCResponse.accResponseMessage = "">
			</cfif>
			<cfset local.returnstruct.strACCResponse = local.strACCResponse>
		</cfif>
		
		<cfquery name="local.qryRatesSelected" dbtype="query">
			select rateAmt, rateName
			from [local].qryRates
			where RFID = #arguments.rc.mccf_RFID#
		</cfquery>
		
		<cfset application.objCustomPageUtils.mh_updateHistory(	memberID=variables.useMID, historyID=variables.useHistoryID, 
																subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
																newAccountsOnly=false)>	

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>		

		<cfset local.strURL = { d="#dateformat(now(),"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", s=variables.siteID, m=variables.useMID, memberUpdateLink=1 }>
		<cfset local.encString = encrypt(serializeJSON(local.strURL),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
		<cfset local.updateurl = "#local.thisScheme#://#local.thisHostname#/?pg=updatemember&id=#local.encString#">

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationMessage)>
				<div>#variables.strPageFields.ConfirmationMessage#</div>
			</cfif>

			<div>Please <a href="#local.updateurl#">Click Here</a> to update any information that you may have missed or want to update. We ask that you also provide a digital PHOTO for your listing. 
			The digital photo size to upload is restricted to 83 pixels wide by 124 pixels high.</div>	

			<!--@@specialcontent@@-->

			<div>Here are the details of your application:</div><br/>

			#local.strFieldSetContent1.fieldSetContent#

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Mediator Join - Membership Type</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<div>#local.qryRatesSelected.rateName# - #dollarFormat(local.qryRatesSelected.rateAmt)#</div>
				</td>
			</tr>
			</table>
			<br/>
			<cfif local.qryRatesSelected.rateAmt gt 0>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Mediator Join - Payment</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.memberEmail.from },
                            emailto=[{ name="", email=variables.memberEmail.to }],
                            emailreplyto=variables.ORGEmail.to,
                            emailsubject=variables.memberEmail.SUBJECT,
                            emailtitle="#arguments.rc.mc_siteInfo.sitename# - #variables.formNameDisplay#",
                            emailhtmlcontent=local.confirmationHTML,
                            siteID=arguments.rc.mc_siteinfo.siteid,
                            memberID=val(variables.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to Memphsis Bar Association", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">
		<cfargument name="redirecturl" type="string" required="false">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">
                <cfif arguments.errorCode eq "activemembershipfound">
                    #variables.strPageFields.ErrorActiveSubscriptionFound#
                <cfelseif arguments.errorCode eq "activefound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.ErrorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script language="javascript">
						function callback() {
							return function() {
								window.location = "#arguments.redirecturl#";
							}
						}
						setTimeout(callback(), 5000);
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>
</cfcomponent>