<cfsavecontent variable="local.referralPayJS">
	<cfoutput>
    <script type="text/javascript">
		var checkPayFormInProgress = false;
		function hideAlert() { $('##pageWellPmtErr').html('').hide(); };
		function showAlert(msg) { $('##pageWellPmtErr').html(msg).attr('class','alert').show(); };

		function showHideOverSoldItemPanel(rk) {
			$('div##overSoldItemAccordion'+rk+' div.issuePanel').toggle();
			$('div##overSoldItemAccordion'+rk+' span.showOverSoldItems').toggle();
			$('div##overSoldItemAccordion'+rk+' span.hideOverSoldItems').toggle();
		}
		function selectPayment(pid) {
			hideAlert();
			$('##profileid').val(pid);
		}
		
		function pageWellMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
						var iframeWin = document.querySelector('iframe[name=iframeManageForm'+event.data.profileid+']').contentWindow;
						var message = { success:true, 
										messagetype:"MCFrontEndPaymentEvent", 
										paymentbuttonname:$('##divBtnWrapper'+event.data.profileid+' button[type="submit"]').text(), 
										profileid:event.data.profileid };
						iframeWin.postMessage(message, event.origin);
						break;

					case "mcgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.payprofileid) {
							showRefPaymentProcessing(event.data.profileid);
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmPurchasepageWell');
							setRefPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchasepageWell').submit();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							showRefPaymentProcessing(event.data.profileid);
							setRefPaymentTokenData(event.data.tokendata,event.data.profileid);
							setRefPaymentProcessingFeesField(event.data);
							selectPayment(event.data.profileid);
							$('##frmPurchasepageWell').submit();
						}
						break;
				};

			} else {
				return false;
			}
		}
		function showRefPaymentProcessing(pid) {
			$('##divPaymentTable' + pid)
				.html('<i class="icon icon-spinner"></i> Please Wait...')
				.css({'height':'75px', 'padding':'5px'});
		}
		function setRefPaymentProcessingFeesField(obj) {
			if (typeof obj.enableprocessingfee != "undefined") {
				let processFeeDonationElement = $('##processFeeDonation'+obj.profileid);
				if (processFeeDonationElement.length) {
					processFeeDonationElement.val(obj.enableprocessingfee);
				} else {
					$('<input>').attr({ type: 'hidden', name: 'processFeeDonation'+obj.profileid, id: 'processFeeDonation'+obj.profileid }).val(obj.enableprocessingfee).appendTo('form##frmPurchasepageWell');
				}
			}
		}
		function setRefPaymentTokenData(tokendata,profileid) {
			let tokenDataElement = $('##p_'+profileid+'_tokenData');
			if (tokenDataElement.length) {
				tokenDataElement.val(JSON.stringify(tokendata));
			} else {
				$('<input>').attr({ type: 'hidden', name: 'p_'+profileid+'_tokenData', id: 'p_'+profileid+'_tokenData' }).val(JSON.stringify(tokendata)).appendTo('form##frmPurchasepageWell');
			}
		}
		function onchangepageWellPaymentTabHandler(pid) {
			$('.pageWellCartTotals').hide();
			$('.pageWellCartItemResponse').html('').hide();
			$('.pageWellCartItemTotal').removeClass('pageWell-opacity-1');

			let nonmatchingitemkeys = $('##divPaymentTable'+pid).data('nonmatchingitemkeys');
			if (nonmatchingitemkeys && nonmatchingitemkeys.length) {
				let arrNonMatchingItems = nonmatchingitemkeys.split(',');
				
				arrNonMatchingItems.forEach(function(rk,index) {
					$('##pageWellKey' + rk + ' .pageWellCartItemResponse').html('This pending registration does not accept this payment method.').show();
					$('##pageWellKey' + rk + ' .pageWellCartItemTotal').addClass('pageWell-opacity-1');
				});

				$('##pageWellCartTotalSummary'+pid).show();

			} else {
				$('##pageWellCartTotalSummary').show();
			}
		}
		
		$(function() {
			
			if (window.addEventListener) {
				window.addEventListener("message", pageWellMessageHandler, false);
			} else if (window.attachEvent) {
				window.attachEvent("message", pageWellMessageHandler);
			}

			<cfif variables.structData.view EQ 'default'>
				initCollapsibleDivSet('paymentTypePills');

				$('##paymentTypeTabs li.tsAppNavButton a.paymentTypePills').on('click',function() {
					onchangepageWellPaymentTabHandler($(this).data('mccollapsibledivshow').replace('profile',''));
				});

				if ($('##paymentTypeTabs li.tsAppNavButton a.paymentTypePills').length) {
					onchangepageWellPaymentTabHandler($('##paymentTypeTabs li.tsAppNavButton a.paymentTypePills:first').data('mccollapsibledivshow').replace('profile',''));
				}
			<cfelse>
				$('##pageWellPmtTabs a[data-toggle="tab"]').on('shown', function (e) {
					onchangepageWellPaymentTabHandler($(e.target).attr('href').replace('##profile',''));
				});

				if ($('##pageWellPmtTabs a[data-toggle="tab"]').length) {
					onchangepageWellPaymentTabHandler($('##pageWellPmtTabs a[data-toggle="tab"]:first').attr('href').replace('##profile',''));
				}
			</cfif>
		});

		
	function checkPayForm() {

		/* disable payment buttons while validation is running */
		$('button[type="submit"]',$('##frmPurchasepageWell')).each(function(index,thisButton){
			$(thisButton).attr('disabled','disabled');
		});
	
		var validationPassed = true;

		/* prevent race condition caused by double submitting before validation can be completed */
		if (checkPayFormInProgress) {
			validationPassed = false;
		} else {
			checkPayFormInProgress = true;
			hideAlert();
			var arrReq = new Array();

			<cfif len(local.extrapayJS)>
				var thisForm = document.forms["frmPurchasepageWell"];
				#local.extrapayJS#
			</cfif>
			
			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				validationPassed = false;
			}

			if (validationPassed) {
				/* change text of payment buttons and leave disabled */
				$('button[type="submit"]',$('##frmPurchasepageWell')).each(function(index,thisButton){
					$(thisButton).text('Please Wait...');
				});
			} else {
				/* reenable buttons */
				$('button[type="submit"]',$('##frmPurchasepageWell')).each(function(index,thisButton){
					$(thisButton).removeAttr("disabled");
				});
			}
			checkPayFormInProgress = false;
		}
		
		return validationPassed;		
	};
	</script>  

    </cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.referralPayJS)#">