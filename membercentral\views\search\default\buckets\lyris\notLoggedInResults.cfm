<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<cfif local.hasDepositionBucket>
	<div id="MCNotLoggedInContainer"<cfif val(local.strResultsCount.itemCount) GT 0> style="display:none;"</cfif>>
		<cfif local.strResultsCount.itemCount EQ 0>
			<div class="s_rnfne">#showCommonNotFound(bucketID=arguments.bucketID, searchID=arguments.searchID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#</div>
		<cfelse>
			<div style="padding-bottom:10px;">
				<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
				<button class="tsAppBodyButton bucketHeaderButton" onclick="document.location.href='/?pg=listViewer&lsAction=listBrowse'"><i class="icon-envelope-alt"></i> Browse My Lists</button>
			</div>

			<cfif val(local.strResultsCount.itemCount) gt 0>
				<div class="tsAppBodyText tsAppBB" style="padding-bottom:3px;"><strong>#Numberformat(local.strResultsCount.itemCount)#</strong> messages found</div>
			</cfif>
			#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#
		</cfif>
	</div>
	<cfif val(local.strResultsCount.itemCount) GT 0>
		<div id="MCSearchSummaryContainer">
			<cfif len(trim(local.stsearchVerboseNoName))>
				<div>
					<strong>Search Criteria:</strong> <em>#local.stsearchVerboseNoName#</em>
				</div>
			</cfif>

			<div class="bk-mb-3 bk-mt-4">
				<button type="button" class="tsAppBodyButton" onclick="b_showLoginContainer();">View Messages</button>
			</div>

			<div class="bk-d-flex bk-flex-wrap bk-mt-3">
				<div id="MCSearchSummaryBarChart" class="bk-mb-3"></div>
			</div>

			<cfif len(local.strSearchSummary.uniqueLawyers) and len(local.strSearchSummary.mostrecentmessage)>
				<div class="bk-d-flex bk-mt-4 bk-flex-wrap">
					<div class="bk-d-flex bk-mr-5 bk-list-search-circle bk-mb-3">
						<div class="bk-summary-circle bk-text-light">#numberFormat(local.strSearchSummary.totalCount)#</div>
						<div class="bk-d-flex bk-align-self-center bk-pl-3 bk-font-weight-bold">Messages</div>
					</div>
					<div class="bk-d-flex bk-mr-5 bk-list-search-circle bk-mb-3">
						<div class="bk-summary-circle bk-text-light">#numberFormat(local.strSearchSummary.uniqueLawyers)#</div>
						<div class="bk-d-flex bk-align-self-center bk-pl-3 bk-font-weight-bold">Unique Lawyers Posting</div>
					</div>
					<div class="bk-d-flex bk-mr-3 bk-list-search-circle bk-mb-3">
						<div class="bk-summary-circle bk-text-light">#DateFormat(local.strSearchSummary.mostrecentmessage,"mmm<br />yyyy")#</div>
						<div class="bk-d-flex bk-align-self-center bk-pl-3 bk-font-weight-bold">Most Recent Message</div>
					</div>
				</div>
			</cfif>
		</div>
		#showFooter(viewDirectory=arguments.viewDirectory)#
	</cfif>
<cfelse>
	<div style="padding-bottom:10px;">
		<button class="tsAppBodyButton bucketHeaderButton" onclick="document.location.href='/?pg=listViewer&lsAction=listBrowse'"><i class="icon-envelope-alt"></i> Browse My Lists</button>
	</div>
	<cfif (not variables.cfcuser_isSiteAdmin and val(local.qryBucketInfo.restrictToGroupID) gt 0 and local.qryBucketInfo.isMemberInRestrictedGroup is not 1)>
		<cfif not variables.cfcuser_isLoggedIn>	
			<div class="tsAppBodyText tsAppBB" style="padding-bottom:3px;">You are not authorized to see these results.</div>	
			#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#						
		<cfelse>
			<div class="tsAppBodyText" style="padding-bottom:3px;">You are not authorized to see these results.</div>
		</cfif>
	<cfelseif local.strResultsCount.itemCount EQ 0>
		<div class="s_rnfne"><cfoutput>#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName)#</cfoutput></div>
	<cfelse>
		<cfif val(local.strResultsCount.itemCount) gt 0>	
			<div class="tsAppBodyText tsAppBB" style="padding-bottom:3px;">#Numberformat(local.strResultsCount.itemCount)# messages found</div>	
		</cfif>	
		<cfif not variables.cfcuser_isLoggedIn>	
			#showNotLoggedInResults(viewDirectory=arguments.viewDirectory)#					
		</cfif>
	</cfif>
</cfif>
</cfoutput>