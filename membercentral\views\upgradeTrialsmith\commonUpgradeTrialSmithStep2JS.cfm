<cfsavecontent variable="local.upgradeTrialSmithStep2Head">		
	<cfoutput>
	<script type="text/javascript">    

        function editUpgradeTrialSmithStep3() {
			$('##upgradeTrialSmithSelectedPlanContainer').hide();
			$('##upgradeTrialSmithPlanContainer').show(300);
		}

        function loadStep2(payload){
            $('##upgradeTrialSmithStep2SaveLoading')
            .html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
            .load('#arguments.event.getValue('upgradetrialsmithurl')#&action=saves2',payload,
                function(resp) {
                    let respObj = JSON.parse(resp);
                    if(typeof(respObj.redirectlocation) != 'undefined'){
                        window.location = respObj.redirectlocation;
                    }
                    let arrSteps = respObj.loadsteps.split(',');
                    let arrClearSteps = respObj.clearsteps.split(',');
                    var arrReq = new Array();

                    if ($.trim(respObj.codeerr).length) arrReq[arrReq.length] = respObj.codeerr;

                    if (arrReq.length) {
                        $('html, body').animate({
                            scrollTop: $('##upgradeTrialSmithStep2').offset().top - 75
                        }, 750);

                        $('##upgradeTrialSmithStep2 ##upgradeTrialSmithPlanContainer').prepend('<div class="alert alert-danger planError">'+arrReq.join('</br>')+'</div>')
                    }else{
                        $('##upgradeTrialSmithSelectedPlan').html(respObj.selectedplanstring);
                    
                        $('##upgradeTrialSmithPlanContainer,##upgradeTrialSmithStep2SaveLoading').hide();
                        $('##upgradeTrialSmithSelectedPlanContainer').show(300);                    

                        $('html, body').animate({
                            scrollTop: $('##upgradeTrialSmithStep'+arrSteps[0]).offset().top - 75
                        }, 750);
                        
                        arrClearSteps.forEach(function(step,index) {
                            $('##upgradeTrialSmithStep'+step).html('');
                        });

                        arrSteps.forEach(function(step,index) {
                            loadUpgradeTrialSmithSteps(step);
                        });

                    }
                }
            )
            .show();
        }

        function promoSubmit(mode){
            $(".planError").remove();
            if(mode){
                var arrReq = new Array();
                var payload = {};
                if($.trim($("##promoCode").val()).length){
                    payload = {addpromocode:1,promocode:$.trim($("##promoCode").val())};
                    loadStep2(payload);
                }else{
                    if ($.trim($('##promoCode').val()).length == 0) arrReq[arrReq.length] = 'Enter a valid promotional code.';

                    if (arrReq.length) {
                        $('html, body').animate({
                            scrollTop: $('##upgradeTrialSmithStep2').offset().top - 75
                        }, 750);

                        $('##upgradeTrialSmithStep2 ##upgradeTrialSmithPlanContainer').prepend('<div class="alert alert-danger planError">'+arrReq.join('</br>')+'</div>')
                    }
                }
                
            }else{
                var payload = {};
                payload = {removepromocode:1};
                loadStep2(payload);
            }
        }

		$(function() {
            $('input[type=radio][name=membertype]').change(function() {
                $(".planError").remove();
                var payload = {};
                if(this.value.length){
                    $("##promoCode").val('');
                    if($.trim($("##btnPromo").val()).length){
                        payload = {membertype:this.value,promocode:$.trim($("##btnPromo").val())};
                    }else{
                        payload = {membertype:this.value};
                    }
                    
                    loadStep2(payload);
                }                
            });
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.upgradeTrialSmithStep2Head)#">