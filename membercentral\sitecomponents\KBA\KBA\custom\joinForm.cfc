<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		variables.applicationReservedURLParams = "fa";
		variables.baselink = "/?event=cms.showresource&resid=#this.siteResourceID#&mode=stream";
		local.formAction = arguments.event.getValue('fa','showLookup');
		local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser);

		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorIntroText", type="STRING", desc="Text to show above account locator", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Identify Yourself" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Continue" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="KBACC" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="PaymentProfileCodeCheck", type="STRING", desc="pay profile code for Check", value="PaybyCheck" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorActiveSubscriptionFound", type="CONTENTOBJ", desc="Error message when active subscription found", value="Knoxville Bar Association records indicate you are currently have a Membership on the Knoxbar.org website." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorBilledSubscriptionFound", type="CONTENTOBJ", desc="Error message when billed subscription found", value="You need to renew your Membership. You will be re-directed to your renewal shortly." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="BilledJoinMessage", type="CONTENTOBJ", desc="Message displayed when account selected billed subscription", value="Our records indicate your membership application is currently being processed." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Content on Confirmation", value="Thank you.  Your Membership application has been submitted.   You will receive an email from Knoxville Bar Association with the information you provided." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="contentMembershipInfo", type="CONTENTOBJ", desc="Content above Membership Information", value="Fields marked with a * are required." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);		
		local.tmpField = { name="subscriptionUID",type="STRING",desc="UID of the Root Subscription that this form offers",value="4597e948-336a-4ead-bd86-d79db5d3a400" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="memberBenefitsHistCode",type="STRING",desc="These selections are mapped to History codes.",value="clepass,mediatorlisting,lawyersconcerned,lrisprogram,lawfirmlisting,committees,networking,memberbenefits" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);	
		local.tmpField = { name="memberBenefitsHistIntroText", type="CONTENTOBJ", desc="Member Benefits information", value="You will receive additional information about all the benefits of membership in KBA or available programs in which you can participate. Please indicate your interest in the following options:" }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="memberAdditionalInformationIntroText", type="CONTENTOBJ", desc="Additional Information text for content block", value="Dues paid to the Knoxville Bar Association are not deductible for income tax purposes as a charitable contribution." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);			
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
		/* ***************** */
		/* set form defaults */
		/* ***************** */
		StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmJoin',
			formNameDisplay='Membership Application',
			orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
			memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
		));
		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="join");
		/* ******************* */
		/* Member History Vars */
		/* ******************* */
		variables.useHistoryID = 0;
		if (int(val(arguments.event.getValue('useHistoryID',0))) gt 0)
			variables.useHistoryID = int(val(arguments.event.getValue('useHistoryID')));
		if (not val(variables.useHistoryID) and structKeyExists(session, "useHistoryID") and int(val(session.useHistoryID)) gt 0)
			variables.useHistoryID = int(val(session.useHistoryID));			
		variables.qryHistoryStarted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MBRApp', subName='Started');
		variables.qryHistoryCompleted = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='MBRApp', subName='Completed');
		variables.historyStartedText = "Member started Membership form.";
		variables.historyCompletedText = "Member completed Membership form.";

		/* ***************** */
		/* Form Process Flow */
		/* ***************** */
		switch (local.formAction) {
			case "processLookup":
				switch (processLookup()) {
					case "success":
						local.returnHTML = showMemberInfo(rc=arguments.event.getCollection());
						break;		
					case "activefound":
						local.returnHTML = showError(errorCode='activefound');
						break;		
					case "acceptedfound":
						local.returnHTML = showError(errorCode='acceptedfound');
						break;
					case "billedjoinfound":
						local.returnHTML = showError(errorCode='billedjoinfound');
						break;
					case "billedfound":
						local.returnHTML = showError(errorCode='billedfound');
						break;		
					default:
						application.objCommon.redirect(variables.baselink);
						break;				
				}
				break;
			case "processMemberInfo":
				switch (processMemberInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
						break;
					case "spam":
							local.returnHTML = showError(errorCode='spam');
							break;
					default:
						local.returnHTML = showError(errorCode='failsavemember');
						break;				
				}
				break;
			case "processMembershipInfo":
				switch (processMembershipInfo(rc=arguments.event.getCollection())) {
					case "success":
						local.returnHTML = showPayment(rc=arguments.event.getCollection());
						break;
					default:
						local.returnHTML = showError(errorCode='failsavemembership');
						break;				
				}
				break;
			case "processPayment":
				switch (processPayment(rc=arguments.event.getCollection())) {
					case "success":
						local.confirmationHTML = produceConfirmation(rc=arguments.event.getCollection());
						local.returnHTML = showConfirmation(confirmationHTML=local.confirmationHTML);
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						structDelete(session, "formFields");
						break;
					default:
						local.returnHTML = showError(errorCode='failpayment');
						break;				
				}
				break;
			case "showMembershipInfo":
				local.returnHTML = showMembershipInfo(rc=arguments.event.getCollection());
				break;				
			default:
				local.returnHTML = showLookup();
				break;
		}

		local.returnHTML = "<div id='div#variables.formName#wrapper'>#local.returnHTML#</div><div id='div#variables.formName#loading'></div>";

		return returnAppStruct(local.returnHTML,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="showLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset variables.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.orgID)>
			
		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
			</style>
			#variables.pageJS#
			<script type="text/javascript">
				var step = 0;
				var prevStep = 0;
				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);	
				}
				function assignMemberData(memObj) {
					$('###variables.formName# ##memberID, ###variables.formName# ##origMemberID').val(memObj.memberID);
					mc_continueForm($('###variables.formName#'),afterFormLoad);
				}
				function toggleFTM() {
				}
				function validatePaymentForm(isPaymentRequired) {
					if(isPaymentRequired == 'YES'){
						var arrReq = mccf_validatePPForm();
						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg);
							return false;
						}
					}
					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}

				window.onhashchange = function() {       
				    if (location.hash.length > 0) {        
				        step = parseInt(location.hash.replace('##',''),10);     
				        if (prevStep > step){
				        	if(step==1)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("processLookup");
							if(step==2)
				        		$('##'+$('###variables.formName#').attr("name")+' ##fa').val("showMembershipInfo");
				        	mc_loadDataForForm($('###variables.formName#'),'previous');			        	
				        }
				    } else {
				        step = 1;
				    }
				    prevStep = step;				    
				}				
				
				$(document).ready(function() {
				<cfif variables.useMID and NOT local.isSuperUser>					
					var mo = { memberID:#variables.useMID# };
					assignMemberData(mo);					
				<cfelseif local.isSuperUser>
					$('div##div#variables.formName#wrapper').hide();
					$('div##div#variables.formName#loading').html('<i class="icon-exclamation icon-2x"></i> This form is not available when logged in as a SuperUser.').css('text-align','center').show();
				</cfif>
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#">
			<cfinput type="hidden" name="fa" id="fa" value="processLookup">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#variables.useMID#">
						
			<cfif len(variables.strPageFields.AccountLocatorIntroText)>
				<div id="AccountLocatorIntroText">#variables.strPageFields.AccountLocatorIntroText#</div><br/>
			</cfif>											
			<div class="tsAppSectionHeading">#variables.strPageFields.accountLocatorTitle#</div>
			<div class="tsAppSectionContentContainer">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
				<tr>
					<td width="175" style="text-align:center;">
						<button name="btnAddAssoc" type="button" class="tsAppBodyButton" onClick="selectMember()">#variables.strPageFields.AccountLocatorButton#</button>
					</td>
					<td class="tsAppBodyText">#variables.strPageFields.accountLocatorInstructions#</td>
				</tr>
				</table>
			</div>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processLookup" access="private" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.stReturn = "nomatch">

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=variables.useMID, orgID=variables.orgID)>
		<cfif local.qryMember.recordcount is not 1>
			<cfset local.stReturn = "nomatch">
		<cfelse>
			<cfset local.annualMembershipDuesUID = "25a454f8-cf5b-482f-b313-88b025698820">

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>
			<cfif listFindNoCase(valueList(local.qryActiveSubs.typeUID), local.annualMembershipDuesUID)>
				<cfset local.stReturn = "activefound">
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.typeUID), local.annualMembershipDuesUID)>
					<cfset local.stReturn = "acceptedfound">
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true,includeRate=true)>
					<cfif listFindNoCase(valueList(local.qryBilledSubs.typeUID), local.annualMembershipDuesUID)>
						<cfif local.qryBilledSubs.isRenewalRate>
							<cfset local.stReturn = "billedfound">
						<cfelse>
							<cfset local.stReturn = "billedjoinfound">
						</cfif>
					<cfelse>
						<cfset local.stReturn = "success">
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.stReturn>
	</cffunction>

	<cffunction name="showMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		
		<!--- Load prefill data: either the fields from new acct form or from the members table --->
		<cfset local.strPrefillMemberData = application.objCustomPageUtils.mem_PrefillMemberData(siteID=val(variables.siteID), orgID=val(variables.orgID), memberID=val(variables.useMID))>		
		<cfset local.isLoggedIn = application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

		<cfset local.strData = {}>
		<cfset local.strData.memberID = 0>
		<cfset local.p1Visited = 0>
		<cfset local.licenseTextArr = arrayNew(1)>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
			<cfset local.strData = session.formFields.step1>
			<cfset variables.origMemberID = session.formFields.step1.origMemberID>
			<cfif session.useHistoryID>
				<cfset variables.useHistoryID = session.useHistoryID>
			</cfif>	
			<cfset local.strData.memberID = variables.useMID>	
			<cfset local.p1Visited = 1>	
		<cfelse>
			<cfset variables.origMemberID = variables.useMID>
			<cfif local.isLoggedIn>
				<cfset local.strData.memberID = variables.useMID>
			</cfif>
		</cfif>

		<cfif not structKeyExists(local.strData, "orgID")>
			<cfset local.strData.orgID = variables.orgID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "siteID")>
			<cfset local.strData.siteID = variables.siteID>
		</cfif>		

		<cfif not structKeyExists(local.strData, "p1Visited")>
			<cfset local.strData.p1Visited = local.p1Visited>
		</cfif>	

		<!--- KBA - Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='1b48b29b-f734-4d68-a5eb-86efc0b39e79', mode="collection", strData=local.strData)>
		<!--- Join KBA - Practice Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='01e35d1f-1c2b-4f64-90df-f37ffff93aaa', mode="collection", strData=local.strData)>
		<!--- Mailing Address --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='678776e3-4ad4-44b5-8ee7-085a3d0b0a2a', mode="collection", strData=local.strData)>
		<!--- Physical Address --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='0b2a6134-3d12-4f7a-a42b-54780119d966', mode="collection", strData=local.strData)>
		<!--- Home Address --->		
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='1eaba621-ccff-4325-b9b2-fc4654409e23', mode="collection", strData=local.strData)>
		<!--- Alternate Address --->		
		<cfset local.strFieldSetContent9 = application.objCustomPageUtils.renderFieldSet(uid='c5196d5d-5f26-47e1-88b3-514cb4ac9481', mode="collection", strData=local.strData)>		
		<!--- Join KBA - Address Preferences --->		
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='515cf5f1-99d1-40b0-962c-8cbee5e2401b', mode="collection", strData=local.strData)>
		<!--- Join KBA - Optional Information --->		
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='f1851c77-1065-4331-9bf0-9ff755dae88d', mode="collection", strData=local.strData)>
		<!--- Membership Category --->	
		<cfset local.strFieldSetContent8 = application.objCustomPageUtils.renderFieldSet(uid='6000c633-1848-44a5-8a44-11019c55722e', mode="collection", strData=local.strData)>	

		
		<!--- get Kansas Bar Num, Admission Date --->
		<cfloop collection="#local.strFieldSetContent1.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent1.strFields[local.thisField] eq "Law School">
				<cfset local.lawSchool = local.thisField>
			<cfelseif local.strFieldSetContent1.strFields[local.thisField] eq "Law School Graduation Date (or expected date)">
				<cfset local.gradDate = local.thisField>
			</cfif>
		</cfloop>

		<!--- get Physical Address --->
		<cfloop collection="#local.strFieldSetContent4.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent4.strFields[local.thisField] eq "Address 1">
				<cfset local.memberPhisAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>	

		<!--- get Home Address --->
		<cfloop collection="#local.strFieldSetContent5.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent5.strFields[local.thisField] eq "Address 1">
				<cfset local.memberHomeAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<!--- get Alternate Address --->
		<cfloop collection="#local.strFieldSetContent9.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent9.strFields[local.thisField] eq "Address 1">
				<cfset local.memberAltAddress1 = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>

		<!--- get  Address Preferences --->
		<cfset local.memberPrefPrimAddress = "">
		<cfset local.memberPrefBillAddress = "">		
		<cfloop collection="#local.strFieldSetContent6.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent6.strFields[local.thisField] eq "Use this address for Primary">
				<cfset local.memberPrefPrimAddress = local.thisField>
			</cfif>
			<cfif local.strFieldSetContent6.strFields[local.thisField] eq "Use this address for Billing">
				<cfset local.memberPrefBillAddress = local.thisField>
			</cfif>			
		</cfloop>			

		<!--- get Membership Category --->
		<cfset local.memberCatNum = "">
		<cfloop collection="#local.strFieldSetContent8.strFields#" item="local.thisField">
			<cfif local.strFieldSetContent8.strFields[local.thisField] eq "Membership Category">
				<cfset local.memberCatNum = local.thisField>
				<cfbreak>
			</cfif>
		</cfloop>		

		<cfset local.profLicenseIDList = "">
		<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
			<cfset local.profLicenseIDList = local.strData.mpl_pltypeid>
		</cfif>	

		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgid)>	

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<script language="javascript">
				function checkCaptchaAndValidate(){
					var thisForm = document.forms["#variables.formName#"];
					var status = false;
					var captcha_callback = function(captcha_response){
						if (captcha_response.response && captcha_response.response != 'success') {
							status = false;
						} else {
							status = true;
						}
					}
					if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					} else {
						#variables.captchaDetails.jsvalidationcode#
					}
					if(status){
						return validateMemberInfoForm();
					} else {
						alert('Please enter the correct code shown in the graphic.');
						return false;
					}
				}
				function validateMemberInfoForm(){
					var _CF_this = document.forms['#variables.formName#'];
					var arrReq = new Array();

					#local.strFieldSetContent1.jsValidation#
					#local.strFieldSetContent2.jsValidation#
					#local.strFieldSetContent3.jsValidation#
					#local.strFieldSetContent4.jsValidation#
					#local.strFieldSetContent5.jsValidation#
					#local.strFieldSetContent6.jsValidation#
					#local.strFieldSetContent7.jsValidation#					
					#local.strFieldSetContent8.jsValidation#
					#local.strFieldSetContent9.jsValidation#

					var prof_license = $('##mpl_pltypeid').val();
					var isProfLicenseSelected = false;
					if(prof_license != "" && prof_license != null){
						isProfLicenseSelected = true;
						$.each(prof_license,function(i,val){
							var text = $("##mpl_"+val+"_licensenumber").parent().prev().text();
							if($("##mpl_"+val+"_licensenumber").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your '+text +' License  Number.'; }
							if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
							if($("##mpl_"+val+"_status").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Please select   '+text +' License Status.'; }
						});

					}

					var memberPrefPrimAddress = "";
					<cfif len(trim(local.memberPrefPrimAddress))>
						memberPrefPrimAddress = $('###variables.formName# ###local.memberPrefPrimAddress# option:selected').text();
					</cfif>

					var memberPrefBillAddress = "";
					<cfif len(trim(local.memberPrefBillAddress))>
						memberPrefBillAddress = $('###variables.formName# ###local.memberPrefBillAddress# option:selected').text();
					</cfif>		

					<cfif isDefined("local.memberPhisAddress1")>						
						if ( (memberPrefPrimAddress == "Physical Address" || memberPrefBillAddress == "Physical Address") && $.trim($('###variables.formName# ###local.memberPhisAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Physical Address is required.";
					</cfif>

					<cfif isDefined("local.memberHomeAddress1")>						
						if ( (memberPrefPrimAddress == "Home Address" || memberPrefBillAddress == "Home Address") && $.trim($('###variables.formName# ###local.memberHomeAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Home Address is required.";
					</cfif>

					<cfif isDefined("local.memberAltAddress1")>						
						if ( (memberPrefPrimAddress == "Alternate Address" || memberPrefBillAddress == "Alternate Address") && $.trim($('###variables.formName# ###local.memberAltAddress1#').val()) == '')			
							arrReq[arrReq.length] = " Alternate Address is required.";
					</cfif>						

					<cfif len(trim(local.memberCatNum))>
						var mcSel = $('###variables.formName# ###local.memberCatNum# option:selected').text();
						if (mcSel == 'Law Student') {
							<cfif isDefined("local.lawSchool") and len(trim(local.lawSchool))>
								if ($.trim($('###variables.formName# ###local.lawSchool#').val()) == '') arrReq[arrReq.length] = "Law School is required.";
							</cfif>
							<cfif isDefined("local.gradDate") and len(trim(local.gradDate))>
								if ($.trim($('###variables.formName# ###local.gradDate#').val()) == '') arrReq[arrReq.length] = "Graduation Date is required.";
							</cfif>
						} 	
						else{ 
							if (mcSel != '' && !isProfLicenseSelected)
								arrReq[arrReq.length] = "Professional License is required.";
						}					
					</cfif>								

					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}

					mc_continueForm($('###variables.formName#'),afterFormLoad);
					return false;
				}
				function prefillData() {
					var objPrefill = new Object();
					<cfloop collection="#local.strPrefillMemberData#" item="local.thisKey">
						#toScript(local.strPrefillMemberData[local.thisKey],"objPrefill.#local.thisKey#")#
					</cfloop>
					for (var key in objPrefill) {
						if (objPrefill.hasOwnProperty(key)) { 
							$('###variables.formName# ##'+key).val(objPrefill[key]);
						}
					}
				}

				function afterFormLoad(){
					$('html, body').animate({ scrollTop: 0 }, 500);
				}

				$(document).ready(function() {
					prefillData();
					<cfif structKeyExists(local.strData, "mccf_firstTimeMember")>
					toggleFTM();
					</cfif>
					<cfif listLen(local.profLicenseIDList)>
						$("##state_table").show();
					</cfif>

					<cfif NOT structKeyExists(session, "captchaEntered")>
						showCaptcha();
					</cfif>

					$("##mpl_pltypeid").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 1,
						minWidth: 400,
						click: function(event, ui){
							if(ui.checked){
								$("##state_table").show();
								$('##state_table tr:last').after('<tr id="tr_state_'+ui.value+'">'
																+'<td align="right" class="tsAppBodyText">'+ui.text+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="13" name="mpl_'+ui.value+'_licensenumber"id="mpl_'+ui.value+'_licensenumber" type="text" value="" />'
																+'	<input name="mpl_'+ui.value+'_licensename" id="mpl_'+ui.value+'_licensename" type="hidden" value="'+ui.text+'" />'
																+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<input size="13" maxlength="10" name="mpl_'+ui.value+'_activeDate" id="mpl_'+ui.value+'_activeDate" type="text" value="" class="tsAppBodyText" />'
																+'</td>'
																+'<td align="center" class="tsAppBodyText">'
																+'	<select name="mpl_'+ui.value+'_status" id="mpl_'+ui.value+'_status"><option value="">Please Select</option><option value="active">Active</option><option value="inactive">Inactive</option><option value="disbarred">Disbarred</option><option value="suspended">Suspended</option></select>'
																+'</td>'
														+'</tr>');
								if ($("##tr_state_"+ui.value).is(':visible') &&  $('##mpl_'+ui.value+'_activeDate').is(':visible')) {
									mca_setupDatePickerField('mpl_'+ui.value+'_activeDate');
								}
								$('##mpl_'+ui.value+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color:##fff; cursor:text')
								}else{
								$("##tr_state_"+ui.value).remove();
							}
					   },
					});
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<form name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" <cfif structKeyExists(session, "captchaEntered")> onsubmit="return validateMemberInfoForm();"<cfelse> onsubmit="return checkCaptchaAndValidate();"</cfif>>
			<input type="hidden" name="fa" id="fa" value="processMemberInfo">
			<input type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<input type="hidden" name="origMemberID" id="origMemberID" value="#variables.origMemberID#">
			<input type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<input type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa,memberID">
			<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
			
			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<cfif len(variables.strPageFields.contentMembershipInfo)>
				<div>#variables.strPageFields.contentMembershipInfo#</div>
				<br/>
			</cfif>			

			<div class="tsAppSectionHeading">#local.strFieldSetContent1.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent1.fieldSetContent#

				<table cellpadding="3" border="0" cellspacing="0">		
					<tr class="top">
						<th class="tsAppBodyText" colspan="3" align="left">
							<b>Please add all State Licensures. For #variables.strProfLicenseLabels.profLicenseNumberLabel#, please be sure to use any leading zeroes on the number.</b>
						</th>
					</tr>							
					<tr align="top">
						<td class="tsAppBodyText" width="10">&nbsp;</td>
						<td class="tsAppBodyText" width="365">Professional License:</td>
						<td class="tsAppBodyText">
							<select id="mpl_pltypeid" name="mpl_pltypeid" multiple="multiple">
								<cfloop query="local.qryOrgPlTypes">
									<cfset  local.licenseTextArr[local.qryOrgPlTypes.pltypeid] = local.qryOrgPlTypes.PLName>
									<option value="#local.qryOrgPlTypes.pltypeid#" <cfif listFind(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>
								</cfloop>
							</select>
						</td>
					</tr>
					<tr class="top">
						<td class="tsAppBodyText" width="10"></td>
						<td class="tsAppBodyText"></td>
						<td class="tsAppBodyText"></td>
					</tr>
				</table>
				<table cellpadding="3" border="0" cellspacing="0">
					<tr>
						<td class="tsAppBodyText" width="375">&nbsp;</td>
						<td>
							<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
								<thead>
									<tr valign="top">
										<th align="center" class="tsAppBodyText">State Name</th>
										<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
										<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
										<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
									</tr>
								</thead>
								<tbody>
								<cfif structKeyExists(local.strData, "mpl_pltypeid") and listLen(local.strData.mpl_pltypeid)>
									<cfloop array="#listToArray(local.strData.mpl_pltypeid)#" index="local.thisItem">
										<cfset  local.license_no  = local.strData['mpl_#local.thisItem#_licensenumber']>
										<cfset  local.license_date  = local.strData['mpl_#local.thisItem#_activeDate']>
										<cfset  local.license_status  = local.strData['mpl_#local.thisItem#_status']>
										<tr id="tr_state_#local.thisItem#">
											<td align="right" class="tsAppBodyText">#local.licenseTextArr[local.thisItem]#</td>
											<td align="center" class="tsAppBodyText">
												<input name="mpl_#local.thisItem#_licensenumber"id="mpl_#local.thisItem#_licensenumber" class="tsAppBodyText" type="text" value="#local.license_no#" size="13" maxlength="13" />
												<input name="mpl_#local.thisItem#_licensename"id="mpl_#local.thisItem#_licensename" type="hidden" value="#local.licenseTextArr[local.thisItem]#" />
											</td>
											<td align="center" class="tsAppBodyText">
												<input name="mpl_#local.thisItem#_activeDate" id="mpl_#local.thisItem#_activeDate" type="text" value="#local.license_date#" class="tsAppBodyText" size="13" maxlength="10" />
												<cfsavecontent variable="local.datejs">
													<cfoutput>
													<script language="javascript">
														$(document).ready(function() { 
															mca_setupDatePickerField('mpl_#local.thisItem#_activeDate');
														});
													</script>
													<style type="text/css">
													##mpl_#local.thisItem#_activeDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
													</style>
													</cfoutput>
												</cfsavecontent>
												<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
											</td>
											<td align="center" class="tsAppBodyText">
												<select name="mpl_#local.thisItem#_status" id="mpl_#local.thisItem#_status" class="tsAppBodyText">
													<option value="">Please Select</option>
													<option <cfif local.license_status eq "active">selected="selected"</cfif> value="active">Active</option>
													<option <cfif local.license_status eq "inactive">selected="selected"</cfif>value="inactive">Inactive</option>
													<option <cfif local.license_status eq "disbarred">selected="selected"</cfif>value="disbarred">Disbarred</option>
													<option <cfif local.license_status eq "suspended">selected="selected"</cfif>value="suspended">Suspended</option>
												</select>
											</td>
										</tr>
									</cfloop>
								</cfif>									
								</tbody>
							</table>
						</td>
					</tr>					
				</table>
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent2.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent2.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent3.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent3.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent4.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent4.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent5.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent5.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent9.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent9.fieldSetContent#
			</div>			

			<div class="tsAppSectionHeading">#local.strFieldSetContent6.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent6.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent7.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent7.fieldSetContent#
			</div>

			<div class="tsAppSectionHeading">#local.strFieldSetContent8.fieldSetTitle#</div>
			<div class="tsAppSectionContentContainer">
				#local.strFieldSetContent8.fieldSetContent#
			</div>
			<div class="row-fluid">
				<div class="span12">							
					#variables.captchaDetails.htmlContent#
				</div>
			</div> 
			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>

			#application.objWebEditor.showEditorHeadScripts()#
			<script language="javascript">				
				function editContentBlock(cid,srid,tname) {
					var editMember = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							$('##frmmd_'+cid).html(r.html);
							var x = div.getElementsByTagName("script");
							for(var i=0;i<x.length;i++) eval(x[i].text); 
						}
					};
					var objParams = { frmField:'md_'+cid, siteResourceID:srid, tools:tname };
					TS_AJX('UPDATEMEMBER','showContentEditor',objParams,editMember,editMember,10000,editMember);
				}			
			</script>
			</form>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMemberInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldNamesList" type="string" required="false" default="">
		<cfargument name="doNotIncludeList" type="string" required="false" default="">		

		<cfset var local = structNew()>

		<cfif (structKeyExists(arguments.rc, "iAgree")) 
			OR (structKeyExists(arguments.rc, "captcha") and NOT len(arguments.rc.captcha)) 
			OR (structKeyExists(arguments.rc, "captcha") and structKeyExists(arguments.rc, "captcha_check") and application.objCustomPageUtils.validateCaptcha(code=arguments.rc.captcha,captcha=arguments.rc.captcha_check).response NEQ "success")>
			<cfset local.response = "spam">
			<cfreturn local.response>
		</cfif>	

		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=variables.orgid).additionalDataXML>	

		<!--- save member info and record history --->		
		<cfset local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.rc)>
		<cfif local.strResult.success>
			<cfset variables.useMID = local.strResult.memberID>

			<!--- Only add shistory if step 1 is visited for the first time --->
			<cfif not structKeyExists(session, "formFields")>
				<cfset variables.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, categoryID=variables.qryHistoryStarted.categoryID, 
													subCategoryID=variables.qryHistoryStarted.subCategoryID, description=variables.historyStartedText, 
													enteredByMemberID=variables.useMID, newAccountsOnly=false)>	
				<cfset session.useHistoryID = variables.useHistoryID>			
			</cfif>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step1")>
				<cfset structDelete(session.formFields, "step1")>
			</cfif>			
			<cfset session.formFields.step1 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>										

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		 
		<cfset local.doNotIncludeList = "fa,memberID,origMemberID,useHistoryID">
		<cfset local.strData = {}>
		<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
			<cfset local.strData = session.formFields.step2>
		</cfif>		
	
 		<cfset local.result = application.objCustomPageUtils.renderSubscriptionForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID, 
				strData=local.strData
				)>

		<cfset local.qryMemberBenefits = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode=variables.strPageFields.memberBenefitsHistCode)>
		<cfsavecontent variable="local.headCode">
            <cfoutput>
                <script type="text/javascript">	
                    #local.result.jsAddonValidation#
				    function validateMembershipInfoForm(){
						var arrReq = new Array();
						#local.result.JSVALIDATION#

						if($("##sub"+"#local.subscriptionID#"+"_rateFrequencySelected").val().length == 0){
							arrReq[arrReq.length] = " Select Membership.";
						}	
						if (!$('###variables.formName# ##mccf_verify').is(':checked')) arrReq[arrReq.length] = "You must agree to the statement that all provided information is current.";

						if (arrReq.length > 0) {
							var msg = '';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
							showAlert(msg,afterFormLoad);
							return false;
						}		

						mc_continueForm($('###variables.formName#'),afterFormLoad);
						return false;
					}

					function afterFormLoad(){
						$('html, body').animate({ scrollTop: 0 }, 500);
					}	
                </script>
            </cfoutput>
        </cfsavecontent>
        <cfhtmlhead text="#local.headCode#">
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validateMembershipInfoForm()">
			<cfinput type="hidden" name="fa" id="fa" value="processMembershipInfo">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#arguments.rc.useHistoryID#">
			<cfloop collection="#session.formFields.step1#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#session.formFields.step1[local.thisField]#">
					<cfset local.doNotIncludeList = listAppend(local.doNotIncludeList,local.thisField)>
				</cfif>
			</cfloop>
			<cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="#local.doNotIncludeList#">

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						#local.result.formcontent#
						<div class="addonMessageArea"></div>
						<cfif local.qryMemberBenefits.recordCount>	
						<br/><br/>	
						<div>					
							<div class="well subAddonWrapper" id="memberBenefitsHist">
								<legend>Member Benefits</legend>
								<cfif len(variables.strPageFields.memberBenefitsHistIntroText)>
									<div id="memberBenefitsHistIntroText">#variables.strPageFields.memberBenefitsHistIntroText#</div><br/>
								</cfif>
								<div class="addonMessageArea"></div>
								<div>									
									<cfloop query="local.qryMemberBenefits">
										<div class="">
											<label class="checkbox subLabel" for="mccf_#local.qryMemberBenefits.categoryCode#Checkbox">
												<input class="subCheckbox" type="checkbox" name="mccf_memberBenefitsHistCheckbox" id="mccf_#local.qryMemberBenefits.categoryCode#Checkbox" value="#local.qryMemberBenefits.categoryCode#" <cfif structKeyExists(local.strData, 'mccf_memberBenefitsHistCheckbox') and listFindNoCase(local.strData.mccf_memberBenefitsHistCheckbox, local.qryMemberBenefits.categoryCode)>checked="checked"</cfif>> 
												<span class="labelText">#local.qryMemberBenefits.categoryName#</span>
											</label>
										</div>
									</cfloop>									
								</div>
							</div>	
						</div>
						</cfif>		
						<br/><br/>	
						<div>					
							<div class="well subAddonWrapper" id="memberAdditionalInformation">
								<legend>Additional Information</legend>
								<cfif len(variables.strPageFields.memberAdditionalInformationIntroText)>
									<div id="memberAdditionalInformationIntroText">#variables.strPageFields.memberAdditionalInformationIntroText#</div><br/>
								</cfif>
								<div class="addonMessageArea"></div>
								<div>									
									<div class="">
										<label class="checkbox subLabel" for="mccf_verify">
											<input class="subCheckbox" type="checkbox" name="mccf_verify" id="mccf_verify" value="1" <cfif structKeyExists(local.strData, 'mccf_verify')>checked="checked"</cfif>> 
											<span class="labelText">I verify all information contained in this membership application is current.</span>
										</label>
									</div>								
								</div>
							</div>	
						</div>	
						<br/><br/>								
					</div>	<!--- /div class="span12" --->
				</div>	<!--- /div class="row-fluid" --->
			</div> <!--- /div class="container-fluid" --->

			<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
			<button name="btnBack" id="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'processLookup');">&lt;&lt; Back</button>	

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processMembershipInfo" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- Updates fields if needed here  --->

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>
		<cfset local.strResult = application.objCustomPageUtils.getSubscriptionStructFromForm(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				rc = arguments.rc)>

		<cfif local.strResult.success>
			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "step2")>
				<cfset structDelete(session.formFields, "step2")>
			</cfif>			
			<cfset session.formFields.step2 = application.objCustomPageUtils.createSessionStructure(rc=arguments.rc)>

			<cfif structKeyExists(session, "formFields") and structKeyExists(session.formFields, "substruct")>
				<cfset structDelete(session.formFields, "substruct")>
			</cfif>			
			<cfset session.formFields.substruct = local.strResult.subscription>

			<cfset local.response = "success">
		<cfelse>
			<cfset local.response = "failure">
		</cfif>	

		<cfreturn local.response>
	</cffunction>

	<cffunction name="showPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0)>

		<cfif local.paymentRequired>
			<cfset local.arrPayMethods = [ variables.strPageFields.PaymentProfileCodeCC, variables.strPageFields.PaymentProfileCodeCheck ]>
			<cfset local.strReturn = 
				application.objCustomPageUtils.renderPaymentForm(
					arrPayMethods=local.arrPayMethods, 
					siteID=variables.siteID, 
					memberID=variables.useMID, 
					title="Choose Your Payment Method", 
					formName=variables.formName, 
					backStep="showMembershipInfo"
				)>

			<cfsavecontent variable="local.headcode">
				<cfoutput>#local.strReturn.headcode#</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.headcode#">
		</cfif>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
 			<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return validatePaymentForm(#local.paymentRequired#)">
			<cfinput type="hidden" name="fa" id="fa" value="processPayment">
			<cfinput type="hidden" name="memberID" id="memberID" value="#variables.useMID#">
			<cfinput type="hidden" name="origMemberID" id="origMemberID" value="#arguments.rc.origMemberID#">
			<cfinput type="hidden" name="useHistoryID" id="useHistoryID" value="#variables.useHistoryID#">
			<cfloop collection="#arguments.rc#" item="local.thisField">
				<cfif left(local.thisField,2) eq "m_" or left(local.thisField,3) eq "me_" or left(local.thisField,3) eq "mw_" or left(local.thisField,4) eq "mpl_"
					or left(local.thisField,3) eq "ma_" or left(local.thisField,4) eq "mat_" or left(local.thisField,3) eq "mp_" or left(local.thisField,3) eq "md_"
					or left(local.thisField,5) eq "mccf_"
					or left(local.thisField,3) eq "sub">
					<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.rc[local.thisField]#">
				</cfif>
			</cfloop>

			<div id="divFrmErr" class="alert" style="display:none;margin:6px 0;"></div>

			<div class="tsAppSectionHeading">Membership Selections Confirmation</div>
			<div class="tsAppSectionContentContainer">						
				#local.strResult.formContent#
				<br/>
			</div>
			<div class="tsAppSectionHeading">Total Price</div>
			<div class="tsAppSectionContentContainer">						
				Amount Due: #dollarFormat(local.strResult.totalFullPrice)#
			</div>

			<br/><br/>

			<cfif local.paymentRequired>
				#local.strReturn.paymentHTML#
			<cfelse>
				<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();">Continue</button>
				<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###variables.formName#'),'showMembershipInfo');">&lt;&lt; Back</button>
			</cfif>

			</cfform>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>	

	<cffunction name="processPayment" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset application.objCustomPageUtils.mh_updateHistory(memberID=variables.useMID, historyID=variables.useHistoryID, 
					subCategoryID=variables.qryHistoryCompleted.subCategoryID, description=variables.historyCompletedText, 
					newAccountsOnly=false)>

		<cfif structKeyExists(arguments.rc,"mccf_memberBenefitsHistCheckbox") and listLen(arguments.rc.mccf_memberBenefitsHistCheckbox)>
			<cfset local.qryMemberBenefits = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode=variables.strPageFields.memberBenefitsHistCode)>
			<cfloop query="local.qryMemberBenefits">
				<cfloop list="#arguments.rc.mccf_memberBenefitsHistCheckbox#" index="local.thisItem">
					<cfif local.qryMemberBenefits.categoryCode eq local.thisItem>
						<cfset application.objCustomPageUtils.mh_addHistory(memberID=variables.useMID, 
																			categoryID=local.qryMemberBenefits.categoryID, 
																			subCategoryID=0, 
																			description=local.qryMemberBenefits.categoryName, 
																			enteredByMemberID=variables.useMID, 
																			newAccountsOnly=false)>
						<cfbreak>
					</cfif>					
				</cfloop>
			</cfloop>
		</cfif>

		<cfset local.response = "success">

		<cfreturn local.response>
	</cffunction>	

	<cffunction name="produceConfirmation" access="private" output="false" returntype="string">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- KBA - Member Information --->
		<cfset local.strFieldSetContent1 = application.objCustomPageUtils.renderFieldSet(uid='1b48b29b-f734-4d68-a5eb-86efc0b39e79', mode="confirmation", strData=arguments.rc)>
		<!--- Join KBA - Practice Information --->
		<cfset local.strFieldSetContent2 = application.objCustomPageUtils.renderFieldSet(uid='01e35d1f-1c2b-4f64-90df-f37ffff93aaa', mode="confirmation", strData=arguments.rc)>
		<!--- Mailing Address --->
		<cfset local.strFieldSetContent3 = application.objCustomPageUtils.renderFieldSet(uid='678776e3-4ad4-44b5-8ee7-085a3d0b0a2a', mode="confirmation", strData=arguments.rc)>
		<!--- Physical Address --->
		<cfset local.strFieldSetContent4 = application.objCustomPageUtils.renderFieldSet(uid='0b2a6134-3d12-4f7a-a42b-54780119d966', mode="confirmation", strData=arguments.rc)>
		<!--- Home Address --->		
		<cfset local.strFieldSetContent5 = application.objCustomPageUtils.renderFieldSet(uid='1eaba621-ccff-4325-b9b2-fc4654409e23', mode="confirmation", strData=arguments.rc)>
		<!--- Alternate Address --->		
		<cfset local.strFieldSetContent9 = application.objCustomPageUtils.renderFieldSet(uid='c5196d5d-5f26-47e1-88b3-514cb4ac9481', mode="confirmation", strData=arguments.rc)>		
		<!--- Join KBA - Address Preferences --->		
		<cfset local.strFieldSetContent6 = application.objCustomPageUtils.renderFieldSet(uid='515cf5f1-99d1-40b0-962c-8cbee5e2401b', mode="confirmation", strData=arguments.rc)>
		<!--- Join KBA - Optional Information --->		
		<cfset local.strFieldSetContent7 = application.objCustomPageUtils.renderFieldSet(uid='f1851c77-1065-4331-9bf0-9ff755dae88d', mode="confirmation", strData=arguments.rc)>
		<!--- Membership Category --->	
		<cfset local.strFieldSetContent8 = application.objCustomPageUtils.renderFieldSet(uid='6000c633-1848-44a5-8a44-11019c55722e', mode="confirmation", strData=arguments.rc)>	

		<cfset local.subscriptionID = application.objCustomPageUtils.sub_getSubscriptionFromUID(
				siteID=variables.siteID,
				uid = variables.strPageFields.subscriptionUID)>

		<cfset local.strResult = application.objCustomPageUtils.showSubscriptionFormSelections(
				subscriptionID = local.subscriptionID,
				memberID = variables.useMID,
				isRenewalRate = false,
				siteID = variables.siteID,
				strdata = arguments.rc)>

		<cfset local.paymentRequired = (local.strResult.totalFullPrice gt 0) >

		<cfset local.memberPayProfileDetail = "">
		<cfif local.paymentRequired>
			<cfif structKeyExists(arguments.rc,"p_#arguments.rc.mccf_payMethID#_mppid")>
				<cfset local.memberPayProfileSelected = int(val(arguments.rc["p_#arguments.rc.mccf_payMethID#_mppid"]))>
			<cfelse>
				<cfset local.memberPayProfileSelected = 0>
			</cfif>
			<cfif local.memberPayProfileSelected gt 0>
				<cfset local.memberPayProfileDetail = application.objPayments.getSavedInfoOnFile(mppid=local.memberPayProfileSelected, memberID=variables.useMID, profileID=arguments.rc.mccf_payMethID).detail>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.confirmationHTML">
			<cfoutput>
			<cfif len(variables.strPageFields.ConfirmationMessage)>
				<div>#variables.strPageFields.ConfirmationMessage#</div>
			</cfif>

			<!--@@specialcontent@@-->

			<div>Here are the details of your application:</div><br/>

			#local.strFieldSetContent1.fieldSetContent#
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KBA - Professional License Information</td>
			</tr>				
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<table cellpadding="3" border="0" cellspacing="0">						
						<tr valign="top">
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
								<cfif structKeyExists(arguments.rc, "mpl_pltypeid") and listLen(arguments.rc.mpl_pltypeid)>
		 							<table id="state_table" cellpadding="3" border="0" cellspacing="0">
										<thead>
											<tr valign="top">
												<th align="center" class="tsAppBodyText">State Name</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseNumberLabel#</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseDateLabel#</th>
												<th align="center" class="tsAppBodyText">#variables.strProfLicenseLabels.profLicenseStatusLabel#</th>
											</tr>
										</thead>
										<tbody>
										<cfloop list="#arguments.rc.mpl_pltypeid#" index="local.key">
											<tr id="tr_state_#local.key#">
												<td align="right" class="tsAppBodyText">#arguments.rc['mpl_#local.key#_licensename']#</td>
												<td align="center" class="tsAppBodyText">#arguments.rc['mpl_#local.key#_licensenumber']#</td>
												<td align="center" class="tsAppBodyText">#arguments.rc['mpl_#local.key#_activeDate']#</td>
												<td align="center" class="tsAppBodyText">#application.objCustomPageUtils.upperFirst(arguments.rc['mpl_#local.key#_status'])# </td>
											</tr>
										</cfloop>																	
										</tbody>
									</table>
								</cfif>	
							</td>
						</tr>						
					</table>
				</td>
			</tr>
			</table>
			<br/>		
			#local.strFieldSetContent2.fieldSetContent#
			#local.strFieldSetContent3.fieldSetContent#
			#local.strFieldSetContent4.fieldSetContent#
			#local.strFieldSetContent5.fieldSetContent#
			#local.strFieldSetContent9.fieldSetContent#
			#local.strFieldSetContent6.fieldSetContent#
			#local.strFieldSetContent7.fieldSetContent#
			#local.strFieldSetContent8.fieldSetContent#

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KBA - Membership Selections</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<div>#local.strResult.formContent#</div>
					<br/>
					<strong>Total Price:</strong> #dollarFormat(local.strResult.totalFullPrice)#
					<br/>					
				</td>
			</tr>
			</table>
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KBA - Member Benefits</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<cfif structKeyExists(arguments.rc,"mccf_memberBenefitsHistCheckbox") and listLen(arguments.rc.mccf_memberBenefitsHistCheckbox)>
						<cfset local.qryMemberBenefits = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode=variables.strPageFields.memberBenefitsHistCode)>
						<cfloop query="local.qryMemberBenefits">
							<cfloop list="#arguments.rc.mccf_memberBenefitsHistCheckbox#" index="local.thisItem">
								<cfif local.qryMemberBenefits.categoryCode eq local.thisItem>
									<div>#local.qryMemberBenefits.categoryName#</div>
									<cfbreak>
								</cfif>					
							</cfloop>
						</cfloop>
					<cfelse>
						<div>None selected</div>
					</cfif>
				</td>
			</tr>
			</table>
			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Join KBA - Additional Information</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<table cellpadding="3" border="0" cellspacing="0">
					<tr valign="top">
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>I verify all information contained in this membership application is current: &nbsp;</td>
						<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
							<cfif structKeyExists(arguments.rc,"mccf_verify")>
								#YesNoFormat(arguments.rc.mccf_verify)#
							<cfelse>
								No
							</cfif>
						</td>
					</tr>
					</table>
				</td>
			</tr>
			</table>	
			<cfif local.paymentRequired>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Method</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<cfif structKeyExists(arguments.rc,"mccf_payMeth")>
							<table cellpadding="3" border="0" cellspacing="0">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>Payment Method: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">
									#arguments.rc.mccf_payMethTitle#<cfif len(local.memberPayProfileDetail)> - #local.memberPayProfileDetail#</cfif>
								</td>
							</tr>
							</table>
						<cfelse>
							None selected.
						</cfif>
					</td>
				</tr>
				</table>
			</cfif>					
			</cfoutput>
		</cfsavecontent>

		<!--- email to member --->
		<cfif application.MCEnvironment eq "production">
			<cfset variables.memberEmail.TO = application.objMember.getMainEmail(memberID=variables.useMID).email>
		</cfif>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.memberEmail.from },
			emailto=[
				{ name="", email=variables.memberEmail.to }
			],
			emailreplyto=variables.ORGEmail.to,
			emailsubject=variables.memberEmail.subject,
			emailtitle = "Thank you for your application to Knoxville Bar Association",
			emailhtmlcontent = local.confirmationHTML,
			siteID = variables.siteID,
			memberID = variables.useMID,
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		<!--- email to association --->
		<cfset local.stOrigMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.rc.origMemberID, orgID=variables.orgID)>
		<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.useMID, orgID=variables.orgID)>
		<cfsavecontent variable="local.specialText">
			<cfoutput>
			<div style="padding-bottom:4px;">MemberNumber Found/Created In Account Lookup: <b>#local.stOrigMemberNumber#</b></div>
			<div style="padding-bottom:4px;">MemberNumber of Final Member Record: <b>#local.stFinalMemberNumber#</b></div>
			<cfif NOT local.emailSentToUser>
				<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
			</cfif>
			<br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>
		<cfset local.responseStruct = application.objCustomPageUtils.sendConfirmationEmail(sitecode=arguments.rc.mc_siteinfo.sitecode, strEmail=variables.ORGEmail, emailTitle="Thank you for your application to Knoxville Bar Association", emailContent=local.confirmationHTMLToStaff)>

		<cfreturn local.confirmationHTML>
	</cffunction>

	<cffunction name="showConfirmation" access="private" output="false" returntype="string">
		<cfargument name="confirmationHTML" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">Thank you for your application.</div>
			<div class="tsAppSectionContentContainer">						
				#arguments.confirmationHTML#
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "activefound">
					#variables.strPageFields.errorActiveSubscriptionFound#	
				<cfelseif arguments.errorCode eq "acceptedfound">
					#variables.strPageFields.errorActiveSubscriptionFound#
				<cfelseif arguments.errorCode eq "billedjoinfound">
					#variables.strPageFields.BilledJoinMessage#	
				<cfelseif arguments.errorCode eq "billedfound">
					#variables.strPageFields.ErrorBilledSubscriptionFound#
					<script type="text/javascript">
						setTimeout("AJAXRenewSub(#variables.useMID#)", 3000);
						function AJAXRenewSub(member){
							var redirect = function(r) {
								redirectLink = '/renewsub/' + r.data.directlinkcode[0];
								window.location = redirectLink;								
							};		
							
							var params = { memberID:member, status:'O', distinct:false };
							TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,redirect);
						}						
					</script>
				<cfelseif arguments.errorCode eq "failsavemember">
					We were unable to save the member information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failsavemembership">
					We were unable to process the membership information provided. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "failpayment">
					We were unable to process your selected payment method. Please contact the association or try again later.
				<cfelseif arguments.errorCode eq "spam">
					Your submission was blocked and will not be processed at this time.
				<cfelse>
					An error occurred. Please contact the association or try again later.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>