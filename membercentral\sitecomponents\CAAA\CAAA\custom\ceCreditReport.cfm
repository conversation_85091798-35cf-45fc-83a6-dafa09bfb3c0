<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>
<cfset local.thisPageName = "ceCreditReport">

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=MyCLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div class="tsAppHeading">Continuing Education Credit Report</div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact CAAA for assistance.
	</div>
	</cfoutput>

<cfelse>

	<cfset local.doPDF = arguments.event.getValue('pdf',0)>
	<cfset local.startDate = arguments.event.getValue('f_startdate','')>
	<cfset local.endDate = arguments.event.getValue('f_enddate','')>
	<cfif Len(local.startDate) eq 0>
		<cfset local.startDate = DateFormat(DateAdd("m", -11, CreateDate(year(now()), month(now()), 1)), "m/d/yyyy")>
	</cfif>
	<cfif Len(local.endDate) eq 0>
		<cfset local.endDate = DateFormat(CreateDate(year(now()), month(now()), daysinmonth(now())), "m/d/yyyy")>
	</cfif>
	         
	<cfset local.memberInfo = application.objMember.getMemberInfo(session.cfcUser.memberData.memberID, arguments.event.getValue('mc_siteInfo.orgID'))>
	<cfset local.memberAddress = application.objMember.getMemberAddressByFirstAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=session.cfcUser.memberData.memberID)>
	<cfset local.memberEmail = application.objMember.getMainEmail(session.cfcUser.memberData.memberID)>

	<!--- Credit Authorities --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAuthorities">
		select ca.authorityID, ca.authorityName
		from dbo.ams_members m
		inner join dbo.ev_registrants as r on r.memberID = m.memberID
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
		inner join dbo.crd_authoritySponsors as cas on cas.ASID = ast.ASID
		inner join dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
		where m.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
		and rc.creditAwarded = 1
		and r.status = 'A'
		and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
		group by ca.authorityID, ca.authorityName
		order by ca.authorityName
	</cfquery>
	<cfset local.listParam=ValueList(local.qryAuthorities.authorityID)>
	<cfset local.crAuthority = arguments.event.getValue('f_selAgency',0)>
	
	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		select e.eventid, r.registrantID, c.contentTitle, rc.creditValueAwarded,
			isnull(ast.ovTypeName,cat.typeName) as creditType, ca.authorityName, 
			e.isAllDayEvent, et.startTime, et.endTime, eco.approvalNum
		from dbo.ams_members m
		inner join dbo.ev_registrants as r on r.memberID = m.memberID
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID and r.recordedOnSiteID = evr.siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid and e.siteID = evr.siteID
		inner join dbo.ev_times as et on et.eventID = e.eventID 
		inner join dbo.cms_contentLanguages as c on c.contentID = e.eventContentID and c.languageID = 1
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_offerings as eco on eco.offeringID = ect.offeringID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		inner join dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
		where m.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
		and rc.creditAwarded = 1
		and r.status = 'A'
		and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
		 <cfif local.crAuthority neq 0 >
			and ca.authorityID IN(#local.crAuthority#)
		<cfelseif ListLen(local.listParam) GT 0>
			and ca.authorityID IN(#local.listParam#)
		</cfif>  
		and et.timezoneID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.defaultTimeZoneID')#">
		and et.endTime >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.startDate#">
		and et.endTime <= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.endDate# 23:59:59.997">
		order by et.endTime desc, e.eventid, authorityName, creditType
	</cfquery>

	<cfquery name="local.qryCLETotals" dbtype="query">
		select authorityName, creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by authorityName, creditType
		order by authorityName, creditType
	</cfquery>
	
	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<cfif local.doPDF neq 1>
		<script language="JavaScript">
		$(document).ready(function() {
			$("##f_selAgency").multiselect({
						header: true,
						noneSelectedText: ' - Please Select - ',
						selectedList: 3,
						minWidth: 300
						});	
			});	
			function printIt() {
				var print='';				
				if($('##f_selAgency').val()!=null){
				
					print=$('##f_selAgency').val();
					self.location.href = '/?pg=#local.thisPageName#&f_startdate='+$('##f_startdate').val()+'&f_enddate='+$('##f_enddate').val()+'&f_selAgency='+print+'&mode=stream&pdf=1';
				}
				else{
				self.location.href = '/?pg=#local.thisPageName#&f_startdate='+$('##f_startdate').val()+'&f_enddate='+$('##f_enddate').val()+'&mode=stream&pdf=1';
				}
					 setTimeout(function() {
						$('##frmReport').trigger('submit');},2500);
			}
			function checkAgency() {
				if ($('##f_selAgency').val() == 0) {
					$('button##printbutton').attr('disabled','disabled');
				} else {
					$('button##printbutton').removeAttr('disabled');
				}
			}
			
			$(function() {
				checkAgency();
				mca_setupDatePickerRangeFields('f_startdate','f_enddate');
			});
			
		</script>
		 <cfelse>		
		<script>
		$(function() {
			self.location.href = '/?pg=#local.thisPageName#';
			});
		</script>
		</cfif>
		<style type="text/css">
		##f_startdate, ##f_enddate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
		##clehistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">
	
	<cfsavecontent variable="local.showPage">
	<cfoutput>
	<div class="tsAppHeading" align="center">Continuing Education Credit Report</div>
	<br/>
	<cfif local.doPDF neq 1>
		<cfform name="frmReport"  id="frmReport" method="post" action="/?pg=#local.thisPageName#">
		<div align="center">
			<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
			<tr valign="top">
				<td align="right">Period:&nbsp;</td>
				<td align="left">
				<cfinput type="text" name="f_startdate"  id="f_startdate" value="#local.startDate#" size="16"> to 
				<cfinput type="text" name="f_enddate"  id="f_enddate" value="#local.endDate#" size="16"></td>
			</tr>
			
			<tr valign="top">
				<td align="right">Accrediting Agency:&nbsp;</td>
				<td align="left">
					 <select id="f_selAgency" name="f_selAgency" multiple="multiple" onChange="checkAgency();">		
							<cfloop query="local.qryAuthorities">
								<cfset  local.authorityArr[local.qryAuthorities.authorityid] = local.qryAuthorities.authorityname>
								<option value="#local.qryAuthorities.authorityID#"<cfif listFind(local.crAuthority,local.qryAuthorities.authorityID)>selected="selected"</cfif>>#local.qryAuthorities.authorityname#</option>
							</cfloop>
					</select>					
				</td>
			</tr>
			<tr valign="top">
				<td align="right">
					<button id="printbutton" type="button" class="tsAppBodyButton" style="width:100px;" onClick="printIt()" <cfif local.qryCLE.recordcount is 0>disabled</cfif>><i class="icon-print" style="vertical-align:middle;"></i> Print</button>
				</td>			
				<td align="right">
					<button type="submit" class="tsAppBodyButton" onClick="return chkReport();">Filter Report</button>
				</td>
			</tr>
			</table>
		</div>
		</cfform>
	<cfelse>
		<div align="center">Period: #local.startDate# - #local.endDate#</div>
		<cfif len(local.qryCLE.authorityName)>
			<div align="center" style="margin-top:5px;">Accrediting Agency: #local.qryCLE.authorityName#</div>
		</cfif>
	</cfif>
	<br>
	<table cellpadding="2" cellspacing="0" class="tsAppBodyText" width="100%">
	<tr>
		<td><b>Provider:</b></td>
		<td><b>Participant:</b></td>
	</tr>
	<tr>
		<td width="50%" valign="top" class="tsAppBodyText">
			<div class="pdfind">
				California Applicant's Attorneys Association<br>
				1303 J Street, Suite 420 <br>
				Sacramento, California 95814 <br>
				T: (************* <br>
				F: (************* <br>
				<b>Director of Education & Events:</b> Nikki Smith<br>
				<EMAIL>
			</div>
		</td>
		<td width="50%" valign="top" class="tsAppBodyText">
			<div class="pdfind">
				#Trim(Trim(local.memberInfo.firstName & ' ' & local.memberInfo.middleName) & ' ' & local.memberInfo.lastName & ' ' & local.memberInfo.suffix)# <br>
				<cfif local.memberAddress.hasAttn is 1 and len(local.memberAddress.attn)>#local.memberAddress.attn#<br/></cfif>
				<cfif len(local.memberAddress.address1)>#local.memberAddress.address1#</cfif><br/>
				<cfif local.memberAddress.hasAddress2 is 1 and len(local.memberAddress.address2)>#local.memberAddress.address2#<br/></cfif>
				<cfif local.memberAddress.hasAddress3 is 1 and len(local.memberAddress.address3)>#local.memberAddress.address3#<br/></cfif>
				<cfif len(local.memberAddress.city)>#local.memberAddress.city#,</cfif> 
				<cfif len(local.memberAddress.stateName)>#local.memberAddress.stateName#</cfif> 
				<cfif len(local.memberAddress.postalcode)>#local.memberAddress.postalcode#</cfif><br/>
				<cfif len(local.memberAddress.phone)>#local.memberAddress.phone#</cfif><br/>
				<cfif len(local.memberEmail.email)>#local.memberEmail.email#</cfif>
			</div>
		</td>
	</tr>
	</table>	
	<br>
	<div class="tsAppHeading" align="center">CREDIT DETAILED REPORT</div>
	<br/>
	</cfoutput>
	
	<cfif local.qryCLE.recordcount>
		<cfoutput>

		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory2">
		<cfset local.oddeven = 0>
		</cfoutput>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfsavecontent variable="local.creditSubjects">
				<cfoutput>
				&nbsp;&nbsp;#local.qryCLE.creditValueAwarded#&nbsp;&nbsp;&nbsp;&nbsp;#local.qryCLE.creditType#<br/>
				</cfoutput>
			</cfsavecontent>

			<cfset local.eventtime = "">
			<cfif local.qryCLE.isAllDayEvent>
				<cfif Month(local.qryCLE.endTime) is not Month(local.qryCLE.startTime)
					or Year(local.qryCLE.endTime) is not Year(local.qryCLE.startTime)>
					<cfset local.eventtime = local.eventtime & '#DateFormat(local.qryCLE.startTime, "mmmm d, yyyy")#'>
					<cfset local.eventtime = local.eventtime & ' - #DateFormat(local.qryCLE.endTime, "mmmm d, yyyy")#'>
				<cfelse>
					<cfset local.eventtime = local.eventtime & '#DateFormat(local.qryCLE.startTime, "mmmm d")#'>
					<cfif DateCompare(local.qryCLE.endTime,local.qryCLE.startTime,"d") IS NOT 0>
						<cfset local.eventtime = local.eventtime & '-#DateFormat(local.qryCLE.endTime, "d")#'>
					</cfif>
					<cfset local.eventtime = local.eventtime & ' #DateFormat(local.qryCLE.startTime, ", yyyy")#'>
				</cfif>
			<cfelse>
				<cfset local.eventtime = local.eventtime & '#DateFormat(local.qryCLE.startTime, "mmmm d, yyyy")# '>
				<cfif DateCompare(local.qryCLE.endTime,local.qryCLE.startTime,"d") IS 0 and DateDiff("n",local.qryCLE.endTime,local.qryCLE.startTime) is 0>
					<cfset local.eventtime = local.eventtime & ' #TimeFormat(local.qryCLE.startTime, "h:mm TT")#'>
				<cfelseif DateCompare(local.qryCLE.endTime,local.qryCLE.startTime,"d") IS 0>
					<cfset local.eventtime = local.eventtime & ' #TimeFormat(local.qryCLE.startTime, "h:mm TT")# '>
					<cfset local.eventtime = local.eventtime & '- #timeformat(local.qryCLE.endTime,"h:mm TT")#'>
				<cfelse>
					<cfset local.eventtime = local.eventtime & ' #TimeFormat(local.qryCLE.startTime, "h:mm TT")# '>
					<cfset local.eventtime = local.eventtime & '- #DateFormat(local.qryCLE.endTime, "mmmm d, yyyy")#'>
					<cfset local.eventtime = local.eventtime & '#TimeFormat(local.qryCLE.endTime, "h:mm TT")#'>
				</cfif>
			</cfif>
			
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" width="30" nowrap align="right"><b>Date:</b> &nbsp;</td>
				<td class="tsAppBodyText" colspan="6">#local.eventtime# </td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" nowrap align="right"><b>Event:</b> &nbsp;</td>
				<td class="tsAppBodyText" align="left" colspan="6">#local.qryCLE.contentTitle# <br><br></td>
			</tr>	
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="right" nowrap><b>Participation Method:</b>&nbsp; </td>
				<td class="tsAppBodyText" align="left">Attended</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" colspan="3" nowrap><b>Credits:</b>&nbsp;#local.qryCLE.creditValueAwarded# units</td>
				<td class="tsAppBodyText" align="right" nowrap><b>Accreditation Number:</b>&nbsp;#local.qryCLE.approvalNum# </td>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				<cfif local.doPDF neq 1>
				<td class="tsAppBodyText" align="right">&nbsp;</td>
				<cfelse>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
				</cfif>
				<td class="tsAppBodyText" align="left">&nbsp;</td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" align="left" nowrap colspan="7"><b>Credits in Subjects:</b>&nbsp;</td>
			</tr>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText" class="tsAppBodyText" colspan="7" nowrap>#local.creditSubjects#<br></td>
			</tr>
			<tr>
				<td class="tsAppBodyText" style="border-bottom: 1px dashed ##ccc;" class="pdfline" colspan="7" nowrap>&nbsp;</td>
			</tr>
		</cfoutput>
		<cfoutput>
		</table>		
		<br>
		<table cellpadding="2" cellspacing="0" border="0">
		</cfoutput>
		<cfoutput query="local.qryCLETotals" group="authorityName">
			<tr><td colspan="3" class="tsAppBodyText"><b>Credit Totals for #local.qryCLETotals.AuthorityName#</b></td></tr>
			<cfoutput>
			<tr valign="top">
				<td class="tsAppBodyText">&nbsp;&nbsp;&nbsp;&nbsp;#local.qryCLETotals.creditType#</td>
				<td class="tsAppBodyText" width="10">&nbsp;</td>
				<td class="tsAppBodyText" align="right"><b>#local.qryCLETotals.totalCLE#</b> credit<cfif local.qryCLETotals.totalCLE is not 1>s<cfelse>&nbsp;</cfif></td>
			</tr>
			</cfoutput>
		</cfoutput>
		<cfoutput>
		</table>
		<br/>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText" align="center">
			<br>
			<b>There are no credits to report for this period.</b>
		</div>
		</cfoutput>
	</cfif>
	</cfsaveContent>
		
	<cfif local.doPDF eq 1>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='caaa')>
		<cfset local.reportFileName = "EventCredits.pdf">

		<cfdocument format="PDF" filename="#local.strFolder.folderPath#/#local.reportFileName#" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" unit="in" backgroundvisible="Yes" scale="100">
			<cfoutput>
			<html>
			<head>
				<style type="text/css">
					body, td, th, .tsAppBodyText { font-size:9.5pt; font-family:arial; }
					.tsAppHeading { font-size:12pt; font-family:arial; font-weight:bold; }
					.pdfind { margin-left:10px; }
					.pdfline { border-top:1px solid ##ccc; padding-bottom:6px; }
				</style>
			</head>
			<body>
			#local.showPage#
			</body>
			</html>
			</cfoutput>
		</cfdocument>

		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
	<cfelse>
		<cfoutput>#local.showPage#</cfoutput>
	</cfif>
</cfif>