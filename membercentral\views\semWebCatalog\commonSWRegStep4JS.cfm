<cfsavecontent variable="local.swRegStep4JS">
	<cfoutput>
	<cfset local.radioChecked = ''>
	<cfloop query="local.strCredit.qryCredit">
		<cfif local.strCredit.qryCredit.isCreditRequired or local.strCredit.qryCredit.isCreditDefaulted>
			<cfset local.radioChecked = 'checked'>
			<cfbreak>
		</cfif>
	</cfloop>
	<script type="text/javascript">
		var sw_initcredit = false;
		var #toScript(local.strCreditSelections,"sw_strcreditselections")#
		var #toScript(local.radioChecked,"sw_strRadioChecked")#

		function initSWRegStep4() {
			if (typeof sw_strcreditselections == 'object' && typeof sw_strcreditselections.creditlinks != 'undefined') {
				if (sw_strcreditselections.creditlinks.length) {
					var arrcreditsels = sw_strcreditselections.creditlinks.split(',');
					$.each(arrcreditsels, 
						function(index,value) { 
							if ($('##frmcreditlink'+value).length) {
								$('##frmcreditlink'+value).prop('checked',true);
								
								if (typeof sw_strcreditselections[value] != 'undefined' && sw_strcreditselections[value].length) {
									showIDNum(value);
									$('##frm'+value+'ID').val(sw_strcreditselections[value]);
								}

								var linkedcreditselect = $('##frmcreditlink'+value).closest('.creditSelContainer').data('linkedcreditselect');
								if (linkedcreditselect.length) {
									$('input[name="'+linkedcreditselect+'"][value="1"]').trigger('click');
								}
							}
						}
					);
					var unselectCredit = $('.creditSelContainer:hidden');
				} else {
					var unselectCredit = $('.creditSelContainer');
				}

				if (unselectCredit.length) {
					unselectCredit.each(function() {
						var linkedcreditselect = $(this).data('linkedcreditselect');
						if (linkedcreditselect.length) {
							$('input[name="'+linkedcreditselect+'"][value="0"]').trigger('click');
						}
					});
				}
			}
			$('##btnSaveStep4').prop('disabled',false);
			sw_initcredit = true;
		}
		applyCreditForEachSelected = function(rdo,sId) {
		
			var credList = $(rdo).data('linkedcredits');
			arrCredList = credList.toString().split(",");
			if (rdo.checked == false) {
				var arrCBox = getElementsByClassName(document.getElementById('creditDIV' + sId),'input','creditcbox');
				arrCBox.checked = false;
			} else if (rdo.checked) {
				var arrSCID = arrCredList;
				var noneSelected = true;
				for (var i=0; i < arrSCID.length; i++) {
					if (document.getElementById('frmcreditlink' + arrSCID[i]).checked ) {
						noneSelected = false;				
					}
				}
				if (noneSelected ) return false;
			} else {
				return false;
			}
			return true;	
		};
		applyCreditForEachMissingID = function(rdo,sId) {
		
			var credList = $(rdo).data('linkedcredits');
			arrCredList = credList.toString().split(",");
			if (rdo.checked == false) {
				var arrCBox = getElementsByClassName(document.getElementById('creditDIV' + sId),'input','creditcbox');
				arrCBox.checked = false;
			} else if (rdo.checked) {
				var arrSCID = arrCredList;
				var arrSCID_IDREQ = new Array(#quotedValueList(local.qryIDRequired.seminarCreditID)#);				
				var missingID = false;
				for (var i=0; i < arrSCID.length; i++) {
					if (document.getElementById('frmcreditlink' + arrSCID[i]).checked) {						
						if (document.getElementById('frm' + arrSCID[i] + 'ID') && document.getElementById('frm' + arrSCID[i] + 'ID').value.length == 0 && inArray(arrSCID_IDREQ,arrSCID[i])) missingID = true;
					}
				}
				if ( missingID) return false;
			} else {
				return false;
			}
			return true;	
		};
		inArray = function(array,value) {
			for (var i=(array.length-1); i>=0; i--) if (array[i] === value) return true;
			return false;
		};
		getElementsByClassName = function(oElm, strTagName, strClassName) {
			var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
			var arrReturnElements = new Array();
			strClassName = strClassName.replace(/\-/g, "\\-");
			var oRegExp = new RegExp("(^|\\s)" + strClassName + "(\\s|$)");
			var oElement;
			for(var i=0; i<arrElements.length; i++){
				oElement = arrElements[i];		
				if(oRegExp.test(oElement.className)) arrReturnElements.push(oElement);
			}
			return (arrReturnElements);
		};
		checkIDNum = function(cbox,crreq) { 
			hideStep4Alert();
			if (crreq == 1) cbox.checked = true;
			if (cbox.checked) showIDNum(cbox.value); else hideIDNum(cbox.value); 
		};
		showIDNum = function(lid) { document.getElementById('dividnum' + lid).style.display = ''; };
		hideIDNum = function(lid) { document.getElementById('dividnum' + lid).style.display = 'none'; };

		<cfif GetToken(local.rc.item,1,"-") eq "SWB">
			showCreditSelect = function(sId) { document.getElementById('creditDIV' + sId).style.display = ''; hideStep4Alert(); };
			hideCreditSelect = function(sId) { document.getElementById('creditDIV' + sId).style.display = 'none'; hideStep4Alert(); };
			showhideCreditSelect = function(cbox,sId) { 			
				if (cbox.checked) {
					document.getElementById('creditDIV' + sId).style.display = ''; 		
					
				}	
				else {
					document.getElementById('creditDIV' + sId).style.display = 'none';
				}					
				hideStep4Alert(); 
			};

		<cfelse>
			showCreditSelect = function() { document.getElementById('creditDIV').style.display = ''; hideStep4Alert(); };
			hideCreditSelect = function() { document.getElementById('creditDIV').style.display = 'none'; hideStep4Alert(); };
		</cfif>

		hideStep4Alert = function() {
			var abox = document.getElementById('step4Err');
				abox.innerHTML = '';
				abox.style.display = 'none';
		};
		function doS4ValidateAndSave() {
			$('##btnSaveStep4').html('<i class="icon icon-spinner"></i> Please wait..').prop('disabled',true);
			
			var _CF_this = document.forms['frmSWRegStep4'];
			var strErr = '';
			var errmsg1='';
			var errmsg2='';
			<cfif GetToken(local.rc.item,1,"-") eq "SWB">
				var swberr = 0, swbSkipValidation = false;
				if (<cfoutput query="local.qryDistSeminars">(!_CF_this.creditSelect#local.qryDistSeminars.contentID#)<cfif local.qryDistSeminars.currentrow lt local.qryDistSeminars.recordcount> && </cfif></cfoutput>) swbSkipValidation = true;
				
				if (!swbSkipValidation) {
					<cfoutput query="local.qryDistSeminars">
						if ( _CF_this.creditSelect#local.qryDistSeminars.contentID# && !applyCreditForEachSelected(_CF_this.creditSelect#local.qryDistSeminars.contentID#,#local.qryDistSeminars.contentID#)) {swberr = 1; var seminarName='#EncodeForJavaScript(local.qryDistSeminars.contentName)#' ;errmsg1 += 'Select a credit jurisdiction for '+seminarName+'.</br>'; }
						else if (_CF_this.creditSelect#local.qryDistSeminars.contentID# && !applyCreditForEachMissingID(_CF_this.creditSelect#local.qryDistSeminars.contentID#,#local.qryDistSeminars.contentID#)) {swberr = 2; var seminarName='#EncodeForJavaScript(local.qryDistSeminars.contentName)#'; errmsg2 += 'Enter the required ID number for '+seminarName+'.</br>'; }
					</cfoutput>
					if (swberr == 1 || swberr == 2 ) strErr=errmsg1 + errmsg2;
				}
			<cfelse>
				if (_CF_this.creditSelect) {
					if (_CF_this.creditSelect[0].checked) {
						var arrCBox = getElementsByClassName(document.getElementById('creditDIV'),'input','creditcbox');
						for (var i=0; i < arrCBox.length; i++) arrCBox[i].checked = false;
					} else if (_CF_this.creditSelect[1].checked) {
						var arrSCID = new Array(#quotedValueList(local.strCredit.qryCredit.seminarCreditID)#);
						var arrSCID_IDREQ = new Array(#quotedValueList(local.qryIDRequired.seminarCreditID)#);
						var noneSelected = true;
						var missingID = false;
						for (var i=0; i < arrSCID.length; i++) {
							if (document.getElementById('frmcreditlink' + arrSCID[i]).checked) {
								noneSelected = false;
								if (document.getElementById('frm' + arrSCID[i] + 'ID') && document.getElementById('frm' + arrSCID[i] + 'ID').value.length == 0 && inArray(arrSCID_IDREQ,arrSCID[i])) missingID = true;
							}
						}
						if (noneSelected) {
							strErr += 'No jurisdictions are selected. Select at least one jurisdiction to apply for credit, or choose "I am NOT interested in applying for credit".';
						} else if (missingID) {
							strErr += 'Enter the requested information for each jurisdiction you have selected.';
						}
					} else {
						strErr += 'Choose your interest in applying for credit.';
					}
				}
			</cfif>
			
			return new Promise(function(resolve, reject) {
				if (strErr.length > 0) {
					if ($('##frmSWRegStep4').is(':hidden')) editRegStep4();
					$('##step4Err').html(strErr).show();
					$('##btnSaveStep4').html('<i class="icon-arrow-right"></i> Continue').prop('disabled',false);
					reject(strErr);
					return false;
				} else {
					$('##step4Err').html('').hide();
					$('##swRegStep4SaveLoading')
						.html('<i class="icon-spin icon-spinner"></i> <b>Please Wait...</b>')
						.load('#arguments.event.getValue('swregresourceurl')#&regaction=saves4', $('##frmSWRegStep4').serializeArray(),
							function(resp) {
								let respObj = JSON.parse(resp);
								
								$('##btnSaveStep4').html('Save Changes').prop('disabled',false);
								$('html, body').animate({
									scrollTop: $('##SWRegStep4').offset().top - 175
								}, 750);
								
								if (respObj.checkout) {
									regCartCheckout();
								} else {
									$('##frmSWRegStep4').hide();
									$('##swRegStep4Summary').show(300);
								}
								
								resolve();
							}
						);
				}
			});
		}
		function editRegStep4() {
			$('##swRegStep4Summary').hide();
			$('##frmSWRegStep4').show(300);
			<cfif local.swReg.currentReg.isRegCartItem EQ 1>
				if (!sw_initcredit) initSWRegStep4();
			</cfif>
		}

		<cfif NOT local.swReg.currentReg.isRegCartItem>
			$(function() {
				initSWRegStep4();
			});
		</cfif>
		$(document).ready(function() {
			$("##parent").click(function() {
				if(this.checked == true){
					$(".child, .creditcbox").not(':checked').trigger('click');
				} 
				else {
					
					$(".child[data-isCredRequired=0]:checked, .creditcbox[data-isCredRequired=0]:checked").trigger('click');
				}
			
			var $checked = $(".child").map(function () {					
					var idval= this.id;
					idval =idval.slice(12);
					showhideCreditSelect(this,idval);				
				});
	
			});
			
			$('.child').click(function() {
				if ($('.child:checked').length == $('.child').length) {
				$('##parent').prop('checked', true);
				} else {
				$('##parent').prop('checked', false);
				}
			});
			if(sw_strRadioChecked != ''){
				showCreditSelect();
			}
		});

	

	function copyDown(obj,aID){
		hideStep4Alert();	
		$(".frmTxt"+aID).val(obj.val());
	}	
	function showCopyDown(sid){
		hideStep4Alert();
		if($("##frm"+sid+"ID").val().trim().length > 0){
			$("##frmlink"+sid+"ID").show();
		}
		else {
			$("##frmlink"+sid+"ID").hide();
		}	
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.swRegStep4JS#">