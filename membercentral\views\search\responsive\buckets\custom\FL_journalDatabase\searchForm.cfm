<cfsavecontent variable="local.js">
	<script type="text/javascript">
		function b_search() {
			$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
			return true;
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.js,'\s{2,}',' ','ALL')#">

<cfoutput>
#showHeader(settings=local.qryBucketInfo, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>
<form name="frms_Search" id="frms_Search" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();" class="form-horizontal form-medium-lg">
	
	<div class="control-group">
		<label class="control-label" for="s_aut">Authors: <AUTHORS>
		<div class="controls">
			<select name="s_aut"  id="s_aut" multiple="true">
				<option value="">All Authors</option>
				<cfloop query="local.qryAuthors">
					<option value="#local.qryAuthors.authorID#" <cfif listFindNoCase(local.strSearchForm.s_aut,local.qryAuthors.authorID)>selected="selected"</cfif>>#local.qryAuthors.authorName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_category">Sections:</label>
		<div class="controls">
			<select name="s_category"  id="s_category">
				<option value="">All Sections</option>
				<cfloop query="local.qrySections">
					<option value="#local.qrySections.sectionID#" <cfif local.strSearchForm.s_category eq local.qrySections.sectionID>selected="selected"</cfif>>#local.qrySections.sectionName#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_cat">Years:</label>
		<div class="controls">
			<select name="s_cat"  id="s_cat">
				<option value="">All Years</option>
				<cfloop query="local.qryYears">
					<option value="#local.qryYears.issueYear#" <cfif local.strSearchForm.s_cat eq local.qryYears.issueYear>selected="selected"</cfif>>#local.qryYears.issueYear#</option>
				</cfloop>
			</select>
		</div>
	</div>
	<div class="control-group" style="margin-top:30px;">
		<label class="control-label" for="s_key_all">Keywords:</label>
		<div class="controls">
			<input type="text" name="s_key_all" id="s_key_all" value="#local.strSearchForm.s_key_all#"maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<input type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</form>
</cfoutput>