<cfinclude template="../commonJS.cfm">

<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function loadProjectSummaryStats() {
			var objParams = { workspaceID:#attributes.data.workspaceID#, projectID:#attributes.data.projectID# };
			$.getJSON('/?event=proxy.ts_json&c=ADMWORKSPACE&m=getWorkspaceProjectStats', objParams)
				.done(showProjectSummaryStats)
				.fail(showProjectSummaryStats);
		}
		function showProjectSummaryStats(r) {
			if (r.success) {
				var projSummStatsTemplateSource = $('##mc_projectStatsTempate').html();
				var projSummStatsTemplate = Handlebars.compile(projSummStatsTemplateSource);
				$('##divProjectSummaryStats').html(projSummStatsTemplate(r));
			} else {
				alert('We were unable to load #attributes.data.labels.taskFieldLabel# Summary Statistics.');
			}
		};

		$(function () {
			initTaskProspectsSection();
			loadProjectSummaryStats();
			<cfif arguments.event.valueExists('fFilterMode')>
				showAssignedProspects();
				$('html, body').animate({
					scrollTop: $('##divAssignedProspects').offset().top - 450
				}, 750);
			</cfif>
		});
	</script>
	<style type="text/css">
		/* div##divAssignedProspects .caption { padding:4px; } */
		.thumbnails > li.span2:nth-child(6n+1),
		.thumbnails > li.span3:nth-child(4n+1),
		.thumbnails > li.span4:nth-child(3n+1),
		.thumbnails > li.span6:nth-child(2n+3){ margin-left: 0 !important; }
		.thumbnails .caption {max-width:none;}
		##frmSearchAssignedTasks, ##btnFilter, ##btnResetFilter { margin-bottom:10px; }
		##fTaskStatus { margin-right:4px; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<div id="MCWorkspaceContainer" class="container-fluid" style="padding:0;">
	<div class="row-fluid display-flex">
		<cfif attributes.data.qryInCompleteTasks.recordCount>
			<div class="span3 hidden-phone sideBarContainer">
				<cfif attributes.data.availableProspectsCount gt 0>
					<div class="divChooseProspects">
						<button class="btn btn-primary btnChooseProspects btn-block" onclick="showChooseTasks(#attributes.data.qryProjectDetails.projectID#)">Choose #attributes.data.labels.taskFieldLabelPlural#</button>
					</div>
				</cfif>
				
				<!--- common sidebar --->
				<cfset local.strProjectData = {
					orgMemberID = attributes.data.orgMemberID,
					projectID = attributes.data.projectID,
					selectedTaskID = 0,
					qryAssignedTasks = attributes.data.qryInCompleteTasks,
					qryFeaturedProjectTasksFieldData = attributes.data.qryFeaturedProjectTasksFieldData,
					qryFeaturedWorkspaceTasksFieldData = attributes.data.qryFeaturedWorkspaceTasksFieldData,
					taskFieldLabelPlural = attributes.data.labels.taskFieldLabelPlural
				}>
				<cfinclude template="dsp_projectAssignedTasksSidebar.cfm">
			</div>
		</cfif>
		<div id="tskProspectDetails" class="<cfif attributes.data.qryInCompleteTasks.recordCount>span9<cfelse>span12</cfif>">
            <div class="container-fluid">
                <div class="row-fluid">
					<div class="page-header span12">
						<ul class="breadcrumb lead">
							<li><a href="#attributes.data.mainurl#">#attributes.data.workspaceName#</a> <span class="divider">/</span></li>
							<li class="active">#attributes.data.qryProjectDetails.projectName#</li>
						</ul>
						<h2>#attributes.data.qryProjectDetails.projectName#</h2>
					</div>
				</div>
				<div class="row-fluid">
					<div class="span12" style="margin-bottom:30px;">#attributes.data.qryProjectDetails.projectDescription#</div>
				</div>
				<!--- workspace project fields --->
				<cfif attributes.data.qryWorkspaceProjectFieldData.recordCount>
					<div class="row-fluid">
						<div class="span12 well">
							<div class="row-fluid">
								<h4>Project Informational Fields</h4>
								<div class="span12">
									<cfloop query="attributes.data.qryWorkspaceProjectFieldData">
										<cfif len(attributes.data.qryWorkspaceProjectFieldData.answer)>
											<p><strong>#attributes.data.qryWorkspaceProjectFieldData.titleOnInvoice#:</strong> <cfif listFindNoCase("INTEGER,DECIMAL2",attributes.data.qryWorkspaceProjectFieldData.dataTypeCode)>#NumberFormat(attributes.data.qryWorkspaceProjectFieldData.answer,',')#<cfelse>#attributes.data.qryWorkspaceProjectFieldData.answer#</cfif></p>
										</cfif>
									</cfloop>
								</div>
							</div>
						</div>
					</div>
				</cfif>
				<!--- choose tasks --->
				<cfif attributes.data.availableProspectsCount gt 0>
					<div class="row-fluid">
						<div class="alert alert-info clearfix">
							<strong>There are Unassigned #attributes.data.labels.taskFieldLabelPlural# available for selection</strong><br><br>
							<button type="button" class="btn btn-primary" onclick="showChooseTasks(#attributes.data.qryProjectDetails.projectID#)">Choose #attributes.data.labels.taskFieldLabelPlural#</button>
						</div>				
					</div>
				</cfif>


			<!--- prospects assigned --->
			<cfif attributes.data.qryTasksAssigned.recordCount>
				<cfquery name="local.qryTaskStatus" dbtype="query">
					select distinct statusName, count(memberID) as prospectCount 
					from attributes.data.qryTasksAssigned
					group by statusName
					order by statusName
				</cfquery>
				<div class="row-fluid">
					<div class="span12 well">
						<h4><cfif attributes.data.qryTasksAssigned.recordCount gt 1>#attributes.data.labels.taskFieldLabelPlural#<cfelse>#attributes.data.labels.taskFieldLabel#</cfif> Assigned to You</h4>
						<cfloop query="local.qryTaskStatus">
							<p><b>#local.qryTaskStatus.statusName#:</b> #local.qryTaskStatus.prospectCount#</p>
						</cfloop>
						<br/>
						<button type="button" class="btn" name="btnShowAssignedProspects" id="btnShowAssignedProspects" onclick="showAssignedProspects();">View Assigned <cfif attributes.data.qryTasksAssigned.recordCount gt 1>#attributes.data.labels.taskFieldLabelPlural#<cfelse>#attributes.data.labels.taskFieldLabel#</cfif></button>
						<div id="divAssignedProspects" class="container-fluid" style="display:none;">
							<div class="row-fluid">
								<div class="span12">
									<form name="frmSearchAssignedTasks" id="frmSearchAssignedTasks" method="post" onsubmit="$('##btnFilter').prop('disabled',true);" autocomplete="off">
										<input type="hidden" name="fFilterMode" value="#arguments.event.getValue('fFilterMode','filtertasks')#">
										<input type="text" name="fFirstName" id="fFirstName" class="input-small" value="#arguments.event.getTrimValue('fFirstName','')#" placeholder="First Name">
										<input type="text" name="fLastName" id="fLastName" class="input-small" value="#arguments.event.getTrimValue('fLastName','')#" placeholder="Last Name">
										<input type="text" name="fCompany" id="fCompany" class="input-small" value="#arguments.event.getTrimValue('fCompany','')#" placeholder="Company">
										<select name="fTaskStatus" id="fTaskStatus">
											<option value="">Select a Status</option>
											<cfloop query="attributes.data.qryTaskStatuses">
												<option value="#attributes.data.qryTaskStatuses.statusName#"<cfif arguments.event.getValue('fTaskStatus','') EQ attributes.data.qryTaskStatuses.statusName> selected</cfif>>#attributes.data.qryTaskStatuses.statusName#</option>
											</cfloop>
										</select>
										<button type="submit" name="btnFilter" id="btnFilter" class="btn btn-primary">Filter</button>
										<button type="button" name="btnResetFilter" id="btnResetFilter" class="btn btn-default" onclick="clearSearchAssignedTasks();">Clear</button>
									</form>
								</div>
							</div>
							<cfif attributes.data.qryFilteredAssignedTasks.recordCount>
								<div class="mctask-d-flex mctask-flex-wrap">
									<cfloop query="attributes.data.qryFilteredAssignedTasks">
										<cfquery name="local.qryFeaturedProjectTaskFieldData" dbtype="query">
											select fieldDisplayName, dataTypeCode, answer
											from attributes.data.qryFeaturedProjectTasksFieldData
											where taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#attributes.data.qryFilteredAssignedTasks.taskID#">
											order by fieldOrder;
										</cfquery>
										<cfquery name="local.qryFeaturedWorkspaceTaskFieldData" dbtype="query">
											select fieldDisplayName, dataTypeCode, answer, categoryID, categoryTreeName, categoryDisplayName
											from attributes.data.qryFeaturedWorkspaceTasksFieldData
											where taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#attributes.data.qryFilteredAssignedTasks.taskID#">
											order by fieldOrder;
										</cfquery>
										<div class="thumbnail mctask-w-45 mctask-m-2">
											<div class="caption">
												<p><b><a href="#attributes.data.mainurl#&wsAction=showTaskDetail&pid=#attributes.data.qryProjectDetails.projectID#&tid=#attributes.data.qryFilteredAssignedTasks.taskID#">#replace(replace(attributes.data.qryFilteredAssignedTasks.mc_combinedNameNoMemberNumber,'  ',' ','ALL'),' ,',',','ALL')#</a></b> <span class="label label-info">#attributes.data.qryFilteredAssignedTasks.statusName#</span></p>
												<p><i>#attributes.data.qryFilteredAssignedTasks.company#</i></p>
												<p>Last Activity: #DateFormat(attributes.data.qryFilteredAssignedTasks.dateLastModified,'m/d/yyyy')#</p>
												<cfoutput query="local.qryFeaturedWorkspaceTaskFieldData" group="categoryID">
													<p>#local.qryFeaturedWorkspaceTaskFieldData.categoryTreeName#: #local.qryFeaturedWorkspaceTaskFieldData.categoryDisplayName#</p>
													<cfoutput>
														<cfif len(local.qryFeaturedWorkspaceTaskFieldData.answer)>
															<p><div style="margin-left:5px;">- #local.qryFeaturedWorkspaceTaskFieldData.fieldDisplayName#: <cfif listFindNoCase("INTEGER,DECIMAL2",local.qryFeaturedWorkspaceTaskFieldData.dataTypeCode)>#NumberFormat(local.qryFeaturedWorkspaceTaskFieldData.answer,',')#<cfelse>#local.qryFeaturedWorkspaceTaskFieldData.answer#</cfif></div></p>
														</cfif>
													</cfoutput>
												</cfoutput>												
												<cfloop query="local.qryFeaturedProjectTaskFieldData">
													<cfif len(local.qryFeaturedProjectTaskFieldData.answer)>
														<p>#local.qryFeaturedProjectTaskFieldData.fieldDisplayName#: <cfif listFindNoCase("INTEGER,DECIMAL2",local.qryFeaturedProjectTaskFieldData.dataTypeCode)>#NumberFormat(local.qryFeaturedProjectTaskFieldData.answer,',')#<cfelse>#local.qryFeaturedProjectTaskFieldData.answer#</cfif></p>
													</cfif>
												</cfloop>
											</div>
										</div>
									</cfloop>
								</div>
							<cfelse>
								<p>No #attributes.data.labels.taskFieldLabelPlural# Found.</p>
							</cfif>
						</div>
					</div>
				</div>
			</cfif>

			<div class="row-fluid">
				<div class="span12 well">
					<h4>#attributes.data.labels.taskFieldLabel# Summary Statistics</h4>
					<div id="divProjectSummaryStats" class="span12">
						<div class="c"><i class="icon-spin icon-spinner"></i><b>Please Wait...</b></div>
					</div>
				</div>
			</div>

		</div>
	</div>
</div>


<script id="mc_projectStatsTempate" type="text/x-handlebars-template">
	<!--- Task Stats --->
	<p><b>Total #attributes.data.labels.taskFieldLabelPlural#:</b> {{totalTasks}}</p>
	<p><b>Unassigned #attributes.data.labels.taskFieldLabelPlural#:</b> {{availableTaskCount}}</p>
	<p><b>In progress #attributes.data.labels.taskFieldLabelPlural#:</b> {{inProgressTaskCount}}</p>
	<p><b>Completed #attributes.data.labels.taskFieldLabelPlural#:</b> {{completedTaskCount}}</p>
	
	<!--- Task Result Option Stats --->
	{{##compare arrTaskResultOptionStats.length '>' 0}}
		<br/>
		<div>
			<p><b>Summary of #attributes.data.labels.taskFieldLabel# Options:</b></p>
			<div style="padding-left:10px;">
				{{##each arrTaskResultOptionStats}}
					<p>{{tagName}}: {{selectedTagInPercent}}%</p>
				{{/each}}
			</div>
		</div>
	{{/compare}}

	<!--- Task Result Option Field Stats --->
	{{##if showTaskResultOptFieldStats}}
		<br/>
		<div>
			<div>
				<b>Summary of #attributes.data.labels.taskFieldLabel# Option Fields (completed only):</b>
			</div>

			<div style="padding-left:15px;">
				{{##each arrTaskResultOptionFieldStats}}
					<div style="padding:10px 0 5px 0;">
						{{tagName}}
					</div>
					<div style="padding-left:10px;">
						{{##each arrFields}}
							<div style="padding-top:4px;">
								{{fieldText}}
							</div>
							<div style="padding-left:15px;">
								{{##each arrOpts}}
									<div style="padding-top:4px;">
										{{fieldValue}}: {{numSelectedValue}}
									</div>
								{{/each}}
							</div>
						{{/each}}
					</div>
				{{/each}}

				{{##each arrTaskResultNonOptionFieldStats}}
					<div style="padding:10px 0 5px 0;">
						{{tagName}}
					</div>
					<div style="padding-left:15px;">
						{{##each arrFields}}
							<div style="padding-top:4px;">
								{{fieldText}}: {{fieldData}}
							</div>
						{{/each}}
					</div>
				{{/each}}
			</div>
		</div>
	{{/if}}

	<!--- Project Objective field Stats --->
	{{##if showProjectObjectiveFieldStats}}
		<br/>
		<div style="padding:10px 0 5px 0;">
			<div>
				<b>Summary of #attributes.data.labels.taskFieldLabel# Objective Fields (completed only):</b>
			</div>

			<div style="padding-left:15px;">
				{{##each arrObjectiveOptionFieldStats}}
					<div style="padding-top:4px;">
						{{fieldText}}
					</div>
					<div style="padding-left:15px;">
						{{##each arrOpts}}
							<div style="padding-top:4px;">
								{{fieldValue}}: {{numSelectedValue}}
							</div>
						{{/each}}
					</div>
				{{/each}}

				{{##each arrObjectiveNonOptionFieldStats}}
					<div style="padding-top:4px;">
						{{fieldText}}: {{fieldData}}
					</div>
				{{/each}}
			</div>
		</div>
	{{/if}}
</script>
</cfoutput>