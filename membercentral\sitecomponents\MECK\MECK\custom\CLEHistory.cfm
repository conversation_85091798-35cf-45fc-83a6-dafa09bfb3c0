<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	// dates to use for the period: --------------------------------------------------------------
	local.today = now();
	arguments.event.paramValue('periodStartDate','1/1/#year(now())#');
	arguments.event.paramValue('periodEndDate',dateFormat(local.today,'m/dd/yyyy'));
	arguments.event.paramValue('membernumber','');
	arguments.event.paramValue('panel','showList');
</cfscript>

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
		// get encrypted registrantid
		local.encryptedRID = arguments.event.getValue('rid','');
		
		// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
		try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
		catch (any e) { local.decryptedRID = 0; }
		
		// generate certificate for registrantID
		local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=CLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>

<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
		<div class="tsAppHeading">My CLE History</div>
		<br/>
		<div class="tsAppBodyText">
			<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
			If you continue to see this message, please contact MCB for assistance.
		</div>
	</cfoutput>

<cfelse>
	<cfset local.getFieldsInFieldset = application.objCustomPageUtils.renderFieldSet(uid='a850e99f-83b5-49cd-b0c6-4f13bdcb9f69', mode="collection")>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfset thisMembernumber = event.getValue('membernumber')>
		<cfset local.memberId = application.objMember.getMemberIDByMemberNumber(memberNumber="#thisMembernumber#", orgID=arguments.event.getValue('mc_siteinfo.orgID'))/>
		<cfset local.memberDetails = application.objMember.getMemberInfo(memberID=local.memberId, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberid=local.memberId,orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryFirstAddressType = application.objMember.getMemberAddressByFirstAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.memberId)>
		<cfset local.qryGetMemberPhones = application.objMember.getMemberPhones(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=val(local.memberID), addressTypeID=local.qryFirstAddressType.addressTypeID)>
	<cfelse>
		<cfset local.memberId = val(session.cfcUser.memberData.memberID)>
		<cfset local.memberDetails = application.objMember.getMemberInfo(memberID=local.memberId, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberid=local.memberId,orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryFirstAddressType = application.objMember.getMemberAddressByFirstAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.memberId)>
		<cfset local.qryGetMemberPhones = application.objMember.getMemberPhones(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=val(local.memberID), addressTypeID=local.qryFirstAddressType.addressTypeID)>
	</cfif>
	
	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		select e.eventid, r.registrantID, r.dateRegistered, et.startTime as eventStart, c.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,et.startTime) as CLEYear
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID and r.recordedOnSiteID = evr.siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid and e.siteID = evr.siteID
		inner join dbo.cms_contentLanguages as c on c.contentID = e.eventContentID and c.languageID = 1
		inner join dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid 
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID 
		inner join dbo.crd_authorities as ca on ca.authorityID = cat.authorityID AND authorityName like 'North Carolina State Bar, Board of Continuing Legal Education'
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			inner join dbo.ams_members as m on m.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER"> AND m.membernumber=<cfqueryparam value="#event.getValue('membernumber')#" cfsqltype="CF_SQL_VARCHAR"> 	
		<cfelse>
			inner join dbo.ams_members as m on m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID
		inner join dbo.crd_offerings as co on co.eventID = e.eventID
		and et.endTime between co.offeredStartDate and co.offeredEndDate
		and co.offeredStartDate >= <cfqueryparam value="#event.getValue('periodStartDate')#" cfsqltype="cf_sql_date">
		and co.offeredEndDate <= <cfqueryparam value="#event.getValue('periodEndDate')# 23:59:59.997" cfsqltype="cf_sql_timestamp">
		where r.memberid = mMerged.memberID
		order by r.dateRegistered desc, e.eventid
	</cfquery>
	<cfquery name="local.qryCLECreditTypes" datasource="#application.dsn.membercentral.dsn#">
		select distinct isnull(ast.ovTypeName,cat.typeName) as creditType
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID and r.recordedOnSiteID = evr.siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid and e.siteID = evr.siteID
		inner join dbo.ev_times as et on et.eventID = e.eventID 
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		inner join dbo.ams_members as m on m.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
		inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID
		inner join dbo.crd_offerings as co on co.eventID = e.eventID
		where r.memberid = mMerged.memberID
		order by creditType
	</cfquery>
	<cfquery name="local.qryCLETotals" dbtype="query">
		select CLEYear, creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by CLEYear, creditType
		order by CLEYear, totalCLE
	</cfquery>
	
	<cfset local.CLEYearStruct = structNew()>
	<cfloop query="local.qryCLETotals">
		<cfif not structKeyExists(local.CLEYearStruct,local.qryCLETotals.CLEYear)>
			<cfset local.CLEYearStruct[local.qryCLETotals.CLEYear] = structNew()>
		</cfif>
		<cfif not structKeyExists(local.CLEYearStruct[local.qryCLETotals.CLEYear],local.qryCLETotals.creditType)>
			<cfif local.qryCLETotals.totalCLE gt 0>
				<cfset local.CLEYearStruct[local.qryCLETotals.CLEYear][local.qryCLETotals.creditType] = local.qryCLETotals.totalCLE>
			<cfelse>
				<cfset local.CLEYearStruct[local.qryCLETotals.CLEYear][local.qryCLETotals.creditType] = "---">
			</cfif>
		</cfif>
	</cfloop>
	
	<cfsavecontent variable="local.CLEHead">
		<cfoutput>
			<style type="text/css">
				##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
				##periodStartDate, ##periodEndDate { 
					background-image:url("/assets/common/images/calendar/monthView.gif"); 
					background-position:right center; background-repeat:no-repeat; 
				}
				.alertMsg {
					text-align: left;
					padding: 5px 20px 5px 45px;
					border: 2px solid ##fc6;
				}
				.b { font-weight: bold; }
				.table-heading { border-bottom: 1px solid ##666; }
				.row-fluid.cleHistorySummary {
					color: ##666;
				}
				.span3.visible-phone { padding-left: 0px; }
				.row-fluid.inDesktop { display:block; }
				.row-fluid.inPhone { display:none; }
				@media screen and (max-width: 1280px) and (min-width: 980px) {
					.section-inner div.container div.row-fluid {
						padding-left: 0px !important;
						padding-right: 0px !important;
					}
				}
				@media screen and (min-width: 768px) and (max-width: 880px) {
					.span3 {
						width: 22% !important;
					}
				}
				@media screen and (min-width: 451px) and (max-width: 767px){
					.row-fluid.inDesktop { display:none; }
					.row-fluid.inPhone { display:block; }
					.innerSpan3 { display:inline !important; padding-right: 5px !important; }
					.leftDiv { display: inline !important;; }
					.rightDiv { width: 88% !important; float: right; }
					.contentRight { float: right; width: 80%; }
					table.filterArea td {
						display: table;
					}
					.startDate:nth-child(1) {
						float: left;
						margin-right: 5px;
					}
					td.r.endDate {
						float: left;
						margin-right: 5px;
					}
					
					.cleHistorySummary th {
						display: table-row;
					}
					.cleHistorySummaryHeading {
						border-bottom: 1px solid ##666;
					}
					.cleHistorySummary th { 
						border-bottom: none !important;
					}
					.cleHistorySummary tr {
						border-top: 8px solid ##fff;
					}
				}
				@media screen and (max-width: 450px){
					.row-fluid.inDesktop { display:none; }
					.row-fluid.inPhone { display:block; }
					.innerSpan3 { display:inline !important; padding-right: 5px !important; }
					.leftDiv { display: inline !important;; }
					.rightDiv { width: 88% !important; float: right; }
					.contentRight { float: right; width: 80%; }
					td.tsAppBodyText, .cleHistorySummary th, .filterArea td {
						display: table-row;
					}
					.cleHistoryDetail th {
						display: none;
					}
					.cleHistorySummaryHeading, .cleHistoryDetailHeading {
						border-bottom: 1px solid ##666;
					}
					.cleHistoryDetail tr td:nth-child(3), .cleHistoryDetail tr td:nth-child(4) {
						display: inline-block;
					}
					.memberNumber, .startDate, .endDate {
						display: table !important;
						float: left;
						margin-right: 5px;
					}
					.cleHistorySummary th { 
						border-bottom: none !important;
					}
					##membernumber { 
						width: 100%;
					}
					.cleHistorySummary tr {
						border-top: 8px solid ##fff;
					}
					.print { 
						margin-left: 5px;
					}
					.print .btn {
						padding: 0px 7px;
						margin-top: -4px;
					}
				}
			</style>
			<script language="JavaScript">
				function viewEVCert(rid) {
					var certURL = '/?pg=CLEHistory&panel=viewCert&mode=stream&rid=' + rid;
					window.open(certURL,'ViewCertificate','width=990,height=500');
				}
				function _FB_hasValue(obj, obj_type){
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
					else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
					else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
					else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
					else{ return true; }
				}
				function hideAlert() { $('##issuemsg').html('').hide(); };
				function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alertMsg').show(); };
				function _FB_validateForm(){
					var theForm = document.forms["frmMyCLEHistory"];
					var arrReq = new Array();
					if (typeof $('##membernumber') != "undefined" && !_FB_hasValue(theForm['membernumber'], 'TEXT')) arrReq[arrReq.length] ='Must enter MemberNumber before you can filter report.';
					if (arrReq.length > 0) {
						var msg = 'The following fields are required:\n\n';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
						showAlert(msg);
						return false;
					}
				}
				
				$(function() {
					mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
				});
			</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.CLEHead#">
	<cfoutput>
		<div class="tsAppHeading">My CLE History</div>
		<br/>

		<cfform name="frmMyCLEHistory" id="frmMyCLEHistory" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">

			<cfif local.memberId gt 0>
				<cfif local.memberDetails.recordcount>
					<cfset local.strData = {}>
					<cfset local.strData["m_firstname"] = ''>
					<cfset local.strData["m_middlename"] = ''>
					<cfset local.strData["m_lastname"] = ''>
					<cfset local.strData["m_suffix"] = ''>
					<cfset local.strData["m_professionalsuffix"] = ''>
					<cfset local.strData["m_company"] = ''>
					<cfset local.strData["me_305_email"] = ''>
					<cfset local.strData["mp_#local.qryFirstAddressType.addressTypeID#_#local.qryGetMemberPhones.phoneTypeID#"] = ''>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"m_firstname")>
						<cfset local.strData["m_firstname"] = local.memberDetails.firstName>
					</cfif>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"m_middlename")>
						<cfset local.strData["m_middlename"] = local.memberDetails.middleName>
					</cfif>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"m_lastname")>
						<cfset local.strData["m_lastname"] = local.memberDetails.lastName>
					</cfif>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"m_suffix")>
						<cfset local.strData["m_suffix"] = local.memberDetails.suffix>
					</cfif>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"m_professionalsuffix")>
						<cfset local.strData["m_professionalsuffix"] = local.memberDetails.professionalSuffix>
					</cfif>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"m_company")>
						<cfset local.strData["m_company"] = local.memberDetails.company>
					</cfif>
					<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"me_#local.qryMemberEmails.emailTypeID#_email")>
						<cfset local.strData["me_#local.qryMemberEmails.emailTypeID#_email"] = local.qryMemberEmails.email>
					</cfif>
					<cfif local.qryGetMemberPhones.recordcount>
						<cfif StructKeyExists(local.getFieldsInFieldset.strFields,"mp_#local.qryFirstAddressType.addressTypeID#_#local.qryGetMemberPhones.phoneTypeID#")>
							<cfset local.strData["mp_#local.qryFirstAddressType.addressTypeID#_#local.qryGetMemberPhones.phoneTypeID#"] = local.qryGetMemberPhones.phone>
						</cfif>
					</cfif>
					<cfif local.strData["m_firstname"] neq '' OR local.strData["m_middlename"] neq '' OR local.strData["m_lastname"] neq '' OR local.strData["m_suffix"] neq '' OR local.strData["m_professionalsuffix"] neq ''>
						#local.strData["m_firstname"]# #local.strData["m_middlename"]# #local.strData["m_lastname"]# #local.strData["m_suffix"]# #local.strData["m_professionalsuffix"]#
						<br />
					</cfif>
					<cfif local.strData["m_company"] neq ''> 
						#local.strData["m_company"]# 
						<br />
					</cfif>
					<cfif local.strData["mp_#local.qryFirstAddressType.addressTypeID#_#local.qryGetMemberPhones.phoneTypeID#"] neq ''> 
						#local.strData["mp_#local.qryFirstAddressType.addressTypeID#_#local.qryGetMemberPhones.phoneTypeID#"]#
						<br />
					</cfif>
					<cfif local.strData["me_#local.qryMemberEmails.emailTypeID#_email"] neq ''>
						#local.strData["me_#local.qryMemberEmails.emailTypeID#_email"]#
						<br />
					</cfif>
					<br />
				</cfif>
			</cfif>
			
			<h4 style="text-transform: none;">Accrediting Authority: </h4>
			<h6>North Carolina State Bar, Board of Continuing Legal Education</h6><br />
			<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
			<span class="print" style="float:right;">
				<button class="btn" type="button" onClick="window.print();" title="Print certificate"><i class="icon-print"></i> Print</button>
			</span>
			<table cellpadding="3" cellspacing="0" border="0" class="filterArea">
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<tr>
						<td class="r memberNumber">MemberNumber</td>
						<td colspan="4">
							<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber"  value="#event.getValue('membernumber')#" size="45" placeholder="Must enter MemberNumber before you can filter report.">
						</td>
					</tr>
				</cfif>
				<tr>
					<td class="r">Date Filter:</td>
					<td colspan="4">&nbsp;</td>
				</tr>
				<tr>
					<td class="r startDate">Start Date</td>
					<td class="startDate">
						<cfinput class="tsAppBodyText" type="text" name="periodStartDate"  id="periodStartDate" value="#event.getValue('periodStartDate')#" autocomplete="off" size="16">
						<a href="javascript:void(0)" onclick="javascript:mca_clearDateRangeField('periodStartDate');">clear</a>
					</td>
					<td>&nbsp;</td>
					<td class="r endDate">End Date</td>
					<td class="endDate">
						<cfinput class="tsAppBodyText" type="text" name="periodEndDate"  id="periodEndDate" value="#event.getValue('periodEndDate')#" autocomplete="off" size="16">
						<a href="javascript:void(0)" onclick="javascript:mca_clearDateRangeField('periodEndDate');">clear</a>
					</td>
				</tr>
				<tr>
					<td>&nbsp;</td>
					<td colspan="4">
						<button type="submit" name="btnSubmit">Search</button>
					</td>
				</tr>
			</table>
		</cfform>
		<br /><br />
	</cfoutput>	
	<cfsavecontent variable="local.CLEContent">
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
	
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText">#dateformat(local.qryCLE.eventStart,"mm/dd/yyyy")#</td>
				<td class="tsAppBodyText">#local.qryCLE.contentTitle#</td>
				<td class="tsAppBodyText"><a href="javascript:viewEVCert('#local.rID#');" title="Print certificate"><i class="icon-print"></i></a></td>
				<td class="tsAppBodyText" nowrap>
				<cfoutput>#local.qryCLE.creditValueAwarded#&nbsp;&nbsp;#local.qryCLE.creditType#<br/></cfoutput></td>
			</tr>	
		</cfoutput>
	</cfsavecontent>	
		<cfif local.qryCLE.recordcount>
			<cfoutput>
				<h4 class="cleHistorySummaryHeading">Summary</h4>
				<br />
				<div class="cleHistorySummary">
					<div class="row-fluid cleHistorySummary">
						<div class="row-fluid table-heading hidden-phone">
							<div class="span3 b">Year</div>
							<cfloop query="local.qryCLECreditTypes">
								<div class="span3 b">#local.qryCLECreditTypes.creditType#</div>
							</cfloop>
						</div>
						<cfloop collection="#local.CLEYearStruct#" item="local.thisYear">
							<div class="row-fluid inDesktop">
								<div class="span3"><div class="span3 visible-phone">Year</div><div class="span3">#local.thisYear#</div></div>
								<cfloop query="local.qryCLECreditTypes">
									<cfif structKeyExists(local.CLEYearStruct[local.thisYear],local.qryCLECreditTypes.creditType)>
										<div class="span3"><div class="span3 visible-phone">#local.qryCLECreditTypes.creditType#</div><div class="span3">#local.CLEYearStruct[local.thisYear][local.qryCLECreditTypes.creditType]#</div></div>
								<cfelse>
										<div class="span3"><div class="span3 visible-phone">#local.qryCLECreditTypes.creditType#</div><div class="span3">0.00</div></div>
									</cfif>
								</cfloop>
							</div>
							<div class="row-fluid inPhone">
								<div class="inPhone leftDiv b">#local.thisYear#</div>
								<div class="inPhone rightDiv">
									<cfloop query="local.qryCLECreditTypes">
										<cfif structKeyExists(local.CLEYearStruct[local.thisYear],local.qryCLECreditTypes.creditType)>
											<div class="span3"><div class="span3 visible-phone"><div class="span3 innerSpan3">#local.CLEYearStruct[local.thisYear][local.qryCLECreditTypes.creditType]#</div><div class="contentRight">#local.qryCLECreditTypes.creditType#</div></div></div>
										<cfelse>
											<div class="span3"><div class="span3 visible-phone"><div class="span3 innerSpan3">0.00</div><div class="contentRight">#local.qryCLECreditTypes.creditType#</div></div></div>
										</cfif>
									</cfloop>
								</div>
							</div>
						</cfloop>
					</div>
				</div>
				<br/>
				<h4 class="cleHistoryDetailHeading">Detail</h4>
				<br />
				<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory" class="cleHistoryDetail">
					<tr class="tsAppBodyText">
						<th style="width:25%;">Date</th>
						<th style="width:50%;">Title</th>
						<th colspan="2" style="width:20%;">Credit Awarded</th>
					</tr>				
					#local.CLEContent#			
				</table>
			</cfoutput>
		<cfelse>
			<cfoutput>
				<div class="tsAppBodyText">
					There are no Live Conferences & Events to display.
				</div>	
			</cfoutput>
		</cfif>	
</cfif>