<cfset local.dataStruct = attributes.data>
<cfset local.rc = attributes.event.getCollection()>

<cfinclude template="../commonJS.cfm">

<!--- extra style sheet info for tabs --->
<cfsavecontent variable="local.tabs">
    <cfoutput>
    <style type="text/css">
        ##tabs { margin:0; padding-left:10px; }
        ##tabs ul, ##tabs li { display:inline; list-style-type:none; margin:0 8px 0 0; padding:0; }
        ##tabs a:link, ##tabs a:visited { background:##E8EBF0; border:1px solid ##ccc; border-bottom:none; color:##666; float:left; text-decoration:none; }
        ##tabs a:link.active, ##tabs a:visited.active { border-bottom:1px solid ##fff; color:##000; }
        ##tabs a:hover { color:##f00; }
        ##tabs a{padding:12px 8px;}
        ##tabs li.active,##tabs li.active a{background: ##fff;}
        .tabsetCD ##tabs li##nav_CD a,
        .tabsetPD ##tabs li##nav_PD a,
        .tabsetPMD ##tabs li##nav_PMD a,
        .tabsetPV ##tabs li##nav_PV a,
        .tabsetPDA ##tabs li##nav_PDA a,
        .tabsetPDT ##tabs li##nav_PDT a { background:##fff; border-bottom:1px solid ##fff; color:##000; }
        ##tabs ul a:hover { color:##f00 !important; }
        ##s_listing { border:1px solid ##ccc; border-top:none;}
        div.s_pgtop { padding:3px 3px 6px 3px; }
        div.s_rhrd { background-color:##DEDEDE;padding:3px;margin-bottom:3px; }
        div.s_row { padding:10px; }
        div.s_row_alt { background-color:##DEDEDE; }
        div.s_act { float:none; padding:0 0 5px 5px; text-align:left;}
        div.s_dtl { margin-left:20px; }
        div.s_dtl .alert{margin-bottom:0;margin-top:10px;}
        div.s_opt { padding:3px; }
        ##s_listing ##paginationBar .pager li>a, .pager li>span  {padding: 2px 10px;font-size: 13px;}
        ##s_listing .paginationBar .currentLabel{font-size:13px;}
        ##s_listing ##paginationBar .pager{margin: 2px;}
        .s_action{display:inline;}
        b.s_label{font-weight:600;}
        .s_opt i[class*=icon]{text-decoration:none;}
        @media screen and (max-width:767px){
            .s_action{display:block;padding-left:20px;}
            .s_act{text-align:right!important;}
            .pager li{display:block;}
            ##myDocumentsTabs .nav-tabs li,##myDocumentsTabs .nav-tabs li > a{width:100%;}
            ##myDocumentsTabs .nav-tabs li > a{text-align:center;}
        }
    </style>
    </cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.tabs#">
<cfoutput>
    <div class="row">
        <div id="mydocumentWrapper">
            <div class="hidden-phone">
                <div class="navbar" id="tsSearchAppWrapper">
                    <div class="navbar-inner">
                        <ul class="nav" id="tssearchbar"></ul>
                        <ul class="nav" id="tsdocumentviewerbar" style="display:none;;"></ul>
                        <ul class="nav" id="tsinlinecartbar" style="display:none;">
                            <li id="cartnav_checkout">
                                <a href="javascript:doCheckoutDocumentCart(#session.cfcuser.memberdata.memberid#, #local.dataStruct.hasMissingTaxInfo#);"><i class="icon-shopping-cart" style="vertical-align: inherit;"></i> Checkout</a>
                            </li>
                            <li id="cartnav_close">
                                <a href="javascript:cart_closeInlineCart();"><i class="icon-remove-circle" style="vertical-align: inherit;"></i> Close Cart</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div id="tsDocumentViewer" style="height: 500px; overflow: hidden;display:none;"></div><div id="tsCartViewer" style="display:none;"></div>
            <!--- header --->
            <div style="margin-top:8px;margin-bottom:10px" id="searchMyDocumentsButtonDiv">
                <div class="row-fluid">
                    <div class="span6"><h3>My Documents<h3></div>
                    <div class="span6 text-right">
                        <a class="btn btn-default" href="javascript:self.location.href='/?pg=uploaddocuments&ul=1';">
                            <i class="icon icon-upload" style="vertical-align:middle;padding:2px;"></i><b>Upload Documents</b>
                        </a>
                        <a class="btn btn-default" href="javascript:self.location.href='/?pg=search&bid=#local.dataStruct.mydocumentsBucketID#';">
                            <i class="icon icon-search" style="vertical-align:middle;padding:2px;"></i><b>Search My Documents</b>
                        </a>
                    </div>
                </div>
            </div>
            <!--- tabs --->
            <div id="myDocumentsTabs" class="tabset#local.dataStruct.tab#">
                <ul class="nav nav-tabs" id="tabs">
                    <li id="nav_CD"<cfif local.dataStruct.tab eq 'CD'> class="active"</cfif>><a href="/?pg=myDocuments&tab=CD">Contributed Documents</a></li>
                    <li id="nav_PD"<cfif local.dataStruct.tab eq 'PD'> class="active"</cfif>><a href="/?pg=myDocuments&tab=PD">Documents</a></li>
                    <li id="nav_PV"<cfif local.dataStruct.tab eq 'PV'> class="active"</cfif>><a href="/?pg=myDocuments&tab=PV">Verdicts</a></li>
                    <li id="nav_PMD"<cfif local.dataStruct.tab eq 'PMD'> class="active"</cfif>><a href="/?pg=myDocuments&tab=PMD">Medline Documents</a></li>
                    <li id="nav_PDA"<cfif local.dataStruct.tab eq 'PDA'> class="active"</cfif>><a href="/?pg=myDocuments&tab=PDA">Disciplinary Actions</a></li>
                    <li id="nav_PDT"<cfif local.dataStruct.tab eq 'PDT'> class="active"</cfif>><a href="/?pg=myDocuments&tab=PDT">Expert Challenges</a></li>
                    <cfif local.dataStruct.documentChatAllowed>
                    <li id="nav_PTSAI"<cfif local.dataStruct.tab eq 'PTSAI'> class="active"</cfif>><a href="/?pg=myDocuments&tab=PTSAI">AI Expert Case Files</a></li>
                    </cfif>
                </ul>
            </div>

            <!--- switch depending on tab --->
            <div id="s_listing" class="tab-content">
                <div class="tab-pane active">
                </div>
            </div>
        </div>
    </div>
</cfoutput>
