<cfsavecontent variable="local.customJS">
	<script type="text/javascript">
		function b_search() {
			$('#searchBoxError').html('').hide();
			var isEmptyForm = bk_isEmptySearchForm('frms_Search',true);
			if (isEmptyForm){
				$('#searchBoxError').html('Search using at least one criteria below.').show();
				$("form#frms_Search input:text").first().focus();
				return false;
			} else {
				$('form#frms_Search #s_btn').html('<div><i class="icon-refresh fa-spin"></i> Please Wait...</div>').attr('disabled','disabled');
				return true;
			}
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.customJS,'\s{2,}','','ALL')#">

<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#

<div id="searchBoxError" class="alert alert-error hide"></div>

<cfform name="frms_Search" id="frms_Search" class="form-horizontal form-medium-lg" method="POST" action="/?pg=search&bid=#arguments.bucketID#&s_a=doSearch" onsubmit="return b_search();">
	<div class="control-group">
		<label class="control-label" for="s_code">Rule:</label>
		<div class="controls">
			<cfselect query="local.qryCodes" selected="#local.strSearchForm.s_code#" display="code" value="codeid" name="s_code" id="s_code" queryPosition="below"><option value="">All Rules</option></cfselect>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_topic">Topic:</label>
		<div class="controls">
			<cfselect query="local.qryTopics" selected="#local.strSearchForm.s_topic#" display="topic" value="topicid" name="s_topic" id="s_topic" class="tsAppBodyText" queryPosition="below"><option value="">All Topics</option></cfselect>
		</div>
	</div>
	<br/>
	<div class="control-group">
		<label class="control-label" for="sdcbaleq_s_key_all">With <b>all</b> of these words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_all" id="sdcbaleq_s_key_all" value="#local.strSearchForm.s_key_all#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_phrase">With the <b>exact phrase</b>:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_phrase"  id="s_key_phrase" value="#local.strSearchForm.s_key_phrase#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_one">With <b>at least one</b> of the words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_one"  id="s_key_one" value="#local.strSearchForm.s_key_one#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" for="s_key_x"><b>Without</b> the words:</label>
		<div class="controls">
			<cfinput type="text" name="s_key_x"  id="s_key_x" value="#local.strSearchForm.s_key_x#" size="32" maxlength="200">
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<cfinput type="hidden" name="s_frm" id="s_frm" value="1">
			<button type="submit" name="s_btn" id="s_btn" class="btn">Search</button>
		</div>
	</div>
</cfform>
</cfoutput>