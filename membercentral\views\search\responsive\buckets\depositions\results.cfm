<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#

<cfset local.formattedPctotal = (local.pcRemainingtotal LT 0 ? "-" : "") & "$" & replace(abs(local.pcRemainingtotal), ".00", "")>
<div style="padding-bottom:10px;">
	<button class="btn searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
	<button class="btn" onclick="document.location.href='/?pg=uploadDocuments'" id="creditAmtRemaining" data-creditAmtRemaining="<cfoutput>#local.pcRemainingtotal#</cfoutput>"><i class="icon-upload"></i> Upload a deposition <span style="font-weight:normal;font-style:italic;">(Earn up to $30 in purchase credits)</span></button>
</div>
<cfif local.qryResults.recordcount EQ 0>
	#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#
<cfelse>
	<cfset local.tmpSort = ArrayNew(1)>
	<cfset ArrayAppend(local.tmpSort,"rank|Relevance")>
	<cfset ArrayAppend(local.tmpSort,"date|Date")>
	<cfset ArrayAppend(local.tmpSort,"case|Case")>
	<cfset ArrayAppend(local.tmpSort,"witness|Witness")>

	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='document', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=arguments.sortType, viewDirectory=arguments.viewDirectory)#
	<div style="clear:both;"></div>
	<div id="searchSponsorRightSideContainer" class="hidden-phone hidden-tablet"></div>
	
	<!--- results table --->
	<cfset local.currentDocument = CreateObject('component','model.system.platform.tsDocument')>
	<cfset local.objPlans = CreateObject("component","model.system.platform.tsPlans")>
	<cfloop query="local.qryResults">
		<cfset local.currentDocument.load(local.qryResults.documentid)>
		<cfset local.mycost = local.currentDocument.getPrice(
			membertype=variables.cfcuser_membertype,
			billingstate=variables.cfcuser_billingstate,
			billingzip=variables.cfcuser_billingzip,
			websiteorgcode=session.mcstruct.sitecode,
			depomemberdataid=variables.cfcuser_depomemberdataid)>
		<cfset local.docCaseRefDescTxt = '#trim(local.qryResults.expertname)# from #DateFormat(local.qryResults.DocumentDate, "m/d/yyyy")# - #replace(dollarformat(local.mycost.price),".00","")#'>
		<cfif local.qryResults.inCart>
			<cfset local.inCartClass = "" />
			<cfset local.addToCartClass = "hide" />
		<cfelse>
			<cfset local.inCartClass = "hide" />
			<cfset local.addToCartClass = "" />
		</cfif>
		<div class="row-fluid tsDocViewerDocument s_row" data-tsdocumentid="#local.qryResults.documentid#">
			<div class="span12 well bk-mb-0 bk-pb-0 bk-pt-1">
				<div class="bk-d-flex">
					<div class="bk-col-auto p-3">
						<div class="s_opt">							
							<div class="bk-mb-1 text-center">
								<cfif ListFind("1,8",variables.cfcuser_membertype)>											
									<a href="javascript:viewDocPrompt(#local.qryResults.documentid#);"  title="Click to view document">
										<img src="#application.paths.images.url#images/search/ReadTranscript.png" width="80" border="0" class="hidden-phone"/>
									</a>
								<cfelse>
									<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchID#);" title="Click to view document">
										<img src="#application.paths.images.url#images/search/ReadforFree.png" width="80" border="0" class="hidden-phone"/>
									</a>
								</cfif>
								<div class="bk-pt-1">#replace(dollarformat(local.mycost.price),".00","")#</div>
								<div>
								<a href="/?pg=uploadDocuments" class="btn btn-link btn-sm text-center p-0">You have 
									<span id="creditAmtRemaining_#local.qryResults.documentID#" class="creditAmtRemaining" data-mycostprice="#local.mycost.price#">
										<cfoutput>#local.formattedPctotal#</cfoutput>
									</span><br> in credits
								</a>
								</div>
							</div>							
						</div>
					</div>
					<div class="bk-col">						
						<div class="s_dtl ml-10 bk-minh-104">
							<a 
								<cfif ListFind("1,8", variables.cfcuser_membertype)>
									href="javascript:viewDocPrompt(#local.qryResults.documentid#);" 
								<cfelse>
									href="javascript:docViewer(#local.qryResults.documentid#, #variables.thisBucketCartItemTypeID#, #arguments.searchID#);" 
								</cfif>
								title="Click to view document">
								<b>
									<cfif len(trim(local.qryResults.expertname))>
										#trim(local.qryResults.expertname)#
									<cfelse>
										Unknown Expert
									</cfif> 
									- #DateFormat(local.qryResults.DocumentDate, "mmm d, yyyy")# 
									<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
										- Document ###local.qryResults.documentid#
									</cfif>
								</b>
							</a>							
							<br>#local.qryResults.style#
							<br/>Jurisdiction: #local.qryResults.stateName#
							<cfif (variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned) AND NOT ListFind("1,8",variables.cfcuser_membertype)>
								<br/>Shared by: 
								<cfset local.tmp = "">
								<cfif len(local.qryResults.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingfirm)></cfif>
								<cfif len(local.qryResults.BillingCity)><cfset local.tmp = listAppend(local.tmp, local.qryResults.BillingCity)></cfif>
								<cfif len(local.qryResults.BillingState)><cfset local.tmp = listAppend(local.tmp, local.qryResults.BillingState)></cfif>
								#replace(local.tmp,",",", ","ALL")#
							</cfif>
						</div>
						<div class="py-10">							
							<span class="#local.inCartClass#" id="s_incart#local.qryResults.documentID#">
								<button type="button" class="btn btn-link btn-sm text-center" onClick="viewCart();">
									<i class="icon-shopping-cart bk-mr-2"></i>In Cart
								</button>
							</span>
							<cfif local.qryResults.contributed>
								<span class="bk-font-size-md">
									<i class="fa fa-check-square text-success" aria-hidden="true"></i> Contributed
								</span>
							<cfelseif not local.qryResults.owned>
								<span class="#local.addToCartClass#" id="s_addtocart#local.qryResults.documentID#">
									<button name="addCart_btn_#local.qryResults.documentID#" id="addCart_btn_#local.qryResults.documentID#" type="button" class="btn btn-secondary btn-sm text-center" onClick="addDepoDocToCartPrompt(#local.qryResults.documentid#,#arguments.searchID#);" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" ><i class="icon-plus-sign" title="Add to Cart"></i> Add to Cart</button>
								</span>
							<cfelseif local.qryResults.owned>
								<span class="#local.addToCartClass#" id="s_addtocart#local.qryResults.documentID#">
									<span class="bk-font-size-xs bk-mb-2 bk-font-weight-bold bk-text-nowrap" style="padding: 4px 12px;"><i class="fa fa-check-square text-success" aria-hidden="true"></i> Purchased</span>
								</span>
							</cfif>
							<cfif len(local.qryResults.uploadpdfdate)>
								<cfset local.fileName = '#local.currentDocument.expertFileName#.pdf'>

								<cfif local.qryResults.owned>
									<cfset local.onClickFn = "downloadDocImmedate(#local.qryResults.DocumentID#,'pdf');">
								<cfelse>
									<cfset local.onClickFn = "oneClickPurchasePrompt(#local.qryResults.documentid#,#arguments.searchID#,'#encodeForJavaScript(local.fileName)#','pdf',#local.qryResults.currentRow#,'dload');">
								</cfif>								
								<button type="button" name="pdf_btn_#local.qryResults.currentRow#" id="pdf_btn_#local.qryResults.currentRow#" class="btn btn-secondary btn-sm" onClick="#local.onClickFn#" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" data-downloaddocid="#local.qryResults.documentID#" data-downloaddocformat="pdf">
									<i class="icon-download-alt" title="Download PDF"></i> Download PDF
								</button>								
							</cfif>
							<cfif len(local.qryResults.uploadpdfdate) and len(local.dropboxAppKey)>
								<cfif local.qryResults.owned>
									<cfset local.onClickFn = "doDropboxSave(#local.qryResults.documentID#,'#local.currentDocument.expertFileName#.pdf','pdf',#local.qryResults.currentRow#);">
								<cfelse>
									<cfset local.onClickFn = "oneClickPurchasePrompt(#local.qryResults.documentid#,#arguments.searchID#,'#local.currentDocument.expertFileName#.pdf','pdf',#local.qryResults.currentRow#,'dbox');">
								</cfif>
								<button name="dropb_btn_#local.qryResults.currentRow#" id="dropb_btn_#local.qryResults.currentRow#" type="button" class="btn btn-secondary btn-sm drop-box-file-list" onClick="#local.onClickFn#" data-elmid="#local.qryResults.documentID#" data-elmname="#local.currentDocument.expertFileName#.pdf" data-elmext="pdf" data-rowid="#local.qryResults.currentRow#" data-doccaserefdesctxt="#encodeForHTMLAttribute(local.docCaseRefDescTxt)#" data-dropboxdownloaddocid="#local.qryResults.documentID#"><img src="/assets/common/images/dropbox-icon-ios.png" id="dropb_img_#local.qryResults.currentRow#" data-toggle="tooltip" width="15" height="15" border="0" title="Add to Dropbox"/> Add to Dropbox</button>
							</cfif>
							<cfif not len(local.qryResults.uploadpdfdate)>
								<span id="txt_#local.qryResults.documentID#">
									<cfif local.qryResults.owned>
										<button name="txt_btn_#local.qryResults.currentRow#" type="button" class="btn btn-secondary btn-sm" onClick="downloadDocImmedate(#local.qryResults.DocumentID#,'txt');" style="margin-top:10px; text-align:center;"><i class="icon-download-alt" title="Download TEXT" data-downloaddocid="#local.qryResults.documentID#" data-downloaddocformat="txt"></i> Download TEXT</button>
									</cfif>
								</span>
							</cfif>
						</div>
					</div>
				</div>
			</div>
		</div>
	</cfloop>
	<br clear="all"/>

	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.qryResults.itemCount, itemWord='document', itemWordSuffix='s', arrSort=local.tmpSort, currentSort=arguments.sortType, viewDirectory=arguments.viewDirectory)#
</cfif>
</cfoutput>