<cfsavecontent variable="local.reportsListJS">
<cfoutput>
	<script language="javascript">
		let savedReportsTable;

		function initializeSavedReportsTable(){
			let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			savedReportsTable = $('##savedReportsList').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.savedReportsLink#",
					"type": "post",
					"data": function(d) {
						var arrFormData = $('##frmReportSearch').serializeArray();
						$.each(arrFormData, function() {
							d[this.name] = this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? '<div>'+ data.reportname +'</div><div class="small text-dim">'+ data.tooldesc +'</div>' : data;
						},
						"width": "40%"
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? '<div>'+ data.lastrundatetime +'</div><div class="small text-dim">'+data.lastrunname+'</div>' : data;
						},
						"width": "19%"
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? '<div>'+ data.createddatetime +'</div><div class="small text-dim">'+data.creatorname+'</div>' : data;
						},
						"width": "19%"
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if (data.viewrights) {
									renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="viewReport(\''+data.reporturl+'\');return false;" title="View Report"><i class="fa-solid fa-eye"></i></a>';
								}
								if (data.editrights) {
									renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="copyReport('+data.reportid+',\''+data.tooltype+'\');return false;" title="Copy Report"><i class="fa-solid fa-clone"></i></a>';
									renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="statusChange('+data.reportid+',\''+data.statuschangeto+'\',\''+data.reportname.replace(/'/g, "\\'").replace(/"/g, '\\"')+'\');return false;" title="'+data.switchtext+'"><i class="fa-solid fa-shuffle"></i></a>';
								}
								if (data.deleterights) {
									renderData += '<a href="##" id="btnDelRpt'+data.reportid+'" class="btn btn-sm btn-outline-danger px-2 m-1" onclick="deleteReport('+data.reportid+');return false;" title="Delete Report" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
								}
							}

							return type === 'display' ? renderData : data;
						},
						"width": "22%",
						"className": "text-center",
						"orderable": false
					}
				],
				"order": [[1, 'desc']],
				"searching": false,
				"createdRow": function( row, data, index ) {
					if (data.status.toLowerCase() != "active") {debugger;
						$('td', row).eq(0).prepend('<span class="badge badge-warning float-right ml-2">Inactive</span>');
					}
				}
			});
		}
		function reloadSavedReports() {
			setMultiSelectFilterData();
			savedReportsTable.draw(false);
		}
		function filterReports() {
			if (!$('##divFilterForm').is(':visible')) {
				$('div.reportToolBarItem').hide();
				$('##divFilterForm').show();
			}
		}
		function setMultiSelectFilterData(){
			var cbIDText = $('##cbID').val();
			$('##cbIDList').val(cbIDText);

			var rtIDText = $('##rtID').val();
			$('##rtIDList').val(rtIDText);
		}
		function filterReportGrid() {
			$('##divFilterForm').hide('slow');
			reloadSavedReports();
		}
		function clearReportGrid() {
			$('##frmReportSearch')[0].reset();
			$('##frmReportSearch [data-toggle="custom-select2"]').trigger('change');
			$('##divFilterForm').hide('slow');
			reloadSavedReports();
		}
		function statusChange(thisReportID, statusChangeTo, reportName) {
			// If activating, proceed directly
			if (statusChangeTo === 'A') {
				executeStatusChange(thisReportID, statusChangeTo);
				return;
			}

			// If inactivating, check for schedules first
			if (statusChangeTo === 'I') {
				checkReportSchedulesAndConfirm(thisReportID, reportName);
			}
		}
		function executeStatusChange(reportID, statusChangeTo) {
			let statusChangeResult = function(r) {
				if (r.success && r.success.toLowerCase() === 'true') {debugger;
					// Reload the appropriate table based on context
					if(MCModalUtils.isShown())
						top.MCModalUtils.hideModal();
					reloadSavedReports();
				} else {
					alert('We were unable to ' + (statusChangeTo === 'A' ? 'activate' : 'inactivate') + ' this report. Try again.');
				}
			};

			let objParams = { reportID: reportID, status: statusChangeTo };
			TS_AJX('SAVEDREPORT', 'doStatusChange', objParams, statusChangeResult, statusChangeResult, 10000, statusChangeResult);
		}
		function checkReportSchedulesAndConfirm(reportID, reportName) {
			let checkSchedulesResult = function(r) {
				if (r.success && r.success.toLowerCase() === 'true') {
					confirmReportInactivation(reportID, reportName || r.reportname, r.hasschedules);
				} else {
					alert('Unable to check report schedules. Please try again.');
				}
			};

			let objParams = { reportID: reportID };
			TS_AJX('SAVEDREPORT', 'checkReportSchedules', objParams, checkSchedulesResult, checkSchedulesResult, 10000, checkSchedulesResult);
		}
		function confirmReportInactivation(reportID, reportName, hasSchedules) {
			let title, content, confirmBtn;debugger;

			if (hasSchedules) {
				content = `<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning mb-0">
						<span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-solid fa-triangle-exclamation"></i></span>
						<span><strong>Warning:</strong> This report has scheduled deliveries.</p>
						<p>You are about to inactivate "<strong>${reportName}</strong>".</p>
						<p>This report is scheduled to run automatically. By continuing, it will be skipped from future scheduled runs until reactivated.</p>
						<p>Are you sure you want to continue?</p></span>
				   </div>`;
			} else {
				executeStatusChange(reportID, 'I');
				return;
			}

			MCModalUtils.showConfirmationPopup(
				'Confirm Report Inactivation',
				content,
				{btnID: 'btnCancelInactivateReport', label: 'Cancel', classList: 'btn-sm btn-secondary', clickFnName: 'hideModal', clickFnArgs: {}, isClickFnPromise:0},
				{btnID: 'btnConfirmInactivateReport', label: 'Inactivate Report', classList: 'btn-sm btn-primary', clickFnName: 'executeStatusChange',clickFnArgs:{reportID: reportID, statusChangeTo: 'I'}, isClickFnPromise:0},
				'lg'
			);
		}
		function hideModal() {
			top.MCModalUtils.hideModal();
		}
		function viewReport(r) {
			self.location.href = unescape(r);
		}
		function copyReport(r,tt) {
			self.location.href = '#this.link.copyReport#&rptId=' + r + '&toolType=' + tt;
		}
		function deleteReport(rid) {
			var deleteResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadSavedReports();
				} else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('We were unable to delete this Report. Try again.');
				}
			};

			let delBtn = $('##btnDelRpt'+rid);
			mca_initConfirmButton(delBtn, function(){
				let objParams = { rptID:rid };
				TS_AJX('SAVEDREPORT', 'deleteReport', objParams, deleteResult, deleteResult, 10000, deleteResult);
			});
		}
		$(document).ready(function() {
			mca_setupDatePickerRangeFields('crdateStart','crdateEnd');
			mca_setupDatePickerRangeFields('lrdateStart','lrdateEnd');
			mca_setupCalendarIcons('frmReportSearch');
			mca_setupSelect2();
		});
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.reportsListJS#">

<cfoutput>
<div class="toolButtonBar">
	<div><a href="javascript:filterReports();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter reports."><i class="fa-solid fa-filter"></i> Filter Reports</a></div>
</div>
<div id="divFilterForm" class="reportToolBarItem" style="display:none;">
	<div style="margin-top:10px;margin-bottom:30px;">
		<cfform action="#this.link.showAllReports#" method="POST" name="frmReportSearch" id="frmReportSearch" onsubmit="filterReportGrid(); return false;" >
			<input type="hidden" name="cbIDList" id="cbIDList">
			<input type="hidden" name="rtIDList" id="rtIDList">
			
			<div class="row mb-2">
				<div class="col-xl-12">
					<div class="card card-box mb-1">
						<div class="card-header bg-light">
							<div class="card-header--title font-weight-bold font-size-lg">
								Filter Reports
							</div>
						</div>
						<div class="card-body pb-3">
							<div class="form-row">
								<div class="col-md-6 col-sm-12 pr-md-3">
									<div class="form-group">
										<div class="form-label-group">
											<input type="text" size="35" name="kw" id="kw" class="form-control" value="#arguments.event.getValue('kw','')#">
											<label for="kw">Keywords</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 col-sm-12">
									<div class="form-row">
										<div class="col-sm-6">
											<div class="form-group">
												<div class="form-label-group">
													<div class="input-group">
														<input type="text" name="crdateStart" id="crdateStart" value="" size="16" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="crdateStart"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('crdateStart');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="crdateStart">Created From</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-sm-6">
											<div class="form-group">
												<div class="form-label-group">
													<div class="input-group">
														<input type="text" name="crdateEnd" id="crdateEnd" value="" size="16" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="crdateEnd"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('crdateEnd');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="crdateEnd">Created To</label>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="form-row">
								<div class="col-md-6 col-sm-12 pr-md-3">
									<div class="form-group">
										<div class="form-label-group">
											<cfselect name="rtID" id="rtID" multiple="yes" class="form-control form-control-sm" passthrough="data-toggle=""custom-select2""" query="local.qryReportTypes" display="typeName" value="toolTypeID" selected="#arguments.event.getValue('rtID','')#"></cfselect>
											<label for="rtID">Report Types</label>
										</div>
									</div>
								</div>
								<div class="col-md-6 col-sm-12">
									<div class="form-row">
										<div class="col-sm-6">
											<div class="form-group">
												<div class="form-label-group">
													<div class="input-group">
														<input type="text" name="lrdateStart" id="lrdateStart" value="" size="16" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="lrdateStart"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('lrdateStart');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="lrdateStart">Last Run From</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-sm-6">
											<div class="form-group">
												<div class="form-label-group">
													<div class="input-group">
														<input type="text" name="lrdateEnd" id="lrdateEnd" value="" size="16" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="lrdateEnd"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('lrdateEnd');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="lrdateEnd">Last Run To</label>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="form-row">
								<div class="col-md-6 col-sm-12 pr-md-3">
									<div class="form-check-inline ml-1">
										<input type="checkbox" name="showInactiveReports" id="showInactiveReports" value="1" class="form-check-input" <cfif arguments.event.getValue('showInactiveReports', 0) is 1>checked</cfif>>
										<label for="showInactiveReports" class="form-check-label">Show Inactive Reports</label>
									</div>
								</div>
								<div class="col-md-6 col-sm-12">
									<cfif local.dsp neq "my">
										<div class="form-group">
											<div class="form-label-group">
												<cfselect name="cbID" id="cbID" multiple="yes" class="form-control  form-control-sm" passthrough="data-toggle=""custom-select2"" placeholder=""Created By""" query="local.qryReportCreators" display="createdByName" value="memberID" selected="#arguments.event.getValue('cbID','')#"></cfselect>
												<label for="cbID">Created By</label>
											</div>
										</div>
									</cfif>
								</div>
							</div>
						</div>
						<div class="card-footer text-right">
							<button type="button" class="btn btn-sm btn-secondary" onclick="clearReportGrid();">Clear Filters</button>
							<button type="submit" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Filter Reports</button>
						</div>
					</div>
				</div>
			</div>
		</cfform>
	</div>
</div>

<table id="savedReportsList" class="table table-striped table-bordered table-sm" style="width:100%">
	<thead>
		<tr>
			<th>Report Name</th>
			<th>Last Run</th>
			<th>Created</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>
</cfoutput>