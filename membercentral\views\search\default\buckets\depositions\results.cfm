<cfoutput>
<cfif len(local.strSearch.expertFName & local.strSearch.expertLName)>
	<cfset local.qryBucketInfo.bucketName = local.qryBucketInfo.bucketName & " ON #ucase(local.strSearch.expertFName)# #ucase(local.strSearch.expertLName)#">
</cfif>
#showHeader(bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)#
<cfset local.formattedPctotal = (local.pcRemainingtotal LT 0 ? "-" : "") & "$" & replace(abs(local.pcRemainingtotal), ".00", "")>
<div style="padding-bottom:10px;">
	<button class="tsAppBodyButton bucketHeaderButton searchReportButton hiddenButton" onclick="showReport();"><i class="icon-file icon-white"></i> Download Search Report</button>
	<button class="tsAppBodyButton bucketHeaderButton" onclick="document.location.href='/?pg=uploadDocuments'" id="creditAmtRemaining" data-creditAmtRemaining="<cfoutput>#local.pcRemainingtotal#</cfoutput>"><i class="icon-upload"></i> Upload a deposition <span style="font-weight:normal;font-style:italic;">(Earn up to $30 in purchase credits)</span></button>
</div>
<cfif local.qryResults.recordcount EQ 0>
	<div class="s_rnfne">#showCommonNotFound(arguments.bucketid,arguments.searchid,local.qryBucketInfo.bucketName,arguments.viewDirectory)#</div>
<cfelse>
	<cfset local.tmpSort = ArrayNew(1)>
	<cfset ArrayAppend(local.tmpSort,"rank|Relevance")>
	<cfset ArrayAppend(local.tmpSort,"date|Date")>
	<cfset ArrayAppend(local.tmpSort,"case|Case")>
	<cfset ArrayAppend(local.tmpSort,"witness|Witness")>

	#showSearchResultsPaging('top',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'document','s',local.tmpSort,arguments.sortType,arguments.viewDirectory)#
	<div style="clear:both;"></div>
	<div id="searchSponsorRightSideContainer"></div>

	<!--- results table --->
	<cfset local.currentDocument = CreateObject('component','model.system.platform.tsDocument')>
	<cfloop query="local.qryResults">
		<cfset local.currentDocument.load(local.qryResults.documentid)>
		<cfset local.mycost = local.currentDocument.getPrice(
			membertype=variables.cfcuser_membertype,
			billingstate=variables.cfcuser_billingstate,
			billingzip=variables.cfcuser_billingzip,
			websiteorgcode=session.mcstruct.sitecode,
			depomemberdataid=variables.cfcuser_depomemberdataid)>

		<cfif local.qryResults.inCart>
			<cfset local.inCartStyle = "" />
			<cfset local.addToCartStyle = "display:none;" />
		<cfelse>
			<cfset local.inCartStyle = "display:none;" />
			<cfset local.addToCartStyle = "" />
		</cfif>
		<div class="tsAppBodyText tsDocViewerDocument s_row <cfif local.qryResults.currentrow mod 2 is 0>s_row_alt</cfif>" data-tsdocumentid="#local.qryResults.documentid#">
			<table style="border-collapse: collapse; border: none; box-shadow: none;">
			<tr>
				<td style="border: none; box-shadow: none;vertical-align: top;">
					<div>
						<a 
							<cfif ListFind("1,8", variables.cfcuser_membertype)>
								href="javascript:viewDocPopUp(#local.qryResults.documentid#,1);" 
							<cfelse>
								href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchID#);" 
							</cfif>
							id="docLink1#local.qryResults.documentID#" title="Click to view document">
							<b>
								<cfif len(trim(local.qryResults.expertname))>
									#trim(local.qryResults.expertname)#
								<cfelse>
									Unknown Expert
								</cfif> 
								- #DateFormat(local.qryResults.DocumentDate, "mmm d, yyyy")# 
								<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
									- Document ###local.qryResults.documentid#
								</cfif>
							</b>
						</a>				
						<br/>#local.qryResults.style#
						<br/>Jurisdiction: #local.qryResults.stateName#
						<cfif (variables.cfcuser_DisplayVersion gt 1 or local.qryResults.owned) AND NOT ListFind("1,8",variables.cfcuser_membertype)>
							<br/>Shared by: 
							<cfset local.tmp = "">
							<cfif len(local.qryResults.billingfirm)><cfset local.tmp = listAppend(local.tmp,local.qryResults.billingfirm)></cfif>
							<cfif len(local.qryResults.BillingCity)><cfset local.tmp = listAppend(local.tmp, local.qryResults.BillingCity)></cfif>
							<cfif len(local.qryResults.BillingState)><cfset local.tmp = listAppend(local.tmp, local.qryResults.BillingState)></cfif>
							#replace(local.tmp,",",", ","ALL")#
						</cfif>

						<div class="s_opt bk-mt-2">
							<span style="#local.inCartStyle#" id="s_incart#local.qryResults.documentID#">
								<a href="javascript:viewCart();" title="View your cart/checkout"><img src="#application.paths.images.url#images/search/cart.png" width="16" height="16" border="0" />In Cart</a>
							</span>
							<cfif local.qryResults.owned>
								<a href="javascript:void(0)" style="#local.addToCartStyle#"><img src="#application.paths.images.url#images/search/green_tick.png" width="16" height="16" border="0">Purchased</a>
							</cfif>
							<cfif not local.qryResults.owned>
								<span style="#local.addToCartStyle#" id="s_addtocart#local.qryResults.documentID#">
									<a href="javascript:addToCart(#local.qryResults.documentid#,#arguments.searchid#);" title="Buy Document"><img src="#application.paths.images.url#images/search/cart.png" width="16" height="16" border="0">Buy</a>
								</span>
							<cfelse>
								<a href="javascript:downloadDoc(#local.qryResults.documentid#);" title="Download Document"><img src="#application.paths.images.url#images/search/page_white_put.png" width="16" height="16" border="0" />Download</a>
							</cfif>
						</div>
					</div>
				</td>
				<td style="border: none; box-shadow: none;vertical-align: top;width:1%;text-wrap: nowrap;">
					<div class="bk-text-center">
						<cfif ListFind("1,8",variables.cfcuser_membertype)>
							<a href="javascript:viewDocPrompt(#local.qryResults.documentid#);"  title="Click to view document">
								<img src="#application.paths.images.url#images/search/ReadTranscript.png" width="50" border="0"/>
							</a>
						<cfelse>
							<a href="javascript:docViewer(#local.qryResults.documentid#,#variables.thisBucketCartItemTypeID#,#arguments.searchID#);" title="Click to view document">
								<img src="#application.paths.images.url#images/search/ReadforFree.png" width="50" border="0"/>
							</a>
						</cfif>
						<br>
						#replace(dollarformat(local.mycost.price),".00","")# <br>
						<div>
						<a href="/?pg=uploadDocuments">You have 
							<span id="creditAmtRemaining_#local.qryResults.documentID#" class="creditAmtRemaining" data-mycostprice="#local.mycost.price#">
								<cfoutput>#local.formattedPctotal#</cfoutput>
							</span><br> in credits
						</a>
						</div>
					</div>
				</td>
			</tr>
			</table>
		</div>
	</cfloop>
	<br clear="all"/>
	#showSearchResultsPaging('bottom',arguments.searchid,arguments.startrow,local.NumTotalPages,local.NumCurrentPage,local.qryResults.itemCount,'document','s',local.tmpSort,arguments.sortType,arguments.viewDirectory)#
</cfif>
</cfoutput>