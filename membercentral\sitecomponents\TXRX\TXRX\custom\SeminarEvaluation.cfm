<cfset local.thisAppLink = "/?pg=SeminarEvaluation">
<cfset arguments.event.paramValue('isSubmitted',0)>

<cfif val(event.getValue('isSubmitted',0))>
	<cfparam name="form.ReinforcedCurrentPractice" default="">
	<cfparam name="form.ImproveMyPractice" default="">
	<cfparam name="form.ProvidedNewIdeas" default="">
	<cfparam name="form.EnhancedCurrentKnowledge" default="">
	
	<cfquery datasource="#application.dsn.customapps.dsn#" name="local.qryEvalCheck">
		SELECT	evaluationID
		FROM	TXRX_Evaluation_Responses
		WHERE	memberID = <cfqueryparam value="#event.getValue('memberID')#" cfsqltype="CF_SQL_INTEGER">
			AND eventID = <cfqueryparam value="#event.getValue('eventID')#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	
	<cfif local.qryEvalCheck.recordcount eq 0>
	
		<cfquery datasource="#application.dsn.customapps.dsn#" name="local.qryEvents">
			INSERT INTO TXRX_Evaluation_Responses(memberID
				, eventID
				, overallOuality
				, educationalNeeds
				, contentValue
				, effectiveness
				, appropriateness
				, materials
				, ReinforcedCurrentPractice
				, ProvidedNewIdeas
				, ImproveMyPractice
				, EnhancedCurrentKnowledge
				, ProgramIncreasedKnowledge
				, futureActivities
				, notPromote
				, didPromote
				, metLearningObjectives
				, notMetlearningObjectives
				, InfoCauseChanges
				, ThingsDifferently
				, HowCommitted
				, ActivityEnjoyedMost
				, ActivityEnjoyedLeast
				, AdditionalComments
				, subjectMatterKnowledge
				, CommPresentationSkills
				, OverallResponsiveness
				, SpeakerQuality
				, FollowUp
				, DateCompleted)
			VALUES (<cfqueryparam value="#event.getValue('memberID')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('eventID')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('overallOuality')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('educationalNeeds')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('contentValue')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('effectiveness')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('appropriateness')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('materials')#" cfsqltype="CF_SQL_INTEGER">
				<cfif len(form.ReinforcedCurrentPractice)>
					, <cfqueryparam value="#event.getValue('ReinforcedCurrentPractice')#" cfsqltype="CF_SQL_BIT">
				<cfelse>
					, <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
				</cfif>
				<cfif len(form.ProvidedNewIdeas)>
					, <cfqueryparam value="#event.getValue('ProvidedNewIdeas')#" cfsqltype="CF_SQL_BIT">
				<cfelse>
					, <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
				</cfif>
				<cfif len(form.ImproveMyPractice)>
					, <cfqueryparam value="#event.getValue('ImproveMyPractice')#" cfsqltype="CF_SQL_BIT">
				<cfelse>
					, <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
				</cfif>
				<cfif len(form.EnhancedCurrentKnowledge)>
					, <cfqueryparam value="#event.getValue('EnhancedCurrentKnowledge')#" cfsqltype="CF_SQL_BIT">
				<cfelse>
					, <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
				</cfif>
				, <cfqueryparam value="#event.getValue('ProgramIncreasedKnowledge')#" cfsqltype="CF_SQL_BIT">
				, <cfqueryparam value="#event.getValue('futureActivities')#" cfsqltype="CF_SQL_BIT">
				, <cfqueryparam value="#event.getValue('notPromote')#" cfsqltype="CF_SQL_BIT">
				<cfif len(event.getValue('didPromote'))>
				, <cfqueryparam value="#event.getValue('didPromote')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('metLearningObjectives'))>
				, <cfqueryparam value="#event.getValue('metLearningObjectives')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('notMetlearningObjectives'))>
				, <cfqueryparam value="#event.getValue('notMetlearningObjectives')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				, <cfqueryparam value="#event.getValue('InfoCauseChanges')#" cfsqltype="CF_SQL_BIT">
				<cfif len(event.getValue('ThingsDifferently'))>
				, <cfqueryparam value="#event.getValue('ThingsDifferently')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				, <cfqueryparam value="#event.getValue('HowCommitted')#" cfsqltype="CF_SQL_INTEGER">
				<cfif len(event.getValue('ActivityEnjoyedMost'))>
				, <cfqueryparam value="#event.getValue('ActivityEnjoyedMost')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('ActivityEnjoyedLeast'))>
				, <cfqueryparam value="#event.getValue('ActivityEnjoyedLeast')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(event.getValue('AdditionalComments'))>
				, <cfqueryparam value="#event.getValue('AdditionalComments')#" cfsqltype="CF_SQL_VARCHAR">
				<cfelse>
				, <cfqueryparam null="true" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				, <cfqueryparam value="#event.getValue('subjectMatterKnowledge')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('CommPresentationSkills')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('OverallResponsiveness')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('SpeakerQuality')#" cfsqltype="CF_SQL_INTEGER">
				, <cfqueryparam value="#event.getValue('FollowUp')#" cfsqltype="CF_SQL_BIT">
				, <cfqueryparam value="#now()#" cfsqltype="CF_SQL_DATE">
			)
		</cfquery>
		
		<cfset local.siteSRID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID')>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.awardCredit">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpNewRegCredits') IS NOT NULL 
				DROP TABLE ##tmpNewRegCredits;
			CREATE TABLE ##tmpNewRegCredits (requestID int);

			DECLARE @siteID int, @siteSRID int, @minRegistrantID int, @dataXML xml;
			DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
			SET @siteSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteSRID#">;

			INSERT INTO dbo.crd_requests (offeringTypeID, idNumber, lastDateToComplete, creditAwarded, creditValueAwarded, registrantID)
				OUTPUT INSERTED.requestID INTO ##tmpNewRegCredits
			SELECT DISTINCT ect.offeringTypeID, '#event.getValue('NABPNumber')#' AS idNumber, DATEADD(ss, 86340, DATEADD(dd, DATEDIFF(dd,0,evTime.lastDate), 0)), 
				1, ect.creditValue, ereg.registrantid
			FROM dbo.ams_members m
			INNER JOIN dbo.ev_events as e on e.siteID = @siteID
				and e.eventID = <cfqueryparam value="#event.getValue('eventID')#" cfsqltype="CF_SQL_INTEGER"> 
				AND e.status = 'A' 
				AND e.altRegistrationURL IS NULL 
			INNER JOIN dbo.ev_registration as er on er.eventid = e.eventid 
				AND er.siteID = @siteID
				AND er.status = 'A'
			INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID 
				AND ert.registrationType = 'Reg'
			INNER JOIN dbo.ev_registrants as ereg on ereg.memberid = m.memberid 
				AND ereg.registrationid = er.registrationid 
				AND ereg.attended = 1
				AND ereg.status = 'A'
			CROSS APPLY (
				select distinct 
					C.cred.value('@offeringTypeID','int') as offeringTypeID,
					C.cred.value('@creditValue','decimal(6,2)') as creditValue
				from (
					SELECT cast(isnull((
						select ECT.offeringTypeID, ECT.ASTID, ect.creditValue, isnull(ast.ovTypeName,cat.typeName) as creditType
						from dbo.crd_offeringTypes as ect
						inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
						inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
						where ect.offeringID = ec.offeringID
						FOR XML AUTO, ROOT('creditTypes'), TYPE
						),'<creditTypes/>') as xml) as eventCreditTypes
					FROM dbo.crd_offerings AS ec
					WHERE ec.eventID = e.eventID
						<cfif event.getValue('CreditType') eq "Pharmacist">
							AND ec.ASID = 5
						<cfelse>
							AND ec.ASID = 38
						</cfif>
					) t
				CROSS APPLY eventCreditTypes.nodes('//ect') as C(cred)
			) as ect
			cross apply (
				SELECT max(endTime) as lastDate
				from dbo.ev_times
				where eventID = e.eventID
			) as evTime
			left outer join dbo.crd_requests as rc on rc.registrantID = ereg.registrantid
				and rc.offeringTypeID = ect.offeringTypeID
			where rc.requestID is null
			AND m.orgid = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
			AND m.activeMemberID = <cfqueryparam value="#session.cfcuser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT @minRegistrantID = min(crd.registrantID)
			FROM ##tmpNewRegCredits as tmp
			INNER JOIN dbo.crd_requests as crd on crd.requestID = tmp.requestID
			WHERE crd.creditAwarded = 1;
			
			WHILE @minRegistrantID IS NOT NULL BEGIN
				SET @dataXML = null;

				SELECT @dataXML = 
					ISNULL((
						SELECT 'event' AS itemtype, @minRegistrantID AS itemid for xml path ('data')
					),'<data/>');
				
				INSERT INTO @tblHookListeners (executionType, objectPath)
				EXEC dbo.hooks_runHook @event='creditAwarded', @siteResourceID=@siteSRID, @dataXML=@dataXML;

				SELECT @minRegistrantID = min(crd.registrantID)
				FROM ##tmpNewRegCredits as tmp
				INNER JOIN dbo.crd_requests as crd on crd.requestID = tmp.requestID
				WHERE crd.creditAwarded = 1
				AND crd.registrantID > @minRegistrantID;
			END

			IF OBJECT_ID('tempdb..##tmpNewRegCredits') IS NOT NULL 
				DROP TABLE ##tmpNewRegCredits;
		</cfquery>
		
		<cfset local.memberid = session.cfcuser.memberData.memberID>
		<cfset local.orgID = arguments.event.getValue('mc_siteinfo.orgID')>
		
		<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.memberid)>
		<cfset local.objSaveMember.setCustomField(field='NABPNumber', value=event.getTrimValue('NABPNumber',''))>
		
		<cfset local.objSaveMember.setCustomField(field='DateOfBirth', value=event.getTrimValue('DateOfBirth',''))>
		
		
		<cfset local.objSaveMember.setCustomField(field='CreditType', value=event.getTrimValue('CreditType',''))>
		
		<cfset local.objSaveMember.setMemberType(memberType='User')>
		
	</cfif>

</cfif>

<cfquery datasource="#application.dsn.customapps.dsn#" name="local.qryCompletedEvals">
	SELECT	eventID
	FROM	TXRX_Evaluation_Responses
	WHERE	memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">
</cfquery>

<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCustomData">
	DECLARE @orgID int;

	SELECT @orgID = dbo.fn_getOrgIDFromOrgCode('TXRX');

	SELECT TOP 1
		mdcv1.columnValueInteger AS NABPNumber,
		mdcv2.columnValueString AS CreditType,
		mdcv3.columnvalueDate AS DateOfBirth
	FROM dbo.ams_members AS m
	LEFT OUTER JOIN dbo.ams_memberData AS md1
		INNER JOIN dbo.ams_memberDataColumnValues AS mdcv1 ON mdcv1.valueID = md1.valueID
		INNER JOIN dbo.ams_memberDataColumns AS mdc1 ON mdc1.orgID = @orgID
			AND mdc1.columnID = mdcv1.columnID
			AND mdc1.columnName = 'NABPNumber'
		ON md1.memberID = m.memberID
	LEFT OUTER JOIN dbo.ams_memberData AS md2
		INNER JOIN dbo.ams_memberDataColumnValues AS mdcv2 ON mdcv2.valueID = md2.valueID
		INNER JOIN dbo.ams_memberDataColumns AS mdc2 ON mdc2.orgID = @orgID
			AND mdc2.columnID = mdcv2.columnID
			AND mdc2.columnName = 'CreditType'
		ON md2.memberID = m.memberID
	LEFT OUTER JOIN dbo.ams_memberData AS md3
		INNER JOIN dbo.ams_memberDataColumnValues AS mdcv3 ON mdcv3.valueID = md3.valueID
		INNER JOIN dbo.ams_memberDataColumns AS mdc3 ON mdc3.orgID = @orgID
			AND mdc3.columnID = mdcv3.columnID
			AND mdc3.columnName = 'DateOfBirth'
		ON md3.memberID = m.memberID
	WHERE m.orgID = @orgID
	AND m.memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">
	AND m.memberID = m.activeMemberID
	AND m.[status] = 'A';
</cfquery>

<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvents">
	set nocount on;

	DECLARE @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;

	-- get events on site
	IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
		DROP TABLE ##tmpEventsOnSite;
	CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
		startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
		displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
		displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, 
		altRegistrationURL varchar(300), eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
		categoryIDList varchar(max));
	EXEC dbo.ev_getEventsOnSite @siteID=@siteID, @startDate='#dateformat(dateadd("m",-12,now()),"m/d/yyyy")#', @endDate=null, @categoryIDList='';

	SELECT e.eventid, tmp.eventTitle, tmp.startTime
	FROM dbo.ev_events as e
	INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
	INNER JOIN dbo.ev_registration AS er ON er.eventID = e.eventID and er.siteID = @siteID and er.status = 'A'
	INNER JOIN dbo.ev_registrationTypes AS ert ON er.registrationTypeID = ert.registrationTypeID and ert.registrationType = 'Reg'
	INNER JOIN dbo.ev_registrants AS r ON er.registrationID = r.registrationID 
		AND r.attended = 1
	INNER JOIN dbo.ams_members as m on m.memberID = r.memberID
	INNER JOIN dbo.ams_members as m2 on m.activeMemberID = m2.memberID 
		AND m2.memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">
	WHERE e.siteID = @siteID
	AND e.altRegistrationURL IS NULL 
	AND e.status = 'A'
	AND r.status = 'A'
	<cfif local.qryCompletedEvals.recordcount>
		AND e.eventID NOT IN (#ValueList(local.qryCompletedEvals.eventID)#)
	</cfif>
	ORDER BY tmp.startTime DESC;
	
	IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
		DROP TABLE ##tmpEventsOnSite;
</cfquery>

<cfoutput>
	
	<style type="text/css">
		##DateOfBirth { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
	</style>	
		
	<script type="text/javascript">
		function _FB_hasValue(obj, obj_type){
			if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
			else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
			else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
			else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }}return false; }
			else{ return true; }
		}
		
		function _validate() {
			var thisForm = document.forms["frmEvaluation"];
			var arrReq = new Array();
			
			if (!_FB_hasValue(thisForm['NABPNumber'], 'TEXT')) arrReq[arrReq.length] = 'NABP Number';
			if (!_FB_hasValue(thisForm['DateOfBirth'], 'TEXT')) arrReq[arrReq.length] = 'Date of Birth';
			if (!_FB_hasValue(thisForm['CreditType'], 'SELECT')) arrReq[arrReq.length] = 'Credit Type';
			if (!_FB_hasValue(thisForm['overallOuality'], 'RADIO')) arrReq[arrReq.length] = 'I. ACTIVITY EVALUATION, ##1';
			if (!_FB_hasValue(thisForm['educationalNeeds'], 'RADIO')) arrReq[arrReq.length] = 'I. ACTIVITY EVALUATION, ##2';
			if (!_FB_hasValue(thisForm['contentValue'], 'RADIO')) arrReq[arrReq.length] = 'I. ACTIVITY EVALUATION, ##3';
			if (!_FB_hasValue(thisForm['effectiveness'], 'RADIO')) arrReq[arrReq.length] = 'I. ACTIVITY EVALUATION, ##4';
			if (!_FB_hasValue(thisForm['appropriateness'], 'RADIO')) arrReq[arrReq.length] = 'I. ACTIVITY EVALUATION, ##5';
			if (!_FB_hasValue(thisForm['materials'], 'RADIO')) arrReq[arrReq.length] = 'I. ACTIVITY EVALUATION, ##6';
		
			if (!_FB_hasValue(thisForm['ProgramIncreasedKnowledge'], 'RADIO')) arrReq[arrReq.length] = 'II. IMPACT OF THE ACTIVITY, B.';
			if (!_FB_hasValue(thisForm['futureActivities'], 'RADIO')) arrReq[arrReq.length] = 'II. IMPACT OF THE ACTIVITY, C.';
			if (!_FB_hasValue(thisForm['notPromote'], 'RADIO')) arrReq[arrReq.length] = 'II. IMPACT OF THE ACTIVITY, D.';
			if (!_FB_hasValue(thisForm['metLearningObjectives'], 'RADIO')) arrReq[arrReq.length] = 'II. IMPACT OF THE ACTIVITY, E.';
			if (!_FB_hasValue(thisForm['InfoCauseChanges'], 'RADIO')) arrReq[arrReq.length] = 'II. IMPACT OF THE ACTIVITY, F.';
			if (!_FB_hasValue(thisForm['HowCommitted'], 'RADIO')) arrReq[arrReq.length] = 'II. IMPACT OF THE ACTIVITY, G.';
			if (!_FB_hasValue(thisForm['subjectMatterKnowledge'], 'RADIO')) arrReq[arrReq.length] = 'IV. ACTIVITY SPEAKER(S), A.';
			if (!_FB_hasValue(thisForm['CommPresentationSkills'], 'RADIO')) arrReq[arrReq.length] = 'IV. ACTIVITY SPEAKER(S), B.';
			if (!_FB_hasValue(thisForm['OverallResponsiveness'], 'RADIO')) arrReq[arrReq.length] = 'IV. ACTIVITY SPEAKER(S), C.';
			if (!_FB_hasValue(thisForm['SpeakerQuality'], 'RADIO')) arrReq[arrReq.length] = 'IV. ACTIVITY SPEAKER(S), D.';
			if (!_FB_hasValue(thisForm['FollowUp'], 'RADIO')) arrReq[arrReq.length] = 'V. FOLLOW-UP';
			if (arrReq.length > 0) {
				var msg = 'The following fields are required:\n\n';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
				alert(msg);
				return false;
			}
			return true;
		}
			
		$(document).ready(function(){
			mca_setupDatePickerField('DateOfBirth');
		});					
	</script>
	
<div class="tsAppHeading">Seminar Evaluation Form</div><br/>
<div class="bodyText">
<cfif val(event.getValue('isSubmitted',0))>
<div style="font-family:Verdana, Arial, Helvetica, sans-serif; font-size:8pt; color:##990000; font-weight:bold;text-align:center;">Your evaluation has been received!
<cfif local.qryEvents.recordcount>
Please continue the event evaluations below.
<cfelse>
You have completed all required evaluations.
</cfif>
<br><br>
</div>
</cfif>

<cfif local.qryEvents.recordcount>
	
		<cfform name="frmEvaluation"  id="frmEvaluation" action="#local.thisAppLink#" method="post" onsubmit="return _validate();">
		<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1">
		<cfinput type="hidden" name="memberID"  id="memberID" value="#session.cfcuser.memberdata.memberID#">
		
		<table class="bodyText" cellpadding="4" cellspacing="0">
		<tr valign="top">
			<td class="c"><b>NABP Number</b></td>
			<td>
					<cfinput type="text" name="NABPNumber"  id="NABPNumber" value="#local.qryCustomData.NABPNumber#" class="bodyText" size="15" maxlength="15">
			</td>
		</tr>
		<tr valign="top">
			<td class="c"><b>Credit Type</b></td>
			<td>
				<cfselect name="CreditType"  id="CreditType" class="bodyText">
						<option value="">Choose Credit Type</option>
						<option value="Technician" <cfif local.qryCustomData.CreditType eq "Technician">selected</cfif>>Technician</option>
						<option value="Pharmacist" <cfif local.qryCustomData.CreditType eq "Pharmacist">selected</cfif>>Pharmacist</option>
				</cfselect>
			</td>
		</tr>
		<tr valign="top">
			<td class="c"><b>Date of Birth</b></td>
			<td>
				<cfinput type="text" name="DateOfBirth" id="DateOfBirth" value="#dateformat(local.qryCustomData.DateOfBirth, 'MM/DD/YYYY')#" validate="USDate" class="bodyText" size="15" maxlength="10"> 
				<a href="javascript:mca_clearDateRangeField('DateOfBirth');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 0 7px;"></i></a> (mm/dd/yyyy)
			</td>
		</tr>
		<tr valign="top">
			<td class="c"><b>Step 1</b></td>
			<td>
				Select event you attended but have not yet evaluated: <br/>
				<cfselect name="eventID"  id="eventID" class="bodyText">
					<cfloop query="local.qryEvents">
						<option value="#local.qryEvents.eventid#">#dateformat(local.qryEvents.startTime,'mm/dd/yy')# &nbsp; #left(local.qryEvents.eventTitle,120)#<cfif len(local.qryEvents.eventTitle) gt 120>...</cfif></option>
					</cfloop>
				</cfselect>			
			</td>
		</tr>
		<tr>
			<td colspan="2"><strong>I. ACTIVITY EVALUATION</strong></td>
		</tr>
		<tr>
			<td colspan="2">
			<table>
				<tr>
					<td width="64%">Please Evaluate Each Item</td> 
					<td width="6%">&nbsp;</td>
					<td width="6%">Poor</td>
					<td width="6%">&nbsp;</td>
					<td width="6%">&nbsp;</td>
					<td width="6%">&nbsp;</td>
					<td width="6%">Excellent</td>
				</tr>
				<tr>
					<td>1.&nbsp;&nbsp;Overall quality of this activity</td>
					<td>&nbsp;</td>
					<td align="center"><cfinput type="radio" name="overallOuality"  id="overallOuality" value="1" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="overallOuality"  id="overallOuality" value="2" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="overallOuality"  id="overallOuality" value="3" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="overallOuality"  id="overallOuality" value="4" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="overallOuality"  id="overallOuality" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td>2.&nbsp;&nbsp;How well did this activity meet your individual educational needs?</td>
					<td>&nbsp;</td>
					<td align="center"><cfinput type="radio" name="educationalNeeds"  id="educationalNeeds" value="1" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="educationalNeeds"  id="educationalNeeds" value="2" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="educationalNeeds"  id="educationalNeeds" value="3" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="educationalNeeds"  id="educationalNeeds" value="4" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="educationalNeeds"  id="educationalNeeds" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td>3.&nbsp;&nbsp;Value of the content</td>
					<td>&nbsp;</td>
					<td align="center"><cfinput type="radio" name="contentValue"  id="contentValue" value="1" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="contentValue"  id="contentValue" value="2" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="contentValue"  id="contentValue" value="3" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="contentValue"  id="contentValue" value="4" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="contentValue"  id="contentValue" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td>4.&nbsp;&nbsp;Please rate the effectiveness of the active learning exercises.</td>
					<td>&nbsp;</td>
					<td align="center"><cfinput type="radio" name="effectiveness"  id="effectiveness" value="1" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="effectiveness"  id="effectiveness" value="2" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="effectiveness"  id="effectiveness" value="3" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="effectiveness"  id="effectiveness" value="4" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="effectiveness"  id="effectiveness" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td>5.&nbsp;&nbsp;Please rate the appropriateness of the final exam questions.</td>
					<td>&nbsp;</td>
					<td align="center"><cfinput type="radio" name="appropriateness"  id="appropriateness" value="1" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="appropriateness"  id="appropriateness" value="2" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="appropriateness"  id="appropriateness" value="3" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="appropriateness"  id="appropriateness" value="4" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="appropriateness"  id="appropriateness" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td>6.&nbsp;&nbsp;Please rate the usefulness of the educational materials used during this activity (e.g. handouts)</td>
					<td>&nbsp;</td>
					<td align="center"><cfinput type="radio" name="materials"  id="materials" value="1" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="materials"  id="materials" value="2" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="materials"  id="materials" value="3" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="materials"  id="materials" value="4" class="bodyText"></td>
					<td align="center"><cfinput type="radio" name="materials"  id="materials" value="5" class="bodyText"></td>
				</tr>
			</table>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2"><hr style="width:98%;"><hr style="width:98%;"></td>
		</tr>
		<tr>
			<td colspan="2"><strong>II. IMPACT OF THE ACTIVITY</strong></td>
		</tr>
		<tr valign="top">
			<td colspan="2">A. The information presented (check all that apply):</td>
		</tr>
		<tr>
			<td colspan="2"><div style="padding:4px;">
					<cfinput type="checkbox" name="ReinforcedCurrentPractice"  id="ReinforcedCurrentPractice" value="1" class="bodyText"> Reinforced my current practice/treatment habits<br/>
					<cfinput type="checkbox" name="ProvidedNewIdeas"  id="ProvidedNewIdeas" value="1" class="bodyText"> Provided new ideas or information I expect to use<br/>
					<cfinput type="checkbox" name="ImproveMyPractice"  id="ImproveMyPractice" value="1" class="bodyText"> Will improve my practice/patient outcomes<br/>
					<cfinput type="checkbox" name="EnhancedCurrentKnowledge"  id="EnhancedCurrentKnowledge" value="1" class="bodyText"> Enhanced my current knowledge base<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">B. The program increased my knowledge in the subject areas.</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<cfinput type="radio" name="ProgramIncreasedKnowledge"  id="ProgramIncreasedKnowledge" value="1" class="bodyText"> Agree<br/>
					<cfinput type="radio" name="ProgramIncreasedKnowledge"  id="ProgramIncreasedKnowledge" value="0" class="bodyText"> Disagree<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">C. I feel future activities on this subject matter are necessary and/or important to my practice.</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<cfinput type="radio" name="futureActivities"  id="futureActivities" value="1" class="bodyText"> Agree<br/>
					<cfinput type="radio" name="futureActivities"  id="futureActivities" value="0" class="bodyText"> Disagree<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">D. The program did not promote a particular product or company.</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<cfinput type="radio" name="notPromote"  id="notPromote" value="1" class="bodyText"> Agree<br/>
					<cfinput type="radio" name="notPromote"  id="notPromote" value="0" class="bodyText"> Disagree<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">If you disagreed with question D, please indicate which particular product or company was promoted.</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<textarea name="didPromote" rows="5" cols="55"></textarea>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">E. Did this activity meet the stated learning objectives?</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<cfinput type="radio" name="metLearningObjectives"  id="metLearningObjectives" value="1" class="bodyText"> Agree<br/>
					<cfinput type="radio" name="metLearningObjectives"  id="metLearningObjectives" value="0" class="bodyText"> Disagree<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">If you disagreed with question E, please indicate which learning objectives were not met.</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<textarea name="notMetlearningObjectives" rows="5" cols="55"></textarea>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">F. Will the information presented cause you to make any changes in your practice?</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<cfinput type="radio" name="InfoCauseChanges"  id="InfoCauseChanges" value="1" class="bodyText"> Agree<br/>
					<cfinput type="radio" name="InfoCauseChanges"  id="InfoCauseChanges" value="0" class="bodyText"> Disagree<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">Please list 1-2 things you will do differently:</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<textarea name="ThingsDifferently" rows="5" cols="55"></textarea>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">G. How committed are you to making these changes?</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					1 = not at all, 5 = very committed<br/>
					1 <cfinput type="radio" name="HowCommitted"  id="HowCommitted" value="1" class="bodyText"> 
					2 <cfinput type="radio" name="HowCommitted"  id="HowCommitted" value="2" class="bodyText">
					3 <cfinput type="radio" name="HowCommitted"  id="HowCommitted" value="3" class="bodyText">
					4 <cfinput type="radio" name="HowCommitted"  id="HowCommitted" value="4" class="bodyText">
					5 <cfinput type="radio" name="HowCommitted"  id="HowCommitted" value="5" class="bodyText">
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2"><hr style="width:98%;"><hr style="width:98%;">
			</td>
		</tr>
		<tr>
			<td colspan="2"><strong>III. ACTIVITY COMMENTS</strong></td>
		</tr>
		<tr valign="top">
			<td colspan="2">1. What aspects of this live training activity did you enjoy <strong><u>most?</u></strong></td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<textarea name="ActivityEnjoyedMost" rows="5" cols="55"></textarea>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">2. What aspects of this live training activity did you enjoy <strong><u>least?</u></strong></td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<textarea name="ActivityEnjoyedLeast" rows="5" cols="55"></textarea>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">3. Please provide <u>any additional comments</u> about the seminar (including anything you would change for future offerings):</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<textarea name="AdditionalComments" rows="5" cols="55"></textarea>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2"><hr style="width:98%;"><hr style="width:98%;">
			</td>
		</tr>
		<tr>
			<td colspan="2"><strong>IV. ACTIVITY SPEAKER(S)</strong></td>
		</tr>
		<tr valign="top">
			<td colspan="2">Instructions: Select the number that best indicates your opinion of the speaker(s).</br>
		Key: 1 = Needs Improvement&nbsp;&nbsp;&nbsp;&nbsp;2 = Fair&nbsp;&nbsp;&nbsp;&nbsp;3 = Good&nbsp;&nbsp;&nbsp;&nbsp;4 = Very Good&nbsp;&nbsp;&nbsp;&nbsp;5 = Excellent</td>
		</tr>
		<tr>
			<td colspan="2">
			<table>
				<tr>
					<td width="65%">A. Knowledge of Subject Matter</td>
					<td>&nbsp;</td>
					<td align="center">1&nbsp;<cfinput type="radio" name="subjectMatterKnowledge"  id="subjectMatterKnowledge" value="1" class="bodyText"></td>
					<td align="center">2&nbsp;<cfinput type="radio" name="subjectMatterKnowledge"  id="subjectMatterKnowledge" value="2" class="bodyText"></td>
					<td align="center">3&nbsp;<cfinput type="radio" name="subjectMatterKnowledge"  id="subjectMatterKnowledge" value="3" class="bodyText"></td>
					<td align="center">4&nbsp;<cfinput type="radio" name="subjectMatterKnowledge"  id="subjectMatterKnowledge" value="4" class="bodyText"></td>
					<td align="center">5&nbsp;<cfinput type="radio" name="subjectMatterKnowledge"  id="subjectMatterKnowledge" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td width="65%">B. Communication and presentation skills</td>
					<td>&nbsp;</td>
					<td align="center">1&nbsp;<cfinput type="radio" name="CommPresentationSkills"  id="CommPresentationSkills" value="1" class="bodyText"></td>
					<td align="center">2&nbsp;<cfinput type="radio" name="CommPresentationSkills"  id="CommPresentationSkills" value="2" class="bodyText"></td>
					<td align="center">3&nbsp;<cfinput type="radio" name="CommPresentationSkills"  id="CommPresentationSkills" value="3" class="bodyText"></td>
					<td align="center">4&nbsp;<cfinput type="radio" name="CommPresentationSkills"  id="CommPresentationSkills" value="4" class="bodyText"></td>
					<td align="center">5&nbsp;<cfinput type="radio" name="CommPresentationSkills"  id="CommPresentationSkills" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td width="65%">C. Overall responsiveness to audience's questions</td>
					<td>&nbsp;</td>
					<td align="center">1&nbsp;<cfinput type="radio" name="OverallResponsiveness"  id="OverallResponsiveness" value="1" class="bodyText"></td>
					<td align="center">2&nbsp;<cfinput type="radio" name="OverallResponsiveness"  id="OverallResponsiveness" value="2" class="bodyText"></td>
					<td align="center">3&nbsp;<cfinput type="radio" name="OverallResponsiveness"  id="OverallResponsiveness" value="3" class="bodyText"></td>
					<td align="center">4&nbsp;<cfinput type="radio" name="OverallResponsiveness"  id="OverallResponsiveness" value="4" class="bodyText"></td>
					<td align="center">5&nbsp;<cfinput type="radio" name="OverallResponsiveness"  id="OverallResponsiveness" value="5" class="bodyText"></td>
				</tr>
				<tr>
					<td width="65%">D. Overall speaker quality</td>
					<td>&nbsp;</td>
					<td align="center">1&nbsp;<cfinput type="radio" name="SpeakerQuality"  id="SpeakerQuality" value="1" class="bodyText"></td>
					<td align="center">2&nbsp;<cfinput type="radio" name="SpeakerQuality"  id="SpeakerQuality" value="2" class="bodyText"></td>
					<td align="center">3&nbsp;<cfinput type="radio" name="SpeakerQuality"  id="SpeakerQuality" value="3" class="bodyText"></td>
					<td align="center">4&nbsp;<cfinput type="radio" name="SpeakerQuality"  id="SpeakerQuality" value="4" class="bodyText"></td>
					<td align="center">5&nbsp;<cfinput type="radio" name="SpeakerQuality"  id="SpeakerQuality" value="5" class="bodyText"></td>
				</tr>
			</table>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2"><hr style="width:98%;"><hr style="width:98%;">
			</td>
		</tr>
		<tr>
			<td colspan="2"><strong>V. FOLLOW-UP</strong></td>
		</tr>
		<tr valign="top">
			<td colspan="2">As part of our ongoing quality-improvement effort, we would like to be able to contact you in the event we conduct a follow-up survey to assess the impact of our educational interventions on professional practice. Are you willing to participate in such a survey?</td>
		</tr>
		<tr>
			<td colspan="2">
				<div style="padding:4px;">
					<cfinput type="radio" name="FollowUp"  id="FollowUp" value="1" class="bodyText"> Yes<br/>
					<cfinput type="radio" name="FollowUp"  id="FollowUp" value="0" class="bodyText"> No<br/>
				</div>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="2">&nbsp;</td>
		</tr>
		<tr valign="top">
			<td colspan="2"><cfinput type="submit" value="Submit" name="btnSubmit"  id="btnSubmit" class="tsAppBodyText" /></td>
		</tr>
		</table>
		</cfform>
	<cfelse>
	<div align="center">
		There are no event evaluations that require completion at this time.
	</div>
	</cfif>
	</div>
</cfoutput>