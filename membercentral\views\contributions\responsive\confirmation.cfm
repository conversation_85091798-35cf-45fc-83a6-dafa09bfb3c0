<cfset local.stepNum = 4>

<cfinclude template="../commonJS_step4.cfm">

<cfoutput>
<div class="container-fluid">
	<div class="contribution-section">
		
		<cfinclude template="navigation.cfm">

		<div class="tab-content">
			<div class="tab-pane active">
				<div class="contribution">
					<h2 class="title-blue">#attributes.data.strConfirmation.qryContributionInfo.intakeFormTitle#</h2>
					<div style="margin:10px 0 20px 0;">#attributes.data.strConfirmation.programConfirmContent#</div>
					<div style="clear:both;"></div>

					<cfform action="##" method="GET" name="frmSendConfirmation" id="frmSendConfirmation">
						<div>
							If you have an e-mail address on file, we have already e-mailed you a copy of this confirmation.
							You may send this confirmation to another address here:
						</div>
						<div>
							<cfinput type="text" name="sendToEmail" style="width:200px;margin:0;" id="sendToEmail" placeholder="<EMAIL>" required="true" validate="regular_expression" pattern="#application.regEx.email#" message="Enter a valid e-mail address.">
							<button type="button" class="btn" name="btnResendConfirmation" id="btnResendConfirmation" onClick="sendConfirmation();">Send</button>
							<span id="spanResendInd" style="display:none;"><i class="icon-spinner icon-spin"></i> Sending...</span>
							<span id="spanResendMsg" style="display:none;"></span>
						</div>
					</cfform>

				</div>
			</div>
		</div>

		#attributes.data.strConfirmation.contributionData#
	</div>
</div>
</cfoutput>
